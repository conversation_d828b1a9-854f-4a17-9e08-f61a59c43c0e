-- 库存对比策略
CREATE TABLE `sg_c_stock_contrast_strategy`
(
    `id`                     bigint(20) NOT NULL,
    `cp_c_shop_id`           bigint(20) DEFAULT NULL COMMENT '店铺ID',
    `is_open_stock_contrast` int(1) DEFAULT '1' COMMENT '是否开启库存对比 1-是,0-否',
    `data_source_type`       int(2) DEFAULT NULL COMMENT '库存对比数据源',
    `time_range`             bigint(10) DEFAULT NULL COMMENT 'n小时内订单',
    `synstock_type`          int(2) DEFAULT NULL COMMENT '库存同步类型 1-仅同步平台库存大于系统库存的记录；2-仅同步平台库存小于系统库存的记录；3-存在差异的记录',
    `is_sync_stock`          int(1) DEFAULT NULL COMMENT '是否允许同步平台库存小于系统库存 1-是,0-否',
    `begin_time`             int(2) DEFAULT NULL COMMENT '对比开始时间',
    `end_time`               int(2) DEFAULT NULL COMMENT '对比结束时间',
    `warning_type`           int(2) DEFAULT NULL COMMENT '提醒方式',
    `warning_url`            text          DEFAULT NULL COMMENT '预警地址',
    `remark`                 varchar(2000) DEFAULT NULL COMMENT '备注',
    `ad_client_id`           bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`              bigint(20) DEFAULT '27' COMMENT '所属组织',
    `isactive`               char(1)       DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                bigint(20) DEFAULT NULL COMMENT '创建人id',
    `ownerename`             varchar(50)   DEFAULT NULL COMMENT '创建人姓名',
    `ownername`              varchar(50)   DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`           datetime      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifierid`             bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modifierename`          varchar(50)   DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`           varchar(50)   DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`           datetime      DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `deler_id`               bigint(20) DEFAULT NULL COMMENT '作废人id',
    `deler_ename`            varchar(50)   DEFAULT NULL COMMENT '作废人姓名',
    `deler_name`             varchar(50)   DEFAULT NULL COMMENT '作废人用户名',
    `del_time`               datetime      DEFAULT NULL COMMENT '作废时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                      `ix_cp_c_shop_id01` (`cp_c_shop_id`) USING BTREE COMMENT '店铺ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

#YY经销商
聚合仓档案增加字段
ALTER TABLE sg_c_share_store
    ADD COLUMN `is_yy_store` char(1) DEFAULT 'N' COMMENT '是否yy聚合仓';
#欧瑞共享库存同步结果表增加字段
ALTER TABLE `sg_c_sync_share_storage_result`
    ADD COLUMN `qty_sale_retain` decimal(18, 4) DEFAULT '0.0000' COMMENT '中台保留库存：中台聚合仓的在库量-共享池分配量（0）-共享类型的配销仓在库量';

#配销仓选款结果表
CREATE TABLE `sg_c_sa_selection`
(
    `id`                     bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`           bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`              bigint(20) DEFAULT '27' COMMENT '所属组织',
    `batch_no`               varchar(100)   DEFAULT NULL COMMENT '批次号',
    `start_time`             datetime       DEFAULT NULL COMMENT '开始时间',
    `end_time`               datetime       DEFAULT NULL COMMENT '结束时间',
    `sg_c_share_store_id`    bigint(20) DEFAULT NULL COMMENT '聚合仓id',
    `sg_c_share_store_ecode` varchar(20)    DEFAULT NULL COMMENT '聚合仓编码',
    `sg_c_share_store_ename` varchar(255)   DEFAULT NULL COMMENT '聚合仓名称',
    `sg_c_sa_store_id`       varchar(255)   DEFAULT NULL COMMENT '配销仓id',
    `sg_c_sa_store_ecode`    varchar(255)   DEFAULT NULL COMMENT '配销仓编码',
    `sg_c_sa_store_ename`    varchar(255)   DEFAULT NULL COMMENT '配销仓名称',
    `ps_c_sku_id`            bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`         varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `ps_c_pro_id`            bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`         varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`         varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `share_type`             int(2) DEFAULT '1' COMMENT '共享维度:1-按数量，2-按比例',
    `share_ratio`            decimal(18, 4) DEFAULT '0.0000' COMMENT '共享系数',
    `remark`                 varchar(500)   DEFAULT NULL COMMENT '备注',
    `version`                bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`               char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`             varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`              varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`           datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`             bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`          varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`           varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`           datetime       DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    key                      `idx_sg_c_sa_selection_01` (`sg_c_share_store_id`) USING BTREE,
    key                      `idx_sg_c_sa_selection_02` (`sg_c_sa_store_id`) USING BTREE,
    key                      `idx-sg_c_sa_selection_03` (`ps_c_sku_id`) USING BTREE,
    key                      `idx_sg_c_sa_selection_04` (`ps_c_pro_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配销仓选款结果表';

CREATE TABLE `sg_b_sa_target_storage_summary_temp`
(
    `id`                        bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `ad_client_id`              bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                 bigint(20) DEFAULT '27' COMMENT '所属组织',
    `sg_c_sa_store_id`          bigint(20) DEFAULT NULL COMMENT '配货仓ID',
    `sg_c_sa_store_ecode`       varchar(100)   DEFAULT NULL COMMENT '配货仓编码',
    `sg_c_sa_store_ename`       varchar(255)   DEFAULT NULL COMMENT '配货仓名称',
    `ps_c_sku_id`               bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`            varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `ps_c_pro_id`               bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`            varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`            varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `ps_c_spec1_id`             bigint(20) DEFAULT NULL COMMENT '规格1ID',
    `ps_c_spec1_ecode`          varchar(100)   DEFAULT NULL COMMENT '规格1编码',
    `ps_c_spec1_ename`          varchar(100)   DEFAULT NULL COMMENT '规格1名称',
    `ps_c_spec2_id`             bigint(20) DEFAULT NULL COMMENT '规格2ID',
    `ps_c_spec2_ecode`          varchar(100)   DEFAULT NULL COMMENT '规格2编码',
    `ps_c_spec2_ename`          varchar(100)   DEFAULT NULL COMMENT '规格2名称',
    `ps_c_spec3_id`             bigint(20) DEFAULT NULL COMMENT '规格3ID',
    `ps_c_spec3_ecode`          varchar(100)   DEFAULT NULL COMMENT '规格3编码',
    `ps_c_spec3_ename`          varchar(100)   DEFAULT NULL COMMENT '规格3名称',
    `qty_target_storage`        decimal(18, 4) DEFAULT '0.0000' COMMENT '目标库存',
    `remark`                    varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`                   bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`                  char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                   bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                 varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`              datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`             varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`              varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`              datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    `sg_c_share_store_id`       bigint(20) DEFAULT NULL COMMENT '共享仓ID',
    `sg_c_share_store_ecode`    varchar(20)    DEFAULT NULL COMMENT '共享仓编码',
    `sg_c_share_store_ename`    varchar(255)   DEFAULT NULL COMMENT '共享仓名称',
    `qty_share_unshared_preout` decimal(18, 4) DEFAULT '0.0000' COMMENT '聚合仓非共享占用数量',
    `qty_share_shared_preout`   decimal(18, 4) DEFAULT '0.0000' COMMENT '聚合仓共享占用数量',
    `qty_share_preout`          decimal(18, 4) DEFAULT '0.0000' COMMENT '聚合仓占用数量',
    `qty_share_prein`           decimal(18, 4) DEFAULT '0.0000' COMMENT '聚合仓在途数量',
    `qty_share_freeze`          decimal(18, 4) DEFAULT '0.0000' COMMENT '聚合仓冻结数量',
    `qty_share_sa_preout`       decimal(18, 4) DEFAULT '0.0000' COMMENT '聚合仓配销总占用数量',
    `qty_share_sa_storage`      decimal(18, 4) DEFAULT '0.0000' COMMENT '聚合仓配销总在库数量',
    `qty_share_storage`         decimal(18, 4) DEFAULT '0.0000' COMMENT '聚合仓在库数量',
    `qty_share_sale_available`  decimal(18, 4) DEFAULT '0.0000' COMMENT '聚合仓可售数量',
    `qty_store_available`       decimal(18, 4) DEFAULT '0.0000' COMMENT '逻辑仓可用数量',
    `qty_share_available`       decimal(18, 4) DEFAULT '0.0000' COMMENT '聚合仓可用数量',
    `qty_sp_preout`             decimal(18, 4) DEFAULT '0.0000' COMMENT '共享池可用数量',
    `qty_sp_storage`            decimal(18, 4) DEFAULT '0.0000' COMMENT '共享池在库数量',
    `qty_sa_available`          decimal(18, 4) DEFAULT '0.0000' COMMENT '聚合仓可用数量',
    `qty_stock_depth_target`    decimal(18, 4) DEFAULT '0.0000' COMMENT '库存目标深度',
    `qty_stock_depth_begin`     decimal(18, 4) DEFAULT '0.0000' COMMENT '库存深度开始数量',
    `qty_stock_depth_end`       decimal(18, 4) DEFAULT '0.0000' COMMENT '库存深度结束数量',
    `attribute`                 varchar(80)    DEFAULT NULL COMMENT '属性',
    `channel_name`              varchar(50)    DEFAULT NULL COMMENT '主控渠道',
    `product_level`             varchar(50)    DEFAULT NULL COMMENT '商品级别',
    `drp_style`                 varchar(50)    DEFAULT NULL COMMENT '型号(Style)',
    `clf`                       varchar(80)    DEFAULT NULL COMMENT '内部CLF',
    `stockout_rate`             decimal(10, 2) DEFAULT '0.00' COMMENT '库存售罄',
    `inline_season`             varchar(80)    DEFAULT NULL COMMENT 'inLine Season',
    `qty_sa_allocation`         decimal(18, 4) DEFAULT '0.0000' COMMENT '配销仓待分配量',
    `qty_allocation_begin`      decimal(18, 4) DEFAULT '0.0000' COMMENT '店铺铺底策略起始量',
    `qty_allocation_end`        decimal(18, 4) DEFAULT '0.0000' COMMENT '店铺铺底策略终止量',
    `qty_oneset_target`         decimal(18, 4) DEFAULT '0.0000' COMMENT '店铺铺底目标手数',
    `qty_oneset`                decimal(18, 4) DEFAULT '0.0000' COMMENT '一手码数量',
    `season`                    varchar(80)    DEFAULT NULL COMMENT '季节',
    `price_list`                decimal(18, 4) DEFAULT '0.0000' COMMENT '吊牌价',
    `channel`                   varchar(50)    DEFAULT NULL COMMENT '渠道',
    `tale_sort`                 varchar(500)   DEFAULT NULL COMMENT '故事分类',
    `qty_share_available_after` decimal(18, 4) DEFAULT NULL COMMENT '聚合仓之后可用数量',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                         `batch_no` (`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='配销仓目标库存汇总';

CREATE
UNIQUE index idx_sg_c_ecom_product_maint_01 on sg_c_ecom_product_maint (`ps_c_pro_id`);