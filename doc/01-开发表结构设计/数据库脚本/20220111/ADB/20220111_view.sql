
#分货结果和目标量查询#
DROP VIEW `data_check`.v_sg_share_target_quantity;

CREATE  VIEW v_sg_share_target_quantity AS
SELECT
  yy.sg_b_sa_storage_id AS id,
  m.sg_c_share_store_ecode,
  m.sg_c_share_store_id,
  yy.sg_c_sa_store_ecode,
  yy.sg_c_sa_store_id,
  psc.ECODE AS PS_C_PRO_ECODE,
  m.ps_c_pro_id,
  yy.ps_c_sku_ecode,
  m.ps_c_sku_id,
  scp.product_category,
  psc.ware_type,
  IFNULL( sc.qty, sc1.qty ) AS in_code_qty,-- 一手码数量
  sca.qty_oneset_target,-- 目标手数
  IFNULL( sc.qty, sc1.qty )* sca.qty_oneset_target AS qty_target,-- 目标库存
  yy.px_qty_storage,-- 配销仓库存
  yy.px_qty_preout,-- 配销仓占用
  yy.px_qty_available,-- 配销仓可用
  IFNULL( sc.qty, sc1.qty )* sca.qty_oneset_target - yy.px_qty_storage AS to_distributed_qty,-- 待分配量
  IFNULL( sbt.qty_sa_allocation, 0 ) AS qty_sa_allocation,-- 最近一次分配量
  sbt.modifieddate,-- 最近一次分货时间
  sca.qty_allocation_begin,-- 可分配库存开始量
  sca.qty_allocation_end,-- 可分配库存结束量
  vrp.sku_share_store_sale_qty,-- 聚合仓SKU可分配库存
  ifnull( m.lj_qty_storage, 0 ) - ifnull( m.lj_qty_freeze, 0 )- ifnull( m.lj_qty_unshared_preout, 0 )- ifnull( m.QTY_SP_ALLOCATION, 0 ) - ifnull( m.px_qty_preout, 0 ) AS share_store_sale_qty,-- 聚合仓SKC可分配库存
  m.qty_available AS share_store_available_qty,-- 聚合仓可用量
  m.lj_qty_storage,-- 聚合仓在库量
  m.lj_qty_freeze,-- 聚合仓冻结量
  m.lj_qty_unshared_preout,-- 聚合仓逻辑占用量
  m.px_qty_storage as share_px_qty_storage,-- 配销分配量
  m.px_qty_preout AS share_px_qty_preout,-- 聚合仓配销占用量
  ifnull( IFNULL( secx.is_gift, secy.is_gift ), 'N' ) AS IS_GIFT,-- 是否活动
  yy.isactive,
  yy.ad_client_id,
  yy.ad_org_id,
  yy.remark,
  yy.version,
  yy.ownerid,
  yy.ownerename,
  yy.ownername,
  yy.creationdate,
  yy.modifierid,
  yy.modifierename,
  yy.modifiername
FROM
  v_rpt_sg_b_share_storage m -- 聚合仓库存
  JOIN (
  SELECT
    vr.sg_c_share_store_id,
    vr.ps_c_pro_id,
    sum(
      ifnull( vr.lj_qty_storage, 0 ) - ifnull( vr.lj_qty_freeze, 0 )- ifnull( vr.lj_qty_unshared_preout, 0 )- ifnull( vr.QTY_SP_ALLOCATION, 0 ) - ifnull( vr.px_qty_preout, 0 )
    ) AS sku_share_store_sale_qty
  FROM
    v_rpt_sg_b_share_storage vr
  GROUP BY
    vr.sg_c_share_store_id,
    vr.ps_c_pro_id
  ) vrp ON m.sg_c_share_store_id = vrp.sg_c_share_store_id
  AND m.ps_c_pro_id = vrp.ps_c_pro_id
  JOIN (
  SELECT
    sg.sg_c_share_store_id,
    s.sg_c_sa_store_id,
    s.sg_c_sa_store_ecode,
    s.ps_c_sku_id,
    s.PS_C_SKU_ECODE,
    s.qty_available AS px_qty_available,
    s.qty_storage AS px_qty_storage,
    s.qty_preout AS px_qty_preout,
    s.id AS sg_b_sa_storage_id,
    s.isactive,
    s.ad_client_id,
    s.ad_org_id,
    s.remark,
    s.version,
    s.ownerid,
    s.ownerename,
    s.ownername,
    s.creationdate,
    s.modifierid,
    s.modifierename,
    s.modifiername,
    s.modifieddate
  FROM
    prod_r3_oms_sg.SG_B_SA_STORAGE s
    JOIN prod_r3_oms_sg.SG_C_SA_STORE sg ON s.sg_c_sa_store_id = sg.id
  WHERE
    EXISTS (
    SELECT
      1
    FROM
      prod_r3_oms_sg.SG_C_SHOP_SKU_SALE_SETTING sgs
      JOIN prod_r3_oms_sg.sg_c_channel_ratio_strategy sgy ON sgs.cp_c_shop_id = sgy.cp_c_shop_id
      JOIN prod_r3_oms_sg.sg_c_channel_ratio_strategy_item sgi ON sgi.sg_c_channel_ratio_strategy_id = sgy.ID
      AND sgi.ratio > 0
    WHERE
      sgs.is_sale = 'Y'
      AND sgs.ps_c_pro_id = s.ps_c_pro_id
      AND sgi.sg_c_sa_store_id = s.sg_c_sa_store_id
    )
  ) yy -- 配销仓数据
  ON m.sg_c_share_store_id = yy.sg_c_share_store_id
  AND m.ps_c_sku_id = yy.ps_c_sku_id
  JOIN prod_r3_oms_sg.SG_C_ECOM_PRODUCT_MAINT scp -- 电商商品属性维护
  ON m.ps_c_pro_id = scp.ps_c_pro_id
  JOIN r3_base.ps_c_pro psc -- 商品档案
  ON m.ps_c_pro_id = psc.id
  LEFT JOIN prod_r3_oms_sg.SG_C_ONESET_SA_SETTING sc -- 配销仓特殊条码设置
  ON yy.sg_c_sa_store_id = sc.SG_C_SA_STORE_ID
  AND sc.ps_c_sku_id = m.PS_C_SKU_ID
  JOIN prod_r3_oms_sg.SG_C_ONESET_STORAGE_GRADIENT_QTY sc1 -- 一手码库存阶梯数量
  ON sc1.qty_allocation_begin <= ifnull( m.lj_qty_storage, 0 ) - ifnull( m.lj_qty_freeze, 0 )- ifnull( m.lj_qty_unshared_preout, 0 )- ifnull( m.QTY_SP_ALLOCATION, 0 ) - ifnull( yy.px_qty_preout, 0 )
  AND sc1.qty_allocation_end > ifnull( m.lj_qty_storage, 0 ) - ifnull( m.lj_qty_freeze, 0 )- ifnull( m.lj_qty_unshared_preout, 0 )- ifnull( m.QTY_SP_ALLOCATION, 0 ) - ifnull( yy.px_qty_preout, 0 )
  LEFT JOIN prod_r3_oms_sg.sg_c_shop_allocation sca -- 店铺铺底设置
  ON sca.sg_c_sa_store_id = yy.sg_c_sa_store_id
  AND sca.PRODUCT_CATEGORY = scp.product_category
  AND sca.WARE_TYPE = psc.ware_type
  AND sca.qty_allocation_begin <= vrp.sku_share_store_sale_qty AND sca.qty_allocation_end > vrp.sku_share_store_sale_qty
  LEFT JOIN prod_r3_oms_sg.sg_b_sa_target_storage_summary sbt -- 配销仓目标库存汇总
  ON sbt.sg_c_share_store_id = m.sg_c_share_store_id
  AND sbt.sg_c_sa_store_id = yy.sg_c_sa_store_id
  AND sbt.ps_c_sku_id = m.ps_c_sku_id
  JOIN prod_r3_oms_sg.SG_C_SHARE_STORE sh -- 聚合仓档案
  ON m.sg_c_share_store_id = sh.id
  JOIN prod_r3_oms_sg.SG_C_SA_STORE sa -- 配销仓档案
  ON yy.sg_c_sa_store_id = sa.id
  LEFT JOIN (
  SELECT
    sec.sg_c_sa_store_id,
    'Y' AS is_gift
  FROM
    prod_r3_oms_sg.sg_c_ecom_activity_setting_sa_item sec -- E-COM活动时间设置-配销仓明细
    JOIN prod_r3_oms_sg.sg_c_ecom_activity_setting sec1 ON sec.sg_c_ecom_activity_setting_id = sec1.ID
  WHERE
    sec1.`status` = 2
  AND sec.BEGIN_TIME <= SYSDATE() AND sec.FREEZE_TIME >= SYSDATE()) secx ON yy.sg_c_sa_store_id = secx.sg_c_sa_store_id
  LEFT JOIN (
  SELECT
    sec.sg_c_sa_store_id,
    sec.ps_c_pro_id,
    'Y' AS is_gift
  FROM
    prod_r3_oms_sg.sg_c_ecom_activity_setting_pro_item sec -- E-COM活动时间设置-商品明细
    JOIN prod_r3_oms_sg.sg_c_ecom_activity_setting sec1 ON sec.sg_c_ecom_activity_setting_id = sec1.ID
  WHERE
    sec1.`status` = 2
  AND sec.BEGIN_TIME <= SYSDATE() AND sec.FREEZE_TIME >= SYSDATE()) secy ON yy.sg_c_sa_store_id = secy.sg_c_sa_store_id
  AND m.ps_c_pro_id = secy.ps_c_pro_id
WHERE
  sh.IS_AUTO_ALLOCATION = 'Y'
  AND sa.is_auto_allocation = 'Y';



  create view v_rpt_sg_b_share_storage as (select
  m.id,
	m.sg_c_share_store_ecode ,
	m.sg_c_share_store_id ,
	m.ps_c_pro_id ,
	m.ps_c_sku_id ,
	m.ps_c_spec1_id ,
	m.ps_c_spec2_id ,
	ifnull( xx.lj_qty_storage,0) - ifnull( xx.lj_qty_freeze, 0 )- ifnull( xx.lj_qty_unshared_preout, 0 )- ifnull( yy.px_qty_storage, 0 ) - ifnull( m.qty_sp_allocation, 0 ) qty_available,
	ifnull(xx.lj_qty_storage,0) lj_qty_storage,
	ifnull(xx.lj_qty_freeze,0) lj_qty_freeze ,
	ifnull(xx.lj_qty_unshared_preout,0) lj_qty_unshared_preout,
	ifnull(yy.px_qty_storage,0) px_qty_storage,
	ifnull(yy.px_qty_preout,0) px_qty_preout,
	ifnull( m.qty_sp_allocation,0) qty_sp_allocation,
	ifnull( m.qty_sp_preout,0)  qty_sp_preout
from
	 prod_r3_oms_sg.sg_b_share_storage m
	left join (
	select
		b.sg_c_share_store_id,
		a.ps_c_sku_id,
		sum( a.qty_storage ) as lj_qty_storage,
		sum( a.qty_freeze ) as lj_qty_freeze,
		sum( a.qty_unshared_preout ) as lj_qty_unshared_preout
	from
		 prod_r3_oms_sg.sg_b_storage a
		join  prod_r3_oms_sg.cp_c_store b on a.cp_c_store_id = b.id
	where
		b.sg_c_share_store_id is not null
	group by
		b.sg_c_share_store_id,
		a.ps_c_sku_id
	) xx on m.sg_c_share_store_id = xx.sg_c_share_store_id
	and m.ps_c_sku_id = xx.ps_c_sku_id
	left join (
	select
		sg.sg_c_share_store_id,
		s.ps_c_sku_id,
		sum( s.qty_available ) as px_qty_available,
		sum( s.qty_storage ) as px_qty_storage,
		sum( s.qty_preout ) as px_qty_preout
	from
		 prod_r3_oms_sg.sg_b_sa_storage s
		join  prod_r3_oms_sg.sg_c_sa_store sg on s.sg_c_sa_store_id = sg.id
	group by
		sg.sg_c_share_store_id,
		s.ps_c_sku_id
	) yy on m.sg_c_share_store_id = yy.sg_c_share_store_id
	and m.ps_c_sku_id = yy.ps_c_sku_id);