SgChannelProductDownloadTask  仅修改用户参数(默认以平台维度插入缓存池下载平台商品库存,若要以店铺维度,维护平台类型id同时也要维护店铺id)
{"param":"{\"platformIds\":'2',"shopIds":''}","className":"com.burgeon.r3.sg.channel.task.SgChannelProductDownloadTask","type":"localJob"}

SgChannelProductBufferDownloadTask   仅修改用户参数(默认以平台维度拉取缓存池数据下载平台商品库存,若要以店铺维度下载,维护平台类型id同时也要维护店铺id)
{"param":"{\"platformIds\":'2',"shopIds":''}","className":"com.burgeon.r3.sg.channel.task.SgChannelProductBufferDownloadTask","type":"localJob"}

SgChannelStorageToWingTask (一件代发店铺库存同步wing)
Cron = 0 0 2 * * ?
{"className":"com.burgeon.r3.sg.stocksync.task.SgChannelStorageToWingTask","type":"localJob"}

ChannelProductStorageContrastTask   (唯品会库存对比)
0 0 0/2 * * ?
依赖上一周期
r3_sg_skq
{"param":"{\"platformIds\":'19'}","className":"com.burgeon.r3.sg.stocksync.task.ChannelProductStorageContrastTask","type":"localJob"}
