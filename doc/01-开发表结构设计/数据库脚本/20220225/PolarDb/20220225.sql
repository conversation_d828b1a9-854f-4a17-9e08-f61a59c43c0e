alter table sg_b_channel_storage_full_sync
    add column is_close_stock_sync int(1) default 0 COMMENT '是否关闭库存同步';
alter table sg_c_channel_source_strategy_force_item
    add column source_bill_no varchar(100) default null COMMENT '数据来源';

-- 逻辑仓修改聚合仓
CREATE TABLE `sg_b_sto_store_update_share_store`
(
    `id`                         bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`               bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                  bigint(20) DEFAULT '27' COMMENT '所属组织',
    `bill_no`                    varchar(50)  DEFAULT NULL COMMENT '单据编号',
    `bill_date`                  date         DEFAULT NULL COMMENT '单据日期',
    `cp_c_store_id`              bigint(20) DEFAULT NULL COMMENT '逻辑仓ID',
    `cp_c_store_ecode`           varchar(100) DEFAULT NULL COMMENT '逻辑仓编码',
    `cp_c_store_ename`           varchar(255) DEFAULT NULL COMMENT '逻辑仓名称',
    `new_sg_c_share_store_id`    bigint(20) DEFAULT NULL COMMENT '所属聚合仓id',
    `new_sg_c_share_store_ecode` varchar(100) DEFAULT NULL COMMENT '所属聚合仓编码',
    `new_sg_c_share_store_ename` varchar(255) DEFAULT NULL COMMENT '所属聚合仓名称',
    `old_sg_c_share_store_id`    bigint(20) DEFAULT NULL COMMENT '旧聚合仓id',
    `old_sg_c_share_store_ecode` varchar(100) DEFAULT NULL COMMENT '旧聚合仓编码',
    `old_sg_c_share_store_ename` varchar(255) DEFAULT NULL COMMENT '旧聚合仓名称',
    `status`                     int(11) DEFAULT NULL COMMENT '单据状态',
    `remark`                     varchar(255) DEFAULT NULL COMMENT '备注',
    `version`                    bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`                   char(1)      DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                    bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                 varchar(50)  DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                  varchar(50)  DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`               datetime     DEFAULT NULL COMMENT '创建时间',
    `modifierid`                 bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`              varchar(50)  DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`               varchar(50)  DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`               datetime     DEFAULT NULL COMMENT '修改时间',
    `deler_id`                   bigint(20) DEFAULT NULL COMMENT '作废人ID',
    `deler_ename`                varchar(50)  DEFAULT NULL COMMENT '作废人姓名',
    `deler_name`                 varchar(50)  DEFAULT NULL COMMENT '作废人用户名',
    `del_time`                   datetime     DEFAULT NULL COMMENT '作废时间',
    `status_id`                  int(11) DEFAULT '0' COMMENT '提交人ID',
    `status_ename`               varchar(50)  DEFAULT NULL COMMENT '提交人姓名',
    `status_name`                varchar(50)  DEFAULT NULL COMMENT '提交人用户名',
    `status_time`                datetime     DEFAULT NULL COMMENT '提交时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                          `idx_sg_b_sto_store_update_share_store_01` (`bill_no`),
    KEY                          `idx_sg_b_sto_store_update_share_store_02` (`bill_date`),
    KEY                          `idx_sg_b_sto_store_update_share_store_03` (`cp_c_store_id`),
    KEY                          `idx_sg_b_sto_store_update_share_store_04` (`new_sg_c_share_store_id`),
    KEY                          `idx_sg_b_sto_store_update_share_store_05` (`old_sg_c_share_store_id`),
    KEY                          `idx_sg_b_sto_store_update_share_store_06` (`cp_c_store_ecode`),
    KEY                          `idx_sg_b_sto_store_update_share_store_07` (`new_sg_c_share_store_ecode`),
    KEY                          `idx_sg_b_sto_store_update_share_store_08` (`old_sg_c_share_store_ecode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='逻辑仓修改聚合仓';

-- 未完成单据
CREATE TABLE `sg_b_sto_store_update_share_store_item`
(
    `id`                                   bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`                         bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                            bigint(20) DEFAULT '27' COMMENT '所属组织',
    `sg_b_sto_store_update_share_store_id` bigint(20) DEFAULT NULL COMMENT '所属逻辑仓修改聚合仓',
    `oc_b_order_bill_no`                   varchar(50)  DEFAULT NULL COMMENT '零售发货单编号',
    `tid`                                  varchar(200) DEFAULT NULL COMMENT '平台单号',
    `sg_b_sto_out_bill_no`                 varchar(200) DEFAULT NULL COMMENT '逻辑占用单编号',
    `oc_b_order_status`                    varchar(255) DEFAULT NULL COMMENT '零售发货单单据状态',
    `remark`                               varchar(255) DEFAULT NULL COMMENT '备注',
    `version`                              bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`                             char(1)      DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                              bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                           varchar(50)  DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                            varchar(50)  DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`                         datetime     DEFAULT NULL COMMENT '创建时间',
    `modifierid`                           bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`                        varchar(50)  DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`                         varchar(50)  DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`                         datetime     DEFAULT NULL COMMENT '修改时间',
    `deler_id`                             bigint(20) DEFAULT NULL COMMENT '作废人ID',
    `deler_ename`                          varchar(50)  DEFAULT NULL COMMENT '作废人姓名',
    `deler_name`                           varchar(50)  DEFAULT NULL COMMENT '作废人用户名',
    `del_time`                             datetime     DEFAULT NULL COMMENT '作废时间',
    PRIMARY KEY (`id`),
    KEY                                    `idx_sg_b_sto_store_update_share_store_item_01` (`sg_b_sto_store_update_share_store_id`),
    KEY                                    `idx_sg_b_sto_store_update_share_store_item_02` (`oc_b_order_bill_no`),
    KEY                                    `idx_sg_b_sto_store_update_share_store_item_03` (`sg_b_sto_out_bill_no`),
    KEY                                    `idx_sg_b_sto_store_update_share_store_item_04` (`tid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='未完成单据明细';


-- 分货单
ALTER TABLE `sg_b_share_allocation`
    ADD COLUMN `status_id` int(11) NULL DEFAULT 0 COMMENT '提交人ID',
ADD COLUMN `status_ename` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
ADD COLUMN `status_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
ADD COLUMN `status_time` datetime(0) NULL DEFAULT NULL COMMENT '提交时间',
ADD COLUMN `confirm_id` bigint(20) NULL DEFAULT NULL COMMENT '确认人',
ADD COLUMN `confirm_time` datetime(0) NULL DEFAULT NULL COMMENT '确认时间',
ADD COLUMN `is_auto_confirm` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'Y',
ADD COLUMN `rejected_reason` varchar(500) NULL COMMENT '驳回原因';

--分货退货单
ALTER TABLE `sg_b_share_allocation_return`
    ADD COLUMN `status_id` int(11) NULL DEFAULT 0 COMMENT '提交人ID',
ADD COLUMN `status_ename` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
ADD COLUMN `status_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
ADD COLUMN `status_time` datetime(0) NULL DEFAULT NULL COMMENT '提交时间';

--唯品会商品补货信息表
alter table `sg_b_share_vip_replenish`
    add column `replenish_id` bigint(20) DEFAULT NULL COMMENT '补货执行人ID',
add column `replenish_ename` varchar(50) DEFAULT NULL COMMENT '补货执行人姓名',
add column `replenish_name` varchar(50) DEFAULT NULL COMMENT '补货执行人用户名',
add column `replenish_time` datetime DEFAULT NULL COMMENT '执行补货时间';

-- 配销仓档案
ALTER TABLE `sg_c_sa_store`
    ADD COLUMN `is_auto_confirm` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'Y';


