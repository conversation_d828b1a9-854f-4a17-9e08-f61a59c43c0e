# 中台库存关系表视图
Create view v_sg_storage_relation as (
SELECT
	a.id,
	a.ad_client_id AS ad_client_id,
	a.ad_org_id AS ad_org_id,
	a.ecode AS warehouse_ecode,
	a.ename AS warehouse_ename,
	b.id AS cp_c_store_id,
	b.cp_c_store_ecode,
	b.cp_c_store_ename,
	c.id AS sg_c_share_store_id,
	c.ecode AS sg_c_share_store_ecode,
	c.ename AS sg_c_share_store_ename,
	d.id AS sg_c_sa_store_id,
	d.ecode AS sg_c_sa_store_ecode,
	d.ename AS sg_c_sa_store_ename,
	e.cp_c_shop_id,
	e.cp_c_shop_ecode,
	e.cp_c_shop_title
FROM
	r3_oms_sg.cp_c_phy_warehouse a
	INNER JOIN r3_oms_sg.cp_c_store b ON a.id = b.cp_c_phy_warehouse_id
	INNER JOIN r3_oms_sg.sg_c_share_store c ON b.sg_c_share_store_id = c.id
	INNER JOIN r3_oms_sg.sg_c_sa_store d ON c.id = d.sg_c_share_store_id
	INNER JOIN (
SELECT
	a.cp_c_shop_id,
	a.cp_c_shop_ecode,
	a.cp_c_shop_title,
	b.sg_c_sa_store_id
FROM
	r3_oms_sg.sg_c_channel_ratio_strategy a
	INNER JOIN r3_oms_sg.sg_c_channel_ratio_strategy_item b ON a.id = b.sg_c_channel_ratio_strategy_id
	AND a.isactive = 'Y'
	AND b.isactive = 'Y'
	UNION
SELECT DISTINCT
	a.cp_c_shop_id,
	a.cp_c_shop_ecode,
	a.cp_c_shop_ename AS cp_c_shop_title,
	b.sg_c_sa_store_id
FROM
	r3_oms_sg.sg_c_channel_sku_strategy a
	INNER JOIN r3_oms_sg.sg_c_channel_sku_strategy_sa_item b ON a.id = b.sg_c_channel_sku_strategy_id
	AND a.isactive = 'Y'
	AND b.isactive = 'Y'
WHERE
	now( ) >= a.begin_time
	AND now( ) <= a.end_time
	AND a.`status` = 2
	) e ON d.id = e.sg_c_sa_store_id);



-- 分货单查询明细
	CREATE VIEW v_sg_b_share_allocation AS
SELECT
`a`.`id` `id`,
`a`.`bill_no` `bill_no`,
`a`.`bill_date` `bill_date`,
`a`.`sg_c_share_store_id` `sg_c_share_store_id`,
`a`.`sg_c_share_store_ecode` `sg_c_share_store_ecode`,
`b`.`sg_c_sa_store_id` `sg_c_sa_store_id`,
`b`.`sg_c_sa_store_ecode` `sg_c_sa_store_ecode`,
`b`.`ps_c_pro_id` `ps_c_pro_id`,
`b`.`gbcode` `gbcode`,
`b`.`ps_c_sku_id` `ps_c_sku_id`,
`b`.`ps_c_spec1_ecode` `ps_c_spec1_ecode`,
`b`.`ps_c_spec1_ename` `ps_c_spec1_ename`,
`b`.`ps_c_spec2_ecode` `ps_c_spec2_ecode`,
`b`.`ps_c_spec2_ename` `ps_c_spec2_ename`,
`b`.`qty` `qty`,
`a`.`remark` `remark`,
`a`.`source_bill_type` `source_bill_type`,
`a`.`source_bill_no` `source_bill_no`,
`a`.`ownerid` `ownerid`,
`a`.`creationdate` `creationdate`,
`a`.`modifierid` `modifierid`,
`a`.`modifieddate` `modifieddate`,
`a`.`isactive` `isactive`,
`a`.`ad_client_id` `ad_client_id`,
`a`.`ad_org_id` `ad_org_id` ,
`a`.`status_id` `status_id` ,
`a`.`status_time` `status_time`
FROM
	(
		`r3_oms_sg`.`sg_b_share_allocation` `a`
		INNER JOIN `r3_oms_sg`.`sg_b_share_allocation_item` `b` ON ( `a`.`id` = `b`.`sg_b_share_allocation_id` )
	) UNION ALL
SELECT
	`a`.`id` `id`,
	`a`.`bill_no` `bill_no`,
	`a`.`bill_date` `bill_date`,
	`a`.`sg_c_share_store_id` `sg_c_share_store_id`,
	`a`.`sg_c_share_store_ecode` `sg_c_share_store_ecode`,
	`a`.`sg_c_sa_store_id` `sg_c_sa_store_id`,
	`a`.`sg_c_sa_store_ecode` `sg_c_sa_store_ecode`,
	`b`.`ps_c_pro_id` `ps_c_pro_id`,
	`b`.`gbcode` `gbcode`,
	`b`.`ps_c_sku_id` `ps_c_sku_id`,
	`b`.`ps_c_spec1_ecode` `ps_c_spec1_ecode`,
	`b`.`ps_c_spec1_ename` `ps_c_spec1_ename`,
	`b`.`ps_c_spec2_ecode` `ps_c_spec2_ecode`,
	`b`.`ps_c_spec2_ename` `ps_c_spec2_ename`,
	- `b`.`qty` `qty`,
	`a`.`remark` `remark`,
	`a`.`source_bill_type` `source_bill_type`,
	`a`.`source_bill_no` `source_bill_no`,
	`a`.`ownerid` `ownerid`,
	`a`.`creationdate` `creationdate`,
	`a`.`modifierid` `modifierid`,
	`a`.`modifieddate` `modifieddate`,
	`a`.`isactive` `isactive`,
	`a`.`ad_client_id` `ad_client_id`,
	`a`.`ad_org_id` `ad_org_id` ,
	`a`.`status_id` `status_id` ,
	`a`.`status_time` `status_time`
FROM
	(
		`r3_oms_sg`.`sg_b_share_allocation_return` `a`
	INNER JOIN `r3_oms_sg`.`sg_b_share_allocation_return_item` `b` ON ( `a`.`id` = `b`.`sg_b_share_allocation_return_id` )
	);