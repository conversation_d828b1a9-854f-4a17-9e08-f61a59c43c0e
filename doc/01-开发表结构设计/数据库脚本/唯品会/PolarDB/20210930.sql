/*实体仓档案新增是否允许合单字段*/
ALTER TABLE `cp_c_phy_warehouse`
    ADD `is_merge_order` char(1) DEFAULT 'Y' COMMENT '合并标记:  N 不允许  Y 允许 默认' AFTER `wms_control_warehouse`;

/*配销仓档案新加字段*/
ALTER TABLE `sg_c_sa_store`
    ADD `category` char(1) DEFAULT '0' COMMENT '类型: 0 非唯品会   1 唯品会' AFTER `type`;
/*共享占用单主表 新增字段*/
ALTER TABLE `sg_b_share_out`
    ADD `merge_mark` tinyint(1) DEFAULT '0' COMMENT '合并标记:  默认 0 false  1 true' AFTER `tot_qty_out`;

/*逻辑占用单主表 新增字段*/
ALTER TABLE `sg_b_sto_out`
    ADD `merge_mark` tinyint(1) DEFAULT '0' COMMENT '合并标记:  默认 0 false  1 true' AFTER `tot_qty_out`;


/*初始化类型*/
UPDATE `sg_c_sa_store`
SET `category` = '0'
WHERE `category` is null;

--已添加？
/*调拨单主表新增tms需要的字段*/
ALTER TABLE `sg_b_sto_transfer`
    ADD COLUMN `demand_type` char(2) NULL COMMENT '需求类型-tms接口',
ADD COLUMN `is_pass_tms` char(1) NULL COMMENT '是否传TMS',
ADD COLUMN `tms_business_type` varchar(20) NULL COMMENT 'TMS业务类型',
ADD COLUMN `tms_date` datetime(0) NULL COMMENT 'TMS要求提货日期',
ADD COLUMN `is_temporary_address` char(1) NULL COMMENT '是否使用临时地址',
ADD COLUMN `actual_list_date` datetime(0) NULL COMMENT '实际上市日期';

--已添加？
/*调拨单明细表新增tms需要的字段*/
ALTER TABLE `sg_b_sto_transfer_item`
    ADD COLUMN `price_sale` decimal(18, 4) NULL COMMENT '销售价-tms接口',
ADD COLUMN `discount_sale` decimal(18, 4) NULL COMMENT '销售折扣-tms接口';

CREATE TABLE `sg_b_sourcing_retry`
(
    `id`                bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`      bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`         bigint(20) DEFAULT '27' COMMENT '所属组织',
    `source_bill_id`    bigint(20) DEFAULT NULL COMMENT '来源单据id',
    `source_bill_no`    varchar(100)   DEFAULT NULL COMMENT '来源单据编号',
    `source_bill_type`  int(11) DEFAULT NULL COMMENT '来源单据类型',
    `retry_count`       int(11) DEFAULT '0' COMMENT '重试次数',
    `remark`            varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`           bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`          char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`           bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`        varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`         varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`      datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`        bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`     varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`      varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`      datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='寻源重试表';

create index idx_sg_b_sourcing_retry_01 on sg_b_sourcing_retry (source_bill_id);
create index idx_sg_b_sourcing_retry_02 on sg_b_sourcing_retry (source_bill_no);

--已添加？
/*虚拟批量逻辑调拨单*/
DROP TABLE IF EXISTS `sg_b_sto_batch_transfer`;
CREATE TABLE `sg_b_sto_batch_transfer`
(
    `id`                bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`      bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`         bigint(20) DEFAULT '27' COMMENT '所属组织',
    `bill_no`           varchar(100)   DEFAULT NULL COMMENT '单据编号',
    `bill_date`         date           DEFAULT NULL COMMENT '单据日期',
    `status`            int(11) DEFAULT NULL COMMENT '单据状态',
    `remark`            varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`           bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`          char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `is_auto_confirm`   char(1)        DEFAULT 'Y' COMMENT '是否提交确认调拨单',
    `tot_row_num`       int(11) DEFAULT '0' COMMENT '总行数',
    `tot_qty`           decimal(18, 4) DEFAULT '0.0000' COMMENT '总数量',
    `tot_qty_out`       decimal(18, 4) DEFAULT '0.0000' COMMENT '总实际调拨数量',
    `tot_amt`           decimal(18, 4) DEFAULT '0.0000' COMMENT '总吊牌金额',
    `fail_row_num`      decimal(18, 4) DEFAULT '0.0000' COMMENT '失败行数',
    `success_row_num`   decimal(18, 4) DEFAULT '0.0000' COMMENT '成功行数',
    `ownerid`           bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`        varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`         varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`      datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`        bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`     varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`      varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`      datetime       DEFAULT NULL COMMENT '修改时间',
    `deler_id`          bigint(20) DEFAULT NULL COMMENT '作废人ID',
    `deler_ename`       varchar(50)    DEFAULT NULL COMMENT '作废人姓名',
    `deler_name`        varchar(50)    DEFAULT NULL COMMENT '作废人用户名',
    `del_time`          datetime       DEFAULT NULL COMMENT '作废时间',
    `status_id`         bigint(20) DEFAULT NULL COMMENT '提交人ID',
    `status_ename`      varchar(50)    DEFAULT NULL COMMENT '提交人姓名',
    `status_name`       varchar(50)    DEFAULT NULL COMMENT '提交人用户名',
    `status_time`       datetime       DEFAULT NULL COMMENT '提交时间',
    `reserve_bigint01`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='虚拟批量逻辑调拨单';

--已添加？
/*虚拟批量逻辑调拨单明细*/
DROP TABLE IF EXISTS `sg_b_sto_batch_transfer_item`;
CREATE TABLE `sg_b_sto_batch_transfer_item`
(
    `id`                         bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`               bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                  bigint(20) DEFAULT '27' COMMENT '所属组织',
    `item_status`                int(11) DEFAULT NULL COMMENT '状态',
    `sg_b_sto_batch_transfer_id` bigint(20) DEFAULT NULL COMMENT '所属虚拟批量逻辑调拨单',
    `sg_b_sto_transfer_id`       bigint(20) DEFAULT NULL COMMENT '逻辑调拨单id',
    `sg_b_sto_transfer_bill_no`  varchar(50)    DEFAULT NULL COMMENT '逻辑调拨单编码',
    `sender_store_id`            bigint(20) DEFAULT NULL COMMENT '发货逻辑仓ID',
    `sender_store_ecode`         varchar(100)   DEFAULT NULL COMMENT '发货逻辑仓编码',
    `sender_store_ename`         varchar(255)   DEFAULT NULL COMMENT '发货逻辑仓名称',
    `sender_customer_id`         bigint(20) DEFAULT NULL COMMENT '发货经销商ID',
    `sender_customer_ecode`      varchar(50)    DEFAULT NULL COMMENT '发货经销商编码',
    `sender_customer_ename`      varchar(50)    DEFAULT NULL COMMENT '发货经销商编码',
    `receiver_store_id`          bigint(20) DEFAULT NULL COMMENT '收货逻辑仓ID',
    `receiver_store_ecode`       varchar(100)   DEFAULT NULL COMMENT '收货逻辑仓编码',
    `receiver_store_ename`       varchar(255)   DEFAULT NULL COMMENT '收货逻辑仓名称',
    `receiver_customer_id`       bigint(20) DEFAULT NULL COMMENT '收货经销商ID',
    `receiver_customer_ecode`    varchar(50)    DEFAULT NULL COMMENT '收货经销商编码',
    `receiver_customer_ename`    varchar(50)    DEFAULT NULL COMMENT '收货经销商编码',
    `ps_c_sku_id`                bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`             varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `gbcode`                     varchar(100)   DEFAULT NULL COMMENT '国标码',
    `ps_c_brand_id`              bigint(20) DEFAULT NULL COMMENT '品牌ID',
    `ps_c_pro_id`                bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`             varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`             varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `ps_c_spec1_id`              bigint(20) DEFAULT NULL COMMENT '规格1ID',
    `ps_c_spec1_ecode`           varchar(100)   DEFAULT NULL COMMENT '规格1编码',
    `ps_c_spec1_ename`           varchar(100)   DEFAULT NULL COMMENT '规格1名称',
    `ps_c_spec2_id`              bigint(20) DEFAULT NULL COMMENT '规格2ID',
    `ps_c_spec2_ecode`           varchar(100)   DEFAULT NULL COMMENT '规格2编码',
    `ps_c_spec2_ename`           varchar(100)   DEFAULT NULL COMMENT '规格2名称',
    `ps_c_spec3_id`              bigint(20) DEFAULT NULL COMMENT '规格3ID',
    `ps_c_spec3_ecode`           varchar(100)   DEFAULT NULL COMMENT '规格3编码',
    `ps_c_spec3_ename`           varchar(100)   DEFAULT NULL COMMENT '规格3名称',
    `qty`                        decimal(18, 4) DEFAULT '0.0000' COMMENT '数量',
    `qty_out`                    decimal(18, 4) DEFAULT '0.0000' COMMENT '实际调拨数量',
    `amt`                        decimal(18, 4) DEFAULT '0.0000' COMMENT '吊牌金额',
    `price_list`                 decimal(18, 4) DEFAULT '0.0000' COMMENT '吊牌价',
    `fail_reason`                varchar(1000)  DEFAULT NULL COMMENT '失败原因',
    `tms_date`                   datetime       DEFAULT NULL COMMENT 'TMS要求提货日期',
    `cp_c_tranway_assign_id`     bigint(20) DEFAULT NULL COMMENT '运输类型',
    `demand_type`                int(11) DEFAULT NULL COMMENT '需求类型',
    `receiver_address`           varchar(500)   DEFAULT NULL COMMENT '收货地址',
    `is_temporary_address`       char(1)        DEFAULT NULL COMMENT '是否使用临时地址',
    `remark`                     varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`                    bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`                   char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                    bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                 varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                  varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`               datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                 bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`              varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`               varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`               datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='虚拟批量逻辑调拨单明细';

ALTER TABLE `sg_b_sto_out_notices`
    ADD COLUMN `out_bill_no` varchar(100) DEFAULT NULL COMMENT '出仓单号',
ADD COLUMN `po_no` varchar(100) DEFAULT NULL  COMMENT 'PO号',
ADD COLUMN `vipcom_warehouse_id` bigint(20) DEFAULT NULL  COMMENT '唯品会仓库ID',
ADD COLUMN `vipcom_warehouse_ecode` varchar(100) DEFAULT NULL  COMMENT '唯品会仓库编码',
ADD COLUMN `vipcom_warehouse_ename` varchar(255) DEFAULT NULL  COMMENT '唯品会仓库名称';

--已添加？
ALTER TABLE `sg_b_sto_out_notices` ADD COLUMN (
`sink_area` varchar(50) DEFAULT NULL COMMENT '下沉仓区域'
);

CREATE TABLE `sg_c_channel_storage_sync_gradient_strategy`
(

    `id`                bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`      bigint(20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`         bigint(20) DEFAULT 27 COMMENT '所属组织',
    `cp_c_shop_id`      bigint(20) DEFAULT NULL COMMENT '平台店铺ID',
    `cp_c_shop_title`   varchar(255)   DEFAULT NULL COMMENT '平台店铺标题',
    `channel_name`      varchar(100)   DEFAULT NULL COMMENT '主控渠道',
    `product_level`     varchar(50)    DEFAULT NULL COMMENT '商品级别',
    `qty_begin`         decimal(18, 4) DEFAULT 0 COMMENT '起始量',
    `qty_end`           decimal(18, 4) DEFAULT null COMMENT '结束量',
    `qty_standard`      decimal(18, 4) DEFAULT null COMMENT '标准库存量',
    `ratio`             decimal(18, 4) DEFAULT null COMMENT '同步比例',
    `remark`            varchar(255)   DEFAULT null COMMENT '备注',
    `version`           bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`          char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`           bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`        varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`         varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`      datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`        bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`     varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`      varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`      datetime       DEFAULT NULL COMMENT '修改时间',
    `deler_id`          bigint(20) DEFAULT NULL COMMENT '作废人ID',
    `deler_ename`       varchar(50)    DEFAULT NULL COMMENT '作废人姓名',
    `deler_name`        varchar(50)    DEFAULT NULL COMMENT '作废人用户名',
    `del_time`          datetime       DEFAULT NULL COMMENT '作废时间',
    `reserve_bigint01`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='平台库存同步梯度策略';

CREATE TABLE `sg_c_channel_storage_sync_pro_gradient_strategy`
(

    `id`                bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`      bigint(20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`         bigint(20) DEFAULT 27 COMMENT '所属组织',
    `cp_c_shop_id`      bigint(20) DEFAULT NULL COMMENT '平台店铺ID',
    `cp_c_shop_title`   varchar(255)   DEFAULT NULL COMMENT '平台店铺标题',
    `ps_c_sku_id`       bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`    varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `ps_c_pro_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`    varchar(100)   DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`    varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `qty_begin`         decimal(18, 4) DEFAULT 0 COMMENT '起始量',
    `qty_end`           decimal(18, 4) DEFAULT null COMMENT '结束量',
    `qty_standard`      decimal(18, 4) DEFAULT null COMMENT '标准库存量',
    `ratio`             decimal(18, 4) DEFAULT null COMMENT '同步比例',
    `remark`            varchar(255)   DEFAULT null COMMENT '备注',
    `version`           bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`          char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`           bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`        varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`         varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`      datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`        bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`     varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`      varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`      datetime       DEFAULT NULL COMMENT '修改时间',
    `deler_id`          bigint(20) DEFAULT NULL COMMENT '作废人ID',
    `deler_ename`       varchar(50)    DEFAULT NULL COMMENT '作废人姓名',
    `deler_name`        varchar(50)    DEFAULT NULL COMMENT '作废人用户名',
    `del_time`          datetime       DEFAULT NULL COMMENT '作废时间',
    `reserve_bigint01`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='平台商品库存同步梯度策略';

CREATE TABLE `sg_b_sto_batch_transfer_pro_item`
(
    `id`                         bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`               bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                  bigint(20) DEFAULT '27' COMMENT '所属组织',
    `item_status`                int(11) DEFAULT NULL COMMENT '状态',
    `sg_b_sto_batch_transfer_id` bigint(20) DEFAULT NULL COMMENT '所属虚拟批量逻辑调拨单',
    `sender_store_id`            bigint(20) DEFAULT NULL COMMENT '发货逻辑仓ID',
    `sender_store_ecode`         varchar(100)   DEFAULT NULL COMMENT '发货逻辑仓编码',
    `sender_store_ename`         varchar(255)   DEFAULT NULL COMMENT '发货逻辑仓名称',
    `receiver_store_id`          bigint(20) DEFAULT NULL COMMENT '收货逻辑仓ID',
    `receiver_store_ecode`       varchar(100)   DEFAULT NULL COMMENT '收货逻辑仓编码',
    `receiver_store_ename`       varchar(255)   DEFAULT NULL COMMENT '收货逻辑仓名称',
    `ps_c_pro_ecode`             varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`             varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `qty`                        decimal(18, 4) DEFAULT '0.0000' COMMENT '数量',
    `tms_date`                   datetime       DEFAULT NULL COMMENT 'TMS要求提货日期',
    `demand_type`                int(11) DEFAULT NULL COMMENT '需求类型',
    `receiver_address`           varchar(500)   DEFAULT NULL COMMENT '收货地址',
    `is_temporary_address`       char(1)        DEFAULT NULL COMMENT '是否使用临时地址',
    `remark`                     varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`                    bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`                   char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                    bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                 varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                  varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`               datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                 bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`              varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`               varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`               datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='按款导入明细';

create index idx_sg_b_sto_batch_transfer_pro_item_01 on sg_b_sto_batch_transfer_pro_item (sg_b_sto_batch_transfer_id);

CREATE TABLE `sg_b_share_sa_batch_transfer`
(
    `id`                bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`      bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`         bigint(20) DEFAULT '27' COMMENT '所属组织',
    `bill_no`           varchar(100)   DEFAULT NULL COMMENT '单据编号',
    `bill_date`         date           DEFAULT NULL COMMENT '单据日期',
    `tot_row_num`       int(11) DEFAULT '0' COMMENT '总行数',
    `success_row_num`   int(11) DEFAULT '0' COMMENT '成功行数',
    `fail_row_num`      int(11) DEFAULT '0' COMMENT '失败(未处理)行数',
    `tot_qty`           decimal(18, 4) DEFAULT '0.0000' COMMENT '总调拨数量',
    `remark`            varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`           bigint(20) DEFAULT NULL COMMENT '版本号',
    `bill_status`       int(11) DEFAULT NULL COMMENT '单据状态(未提交、已提交、已作废)',
    `status`            int(11) DEFAULT NULL COMMENT '单据状态',
    `ownerid`           bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`        varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`         varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`      datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`        bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`     varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`      varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`      datetime       DEFAULT NULL COMMENT '修改时间',
    `status_id`         int(11) DEFAULT '0' COMMENT '提交人ID',
    `status_ename`      varchar(50)    DEFAULT NULL COMMENT '提交人姓名',
    `status_name`       varchar(50)    DEFAULT NULL COMMENT '提交人用户名',
    `status_time`       datetime       DEFAULT NULL COMMENT '提交时间',
    `deler_id`          bigint(20) DEFAULT NULL COMMENT '作废人ID',
    `deler_ename`       varchar(50)    DEFAULT NULL COMMENT '作废人姓名',
    `deler_name`        varchar(50)    DEFAULT NULL COMMENT '作废人用户名',
    `del_time`          datetime       DEFAULT NULL COMMENT '作废时间',
    `isactive`          char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `reserve_bigint01`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配销仓调拨单批量导入';

create index idx_sg_b_share_sa_batch_transfer_01 on sg_b_share_sa_batch_transfer (bill_no);
create index idx_sg_b_share_sa_batch_transfer_02 on sg_b_share_sa_batch_transfer (bill_date);

CREATE TABLE `sg_b_share_sa_batch_transfer_item`
(
    `id`                              bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`                    bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                       bigint(20) DEFAULT '27' COMMENT '所属组织',
    `sg_b_share_sa_transfer_id`       bigint(20) DEFAULT NULL COMMENT '配销调拨单ID',
    `sg_b_share_sa_transfer_bill_no`  varchar(100)   DEFAULT NULL COMMENT '配销调拨单单号',
    `sg_b_share_sa_batch_transfer_id` bigint(20) DEFAULT NULL COMMENT '配销调拨单批量导入ID',
    `sender_sa_store_id`              bigint(20) DEFAULT NULL COMMENT '配销发货仓ID',
    `sender_sa_store_ecode`           varchar(100)   DEFAULT NULL COMMENT '配销发货仓编码',
    `sender_sa_store_ename`           varchar(255)   DEFAULT NULL COMMENT '配销发货仓名称',
    `receiver_sa_store_id`            bigint(20) DEFAULT NULL COMMENT '配销收货仓ID',
    `receiver_sa_store_ecode`         varchar(100)   DEFAULT NULL COMMENT '配销收货仓编码',
    `receiver_sa_store_ename`         varchar(255)   DEFAULT NULL COMMENT '配销收货仓名称',
    `ps_c_sku_id`                     bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`                  varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `ps_c_pro_id`                     bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`                  varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`                  varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `gbcode`                          varchar(100)   DEFAULT NULL COMMENT '国标码',
    `qty`                             decimal(18, 4) DEFAULT '0.0000' COMMENT '调拨数量',
    `remark`                          varchar(255)   DEFAULT NULL COMMENT '备注',
    `item_status`                     int(11) DEFAULT NULL COMMENT '状态',
    `fail_reason`                     varchar(255)   default null comment '失败原因',
    `ownerid`                         bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                      varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                       varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`                    datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                      bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`                   varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`                    varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`                    datetime       DEFAULT NULL COMMENT '修改时间',
    `isactive`                        char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `reserve_bigint01`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配销仓调拨单批量导入明细';

create index idx_sg_b_share_sa_batch_transfer_item_01 on sg_b_share_sa_batch_transfer_item (sg_b_share_sa_transfer_id);
create index idx_sg_b_share_sa_batch_transfer_item_02 on sg_b_share_sa_batch_transfer_item (sg_b_share_sa_transfer_bill_no);
create index idx_sg_b_share_sa_batch_transfer_item_03 on sg_b_share_sa_batch_transfer_item (sg_b_share_sa_batch_transfer_id);

CREATE TABLE `sg_c_sync_share_storage_result`
(
    `id`                         bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`               bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                  bigint(20) DEFAULT '27' COMMENT '所属组织',
    `sg_c_share_store_id`        bigint(20) DEFAULT NULL COMMENT '共享仓ID',
    `sg_c_share_store_ecode`     varchar(100)   DEFAULT NULL COMMENT '共享仓编码',
    `sg_c_share_store_ename`     varchar(255)   DEFAULT NULL COMMENT '共享仓名称',
    `bill_date`                  date           DEFAULT NULL COMMENT '单据日期',
    `bill_no`                    varchar(100)   DEFAULT NULL COMMENT '单据编号',
    `ps_c_sku_ecode`             varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `ps_c_pro_ecode`             varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `qty_sale_available`         decimal(18, 4) DEFAULT '0.0000' COMMENT '中台可售库存',
    `qty_sale_available_spec`    decimal(18, 4) DEFAULT '0.0000' COMMENT '特殊中台可售库存: 去除不允许退回的非唯品配销可用',
    `qty_share`                  decimal(18, 4) DEFAULT '0.0000' COMMENT '中台共享库存',
    `qty_pro_sys_storage`        decimal(18, 4) DEFAULT '0.0000' COMMENT '商品系统库存',
    `qty_pro_sys_share_storage`  decimal(18, 4) DEFAULT '0.0000' COMMENT '商品系统共享库存',
    `qty_pro_sys_retain_storage` decimal(18, 4) DEFAULT '0.0000' COMMENT '商品系统保留库存',
    `qty_final_share`            decimal(18, 4) DEFAULT '0.0000' COMMENT '最终共享量',
    `remark`                     varchar(255) DEFAULT NULL COMMENT '备注',
    `isactive`                   char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                    bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownername`                  varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`               datetime       DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifierid`                 bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifiername`               varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`               datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`           bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`          varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`          decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_sg_c_sync_share_storage_result_07` (`bill_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='欧睿平台库存同步共享库存结果表';

create index idx_sg_c_sync_share_storage_result_01 on sg_c_sync_share_storage_result (sg_c_share_store_id);
create index idx_sg_c_sync_share_storage_result_02 on sg_c_sync_share_storage_result (sg_c_share_store_ecode);
create index idx_sg_c_sync_share_storage_result_03 on sg_c_sync_share_storage_result (bill_date);
create index idx_sg_c_sync_share_storage_result_04 on sg_c_sync_share_storage_result (bill_no);
create index idx_sg_c_sync_share_storage_result_05 on sg_c_sync_share_storage_result (ps_c_sku_ecode);
create index idx_sg_c_sync_share_storage_result_06 on sg_c_sync_share_storage_result (ps_c_pro_ecode);

create table sg_c_channel_storage_sync_gradient_strategy_log
(
    id                bigint not null comment 'ID',
    ad_client_id      bigint   default 37 null comment '所属公司',
    ad_org_id         bigint   default 27 null comment '所属组织',
    operation_type    varchar(50) null comment '操作类型',
    record_id         bigint null comment '修改对象ID',
    update_model_name varchar(200) null comment '修改对象名称',
    mod_content       varchar(500) null comment '修改内容',
    before_data       varchar(1000) null comment '操作前',
    after_data        varchar(1000) null comment '操作后',
    isactive          char     default 'Y' null comment '可用',
    ownerid           bigint null comment '创建人',
    ownerename        varchar(50) null comment '创建人姓名',
    ownername         varchar(50) null comment '操作人',
    creationdate      datetime default CURRENT_TIMESTAMP null comment '操作时间',
    modifierid        bigint null comment '修改人',
    modifierename     varchar(50) null comment '修改人姓名',
    modifiername      varchar(50) null comment '修改人名称',
    modifieddate      datetime default CURRENT_TIMESTAMP null comment '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='平台库存同步梯度策略日志表';

create index idx_sg_c_channel_storage_sync_gradient_strategy_log_01
    on sg_c_channel_storage_sync_gradient_strategy_log (record_id);

create table sg_c_channel_storage_sync_pro_gradient_strategy_log
(
    id                bigint not null comment 'ID',
    ad_client_id      bigint   default 37 null comment '所属公司',
    ad_org_id         bigint   default 27 null comment '所属组织',
    operation_type    varchar(50) null comment '操作类型',
    record_id         bigint null comment '修改对象ID',
    update_model_name varchar(200) null comment '修改对象名称',
    mod_content       varchar(500) null comment '修改内容',
    before_data       varchar(1000) null comment '操作前',
    after_data        varchar(1000) null comment '操作后',
    isactive          char     default 'Y' null comment '可用',
    ownerid           bigint null comment '创建人',
    ownerename        varchar(50) null comment '创建人姓名',
    ownername         varchar(50) null comment '操作人',
    creationdate      datetime default CURRENT_TIMESTAMP null comment '操作时间',
    modifierid        bigint null comment '修改人',
    modifierename     varchar(50) null comment '修改人姓名',
    modifiername      varchar(50) null comment '修改人名称',
    modifieddate      datetime default CURRENT_TIMESTAMP null comment '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='平台商品库存同步梯度策略日志表';

create index idx_sg_c_channel_storage_sync_pro_gradient_strategy_log_01
    on sg_c_channel_storage_sync_pro_gradient_strategy_log (record_id);

CREATE TABLE `sg_c_customer_store`
(
    `id`               bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`     bigint(20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`        bigint(20) DEFAULT 27 COMMENT '所属组织',
    `customer_store`   varchar(100) DEFAULT NULL COMMENT '经销商店仓编码',
    `cp_c_store_id`    bigint(20) DEFAULT NULL COMMENT '逻辑仓ID',
    `cp_c_store_ecode` varchar(100) DEFAULT NULL COMMENT '逻辑仓编码',
    `cp_c_store_ename` varchar(255) DEFAULT NULL COMMENT '逻辑仓名称',
    `remark`           varchar(255) DEFAULT NULL COMMENT '备注',
    `version`          bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`         char(1)      DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`          bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`       varchar(50)  DEFAULT NULL COMMENT '创建人姓名',
    `ownername`        varchar(50)  DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`     datetime     DEFAULT NULL COMMENT '创建时间',
    `modifierid`       bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`    varchar(50)  DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`     varchar(50)  DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`     datetime     DEFAULT NULL COMMENT '修改时间',
    `deler_id`         bigint(20) DEFAULT NULL COMMENT '作废人ID',
    `deler_ename`      varchar(50)  DEFAULT NULL COMMENT '作废人姓名',
    `deler_name`       varchar(50)  DEFAULT NULL COMMENT '作废人用户名',
    `del_time`         datetime     DEFAULT NULL COMMENT '作废时间',
    PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='经销商店仓对照表';

create index idx_sg_c_customer_store_01 on sg_c_customer_store (cp_c_store_id);
create index idx_sg_c_customer_store_02 on sg_c_customer_store (cp_c_store_ecode);

CREATE TABLE `sg_b_customer_storage`
(

    `id`                bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`      bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`         bigint(20) DEFAULT '27' COMMENT '所属组织',
    `cp_c_store_id`     bigint(20) DEFAULT NULL COMMENT '逻辑仓ID',
    `cp_c_store_ecode`  varchar(100)   DEFAULT NULL COMMENT '逻辑仓编码',
    `cp_c_store_ename`  varchar(255)   DEFAULT NULL COMMENT '逻辑仓名称',
    `ps_c_sku_id`       bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`    varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `barcode`           varchar(100)   DEFAULT NULL COMMENT '国标码',
    `ps_c_brand_id`     bigint(20) DEFAULT NULL COMMENT '品牌',
    `ps_c_pro_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`    varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`    varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `qty_storage`       decimal(18, 4) DEFAULT 0 COMMENT '在库数量',
    `remark`            varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`           bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`          char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`           bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`        varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`         varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`      datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`        bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`     varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`      varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`      datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='经销商逻辑仓库存表';

create index idx_sg_b_customer_storage_01 on sg_b_customer_storage (cp_c_store_id);
create index idx_sg_b_customer_storage_02 on sg_b_customer_storage (cp_c_store_ecode);
create index idx_sg_b_customer_storage_03 on sg_b_customer_storage (ps_c_sku_id);
create index idx_sg_b_customer_storage_04 on sg_b_customer_storage (ps_c_sku_ecode);
create index idx_sg_b_customer_storage_05 on sg_b_customer_storage (ps_c_pro_id);
create index idx_sg_b_customer_storage_06 on sg_b_customer_storage (ps_c_pro_ecode);

CREATE TABLE `sg_b_customer_storage_change_ftp`
(

    `id`                bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`      bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`         bigint(20) DEFAULT '27' COMMENT '所属组织',
    `cp_c_store_id`     bigint(20) DEFAULT NULL COMMENT '逻辑仓ID',
    `cp_c_store_ecode`  varchar(100)   DEFAULT NULL COMMENT '逻辑仓编码',
    `cp_c_store_ename`  varchar(255)   DEFAULT NULL COMMENT '逻辑仓名称',
    `ps_c_sku_id`       bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`    varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `barcode`           varchar(100)   DEFAULT NULL COMMENT '国标码',
    `ps_c_brand_id`     bigint(20) DEFAULT NULL COMMENT '品牌',
    `ps_c_pro_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`    varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`    varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `qty_storage`       decimal(18, 4) DEFAULT 0 COMMENT '在库数量',
    `remark`            varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`           bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`          char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`           bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`        varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`         varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`      datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`        bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`     varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`      varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`      datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='经销商逻辑仓库存变动流水表';

create index idx_sg_b_customer_storage_change_ftp_01 on sg_b_customer_storage_change_ftp (cp_c_store_id);
create index idx_sg_b_customer_storage_change_ftp_02 on sg_b_customer_storage_change_ftp (cp_c_store_ecode);
create index idx_sg_b_customer_storage_change_ftp_03 on sg_b_customer_storage_change_ftp (ps_c_sku_id);
create index idx_sg_b_customer_storage_change_ftp_04 on sg_b_customer_storage_change_ftp (ps_c_sku_ecode);
create index idx_sg_b_customer_storage_change_ftp_05 on sg_b_customer_storage_change_ftp (ps_c_pro_id);
create index idx_sg_b_customer_storage_change_ftp_06 on sg_b_customer_storage_change_ftp (ps_c_pro_ecode);

CREATE TABLE `sg_b_share_vip_replenish_strategy`
(
    `id`                bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`      bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`         bigint(20) DEFAULT '27' COMMENT '所属组织',
    `cp_c_shop_id`      bigint(20) DEFAULT NULL COMMENT '平台店铺ID',
    `cp_c_shop_ecode`   varchar(100)   DEFAULT NULL COMMENT '平台店铺编码',
    `cp_c_shop_title`   varchar(255)   DEFAULT NULL COMMENT '平台店铺名称',
    `bill_no`           varchar(100)   DEFAULT NULL COMMENT '单据编码',
    `begin_time`        datetime       DEFAULT NULL COMMENT '开始时间',
    `end_time`          datetime       DEFAULT NULL COMMENT '结束时间',
    `status`            int(11) DEFAULT '1' COMMENT '处理状态:1-未提交；2-已提交；3-已结案；4-已作废',
    `remark`            varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`           bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`          char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`           bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`        varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`         varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`      datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`        bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`     varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`      varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`      datetime       DEFAULT NULL COMMENT '修改时间',
    `status_id`         bigint(20) DEFAULT NULL COMMENT '提交人ID',
    `status_ename`      varchar(50)    DEFAULT NULL COMMENT '提交人姓名',
    `status_name`       varchar(50)    DEFAULT NULL COMMENT '提交人用户名',
    `status_time`       datetime       DEFAULT NULL COMMENT '提交时间',
    `uncheck_id`        bigint(20) DEFAULT NULL COMMENT '取消提交人ID',
    `uncheck_ename`     varchar(50)    DEFAULT NULL COMMENT '取消提交人姓名',
    `uncheck_name`      varchar(50)    DEFAULT NULL COMMENT '取消提交人用户名',
    `uncheck_time`      datetime       DEFAULT NULL COMMENT '取消提交时间',
    `deler_id`          bigint(20) DEFAULT NULL COMMENT '作废人ID',
    `deler_ename`       varchar(50)    DEFAULT NULL COMMENT '作废人姓名',
    `deler_name`        varchar(50)    DEFAULT NULL COMMENT '作废人用户名',
    `del_time`          datetime       DEFAULT NULL COMMENT '作废时间',
    `closer_id`         bigint(20) DEFAULT NULL COMMENT '结案人id',
    `closer_name`       varchar(50)    DEFAULT NULL COMMENT '结案人用户名',
    `closer_ename`      varchar(50)    DEFAULT NULL COMMENT '结案人姓名',
    `close_time`        datetime       DEFAULT NULL COMMENT '结案人时间',
    `reserve_bigint01`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='唯品会商品补货策略表';

create index idx_sg_b_share_vip_replenish_strategy_01 on sg_b_share_vip_replenish_strategy (cp_c_shop_id);
create index idx_sg_b_share_vip_replenish_strategy_02 on sg_b_share_vip_replenish_strategy (cp_c_shop_ecode);
create index idx_sg_b_share_vip_replenish_strategy_03 on sg_b_share_vip_replenish_strategy (bill_no);
create index idx_sg_b_share_vip_replenish_strategy_04 on sg_b_share_vip_replenish_strategy (begin_time, end_time);

CREATE TABLE `sg_b_share_vip_replenish_strategy_item`
(
    `id`                                   bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`                         bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                            bigint(20) DEFAULT '27' COMMENT '所属组织',
    `sg_b_share_vip_replenish_strategy_id` bigint(20) DEFAULT NULL COMMENT '唯品会商品补货策略表主表id',
    `sg_c_sa_store_id`                     bigint(20) DEFAULT NULL COMMENT 'SA仓ID',
    `sg_c_sa_store_ecode`                  varchar(100)   DEFAULT NULL COMMENT 'SA仓编码',
    `sg_c_sa_store_ename`                  varchar(255)   DEFAULT NULL COMMENT 'SA仓名称',
    `sg_c_share_store_id`                  bigint(20) DEFAULT NULL COMMENT '共享仓ID',
    `sg_c_share_store_ecode`               varchar(100)   DEFAULT NULL COMMENT '共享仓编码',
    `sg_c_share_store_ename`               varchar(255)   DEFAULT NULL COMMENT '共享仓名称',
    `priority`                             decimal(18, 4) DEFAULT NULL COMMENT '优先级',
    `remark`                               varchar(255)   DEFAULT NULL COMMENT '备注',
    `isactive`                             char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                              bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                           varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                            varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`                         datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                           bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`                        varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`                         varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`                         datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='唯品会商品补货策略明细表';

create index idx_sg_b_share_vip_replenish_strategy_itemy_01 on sg_b_share_vip_replenish_strategy_item (sg_b_share_vip_replenish_strategy_id);
create index idx_sg_b_share_vip_replenish_strategy_itemy_02 on sg_b_share_vip_replenish_strategy_item (sg_c_sa_store_id);
create index idx_sg_b_share_vip_replenish_strategy_itemy_03 on sg_b_share_vip_replenish_strategy_item (sg_c_sa_store_ecode);
create index idx_sg_b_share_vip_replenish_strategy_itemy_04 on sg_b_share_vip_replenish_strategy_item (sg_c_share_store_id);
create index idx_sg_b_share_vip_replenish_strategy_itemy_05 on sg_b_share_vip_replenish_strategy_item (sg_c_share_store_ename);

CREATE TABLE `sg_b_share_vip_replenish`
(
    `id`                   bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`         bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`            bigint(20) DEFAULT '27' COMMENT '所属组织',
    `bill_no`              varchar(100)   DEFAULT NULL COMMENT '单据编码',
    `cp_c_shop_id`         bigint(20) DEFAULT NULL COMMENT '平台店铺ID',
    `cp_c_shop_ecode`      varchar(100)   DEFAULT NULL COMMENT '平台店铺编码',
    `cp_c_shop_title`      varchar(255)   DEFAULT NULL COMMENT '平台店铺名称',
    `ps_c_sku_id`          bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`       varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `forcode`              varchar(255)   DEFAULT NULL COMMENT '国际码',
    `qty_need_replenish`   decimal(18, 4) DEFAULT '0.0000' COMMENT '需补货数量',
    `qty_actual_replenish` decimal(18, 4) DEFAULT '0.0000' COMMENT '实际已补货数量',
    `is_processed`         char(1)        DEFAULT 'N' COMMENT '是否处理',
    `remark`               varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`              bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`             char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`              bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`           varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`            varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`         datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`           bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`        varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`         varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`         datetime       DEFAULT NULL COMMENT '修改时间',
    `deler_id`             bigint (20) DEFAULT NULL COMMENT '作废人ID',
    `deler_ename`          varchar(50)    DEFAULT NULL COMMENT '作废人姓名',
    `deler_name`           varchar(50)    DEFAULT NULL COMMENT '作废人用户名',
    `del_time`             datetime       DEFAULT NULL COMMENT '作废时间',
    `reserve_bigint01`     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_varchar01`    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_decimal01`    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    PRIMARY KEY (`id`),
    KEY                    `idx_sg_b_share_vip_replenish_01` (`cp_c_shop_id`) USING BTREE,
    KEY                    `idx_sg_b_share_vip_replenish_02` (`forcode`) USING BTREE,
    KEY                    `idx_sg_b_share_vip_replenish_03` (`ps_c_sku_id`) USING BTREE,
    KEY                    `idx_sg_b_share_vip_replenishg_04` (`bill_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='唯品会商品补货信息表';

create index idx_sg_b_share_vip_replenish_05 on sg_b_share_vip_replenish (cp_c_shop_ecode);
create index idx_sg_b_share_vip_replenish_06 on sg_b_share_vip_replenish (ps_c_sku_ecode);

CREATE TABLE `sg_c_product_system_share_log`
(
    `id`                  bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`        bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`           bigint(20) DEFAULT '27' COMMENT '所属组织',
    `bill_date`           date           DEFAULT NULL COMMENT '单据日期',
    `sg_c_share_store_id` bigint(20) DEFAULT NULL COMMENT '聚合仓id',
    `cp_c_store_id`       bigint(20) DEFAULT NULL COMMENT '逻辑仓id',
    `cp_c_store_ecode`    varchar(100)   DEFAULT NULL COMMENT '逻辑仓编码',
    `cp_c_store_ename`    varchar(255)   DEFAULT NULL COMMENT '逻辑仓名称',
    `ps_c_sku_id`         bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`      varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `ps_c_pro_id`         bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`      varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`      varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `qty`                 decimal(18, 4) DEFAULT '0.0000' COMMENT '在库库存',
    `share_qty`           decimal(18, 4) DEFAULT '0.0000' COMMENT '共享库存',
    `min_qty`             decimal(18, 4) DEFAULT '0.0000' COMMENT '保留库存',
    `sync_status`         int(11) DEFAULT '0' COMMENT '同步状态',
    `sync_time`           datetime       DEFAULT NULL COMMENT '同步时间',
    `sync_failed_count`   int(11) DEFAULT '0' COMMENT '同步失败次数',
    `remark`              varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`             bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`            char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`             bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`          varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`           varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`        datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`          bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`       varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`        varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`        datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`    bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`    bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`    bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_varchar01`   varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`   varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`   varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_decimal01`   decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`   decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`   decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    PRIMARY KEY (`id`),
    KEY                   `idx_sg_c_product_system_share_log_01` (`cp_c_store_ecode`) USING BTREE,
    KEY                   `idx_sg_c_product_system_share_log_02` (`ps_c_sku_ecode`) USING BTREE,
    KEY                   `idx_sg_c_product_system_share_log_03` (`ps_c_sku_id`) USING BTREE,
    KEY                   `idx_sg_c_product_system_share_log_04` (`bill_date`) USING BTREE,
    KEY                   `idx_sg_c_product_system_share_log_05` (`sg_c_share_store_id`) USING BTREE,
    KEY                   `idx_sg_c_product_system_share_log_06` (`sg_c_share_store_id`,`ps_c_sku_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品系统共享日志表';

CREATE TABLE `sg_b_share_out_item_log`
(
    `id`                    bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`          bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`             bigint(20) DEFAULT '27' COMMENT '所属组织',
    `sg_b_share_out_id`     bigint(20) DEFAULT NULL COMMENT '共享占用单ID',
    `source_bill_item_id`   bigint(20) DEFAULT NULL COMMENT '来源单据明细ID',
    `sg_c_sa_store_id`      bigint(20) DEFAULT NULL COMMENT 'SA仓ID',
    `sg_c_sa_store_ecode`   varchar(100)   DEFAULT NULL COMMENT 'SA仓编码',
    `sg_c_sa_store_ename`   varchar(255)   DEFAULT NULL COMMENT 'SA仓名称',
    `sg_c_share_pool_id`    bigint(20) DEFAULT NULL COMMENT '共享池ID',
    `sg_c_share_pool_ecode` varchar(20)    DEFAULT NULL COMMENT '共享池编码',
    `sg_c_share_pool_ename` varchar(255)   DEFAULT NULL COMMENT '共享池名称',
    `qty`                   decimal(18, 4) DEFAULT '0.0000' COMMENT '原单数量',
    `qty_preout`            decimal(18, 4) DEFAULT '0.0000' COMMENT '占用数量',
    `qty_out`               decimal(18, 4) DEFAULT '0.0000' COMMENT '发货数量',
    `ps_c_sku_id`           bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`        varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `gbcode`                varchar(100)   DEFAULT NULL COMMENT '国标码',
    `ps_c_brand_id`         bigint(20) DEFAULT NULL COMMENT '品牌ID',
    `ps_c_pro_id`           bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`        varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`        varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `ps_c_spec1_id`         bigint(20) DEFAULT NULL COMMENT '规格1ID',
    `ps_c_spec1_ecode`      varchar(100)   DEFAULT NULL COMMENT '规格1编码',
    `ps_c_spec1_ename`      varchar(100)   DEFAULT NULL COMMENT '规格1名称',
    `ps_c_spec2_id`         bigint(20) DEFAULT NULL COMMENT '规格2ID',
    `ps_c_spec2_ecode`      varchar(100)   DEFAULT NULL COMMENT '规格2编码',
    `ps_c_spec2_ename`      varchar(100)   DEFAULT NULL COMMENT '规格2名称',
    `ps_c_spec3_id`         bigint(20) DEFAULT NULL COMMENT '规格3ID',
    `ps_c_spec3_ecode`      varchar(100)   DEFAULT NULL COMMENT '规格3编码',
    `ps_c_spec3_ename`      varchar(100)   DEFAULT NULL COMMENT '规格3名称',
    `price_list`            decimal(18, 4) DEFAULT '0.0000' COMMENT '吊牌价',
    `version`               bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`              char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`               bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`            varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`             varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`          datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`            bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`         varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`          varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`          datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`      bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`      bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`      bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`      bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`      bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`      bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`      bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`      bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`      bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`      bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`     varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`     varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`     varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`     varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`     varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`     varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`     varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`     varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`     varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`     varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`     decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`     decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`     decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`     decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`     decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`     decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`     decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`     decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`     decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`     decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    `sto_out_status`        int(4) DEFAULT '27' COMMENT '逻辑层占用状态',
    `source_storage`        varchar(10)    DEFAULT NULL COMMENT '库存来源',
    `sku_id`                varchar(50)    DEFAULT NULL COMMENT '平台条码id',
    `numiid`                varchar(50)    DEFAULT NULL COMMENT '平台商品id',
    PRIMARY KEY (`id`),
    KEY                     `idx_sg_b_share_out_id` (`sg_b_share_out_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='共享占用单日志明细表';

CREATE TABLE `sg_b_sto_out_item_log`
(
    `id`                  bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`        bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`           bigint(20) DEFAULT '27' COMMENT '所属组织',
    `sg_b_sto_out_id`     bigint(20) DEFAULT NULL COMMENT '逻辑占用单ID',
    `source_bill_item_id` bigint(20) DEFAULT NULL COMMENT '来源单据明细ID',
    `cp_c_store_id`       bigint(20) DEFAULT NULL COMMENT '逻辑仓ID',
    `cp_c_store_ecode`    varchar(100)   DEFAULT NULL COMMENT '逻辑仓编码',
    `cp_c_store_ename`    varchar(255)   DEFAULT NULL COMMENT '逻辑仓名称',
    `qty`                 decimal(18, 4) DEFAULT '0.0000' COMMENT '原单数量',
    `qty_preout`          decimal(18, 4) DEFAULT '0.0000' COMMENT '占用数量',
    `qty_out`             decimal(18, 4) DEFAULT '0.0000' COMMENT '发货数量',
    `ps_c_sku_id`         bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`      varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `gbcode`              varchar(100)   DEFAULT NULL COMMENT '国标码',
    `ps_c_brand_id`       bigint(20) DEFAULT NULL COMMENT '品牌ID',
    `ps_c_pro_id`         bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`      varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`      varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `ps_c_spec1_id`       bigint(20) DEFAULT NULL COMMENT '规格1ID',
    `ps_c_spec1_ecode`    varchar(100)   DEFAULT NULL COMMENT '规格1编码',
    `ps_c_spec1_ename`    varchar(100)   DEFAULT NULL COMMENT '规格1名称',
    `ps_c_spec2_id`       bigint(20) DEFAULT NULL COMMENT '规格2ID',
    `ps_c_spec2_ecode`    varchar(100)   DEFAULT NULL COMMENT '规格2编码',
    `ps_c_spec2_ename`    varchar(100)   DEFAULT NULL COMMENT '规格2名称',
    `ps_c_spec3_id`       bigint(20) DEFAULT NULL COMMENT '规格3ID',
    `ps_c_spec3_ecode`    varchar(100)   DEFAULT NULL COMMENT '规格3编码',
    `ps_c_spec3_ename`    varchar(100)   DEFAULT NULL COMMENT '规格3名称',
    `price_list`          decimal(18, 4) DEFAULT '0.0000' COMMENT '吊牌价',
    `version`             bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`            char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`             bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`          varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`           varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`        datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`          bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`       varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`        varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`        datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`    bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`    bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`    bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`    bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`    bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`    bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`    bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`    bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`    bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`    bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`   varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`   varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`   varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`   varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`   varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`   varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`   varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`   varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`   varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`   varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`   decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`   decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`   decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`   decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`   decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`   decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`   decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`   decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`   decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`   decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    `is_pass_wms`         varchar(255)   DEFAULT NULL COMMENT '是否传WMS',
    `qty_scan`            decimal(18, 4) DEFAULT NULL COMMENT '扫描数量',
    `tid`                 varchar(255)   DEFAULT NULL COMMENT '平台单号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='逻辑占用单日志明细';

--pos入库单增加字段
alter table sg_b_sto_in_notices
    add column status int default 1 COMMENT '提交状态',
    add column status_id bigint default null COMMENT '提交人ID',
    add column status_time datetime default null COMMENT '提交时间',
    add column status_ename varchar(50) default null COMMENT '提交人姓名',
    add column status_name varchar(50) default null COMMENT '提交人用户名',
    add column tot_qty_scan decimal(18, 4) null  COMMENT '总扫描数量';

--欧睿共享量同步中台
CREATE TABLE `sg_c_own_store_selection`
(
    `id`             bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`   bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`      bigint(20) DEFAULT '27' COMMENT '所属组织',
    `batch_no`       varchar(100)   DEFAULT NULL COMMENT '批次号',
    `ps_c_sku_id`    bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode` varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `ps_c_pro_id`    bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode` varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename` varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `stock_ratio`    decimal(18, 4) DEFAULT '0.0000' COMMENT '库存比例',
    `qty_safety`     decimal(18, 4) DEFAULT '0.0000' COMMENT '安全库存',
    `remark`         varchar(500)   DEFAULT NULL COMMENT '备注',
    `version`        bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`       char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`        bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`     varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`      varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`   datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`     bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`  varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`   varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`   datetime       DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='自营门店选款';

create index idx_sg_c_own_store_selection_01 on sg_c_own_store_selection (batch_no);
create index idx_sg_c_own_store_selection_02 on sg_c_own_store_selection (ps_c_sku_id);
create index idx_sg_c_own_store_selection_03 on sg_c_own_store_selection (ps_c_sku_ecode);
create index idx_sg_c_own_store_selection_04 on sg_c_own_store_selection (ps_c_pro_id);
create index idx_sg_c_own_store_selection_05 on sg_c_own_store_selection (ps_c_pro_ecode);

CREATE TABLE `sg_c_offline_warehouse_selection`
(
    `id`             bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`   bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`      bigint(20) DEFAULT '27' COMMENT '所属组织',
    `batch_no`       varchar(100)   DEFAULT NULL COMMENT '批次号',
    `ps_c_sku_id`    bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode` varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `ps_c_pro_id`    bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode` varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename` varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `stock_ratio`    decimal(18, 4) DEFAULT '0.0000' COMMENT '库存比例',
    `qty_safety`     decimal(18, 4) DEFAULT '0.0000' COMMENT '安全库存',
    `remark`         varchar(500)   DEFAULT NULL COMMENT '备注',
    `version`        bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`       char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`        bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`     varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`      varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`   datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`     bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`  varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`   varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`   datetime       DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='线下自营仓选款';

create index idx_sg_c_offline_warehouse_selection_01 on sg_c_offline_warehouse_selection (batch_no);
create index idx_sg_c_offline_warehouse_selection_02 on sg_c_offline_warehouse_selection (ps_c_sku_id);
create index idx_sg_c_offline_warehouse_selection_03 on sg_c_offline_warehouse_selection (ps_c_sku_ecode);
create index idx_sg_c_offline_warehouse_selection_04 on sg_c_offline_warehouse_selection (ps_c_pro_id);
create index idx_sg_c_offline_warehouse_selection_05 on sg_c_offline_warehouse_selection (ps_c_pro_ecode);

CREATE TABLE `sg_c_smart_store_selection`
(
    `id`             bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`   bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`      bigint(20) DEFAULT '27' COMMENT '所属组织',
    `batch_no`       varchar(100)   DEFAULT NULL COMMENT '批次号',
    `ps_c_sku_id`    bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode` varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `ps_c_pro_id`    bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode` varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename` varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `stock_ratio`    decimal(18, 4) DEFAULT '0.0000' COMMENT '库存比例',
    `qty_safety`     decimal(18, 4) DEFAULT '0.0000' COMMENT '安全库存',
    `remark`         varchar(500)   DEFAULT NULL COMMENT '备注',
    `version`        bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`       char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`        bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`     varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`      varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`   datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`     bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`  varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`   varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`   datetime       DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='SmartStore门店选款';

create index idx_sg_c_smart_store_selection_01 on sg_c_smart_store_selection (batch_no);
create index idx_sg_c_smart_store_selection_02 on sg_c_smart_store_selection (ps_c_sku_id);
create index idx_sg_c_smart_store_selection_03 on sg_c_smart_store_selection (ps_c_sku_ecode);
create index idx_sg_c_smart_store_selection_04 on sg_c_smart_store_selection (ps_c_pro_id);
create index idx_sg_c_smart_store_selection_05 on sg_c_smart_store_selection (ps_c_pro_ecode);

CREATE TABLE `sg_c_store_warehouse_selection`
(
    `id`               bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`     bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`        bigint(20) DEFAULT '27' COMMENT '所属组织',
    `batch_no`         varchar(100)   DEFAULT NULL COMMENT '批次号',
    `cp_c_store_id`    bigint(20) DEFAULT NULL COMMENT '逻辑仓ID',
    `cp_c_store_ecode` varchar(100)   DEFAULT NULL COMMENT '逻辑仓编码',
    `cp_c_store_ename` varchar(255)   DEFAULT NULL COMMENT '逻辑仓名称',
    `ps_c_sku_id`      bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`   varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `ps_c_pro_id`      bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`   varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`   varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `stock_ratio`      decimal(18, 4) DEFAULT '0.0000' COMMENT '库存比例',
    `qty_safety`       decimal(18, 4) DEFAULT '0.0000' COMMENT '安全库存',
    `remark`           varchar(500)   DEFAULT NULL COMMENT '备注',
    `version`          bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`         char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`          bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`       varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`        varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`     datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`       bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`    varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`     varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`     datetime       DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店仓维度选款';

create index idx_sg_c_store_warehouse_selection_01 on sg_c_store_warehouse_selection (batch_no);
create index idx_sg_c_store_warehouse_selection_02 on sg_c_store_warehouse_selection (ps_c_sku_id);
create index idx_sg_c_store_warehouse_selection_03 on sg_c_store_warehouse_selection (ps_c_sku_ecode);
create index idx_sg_c_store_warehouse_selection_04 on sg_c_store_warehouse_selection (ps_c_pro_id);
create index idx_sg_c_store_warehouse_selection_05 on sg_c_store_warehouse_selection (ps_c_pro_ecode);

--已添加？
CREATE TABLE `sg_b_share_sa_allocation_transfer`
(
    `id`                   bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`         bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`            bigint(20) DEFAULT '27' COMMENT '所属组织',
    `bill_no`              varchar(100)   DEFAULT NULL COMMENT '单据编号',
    `bill_date`            date           DEFAULT NULL COMMENT '单据日期',
    `tot_qty_apply`        decimal(18, 4) DEFAULT '0.0000' COMMENT '总申请数量',
    `tot_qty_share_transf` decimal(18, 4) DEFAULT '0.0000' COMMENT '总聚合调拨数量',
    `tot_qty_allocation`   decimal(18, 4) DEFAULT '0.0000' COMMENT '总分货数量',
    `remark`               varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`              bigint(20) DEFAULT NULL COMMENT '版本号',
    `bill_status`          int(11) DEFAULT NULL COMMENT '单据状态(0-未提交、1-完成聚合调拨、2-完成分货、3-已作废)',
    `status`               int(11) DEFAULT NULL COMMENT '单据状态',
    `ownerid`              bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`           varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`            varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`         datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`           bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`        varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`         varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`         datetime       DEFAULT NULL COMMENT '修改时间',
    `status_id`            int(11) DEFAULT '0' COMMENT '提交人ID',
    `status_ename`         varchar(50)    DEFAULT NULL COMMENT '提交人姓名',
    `status_name`          varchar(50)    DEFAULT NULL COMMENT '提交人用户名',
    `status_time`          datetime       DEFAULT NULL COMMENT '提交时间',
    `deler_id`             bigint(20) DEFAULT NULL COMMENT '作废人ID',
    `deler_ename`          varchar(50)    DEFAULT NULL COMMENT '作废人姓名',
    `deler_name`           varchar(50)    DEFAULT NULL COMMENT '作废人用户名',
    `del_time`             datetime       DEFAULT NULL COMMENT '作废时间',
    `isactive`             char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `reserve_bigint01`     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='聚合仓配销仓调拨单';

create index idx_sg_b_share_sa_allocation_transfer_01 on sg_b_share_sa_allocation_transfer (bill_no);
create index idx_sg_b_share_sa_allocation_transfer_02 on sg_b_share_sa_allocation_transfer (bill_date);

--已添加？
CREATE TABLE `sg_b_share_sa_allocation_import_item`
(
    `id`                                   bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`                         bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                            bigint(20) DEFAULT '27' COMMENT '所属组织',
    `sg_b_share_sa_allocation_transfer_id` bigint(20) DEFAULT NULL COMMENT '聚合仓配销仓调拨单ID',
    `sender_share_store_id`                bigint(20) DEFAULT NULL COMMENT '发货聚合仓id',
    `sender_share_store_ecode`             varchar(100)   DEFAULT NULL COMMENT '发货聚合仓编码',
    `sender_share_store_ename`             varchar(255)   DEFAULT NULL COMMENT '发货聚合仓名称',
    `receiver_sa_store_id`                 bigint(20) DEFAULT NULL COMMENT '收货配销仓ID',
    `receiver_sa_store_ecode`              varchar(100)   DEFAULT NULL COMMENT '收货配销仓编码',
    `receiver_sa_store_ename`              varchar(255)   DEFAULT NULL COMMENT '收货配销仓名称',
    `receiver_share_store_id`              bigint(20) DEFAULT NULL COMMENT '收货聚合仓id',
    `receiver_share_store_ecode`           varchar(100)   DEFAULT NULL COMMENT '收货聚合仓编码',
    `receiver_share_store_ename`           varchar(255)   DEFAULT NULL COMMENT '收货聚合仓名称',
    `ps_c_sku_or_pro`                      varchar(100)   DEFAULT NULL COMMENT '款号或者条码',
    `ps_c_sku_id`                          bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`                       varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `ps_c_pro_id`                          bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`                       varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`                       varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `qty_apply`                            decimal(18, 4) default '0.0000' null comment '申请数量',
    `transfer_dimension`                   int(11) DEFAULT NULL COMMENT '调拨维度(1-条码维度、2-商品维度)',
    `price_list`                           decimal(18, 4) DEFAULT '0.0000' COMMENT '吊牌价',
    `gbcode`                               varchar(100)   DEFAULT NULL COMMENT '国标码',
    `remark`                               varchar(255)   DEFAULT NULL COMMENT '备注',
    `ownerid`                              bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                           varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                            varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`                         datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                           bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`                        varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`                         varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`                         datetime       DEFAULT NULL COMMENT '修改时间',
    `isactive`                             char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `reserve_bigint01`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='聚合仓配销仓调拨单导入明细';

create index idx_sg_b_share_sa_allocation_import_item_01 on sg_b_share_sa_allocation_import_item (sg_b_share_sa_allocation_transfer_id);

--已添加？
alter table sg_b_channel_product
    add column qty_out_stock decimal(18, 4) default 0.0000 COMMENT '缺货数量' AFTER `qty_storage`;
--已添加？
CREATE TABLE `sg_b_share_sa_allocation_transfer_item`
(
    `id`                                   bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`                         bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                            bigint(20) DEFAULT '27' COMMENT '所属组织',
    `sg_b_share_sa_allocation_transfer_id` bigint(20) DEFAULT NULL COMMENT '聚合仓配销仓调拨单ID',
    `sender_share_store_id`                bigint(20) DEFAULT NULL COMMENT '发货聚合仓id',
    `sender_share_store_ecode`             varchar(100)   DEFAULT NULL COMMENT '发货聚合仓编码',
    `sender_share_store_ename`             varchar(255)   DEFAULT NULL COMMENT '发货聚合仓名称',
    `receiver_sa_store_id`                 bigint(20) DEFAULT NULL COMMENT '收货配销仓ID',
    `receiver_sa_store_ecode`              varchar(100)   DEFAULT NULL COMMENT '收货配销仓编码',
    `receiver_sa_store_ename`              varchar(255)   DEFAULT NULL COMMENT '收货配销仓名称',
    `receiver_share_store_id`              bigint(20) DEFAULT NULL COMMENT '收货聚合仓id',
    `receiver_share_store_ecode`           varchar(100)   DEFAULT NULL COMMENT '收货聚合仓编码',
    `receiver_share_store_ename`           varchar(255)   DEFAULT NULL COMMENT '收货聚合仓名称',
    `ps_c_sku_id`                          bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`                       varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `gbcode`                               varchar(100)   DEFAULT NULL COMMENT '国标码',
    `ps_c_pro_id`                          bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`                       varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`                       varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `ps_c_spec1_id`                        bigint(20) DEFAULT NULL COMMENT '规格1ID',
    `ps_c_spec1_ecode`                     varchar(100)   DEFAULT NULL COMMENT '规格1编码',
    `ps_c_spec1_ename`                     varchar(100)   DEFAULT NULL COMMENT '规格1名称',
    `ps_c_spec2_id`                        bigint(20) DEFAULT NULL COMMENT '规格2ID',
    `ps_c_spec2_ecode`                     varchar(100)   DEFAULT NULL COMMENT '规格2编码',
    `ps_c_spec2_ename`                     varchar(100)   DEFAULT NULL COMMENT '规格2名称',
    `ps_c_spec3_id`                        bigint(20) DEFAULT NULL COMMENT '规格3ID',
    `ps_c_spec3_ecode`                     varchar(100)   DEFAULT NULL COMMENT '规格3编码',
    `ps_c_spec3_ename`                     varchar(100)   DEFAULT NULL COMMENT '规格3名称',
    `price_list`                           decimal(18, 4) default '0.0000' null comment '吊牌价',
    `qty_apply`                            decimal(18, 4) default '0.0000' null comment '申请数量',
    `qty_share_transf`                     decimal(18, 4) DEFAULT '0.0000' COMMENT '聚合调拨数量',
    `qty_allocation`                       decimal(18, 4) DEFAULT '0.0000' COMMENT '分货数量',
    `remark`                               varchar(2000)  DEFAULT NULL COMMENT '备注',
    `ownerid`                              bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                           varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                            varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`                         datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                           bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`                        varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`                         varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`                         datetime       DEFAULT NULL COMMENT '修改时间',
    `isactive`                             char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `reserve_bigint01`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='聚合仓配销仓调拨结果单明细';

create index idx_sg_b_share_sa_allocation_transfer_item_01 on sg_b_share_sa_allocation_transfer_item (sg_b_share_sa_allocation_transfer_id);

--已添加？
ALTER TABLE `sg_b_channel_product`
    ADD COLUMN `is_special` char(1) DEFAULT 'N' COMMENT '是否特殊商品';

ALTER TABLE `sg_b_sa_target_storage_summary`
    MODIFY COLUMN `version` varchar (50) NULL DEFAULT NULL COMMENT '版本号' AFTER `remark`;

CREATE TABLE `sg_b_share_sa_cross_transfer`
(
    `id`                        bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`              bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                 bigint(20) DEFAULT '27' COMMENT '所属组织',
    `bill_date`                 date           DEFAULT NULL COMMENT '单据日期',
    `bill_no`                   varchar(100)   DEFAULT NULL COMMENT '单据编号',
    `status`                    int(11) DEFAULT NULL COMMENT '单据状态',
    `tot_qty_apply`             decimal(18, 4) DEFAULT '0.0000' COMMENT '总申请数量',
    `tot_qty_allocation_return` decimal(18, 4) DEFAULT '0.0000' COMMENT '总分货退数量',
    `tot_qty_share_transfer`    decimal(18, 4) DEFAULT '0.0000' COMMENT '总聚合调拨数量',
    `tot_qty_allocation`        decimal(18, 4) DEFAULT '0.0000' COMMENT '总分货数量',
    `remark`                    varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`                   bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`                  char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                   bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                 varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`              datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`             varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`              varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`              datetime       DEFAULT NULL COMMENT '修改时间',
    `status_id`                 bigint(20) DEFAULT NULL COMMENT '提交人ID',
    `status_ename`              varchar(50)    DEFAULT NULL COMMENT '提交人姓名',
    `status_name`               varchar(50)    DEFAULT NULL COMMENT '提交人用户名',
    `status_time`               datetime       DEFAULT NULL COMMENT '提交时间',
    `deler_id`                  bigint(20) DEFAULT NULL COMMENT '作废人ID',
    `deler_ename`               varchar(50)    DEFAULT NULL COMMENT '作废人姓名',
    `deler_name`                varchar(50)    DEFAULT NULL COMMENT '作废人用户名',
    `del_time`                  datetime       DEFAULT NULL COMMENT '作废时间',
    `reserve_bigint01`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`),
    KEY                         `idx_sg_b_share_sa_cross_transfer_01` (`bill_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配销跨聚合仓调拨单';

create index idx_sg_b_share_sa_cross_transfer_02 on sg_b_share_sa_cross_transfer (bill_date);

CREATE TABLE `sg_b_share_sa_cross_transfer_import_item`
(
    `id`                              bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`                    bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                       bigint(20) DEFAULT '27' COMMENT '所属组织',
    `sg_b_share_sa_cross_transfer_id` bigint(20) DEFAULT NULL COMMENT '配销跨聚合仓调拨单ID',
    `sender_sa_store_id`              bigint(20) DEFAULT NULL COMMENT '发货配销仓id',
    `sender_sa_store_ecode`           varchar(100)   DEFAULT NULL COMMENT '发货配销仓编码',
    `sender_sa_store_ename`           varchar(255)   DEFAULT NULL COMMENT '发货配销仓名称',
    `sender_share_store_id`           bigint(20) DEFAULT NULL COMMENT '发货聚合仓id',
    `sender_share_store_ecode`        varchar(100)   DEFAULT NULL COMMENT '发货聚合仓编码',
    `sender_share_store_ename`        varchar(255)   DEFAULT NULL COMMENT '发货聚合仓名称',
    `receiver_sa_store_id`            bigint(20) DEFAULT NULL COMMENT '收货配销仓id',
    `receiver_sa_store_ecode`         varchar(100)   DEFAULT NULL COMMENT '收货配销仓编码',
    `receiver_sa_store_ename`         varchar(255)   DEFAULT NULL COMMENT '收货配销仓名称',
    `receiver_share_store_id`         bigint(20) DEFAULT NULL COMMENT '收货聚合仓id',
    `receiver_share_store_ecode`      varchar(100)   DEFAULT NULL COMMENT '收货聚合仓编码',
    `receiver_share_store_ename`      varchar(255)   DEFAULT NULL COMMENT '收货聚合仓名称',
    `ps_c_sku_or_pro`                 varchar(100)   DEFAULT NULL COMMENT '款号或者条码',
    `ps_c_sku_id`                     bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`                  varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `gbcode`                          varchar(100)   DEFAULT NULL COMMENT '国标码',
    `ps_c_pro_id`                     bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`                  varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`                  varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `ps_c_brand_id`                   bigint(20) DEFAULT NULL COMMENT '品牌ID',
    `ps_c_spec1_id`                   bigint(20) DEFAULT NULL COMMENT '规格1ID',
    `ps_c_spec1_ecode`                varchar(100)   DEFAULT NULL COMMENT '规格1编码',
    `ps_c_spec1_ename`                varchar(100)   DEFAULT NULL COMMENT '规格1名称',
    `ps_c_spec2_id`                   bigint(20) DEFAULT NULL COMMENT '规格2ID',
    `ps_c_spec2_ecode`                varchar(100)   DEFAULT NULL COMMENT '规格2编码',
    `ps_c_spec2_ename`                varchar(100)   DEFAULT NULL COMMENT '规格2名称',
    `ps_c_spec3_id`                   bigint(20) DEFAULT NULL COMMENT '规格3ID',
    `ps_c_spec3_ecode`                varchar(100)   DEFAULT NULL COMMENT '规格3编码',
    `ps_c_spec3_ename`                varchar(100)   DEFAULT NULL COMMENT '规格3名称',
    `price_list`                      decimal(18, 4) DEFAULT '0.0000' COMMENT '吊牌价',
    `qty`                             decimal(18, 4) DEFAULT '0.0000' COMMENT '申请数量',
    `transfer_dimension`              int(11) DEFAULT '0' COMMENT '调拨维度',
    `remark`                          varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`                         bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`                        char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                         bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                      varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                       varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`                    datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                      bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`                   varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`                    varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`                    datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`),
    KEY                               `idx_sg_b_share_sa_cross_transfer_import_item_01` (`sg_b_share_sa_cross_transfer_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配销跨聚合仓调拨单-调拨导入明细';

CREATE TABLE `sg_b_share_sa_cross_transfer_result_item`
(
    `id`                              bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`                    bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                       bigint(20) DEFAULT '27' COMMENT '所属组织',
    `sg_b_share_sa_cross_transfer_id` bigint(20) DEFAULT NULL COMMENT '配销跨聚合仓调拨单ID',
    `sender_sa_store_id`              bigint(20) DEFAULT NULL COMMENT '发货配销仓id',
    `sender_sa_store_ecode`           varchar(100)   DEFAULT NULL COMMENT '发货配销仓编码',
    `sender_sa_store_ename`           varchar(255)   DEFAULT NULL COMMENT '发货配销仓名称',
    `sender_share_store_id`           bigint(20) DEFAULT NULL COMMENT '发货聚合仓id',
    `sender_share_store_ecode`        varchar(100)   DEFAULT NULL COMMENT '发货聚合仓编码',
    `sender_share_store_ename`        varchar(255)   DEFAULT NULL COMMENT '发货聚合仓名称',
    `receiver_sa_store_id`            bigint(20) DEFAULT NULL COMMENT '收货配销仓id',
    `receiver_sa_store_ecode`         varchar(100)   DEFAULT NULL COMMENT '收货配销仓编码',
    `receiver_sa_store_ename`         varchar(255)   DEFAULT NULL COMMENT '收货配销仓名称',
    `receiver_share_store_id`         bigint(20) DEFAULT NULL COMMENT '收货聚合仓id',
    `receiver_share_store_ecode`      varchar(100)   DEFAULT NULL COMMENT '收货聚合仓编码',
    `receiver_share_store_ename`      varchar(255)   DEFAULT NULL COMMENT '收货聚合仓名称',
    `ps_c_sku_id`                     bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`                  varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `gbcode`                          varchar(100)   DEFAULT NULL COMMENT '国标码',
    `ps_c_brand_id`                   bigint(20) DEFAULT NULL COMMENT '品牌ID',
    `ps_c_pro_id`                     bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`                  varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`                  varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `ps_c_spec1_id`                   bigint(20) DEFAULT NULL COMMENT '规格1ID',
    `ps_c_spec1_ecode`                varchar(100)   DEFAULT NULL COMMENT '规格1编码',
    `ps_c_spec1_ename`                varchar(100)   DEFAULT NULL COMMENT '规格1名称',
    `ps_c_spec2_id`                   bigint(20) DEFAULT NULL COMMENT '规格2ID',
    `ps_c_spec2_ecode`                varchar(100)   DEFAULT NULL COMMENT '规格2编码',
    `ps_c_spec2_ename`                varchar(100)   DEFAULT NULL COMMENT '规格2名称',
    `ps_c_spec3_id`                   bigint(20) DEFAULT NULL COMMENT '规格3ID',
    `ps_c_spec3_ecode`                varchar(100)   DEFAULT NULL COMMENT '规格3编码',
    `ps_c_spec3_ename`                varchar(100)   DEFAULT NULL COMMENT '规格3名称',
    `price_list`                      decimal(18, 4) DEFAULT '0.0000' COMMENT '吊牌价',
    `qty_apply`                       decimal(18, 4) DEFAULT '0.0000' COMMENT '申请数量',
    `qty_allocation_return`           decimal(18, 4) DEFAULT '0.0000' COMMENT '分货退数量',
    `qty_share_transfer`              decimal(18, 4) DEFAULT '0.0000' COMMENT '聚合调拨数量',
    `qty_allocation`                  decimal(18, 4) DEFAULT '0.0000' COMMENT '分货数量',
    `remark`                          varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`                         bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`                        char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                         bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                      varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                       varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`                    datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                      bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`                   varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`                    varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`                    datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                               `idx_sg_b_share_sa_cross_transfer_result_item_01` (`sg_b_share_sa_cross_transfer_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配销跨聚合仓调拨单-调拨结果明细';

--已添加？
ALTER TABLE `sg_b_share_transfer_item`
    ADD COLUMN `source_bill_item_id` bigint(20) NULL DEFAULT NULL COMMENT '来源单据明细ID';

create index idx_sg_b_share_transfer_item_02 on sg_b_share_transfer_item (source_bill_item_id);

ALTER TABLE `sg_b_share_transfer_import_item`
    ADD COLUMN `source_bill_item_id` bigint(20) NULL DEFAULT NULL COMMENT '来源单据明细ID';

create index idx_sg_b_share_transfer_import_item_02 on sg_b_share_transfer_import_item (source_bill_item_id);

CREATE TABLE `sg_b_share_from_sa_transfer`
(
    `id`                        bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`              bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                 bigint(20) DEFAULT '27' COMMENT '所属组织',
    `bill_date`                 date           DEFAULT NULL COMMENT '单据日期',
    `bill_no`                   varchar(100)   DEFAULT NULL COMMENT '单据编号',
    `status`                    int(11) DEFAULT NULL COMMENT '单据状态',
    `tot_qty_apply`             decimal(18, 4) DEFAULT '0.0000' COMMENT '总申请数量',
    `tot_qty_allocation_return` decimal(18, 4) DEFAULT '0.0000' COMMENT '总分货退数量',
    `tot_qty_share_transfer`    decimal(18, 4) DEFAULT '0.0000' COMMENT '总聚合调拨数量',
    `remark`                    varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`                   bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`                  char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                   bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                 varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`              datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`             varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`              varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`              datetime       DEFAULT NULL COMMENT '修改时间',
    `status_id`                 bigint(20) DEFAULT NULL COMMENT '提交人ID',
    `status_ename`              varchar(50)    DEFAULT NULL COMMENT '提交人姓名',
    `status_name`               varchar(50)    DEFAULT NULL COMMENT '提交人用户名',
    `status_time`               datetime       DEFAULT NULL COMMENT '提交时间',
    `deler_id`                  bigint(20) DEFAULT NULL COMMENT '作废人ID',
    `deler_ename`               varchar(50)    DEFAULT NULL COMMENT '作废人姓名',
    `deler_name`                varchar(50)    DEFAULT NULL COMMENT '作废人用户名',
    `del_time`                  datetime       DEFAULT NULL COMMENT '作废时间',
    `reserve_bigint01`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`),
    KEY                         `idx_sg_b_share_from_sa_transfer_01` (`bill_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配销仓->聚合仓调拨单';

create index idx_sg_b_share_from_sa_transfer_02 on sg_b_share_from_sa_transfer (bill_date);

CREATE TABLE `sg_b_share_from_sa_transfer_import_item`
(
    `id`                             bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`                   bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                      bigint(20) DEFAULT '27' COMMENT '所属组织',
    `sg_b_share_from_sa_transfer_id` bigint(20) DEFAULT NULL COMMENT '配销跨聚合仓调拨单ID',
    `sender_sa_store_id`             bigint(20) DEFAULT NULL COMMENT '发货配销仓id',
    `sender_sa_store_ecode`          varchar(100)   DEFAULT NULL COMMENT '发货配销仓编码',
    `sender_sa_store_ename`          varchar(255)   DEFAULT NULL COMMENT '发货配销仓名称',
    `sender_share_store_id`          bigint(20) DEFAULT NULL COMMENT '发货聚合仓id',
    `sender_share_store_ecode`       varchar(100)   DEFAULT NULL COMMENT '发货聚合仓编码',
    `sender_share_store_ename`       varchar(255)   DEFAULT NULL COMMENT '发货聚合仓名称',
    `receiver_share_store_id`        bigint(20) DEFAULT NULL COMMENT '收货聚合仓id',
    `receiver_share_store_ecode`     varchar(100)   DEFAULT NULL COMMENT '收货聚合仓编码',
    `receiver_share_store_ename`     varchar(255)   DEFAULT NULL COMMENT '收货聚合仓名称',
    `ps_c_sku_or_pro`                varchar(100)   DEFAULT NULL COMMENT '款号或者条码',
    `ps_c_sku_id`                    bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`                 varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `gbcode`                         varchar(100)   DEFAULT NULL COMMENT '国标码',
    `ps_c_pro_id`                    bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`                 varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`                 varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `ps_c_brand_id`                  bigint(20) DEFAULT NULL COMMENT '品牌ID',
    `ps_c_spec1_id`                  bigint(20) DEFAULT NULL COMMENT '规格1ID',
    `ps_c_spec1_ecode`               varchar(100)   DEFAULT NULL COMMENT '规格1编码',
    `ps_c_spec1_ename`               varchar(100)   DEFAULT NULL COMMENT '规格1名称',
    `ps_c_spec2_id`                  bigint(20) DEFAULT NULL COMMENT '规格2ID',
    `ps_c_spec2_ecode`               varchar(100)   DEFAULT NULL COMMENT '规格2编码',
    `ps_c_spec2_ename`               varchar(100)   DEFAULT NULL COMMENT '规格2名称',
    `ps_c_spec3_id`                  bigint(20) DEFAULT NULL COMMENT '规格3ID',
    `ps_c_spec3_ecode`               varchar(100)   DEFAULT NULL COMMENT '规格3编码',
    `ps_c_spec3_ename`               varchar(100)   DEFAULT NULL COMMENT '规格3名称',
    `price_list`                     decimal(18, 4) DEFAULT '0.0000' COMMENT '吊牌价',
    `qty`                            decimal(18, 4) DEFAULT '0.0000' COMMENT '申请数量',
    `transfer_dimension`             int(11) DEFAULT '0' COMMENT '调拨维度',
    `remark`                         varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`                        bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`                       char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                        bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                     varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                      varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`                   datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                     bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`                  varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`                   varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`                   datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`),
    KEY                              `idx_sg_b_share_from_sa_transfer_import_item_01` (`sg_b_share_from_sa_transfer_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配销仓->聚合仓调拨单导入明细';

CREATE TABLE `sg_b_share_from_sa_transfer_result_item`
(
    `id`                             bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`                   bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                      bigint(20) DEFAULT '27' COMMENT '所属组织',
    `sg_b_share_from_sa_transfer_id` bigint(20) DEFAULT NULL COMMENT '配销跨聚合仓调拨单ID',
    `sender_sa_store_id`             bigint(20) DEFAULT NULL COMMENT '发货配销仓id',
    `sender_sa_store_ecode`          varchar(100)   DEFAULT NULL COMMENT '发货配销仓编码',
    `sender_sa_store_ename`          varchar(255)   DEFAULT NULL COMMENT '发货配销仓名称',
    `sender_share_store_id`          bigint(20) DEFAULT NULL COMMENT '发货聚合仓id',
    `sender_share_store_ecode`       varchar(100)   DEFAULT NULL COMMENT '发货聚合仓编码',
    `sender_share_store_ename`       varchar(255)   DEFAULT NULL COMMENT '发货聚合仓名称',
    `receiver_share_store_id`        bigint(20) DEFAULT NULL COMMENT '收货聚合仓id',
    `receiver_share_store_ecode`     varchar(100)   DEFAULT NULL COMMENT '收货聚合仓编码',
    `receiver_share_store_ename`     varchar(255)   DEFAULT NULL COMMENT '收货聚合仓名称',
    `ps_c_sku_id`                    bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`                 varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `gbcode`                         varchar(100)   DEFAULT NULL COMMENT '国标码',
    `ps_c_brand_id`                  bigint(20) DEFAULT NULL COMMENT '品牌ID',
    `ps_c_pro_id`                    bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`                 varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`                 varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `ps_c_spec1_id`                  bigint(20) DEFAULT NULL COMMENT '规格1ID',
    `ps_c_spec1_ecode`               varchar(100)   DEFAULT NULL COMMENT '规格1编码',
    `ps_c_spec1_ename`               varchar(100)   DEFAULT NULL COMMENT '规格1名称',
    `ps_c_spec2_id`                  bigint(20) DEFAULT NULL COMMENT '规格2ID',
    `ps_c_spec2_ecode`               varchar(100)   DEFAULT NULL COMMENT '规格2编码',
    `ps_c_spec2_ename`               varchar(100)   DEFAULT NULL COMMENT '规格2名称',
    `ps_c_spec3_id`                  bigint(20) DEFAULT NULL COMMENT '规格3ID',
    `ps_c_spec3_ecode`               varchar(100)   DEFAULT NULL COMMENT '规格3编码',
    `ps_c_spec3_ename`               varchar(100)   DEFAULT NULL COMMENT '规格3名称',
    `price_list`                     decimal(18, 4) DEFAULT '0.0000' COMMENT '吊牌价',
    `qty_apply`                      decimal(18, 4) DEFAULT '0.0000' COMMENT '申请数量',
    `qty_allocation_return`          decimal(18, 4) DEFAULT '0.0000' COMMENT '分货退数量',
    `qty_share_transfer`             decimal(18, 4) DEFAULT '0.0000' COMMENT '聚合调拨数量',
    `remark`                         varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`                        bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`                       char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                        bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                     varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                      varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`                   datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                     bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`                  varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`                   varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`                   datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`               bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`              varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`              decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                              `idx_sg_b_share_from_sa_transfer_result_item_01` (`sg_b_share_from_sa_transfer_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配销仓->聚合仓调拨单结果明细';

/*逻辑调拨单批量导入->按款导入->字段调整*/
ALTER TABLE `sg_b_sto_batch_transfer_pro_item`
    ADD COLUMN `ps_c_pro_id` bigint(20) DEFAULT NULL COMMENT '商品ID' after `receiver_store_ename`,
    ADD COLUMN `ps_c_sku_id` bigint(20) NULL COMMENT '条码ID' after `ps_c_pro_ename`,
    ADD COLUMN `ps_c_sku_ecode` varchar(100) NULL COMMENT '条码编码' after `ps_c_sku_id`,
    ADD COLUMN `sku_ecode` varchar(100) NULL COMMENT '款号或者条码' after `ps_c_sku_ecode`,
    ADD COLUMN `transfer_dimension` int(11) NULL COMMENT '调拨维度(1-条码维度、2-商品维度)' after `sku_ecode`,
    ADD COLUMN `sender_customer_id` bigint(20) DEFAULT NULL COMMENT '发货经销商ID',
    ADD COLUMN `sender_customer_ecode` varchar(50) DEFAULT NULL COMMENT '发货经销商编码',
    ADD COLUMN `sender_customer_ename` varchar(50) DEFAULT NULL COMMENT '发货经销商编码',
    ADD COLUMN `receiver_customer_id` bigint(20) DEFAULT NULL COMMENT '收货经销商ID',
    ADD COLUMN `receiver_customer_ecode` varchar(50) DEFAULT NULL COMMENT '收货经销商编码',
    ADD COLUMN `receiver_customer_ename` varchar(50) DEFAULT NULL COMMENT '收货经销商编码',
    ADD COLUMN `cp_c_tranway_assign_id`     bigint(20) DEFAULT NULL COMMENT '运输类型';

ALTER TABLE `sg_b_sto_batch_transfer`
    ADD COLUMN `pro_tot_row_num` int(11) DEFAULT '0' COMMENT '总导入行数' after `tot_row_num`,
    MODIFY COLUMN `tot_row_num` int(11) DEFAULT '0' COMMENT '总结果行数';


ALTER TABLE `sg_b_channel_storage_inc_sync_success_item`
    MODIFY COLUMN `sku_id` varchar (255) DEFAULT NULL COMMENT '平台条码id',
    MODIFY COLUMN `numiid` varchar (255) DEFAULT NULL COMMENT '平台商品id';
ALTER TABLE `sg_b_share_out_item`
    MODIFY COLUMN `sku_id` varchar (255) DEFAULT NULL COMMENT '平台条码id',
    MODIFY COLUMN `numiid` varchar (255) DEFAULT NULL COMMENT '平台商品id';
ALTER TABLE `sg_b_share_out_item_log`
    MODIFY COLUMN `sku_id` varchar (255) DEFAULT NULL COMMENT '平台条码id',
    MODIFY COLUMN `numiid` varchar (255) DEFAULT NULL COMMENT '平台商品id';

ALTER TABLE `sg_b_sa_target_storage_summary`
MODIFY COLUMN  `creationdate` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ;