ALTER TABLE `cp_c_phy_warehouse` MODIFY COLUMN `ecode` varchar (100);
ALTER TABLE `cp_c_store` MODIFY COLUMN `cp_c_phy_warehouse_ecode` varchar (100);
ALTER TABLE `sg_b_sto_in_notices` MODIFY COLUMN `cp_c_phy_warehouse_ecode` varchar (100);
ALTER TABLE `sg_b_sto_out` MODIFY COLUMN `cp_c_phy_warehouse_ecode` varchar (100);
ALTER TABLE `sg_b_sto_out_notices` MODIFY COLUMN `cp_c_phy_warehouse_ecode` varchar (100);
ALTER TABLE `sg_c_store_score_strategy` MODIFY COLUMN `cp_c_phy_warehouse_ecode` varchar (100);
ALTER TABLE `sg_c_store_score_strategy` MODIFY COLUMN `cp_c_phy_warehouse_ename` varchar (255);
ALTER TABLE `cp_c_store` MODIFY COLUMN `priority` int (11)

CREATE TABLE `sg_c_warehouse_scope`
(
    `id`                       bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`             bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                bigint(20) DEFAULT '27' COMMENT '所属组织',
    `cp_c_phy_warehouse_id`    bigint(20) DEFAULT NULL COMMENT '实体仓ID',
    `cp_c_phy_warehouse_ecode` varchar(100)   DEFAULT NULL COMMENT '实体仓编码',
    `cp_c_phy_warehouse_ename` varchar(100)   DEFAULT NULL COMMENT '实体仓名称',
    `cp_c_province_id`         bigint(20) DEFAULT NULL COMMENT '省id',
    `priority`                 int(11) DEFAULT '0' COMMENT '优先级',
    `remark`                   varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`                  bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`                 char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                  bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`               varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`             datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`               bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`            varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`             varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`             datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`         bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`         bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`         bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`         bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`         bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`         bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`         bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`         bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`         bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`         bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`        varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`        varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`        varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`        varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`        varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`        varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`        varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`        varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`        varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`        varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`        decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`        decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`        decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`        decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`        decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`        decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`        decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`        decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`        decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`        decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`),
    KEY                        `idx_warehouse_scope_01` (`cp_c_phy_warehouse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='仓库配送范围';

CREATE TABLE `sg_c_oneset_setting`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `ad_client_id`           BIGINT (20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`              BIGINT (20) DEFAULT 27 COMMENT '所属组织',
    `sg_c_share_store_id`    BIGINT (20) DEFAULT NULL COMMENT '聚合仓ID',
    `sg_c_share_store_ecode` VARCHAR(100)   DEFAULT NULL COMMENT '聚合仓编码',
    `sg_c_share_store_ename` VARCHAR(255)   DEFAULT NULL COMMENT '聚合仓名称',
    `ps_c_sku_id`            BIGINT (20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`         VARCHAR(100)   DEFAULT NULL COMMENT '条码编码',
    `qty`                    DECIMAL(18, 4) DEFAULT 0 COMMENT '一手码数量',
    `ps_c_pro_id`            BIGINT (20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`         VARCHAR(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`         VARCHAR(255)   DEFAULT NULL COMMENT '商品名称',
    `ps_c_spec1_id`          BIGINT (20) DEFAULT NULL COMMENT '规格1ID',
    `ps_c_spec1_ecode`       VARCHAR(100)   DEFAULT NULL COMMENT '规格1编码',
    `ps_c_spec1_ename`       VARCHAR(100)   DEFAULT NULL COMMENT '规格1名称',
    `ps_c_spec2_id`          BIGINT (20) DEFAULT NULL COMMENT '规格2ID',
    `ps_c_spec2_ecode`       VARCHAR(100)   DEFAULT NULL COMMENT '规格2编码',
    `ps_c_spec2_ename`       VARCHAR(100)   DEFAULT NULL COMMENT '规格2名称',
    `ps_c_spec3_id`          BIGINT (20) DEFAULT NULL COMMENT '规格3ID',
    `ps_c_spec3_ecode`       VARCHAR(100)   DEFAULT NULL COMMENT '规格3编码',
    `ps_c_spec3_ename`       VARCHAR(100)   DEFAULT NULL COMMENT '规格3名称',
    `remark`                 VARCHAR(255)   DEFAULT NULL COMMENT '备注',
    `version`                BIGINT (20) DEFAULT NULL COMMENT '版本号',
    `isactive`               CHAR(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                BIGINT (20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`             VARCHAR(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`              VARCHAR(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`           datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`             BIGINT (20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`          VARCHAR(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`           VARCHAR(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`           datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`       BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`       BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`       BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`       BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`       BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`       BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`       BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`       BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`       BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`       BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`      VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`      VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`      VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`      VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`      VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`      VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`      VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`      VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`      VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`      VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`      DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`      DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`      DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`      DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`      DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`      DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`      DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`      DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`      DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`      DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '一手码设置';

CREATE TABLE `sg_c_oneset_sa_setting`
(
    `id`                  BIGINT (20) NOT NULL COMMENT 'ID',
    `ad_client_id`        BIGINT (20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`           BIGINT (20) DEFAULT 27 COMMENT '所属组织',
    `sg_c_sa_store_id`    BIGINT (20) DEFAULT NULL COMMENT '配货仓ID',
    `sg_c_sa_store_ecode` VARCHAR(100)   DEFAULT NULL COMMENT '配货仓编码',
    `sg_c_sa_store_ename` VARCHAR(255)   DEFAULT NULL COMMENT '配货仓名称',
    `ps_c_sku_id`         BIGINT (20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`      VARCHAR(100)   DEFAULT NULL COMMENT '条码编码',
    `qty`                 DECIMAL(18, 4) DEFAULT 0 COMMENT '一手码数量',
    `ps_c_pro_id`         BIGINT (20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`      VARCHAR(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`      VARCHAR(255)   DEFAULT NULL COMMENT '商品名称',
    `ps_c_spec1_id`       BIGINT (20) DEFAULT NULL COMMENT '规格1ID',
    `ps_c_spec1_ecode`    VARCHAR(100)   DEFAULT NULL COMMENT '规格1编码',
    `ps_c_spec1_ename`    VARCHAR(100)   DEFAULT NULL COMMENT '规格1名称',
    `ps_c_spec2_id`       BIGINT (20) DEFAULT NULL COMMENT '规格2ID',
    `ps_c_spec2_ecode`    VARCHAR(100)   DEFAULT NULL COMMENT '规格2编码',
    `ps_c_spec2_ename`    VARCHAR(100)   DEFAULT NULL COMMENT '规格2名称',
    `ps_c_spec3_id`       BIGINT (20) DEFAULT NULL COMMENT '规格3ID',
    `ps_c_spec3_ecode`    VARCHAR(100)   DEFAULT NULL COMMENT '规格3编码',
    `ps_c_spec3_ename`    VARCHAR(100)   DEFAULT NULL COMMENT '规格3名称',
    `remark`              VARCHAR(255)   DEFAULT NULL COMMENT '备注',
    `version`             BIGINT (20) DEFAULT NULL COMMENT '版本号',
    `isactive`            CHAR(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`             BIGINT (20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`          VARCHAR(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`           VARCHAR(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`        datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`          BIGINT (20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`       VARCHAR(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`        VARCHAR(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`        datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '一手码配销仓特殊设置';

CREATE TABLE `sg_c_oneset_storage_gradient_qty`
(
    `id`                   BIGINT (20) NOT NULL COMMENT 'ID',
    `ad_client_id`         BIGINT (20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`            BIGINT (20) DEFAULT 27 COMMENT '所属组织',
    `qty_allocation_begin` DECIMAL(18, 4) DEFAULT 0 COMMENT '可分配库存开始量',
    `qty_allocation_end`   DECIMAL(18, 4) DEFAULT 0 COMMENT '可分配库存结束量',
    `qty`                  DECIMAL(18, 4) DEFAULT 0 COMMENT '一手码数量',
    `remark`               VARCHAR(255)   DEFAULT NULL COMMENT '备注',
    `version`              BIGINT (20) DEFAULT NULL COMMENT '版本号',
    `isactive`             CHAR(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`              BIGINT (20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`           VARCHAR(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`            VARCHAR(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`         datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`           BIGINT (20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`        VARCHAR(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`         VARCHAR(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`         datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '一手码库存阶梯数量';

CREATE TABLE `sg_c_shop_sku_sale_setting`
(
    `id`                BIGINT (20) NOT NULL COMMENT 'ID',
    `ad_client_id`      BIGINT (20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`         BIGINT (20) DEFAULT 27 COMMENT '所属组织',
    `cp_c_shop_id`      BIGINT (20) DEFAULT NULL COMMENT '平台店铺ID',
    `cp_c_shop_title`   VARCHAR(255)   DEFAULT NULL COMMENT '平台店铺标题',
    `ps_c_sku_id`       BIGINT (20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`    VARCHAR(100)   DEFAULT NULL COMMENT '条码编码',
    `ps_c_pro_id`       BIGINT (20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`    VARCHAR(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`    VARCHAR(255)   DEFAULT NULL COMMENT '商品名称',
    `ps_c_spec1_id`     BIGINT (20) DEFAULT NULL COMMENT '规格1ID',
    `ps_c_spec1_ecode`  VARCHAR(100)   DEFAULT NULL COMMENT '规格1编码',
    `ps_c_spec1_ename`  VARCHAR(100)   DEFAULT NULL COMMENT '规格1名称',
    `ps_c_spec2_id`     BIGINT (20) DEFAULT NULL COMMENT '规格2ID',
    `ps_c_spec2_ecode`  VARCHAR(100)   DEFAULT NULL COMMENT '规格2编码',
    `ps_c_spec2_ename`  VARCHAR(100)   DEFAULT NULL COMMENT '规格2名称',
    `ps_c_spec3_id`     BIGINT (20) DEFAULT NULL COMMENT '规格3ID',
    `ps_c_spec3_ecode`  VARCHAR(100)   DEFAULT NULL COMMENT '规格3编码',
    `ps_c_spec3_ename`  VARCHAR(100)   DEFAULT NULL COMMENT '规格3名称',
    `is_sale`           CHAR(1)        DEFAULT 'Y' COMMENT '当前是否售卖',
    `remark`            VARCHAR(255)   DEFAULT NULL COMMENT '备注',
    `version`           BIGINT (20) DEFAULT NULL COMMENT '版本号',
    `isactive`          CHAR(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`           BIGINT (20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`        VARCHAR(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`         VARCHAR(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`      datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`        BIGINT (20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`     VARCHAR(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`      VARCHAR(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`      datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '店铺商品售卖设置';

CREATE TABLE `sg_c_shop_allocation_gradient`
(
    `id`                   BIGINT (20) NOT NULL COMMENT 'ID',
    `ad_client_id`         BIGINT (20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`            BIGINT (20) DEFAULT 27 COMMENT '所属组织',
    `ename`                VARCHAR(255)   DEFAULT NULL COMMENT '名称',
    `qty_allocation_begin` DECIMAL(18, 4) DEFAULT 0 COMMENT '可分配库存开始量',
    `qty_allocation_end`   DECIMAL(18, 4) DEFAULT 0 COMMENT '可分配库存结束量',
    `remark`               VARCHAR(255)   DEFAULT NULL COMMENT '备注',
    `version`              BIGINT (20) DEFAULT NULL COMMENT '版本号',
    `isactive`             CHAR(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`              BIGINT (20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`           VARCHAR(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`            VARCHAR(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`         datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`           BIGINT (20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`        VARCHAR(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`         VARCHAR(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`         datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`     BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`    VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`    DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '店铺铺底梯度设置';

CREATE TABLE `sg_c_shop_allocation`
(
    `id`                               BIGINT (20) NOT NULL COMMENT 'ID',
    `ad_client_id`                     BIGINT (20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`                        BIGINT (20) DEFAULT 27 COMMENT '所属组织',
    `sg_c_shop_allocation_gradient_id` BIGINT (20) DEFAULT NULL COMMENT '店铺铺底梯度设置ID',
    `sg_c_sa_store_id`                 BIGINT (20) DEFAULT NULL COMMENT '配货仓ID',
    `sg_c_sa_store_ecode`              VARCHAR(100)   DEFAULT NULL COMMENT '配货仓编码',
    `sg_c_sa_store_ename`              VARCHAR(255)   DEFAULT NULL COMMENT '配货仓名称',
    `product_category`                 BIGINT (20) DEFAULT NULL COMMENT '商品类目',
    `qty_allocation_begin`             DECIMAL(18, 4) DEFAULT '0.0000' COMMENT '可分配库存开始量',
    `qty_allocation_end`               DECIMAL(18, 4) DEFAULT '0.0000' COMMENT '可分配库存结束量',
    `qty_oneset_target`                DECIMAL(18, 4) DEFAULT 0 COMMENT '目标手数',
    `remark`                           VARCHAR(255)   DEFAULT NULL COMMENT '备注',
    `version`                          BIGINT (20) DEFAULT NULL COMMENT '版本号',
    `isactive`                         CHAR(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                          BIGINT (20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                       VARCHAR(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                        VARCHAR(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`                     datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                       BIGINT (20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`                    VARCHAR(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`                     VARCHAR(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`                     datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`                 BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`                 BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`                 BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`                 BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`                 BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`                 BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`                 BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`                 BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`                 BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`                 BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`                VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`                VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`                VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`                VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`                VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`                VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`                VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`                VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`                VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`                VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`                DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`                DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`                DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`                DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`                DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`                DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`                DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`                DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`                DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`                DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '店铺铺底设置';

CREATE TABLE `sg_b_sa_target_storage_summary`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `ad_client_id`        BIGINT (20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`           BIGINT (20) DEFAULT 27 COMMENT '所属组织',
    `sg_c_sa_store_id`    BIGINT (20) DEFAULT NULL COMMENT '配货仓ID',
    `sg_c_sa_store_ecode` VARCHAR(100)   DEFAULT NULL COMMENT '配货仓编码',
    `sg_c_sa_store_ename` VARCHAR(255)   DEFAULT NULL COMMENT '配货仓名称',
    `ps_c_sku_id`         BIGINT (20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`      VARCHAR(100)   DEFAULT NULL COMMENT '条码编码',
    `ps_c_pro_id`         BIGINT (20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`      VARCHAR(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`      VARCHAR(255)   DEFAULT NULL COMMENT '商品名称',
    `ps_c_spec1_id`       BIGINT (20) DEFAULT NULL COMMENT '规格1ID',
    `ps_c_spec1_ecode`    VARCHAR(100)   DEFAULT NULL COMMENT '规格1编码',
    `ps_c_spec1_ename`    VARCHAR(100)   DEFAULT NULL COMMENT '规格1名称',
    `ps_c_spec2_id`       BIGINT (20) DEFAULT NULL COMMENT '规格2ID',
    `ps_c_spec2_ecode`    VARCHAR(100)   DEFAULT NULL COMMENT '规格2编码',
    `ps_c_spec2_ename`    VARCHAR(100)   DEFAULT NULL COMMENT '规格2名称',
    `ps_c_spec3_id`       BIGINT (20) DEFAULT NULL COMMENT '规格3ID',
    `ps_c_spec3_ecode`    VARCHAR(100)   DEFAULT NULL COMMENT '规格3编码',
    `ps_c_spec3_ename`    VARCHAR(100)   DEFAULT NULL COMMENT '规格3名称',
    `qty_target_storage`  DECIMAL(18, 4) DEFAULT 0 COMMENT '目标库存',
    `remark`              VARCHAR(255)   DEFAULT NULL COMMENT '备注',
    `version`             BIGINT (20) DEFAULT NULL COMMENT '版本号',
    `isactive`            CHAR(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`             BIGINT (20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`          VARCHAR(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`           VARCHAR(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`        datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`          BIGINT (20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`       VARCHAR(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`        VARCHAR(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`        datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`    BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`   VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`   DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '配销仓目标库存汇总';

CREATE TABLE `sg_c_ecom_activity_setting`
(
    `id`                BIGINT (20) NOT NULL COMMENT 'ID',
    `ad_client_id`      BIGINT (20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`         BIGINT (20) DEFAULT 27 COMMENT '所属组织',
    `bill_no`           VARCHAR(100)   DEFAULT NULL COMMENT '单据编号',
    `bill_date`         date           DEFAULT NULL COMMENT '单据日期',
    `cp_c_shop_id`      BIGINT (20) DEFAULT NULL COMMENT '平台店铺ID',
    `cp_c_shop_title`   VARCHAR(255)   DEFAULT NULL COMMENT '平台店铺标题',
    `status`            INT (11) DEFAULT NULL COMMENT '单据状态',
    `source_bill_type`  INT (11) DEFAULT NULL COMMENT '来源单据类型',
    `source_bill_id`    BIGINT (20) DEFAULT NULL COMMENT '来源单据ID',
    `source_bill_no`    VARCHAR(100)   DEFAULT NULL COMMENT '来源单据编号',
    `remark`            VARCHAR(255)   DEFAULT NULL COMMENT '备注',
    `version`           BIGINT (20) DEFAULT NULL COMMENT '版本号',
    `isactive`          CHAR(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`           BIGINT (20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`        VARCHAR(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`         VARCHAR(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`      datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`        BIGINT (20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`     VARCHAR(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`      VARCHAR(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`      datetime       DEFAULT NULL COMMENT '修改时间',
    `status_id`         BIGINT (20) DEFAULT NULL COMMENT '提交人ID',
    `status_ename`      VARCHAR(50)    DEFAULT NULL COMMENT '提交人姓名',
    `status_name`       VARCHAR(50)    DEFAULT NULL COMMENT '提交人用户名',
    `status_time`       datetime       DEFAULT NULL COMMENT '提交时间',
    `uncheck_id`        bigint(20) DEFAULT NULL COMMENT '取消提交人ID',
    `uncheck_ename`     varchar(50)    DEFAULT NULL COMMENT '取消提交人姓名',
    `uncheck_name`      varchar(50)    DEFAULT NULL COMMENT '取消提交人用户名',
    `uncheck_time`      datetime       DEFAULT NULL COMMENT '取消提交时间',
    `deler_id`          BIGINT (20) DEFAULT NULL COMMENT '作废人ID',
    `deler_ename`       VARCHAR(50)    DEFAULT NULL COMMENT '作废人姓名',
    `deler_name`        VARCHAR(50)    DEFAULT NULL COMMENT '作废人用户名',
    `del_time`          datetime       DEFAULT NULL COMMENT '作废时间',
    `reserve_bigint01`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`  BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10` VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10` DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = 'E-COM活动时间设置';

CREATE TABLE `sg_c_ecom_activity_setting_sa_item`
(
    `id`                            BIGINT (20) NOT NULL COMMENT 'ID',
    `ad_client_id`                  BIGINT (20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`                     BIGINT (20) DEFAULT 27 COMMENT '所属组织',
    `sg_c_ecom_activity_setting_id` BIGINT (20) DEFAULT NULL COMMENT 'E-COM活动时间设置ID',
    `activity_name`                 VARCHAR(255)   DEFAULT NULL COMMENT '活动名称',
    `sg_c_sa_store_id`              BIGINT (20) DEFAULT NULL COMMENT '配货仓ID',
    `sg_c_sa_store_ecode`           VARCHAR(100)   DEFAULT NULL COMMENT '配货仓编码',
    `sg_c_sa_store_ename`           VARCHAR(255)   DEFAULT NULL COMMENT '配货仓名称',
    `begin_time`                    datetime       DEFAULT NULL COMMENT '活动开始时间',
    `end_time`                      datetime       DEFAULT NULL COMMENT '活动结束时间',
    `freeze_time`                   datetime       DEFAULT NULL COMMENT '库存冻结时间',
    `remark`                        VARCHAR(255)   DEFAULT NULL COMMENT '备注',
    `version`                       BIGINT (20) DEFAULT NULL COMMENT '版本号',
    `isactive`                      CHAR(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                       BIGINT (20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                    VARCHAR(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                     VARCHAR(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`                  datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                    BIGINT (20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`                 VARCHAR(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`                  VARCHAR(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`                  datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = 'E-COM活动时间设置-配销仓明细';

CREATE TABLE `sg_c_ecom_activity_setting_pro_item`
(
    `id`                            BIGINT (20) NOT NULL COMMENT 'ID',
    `ad_client_id`                  BIGINT (20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`                     BIGINT (20) DEFAULT 27 COMMENT '所属组织',
    `sg_c_ecom_activity_setting_id` BIGINT (20) DEFAULT NULL COMMENT 'E-COM活动时间设置ID',
    `activity_name`                 VARCHAR(255)   DEFAULT NULL COMMENT '活动名称',
    `ps_c_pro_id`                   BIGINT (20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`                VARCHAR(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`                VARCHAR(255)   DEFAULT NULL COMMENT '商品名称',
    `sg_c_sa_store_id`              BIGINT (20) DEFAULT NULL COMMENT '配货仓ID',
    `sg_c_sa_store_ecode`           VARCHAR(100)   DEFAULT NULL COMMENT '配货仓编码',
    `sg_c_sa_store_ename`           VARCHAR(255)   DEFAULT NULL COMMENT '配货仓名称',
    `begin_time`                    datetime       DEFAULT NULL COMMENT '活动开始时间',
    `end_time`                      datetime       DEFAULT NULL COMMENT '活动结束时间',
    `freeze_time`                   datetime       DEFAULT NULL COMMENT '库存冻结时间',
    `remark`                        VARCHAR(255)   DEFAULT NULL COMMENT '备注',
    `version`                       BIGINT (20) DEFAULT NULL COMMENT '版本号',
    `isactive`                      CHAR(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                       BIGINT (20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                    VARCHAR(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                     VARCHAR(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`                  datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                    BIGINT (20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`                 VARCHAR(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`                  VARCHAR(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`                  datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`              BIGINT (20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`             VARCHAR(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`             DECIMAL(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = 'E-COM活动时间设置-商品明细';


CREATE TABLE `sg_b_auto_allocation_batch`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `ad_client_id`      bigint(20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`         bigint(20) DEFAULT 27 COMMENT '所属组织',
    `batch_no`          varchar(100)   DEFAULT NULL COMMENT '自动配货批次号',
    `bill_type`         int(11) DEFAULT 1 COMMENT '单据类型',
    `batch_status`      varchar(10)    DEFAULT 0 COMMENT '批次状态',
    `fail_reason`       mediumtext     DEFAULT NULL COMMENT '失败原因',
    `fail_count`        bigint(20) DEFAULT 0 COMMENT '失败次数',
    `remark`            varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`           bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`          char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`           bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`        varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`         varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`      datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`        bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`     varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`      varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`      datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='自动配货批次表';


CREATE TABLE `sg_b_auto_allocation_batch_item`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `ad_client_id`           bigint(20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`              bigint(20) DEFAULT 27 COMMENT '所属组织',
    `batch_no`               varchar(100)   DEFAULT NULL COMMENT '自动配货批次号',
    `sg_c_share_store_id`    bigint(20) DEFAULT NULL COMMENT '共享仓ID',
    `sg_c_share_store_ecode` varchar(20)    DEFAULT NULL COMMENT '共享仓编码',
    `sg_c_share_store_ename` varchar(255)   DEFAULT NULL COMMENT '共享仓名称',
    `sg_c_sa_store_id`       bigint(20) DEFAULT NULL COMMENT 'SA仓ID',
    `sg_c_sa_store_ecode`    varchar(20)    DEFAULT NULL COMMENT 'SA仓编码',
    `sg_c_sa_store_ename`    varchar(255)   DEFAULT NULL COMMENT 'SA仓名称',
    `ps_c_sku_id`            bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`         varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `qty`                    decimal(18, 4) DEFAULT 0 COMMENT '数量',
    `item_status`            varchar(10)    DEFAULT 0 COMMENT '明细状态',
    `remark`                 varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`                bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`               char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`             varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`              varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`           datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`             bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`          varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`           varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`           datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='自动配货批次明细表';

CREATE TABLE `sg_c_channel_store_safety_setting`
(

    `id`                bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`      bigint(20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`         bigint(20) DEFAULT 27 COMMENT '所属组织',
    `batch_no`          varchar(100)   DEFAULT NULL COMMENT '批次号',
    `status`            int(11) DEFAULT NULL COMMENT '单据状态',
    `remark`            varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`           bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`          char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`           bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`        varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`         varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`      datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`        bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`     varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`      varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`      datetime       DEFAULT NULL COMMENT '修改时间',
    `status_id`         bigint(20) DEFAULT NULL COMMENT '提交人ID',
    `status_ename`      varchar(50)    DEFAULT NULL COMMENT '提交人姓名',
    `status_name`       varchar(50)    DEFAULT NULL COMMENT '提交人用户名',
    `status_time`       datetime       DEFAULT NULL COMMENT '提交时间',
    `deler_id`          bigint(20) DEFAULT NULL COMMENT '作废人ID',
    `deler_ename`       varchar(50)    DEFAULT NULL COMMENT '作废人姓名',
    `deler_name`        varchar(50)    DEFAULT NULL COMMENT '作废人用户名',
    `del_time`          datetime       DEFAULT NULL COMMENT '作废时间',
    `reserve_bigint01`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`  bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10` varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10` decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='平台店铺安全库存批量设置';

CREATE TABLE `sg_c_channel_store_safety_setting_item`
(

    `id`                                   bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`                         bigint(20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`                            bigint(20) DEFAULT 27 COMMENT '所属组织',
    `sg_c_channel_store_safety_setting_id` bigint(20) DEFAULT NULL COMMENT '平台店铺安全库存批量设置id',
    `cp_c_shop_id`                         bigint(20) DEFAULT NULL COMMENT '平台店铺ID',
    `cp_c_shop_title`                      varchar(255)   DEFAULT NULL COMMENT '平台店铺标题',
    `pro_type`                             varchar(10)    DEFAULT NULL COMMENT '商品类别',
    `ps_c_sku_id`                          bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`                       varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `numiid`                               varchar(100)   DEFAULT NULL COMMENT '平台商品ID',
    `sku_id`                               varchar(100)   DEFAULT NULL COMMENT '平台条码ID',
    `ps_c_pro_id`                          bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`                       varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`                       varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `qty_safety`                           decimal(18, 4) DEFAULT 0 COMMENT '安全库存',
    `remark`                               varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`                              bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`                             char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                              bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                           varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                            varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`                         datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                           bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`                        varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`                         varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`                         datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`                     bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`                    varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`                    decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='平台店铺安全库存批量设置明细';


CREATE TABLE `sg_b_share_sa_transfer`
(
    `id`                      bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`            bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`               bigint(20) DEFAULT '27' COMMENT '所属组织',
    `bill_no`                 varchar(100)   default null comment '单据编号',
    `bill_date`               date           default null comment '单据日期',
    `sender_sa_store_id`      bigint(20) DEFAULT NULL COMMENT '配销发货仓ID',
    `sender_sa_store_ecode`   varchar(100)   DEFAULT NULL COMMENT '配销发货仓编码',
    `sender_sa_store_ename`   varchar(255)   DEFAULT NULL COMMENT '配销发货仓名称',
    `receiver_sa_store_id`    bigint(20) DEFAULT NULL COMMENT '配销收货仓ID',
    `receiver_sa_store_ecode` varchar(100)   DEFAULT NULL COMMENT '配销收货仓编码',
    `receiver_sa_store_ename` varchar(255)   DEFAULT NULL COMMENT '配销收货仓名称',
    `source_bill_type`        int(11) default null comment '来源单类型',
    `source_bill_id`          bigint(20) default null comment '来源单ID',
    `source_bill_no`          varchar(100)   default null comment '来源单号',
    `tot_row_num`             int(11) DEFAULT '0' COMMENT '总行数',
    `tot_qty`                 decimal(18, 4) DEFAULT '0.0000' COMMENT '总调拨数量',
    `tot_amt`                 decimal(18, 4) DEFAULT '0.0000' COMMENT '总吊牌金额',
    `remark`                  varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`                 bigint(20) DEFAULT NULL COMMENT '版本号',
    `status`                  int(11) DEFAULT NULL COMMENT '单据状态',
    `ownerid`                 bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`              varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`               varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`            datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`              bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`           varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`            varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`            datetime       DEFAULT NULL COMMENT '修改时间',
    `status_id`               int(11) DEFAULT '0' COMMENT '提交人ID',
    `status_ename`            varchar(50)    DEFAULT NULL COMMENT '提交人姓名',
    `status_name`             varchar(50)    DEFAULT NULL COMMENT '提交人用户名',
    `status_time`             datetime       DEFAULT NULL COMMENT '提交时间',
    `deler_id`                bigint(20) DEFAULT NULL COMMENT '作废人ID',
    `deler_ename`             varchar(50)    DEFAULT NULL COMMENT '作废人姓名',
    `deler_name`              varchar(50)    DEFAULT NULL COMMENT '作废人用户名',
    `del_time`                datetime       DEFAULT NULL COMMENT '作废时间',
    `isactive`                char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `reserve_bigint01`        bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`        bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`        bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`        bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`        bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`        bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`        bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`        bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`        bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`        bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`       varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`       varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`       varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`       varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`       varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`       varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`       varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`       varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`       varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`       varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`       decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`       decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`       decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`       decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`       decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`       decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`       decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`       decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`       decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`       decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配销仓调拨单';

CREATE TABLE `sg_b_share_sa_transfer_item`
(
    `id`                        bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`              bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                 bigint(20) DEFAULT '27' COMMENT '所属组织',
    `sg_b_share_sa_transfer_id` bigint(20) DEFAULT NULL COMMENT '配销调拨单ID',
    `ps_c_sku_id`               bigint(20) DEFAULT NULL COMMENT '条码ID',
    `ps_c_sku_ecode`            varchar(100)   DEFAULT NULL COMMENT '条码编码',
    `ps_c_pro_id`               bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`            varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`            varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `gbcode`                    varchar(100)   DEFAULT NULL COMMENT '国标码',
    `ps_c_spec1_id`             bigint(20) DEFAULT NULL COMMENT '规格1ID，颜色',
    `ps_c_spec1_ecode`          varchar(100)   DEFAULT NULL COMMENT '规格1编码',
    `ps_c_spec1_ename`          varchar(100)   DEFAULT NULL COMMENT '规格1名称',
    `ps_c_spec2_id`             bigint(20) DEFAULT NULL COMMENT '规格2ID，尺寸',
    `ps_c_spec2_ecode`          varchar(100)   DEFAULT NULL COMMENT '规格2编码',
    `ps_c_spec2_ename`          varchar(100)   DEFAULT NULL COMMENT '规格2名称',
    `price_list`                decimal(18, 4) DEFAULT '0.0000' COMMENT '吊牌价',
    `qty`                       decimal(18, 4) DEFAULT '0.0000' COMMENT '调拨数量',
    `amt`                       decimal(18, 4) DEFAULT '0.0000' COMMENT '吊牌金额',
    `remark`                    varchar(255)   DEFAULT NULL COMMENT '备注',
    `ownerid`                   bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                 varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`              datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`             varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`              varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`              datetime       DEFAULT NULL COMMENT '修改时间',
    `isactive`                  char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `reserve_bigint01`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`          bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`         varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`         decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配销仓调拨单明细';


ALTER TABLE sg_c_sa_store
    ADD COLUMN `is_auto_allocation` char(1) DEFAULT 'N' COMMENT '是否参与自动分货' after `ename`;
ALTER TABLE sg_c_sa_store
    ADD COLUMN `product_category` bigint(20) DEFAULT NULL COMMENT '商品类目' after `is_auto_allocation`;
ALTER TABLE sg_c_sa_store
    ADD COLUMN `priority_allocation` int(11) DEFAULT '0' COMMENT '分货优先级' after `product_category`;
ALTER TABLE sg_c_share_store
    ADD COLUMN `is_auto_allocation` char(1) DEFAULT 'N' COMMENT '是否参与自动分货' after `ename`;

CREATE TABLE `sg_c_ecom_product_maint`
(
    `id`                     bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`           bigint(20) DEFAULT 37 COMMENT '所属公司',
    `ad_org_id`              bigint(20) DEFAULT 27 COMMENT '所属组织',
    `ps_c_pro_id`            bigint(20) DEFAULT NULL COMMENT '商品ID',
    `ps_c_pro_ecode`         varchar(20)    DEFAULT NULL COMMENT '商品编码',
    `ps_c_pro_ename`         varchar(255)   DEFAULT NULL COMMENT '商品名称',
    `product_category`       bigint(20) DEFAULT NULL COMMENT '商品类目',
    `size_sort`              varchar(50)    DEFAULT NULL COMMENT '大小童',
    `tale_sort`              varchar(500)   DEFAULT NULL COMMENT '故事分类',
    `season`                 varchar(80)    DEFAULT NULL COMMENT '季节',
    `inline_season`          varchar(80)    DEFAULT NULL COMMENT 'inLine Season',
    `stockout_rate`          decimal(10, 2) DEFAULT 0 COMMENT '库存售罄',
    `clf`                    varchar(80)    DEFAULT NULL COMMENT '内部CLF',
    `attribute`              varchar(80)    DEFAULT NULL COMMENT '属性',
    `responsibility_channel` varchar(80)    DEFAULT NULL COMMENT '责任渠道',
    `category_attributes`    varchar(80)    DEFAULT NULL COMMENT '类目属性',
    `remark`                 varchar(255)   DEFAULT NULL COMMENT '备注',
    `version`                bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`               char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`             varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`              varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`           datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`             bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`          varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`           varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`           datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`       bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`      varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`      decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='电商商品属性维护';

ALTER TABLE `sg_b_monitor_mq_error_datas`
    ADD COLUMN `msg_id` varchar(255) NULL COMMENT '消息ID' AFTER `tag`,
ADD COLUMN `partition`  int(11) NULL COMMENT '分区' AFTER `sharding_key`;

ALTER TABLE `sg_b_share_out`
    ADD INDEX `idx_sg_b_share_out_05` (`source_bill_type`, `source_bill_id`) USING BTREE;

ALTER TABLE `sg_b_share_out`
    ADD INDEX `idx_sg_b_share_out_06` (`source_bill_id`) USING BTREE;

ALTER TABLE `cp_c_store`
    ADD INDEX `idx_cp_c_store__05` (`sg_c_share_store_id`) USING BTREE;

alter table sg_b_channel_product
    add column `trans_time` datetime DEFAULT NULL COMMENT '平台库存转换时间';

ALTER TABLE `sg_b_sa_target_storage_summary`
    ADD COLUMN `sg_c_share_store_id` bigint(20) DEFAULT NULL  COMMENT '共享仓ID',
    ADD COLUMN `sg_c_share_store_ecode` varchar(20) DEFAULT NULL  COMMENT '共享仓编码',
    ADD COLUMN `sg_c_share_store_ename` varchar(255) DEFAULT NULL  COMMENT '共享仓名称',
    ADD COLUMN `qty_share_unshared_preout` decimal(18,4) DEFAULT 0 COMMENT '聚合仓非共享占用数量',
    ADD COLUMN `qty_share_shared_preout` decimal(18,4) DEFAULT 0 COMMENT '聚合仓共享占用数量',
    ADD COLUMN `qty_share_preout` decimal(18,4) DEFAULT 0 COMMENT '聚合仓占用数量',
    ADD COLUMN `qty_share_prein` decimal(18,4) DEFAULT 0 COMMENT '聚合仓在途数量',
    ADD COLUMN `qty_share_freeze` decimal(18,4) DEFAULT 0 COMMENT '聚合仓冻结数量',
    ADD COLUMN `qty_share_sa_preout` decimal(18,4) DEFAULT 0 COMMENT '聚合仓配销总占用数量',
    ADD COLUMN `qty_share_sa_storage` decimal(18,4) DEFAULT 0 COMMENT '聚合仓配销总在库数量',
    ADD COLUMN `qty_share_storage` decimal(18,4) DEFAULT 0 COMMENT '聚合仓在库数量',
    ADD COLUMN `qty_share_sale_available` decimal(18,4) DEFAULT 0 COMMENT '聚合仓可售数量',
    ADD COLUMN `qty_store_available` decimal(18,4) DEFAULT 0 COMMENT '逻辑仓可用数量',
    ADD COLUMN `qty_share_available` decimal(18,4) DEFAULT 0 COMMENT '聚合仓可用数量',
    ADD COLUMN `qty_sp_preout` decimal(18,4) DEFAULT 0 COMMENT '共享池可用数量',
    ADD COLUMN `qty_sp_storage` decimal(18,4) DEFAULT 0 COMMENT '共享池在库数量',
    ADD COLUMN `qty_sa_available` decimal(18,4) DEFAULT 0 COMMENT '聚合仓可用数量',
    ADD COLUMN `qty_stock_depth_target` decimal(18,4) DEFAULT 0 COMMENT '库存目标深度',
    ADD COLUMN `qty_stock_depth_begin` decimal(18,4) DEFAULT 0 COMMENT '库存深度开始数量',
    ADD COLUMN `qty_stock_depth_end` decimal(18,4) DEFAULT 0 COMMENT '库存深度结束数量';

CREATE UNIQUE index index_unique on sg_c_oneset_setting (sg_c_share_store_ecode, ps_c_sku_ecode);
CREATE UNIQUE index index_unique on sg_b_sa_target_storage_summary (sg_c_sa_store_ecode, ps_c_sku_ecode);

ALTER TABLE sg_c_oneset_setting
    ADD COLUMN `is_maincode` varchar(80) DEFAULT '0' COMMENT '是否主码' after `qty`;
ALTER TABLE sg_c_shop_allocation
    ADD COLUMN `ware_type` bigint(20) DEFAULT NULL COMMENT '商品部类' after `sg_c_sa_store_ename`;

ALTER TABLE `sg_b_sa_target_storage_summary`
    ADD COLUMN `attribute` varchar(80) DEFAULT NULL COMMENT '属性',
    ADD COLUMN `channel_name` varchar(50) DEFAULT NULL  COMMENT '主控渠道',
    ADD COLUMN `product_level` varchar(50) DEFAULT NULL  COMMENT '商品级别',
    ADD COLUMN `drp_style` varchar(50) DEFAULT NULL  COMMENT '型号(Style)',
    ADD COLUMN `clf` varchar(80) DEFAULT NULL  COMMENT '内部CLF',
    ADD COLUMN `stockout_rate` decimal(10,2) DEFAULT 0 COMMENT '库存售罄',
    ADD COLUMN `inline_season` varchar(80) DEFAULT NULL  COMMENT 'inLine Season',
    ADD COLUMN `qty_sa_allocation` decimal(18,4) DEFAULT 0 COMMENT '配销仓待分配量',
    ADD COLUMN `qty_allocation_begin` decimal(18,4) DEFAULT 0 COMMENT '店铺铺底策略起始量',
    ADD COLUMN `qty_allocation_end` decimal(18,4) DEFAULT 0 COMMENT '店铺铺底策略终止量',
    ADD COLUMN `qty_oneset_target` decimal(18,4) DEFAULT 0 COMMENT '店铺铺底目标手数',
    ADD COLUMN `qty_oneset` decimal(18,4) DEFAULT 0 COMMENT '一手码数量',
    ADD COLUMN `season` varchar(80) DEFAULT NULL  COMMENT '季节',
    ADD COLUMN `price_list` decimal(18,4) DEFAULT 0 COMMENT '吊牌价',
    ADD COLUMN `channel` varchar(50) DEFAULT NULL  COMMENT '渠道',
    ADD COLUMN `tale_sort` varchar(500) DEFAULT NULL  COMMENT '故事分类';

ALTER TABLE `sg_b_storage`
    ADD INDEX `idx_sg_b_storage_05` (`cp_c_store_ecode`) USING BTREE;

ALTER TABLE `sg_b_sto_in_notices`
    ADD INDEX `idx_sg_b_sto_in_notices_07` (`cp_c_phy_warehouse_id`) USING BTREE;

ALTER TABLE `sg_b_sto_out_notices`
    ADD INDEX `idx_sg_b_sto_out_notices_07` (`sourcecode`(50)) USING BTREE;

ALTER TABLE `sg_b_channel_product`
    ADD COLUMN `qty_channel` decimal(18,4) DEFAULT 0 COMMENT '渠道库存' after `qty_storage`;
ALTER TABLE `sg_b_channel_product`
    ADD COLUMN `qty_differences` decimal(18,4) DEFAULT 0 COMMENT '渠道库存和平台库存的差异值' after `qty_channel`;

ALTER TABLE `sg_b_channel_product`
MODIFY COLUMN `qty_storage`  decimal(18,4) NULL DEFAULT 0 COMMENT '平台库存';

create table sg_c_channel_sku_strategy_log
(
    id                bigint not null comment 'ID'
        primary key,
    operation_type    varchar(50) null comment '操作类型',
    record_id         bigint null comment '修改对象ID',
    update_model_name varchar(200) null comment '修改对象名称',
    mod_content       varchar(500) null comment '修改内容',
    before_data       varchar(1000) null comment '操作前',
    after_data        varchar(1000) null comment '操作后',
    ad_org_id         bigint   default 27 null comment '所属组织',
    isactive          char     default 'Y' null comment '可用',
    ad_client_id      bigint   default 37 null comment '所属公司',
    ownerid           bigint null comment '创建人',
    ownerename        varchar(50) null comment '创建人姓名',
    ownername         varchar(50) null comment '操作人',
    creationdate      datetime default CURRENT_TIMESTAMP null comment '操作时间',
    modifierid        bigint null comment '修改人',
    modifierename     varchar(50) null comment '修改人姓名',
    modifiername      varchar(50) null comment '修改人名称',
    modifieddate      datetime default CURRENT_TIMESTAMP null comment '修改时间'
) comment '特殊条码按比例同步策略日志表' charset = utf8mb4;

create index idx_sg_c_channel_SKU_STRATEGY_log_01
    on sg_c_channel_ratio_strategy_log (record_id);
