ALTER TABLE sg_b_share_transfer_import_item CHANGE  transfer_latitude  transfer_dimension  INT(11);

ALTER TABLE `sg_b_share_transfer_item`
    ADD COLUMN `transfer_dimension`  INT(11)  DEFAULT NULL COMMENT '调拨拨纬度';

ALTER TABLE `sg_b_share_transfer_item`
    modify COLUMN `item_status`  INT(1)  DEFAULT 0 COMMENT '明细单据状态--是否添加';

ALTER TABLE `sg_b_share_transfer_import_item`
    modify COLUMN `item_status`  INT(1)  DEFAULT 0 COMMENT '明细单据状态--是否添加';
