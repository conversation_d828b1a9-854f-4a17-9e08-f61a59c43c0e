create table sg_c_channel_stock_control_log
(
    `id`             bigint not null comment 'ID',
    `ad_client_id`   bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`      bigint(20) DEFAULT '27' COMMENT '所属组织',
    `operation_type` varchar(50) null comment '操作类型',
    `mod_content`    mediumtext null comment '修改内容',
    `isactive`       char(1)     DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`        bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`     varchar(50) DEFAULT NULL COMMENT '创建人姓名',
    `ownername`      varchar(50) DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`   datetime    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifierid`     bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`  varchar(50) DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`   varchar(50) DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`   datetime    DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (ID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='平台店铺库存管理通用日志表';


CREATE TABLE `sg_b_channel_product_download_buffer`
(
    `id`                  bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`        bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`           bigint(20) DEFAULT '27' COMMENT '所属组织',
    `cp_c_shop_id`        bigint(20) DEFAULT NULL COMMENT '平台店铺ID',
    `cp_c_platform_id`    bigint(20) DEFAULT NULL COMMENT '平台类型',
    `numiid`              varchar(255)   DEFAULT NULL COMMENT '平台款号ID',
    `download_status`     int(11) DEFAULT '0' COMMENT '下载状态:-1 失败;0 未下载；1 下载中; 2 成功',
    `download_fail_count` int(11) DEFAULT '0' COMMENT '下载失败次数',
    `isactive`            char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`             bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`          varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`           varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`        datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifierid`          bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`       varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`        varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`        datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `reserve_bigint01`    bigint(20) DEFAULT NULL,
    `reserve_bigint02`    bigint(20) DEFAULT NULL,
    `reserve_bigint03`    bigint(20) DEFAULT NULL,
    `reserve_bigint04`    bigint(20) DEFAULT NULL,
    `reserve_bigint05`    bigint(20) DEFAULT NULL,
    `reserve_bigint06`    bigint(20) DEFAULT NULL,
    `reserve_bigint07`    bigint(20) DEFAULT NULL,
    `reserve_bigint08`    bigint(20) DEFAULT NULL,
    `reserve_bigint09`    bigint(20) DEFAULT NULL,
    `reserve_bigint10`    bigint(20) DEFAULT NULL,
    `reserve_varchar01`   varchar(200)   DEFAULT NULL,
    `reserve_varchar02`   varchar(200)   DEFAULT NULL,
    `reserve_varchar03`   varchar(200)   DEFAULT NULL,
    `reserve_varchar04`   varchar(200)   DEFAULT NULL,
    `reserve_varchar05`   varchar(200)   DEFAULT NULL,
    `reserve_varchar06`   varchar(200)   DEFAULT NULL,
    `reserve_varchar07`   varchar(200)   DEFAULT NULL,
    `reserve_varchar08`   varchar(200)   DEFAULT NULL,
    `reserve_varchar09`   varchar(200)   DEFAULT NULL,
    `reserve_varchar10`   varchar(200)   DEFAULT NULL,
    `reserve_decimal01`   decimal(18, 4) DEFAULT NULL,
    `reserve_decimal02`   decimal(18, 4) DEFAULT NULL,
    `reserve_decimal03`   decimal(18, 4) DEFAULT NULL,
    `reserve_decimal04`   decimal(18, 4) DEFAULT NULL,
    `reserve_decimal05`   decimal(18, 4) DEFAULT NULL,
    `reserve_decimal06`   decimal(18, 4) DEFAULT NULL,
    `reserve_decimal07`   decimal(18, 4) DEFAULT NULL,
    `reserve_decimal08`   decimal(18, 4) DEFAULT NULL,
    `reserve_decimal09`   decimal(18, 4) DEFAULT NULL,
    `reserve_decimal10`   decimal(18, 4) DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_sg_b_channel_product_download_buffer_01` (`numiid`,`cp_c_shop_id`,`cp_c_platform_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='下载平台商品计算缓存池';


CREATE TABLE `sg_c_channel_source_strategy_warehouse_item`
(
    `id`                              bigint(20) NOT NULL COMMENT 'ID',
    `ad_client_id`                    bigint(20) DEFAULT '37' COMMENT '所属公司',
    `ad_org_id`                       bigint(20) DEFAULT '27' COMMENT '所属组织',
    `sg_c_channel_source_strategy_id` bigint(20) DEFAULT NULL COMMENT '寻源策略表id',
    `cp_c_phy_warehouse_id`           bigint(20) DEFAULT NULL COMMENT '实体仓主键id',
    `cp_c_phy_warehouse_ecode`        varchar(100)   DEFAULT NULL COMMENT '实体仓ecode',
    `cp_c_phy_warehouse_ename`        varchar(100)   DEFAULT NULL COMMENT '实体仓ename',
    `priority`                        int(11) DEFAULT NULL COMMENT '优先级',
    `version`                         bigint(20) DEFAULT NULL COMMENT '版本号',
    `isactive`                        char(1)        DEFAULT 'Y' COMMENT '是否可用',
    `ownerid`                         bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `ownerename`                      varchar(50)    DEFAULT NULL COMMENT '创建人姓名',
    `ownername`                       varchar(50)    DEFAULT NULL COMMENT '创建人用户名',
    `creationdate`                    datetime       DEFAULT NULL COMMENT '创建时间',
    `modifierid`                      bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifierename`                   varchar(50)    DEFAULT NULL COMMENT '修改人姓名',
    `modifiername`                    varchar(50)    DEFAULT NULL COMMENT '修改人用户名',
    `modifieddate`                    datetime       DEFAULT NULL COMMENT '修改时间',
    `reserve_bigint01`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
    `reserve_bigint02`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
    `reserve_bigint03`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
    `reserve_bigint04`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
    `reserve_bigint05`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
    `reserve_bigint06`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
    `reserve_bigint07`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
    `reserve_bigint08`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
    `reserve_bigint09`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
    `reserve_bigint10`                bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
    `reserve_varchar01`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)1',
    `reserve_varchar02`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)2',
    `reserve_varchar03`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)3',
    `reserve_varchar04`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)4',
    `reserve_varchar05`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)5',
    `reserve_varchar06`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)6',
    `reserve_varchar07`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)7',
    `reserve_varchar08`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)8',
    `reserve_varchar09`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)9',
    `reserve_varchar10`               varchar(200)   DEFAULT NULL COMMENT '备用字段(文本型)10',
    `reserve_decimal01`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
    `reserve_decimal02`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
    `reserve_decimal03`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
    `reserve_decimal04`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
    `reserve_decimal05`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
    `reserve_decimal06`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
    `reserve_decimal07`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
    `reserve_decimal08`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
    `reserve_decimal09`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
    `reserve_decimal10`               decimal(18, 4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                               `idx_sg_c_channel_source_strategy_id` (`sg_c_channel_source_strategy_id`),
    KEY                               `idx_cp_c_phy_warehouse_id` (`cp_c_phy_warehouse_id`),
    KEY                               `idx_cp_c_phy_warehouse_ecode` (`cp_c_phy_warehouse_ecode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='仓优先策略明细表';

ALTER TABLE `sg_b_channel_product`
    ADD COLUMN `is_special` char(1) DEFAULT 'N' COMMENT '是否特殊商品';










