# 库存同步排除实体仓
create table sg_c_channel_shop_appoint_warehouse
(
    id                     bigint                        not null comment 'ID'
        primary key,
    ad_client_id           bigint         default 37     null comment '所属公司',
    ad_org_id              bigint         default 27     null comment '所属组织',
    bill_no                varchar(100)                  null comment '单据编号',
    cp_c_shop_id          bigint                        null comment '平台店铺ID',
    cp_c_shop_title       varchar(255)                  null comment '平台店铺标题',
    cp_c_shop_ecode       varchar(50)                   null comment '店铺编码',
    begin_time              date                          null comment '开始时间',
    end_time              date                          null comment '结束时间',
    status                 int                           null comment '单据状态',
    remark                 varchar(255)                  null comment '备注',
    version                bigint                        null comment '版本号',
    isactive               char           default 'Y'    null comment '是否可用',
    ownerid                bigint                        null comment '创建人ID',
    ownerename             varchar(50)                   null comment '创建人姓名',
    ownername              varchar(50)                   null comment '创建人用户名',
    creationdate           datetime                      null comment '创建时间',
    modifierid             bigint                        null comment '修改人ID',
    modifierename          varchar(50)                   null comment '修改人姓名',
    modifiername           varchar(50)                   null comment '修改人用户名',
    modifieddate           datetime                      null comment '修改时间',
    deler_id               bigint                        null comment '作废人ID',
    deler_ename            varchar(50)                   null comment '作废人姓名',
    deler_name             varchar(50)                   null comment '作废人用户名',
    del_time               datetime                      null comment '作废时间',
    status_id           bigint                        null comment '提交人ID',
    status_time         datetime                      null comment '提交时间',
    status_ename        varchar(50)                   null comment '提交人姓名',
    status_name         varchar(50)                   null comment '提交人用户名',
    event_id           bigint                        null comment '结案人ID',
    event_time         datetime                      null comment '结案时间',
    event_ename        varchar(50)                   null comment '结案人姓名',
    event_name         varchar(50)                   null comment '结案人用户名',
    uncheck_id bigint null comment '取消提交人ID',
    uncheck_ename varchar(50) null comment '取消提交人姓名',
    uncheck_name varchar(50) null comment '取消提交人用户名',
    uncheck_time datetime null comment '取消提交时间'

)
    comment '店铺指定实体仓设置';

create table sg_c_channel_shop_appoint_warehouse_appoint_item
(
    id                     bigint                        not null comment 'ID'
        primary key,
    ad_client_id           bigint         default 37     null comment '所属公司',
    ad_org_id              bigint         default 27     null comment '所属组织',
    sg_c_channel_shop_appoint_warehouse_id bigint null comment '主表id',
    cp_c_store_id       bigint                        null comment '逻辑仓ID',
    cp_c_store_ecode    varchar(100)                  null comment '逻辑仓编码',
    cp_c_store_ename    varchar(255)                  null comment '逻辑仓名称',
    version                bigint                        null comment '版本号',
    isactive               char           default 'Y'    null comment '是否可用',
    ownerid                bigint                        null comment '创建人ID',
    ownerename             varchar(50)                   null comment '创建人姓名',
    ownername              varchar(50)                   null comment '创建人用户名',
    creationdate           datetime                      null comment '创建时间',
    modifierid             bigint                        null comment '修改人ID',
    modifierename          varchar(50)                   null comment '修改人姓名',
    modifiername           varchar(50)                   null comment '修改人用户名',
    modifieddate           datetime                      null comment '修改时间'
)
    comment '店铺指定实体仓设置指定明细';


create table sg_c_channel_shop_appoint_warehouse_exclude_item
(
    id                     bigint                        not null comment 'ID'
        primary key,
    ad_client_id           bigint         default 37     null comment '所属公司',
    ad_org_id              bigint         default 27     null comment '所属组织',
    sg_c_channel_shop_appoint_warehouse_id bigint null comment '主表id',
    cp_c_store_id       bigint                        null comment '逻辑仓ID',
    cp_c_store_ecode    varchar(100)                  null comment '逻辑仓编码',
    cp_c_store_ename    varchar(255)                  null comment '逻辑仓名称',
    version                bigint                        null comment '版本号',
    isactive               char           default 'Y'    null comment '是否可用',
    ownerid                bigint                        null comment '创建人ID',
    ownerename             varchar(50)                   null comment '创建人姓名',
    ownername              varchar(50)                   null comment '创建人用户名',
    creationdate           datetime                      null comment '创建时间',
    modifierid             bigint                        null comment '修改人ID',
    modifierename          varchar(50)                   null comment '修改人姓名',
    modifiername           varchar(50)                   null comment '修改人用户名',
    modifieddate           datetime                      null comment '修改时间'
)
    comment '店铺指定实体仓设置排除明细';

