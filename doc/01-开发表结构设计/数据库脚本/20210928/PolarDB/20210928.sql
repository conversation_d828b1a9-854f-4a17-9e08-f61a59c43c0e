/*虚拟批量逻辑调拨单*/
CREATE TABLE `sg_b_sto_batch_transfer` (
  `id` bigint(20) NOT NULL COMMENT 'ID',
  `ad_client_id` bigint(20) DEFAULT '37' COMMENT '所属公司',
  `ad_org_id` bigint(20) DEFAULT '27' COMMENT '所属组织',
  `bill_no` varchar(100) DEFAULT NULL COMMENT '单据编号',
  `bill_date` date DEFAULT NULL COMMENT '单据日期',
  `status` int(11) DEFAULT NULL COMMENT '单据状态',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `version` bigint(20) DEFAULT NULL COMMENT '版本号',
  `isactive` char(1) DEFAULT 'Y' COMMENT '是否可用',
  `is_auto_confirm` char(1) DEFAULT 'Y' COMMENT '是否提交确认调拨单',
  `tot_row_num` int(11) DEFAULT '0' COMMENT '总行数',
  `tot_qty` decimal(18,4) DEFAULT '0.0000' COMMENT '总数量',
  `tot_qty_out` decimal(18,4) DEFAULT '0.0000' COMMENT '总实际调拨数量',
  `tot_amt` decimal(18,4) DEFAULT '0.0000' COMMENT '总吊牌金额',
  `fail_row_num` decimal(18,4) DEFAULT '0.0000' COMMENT '失败行数',
  `success_row_num` decimal(18,4) DEFAULT '0.0000' COMMENT '成功行数',
  `ownerid` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `ownerename` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `ownername` varchar(50) DEFAULT NULL COMMENT '创建人用户名',
  `creationdate` datetime DEFAULT NULL COMMENT '创建时间',
  `modifierid` bigint(20) DEFAULT NULL COMMENT '修改人ID',
  `modifierename` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
  `modifiername` varchar(50) DEFAULT NULL COMMENT '修改人用户名',
  `modifieddate` datetime DEFAULT NULL COMMENT '修改时间',
  `deler_id` bigint(20) DEFAULT NULL COMMENT '作废人ID',
  `deler_ename` varchar(50) DEFAULT NULL COMMENT '作废人姓名',
  `deler_name` varchar(50) DEFAULT NULL COMMENT '作废人用户名',
  `del_time` datetime DEFAULT NULL COMMENT '作废时间',
  `status_id` bigint(20) DEFAULT NULL COMMENT '提交人ID',
  `status_ename` varchar(50) DEFAULT NULL COMMENT '提交人姓名',
  `status_name` varchar(50) DEFAULT NULL COMMENT '提交人用户名',
  `status_time` datetime DEFAULT NULL COMMENT '提交时间',
  `reserve_bigint01` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
  `reserve_bigint02` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
  `reserve_bigint03` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
  `reserve_bigint04` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
  `reserve_bigint05` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
  `reserve_bigint06` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
  `reserve_bigint07` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
  `reserve_bigint08` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
  `reserve_bigint09` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
  `reserve_bigint10` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
  `reserve_varchar01` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)1',
  `reserve_varchar02` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)2',
  `reserve_varchar03` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)3',
  `reserve_varchar04` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)4',
  `reserve_varchar05` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)5',
  `reserve_varchar06` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)6',
  `reserve_varchar07` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)7',
  `reserve_varchar08` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)8',
  `reserve_varchar09` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)9',
  `reserve_varchar10` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)10',
  `reserve_decimal01` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
  `reserve_decimal02` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
  `reserve_decimal03` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
  `reserve_decimal04` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
  `reserve_decimal05` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
  `reserve_decimal06` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
  `reserve_decimal07` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
  `reserve_decimal08` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
  `reserve_decimal09` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
  `reserve_decimal10` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='虚拟批量逻辑调拨单';

/*虚拟批量逻辑调拨单明细*/
CREATE TABLE `sg_b_sto_batch_transfer_item` (
  `id` bigint(20) NOT NULL COMMENT 'ID',
  `ad_client_id` bigint(20) DEFAULT '37' COMMENT '所属公司',
  `ad_org_id` bigint(20) DEFAULT '27' COMMENT '所属组织',
  `item_status` int(11) DEFAULT NULL COMMENT '状态',
  `sg_b_sto_batch_transfer_id` bigint(20) DEFAULT NULL COMMENT '所属虚拟批量逻辑调拨单',
  `sg_b_sto_transfer_id` bigint(20) DEFAULT NULL COMMENT '逻辑调拨单id',
  `sg_b_sto_transfer_bill_no` varchar(50) DEFAULT NULL COMMENT '逻辑调拨单编码',
  `sender_store_id` bigint(20) DEFAULT NULL COMMENT '发货逻辑仓ID',
  `sender_store_ecode` varchar(100) DEFAULT NULL COMMENT '发货逻辑仓编码',
  `sender_store_ename` varchar(255) DEFAULT NULL COMMENT '发货逻辑仓名称',
  `sender_customer_id` bigint(20) DEFAULT NULL COMMENT '发货经销商ID',
  `sender_customer_ecode` varchar(50) DEFAULT NULL COMMENT '发货经销商编码',
  `sender_customer_ename` varchar(50) DEFAULT NULL COMMENT '发货经销商编码',
  `receiver_store_id` bigint(20) DEFAULT NULL COMMENT '收货逻辑仓ID',
  `receiver_store_ecode` varchar(100) DEFAULT NULL COMMENT '收货逻辑仓编码',
  `receiver_store_ename` varchar(255) DEFAULT NULL COMMENT '收货逻辑仓名称',
  `receiver_customer_id` bigint(20) DEFAULT NULL COMMENT '收货经销商ID',
  `receiver_customer_ecode` varchar(50) DEFAULT NULL COMMENT '收货经销商编码',
  `receiver_customer_ename` varchar(50) DEFAULT NULL COMMENT '收货经销商编码',
  `ps_c_sku_id` bigint(20) DEFAULT NULL COMMENT '条码ID',
  `ps_c_sku_ecode` varchar(100) DEFAULT NULL COMMENT '条码编码',
  `gbcode` varchar(100) DEFAULT NULL COMMENT '国标码',
  `ps_c_brand_id` bigint(20) DEFAULT NULL COMMENT '品牌ID',
  `ps_c_pro_id` bigint(20) DEFAULT NULL COMMENT '商品ID',
  `ps_c_pro_ecode` varchar(20) DEFAULT NULL COMMENT '商品编码',
  `ps_c_pro_ename` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `ps_c_spec1_id` bigint(20) DEFAULT NULL COMMENT '规格1ID',
  `ps_c_spec1_ecode` varchar(100) DEFAULT NULL COMMENT '规格1编码',
  `ps_c_spec1_ename` varchar(100) DEFAULT NULL COMMENT '规格1名称',
  `ps_c_spec2_id` bigint(20) DEFAULT NULL COMMENT '规格2ID',
  `ps_c_spec2_ecode` varchar(100) DEFAULT NULL COMMENT '规格2编码',
  `ps_c_spec2_ename` varchar(100) DEFAULT NULL COMMENT '规格2名称',
  `ps_c_spec3_id` bigint(20) DEFAULT NULL COMMENT '规格3ID',
  `ps_c_spec3_ecode` varchar(100) DEFAULT NULL COMMENT '规格3编码',
  `ps_c_spec3_ename` varchar(100) DEFAULT NULL COMMENT '规格3名称',
  `qty` decimal(18,4) DEFAULT '0.0000' COMMENT '数量',
  `qty_out` decimal(18,4) DEFAULT '0.0000' COMMENT '实际调拨数量',
  `amt` decimal(18,4) DEFAULT '0.0000' COMMENT '吊牌金额',
  `price_list` decimal(18,4) DEFAULT '0.0000' COMMENT '吊牌价',
  `fail_reason` varchar(1000) DEFAULT NULL COMMENT '失败原因',
  `tms_date` datetime DEFAULT NULL COMMENT 'TMS要求提货日期',
  `cp_c_tranway_assign_id` bigint(20) DEFAULT NULL COMMENT '运输类型',
  `demand_type` int(11) DEFAULT NULL COMMENT '需求类型',
  `receiver_address` varchar(500) DEFAULT NULL COMMENT '收货地址',
  `is_temporary_address` char(1) DEFAULT NULL COMMENT '是否使用临时地址',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `version` bigint(20) DEFAULT NULL COMMENT '版本号',
  `isactive` char(1) DEFAULT 'Y' COMMENT '是否可用',
  `ownerid` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `ownerename` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `ownername` varchar(50) DEFAULT NULL COMMENT '创建人用户名',
  `creationdate` datetime DEFAULT NULL COMMENT '创建时间',
  `modifierid` bigint(20) DEFAULT NULL COMMENT '修改人ID',
  `modifierename` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
  `modifiername` varchar(50) DEFAULT NULL COMMENT '修改人用户名',
  `modifieddate` datetime DEFAULT NULL COMMENT '修改时间',
  `reserve_bigint01` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
  `reserve_bigint02` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
  `reserve_bigint03` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
  `reserve_bigint04` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
  `reserve_bigint05` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
  `reserve_bigint06` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
  `reserve_bigint07` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
  `reserve_bigint08` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
  `reserve_bigint09` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
  `reserve_bigint10` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
  `reserve_varchar01` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)1',
  `reserve_varchar02` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)2',
  `reserve_varchar03` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)3',
  `reserve_varchar04` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)4',
  `reserve_varchar05` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)5',
  `reserve_varchar06` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)6',
  `reserve_varchar07` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)7',
  `reserve_varchar08` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)8',
  `reserve_varchar09` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)9',
  `reserve_varchar10` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)10',
  `reserve_decimal01` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
  `reserve_decimal02` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
  `reserve_decimal03` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
  `reserve_decimal04` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
  `reserve_decimal05` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
  `reserve_decimal06` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
  `reserve_decimal07` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
  `reserve_decimal08` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
  `reserve_decimal09` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
  `reserve_decimal10` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='虚拟批量逻辑调拨单明细';


/*调拨单主表新增tms需要的字段*/
ALTER TABLE `sg_b_sto_transfer`
ADD COLUMN `demand_type` char(2) NULL COMMENT '需求类型-tms接口',
ADD COLUMN `is_pass_tms` char(1) NULL COMMENT '是否传TMS',
ADD COLUMN `tms_business_type` varchar(20) NULL COMMENT 'TMS业务类型',
ADD COLUMN `tms_date` datetime(0) NULL COMMENT 'TMS要求提货日期',
ADD COLUMN `is_temporary_address` char(1) NULL COMMENT '是否使用临时地址',
ADD COLUMN `actual_list_date` datetime(0) NULL COMMENT '实际上市日期';


/*调拨单明细表新增tms需要的字段*/
ALTER TABLE `sg_b_sto_transfer_item`
ADD COLUMN `price_sale` decimal(18, 4) NULL COMMENT '销售价-tms接口',
ADD COLUMN `discount_sale` decimal(18, 4) NULL COMMENT '销售折扣-tms接口';



//uat不需要执行
DROP TABLE IF EXISTS `sg_b_sa_target_storage_summary`;
CREATE TABLE `sg_b_sa_target_storage_summary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `ad_client_id` bigint(20) DEFAULT '37' COMMENT '所属公司',
  `ad_org_id` bigint(20) DEFAULT '27' COMMENT '所属组织',
  `sg_c_sa_store_id` bigint(20) DEFAULT NULL COMMENT '配货仓ID',
  `sg_c_sa_store_ecode` varchar(100) DEFAULT NULL COMMENT '配货仓编码',
  `sg_c_sa_store_ename` varchar(255) DEFAULT NULL COMMENT '配货仓名称',
  `ps_c_sku_id` bigint(20) DEFAULT NULL COMMENT '条码ID',
  `ps_c_sku_ecode` varchar(100) DEFAULT NULL COMMENT '条码编码',
  `ps_c_pro_id` bigint(20) DEFAULT NULL COMMENT '商品ID',
  `ps_c_pro_ecode` varchar(20) DEFAULT NULL COMMENT '商品编码',
  `ps_c_pro_ename` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `ps_c_spec1_id` bigint(20) DEFAULT NULL COMMENT '规格1ID',
  `ps_c_spec1_ecode` varchar(100) DEFAULT NULL COMMENT '规格1编码',
  `ps_c_spec1_ename` varchar(100) DEFAULT NULL COMMENT '规格1名称',
  `ps_c_spec2_id` bigint(20) DEFAULT NULL COMMENT '规格2ID',
  `ps_c_spec2_ecode` varchar(100) DEFAULT NULL COMMENT '规格2编码',
  `ps_c_spec2_ename` varchar(100) DEFAULT NULL COMMENT '规格2名称',
  `ps_c_spec3_id` bigint(20) DEFAULT NULL COMMENT '规格3ID',
  `ps_c_spec3_ecode` varchar(100) DEFAULT NULL COMMENT '规格3编码',
  `ps_c_spec3_ename` varchar(100) DEFAULT NULL COMMENT '规格3名称',
  `qty_target_storage` decimal(18,4) DEFAULT '0.0000' COMMENT '目标库存',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `version` varchar(255) DEFAULT NULL COMMENT '版本号',
  `isactive` char(1) DEFAULT 'Y' COMMENT '是否可用',
  `ownerid` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `ownerename` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `ownername` varchar(50) DEFAULT NULL COMMENT '创建人用户名',
  `creationdate` datetime DEFAULT NULL COMMENT '创建时间',
  `modifierid` bigint(20) DEFAULT NULL COMMENT '修改人ID',
  `modifierename` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
  `modifiername` varchar(50) DEFAULT NULL COMMENT '修改人用户名',
  `modifieddate` datetime DEFAULT NULL COMMENT '修改时间',
  `reserve_bigint01` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
  `reserve_bigint02` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
  `reserve_bigint03` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
  `reserve_bigint04` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
  `reserve_bigint05` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
  `reserve_bigint06` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
  `reserve_bigint07` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
  `reserve_bigint08` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
  `reserve_bigint09` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
  `reserve_bigint10` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
  `reserve_varchar01` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)1',
  `reserve_varchar02` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)2',
  `reserve_varchar03` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)3',
  `reserve_varchar04` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)4',
  `reserve_varchar05` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)5',
  `reserve_varchar06` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)6',
  `reserve_varchar07` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)7',
  `reserve_varchar08` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)8',
  `reserve_varchar09` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)9',
  `reserve_varchar10` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)10',
  `reserve_decimal01` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
  `reserve_decimal02` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
  `reserve_decimal03` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
  `reserve_decimal04` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
  `reserve_decimal05` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
  `reserve_decimal06` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
  `reserve_decimal07` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
  `reserve_decimal08` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
  `reserve_decimal09` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
  `reserve_decimal10` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
  `sg_c_share_store_id` bigint(20) DEFAULT NULL COMMENT '共享仓ID',
  `sg_c_share_store_ecode` varchar(20) DEFAULT NULL COMMENT '共享仓编码',
  `sg_c_share_store_ename` varchar(255) DEFAULT NULL COMMENT '共享仓名称',
  `qty_share_unshared_preout` decimal(18,4) DEFAULT '0.0000' COMMENT '聚合仓非共享占用数量',
  `qty_share_shared_preout` decimal(18,4) DEFAULT '0.0000' COMMENT '聚合仓共享占用数量',
  `qty_share_preout` decimal(18,4) DEFAULT '0.0000' COMMENT '聚合仓占用数量',
  `qty_share_prein` decimal(18,4) DEFAULT '0.0000' COMMENT '聚合仓在途数量',
  `qty_share_freeze` decimal(18,4) DEFAULT '0.0000' COMMENT '聚合仓冻结数量',
  `qty_share_sa_preout` decimal(18,4) DEFAULT '0.0000' COMMENT '聚合仓配销总占用数量',
  `qty_share_sa_storage` decimal(18,4) DEFAULT '0.0000' COMMENT '聚合仓配销总在库数量',
  `qty_share_storage` decimal(18,4) DEFAULT '0.0000' COMMENT '聚合仓在库数量',
  `qty_share_sale_available` decimal(18,4) DEFAULT '0.0000' COMMENT '聚合仓可售数量',
  `qty_store_available` decimal(18,4) DEFAULT '0.0000' COMMENT '逻辑仓可用数量',
  `qty_share_available` decimal(18,4) DEFAULT '0.0000' COMMENT '聚合仓可用数量',
  `qty_share_available_after` decimal(18,4) DEFAULT '0.0000' COMMENT '聚合仓分配后可用数量',
  `qty_sp_preout` decimal(18,4) DEFAULT '0.0000' COMMENT '共享池可用数量',
  `qty_sp_storage` decimal(18,4) DEFAULT '0.0000' COMMENT '共享池在库数量',
  `qty_sa_available` decimal(18,4) DEFAULT '0.0000' COMMENT '聚合仓可用数量',
  `qty_stock_depth_target` decimal(18,4) DEFAULT '0.0000' COMMENT '库存目标深度',
  `qty_stock_depth_begin` decimal(18,4) DEFAULT '0.0000' COMMENT '库存深度开始数量',
  `qty_stock_depth_end` decimal(18,4) DEFAULT '0.0000' COMMENT '库存深度结束数量',
  `attribute` varchar(80) DEFAULT NULL COMMENT '属性',
  `channel_name` varchar(50) DEFAULT NULL COMMENT '主控渠道',
  `product_level` varchar(50) DEFAULT NULL COMMENT '商品级别',
  `drp_style` varchar(50) DEFAULT NULL COMMENT '型号(Style)',
  `clf` varchar(80) DEFAULT NULL COMMENT '内部CLF',
  `stockout_rate` decimal(10,2) DEFAULT '0.00' COMMENT '库存售罄',
  `inline_season` varchar(80) DEFAULT NULL COMMENT 'inLine Season',
  `qty_sa_allocation` decimal(18,4) DEFAULT '0.0000' COMMENT '配销仓待分配量',
  `qty_allocation_begin` decimal(18,4) DEFAULT '0.0000' COMMENT '店铺铺底策略起始量',
  `qty_allocation_end` decimal(18,4) DEFAULT '0.0000' COMMENT '店铺铺底策略终止量',
  `qty_oneset_target` decimal(18,4) DEFAULT '0.0000' COMMENT '店铺铺底目标手数',
  `qty_oneset` decimal(18,4) DEFAULT '0.0000' COMMENT '一手码数量',
  `season` varchar(80) DEFAULT NULL COMMENT '季节',
  `price_list` decimal(18,4) DEFAULT '0.0000' COMMENT '吊牌价',
  `channel` varchar(50) DEFAULT NULL COMMENT '渠道',
  `tale_sort` varchar(500) DEFAULT NULL COMMENT '故事分类',
  PRIMARY KEY (`id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配销仓目标库存汇总';

//uat不需要执行
DROP TABLE IF EXISTS `sg_c_oneset_setting`;
CREATE TABLE `sg_c_oneset_setting` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `ad_client_id` bigint(20) DEFAULT '37' COMMENT '所属公司',
  `ad_org_id` bigint(20) DEFAULT '27' COMMENT '所属组织',
  `sg_c_share_store_id` bigint(20) DEFAULT NULL COMMENT '聚合仓ID',
  `sg_c_share_store_ecode` varchar(100) DEFAULT NULL COMMENT '聚合仓编码',
  `sg_c_share_store_ename` varchar(255) DEFAULT NULL COMMENT '聚合仓名称',
  `ps_c_sku_id` bigint(20) DEFAULT NULL COMMENT '条码ID',
  `ps_c_sku_ecode` varchar(100) DEFAULT NULL COMMENT '条码编码',
  `qty` decimal(18,4) DEFAULT '0.0000' COMMENT '一手码数量',
  `is_maincode` varchar(80) DEFAULT '0' COMMENT '是否主码',
  `ps_c_pro_id` bigint(20) DEFAULT NULL COMMENT '商品ID',
  `ps_c_pro_ecode` varchar(20) DEFAULT NULL COMMENT '商品编码',
  `ps_c_pro_ename` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `ps_c_spec1_id` bigint(20) DEFAULT NULL COMMENT '规格1ID',
  `ps_c_spec1_ecode` varchar(100) DEFAULT NULL COMMENT '规格1编码',
  `ps_c_spec1_ename` varchar(100) DEFAULT NULL COMMENT '规格1名称',
  `ps_c_spec2_id` bigint(20) DEFAULT NULL COMMENT '规格2ID',
  `ps_c_spec2_ecode` varchar(100) DEFAULT NULL COMMENT '规格2编码',
  `ps_c_spec2_ename` varchar(100) DEFAULT NULL COMMENT '规格2名称',
  `ps_c_spec3_id` bigint(20) DEFAULT NULL COMMENT '规格3ID',
  `ps_c_spec3_ecode` varchar(100) DEFAULT NULL COMMENT '规格3编码',
  `ps_c_spec3_ename` varchar(100) DEFAULT NULL COMMENT '规格3名称',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `version` bigint(20) DEFAULT NULL COMMENT '版本号',
  `isactive` char(1) DEFAULT 'Y' COMMENT '是否可用',
  `ownerid` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `ownerename` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `ownername` varchar(50) DEFAULT NULL COMMENT '创建人用户名',
  `creationdate` datetime DEFAULT NULL COMMENT '创建时间',
  `modifierid` bigint(20) DEFAULT NULL COMMENT '修改人ID',
  `modifierename` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
  `modifiername` varchar(50) DEFAULT NULL COMMENT '修改人用户名',
  `modifieddate` datetime DEFAULT NULL COMMENT '修改时间',
  `reserve_bigint01` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
  `reserve_bigint02` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
  `reserve_bigint03` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
  `reserve_bigint04` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
  `reserve_bigint05` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
  `reserve_bigint06` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
  `reserve_bigint07` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
  `reserve_bigint08` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
  `reserve_bigint09` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
  `reserve_bigint10` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
  `reserve_varchar01` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)1',
  `reserve_varchar02` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)2',
  `reserve_varchar03` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)3',
  `reserve_varchar04` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)4',
  `reserve_varchar05` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)5',
  `reserve_varchar06` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)6',
  `reserve_varchar07` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)7',
  `reserve_varchar08` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)8',
  `reserve_varchar09` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)9',
  `reserve_varchar10` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)10',
  `reserve_decimal01` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
  `reserve_decimal02` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
  `reserve_decimal03` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
  `reserve_decimal04` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
  `reserve_decimal05` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
  `reserve_decimal06` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
  `reserve_decimal07` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
  `reserve_decimal08` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
  `reserve_decimal09` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
  `reserve_decimal10` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
  PRIMARY KEY (`id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='一手码设置';
