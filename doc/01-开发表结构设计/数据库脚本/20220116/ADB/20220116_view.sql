# 分货单/分货退货单 ADB视图
create view v_sg_b_share_allocation as
SELECT `a`.`id`                  `id`
     , `a`.`bill_no`             `bill_no`
     , `a`.`bill_date`           `bill_date`
     , `a`.`sg_c_share_store_id` `sg_c_share_store_id`
     , `a`.`sg_c_share_store_ecode` `sg_c_share_store_ecode`
     , `b`.`sg_c_sa_store_id`    `sg_c_sa_store_id`
     , `b`.`sg_c_sa_store_ecode`    `sg_c_sa_store_ecode`
     , `b`.`ps_c_pro_id`         `ps_c_pro_id`
     , `b`.`gbcode`              `gbcode`
     , `b`.`ps_c_sku_id`         `ps_c_sku_id`
     , `b`.`ps_c_spec1_ecode`    `ps_c_spec1_ecode`
     , `b`.`ps_c_spec1_ename`    `ps_c_spec1_ename`
     , `b`.`ps_c_spec2_ecode`    `ps_c_spec2_ecode`
     , `b`.`ps_c_spec2_ename`    `ps_c_spec2_ename`
     , `b`.`qty`                 `qty`
     , `a`.`remark`              `remark`
     , `a`.`source_bill_type`    `source_bill_type`
     , `a`.`source_bill_no`      `source_bill_no`
     , `a`.`ownerid`             `ownerid`
     , `a`.`creationdate`        `creationdate`
     , `a`.`modifierid`          `modifierid`
     , `a`.`modifieddate`        `modifieddate`
     , `a`.`isactive`        `isactive`
     , `a`.`ad_client_id`        `ad_client_id`
     , `a`.`ad_org_id`        `ad_org_id`
FROM (`r3_oms_sg`.`sg_b_share_allocation` `a`
         INNER JOIN `r3_oms_sg`.`sg_b_share_allocation_item` `b` ON (`a`.`id` = `b`.`sg_b_share_allocation_id`))
UNION ALL
SELECT `a`.`id`                  `id`
     , `a`.`bill_no`             `bill_no`
     , `a`.`bill_date`           `bill_date`
     , `a`.`sg_c_share_store_id` `sg_c_share_store_id`
     , `a`.`sg_c_share_store_ecode` `sg_c_share_store_ecode`
     , `a`.`sg_c_sa_store_id`    `sg_c_sa_store_id`
     , `a`.`sg_c_sa_store_ecode`    `sg_c_sa_store_ecode`
     , `b`.`ps_c_pro_id`         `ps_c_pro_id`
     , `b`.`gbcode`              `gbcode`
     , `b`.`ps_c_sku_id`         `ps_c_sku_id`
     , `b`.`ps_c_spec1_ecode`    `ps_c_spec1_ecode`
     , `b`.`ps_c_spec1_ename`    `ps_c_spec1_ename`
     , `b`.`ps_c_spec2_ecode`    `ps_c_spec2_ecode`
     , `b`.`ps_c_spec2_ename`    `ps_c_spec2_ename`
     , -`b`.`qty`                `qty`
     , `a`.`remark`              `remark`
     , `a`.`source_bill_type`    `source_bill_type`
     , `a`.`source_bill_no`      `source_bill_no`
     , `a`.`ownerid`             `ownerid`
     , `a`.`creationdate`        `creationdate`
     , `a`.`modifierid`          `modifierid`
     , `a`.`modifieddate`        `modifieddate`
     , `a`.`isactive`        `isactive`
     , `a`.`ad_client_id`        `ad_client_id`
     , `a`.`ad_org_id`        `ad_org_id`
FROM (`r3_oms_sg`.`sg_b_share_allocation_return` `a`
         INNER JOIN `r3_oms_sg`.`sg_b_share_allocation_return_item` `b`
                    ON (`a`.`id` = `b`.`sg_b_share_allocation_return_id`));

# 逻辑调拨单明细查询
create view `v_sg_b_sto_transfer` as
select b.id ,a.bill_no,a.bill_date ,a.out_date ,a.in_date ,
       a.sender_store_id ,a.sender_store_ecode ,a.sender_store_ename ,
       a.receiver_store_id ,a.receiver_store_ecode ,a.receiver_store_ename ,
       b.ps_c_pro_id ,b.ps_c_pro_ecode ,b.ps_c_pro_ename ,
       b.gbcode ,b.ps_c_sku_id ,b.ps_c_sku_ecode ,
       b.ps_c_spec1_id ,b.ps_c_spec1_ecode ,b.ps_c_spec1_ename ,
       b.ps_c_spec2_id ,b.ps_c_spec2_ecode ,b.ps_c_spec2_ename ,
       b.qty ,b.qty_out ,b.qty_in ,b.qty_diff ,
       a.remark ,a.source_bill_type ,a.source_bill_id ,a.source_bill_no ,a.source_bill_date ,
       a.status ,a.ownerid ,a.ownername ,a.ownerename ,a.creationdate ,
       a.modifierid ,a.modifiername ,a.modifierename ,a.modifieddate ,
       a.status_id ,a.status_name ,a.status_ename ,a.status_time ,
       a.outer_id ,a.outer_name ,a.outer_ename ,a.out_time ,
       a.iner_id ,a.iner_name ,a.iner_ename ,a.in_time ,
       a.isactive
from r3_oms_sg.sg_b_sto_transfer a
right join r3_oms_sg.sg_b_sto_transfer_item b
on a.id = b.sg_b_sto_transfer_id;
























库存调整单明细表视图
create view  v_sg_b_sto_adjust as
SELECT
	a.id,
	a.ad_client_id,
	a.ad_org_id,
	a.bill_no,
	a.bill_date,
	a.bill_type,
	a.cp_c_store_id,
	a.cp_c_store_ecode,
	a.cp_c_store_ename,
	a.sg_b_adjust_prop_id,
	b.ps_c_sku_id,
	b.ps_c_sku_ecode,
	b.ps_c_pro_id,
	b.ps_c_pro_ecode,
	b.ps_c_pro_ename,
	b.ps_c_spec1_ename,
	b.ps_c_spec2_ename,
	b.qty,
	a.remark,
	a.source_bill_type,
	a.source_bill_no,
	a.ownerid,
	a.creationdate,
	a.modifierid,
	a.modifieddate,
	a.status_id,
	a.status_time
FROM
	r3_oms_sg.sg_b_sto_adjust a
	INNER JOIN r3_oms_sg.sg_b_sto_adjust_item b ON a.id = b.sg_b_sto_adjust_id
WHERE
	a.STATUS = 2
