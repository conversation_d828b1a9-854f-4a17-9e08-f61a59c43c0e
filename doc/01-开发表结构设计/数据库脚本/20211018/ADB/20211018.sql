CREATE TABLE `SG_B_SHOP_STOCK_OTO_SETTING` (
  `id` bigint(20) NOT NULL COMMENT 'ID',
  `ad_client_id` bigint(20) DEFAULT '37' COMMENT '所属公司',
  `ad_org_id` bigint(20) DEFAULT '27' COMMENT '所属组织',
  `CP_C_SHOP_ID` bigint(20) DEFAULT NULL COMMENT '平台店铺ID',
  `PS_C_SKU_ID` bigint(20) DEFAULT NULL COMMENT '条码ID',
  `SG_C_SA_STORE_ECODE` varchar(100) DEFAULT NULL COMMENT '店铺配销仓编号',
  `SG_C_SHARE_STORE_ECODE` varchar(100) DEFAULT NULL COMMENT '店铺聚合仓编号',
  `SG_OFFLINE_C_SA_STORE_ECODE` varchar(100) DEFAULT NULL COMMENT '线下门店配销仓编号',
  `SG_OFFLINE_C_SHARE_STORE_ECODE` varchar(100) DEFAULT NULL COMMENT '线下门店聚合仓编号',
  `OTO_QTY` decimal(18,4) DEFAULT '0.0000' COMMENT 'OTO店铺库存',
  `ONLINE_QTY` decimal(18,4) DEFAULT '0.0000' COMMENT '同步线上库存',
  `SAFE_QTY` decimal(18,4) DEFAULT '0.0000' COMMENT '安全库存(绝对值)',
  `isactive` char(1) DEFAULT 'Y' COMMENT '是否可用',
  `ownerid` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `ownerename` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `ownername` varchar(50) DEFAULT NULL COMMENT '创建人用户名',
  `creationdate` datetime DEFAULT NULL COMMENT '创建时间',
  `modifierid` bigint(20) DEFAULT NULL COMMENT '修改人ID',
  `modifierename` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
  `modifiername` varchar(50) DEFAULT NULL COMMENT '修改人用户名',
  `modifieddate` datetime DEFAULT NULL COMMENT '修改时间',
  `deler_id` bigint(20) DEFAULT NULL COMMENT '作废人ID',
  `deler_ename` varchar(50) DEFAULT NULL COMMENT '作废人姓名',
  `deler_name` varchar(50) DEFAULT NULL COMMENT '作废人用户名',
  `del_time` datetime DEFAULT NULL COMMENT '作废时间',
  `reserve_bigint01` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
  `reserve_bigint02` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
  `reserve_bigint03` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
  `reserve_bigint04` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
  `reserve_bigint05` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
  `reserve_bigint06` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
  `reserve_bigint07` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
  `reserve_bigint08` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
  `reserve_bigint09` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
  `reserve_bigint10` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
  `reserve_varchar01` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)1',
  `reserve_varchar02` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)2',
  `reserve_varchar03` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)3',
  `reserve_varchar04` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)4',
  `reserve_varchar05` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)5',
  `reserve_varchar06` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)6',
  `reserve_varchar07` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)7',
  `reserve_varchar08` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)8',
  `reserve_varchar09` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)9',
  `reserve_varchar10` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)10',
  `reserve_decimal01` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
  `reserve_decimal02` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
  `reserve_decimal03` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
  `reserve_decimal04` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
  `reserve_decimal05` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
  `reserve_decimal06` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
  `reserve_decimal07` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
  `reserve_decimal08` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
  `reserve_decimal09` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
  `reserve_decimal10` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='平台店铺OTO库存设置';

Create view skx_report_warn_oto_storage as
SELECT
  a.id,
  a.ad_client_id,
  a.ad_org_id,
  a.cp_c_shop_id,
  c.DRP_STYLE,
  c.ECODE AS pro_ecode,
  d.ecode AS size_ecode,
  a.ps_c_sku_id,
  c.ECODE AS sku_ecode,
  a.`OTO_QTY`,
  a.`ONLINE_QTY`,
  a.`SAFE_QTY`,
  e.pt_qty,
  IFNULL(g.qty_available, 0) + IFNULL(gg.qty_available, 0) AS erp_qty,
  h.tot_num,
  i.tot_num AS isactive_tot_num,
  f.qty_available AS oto_share_qty,
  CASE
WHEN IFNULL(f.qty_available, 0) - IFNULL(a.ONLINE_QTY, 0) < IFNULL(a.SAFE_QTY, 0) THEN
  '有风险'
ELSE
  '无风险'
END AS error_status,
 a.`isactive`,
 a.`ownerid`,
 a.`ownerename`,
 a.`ownername`,
 a.`creationdate`,
 a.`modifierid`,
 a.`modifierename`,
 a.`modifiername`,
 a.`modifieddate`
FROM
  SG_B_SHOP_STOCK_OTO_SETTING a
JOIN r3_oms_sg.PS_C_SKU b ON a.ps_c_sku_id = b.id
JOIN r3_base.ps_c_pro c ON b.PS_C_PRO_ID = c.id
JOIN r3_oms_sg.PS_C_SPECOBJ d ON b.PS_C_SPEC2OBJ_ID = d.id
JOIN (
  SELECT
    aa.cp_c_shop_id,
    aa.ps_c_sku_id,
    sum(aa.qty_storage) AS pt_qty
  FROM
    r3_oms_sg.sg_b_channel_product aa
  GROUP BY
    aa.cp_c_shop_id,
    aa.ps_c_sku_id
) e ON e.cp_c_shop_id = a.CP_C_SHOP_ID
AND e.ps_c_sku_id = a.PS_C_SKU_ID
LEFT JOIN (
  SELECT
    substr(ee.sg_c_sa_store_ecode, 1, 3) AS sg_c_sa_store_ecode,
    ee.ps_c_sku_id,
    sum(ee.qty_available) AS qty_available
  FROM
    r3_oms_sg.SG_B_SA_STORAGE ee
  GROUP BY
    1,
    2
) g ON g.sg_c_sa_store_ecode = a.SG_C_SA_STORE_ECODE
AND g.ps_c_sku_id = a.PS_C_SKU_ID
LEFT JOIN (
  SELECT
    sg_c_sa_store_ecode,
    ee.ps_c_sku_id,
    sum(ee.qty_available) AS qty_available
  FROM
    r3_oms_sg.SG_B_SA_STORAGE ee
  GROUP BY
    1,
    2
) gg ON gg.sg_c_sa_store_ecode = a.SG_OFFLINE_C_SA_STORE_ECODE
AND gg.ps_c_sku_id = a.PS_C_SKU_ID
LEFT JOIN (
  SELECT
    cc.sg_c_share_store_id,
    dd.ecode,
    bb.ps_c_sku_id,
    sum(bb.qty_available) AS qty_available
  FROM
    r3_oms_sg.SG_B_STORAGE bb
  JOIN r3_oms_sg.cp_c_store cc ON bb.cp_c_store_id = cc.id
  JOIN r3_oms_sg.sg_c_share_store dd ON cc.sg_c_share_store_id = dd.id
  GROUP BY
    cc.sg_c_share_store_id,
    dd.ecode,
    bb.ps_c_sku_id
) f ON a.SG_OFFLINE_C_SHARE_STORE_ECODE = f.ecode
AND f.ps_c_sku_id = a.ps_c_sku_id
JOIN (
  SELECT
    ee.CP_C_SHOP_ID,
    ff.OUTER_SKU_ID,
    sum(ff.NUM) AS tot_num
  FROM
    r3_rc_datasync.IP_B_TAOBAO_ORDER ee
  JOIN r3_rc_datasync.IP_B_TAOBAO_ORDER_ITEM ff ON ff.IP_B_TAOBAO_ORDER_ID = ee.id
  GROUP BY
    ee.CP_C_SHOP_ID,
    ff.OUTER_SKU_ID
) h ON h.CP_C_SHOP_ID = a.CP_C_SHOP_ID
AND h.OUTER_SKU_ID = b.ecode
JOIN (
  SELECT
    ee.CP_C_SHOP_ID,
    ff.OUTER_SKU_ID,
    sum(ff.NUM) AS tot_num
  FROM
    r3_rc_datasync.IP_B_TAOBAO_ORDER ee
  JOIN r3_rc_datasync.IP_B_TAOBAO_ORDER_ITEM ff ON ff.IP_B_TAOBAO_ORDER_ID = ee.id
  WHERE
    IFNULL(ee. STATUS, ' ') <> 'TRADE_CLOSED'
  GROUP BY
    ee.CP_C_SHOP_ID,
    ff.OUTER_SKU_ID
) i ON i.CP_C_SHOP_ID = a.CP_C_SHOP_ID
AND i.OUTER_SKU_ID = b.ecode;
