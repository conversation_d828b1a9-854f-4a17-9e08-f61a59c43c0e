ALTER TABLE `sg_b_sp_storage_preout_ftp`
    ADD INDEX `idx_sg_b_sp_storage_preout_ftp_10` (`bill_id`) USING BTREE;

ALTER TABLE `sg_b_sa_storage_preout_ftp`
    ADD INDEX `idx_sg_b_sa_storage_preout_ftp_10` (`bill_id`) USING BTREE;

ALTER TABLE `sg_b_storage_shared_preout_ftp`
    ADD INDEX `idx_sg_b_storage_shared_preout_ftp_10` (`bill_id`) USING BTREE;



CREATE TABLE `sg_b_share_transfer` (
                                       `id` bigint(20) NOT NULL COMMENT 'ID',
                                       `ad_client_id` bigint(20) DEFAULT '37' COMMENT '所属公司',
                                       `ad_org_id` bigint(20) DEFAULT '27' COMMENT '所属组织',
                                       `bill_no` varchar(100) DEFAULT NULL COMMENT '单据编号',
                                       `bill_date` date DEFAULT NULL COMMENT '单据日期',
                                       `status` int(11) DEFAULT NULL COMMENT '单据状态',
                                       `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                                       `version` bigint(20) DEFAULT NULL COMMENT '版本号',
                                       `isactive` char(1) DEFAULT 'Y' COMMENT '是否可用',
                                       `is_auto_confirm` char(1) DEFAULT 'Y' COMMENT '是否提交确认调拨单',
                                       `tot_row_num` int(11) DEFAULT '0' COMMENT '总行数',
                                       `tot_qty` decimal(18,4) DEFAULT '0.0000' COMMENT '总预计调拨数量',
                                       `tot_qty_out` decimal(18,4) DEFAULT '0.0000' COMMENT '总实际调拨数量',
                                       `tot_amt` decimal(18,4) DEFAULT '0.0000' COMMENT '总吊牌金额',
                                       `fail_row_num` decimal(18,4) DEFAULT '0.0000' COMMENT '失败行数',
                                       `success_row_num` decimal(18,4) DEFAULT '0.0000' COMMENT '成功行数',
                                       `ownerid` bigint(20) DEFAULT NULL COMMENT '创建人ID',
                                       `ownerename` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
                                       `ownername` varchar(50) DEFAULT NULL COMMENT '创建人用户名',
                                       `creationdate` datetime DEFAULT NULL COMMENT '创建时间',
                                       `modifierid` bigint(20) DEFAULT NULL COMMENT '修改人ID',
                                       `modifierename` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
                                       `modifiername` varchar(50) DEFAULT NULL COMMENT '修改人用户名',
                                       `modifieddate` datetime DEFAULT NULL COMMENT '修改时间',
                                       `deler_id` bigint(20) DEFAULT NULL COMMENT '作废人ID',
                                       `deler_ename` varchar(50) DEFAULT NULL COMMENT '作废人姓名',
                                       `deler_name` varchar(50) DEFAULT NULL COMMENT '作废人用户名',
                                       `del_time` datetime DEFAULT NULL COMMENT '作废时间',
                                       `status_id` bigint(20) DEFAULT NULL COMMENT '提交人ID',
                                       `status_ename` varchar(50) DEFAULT NULL COMMENT '提交人姓名',
                                       `status_name` varchar(50) DEFAULT NULL COMMENT '提交人用户名',
                                       `status_time` datetime DEFAULT NULL COMMENT '提交时间',
                                       `reserve_bigint01` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
                                       `reserve_bigint02` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
                                       `reserve_bigint03` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
                                       `reserve_bigint04` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
                                       `reserve_bigint05` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
                                       `reserve_bigint06` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
                                       `reserve_bigint07` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
                                       `reserve_bigint08` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
                                       `reserve_bigint09` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
                                       `reserve_bigint10` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
                                       `reserve_varchar01` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)1',
                                       `reserve_varchar02` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)2',
                                       `reserve_varchar03` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)3',
                                       `reserve_varchar04` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)4',
                                       `reserve_varchar05` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)5',
                                       `reserve_varchar06` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)6',
                                       `reserve_varchar07` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)7',
                                       `reserve_varchar08` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)8',
                                       `reserve_varchar09` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)9',
                                       `reserve_varchar10` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)10',
                                       `reserve_decimal01` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
                                       `reserve_decimal02` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
                                       `reserve_decimal03` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
                                       `reserve_decimal04` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
                                       `reserve_decimal05` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
                                       `reserve_decimal06` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
                                       `reserve_decimal07` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
                                       `reserve_decimal08` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
                                       `reserve_decimal09` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
                                       `reserve_decimal10` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='聚会仓调拨单';

CREATE TABLE `sg_b_share_transfer_import_item` (
                                                   `id` bigint(20) NOT NULL COMMENT 'ID',
                                                   `ad_client_id` bigint(20) DEFAULT '37' COMMENT '所属公司',
                                                   `ad_org_id` bigint(20) DEFAULT '27' COMMENT '所属组织',
                                                   `sg_b_share_transfer_id` bigint(20) DEFAULT NULL COMMENT '所属聚合仓调拨单',
                                                   `sender_share_store_id` bigint(20) DEFAULT NULL COMMENT '发货聚合仓id',
                                                   `sender_share_store_ecode` varchar(100) DEFAULT NULL COMMENT '发货聚合仓编码',
                                                   `sender_share_store_ename` varchar(255) DEFAULT NULL COMMENT '发货聚合仓名称',
                                                   `receiver_share_store_id` bigint(20) DEFAULT NULL COMMENT '收货聚合仓ID',
                                                   `receiver_share_store_ecode` varchar(100) DEFAULT NULL COMMENT '收货聚合仓编码',
                                                   `receiver_share_store_ename` varchar(255) DEFAULT NULL COMMENT '收货聚合仓名称',
                                                   `ps_c_sku_or_pro` varchar(100) DEFAULT NULL COMMENT '款号或者条码',
                                                   `ps_c_sku_id` bigint(20) DEFAULT NULL COMMENT '条码ID',
                                                   `ps_c_sku_ecode` varchar(100) DEFAULT NULL COMMENT '条码编码',
                                                   `ps_c_pro_id` bigint(20) DEFAULT NULL COMMENT '商品ID',
                                                   `ps_c_pro_ecode` varchar(20) DEFAULT NULL COMMENT '商品编码',
                                                   `ps_c_pro_ename` varchar(255) DEFAULT NULL COMMENT '商品名称',
                                                   `qty` decimal(18,4) DEFAULT '0.0000' COMMENT '数量',
                                                   `transfer_latitude` int(11) DEFAULT '0' COMMENT '调拨纬度',
                                                   `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                                                   `item_status` char(1) DEFAULT NULL COMMENT '明细单据状态--是否添加',
                                                   `version` bigint(20) DEFAULT NULL COMMENT '版本号',
                                                   `isactive` char(1) DEFAULT 'Y' COMMENT '是否可用',
                                                   `ownerid` bigint(20) DEFAULT NULL COMMENT '创建人ID',
                                                   `ownerename` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
                                                   `ownername` varchar(50) DEFAULT NULL COMMENT '创建人用户名',
                                                   `creationdate` datetime DEFAULT NULL COMMENT '创建时间',
                                                   `modifierid` bigint(20) DEFAULT NULL COMMENT '修改人ID',
                                                   `modifierename` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
                                                   `modifiername` varchar(50) DEFAULT NULL COMMENT '修改人用户名',
                                                   `modifieddate` datetime DEFAULT NULL COMMENT '修改时间',
                                                   `reserve_bigint01` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
                                                   `reserve_bigint02` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
                                                   `reserve_bigint03` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
                                                   `reserve_bigint04` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
                                                   `reserve_bigint05` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
                                                   `reserve_bigint06` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
                                                   `reserve_bigint07` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
                                                   `reserve_bigint08` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
                                                   `reserve_bigint09` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
                                                   `reserve_bigint10` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
                                                   `reserve_varchar01` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)1',
                                                   `reserve_varchar02` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)2',
                                                   `reserve_varchar03` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)3',
                                                   `reserve_varchar04` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)4',
                                                   `reserve_varchar05` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)5',
                                                   `reserve_varchar06` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)6',
                                                   `reserve_varchar07` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)7',
                                                   `reserve_varchar08` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)8',
                                                   `reserve_varchar09` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)9',
                                                   `reserve_varchar10` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)10',
                                                   `reserve_decimal01` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
                                                   `reserve_decimal02` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
                                                   `reserve_decimal03` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
                                                   `reserve_decimal04` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
                                                   `reserve_decimal05` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
                                                   `reserve_decimal06` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
                                                   `reserve_decimal07` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
                                                   `reserve_decimal08` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
                                                   `reserve_decimal09` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
                                                   `reserve_decimal10` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='聚合仓调拨单导入明细';

CREATE TABLE `sg_b_share_transfer_item` (
                                            `id` bigint(20) NOT NULL COMMENT 'ID',
                                            `ad_client_id` bigint(20) DEFAULT '37' COMMENT '所属公司',
                                            `ad_org_id` bigint(20) DEFAULT '27' COMMENT '所属组织',
                                            `item_status` char(1) NOT NULL COMMENT '状态',
                                            `sg_b_share_transfer_id` bigint(20) DEFAULT NULL COMMENT '所属聚合仓调拨单',
                                            `sg_b_sto_transfer_id` bigint(20) DEFAULT NULL COMMENT '逻辑调拨单id',
                                            `sg_b_sto_transfer_bill_no` varchar(50) DEFAULT NULL COMMENT '逻辑调拨单编码',
                                            `sender_share_store_id` bigint(20) DEFAULT NULL COMMENT '发货聚合仓id',
                                            `sender_share_store_ecode` varchar(100) DEFAULT NULL COMMENT '发货聚合仓编码',
                                            `sender_share_store_ename` varchar(255) DEFAULT NULL COMMENT '发货聚合仓名称',
                                            `sender_store_id` bigint(20) DEFAULT NULL COMMENT '发货逻辑仓ID',
                                            `sender_store_ecode` varchar(100) DEFAULT NULL COMMENT '发货逻辑仓编码',
                                            `sender_store_ename` varchar(255) DEFAULT NULL COMMENT '发货逻辑仓名称',
                                            `sender_customer_id` bigint(20) DEFAULT NULL COMMENT '发货经销商ID',
                                            `sender_customer_ecode` varchar(50) DEFAULT NULL COMMENT '发货经销商编码',
                                            `sender_customer_ename` varchar(50) DEFAULT NULL COMMENT '发货经销商编码',
                                            `receiver_share_store_id` bigint(20) DEFAULT NULL COMMENT '收货聚合仓ID',
                                            `receiver_share_store_ecode` varchar(100) DEFAULT NULL COMMENT '收货聚合仓编码',
                                            `receiver_share_store_ename` varchar(255) DEFAULT NULL COMMENT '收货聚合仓名称',
                                            `receiver_store_id` bigint(20) DEFAULT NULL COMMENT '收货逻辑仓ID',
                                            `receiver_store_ecode` varchar(100) DEFAULT NULL COMMENT '收货逻辑仓编码',
                                            `receiver_store_ename` varchar(255) DEFAULT NULL COMMENT '收货逻辑仓名称',
                                            `receiver_customer_id` bigint(20) DEFAULT NULL COMMENT '收货经销商ID',
                                            `receiver_customer_ecode` varchar(50) DEFAULT NULL COMMENT '收货经销商编码',
                                            `receiver_customer_ename` varchar(50) DEFAULT NULL COMMENT '收货经销商编码',
                                            `ps_c_sku_id` bigint(20) DEFAULT NULL COMMENT '条码ID',
                                            `ps_c_sku_ecode` varchar(100) DEFAULT NULL COMMENT '条码编码',
                                            `gbcode` varchar(100) DEFAULT NULL COMMENT '国标码',
                                            `ps_c_brand_id` bigint(20) DEFAULT NULL COMMENT '品牌ID',
                                            `ps_c_pro_id` bigint(20) DEFAULT NULL COMMENT '商品ID',
                                            `ps_c_pro_ecode` varchar(20) DEFAULT NULL COMMENT '商品编码',
                                            `ps_c_pro_ename` varchar(255) DEFAULT NULL COMMENT '商品名称',
                                            `ps_c_spec1_id` bigint(20) DEFAULT NULL COMMENT '规格1ID',
                                            `ps_c_spec1_ecode` varchar(100) DEFAULT NULL COMMENT '规格1编码',
                                            `ps_c_spec1_ename` varchar(100) DEFAULT NULL COMMENT '规格1名称',
                                            `ps_c_spec2_id` bigint(20) DEFAULT NULL COMMENT '规格2ID',
                                            `ps_c_spec2_ecode` varchar(100) DEFAULT NULL COMMENT '规格2编码',
                                            `ps_c_spec2_ename` varchar(100) DEFAULT NULL COMMENT '规格2名称',
                                            `ps_c_spec3_id` bigint(20) DEFAULT NULL COMMENT '规格3ID',
                                            `ps_c_spec3_ecode` varchar(100) DEFAULT NULL COMMENT '规格3编码',
                                            `ps_c_spec3_ename` varchar(100) DEFAULT NULL COMMENT '规格3名称',
                                            `qty` decimal(18,4) DEFAULT '0.0000' COMMENT '预计调拨数量',
                                            `qty_out` decimal(18,4) DEFAULT '0.0000' COMMENT '实际调拨数量',
                                            `amt` decimal(18,4) DEFAULT '0.0000' COMMENT '吊牌金额',
                                            `price_list` decimal(18,4) DEFAULT '0.0000' COMMENT '吊牌价',
                                            `fail_reason` varchar(1000) DEFAULT NULL COMMENT '失败原因',
                                            `tms_date` datetime DEFAULT NULL COMMENT 'TMS要求提货日期',
                                            `cp_c_tranway_assign_id` bigint(20) DEFAULT NULL COMMENT '运输类型',
                                            `demand_type` char(8) DEFAULT NULL COMMENT '需求类型',
                                            `receiver_address` varchar(500) DEFAULT NULL COMMENT '收货地址',
                                            `is_temporary_address` char(1) DEFAULT NULL COMMENT '是否使用临时地址',
                                            `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                                            `version` bigint(20) DEFAULT NULL COMMENT '版本号',
                                            `isactive` char(1) DEFAULT 'Y' COMMENT '是否可用',
                                            `ownerid` bigint(20) DEFAULT NULL COMMENT '创建人ID',
                                            `ownerename` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
                                            `ownername` varchar(50) DEFAULT NULL COMMENT '创建人用户名',
                                            `creationdate` datetime DEFAULT NULL COMMENT '创建时间',
                                            `modifierid` bigint(20) DEFAULT NULL COMMENT '修改人ID',
                                            `modifierename` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
                                            `modifiername` varchar(50) DEFAULT NULL COMMENT '修改人用户名',
                                            `modifieddate` datetime DEFAULT NULL COMMENT '修改时间',
                                            `reserve_bigint01` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
                                            `reserve_bigint02` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
                                            `reserve_bigint03` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
                                            `reserve_bigint04` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
                                            `reserve_bigint05` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
                                            `reserve_bigint06` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
                                            `reserve_bigint07` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
                                            `reserve_bigint08` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
                                            `reserve_bigint09` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
                                            `reserve_bigint10` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
                                            `reserve_varchar01` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)1',
                                            `reserve_varchar02` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)2',
                                            `reserve_varchar03` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)3',
                                            `reserve_varchar04` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)4',
                                            `reserve_varchar05` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)5',
                                            `reserve_varchar06` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)6',
                                            `reserve_varchar07` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)7',
                                            `reserve_varchar08` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)8',
                                            `reserve_varchar09` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)9',
                                            `reserve_varchar10` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)10',
                                            `reserve_decimal01` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
                                            `reserve_decimal02` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
                                            `reserve_decimal03` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
                                            `reserve_decimal04` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
                                            `reserve_decimal05` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
                                            `reserve_decimal06` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
                                            `reserve_decimal07` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
                                            `reserve_decimal08` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
                                            `reserve_decimal09` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
                                            `reserve_decimal10` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
                                            `is_auto_in` char(1) DEFAULT 'n' COMMENT '自动入库',
                                            `is_auto_out` char(1) DEFAULT 'n' COMMENT '自动出库',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='聚合仓调拨单明细';

ALTER TABLE `sg_b_share_transfer_item`
    ADD INDEX `idx_sg_b_share_transfer_item_1` (`sg_b_share_transfer_id`) USING BTREE;

ALTER TABLE `sg_b_share_transfer_import_item`
    ADD INDEX `idx_sg_b_share_transfer_import_item_1` (`sg_b_share_transfer_id`) USING BTREE;

ALTER TABLE `sg_b_share_transfer`
    ADD INDEX `idx_sg_b_share_transfer_1` (`bill_date`) USING BTREE;

CREATE TABLE `SG_C_WAREHOUSE_MAXORDER` (
  `id` bigint(20) NOT NULL COMMENT 'ID',
  `ad_client_id` bigint(20) DEFAULT '37' COMMENT '所属公司',
  `ad_org_id` bigint(20) DEFAULT '27' COMMENT '所属组织',
  `CP_C_PHY_WAREHOUSE_ID` bigint(20) DEFAULT NULL COMMENT '实体仓ID',
  `CP_C_PHY_WAREHOUSE_ECODE` varchar(100) DEFAULT NULL COMMENT '实体仓编号',
  `CP_C_PHY_WAREHOUSE_ENAME` varchar(100) DEFAULT NULL COMMENT '实体仓名称',
  `MAXQTY` decimal(18,4) DEFAULT '0.0000' COMMENT '最大接单量',
  `isactive` char(1) DEFAULT 'Y' COMMENT '是否可用',
  `ownerid` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `ownerename` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `ownername` varchar(50) DEFAULT NULL COMMENT '创建人用户名',
  `creationdate` datetime DEFAULT NULL COMMENT '创建时间',
  `modifierid` bigint(20) DEFAULT NULL COMMENT '修改人ID',
  `modifierename` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
  `modifiername` varchar(50) DEFAULT NULL COMMENT '修改人用户名',
  `modifieddate` datetime DEFAULT NULL COMMENT '修改时间',
  `reserve_bigint01` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)1',
  `reserve_bigint02` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)2',
  `reserve_bigint03` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)3',
  `reserve_bigint04` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)4',
  `reserve_bigint05` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)5',
  `reserve_bigint06` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)6',
  `reserve_bigint07` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)7',
  `reserve_bigint08` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)8',
  `reserve_bigint09` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)9',
  `reserve_bigint10` bigint(20) DEFAULT NULL COMMENT '备用字段(整型)10',
  `reserve_varchar01` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)1',
  `reserve_varchar02` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)2',
  `reserve_varchar03` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)3',
  `reserve_varchar04` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)4',
  `reserve_varchar05` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)5',
  `reserve_varchar06` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)6',
  `reserve_varchar07` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)7',
  `reserve_varchar08` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)8',
  `reserve_varchar09` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)9',
  `reserve_varchar10` varchar(200) DEFAULT NULL COMMENT '备用字段(文本型)10',
  `reserve_decimal01` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)1',
  `reserve_decimal02` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)2',
  `reserve_decimal03` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)3',
  `reserve_decimal04` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)4',
  `reserve_decimal05` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)5',
  `reserve_decimal06` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)6',
  `reserve_decimal07` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)7',
  `reserve_decimal08` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)8',
  `reserve_decimal09` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)9',
  `reserve_decimal10` decimal(18,4) DEFAULT NULL COMMENT '备用字段(浮点型)10',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门店最大接单量设置';


CREATE TABLE `SG_C_STORE_MAX_ORDERS_SETTING` (
  `id` bigint(20) NOT NULL COMMENT 'ID',
  `ad_client_id` bigint(20) DEFAULT NULL COMMENT '所属公司',
  `ad_org_id` bigint(20) DEFAULT NULL COMMENT '所属组织',
  `task_code` varchar(50) DEFAULT NULL COMMENT '任务编码',
  `task_name` varchar(50) DEFAULT NULL COMMENT '任务名称',
  `customize_sql` mediumtext COMMENT '自定义SQL',
  `isactive` char(1) DEFAULT 'Y' COMMENT '启用',
  `ownerid` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `ownerename` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `ownername` varchar(50) DEFAULT NULL COMMENT '创建人用户名',
  `creationdate` datetime DEFAULT NULL COMMENT '创建时间',
  `modifierid` bigint(20) DEFAULT NULL COMMENT '修改人ID',
  `modifierename` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
  `modifiername` varchar(50) DEFAULT NULL COMMENT '修改人用户名',
  `modifieddate` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门店最大接单量-sql定时执行';

INSERT INTO `sg_c_store_max_orders_setting` VALUES (1, 37, 27, 'insert-sg_c_channel_source_strategy_force_item', '每一分钟-写入强制寻源规则', 'INSERT INTO sg_c_channel_source_strategy_force_item (id, ad_client_id, ad_org_id, sg_c_channel_source_strategy_id, sg_c_channel_source_force_strategy_id, sg_c_channel_source_force_strategy_ecode, sg_c_channel_source_force_strategy_ename, cp_c_store_id, cp_c_store_ecode,cp_c_store_ename,isactive,ownerid,ownerename,ownername,creationdate,modifierid,modifierename,modifiername,modifieddate ) SELECT @r := @r + 1,37,27,e.id,1,''PC'',''排除'',c.id,c.cp_c_store_ecode,c.cp_c_store_ename,''Y'',893,''系统管理员'',''root'',NOW(),893,''系统管理员'',''root'',NOW() FROM SG_C_WAREHOUSE_MAXORDER a JOIN cp_c_phy_warehouse b ON a.CP_C_PHY_WAREHOUSE_ID = b.id	JOIN cp_c_store c ON c.cp_c_phy_warehouse_id = b.id	JOIN SG_C_CHANNEL_SOURCE_STRATEGY e ON 1 = 1 JOIN ( SELECT @r := 0 ) x ON 1 = 1	JOIN (SELECT aa.cp_c_phy_warehouse_id,	count( 1 ) AS nowqty FROM	SG_B_STO_OUT_NOTICES aa WHERE	DATE_FORMAT( aa.bill_date, ''%Y-%m-%d'' ) = DATE_FORMAT( NOW(), ''%Y-%m-%d'' ) 	AND aa.isactive = ''Y'' GROUP BY	aa.cp_c_phy_warehouse_id ) d ON a.CP_C_PHY_WAREHOUSE_ID = d.cp_c_phy_warehouse_id WHERE	d.nowqty >= a.MAXQTY 	AND NOT EXISTS ( SELECT 1 FROM sg_c_channel_source_strategy_force_item dd WHERE dd.cp_c_store_id = c.id AND dd.sg_c_channel_source_strategy_id = e.id )', 'Y', 893, '系统管理员', 'root', '2021-10-18 13:08:02', NULL, NULL, NULL, NULL);
INSERT INTO `sg_c_store_max_orders_setting` VALUES (2, 37, 27, 'delete-sg_c_channel_source_strategy_force_item', '每天凌晨0点-清空强制寻源规则', 'Delete from SG_C_CHANNEL_SOURCE_STRATEGY_FORCE_ITEM', 'Y', 893, '系统管理员', 'root', '2021-10-18 13:08:02', NULL, NULL, NULL, NULL);
INSERT INTO `sg_c_store_max_orders_setting` VALUES (3, 37, 27, 'update-sg_c_store_score_strategy_one', '每一分钟-平衡门店的接单量，防止单一门店派单量过大', 'UPDATE SG_C_STORE_SCORE_STRATEGY e, ( SELECT 	@r := @r + 1 AS num, 	a.cp_c_phy_warehouse_id, 	a.nowqty  FROM 	( SELECT 	aa.cp_c_phy_warehouse_id, 	count( 1 ) AS nowqty  FROM 	SG_B_STO_OUT_NOTICES aa  WHERE 	DATE_FORMAT( aa.bill_date, ''%Y-%m-%d'' ) = DATE_FORMAT( NOW( ), ''%Y-%m-%d'' )  	AND aa.isactive = ''Y''  GROUP BY 	aa.cp_c_phy_warehouse_id  ORDER BY 	nowqty DESC	) a, ( SELECT @r := 0 ) x  ) f  SET e.SCORE = f.num  WHERE 	e.cp_c_phy_warehouse_id = f.cp_c_phy_warehouse_id 	AND e.sg_c_share_score_factor_strategy_ename = ''评分因子''', 'Y', 893, '系统管理员', 'root', '2021-10-18 13:08:02', NULL, NULL, NULL, NULL);
INSERT INTO `sg_c_store_max_orders_setting` VALUES (4, 37, 27, 'update-sg_c_store_score_strategy', '每天凌晨0点-更新所有实体仓的【店铺评分设置表】分值为0', 'UPDATE SG_C_STORE_SCORE_STRATEGY e SET e.SCORE = 0 WHERE e.sg_c_share_score_factor_strategy_ename = ''评分因子''', 'Y', 893, '系统管理员', 'root', '2021-10-18 13:08:02', NULL, NULL, NULL, NULL);
