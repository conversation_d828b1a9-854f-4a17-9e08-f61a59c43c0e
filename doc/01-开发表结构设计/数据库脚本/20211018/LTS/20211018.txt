SgCStoreMaxOrdersSettingTask-001   0 0/1 * * * ? *   {"param":"{\"taskcode\":\"insert-sg_c_channel_source_strategy_force_item\"}","className":"com.burgeon.r3.sg.sourcing.task.SgCStoreMaxOrdersSettingTask","type":"localJob"}
SgCStoreMaxOrdersSettingTask-002   0 0 0 */1 * ? *   {"param":"{\"taskcode\":\"delete-sg_c_channel_source_strategy_force_item\"}","className":"com.burgeon.r3.sg.sourcing.task.SgCStoreMaxOrdersSettingTask","type":"localJob"}
SgCStoreMaxOrdersSettingTask-003   0 0/1 * * * ? *   {"param":"{\"taskcode\":\"update-sg_c_store_score_strategy-one\"}","className":"com.burgeon.r3.sg.sourcing.task.SgCStoreMaxOrdersSettingTask","type":"localJob"}
SgCStoreMaxOrdersSettingTask-004   0 0 0 */1 * ? *   {"param":"{\"taskcode\":\"update-sg_c_store_score_strategy\"}","className":"com.burgeon.r3.sg.sourcing.task.SgCStoreMaxOrdersSettingTask","type":"localJob"}
