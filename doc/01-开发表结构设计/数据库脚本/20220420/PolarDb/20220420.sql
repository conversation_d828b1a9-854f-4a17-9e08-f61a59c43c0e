-- 多渠道调拨单结果查询
DROP VIEW `data_check`.v_sg_b_sto_multi_channel_transfer_query_result;

CREATE VIEW v_sg_b_sto_multi_channel_transfer_query_result AS
SELECT
t.id,
1 AS transfer_type,-- 调拨类型
'SG_B_STO_BATCH_TRANSFER' AS bill_type,-- 单据类型 逻辑调拨单批量导入
IF
	( t.`status` = 3, 1, 2 ) AS bill_is_complete,-- 单据是否完成 1:全部提交 2:其它状态
	( CASE t.`status` WHEN 1 THEN 0 WHEN 2 THEN 1 WHEN 3 THEN 2 WHEN 4 THEN 3 ELSE 9 END ) AS bill_status,-- 单据状态
	t.bill_no,-- 单据编号
	t.bill_date,-- 单据日期
	s1.sender_store_ecode AS sender_ecode,-- 发货逻辑仓编码
	s1.sender_store_ename AS sender_ename,-- 发货逻辑仓编码
	s1.receiver_store_ecode AS receiver_ecode,-- 发货逻辑仓编码
	s1.receiver_store_ename AS receiver_ename,-- 发货逻辑仓编码
	s1.qty,-- 数量
	s1.qty_out,-- 实际调拨数量
	NULL AS allocation_return_qty,-- 分货退货数量,
	NULL AS storage_qty,-- 聚合调拨数量
	NULL AS return_qty,-- 分货数量
	s1.remark,-- 备注
	t.ownerid,-- 创建人
	t.ownerename,
	t.ownername,
	t.creationdate,
	t.modifierid,-- 修改人
	t.modifierename,
	t.modifiername,
	t.modifieddate,
	t.isactive,
	t.ad_org_id,
	t.ad_client_id
FROM
	prod_r3_oms_sg.sg_b_sto_batch_transfer t
	JOIN (
	SELECT
		id,
		sg_b_sto_batch_transfer_id,
		sender_store_ecode,
		sender_store_ename,
		receiver_store_ecode,
		receiver_store_ename,
		sum( qty ) AS qty,
		sum( qty_out ) AS qty_out,
		remark
	FROM
		prod_r3_oms_sg.sg_b_sto_batch_transfer_item
	GROUP BY
		sg_b_sto_batch_transfer_id,
		sender_store_ecode,
		receiver_store_ecode
	) s1 ON t.id = s1.sg_b_sto_batch_transfer_id
WHERE
	t.source_bill_type = 45 UNION ALL
SELECT
	t.id,
	2 AS transfer_type,-- 调拨类型
	'SG_B_SHARE_TRANSFER' AS bill_type,-- 单据类型:聚合仓调拨单
IF
	( t.`status` = 2, 1, 2 ) AS bill_is_complete,-- 单据是否完成 1:全部提交 2:其它状态
	( CASE t.`status` WHEN 1 THEN 0 WHEN 2 THEN 2 WHEN 4 THEN 3 WHEN 0 THEN 1 ELSE 9 END ) AS bill_status,-- 单据状态
	t.bill_no,-- 单据编号
	t.bill_date,-- 单据日期
	s1.sender_share_store_ecode AS sender_ecode,-- 发货聚合仓编码
	s1.sender_share_store_ename AS sender_ename,-- 发货聚合仓编码
	s1.receiver_share_store_ecode AS receiver_ecode,-- 发货聚合仓编码
	s1.receiver_share_store_ename AS receiver_ename,-- 发货聚合仓编码
	s1.qty,-- 数量
	s1.qty_out,-- 实际调拨数量
	NULL AS allocation_return_qty,-- 分货退货数量,
	NULL AS storage_qty,-- 聚合调拨数量
	NULL AS return_qty,-- 分货数量
	s1.remark,-- 备注
	t.ownerid,-- 创建人
	t.ownerename,
	t.ownername,
	t.creationdate,
	t.modifierid,-- 修改人
	t.modifierename,
	t.modifiername,
	t.modifieddate,
	t.isactive,
	t.ad_org_id,
	t.ad_client_id
FROM
	prod_r3_oms_sg.sg_b_share_transfer t
	JOIN (
	SELECT
		id,
		sg_b_share_transfer_id,
		sender_share_store_ecode,
		sender_share_store_ename,
		receiver_share_store_ecode,
		receiver_share_store_ename,
		sum( qty ) AS qty,
		sum( qty_out ) AS qty_out,
		remark
	FROM
		prod_r3_oms_sg.sg_b_share_transfer_item
	GROUP BY
		sg_b_share_transfer_id,
		sender_share_store_ecode,
		receiver_share_store_ecode
	) s1 ON t.id = s1.sg_b_share_transfer_id
WHERE
	t.source_bill_type = 45 UNION ALL
SELECT
	t.id,
	2 AS transfer_type,-- 调拨类型
	'SG_B_SHARE_SA_CROSS_TRANSFER' AS bill_type,-- 单据类型:配销仓调拨单
IF
	( t.`status` = 2, 1, 2 ) AS bill_is_complete,-- 单据是否完成 1:全部提交 2:其它状态
	( CASE t.`status` WHEN 1 THEN 0 WHEN 2 THEN 5 WHEN 4 THEN 4 WHEN 3 THEN 3 ELSE 9 END ) AS bill_status,-- 单据状态
	t.bill_no,-- 单据编号
	t.bill_date,-- 单据日期
	s1.sender_sa_store_ecode AS sender_ecode,-- 发货配销仓编码
	s1.sender_sa_store_ename AS sender_ename,-- 发货配销仓名称
	s1.receiver_sa_store_ecode AS receiver_ecode,-- 收货配销仓编码
	s1.receiver_sa_store_ename AS receiver_ename,-- 收货配销仓名称
	s1.qty,-- 数量
	NULL AS qty_out,-- 实际调拨数量
	s1.allocation_return_qty,-- 分货退货数量,
	s1.storage_qty,-- 聚合调拨数量
	s1.return_qty,-- 分货数量
	s1.remark,-- 备注
	t.ownerid,-- 创建人
	t.ownerename,
	t.ownername,
	t.creationdate,
	t.modifierid,-- 修改人
	t.modifierename,
	t.modifiername,
	t.modifieddate,
	t.isactive,
	t.ad_org_id,
	t.ad_client_id
FROM
	prod_r3_oms_sg.sg_b_share_sa_cross_transfer t
	JOIN (
	SELECT
		id,
		sg_b_share_sa_cross_transfer_id,
		sender_sa_store_ecode,
		sender_sa_store_ename,
		receiver_sa_store_ecode,
		receiver_sa_store_ename,
		sum( qty_apply ) AS qty,
		sum( qty_allocation_return ) AS allocation_return_qty,
		sum( qty_share_transfer ) AS storage_qty,
		sum( qty_allocation ) AS return_qty,
		remark
	FROM
		prod_r3_oms_sg.sg_b_share_sa_cross_transfer_result_item
	GROUP BY
		sg_b_share_sa_cross_transfer_id,
		sender_sa_store_ecode,
		receiver_sa_store_ecode
	) s1 ON t.id = s1.sg_b_share_sa_cross_transfer_id
WHERE
	t.source_bill_type = 45 UNION ALL
SELECT
	t.id,
	2 AS transfer_type,-- 调拨类型
	'SG_B_SHARE_FROM_SA_TRANSFER' AS bill_type,-- 单据类型:配销仓到聚合仓调拨单
IF
	( t.`status` = 5, 1, 2 ) AS bill_is_complete,-- 单据是否完成 1:全部提交 2:其它状态
	(
	CASE
			t.`status`
			WHEN 1 THEN
			0
			WHEN 2 THEN
			5
			WHEN 3 THEN
			3
			WHEN 4 THEN
			4
			WHEN 5 THEN
			8 ELSE 9
		END
		) AS bill_status,-- 单据状态
		t.bill_no,-- 单据编号
		t.bill_date,-- 单据日期
		s1.sender_sa_store_ecode AS sender_ecode,-- 发货配销仓编码
		s1.sender_sa_store_ename AS sender_ename,-- 发货配销仓名称
		s1.receiver_share_store_ecode AS receiver_ecode,-- 收货聚合仓编码
		s1.receiver_share_store_ename AS receiver_ename,-- 收货聚合仓名称
		s1.qty,-- 数量
		NULL AS qty_out,-- 实际调拨数量
		s1.allocation_return_qty,-- 分货退货数量,
		s1.storage_qty,-- 聚合调拨数量
		NULL AS return_qty,-- 分货数量
		s1.remark,-- 备注
		t.ownerid,-- 创建人
		t.ownerename,
		t.ownername,
		t.creationdate,
		t.modifierid,-- 修改人
		t.modifierename,
		t.modifiername,
		t.modifieddate,
		t.isactive,
		t.ad_org_id,
		t.ad_client_id
	FROM
		prod_r3_oms_sg.sg_b_share_from_sa_transfer t
		JOIN (
		SELECT
			id,
			sg_b_share_from_sa_transfer_id,
			sender_sa_store_ecode,
			sender_sa_store_ename,
			receiver_share_store_ecode,
			receiver_share_store_ename,
			sum( qty_apply ) AS qty,
			sum( qty_allocation_return ) AS allocation_return_qty,
			sum( qty_share_transfer ) AS storage_qty,
			remark
		FROM
			prod_r3_oms_sg.sg_b_share_from_sa_transfer_result_item
		GROUP BY
			sg_b_share_from_sa_transfer_id,
			sender_sa_store_ecode,
			receiver_share_store_ecode
		) s1 ON t.id = s1.sg_b_share_from_sa_transfer_id
	WHERE
		t.source_bill_type = 45 UNION ALL
	SELECT
		t.id,
		2 AS transfer_type,-- 调拨类型
		'SG_B_SHARE_SA_ALLOCATION_TRANSFER' AS bill_type,-- 单据类型:聚合仓到配销仓调拨单
	IF
		( t.`status` = 3, 1, 2 ) AS bill_is_complete,-- 单据是否完成 1:全部提交 2:其它状态
		( CASE t.`status` WHEN 1 THEN 0 WHEN 2 THEN 6 WHEN 3 THEN 7 WHEN 4 THEN 3 ELSE 9 END ) AS bill_status,-- 单据状态
		t.bill_no,-- 单据编号
		t.bill_date,-- 单据日期
		s1.sender_share_store_ecode AS sender_ecode,-- 发货聚合仓编码
		s1.sender_share_store_ename AS sender_ename,-- 发货聚合仓名称
		s1.receiver_sa_store_ecode AS receiver_ecode,-- 收货配销仓编码
		s1.receiver_sa_store_ename AS receiver_ename,-- 收货配销仓名称
		s1.qty,-- 数量
		NULL AS qty_out,-- 实际调拨数量
		NULL AS allocation_return_qty,-- 分货退货数量,
		s1.storage_qty,-- 聚合调拨数量
		s1.return_qty,-- 分货数量
		s1.remark,-- 备注
		t.ownerid,-- 创建人
		t.ownerename,
		t.ownername,
		t.creationdate,
		t.modifierid,-- 修改人
		t.modifierename,
		t.modifiername,
		t.modifieddate,
		t.isactive,
		t.ad_org_id,
		t.ad_client_id
	FROM
		prod_r3_oms_sg.sg_b_share_sa_allocation_transfer t
		JOIN (
		SELECT
			id,
			sg_b_share_sa_allocation_transfer_id,
			sender_share_store_ecode,
			sender_share_store_ename,
			receiver_sa_store_ecode,
			receiver_sa_store_ename,
			sum( qty_apply ) AS qty,
			sum( qty_share_transf ) AS storage_qty,
			sum( qty_allocation ) AS return_qty,
			remark
		FROM
			prod_r3_oms_sg.sg_b_share_sa_allocation_transfer_item
		GROUP BY
			sg_b_share_sa_allocation_transfer_id,
			sender_share_store_ecode,
			receiver_sa_store_ecode
		) s1 ON t.id = s1.sg_b_share_sa_allocation_transfer_id
WHERE
	t.source_bill_type = 45;