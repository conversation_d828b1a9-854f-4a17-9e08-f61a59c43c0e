<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.0.6.RELEASE</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.burgeon.r3</groupId>
    <artifactId>r3-sg</artifactId>
    <version>3.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>modules/core/r3-sg-core-model</module>
        <module>modules/core/r3-sg-core-utils</module>

        <module>modules/basic/r3-sg-basic-api</module>
        <module>modules/basic/r3-sg-basic-bll</module>
        <module>modules/basic/r3-sg-basic-ctrl</module>
        <module>modules/basic/r3-sg-basic-srv</module>
        <module>modules/basic/r3-sg-basic-task</module>

        <module>modules/store/r3-sg-store-api</module>
        <module>modules/store/r3-sg-store-bll</module>
        <module>modules/store/r3-sg-store-ctrl</module>
        <module>modules/store/r3-sg-store-srv</module>
        <module>modules/store/r3-sg-store-task</module>

        <module>modules/share/r3-sg-share-api</module>
        <module>modules/share/r3-sg-share-bll</module>
        <module>modules/share/r3-sg-share-ctrl</module>
        <module>modules/share/r3-sg-share-srv</module>
        <module>modules/share/r3-sg-share-task</module>

        <module>modules/channel/r3-sg-channel-api</module>
        <module>modules/channel/r3-sg-channel-bll</module>
        <module>modules/channel/r3-sg-channel-ctrl</module>
        <module>modules/channel/r3-sg-channel-srv</module>
        <module>modules/channel/r3-sg-channel-task</module>

        <module>modules/interface/r3-sg-interface-api</module>
        <module>modules/interface/r3-sg-interface-bll</module>
        <module>modules/interface/r3-sg-interface-ctrl</module>
        <module>modules/interface/r3-sg-interface-srv</module>
        <module>modules/interface/r3-sg-interface-task</module>

        <module>modules/sourcing/r3-sg-sourcing-api</module>
        <module>modules/sourcing/r3-sg-sourcing-bll</module>
        <module>modules/sourcing/r3-sg-sourcing-ctrl</module>
        <module>modules/sourcing/r3-sg-sourcing-srv</module>
        <module>modules/sourcing/r3-sg-sourcing-task</module>

        <module>modules/migration/r3-sg-migration-api</module>
        <module>modules/migration/r3-sg-migration-bll</module>
        <module>modules/migration/r3-sg-migration-ctrl</module>
        <module>modules/migration/r3-sg-migration-srv</module>

        <module>modules/stocksync/r3-sg-stocksync-api</module>
        <module>modules/stocksync/r3-sg-stocksync-bll</module>
        <module>modules/stocksync/r3-sg-stocksync-ctrl</module>
        <module>modules/stocksync/r3-sg-stocksync-srv</module>
        <module>modules/stocksync/r3-sg-stocksync-task</module>

        <module>shell/r3-sg-shell-dubbo</module>
    </modules>

    <properties>
        <project.stable.version>3.0.0-SNAPSHOT</project.stable.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>1.8</java.version>

        <raincloud.version>1.4.0-SNAPSHOT</raincloud.version>
        <raincloud.dubbo.version>1.4.1-SNAPSHOT</raincloud.dubbo.version>
        <raincloud.edas.version>1.4.1-SNAPSHOT</raincloud.edas.version>
        <r3.version>1.4.0-SNAPSHOT</r3.version>
        <r3.framework.version>3.0.0-SNAPSHOT</r3.framework.version>
        <r3.project.version>3.0.0-SNAPSHOT</r3.project.version>

        <r3.mq.version>3.1.0-SNAPSHOT</r3.mq.version>
        <r3.service.task.version>3.1.0-SNAPSHOT</r3.service.task.version>
        <r3.system.shutdown.version>3.2.0-SNAPSHOT</r3.system.shutdown.version>

        <Swagger.version>2.7.0</Swagger.version>
        <guava.version>18.0</guava.version>
        <commons.langs.version>3.3.2</commons.langs.version>
        <elasticsearch.version>5.5.3</elasticsearch.version>
        <fastjson.version>1.2.83</fastjson.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- r3-base -->
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-model-query</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-dubbo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-model-base</artifactId>
                <version>${r3.framework.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-es</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-jdbc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-redis</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-web</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-data-basic-bll</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>r3-sg-basic-api</artifactId>
                        <groupId>com.burgeon.r3</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>r3-sg-store-api</artifactId>
                        <groupId>com.burgeon.r3</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-dubbo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--<dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-mq</artifactId>
                <version>${r3.mq.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>r3-model-base</artifactId>
                        <groupId>com.burgeon.r3</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-es</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.burgeon.r3</groupId>
                        <artifactId>r3-elasticsearch-ext</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>-->
            <dependency>
                <groupId>com.burgeon.mq</groupId>
                <artifactId>mq-spring-boot-starter</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>com.burgeon.r3</groupId>-->
            <!--                <artifactId>r3-service-task</artifactId>-->
            <!--                <version>${r3.service.task.version}</version>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>ryytn-xxl-job-spring-boot-starter</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <!--健康检查、优雅关闭-->
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-system-shutdown</artifactId>
                <version>${r3.system.shutdown.version}</version>
            </dependency>

            <!-- r3-module -->
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-sg-interface-task</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>apollo-client</artifactId>
                        <groupId>com.ctrip.framework.apollo</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-dubbo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-sg-store-ctrl</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>apollo-client</artifactId>
                        <groupId>com.ctrip.framework.apollo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>raincloud-dubbo</artifactId>
                        <groupId>org.syman</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-sg-basic-task</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>raincloud-dubbo</artifactId>
                        <groupId>org.syman</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-ad-util</artifactId>
                <version>${project.stable.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>raincloud-sysapi</artifactId>
                        <groupId>org.syman</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-dubbo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- r3-service -->
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-service-util</artifactId>
                <version>${r3.framework.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.burgeon.r3</groupId>
                        <artifactId>r3-cp-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-dubbo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-service-core</artifactId>
                <version>3.1.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-model-base</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.aliyun.openservices</groupId>
                        <artifactId>ons-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-dubbo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-service-api-model</artifactId>
                <version>${r3.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>R3-Starter-Dubbo</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>2.12.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- r3-srv-parent -->
        <dependency>
            <groupId>org.syman</groupId>
            <artifactId>raincloud-util</artifactId>
            <version>${raincloud.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
            <version>2.7.7</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo-registry-nacos</artifactId>
            <version>2.7.7</version>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo-common</artifactId>
                    <groupId>org.apache.dubbo</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- springboot -->
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
            <version>0.2.7</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.2.13</version>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis.spring.boot</groupId>
                    <artifactId>mybatis-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
            <version>2.7.7</version>
        </dependency>

        <!-- other -->
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>
    </dependencies>

    <distributionManagement>
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <url>https://packages.aliyun.com/maven/repository/2228469-snapshot-tknOVW/</url>
        </snapshotRepository>
        <repository>
            <id>rdc-releases</id>
            <url>https://packages.aliyun.com/maven/repository/2228469-release-3W5OzY/</url>
        </repository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <verbose>true</verbose>
                    <fork>true</fork>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>