<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.9.0.final using JasperReports Library version 6.9.0-cb8f9004be492ccc537180b49c026951f4220bf3  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="sg_b_sto_transfer_main" pageWidth="755" pageHeight="600" orientation="Landscape" columnWidth="730" leftMargin="15" rightMargin="10" topMargin="20" bottomMargin="15" uuid="8f4b6ef9-e236-4f88-98e9-0adf458596d3">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="斯凯奇\skxTransfer.xml"/>
	<queryString language="JSON">
		<![CDATA[SG_B_STO_TRANSFER]]>
	</queryString>
	<field name="BILL_DATE" class="java.sql.Date">
		<property name="net.sf.jasperreports.json.field.expression" value="BILL_DATE"/>
		<fieldDescription><![CDATA[BILL_DATE]]></fieldDescription>
	</field>
	<field name="BILL_NO" class="java.lang.String">
		<property name="net.sf.jasperreports.json.field.expression" value="BILL_NO"/>
		<fieldDescription><![CDATA[BILL_NO]]></fieldDescription>
	</field>
	<field name="COLUMNS" class="java.lang.Integer">
		<property name="net.sf.jasperreports.json.field.expression" value="COLUMNS"/>
		<fieldDescription><![CDATA[COLUMNS]]></fieldDescription>
	</field>
	<field name="CREATIONDATE" class="java.sql.Date">
		<property name="net.sf.jasperreports.json.field.expression" value="CREATIONDATE"/>
		<fieldDescription><![CDATA[CREATIONDATE]]></fieldDescription>
	</field>
	<field name="CURRENT_DATE_TIME" class="java.sql.Date">
		<property name="net.sf.jasperreports.json.field.expression" value="CURRENT_DATE_TIME"/>
		<fieldDescription><![CDATA[CURRENT_DATE_TIME]]></fieldDescription>
	</field>
	<field name="OWNERNAME" class="java.lang.String">
		<property name="net.sf.jasperreports.json.field.expression" value="OWNERNAME"/>
		<fieldDescription><![CDATA[OWNERNAME]]></fieldDescription>
	</field>
	<field name="RECEIVER_ADDRESS" class="java.lang.String">
		<property name="net.sf.jasperreports.json.field.expression" value="RECEIVER_ADDRESS"/>
		<fieldDescription><![CDATA[RECEIVER_ADDRESS]]></fieldDescription>
	</field>
	<field name="RECEIVER_STORE_ECODE" class="java.lang.String">
		<property name="net.sf.jasperreports.json.field.expression" value="RECEIVER_STORE_ECODE"/>
		<fieldDescription><![CDATA[RECEIVER_STORE_ECODE]]></fieldDescription>
	</field>
	<field name="REMARK" class="java.lang.String">
		<property name="net.sf.jasperreports.json.field.expression" value="REMARK"/>
		<fieldDescription><![CDATA[REMARK]]></fieldDescription>
	</field>
	<field name="SC_B_TRANSFER_SUB_REPORT" class="java.lang.String">
		<property name="net.sf.jasperreports.json.field.expression" value="SC_B_TRANSFER_SUB_REPORT"/>
		<fieldDescription><![CDATA[SC_B_TRANSFER_SUB_REPORT]]></fieldDescription>
	</field>
	<field name="SENDER_STORE_ECODE" class="java.lang.String">
		<property name="net.sf.jasperreports.json.field.expression" value="SENDER_STORE_ECODE"/>
		<fieldDescription><![CDATA[SENDER_STORE_ECODE]]></fieldDescription>
	</field>
	<field name="STATUS_ENAME" class="java.lang.String">
		<property name="net.sf.jasperreports.json.field.expression" value="STATUS_ENAME"/>
		<fieldDescription><![CDATA[STATUS_ENAME]]></fieldDescription>
	</field>
	<field name="STATUS_TIME" class="java.sql.Date">
		<property name="net.sf.jasperreports.json.field.expression" value="STATUS_TIME"/>
		<fieldDescription><![CDATA[STATUS_TIME]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="362" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<subreport>
				<reportElement x="0" y="107" width="730" height="255" uuid="2e3977cc-bde5-46c6-a069-24154cc13d26">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<subreportParameter name="COLUMNS">
					<subreportParameterExpression><![CDATA[$F{COLUMNS}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("SC_B_TRANSFER_SUB_REPORT")]]></dataSourceExpression>
				<subreportExpression><![CDATA["sg_b_sto_transfer_sub.jasper"]]></subreportExpression>
			</subreport>
			<staticText>
				<reportElement x="0" y="3" width="730" height="24" uuid="dc3ae4be-2d1e-4ed0-bc39-9a32948ca4b7">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="16"/>
				</textElement>
				<text><![CDATA[调拨出库单]]></text>
			</staticText>
			<staticText>
				<reportElement x="30" y="27" width="60" height="20" uuid="e1fd0d08-**************-d3d3c1692dbc">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<text><![CDATA[单据编号：]]></text>
			</staticText>
			<staticText>
				<reportElement x="30" y="47" width="60" height="20" uuid="9dbda8b6-5876-41d3-97b5-2d3dc3f59e43">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<text><![CDATA[发货店仓：]]></text>
			</staticText>
			<staticText>
				<reportElement x="430" y="27" width="60" height="20" uuid="81dd91dc-8a66-4837-8e05-8f8c9ec39d02">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<text><![CDATA[单据日期：]]></text>
			</staticText>
			<staticText>
				<reportElement x="430" y="47" width="60" height="20" uuid="650fa0d0-3a59-4e36-84e0-4739f72dbd03">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<text><![CDATA[打印日期：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="90" y="27" width="160" height="20" uuid="f8c0cf22-973b-4f40-abc8-9aa0a993ec4c">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BILL_NO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="90" y="47" width="160" height="20" uuid="4c494544-d22a-4eb9-8fa3-875384fb6f5e">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SENDER_STORE_ECODE}]]></textFieldExpression>
			</textField>
			<textField pattern="yyyy-MM-dd" isBlankWhenNull="true">
				<reportElement x="490" y="27" width="160" height="20" uuid="babd00f4-8d94-4bb3-a230-61c9b58e2646">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BILL_DATE}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="490" y="47" width="160" height="20" uuid="b10b63d9-c6dd-484e-8ef4-4a6b867c6a28">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CURRENT_DATE_TIME}.toString()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="460" y="67" width="30" height="15" uuid="09848bef-db4e-4364-8d65-501935cb21f2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["第" + $V{PAGE_NUMBER}+"页"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="490" y="67" width="50" height="15" uuid="ab76c9d1-ed32-4f96-a076-2c50f369b2a6">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["/共" + $V{PAGE_NUMBER}+"页"]]></textFieldExpression>
			</textField>
			<break>
				<reportElement x="0" y="0" width="730" height="1" uuid="b016e585-61b7-415e-9149-6fc8628b912f">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</break>
			<break>
				<reportElement x="0" y="361" width="730" height="1" uuid="99a7abe1-538a-4d02-bb80-766b6551e7a0">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
			</break>
			<staticText>
				<reportElement x="30" y="67" width="60" height="20" uuid="e2f5db2d-5f42-4d67-9960-ccef122361f9">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<text><![CDATA[收货店仓：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="90" y="67" width="160" height="20" uuid="dfa0e3c8-2bae-4532-9812-e6cbcad20f34">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RECEIVER_STORE_ECODE}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="30" y="87" width="60" height="20" uuid="a2737e3a-473d-4bac-9875-5d8ff9f65498">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<text><![CDATA[收货地址：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="90" y="87" width="280" height="20" uuid="12084b27-946e-4e74-8838-feb4ff9a6370">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RECEIVER_ADDRESS}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="60">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="45" y="20" width="50" height="20" uuid="8516982e-8829-4f67-81bc-8b141fc41c66">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<text><![CDATA[创建人：]]></text>
			</staticText>
			<textField>
				<reportElement x="95" y="20" width="122" height="20" uuid="1ee18008-5163-4004-a10d-d5fe3eea9ef2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OWNERNAME}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="45" y="40" width="60" height="20" uuid="62a16a00-93b2-42e8-a57f-9922bcb7b438">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<text><![CDATA[创建日期：]]></text>
			</staticText>
			<textField pattern="yyyy-MM-dd">
				<reportElement x="105" y="40" width="100" height="20" uuid="e0d04af4-e7b0-46b0-9c44-4dbd99bac30a">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CREATIONDATE}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="45" y="0" width="60" height="20" uuid="2e76237f-64ed-40e7-9416-b3e88b1502cc">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<text><![CDATA[备注：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="95" y="0" width="160" height="20" uuid="ef90fdec-ceaf-42f0-8d7b-aa5d1dd74d4d">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{REMARK}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="230" y="20" width="50" height="20" uuid="6a2ff7d9-d988-4b2d-b4c5-8e11c3ec482e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<text><![CDATA[提交人：]]></text>
			</staticText>
			<textField>
				<reportElement x="280" y="20" width="122" height="20" uuid="9ddf11d3-9865-4d14-a836-9151b0454bbb">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{STATUS_ENAME}]]></textFieldExpression>
			</textField>
			<textField pattern="yyyy-MM-dd">
				<reportElement x="290" y="40" width="100" height="20" uuid="89abe669-2552-4ddb-8b29-e4d6e454a885">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{STATUS_TIME}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="230" y="40" width="60" height="20" uuid="fb767289-8d58-4801-89d7-d5b0c64a4fd2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10"/>
				</textElement>
				<text><![CDATA[提交日期：]]></text>
			</staticText>
		</band>
	</pageFooter>
</jasperReport>
