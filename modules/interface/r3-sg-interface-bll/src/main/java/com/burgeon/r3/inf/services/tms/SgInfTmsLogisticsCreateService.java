package com.burgeon.r3.inf.services.tms;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.store.model.result.tms.LogisticsResult;
import com.burgeon.r3.sg.store.services.tms.out.SgTmsLogisticsCreateService;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/16 15:46
 */
@Slf4j
@Component
public class SgInfTmsLogisticsCreateService {

    @Autowired
    private SgTmsLogisticsCreateService createService;

    /**
     * 本地模拟请求的下发接口
     *
     * @param querySession 本地模拟请求参数
     * @return return
     */
    public ValueHolder orderSend(QuerySession querySession) {
        String request = querySession.getAttribute("request").toString();

        if (log.isDebugEnabled()) {
            log.debug("Start SgInfTmsOrderSendService.orderSend:request{}", request);
        }
        ValueHolder valueHolder = new ValueHolder();

        try {
            JSONObject jsonObject = JSONObject.parseObject(request);
            AssertUtils.notBlank(request, "SgInfTmsOrderSendService.orderSend:请求参数不能为空！");
//            LogisticsResult result = doSend(jsonObject, new SgBStoOutNotices());
//            LogisticsResult result = createService.transSend()
            HashMap<String, String> resultMap = new HashMap<>(1);
//            resultMap.put("result", result.toString());
//            log.info("SgInfTmsLogisticsCreateService.orderSend:result={}", result.toString());
            valueHolder.setData(resultMap);
        } catch (Exception e) {
            AssertUtils.logAndThrowException("SgInfTmsLogisticsCreateService.orderSend:请求发生了异常！", e);
        }

        return valueHolder;
    }

    /**
     * 处理批量的订单,转换成xml格式的json，调用下发
     *
     * @param list 入参
     * @return return
     */
    public ValueHolderV14<LogisticsResult> transSend(List<SgBStoOutNotices> list) {
        ValueHolderV14<LogisticsResult> v14 = new ValueHolderV14<>();
        if (log.isDebugEnabled()) {
            log.debug("Start SgInfTmsLogisticsCreateService.transSend:request={}", JSONObject.toJSONString(list));
        }
//        createService.transSend(list);
        v14.setCode(ResultCode.SUCCESS);
        return v14;
    }
}
