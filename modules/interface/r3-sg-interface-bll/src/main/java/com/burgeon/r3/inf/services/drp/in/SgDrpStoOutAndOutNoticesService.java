package com.burgeon.r3.inf.services.drp.in;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.inf.config.SgDrpConfig;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpStoOutItemSaveRequest;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpStoOutSaveRequest;
import com.burgeon.r3.sg.inf.model.result.drp.in.SgDrpStoOutSaveResult;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutMapper;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutSaveRequest;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutBillSaveResult;
import com.burgeon.r3.sg.store.services.out.SgBStoOutSaveService;
import com.google.common.base.Throwables;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description DRP--->中台：逻辑占用单创建并自动出库：生成逻辑占用单并审核->生成出库通知单->生成逻辑出库单创建并审核（反向更新出库通知单状态）->
 * 生成逻辑在途单->生成入库单通知单
 * <AUTHOR>
 * @Date 2021/7/15 13:49
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgDrpStoOutAndOutNoticesService {

    @Autowired
    private SgBStoOutSaveService sgBStoOutSaveService;

    @Autowired
    private SgBStoOutMapper sgBStoOutMapper;

    @Autowired
    private SgDrpConfig sgDrpConfig;

    public ValueHolderV14<SgDrpStoOutSaveResult> saveSgDrpStoOutAndOutNotices(SgDrpStoOutSaveRequest request) {
        if (!sgDrpConfig.getEnableDrpSend()) {
            return new ValueHolderV14<>(ResultCode.FAIL, "中台系统未开放,请稍后再试!");
        }

        log.info("Start SgDrpStoOutAndOutNoticesService.saveSgDrpStoOutAndOutNotices param={}",
                JSONObject.toJSONString(request));

        String lockKsy = SgConstants.INTERFACE_DRP + ":" + SgConstants.SG_B_STO_OUT + ":" +
                SgConstants.SG_B_STO_OUT_NOTICES + ":" + request.getSourceBillNo();
        SgRedisLockUtils.lock(lockKsy);

        ValueHolderV14<SgDrpStoOutSaveResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "创建并自动出库成功");
        User user = request.getLoginUser() == null ? DrpUtils.getUser() : request.getLoginUser();
        checkParams(request);

        ValueHolderV14<SgBStoOutBillSaveRequest> sgStoOutBillSaveRequestValueHolderV14 = insertStoOut(request, user);
        if (!sgStoOutBillSaveRequestValueHolderV14.isOK()) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(sgStoOutBillSaveRequestValueHolderV14.getMessage());
            return v14;
        }
        SgBStoOutBillSaveRequest sgBStoOutBillSaveRequest = sgStoOutBillSaveRequestValueHolderV14.getData();

        //保存报错时redis中需要回滚的数据
        List<String> redisBillFtpKeyList = new ArrayList<>();
        try {
            if (StringUtils.isEmpty(sgBStoOutBillSaveRequest.getSgBStoOutSaveRequest().getBillDate())) {
                sgBStoOutBillSaveRequest.getSgBStoOutSaveRequest().setBillDate(new Date());
            }
            //修改方式判断：全量或增量
            sgBStoOutBillSaveRequest.setUpdateMethod(SgConstantsIF.ITEM_UPDATE_TYPE_ALL);
            //定义接口标识
            sgBStoOutBillSaveRequest.setInterfaceTypeFlag(SgConstantsIF.Interface_FLAG_DRP_OUT_NOTICES);
            //是否允许负库存
            sgBStoOutBillSaveRequest.setIsNegativePrein(false);
            sgBStoOutBillSaveRequest.setPreoutWarningType(SgConstantsIF.PREOUT_OPT_TYPE_ERROR);
            sgBStoOutBillSaveRequest.setIsCancel(false);
            // 执行逻辑占用单的具体创建逻辑
            ValueHolderV14<SgBStoOutBillSaveResult> valueHolderV14Result =
                    sgBStoOutSaveService.saveSgStoOut(sgBStoOutBillSaveRequest);
            if (!valueHolderV14Result.isOK()) {
                AssertUtils.logAndThrow("新增逻辑占用单失败！" + valueHolderV14Result.getMessage(), user.getLocale());
            }
            redisBillFtpKeyList.addAll(valueHolderV14Result.getData().getRedisKey());
            if (log.isDebugEnabled()) {
                log.debug("SgDrpStoOutAndOutNoticesService.saveSgDrpStoOutAndOutNotices.sgBStoOutSaveService" +
                        ".saveSgStoOut:valueHolderV14Result:{}", JSONObject.toJSONString(valueHolderV14Result));
            }

            SgDrpStoOutSaveResult sgDrpStoOutSaveResult = new SgDrpStoOutSaveResult();
            sgDrpStoOutSaveResult.setOutBillNo(valueHolderV14Result.getData().getBillNo());
            sgDrpStoOutSaveResult.setSourceBillNo(request.getSourceBillNo());
            //返回的数据
            v14.setData(sgDrpStoOutSaveResult);

        } catch (Exception ex) {
            // 回滚库存
            StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, user);
            log.error("SgDrpStoOutAndOutNoticesService.saveSgDrpStoOutAndOutNotices. error:{}",
                    Throwables.getStackTraceAsString(ex));
            return new ValueHolderV14<>(com.jackrain.nea.constants.ResultCode.FAIL,
                    SgConstants.MESSAGE_STATUS_FAIL + ":" + ex.getMessage());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        return v14;
    }

    /**
     * 整合逻辑占用单数据
     *
     * @param request 接口请求数据
     * @param user    登录用户
     * @return
     */
    private ValueHolderV14<SgBStoOutBillSaveRequest> insertStoOut(SgDrpStoOutSaveRequest request, User user) {
        ValueHolderV14<SgBStoOutBillSaveRequest> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "逻辑占用单数据整合完成！");
        SgBStoOutBillSaveRequest sgBStoOutBillSaveRequest = new SgBStoOutBillSaveRequest();
        SgBStoOutSaveRequest sgBStoOutSaveRequest = new SgBStoOutSaveRequest();
        BeanUtils.copyProperties(request, sgBStoOutSaveRequest);

        List<SgBStoOutItemSaveRequest> sgBStoOutItemSaveRequests = new ArrayList<>();
        //批量设置sku信息
        CommonCacheValUtils.setSkuInfoByCode(request.getSgDrpStoInItemSaveRequests());
        for (SgDrpStoOutItemSaveRequest itemSave : request.getSgDrpStoInItemSaveRequests()) {
            SgBStoOutItemSaveRequest sgBStoOutItemSaveRequest = new SgBStoOutItemSaveRequest();
            BeanUtils.copyProperties(itemSave, sgBStoOutItemSaveRequest);
            //根据条码编码获取条码信息
            //            CommonCacheValUtils.setSkuInfo(null, itemSave.getPsCSkuEcode(), sgBStoOutItemSaveRequest);
            //根据逻辑仓信息获取
            CpCStore storeInfoByEcode = CommonCacheValUtils.getStoreInfoByEcode(itemSave.getCpCStoreEcode());
            if (!StringUtils.isEmpty(storeInfoByEcode)) {
                sgBStoOutItemSaveRequest.setCpCStoreId(storeInfoByEcode.getId());
                sgBStoOutItemSaveRequest.setCpCStoreEname(storeInfoByEcode.getEname());
            } else {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("逻辑仓编码为：" + itemSave.getCpCStoreEcode() + "对应的逻辑仓档案不存在！");
                return v14;
            }
            sgBStoOutItemSaveRequest.setQty(itemSave.getQty());
            sgBStoOutItemSaveRequest.setQtyPreout(itemSave.getQtyPreout());
            sgBStoOutItemSaveRequest.setPsCSkuEcode(itemSave.getPsCSkuEcode());
            sgBStoOutItemSaveRequest.setCpCStoreEcode(itemSave.getCpCStoreEcode());
            sgBStoOutItemSaveRequest.setSourceBillItemId(itemSave.getSourceBillItemId());

            sgBStoOutItemSaveRequests.add(sgBStoOutItemSaveRequest);
        }
        //自动出库
        sgBStoOutBillSaveRequest.setIsAutoOut(SgConstants.IS_ACTIVE_Y);
        sgBStoOutBillSaveRequest.setOaid(request.getOaid());
        sgBStoOutBillSaveRequest.setLoginUser(user);
        sgBStoOutBillSaveRequest.setSgBStoOutSaveRequest(sgBStoOutSaveRequest);
        sgBStoOutBillSaveRequest.setSgBStoOutItemSaveRequests(sgBStoOutItemSaveRequests);
        v14.setData(sgBStoOutBillSaveRequest);
        return v14;
    }

    /**
     * 参数校验和获取
     *
     * @param request
     * @return
     */
    private void checkParams(SgDrpStoOutSaveRequest request) {
        AssertUtils.notNull(request.getSourceBillType(), "来源单据类型不可为空！");
        AssertUtils.notNull(request.getSourceBillId(), "来源单id不可为空！");
        AssertUtils.notNull(request.getSourceBillNo(), "来源单编号不可为空！");
        AssertUtils.notNull(request.getSgDrpStoInItemSaveRequests(), "明细不可为空！");
        for (SgDrpStoOutItemSaveRequest itemSaveRequest : request.getSgDrpStoInItemSaveRequests()) {
            AssertUtils.notNull(itemSaveRequest.getSourceBillItemId(), "来源明细id不可为空！");
            AssertUtils.notNull(itemSaveRequest.getCpCStoreEcode(), "逻辑仓编码不可为空！");
            AssertUtils.notNull(itemSaveRequest.getPsCSkuEcode(), "条码不可为空！");
            AssertUtils.notNull(itemSaveRequest.getQty(), "原单数量不可为空！");
            AssertUtils.notNull(itemSaveRequest.getQtyPreout(), "占用数量不可为空！");
        }
    }
}



