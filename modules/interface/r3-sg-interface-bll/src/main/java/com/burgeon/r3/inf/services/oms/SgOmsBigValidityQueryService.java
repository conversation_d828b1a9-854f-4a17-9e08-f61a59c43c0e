package com.burgeon.r3.inf.services.oms;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyItemMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategySaItemMapper;
import com.burgeon.r3.sg.core.enums.SaCategoryEnum;
import com.burgeon.r3.sg.core.model.table.channel.skustrategy.SgCChannelSkuStrategySaItem;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsBigValiditySkuIdQueryRequest;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsBigValiditySkuIdQueryResult;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/29 15:49
 * 大效期查询Service
 */
@Slf4j
@Component
public class SgOmsBigValidityQueryService {

    @Autowired
    private SgCChannelSkuStrategySaItemMapper sgChannelSkuStrategySaItemMapper;
    @Autowired
    private SgCChannelRatioStrategyItemMapper sgChannelRatioStrategyItemMapper;

    public ValueHolderV14<List<SgOmsBigValiditySkuIdQueryResult>> querySaStoreBigValidityBySkuIds(SgOmsBigValiditySkuIdQueryRequest request) {
        log.info(LogUtil.format("SgOmsBigValidityQueryService.saStoreBigValidityQueryByProductId.request={}",
                "SgOmsBigValidityQueryService.saStoreBigValidityQueryByProductId", JSONObject.toJSONString(request)));
        List<Long> skuIdList = request.getPsCSkuIds();
        Long cpShopId = request.getCpCShopId();

        if (Objects.isNull(cpShopId)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "查询失败,店铺id不能为空!");
        }
        if (CollectionUtils.isEmpty(skuIdList)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "查询失败,条码id不能为空!");
        }
        List<SgOmsBigValiditySkuIdQueryResult> queryResultList = new ArrayList<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String nowDate = format.format(Calendar.getInstance().getTime());
        Map<Long, List<SgCChannelSkuStrategySaItem>> skuIdMap = new HashMap<>(16);
        //特殊条码策略存在配销仓明细的
        List<SgCChannelSkuStrategySaItem> strategySaItemList =
                sgChannelSkuStrategySaItemMapper.querySkuIdWithSaStoreCategory(cpShopId, nowDate, skuIdList);
        if (CollectionUtils.isNotEmpty(strategySaItemList)) {
            skuIdMap =
                    strategySaItemList.stream().collect(Collectors.groupingBy(SgCChannelSkuStrategySaItem::getPsCSkuId));
        }
        boolean isRatioBigValidity = false;
        if (skuIdList.size() != skuIdMap.keySet().size()) {
            //比例策略存在配销仓明细的
            String category = sgChannelRatioStrategyItemMapper.querySaStoreCategoryByShop(cpShopId);
            if (SaCategoryEnum.BIG_VALIDITY.getCode().equals(category)) {
                isRatioBigValidity = true;
            }
        }
        for (Long skuId : skuIdList) {
            SgOmsBigValiditySkuIdQueryResult queryResult = new SgOmsBigValiditySkuIdQueryResult();
            queryResult.setPsCSkuId(skuId);
            List<SgCChannelSkuStrategySaItem> skuStrategySaItemList = skuIdMap.get(skuId);
            if (CollectionUtils.isNotEmpty(skuStrategySaItemList)) {
                long count = skuStrategySaItemList.stream().filter(i -> i.getSaStoreType()
                        .equals(SaCategoryEnum.BIG_VALIDITY.getCode())).count();
                if (count > 0) {
                    queryResult.setBigValidity(true);
                } else {
                    queryResult.setBigValidity(false);
                }
            }else{
                queryResult.setBigValidity(isRatioBigValidity);
            }
            queryResultList.add(queryResult);
        }
        return new ValueHolderV14<>(queryResultList,ResultCode.SUCCESS, "订单平台条码明细大效期查询成功!");
    }
}
