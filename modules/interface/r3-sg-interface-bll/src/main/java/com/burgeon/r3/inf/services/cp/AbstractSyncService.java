package com.burgeon.r3.inf.services.cp;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.cpext.api.sg.CpSgCSyncCmd;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/10 10:55
 */
@Slf4j
public abstract class AbstractSyncService<T extends BaseModel> {

    private String tableName;

    public AbstractSyncService(String tableName) {
        this.tableName = tableName;
    }

    /**
     * 抽象实施
     *
     * @param pageIndex      页码
     * @param pageSize       分页大小
     * @param intervalMinute 间隔时间
     * @param isAllSync      是否全量
     * @return return
     */
    abstract List<T> execute(int pageIndex,
                             int pageSize,
                             int intervalMinute,
                             boolean isAllSync);

    public void doExecute(Integer pageS, Integer intervalM, Boolean isAllS) {
        int pageSize = pageS;
        int intervalMinute = intervalM;
        boolean isAllSync = isAllS;

        int pageIndex = 0;
        int totalCount = 0;
        while (true) {
            ++pageIndex;
            List<T> result = execute(pageIndex, pageSize, intervalMinute, isAllSync);
            if (CollectionUtils.isNotEmpty(result)) {
                if (log.isDebugEnabled()) {
                    log.debug("AbstractSyncService.doPageList:pageIndex={},pageSize={},item={}",
                            pageIndex, pageSize, JSONObject.toJSONString(result));
                }
                totalCount += result.size();
                doCpRpcRequest(result);
            }
            if (CollectionUtils.isEmpty(result) || result.size() < pageSize) {
                if (log.isDebugEnabled()) {
                    log.debug("AbstractSyncService.doExecute. tableName={},total={}", this.tableName, totalCount);
                }
                //最后一页小于分页
                return;
            }
        }
    }

    private void doCpRpcRequest(List<T> listRequest) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("table_name", this.tableName);
        jsonObject.put("table_list", JSONObject.toJSONString(listRequest));

        if (log.isDebugEnabled()) {
            log.debug("AbstractSyncService.doCpRpcRequest. request={}", jsonObject.toJSONString());
        }

        CpSgCSyncCmd refer = (CpSgCSyncCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                CpSgCSyncCmd.class.getName(), "cp-ext", "1.0");
        ValueHolderV14 v14 = refer.syncSgCTable(jsonObject.toJSONString());
        if (ResultCode.FAIL == v14.getCode()) {
            log.error("AbstractSyncService.doCpRpcRequest: tableName={}, error={}", this.tableName, v14.getMessage());
        }
    }
}
