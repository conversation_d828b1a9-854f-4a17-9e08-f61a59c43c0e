package com.burgeon.r3.inf.services.wms.in;


import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.r3.inf.model.SgWmsStockQueryRequest;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.basic.mapper.SgBWmsStockResultMapper;
import com.burgeon.r3.sg.basic.model.request.SgStorageQueryRequest;
import com.burgeon.r3.sg.basic.rpc.RpcPsService;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorage;
import com.burgeon.r3.sg.core.model.table.basic.SgBWmsStockResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustMainSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustSaveRequest;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSaveService;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.assertj.core.util.DateUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
@Slf4j
public class SgBWmsStockResultService extends ServiceImpl<SgBWmsStockResultMapper, SgBWmsStockResult> {

    private final CpCPhyWarehouseMapper cpCPhyWarehouseMapper;

    private final SgBWmsStockResultMapper sgBWmsStockResultMapper;

    private final CpCStoreMapper cpCStoreMapper;

    private final SgStorageQueryService storageQueryService;

    private final RpcPsService rpcPsService;

    private final SgBStoAdjustSaveService sgBStoAdjustSaveService;

    @NacosValue(value = "${sg.wms_to_stock_result_max_query_limit:200}",autoRefreshed = true)
    private Integer queryCnt;

    /**
     * 报文解析
     *
     * @param msg 报文
     * @return 响应体
     */
    public ValueHolderV14<String> parseSaveMsg(String msg) {
        try {
            SgWmsStockQueryRequest request = JSON.parseObject(msg, SgWmsStockQueryRequest.class);

            if (!request.checkIsSuccess()) {
                return new ValueHolderV14<>(ResultCode.FAIL, "数据校验异常");
            }
            List<SgBWmsStockResult> sgBWmsStockResultList = Lists.newArrayList();
            SgWmsStockQueryRequest.Items items = request.getResponse().getItems();
            List<SgWmsStockQueryRequest.Item> itemList = items.getItem();

            // 过滤重复数据
            List<SgWmsStockQueryRequest.Item> filterItemList = filterDistinct(itemList);

            Map<String, List<SgWmsStockQueryRequest.Item>> resultWarehouseMap = filterItemList.stream().collect(Collectors.groupingBy(SgWmsStockQueryRequest.Item::getWarehouseCode, Collectors.toList()));

            if (MapUtils.isEmpty(resultWarehouseMap)) {
                return new ValueHolderV14<>(ResultCode.SUCCESS, "无需新增数据");
            }

            Map<String, SgCpCPhyWarehouse> warehouseMapMap = Maps.newHashMap();
            Map<String, List<SgCpCStore>> storeInfoMap = Maps.newHashMap();
            getWarehouseStoreList(resultWarehouseMap, warehouseMapMap, storeInfoMap);

            buildInsertResultList(resultWarehouseMap, warehouseMapMap, storeInfoMap, sgBWmsStockResultList, msg);

            sgBWmsStockResultMapper.batchInsert(sgBWmsStockResultList);
        } catch (Exception e) {
            log.error(LogUtil.format("库存查询回传异常"), Throwables.getStackTraceAsString(e));
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "success");
    }

    /**
     * 构建插入数据
     *
     * @param resultWarehouseMap    请求体分组map
     * @param warehouseMapMap       实体仓map
     * @param storeInfoMap          逻辑仓map
     * @param sgBWmsStockResultList 新增结果集合
     * @param msg                   原始报文
     */
    private void buildInsertResultList(Map<String, List<SgWmsStockQueryRequest.Item>> resultWarehouseMap,
                                       Map<String, SgCpCPhyWarehouse> warehouseMapMap,
                                       Map<String, List<SgCpCStore>> storeInfoMap,
                                       List<SgBWmsStockResult> sgBWmsStockResultList,
                                       String msg) {
        for (Map.Entry<String, List<SgWmsStockQueryRequest.Item>> entry : resultWarehouseMap.entrySet()) {
            String warehouseCode = entry.getKey();
            List<SgWmsStockQueryRequest.Item> value = entry.getValue();
            SgCpCPhyWarehouse sgCpCPhyWarehouse = warehouseMapMap.get(warehouseCode);

            if (Objects.isNull(sgCpCPhyWarehouse)) {
                value.stream().map(item -> convertMessage(item, new HashMap<>(), msg, "无对应实体仓",
                        5, SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED)).forEach(sgBWmsStockResultList::add);
                continue;
            }

            List<SgCpCStore> storeList = storeInfoMap.get(sgCpCPhyWarehouse.getEcode());
            if (CollectionUtils.isEmpty(storeList)) {
                value.stream().map(item -> convertMessage(item, new HashMap<>(), msg, "无对应逻辑仓",
                        5, SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED)).forEach(sgBWmsStockResultList::add);
                continue;
            }

            List<Long> storeIds = storeList.stream().map(SgCpCStore::getId).collect(Collectors.toList());
            List<String> skuEcodes = value.stream().map(SgWmsStockQueryRequest.Item::getItemCode).collect(Collectors.toList());

            SgStorageQueryRequest queryRequest = new SgStorageQueryRequest();
            queryRequest.setStoreIds(storeIds);
            queryRequest.setSkuEcodes(skuEcodes);

            // 库存查询
            ValueHolderV14<List<SgBStorage>> v14 = storageQueryService.queryStorage(queryRequest, SystemUserResource.getRootUser());

            if (v14.isOK()) {
                Map<String, List<SgBStorage>> storageMap = v14.getData().stream().collect(Collectors.groupingBy(SgBStorage::getPsCSkuEcode, Collectors.toList()));
                value.stream().map(item -> convertMessage(item, storageMap, msg, null,
                        0, SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT)).forEach(sgBWmsStockResultList::add);
            }
        }
    }

    /**
     * 过滤请求明细
     *
     * @param itemList 请求明细
     * @return 过滤后的结果集
     */
    public List<SgWmsStockQueryRequest.Item> filterDistinct(List<SgWmsStockQueryRequest.Item> itemList) {
        // 查询存在的历史记录
        String queryDate = DateFormatUtils.format(DateUtil.now(), "yyyy-MM-dd");
        List<SgBWmsStockResult> wmsStockResultList = sgBWmsStockResultMapper.selectHistoryOrder(queryDate);
        if (CollectionUtils.isNotEmpty(wmsStockResultList)) {
            Map<String, SgBWmsStockResult> sgBWmsStockResultMap =
                    wmsStockResultList.stream().collect(Collectors.toMap(k -> k.getWarehouseCode() + "_" + k.getItemCode(), Function.identity()));

            return itemList.stream().filter(item -> !sgBWmsStockResultMap.containsKey(item.getWarehouseCode() + "_" + item.getItemCode())).collect(Collectors.toList());
        }
        return itemList;
    }

    /**
     * @param resultWarehouseMap 实体仓编码分组map
     * @param warehouseMapMap    实体仓map
     * @param storeInfoMap       逻辑仓map
     */
    private void getWarehouseStoreList(Map<String, List<SgWmsStockQueryRequest.Item>> resultWarehouseMap,
                                       Map<String, SgCpCPhyWarehouse> warehouseMapMap,
                                       Map<String, List<SgCpCStore>> storeInfoMap) {
        // 实体仓编码集合
        Set<String> warehouseCodeList = resultWarehouseMap.keySet();
        // 批量查询实体仓
        List<SgCpCPhyWarehouse> cpCPhyWarehouseList = cpCPhyWarehouseMapper.selectList(new LambdaQueryWrapper<SgCpCPhyWarehouse>()
                .in(SgCpCPhyWarehouse::getWmsWarehouseCode, warehouseCodeList)
                .eq(SgCpCPhyWarehouse::getIsactive, SgConstants.IS_ACTIVE_Y));

        // k->wms单据编号, value->实体仓
        warehouseMapMap.putAll(cpCPhyWarehouseList.stream().collect(Collectors.toMap(SgCpCPhyWarehouse::getWmsWarehouseCode, Function.identity())));

        List<Long> warehouseIdList = cpCPhyWarehouseList.stream().map(SgCpCPhyWarehouse::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(warehouseIdList)) {
            return;
        }
        // 批量查询逻辑仓
        List<SgCpCStore> cpCStoreList = cpCStoreMapper.selectList(new LambdaQueryWrapper<SgCpCStore>()
                .in(SgCpCStore::getCpCPhyWarehouseId, warehouseIdList)
                .eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y));

        // k->wms仓库编号, value->逻辑仓集合
        storeInfoMap.putAll(cpCStoreList.stream().collect(Collectors.groupingBy(SgCpCStore::getCpCPhyWarehouseEcode, Collectors.toList())));
    }

    /**
     * 报文转换
     *
     * @param item           报文明细
     * @param map            逻辑仓库存
     * @param msg            逻辑仓库存
     * @param failedReason   失败原因
     * @param failedCount    失败次数
     * @param transferStatus 转换状态
     * @return 实体
     */
    private SgBWmsStockResult convertMessage(SgWmsStockQueryRequest.Item item,
                                             Map<String, List<SgBStorage>> map,
                                             String msg, String failedReason,
                                             Integer failedCount,
                                             Integer transferStatus) {
        List<SgBStorage> sgBStorages = map.get(item.getItemCode());
        BigDecimal qtyStorage = CollectionUtils.isEmpty(sgBStorages) ? new BigDecimal("0") :
                sgBStorages.stream().map(SgBStorage::getQtyStorage).reduce(BigDecimal.ZERO, BigDecimal::add);

        SgBWmsStockResult saveResult = new SgBWmsStockResult();
        saveResult.setId(ModelUtil.getSequence(SgConstants.SG_B_WMS_STOCK_RESULT));
        saveResult.setItemCode(item.getItemCode());
        saveResult.setWarehouseCode(item.getWarehouseCode());
        saveResult.setQueryTime(DateUtil.now());
        saveResult.setQueryDate(DateUtil.now());
        saveResult.setWmsQtyStorage(item.getQuantity());
        saveResult.setZtQtyStorage(qtyStorage);
        saveResult.setMessage(msg);
        saveResult.setDiffQtyStorage(saveResult.getWmsQtyStorage().subtract(saveResult.getZtQtyStorage()));
        saveResult.setFailedCount(failedCount);
        saveResult.setFailedReason(failedReason);
        saveResult.setTransferStatus(transferStatus);
        StorageUtils.setBModelDefalutData(saveResult, SystemUserResource.getRootUser());
        return saveResult;
    }

    /**
     * 生成调整单
     *
     * @return 结果集
     */
    public ValueHolderV14<String> createAdjust() {
        List<Integer> status = Lists.newArrayList(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT, SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED);
        List<SgBWmsStockResult> sgBWmsToStoOutResults = sgBWmsStockResultMapper.selectTransferTaskList(StringUtils.join(status, ","), queryCnt);
        if (CollectionUtils.isEmpty(sgBWmsToStoOutResults)) {
            return new ValueHolderV14<>(ResultCode.SUCCESS, "无需要转换数据");
        }

        Map<String, List<SgBWmsStockResult>> storeInfoMap = sgBWmsToStoOutResults.stream().collect(Collectors.groupingBy(SgBWmsStockResult::getWarehouseCode, Collectors.toList()));

        List<SgBWmsStockResult> updateStockList = Lists.newArrayList();

        for (Map.Entry<String, List<SgBWmsStockResult>> entry : storeInfoMap.entrySet()) {
            String warehouseCode = entry.getKey();
            List<SgBWmsStockResult> stockResultList = entry.getValue();
            try {
                if (CollectionUtils.isEmpty(stockResultList)) {
                    continue;
                }
                SgBStoAdjustSaveRequest sgBStoAdjustSaveRequest = parseMessage2SgAdjust(warehouseCode, stockResultList, updateStockList);
                if (Objects.isNull(sgBStoAdjustSaveRequest)) {
                    continue;
                }
                ValueHolderV14<SgR3BaseResult> resultV14 = sgBStoAdjustSaveService.saveAndSubmit(sgBStoAdjustSaveRequest);
                if (resultV14.isOK()) {
                    SgR3BaseResult result = resultV14.getData();
                    updateStockList.addAll(buildUpdateRecord(stockResultList, StringUtils.EMPTY, SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS, result.getBillNo()));
                } else {
                    updateStockList.addAll(buildUpdateRecord(stockResultList, resultV14.getMessage(), SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED, null));
                }

            } catch (Exception e) {
                updateStockList.addAll(buildUpdateRecord(stockResultList, e.getMessage(), SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED, null));
            }
        }

        if (CollectionUtils.isNotEmpty(updateStockList)) {
            SgBWmsStockResultService service = ApplicationContextHandle.getBean(SgBWmsStockResultService.class);
            service.updateBatchById(updateStockList);
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "处理成功");
    }

    /**
     * WMS库存回传数据转化为库存调整单
     *
     * @param warehouseCode      wms单据编号
     * @param wmsStockResultList 中间表集合
     * @return 调整单
     */
    private SgBStoAdjustSaveRequest parseMessage2SgAdjust(String warehouseCode,
                                                          List<SgBWmsStockResult> wmsStockResultList,
                                                          List<SgBWmsStockResult> updateStockList) {
        User user = SystemUserResource.getRootUser();
        SgBStoAdjustSaveRequest adjustRequest = new SgBStoAdjustSaveRequest();
        List<SgBStoAdjustItemSaveRequest> items = Lists.newArrayList();

        List<String> itemCodeList = wmsStockResultList.stream().map(SgBWmsStockResult::getItemCode).collect(Collectors.toList());

        ValueHolderV14<List<PsCSku>> valueHolderV14 = rpcPsService.querySKUByEcodeList(itemCodeList);
        List<PsCSku> skuList = valueHolderV14.getData();

        AssertUtils.cannot(CollectionUtils.isEmpty(skuList), "SKU在系统不存在");

        Map<String, PsCSku> psCSkuMap = skuList.stream().collect(Collectors.toMap(PsCSku::getEcode, Function.identity()));

        // 批量查询实体仓
        List<SgCpCPhyWarehouse> cpCPhyWarehouseList = cpCPhyWarehouseMapper.selectList(new LambdaQueryWrapper<SgCpCPhyWarehouse>()
                .eq(SgCpCPhyWarehouse::getWmsWarehouseCode, warehouseCode)
                .eq(SgCpCPhyWarehouse::getIsactive, SgConstants.IS_ACTIVE_Y));

        List<Long> warehouseIdList = cpCPhyWarehouseList.stream().map(SgCpCPhyWarehouse::getId).collect(Collectors.toList());

        // 批量查询逻辑仓
        List<SgCpCStore> cpCStoreList = cpCStoreMapper.selectList(new LambdaQueryWrapper<SgCpCStore>()
                .in(SgCpCStore::getCpCPhyWarehouseId, warehouseIdList)
                .eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y)
                .orderByDesc(SgCpCStore::getId));

        // 取逻辑仓是主仓的，如果所有逻辑仓都没有主仓，取第id最大的
        List<SgCpCStore> mainStoreList = cpCStoreList.stream().filter(s -> (s.getIsMainWarehouse() != null && s.getIsMainWarehouse() == 1)).collect(Collectors.toList());
        SgCpCStore store = mainStoreList.size() == 0 ? cpCStoreList.get(0) : mainStoreList.get(0);

        // 主表
        SgBStoAdjustMainSaveRequest saveRequest = new SgBStoAdjustMainSaveRequest();
        saveRequest.setObjId(-1L);
        saveRequest.setLoginUser(user);
        saveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_PHY_PROFIT);
        saveRequest.setSourceBillId(System.currentTimeMillis());
        saveRequest.setSourceBillNo(String.valueOf(System.currentTimeMillis()));
        saveRequest.setBillDate(new Date());
        saveRequest.setCpCStoreId(store.getId());
        saveRequest.setCpCStoreEcode(store.getCpCStoreEcode());
        saveRequest.setCpCStoreEname(store.getCpCStoreEname());
        saveRequest.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL);
        saveRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_ADJUST_WMS_STOCK_ADJUSTMENT);
        saveRequest.setRemark("由WMS（全量）库存接口生成");
        saveRequest.setDrpStatus(Integer.valueOf(SgStoreConstants.SEND_DRP_STATUS_UNDECLARED));

        // 子表
        for (SgBWmsStockResult stockResult : wmsStockResultList) {
            if (Objects.isNull(stockResult.getDiffQtyStorage())
                    || stockResult.getDiffQtyStorage().compareTo(BigDecimal.ZERO) == 0) {
                updateStockList.addAll(buildUpdateRecord(Collections.singletonList(stockResult),
                        "差异量为0无需转换", SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED, null));
                continue;
            }

            if (Objects.isNull(psCSkuMap.get(stockResult.getItemCode()))) {
                updateStockList.addAll(buildUpdateRecord(Collections.singletonList(stockResult),
                        "SKU在系统不存在", SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED, null));
                continue;
            }
            SgBStoAdjustItemSaveRequest itemSaveRequest = new SgBStoAdjustItemSaveRequest();
            itemSaveRequest.setId(-1L);
            itemSaveRequest.setPsCSkuId(psCSkuMap.get(stockResult.getItemCode()).getId());
            itemSaveRequest.setCpCStoreId(store.getId());
            itemSaveRequest.setCpCStoreEcode(store.getCpCStoreEcode());
            itemSaveRequest.setCpCStoreEname(store.getCpCStoreEname());
            itemSaveRequest.setQty(stockResult.getDiffQtyStorage());
            itemSaveRequest.setSourceBillItemId(System.currentTimeMillis());
            items.add(itemSaveRequest);
        }
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }
        adjustRequest.setMainRequest(saveRequest);
        adjustRequest.setItems(items);
        adjustRequest.setLoginUser(user);
        adjustRequest.setObjId(-1L);
        return adjustRequest;
    }

    /**
     * 构造待更新的中间表记录
     *
     * @param stockResultList 中间表数据集合
     * @param error           失败原因
     * @param status          成功失败状态
     * @param adjustBillNo    调整单单号
     */
    private List<SgBWmsStockResult> buildUpdateRecord(List<SgBWmsStockResult> stockResultList,
                                                      String error, Integer status, String adjustBillNo) {
        List<SgBWmsStockResult> updateResultList = Lists.newArrayList();
        for (SgBWmsStockResult stockResult : stockResultList) {
            SgBWmsStockResult updateResult = new SgBWmsStockResult();
            updateResult.setId(stockResult.getId());
            updateResult.setFailedReason(StorageUtils.strSubString(error, SgConstants.SG_COMMON_STRING_SIZE - 1));
            if (SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED == status) {
                updateResult.setFailedCount(Optional.ofNullable(stockResult.getFailedCount()).orElse(0) + 1);
            } else if (SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS == status) {
                updateResult.setFailedCount(0);
            }
            updateResult.setTransferStatus(status);
            updateResult.setSgBStoAdjustBillNo(adjustBillNo);
            StorageUtils.setBModelDefalutDataByUpdate(updateResult, SystemUserResource.getRootUser());
            updateResultList.add(updateResult);
        }
        return updateResultList;
    }
}