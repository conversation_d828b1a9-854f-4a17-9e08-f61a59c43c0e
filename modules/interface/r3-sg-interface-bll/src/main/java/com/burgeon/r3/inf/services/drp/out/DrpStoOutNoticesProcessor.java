package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.error.MqException;
import com.burgeon.r3.inf.services.drp.in.DrpUtils;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.config.SgStorageMqConfig;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.LabelRequestStatusEnum;
import com.burgeon.r3.sg.core.enums.SgMonitorMqEnum;
import com.burgeon.r3.sg.core.model.request.SgBMonitorMqErrorDatasRequest;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNoticesItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.DateUtils;
import com.burgeon.r3.sg.core.utils.SgMonitorUtils;
import com.burgeon.r3.sg.inf.common.SgInfConstants;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.api.GetDetailCmd;
import com.jackrain.nea.oc.oms.api.OcBorderDetailCmd;
import com.jackrain.nea.oc.oms.model.result.GetOrderResult;
import com.jackrain.nea.oc.oms.model.table.OcBorderItemExtention;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 出库通知单推送接口
 * @version 1.0
 * @date 2021/8/26
 */
@Slf4j
@Component
public class DrpStoOutNoticesProcessor extends AbstractDrpInterfaceProcessor<SgBStoOutNotices, SgBStoOutNoticesItem> {

    @Value("${drp.interface.failNum:3}")
    public int failNum;

    @Reference(group = "oms-fi", version = "1.0")
    private GetDetailCmd getDetailCmd;

    @Reference(group = "oms-fi", version = "1.0")
    private OcBorderDetailCmd borderDetailCmd;

    //    @Autowired
//    private R3MqSendHelper r3MqSendHelper;
    @Autowired
    private DefaultProducerSend defaultProducerSend;
    @Autowired
    private SgStorageMqConfig mqConfig;
    @Autowired
    private SgMonitorUtils sgMonitorUtils;

    @Override
    public LambdaQueryWrapper<SgBStoOutNotices> execMainWrapper() {
        LambdaQueryWrapper<SgBStoOutNotices> wrapper = new LambdaQueryWrapper<>();
        // 传第三方类型为ERP
        wrapper.eq(SgBStoOutNotices::getThirdPartyType, SgConstantsIF.THIRD_PARTY_TYPE_ERP);
        wrapper.eq(SgBStoOutNotices::getSourceBillType, SgConstantsIF.BILL_TYPE_RETAIL);

        // 传第三方状态为未传，或者传失败
        wrapper.and(x -> x.eq(SgBStoOutNotices::getThirdPartyStatus, SgStoreConstants.THIRD_PARTY_STATUS_WAIT_PASS).or().eq(SgBStoOutNotices::getThirdPartyStatus, SgStoreConstants.THIRD_PARTY_STATUS_PASS_FAILED));

        // 获取电子面单为不需要获取，或者获取成功
        wrapper.and(x -> x.eq(SgBStoOutNotices::getIsEnableEwaybill, SgStoreConstants.ENABLE_EWAY_BILL_N).or().eq(SgBStoOutNotices::getEwaybillStatus, LabelRequestStatusEnum.SUCCESS.getCode()));

        // 失败次数小于6
        wrapper.and(x -> x.isNull(SgBStoOutNotices::getThirdPartyFailCount).or().lt(SgBStoOutNotices::getThirdPartyFailCount, failNum));

        wrapper.eq(SgBStoOutNotices::getBillStatus, SgConstantsIF.SG_STO_OUT_BILL_STATUS_WAIT_OUT);
        wrapper.eq(SgBStoOutNotices::getIsactive, SgConstants.IS_ACTIVE_Y);

        return wrapper;
    }

    @Override
    public LambdaQueryWrapper<SgBStoOutNoticesItem> execitemWrapper(Long mainId) {
        return null;
    }

    @Override
    public String drpInterfaceUrl() {
        return "erp.o2oso.notice";
    }

    @Override
    public String getClassName() {
        return this.getClass().getName();
    }

    @Override
    public String itemMainField() {
        return "sg_b_sto_out_notices_id";
    }

    @Override
    public String drpStatus() {
        return "THIRD_PARTY_STATUS";
    }

    @Override
    public String drpStatusFailCount() {
        return "THIRD_PARTY_FAIL_COUNT";
    }

    @Override
    public String drpStatusFailReason() {
        return "THIRD_PARTY_FAIL_REASON";
    }

    @Override
    public int drpSuccessStatus() {
        return (int) SgStoreConstants.THIRD_PARTY_STATUS_PASS_SUCCESS;
    }

    @Override
    public int drpFailStatus() {
        return (int) SgStoreConstants.THIRD_PARTY_STATUS_PASS_FAILED;
    }

    @Override
    public JSONObject execInterfaceParam(SgBStoOutNotices outNotices, List<SgBStoOutNoticesItem> itemList) {
        // 查询零售发货单
        GetOrderResult ocOrderResult = getOcOrder(outNotices);
        List<OcBorderItemExtention> ocOrderItemList = getOcOrderItem(outNotices, ocOrderResult);

        JSONObject request = new JSONObject();

        String billDate = "";
        if (outNotices.getBillDate() != null) {
            billDate = DateUtils.formatSync8(outNotices.getBillDate(), DateUtils.DATE_PATTERN);
        }

        CpShop shopInfo = CommonCacheValUtils.getShopInfo(ocOrderResult.getCpCShopId());

        AssertUtils.notNull(shopInfo, "未获取到平台店铺");

//        String cpCStoreEcode = ocOrderResult.getCpCStoreEcode();
//        if (StringUtils.isEmpty(cpCStoreEcode)) {
//            cpCStoreEcode = shopInfo.getCpCStoreEcode();
//        }

        request.put("ZTDOCNO", outNotices.getBillNo());
        request.put("BILLDATE", billDate);
//        request.put("C_STORE_ID_CODE", cpCStoreEcode);
        request.put("C_ORIG_ID_CODE", itemList.get(0).getCpCStoreEcode());
        request.put("INTERFACE_TYPE", SgStoreConstants.IS_ONLINE_NO);
        request.put("RECEIVER_NAME", ocOrderResult.getReceiverName());
        request.put("C_PROVINCE_ID_NAME", ocOrderResult.getCpCRegionProvinceEname());
        request.put("C_CITY_ID_NAME", ocOrderResult.getCpCRegionCityEname());
        request.put("C_DISTRICT_ID_NAME", ocOrderResult.getCpCRegionAreaEname());
        request.put("RECEIVER_MOBILE", ocOrderResult.getReceiverMobile());
        request.put("RECEIVER_PHONE", ocOrderResult.getReceiverPhone());
        request.put("RECEIVER_ADDRESS", ocOrderResult.getReceiverAddress());
        request.put("EB_LOGIS_ID__COMNAME", outNotices.getCpCLogisticsEcode());
        request.put("FASTNO", outNotices.getLogisticNumber());
        request.put("O2O_RETAIL_CHANNEL_ID", outNotices.getCpCPlatformId());
        request.put("OAID", outNotices.getOaid());
        request.put("TID", outNotices.getSourcecode());
        request.put("SOURCE_BILL_NO", outNotices.getSourceBillNo());
        request.put("SELLER_NICK", shopInfo.getSellerNick());

        // 20220217 新增字段
        request.put("jitx_voucher_content", outNotices.getJitxVoucherContent());
        request.put("print_data", outNotices.getJitxVoucherContent());

        // 20220321 增加字段
        request.put("IS_RETAIL", Optional.ofNullable(shopInfo.getRegionalStore()).orElse(SgConstants.IS_YES_OR_NO_N));
        request.put("SHIP_AMT", ocOrderResult.getShipAmt());
        request.put("BUYER_MESSAGE", ocOrderResult.getBuyerMessage());

        ocOrderItemList = ocOrderItemList.stream().filter(x ->
                (Objects.isNull(x.getRefundStatus()) || x.getRefundStatus() != 6)
                        && (Objects.isNull(x.getProType()) || x.getProType() != 4)).collect(Collectors.toList());

        List<JSONObject> requestItemList = new ArrayList<>();
        ocOrderItemList.forEach(x -> {
            JSONObject requestItem = new JSONObject();
            requestItem.put("M_PRODUCTALIAS_ID_NO", x.getPsCSkuEcode());
            requestItem.put("QTY", x.getQty());

            requestItem.put("PRICELIST", x.getPriceTag());
            AssertUtils.notNull(x.getRealAmt(), "总金额不能为空");
            AssertUtils.notNull(x.getQty(), "数量不能为空");
            AssertUtils.cannot(BigDecimal.ZERO.compareTo(x.getQty()) == 0, "数量不能为0");
            requestItem.put("PRICE", x.getRealAmt().divide(x.getQty(), 4, BigDecimal.ROUND_HALF_UP));
            requestItem.put("TOT_AMT_LIST", x.getQty().multiply(x.getPriceTag()));
            requestItem.put("TOT_AMT_ACTUAL", x.getRealAmt());

            requestItemList.add(requestItem);
        });

        request.put("items", requestItemList);
        return request;
    }

    private List<OcBorderItemExtention> getOcOrderItem(SgBStoOutNotices outNotices, GetOrderResult ocOrderResult) {
        Map<String, Long> ocRequest = new HashMap<>();
        ocRequest.put("ID", outNotices.getSourceBillId());
        ocRequest.put("id", outNotices.getSourceBillId());
        ocRequest.put("pageSize", 10000L);

        if (log.isDebugEnabled()) {
            log.debug("start.WingStoOutNoticesService.createOrderByStoOutNotice.getDetailCmd.ocItemRequest={}",
                    JSONObject.toJSONString(ocRequest));
        }

        ValueHolderV14 detailList = borderDetailCmd.getOrderDetailList(JSONObject.toJSONString(ocRequest), DrpUtils.getUser());

        if (log.isDebugEnabled()) {
            log.debug("start.WingStoOutNoticesService.createOrderByStoOutNotice.borderDetailCmd.ocOrderResult={}",
                    JSONObject.toJSONString(detailList));
        }

        AssertUtils.isTrue(detailList.isOK(), "查询零售发货单明细失败");

        Object data = detailList.getData();
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(data));
        Object records = jsonObject.get("records");
        List<OcBorderItemExtention> ocOrderItemList = JSONArray.parseArray(JSONObject.toJSONString(records), OcBorderItemExtention.class);

        AssertUtils.cannot(CollectionUtils.isEmpty(ocOrderItemList), "查询零售发货单未查询到明细数据！");
        return ocOrderItemList;
    }

    private GetOrderResult getOcOrder(SgBStoOutNotices stoOutNotices) {
        // 调用oc 接口，获取零售发货单 及 明细
        Map<String, Long> ocRequest = new HashMap<>();
        ocRequest.put("ID", stoOutNotices.getSourceBillId());
        ocRequest.put("id", stoOutNotices.getSourceBillId());
        ValueHolder detail = getDetailCmd.getDetail(JSONObject.parseObject(JSON.toJSONString(ocRequest)), DrpUtils.getUser());

        if (log.isDebugEnabled()) {
            log.debug("start.WingStoOutNoticesService.createOrderByStoOutNotice.getDetailCmd.result={}",
                    JSONObject.toJSONString(detail));
        }

        AssertUtils.isTrue(detail.isOK(), "查询零售发货单失败");

        GetOrderResult ocOrderResult = JSON.parseObject(JSON.toJSONString(detail.getData().get("data")), GetOrderResult.class);

        if (log.isDebugEnabled()) {
            log.debug("start.WingStoOutNoticesService.createOrderByStoOutNotice.getDetailCmd.ocOrderResult={}",
                    JSONObject.toJSONString(ocOrderResult));
        }

        AssertUtils.notNull(ocOrderResult, "查询零售发货单未查询到主表数据！");

        return ocOrderResult;
    }

    @Override
    public void handleBysuccess(SgBStoOutNotices stoOutNotices, List<SgBStoOutNoticesItem> items) {

        List<JSONObject> retailBodys = Lists.newArrayList();

        JSONObject body = new JSONObject();
        body.put("id", stoOutNotices.getId());
        body.put("orderId", stoOutNotices.getSourceBillId());
        body.put("orderNo", stoOutNotices.getSourceBillNo());
        body.put("noticesBillNo", stoOutNotices.getBillNo());

        // 回执结果
        body.put("code", SgInfConstants.WMS_RECEIPT_STATUS_SUCCESS);
        body.put("orderType", stoOutNotices.getSourceBillType());

        retailBodys.add(body);

        JSONObject ret = new JSONObject();
        ret.put("type", SgConstants.SECOND_TO_PASS_TYPE);
        ret.put("body", retailBodys);

        try {
//            r3MqSendHelper.sendMessage(mqConfig.getSgDefaultConfigName(),
//                    ret.toJSONString(),
//                    mqConfig.getNoticesToOmsTopic(),
//                    SgConstantsIF.MSG_TAG_OUTNOTICES_TO_OMS,
//                    UUID.randomUUID().toString().replaceAll("-", ""));
            defaultProducerSend.sendTopic(mqConfig.getNoticesToOmsTopic(),
                    SgConstantsIF.MSG_TAG_OUTNOTICES_TO_OMS,
                    ret.toJSONString(),
                    UUID.randomUUID().toString().replaceAll("-", ""));
        } catch (MqException e) {
            SgBMonitorMqErrorDatasRequest error = new SgBMonitorMqErrorDatasRequest();
            error.setProducer(SgMonitorMqEnum.SG);
            error.setConsumer(SgMonitorMqEnum.OMS);
            error.setTopic(mqConfig.getNoticesToOmsTopic());
            sgMonitorUtils.saveMqErrorDatas(error, e, R3SystemUserResource.getSystemRootUser());

            log.error("DrpStoOutNoticesProcessor.sendMsgToSourceBillNo. exception_has_occured:{}", Throwables.getStackTraceAsString(e));
        }

    }
}
