package com.burgeon.r3.inf.services.wms.in;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.store.in.SgBWmsToInventoryChangeNoticeTask;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.in.SgBWmsToInventoryChangeNoticeTaskMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Description: 库存异动通知中间表
 *
 * @Author: guo.kw
 * @Since: 2022/7/28
 * create at: 2022/7/28 15:11
 */
@Slf4j
@Component
public class WmsBackStockchangeReportRequest {

    @Autowired
    private SgBWmsToInventoryChangeNoticeTaskMapper sgBWmsToInventoryChangeNoticeTaskMapper;

    @Autowired
    private CpCPhyWarehouseMapper cpCPhyWarehouseMapper;

    private static final List<String> STOCK_TYPE = Lists.newArrayList(SgConstantsIF.STOCK_TYPE_GOODS,
            SgConstantsIF.STOCK_TYPE_IMPERFECT, SgConstantsIF.STOCK_TYPE_CALLBACK, SgConstantsIF.STOCK_TYPE_QUALITY,
            SgConstantsIF.STOCK_TYPE_LOSE, SgConstantsIF.STOCK_TYPE_DISABLED);

    public ValueHolderV14<String> apiProcess(String msg) {
        log.info(LogUtil.format("WmsBackStockchangeReportRequest apiProcess msg={}",
                "WmsBackStockchangeReportRequest apiProcess"), msg);
        CusRedisTemplate<Object, Object> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();
        String lockKsy = SgConstants.SG_BILL_LOCK_WMSRETURN;
        try {

            JSONObject request = JSONObject.parseObject(msg);

            JSONArray items = request.getJSONArray("items");
            JSONObject jsonObject = items.getJSONObject(0);
            //外部业务编码
            String outBizCode = jsonObject.getString("outBizCode");
            lockKsy += outBizCode;
            Boolean ifAbsent = redisMasterTemplate.opsForValue().setIfAbsent(lockKsy, "OK");

            List<SgBWmsToInventoryChangeNoticeTask> sgBWmsToInventoryChangeNoticeTasks =
                    sgBWmsToInventoryChangeNoticeTaskMapper.selectList(new LambdaQueryWrapper<SgBWmsToInventoryChangeNoticeTask>()
                            .eq(SgBWmsToInventoryChangeNoticeTask::getOutBizCode, outBizCode)
                            .eq(SgBWmsToInventoryChangeNoticeTask::getIsactive, "Y"));

            if (CollectionUtils.isNotEmpty(sgBWmsToInventoryChangeNoticeTasks) || ifAbsent == null || !ifAbsent) {
                log.error(LogUtil.format("库存异动通知单WMS回传重复.messageBody=", "库存异动通知单WMS回传重复", sgBWmsToInventoryChangeNoticeTasks), msg);
            } else {
                redisMasterTemplate.expire(lockKsy, 30, TimeUnit.SECONDS);
                List<SgBWmsToInventoryChangeNoticeTask> inventoryChangeNoticeTaskList = new ArrayList<>();
                for (int i = 0; i < items.size(); i++) {
                    JSONObject jsonObject1 = items.getJSONObject(i);
                    SgBWmsToInventoryChangeNoticeTask sgBWmsToInventoryChangeNoticeTask = new SgBWmsToInventoryChangeNoticeTask();
                    sgBWmsToInventoryChangeNoticeTask.setId(ModelUtil.getSequence(SgConstants.SG_B_INVENTORY_CHANGE_NOTICE_TASK));
                    //单据类型
                    sgBWmsToInventoryChangeNoticeTask.setBillType(jsonObject1.getString("orderType"));
                    //仓库编码
                    String warehouseCode = jsonObject1.getString("warehouseCode");
                    sgBWmsToInventoryChangeNoticeTask.setWarehouseCode(warehouseCode);
                    //wms单号
                    sgBWmsToInventoryChangeNoticeTask.setWmsBillCode(jsonObject1.getString("orderCode"));
                    //外部业务编码
                    sgBWmsToInventoryChangeNoticeTask.setOutBizCode(jsonObject1.getString("outBizCode"));
                    //商品编码
                    sgBWmsToInventoryChangeNoticeTask.setPsCProEcode(jsonObject1.getString("itemCode"));
                    //库存类型
                    String stockType = jsonObject1.getString("inventoryType");
                    if (!STOCK_TYPE.contains(stockType)) {
                        throw new NDSException("库存类型非法！");
                    }
                    sgBWmsToInventoryChangeNoticeTask.setStockType(stockType);
                    //变化量
                    sgBWmsToInventoryChangeNoticeTask.setQtyChange(jsonObject1.getInteger("quantity"));
                    //批次编码
                    sgBWmsToInventoryChangeNoticeTask.setBatchCode(jsonObject1.getString("batchCode"));
                    //异动时间
                    sgBWmsToInventoryChangeNoticeTask.setChangeTime(jsonObject1.getDate("changeTime"));
                    //异动时间
                    sgBWmsToInventoryChangeNoticeTask.setChangeTime(jsonObject1.getDate("changeTime"));
                    //仓库类型
                    SgCpCPhyWarehouse cpCPhyWarehouse = null;
                    if (!StringUtils.isEmpty(warehouseCode)) {
                        cpCPhyWarehouse = cpCPhyWarehouseMapper.getCpCPhyWarehouseName(warehouseCode);
                    }
                    if (cpCPhyWarehouse != null) {
                        sgBWmsToInventoryChangeNoticeTask.setWmsWarehouseType(cpCPhyWarehouse.getWmsType());
                        sgBWmsToInventoryChangeNoticeTask.setWarehouseCode(warehouseCode);
                        if (ThirdWmsTypeEnum.FLWMS.getCode().equals(cpCPhyWarehouse.getWmsType())) {
                            JSONObject extendProps = request.getJSONObject("extendProps");
                            if (extendProps != null) {
                                jsonObject1.put("extendProps", extendProps);
                                String storageLocation = extendProps.getString("storageLocation");
                                if (StringUtils.isNotEmpty(storageLocation)) {
                                    sgBWmsToInventoryChangeNoticeTask.setWarehouseCode(storageLocation);
                                }
                                if ((SgConstantsIF.BILL_TYPE_KCYK.equals(sgBWmsToInventoryChangeNoticeTask.getBillType())
                                        || SgConstantsIF.BILL_TYPE_BWQZC.equals(sgBWmsToInventoryChangeNoticeTask.getBillType()))
                                        && sgBWmsToInventoryChangeNoticeTask.getQtyChange() != null
                                        && sgBWmsToInventoryChangeNoticeTask.getQtyChange() > 0) {
                                    String inStorageLocation = extendProps.getString("inStorageLocation");
                                    if (StringUtils.isNotEmpty(inStorageLocation)) {
                                        sgBWmsToInventoryChangeNoticeTask.setWarehouseCode(inStorageLocation);
                                    }
                                }
                            }
                        }
                    }
                    sgBWmsToInventoryChangeNoticeTask.setMessage(jsonObject1.toString());
                    sgBWmsToInventoryChangeNoticeTask.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT);
                    sgBWmsToInventoryChangeNoticeTask.setFailedCount(NumberUtils.INTEGER_ZERO);
                    sgBWmsToInventoryChangeNoticeTask.setIsactive(SgConstants.IS_ACTIVE_Y);
                    StorageUtils.setBModelDefalutData(sgBWmsToInventoryChangeNoticeTask, R3SystemUserResource.getSystemRootUser());
                    inventoryChangeNoticeTaskList.add(sgBWmsToInventoryChangeNoticeTask);
                }
                sgBWmsToInventoryChangeNoticeTaskMapper.batchInsert(inventoryChangeNoticeTaskList);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("库存异动通知单WMS回传异常={}", "库存异动通知单WMS回传异常"), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        } finally {
            redisMasterTemplate.delete(lockKsy);
        }
        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("success"));
    }
}
