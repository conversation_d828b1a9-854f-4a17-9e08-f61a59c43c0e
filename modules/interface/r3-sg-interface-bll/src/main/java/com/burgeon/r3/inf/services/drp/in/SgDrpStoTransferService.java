package com.burgeon.r3.inf.services.drp.in;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.inf.config.SgDrpConfig;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransfer;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpTransferCancelRequest;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpTransferConfirmRequest;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpTransferItemSaveRequest;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpTransferSaveRequest;
import com.burgeon.r3.sg.inf.model.result.drp.in.SgDrpTransferSavResult;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.transfer.SgBStoTransferMapper;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferSaveRequest;
import com.burgeon.r3.sg.store.services.transfer.SgBStoTransferCancelSubmitService;
import com.burgeon.r3.sg.store.services.transfer.SgBStoTransferSaveAndSubmitService;
import com.burgeon.r3.sg.store.services.transfer.SgTransferBillStatusEnum;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCTranwayAssign;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * 分销调拨单创建接口
 * @date 2021/6/19 13:11
 */
@Slf4j
@Component
public class SgDrpStoTransferService {
    @Autowired
    SgBStoTransferSaveAndSubmitService sgBStoTransferSaveAndSubmitService;
    @Autowired
    SgBStoTransferCancelSubmitService sgBStoTransferCancelSubmitService;
    @Autowired
    SgDrpStoTransferConfirmService sgDrpStoTransferConfirmService;
    @Autowired
    SgBStoTransferMapper sgStoTransferMapper;
    @Autowired
    private SgDrpConfig sgDrpConfig;

    /**
     * 调拨单保存
     */
    public ValueHolderV14<SgDrpTransferSavResult> save(SgDrpTransferSaveRequest request) {
        if (!sgDrpConfig.getEnableDrpSend()) {
            return new ValueHolderV14<>(ResultCode.FAIL, "中台系统未开放,请稍后再试!");
        }

        log.info("Start SgDrpStoTransferService.save param={}", JSONObject.toJSONString(request));

        String lockKsy =
                SgConstants.INTERFACE_DRP + ":" + SgConstants.SG_B_STO_TRANSFER + ":" + request.getSourceBillNo();
        SgRedisLockUtils.lock(lockKsy);

        ValueHolderV14<SgDrpTransferSavResult> v14 = new ValueHolderV14<>();
        try {
            SgDrpTransferSavResult baseResult = new SgDrpTransferSavResult();
            SgBStoTransfer stoTransfer = sgStoTransferMapper.selectOne(new LambdaQueryWrapper<SgBStoTransfer>()
                    .eq(SgBStoTransfer::getBillNo, request.getBillNo())
                    .ne(SgBStoTransfer::getStatus, SgTransferBillStatusEnum.VOIDED.getVal())
                    .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (stoTransfer != null) {
                v14.setCode(ResultCode.SUCCESS);
                v14.setMessage("当前单据编号:" + request.getBillNo() + "已存在,不允许重复创建!");
                return v14;
            }
            SgBStoTransferBillSaveRequest transferSaveService = new SgBStoTransferBillSaveRequest();
            transferSaveService.setLoginUser(DrpUtils.getUser());
            SgBStoTransferSaveRequest saveRequest = new SgBStoTransferSaveRequest();
            BeanUtils.copyProperties(request, saveRequest);
            if (SgStoreConstants.DRP_BILL_TYPE_SR.equals(request.getDrpBillType())) {
                if (StringUtils.isNotEmpty(request.getOutTime())) {
                    saveRequest.setOutDate(DateUtil.stringToDate(request.getOutTime()));
                } else {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage("Drp类型为销售退货流程,出库日期不能为空!");
                    return v14;
                }
            }
            if (!StringUtils.isEmpty(request.getCpCTranwayAssign())) {
                CpCTranwayAssign cpCTranwayAssign =
                        CommonCacheValUtils.getCpCTranwayAssignpByName(request.getCpCTranwayAssign());
                if (cpCTranwayAssign != null) {
                    saveRequest.setCpCTranwayAssignId(cpCTranwayAssign.getId());
                }
            }

            //erp创建 drp状态为不传
            saveRequest.setDrpStatus(SgStoreConstants.SEND_DRP_STATUS_NOT_PASS);

            //erp没有此字段 调用入口赋值取当前时间
            if (!StringUtils.isEmpty(request.getBillDate())) {
                saveRequest.setBillDate(DateUtil.stringToDate(request.getBillDate()));
            } else {
                saveRequest.setBillDate(new Date());
            }
            //允许负库存  逻辑占用单
            transferSaveService.setNegativePrein(true);
            transferSaveService.setObjId(-1L);
            transferSaveService.setTransferSaveRequest(saveRequest);
            List<SgBStoTransferItemSaveRequest> itemSaveRequestList = new ArrayList<>();
            if (CollectionUtils.isEmpty(request.getItems())) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("逻辑调拨单保存,明细信息不能为空!");
                return v14;
            }
            for (SgDrpTransferItemSaveRequest item : request.getItems()) {
                SgBStoTransferItemSaveRequest itemSaveRequest = new SgBStoTransferItemSaveRequest();
                BeanUtils.copyProperties(item, itemSaveRequest);
                itemSaveRequestList.add(itemSaveRequest);
            }
            transferSaveService.setItems(itemSaveRequestList);
            ValueHolderV14<SgR3BaseResult> result =
                    sgBStoTransferSaveAndSubmitService.saveAndSubmit(transferSaveService);
            baseResult.setBillNo(result.getData().getBillNo());
            return new ValueHolderV14<>(baseResult, ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        } catch (Exception ex) {
            log.error("SgDrpStoTransferService.save exception_has_occured:{}",
                    Throwables.getStackTraceAsString(ex));
            return new ValueHolderV14<>(ResultCode.FAIL, ex.getMessage());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
    }

    /**
     * 调拨单取消审核
     */
    public ValueHolderV14<SgR3BaseResult> cancelTransfer(SgDrpTransferCancelRequest request) {
        if (!sgDrpConfig.getEnableDrpSend()) {
            return new ValueHolderV14<>(ResultCode.FAIL, "中台系统未开放,请稍后再试!");
        }

        log.info("Start SgDrpStoTransferService.cancelTransfer param={}", JSONObject.toJSONString(request));

        String lockKsy = SgConstants.INTERFACE_DRP + ":" + SgConstants.SG_B_STO_TRANSFER +
                SgConstantsIF.VOID + ":" + request.getBillNo();
        SgRedisLockUtils.lock(lockKsy);

        try {
            SgDrpTransferConfirmRequest confirmRequest = new SgDrpTransferConfirmRequest();
            confirmRequest.setBillNo(request.getBillNo());
            return sgDrpStoTransferConfirmService.cancel(confirmRequest, false);

        } catch (Exception ex) {
            log.error("SgDrpStoTransferService.cancelTransfer exception_has_occured:{}",
                    Throwables.getStackTraceAsString(ex));
            return new ValueHolderV14<>(ResultCode.FAIL, ex.getMessage());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
    }
}
