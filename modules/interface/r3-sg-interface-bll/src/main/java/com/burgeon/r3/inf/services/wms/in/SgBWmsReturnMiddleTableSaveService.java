package com.burgeon.r3.inf.services.wms.in;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.basic.rpc.RpcPsService;
import com.burgeon.r3.sg.channel.mapper.storage.SgBWmsReturnMiddleTableMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBWmsReturnMiddleTable;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.stocksync.common.SgResultCode;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustMainSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustSaveRequest;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSaveService;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * WMS发起回传中间表
 */
@Component
@Slf4j
public class SgBWmsReturnMiddleTableSaveService {

    @Autowired
    private SgBWmsReturnMiddleTableMapper wmsReturnMiddleTableMapper;

    @Autowired
    private RpcPsService rpcPsService;

    @Autowired
    private CpCStoreMapper cpCStoreMapper;

    @Autowired
    SgBStoAdjustSaveService sgBStoAdjustSaveService;

    @Autowired
    CpCPhyWarehouseMapper cpCPhyWarehouseMapper;

    public ValueHolderV14 saveWmsReturnMiddleTable(String message) {
        log.info("SgBWmsReturnMiddleTableSaveService saveWmsReturnMiddleTable message:{}",message);
        SgBWmsReturnMiddleTable returnMiddleTable = new SgBWmsReturnMiddleTable();
        ValueHolderV14 vh14 = new ValueHolderV14();
        JSONObject result = JSONObject.parseObject(message);
        JSONArray requestArray = result.getJSONArray("request");
        User rootUser = SystemUserResource.getRootUser();
        Map<String,JSONArray> warehouseGroupItemMap = new HashMap<>();
        log.info("requestArray : {}",requestArray);
        for (Object obj : requestArray) {
            JSONObject requestJSON = (JSONObject) obj;

            if (requestJSON.get("quantity") == null
                    ||requestJSON.getLong("quantity") == 0) {
                continue;
            }
            //根据仓库分组
            JSONArray tempJSONArray;
            String groupKey = requestJSON.getString("orderCode")+" "+requestJSON.getString("warehouseCode");
            if(warehouseGroupItemMap.get(groupKey)!=null){
                tempJSONArray = warehouseGroupItemMap.get(groupKey);
            }else{
                tempJSONArray = new JSONArray();
            }
            tempJSONArray.add(requestJSON);
            warehouseGroupItemMap.put(groupKey,tempJSONArray);
        }
        log.info("warehouseGroupItemMap : {}",warehouseGroupItemMap.size());
        for(String key : warehouseGroupItemMap.keySet()){
            String orderCode = key.split(" ")[0];
            String warehouseCode = key.split(" ")[1];
//            String orderType = key.split("-")[2];

            /**判断单号是否已存在*/
            QueryWrapper<SgBWmsReturnMiddleTable> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SgBWmsReturnMiddleTable::getIsactive, "Y");
            queryWrapper.lambda().eq(SgBWmsReturnMiddleTable::getWmsBillNo, orderCode);
            queryWrapper.lambda().eq(SgBWmsReturnMiddleTable::getWarehouseCode, warehouseCode);

            SgBWmsReturnMiddleTable oldReturnMiddleTable = wmsReturnMiddleTableMapper.selectOne(queryWrapper);
            if (oldReturnMiddleTable != null) {
                //单号已存在，更新时间后结束
                returnMiddleTable.setId(oldReturnMiddleTable.getId());
                returnMiddleTable.setWmsBackCount(oldReturnMiddleTable.getWmsBackCount().add(BigDecimal.ONE));
                returnMiddleTable.setModifierid(Long.valueOf(rootUser.getId()));
                returnMiddleTable.setModifiername(rootUser.getName());
                returnMiddleTable.setModifieddate(new Date());
                int updateResult = wmsReturnMiddleTableMapper.updateById(returnMiddleTable);
                log.info("SgBWmsReturnMiddleTableSaveService saveWmsReturnMiddleTable updateMiddleResult:{} ",updateResult);
                continue;
            }

            returnMiddleTable.setId(ModelUtil.getSequence(SgConstants.SG_B_WMS_RETURN_MIDDLE_TABLE));
            returnMiddleTable.setOutResultStr(message);
            returnMiddleTable.setOutAnalyseStr(warehouseGroupItemMap.get(key).toJSONString());
            returnMiddleTable.setWarehouseCode(warehouseCode);
            returnMiddleTable.setOutType("0");
            returnMiddleTable.setOutStatus("0");
            returnMiddleTable.setWmsBackCount(BigDecimal.ONE);
            returnMiddleTable.setWmsBillNo(orderCode);
            returnMiddleTable.setWmsFailedCount(0L);
            returnMiddleTable.setAdClientId(37L);
            returnMiddleTable.setAdOrgId(27L);
            returnMiddleTable.setCreationdate(new Date());
            returnMiddleTable.setOwnername(rootUser.getName());
            returnMiddleTable.setOwnerid(Long.valueOf(rootUser.getId()));
            returnMiddleTable.setIsactive("Y");
            try{
                SgCpCStore store = this.getStoreInfo(warehouseCode);
                returnMiddleTable.setCpCStoreId(store.getId());
                returnMiddleTable.setCpCStoreEcode(store.getCpCStoreEcode());
                returnMiddleTable.setCpCStoreEname(store.getCpCStoreEname());
            }catch (Exception e){
                log.info("获取逻辑仓失败！{}",e.getMessage());
                vh14.setCode(ResultCode.FAIL);
                vh14.setMessage("获取逻辑仓失败！"+e.getMessage());
            }
            int saveMiddleResult = wmsReturnMiddleTableMapper.insert(returnMiddleTable);
            log.info("SgBWmsReturnMiddleTableSaveService saveWmsReturnMiddleTable saveMiddleResult:{} returnMiddleTable:{}",saveMiddleResult,returnMiddleTable);
            if(saveMiddleResult>0){
                vh14.setCode(ResultCode.SUCCESS);
                vh14.setMessage("共更新"+saveMiddleResult+"条！");
            }else{
                vh14.setCode(ResultCode.FAIL);
                vh14.setMessage("更新失败！");
            }
        }
        return vh14;
    }

//    public static void main(String[] args) {
//        String str = "ss-33 55-";
//        System.out.println(str.split(" ")[1]);
//    }

    /**
     * 查询仓库信息
     * @param warehouseCode
     * @return
     */
    public SgCpCStore getStoreInfo(String warehouseCode){
        SgCpCStore store = new SgCpCStore();
        /** 1、warehouseCode”对应【实体仓档案】中“实体仓编码”记录的实体仓； */
        List<SgCpCPhyWarehouse> phyList = cpCPhyWarehouseMapper.selectList(new LambdaQueryWrapper<SgCpCPhyWarehouse>()
                .eq(SgCpCPhyWarehouse::getIsactive,"Y")
                .eq(SgCpCPhyWarehouse::getWmsWarehouseCode,warehouseCode));
        if(phyList==null||phyList.size()==0){
            log.info("getStoreInfo 没有查询到实体仓档案");
            return store;
        }
        List<Long> phyIdList = phyList.stream().map(SgCpCPhyWarehouse::getId).collect(Collectors.toList());
        /** 2、根据 实体仓id 查询所有逻辑仓 */
        List<SgCpCStore> storeList = cpCStoreMapper.selectList(new LambdaQueryWrapper<SgCpCStore>()
                .eq(SgCpCStore::getIsactive,"Y")
                .in(SgCpCStore::getCpCPhyWarehouseId,phyIdList)
                .last(" order by id desc"));
        if(storeList==null||storeList.size()==0){
            log.info("getStoreInfo 没有查询到逻辑仓档案");
            return store;
        }

        /** 3、取逻辑仓是主仓的，如果所有逻辑仓都没有主仓，取第一个（id已排序） */
        List<SgCpCStore> mainStoreList = storeList.stream().filter(s->(s.getIsMainWarehouse()!=null && s.getIsMainWarehouse()==1)).collect(Collectors.toList());
        store = mainStoreList.size()==0?storeList.get(0):mainStoreList.get(0);
        return store;
    }

    public ValueHolderV14 createAdjustBill() {
        User user = SystemUserResource.getRootUser();

        QueryWrapper<SgBWmsReturnMiddleTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SgBWmsReturnMiddleTable::getIsactive, "Y");
        queryWrapper.lambda().eq(SgBWmsReturnMiddleTable::getOutType, "0");
        queryWrapper.lambda().lt(SgBWmsReturnMiddleTable::getWmsFailedCount, 5);
        queryWrapper.lambda().in(SgBWmsReturnMiddleTable::getOutStatus, new ArrayList<String>() {{
            add("0");
            add("3");
            add("4");
        }});
        queryWrapper.lambda().orderBy(true, true, SgBWmsReturnMiddleTable::getCreationdate);
        PageHelper.startPage(0, 1000);
        List<SgBWmsReturnMiddleTable> list = wmsReturnMiddleTableMapper.selectList(queryWrapper);
        List<SgBWmsReturnMiddleTable> updateList = new ArrayList<>();
        int updateResult = 0;
        for (SgBWmsReturnMiddleTable wmsReturnMiddleTable : list) {

            SgBStoAdjustSaveRequest request = new SgBStoAdjustSaveRequest();
            SgBWmsReturnMiddleTable updateMidTable = new SgBWmsReturnMiddleTable();
            Long wmsFailedCount = wmsReturnMiddleTable.getWmsFailedCount();

            updateMidTable.setId(wmsReturnMiddleTable.getId());
            updateMidTable.setOutStatus("0");
            updateMidTable.setWmsFailedReason("默认值");
            updateMidTable.setWmsFailedCount(wmsFailedCount+1);

            try {
                request = parseInventoryMessage2SgPhyAdjust(wmsReturnMiddleTable);
            } catch (Exception e) {
                log.error("parseInventoryMessage2SgPhyAdjust.Exception{}", e.getMessage(), e);
                updateMidTable.setOutStatus("3");
                updateMidTable.setWmsFailedCount(wmsFailedCount+1);
                updateMidTable.setWmsFailedReason(StringUtils.substring(e.getMessage(), 0, 500));
                updateMidTable.setModifieddate(wmsReturnMiddleTable.getModifieddate());
                updateList.add(updateMidTable);
                continue;
            }

            try {
                log.info("SgBWmsReturnMiddleTableSaveService createAdjustBill 创建调整单请求 request：{} ",request);
                ValueHolderV14<SgR3BaseResult> holderV14 = sgBStoAdjustSaveService.saveAndSubmit(request);
                log.info("SgBWmsReturnMiddleTableSaveService createAdjustBill 创建调整单结果 holderV14：{} ",holderV14);
                if (SgResultCode.isFail(holderV14)) {
                    updateMidTable.setWmsFailedReason(holderV14.getMessage());
                    updateMidTable.setWmsFailedCount(wmsFailedCount + 1);
                    updateMidTable.setOutStatus("4");
                }else{
                    SgR3BaseResult sgR3BaseResult = holderV14.getData();
                    updateMidTable.setWmsFailedReason("调整单处理成功");
                    updateMidTable.setWmsFailedCount(wmsFailedCount);
                    updateMidTable.setOutStatus("2");
                    updateMidTable.setAdjustBillNo(sgR3BaseResult.getBillNo());
                }
            } catch (Exception e) {
                updateMidTable.setWmsFailedReason(StringUtils.substring(e.getMessage(), 0, 500));
                updateMidTable.setWmsFailedCount(wmsFailedCount + 1);
                updateMidTable.setOutStatus("4");

                log.error("生成调整单异常 {}", e.getMessage());
                e.printStackTrace();
            }

            updateMidTable.setModifieddate(wmsReturnMiddleTable.getModifieddate());
            updateList.add(updateMidTable);
        }
        log.info("准备更新  updateList： {}",updateList);
        if (CollectionUtils.isNotEmpty(updateList)) {
//            wmsReturnMiddleTableMapper.batchUpdate(updateList);
            for(SgBWmsReturnMiddleTable wmsReturnMiddleTable : updateList){
                updateResult += wmsReturnMiddleTableMapper.updateById(wmsReturnMiddleTable);

            }
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "共更新 "+updateResult+" 条!");
    }


    /**
     * 奇门库存盘点结果WMS回传服务数据转化为库存调整单
     *
     * @param middleTable
     * @return
     */
    private SgBStoAdjustSaveRequest parseInventoryMessage2SgPhyAdjust(SgBWmsReturnMiddleTable middleTable) {
        String message = middleTable.getOutAnalyseStr();
        User user = SystemUserResource.getRootUser();
        log.info("parseInventoryMessage2SgPhyAdjust  message 盘点中间表信息：{}", message);
        SgBStoAdjustSaveRequest adjustRequest = new SgBStoAdjustSaveRequest();
        JSONArray profitItem = JSONArray.parseArray(message);
        /** 0、查询商品信息 */
        List<SgBStoAdjustItemSaveRequest> items = Lists.newArrayList();
        List<String> itemCodeList = new ArrayList<>();
        for (Object obj : profitItem) {
            JSONObject item = (JSONObject) obj;
            itemCodeList.add(item.getString("itemCode"));
        }
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(itemCodeList)) {
            AssertUtils.logAndThrow("parseInventoryMessage2SgPhyAdjust 无异动明细差异数量不等于0的数据，不允许冲账", user.getLocale());
        }
        Map<String, PsCSku> skuMap = new HashMap<>();
        log.info("SgBWmsReturnMiddleTableSaveService parseInventoryMessage2SgPhyAdjust 查询条码 itemCodeList：{} ",itemCodeList);
        ValueHolderV14<List<PsCSku>> valueHolderV14 = rpcPsService.querySKUByEcodeList(itemCodeList);
        log.info("SgBWmsReturnMiddleTableSaveService parseInventoryMessage2SgPhyAdjust 查询条码结果 valueHolderV14：{} ",valueHolderV14);

        if(valueHolderV14.isOK()){
            List<PsCSku> skuList = valueHolderV14.getData();
            for (PsCSku sku : skuList){
                if (skuMap.get(sku.getEcode())==null){
                    skuMap.put(sku.getEcode(),sku);
                }
            }
        }else{
            AssertUtils.logAndThrow("parseInventoryMessage2SgPhyAdjust 异动明细商品信息查询失败！", user.getLocale());
        }

        /** 1、处理主表数据*/
        SgBStoAdjustMainSaveRequest saveRequest = new SgBStoAdjustMainSaveRequest();
        saveRequest.setObjId(-1L);
        saveRequest.setLoginUser(user);
        saveRequest.setSourceBillId(middleTable.getId());
        saveRequest.setSourceBillNo(middleTable.getWmsBillNo());
        saveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_WMS_ALTER);
        // 盘点单时间，取当前时间
        saveRequest.setBillDate(new Date());
        saveRequest.setCpCStoreId(middleTable.getCpCStoreId());
        saveRequest.setCpCStoreEcode(middleTable.getCpCStoreEcode());
        saveRequest.setCpCStoreEname(middleTable.getCpCStoreEname());
        saveRequest.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL);
        saveRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_ADJUST_PROP_ALTER_ADJUSTMENT);
        saveRequest.setRemark("wms库存异动！"+middleTable.getWmsBillNo());
        saveRequest.setDrpStatus(Integer.valueOf(SgStoreConstants.SEND_DRP_STATUS_UNDECLARED));
        /** 2、处理子表数据 */
        for (Object obj : profitItem) {
            JSONObject item = (JSONObject) obj;
            if (item.getLong("quantity") == 0) {
                continue;
            }
            SgBStoAdjustItemSaveRequest itemSaveRequest = new SgBStoAdjustItemSaveRequest();
            itemSaveRequest.setId(-1L);
            itemSaveRequest.setSourceBillItemId(skuMap.get(item.get("itemCode")).getId());
            itemSaveRequest.setCpCStoreId(middleTable.getCpCStoreId());
            itemSaveRequest.setCpCStoreEcode(middleTable.getCpCStoreEcode());
            itemSaveRequest.setCpCStoreEname(middleTable.getCpCStoreEname());
//            itemSaveRequest.setPsCSkuEcode(skuMap.get(item.get("itemCode")).getEcode());
            itemSaveRequest.setPsCSkuId(skuMap.get(item.get("itemCode")).getId());
            itemSaveRequest.setQty(new BigDecimal(item.getLong("quantity")));
            items.add(itemSaveRequest);
        }
        adjustRequest.setMainRequest(saveRequest);
        adjustRequest.setItems(items);
        adjustRequest.setLoginUser(user);
        adjustRequest.setObjId(-1L);

        log.info("WMS回传服务数据转化为库存调整单 parseInventoryMessage2SgPhyAdjust :{}", adjustRequest);
        return adjustRequest;
    }



}