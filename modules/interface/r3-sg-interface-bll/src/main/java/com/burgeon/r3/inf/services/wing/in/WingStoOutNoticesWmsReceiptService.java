package com.burgeon.r3.inf.services.wing.in;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.error.MqException;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.config.SgStorageMqConfig;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.inf.common.SgInfConstants;
import com.burgeon.r3.sg.inf.model.request.wing.out.SgWingStoOutNoticesReceiptRequest;
import com.burgeon.r3.sg.inf.model.result.wing.SgWingStoOutNoticesReceiptResult;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesMapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * （wing->中台）出库通知单WMS回执接口
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WingStoOutNoticesWmsReceiptService {

    @Autowired
    private SgBStoOutNoticesMapper mapper;

    //    @Autowired
//    private R3MqSendHelper r3MqSendHelper;
    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private SgStorageMqConfig mqConfig;

    /**
     * （wing->中台）出库通知单WMS回执接口
     *
     * @param requests 请求参数
     * @return ValueHolderV14
     */
    public ValueHolderV14<List<SgWingStoOutNoticesReceiptResult>> receiptWmsStoOutNotices(List<SgWingStoOutNoticesReceiptRequest> requests) {
        if (CollectionUtils.isEmpty(requests)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "请求参数不能为空!");
        }

        log.info("Start WingStoOutNoticesWmsReceiptService.receiptWmsStoOutNotices. ReceiveParams:requests={}",
                JSONObject.toJSONString(requests));

        ValueHolderV14<List<SgWingStoOutNoticesReceiptResult>> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "回执成功!");

        try {

            WingStoOutNoticesWmsReceiptService service =
                    ApplicationContextHandle.getBean(WingStoOutNoticesWmsReceiptService.class);
            vh = service.execWmsReceipt(requests);

            if (ResultCode.FAIL == vh.getCode()) {
                log.error("{}.execWmsReceipt. error_has_occured:出库通知单WMS回执-{}", this.getClass().getName(),
                        vh.getMessage());
            }

        } catch (Exception e) {

            log.error("{}.receiptWmsStoOutNotices. exception_has_occured:{}", this.getClass().getName(),
                    Throwables.getStackTraceAsString(e));

            vh.setCode(ResultCode.FAIL);
            vh.setMessage("（wing->中台）出库通知单WMS回执发生异常! 失败原因:" + e.getMessage());
        }

        log.info("Finish WingStoOutNoticesWmsReceiptService.receiptWmsStoOutNotices. ReturnResult:ValueHolder:{}",
                JSONObject.toJSONString(vh));

        return vh;
    }

    /**
     * 出库通知单WMS回执逻辑
     *
     * @param requests 请求参数
     */
    public ValueHolderV14<List<SgWingStoOutNoticesReceiptResult>> execWmsReceipt(List<SgWingStoOutNoticesReceiptRequest> requests) {

        ValueHolderV14<List<SgWingStoOutNoticesReceiptResult>> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "回执成功!");
        List<String> outNoticesBillNos =
                requests.stream().map(SgWingStoOutNoticesReceiptRequest::getSourceBillNo).collect(Collectors.toList());
        //获取出库通知单map
        Map<String, SgBStoOutNotices> outNoticesHashMap = getStringSgStoOutNoticesMap(outNoticesBillNos);
        List<SgWingStoOutNoticesReceiptResult> results = new ArrayList<>();
        for (SgWingStoOutNoticesReceiptRequest request : requests) {
            SgWingStoOutNoticesReceiptResult receiptResult = new SgWingStoOutNoticesReceiptResult();
//            //校验
//            if (SgConstantsIF.BILL_TYPE_RETAIL != request.getSourceBillType() &&
//                    SgConstantsIF.BILL_TYPE_RETAIL_REF != request.getSourceBillType() &&
//                    SgConstantsIF.BILL_TYPE_VIPSHOP != request.getSourceBillType()) {
//
//                receiptResult.setBillNo(request.getSourceBillNo());
//                receiptResult.setFailReason("来源单据类型参数不符合条件！单据类型:" + request.getSourceBillType());
//                results.add(receiptResult);
//                continue;
//            }

            if (!SgInfConstants.MESSAGE_TYPE_WMS.equals(request.getMessageType())) {
                receiptResult.setBillNo(request.getSourceBillNo());
                receiptResult.setFailReason("通知类型参数不符合条件！通知类型:" + request.getMessageType());
                results.add(receiptResult);
                continue;
            }
            SgBStoOutNotices stoOutNotices = outNoticesHashMap.get(request.getSourceBillNo());
            if (stoOutNotices == null) {
                receiptResult.setBillNo(request.getSourceBillNo());
                receiptResult.setFailReason("对应出库通知单不存在！来源单据编号:" + request.getSourceBillNo());
                results.add(receiptResult);
            } else {
                //判断传wms成功状态  防止同一出库通知单wing重复配货请求
                if (stoOutNotices.getThirdPartyStatus() == SgStoreConstants.WMS_UPLOAD_STATUTS_SUCCESS) {
                    continue;
                }
                boolean isUpdateFlag = true;
                boolean isSendMQFlag = false;
                SgBStoOutNotices updateStoOutNotices = new SgBStoOutNotices();
                updateStoOutNotices.setId(stoOutNotices.getId());
                StorageUtils.setBModelDefalutDataByUpdate(updateStoOutNotices, SystemUserResource.getRootUser());
                if (Long.valueOf(SgStoreConstants.WMS_STATUS_PASSING).equals(stoOutNotices.getThirdPartyStatus())){
                    if (SgInfConstants.WMS_RECEIPT_STATUS_SUCCESS.equals(request.getStatus())) {
                        isSendMQFlag = true;
                        // 回执状态成功
                        updateStoOutNotices.setThirdPartyStatus(SgStoreConstants.WMS_UPLOAD_STATUTS_SUCCESS);
                        updateStoOutNotices.setThirdPartyFailReason("");
                    } else if (SgInfConstants.WMS_RECEIPT_STATUS_FAIL.equals(request.getStatus())) {
                        isSendMQFlag = true;
                        // 回执状态失败
                        updateStoOutNotices.setThirdPartyFailReason(request.getReason());
                        updateStoOutNotices.setThirdPartyFailCount(Optional.ofNullable(stoOutNotices.getThirdPartyFailCount()).orElse(0L) + 1);
                        updateStoOutNotices.setThirdPartyStatus(SgStoreConstants.WMS_UPLOAD_STATUTS_FAIL);
                    } else {
                        receiptResult.setBillNo(request.getSourceBillNo());
                        receiptResult.setFailReason("回执状态参数不符合条件！回执状态:" + request.getStatus());
                        results.add(receiptResult);
                        continue;
                    }
                } else {
                    isUpdateFlag = false;
                }

                //更新和发送mq
                WingStoOutNoticesWmsReceiptService receiptService =
                        ApplicationContextHandle.getBean(WingStoOutNoticesWmsReceiptService.class);
                receiptService.updateAndSend(request, stoOutNotices, updateStoOutNotices, results, isUpdateFlag
                        , isSendMQFlag);
            }
        }
        if (CollectionUtils.isNotEmpty(results)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("回执失败！");
            vh.setData(results);
        }
        if (log.isDebugEnabled()) {
            log.debug("Finsh WingStoOutNoticesWmsReceiptService.execWmsReceiptparam.vh={}:",
                    JSONObject.toJSONString(vh));
        }
        return vh;
    }

    /**
     * 根据单号集合获取出库通知单map
     *
     * @param outNoticesBillNos 出库通知单单号集合
     * @return Map<String, SgBStoOutNotices>
     */
    public Map<String, SgBStoOutNotices> getStringSgStoOutNoticesMap(List<String> outNoticesBillNos) {
        if (log.isDebugEnabled()) {
            log.debug("Start WingStoOutNoticesWmsReceiptService.getStringSgStoOutNoticesMap.outNoticesBillNos={}",
                    JSONObject.toJSONString(outNoticesBillNos));
        }
        List<SgBStoOutNotices> sgStoOutNotices = mapper.selectList(new LambdaQueryWrapper<SgBStoOutNotices>()
                .in(SgBStoOutNotices::getBillNo, outNoticesBillNos)
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));

        //map收集出库通知单
        Map<String, SgBStoOutNotices> outNoticesHashMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(sgStoOutNotices)) {
            sgStoOutNotices.forEach(x -> outNoticesHashMap.put(x.getBillNo(), x));
        }
        return outNoticesHashMap;
    }

    /**
     * 更新数据库 发送mq给订单中心
     *
     * @param request             请求参数
     * @param stoOutNotices       出库通知单
     * @param updateStoOutNotices 更新出库通知单
     * @param results             响应wing'results
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAndSend(SgWingStoOutNoticesReceiptRequest request, SgBStoOutNotices stoOutNotices
            , SgBStoOutNotices updateStoOutNotices, List<SgWingStoOutNoticesReceiptResult> results
            , boolean isUpdateFlag, boolean isSendMQFlag) {
        try {
            if (isUpdateFlag) {
                //更新出库通知单wms信息
                mapper.update(updateStoOutNotices, new LambdaUpdateWrapper<SgBStoOutNotices>()
                        .set(SgBStoOutNotices::getThirdPartyFailReason, updateStoOutNotices.getThirdPartyFailReason())
                        .eq(SgBStoOutNotices::getId, stoOutNotices.getId()));
            }
            if (isSendMQFlag){
                //发送Mq【库存中心传wms回执】
                sendMsgToSourceBillNo(stoOutNotices, request);
            }
        } catch (Exception e) {
            log.error("WingStoOutNoticesWmsReceiptService.updateAndSend error:{}", Throwables.getStackTraceAsString(e));
            SgWingStoOutNoticesReceiptResult receiptResult = new SgWingStoOutNoticesReceiptResult();
            receiptResult.setBillNo(request.getSourceBillNo());
            receiptResult.setFailReason("更新数据发送mq失败,失败单号" + request.getSourceBillNo());
            results.add(receiptResult);
        }
    }

    /**
     * @param stoOutNotices
     * @param request
     */
    private void sendMsgToSourceBillNo(SgBStoOutNotices stoOutNotices,
                                       SgWingStoOutNoticesReceiptRequest request) {

        // 来源单据ID和来源单据类型并发送MQ消息给到【更新全渠道订单状态服务】...
        List<JSONObject> retailBodys = Lists.newArrayList();
        List<JSONObject> vipBodys = Lists.newArrayList();
        List<String> retailBillNos = Lists.newArrayList();
        List<String> vipBillNos = Lists.newArrayList();

        JSONObject body = new JSONObject();
        body.put("id", stoOutNotices.getId());
        body.put("orderId", stoOutNotices.getSourceBillId());
        body.put("orderNo", stoOutNotices.getSourceBillNo());
        body.put("noticesBillNo", stoOutNotices.getBillNo());
        body.put("docnos",request.getDocnos());

        // 回执结果 code:19成功 20失败
        body.put("code", request.getStatus() == ResultCode.SUCCESS ? SgConstantsIF.ORDER_RESULT_CODE_WMS_SUCCESS :
                SgConstantsIF.ORDER_RESULT_CODE_WMS_FAIL);
        body.put("orderType", stoOutNotices.getSourceBillType());
        //失败原因
        body.put("flag", request.getReason());

        body.put("outWingToWmsTime", request.getOutWingToWmsTime());
        body.put("outWmsReceiveTime", request.getOutWmsReceiveTime());

        if (SgConstantsIF.BILL_TYPE_RETAIL == stoOutNotices.getSourceBillType()) {
            retailBodys.add(body);
            retailBillNos.add(stoOutNotices.getBillNo());
        } else if (SgConstantsIF.BILL_SHARE_DISTRIBUTION == stoOutNotices.getSourceBillType()) {
            vipBodys.add(body);
            vipBillNos.add(stoOutNotices.getBillNo());
        }

        if (CollectionUtils.isNotEmpty(retailBodys)) {

            JSONObject ret = new JSONObject();
            ret.put("type", SgConstants.SECOND_TO_PASS_TYPE);
            ret.put("body", retailBodys);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format(" 零售回执消息体 :{} 通知单:{};", "零售回执消息体"),
                        ret.toJSONString(), retailBillNos);
            }

            try {
//                r3MqSendHelper.sendMessage(mqConfig.getSgDefaultConfigName(),
//                        ret.toJSONString(),
//                        mqConfig.getNoticesToOmsTopic(),
//                        SgConstantsIF.MSG_TAG_OUTNOTICES_TO_OMS,
//                        UUID.randomUUID().toString().replaceAll("-", ""));
                defaultProducerSend.sendTopic(mqConfig.getMq5callbackToOms(),
                        ret.toJSONString(),
                        SgConstantsIF.MSG_TAG_OUTNOTICES_TO_OMS,
                        UUID.randomUUID().toString().replaceAll("-", ""));
            } catch (MqException e) {
                log.error("{}.sendMsgToSourceBillNo. exception_has_occured:{}", this.getClass().getName(),
                        Throwables.getStackTraceAsString(e));
            }

        }

        if (CollectionUtils.isNotEmpty(vipBodys)) {
            JSONObject ret = new JSONObject();
            ret.put("body", vipBodys);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format(" 唯品会回执消息体 :{} 通知单:{};", "唯品会回执消息体"),
                        ret.toJSONString(), vipBodys);
            }

            try {
//                r3MqSendHelper.sendMessage(mqConfig.getSgDefaultConfigName(),
//                        ret.toJSONString(),
//                        mqConfig.getNoticesToVipTopic(),
//                        SgConstantsIF.MSG_TAG_OUTNOTICES_TO_VIP,
//                        UUID.randomUUID().toString().replaceAll("-", ""));
                defaultProducerSend.sendTopic(mqConfig.getNoticesToVipTopic(),
                        ret.toJSONString(),
                        SgConstantsIF.MSG_TAG_OUTNOTICES_TO_VIP,
                        UUID.randomUUID().toString().replaceAll("-", ""));
            } catch (MqException e) {
                log.error("{}.sendMsgToSourceBillNo. exception_has_occured:{}", this.getClass().getName(),
                        Throwables.getStackTraceAsString(e));
            }
        }
    }

}
