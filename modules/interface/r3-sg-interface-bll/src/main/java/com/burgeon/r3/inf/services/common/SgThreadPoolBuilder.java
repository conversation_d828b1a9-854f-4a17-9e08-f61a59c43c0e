package com.burgeon.r3.inf.services.common;

import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Desc : 线程池创建
 * <AUTHOR> xiWen
 * @Date : 2021/1/12
 */
@Slf4j
@Deprecated
public final class SgThreadPoolBuilder {

    //private static AtomicBoolean isLock = new AtomicBoolean(false);

    /**
     * 分库数量
     */
    private static int SIZE = 8;

    private static final String BASE_ODR = "SG_B_WMS_TO_STO_OUT_RESULT";

    /**
     * 线程池
     */
    private static ConcurrentHashMap<String, ThreadPoolExecutor> threadPoolMap = new ConcurrentHashMap<>();

    /**
     * 获取线程池
     *
     * @param poolName      线程池名称
     * @param queueCapacity 队列
     * @return 线程池
     */
    @Deprecated
    public static ThreadPoolExecutor build(String poolName, int... queueCapacity) {

        AssertUtils.cannot(StringUtils.isBlank(poolName), "ThreadPool Name Can Not Be Empty");
        ThreadPoolExecutor var = threadPoolMap.get(poolName);
        if (var == null) {
            synchronized (SgThreadPoolBuilder.class) {
                int queue = queueCapacity.length > 0 ? (Math.max(queueCapacity[0], SIZE)) : SIZE;
                var = threadPoolMap.get(poolName);
                if (var == null) {
                    var = new ThreadPoolExecutor(
                            SIZE,
                            SIZE,
                            0L,
                            TimeUnit.SECONDS,
                            new ArrayBlockingQueue(queue),
                            new ThreadFactoryBuilder().setNameFormat(poolName).build(),
                            new ThreadPoolExecutor.AbortPolicy()
                    );
                    //    executor.allowCoreThreadTimeOut(true);
                    threadPoolMap.putIfAbsent(poolName, var);
                }
            }
        }
        return var;
    }

}
