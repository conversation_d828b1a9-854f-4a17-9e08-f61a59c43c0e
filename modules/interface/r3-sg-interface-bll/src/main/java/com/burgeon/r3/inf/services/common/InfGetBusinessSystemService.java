package com.burgeon.r3.inf.services.common;

import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.util.Tools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2020/11/27 11:36
 * @desc 库存计算扩展
 */
@Component
@Slf4j
public class InfGetBusinessSystemService {
    public static final String BUSINESS_SYSTEM_PREFIX = "business_system:";
    /*** 库存盘点单每批处理数据数量 */
    public static final String SG_B_WMS_INVENTORY_REPORT_BATCH_TIME_SIZE = "sg_b_wms_inventory_report_batch_time_size";

    /**
     * 库存盘点单每批处理数据数量
     *
     * @return
     */
    public static Integer getSgBWmsInventoryReportBatchTimeSize() {
        Integer sgBWmsInventoryReportBatchTimeSize = 100;
        Object o = RedisOpsUtil.getStrRedisTemplate().opsForValue().get(BUSINESS_SYSTEM_PREFIX + SG_B_WMS_INVENTORY_REPORT_BATCH_TIME_SIZE);
        if (o != null) {
            try {
                sgBWmsInventoryReportBatchTimeSize = Tools.getInteger(o.toString(), true);
            } catch (Exception e) {
                sgBWmsInventoryReportBatchTimeSize = 100;
            }
        }
        return sgBWmsInventoryReportBatchTimeSize;
    }
}
