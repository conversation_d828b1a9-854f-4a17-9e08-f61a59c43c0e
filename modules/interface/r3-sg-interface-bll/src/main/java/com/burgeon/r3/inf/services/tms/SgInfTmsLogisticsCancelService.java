package com.burgeon.r3.inf.services.tms;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.store.services.tms.out.SgTmsLogisticsCancelService;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/16 15:47
 */
@Slf4j
@Component
public class SgInfTmsLogisticsCancelService {

    @Autowired
    private SgTmsLogisticsCancelService cancelService;

    /**
     * 本地模拟订单取消
     *
     * @param querySession request
     * @return return
     */
    public ValueHolder orderCancel(QuerySession querySession) {
        String request = querySession.getAttribute("request").toString();
        if (log.isDebugEnabled()) {
            log.debug("Start SgInfTmsOrderCancelService.orderCancel:request{}", request);
        }
        JSONObject jsonObject = JSONObject.parseObject(request);
        ValueHolder valueHolder = new ValueHolder();
        long sourceBillId = jsonObject.getLong("source_bill_id");
        int sourceBillType = jsonObject.getInteger("source_bill_type");

        if (sourceBillId <= 0L) {
            AssertUtils.logAndThrow("来源单据ID不能为空！");
        }
        if (sourceBillType <= 0) {
            AssertUtils.logAndThrow("来源单据类型不能为空！");
        }

        try {
            valueHolder = cancelService.orderCancel(sourceBillId, sourceBillType);
        } catch (Exception var1) {
            AssertUtils.logAndThrowException("SgInfTmsOrderCancelService.orderCancel: error", var1);
        }

        return valueHolder;
    }
}
