package com.burgeon.r3.inf.services.oms.product;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.burgeon.r3.inf.mapper.SgBChannelStorageExtMapper;
import com.burgeon.r3.inf.model.SgChannelProductVoidRequest;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.omni.SgBOmniChannelProductMapper;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.model.request.product.SgBChannelProductQueryRequest;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductSaveRequest;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductStorageBufferDelRequest;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.omni.SgBOmniChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.oms.SgBChannelStorage;
import com.burgeon.r3.sg.core.model.table.oms.SgBChannelStorageBuffer;
import com.burgeon.r3.sg.core.model.tableExtend.SgBChannelStorageBufferExtend;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.inf.common.enums.ActiveEnum;
import com.burgeon.r3.sg.stocksync.api.SgChannelStorageOmsInitCmd;
import com.burgeon.r3.sg.stocksync.common.OmsConstantsIF;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageOmsManualSynchRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/3 13:52
 */
@Slf4j
@Component
public class SgChannelProductSaveService {

    public static final String SG_CONTROL_ECODE_TRIM_REGEX = "business_system:sg_control_ecode_trim_regex";

    //TODO:渠道库存后面可能需要调用MQ
//    @Autowired
//    SgChannelStorageOmsBufferService sgChannelStorageOmsBufferService;

    @Autowired
    SgBChannelStorageExtMapper sgBChannelStorageExtMapper;

//    @Autowired
//    SgBChannelStorageBufferMapper sgBChannelStorageBufferMapper;

    @Autowired
    private SgBChannelProductMapper sgBChannelProductMapper;

    @Autowired
    private SgBOmniChannelProductMapper sgBOmniChannelProductMapper;

    @Value("${sg.control.newProductDonwload.buffer:true}")
    private Boolean storageBufferControl;

    @Value("${sg.channel.product.delete.key:商品删除}")
    private String voidKey;

    /**
     * 保存或新增渠道商品
     *
     * @param channelProductSaveRequest 淘宝云枢纽商品信息集合
     * @return 执行结果
     */
    public ValueHolderV14 saveChannelProduct(SgChannelProductSaveRequest channelProductSaveRequest) {
        ValueHolderV14 holder;
        long startTime = System.currentTimeMillis();
        log.info(" Start SgChannelProductSaveService.saveChannelProduct. ReceiveParams:SgChannelProductSaveRequest={};"
                , JSONObject.toJSONString(channelProductSaveRequest));
        //检查入参
        holder = checkServiceParam(channelProductSaveRequest);
        if (ResultCode.FAIL == holder.getCode()) {
            return holder;
        }

        User user = R3SystemUserResource.getSystemRootUser();

        //定义总条数和错误条数
        int totalNum = channelProductSaveRequest.getSgBChannelProductList().size();
        int errorNum = 0;
        Set<String> errorSet = Sets.newHashSet();

        //TODO 过滤掉数据不全的数据
        //获取数据信息 齐全的 渠道商品集合
        List<SgBChannelProduct> channelProdctList = Lists.newArrayList();
        List<SgChannelProductStorageBufferDelRequest> delRequests = Lists.newArrayList();
        Set<String> skuIdSet = Sets.newHashSet();
        StringBuilder sb = new StringBuilder();
        for (SgBChannelProduct sgBChannelProduct : channelProductSaveRequest.getSgBChannelProductList()) {
            // 先统一加标记,有SKU_ID
            sgBChannelProduct.setReserveBigint01(ActiveEnum.N.getKey());
            //平台维护的有时候有空格，需要剔除，否则匹配失败
            if (StringUtils.isNotEmpty(sgBChannelProduct.getPsCSkuEcode())) {
                String trim = sgBChannelProduct.getPsCSkuEcode().trim();
                sgBChannelProduct.setPsCSkuEcode(trim);
            }
            // 中台skuecode+shopid_numiid
            if (StringUtils.isEmpty(sgBChannelProduct.getPsCSkuEcode()) ||
                    StringUtils.isEmpty(sgBChannelProduct.getNumiid()) ||
                    Objects.isNull(sgBChannelProduct.getCpCShopId())) {
                errorNum++;
                sb.append("条码编码/店铺id/numiid 为空,skuEcode:").append(sgBChannelProduct.getPsCSkuEcode());
                sb.append(",numiid").append(sgBChannelProduct.getNumiid());
                sb.append(",shopId:").append(sgBChannelProduct.getCpCShopId());
                String errorMsg = sb.toString();
                log.error(errorMsg);
                errorSet.add(errorMsg);
                sb.setLength(0);
            } else {
                // 若明细sku_id为空，则按SKUEECODE_SHOPID_NUMIID拼接平台条码ID
                if (StringUtils.isBlank(sgBChannelProduct.getSkuId())) {
                    sb.append(sgBChannelProduct.getPsCSkuEcode()).append("_")
                            .append(sgBChannelProduct.getCpCShopId()).append("_")
                            .append(sgBChannelProduct.getNumiid());
                    sgBChannelProduct.setSkuId(sb.toString());
                    // 假如是拼接的给与标记
                    sgBChannelProduct.setReserveBigint01(ActiveEnum.Y.getKey());
                    sb.setLength(0);
                }
                // 删除渠道商品
                if (sgBChannelProduct.getIsDelete() != null && sgBChannelProduct.getIsDelete()) {
                    SgChannelProductStorageBufferDelRequest request = new SgChannelProductStorageBufferDelRequest();
                    request.setSkuCode(sgBChannelProduct.getPsCSkuEcode());
                    request.setSkuId(sgBChannelProduct.getSkuId());
                    delRequests.add(request);
                    continue;
                }
                if (StringUtils.isNotBlank(sgBChannelProduct.getSkuSpec())) {
                    // @20210114 数据库字段长度限制 100
                    sgBChannelProduct.setSkuSpec(StringUtils.substring(sgBChannelProduct.getSkuSpec(), 0, 100));
                }
                channelProdctList.add(sgBChannelProduct);
                skuIdSet.add(sgBChannelProduct.getSkuId());
            }
        }
        if (!delRequests.isEmpty()) {
            this.delChannelProductStorageBuffer(delRequests);
        }
        ValueHolderV14 result = this.getResult(channelProdctList, delRequests, errorSet, totalNum, errorNum);
        if (ResultCode.SUCCESS != result.getCode()) {
            return result;
        }
        if (channelProdctList.isEmpty()) {
            return new ValueHolderV14(ResultCode.SUCCESS, result.getMessage());
        }
        //批量查询所有渠道商品的条码信息 默认500/次
        Set<String> skuEcodeSet = channelProdctList.stream().map(SgBChannelProduct::getPsCSkuEcode).collect(Collectors.toSet());
        Map<String, PsCProSkuResult> psCProSkuResultMap = new HashMap<>();
        try {
            psCProSkuResultMap = StorageBasicUtils.getSkus(Lists.newArrayList(skuEcodeSet), SgConstants.SG_COMMON_QUERY_SIZE);
        } catch (Exception e) {
            log.error("调用basicPsQueryService服务失败 ,{} error:{}", e.getMessage(), Throwables.getStackTraceAsString(e));
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(e.getMessage());
            return holder;
        }
        log.info(" Start SgChannelProductSaveService.saveChannelProduct. ReceiveParams:skuIdSet {};"
                , JSONObject.toJSONString(skuIdSet));
        //根据skuId查询平台店铺商品表
        Map<String, List<SgBChannelProduct>> channelProductMap = sgBChannelProductMapper.selectList(new QueryWrapper<SgBChannelProduct>().lambda()
                .in(CollectionUtils.isNotEmpty(skuIdSet), SgBChannelProduct::getSkuId, Lists.newArrayList(skuIdSet)))
                .stream().collect(Collectors.groupingBy(SgBChannelProduct::getSkuId));
        log.info(" Start SgChannelProductSaveService.saveChannelProduct. ReceiveParams:channelProductMap {};"
                , JSONObject.toJSONString(channelProductMap));
        //对于新增的商品和更改掉psCEcode的商品，需要插入渠道计算缓存池
        List<SgBChannelStorageBuffer> sgBChannelStorageBufferList = new ArrayList<>();
        List<SgBChannelProduct> batchInsertSgBChannelProduct = new ArrayList<>();
        log.info(" Start SgChannelProductSaveService.saveChannelProduct. ReceiveParams:channelProdctList {};"
                , JSONObject.toJSONString(channelProdctList));
        //判断入参数据是新增还是修改，并执行
        for (SgBChannelProduct preSgBChannelProduct : channelProdctList) {
            preSgBChannelProduct.setTransTime(new Date());
            try {
                //获取原 平台店铺商品信息
                SgBChannelProduct sgBChannelProduct = null;
                List<SgBChannelProduct> channelProducts = channelProductMap.get(preSgBChannelProduct.getSkuId());
                if (CollectionUtils.isNotEmpty(channelProducts)){
                    channelProducts = channelProducts.stream().filter(x -> preSgBChannelProduct.getCpCShopId().equals(x.getCpCShopId())).collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(channelProducts) && channelProducts.size() > 1) {
                    errorNum++;
                    String errorMsg = "skuid" + preSgBChannelProduct.getSkuId() + "存在多条平台店铺商品记录!";
                    log.error(errorMsg);
                    errorSet.add(errorMsg);
                    continue;
                } else if (CollectionUtils.isNotEmpty(channelProducts) && channelProducts.size() == 1) {
                    sgBChannelProduct = channelProducts.get(0);
                }

                //TODO psCSkuEcode.toUpperCase()??
                String psCSkuEcode = preSgBChannelProduct.getPsCSkuEcode();
                PsCProSkuResult psCProSkuResult = psCProSkuResultMap.get(psCSkuEcode.toUpperCase());
                //判断该渠道商品的条码是否查询到条码、商品、颜色尺寸信息
                if (psCProSkuResult == null) {
                    errorNum++;
                    String errorMsg = "条码编码为" + psCSkuEcode + "未查到条码、商品、颜色尺寸信息";
                    log.error(errorMsg);
                    errorSet.add(errorMsg);
                    continue;
                }

                //为0新增，不为0更新
                if (sgBChannelProduct == null) {
                    //封装条码，商品，颜色尺寸信息,封装渠道库存计算缓存池入参
                    dataEncapsulation(preSgBChannelProduct, psCProSkuResult, batchInsertSgBChannelProduct, sgBChannelStorageBufferList, user);
                } else if (!(Objects.equals(preSgBChannelProduct.getPsCSkuEcode(), sgBChannelProduct.getPsCSkuEcode()))) {
                    //如果更新了商品的psCSkuEcode,也需要添加到渠道库存计算缓存池
                    preSgBChannelProduct.setModifieddate(new Timestamp(System.currentTimeMillis()));

                    SgBChannelStorageBuffer sgBChannelStorageBuffer = new SgBChannelStorageBuffer();
                    sgBChannelStorageBuffer.setWareType("Y".equalsIgnoreCase(psCProSkuResult.getIsGroup()) ? 1 : 0);
                    sgBChannelStorageBuffer.setCpCShopId(preSgBChannelProduct.getCpCShopId());
                    sgBChannelStorageBuffer.setCpCShopTitle(preSgBChannelProduct.getCpCShopTitle());
                    sgBChannelStorageBuffer.setCpCPlatformId(preSgBChannelProduct.getCpCPlatformId());
                    if (preSgBChannelProduct.getSkuId() != null) {
                        sgBChannelStorageBuffer.setSkuId(preSgBChannelProduct.getSkuId());
                        sgBChannelStorageBuffer.setPsCSkuId(preSgBChannelProduct.getPsCSkuId());
                        sgBChannelStorageBuffer.setPsCSkuEcode(preSgBChannelProduct.getPsCSkuEcode());
                        sgBChannelStorageBuffer.setPsCProId(preSgBChannelProduct.getPsCProId());
                    }
                    sgBChannelStorageBuffer.setDealStatus(SgBChannelStorageBufferExtend.DealStatusEnum.UN_DEAL.getCode());
                    StorageUtils.setBModelDefalutData(sgBChannelStorageBuffer, user);
                    sgBChannelStorageBuffer.setOwnerename(user.getEname());
                    sgBChannelStorageBuffer.setModifierename(user.getEname());
                    sgBChannelStorageBufferList.add(sgBChannelStorageBuffer);

                    //如果更新了商品的psCEcode,去条码表找到对应的条码信息
                    preSgBChannelProduct.setPsCProId(psCProSkuResult.getPsCProId());
                    preSgBChannelProduct.setPsCProEcode(psCProSkuResult.getPsCProEcode());
                    preSgBChannelProduct.setPsCProEname(psCProSkuResult.getPsCProEname());
                    preSgBChannelProduct.setPsCSkuId(psCProSkuResult.getId());
                    preSgBChannelProduct.setPsCSkuEcode(psCProSkuResult.getSkuEcode().toUpperCase());
                    preSgBChannelProduct.setPsCSpec1Id(psCProSkuResult.getPsCSpec1objId());
                    preSgBChannelProduct.setPsCSpec1Ecode(psCProSkuResult.getClrsEcode());
                    preSgBChannelProduct.setPsCSpec1Ename(psCProSkuResult.getClrsEname());
                    preSgBChannelProduct.setPsCSpec2Id(psCProSkuResult.getPsCSpec2objId());
                    preSgBChannelProduct.setPsCSpec2Ecode(psCProSkuResult.getSizesEcode());
                    preSgBChannelProduct.setPsCSpec2Ename(psCProSkuResult.getSizesEname());
                    preSgBChannelProduct.setGbcode(psCProSkuResult.getGbcode());
                    if (Objects.nonNull(preSgBChannelProduct.getStatus())) {
                        preSgBChannelProduct.setSaleStatus(preSgBChannelProduct.getStatus());
                    }
                    preSgBChannelProduct.setStatus(SgChannelConstants.CHANNEL_PRODUCT_STATUS_SALING);
                    setIsTmallCity(preSgBChannelProduct);
                    if (log.isDebugEnabled()) {
                        log.debug("SgChannelProductSaveService.saveChannelProduct.update:result={}", JSONObject.toJSONString(preSgBChannelProduct));
                    }

                    //更新 平台店铺商品
                    int update = sgBChannelProductMapper.update(preSgBChannelProduct, new UpdateWrapper<SgBChannelProduct>().lambda()
                            .eq(SgBChannelProduct::getSkuId, preSgBChannelProduct.getSkuId()));

                    if (update < 0) {
                        log.error("更新渠道商品出错,明细为:{};", JSONObject.toJSONString(preSgBChannelProduct));
                    } else {
                        //在psCEcode变化时，删除渠道库存的记录
                        if (!(Objects.equals(preSgBChannelProduct.getPsCSkuEcode(), sgBChannelProduct.getPsCSkuEcode()))) {
                            if (log.isDebugEnabled()) {
                                log.debug("开始删除渠道库存记录,渠道id:{},skuId:{}",
                                        preSgBChannelProduct.getCpCShopId(), preSgBChannelProduct.getSkuId());
                            }

                            UpdateWrapper<SgBChannelStorage> updateWrapper = new UpdateWrapper<>();
                            updateWrapper.eq("numiid", preSgBChannelProduct.getNumiid());
                            updateWrapper.eq("cp_c_shop_id", preSgBChannelProduct.getCpCShopId());
                            if (preSgBChannelProduct.getSkuId() != null) {
                                updateWrapper.eq("sku_id", preSgBChannelProduct.getSkuId());
                            }
                            int delete = sgBChannelStorageExtMapper.delete(updateWrapper);
                            if (delete < 0) {
                                log.error("删除渠道库存记录失败,明细为:{};", JSONObject.toJSONString(preSgBChannelProduct));
                            }
                        }
                    }
                } else if (!Objects.equals(preSgBChannelProduct.getStatus(), sgBChannelProduct.getSaleStatus())) {
                    SgBChannelProduct updateProduct = new SgBChannelProduct();
                    updateProduct.setSaleStatus(preSgBChannelProduct.getStatus());
                    sgBChannelProductMapper.update(updateProduct, new UpdateWrapper<SgBChannelProduct>().lambda()
                            .eq(SgBChannelProduct::getSkuId, preSgBChannelProduct.getSkuId()));
                }

                if (Objects.isNull(sgBChannelProduct)) {
                    continue;
                }
                if (!Objects.equals(preSgBChannelProduct.getCombinationSkuIds(), sgBChannelProduct.getCombinationSkuIds())
                        || !Objects.equals(preSgBChannelProduct.getIsCombinationItem(), sgBChannelProduct.getIsCombinationItem())) {
                    SgBChannelProduct updateProduct = new SgBChannelProduct();
                    updateProduct.setCombinationSkuIds(preSgBChannelProduct.getCombinationSkuIds());
                    updateProduct.setIsCombinationItem(preSgBChannelProduct.getIsCombinationItem());
                    updateProduct.setReserveBigint01(sgBChannelProduct.getReserveBigint01());
                    StorageUtils.setBModelDefalutDataByUpdate(updateProduct, user);
                    sgBChannelProductMapper.update(updateProduct, new UpdateWrapper<SgBChannelProduct>().lambda()
                            .eq(SgBChannelProduct::getSkuId, preSgBChannelProduct.getSkuId()));
                }
            } catch (Exception e) {
                log.warn("SgChannelProductSaveService.saveChannelProduct. exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            }
        }
        log.info(" SgChannelProductSaveService.saveChannelProduct.batchInsertSgBChannelProduct:{}", JSON.toJSONString(batchInsertSgBChannelProduct));
        if (CollectionUtils.isNotEmpty(batchInsertSgBChannelProduct)) {
            try {
                StorageUtils.batchInsertTeus(batchInsertSgBChannelProduct, "渠道商品",
                        SgConstants.SG_COMMON_INSERT_PAGE_SIZE, sgBChannelProductMapper);
            } catch (DuplicateKeyException e) {
                log.warn(LogUtil.format("渠道商品插入时DB完整性约束冲突异常，异常信息：{}",
                        "SgChannelProductSaveService.saveChannelProduct"), Throwables.getStackTraceAsString(e));
                throw new NDSException(e.getMessage());
            }
        }

        //调用渠道库存计算缓存池服务
        if (CollectionUtils.isNotEmpty(sgBChannelStorageBufferList) && storageBufferControl) {
            try {
                if (log.isDebugEnabled()) {
                    log.debug("Start SgChannelProductSaveService.manualCalcAndSyncChannelProduct. request:{}",
                            JSONObject.toJSONString(sgBChannelStorageBufferList));
                }
                SgChannelStorageOmsInitCmd sgChannelStorageOmsInitCmd = (SgChannelStorageOmsInitCmd) ReferenceUtil.refer(
                        ApplicationContextHandle.getApplicationContext(),
                        SgChannelStorageOmsInitCmd.class.getName(),
                        SgConstantsIF.GROUP, SgConstantsIF.VERSION);
                for (SgBChannelStorageBuffer bufferItem : sgBChannelStorageBufferList) {
                    SgChannelStorageOmsManualSynchRequest requestItem = new SgChannelStorageOmsManualSynchRequest();
                    requestItem.setOperate(SgChannelStorageOmsManualSynchRequest.QUERY_BY_CONDITION);
                    requestItem.setCpCShopId(bufferItem.getCpCShopId());
                    requestItem.setSkuId(bufferItem.getSkuId());
                    requestItem.setSourceno("平台店铺商品表保存触发库存同步" + bufferItem.getCpCShopId());
                    try {
                        ValueHolderV14<Boolean> booleanValueHolderV14 = sgChannelStorageOmsInitCmd.manualCalcAndSyncChannelProduct(requestItem);
                        if (ResultCode.FAIL == booleanValueHolderV14.getCode()) {
                            log.error("SgChannelProductSaveService.manualCalcAndSyncChannelProduct. failed:{},msg={}",
                                    JSONObject.toJSONString(requestItem), booleanValueHolderV14.getMessage());
                        }
                    } catch (Exception ex) {
                        log.error("SgChannelProductSaveService.manualCalcAndSyncChannelProduct. error:request={},Exception={}",
                                JSONObject.toJSONString(sgBChannelStorageBufferList), Throwables.getStackTraceAsString(ex));
                    }
                }

//                ValueHolderV14 holderV14 = sgChannelStorageOmsBufferService.addSgBChannelStorageBuffer(sgBChannelStorageBufferList, null);
//                if (0 == (int) holderV14.getData()) {
//                    log.error(this.getClass().getName() + " 调用新增渠道库存计算缓存池出错,明细为 {}", JSONObject.toJSONString(sgBChannelStorageBufferList));
//                }
            } catch (Exception e) {
                log.error("SgChannelProductSaveService.manualCalcAndSyncChannelProduct. errorMsg:{};", Throwables.getStackTraceAsString(e));
            }
        }

        //封装返回结果
        if (errorNum > 0) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("共" + totalNum + "条数据，失败" + errorNum + "条");
            holder.setData(errorSet);
        } else {
            holder.setCode(ResultCode.SUCCESS);
            holder.setMessage("调用成功");
        }

        if (log.isDebugEnabled()) {
            log.debug("Finish SgChannelProductSaveService.saveChannelProduct. ReturnResult:holder:{} spend time:{}ms;"
                    , JSONObject.toJSONString(holder), System.currentTimeMillis() - startTime);
        }
        return holder;
    }

    /**
     * 根据淘宝商品编码查询条码信息，商品信息，颜色尺寸信息，并新增
     *
     * @param preSgBChannelProduct 淘宝云枢纽商品信息
     * @param user
     */
    private void dataEncapsulation(SgBChannelProduct preSgBChannelProduct, PsCProSkuResult psCProSkuResult,
                                   List<SgBChannelProduct> batchInsertSgBChannelProduct,
                                   List<SgBChannelStorageBuffer> sgBChannelStorageBufferList,
                                   User user) {
        User rootUser = R3SystemUserResource.getSystemRootUser();
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        SgBChannelStorageBuffer sgBChannelStorageBuffer = new SgBChannelStorageBuffer();

        Long id = ModelUtil.getSequence("sg_b_channel_product");
        preSgBChannelProduct.setId(id);
        preSgBChannelProduct.setPsCSkuId(psCProSkuResult.getId());
        preSgBChannelProduct.setPsCProId(psCProSkuResult.getPsCProId());
        preSgBChannelProduct.setPsCProEcode(psCProSkuResult.getPsCProEcode());
        preSgBChannelProduct.setPsCProEname(psCProSkuResult.getPsCProEname());
        preSgBChannelProduct.setGbcode(psCProSkuResult.getGbcode());
        preSgBChannelProduct.setPsCSpec1Id(psCProSkuResult.getPsCSpec1objId());
        preSgBChannelProduct.setPsCSpec1Ecode(psCProSkuResult.getClrsEcode());
        preSgBChannelProduct.setPsCSpec1Ename(psCProSkuResult.getClrsEname());
        preSgBChannelProduct.setPsCSpec2Id(psCProSkuResult.getPsCSpec2objId());
        preSgBChannelProduct.setPsCSpec2Ecode(psCProSkuResult.getSizesEcode());
        preSgBChannelProduct.setPsCSpec2Ename(psCProSkuResult.getSizesEname());

        preSgBChannelProduct.setAdClientId(Integer.valueOf(rootUser.getClientId()).longValue());
        preSgBChannelProduct.setAdOrgId(Integer.valueOf(rootUser.getOrgId()).longValue());
        preSgBChannelProduct.setOwnerid(rootUser.getId().longValue());
        preSgBChannelProduct.setModifierid(rootUser.getId().longValue());
        preSgBChannelProduct.setCreationdate(timestamp);
        preSgBChannelProduct.setModifieddate(timestamp);
        preSgBChannelProduct.setModifiername(rootUser.getName());
        preSgBChannelProduct.setModifierename(rootUser.getEname());
        preSgBChannelProduct.setOwnername(rootUser.getName());
        preSgBChannelProduct.setOwnerename(rootUser.getEname());
        preSgBChannelProduct.setOwnerename(rootUser.getEname());
        preSgBChannelProduct.setWareType("Y".equalsIgnoreCase(psCProSkuResult.getIsGroup()) ? 1 : 0);
        preSgBChannelProduct.setSaStoreType(SgConstants.SA_STORE_TYPE_ACTIVITY);
        preSgBChannelProduct.setIstrans(SgConstants.IS_AUTO_Y);
        preSgBChannelProduct.setSyncRatio(OmsConstantsIF.PERCENT);
        if (Objects.isNull(preSgBChannelProduct.getIslock())) {
            preSgBChannelProduct.setIslock(SgConstants.IS_AUTO_N);
        }
        preSgBChannelProduct.setIsactive(SgConstants.IS_ACTIVE_Y);
        if (Objects.nonNull(preSgBChannelProduct.getStatus())) {
            preSgBChannelProduct.setSaleStatus(preSgBChannelProduct.getStatus());
        }
        preSgBChannelProduct.setStatus(SgChannelConstants.CHANNEL_PRODUCT_STATUS_SALING);
        preSgBChannelProduct.setQtySafety(BigDecimal.ZERO);
        setIsTmallCity(preSgBChannelProduct);
        if (log.isDebugEnabled()) {
            log.debug("SgChannelProductSaveService.dataEncapsulation:result={}", JSONObject.toJSONString(preSgBChannelProduct));
        }
        batchInsertSgBChannelProduct.add(preSgBChannelProduct);

        //封装渠道计算缓存池数据
        sgBChannelStorageBuffer.setWareType("Y".equalsIgnoreCase(psCProSkuResult.getIsGroup()) ? 1 : 0);
        sgBChannelStorageBuffer.setCpCShopId(preSgBChannelProduct.getCpCShopId());
        sgBChannelStorageBuffer.setCpCShopTitle(preSgBChannelProduct.getCpCShopTitle());
        sgBChannelStorageBuffer.setCpCPlatformId(preSgBChannelProduct.getCpCPlatformId());
        sgBChannelStorageBuffer.setSkuId(preSgBChannelProduct.getSkuId());
        sgBChannelStorageBuffer.setPsCSkuId(preSgBChannelProduct.getPsCSkuId());
        sgBChannelStorageBuffer.setPsCProId(preSgBChannelProduct.getPsCProId());
        sgBChannelStorageBuffer.setPsCSkuEcode(preSgBChannelProduct.getPsCSkuEcode());
        sgBChannelStorageBuffer.setDealStatus(SgBChannelStorageBufferExtend.DealStatusEnum.UN_DEAL.getCode());
        StorageUtils.setBModelDefalutData(sgBChannelStorageBuffer, user);
        sgBChannelStorageBuffer.setOwnerename(user.getEname());
        sgBChannelStorageBuffer.setModifierename(user.getEname());
        sgBChannelStorageBufferList.add(sgBChannelStorageBuffer);
    }

    private void setIsTmallCity(SgBChannelProduct preSgBChannelProduct) {
        if (StringUtils.isNotEmpty(preSgBChannelProduct.getNumiid())) {
            SgBOmniChannelProduct sgBOmniChannelProduct = sgBOmniChannelProductMapper.selectOne(new LambdaQueryWrapper<SgBOmniChannelProduct>()
                    .eq(SgBOmniChannelProduct::getNumiid, preSgBChannelProduct.getNumiid())
                    .eq(SgBOmniChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (Objects.nonNull(sgBOmniChannelProduct)) {
                preSgBChannelProduct.setIsTmllCity(sgBOmniChannelProduct.getIsTmllCity());
            }
        }
    }

    /**
     * 入参校验
     *
     * @param request 淘宝云枢纽商品信息集合
     * @return 执行结果
     */
    private ValueHolderV14 checkServiceParam(SgChannelProductSaveRequest request) {
        ValueHolderV14 holder = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

        if (request == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("请求体不能为空!");
        }

        if (request != null && CollectionUtils.isEmpty(request.getSgBChannelProductList())) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("渠道商品信息不能为空!");
        }
        return holder;
    }

    /**
     * 删除渠道库存，渠道商品，更新缓存池
     *
     * @param sgChannelProductStorageBufferDelRequestList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 delChannelProductStorageBuffer(List<SgChannelProductStorageBufferDelRequest> sgChannelProductStorageBufferDelRequestList) {
        if (log.isDebugEnabled()) {
            log.debug("Start delChannelProductStorageBuffer ReceiveParams: sgChannelProductList:{}",
                    JSONObject.toJSONString(sgChannelProductStorageBufferDelRequestList));
        }
        ValueHolderV14 valueHolder = new ValueHolderV14();
        // 有问题的接口商品
        List<SgChannelProductStorageBufferDelRequest> errorList = new ArrayList<>();
        //需要删除的接口商品
        List<SgChannelProductStorageBufferDelRequest> delRequestList = new ArrayList<>();
        //平台条码id
        if (CollectionUtils.isNotEmpty(sgChannelProductStorageBufferDelRequestList)) {
            for (SgChannelProductStorageBufferDelRequest sgChannelProductStorageBufferDelRequest : sgChannelProductStorageBufferDelRequestList) {
                if (StringUtils.isEmpty(sgChannelProductStorageBufferDelRequest.getSkuId()) &&
                        StringUtils.isEmpty(sgChannelProductStorageBufferDelRequest.getSkuCode())) {
                    errorList.add(sgChannelProductStorageBufferDelRequest);
                    continue;
                }
                delRequestList.add(sgChannelProductStorageBufferDelRequest);
            }
            if (CollectionUtils.isNotEmpty(errorList)) {
                if (log.isDebugEnabled()) {
                    log.debug(this.getClass().getName() + " 有问题的平台条码集合:" + JSONObject.toJSONString(errorList));
                }
            }
            List<String> skuIdList = delRequestList.stream().filter(x -> StringUtils.isNotEmpty(x.getSkuId())).map(x -> x.getSkuId()).collect(Collectors.toList());
            List<String> skuEcodeList = delRequestList.stream().filter(x -> StringUtils.isNotEmpty(x.getSkuCode())).map(x -> x.getSkuCode()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(skuIdList)) {
                try {
                    //删除渠道缓存池
//                    sgBChannelStorageBufferMapper.delChannelBuffer(skuIdList);
                    //删除渠道库存
//                    sgBChannelStorageMapper.delChannelStorage(skuIdList, skuEcodeList);
                    //删除渠道商品
                    sgBChannelProductMapper.delChannelProduct(skuIdList, skuEcodeList);
                } catch (Exception e) {
                    log.error("{},删除不存在系统条码的渠道信息异常:{};", this.getClass().getName(), Throwables.getStackTraceAsString(e));
                    valueHolder.setCode(ResultCode.FAIL);
                    valueHolder.setMessage("删除不存在系统条码的渠道信息异常");
                    return valueHolder;
                }
            } else {
                valueHolder.setCode(ResultCode.FAIL);
                valueHolder.setMessage("未匹配到可删除的渠道商品");
                return valueHolder;
            }
        } else {
            valueHolder.setCode(ResultCode.FAIL);
            valueHolder.setMessage("平台条码集合为空");
            return valueHolder;
        }
        valueHolder.setCode(ResultCode.SUCCESS);
        valueHolder.setMessage("删除渠道库存，商品，缓存池成功");
        return valueHolder;
    }

    /**
     * OMID-981库存同步未查询到商品，作废平台店铺商品
     *
     * @param request request
     * @return return
     */
    public ValueHolderV14 voidChannelProduct(SgChannelProductVoidRequest request) {
        log.info("Start SgChannelProductSaveService.voidChannelProduct :request={}", JSONObject.toJSONString(request));
        User loginUser = R3SystemUserResource.getSystemRootUser();
        sgBChannelProductMapper.update(null, new LambdaUpdateWrapper<SgBChannelProduct>()
                .eq(SgBChannelProduct::getPsCSkuId, request.getPsCSkuId())
                .eq(SgBChannelProduct::getSkuId, request.getSkuId())
                .set(SgBChannelProduct::getIslock, SgConstants.IS_AUTO_Y)
                .set(SgBChannelProduct::getIsactive, SgConstants.IS_ACTIVE_N)
                .set(SgBChannelProduct::getModifierid, Long.valueOf(loginUser.getId()))
                .set(SgBChannelProduct::getModifieddate, new Date())
                .set(SgBChannelProduct::getModifiername, loginUser.getName())
                .set(SgBChannelProduct::getModifierename, loginUser.getEname()));
        return new ValueHolderV14(ResultCode.SUCCESS, "作废成功");
    }

    /**
     * 根据平台数字id跟平台条码id查询渠道商品信息
     *
     * @param skuIdList
     * @return
     */
    public List<SgBChannelProductQueryRequest> queryChannelProductBySkuid(List<String> skuIdList) {
        return sgBChannelProductMapper.queryChannelProductBySkuid(skuIdList);
    }

    /**
     * 更新渠道商品 同步库存状态、同步库存失败原因、同步库存数量
     */
    public ValueHolderV14 updateChannelProduct(SgChannelProductSaveRequest productSaveRequest) {
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + " Platform SKU collection:{}", JSONObject.toJSONString(productSaveRequest));
        }
        ValueHolderV14 holder = new ValueHolderV14(ResultCode.SUCCESS, "success");
        List<SgBChannelProduct> productList = productSaveRequest.getSgBChannelProductList();
        if (CollectionUtils.isEmpty(productList)) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("平台店铺商品不能为空!");
            return holder;
        }
        //聚合待更新渠道商品 shopid -  <sku_id，List<channelProduct>>
        Map<Long, Map<String, List<SgBChannelProduct>>> channelProductMap = Maps.newHashMap();
        for (SgBChannelProduct channelProduct : productList) {
            Long shopId = channelProduct.getCpCShopId();
            String skuId = channelProduct.getSkuId();
            if (channelProductMap.containsKey(shopId)) {
                Map<String, List<SgBChannelProduct>> valueMap = channelProductMap.get(shopId);
                if (valueMap.containsKey(skuId)) {
                    List<SgBChannelProduct> products = valueMap.get(skuId);
                    products.add(channelProduct);
                } else {
                    List<SgBChannelProduct> products = Lists.newArrayList();
                    products.add(channelProduct);
                    valueMap.put(skuId, products);
                }
            } else {
                Map<String, List<SgBChannelProduct>> valueMap = Maps.newHashMap();
                List<SgBChannelProduct> products = Lists.newArrayList();
                products.add(channelProduct);
                valueMap.put(skuId, products);
                channelProductMap.put(shopId, valueMap);
            }
        }

        List<Long> shopIds = Lists.newArrayList(channelProductMap.keySet());

        if (CollectionUtils.isEmpty(shopIds)) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("平台店铺ID不能为空!");
            return holder;
        }

        Map<Long, SgBChannelProduct> productMap = Maps.newHashMap();

        for (Long shopId : shopIds) {
            Map<String, List<SgBChannelProduct>> stringListMap = channelProductMap.get(shopId);
            List<String> skuids = Lists.newArrayList(stringListMap.keySet());
            if (CollectionUtils.isNotEmpty(skuids)) {
                productMap.putAll(sgBChannelProductMapper.selectList(new LambdaQueryWrapper<SgBChannelProduct>()
                        .eq(SgBChannelProduct::getCpCShopId, shopId).in(SgBChannelProduct::getSkuId, skuids))
                        .stream().collect(Collectors.toMap(SgBChannelProduct::getId, Function.identity())));
            }
        }

        productMap.forEach((id, product) -> {
            //最近一次同步库存数量
            BigDecimal finalSyncNum = product.getFinalSyncNum();
            Date finalSyncTime = product.getFinalSyncTime();
            //同步失败次数
            Integer syncFailedCount = Optional.ofNullable(product.getSyncFailedCount()).orElse(0);

            Map<String, List<SgBChannelProduct>> stringListMap = channelProductMap.get(product.getCpCShopId());
            List<SgBChannelProduct> syncProducts = stringListMap.get(product.getSkuId());
            SgBChannelProduct syncProdct = syncProducts.get(syncProducts.size() - 1);
            Integer syncStatus = syncProdct.getSyncStatus();
            //设置成功或失败状态
            product.setSyncStatus(syncStatus);
            //设置计算后库存
            product.setCalculateNum(syncProdct.getFinalSyncNum());

            User rootUser = R3SystemUserResource.getSystemRootUser();
            StorageUtils.setBModelDefalutDataByUpdate(product, rootUser);
            product.setModifierename(rootUser.getEname());
            product.setFinalSyncTime(new Date());

            if (SgConstantsIF.SYNC_FAIL == syncStatus) {
                syncFailedCount++;
                product.setSyncFailedCount(syncFailedCount);
                product.setSyncFailedReason(syncProdct.getSyncFailedReason().substring(0,
                        Math.min(syncProdct.getSyncFailedReason().length(), SgConstants.SG_COMMON_STRING_SIZE)));

                if (log.isDebugEnabled()) {
                    log.debug("SgChannelStorageOmsSynchService.stock sync void channel product voidKey:{} failedReason:{}", voidKey, syncProdct.getSyncFailedReason());
                }
                try {
                    // 20211125 需求，如果云枢纽返回信息中，包含"商品删除" 字样时，调用作废商品接口
                    String[] split = voidKey.split(";");
                    boolean flag = false;
                    for (String key : split) {
                        if (syncProdct.getSyncFailedReason().contains(key)) {
                            flag = true;
                            break;
                        }
                    }

                    if (flag) {
                        product.setIslock(SgConstants.IS_YES_OR_NO_Y);
                        product.setIsactive(SgConstants.IS_ACTIVE_N);
                    }
                } catch (Exception e) {
                    log.error("SgChannelStorageOmsSynchService.stock sync void channel product error : {}", e.getMessage());
                }

            } else if (SgConstantsIF.SYNC_SUCCESS == syncStatus) {
                product.setFinalSyncNum(syncProdct.getFinalSyncNum());
                product.setSyncFailedCount(0);
                product.setSyncFailedReason("");
                //上次为空，最近一次不为空  或者 上次与最近一次都不为空 则将最近一次 赋值上一次，最新的赋给最近一次
                product.setLastSyncNum(finalSyncNum);
                product.setLastSyncTime(finalSyncTime);
            }
            sgBChannelProductMapper.updateById(product);
        });

        return holder;
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 updateSaleStatus(SgChannelProductSaveRequest channelProductSaveRequest) {
        //入参日志记录
        if (log.isDebugEnabled()) {
            log.debug("Start SgChannelProductSaveService.updateSaleStatus. ReceiveParams:SgChannelProductSaveRequest={};"
                    , JSONObject.toJSONString(channelProductSaveRequest));
        }
        ValueHolderV14 holder;
        //检查入参
        holder = checkServiceParam(channelProductSaveRequest);
        if (ResultCode.FAIL == holder.getCode()) {
            return holder;
        }

        for (SgBChannelProduct sgBChannelProduct : channelProductSaveRequest.getSgBChannelProductList()) {
            LambdaQueryWrapper<SgBChannelProduct> eq = new LambdaQueryWrapper<SgBChannelProduct>()
                    .eq(SgBChannelProduct::getNumiid, sgBChannelProduct.getNumiid())
                    .eq(SgBChannelProduct::getPsCSkuEcode, sgBChannelProduct.getPsCSkuEcode());
            List<SgBChannelProduct> sgBChannelProducts = sgBChannelProductMapper.selectList(eq);
            for (SgBChannelProduct product : sgBChannelProducts) {
                BigDecimal qtyChannel = Objects.isNull(product.getQtyChannel()) ? BigDecimal.ZERO : product.getQtyChannel();
                BigDecimal qtyStorage = Objects.isNull(sgBChannelProduct.getQtyStorage()) ? BigDecimal.ZERO : sgBChannelProduct.getQtyStorage();
                product.setSaleStatus(sgBChannelProduct.getStatus());
                product.setQtyStorage(qtyStorage);
                product.setTransTime(new Date());
                product.setQtyDifferences(qtyChannel.subtract(qtyStorage));
                sgBChannelProductMapper.updateById(product);
            }
        }

        return holder;
    }

    private ValueHolderV14 getResult(List<SgBChannelProduct> channelProductList,
                                     List<SgChannelProductStorageBufferDelRequest> delRequests,
                                     Set<String> errorSet,
                                     int totalNum,
                                     int errorNum) {
        ValueHolderV14 holder = new ValueHolderV14<>(ResultCode.SUCCESS, "成功！");
        if (CollectionUtils.isEmpty(channelProductList) && errorNum != 0 && !delRequests.isEmpty()) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("共" + totalNum + "条数据，失败" + errorNum + "条,删除" + delRequests.size() + "条");
            holder.setData(errorSet);
            return holder;
        }
        if (CollectionUtils.isEmpty(channelProductList) && errorNum != 0) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("共" + totalNum + "条数据，失败" + errorNum + "条");
            holder.setData(errorSet);
            return holder;
        }
        if (CollectionUtils.isEmpty(channelProductList) && !delRequests.isEmpty()) {
            holder.setCode(ResultCode.SUCCESS);
            holder.setMessage("共" + totalNum + "条数据，删除" + delRequests.size() + "条");
            return holder;
        }
        return holder;
    }
}
