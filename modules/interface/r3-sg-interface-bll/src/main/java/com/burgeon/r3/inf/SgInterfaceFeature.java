package com.burgeon.r3.inf;

import com.burgeon.r3.inf.filter.SgCOfflineWarehouseSelectionSaveFilter;
import com.burgeon.r3.inf.filter.SgCOwnStoreSelectionSaveFilter;
import com.burgeon.r3.inf.filter.SgCSmartStoreSelectionSaveFilter;
import com.burgeon.r3.inf.filter.SgCStoreWarehouseSelectionSaveFilter;
import com.burgeon.r3.inf.filter.SgCTransferArrivalDaysConfigSaveFilter;
import com.burgeon.r3.inf.filter.ryytndistribution.SgCDepartmentMonthDemandVoidFilter;
import com.burgeon.r3.inf.filter.ryytndistribution.SgCMonthProductionPlanSaveFilter;
import com.burgeon.r3.inf.filter.ryytndistribution.SgCStoreWarnProductionSaveFilter;
import com.burgeon.r3.inf.filter.ryytndistribution.SgCStoreWarnStorageSaveFilter;
import com.burgeon.r3.inf.filter.ryytndistribution.SgcDeptWarnSaStorageSaveFilter;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.jackrain.nea.tableservice.Feature;
import com.jackrain.nea.tableservice.constants.TableServiceConstants;
import com.jackrain.nea.tableservice.feature.FeatureAnnotation;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @create 2021/11/29 17:05
 */
@FeatureAnnotation(value = "SgInterfaceFeature", description = "InterfaceFeature")
public class SgInterfaceFeature extends Feature {
    @Autowired
    private SgCStoreWarehouseSelectionSaveFilter sgCStoreWarehouseSelectionSaveFilter;
    @Autowired
    private SgCOfflineWarehouseSelectionSaveFilter sgCOfflineWarehouseSelectionSaveFilter;
    @Autowired
    private SgCOwnStoreSelectionSaveFilter sgCOwnStoreSelectionSaveFilter;
    @Autowired
    private SgCSmartStoreSelectionSaveFilter sgCSmartStoreSelectionSaveFilter;

//    @Autowired
//    private SgCDepartmentMonthDemandSaveFilter sgCDepartmentMonthDemandSaveFilter;
@Autowired
private SgCDepartmentMonthDemandVoidFilter sgCDepartmentMonthDemandVoidFilter;
    @Autowired
    private SgCMonthProductionPlanSaveFilter sgCMonthProductionPlanSaveFilter;

    @Autowired
    private SgcDeptWarnSaStorageSaveFilter sgcDeptWarnSaStorageSaveFilter;

    @Autowired
    private SgCStoreWarnStorageSaveFilter sgcStoreWarnStorageSaveFilter;

    @Autowired
    private SgCStoreWarnProductionSaveFilter sgCStoreWarnProductionSaveFilter;

    @Autowired
    private SgCTransferArrivalDaysConfigSaveFilter sgCTransferArrivalDaysConfigSaveFilter;

    @Override
    protected void initialization() {
        //线下自营仓选款
        addFilter(sgCOfflineWarehouseSelectionSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_OFFLINE_WAREHOUSE_SELECTION))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //自营门店选款
        addFilter(sgCOwnStoreSelectionSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_OWN_STORE_SELECTION))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //SmartStore门店选款
        addFilter(sgCSmartStoreSelectionSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_SMART_STORE_SELECTION))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //店仓维度选款
        addFilter(sgCStoreWarehouseSelectionSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_STORE_WAREHOUSE_SELECTION))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //部门月需求提报保存
//        addFilter(sgCDepartmentMonthDemandSaveFilter, (tableName, actionName)
//                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_DEPARTMENT_MONTH_DEMAND))
//                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
//                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //部门月需求提报作废
        addFilter(sgCDepartmentMonthDemandVoidFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_DEPARTMENT_MONTH_DEMAND))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_VOID)));

        //月生产计划
        addFilter(sgCMonthProductionPlanSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_MONTH_PRODUCTION_PLAN))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //  部门配销仓库存告警
        addFilter(sgcDeptWarnSaStorageSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_DEPT_WARN_SA_STORAGE))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)));

        //  逻辑仓库存告警
        addFilter(sgcStoreWarnStorageSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_STORE_WARN_STORAGE))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD) || actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SAVE)));

        //  拆组包监控告警配置
        addFilter(sgCStoreWarnProductionSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_STORE_WARN_PRODUCTION))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SAVE)));

        //  调拨时效配置
        addFilter(sgCTransferArrivalDaysConfigSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_TRANSFER_ARRIVAL_DAYS_CONFIG))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)));
    }
}
