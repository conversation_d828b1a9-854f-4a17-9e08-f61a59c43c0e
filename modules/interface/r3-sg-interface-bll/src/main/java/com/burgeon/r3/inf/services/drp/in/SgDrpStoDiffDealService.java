package com.burgeon.r3.inf.services.drp.in;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.inf.config.SgDrpConfig;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.diff.SgBStoDiff;
import com.burgeon.r3.sg.core.model.table.store.diff.SgBStoDiffItem;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpStoDiffDealRequest;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpStoDiffItemDealRequest;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.diff.SgBStoDiffItemMapper;
import com.burgeon.r3.sg.store.mapper.diff.SgBStoDiffMapper;
import com.burgeon.r3.sg.store.services.diff.SgBStoDiffDealR3Service;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2021-06-21 11:25
 * @Description:
 */

@Slf4j
@Component
public class SgDrpStoDiffDealService {

    @Autowired
    private SgBStoDiffMapper diffMapper;
    @Autowired
    private SgBStoDiffItemMapper itemMapper;
    @Autowired
    private BasicPsQueryService psQueryService;
    @Autowired
    private SgBStoDiffDealR3Service dealR3Service;
    @Autowired
    private SgDrpConfig sgDrpConfig;

    /**
     * drp 调用接口，根据传入的参数，对相应的条码差异处理
     *
     * @param request 入参
     * @return 出参
     */
    public ValueHolderV14 dealStoDiff(SgDrpStoDiffDealRequest request) {
        if (!sgDrpConfig.getEnableDrpSend()) {
            return new ValueHolderV14<>(ResultCode.FAIL, "中台系统未开放,请稍后再试!");
        }

        log.info("Start SgDrpStoDiffDealService.dealStoDiff ReceiveParams:request={};",
                JSONObject.toJSONString(request));

        String lockKsy =
                SgConstants.INTERFACE_DRP + ":" + SgConstants.SG_B_STO_DIFF + ":" + request.getSourceBillNo();
        SgRedisLockUtils.lock(lockKsy);
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "差异处理成功！");
        try {
            ValueHolderV14<List<SgBStoDiffItem>> listValueHolderV14 = checkParam(request);
            if (!listValueHolderV14.isOK() || CollectionUtils.isEmpty(listValueHolderV14.getData())) {
                return listValueHolderV14;
            }
            List<SgBStoDiffItem> items  = listValueHolderV14.getData();
            SgBStoDiff sgStoDiff = diffMapper.selectOne(new QueryWrapper<SgBStoDiff>()
                    .lambda()
                    .eq(SgBStoDiff::getSourceBillNo, request.getSourceBillNo())
                    .eq(SgBStoDiff::getSourceBillType, request.getSourceBillType())
                    .eq(SgBStoDiff::getIsactive, SgConstants.IS_ACTIVE_Y));
            //入库时间
            sgStoDiff.setInTime(request.getInTime());

            v14 = dealR3Service.dealDiff(sgStoDiff, items, DrpUtils.getUser(), Boolean.FALSE);
        } catch (Exception e) {
            log.error("SgDrpStoDiffDealService.dealStoDiff. exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            v14.setMessage(e.getMessage());
            v14.setCode(ResultCode.FAIL);
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        return v14;
    }

    /**
     * 参数校验
     *
     * @param request 入参
     * @return 出参
     */
    private ValueHolderV14<List<SgBStoDiffItem>> checkParam(SgDrpStoDiffDealRequest request) {
        ValueHolderV14<List<SgBStoDiffItem>> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "success!");
        if (Objects.isNull(request)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "参数为空请检查!");
        }
        if (Objects.isNull(request.getSourceBillNo())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "单据编号为空请检查!");
        }
        if (Objects.isNull(request.getSourceBillType())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "单据类型为空请检查!");
        }
        SgBStoDiff sgStoDiff = diffMapper.selectOne(new QueryWrapper<SgBStoDiff>()
                .lambda()
                .eq(SgBStoDiff::getSourceBillNo, request.getSourceBillNo())
                .eq(SgBStoDiff::getSourceBillType, request.getSourceBillType())
                .eq(SgBStoDiff::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (Objects.isNull(request.getSourceBillType())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "根据参数来源单据+来源类型查找差异单未找到单据!");
        }
        if (SgStoreConstants.BILL_STATUS_UNSUBMIT != sgStoDiff.getStatus()){
            return new ValueHolderV14<>(ResultCode.SUCCESS, "当前单据状态,不允许重复操作!");
        }
        List<SgDrpStoDiffItemDealRequest> stoDiffItemDealRequests = request.getStoDiffItemDealRequests();
        if (CollectionUtils.isEmpty(stoDiffItemDealRequests)){
            return new ValueHolderV14<>(ResultCode.FAIL, "明细参数为空!");
        }
        List<String> skuCodes =
                stoDiffItemDealRequests.stream().map(SgDrpStoDiffItemDealRequest::getPsCSkuEcode).collect(Collectors.toList());
        //查询条码档案
        SkuInfoQueryRequest queryRequest = new SkuInfoQueryRequest();
        queryRequest.setSkuEcodeList(skuCodes);
        HashMap<String, PsCProSkuResult> skuMap = psQueryService.getSkuInfoByEcode(queryRequest);
        //查明细
        List<SgBStoDiffItem> items = queryDiffItem(skuCodes, sgStoDiff.getId());
        if (CollectionUtils.isEmpty(items)){
            return new ValueHolderV14<>(ResultCode.FAIL, "所传入所有条码在逻辑差异单明细中不存在!");
        }
        Map<String, SgBStoDiffItem> diffItemMap =
                items.stream().collect(Collectors.toMap(SgBStoDiffItem::getPsCSkuEcode,
                        diffItem -> diffItem, (v1, v2) -> v1));
        for (SgDrpStoDiffItemDealRequest itemDealRequest : stoDiffItemDealRequests) {
            if (Objects.isNull(itemDealRequest.getHandleWay())) {
                return new ValueHolderV14<>(ResultCode.FAIL, "处理方式为空请检查!");
            }
            String psSkuEcode = itemDealRequest.getPsCSkuEcode();
            if (StringUtils.isBlank(psSkuEcode)) {
                return new ValueHolderV14<>(ResultCode.FAIL, "条码为空请检查!");
            }
            if (!skuMap.containsKey(psSkuEcode)) {
                return new ValueHolderV14<>(ResultCode.FAIL, "条码【" + psSkuEcode + "]条码档案中不存在！");
            }
            if (!diffItemMap.containsKey(psSkuEcode)) {
                return new ValueHolderV14<>(ResultCode.FAIL, "条码【" + psSkuEcode + "]差异单明细中不存在！");
            }
            SgBStoDiffItem item = diffItemMap.get(psSkuEcode);
            if (SgStoreConstants.DIFF_HANDLE_STATUS_THREE.equals(item.getHandleStatus())){
                return new ValueHolderV14<>(ResultCode.FAIL, "条码【" + psSkuEcode + "]差异单明细中已经处理！");
            }
            // 后面更新差异处理 用
            item.setHandleWay(itemDealRequest.getHandleWay());
        }
        vh.setData(items);
        return vh;
    }

    /**
     * 根据条码和主表id查差异单明细
     *
     * @param skuCodes 条码编码
     * @param mainId   主表id
     * @return 差异明细
     */
    private List<SgBStoDiffItem> queryDiffItem(List<String> skuCodes, Long mainId) {
        ArrayList<SgBStoDiffItem> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(skuCodes)) {
            List<List<String>> skuPageList =
                    StorageUtils.getPageList(skuCodes, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
            for (List<String> skus : skuPageList) {
                list.addAll(itemMapper.selectList(new QueryWrapper<SgBStoDiffItem>().lambda()
                        .eq(SgBStoDiffItem::getSgBStoDiffId, mainId)
                        .in(SgBStoDiffItem::getPsCSkuEcode, skus)
                        .eq(SgBStoDiffItem::getIsactive, SgConstants.IS_ACTIVE_Y)));
            }
        }
        return list;
    }
}
