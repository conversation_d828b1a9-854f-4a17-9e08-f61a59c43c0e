package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.diff.SgBStoDiff;
import com.burgeon.r3.sg.core.model.table.store.diff.SgBStoDiffItem;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.diff.SgBStoDiffItemMapper;
import com.burgeon.r3.sg.store.mapper.diff.SgBStoDiffMapper;
import com.jackrain.nea.constants.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/23 9:59
 * 差异单处理方式更新
 */

@Slf4j
@Component
public class DrpStoDiffService extends AbstractDrpInterfaceProcessor<SgBStoDiff, SgBStoDiffItem> {

    @Autowired
    SgBStoDiffMapper sgStoDiffMapper;
    @Autowired
    SgBStoDiffItemMapper sgBStoDiffItemMapper;

    private final static String TF = "TF";
    private final static String SA = "SA";
    private final static String SR = "SR";

    /**
     * 发货方补单:DO1
     * 发货方调整:DO2
     * 收货方调整:DO3
     * 确认丢失:DO4
     */
    private final static String HANDLE_WAY_TWO = "DO2";
    private final static String HANDLE_WAY_THREE = "DO3";
    private final static String HANDLE_WAY_FOUR = "DO4";

    private final static Integer Fail = -1;


    /**
     * 主表查询条件
     *
     * @return LambdaQueryWrapper
     */
    @Override
    public LambdaQueryWrapper execMainWrapper() {
        log.info("Start sgBStoDiff queryMain");
        LambdaQueryWrapper<SgBStoDiff> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SgBStoDiff::getIsactive, SgConstants.IS_ACTIVE_Y);

        List<Integer> billTypeList = new ArrayList<>();
        billTypeList.add(SgConstantsIF.BILL_STO_TRANSFER);
        billTypeList.add(SgConstantsIF.BILL_TYPE_TRANSFER);
        billTypeList.add(SgConstantsIF.BILL_TYPE_SALE);
        billTypeList.add(SgConstantsIF.BILL_TYPE_SALE_REF);
        wrapper.in(SgBStoDiff::getSourceBillType, billTypeList);
        wrapper.and(o -> {
            o.isNull(SgBStoDiff::getDrpStatus);
            o.or(oo -> oo.eq(SgBStoDiff::getDrpStatus, SgStoreConstants.SEND_DRP_STATUS_UNDECLARED));
            o.or(oo -> oo.eq(SgBStoDiff::getDrpStatus, SgStoreConstants.SEND_DRP_STATUS_FAIL).lt(SgBStoDiff::getDrpFailCount, failNum));
            return o;
        });
        return wrapper;
    }

    @Override
    public LambdaQueryWrapper<SgBStoDiffItem> execitemWrapper(Long mainId) {
        log.info("Start sgBStoDiff queryItem");
        LambdaQueryWrapper<SgBStoDiffItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SgBStoDiffItem::getIsactive, SgConstants.IS_ACTIVE_Y);
        wrapper.eq(SgBStoDiffItem::getHandleStatus, SgStoreConstants.DIFF_HANDLE_STATUS_THREE);
        wrapper.eq(SgBStoDiffItem::getSgBStoDiffId, mainId);
        wrapper.and(o -> {
            o.isNull(SgBStoDiffItem::getDrpStatus);
            o.or(oo -> oo.eq(SgBStoDiffItem::getDrpStatus, SgStoreConstants.SEND_DRP_STATUS_UNDECLARED));
            o.or(oo -> oo.eq(SgBStoDiffItem::getDrpStatus, ResultCode.FAIL).lt(SgBStoDiffItem::getDrpFailCount, failNum));
            return o;
        });
        return wrapper;
    }


    @Override
    public String drpInterfaceUrl() {
        return "erp.diff.handlenotice";
    }

    @Override
    public String itemMainField() {
        return "sg_b_sto_diff_id";
    }

    @Override
    public String drpStatus() {
        return "DRP_STATUS";
    }

    @Override
    public String drpStatusFailCount() {
        return "DRP_FAIL_COUNT";
    }

    @Override
    public String drpStatusFailReason() {
        return "DRP_FAIL_REASON";
    }

    @Override
    public JSONObject execInterfaceParam(SgBStoDiff diff, List<SgBStoDiffItem> itemList) {

        log.info("Start DrpStoDiffService.execInterfaceParam sgBStoDiff={},itemList={}",
                JSONObject.toJSONString(diff), JSONObject.toJSONString(itemList));

        JSONObject mian = new JSONObject();

        //主表
        mian.put("DOCNO", diff.getSourceBillNo());
        mian.put("BILLDATE", new SimpleDateFormat("yyyyMMdd").format(diff.getBillDate()));
        Integer sourceBillType = diff.getSourceBillType();

        switch (sourceBillType) {
            case SgConstantsIF.BILL_TYPE_TRANSFER:
            case SgConstantsIF.BILL_STO_TRANSFER:
                mian.put("DOCNO_TYPE", TF);
                break;
            case SgConstantsIF.BILL_TYPE_SALE:
                mian.put("DOCNO_TYPE", SA);
                break;
            case SgConstantsIF.BILL_TYPE_SALE_REF:
                mian.put("DOCNO_TYPE", SR);
                break;
            default:
                log.info("来源单据类型：" + sourceBillType);
        }

        //明细
        List<JSONObject> items = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(itemList)) {
            itemList.forEach(item -> {
                JSONObject itemJson = new JSONObject();
                itemJson.put("M_PRODUCTALIAS_NO", item.getPsCSkuEcode());
                Integer handleWay = item.getHandleWay();
                switch (handleWay) {
                    case SgStoreConstants.DIFF_HANDLE_WAY_TWO:
                        itemJson.put("DEAL_TYPE", HANDLE_WAY_TWO);
                        break;
                    case SgStoreConstants.DIFF_HANDLE_WAY_THREE:
                        itemJson.put("DEAL_TYPE", HANDLE_WAY_THREE);
                        break;
                    case SgStoreConstants.DIFF_HANDLE_WAY_FOUR:
                        itemJson.put("DEAL_TYPE", HANDLE_WAY_FOUR);
                        break;
                    default:
                        log.info("差异处理类型：" + handleWay);
                }
                items.add(itemJson);
            });
        }
        mian.put("items", items);
        mian.put("CODE", ResultCode.SUCCESS);
        mian.put("MSG", "");
        return mian;
    }

    @Override
    public void handleBysuccess(SgBStoDiff sgBStoDiff, List<SgBStoDiffItem> z) {

        if (log.isDebugEnabled()) {
            log.debug("Start DrpStoDiffService.handleBysuccess Update details and pass the DRP status");
        }

        //更新明细状态
        List<Long> ids = z.stream().map(SgBStoDiffItem::getId).collect(Collectors.toList());
        sgBStoDiffItemMapper.updateDrpByDiffId(ResultCode.SUCCESS, ids);
    }


}
