package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.migration.profit.SgBPhyProfit;
import com.burgeon.r3.sg.core.model.table.migration.profit.SgBPhyProfitItem;
import com.burgeon.r3.sg.store.common.SgPhyProfitConstants;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.jackrain.nea.util.DateUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName : DrpProfitProcessor  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-08-22 11:06
 * 盘点差异结果
 */
public class DrpProfitProcessor extends AbstractDrpInterfaceProcessor<SgBPhyProfit, SgBPhyProfitItem> {
    @Override
    public LambdaQueryWrapper<SgBPhyProfit> execMainWrapper() {
        LambdaQueryWrapper<SgBPhyProfit> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(SgBPhyProfit::getStatus, SgPhyProfitConstants.PROFIT_BILL_STATUS_UNSUBMIT);
        wrapper.ne(SgBPhyProfit::getStatus, SgPhyProfitConstants.PROFIT_BILL_STATUS_VOID);
        wrapper.eq(SgBPhyProfit::getIsactive, SgConstants.IS_ACTIVE_Y);
        wrapper.and(o -> {
            o.isNull(SgBPhyProfit::getDrpStatus);
            o.or(oo -> oo.eq(SgBPhyProfit::getDrpStatus, SgStoreConstants.SEND_DRP_STATUS_UNDECLARED));
            o.or(oo -> oo.eq(SgBPhyProfit::getDrpStatus, SgStoreConstants.SEND_DRP_STATUS_FAIL).lt(SgBPhyProfit::getDrpFailCount, failNum));
            return o;
        });
        return wrapper;
    }

    @Override
    public LambdaQueryWrapper<SgBPhyProfitItem> execitemWrapper(Long mianId) {
        return null;
    }

    @Override
    public String drpInterfaceUrl() {
        return "erp.Inventory.instructions";
    }

    @Override
    public String itemMainField() {
        return "sg_b_phy_profit_id";
    }

    @Override
    public String drpStatus() {
        return "DRP_STATUS";
    }

    @Override
    public String drpStatusFailCount() {
        return "DRP_FAIL_COUNT";
    }

    @Override
    public String drpStatusFailReason() {
        return "DRP_FAIL_REASON";
    }

    @Override
    public JSONObject execInterfaceParam(SgBPhyProfit sgBPhyProfit, List<SgBPhyProfitItem> itemList) {
        JSONObject mian = new JSONObject();
        mian.put("DOCTYPE", SgDrpConstantsIF.profitBillType.get(sgBPhyProfit.getInventoryType()));
        mian.put("BILLDATE", DateUtil.format(sgBPhyProfit.getBillDate(), "yyyyMMdd"));
        mian.put("ZTDOCNO", sgBPhyProfit.getBillNo());
        mian.put("C_STORE_ID__CODE", sgBPhyProfit.getInventoryStoreEcode());
        mian.put("IS_BAS", "N");

        List<JSONObject> items = new ArrayList<>();
        for (SgBPhyProfitItem item : itemList) {
            JSONObject itemJson = new JSONObject();
            itemJson.put("M_PRODUCTALIAS_ID_NO", item.getPsCSkuEcode());
            itemJson.put("QTY", item.getInventoryQty());
            itemJson.put("QTYBOOK", item.getAccountQty());
            itemJson.put("QTYDIFF", item.getDifferenceQty());
            items.add(itemJson);
        }
        mian.put("items", items);
        return mian;
    }

    @Override
    public void handleBysuccess(SgBPhyProfit sgBPhyProfit, List<SgBPhyProfitItem> z) {

    }
}
