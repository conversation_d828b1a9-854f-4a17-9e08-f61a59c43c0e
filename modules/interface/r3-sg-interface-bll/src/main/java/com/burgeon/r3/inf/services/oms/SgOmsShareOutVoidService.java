package com.burgeon.r3.inf.services.oms;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOut;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOutItem;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOutItemLog;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutResult;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutResultItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutItemLogMapper;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutItemMapper;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutMapper;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutBillReleaseRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutItemReleaseRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutReleaseRequest;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutVoidResult;
import com.burgeon.r3.sg.share.services.out.SgBShareOutReleaseService;
import com.burgeon.r3.sg.share.services.out.SgBShareOutVoidService;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutBillReleaseRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutBillVoidRequest;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutBillVoidResult;
import com.burgeon.r3.sg.store.services.out.SgBStoOutReleaseByEdga;
import com.burgeon.r3.sg.store.services.out.SgBStoOutVoidService;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 【OMS】共享占用单作废服务
 * <AUTHOR>
 * @Date 2021/6/28 14:25
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgOmsShareOutVoidService {

    @Autowired
    private SgBShareOutVoidService sgBShareOutVoidService;

    @Autowired
    private SgBShareOutMapper mapper;

    @Autowired
    private SgBShareOutItemMapper itemMapper;

    @Autowired
    private SgBShareOutItemLogMapper itemLogMapper;

    @Autowired
    private SgBStoOutVoidService sgBStoOutVoidService;

    @Autowired
    private SgBStoOutReleaseByEdga sgBStoOutReleaseByEdga;

    /**
     * @param request:
     * @Description: 包含明细取消和主表取消
     * @Author: hwy
     * @Date: 2021/9/7 10:26
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14
     **/
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 voidSgOmsShareOutV2(SgOmsShareOutRequest request) {
        log.info("Start SgOmsShareOutVoidService.voidSgOmsShareOutV2 param:{};", JSONObject.toJSONString(request));
        ValueHolderV14 valueHolderV14;
        checkSgOmsShareOut(request);
        request.setLoginUser(R3SystemUserResource.getSystemRootUser());
        SgOmsShareOutVoidService bean = ApplicationContextHandle.getBean(SgOmsShareOutVoidService.class);
        if (SgConstantsIF.OMS_STORAGE_OCCUPY_CANCEL_TYPE_ITEM.equals(request.getCancelType())) {
            valueHolderV14 = bean.voidSgOmsShareOutByTime(request);
        } else {
            try {
                valueHolderV14 = bean.voidSgOmsShareOut(request);
            } catch (Exception e) {
                valueHolderV14 = new ValueHolderV14();
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(e.getMessage());
            }
        }
        return valueHolderV14;
    }

    /**
     * @param request:
     * @Description: 通过明细取消库存占用
     * @Author: hwy
     * @Date: 2021/9/1 20:53
     * @return: void
     **/
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 voidSgOmsShareOutByTime(SgOmsShareOutRequest request) {
        ValueHolderV14<Object> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "共享占用单作废服务-作废成功！");
        List<String> redisKeys = new ArrayList<>();
        try {
            List<SgOmsShareOutItemRequest> itemRequestList = request.getItemRequestList();
            if (CollectionUtils.isEmpty(itemRequestList)) {
                AssertUtils.notNull(null, "取消占用类型为明细取消时  明细信息不能为空");
            }
            // 聚合参数
            Map<Long, BigDecimal> itemRequestBySourceIdMap = new HashMap<>();
            Map<Long, BigDecimal> itemRequestBySkuMap = new HashMap<>();
            itemRequestList.stream().forEach(o -> {
                Long sourceItemId = o.getSourceItemId();
                Long psCSkuId = o.getPsCSkuId();
                if (itemRequestBySourceIdMap.containsKey(sourceItemId)) {
                    BigDecimal bigDecimal = itemRequestBySourceIdMap.get(sourceItemId);
                    itemRequestBySourceIdMap.put(sourceItemId, bigDecimal.add(o.getQtyPreout()));
                } else {
                    itemRequestBySourceIdMap.put(sourceItemId, o.getQtyPreout());
                }

                if (itemRequestBySkuMap.containsKey(psCSkuId)) {
                    BigDecimal bigDecimal = itemRequestBySkuMap.get(psCSkuId);
                    itemRequestBySkuMap.put(psCSkuId, bigDecimal.add(o.getQtyPreout()));
                } else {
                    itemRequestBySkuMap.put(psCSkuId, o.getQtyPreout());
                }
            });
            SgOmsShareOutVoidService bean = ApplicationContextHandle.getBean(SgOmsShareOutVoidService.class);
            // 取消共享占用
            ValueHolderV14<List<String>> shareValueHolderV14 = bean.releaseShareOutByItem(request, itemRequestBySourceIdMap, itemRequestBySkuMap);
            if (!shareValueHolderV14.isOK()) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(shareValueHolderV14.getMessage());
            }
            if (CollectionUtils.isNotEmpty(shareValueHolderV14.getData())) {
                redisKeys.addAll(shareValueHolderV14.getData());
            }
            // 取消逻辑占用
            List<String> stoOutRedisKey = bean.releaseStoOutByItem(request, itemRequestBySkuMap, redisKeys);
            if (CollectionUtils.isNotEmpty(stoOutRedisKey)) {
                redisKeys.addAll(stoOutRedisKey);
            }
        } catch (Exception e) {
            log.error("SgOmsShareOutVoidService.voidSgOmsShareOutByTime 释放占用发生异常:{}", Throwables.getStackTraceAsString(e));
            StorageBasicUtils.rollbackStorage(redisKeys, request.getLoginUser());
            throw new NDSException("释放占用发生异常");
        }
        return valueHolderV14;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<String> releaseStoOutByItem(SgOmsShareOutRequest request, Map<Long, BigDecimal> itemRequestBySkuMap, List<String> redisKeys) {
        SgBStoOutBillReleaseRequest stoReleaseRequest = new SgBStoOutBillReleaseRequest();
        SgBStoOutResult sgBStoOutResult = new SgBStoOutResult();
        sgBStoOutResult.setSourceBillId(request.getSourceBillId());
        sgBStoOutResult.setSourceBillNo(request.getSourceBillNo());
        sgBStoOutResult.setSourceBillType(request.getSourceBillType());
        stoReleaseRequest.setOutResult(sgBStoOutResult);
        stoReleaseRequest.setLoginUser(request.getLoginUser());
        List<SgBStoOutResultItem> outResultItemList = new ArrayList<>();
        Set<Map.Entry<Long, BigDecimal>> entries = itemRequestBySkuMap.entrySet();
        for (Map.Entry<Long, BigDecimal> entry : entries) {
            Long psCSkuId = entry.getKey();
            BigDecimal qtyRelease = entry.getValue();
            SgBStoOutResultItem sgBStoOutResultItem = new SgBStoOutResultItem();
            sgBStoOutResultItem.setPsCSkuId(psCSkuId);
            sgBStoOutResultItem.setQty(qtyRelease);
            outResultItemList.add(sgBStoOutResultItem);
        }
        stoReleaseRequest.setOutResultItemList(outResultItemList);
        ValueHolderV14<SgBStoOutBillReleaseRequest> valueHolderV14 = sgBStoOutReleaseByEdga.releaseSgBStoOutByEdga(stoReleaseRequest);
        SgBStoOutBillReleaseRequest stoResult = valueHolderV14.getData();
        if (!valueHolderV14.isOK()) {
            log.error("SgOmsShareOutVoidService.releaseStoOutByItem 释放逻辑占用失败:{}", valueHolderV14.getMessage());
            if (stoResult != null && CollectionUtils.isNotEmpty(stoResult.getRedisBillFtpKeyList())) {
                redisKeys.addAll(stoResult.getRedisBillFtpKeyList());
            }
            throw new NDSException("释放逻辑占用失败");
        }
        return redisKeys;
    }


    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<List<String>> releaseShareOutByItem(SgOmsShareOutRequest request, Map<Long, BigDecimal> itemRequestBySourceIdMap, Map<Long, BigDecimal> itemRequestBySkuMap) {
        ValueHolderV14<List<String>> releaseShareResult = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        SgBShareOutReleaseService releaseService = ApplicationContextHandle.getBean(SgBShareOutReleaseService.class);
        List<SgBShareOut> sgBShareOuts = checkSgShareOuts(request);
        if (CollectionUtils.isEmpty(sgBShareOuts)) {
            return releaseShareResult;
        }
        List<Long> shareStoreIds = sgBShareOuts.stream().map(SgBShareOut::getId).distinct().collect(Collectors.toList());
        List<SgBShareOutItem> sgBShareOutItems = itemMapper.selectList(new QueryWrapper<SgBShareOutItem>().lambda().in(SgBShareOutItem::getSgBShareOutId, shareStoreIds));
        Map<Long, List<SgBShareOutItem>> itemMap = sgBShareOutItems.stream().collect(Collectors.groupingBy(SgBShareOutItem::getSgBShareOutId));
        //获取所有日志明细
        List<SgBShareOutItemLog> sgBShareOutItemLogs = itemLogMapper.selectList(Wrappers.<SgBShareOutItemLog>query().lambda()
                .in(SgBShareOutItemLog::getSgBShareOutId,shareStoreIds).eq(SgBShareOutItemLog::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isEmpty(sgBShareOutItemLogs)) {
            sgBShareOutItemLogs = new ArrayList<>();
        }
        Map<Long, List<SgBShareOutItemLog>> itemLogMap = sgBShareOutItemLogs.stream().collect(Collectors.groupingBy(SgBShareOutItemLog::getSgBShareOutId));

        //新加  获取原始的来源明细 时效订单id 主键
        List<Long> sourceItemIds = request.getItemRequestList().stream().map(SgOmsShareOutItemRequest::getSourceItemId).collect(Collectors.toList());
        for (SgBShareOut sgBShareOut : sgBShareOuts) {
            Long id = sgBShareOut.getId();
            SgBShareOutBillReleaseRequest billReleaseRequest = new SgBShareOutBillReleaseRequest();
            ArrayList<SgBShareOutItemReleaseRequest> itemReleaseList = new ArrayList<>();
            SgBShareOutReleaseRequest sgBShareOutReleaseRequest = new SgBShareOutReleaseRequest();
            billReleaseRequest.setShareOutReleaseRequest(sgBShareOutReleaseRequest);
            billReleaseRequest.setShareOutItemReleaseRequests(itemReleaseList);
            sgBShareOutReleaseRequest.setSourceBillid(sgBShareOut.getSourceBillId());
            sgBShareOutReleaseRequest.setSourceBillNo(sgBShareOut.getSourceBillNo());
            sgBShareOutReleaseRequest.setSgCShareStoreId(sgBShareOut.getSgCShareStoreId());
            billReleaseRequest.setLoginUser(request.getLoginUser());
            billReleaseRequest.setIsUpdateOutqty(Boolean.TRUE);
            if (!itemMap.containsKey(id)) {
                continue;
            }
            if (SgConstantsIF.BILL_TYPE_FOR_WAREHOUSE == request.getSourceBillType()) {
                //按明细取消   新加逻辑  寻仓单
                if (CollectionUtils.isEmpty(sourceItemIds) || !sourceItemIds.contains(sgBShareOut.getSourceBillId())) {

                    continue;
                }
            }

            List<SgBShareOutItemLog> updateItemLogList = new ArrayList<>();
            //判断合并标记
            Boolean mergeMark = sgBShareOut.getMergeMark();
            List<SgBShareOutItem> currOutItems = itemMap.get(id);
            for (SgBShareOutItem currOutItem : currOutItems) {
                Long sourceBillItemId = currOutItem.getSourceBillItemId();
                Long psCSkuId = currOutItem.getPsCSkuId();
                String psCSkuEcode = currOutItem.getPsCSkuEcode();
                BigDecimal releaseQty = null;
                if (SgConstantsIF.BILL_TYPE_FOR_WAREHOUSE == request.getSourceBillType()) {
                    releaseQty = itemRequestBySkuMap.get(psCSkuId);
                    if (releaseQty != null && BigDecimal.ZERO.compareTo(releaseQty) == 0 && mergeMark) {
                        //合并后该来源明细可能被合并掉不存在
                        //从日志明细中查找
                        List<SgBShareOutItemLog> itemLogs = itemLogMap.get(id);
                        Map<Long, List<SgBShareOutItemLog>> skuMap = itemLogs.stream().collect(Collectors.groupingBy(SgBShareOutItemLog::getPsCSkuId));
                        itemLogs = skuMap.get(psCSkuId);
                        BigDecimal actReleaseQty = releaseQty;
                        if (!CollectionUtils.isEmpty(itemLogs)) {
                            for (SgBShareOutItemLog itemLog : itemLogs) {
                                BigDecimal qtyPreout = itemLog.getQtyPreout();
                                if (actReleaseQty.compareTo(qtyPreout) >= 0) {
                                    actReleaseQty = actReleaseQty.subtract(qtyPreout);
                                    itemLog.setQtyPreout(BigDecimal.ZERO);
                                } else {
                                    itemLog.setQtyPreout(qtyPreout.subtract(actReleaseQty));
                                }
                                updateItemLogList.add(itemLog);
                            }
                        }
                    }
                } else {
                    releaseQty = itemRequestBySourceIdMap.get(sourceBillItemId);
                    if (mergeMark) {
                        //合并后该来源明细可能被合并掉不存在
                        //从日志明细中查找
                        if (ObjectUtils.isEmpty(releaseQty) || BigDecimal.ZERO.compareTo(releaseQty) == 0) {
                            List<SgBShareOutItemLog> itemLogs = itemLogMap.get(id);
                            Map<Long, List<SgBShareOutItemLog>> skuMap = itemLogs.stream().collect(Collectors.groupingBy(SgBShareOutItemLog::getPsCSkuId));
                            itemLogs = skuMap.get(psCSkuId);
                            if (!CollectionUtils.isEmpty(itemLogs)) {
                                for (SgBShareOutItemLog itemLog : itemLogs) {
                                    releaseQty = itemRequestBySourceIdMap.get(itemLog.getSourceBillItemId());
                                    if (!ObjectUtils.isEmpty(releaseQty) && BigDecimal.ZERO.compareTo(releaseQty) != 0) {
                                        BigDecimal qtyPreout = itemLog.getQtyPreout();
                                        if (releaseQty.compareTo(qtyPreout) >= 0) {
                                            releaseQty = qtyPreout;
                                            itemLog.setQtyPreout(BigDecimal.ZERO);
                                        } else {
                                            itemLog.setQtyPreout(qtyPreout.subtract(releaseQty));
                                        }
                                        updateItemLogList.add(itemLog);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                if (ObjectUtils.isEmpty(releaseQty)) {
                    releaseQty = BigDecimal.ZERO;
                }
                if (releaseQty.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                BigDecimal qtyPreout = currOutItem.getQtyPreout();
                BigDecimal currReleaseQty = BigDecimal.ZERO;
                if (releaseQty.compareTo(qtyPreout) >= 0) {
                    currReleaseQty = qtyPreout;
                } else {
                    currReleaseQty = releaseQty;
                }
                SgBShareOutItemReleaseRequest sgBShareOutItemReleaseRequest = new SgBShareOutItemReleaseRequest();
                sgBShareOutItemReleaseRequest.setQty(currReleaseQty);
                sgBShareOutItemReleaseRequest.setSkuId(psCSkuId);
                sgBShareOutItemReleaseRequest.setSkuEcode(psCSkuEcode);
                itemReleaseList.add(sgBShareOutItemReleaseRequest);
            }
            if (CollectionUtils.isNotEmpty(updateItemLogList)) {
                //更新日志
                for (SgBShareOutItemLog sgBShareOutItemLog : updateItemLogList) {
                    itemLogMapper.update(sgBShareOutItemLog, Wrappers.<SgBShareOutItemLog>lambdaUpdate()
                            .set(SgBShareOutItemLog::getQtyPreout, sgBShareOutItemLog.getQtyPreout())
                            .eq(SgBShareOutItemLog::getId, sgBShareOutItemLog.getId()));
                }
            }
            releaseShareResult = releaseService.releaseShareOut(billReleaseRequest);
            List<String> resultData = releaseShareResult.getData();
            if (!releaseShareResult.isOK()) {
                log.error("com.burgeon.r3.inf.services.oms.SgOmsShareOutVoidService.voidSgOmsShareOutByTime 释放共享占用失败:{}", releaseShareResult.getMessage());
                StorageBasicUtils.rollbackStorage(resultData, request.getLoginUser());
                return releaseShareResult;
            }
        }
        return releaseShareResult;
    }


    /**
     * OMS 共享占用单作废服务
     *
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 voidSgOmsShareOut(SgOmsShareOutRequest request) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "共享占用单作废服务-作废成功！");
        log.info("Start SgOmsShareOutVoidService.voidSgOmsShareOut ReceiveParams:getSourceBillNo={},getSourceBillId={},getSourceBillType={};", request.getSourceBillNo(), request.getSourceBillId(), request.getSourceBillType());

        //参数校验
        //User user = getUser();
        User user = request.getLoginUser();
        if (user == null) {
            user = SystemUserResource.getRootUser();
            request.setLoginUser(user);
        }
        checkSgOmsShareOut(request);

        //保存报错时redis中需要回滚的数据
        List<String> redisBillFtpKeyList = new ArrayList<>();
        try {
            List<SgBShareOut> sgBShareOuts = checkSgShareOuts(request);
            if (CollectionUtils.isEmpty(sgBShareOuts)) {
                return v14;
            }
            //调用共享占用单作废服务
            ValueHolderV14<SgBShareOutVoidResult> result = sgBShareOutVoidService.voidShareOut(sgBShareOuts, request.getLoginUser());
            if (result.getCode() == ResultCode.FAIL) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(result.getMessage());
                return v14;
            }
            if (result.getData() != null && CollectionUtils.isNotEmpty(result.getData().getRedisKey())) {
                redisBillFtpKeyList.addAll(result.getData().getRedisKey());
            }
            // 寻仓单 共享层占用来源单据信息与逻辑仓不一致 需要转换

            //TODO:是所有类型的都要去作废逻辑占用单？
            for (SgBShareOut sgBShareOut : sgBShareOuts) {
                if (SgConstantsIF.BILL_TYPE_FOR_WAREHOUSE == request.getSourceBillType()) {
                    sgBShareOut.setSourceBillId(request.getSourceBillId());
                    sgBShareOut.setSourceBillType(request.getSourceBillType());
                    sgBShareOut.setSourceBillNo(request.getSourceBillNo());
                }
                ValueHolderV14<SgBStoOutBillVoidResult> resultValueHolderV14 = voidSgOmsStoOut(sgBShareOut, request.getLoginUser(), redisBillFtpKeyList);
                if (resultValueHolderV14.getData() != null &&
                        CollectionUtils.isNotEmpty(resultValueHolderV14.getData().getRedisBillFtpKeyList())) {
                    redisBillFtpKeyList.addAll(resultValueHolderV14.getData().getRedisBillFtpKeyList());
                }
                if (!resultValueHolderV14.isOK()) {
                    AssertUtils.logAndThrow("逻辑占用单作废失败，" + resultValueHolderV14.getMessage());
                }
            }
        } catch (Exception e) {
            // 回滚库存
            StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, user);
            log.error("SgOmsShareOutVoidService.voidSgOmsShareOut. error:{}", Throwables.getStackTraceAsString(e));
//            return new ValueHolderV14<>(ResultCode.FAIL, SgConstants.MESSAGE_STATUS_FAIL + ":" + e.getMessage());
            AssertUtils.logAndThrow("逻辑占用单作废异常，" + e.getMessage());
        }
        return v14;
    }

    /**
     * 作废逻辑占用单
     *
     * @param sgBShareOut         共享占用单信息
     * @param user                登录用户
     * @param redisBillFtpKeyList redis key键
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgBStoOutBillVoidResult> voidSgOmsStoOut(SgBShareOut sgBShareOut, User user, List<String> redisBillFtpKeyList) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgOmsShareOutVoidService.voidSgOmsStoOut ReceiveParams:sgBShareOut={};", JSONObject.toJSONString(sgBShareOut));
        }
        ValueHolderV14<SgBStoOutBillVoidResult> billVoidResultValueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "逻辑占用单作废成功！");
        SgBStoOutBillVoidRequest voidRequest = new SgBStoOutBillVoidRequest();
        voidRequest.setLoginUser(user);
        voidRequest.setShareId(sgBShareOut.getId());
        voidRequest.setSourceBillNo(sgBShareOut.getSourceBillNo());
        voidRequest.setRedisBillFtpKeyList(redisBillFtpKeyList);
        SgBShareOut update = new SgBShareOut();
        update.setId(sgBShareOut.getId());
        try {
            billVoidResultValueHolderV14 = sgBStoOutVoidService.voidSgBStoOutByShare(voidRequest);
            if (log.isDebugEnabled()) {
                log.debug("end SgBStoOutVoidService.voidSgBStoOutByShare billVoidResultValueHolderV14={}", JSONObject.toJSONString(billVoidResultValueHolderV14));
            }
            if (!billVoidResultValueHolderV14.isOK()) {
                AssertUtils.logAndThrow("逻辑占用单作废失败" + billVoidResultValueHolderV14.getMessage());
            }
            update.setStoOutVoidStatus(ResultCode.SUCCESS);
            update.setStoOutVoidFailReason("");
            StorageUtils.setBModelDefalutDataByUpdate(update, R3SystemUserResource.getSystemRootUser());
            mapper.updateById(update);

        } catch (Exception ex) {
            // 回滚库存
            StorageBasicUtils.rollbackStorage(billVoidResultValueHolderV14.getData().getRedisBillFtpKeyList(), user);
            return new ValueHolderV14<>(ResultCode.FAIL, "逻辑占用单作废失败" + SgConstants.MESSAGE_STATUS_FAIL + ":" + ex.getMessage());
        }
        return billVoidResultValueHolderV14;
    }

    /**
     * 校验接口入参参数
     *
     * @param request
     */
    private void checkSgOmsShareOut(SgOmsShareOutRequest request) {
        AssertUtils.notNull(request.getLoginUser(), "用户未登录！");
        AssertUtils.notNull(request.getSourceBillId(), "来源单据ID不能为空！");
        AssertUtils.notNull(request.getSourceBillType(), "单据类型不能为空！");
        AssertUtils.notNull(request.getSourceBillNo(), "来源单据编号为空！");
        //AssertUtils.notNull(request.getItemRequestList(), "明细信息不能为空！");
        if (CollectionUtils.isNotEmpty(request.getItemRequestList())) {
            request.getItemRequestList().stream().forEach(item -> {
                String psCSkuEcode = item.getPsCSkuEcode();
                AssertUtils.notNull(psCSkuEcode, "条码编码不能为空！");
                AssertUtils.notNull(item.getQtyPreout(), "占用数不能为空！");
                AssertUtils.notNull(item.getPsCSkuId(), "[" + psCSkuEcode + "]条码ID不能为空！");
            });
        }
    }


    /**
     * 校验来源单据信息是否存在
     *
     * @param request
     * @return
     */
    private List<SgBShareOut> checkSgShareOuts(SgOmsShareOutRequest request) {
        List<SgBShareOut> sgShareOuts = null;
        if (SgConstantsIF.BILL_TYPE_FOR_WAREHOUSE == request.getSourceBillType()) {
            sgShareOuts = mapper.selectList(new QueryWrapper<SgBShareOut>()
                    .lambda()
                    .eq(SgBShareOut::getTid, request.getTid())
                    .eq(SgBShareOut::getIsactive, SgConstants.IS_ACTIVE_Y));
        } else {
            sgShareOuts = mapper.selectList(new QueryWrapper<SgBShareOut>()
                    .lambda()
                    .eq(SgBShareOut::getSourceBillId, request.getSourceBillId())
                    .eq(SgBShareOut::getSourceBillNo, request.getSourceBillNo())
                    .eq(SgBShareOut::getSourceBillType, request.getSourceBillType())
                    .eq(SgBShareOut::getIsactive, SgConstants.IS_ACTIVE_Y));
        }
        if (CollectionUtils.isEmpty(sgShareOuts)) {
            return null;
        }
        if (log.isDebugEnabled()) {
            log.debug("Start SgOmsShareOutVoidService.voidSgOmsShareOut ReceiveParams:sgShareOuts={};", JSONObject.toJSONString(sgShareOuts));
        }
        List<SgBShareOut> shareOuts = sgShareOuts.stream().filter(out -> (SgShareConstants.SHARE_OUT_BILL_STATUS_PART_OUT == out.getBillStatus()
                || SgShareConstants.SHARE_OUT_BILL_STATUS_ALL_OUT == out.getBillStatus()
        )).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(shareOuts)) {
            AssertUtils.logAndThrow("部分单据状态，不允许作废");
        }
        return sgShareOuts;
    }


}
