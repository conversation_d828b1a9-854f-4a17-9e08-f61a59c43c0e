package com.burgeon.r3.inf.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.r3.inf.handler.CallBackApi;
import com.burgeon.r3.sg.core.utils.ApplicationContextProvider;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * WMS 回执消息处理
 * topic：BJ_PRO_R3_IPCS_CALLBACK_TO_SG
 * tag:ipcs_to_sg_qimen_callback
 * groupId:GID_BJ_PRO_R3_SG
 *
 * <AUTHOR>
 * @time 2019/5/6 10:23
 */
@Slf4j
@Component
@RocketMqMessageListener(name = "WMSCallMessageRouteListener", type = MqTypeEnum.DEFAULT)
public class WMSCallMessageRouteListener implements BaseMessageListener {

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        String method;
        try {
            if (log.isInfoEnabled()) {
                log.info(LogUtil.format("接收wms回执mq消息 messageBody:{},messageKey:{},messageTag:{},messageTopic:{}",
                        "接收wms回执mq消息"), messageBody, messageKey, messageTag, messageTopic);
            }
            ValueHolderV14 v14 = getAction(messageBody);
            if (v14.isOK()) {
                return;
            }
            throw new NDSException(v14.getMessage());
        } catch (Exception e) {
            log.error(LogUtil.format("WMSCallMessageRouteListener.consume.error：{}"), Throwables.getStackTraceAsString(e));
            throw e;
        }
    }

    public ValueHolderV14 getAction(String messageBody) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.FAIL, null);
        String method = null;
        JSONObject result = JSONObject.parseObject(messageBody);
        method = (String) result.get("method");
        CallBackApi callBackApi = ApplicationContextProvider.getBean(method, CallBackApi.class);
        if (StringUtils.isEmpty(callBackApi)) {
            log.error(LogUtil.format("服务暂未开放,methed.parameter-error,method:{}"), method);
            v14.setMessage("服务暂未开放,methed.parameter-error,method:" + method);
        }
        ValueHolderV14 valueHolderV14 = callBackApi.apiProcess(messageBody);
        if (valueHolderV14.isOK()) {
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("返回成功" + method);
            return v14;
        } else {
            v14.setMessage("接收wms回执mq消息失败,methed.parameter-error,method:" + method);
            log.error(LogUtil.format("接收wms回执mq消息失败,result:{},method：{}"), JSON.toJSONString(valueHolderV14), method);
            return valueHolderV14;
        }
    }
}
