package com.burgeon.r3.inf.services.wms.in;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.in.SgBWmsToStoreprocessConfirmResult;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.in.SgBWmsToStoreprocessConfirmResultMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * Description: 仓内加工单  根据出库单号进行去重
 *
 * @Author: guo.kw
 * @Since: 2022/7/21
 * create at: 2022/7/21 14:00
 */
@Slf4j
@Component
public class WmsBackStoreprocessConfirmRequest {

    @Autowired
    private SgBWmsToStoreprocessConfirmResultMapper sgBWmsToStoreprocessConfirmResultMapper;
    @Autowired
    private CpCPhyWarehouseMapper cpCPhyWarehouseMapper;

    public ValueHolderV14<String> apiProcess(String msg) {
        log.info("WmsBackStoreprocessConfirmRequest apiProcess msg={}", msg);
        CusRedisTemplate<Object, Object> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();
        String lockKsy = SgConstants.SG_BILL_LOCK_WMSRETURN;

        try {
            JSONObject request = JSONObject.parseObject(msg);

            // 出库通知单号
            String noticesBillNo = request.getString("processOrderCode");
            // WMS单据编号
            String wmsBillCode = request.getString("processOrderId");
            // 外部业务编码
            String outBizCode = request.getString("outBizCode");

            lockKsy += outBizCode;

            Boolean ifAbsent = redisMasterTemplate.opsForValue().setIfAbsent(lockKsy, "OK");

            SgBWmsToStoreprocessConfirmResult sgBWmsToStoreprocessConfirmResult = sgBWmsToStoreprocessConfirmResultMapper.selectOne(new LambdaQueryWrapper<SgBWmsToStoreprocessConfirmResult>()
                    .eq(SgBWmsToStoreprocessConfirmResult::getOutBizCode, outBizCode)
                    .eq(SgBWmsToStoreprocessConfirmResult::getIsactive, "Y"));

            if (Objects.nonNull(sgBWmsToStoreprocessConfirmResult) || ifAbsent == null || !ifAbsent) {
                log.error(LogUtil.format("仓内加工单WMS回传重复.messageBody=", "仓内加工单WMS回传重复", sgBWmsToStoreprocessConfirmResult), msg);
            } else {
                redisMasterTemplate.expire(lockKsy, 30, TimeUnit.SECONDS);

                SgBWmsToStoreprocessConfirmResult sgBWmsToStoreprocessConfirmResult1 = new SgBWmsToStoreprocessConfirmResult();
                sgBWmsToStoreprocessConfirmResult1.setId(ModelUtil.getSequence(SgConstants.SG_B_WMS_TO_STOREPROCESS_CONFIRM_RESULT));
                sgBWmsToStoreprocessConfirmResult1.setNoticesBillNo(noticesBillNo);
                sgBWmsToStoreprocessConfirmResult1.setMessage(msg);
                sgBWmsToStoreprocessConfirmResult1.setWmsBillCode(wmsBillCode);
                sgBWmsToStoreprocessConfirmResult1.setOutBizCode(outBizCode);

                sgBWmsToStoreprocessConfirmResult1.setWmsWarehouseType(ThirdWmsTypeEnum.QMWMS.getCode());

                log.info("wms加工信息回传 msg ===> {}", msg);
                JSONObject msgJSON = JSONObject.parseObject(msg);
                JSONObject extendProps = msgJSON.getJSONObject("extendProps");
                Optional.ofNullable(extendProps)
                        .ifPresent(o -> {
                            String storageLocation = o.getString("storageLocation");
                            if (StringUtils.isNotBlank(storageLocation)) {
                                String wmsWarehouseType = cpCPhyWarehouseMapper.selectWmsTypeByECode(storageLocation);
                                sgBWmsToStoreprocessConfirmResult1.setWmsWarehouseType(wmsWarehouseType);
                            }
                        });
                sgBWmsToStoreprocessConfirmResult1.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT);
                sgBWmsToStoreprocessConfirmResult1.setFailedCount(NumberUtils.INTEGER_ZERO);
                sgBWmsToStoreprocessConfirmResult1.setIsactive(SgConstants.IS_ACTIVE_Y);
                StorageUtils.setBModelDefalutData(sgBWmsToStoreprocessConfirmResult1, R3SystemUserResource.getSystemRootUser());
                sgBWmsToStoreprocessConfirmResultMapper.insert(sgBWmsToStoreprocessConfirmResult1);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("仓内加工单WMS回传异常={}", "仓内加工单WMS回传异常"), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        } finally {
            redisMasterTemplate.delete(lockKsy);
        }
        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("success"));
    }

}
