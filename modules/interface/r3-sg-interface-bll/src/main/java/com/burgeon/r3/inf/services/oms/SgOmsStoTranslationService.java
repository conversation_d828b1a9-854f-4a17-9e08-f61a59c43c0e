package com.burgeon.r3.inf.services.oms;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.SgBSaStoragePreoutFtpMapper;
import com.burgeon.r3.sg.basic.mapper.SgBSpStoragePreoutFtpMapper;
import com.burgeon.r3.sg.basic.mapper.SgBStorageSharedPreoutFtpMapper;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgBSaStoragePreoutFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgBSpStoragePreoutFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorageSharedPreoutFtp;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOut;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOutItem;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOut;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItem;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItemLog;
import com.burgeon.r3.sg.core.model.tableExtend.SgBChannelStorageBufferExtend;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoTranslationBillRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoTranslationRequest;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutItemMapper;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutMapper;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutItemLogMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/3 15:49
 * 库存占用平移Service
 */
@Slf4j
@Component
public class SgOmsStoTranslationService {
    @Autowired
    private SgBShareOutMapper sgShareOutMapper;
    @Autowired
    private SgBShareOutItemMapper sgShareOutItemMapper;
    @Autowired
    private SgBStoOutMapper sgStoOutMapper;
    @Autowired
    private SgBStoOutItemMapper sgStoOutItemMapper;
    @Autowired
    private SgBSpStoragePreoutFtpMapper sgSpStoragePreoutFtpMapper;
    @Autowired
    private SgBSaStoragePreoutFtpMapper sgSaStoragePreoutFtpMapper;
    @Autowired
    private SgBStorageSharedPreoutFtpMapper sgStorageSharedPreoutFtpMapper;
    @Autowired
    private SgBStoOutItemLogMapper sgBStoOutItemLogMapper;

    /**
     * 库存平移服务
     *
     * @param request 请求参数
     * @return ValueHolderV14
     */
    public ValueHolderV14 stoTranslation(SgOmsStoTranslationRequest request) {

        log.info("SgOmsStoTranslationService.stoTranslation request:{}", JSONObject.toJSONString(request));

        ValueHolderV14 v14 = new ValueHolderV14<>();
        List<SgOmsStoTranslationBillRequest> originalOrders = request.getOriginalOrders();
        SgOmsStoTranslationBillRequest newOrder = request.getNewOrder();
        User user = new UserImpl();
        BeanUtils.copyProperties(request.getLoginUser(), user);
        try {
            SgOmsStoTranslationService service = ApplicationContextHandle.getBean(SgOmsStoTranslationService.class);
            v14 = service.merge(originalOrders, newOrder, user);
        } catch (Exception e) {
            log.error("SgOmsStoTranslationService stoTranslation error:{}", Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("库存平移失败！" + e.getMessage());
        }

        log.info("SgOmsStoTranslationService.stoTranslation return:{}", JSONObject.toJSONString(v14));

        return v14;
    }

    /**
     * 合并逻辑
     *
     * @param originalOrders 需要合并的零售单信息集合
     * @param newOrder       新零售单信息
     * @param user           用户
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 merge(List<SgOmsStoTranslationBillRequest> originalOrders,
                                SgOmsStoTranslationBillRequest newOrder, User user) {
        checkParam(originalOrders, newOrder);
        List<Long> originalOrderIds =
                originalOrders.stream().map(SgOmsStoTranslationBillRequest::getId).collect(Collectors.toList());
        AssertUtils.cannot(CollectionUtils.isEmpty(originalOrderIds), "原零售发货单ID不能为空！");
        //共享占用单处理逻辑
        mergeShareOutMainAndItems(originalOrderIds, newOrder, user);
        //逻辑占用单处理逻辑
        mergeStoOutMainAndItems(originalOrderIds, newOrder, user);
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("库存平移成功！"));
    }

    /**
     * 合并共享占用单主表 明细表逻辑
     *
     * @param originalOrderIds 合并的零售发货单id集合
     * @param newOrder         新零售发货单信息
     * @param user             用户
     */
    private void mergeShareOutMainAndItems(List<Long> originalOrderIds,
                                           SgOmsStoTranslationBillRequest newOrder, User user) {
        //获取原零售单单据id集合 找到未被作废的共享占用单
        List<SgBShareOut> sgShareOuts =
                sgShareOutMapper.selectList(new LambdaQueryWrapper<SgBShareOut>()
                        .in(SgBShareOut::getSourceBillId, originalOrderIds)
                        .eq(SgBShareOut::getSourceBillType, SgConstantsIF.BILL_TYPE_RETAIL)
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        Map<Long, SgOmsStoTranslationBillRequest.Item> itemIdMap = getItemIdMap(newOrder);
        if (CollectionUtils.isNotEmpty(sgShareOuts)) {
            //共享占用单新增明细集合
            List<SgBShareOutItem> insertItemsAll = new ArrayList<>();
            //共享占用单新增集合
            List<SgBShareOut> insertMainList = new ArrayList<>();
            //共享占用单新增配销仓占用流水集合
            List<SgBSaStoragePreoutFtp> insertSaStoragePreoutFtps = new ArrayList<>();

            //根据聚合仓分组
            Map<Long, List<SgBShareOut>> groupMap =
                    sgShareOuts.stream().collect(Collectors.groupingBy(SgBShareOut::getSgCShareStoreId));
            List<Long> updateIds = sgShareOuts.stream().map(SgBShareOut::getId).collect(Collectors.toList());

            /*逐个聚合仓处理*/
            for (Map.Entry<Long, List<SgBShareOut>> entry : groupMap.entrySet()) {
                List<SgBShareOut> shareOuts = entry.getValue();
                //被合并共享占用单的id集合
                List<Long> mergeIds = shareOuts.stream().map(SgBShareOut::getId).collect(Collectors.toList());
                //合并新单据信息
                SgBShareOut insertMain = setShareData(newOrder, shareOuts, user);
                //需要合并共享占用单明细集合
                List<SgBShareOutItem> oldItems = sgShareOutItemMapper.selectList(new LambdaQueryWrapper<SgBShareOutItem>()
                        .in(SgBShareOutItem::getSgBShareOutId, mergeIds)
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
                //新旧共享占用单明细id  用于获取对应占用流水
                Map<String, Long> shareOutItemIdMap = new HashMap<>(16);
                /*获取新的占用明细列表*/
                List<SgBShareOutItem> insertItems = mergeShareOutItems(insertMain.getId(), oldItems, itemIdMap, shareOutItemIdMap);
                if (CollectionUtils.isNotEmpty(insertItems)) {
                    insertMain.setTotRowNum(insertItems.size());
                    insertItemsAll.addAll(insertItems);
                }

                /*根据新的占用明细列表计算总占用量*/
                BigDecimal totalOutItem = insertItems.stream().map(SgBShareOutItem::getQty).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                //配销仓占用流水，返回：新的占用流水总占用量
                BigDecimal newTotalQty = mergeSaStoragePreoutFtp(mergeIds, insertMain, insertSaStoragePreoutFtps, newOrder, shareOutItemIdMap, user);
                if (totalOutItem.compareTo(newTotalQty) != 0) {
                    log.warn(LogUtil.format("配销层占用单占用总量与占用流水占用量不一致，可能存在消费延迟，聚合仓ID：{},占用单占用量：{},占用流水占用总量:{}",
                            "SgOmsStoTranslationService.mergeShareOutMainAndItems"), entry.getKey(), totalOutItem, newTotalQty);
                    throw new NDSException("配销层占用单占用总量与占用流水占用量不一致-请稍后重试");
                }

                insertMainList.add(insertMain);
            }

            try {
                //库存平移 共享占用单更新插入数据
                insertAndUpdateShare(user, insertItemsAll, insertMainList, insertSaStoragePreoutFtps, updateIds);
            } catch (Exception e) {
                AssertUtils.logAndThrowException("共享占用单更新数据失败!", e, user.getLocale());
            }
        } else {
            AssertUtils.logAndThrow("没有符合条件的共享占用单！", user.getLocale());
        }
    }

    /**
     * 共享占用单更新插入数据
     *
     * @param user                      用户
     * @param insertItemsAll            共享占用单明细集合
     * @param insertMainList            共享占用单集合
     * @param insertSaStoragePreoutFtps 共享池库存变动流水
     * @param updateIds                 更新ids
     */
    public void insertAndUpdateShare(User user, List<SgBShareOutItem> insertItemsAll, List<SgBShareOut> insertMainList,
                                     List<SgBSaStoragePreoutFtp> insertSaStoragePreoutFtps, List<Long> updateIds) {
        if (CollectionUtils.isNotEmpty(insertMainList)) {
            //作废原共享占用单  新增合并后的新单
            SgBShareOut updateShareOut = new SgBShareOut();
            StorageUtils.setBModelDefalutDataByUpdate(updateShareOut, user);
            updateShareOut.setModifierename(user.getEname());
            updateShareOut.setBillStatus(SgShareConstants.SHARE_OUT_BILL_STATUS_VOID);
            updateShareOut.setIsactive(SgConstants.IS_ACTIVE_N);
            sgShareOutMapper.update(updateShareOut, new LambdaUpdateWrapper<SgBShareOut>()
                    .in(SgBShareOut::getId, updateIds));
            //分批插入数据库
            StorageBasicUtils.batchInsertList(sgShareOutMapper, insertMainList,
                    SgConstants.SG_COMMON_INSERT_PAGE_SIZE, "处理共享占用单结果", user);
            if (CollectionUtils.isNotEmpty(insertItemsAll)) {
                //作废原共享占用单明细  新增合并后的新单明细
                SgBShareOutItem updateShareOutItem = new SgBShareOutItem();
                StorageUtils.setBModelDefalutDataByUpdate(updateShareOutItem, user);
                updateShareOutItem.setModifierename(user.getEname());
                updateShareOutItem.setIsactive(SgConstants.IS_ACTIVE_N);
                sgShareOutItemMapper.update(updateShareOutItem, new LambdaUpdateWrapper<SgBShareOutItem>()
                        .in(SgBShareOutItem::getSgBShareOutId, updateIds));
                //分批插入数据库
                StorageBasicUtils.batchInsertList(sgShareOutItemMapper, insertItemsAll,
                        SgConstants.SG_COMMON_INSERT_PAGE_SIZE, "处理共享占用单明细结果", user);
            }
            if (CollectionUtils.isNotEmpty(insertSaStoragePreoutFtps)) {
                //分批插入数据库
                StorageBasicUtils.batchInsertList(sgSaStoragePreoutFtpMapper, insertSaStoragePreoutFtps,
                        SgConstants.SG_COMMON_INSERT_PAGE_SIZE, "处理配销仓占用变动流水结果", user);
            }
        }
    }

    /**
     * 合并共享池占用流水
     *
     * @param mergeIds          合并的共享占用单id集合
     * @param insertMain        新共享占用单
     * @param newOrder          新零售发货单信息
     * @param shareOutItemIdMap 新旧共享占用单明细id  用于获取对应占用流水
     */
    private void mergeSpStoragePreoutFtp(List<Long> mergeIds, SgBShareOut insertMain,
                                         List<SgBSpStoragePreoutFtp> insertSpStoragePreoutFtps,
                                         SgOmsStoTranslationBillRequest newOrder, Map<String, Long> shareOutItemIdMap) {
        List<SgBSpStoragePreoutFtp> mergeStoragePreOutFtp =
                sgSpStoragePreoutFtpMapper.selectList(new LambdaQueryWrapper<SgBSpStoragePreoutFtp>()
                        .in(SgBSpStoragePreoutFtp::getBillId, mergeIds)
                        .eq(SgBSpStoragePreoutFtp::getIsactive, SgConstants.IS_ACTIVE_Y));
        Map<String, List<SgBSpStoragePreoutFtp>> ftpMap =
                mergeStoragePreOutFtp.stream().collect(Collectors.groupingBy(e -> e.getSgCShareStoreId() + "_" + e.getPsCSkuId()));
        for (List<SgBSpStoragePreoutFtp> value : ftpMap.values()) {
            BigDecimal mergeQtyBegin = value.stream().map(SgBSpStoragePreoutFtp::getQtyBegin).reduce(BigDecimal.ZERO,
                    BigDecimal::add);
            BigDecimal mergeQtyChange = value.stream().map(SgBSpStoragePreoutFtp::getQtyChange).reduce(BigDecimal.ZERO,
                    BigDecimal::add);
            BigDecimal mergeQtyEnd = value.stream().map(SgBSpStoragePreoutFtp::getQtyEnd).reduce(BigDecimal.ZERO,
                    BigDecimal::add);
            for (SgBSpStoragePreoutFtp ftp : value) {
                //新增一条记录平流水
                SgBSpStoragePreoutFtp offsetFtp = new SgBSpStoragePreoutFtp();
                BigDecimal qtyBegin = ftp.getQtyBegin();
                BigDecimal qtyChange = ftp.getQtyChange();
                BigDecimal qtyEnd = ftp.getQtyEnd();
                BeanUtils.copyProperties(ftp, offsetFtp);
                offsetFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SP_STORAGE_PREOUT_FTP));
                offsetFtp.setQtyBegin(qtyEnd);
                offsetFtp.setQtyChange(qtyChange.negate());
                offsetFtp.setQtyEnd(qtyBegin);
                offsetFtp.setCreationdate(new Date());
                offsetFtp.setModifieddate(new Date());
                insertSpStoragePreoutFtps.add(offsetFtp);
            }
            //新增共享占用单流水，绑定新零售单和新共享占用单
            SgBSpStoragePreoutFtp newFtp = new SgBSpStoragePreoutFtp();
            BeanUtils.copyProperties(value.get(0), newFtp);
            newFtp.setQtyBegin(mergeQtyBegin);
            newFtp.setQtyChange(mergeQtyChange);
            newFtp.setQtyEnd(mergeQtyEnd);
            newFtp.setCreationdate(new Date());
            newFtp.setModifieddate(new Date());
            newFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SP_STORAGE_PREOUT_FTP));
            newFtp.setBillId(insertMain.getId());
            newFtp.setBillDate(insertMain.getBillDate());
            newFtp.setBillNo(insertMain.getBillNo());
            //处理共享占用单明细map做了校验
            List<Long> oldShareOutItemIds =
                    value.stream().map(SgBSpStoragePreoutFtp::getBillItemId).filter(Objects::nonNull).sorted().collect(Collectors.toList());
            newFtp.setBillItemId(shareOutItemIdMap.get(oldShareOutItemIds.toString()));
            newFtp.setSourceBillNo(newOrder.getBillNo());
            newFtp.setSourceBillId(newOrder.getId());
            insertSpStoragePreoutFtps.add(newFtp);
        }
    }

    /**
     * 合并配销仓占用流水
     *
     * @param mergeIds          合并的共享占用单id集合
     * @param insertMain        新共享占用单
     * @param newOrder          新零售发货单信息
     * @param shareOutItemIdMap 新旧共享占用单明细id  用于获取对应占用流水
     * @param user
     * @return 新的占用流水总量
     */
    private BigDecimal mergeSaStoragePreoutFtp(List<Long> mergeIds, SgBShareOut insertMain,
                                               List<SgBSaStoragePreoutFtp> insertSaStoragePreoutFtps,
                                               SgOmsStoTranslationBillRequest newOrder, Map<String, Long> shareOutItemIdMap,
                                               User user) {
        BigDecimal newTotalQty = BigDecimal.ZERO;

        List<SgBSaStoragePreoutFtp> mergeStoragePreOutFtp =
                sgSaStoragePreoutFtpMapper.selectList(new LambdaQueryWrapper<SgBSaStoragePreoutFtp>()
                        .in(SgBSaStoragePreoutFtp::getBillId, mergeIds)
                        .eq(SgBSaStoragePreoutFtp::getIsactive, SgConstants.IS_ACTIVE_Y));
        //条码分组
        Map<String, List<SgBSaStoragePreoutFtp>> ftpMap =
                mergeStoragePreOutFtp.stream().collect(Collectors.groupingBy(e -> e.getSgCSaStoreId() + "_" + e.getPsCSkuId()));
        for (List<SgBSaStoragePreoutFtp> value : ftpMap.values()) {
            //合并后qty合计
            /*BigDecimal mergeQtyBegin = value.stream().map(SgBSaStoragePreoutFtp::getQtyBegin).reduce(BigDecimal.ZERO,
                    BigDecimal::add);*/
            BigDecimal mergeQtyChange = value.stream().map(SgBSaStoragePreoutFtp::getQtyChange).reduce(BigDecimal.ZERO,
                    BigDecimal::add);
            /*BigDecimal mergeQtyEnd = value.stream().map(SgBSaStoragePreoutFtp::getQtyEnd).reduce(BigDecimal.ZERO,
                    BigDecimal::add);*/
            //for循环平合并前产生的流水
            for (SgBSaStoragePreoutFtp ftp : value) {
                //新增一条记录平流水
                SgBSaStoragePreoutFtp offsetFtp = new SgBSaStoragePreoutFtp();
                BigDecimal qtyBegin = ftp.getQtyBegin();
                BigDecimal qtyChange = ftp.getQtyChange();
                BigDecimal qtyEnd = ftp.getQtyEnd();
                BeanUtils.copyProperties(ftp, offsetFtp);
                offsetFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SA_STORAGE_PREOUT_FTP));
                offsetFtp.setQtyBegin(qtyEnd);
                offsetFtp.setQtyChange(qtyChange.negate());
                offsetFtp.setQtyEnd(qtyBegin);
                offsetFtp.setCreationdate(new Date());
                offsetFtp.setModifieddate(new Date());
                offsetFtp.setRemark("库存平移-逆向");
                StorageUtils.setBModelDefalutData(offsetFtp, user);

                insertSaStoragePreoutFtps.add(offsetFtp);
            }

            //新增配销仓占用流水，绑定新零售单和新配销仓库存占用流水
            SgBSaStoragePreoutFtp newFtp = new SgBSaStoragePreoutFtp();
            BeanUtils.copyProperties(value.get(0), newFtp);
            /*newFtp.setQtyBegin(mergeQtyBegin);*/
            newFtp.setQtyChange(mergeQtyChange);
            /*newFtp.setQtyEnd(mergeQtyEnd);*/
            newFtp.setRemark("库存平移-新增");
            newFtp.setCreationdate(new Date());
            newFtp.setModifieddate(new Date());
            newFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SA_STORAGE_PREOUT_FTP));
            newFtp.setBillId(insertMain.getId());
            newFtp.setBillDate(insertMain.getBillDate());
            newFtp.setBillNo(insertMain.getBillNo());

            /*List<Long> oldShareOutItemIds =
                    value.stream().map(SgBSaStoragePreoutFtp::getBillItemId).filter(Objects::nonNull).sorted().collect(Collectors.toList());
            newFtp.setBillItemId(shareOutItemIdMap.get(oldShareOutItemIds.toString()));*/
            newFtp.setBillItemId(shareOutItemIdMap.get(insertMain.getId() + "_" + newFtp.getSgCSaStoreId() + "_" + newFtp.getPsCSkuId()));

            newFtp.setSourceBillNo(newOrder.getBillNo());
            newFtp.setSourceBillId(newOrder.getId());

            StorageUtils.setBModelDefalutData(newFtp, user);
            insertSaStoragePreoutFtps.add(newFtp);

            newTotalQty = newTotalQty.add(mergeQtyChange);
        }

        return newTotalQty;
    }

    /**
     * 共享占用单参数赋值
     *
     * @param newOrder  新零售发货单信息
     * @param user      用户
     * @param shareOuts 合并的共享占用单id集合
     * @return SgBShareOut  新共享占用单
     */
    public SgBShareOut setShareData(SgOmsStoTranslationBillRequest newOrder, List<SgBShareOut> shareOuts, User user) {
        //原单数 占用数 发货数
        BigDecimal totQtyOrign = shareOuts.stream().map(SgBShareOut::getTotQtyOrign).reduce(BigDecimal.ZERO,
                BigDecimal::add);
        BigDecimal totQtyPreout = shareOuts.stream().map(SgBShareOut::getTotQtyPreout).reduce(BigDecimal.ZERO,
                BigDecimal::add);
        BigDecimal totQtyOut = shareOuts.stream().map(SgBShareOut::getTotQtyOut).reduce(BigDecimal.ZERO,
                BigDecimal::add);
        SgBShareOut insertMain = new SgBShareOut();
        //聚合仓信息以及收货人信息
        BeanUtils.copyProperties(shareOuts.get(0), insertMain);
        insertMain.setBillStatus(SgShareConstants.SHARE_OUT_STATUS_CREATE);
        Long newId = ModelUtil.getSequence(SgConstants.SG_B_SHARE_OUT);
        StorageUtils.setBModelDefalutData(insertMain, user);
        insertMain.setOwnerename(user.getEname());
        insertMain.setModifierename(user.getEname());
        insertMain.setId(newId);
        insertMain.setAdClientId(SgBChannelStorageBufferExtend.DEFAULT_CLIENT_ID);
        insertMain.setAdOrgId(SgBChannelStorageBufferExtend.DEFAULT_ORG_ID);
        String billNo = SgStoreUtils.getBillNo(SgShareConstants.SEQ_SG_B_SHARE_OUT,
                SgConstants.SG_B_SHARE_OUT.toUpperCase(), insertMain, user.getLocale());
        insertMain.setBillNo(billNo);
        insertMain.setBillDate(new Date());
        insertMain.setTotQtyOrign(totQtyOrign);
        insertMain.setTotQtyPreout(totQtyPreout);
        insertMain.setTotQtyOut(totQtyOut);
        insertMain.setSourceBillDate(newOrder.getBillDate());
        insertMain.setSourceBillId(newOrder.getId());
        insertMain.setSourceBillNo(newOrder.getBillNo());
        insertMain.setSourceBillType(newOrder.getBillType());
        insertMain.setRemark("库存平移-新增");
        return insertMain;
    }

    /**
     * 共享占用单明细封装
     *
     * @param mainId    主表id
     * @param oldItems  需要合并的明细
     * @param itemIdMap 新的零售单明细 map
     * @return List<SgBShareOutItem> 新的占用明细列表
     */
    private List<SgBShareOutItem> mergeShareOutItems(Long mainId, List<SgBShareOutItem> oldItems,
                                                     Map<Long, SgOmsStoTranslationBillRequest.Item> itemIdMap,
                                                     Map<String, Long> shareOutItemIdMap) {
        List<SgBShareOutItem> insertItems = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(oldItems)) {
            Map<String, List<SgBShareOutItem>> skuItemsMap =
                    oldItems.stream().collect(Collectors.groupingBy(e -> e.getSgCSaStoreId() + "_" + e.getPsCSkuId()));
            for (List<SgBShareOutItem> shareOutItems : skuItemsMap.values()) {
                SgBShareOutItem insertItem = new SgBShareOutItem();
                BeanUtils.copyProperties(shareOutItems.get(0), insertItem);
                insertItem.setQty(shareOutItems.stream().map(SgBShareOutItem::getQty).reduce(BigDecimal.ZERO,
                        BigDecimal::add));
                insertItem.setQtyOut(shareOutItems.stream().map(SgBShareOutItem::getQtyOut).reduce(BigDecimal.ZERO,
                        BigDecimal::add));
                insertItem.setQtyPreout(shareOutItems.stream().map(SgBShareOutItem::getQtyPreout).reduce(BigDecimal.ZERO,
                        BigDecimal::add));
                insertItem.setCreationdate(new Date());
                insertItem.setModifieddate(new Date());
                Long shareOutItemId = ModelUtil.getSequence(SgConstants.SG_B_SHARE_OUT_ITEM);
                insertItem.setId(shareOutItemId);
                insertItem.setSgBShareOutId(mainId);
                if (Objects.nonNull(shareOutItems.get(0).getSourceBillItemId()) &&
                        Objects.nonNull(itemIdMap.get(shareOutItems.get(0).getSourceBillItemId()))) {
                    Long newId = itemIdMap.get(shareOutItems.get(0).getSourceBillItemId()).getNewId();
                    insertItem.setSourceBillItemId(newId);
                }

                //新旧共享占用单明细id  用于获取对应占用流水
                /*List<Long> oldShareOutItemIds =
                        shareOutItems.stream().map(SgBShareOutItem::getId).filter(Objects::nonNull).sorted().collect(Collectors.toList());
                shareOutItemIdMap.put(oldShareOutItemIds.toString(), shareOutItemId);*/
                shareOutItemIdMap.put(mainId + "_" + insertItem.getSgCSaStoreId() + "_" + insertItem.getPsCSkuId(), shareOutItemId);

                insertItems.add(insertItem);
            }
        }
        return insertItems;
    }

    /**
     * 校验参数
     *
     * @param originalOrders 需要合并的零售发货单信息
     * @param newOrder       新零售发货单
     */
    public void checkParam(List<SgOmsStoTranslationBillRequest> originalOrders,
                           SgOmsStoTranslationBillRequest newOrder) {
        AssertUtils.cannot(CollectionUtils.isEmpty(originalOrders), "需要合并的原单信息不能为空！");
        AssertUtils.cannot(newOrder == null, "合并后新单信息不能为空！");

    }

    /**
     * 合并逻辑占用单主表 明细表逻辑
     *
     * @param originalOrderIds 合并的零售发货单id集合
     * @param newOrder         新零售发货单信息
     * @param user             用户
     */
    private void mergeStoOutMainAndItems(List<Long> originalOrderIds,
                                         SgOmsStoTranslationBillRequest newOrder, User user) {
        //获取原零售单单据id集合 找到未被作废的逻辑占用单
        List<SgBStoOut> sgStoOuts = sgStoOutMapper.selectList(new LambdaQueryWrapper<SgBStoOut>()
                .in(SgBStoOut::getSourceBillId, originalOrderIds)
                .eq(SgBStoOut::getSourceBillType, SgConstantsIF.BILL_TYPE_RETAIL)
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        Map<Long, SgOmsStoTranslationBillRequest.Item> itemIdMap = getItemIdMap(newOrder);
        if (CollectionUtils.isNotEmpty(sgStoOuts)) {
            List<Long> updateIds = sgStoOuts.stream().map(SgBStoOut::getId).collect(Collectors.toList());
            //逻辑占用单新增明细集合
            List<SgBStoOutItem> insertItemsAll = new ArrayList<>();
            //逻辑占用单新增集合
            List<SgBStoOut> insertMainList = new ArrayList<>();
            //逻辑占用单新增逻辑共享占用流水集合
            List<SgBStorageSharedPreoutFtp> insertStorageSharedPreoutFtps = new ArrayList<>();
            //合并返回新单据信息
            SgBStoOut insertMain = setStoData(newOrder, user, sgStoOuts);
            //需要合并逻辑占用单明细集合
            List<SgBStoOutItem> mergeItems = sgStoOutItemMapper.selectList(new LambdaQueryWrapper<SgBStoOutItem>()
                    .in(SgBStoOutItem::getSgBStoOutId, updateIds)
                    .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
            //新旧逻辑占用单明细id  用于获取对应占用流水
            Map<String, Long> stoOutItemIdMap = new HashMap<>(16);
            List<SgBStoOutItem> insertItems = mergeStoOutItems(insertMain.getId(), mergeItems, itemIdMap, stoOutItemIdMap);
            if (CollectionUtils.isNotEmpty(insertItems)) {
                insertMain.setTotRowNum(insertItems.size());
                insertItemsAll.addAll(insertItems);
            }
            insertMainList.add(insertMain);

            /*根据新的占用明细列表计算总占用量*/
            BigDecimal totalOutItem = insertItems.stream().map(SgBStoOutItem::getQty).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

            //逻辑仓共享占用变动流水
            BigDecimal newTotalQty =
                    mergeStorageSharedPreoutFtp(updateIds, insertMain, insertStorageSharedPreoutFtps, newOrder, stoOutItemIdMap, user);
            if (totalOutItem.compareTo(newTotalQty) != 0) {
                log.warn(LogUtil.format("逻辑层占用单占用总量与占用流水占用量不一致，可能存在消费延迟，占用单占用量：{},占用流水占用总量:{}",
                        "SgOmsStoTranslationService.mergeStoOutMainAndItems"), totalOutItem, newTotalQty);
                throw new NDSException("逻辑层占用单占用总量与占用流水占用量不一致-请稍后重试");
            }

            /*合并逻辑占用明细日志*/
            List<SgBStoOutItemLog> newItemLogList = mergeStoOutItemLogs(updateIds, insertMain, newOrder.getItems(), insertItems, user);

            try {
                //库存平移 逻辑占用单流程更新插入数据
                insertAndUpdateSto(user, updateIds, insertItemsAll, insertMainList, insertStorageSharedPreoutFtps, newItemLogList);
            } catch (Exception e) {
                AssertUtils.logAndThrowException("逻辑占用单更新数据失败!", e, user.getLocale());
            }
        } else {
            AssertUtils.logAndThrow("没有符合条件的逻辑占用单！", user.getLocale());
        }

    }

    /**
     * 构建合并后的新逻辑占用明细日志
     *
     * @param updateIds   原占用单ID列表
     * @param insertMain  新的占用单
     * @param items       新旧来源明细单据ID映射信息
     * @param insertItems 新占用明细
     * @param user        当前用户
     * @return 新逻辑占用明细日志列表
     */
    private List<SgBStoOutItemLog> mergeStoOutItemLogs(List<Long> updateIds,
                                                       SgBStoOut insertMain,
                                                       List<SgOmsStoTranslationBillRequest.Item> items,
                                                       List<SgBStoOutItem> insertItems,
                                                       User user) {
        List<SgBStoOutItemLog> oldLogList = sgBStoOutItemLogMapper.selectList(new LambdaQueryWrapper<SgBStoOutItemLog>()
                .in(SgBStoOutItemLog::getSgBStoOutId, updateIds)
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isEmpty(oldLogList)) {
            return Collections.emptyList();
        }
        Map<String, Long> itemIdMap = ListUtils.emptyIfNull(insertItems).stream()
                .collect(Collectors.toMap(item -> item.getCpCStoreId()
                                + SgConstantsIF.MAP_KEY_DIVIDER + item.getPsCSkuId()
                                + SgConstantsIF.MAP_KEY_DIVIDER + item.getProduceDate(),
                        SgBStoOutItem::getId, (a, b) -> a));

        Map<Long, Long> sourceItemIdMap = ListUtils.emptyIfNull(items).stream()
                .collect(Collectors.toMap(SgOmsStoTranslationBillRequest.Item::getOriginalId,
                        SgOmsStoTranslationBillRequest.Item::getNewId,
                        (a, b) -> a));

        List<SgBStoOutItemLog> newLogList = new ArrayList<>();
        for (SgBStoOutItemLog oldLog : oldLogList) {
            SgBStoOutItemLog newLog = new SgBStoOutItemLog();
            BeanUtils.copyProperties(oldLog, newLog);

            newLog.setId(ModelUtil.getSequence(SgConstants.SG_B_STO_OUT_ITEM_LOG));
            newLog.setSgBStoOutId(insertMain.getId());
            newLog.setSourceBillItemId(sourceItemIdMap.get(oldLog.getSourceBillItemId()));
            newLog.setSgBStoOutItemId(itemIdMap.get(oldLog.getCpCStoreId()
                    + SgConstantsIF.MAP_KEY_DIVIDER + oldLog.getPsCSkuId()
                    + SgConstantsIF.MAP_KEY_DIVIDER + oldLog.getProduceDate()));

            StorageUtils.setBModelDefalutData(newLog, user);
            newLogList.add(newLog);
        }
        return newLogList;
    }

    /**
     * 逻辑占用单更新插入数据
     *
     * @param user                          用户
     * @param updateIds                     更新ids
     * @param insertItemsAll                逻辑占用单明细集合
     * @param insertMainList                逻辑占用单集合
     * @param insertStorageSharedPreoutFtps 逻辑仓共享占用变动流水
     * @param newItemLogList                新的合并后的占用明细日志
     */
    public void insertAndUpdateSto(User user, List<Long> updateIds, List<SgBStoOutItem> insertItemsAll,
                                   List<SgBStoOut> insertMainList,
                                   List<SgBStorageSharedPreoutFtp> insertStorageSharedPreoutFtps,
                                   List<SgBStoOutItemLog> newItemLogList) {
        if (CollectionUtils.isNotEmpty(insertMainList)) {
            //作废原逻辑占用单  新增合并后的新单
            SgBStoOut updateStoOut = new SgBStoOut();
            StorageUtils.setBModelDefalutDataByUpdate(updateStoOut, user);
            updateStoOut.setModifierename(user.getEname());
            updateStoOut.setBillStatus(SgStoreConstants.BILL_STO_OUT_STATUS_CANCELED);
            updateStoOut.setIsactive(SgConstants.IS_ACTIVE_N);
            sgStoOutMapper.update(updateStoOut, new LambdaUpdateWrapper<SgBStoOut>()
                    .in(SgBStoOut::getId, updateIds));
            //分批插入数据库
            StorageBasicUtils.batchInsertList(sgStoOutMapper, insertMainList,
                    SgConstants.SG_COMMON_INSERT_PAGE_SIZE, "处理逻辑占用单结果", user);
            if (CollectionUtils.isNotEmpty(insertItemsAll)) {
                //作废原逻辑占用单明细  新增合并后的新单明细
                SgBStoOutItem updateShareOutItem = new SgBStoOutItem();

                StorageUtils.setBModelDefalutDataByUpdate(updateShareOutItem, user);
                updateShareOutItem.setModifierename(user.getEname());
                updateShareOutItem.setIsactive(SgConstants.IS_ACTIVE_N);
                sgStoOutItemMapper.update(updateShareOutItem, new LambdaUpdateWrapper<SgBStoOutItem>()
                        .in(SgBStoOutItem::getSgBStoOutId, updateIds));
                //分批插入数据库
                StorageBasicUtils.batchInsertList(sgStoOutItemMapper, insertItemsAll,
                        SgConstants.SG_COMMON_INSERT_PAGE_SIZE, "处理逻辑占用单明细结果", user);
            }
            if (CollectionUtils.isNotEmpty(insertStorageSharedPreoutFtps)) {
                //分批插入数据库
                StorageBasicUtils.batchInsertList(sgStorageSharedPreoutFtpMapper, insertStorageSharedPreoutFtps,
                        SgConstants.SG_COMMON_INSERT_PAGE_SIZE, "处理逻辑仓共享占用变动流水结果", user);
            }

            if (CollectionUtils.isNotEmpty(newItemLogList)) {
                //分批插入数据库
                StorageBasicUtils.batchInsertList(sgBStoOutItemLogMapper, newItemLogList,
                        SgConstants.SG_COMMON_INSERT_PAGE_SIZE, "处理逻辑仓共享占用明细日志", user);
            }
        }
    }

    /**
     * 逻辑占用单参数赋值
     *
     * @param newOrder  新零售发货单信息
     * @param user      用户
     * @param sgStoOuts 合并的逻辑占用单集合
     * @return SgBStoOut 新逻辑占用单
     */
    public SgBStoOut setStoData(SgOmsStoTranslationBillRequest newOrder, User user, List<SgBStoOut> sgStoOuts) {
        //原单数 占用数 发货数
        BigDecimal totQtyOrign = sgStoOuts.stream().map(SgBStoOut::getTotQtyOrign).reduce(BigDecimal.ZERO,
                BigDecimal::add);
        BigDecimal totQtyPreout = sgStoOuts.stream().map(SgBStoOut::getTotQtyPreout).reduce(BigDecimal.ZERO,
                BigDecimal::add);
        BigDecimal totQtyOut = sgStoOuts.stream().map(SgBStoOut::getTotQtyOut).reduce(BigDecimal.ZERO,
                BigDecimal::add);
        SgBStoOut insertMain = new SgBStoOut();
        BeanUtils.copyProperties(sgStoOuts.get(0), insertMain);
        insertMain.setBillStatus(SgStoreConstants.BILL_STO_OUT_STATUS_CREATE);
        Long newId = ModelUtil.getSequence(SgConstants.SG_B_STO_OUT);
        StorageUtils.setBModelDefalutData(insertMain, user);
        insertMain.setOwnerename(user.getEname());
        insertMain.setModifierename(user.getEname());
        insertMain.setAdClientId(SgBChannelStorageBufferExtend.DEFAULT_CLIENT_ID);
        insertMain.setAdOrgId(SgBChannelStorageBufferExtend.DEFAULT_ORG_ID);
        insertMain.setId(newId);
        String billNo = SgStoreUtils.getBillNo(SgStoreConstants.SEQ_STO_OUT,
                SgConstants.SG_B_STO_OUT.toUpperCase(), insertMain, user.getLocale());
        insertMain.setBillNo(billNo);
        insertMain.setBillDate(new Date());
        insertMain.setTotQtyOrign(totQtyOrign);
        insertMain.setTotQtyPreout(totQtyPreout);
        insertMain.setTotQtyOut(totQtyOut);
        insertMain.setSourceBillDate(newOrder.getBillDate());
        insertMain.setSourceBillId(newOrder.getId());
        insertMain.setSourceBillNo(newOrder.getBillNo());
        insertMain.setSourceBillType(newOrder.getBillType());
        return insertMain;
    }

    /**
     * 逻辑仓共享占用变动流水
     *
     * @param updateIds       合并的逻辑占用单id集合
     * @param insertMain      合并后新逻辑占用单
     * @param newOrder        新零售发货单信息
     * @param stoOutItemIdMap 新旧逻辑占用单明细id  用于获取对应占用流水
     * @param user
     * @return 新的占用流水总量
     */
    private BigDecimal mergeStorageSharedPreoutFtp(List<Long> updateIds, SgBStoOut insertMain,
                                                   List<SgBStorageSharedPreoutFtp> insertStorageSharedPreoutFtps,
                                                   SgOmsStoTranslationBillRequest newOrder,
                                                   Map<String, Long> stoOutItemIdMap, User user) {
        BigDecimal newTotalQty = BigDecimal.ZERO;

        List<SgBStorageSharedPreoutFtp> mergeStoragePreOutFtp =
                sgStorageSharedPreoutFtpMapper.selectList(new LambdaQueryWrapper<SgBStorageSharedPreoutFtp>()
                        .in(SgBStorageSharedPreoutFtp::getBillId, updateIds)
                        .eq(SgBStorageSharedPreoutFtp::getIsactive, SgConstants.IS_ACTIVE_Y));
        //条码分组
        Map<String, List<SgBStorageSharedPreoutFtp>> ftpMap = mergeStoragePreOutFtp.stream()
                .collect(Collectors.groupingBy(e -> e.getCpCStoreId() + "_" + e.getPsCSkuId() + "_" + e.getProduceDate()));
        for (List<SgBStorageSharedPreoutFtp> value : ftpMap.values()) {
            //合并后qty合计
            /*BigDecimal mergeQtyBegin =
                    value.stream().map(SgBStorageSharedPreoutFtp::getQtyBegin).reduce(BigDecimal.ZERO,
                            BigDecimal::add);*/
            BigDecimal mergeQtyChange =
                    value.stream().map(SgBStorageSharedPreoutFtp::getQtyChange).reduce(BigDecimal.ZERO,
                            BigDecimal::add);
            /*BigDecimal mergeQtyEnd = value.stream().map(SgBStorageSharedPreoutFtp::getQtyEnd).reduce(BigDecimal.ZERO,
                    BigDecimal::add);*/
            //for循环平合并前产生的流水
            for (SgBStorageSharedPreoutFtp ftp : value) {
                //新增一条记录平流水
                SgBStorageSharedPreoutFtp offsetFtp = new SgBStorageSharedPreoutFtp();
                BigDecimal qtyBegin = ftp.getQtyBegin();
                BigDecimal qtyChange = ftp.getQtyChange();
                BigDecimal qtyEnd = ftp.getQtyEnd();
                BeanUtils.copyProperties(ftp, offsetFtp);
                offsetFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_STORAGE_SHARED_PREOUT_FTP));
                offsetFtp.setQtyBegin(qtyEnd);
                offsetFtp.setQtyChange(qtyChange.negate());
                offsetFtp.setQtyEnd(qtyBegin);
                offsetFtp.setCreationdate(new Date());
                offsetFtp.setModifieddate(new Date());
                offsetFtp.setRemark("库存平移-逆向");

                StorageUtils.setBModelDefalutData(offsetFtp, user);
                insertStorageSharedPreoutFtps.add(offsetFtp);
            }
            //新增逻辑仓共享占用变动流水，绑定新零售单和新逻辑占用单
            SgBStorageSharedPreoutFtp newFtp = new SgBStorageSharedPreoutFtp();
            BeanUtils.copyProperties(value.get(0), newFtp);
            /*newFtp.setQtyBegin(mergeQtyBegin);*/
            newFtp.setQtyChange(mergeQtyChange);
            /*newFtp.setQtyEnd(mergeQtyEnd);*/
            newFtp.setRemark("库存平移-新增");
            newFtp.setCreationdate(new Date());
            newFtp.setModifieddate(new Date());
            newFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_STORAGE_SHARED_PREOUT_FTP));
            newFtp.setSourceBillNo(newOrder.getBillNo());
            newFtp.setSourceBillId(newOrder.getId());
            newFtp.setBillId(insertMain.getId());
            newFtp.setBillDate(insertMain.getBillDate());
            newFtp.setBillNo(insertMain.getBillNo());
            /*List<Long> stoOutItemIds =
                    value.stream().map(SgBStorageSharedPreoutFtp::getBillItemId).filter(Objects::nonNull).sorted().collect(Collectors.toList());
            newFtp.setBillItemId(stoOutItemIdMap.get(stoOutItemIds.toString()));*/
            newFtp.setBillItemId(stoOutItemIdMap.get(insertMain.getId() + "_" + newFtp.getCpCStoreId() + "_" + newFtp.getPsCSkuId() + "_" + newFtp.getProduceDate()));
            StorageUtils.setBModelDefalutData(newFtp, user);

            insertStorageSharedPreoutFtps.add(newFtp);
            newTotalQty = newTotalQty.add(mergeQtyChange);
        }

        return newTotalQty;
    }

    /**
     * 逻辑占用单明细封装
     *
     * @param mainId     主表id
     * @param mergeItems 需要合并的明细
     * @param itemIdMap  新的零售单明细 map
     * @return List<SgBShareOutItem> 新的占用明细
     */
    private List<SgBStoOutItem> mergeStoOutItems(Long mainId, List<SgBStoOutItem> mergeItems,
                                                 Map<Long, SgOmsStoTranslationBillRequest.Item> itemIdMap,
                                                 Map<String, Long> stoOutItemIdMap) {
        List<SgBStoOutItem> insertItems = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(mergeItems)) {
            Map<String, List<SgBStoOutItem>> skuItemsMap =
                    mergeItems.stream().collect(Collectors.groupingBy(e ->
                            e.getCpCStoreId() + "_" + e.getPsCSkuId()+"_"+e.getProduceDate()));
            for (List<SgBStoOutItem> stoOutItems : skuItemsMap.values()) {
                SgBStoOutItem insertItem = new SgBStoOutItem();
                BeanUtils.copyProperties(stoOutItems.get(0), insertItem);
                insertItem.setQty(stoOutItems.stream().map(SgBStoOutItem::getQty).reduce(BigDecimal.ZERO,
                        BigDecimal::add));
                insertItem.setQtyOut(stoOutItems.stream().map(SgBStoOutItem::getQtyOut).reduce(BigDecimal.ZERO,
                        BigDecimal::add));
                insertItem.setQtyPreout(stoOutItems.stream().map(SgBStoOutItem::getQtyPreout).reduce(BigDecimal.ZERO,
                        BigDecimal::add));
                insertItem.setCreationdate(new Date());
                insertItem.setModifieddate(new Date());
                Long stoOutItemId = ModelUtil.getSequence(SgConstants.SG_B_STO_OUT_ITEM);
                insertItem.setId(stoOutItemId);
                insertItem.setSgBStoOutId(mainId);
                if (Objects.nonNull(stoOutItems.get(0).getSourceBillItemId()) &&
                        Objects.nonNull(itemIdMap.get(stoOutItems.get(0).getSourceBillItemId()))) {
                    Long newId = itemIdMap.get(stoOutItems.get(0).getSourceBillItemId()).getNewId();
                    insertItem.setSourceBillItemId(newId);
                }
                //新旧共享占用单明细id  用于获取对应占用流水
                /*List<Long> stoOutItemIds =
                        stoOutItems.stream().map(SgBStoOutItem::getId).filter(Objects::nonNull).sorted().collect(Collectors.toList());
                stoOutItemIdMap.put(stoOutItemIds.toString(), stoOutItemId);*/
                stoOutItemIdMap.put(mainId + "_" + insertItem.getCpCStoreId() + "_" + insertItem.getPsCSkuId() + "_" + insertItem.getProduceDate(), stoOutItemId);
                insertItems.add(insertItem);
            }
        }
        return insertItems;
    }

    /**
     * 获取零售单明细新旧id map
     *
     * @param newOrder 新零售发货单信息
     * @return 新旧零售明细id map
     */
    private static Map<Long, SgOmsStoTranslationBillRequest.Item> getItemIdMap(SgOmsStoTranslationBillRequest newOrder) {
        Map<Long, SgOmsStoTranslationBillRequest.Item> itemIdMap = new HashMap<>(16);
        List<SgOmsStoTranslationBillRequest.Item> newOrderItems = newOrder.getItems();
        if (CollectionUtils.isNotEmpty(newOrderItems)) {
            newOrderItems.forEach(item -> itemIdMap.put(item.getOriginalId(), item));
        }
        return itemIdMap;
    }
}
