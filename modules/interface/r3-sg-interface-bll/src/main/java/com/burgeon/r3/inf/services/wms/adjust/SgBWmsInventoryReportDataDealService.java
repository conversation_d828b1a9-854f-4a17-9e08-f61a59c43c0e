package com.burgeon.r3.inf.services.wms.adjust;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.inf.mapper.SgBWmsInventoryReportMapper;
import com.burgeon.r3.inf.services.common.InfGetBusinessSystemService;
import com.burgeon.r3.inf.services.wms.in.SgBWmsReturnMiddleTableSaveService;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.rpc.RpcPsService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.common.SgCoreUtilsConstants;
import com.burgeon.r3.sg.core.model.request.wms.adjust.SgBWmsInventoryReportRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.inf.adjust.SgBWmsInventoryReport;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustMainSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustSaveRequest;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSaveService;
import com.google.common.base.Preconditions;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * WMS->库存盘点回传中间表数据处理
 */
@Component
@Slf4j
public class SgBWmsInventoryReportDataDealService {

    @Autowired
    private RpcPsService rpcPsService;

    @Autowired
    private SgBWmsReturnMiddleTableSaveService sgBWmsReturnMiddleTableSaveService;

    @Autowired
    private SgBWmsInventoryReportMapper sgBWmsInventoryReportMapper;

    public ValueHolderV14 inventoryReportCallBackHandler() {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, null);
        ValueHolderV14<List<SgBWmsInventoryReport>> v14 = new ValueHolderV14(ResultCode.FAIL, "库存盘点回传中间表数据处理成功");
        Integer sgBWmsInventoryReportBatchTimeSize = InfGetBusinessSystemService.getSgBWmsInventoryReportBatchTimeSize();
        LambdaQueryWrapper<SgBWmsInventoryReport> wrapper = new QueryWrapper<SgBWmsInventoryReport>().lambda()
                .lt(SgBWmsInventoryReport::getFailedCount, SgCoreUtilsConstants.MAX_FAIL_TO_WMS_TIME)
                .in(SgBWmsInventoryReport::getTransferStatus, Arrays.asList(SgConstants.SG_B_WMS_INVENTORY_REPORT_STATUS_NOT_TRANSFER,
                        SgConstants.SG_B_WMS_INVENTORY_REPORT_STATUS_TRANSFER_FAIL))
                .eq(SgBWmsInventoryReport::getIsactive, SgConstants.IS_ACTIVE_Y)
                .orderByAsc(SgBWmsInventoryReport::getModifieddate);
        wrapper.last("LIMIT " + sgBWmsInventoryReportBatchTimeSize);
        List<SgBWmsInventoryReport> sgBWmsInventoryReports = sgBWmsInventoryReportMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(sgBWmsInventoryReports)) {
            v14.setMessage("没有符合的库存盘点回传中间表数据");
            return v14;
        }
        int success = 0;
        List<ValueHolderV14> errorHolders = new ArrayList<>();
        for (SgBWmsInventoryReport report : sgBWmsInventoryReports) {
            ValueHolderV14 holderV141 = parseDataWithInventoryReport(report);
            if (!holderV141.isOK()) {
                errorHolders.add(holderV141);
            } else {
                success++;
            }
        }
        if (CollectionUtils.isNotEmpty(errorHolders)) {
            holderV14.setCode(ResultCode.SUCCESS);
            holderV14.setMessage(String.format("成功%s条，失败%s条", success, sgBWmsInventoryReports.size() - success));
            holderV14.setData(errorHolders);
            log.error(LogUtil.format("库存盘点回传中间表数据结果：{}"), JSON.toJSONString(holderV14));
        } else {
            holderV14.setCode(ResultCode.SUCCESS);
            holderV14.setMessage("全部成功");
        }
        return holderV14;
    }

    private ValueHolderV14 parseDataWithInventoryReport(SgBWmsInventoryReport report) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.FAIL, null);
        try {
            String outResultStr = report.getMessage();
            if (StringUtils.isBlank(outResultStr)) {
                v14.setMessage("原报文为空");
                v14.setData(buildErrorInfo(report));
                return v14;
            }
            SgBWmsInventoryReportRequest request = JSON.parseObject(outResultStr,
                    SgBWmsInventoryReportRequest.class);
            Preconditions.checkNotNull(request, "转换后数据为空");
            List<SgBWmsInventoryReportRequest.Item> items = request.getItems();
            Preconditions.checkArgument(CollectionUtils.isNotEmpty(items), "转换后数据明细为空");

            SgCpCStore storeInfo = sgBWmsReturnMiddleTableSaveService.getStoreInfo(request.getWarehouseCode());
            Preconditions.checkArgument(storeInfo != null && storeInfo.getId() != null, "根据WMS仓库编码查询逻辑失败");
            List<String> eCodeList = items.stream().filter(obj -> StringUtils.isNotBlank(obj.getItemCode())).map(SgBWmsInventoryReportRequest.Item::getItemCode).collect(Collectors.toList());
            Preconditions.checkArgument(CollectionUtils.isNotEmpty(eCodeList), "Sku信息为空");
            Map<String, PsCSku> skuMap = this.batchPsCSkuMapByECodes(eCodeList);
            Preconditions.checkArgument(skuMap != null && !skuMap.isEmpty(), "查询Sku信息为空");

            SgBStoAdjustSaveRequest adjustRequest = buildSgBStoAdjustSaveRequest(report, items, storeInfo, skuMap);
            SgBStoAdjustSaveService service = ApplicationContextHandle.getBean(SgBStoAdjustSaveService.class);
            ValueHolderV14<SgR3BaseResult> valueHolderV14 = service.saveAndSubmit(adjustRequest);
            log.info("WMS库存盘点生成调整单 返回结果:{}", JSONObject.toJSON(valueHolderV14));
            String callBackMessage = "WMS库存盘点生成调整单:" + valueHolderV14.getMessage();
            if (valueHolderV14.isOK()) {
                updateInventorySuccessInfo(report, valueHolderV14);
                v14.setCode(ResultCode.SUCCESS);
                v14.setMessage(callBackMessage);
            } else {
                updateInventoryFailInfo(report, callBackMessage);
                v14.setMessage(callBackMessage);
                v14.setData(valueHolderV14.getData());
            }
        } catch (Exception e) {
            log.error(LogUtil.format("库存盘点处理数据异常：{}/{}/{}/{}"), report.getId(), report.getOutBizCode(),
                    report.getWmsBillNo(), Throwables.getStackTraceAsString(e));
            updateInventoryFailInfo(report, e.getMessage());
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
            v14.setData(buildErrorInfo(report));
        }
        return v14;
    }

    private void updateInventoryFailInfo(SgBWmsInventoryReport report, String errorMessage) {
        if (StringUtils.isNotBlank(errorMessage)) {
            if (errorMessage.length() > 1000) {
                errorMessage = errorMessage.substring(0, 999);
            }
        }
        SgBWmsInventoryReport update = SgBWmsInventoryReport.builder()
                .id(report.getId())
                .transferStatus(SgConstants.SG_B_WMS_INVENTORY_REPORT_STATUS_TRANSFER_FAIL)
                .failedCount(Optional.of(report.getFailedCount()).orElse(0) + 1)
                .failedReason(errorMessage)
                .modifieddate(new Date())
                .build();
        sgBWmsInventoryReportMapper.updateById(update);
    }

    private void updateInventorySuccessInfo(SgBWmsInventoryReport report, ValueHolderV14<SgR3BaseResult> v14) {
        SgBWmsInventoryReport update = SgBWmsInventoryReport.builder()
                .id(report.getId())
                .transferStatus(SgConstants.SG_B_WMS_INVENTORY_REPORT_STATUS_TRANSFER_SUCCESS)
                .failedReason("")
                .modifieddate(new Date())
                .build();
        SgR3BaseResult data = v14.getData();
        if (data != null) {
            update.setSgBStoAdjustId(data.getBillId());
            update.setSgBStoAdjustBillNo(data.getBillNo());
        }
        sgBWmsInventoryReportMapper.updateById(update);
    }

    private SgBWmsInventoryReport buildErrorInfo(SgBWmsInventoryReport report) {
        return SgBWmsInventoryReport.builder()
                .id(report.getId())
                .outBizCode(report.getOutBizCode())
                .wmsBillNo(report.getWmsBillNo())
                .wmsWarehouseCode(report.getWmsWarehouseCode())
                .build();
    }

    private SgBStoAdjustSaveRequest buildSgBStoAdjustSaveRequest(SgBWmsInventoryReport report
            , List<SgBWmsInventoryReportRequest.Item> items
            , SgCpCStore storeInfo, Map<String, PsCSku> skuMap) {
        /***  1、处理主表数据 */
        SgBStoAdjustMainSaveRequest saveRequest = new SgBStoAdjustMainSaveRequest();
        saveRequest.setObjId(-1L);
        saveRequest.setLoginUser(SystemUserResource.getRootUser());
        saveRequest.setSourceBillId(report.getId());
        saveRequest.setSourceBillNo(report.getWmsBillNo());
        saveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_INVENTORY_REPORT);
        // 盘点单时间，取当前时间
        saveRequest.setBillDate(new Date());
        saveRequest.setCpCStoreId(storeInfo.getId());
        saveRequest.setCpCStoreEcode(storeInfo.getCpCStoreEcode());
        saveRequest.setCpCStoreEname(storeInfo.getCpCStoreEname());
        saveRequest.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL);
        saveRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_ADJUST_PROP_INVENTORY);
        saveRequest.setRemark("由WMS库存盘点接口传入生成");
        saveRequest.setDrpStatus(Integer.valueOf(SgStoreConstants.SEND_DRP_STATUS_UNDECLARED));
        /** 2、处理子表数据 */
        List<SgBStoAdjustItemSaveRequest> saveAdjustRequests = new ArrayList<>();
        for (SgBWmsInventoryReportRequest.Item item : items) {
            BigDecimal quantity = new BigDecimal(Optional.ofNullable(item.getQuantity()).orElse(0L)).setScale(2,
                    BigDecimal.ROUND_HALF_UP);
            String itemCode = item.getItemCode();
            PsCSku psCSku = skuMap.get(itemCode);
            Preconditions.checkArgument(!BigDecimal.ZERO.equals(quantity), "数量不能为零");
            Preconditions.checkArgument(StringUtils.isNotBlank(itemCode), "sku条码不能为空");
            Preconditions.checkNotNull(psCSku, "sku信息不能为空");
            SgBStoAdjustItemSaveRequest itemSaveRequest = new SgBStoAdjustItemSaveRequest();
            itemSaveRequest.setId(-1L);
            itemSaveRequest.setSourceBillItemId(psCSku.getId());
            itemSaveRequest.setPsCSkuId(psCSku.getId());
            itemSaveRequest.setPsCSkuEcode(psCSku.getEcode());
            itemSaveRequest.setSourceBillItemId(psCSku.getId());
            itemSaveRequest.setCpCStoreId(storeInfo.getId());
            itemSaveRequest.setCpCStoreEcode(storeInfo.getCpCStoreEcode());
            itemSaveRequest.setCpCStoreEname(storeInfo.getCpCStoreEname());
            itemSaveRequest.setQty(quantity);
            saveAdjustRequests.add(itemSaveRequest);
        }
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(saveAdjustRequests), "转换为库存调整单明细为空");
        SgBStoAdjustSaveRequest adjustRequest = new SgBStoAdjustSaveRequest();
        adjustRequest.setMainRequest(saveRequest);
        adjustRequest.setItems(saveAdjustRequests);
        adjustRequest.setLoginUser(saveRequest.getLoginUser());
        return adjustRequest;
    }

    /**
     * <String, Long> ==> <psCSkuEcode,id>
     *
     * @param eCodeList
     * @return
     */
    private Map<String, PsCSku> batchPsCSkuMapByECodes(List<String> eCodeList) {
        List<PsCSku> list = new ArrayList<>();
        List<List<String>> pageList = StorageUtils.getPageList(eCodeList, SgConstants.SG_COMMON_MAX_QUERY_PAGE_SIZE);
        for (List<String> tempCodeList : pageList) {
            ValueHolderV14<List<PsCSku>> listHolderV14 = rpcPsService.querySKUByEcodeList(tempCodeList);
            if (listHolderV14.isOK() && CollectionUtils.isNotEmpty(listHolderV14.getData())) {
                List<PsCSku> psCSkus = listHolderV14.getData();
                list.addAll(psCSkus);
            }

        }
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(list), "查找sku为空！");
        list = list.stream().distinct().collect(Collectors.toList());
        Map<String, PsCSku> skuMap = list.stream().collect(
                Collectors.toMap(PsCSku::getEcode, Function.identity()));
        return skuMap;
    }
}