package com.burgeon.r3.inf.services.wms;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.inf.mapper.SgBWmsToOrderOutStockResultMapper;
import com.burgeon.r3.inf.utils.WmsTaskUtils;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNoticesItem;
import com.burgeon.r3.sg.core.model.table.wms.SgBWmsToOrderOutStockResult;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesMapper;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeSaveItemRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeSaveRequest;
import com.burgeon.r3.sg.store.services.freeze.SgBStoFreezeSaveAndSubmitService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.oc.oms.api.OmsBackExamineOrderCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-07-13 13:24
 * @Description: 发货单缺货通知接口（WMS->中台)定时任务
 */


@Slf4j
@Component
public class SgBWmsToOrderOutStockResultService {


    @Autowired
    private SgBWmsToOrderOutStockResultMapper resultMapper;

    @Autowired
    private SgBStoOutNoticesMapper outNoticesMapper;
    @Autowired
    private SgBStoOutNoticesItemMapper outNoticesItemMapper;
    @Autowired
    private CpCPhyWarehouseMapper cPhyWarehouseMapper;
    @Autowired
    SgBStoFreezeSaveAndSubmitService frezeSaveAndSubmitService;
    @Reference(group = "oc-core", version = "1.0")
    OmsBackExamineOrderCmd orderCmd;

    /**
     * 发货单缺货通知接口（WMS->中台)定时任务
     *
     * @return ValueHolderV14
     */
    public ValueHolderV14 execute() {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "发货单缺货通知接口（WMS->中台)定时任务成功！");

        log.info(LogUtil.format("SgBWmsToOrderOutStockResultService.execute start", "SgBWmsToOrderOutStockResultService.execute "));
        try {
            List<SgBWmsToOrderOutStockResult> sgWmsToOrderOutStockResults = resultMapper.selectList(new LambdaQueryWrapper<SgBWmsToOrderOutStockResult>()
                    .lt(SgBWmsToOrderOutStockResult::getFailedCount, SgConstantsIF.FAIL_COUNT)
                    .ne(SgBWmsToOrderOutStockResult::getTransformStatus, SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS)
                    .eq(SgBWmsToOrderOutStockResult::getWmsWarehouseType, ThirdWmsTypeEnum.QMWMS.getCode()));
            if (CollectionUtils.isEmpty(sgWmsToOrderOutStockResults)) {
                log.info(LogUtil.format("SgBWmsToOrderOutStockResultService.execute 没有可处理数据",
                        "SgBWmsToOrderOutStockResultService.execute "));
                return v14;
            }

            // 出库通知单map
            Map<String, SgBStoOutNotices> noticesHashMap = new HashMap<>(16);
            List<String> noticesBillNoList = new ArrayList<>();
            List<String> warehouseCodeList = new ArrayList<>();

            for (SgBWmsToOrderOutStockResult stockResult : sgWmsToOrderOutStockResults) {
                noticesBillNoList.add(stockResult.getNoticesBillNo());
                warehouseCodeList.add(stockResult.getWarehouseCode());
            }

            // 1.查出库通知单
            List<List<String>> noticesBillNos = StorageUtils.getPageList(noticesBillNoList, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
            for (List<String> noticesBillNo : noticesBillNos) {
                List<SgBStoOutNotices> sgStoOutNotices = outNoticesMapper.selectList(new LambdaQueryWrapper<SgBStoOutNotices>()
                        .in(SgBStoOutNotices::getBillNo, noticesBillNo));
                if (CollectionUtils.isNotEmpty(sgStoOutNotices)) {
                    noticesHashMap.putAll(sgStoOutNotices.stream().collect(Collectors.toMap(SgBStoOutNotices::getBillNo,
                            notices -> notices, (v1, v2) -> v1)));

                }
            }


            //2,根据warehouseCode 查实体仓档案
            Map<String, SgCpCPhyWarehouse> warehouseCodeMap = WmsTaskUtils.getSgCpPhyWarehouseByWarehouseCode(warehouseCodeList);

            //3，处理查出来d中间表数据
            businessLogic(sgWmsToOrderOutStockResults, noticesHashMap, warehouseCodeMap);

        } catch (Exception e) {
            log.warn(LogUtil.format("SgBWmsToOrderOutStockResultService.error message:{}",
                    "SgBWmsToOrderOutStockResultService.error"), Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }

        log.info(LogUtil.format("SgBWmsToOrderOutStockResultService.ValueHolderV14 end ValueHolderV14:{}",
                "SgBWmsToOrderOutStockResultService.ValueHolderV14 "), JSONObject.toJSONString(v14));
        return v14;
    }

    /**
     * 处理查出来d中间表数据
     *
     * @param sgWmsToOrderOutStockResults 中间表数据
     * @param noticesHashMap              出库通知单map
     * @param warehouseCodeMap            实体仓档案map
     */
    private void businessLogic(List<SgBWmsToOrderOutStockResult> sgWmsToOrderOutStockResults,
                               Map<String, SgBStoOutNotices> noticesHashMap,
                               Map<String, SgCpCPhyWarehouse> warehouseCodeMap) {

        log.info(LogUtil.format("SgBWmsToOrderOutStockResultService.businessLogic sgWmsToOrderOutStockResults.size:{}," +
                        "noticesHashMap.size:{},warehouseCodeMap.size:{}",
                "SgBWmsToOrderOutStockResultService.businessLogic"),
                sgWmsToOrderOutStockResults.size(), noticesHashMap.size(), warehouseCodeMap.size());

        for (SgBWmsToOrderOutStockResult stockResult : sgWmsToOrderOutStockResults) {
            try {
                String noticesBillNo = stockResult.getNoticesBillNo();
                String warehouseCode = stockResult.getWarehouseCode();
                //a.是否有出库通知单
                if (MapUtils.isEmpty(noticesHashMap) || !noticesHashMap.containsKey(noticesBillNo)) {
                    updateFail(stockResult, "出库通知单不存在");
                    continue;
                }
                //b.是否存在实体仓档案中
                if (MapUtils.isEmpty(warehouseCodeMap) || !warehouseCodeMap.containsKey(warehouseCode)) {
                    updateFail(stockResult, "仓库编码[" + warehouseCode + "]在【实体仓档案】中不存在");
                    continue;
                }

                //c.解析报文
                String message = stockResult.getMessage();
                if (StringUtils.isEmpty(message)) {
                    updateFail(stockResult, "出库单[" + noticesBillNo + "]在报文不存在！");
                    continue;
                }
                log.info(LogUtil.format("SgBWmsToOrderOutStockResultService.messageParse message:{}",
                        "SgBWmsToOrderOutStockResultService.messageParse"), message);
                List<String> itemCodeList = WmsTaskUtils.messageParseByItemsReturnItemCode(message);
                log.info(LogUtil.format("SgBWmsToOrderOutStockResultService.messageParse message:{}",
                        "SgBWmsToOrderOutStockResultService.itemCodeList"), JSONObject.toJSONString(itemCodeList));
                if (CollectionUtils.isEmpty(itemCodeList)) {
                    updateFail(stockResult, "报文无明细！");
                    continue;
                }
                //d,比对条码信息
                JSONArray array = WmsTaskUtils.compareToSku(itemCodeList);
                if (array.size() > 0) {
                    updateFail(stockResult, "商品编码" + JSONArray.toJSONString(array) + "在【商品SKU】中不存在！");
                    continue;
                }

                SgBStoOutNotices sgStoOutNotices = noticesHashMap.get(noticesBillNo);
                // e,零售发货单反审核
                ValueHolderV14 v141 = orderCmd.backExamineOrder(sgStoOutNotices.getSourceBillId(), R3SystemUserResource.getSystemRootUser());
                log.info(LogUtil.format("SgBWmsToOrderOutStockResultService.backExamineOrder v141:{}",
                        "SgBWmsToOrderOutStockResultService.backExamineOrder"), JSONObject.toJSONString(v141));
                if (!v141.isOK()) {
                    updateFail(stockResult, "零售发货单反审核失败：" + v141.getMessage());
                    continue;
                }

                //f，冻结单新增并审核
                List<SgBStoOutNoticesItem> sgStoOutNoticesItems = outNoticesItemMapper.selectList(new LambdaQueryWrapper<SgBStoOutNoticesItem>()
                        .eq(SgBStoOutNoticesItem::getSgBStoOutNoticesId, sgStoOutNotices.getId())
                        .in(SgBStoOutNoticesItem::getPsCSkuEcode, itemCodeList));
                if (CollectionUtils.isEmpty(sgStoOutNoticesItems)) {
                    updateFail(stockResult, "出库单无匹配明细！！" + sgStoOutNotices.getBillNo() + "条码:" + JSONObject.toJSONString(itemCodeList));
                    continue;
                }

                log.info(LogUtil.format("SgBWmsToOrderOutStockResultService.sgStoOutNotices sgStoOutNotices:{},sgStoOutNoticesItems.size:{}",
                        "SgBWmsToOrderOutStockResultService.sgStoOutNotices"),
                        sgStoOutNotices.getBillNo(), sgStoOutNoticesItems.size());
                ValueHolderV14<SgR3BaseResult> v14 = freezeSaveAndSubmit(sgStoOutNotices, sgStoOutNoticesItems);
                log.info(LogUtil.format("SgBWmsToOrderOutStockResultService.messageParse v14:{}",
                        "SgBWmsToOrderOutStockResultService.v14"), JSONObject.toJSONString(v14));
                if (!v14.isOK()) {
                    updateFail(stockResult, "出库单:" + sgStoOutNotices.getBillNo() + "_" + v14.getMessage());
                }

                //更新成功信息
                updateSuccess(stockResult);

            } catch (Exception e) {
                log.error("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
                updateFail(stockResult, "异常：" + e.getMessage());
            }

        }

    }


    /**
     * 新增并审核逻辑冻结单
     *
     * @param sgStoOutNotices 出库单
     * @param noticesItemList 出库单明细
     * @return ValueHolderV14
     */
    private ValueHolderV14<SgR3BaseResult> freezeSaveAndSubmit(SgBStoOutNotices sgStoOutNotices, List<SgBStoOutNoticesItem> noticesItemList) {

        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "逻辑冻结单审核成功！");

        //组装逻辑冻结单主表和明细
        Map<Long, List<SgBStoOutNoticesItem>> itemMap = noticesItemList.stream().collect(Collectors.groupingBy(SgBStoOutNoticesItem::getCpCStoreId));
        for (Long storeId : itemMap.keySet()) {
            List<SgBStoOutNoticesItem> itemList = itemMap.get(storeId);
            SgBStoOutNoticesItem item1 = itemList.get(0);
            SgBStoFreezeSaveRequest freezeRequest = new SgBStoFreezeSaveRequest();
            freezeRequest.setId(-1L);
            freezeRequest.setCpCStoreId(item1.getCpCStoreId());
            freezeRequest.setCpCStoreEcode(item1.getCpCStoreEcode());
            freezeRequest.setCpCStoreEname(item1.getCpCStoreEname());
            freezeRequest.setSourceBillId(sgStoOutNotices.getSourceBillId());
            freezeRequest.setSourceBillType(sgStoOutNotices.getSourceBillType());
            freezeRequest.setSourceBillNo(sgStoOutNotices.getSourceBillNo());
            freezeRequest.setRemark("由发货单缺货中间表生成：" + sgStoOutNotices.getBillNo());

            List<SgBStoFreezeSaveItemRequest> freezeItems = new ArrayList<>();
            itemList.forEach(item -> {
                SgBStoFreezeSaveItemRequest itemRequest = new SgBStoFreezeSaveItemRequest();
                itemRequest.setId(-1L);
                itemRequest.setPsCSkuEcode(item.getPsCSkuEcode());
                itemRequest.setPsCSkuId(item.getPsCSkuId());
                itemRequest.setQty(item.getQty());
                itemRequest.setPriceList(Optional.ofNullable(item.getPriceList()).orElse(BigDecimal.ZERO));
                itemRequest.setProduceDate(item.getProduceDate());
                itemRequest.setStockType(SgConstantsIF.STOCK_TYPE_GOODS);
                freezeItems.add(itemRequest);
            });


            SgBStoFreezeBillSaveRequest saveRequest = new SgBStoFreezeBillSaveRequest();
            saveRequest.setFreezeSaveRequest(freezeRequest);
            saveRequest.setSgBStoFreezeSaveItemRequests(freezeItems);
            saveRequest.setObjId(-1L);
            saveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());
            //保存
            v14 = frezeSaveAndSubmitService.saveAndSubmit(saveRequest);
            if (!v14.isOK()) {
                return v14;
            }
        }

        return v14;
    }


    /**
     * 更新错误信息
     *
     * @param sgWmsToOrderOutStockResults 中间表数据
     * @param failedReason                失败原因
     */
    private void updateFail(SgBWmsToOrderOutStockResult sgWmsToOrderOutStockResults, String failedReason) {
        SgBWmsToOrderOutStockResult update = new SgBWmsToOrderOutStockResult();
        update.setId(sgWmsToOrderOutStockResults.getId());
        int failedConut = Optional.ofNullable(sgWmsToOrderOutStockResults.getFailedCount()).orElse(0) + 1;
        update.setFailedCount(failedConut);
        update.setFailedReason(failedReason.length() > 500 ? failedReason.substring(0, 500) : failedReason);
        update.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED);
        resultMapper.updateById(update);
    }

    /**
     * 更新成功信息
     *
     * @param inTask 中间表数据
     */
    private void updateSuccess(SgBWmsToOrderOutStockResult inTask) {
        SgBWmsToOrderOutStockResult update = new SgBWmsToOrderOutStockResult();
        update.setId(inTask.getId());
        update.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS);
        update.setTransformationData(new Date());
        resultMapper.update(update, new LambdaUpdateWrapper<SgBWmsToOrderOutStockResult>()
                .set(SgBWmsToOrderOutStockResult::getFailedReason, null)
                .eq(SgBWmsToOrderOutStockResult::getId, inTask.getId()));
    }

}
