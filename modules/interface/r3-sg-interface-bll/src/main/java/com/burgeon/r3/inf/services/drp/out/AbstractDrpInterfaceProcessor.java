package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.inf.services.drp.out.mapper.DrpInterfaceMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.store.services.DrpRestHttpClient;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.core.schema.TableManager;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/21 16:01
 */
@Slf4j
@Component
public abstract class AbstractDrpInterfaceProcessor<T extends BaseModel, Z extends BaseModel> {
    @Value("${drp.interface.pageNum:0}")
    public int pageNum;
    @Value("${drp.interface.pageSize:10}")
    public int pageSize;
    @Value("${drp.interface.failNum:3}")
    public int failNum;
    @Autowired
    DrpRestHttpClient drpRestHttpClient;

    @Autowired
    DrpInterfaceMapper drpInterfaceMapper;

    public abstract LambdaQueryWrapper<T> execMainWrapper();

    /**
     * 明细查询条件(关联主表,和isactive已经封装)
     *
     * @return
     */
    public abstract LambdaQueryWrapper<Z> execitemWrapper(Long mianId);

    public abstract String drpInterfaceUrl();

    public abstract String itemMainField();

    public abstract String drpStatus();

    public String getClassName() {
        return this.getClass().getName();
    }

    public abstract String drpStatusFailCount();

    public abstract String drpStatusFailReason();

    public int drpSuccessStatus() {
        return ResultCode.SUCCESS;
    }

    public int drpFailStatus() {
        return ResultCode.FAIL;
    }

    public abstract JSONObject execInterfaceParam(T t, List<Z> z);

    /**
     * 成功后处理
     *
     * @return
     */
    public abstract void handleBysuccess(T t, List<Z> z);

    @Autowired
    TableManager tableManager;

    public List<ValueHolderV14> exec() {

        Class<T> mianClass = (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
        String mainName = captureName(mianClass.getSimpleName());
        ExtentionMapper mainMapper = ApplicationContextHandle.getBean(mainName + "Mapper");
        Class<T> itemClass = (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[1];
        ExtentionMapper itemMapper = null;
        if (!itemClass.getSimpleName().equalsIgnoreCase(mianClass.getSimpleName())) {
            itemMapper = ApplicationContextHandle.getBean(captureName(itemClass.getSimpleName()) + "Mapper");
        }

        List<ValueHolderV14> resultList = new ArrayList<>();
        /** 分页查询 **/
        PageHelper.startPage(pageNum, pageSize);
        LambdaQueryWrapper<T> wrapper = execMainWrapper();
        if (log.isDebugEnabled()) {
            log.debug("AbstractDrpInterfaceProcessor.exec.wrapper.table={}", mainName);
        }
        PageInfo<T> pageResult = new PageInfo<T>(mainMapper.selectList(wrapper));
        List<T> mainList = pageResult.getList();
        if (CollectionUtils.isEmpty(mainList)) {
            resultList.add(new ValueHolderV14<>(ResultCode.SUCCESS, this.getClass().getSimpleName() + "数据为空"));
        }
        for (T t : mainList) {
            try {
                JSONObject main = (JSONObject) JSONObject.toJSON(t);
                LambdaQueryWrapper<Z> itemLambdaWrapper = execitemWrapper(main.getLong("ID"));
                List<Z> itemList = null;
                if (itemMapper != null) {
                    if (itemLambdaWrapper != null) {
                        itemList = itemMapper.selectList(itemLambdaWrapper);
                    } else {
                        QueryWrapper<Z> itemWrapper = new QueryWrapper();
                        itemWrapper.eq(itemMainField(), main.getLong("ID")).lambda().eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y);
                        itemList = itemMapper.selectList(itemWrapper);
                    }
                }
                ValueHolderV14 result = execInterface(execInterfaceParam(t, itemList), t, humpToLine(mainName), main, itemList);
                log.info("AbstractDrpInterfaceProcessor exec result={}", result);
                resultList.add(result);
            } catch (Exception e) {
                log.error("AbstractDrpInterfaceProcessor exec error={}", Throwables.getStackTraceAsString(e));
            }
        }
        log.info("Finish AbstractDrpInterfaceProcessor exec");
        return resultList;
    }


    private static Pattern humpPattern = Pattern.compile("[A-Z]");

    /**
     * 驼峰转下划线,效率比上面高
     */
    public static String humpToLine(String str) {
        Matcher matcher = humpPattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, "_" + matcher.group(0).toLowerCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public ValueHolderV14 execInterface(JSONObject param, T t, String tableName, JSONObject main, List<Z> itemList) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "新增/更新成功");
        JSONObject json = new JSONObject();
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        wrapper.eq("id", main.getLong("ID"));
        Integer failCount = main.getInteger(drpStatusFailCount());
        failCount = failCount == null ? 0 : failCount + 1;
        try {
            JSONObject result = drpRestHttpClient.post(drpInterfaceUrl(), param);
            if (result.getInteger("code") == 0) {
                log.info("AbstractDrpInterfaceProcessor execInterface url={}.param={}.result={}",
                        drpInterfaceUrl(), param, result);
                drpInterfaceMapper.updateSuccess(tableName,
                        drpStatus(), drpSuccessStatus(), drpStatusFailCount(), drpStatusFailReason(), main.getLong("ID"));
                handleBysuccess(t, itemList);
            } else {
                log.info("AbstractDrpInterfaceProcessor execInterface url={}.param={}.error={}",
                        drpInterfaceUrl(), param, result);
                drpInterfaceMapper.updateFailCount(tableName,
                        drpStatus(), drpFailStatus(),
                        drpStatusFailCount(), failCount,
                        drpStatusFailReason(), result.getString("message"),
                        main.getLong("ID"));
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(result.getString("message"));
            }
        } catch (Exception ex) {
            log.error("AbstractDrpInterfaceProcessor execInterface url={}.param={}.ex={}",
                    drpInterfaceUrl(), param, Throwables.getStackTraceAsString(ex));
            drpInterfaceMapper.updateFailCount(tableName,
                    drpStatus(), drpFailStatus(),
                    drpStatusFailCount(), failCount,
                    drpStatusFailReason(), ex.getMessage(),
                    main.getLong("ID"));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(ex.getMessage());
        }
        return v14;
    }

    private String captureName(String str) {
        // 进行字母的ascii编码前移，效率要高于截取字符串进行转换的操作
        char[] cs = str.toCharArray();
        cs[0] += 32;
        return String.valueOf(cs);
    }
}

