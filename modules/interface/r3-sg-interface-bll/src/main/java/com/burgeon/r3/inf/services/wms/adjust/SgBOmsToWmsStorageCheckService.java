package com.burgeon.r3.inf.services.wms.adjust;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.inf.mapper.SgBOmsToWmsStorageCheckMapper;
import com.burgeon.r3.inf.mapper.SgBWmsToLsStorageMapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.basic.mapper.SgBFreezeStorageMapper;
import com.burgeon.r3.sg.basic.mapper.SgBStorageChangeFtpMapper;
import com.burgeon.r3.sg.basic.mapper.SgBStorageFreezeFtpMapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.common.SgCoreUtilsConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgBFreezeStorage;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorage;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorageChangeFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorageFreezeFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.wms.SgBOmsToWmsStorageCheck;
import com.burgeon.r3.sg.core.model.table.wms.SgBWmsToLsStorage;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.DateUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.inf.common.enums.StoreTypeEnum;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-08-30 13:33
 * @Description: OMS与WMS库存核对服务
 */
@Slf4j
@Component
@Deprecated
public class SgBOmsToWmsStorageCheckService {

    @Autowired
    private SgBOmsToWmsStorageCheckMapper checkMapper;
    @Autowired
    private CpCPhyWarehouseMapper cPhyWarehouseMapper;
    @Autowired
    private SgBStorageChangeFtpMapper storageChangeFtpMapper;
    @Autowired
    private SgBStorageFreezeFtpMapper freezeFtpMapper;
    @Autowired
    private SgBWmsToLsStorageMapper lsStorageMapper;

    @Autowired
    private SgBFreezeStorageMapper sgBFreezeStorageMapper;

    /**
     * 系统参数 redis key
     */
    private final static String R3_SG_B_OMS_TO_WMS_STORAGE_CHECK_DAY = "business_system:r3_sg_b_oms_to_wms_storage_check_day";

    /**
     * WMS库存
     *
     * @return ValueHolderV14
     */
    public ValueHolderV14 saveOmsStorage() {

        log.info(LogUtil.format("OMS与WMS库存核对服务 查询OMS库存 start",
                "SgBOmsToWmsStotageCheckService.saveOmsStorage"));
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "OMS与WMS库存核对新增oms库存成功！");
        try {
            Integer day = getDay();
            Date pastDate = DateUtils.getPastDate(day, new Date());
            int count = checkMapper.selectCount(new LambdaQueryWrapper<SgBOmsToWmsStorageCheck>()
                    .lt(SgBOmsToWmsStorageCheck::getStorageDate, pastDate));
            if (count > 0) {
                checkMapper.delete(new LambdaQueryWrapper<SgBOmsToWmsStorageCheck>()
                        .lt(SgBOmsToWmsStorageCheck::getStorageDate, pastDate));
            }

            batchInsertOmsStorage();
        } catch (Exception e) {
            log.error(LogUtil.format("SgBOmsToWmsStorageCheckService.error:{}",
                    "SgBOmsToWmsStorageCheckService.error", Throwables.getStackTraceAsString(e)));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }

        log.info(LogUtil.format("OMS与WMS库存核对服务 查询OMS库存 end:{}",
                "SgBOmsToWmsStotageCheckService.saveOmsStorage"), JSONObject.toJSONString(v14));
        return v14;
    }

    /**
     * 批量新增WMS库
     */
    private void batchInsertOmsStorage() throws ParseException {
        //获取实体仓 分仓去查OMS与WMS库存核对表
        List<SgCpCPhyWarehouse> sgCpPhyWarehouses = cPhyWarehouseMapper.selectList(new LambdaQueryWrapper<SgCpCPhyWarehouse>()
                .eq(SgCpCPhyWarehouse::getIsactive, SgConstants.IS_ACTIVE_Y));

        log.info(LogUtil.format("实体仓数量:{}", "sgCpPhyWarehouses"),
                CollectionUtils.isNotEmpty(sgCpPhyWarehouses) ? sgCpPhyWarehouses.size() : 0);

        if (CollectionUtils.isNotEmpty(sgCpPhyWarehouses)) {

            Date pastDate = DateUtils.getPastDate(1, new Date());

            log.info(LogUtil.format("系统日期前一天:{}", "pastDate"), pastDate);

            int count = checkMapper.selectCount(new LambdaQueryWrapper<SgBOmsToWmsStorageCheck>()
                    .eq(SgBOmsToWmsStorageCheck::getStorageDate, pastDate));
            if (count > 0) {
                checkMapper.delete(new LambdaQueryWrapper<SgBOmsToWmsStorageCheck>()
                        .eq(SgBOmsToWmsStorageCheck::getStorageDate, pastDate));
            }

            for (SgCpCPhyWarehouse warehouse : sgCpPhyWarehouses) {
                try {

                    //前天的库存记录，当成昨天的期初
                    List<SgBOmsToWmsStorageCheck> sgOmsToWmsStorageChecks = checkMapper.selectByTwoDay(warehouse.getId());

                    log.info(LogUtil.format("期初数据:{},", "sgOmsToWmsStorageChecks"),
                            CollectionUtils.isNotEmpty(sgOmsToWmsStorageChecks) ? JSONObject.toJSONString(sgOmsToWmsStorageChecks) : null);

                    Map<Long, List<CpCStore>> storeInfoByPhyId = CommonCacheValUtils.getStoreInfoByPhyId(warehouse.getId());
                    if (MapUtils.isEmpty(storeInfoByPhyId) || !storeInfoByPhyId.containsKey(warehouse.getId())) {
                        log.info(LogUtil.format("根据实体仓查逻辑仓异常，未查询到逻辑仓信息，实体仓:{}", "getStoreInfoByPhyId"),
                                warehouse.getId());
                        continue;
                    }

                    List<CpCStore> stores = storeInfoByPhyId.get(warehouse.getId());

                    if (CollectionUtils.isEmpty(sgOmsToWmsStorageChecks)) {
                        List<SgBStorage> sgStorageList = CommonCacheValUtils.queryStorageByStore(storeInfoByPhyId);
                        if (CollectionUtils.isNotEmpty(sgStorageList)) {
                            initStorage(sgStorageList, warehouse, pastDate);
                        }
                        initFreezeStorage(stores, warehouse, pastDate);
                        continue;
                    }


                    //一头牛实体仓跟逻辑仓一对一
                    List<Long> storeIdList = stores.stream().map(CpCStore::getId).collect(Collectors.toList());
                    //昨天的逻辑仓在库变动流水
                    List<SgBStorageChangeFtp> sgStorageChangeFtpList = storageChangeFtpMapper.selectList(new LambdaQueryWrapper<SgBStorageChangeFtp>()
                            .eq(SgBStorageChangeFtp::getChangeDate, pastDate)
                            .in(SgBStorageChangeFtp::getCpCStoreId, storeIdList));
                    //昨天的逻辑仓冻结变动流水
                    List<SgBStorageFreezeFtp> sgStorageFreezeFtps = freezeFtpMapper.selectList(new LambdaQueryWrapper<SgBStorageFreezeFtp>()
                            .eq(SgBStorageFreezeFtp::getChangeDate, pastDate)
                            .in(SgBStorageFreezeFtp::getCpCStoreId, storeIdList));

                    log.info(LogUtil.format("期初:{},逻辑仓在库变动流水:{},逻辑仓冻结变动流水:{}", "sgStorageChangeFtpList"),
                            CollectionUtils.isNotEmpty(sgOmsToWmsStorageChecks) ? sgOmsToWmsStorageChecks.size() : 0,
                            CollectionUtils.isNotEmpty(sgStorageChangeFtpList) ? sgStorageChangeFtpList.size() : 0,
                            CollectionUtils.isNotEmpty(sgStorageFreezeFtps) ? sgStorageFreezeFtps.size() : 0);

                    storageCheck(sgOmsToWmsStorageChecks, sgStorageChangeFtpList, sgStorageFreezeFtps, warehouse, pastDate);

                } catch (Exception e) {
                    log.error(LogUtil.format("SgBOmsToWmsStorageCheckService.error:{}",
                            "SgBOmsToWmsStorageCheckService.error", Throwables.getStackTraceAsString(e)));
                }
            }

        }
    }

    /**
     * 冻结期初
     *
     * @param stores    逻辑仓集合
     * @param warehouse 实体仓
     * @param pastDate  日期
     */
    private void initFreezeStorage(List<CpCStore> stores, SgCpCPhyWarehouse warehouse, Date pastDate) {

        if (CollectionUtils.isNotEmpty(stores)) {
            List<Long> storeIds = stores.stream().map(CpCStore::getId).collect(Collectors.toList());

            List<SgBFreezeStorage> freezeStorages = sgBFreezeStorageMapper.selectList(new QueryWrapper<SgBFreezeStorage>().lambda()
                    .in(SgBFreezeStorage::getCpCStoreId, storeIds));

            log.info(LogUtil.format("冻结库存期初size:{}", "freezeStorages"),
                    CollectionUtils.isNotEmpty(freezeStorages) ? freezeStorages.size() : 0);

            if (CollectionUtils.isNotEmpty(freezeStorages)) {

                List<SgBOmsToWmsStorageCheck> resultList = new ArrayList<>();

                Map<String, List<SgBFreezeStorage>> freezeStorageMap = freezeStorages.stream().collect(Collectors.groupingBy(x ->
                        x.getPsCSkuEcode() + x.getProduceDate() + x.getStockType()));

                for (String key : freezeStorageMap.keySet()) {
                    List<SgBFreezeStorage> freezeStorageList = freezeStorageMap.get(key);
                    SgBFreezeStorage sgFreezeStorage = freezeStorageList.get(0);

                    BigDecimal totQtyFreeze = freezeStorageList.stream().map(SgBFreezeStorage::getQtyFreeze).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                    SgBOmsToWmsStorageCheck sgOmsToWmsStorageCheck = new SgBOmsToWmsStorageCheck();

                    sgOmsToWmsStorageCheck.setCpCPhyWarehouseId(warehouse.getId());
                    sgOmsToWmsStorageCheck.setCpCPhyWarehouseEcode(warehouse.getEcode());
                    sgOmsToWmsStorageCheck.setCpCPhyWarehouseEname(warehouse.getEname());
                    sgOmsToWmsStorageCheck.setPsCSkuId(sgFreezeStorage.getPsCSkuId());
                    sgOmsToWmsStorageCheck.setPsCSkuEcode(sgFreezeStorage.getPsCSkuEcode());
                    sgOmsToWmsStorageCheck.setProduceDate(sgFreezeStorage.getProduceDate());
                    sgOmsToWmsStorageCheck.setPsCProId(sgFreezeStorage.getPsCProId());
                    sgOmsToWmsStorageCheck.setPsCProEcode(sgFreezeStorage.getPsCProEcode());
                    sgOmsToWmsStorageCheck.setPsCProEname(sgFreezeStorage.getPsCProEname());
                    sgOmsToWmsStorageCheck.setGbcode(sgFreezeStorage.getGbcode());
                    sgOmsToWmsStorageCheck.setPsCBrandId(sgFreezeStorage.getPsCBrandId());
                    sgOmsToWmsStorageCheck.setStorageType(sgFreezeStorage.getStockType());

                    sgOmsToWmsStorageCheck.setStorageDate(pastDate);
                    sgOmsToWmsStorageCheck.setWmsStotageQty(BigDecimal.ZERO);
                    sgOmsToWmsStorageCheck.setOmsStotageQty(totQtyFreeze);
                    sgOmsToWmsStorageCheck.setDiffQty(sgOmsToWmsStorageCheck.getWmsStotageQty().subtract(sgOmsToWmsStorageCheck.getOmsStotageQty()));
                    sgOmsToWmsStorageCheck.setResult(sgOmsToWmsStorageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                    sgOmsToWmsStorageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_WMS_STORAGE_CHECK));

                    sgOmsToWmsStorageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
                    sgOmsToWmsStorageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
                    sgOmsToWmsStorageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);
//                    sgOmsToWmsStorageCheck.setStorageType(SgConstantsIF.TRADE_MARK_CC);
                    StorageUtils.setBModelDefalutData(sgOmsToWmsStorageCheck, SystemUserResource.getRootUser());
                    resultList.add(sgOmsToWmsStorageCheck);
                }

                log.info(LogUtil.format("新增冻结库存期初数据size:{}", "resultList"),
                        CollectionUtils.isNotEmpty(resultList) ? resultList.size() : 0);

                if (CollectionUtils.isNotEmpty(resultList)) {
                    List<List<SgBOmsToWmsStorageCheck>> pageList = StorageUtils.getPageList(resultList, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
                    for (List<SgBOmsToWmsStorageCheck> page : pageList) {
                        checkMapper.batchInsert(page);
                    }
                }


            }
        }


    }

    /**
     * 初始化期初
     *
     * @param sgStorageList 库存
     * @param warehouse     sh实体仓
     * @param pastDate      riq日期
     */
    private void initStorage(List<SgBStorage> sgStorageList, SgCpCPhyWarehouse warehouse, Date pastDate) {

        log.info(LogUtil.format("库存期初size:{}", "sgStorageList"),
                CollectionUtils.isNotEmpty(sgStorageList) ? sgStorageList.size() : 0);

        List<SgBOmsToWmsStorageCheck> resultList = new ArrayList<>();
        Map<String, List<SgBStorage>> storageMap = sgStorageList.stream().collect(Collectors.groupingBy(x -> x.getPsCSkuId() + x.getProduceDate()));

        for (String key : storageMap.keySet()) {
            List<SgBStorage> sgStorages = storageMap.get(key);
            SgBStorage storage = sgStorages.get(0);

            BigDecimal totQtyStorage = sgStorages.stream().map(SgBStorage::getQtyStorage).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal totQtyFreeze = sgStorages.stream().map(SgBStorage::getQtyFreeze).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            SgBOmsToWmsStorageCheck sgOmsToWmsStorageCheck = new SgBOmsToWmsStorageCheck();

            sgOmsToWmsStorageCheck.setCpCPhyWarehouseId(warehouse.getId());
            sgOmsToWmsStorageCheck.setCpCPhyWarehouseEcode(warehouse.getEcode());
            sgOmsToWmsStorageCheck.setCpCPhyWarehouseEname(warehouse.getEname());
            sgOmsToWmsStorageCheck.setPsCSkuId(storage.getPsCSkuId());
            sgOmsToWmsStorageCheck.setPsCSkuEcode(storage.getPsCSkuEcode());
            sgOmsToWmsStorageCheck.setProduceDate(storage.getProduceDate());
            sgOmsToWmsStorageCheck.setPsCProId(storage.getPsCProId());
            sgOmsToWmsStorageCheck.setPsCProEcode(storage.getPsCProEcode());
            sgOmsToWmsStorageCheck.setPsCProEname(storage.getPsCProEname());
            sgOmsToWmsStorageCheck.setGbcode(storage.getGbcode());
            sgOmsToWmsStorageCheck.setPsCBrandId(storage.getPsCBrandId());
            sgOmsToWmsStorageCheck.setStorageType(StoreTypeEnum.ZP.getValue());

            sgOmsToWmsStorageCheck.setStorageDate(pastDate);
            sgOmsToWmsStorageCheck.setWmsStotageQty(BigDecimal.ZERO);
            sgOmsToWmsStorageCheck.setOmsStotageQty(totQtyStorage.subtract(totQtyFreeze));
            sgOmsToWmsStorageCheck.setDiffQty(sgOmsToWmsStorageCheck.getWmsStotageQty().subtract(sgOmsToWmsStorageCheck.getOmsStotageQty()));
            sgOmsToWmsStorageCheck.setResult(sgOmsToWmsStorageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
            sgOmsToWmsStorageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_WMS_STORAGE_CHECK));

            sgOmsToWmsStorageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
            sgOmsToWmsStorageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
            sgOmsToWmsStorageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);
            StorageUtils.setBModelDefalutData(sgOmsToWmsStorageCheck, SystemUserResource.getRootUser());
            resultList.add(sgOmsToWmsStorageCheck);
        }

        log.info(LogUtil.format("新增库存期初数据size:{}", "resultList"),
                CollectionUtils.isNotEmpty(resultList) ? resultList.size() : 0);

        if (CollectionUtils.isNotEmpty(resultList)) {
            List<List<SgBOmsToWmsStorageCheck>> pageList = StorageUtils.getPageList(resultList, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
            for (List<SgBOmsToWmsStorageCheck> page : pageList) {
                checkMapper.batchInsert(page);
            }
        }

    }

    /**
     * @param sgOmsToWmsStorageChecks 前天的库存记录，当成昨天的期初
     * @param sgStorageChangeFtpList  昨天的逻辑仓在库变动流水
     * @param sgStorageFreezeFtps     昨天的逻辑仓冻结变动流水
     * @param warehouse               实体仓
     * @param pastDate                日期
     */
    private void storageCheck(List<SgBOmsToWmsStorageCheck> sgOmsToWmsStorageChecks,
                              List<SgBStorageChangeFtp> sgStorageChangeFtpList,
                              List<SgBStorageFreezeFtp> sgStorageFreezeFtps,
                              SgCpCPhyWarehouse warehouse, Date pastDate) {

        Map<String, List<SgBStorageChangeFtp>> changeFtpMap = new HashMap<>(16);
        Map<String, List<SgBStorageFreezeFtp>> storageFreezeFtpMap = new HashMap<>(16);
        Map<String, List<SgBStorageFreezeFtp>> freezeFtpMap = new HashMap<>(16);
        Map<String, List<SgBOmsToWmsStorageCheck>> sgOmsToWmsStorageCheckMap = new HashMap<>(16);

        if (CollectionUtils.isNotEmpty(sgStorageChangeFtpList)) {
            //条码+生产日期分组
            changeFtpMap = sgStorageChangeFtpList.stream().collect(Collectors.groupingBy(x
                    -> x.getPsCSkuId() + SgConstantsIF.MAP_KEY_DIVIDER + x.getProduceDate()));
        }

        if (CollectionUtils.isNotEmpty(sgStorageFreezeFtps)) {
            //条码+生产日期+库存类型分组
            storageFreezeFtpMap = sgStorageFreezeFtps.stream().collect(Collectors.groupingBy(x
                    -> x.getPsCSkuId() + SgConstantsIF.MAP_KEY_DIVIDER + x.getProduceDate() + SgConstantsIF.MAP_KEY_DIVIDER + x.getStockType()));

            //条码+生产日期分组
            freezeFtpMap = sgStorageFreezeFtps.stream().collect(Collectors.groupingBy(x
                    -> x.getPsCSkuId() + SgConstantsIF.MAP_KEY_DIVIDER + x.getProduceDate()));
        }

        if (CollectionUtils.isNotEmpty(sgOmsToWmsStorageChecks)) {
            //条码+生产日期+库存类型分组
            sgOmsToWmsStorageCheckMap = sgOmsToWmsStorageChecks.stream().collect(Collectors.groupingBy(x
                    -> x.getPsCSkuId() + SgConstantsIF.MAP_KEY_DIVIDER + x.getProduceDate() + SgConstantsIF.MAP_KEY_DIVIDER + x.getStorageType()));
        }


        List<SgBOmsToWmsStorageCheck> insetCheck = checkChangeFtp(changeFtpMap, freezeFtpMap, sgOmsToWmsStorageCheckMap, sgOmsToWmsStorageChecks, warehouse, pastDate);
        insetCheck.addAll(checkFreezeFtp(storageFreezeFtpMap, sgOmsToWmsStorageCheckMap, sgOmsToWmsStorageChecks, warehouse, pastDate));

        log.info(LogUtil.format("新增库存流水数据size:{}", "insetCheck"),
                CollectionUtils.isNotEmpty(insetCheck) ? insetCheck.size() : 0);

        if (CollectionUtils.isNotEmpty(insetCheck)) {
            List<List<SgBOmsToWmsStorageCheck>> pageList = StorageUtils.getPageList(insetCheck, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
            for (List<SgBOmsToWmsStorageCheck> page : pageList) {
                checkMapper.batchInsert(page);
            }
        }

    }

    /**
     * 比对冻结流水
     *
     * @param storageFreezeFtpMap       冻结变动
     * @param sgOmsToWmsStorageCheckMap 中间表
     * @param warehouse                 实体仓
     * @param pastDate                  日期
     * @return SgBOmsToWmsStorageCheck
     */
    private List<SgBOmsToWmsStorageCheck> checkFreezeFtp(Map<String, List<SgBStorageFreezeFtp>> storageFreezeFtpMap,
                                                         Map<String, List<SgBOmsToWmsStorageCheck>> sgOmsToWmsStorageCheckMap,
                                                         List<SgBOmsToWmsStorageCheck> omsToWmsStorageChecks,
                                                         SgCpCPhyWarehouse warehouse, Date pastDate) {
        List<SgBOmsToWmsStorageCheck> resultList = new ArrayList<>();
        if (MapUtils.isNotEmpty(storageFreezeFtpMap)) {

            // 有期初，但是没流水
            //拿出非正品
            Map<String, List<SgBOmsToWmsStorageCheck>> checkMap = omsToWmsStorageChecks.stream().filter(x -> !SgConstantsIF.TRADE_MARK_ZP.equals(x.getStorageType()))
                    .collect(Collectors.groupingBy(x -> x.getPsCSkuId() + SgConstantsIF.MAP_KEY_DIVIDER + x.getProduceDate() + SgConstantsIF.MAP_KEY_DIVIDER + x.getStorageType()));

            //对比拿出有期初，但是没流水纪律
            List<String> checkMapKeyList = new ArrayList<>(checkMap.keySet());
            List<String> storageFreezeFtpMapKeyList = new ArrayList<>(storageFreezeFtpMap.keySet());

            List<String> mapKey = checkMapKeyList.stream().filter(x -> !storageFreezeFtpMapKeyList.contains(x)).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(mapKey)) {

                log.info(LogUtil.format("有期初，但是没流水 冻结流水 ", "storageFreezeFtpMapKeyList"));

                for (String key : mapKey) {
                    SgBOmsToWmsStorageCheck sgOmsToWmsStorageCheck = new SgBOmsToWmsStorageCheck();
                    List<SgBOmsToWmsStorageCheck> sgOmsToWmsStorageChecks = checkMap.get(key);
                    BeanUtils.copyProperties(sgOmsToWmsStorageChecks.get(0), sgOmsToWmsStorageCheck);
                    sgOmsToWmsStorageCheck.setStorageDate(pastDate);
                    sgOmsToWmsStorageCheck.setWmsStotageQty(BigDecimal.ZERO);
                    sgOmsToWmsStorageCheck.setOmsStotageQty(sgOmsToWmsStorageChecks.get(0).getOmsStotageQty());
                    sgOmsToWmsStorageCheck.setDiffQty(sgOmsToWmsStorageCheck.getWmsStotageQty().subtract(sgOmsToWmsStorageCheck.getOmsStotageQty()));
                    sgOmsToWmsStorageCheck.setResult(sgOmsToWmsStorageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                    sgOmsToWmsStorageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_WMS_STORAGE_CHECK));

                    sgOmsToWmsStorageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
                    sgOmsToWmsStorageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
                    sgOmsToWmsStorageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);
                    StorageUtils.setBModelDefalutData(sgOmsToWmsStorageCheck, SystemUserResource.getRootUser());
//                    sgOmsToWmsStorageCheck.setStorageType(SgConstantsIF.TRADE_MARK_CC);
                    resultList.add(sgOmsToWmsStorageCheck);

                }
            }


            for (String key : storageFreezeFtpMap.keySet()) {
                List<SgBStorageFreezeFtp> sgStorageFreezeFtps = storageFreezeFtpMap.get(key);
                BigDecimal freezeQtyChane = sgStorageFreezeFtps.stream().map(SgBStorageFreezeFtp::getQtyChange).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                SgBOmsToWmsStorageCheck sgOmsToWmsStorageCheck = new SgBOmsToWmsStorageCheck();
                if (MapUtils.isNotEmpty(sgOmsToWmsStorageCheckMap) && sgOmsToWmsStorageCheckMap.containsKey(key)) {
                    List<SgBOmsToWmsStorageCheck> sgOmsToWmsStorageChecks = sgOmsToWmsStorageCheckMap.get(key);
                    BeanUtils.copyProperties(sgOmsToWmsStorageChecks.get(0), sgOmsToWmsStorageCheck);
                    sgOmsToWmsStorageCheck.setOmsStotageQty(sgOmsToWmsStorageChecks.get(0).getOmsStotageQty());
                } else {
                    SgBStorageFreezeFtp sgStorageFreezeFtp = sgStorageFreezeFtps.get(0);
                    sgOmsToWmsStorageCheck.setCpCPhyWarehouseId(warehouse.getId());
                    sgOmsToWmsStorageCheck.setCpCPhyWarehouseEcode(warehouse.getEcode());
                    sgOmsToWmsStorageCheck.setCpCPhyWarehouseEname(warehouse.getEname());
                    sgOmsToWmsStorageCheck.setPsCSkuId(sgStorageFreezeFtp.getPsCSkuId());
                    sgOmsToWmsStorageCheck.setPsCSkuEcode(sgStorageFreezeFtp.getPsCSkuEcode());
                    sgOmsToWmsStorageCheck.setProduceDate(sgStorageFreezeFtp.getProduceDate());
                    sgOmsToWmsStorageCheck.setPsCProId(sgStorageFreezeFtp.getPsCProId());
                    sgOmsToWmsStorageCheck.setPsCProEcode(sgStorageFreezeFtp.getPsCProEcode());
                    sgOmsToWmsStorageCheck.setPsCProEname(sgStorageFreezeFtp.getPsCProEname());
                    sgOmsToWmsStorageCheck.setGbcode(sgStorageFreezeFtp.getGbcode());
                    sgOmsToWmsStorageCheck.setPsCBrandId(sgStorageFreezeFtp.getPsCBrandId());
                    sgOmsToWmsStorageCheck.setOmsStotageQty(BigDecimal.ZERO);
                    sgOmsToWmsStorageCheck.setStorageType(sgStorageFreezeFtp.getStockType());
                }
                sgOmsToWmsStorageCheck.setStorageDate(pastDate);
                sgOmsToWmsStorageCheck.setWmsStotageQty(BigDecimal.ZERO);
                sgOmsToWmsStorageCheck.setOmsStotageQty(sgOmsToWmsStorageCheck.getOmsStotageQty().add(freezeQtyChane));
                sgOmsToWmsStorageCheck.setDiffQty(sgOmsToWmsStorageCheck.getWmsStotageQty().subtract(sgOmsToWmsStorageCheck.getOmsStotageQty()));
                sgOmsToWmsStorageCheck.setResult(sgOmsToWmsStorageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                sgOmsToWmsStorageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_WMS_STORAGE_CHECK));

                sgOmsToWmsStorageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
                sgOmsToWmsStorageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
                sgOmsToWmsStorageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);
                StorageUtils.setBModelDefalutData(sgOmsToWmsStorageCheck, SystemUserResource.getRootUser());
//                sgOmsToWmsStorageCheck.setStorageType(SgConstantsIF.TRADE_MARK_CC);
                resultList.add(sgOmsToWmsStorageCheck);
            }
        } else {
            if (MapUtils.isNotEmpty(sgOmsToWmsStorageCheckMap)) {
                //拿出所有非正品
                for (String key : sgOmsToWmsStorageCheckMap.keySet()) {
                    String[] split = key.split(SgConstantsIF.MAP_KEY_DIVIDER);
                    if (!SgConstantsIF.TRADE_MARK_ZP.equals(split[2])) {
                        SgBOmsToWmsStorageCheck sgOmsToWmsStorageCheck = new SgBOmsToWmsStorageCheck();
                        List<SgBOmsToWmsStorageCheck> sgOmsToWmsStorageChecks = sgOmsToWmsStorageCheckMap.get(key);

                        BeanUtils.copyProperties(sgOmsToWmsStorageChecks.get(0), sgOmsToWmsStorageCheck);
                        sgOmsToWmsStorageCheck.setStorageDate(pastDate);
                        sgOmsToWmsStorageCheck.setWmsStotageQty(BigDecimal.ZERO);
                        sgOmsToWmsStorageCheck.setOmsStotageQty(sgOmsToWmsStorageChecks.get(0).getOmsStotageQty());
                        sgOmsToWmsStorageCheck.setDiffQty(sgOmsToWmsStorageCheck.getWmsStotageQty().subtract(sgOmsToWmsStorageCheck.getOmsStotageQty()));
                        sgOmsToWmsStorageCheck.setResult(sgOmsToWmsStorageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                        sgOmsToWmsStorageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_WMS_STORAGE_CHECK));

                        sgOmsToWmsStorageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
                        sgOmsToWmsStorageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
                        sgOmsToWmsStorageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);
                        StorageUtils.setBModelDefalutData(sgOmsToWmsStorageCheck, SystemUserResource.getRootUser());
//                        sgOmsToWmsStorageCheck.setStorageType(SgConstantsIF.TRADE_MARK_CC);
                        resultList.add(sgOmsToWmsStorageCheck);
                    }
                }
            }
        }

        return resultList;
    }

    /**
     * 比对在库流水
     * ps:在库流水全部当成正品
     *
     * @param changeFtpMap              在库变动
     * @param freezeFtpMap              冻结变动
     * @param sgOmsToWmsStorageCheckMap 中间表
     * @param omsToWmsStorageChecks     前天的库存记录，当成昨天的期初
     * @param warehouse                 实体仓
     * @param pastDate                  日期
     * @return SgBOmsToWmsStorageCheck
     */
    private List<SgBOmsToWmsStorageCheck> checkChangeFtp(Map<String, List<SgBStorageChangeFtp>> changeFtpMap,
                                                         Map<String, List<SgBStorageFreezeFtp>> freezeFtpMap,
                                                         Map<String, List<SgBOmsToWmsStorageCheck>> sgOmsToWmsStorageCheckMap,
                                                         List<SgBOmsToWmsStorageCheck> omsToWmsStorageChecks,
                                                         SgCpCPhyWarehouse warehouse, Date pastDate) {

        List<SgBOmsToWmsStorageCheck> resultList = new ArrayList<>();

        if (MapUtils.isNotEmpty(changeFtpMap)) {
            // 有期初，但是没流水
            //拿出正品
            Map<String, List<SgBOmsToWmsStorageCheck>> checkMap = omsToWmsStorageChecks.stream().filter(x -> SgConstantsIF.TRADE_MARK_ZP.equals(x.getStorageType()))
                    .collect(Collectors.groupingBy(x -> x.getPsCSkuId() + SgConstantsIF.MAP_KEY_DIVIDER + x.getProduceDate()));

            //对比拿出有期初，但是没流水纪律
            List<String> checkMapKeyList = new ArrayList<>(checkMap.keySet());
            List<String> changeFtpMapKeyList = new ArrayList<>(changeFtpMap.keySet());

            List<String> mapKey = checkMapKeyList.stream().filter(x -> !changeFtpMapKeyList.contains(x)).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(mapKey)) {

                log.info(LogUtil.format("有期初，但是没流水 ", "checkMapKeyList"));

                for (String key : mapKey) {

                    SgBOmsToWmsStorageCheck sgOmsToWmsStorageCheck = new SgBOmsToWmsStorageCheck();
                    List<SgBOmsToWmsStorageCheck> sgOmsToWmsStorageChecks = checkMap.get(key);
                    BeanUtils.copyProperties(sgOmsToWmsStorageChecks.get(0), sgOmsToWmsStorageCheck);
                    sgOmsToWmsStorageCheck.setStorageDate(pastDate);
                    sgOmsToWmsStorageCheck.setStorageType(SgConstantsIF.TRADE_MARK_ZP);
                    sgOmsToWmsStorageCheck.setWmsStotageQty(BigDecimal.ZERO);
                    sgOmsToWmsStorageCheck.setOmsStotageQty(sgOmsToWmsStorageChecks.get(0).getOmsStotageQty());

                    //变动数量” - 满足条件的【逻辑仓冻结变动流水】实体仓+条码+生产日期分组汇总“变动数量” }
                    if (MapUtils.isNotEmpty(freezeFtpMap) && freezeFtpMap.containsKey(key)) {
                        List<SgBStorageFreezeFtp> sgStorageFreezeFtps = freezeFtpMap.get(key);
                        BigDecimal freezeQtyChane = sgStorageFreezeFtps.stream().map(SgBStorageFreezeFtp::getQtyChange).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        sgOmsToWmsStorageCheck.setOmsStotageQty(sgOmsToWmsStorageCheck.getOmsStotageQty().subtract(freezeQtyChane));
                    }


                    sgOmsToWmsStorageCheck.setDiffQty(sgOmsToWmsStorageCheck.getWmsStotageQty().subtract(sgOmsToWmsStorageCheck.getOmsStotageQty()));
                    sgOmsToWmsStorageCheck.setResult(sgOmsToWmsStorageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                    sgOmsToWmsStorageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_WMS_STORAGE_CHECK));

                    sgOmsToWmsStorageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
                    sgOmsToWmsStorageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
                    sgOmsToWmsStorageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);
                    StorageUtils.setBModelDefalutData(sgOmsToWmsStorageCheck, SystemUserResource.getRootUser());
                    resultList.add(sgOmsToWmsStorageCheck);

                }
            }


            for (String key : changeFtpMap.keySet()) {
                List<SgBStorageChangeFtp> sgStorageChangeFtpList = changeFtpMap.get(key);
                //变动量
                BigDecimal qtyChange = sgStorageChangeFtpList.stream().map(SgBStorageChangeFtp::getQtyChange).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                //变动数量” - 满足条件的【逻辑仓冻结变动流水】实体仓+条码+生产日期分组汇总“变动数量” }
                if (MapUtils.isNotEmpty(freezeFtpMap) && freezeFtpMap.containsKey(key)) {
                    List<SgBStorageFreezeFtp> sgStorageFreezeFtps = freezeFtpMap.get(key);
                    BigDecimal freezeQtyChane = sgStorageFreezeFtps.stream().map(SgBStorageFreezeFtp::getQtyChange).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    qtyChange = qtyChange.subtract(freezeQtyChane);
                }

                //条码+生产日期+库存类型分组+实体仓可以确定唯一
                SgBOmsToWmsStorageCheck sgOmsToWmsStorageCheck = new SgBOmsToWmsStorageCheck();
                if (MapUtils.isNotEmpty(sgOmsToWmsStorageCheckMap) &&
                        sgOmsToWmsStorageCheckMap.containsKey(key + SgConstantsIF.MAP_KEY_DIVIDER + SgConstantsIF.TRADE_MARK_ZP)) {
                    List<SgBOmsToWmsStorageCheck> sgOmsToWmsStorageChecks = sgOmsToWmsStorageCheckMap.get(key + SgConstantsIF.MAP_KEY_DIVIDER + SgConstantsIF.TRADE_MARK_ZP);
                    BeanUtils.copyProperties(sgOmsToWmsStorageChecks.get(0), sgOmsToWmsStorageCheck);
                    sgOmsToWmsStorageCheck.setOmsStotageQty(sgOmsToWmsStorageChecks.get(0).getOmsStotageQty());
                } else {
                    SgBStorageChangeFtp sgStorageChangeFtp = sgStorageChangeFtpList.get(0);
                    sgOmsToWmsStorageCheck.setCpCPhyWarehouseId(warehouse.getId());
                    sgOmsToWmsStorageCheck.setCpCPhyWarehouseEcode(warehouse.getEcode());
                    sgOmsToWmsStorageCheck.setCpCPhyWarehouseEname(warehouse.getEname());
                    sgOmsToWmsStorageCheck.setPsCSkuId(sgStorageChangeFtp.getPsCSkuId());
                    sgOmsToWmsStorageCheck.setPsCSkuEcode(sgStorageChangeFtp.getPsCSkuEcode());
                    sgOmsToWmsStorageCheck.setProduceDate(sgStorageChangeFtp.getProduceDate());
                    sgOmsToWmsStorageCheck.setPsCProId(sgStorageChangeFtp.getPsCProId());
                    sgOmsToWmsStorageCheck.setPsCProEcode(sgStorageChangeFtp.getPsCProEcode());
                    sgOmsToWmsStorageCheck.setPsCProEname(sgStorageChangeFtp.getPsCProEname());
                    sgOmsToWmsStorageCheck.setGbcode(sgStorageChangeFtp.getGbcode());
                    sgOmsToWmsStorageCheck.setPsCBrandId(sgStorageChangeFtp.getPsCBrandId());
                    sgOmsToWmsStorageCheck.setOmsStotageQty(BigDecimal.ZERO);
                }
                sgOmsToWmsStorageCheck.setStorageDate(pastDate);
                sgOmsToWmsStorageCheck.setStorageType(SgConstantsIF.TRADE_MARK_ZP);
                sgOmsToWmsStorageCheck.setWmsStotageQty(BigDecimal.ZERO);
                sgOmsToWmsStorageCheck.setOmsStotageQty(sgOmsToWmsStorageCheck.getOmsStotageQty().add(qtyChange));
                sgOmsToWmsStorageCheck.setDiffQty(sgOmsToWmsStorageCheck.getWmsStotageQty().subtract(sgOmsToWmsStorageCheck.getOmsStotageQty()));
                sgOmsToWmsStorageCheck.setResult(sgOmsToWmsStorageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                sgOmsToWmsStorageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_WMS_STORAGE_CHECK));

                sgOmsToWmsStorageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
                sgOmsToWmsStorageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
                sgOmsToWmsStorageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);
                StorageUtils.setBModelDefalutData(sgOmsToWmsStorageCheck, SystemUserResource.getRootUser());
                resultList.add(sgOmsToWmsStorageCheck);
            }
        } else {
            if (MapUtils.isNotEmpty(sgOmsToWmsStorageCheckMap)) {
                //拿出所有正品
                for (String key : sgOmsToWmsStorageCheckMap.keySet()) {
                    String[] split = key.split(SgConstantsIF.MAP_KEY_DIVIDER);
                    if (SgConstantsIF.TRADE_MARK_ZP.equals(split[2])) {
                        SgBOmsToWmsStorageCheck sgOmsToWmsStorageCheck = new SgBOmsToWmsStorageCheck();
                        List<SgBOmsToWmsStorageCheck> sgOmsToWmsStorageChecks = sgOmsToWmsStorageCheckMap.get(key);
                        BeanUtils.copyProperties(sgOmsToWmsStorageChecks.get(0), sgOmsToWmsStorageCheck);
                        sgOmsToWmsStorageCheck.setStorageDate(pastDate);
                        sgOmsToWmsStorageCheck.setStorageType(SgConstantsIF.TRADE_MARK_ZP);
                        sgOmsToWmsStorageCheck.setWmsStotageQty(BigDecimal.ZERO);
                        sgOmsToWmsStorageCheck.setOmsStotageQty(sgOmsToWmsStorageChecks.get(0).getOmsStotageQty());

                        //变动数量” - 满足条件的【逻辑仓冻结变动流水】实体仓+条码+生产日期分组汇总“变动数量” }
                        if (MapUtils.isNotEmpty(freezeFtpMap) &&
                                freezeFtpMap.containsKey(split[0] + SgConstantsIF.MAP_KEY_DIVIDER + split[1])) {
                            List<SgBStorageFreezeFtp> sgStorageFreezeFtps = freezeFtpMap.get(split[0] + SgConstantsIF.MAP_KEY_DIVIDER + split[1]);
                            BigDecimal freezeQtyChane = sgStorageFreezeFtps.stream().map(SgBStorageFreezeFtp::getQtyChange).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                            sgOmsToWmsStorageCheck.setOmsStotageQty(sgOmsToWmsStorageCheck.getOmsStotageQty().subtract(freezeQtyChane));
                        }


                        sgOmsToWmsStorageCheck.setDiffQty(sgOmsToWmsStorageCheck.getWmsStotageQty().subtract(sgOmsToWmsStorageCheck.getOmsStotageQty()));
                        sgOmsToWmsStorageCheck.setResult(sgOmsToWmsStorageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                        sgOmsToWmsStorageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_WMS_STORAGE_CHECK));

                        sgOmsToWmsStorageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
                        sgOmsToWmsStorageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
                        sgOmsToWmsStorageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);
                        StorageUtils.setBModelDefalutData(sgOmsToWmsStorageCheck, SystemUserResource.getRootUser());
                        resultList.add(sgOmsToWmsStorageCheck);
                    }
                }
            }
        }

        return resultList;
    }

    /**
     * wms 库存
     *
     * @return ValueHolderV14
     */
    public ValueHolderV14 wmsStorageCheck() {
        log.info(LogUtil.format("OMS与WMS库存核对服务 查询WMS库存 start",
                "SgBOmsToWmsStotageCheckService.wmsStorageCheck"));
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "OMS与WMS库存核对成功！");
        try {
            Integer day = getDay();
            Date pastDate = DateUtils.getPastDate(day, new Date());
            Integer selectCount = lsStorageMapper.selectCount(new LambdaQueryWrapper<SgBWmsToLsStorage>()
                    .lt(SgBWmsToLsStorage::getStockDate, pastDate));
            if (selectCount > 0) {
                lsStorageMapper.delete(new LambdaQueryWrapper<SgBWmsToLsStorage>()
                        .lt(SgBWmsToLsStorage::getStockDate, pastDate));
            }

            batchInsertWmsStorage();
        } catch (Exception e) {
            log.error(LogUtil.format("SgBOmsToWmsStorageCheckService.wmsStorageCheck.error:{}",
                    "SgBOmsToWmsStorageCheckService.wmsStorageCheck.error", Throwables.getStackTraceAsString(e)));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }

        log.info(LogUtil.format("OMS与WMS库存核对服务 查询WMS库存 end:{}",
                "SgBOmsToWmsStotageCheckService.wmsStorageCheck"), JSONObject.toJSONString(v14));
        return v14;
    }

    /**
     * 批量新增WMS库
     */
    private void batchInsertWmsStorage() throws ParseException {
        //获取实体仓 分仓去查OMS与WMS库存核对表
        List<SgCpCPhyWarehouse> sgCpPhyWarehouses = cPhyWarehouseMapper.selectList(new LambdaQueryWrapper<SgCpCPhyWarehouse>()
                .eq(SgCpCPhyWarehouse::getIsactive, SgConstants.IS_ACTIVE_Y));

        log.info(LogUtil.format("实体仓数量:{}", "sgCpPhyWarehouses"),
                CollectionUtils.isNotEmpty(sgCpPhyWarehouses) ? sgCpPhyWarehouses.size() : 0);

        if (CollectionUtils.isNotEmpty(sgCpPhyWarehouses)) {

            Date pastDate = DateUtils.getPastDate(1, new Date());

            log.info(LogUtil.format("系统日期前一天:{}", "wms.pastDate"), pastDate);

            // 昨天的记录删除
//            Integer selectCount = lsStorageMapper.selectCount(new LambdaQueryWrapper<SgBWmsToLsStorage>()
//                    .eq(SgBWmsToLsStorage::getStockDate, pastDate));
//            if (selectCount > 0) {
//                lsStorageMapper.delete(new LambdaQueryWrapper<SgBWmsToLsStorage>()
//                        .eq(SgBWmsToLsStorage::getStockDate, pastDate));
//            }

            for (SgCpCPhyWarehouse warehouse : sgCpPhyWarehouses) {
                try {

                    List<SgBWmsToLsStorage> sgWmsToLsStorageList = lsStorageMapper.selectList(new LambdaQueryWrapper<SgBWmsToLsStorage>()
                            .eq(SgBWmsToLsStorage::getStockDate, pastDate)
                            .eq(SgBWmsToLsStorage::getWmsWarehouseCode, warehouse.getEcode()));

                    List<SgBOmsToWmsStorageCheck> sgOmsToWmsStorageChecks = checkMapper.selectList(new LambdaQueryWrapper<SgBOmsToWmsStorageCheck>()
                            .eq(SgBOmsToWmsStorageCheck::getStorageDate, pastDate)
                            .eq(SgBOmsToWmsStorageCheck::getCpCPhyWarehouseEcode, warehouse.getEcode()));

                    log.info(LogUtil.format("WMS期初数据:{},OMS期初数据:{}", "sgWmsToLsStorageList"),
                            CollectionUtils.isNotEmpty(sgWmsToLsStorageList) ? JSONObject.toJSONString(sgWmsToLsStorageList) : null,
                            CollectionUtils.isNotEmpty(sgOmsToWmsStorageChecks) ? JSONObject.toJSONString(sgOmsToWmsStorageChecks) : null);

                    if (CollectionUtils.isNotEmpty(sgWmsToLsStorageList)) {
                        checkWmsStorage(sgWmsToLsStorageList, sgOmsToWmsStorageChecks, warehouse, pastDate);
                    }

                } catch (Exception e) {
                    log.error(LogUtil.format("SgBOmsToWmsStorageCheckService.error:{}",
                            "SgBOmsToWmsStorageCheckService.error", Throwables.getStackTraceAsString(e)));
                }
            }

        }
    }

    /**
     * 比较OMS库存跟WMS库存
     *
     * @param sgWmsToLsStorageList    WMS库存
     * @param sgOmsToWmsStorageChecks OMS库存
     * @param warehouse               实体仓
     * @param pastDate                日期
     */
    private void checkWmsStorage(List<SgBWmsToLsStorage> sgWmsToLsStorageList, List<SgBOmsToWmsStorageCheck> sgOmsToWmsStorageChecks,
                                 SgCpCPhyWarehouse warehouse, Date pastDate) {
        //仓库编码+商品SKU+库存类型+生产日期 唯一
        Map<String, List<SgBWmsToLsStorage>> sgWmsToLsStorageMap = sgWmsToLsStorageList.stream().collect(Collectors.groupingBy(
                x -> x.getPsCSkuEcode() + SgConstantsIF.MAP_KEY_DIVIDER + x.getStorageType() + SgConstantsIF.MAP_KEY_DIVIDER + x.getProduceDate()));

        //仓库编码+商品SKU+库存类型+生产日期 唯一
        Map<String, List<SgBOmsToWmsStorageCheck>> storageCheckMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(sgOmsToWmsStorageChecks)) {
            storageCheckMap = sgOmsToWmsStorageChecks.stream().collect(Collectors.groupingBy(
                    x -> x.getPsCSkuEcode() + SgConstantsIF.MAP_KEY_DIVIDER + x.getStorageType() + SgConstantsIF.MAP_KEY_DIVIDER + x.getProduceDate()));
        }

        List<SgBOmsToWmsStorageCheck> insertList = new ArrayList<>();
        List<SgBOmsToWmsStorageCheck> updateList = new ArrayList<>();

        for (String key : sgWmsToLsStorageMap.keySet()) {
            SgBWmsToLsStorage sgWmsToLsStorage = sgWmsToLsStorageMap.get(key).get(0);
            SgBOmsToWmsStorageCheck storageCheck = new SgBOmsToWmsStorageCheck();
            if (MapUtils.isNotEmpty(storageCheckMap) && storageCheckMap.containsKey(key)) {
                SgBOmsToWmsStorageCheck sgOmsToWmsStorageCheck = storageCheckMap.get(key).get(0);
                storageCheck.setId(sgOmsToWmsStorageCheck.getId());
                storageCheck.setWmsStotageQty(sgWmsToLsStorage.getQty());
                storageCheck.setDiffQty(sgOmsToWmsStorageCheck.getOmsStotageQty().subtract(sgWmsToLsStorage.getQty()));
                storageCheck.setResult(storageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                StorageUtils.setBModelDefalutDataByUpdate(storageCheck, SystemUserResource.getRootUser());
                updateList.add(storageCheck);
            } else {
                storageCheck.setCpCPhyWarehouseId(warehouse.getId());
                storageCheck.setCpCPhyWarehouseEcode(warehouse.getEcode());
                storageCheck.setCpCPhyWarehouseEname(warehouse.getEname());
                storageCheck.setPsCSkuId(sgWmsToLsStorage.getPsCSkuId());
                storageCheck.setPsCSkuEcode(sgWmsToLsStorage.getPsCSkuEcode());
                storageCheck.setProduceDate(sgWmsToLsStorage.getProduceDate());
//                storageCheck.setPsCProId(sgWmsToLsStorage.getPsCProId());
//                storageCheck.setPsCProEcode(sgWmsToLsStorage.getPsCProEcode());
//                storageCheck.setPsCProEname(sgWmsToLsStorage.getPsCProEname());
//                storageCheck.setGbcode(sgWmsToLsStorage.getGbcode());
//                storageCheck.setPsCBrandId(sgWmsToLsStorage.getPsCBrandId());
                storageCheck.setStorageDate(pastDate);
                storageCheck.setStorageType(sgWmsToLsStorage.getStorageType());
                storageCheck.setWmsStotageQty(sgWmsToLsStorage.getQty());
                storageCheck.setOmsStotageQty(BigDecimal.ZERO);
                storageCheck.setDiffQty(storageCheck.getWmsStotageQty().subtract(storageCheck.getOmsStotageQty()));
                storageCheck.setResult(storageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                storageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_WMS_STORAGE_CHECK));

                storageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
                storageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
                storageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);

                StorageUtils.setBModelDefalutData(storageCheck, SystemUserResource.getRootUser());
                insertList.add(storageCheck);
            }
        }

        if (CollectionUtils.isNotEmpty(insertList)) {
            List<List<SgBOmsToWmsStorageCheck>> pageList = StorageUtils.getPageList(insertList, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
            for (List<SgBOmsToWmsStorageCheck> page : pageList) {
                checkMapper.batchInsert(page);
            }
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            for (SgBOmsToWmsStorageCheck check : updateList) {
                checkMapper.updateById(check);
            }
        }

    }

    private Integer getDay() {
        try {
            String value = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(R3_SG_B_OMS_TO_WMS_STORAGE_CHECK_DAY);
            log.info(LogUtil.format("OMS与WMS库存核对服务 获取系统参数（OMS与WMS库存核对删除设置天数前数据）value：{}", "getDay"), value);
            if (StringUtils.isNotEmpty(value)) {
                return Integer.parseInt(value);
            }
            return 30;
        } catch (Exception e) {
            log.info(LogUtil.format("OMS与WMS库存核对服务 获取系统参数（OMS与WMS库存核对删除设置天数前数据）异常：{}",
                    "getDay.error"), e.getMessage());
            return 30;
        }
    }

    /**
     * 补偿接口
     *
     * @param pastDate       日期
     * @param warehousesCode return ValueHolderV14
     */
    public ValueHolderV14 batchInsertWmsStorageByPub(Date pastDate, String warehousesCode) {

        log.info(LogUtil.format("OMS库存与WMS库存比对补偿接口:入参时间{}:入参仓:{}", "batchInsertWmsStorageByPub"), pastDate, warehousesCode);

        //相隔天数 正常要比当前日期少一天；
        int diffDays = DateUtils.getDiffDays(pastDate, new Date());
        if (diffDays <= 0) {
            return new ValueHolderV14(ResultCode.FAIL, "日期只能小于当前日期");
        }

        for (int i = 0; i < diffDays; i++) {

            List<SgCpCPhyWarehouse> sgCpPhyWarehouseList = new ArrayList<>();

            //是否指定仓
            if (StringUtils.isNotEmpty(warehousesCode)) {

                sgCpPhyWarehouseList = cPhyWarehouseMapper.selectList(new LambdaQueryWrapper<SgCpCPhyWarehouse>()
                        .eq(SgCpCPhyWarehouse::getIsactive, SgConstants.IS_ACTIVE_Y)
                        .eq(SgCpCPhyWarehouse::getEcode, warehousesCode));

            } else {

                //获取实体仓 分仓去查OMS与WMS库存核对表
                sgCpPhyWarehouseList = cPhyWarehouseMapper.selectList(new LambdaQueryWrapper<SgCpCPhyWarehouse>()
                        .eq(SgCpCPhyWarehouse::getIsactive, SgConstants.IS_ACTIVE_Y));

            }

            log.info(LogUtil.format("实体仓数量:{}", "sgCpPhyWarehouseList"),
                    CollectionUtils.isNotEmpty(sgCpPhyWarehouseList) ? sgCpPhyWarehouseList.size() : 0);

            if (CollectionUtils.isNotEmpty(sgCpPhyWarehouseList)) {
                for (SgCpCPhyWarehouse warehouse : sgCpPhyWarehouseList) {
                    try {
                        ValueHolderV14 v14 = batchInsertOmsStorage(DateUtils.getNextDay(pastDate, i), warehouse);
                        if (!v14.isOK()) {
                            AssertUtils.logAndThrow("batchInsertWmsStorageByPub.error:" + v14.getMessage());
                        }
                        checkWmsOrOmsStorage(DateUtils.getNextDay(pastDate, i), warehouse);
                    } catch (Exception e) {
                        log.error(LogUtil.format("SgBOmsToWmsStorageCheckService.batchInsertWmsStorageByPub.日期:{}.仓:{}.error:{}",
                                "SgBOmsToWmsStorageCheckService.error"), pastDate, warehouse.getEcode(), Throwables.getStackTraceAsString(e));
                    }

                }
            } else {
                return new ValueHolderV14(ResultCode.FAIL, "未查询到实体仓，请检查！");
            }

        }

        return new ValueHolderV14(ResultCode.SUCCESS, "补偿成功！");
    }

    /**
     * 重新核对oms库存
     *
     * @param pastDate  日期
     * @param warehouse 实体仓
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 batchInsertOmsStorage(Date pastDate, SgCpCPhyWarehouse warehouse) {
        try {

            //删除传入日期的记录
            int count = checkMapper.selectCount(new LambdaQueryWrapper<SgBOmsToWmsStorageCheck>()
                    .eq(SgBOmsToWmsStorageCheck::getStorageDate, pastDate)
                    .eq(SgBOmsToWmsStorageCheck::getCpCPhyWarehouseEcode, warehouse.getEcode()));
            if (count > 0) {
                checkMapper.delete(new LambdaQueryWrapper<SgBOmsToWmsStorageCheck>()
                        .eq(SgBOmsToWmsStorageCheck::getStorageDate, pastDate)
                        .eq(SgBOmsToWmsStorageCheck::getCpCPhyWarehouseEcode, warehouse.getEcode()));
            }

            //期初
            Date lastDate = DateUtils.getPastDate(1, pastDate);
            List<SgBOmsToWmsStorageCheck> sgOmsToWmsStorageChecks = checkMapper.selectList(new LambdaQueryWrapper<SgBOmsToWmsStorageCheck>()
                    .eq(SgBOmsToWmsStorageCheck::getStorageDate, lastDate)
                    .eq(SgBOmsToWmsStorageCheck::getCpCPhyWarehouseEcode, warehouse.getEcode()));

            log.info(LogUtil.format("batchInsertOmsStorage.期初数据:{},", "sgOmsToWmsStorageChecks"),
                    CollectionUtils.isNotEmpty(sgOmsToWmsStorageChecks) ? JSONObject.toJSONString(sgOmsToWmsStorageChecks) : null);

            Map<Long, List<CpCStore>> storeInfoByPhyId = CommonCacheValUtils.getStoreInfoByPhyId(warehouse.getId());
            if (MapUtils.isEmpty(storeInfoByPhyId) || !storeInfoByPhyId.containsKey(warehouse.getId())) {
                log.info(LogUtil.format("根据实体仓查逻辑仓异常，未查询到逻辑仓信息，实体仓:{}", "getStoreInfoByPhyId"),
                        warehouse.getEcode());
                AssertUtils.logAndThrow("根据实体仓查逻辑仓异常，未查询到逻辑仓信息，实体仓:" + warehouse.getEcode());
            }

            List<CpCStore> stores = storeInfoByPhyId.get(warehouse.getId());

            if (CollectionUtils.isEmpty(sgOmsToWmsStorageChecks)) {
                initStorageAndFreezeStorage(stores, pastDate, warehouse);
                return new ValueHolderV14(ResultCode.SUCCESS, "初始化成功！");
            }

            //一头牛实体仓跟逻辑仓一对一
            List<Long> storeIdList = stores.stream().map(CpCStore::getId).collect(Collectors.toList());
            //逻辑仓在库变动流水
            List<SgBStorageChangeFtp> sgStorageChangeFtpList = storageChangeFtpMapper.selectList(new LambdaQueryWrapper<SgBStorageChangeFtp>()
                    .eq(SgBStorageChangeFtp::getChangeDate, pastDate)
                    .in(SgBStorageChangeFtp::getCpCStoreId, storeIdList));
            //逻辑仓冻结变动流水
            List<SgBStorageFreezeFtp> sgStorageFreezeFtps = freezeFtpMapper.selectList(new LambdaQueryWrapper<SgBStorageFreezeFtp>()
                    .eq(SgBStorageFreezeFtp::getChangeDate, pastDate)
                    .in(SgBStorageFreezeFtp::getCpCStoreId, storeIdList));

            log.info(LogUtil.format("batchInsertOmsStorage.期初:{},逻辑仓在库变动流水:{},逻辑仓冻结变动流水:{}", "sgStorageChangeFtpList"),
                    CollectionUtils.isNotEmpty(sgOmsToWmsStorageChecks) ? sgOmsToWmsStorageChecks.size() : 0,
                    CollectionUtils.isNotEmpty(sgStorageChangeFtpList) ? sgStorageChangeFtpList.size() : 0,
                    CollectionUtils.isNotEmpty(sgStorageFreezeFtps) ? sgStorageFreezeFtps.size() : 0);

            storageCheck(sgOmsToWmsStorageChecks, sgStorageChangeFtpList, sgStorageFreezeFtps, warehouse, pastDate);

        } catch (Exception e) {
            log.error(LogUtil.format("SgBOmsToWmsStorageCheckService.batchInsertOmsStorage.日期:{}.仓:{}.error:{}",
                    "SgBOmsToWmsStorageCheckService.error"), pastDate, warehouse.getEcode(), Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrow("SgBOmsToWmsStorageCheckService.error:" + e.getMessage());
        }
        return new ValueHolderV14(ResultCode.SUCCESS, "初始化成功！");
    }

    /**
     * 重新审核初始化
     *
     * @param stores    逻辑仓集合
     * @param pastDate  生产批次
     * @param warehouse 实体仓
     */
    private void initStorageAndFreezeStorage(List<CpCStore> stores, Date pastDate, SgCpCPhyWarehouse warehouse) {

        List<SgBOmsToWmsStorageCheck> resultList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(stores)) {
            List<Long> storeIdList = stores.stream().map(CpCStore::getId).collect(Collectors.toList());

            //逻辑仓在库变动流水 用id是因为有补的流水
            List<SgBStorageChangeFtp> sgStorageChangeFtpList = storageChangeFtpMapper.selectList(new LambdaQueryWrapper<SgBStorageChangeFtp>()
                    .le(SgBStorageChangeFtp::getChangeDate, pastDate)
                    .in(SgBStorageChangeFtp::getCpCStoreId, storeIdList));

            //逻辑仓冻结变动流水
            List<SgBStorageFreezeFtp> sgStorageFreezeFtps = freezeFtpMapper.selectList(new LambdaQueryWrapper<SgBStorageFreezeFtp>()
                    .le(SgBStorageFreezeFtp::getChangeDate, pastDate)
                    .in(SgBStorageFreezeFtp::getCpCStoreId, storeIdList));

            //初始化数据
            initStorageByPub(sgStorageChangeFtpList, sgStorageFreezeFtps, pastDate, warehouse, resultList);
            initFreezeStorageByPub(sgStorageFreezeFtps, pastDate, warehouse, resultList);

            log.info(LogUtil.format("重新审核init数据size:{}", "initStorageAndFreezeStorage"),
                    CollectionUtils.isNotEmpty(resultList) ? resultList.size() : 0);

            if (CollectionUtils.isNotEmpty(resultList)) {
                List<List<SgBOmsToWmsStorageCheck>> pageList = StorageUtils.getPageList(resultList, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
                for (List<SgBOmsToWmsStorageCheck> page : pageList) {
                    checkMapper.batchInsert(page);
                }
            }
        }

    }

    /**
     * 根据流水初始化oms库存
     *
     * @param sgStorageChangeFtpList 流水
     * @param pastDate               生产批次
     * @param warehouse              实体仓
     * @param resultList             新增数据
     */
    private void initStorageByPub(List<SgBStorageChangeFtp> sgStorageChangeFtpList, List<SgBStorageFreezeFtp> sgStorageFreezeFtps, Date pastDate,
                                  SgCpCPhyWarehouse warehouse, List<SgBOmsToWmsStorageCheck> resultList) {
        if (CollectionUtils.isNotEmpty(sgStorageChangeFtpList)) {
            //条码批次分组
            Map<String, List<SgBStorageChangeFtp>> changeFtp = sgStorageChangeFtpList.stream().collect(Collectors.groupingBy(x ->
                    x.getPsCSkuId() + SgConstantsIF.MAP_KEY_DIVIDER + x.getProduceDate()));

            Map<String, List<SgBStorageFreezeFtp>> changeFreezeFtp = sgStorageFreezeFtps.stream().collect(Collectors.groupingBy(x ->
                    x.getPsCSkuId() + SgConstantsIF.MAP_KEY_DIVIDER + x.getProduceDate()));

            for (String key : changeFtp.keySet()) {
                //求和，变动数量
                List<SgBStorageChangeFtp> storageChangeFtpList = changeFtp.get(key);

                List<SgBStorageFreezeFtp> sgFreezeFtps = changeFreezeFtp.get(key);

                BigDecimal totQtyChange = storageChangeFtpList.stream().map(SgBStorageChangeFtp::getQtyChange).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                if (CollectionUtils.isNotEmpty(sgFreezeFtps)) {
                    BigDecimal freezeQty = sgFreezeFtps.stream().map(SgBStorageFreezeFtp::getQtyChange).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    totQtyChange = totQtyChange.subtract(freezeQty);
                }

                SgBOmsToWmsStorageCheck sgOmsToWmsStorageCheck = new SgBOmsToWmsStorageCheck();
                SgBStorageChangeFtp sgStorageChangeFtp = storageChangeFtpList.get(0);
                sgOmsToWmsStorageCheck.setCpCPhyWarehouseId(warehouse.getId());
                sgOmsToWmsStorageCheck.setCpCPhyWarehouseEcode(warehouse.getEcode());
                sgOmsToWmsStorageCheck.setCpCPhyWarehouseEname(warehouse.getEname());
                sgOmsToWmsStorageCheck.setPsCSkuId(sgStorageChangeFtp.getPsCSkuId());
                sgOmsToWmsStorageCheck.setPsCSkuEcode(sgStorageChangeFtp.getPsCSkuEcode());
                sgOmsToWmsStorageCheck.setProduceDate(sgStorageChangeFtp.getProduceDate());
                sgOmsToWmsStorageCheck.setPsCProId(sgStorageChangeFtp.getPsCProId());
                sgOmsToWmsStorageCheck.setPsCProEcode(sgStorageChangeFtp.getPsCProEcode());
                sgOmsToWmsStorageCheck.setPsCProEname(sgStorageChangeFtp.getPsCProEname());
                sgOmsToWmsStorageCheck.setGbcode(sgStorageChangeFtp.getGbcode());
                sgOmsToWmsStorageCheck.setPsCBrandId(sgStorageChangeFtp.getPsCBrandId());
                sgOmsToWmsStorageCheck.setOmsStotageQty(BigDecimal.ZERO);

                sgOmsToWmsStorageCheck.setStorageDate(pastDate);
                sgOmsToWmsStorageCheck.setStorageType(SgConstantsIF.TRADE_MARK_ZP);
                sgOmsToWmsStorageCheck.setWmsStotageQty(BigDecimal.ZERO);
                sgOmsToWmsStorageCheck.setOmsStotageQty(totQtyChange);
                sgOmsToWmsStorageCheck.setDiffQty(sgOmsToWmsStorageCheck.getWmsStotageQty().subtract(sgOmsToWmsStorageCheck.getOmsStotageQty()));
                sgOmsToWmsStorageCheck.setResult(sgOmsToWmsStorageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                sgOmsToWmsStorageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_WMS_STORAGE_CHECK));

                sgOmsToWmsStorageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
                sgOmsToWmsStorageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
                sgOmsToWmsStorageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);
                StorageUtils.setBModelDefalutData(sgOmsToWmsStorageCheck, SystemUserResource.getRootUser());
                resultList.add(sgOmsToWmsStorageCheck);
            }

        }

    }

    /**
     * 根据流水初始化oms冻结库存
     *
     * @param sgStorageFreezeFtps 流水
     * @param pastDate            生产批次
     * @param warehouse           实体仓
     * @param resultList          新增数据
     */
    private void initFreezeStorageByPub(List<SgBStorageFreezeFtp> sgStorageFreezeFtps, Date pastDate,
                                        SgCpCPhyWarehouse warehouse, List<SgBOmsToWmsStorageCheck> resultList) {

        if (CollectionUtils.isNotEmpty(sgStorageFreezeFtps)) {
            //条码批次分组类型
            Map<String, List<SgBStorageFreezeFtp>> changeFtp = sgStorageFreezeFtps.stream().collect(Collectors.groupingBy(x ->
                    x.getPsCSkuId() + SgConstantsIF.MAP_KEY_DIVIDER + x.getProduceDate() + SgConstantsIF.MAP_KEY_DIVIDER + x.getStockType()));

            for (String key : changeFtp.keySet()) {
                //求和，变动数量
                List<SgBStorageFreezeFtp> storageFreezeFtps = changeFtp.get(key);

                BigDecimal totQtyChange = storageFreezeFtps.stream().map(SgBStorageFreezeFtp::getQtyChange).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                SgBOmsToWmsStorageCheck sgOmsToWmsStorageCheck = new SgBOmsToWmsStorageCheck();
                SgBStorageFreezeFtp sgStorageFreezeFtp = storageFreezeFtps.get(0);
                sgOmsToWmsStorageCheck.setCpCPhyWarehouseId(warehouse.getId());
                sgOmsToWmsStorageCheck.setCpCPhyWarehouseEcode(warehouse.getEcode());
                sgOmsToWmsStorageCheck.setCpCPhyWarehouseEname(warehouse.getEname());
                sgOmsToWmsStorageCheck.setPsCSkuId(sgStorageFreezeFtp.getPsCSkuId());
                sgOmsToWmsStorageCheck.setPsCSkuEcode(sgStorageFreezeFtp.getPsCSkuEcode());
                sgOmsToWmsStorageCheck.setProduceDate(sgStorageFreezeFtp.getProduceDate());
                sgOmsToWmsStorageCheck.setPsCProId(sgStorageFreezeFtp.getPsCProId());
                sgOmsToWmsStorageCheck.setPsCProEcode(sgStorageFreezeFtp.getPsCProEcode());
                sgOmsToWmsStorageCheck.setPsCProEname(sgStorageFreezeFtp.getPsCProEname());
                sgOmsToWmsStorageCheck.setGbcode(sgStorageFreezeFtp.getGbcode());
                sgOmsToWmsStorageCheck.setPsCBrandId(sgStorageFreezeFtp.getPsCBrandId());
                sgOmsToWmsStorageCheck.setOmsStotageQty(BigDecimal.ZERO);

                sgOmsToWmsStorageCheck.setStorageDate(pastDate);
                sgOmsToWmsStorageCheck.setStorageType(sgStorageFreezeFtp.getStockType());
                sgOmsToWmsStorageCheck.setWmsStotageQty(BigDecimal.ZERO);
                sgOmsToWmsStorageCheck.setOmsStotageQty(totQtyChange);
                sgOmsToWmsStorageCheck.setDiffQty(sgOmsToWmsStorageCheck.getWmsStotageQty().subtract(sgOmsToWmsStorageCheck.getOmsStotageQty()));
                sgOmsToWmsStorageCheck.setResult(sgOmsToWmsStorageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                sgOmsToWmsStorageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_WMS_STORAGE_CHECK));

                sgOmsToWmsStorageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
                sgOmsToWmsStorageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
                sgOmsToWmsStorageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);
                StorageUtils.setBModelDefalutData(sgOmsToWmsStorageCheck, SystemUserResource.getRootUser());
                resultList.add(sgOmsToWmsStorageCheck);
            }

        }
    }

    /**
     * 补偿接口 - 比对oms，wms库存
     *
     * @param pastDate  日期
     * @param warehouse 实体仓
     */
    private void checkWmsOrOmsStorage(Date pastDate, SgCpCPhyWarehouse warehouse) {

        try {

            log.info(LogUtil.format("日期:{}", "wms.pastDate"), pastDate);

            List<SgBWmsToLsStorage> sgWmsToLsStorageList = lsStorageMapper.selectList(new LambdaQueryWrapper<SgBWmsToLsStorage>()
                    .eq(SgBWmsToLsStorage::getStockDate, pastDate)
                    .eq(SgBWmsToLsStorage::getWmsWarehouseCode, warehouse.getEcode()));

            List<SgBOmsToWmsStorageCheck> sgOmsToWmsStorageChecks = checkMapper.selectList(new LambdaQueryWrapper<SgBOmsToWmsStorageCheck>()
                    .eq(SgBOmsToWmsStorageCheck::getStorageDate, pastDate)
                    .eq(SgBOmsToWmsStorageCheck::getCpCPhyWarehouseEcode, warehouse.getEcode()));

            log.info(LogUtil.format("WMS期初数据:{},OMS期初数据:{}", "batchInsertWmsStorageByPub.sgWmsToLsStorageList"),
                    CollectionUtils.isNotEmpty(sgWmsToLsStorageList) ? JSONObject.toJSONString(sgWmsToLsStorageList) : null,
                    CollectionUtils.isNotEmpty(sgOmsToWmsStorageChecks) ? JSONObject.toJSONString(sgOmsToWmsStorageChecks) : null);

            if (CollectionUtils.isNotEmpty(sgWmsToLsStorageList)) {
                checkWmsStorage(sgWmsToLsStorageList, sgOmsToWmsStorageChecks, warehouse, pastDate);
            }

        } catch (Exception e) {
            log.error(LogUtil.format("SgBOmsToWmsStorageCheckService.日期:{}.仓:{}.error:{}",
                    "SgBOmsToWmsStorageCheckService.error"), pastDate, warehouse.getEcode(), Throwables.getStackTraceAsString(e));
        }

    }

}
