package com.burgeon.r3.inf.filter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.inf.mapper.SgCOwnStoreSelectionMapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.inf.model.dto.SgCOwnStoreSelectionDto;
import com.burgeon.r3.sg.inf.model.table.SgCOwnStoreSelection;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2021/11/30 15:36
 * 自营门店选款
 */
@Slf4j
@Component
public class SgCOwnStoreSelectionSaveFilter extends BaseSingleFilter<SgCOwnStoreSelectionDto> {

    @Autowired
    private SgCOwnStoreSelectionMapper sgCOwnStoreSelectionMapper;

    @Override
    public String getFilterMsgName() {
        return "自营门店选款";
    }

    @Override
    public Class<?> getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgCOwnStoreSelectionDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("保存成功", loginUser.getLocale()));
        Long objId = mainObject.getId();
        if (objId == null || objId < 1L) {
            Long psCSkuId = mainObject.getPsCSkuId();

            PsCProSkuResult skuInfo = CommonCacheValUtils.getSkuInfo(psCSkuId);
            if (skuInfo == null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("条码信息有误");
                return v14;
            }
            Integer count = sgCOwnStoreSelectionMapper.selectCount(new LambdaQueryWrapper<SgCOwnStoreSelection>()
                    .eq(SgCOwnStoreSelection::getPsCSkuId, psCSkuId)
                    .eq(SgCOwnStoreSelection::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (count > 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("已存在对应条码记录");
                return v14;
            }
            mainObject.setPsCSkuId(skuInfo.getId());
            mainObject.setPsCSkuEcode(skuInfo.getSkuEcode());
            mainObject.setPsCProId(skuInfo.getPsCProId());
            mainObject.setPsCProEcode(skuInfo.getPsCProEcode());
            mainObject.setPsCProEname(skuInfo.getPsCProEname());
        }
        return v14;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgCOwnStoreSelectionDto mainObject, User loginUser) {
        return null;
    }
}
