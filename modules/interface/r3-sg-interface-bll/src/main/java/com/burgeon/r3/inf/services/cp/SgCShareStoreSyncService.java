package com.burgeon.r3.inf.services.cp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.mapper.SgCShareStoreMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/10 10:45
 * <p>
 * 聚合仓档案主表同步到cp的服务
 */
@Slf4j
@Component
public class SgCShareStoreSyncService extends AbstractSyncService<SgCShareStore> {

    @Autowired
    private SgCShareStoreMapper sgCShareStoreMapper;

    public SgCShareStoreSyncService() {
        super(SgConstants.SG_C_SHARE_STORE);
    }

    @Override
    public List<SgCShareStore> execute(int pageIndex, int pageSize, int pageRangeTime, boolean isAllSync) {
        if (log.isDebugEnabled()) {
            log.debug("SgCShareStoreSyncService.execute :pageIndex={} ,pageSize={} ,intervalMinute={}",
                    pageIndex, pageSize, pageRangeTime);
        }
        LambdaQueryWrapper<SgCShareStore> lqw = new LambdaQueryWrapper<SgCShareStore>();

        if (!isAllSync) {
            Date now = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            calendar.add(Calendar.MINUTE, -pageRangeTime);
            lqw.gt(SgCShareStore::getModifieddate, calendar.getTime())
                    .lt(SgCShareStore::getModifieddate, now);
        }

        PageHelper.startPage(pageIndex, pageSize);
        List<SgCShareStore> sgCShareStores = sgCShareStoreMapper.selectList(lqw);
        PageInfo<SgCShareStore> pageInfo = new PageInfo<>(sgCShareStores);
        return pageInfo.getList();
    }
}
