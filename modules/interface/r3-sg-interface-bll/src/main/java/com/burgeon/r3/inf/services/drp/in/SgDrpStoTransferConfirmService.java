package com.burgeon.r3.inf.services.drp.in;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.inf.config.SgDrpConfig;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransfer;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpTransferConfirmRequest;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.transfer.SgBStoTransferMapper;
import com.burgeon.r3.sg.store.services.transfer.SgBStoTransferCancelSubmitService;
import com.burgeon.r3.sg.store.services.transfer.SgBStoTransferConfirmSubmitService;
import com.burgeon.r3.sg.store.services.transfer.SgBStoTransferVoidService;
import com.burgeon.r3.sg.store.services.transfer.SgTransferBillStatusEnum;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * drp->中台  逻辑调拨确认单接口
 * @date 2021/7/26 13:11
 */
@Slf4j
@Component
public class SgDrpStoTransferConfirmService {
    @Autowired
    private SgBStoTransferMapper sgStoTransferMapper;
    @Autowired
    private SgBStoTransferConfirmSubmitService sgStoTransferConfirmationSubmitService;
    @Autowired
    private SgBStoTransferCancelSubmitService sgStoTransferCancelSubmitService;
    @Autowired
    private SgBStoTransferVoidService sgStoTransferVoidService;
    @Autowired
    private SgDrpConfig sgDrpConfig;

    /**
     * 逻辑调拨确认单创建
     */
    public ValueHolderV14 create(SgDrpTransferConfirmRequest request) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "成功!");
        if (!sgDrpConfig.getEnableDrpSend()) {
            return new ValueHolderV14<>(ResultCode.FAIL, "中台系统未开放,请稍后再试!");
        }

        log.info("Start SgDrpStoTransferConfirmService.create request={}", JSONObject.toJSONString(request));

        String lockKsy =
                SgConstants.INTERFACE_DRP + ":" + SgConstants.SG_B_STO_TRANSFER_CONFIRMATION + ":" + request.getBillNo();
        SgRedisLockUtils.lock(lockKsy);

        try {
            SgBStoTransfer sgStoTransfer = checkParam(request);
            Integer status = sgStoTransfer.getStatus();
            if (SgTransferBillStatusEnum.AUDITED_NOT_CONFIRM.getVal() != status){
                return new ValueHolderV14<>(ResultCode.SUCCESS, "当前单据中台已确认,请勿重复请求!");
            }

            SgBStoTransfer updateTransfer = new SgBStoTransfer();
            updateTransfer.setId(sgStoTransfer.getId());
            //更新对应单据的“确认传DRP状态”=不传
            sgStoTransferMapper.update(updateTransfer, new LambdaUpdateWrapper<SgBStoTransfer>()
                    .eq(SgBStoTransfer::getId, sgStoTransfer.getId())
                    .set(SgBStoTransfer::getConfirmDrpStatus, SgStoreConstants.SEND_DRP_STATUS_NOT_PASS));
            SgR3BaseRequest baseRequest = new SgR3BaseRequest();
            baseRequest.setObjId(sgStoTransfer.getId());
            baseRequest.setLoginUser(DrpUtils.getUser());
            List<String> redisKey = new ArrayList<>();
            ValueHolderV14<SgR3BaseResult> transferConfirmationResult =
                    sgStoTransferConfirmationSubmitService.submitTransferConfirmation(baseRequest, redisKey);
            if (!transferConfirmationResult.isOK()) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("创建失败！" + transferConfirmationResult.getMessage());
            }
        } catch (Exception e) {
            log.error("SgDrpStoTransferConfirmService.create exception_has_occured:{}",
                    Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        return v14;
    }

    /**
     * 逻辑调拨单和确认单取消审核
     */
    public ValueHolderV14 cancel(SgDrpTransferConfirmRequest request, boolean isConfirm) {
        if (!sgDrpConfig.getEnableDrpSend()) {
            return new ValueHolderV14<>(ResultCode.FAIL, "中台系统未开放,请稍后再试!");
        }

        log.info("Start SgDrpStoTransferConfirmService.cancel request={},isConfirm:{}",
                JSONObject.toJSONString(request), isConfirm);

        String lockKsy = SgConstants.INTERFACE_DRP + ":" + SgConstants.SG_B_STO_TRANSFER_CONFIRMATION +
                SgConstantsIF.VOID + ":" + request.getBillNo();
        SgRedisLockUtils.lock(lockKsy);
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "成功!");
        try {

            SgBStoTransfer sgStoTransfer = checkParam(request);
            Integer status = sgStoTransfer.getStatus();
            AssertUtils.cannot(SgTransferBillStatusEnum.AUDITED_NOT_OUT.getVal() != status
                    && SgTransferBillStatusEnum.AUDITED_NOT_CONFIRM.getVal() != status, "取消失败:当前单据状态不是" +
                    "已审核未确认或者已确认未出库!");
            SgDrpStoTransferConfirmService confirmService =
                    ApplicationContextHandle.getBean(SgDrpStoTransferConfirmService.class);
            confirmService.cancelAndVoid(request, isConfirm, v14, sgStoTransfer);
        } catch (Exception e) {
            log.error("SgDrpStoTransferConfirmService.cancel exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        return v14;
    }

    /**
     * 取消审核并作废逻辑调拨单/确认单
     *
     * @param request       请求参数
     * @param isConfirm     是否确认单
     * @param v14           ValueHolderV14
     * @param sgStoTransfer 调拨单
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelAndVoid(SgDrpTransferConfirmRequest request, boolean isConfirm, ValueHolderV14 v14,
                              SgBStoTransfer sgStoTransfer) {
        SgR3BaseRequest sgR3BaseRequest = new SgR3BaseRequest();
        User drpUser = DrpUtils.getUser();
        sgR3BaseRequest.setObjId(sgStoTransfer.getId());
        sgR3BaseRequest.setBillNo(request.getBillNo());
        sgR3BaseRequest.setLoginUser(drpUser);
        //出异常时回滚流水
        List<String> redisBillFtpKeyList = new ArrayList<>();
        ValueHolderV14<SgR3BaseResult> cancelResult =
                sgStoTransferCancelSubmitService.cancelSubmitTransfer(sgR3BaseRequest, isConfirm, redisBillFtpKeyList);
        try {
            if (cancelResult.isOK()) {
                SgBStoTransfer updateTransfer = new SgBStoTransfer();
                updateTransfer.setId(sgStoTransfer.getId());
                //更新对应单据的“确认取消传DRP状态”=不传或“取消传DRP状态”=不传
                LambdaUpdateWrapper<SgBStoTransfer> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(SgBStoTransfer::getId, sgStoTransfer.getId());
                if (isConfirm) {
                    wrapper.set(SgBStoTransfer::getConfirmCancelDrpStatus, SgStoreConstants.SEND_DRP_STATUS_NOT_PASS);
                } else {
                    wrapper.set(SgBStoTransfer::getCancelDrpStatus, SgStoreConstants.SEND_DRP_STATUS_NOT_PASS);
                }
                sgStoTransferMapper.update(updateTransfer, wrapper);
                ValueHolderV14<SgR3BaseResult> voidTransfer = sgStoTransferVoidService.voidTransfer(sgR3BaseRequest);
                AssertUtils.isTrue(voidTransfer.isOK(), "取消失败!" + voidTransfer.getMessage());
            } else {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("取消失败!" + cancelResult.getMessage());
            }
        } catch (Exception e) {
            // 回滚库存
            StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, drpUser);
            AssertUtils.logAndThrowException(e.getMessage(), e, drpUser.getLocale());
        }
    }

    /**
     * 校验参数
     *
     * @param request 请求参数
     * @return 调拨单
     */
    private SgBStoTransfer checkParam(SgDrpTransferConfirmRequest request) {
        String billNo = request.getBillNo();
        AssertUtils.cannot(StringUtils.isBlank(billNo), "参数:单据编号不能为空!");
        SgBStoTransfer sgStoTransfer = sgStoTransferMapper.selectOne(new LambdaQueryWrapper<SgBStoTransfer>()
                .eq(SgBStoTransfer::getBillNo, billNo)
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        AssertUtils.cannot(sgStoTransfer == null, "当前单据编号对应单据不存在!");
        return sgStoTransfer;
    }
}
