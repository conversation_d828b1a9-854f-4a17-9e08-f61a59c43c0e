package com.burgeon.r3.inf.services.oa;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.request.SgStoragePageQueryRequest;
import com.burgeon.r3.sg.basic.model.request.SgStoragePageRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageQueryRequest;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorage;
import com.burgeon.r3.sg.core.model.table.store.adjust.SgBStoAdjust;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.inf.common.enums.StoreTypeEnum;
import com.burgeon.r3.sg.inf.model.request.oa.SgPackageRequisitionOrderItemModel;
import com.burgeon.r3.sg.inf.model.request.oa.SgPackageRequisitionOrderModel;
import com.burgeon.r3.sg.store.mapper.adjust.SgBStoAdjustMapper;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustMainSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustSaveRequest;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSaveService;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.model.table.CpCStoreDimItem;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-08-17 15:53
 * @Description: oa报废单下发生成库存调整单
 */

@Slf4j
@Component
public class SgPackageRequisitionOrderService {

    @Autowired
    private SgBStoAdjustMapper adjustMapper;
    @Autowired
    private SgBStoAdjustSaveService sgBStoAdjustSaveService;
    @Resource
    private SgStorageQueryService sgStorageQueryService;

    private static final String produceDate = "00000000";

    /**
     * oa包材领用单下发生成库存调整单
     *
     * @param model 入参
     * @return ValueHolderV14
     */
    public ValueHolderV14<Void> callPackageRequisitionOrder(SgPackageRequisitionOrderModel model) {

        log.info(LogUtil.format("SgPackageRequisitionOrderService.callPackageRequisitionOrder，model:{} ",
                "SgPackageRequisitionOrderService.callPackageRequisitionOrder"),
                JSONObject.toJSONString(model));

        ValueHolderV14<Void> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "OA下发包材领用单处理成功！");
        try {
            Map<String, PsCProSkuResult> skuInfo = new HashMap<>();
            Map<String, CpCStore> cpStoreMap = new HashMap<>();
            Map<String, CpCStoreDimItem> costCenterMap = new HashMap<>();
            String error = checkModel(model, skuInfo, cpStoreMap, costCenterMap);
            if (StringUtils.isNotEmpty(error)) {
                v14.setMessage(error);
                v14.setCode(ResultCode.FAIL);
                return v14;
            }
            //根据成本中心分组处理
            save(model, cpStoreMap);
        } catch (Exception e) {
            log.error("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("OA下发报废单处理异常：" + e.getMessage());
        }

        log.info(LogUtil.format("SgScrapOrderService.checkFreezeQty ValueHolderV14:{} ",
                "SgScrapOrderService.ValueHolderV14"),
                JSONObject.toJSONString(v14));

        return v14;
    }

    @Transactional(rollbackFor = Exception.class)
    public void save(SgPackageRequisitionOrderModel model, Map<String, CpCStore> cpStoreMap) {
        try {
            Map<String, List<SgPackageRequisitionOrderItemModel>> groupByCostCenter =
                    model.getSgPackageRequisitionOrderItemModels().stream()
                            .collect(Collectors.groupingBy(SgPackageRequisitionOrderItemModel::getCostCenterCode));

            List<SgBStoAdjustSaveRequest> saveRequestList = new ArrayList<>();
            for (String costCenter : groupByCostCenter.keySet()) {
                model.setSgPackageRequisitionOrderItemModels(groupByCostCenter.get(costCenter));
                List<SgBStoAdjustSaveRequest> saveRequests = buildAdjust(model, cpStoreMap.get(model.getWerehousecode()), costCenter);
                saveRequestList.addAll(saveRequests);
            }
            if (CollectionUtils.isNotEmpty(saveRequestList)) {
                batchSaveAdjust(saveRequestList);
            }
        } catch (Exception e) {
            throw new NDSException(e.getMessage());
        }
    }

    /**
     * 新增库存调整单
     *
     * @param saveRequests 入参
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveAdjust(List<SgBStoAdjustSaveRequest> saveRequests) {
        for (SgBStoAdjustSaveRequest saveRequest : saveRequests) {
            log.info(LogUtil.format("SgScrapOrderService batchSaveAdjust.save:{}",
                    "SgScrapOrderService.batchSaveAdjust.save"), JSONObject.toJSONString(saveRequest));
            ValueHolderV14<SgR3BaseResult> save = sgBStoAdjustSaveService.save(saveRequest);
            log.info(LogUtil.format("SgScrapOrderService batchSaveAdjust.ValueHolderV14:{}",
                    "SgScrapOrderService.batchSaveAdjust.ValueHolderV14"), JSONObject.toJSONString(save));
            AssertUtils.cannot(!save.isOK(), save.getMessage());
        }

    }

    /**
     * 构建库存调整单数据
     * 因为参数【仓库编码】、【报废性质】（报废性质对应库存调整单的“调整性质”）在明细表中，
     * 故在生成库存调整单时需要做转换，将同【仓库编码】、【报废性质】的商品生成一笔库存调整单
     *
     * @param model      入参
     * @param cpStore
     * @param costCenter
     * @return SgBStoAdjustSaveRequest
     */
    private List<SgBStoAdjustSaveRequest> buildAdjust(SgPackageRequisitionOrderModel model, CpCStore cpStore, String costCenter) {

        List<SgBStoAdjustSaveRequest> saveRequests = new ArrayList<>();

        List<SgPackageRequisitionOrderItemModel> itemModels = model.getSgPackageRequisitionOrderItemModels();
        SgBStoAdjustSaveRequest adjustSaveRequest = new SgBStoAdjustSaveRequest();
        SgBStoAdjustMainSaveRequest mainRequest = new SgBStoAdjustMainSaveRequest();
        //调整日期
        mainRequest.setBillDate(new Date());
        //库存日期
        mainRequest.setStockDate(model.getStockDate());
        //单据类型
        mainRequest.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL);
        CpCStore storeInfoByEcode = CommonCacheValUtils.getStoreInfoByEcode(model.getWerehousecode());
        //逻辑仓
        if (storeInfoByEcode != null) {
            mainRequest.setCpCStoreId(storeInfoByEcode.getId());
            mainRequest.setCpCStoreEcode(storeInfoByEcode.getEcode());
            mainRequest.setCpCStoreEname(storeInfoByEcode.getEname());
        }
        //成本中心
        mainRequest.setCostCenter(costCenter);
        //调整性质
        mainRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_PACKAGE_REQUISITION);
        //来源单据信息
        mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_PACKAGE_REQUISITION);
        mainRequest.setSourceBillNo(model.getLcbh());
        mainRequest.setRemark("由OA包材领用单，OA包材领用单号[" + model.getLcbh() + "]传入生成");

        adjustSaveRequest.setObjId(-1L);
        adjustSaveRequest.setMainRequest(mainRequest);
        adjustSaveRequest.setItems(buildAdjustItem(itemModels));
        adjustSaveRequest.setR3(Boolean.FALSE);
        adjustSaveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());
        saveRequests.add(adjustSaveRequest);
        return saveRequests;
    }


    /**
     * 构建库存调整单明细
     *
     * @param sgPackageRequisitionOrderItemModels 入参
     * @return SgBStoAdjustItemSaveRequest
     */
    private List<SgBStoAdjustItemSaveRequest> buildAdjustItem(List<SgPackageRequisitionOrderItemModel> sgPackageRequisitionOrderItemModels) {
        List<SgBStoAdjustItemSaveRequest> itemRequests = new ArrayList<>();

        sgPackageRequisitionOrderItemModels.forEach(task -> {
            SgBStoAdjustItemSaveRequest adjustItemRequest = new SgBStoAdjustItemSaveRequest();
            adjustItemRequest.setId(-1L);
            adjustItemRequest.setQty(task.getPlanqty());
            adjustItemRequest.setPsCSkuEcode(task.getWlmc1());
            adjustItemRequest.setProduceDate(task.getScrq());
            adjustItemRequest.setStorageType(StoreTypeEnum.getValue(task.getBfxz()));
            itemRequests.add(adjustItemRequest);
        });

        return itemRequests;
    }

    /**
     * 不用判空，上游判断
     *
     * @param model 入参
     * @return 错误信息
     */
    private String checkModel(SgPackageRequisitionOrderModel model, Map<String, PsCProSkuResult> skuInfo,
                              Map<String, CpCStore> cpStoreMap, Map<String, CpCStoreDimItem> costCenterMap) {
        if (model == null) {
            return "入参为空！";
        }
        if (CollectionUtils.isEmpty(model.getSgPackageRequisitionOrderItemModels())) {
            return "入参明细为空！";
        }
        Integer selectCount = adjustMapper.selectCount(new LambdaQueryWrapper<SgBStoAdjust>()
                .eq(SgBStoAdjust::getSourceBillNo, model.getLcbh()));
        if (selectCount > 0) {
            return "来源单号重复";
        }
        List<SgPackageRequisitionOrderItemModel> itemModels = model.getSgPackageRequisitionOrderItemModels();
        List<String> storeList = new ArrayList<>();
        storeList.add(model.getWerehousecode());
        List<String> skuList = new ArrayList<>();
        List<String> costCenterCodeList = new ArrayList<>();
        Map<String, BigDecimal> qtyMap = new HashMap<>();
        for (SgPackageRequisitionOrderItemModel itemModel : itemModels) {
            if (StringUtils.isEmpty(itemModel.getBfxz()) || !StoreTypeEnum.ZP.getCode().equals(itemModel.getBfxz())) {
                return "库存性质为空或者非正品";
            }
            if (StringUtils.isEmpty(itemModel.getScrq()) || !produceDate.equals(itemModel.getScrq())) {
                return "生产日期为空或格式错误";
            }
            if (itemModel.getPlanqty() == null) {
                return "数量不能为空";
            }
            if (qtyMap.containsKey(itemModel.getWlmc1())) {
                qtyMap.put(itemModel.getWlmc1(), qtyMap.getOrDefault(itemModel.getWlmc1(), BigDecimal.ZERO).add(itemModel.getPlanqty()));
            } else {
                qtyMap.put(itemModel.getWlmc1(), itemModel.getPlanqty());
            }
            skuList.add(itemModel.getWlmc1());
            costCenterCodeList.add(itemModel.getCostCenterCode());
        }
        //校验sku是否存在
        if (CollectionUtils.isNotEmpty(skuList)) {
            JSONArray error = new JSONArray();
            skuInfo = CommonCacheValUtils.getSkuInfo(skuList);
            for (String skuCode : skuList) {
                if (!skuInfo.containsKey(skuCode)) {
                    error.add(skuCode);
                }
            }
            if (error.size() > 0) {
                return "条码【" + JSONArray.toJSONString(error) + "】不存在请检查！";
            }
        } else {
            return "条码信息不能为空！";
        }
        //校验逻辑仓
        if (CollectionUtils.isNotEmpty(storeList)) {
            cpStoreMap = CommonCacheValUtils.queryStoreInfosByEcodes(storeList);
            JSONArray error = new JSONArray();
            for (String store : storeList) {
                if (!cpStoreMap.containsKey(store)) {
                    error.add(store);
                }
            }
            if (error.size() > 0) {
                return "仓库【" + JSONArray.toJSONString(error) + "】不存在请检查！";
            }
        } else {
            return "仓库信息不能为空！";
        }
        //校验成本中心
        if (CollectionUtils.isNotEmpty(costCenterCodeList)) {
            costCenterMap = CommonCacheValUtils.queryCostCenterByCodes(costCenterCodeList);
            JSONArray error = new JSONArray();
            for (String costCenter : costCenterCodeList) {
                if (!costCenterMap.containsKey(costCenter)) {
                    error.add(costCenter);
                }
            }
            if (error.size() > 0) {
                return "成本中心【" + JSONArray.toJSONString(error) + "】不存在请检查！";
            }
        } else {
            return "成本中心不能为空！";
        }
        //校验sku库存是否足够(按照逻辑仓维度处理)
        List<Long> storeIdList = cpStoreMap.values().stream().map(CpCStore::getId).collect(Collectors.toList());
        List<SgBStorage> sgBStorageList = querySgBStorage(storeIdList, skuList);
        if (CollectionUtils.isEmpty(sgBStorageList)) {
            return "逻辑仓库存不足";
        }
        sgBStorageList = sgBStorageList.stream().filter(s -> produceDate.equals(s.getProduceDate())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sgBStorageList)) {
            return "逻辑仓库存不足";
        }
        Map<String, BigDecimal> qtyAvailableMap =
                sgBStorageList.stream().collect(Collectors.toMap(SgBStorage::getPsCSkuEcode, SgBStorage::getQtyAvailable));
        JSONArray qtyError = new JSONArray();
        for (String skuCode : qtyMap.keySet()) {
            if (!qtyAvailableMap.containsKey(skuCode)) {
                qtyError.add(skuCode);
                continue;
            }
            if (qtyAvailableMap.get(skuCode).compareTo(qtyMap.get(skuCode).negate()) < 0) {
                qtyError.add(skuCode);
            }
        }
        if (qtyError.size() > 0) {
            return "条码【" + JSONArray.toJSONString(qtyError) + "】库存不足请检查！";
        }
        return null;
    }

    /**
     * @param storeIdList
     * @param skuList
     * @return
     */
    private List<SgBStorage> querySgBStorage(List<Long> storeIdList, List<String> skuList) {
        //所有逻辑仓库存
        List<SgBStorage> sgBStorageList = new ArrayList<>();

        SgStoragePageQueryRequest sgStoragePageQueryRequest = new SgStoragePageQueryRequest();
        SgStorageQueryRequest sgStorageQueryRequest = new SgStorageQueryRequest();
        sgStorageQueryRequest.setStoreIds(storeIdList);
        sgStorageQueryRequest.setSkuEcodes(skuList);

        SgStoragePageRequest sgStoragePageRequest = new SgStoragePageRequest();
        sgStoragePageRequest.setPageNum(1);
        sgStoragePageRequest.setPageSize(SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);

        sgStoragePageQueryRequest.setPageRequest(sgStoragePageRequest);
        sgStoragePageQueryRequest.setQueryRequest(sgStorageQueryRequest);
        ValueHolderV14<PageInfo<SgBStorage>> pageInfoValueHolderV14 = sgStorageQueryService.queryStoragePage(sgStoragePageQueryRequest,
                R3SystemUserResource.getSystemRootUser());
        AssertUtils.cannot(!pageInfoValueHolderV14.isOK(), "根据逻辑仓查逻辑仓库存异常：" + pageInfoValueHolderV14.getMessage());

        PageInfo<SgBStorage> dataInfo = pageInfoValueHolderV14.getData();
        if (dataInfo != null) {

            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dataInfo.getList())) {
                sgBStorageList.addAll(dataInfo.getList());
            }

            //判断是否还有下一页
            if (dataInfo.isHasNextPage()) {
                List<SgBStorage> pageStorage = getPageStorage(storeIdList, dataInfo);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(pageStorage)) {
                    sgBStorageList.addAll(pageStorage);
                }
            }
        }
        return sgBStorageList;
    }

    /**
     * 分页查询库存
     *
     * @param storeidList 店仓id
     * @param dataInfo    分页参数
     * @return 库存
     */
    private List<SgBStorage> getPageStorage(List<Long> storeidList, PageInfo<SgBStorage> dataInfo) {
        List<SgBStorage> data = new ArrayList<>();
        //获取页面
        int pages = dataInfo.getPages();

        //从第二页开始再查
        for (int i = 2; i <= pages; i++) {
            SgStoragePageQueryRequest sgStoragePageQueryRequest = new SgStoragePageQueryRequest();

            SgStorageQueryRequest sgStorageQueryRequest = new SgStorageQueryRequest();
            sgStorageQueryRequest.setStoreIds(storeidList);

            SgStoragePageRequest sgStoragePageRequest = new SgStoragePageRequest();
            sgStoragePageRequest.setPageNum(i);
            sgStoragePageRequest.setPageSize(SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);


            sgStoragePageQueryRequest.setPageRequest(sgStoragePageRequest);
            sgStoragePageQueryRequest.setQueryRequest(sgStorageQueryRequest);
            ValueHolderV14<PageInfo<SgBStorage>> pageInfoValueHolderV14 = sgStorageQueryService.queryStoragePage(sgStoragePageQueryRequest,
                    R3SystemUserResource.getSystemRootUser());
            AssertUtils.cannot(!pageInfoValueHolderV14.isOK(), "根据逻辑仓分页查逻辑仓库存异常：" + pageInfoValueHolderV14.getMessage());

            PageInfo<SgBStorage> pageInfo = pageInfoValueHolderV14.getData();
            if (pageInfo != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(pageInfo.getList())) {
                data.addAll(pageInfo.getList());
            }
        }
        return data;
    }


}
