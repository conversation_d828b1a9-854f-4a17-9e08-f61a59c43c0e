package com.burgeon.r3.inf.services.oms;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoTransferItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoTransferRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferSaveRequest;
import com.burgeon.r3.sg.store.services.transfer.SgBStoTransferSaveAndSubmitService;
import com.google.common.base.Throwables;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/7/3 17:24
 * oms创建审核逻辑调拨单
 */
@Slf4j
@Component
public class SgOmsStoTransferServcie {

    @Autowired
    private SgBStoTransferSaveAndSubmitService saveAndSubmitService;

    public ValueHolderV14<SgR3BaseResult> saveTransfer(SgOmsStoTransferRequest request) {
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "调用[逻辑调拨单创建并审核服务]成功！");
        if (log.isDebugEnabled()) {
            log.debug(" oms创建审核逻辑调拨单 .saveTransfer request:{}", JSONObject.toJSONString(request));
        }
        try {
            List<SgOmsStoTransferItemRequest> itemRequests = check(request);
            SgBStoTransferBillSaveRequest billSaveRequest = new SgBStoTransferBillSaveRequest();
            //主表
            SgBStoTransferSaveRequest saveRequest = new SgBStoTransferSaveRequest();
            //明细集合
            List<SgBStoTransferItemSaveRequest> itemSaveRequests = new ArrayList<>();
            for (SgOmsStoTransferItemRequest itemRequest : itemRequests) {
                SgBStoTransferItemSaveRequest saveItem = new SgBStoTransferItemSaveRequest();
                BeanUtils.copyProperties(itemRequest, saveItem);
                saveItem.setId(-1L);
                itemSaveRequests.add(saveItem);
            }
            BeanUtils.copyProperties(request, saveRequest);
            billSaveRequest.setObjId(-1L);
            billSaveRequest.setLoginUser(request.getLoginUser());
            billSaveRequest.setTransferSaveRequest(saveRequest);
            billSaveRequest.setItems(itemSaveRequests);
            if (log.isDebugEnabled()) {
                log.debug(" oms逻辑调拨单创建审核  billSaveRequest:{}", JSONObject.toJSONString(billSaveRequest));
            }
            ValueHolderV14<SgR3BaseResult> holderV14 = saveAndSubmitService.saveAndSubmit(billSaveRequest);
            if (!holderV14.isOK()) {
                v14.setCode(holderV14.getCode());
                v14.setMessage(holderV14.getMessage());
            } else {
                v14.setData(holderV14.getData());
            }
            if (log.isDebugEnabled()) {
                log.debug(" oms逻辑调拨单创建出参  holderV14:{}", JSONObject.toJSONString(holderV14));
            }
        } catch (Exception e) {
            log.error("SgOmsStoTransferServcie.saveTransfer. exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }
        return v14;
    }

    /**
     * 校验参数
     */
    private List<SgOmsStoTransferItemRequest> check(SgOmsStoTransferRequest request) {
        AssertUtils.notNull(request, "请求参数不能空");
        User loginUser = request.getLoginUser();
        List<SgOmsStoTransferItemRequest> items = request.getItems();
        Long senderStoreId = request.getSenderStoreId();
        Long receiverStoreId = request.getReceiverStoreId();
        String sourceBillNo = request.getSourceBillNo();
        Long sourceBillId = request.getSourceBillId();
        Integer sourceBillType = request.getSourceBillType();
        AssertUtils.notNull(loginUser, "操作用户不能空");
        AssertUtils.notNull(senderStoreId, "发货逻辑仓不能空");
        AssertUtils.notNull(receiverStoreId, "收货逻辑仓不能空");
        AssertUtils.notNull(sourceBillNo, "来源单据不能空");
        AssertUtils.notNull(sourceBillId, "来源单据ID不能空");
        AssertUtils.notNull(sourceBillType, "来源单据类型不能空");
        if (CollectionUtils.isEmpty(items)) {
            AssertUtils.logAndThrow("明细不能为空", loginUser.getLocale());
        }
        return items;
    }
}
