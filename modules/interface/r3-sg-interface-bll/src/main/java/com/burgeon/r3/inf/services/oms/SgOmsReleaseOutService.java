package com.burgeon.r3.inf.services.oms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOut;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOutItem;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOutItemLog;
import com.burgeon.r3.sg.core.model.table.store.out.*;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsPhyStorageOutItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsReleaseOutRequest;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutItemLogMapper;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutItemMapper;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutMapper;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutBillReleaseRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutItemReleaseRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutReleaseRequest;
import com.burgeon.r3.sg.share.services.out.SgBShareOutCleanService;
import com.burgeon.r3.sg.share.services.out.SgBShareOutReleaseService;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutItemLogMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutMapper;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutBillCleanRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutBillReleaseRequest;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutBillReleaseResult;
import com.burgeon.r3.sg.store.services.out.SgBStoOutCleanService;
import com.burgeon.r3.sg.store.services.out.SgBStoOutReleaseService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderV14Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * 指定多商品释放占用库存服务
 * @date 2021/6/28 14:03
 */
@Slf4j
@Component
public class SgOmsReleaseOutService {

    @Autowired
    private SgBShareOutMapper shareOutMapper;
    @Autowired
    private SgBShareOutItemMapper shareOutItemMapper;
    @Autowired
    private SgBShareOutItemLogMapper shareOutItemLogMapper;
    @Autowired
    private SgBStoOutMapper stoOutMapper;
    @Autowired
    private SgBStoOutItemMapper stoOutItemMapper;
    @Autowired
    private SgBStoOutItemLogMapper stoOutItemLogMapper;

    @Autowired
    SgBShareOutCleanService shareOutCleanService;
    @Autowired
    SgBStoOutCleanService stoOutCleanService;


    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 release(SgOmsReleaseOutRequest request) {

        List<String> redisKeyList = new ArrayList<>();

        try {
            log.info("Start SgOmsReleaseOutService.release.ReceiveParams:request={};",
                    JSONObject.toJSONString(request));


            LambdaQueryWrapper<SgBShareOut> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SgBShareOut::getSourceBillNo, request.getRetailBillNo());
            wrapper.eq(SgBShareOut::getBillStatus, SgShareConstants.SHARE_OUT_STATUS_CREATE);
            wrapper.eq(SgBShareOut::getIsactive, SgConstants.IS_ACTIVE_Y);

            List<SgBShareOut> mainList = shareOutMapper.selectList(wrapper);

            List<Integer> stoStatusList = new ArrayList<>();
            stoStatusList.add(SgStoreConstants.BILL_STO_IN_STATUS_CREATE);
            stoStatusList.add(SgStoreConstants.BILL_STO_IN_STATUS_UPDATE);
            LambdaQueryWrapper<SgBStoOut> stoWrapper = new LambdaQueryWrapper<>();
            stoWrapper.eq(SgBStoOut::getSourceBillNo, request.getRetailBillNo());
            stoWrapper.in(SgBStoOut::getBillStatus, stoStatusList);
            List<SgBStoOut> stoMainList = stoOutMapper.selectList(stoWrapper);

            if (CollectionUtils.isEmpty(mainList) && CollectionUtils.isEmpty(stoMainList)) {
                return new ValueHolderV14(ResultCode.SUCCESS, "当前单据均已作废成功");
            }

            if (CollectionUtils.isEmpty(mainList)) {
                return new ValueHolderV14(ResultCode.FAIL, "当前单据状态不允许释放占用库存");
            }

            if (CollectionUtils.isEmpty(stoMainList)) {
                return new ValueHolderV14(ResultCode.FAIL, "当前单据所属逻辑占用单状态不允许释放占用库存");
            }

            Boolean isReleaseOrClean = Optional.ofNullable(request.getIsReleaseOrClean()).orElse(Boolean.FALSE);

            if (isReleaseOrClean) {
                ValueHolderV14 v14 = releaseShareSto(mainList, stoMainList, request, redisKeyList);
                if (!v14.isOK()) {
                    return new ValueHolderV14(ResultCode.FAIL, "指定多商品释放占用库存服务失败:" + v14.getMessage());
                }
            } else {
                cleanShareOut(mainList, request);
                cleanStoOut(stoMainList, request);
            }
            return new ValueHolderV14(ResultCode.SUCCESS, "指定多商品释放占用库存服务成功");
        } catch (Exception ex) {
            log.error("start SgOmsReleaseOutService release 指定多商品释放占用库存服务失败 cause:{}",
                    Throwables.getStackTraceAsString(ex));

//            StorageBasicUtils.rollbackStorage(redisKeyList, request.getLoginUser());

            return new ValueHolderV14(ResultCode.FAIL, "指定多商品释放占用库存服务失败");
        } finally {

        }

    }


    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 batchRelease(List<SgOmsReleaseOutRequest> requests) {
        if (log.isDebugEnabled()) {
            log.debug(" 批量释放占用，入参：{}", JSON.toJSONString(requests));
        }

        if (CollectionUtils.isEmpty(requests)) {
            return ValueHolderV14Utils.getFailValueHolder("入参集合为空");
        }

        ValueHolderV14 result = ValueHolderV14Utils.getSuccessValueHolder("释放成功");
        for (SgOmsReleaseOutRequest request : requests) {
            result = release(request);
            AssertUtils.isTrue(result.isOK(), result.getMessage());
        }

        return result;
    }

    /**
     * 为解决 Transaction rolled back because it has been marked as rollback-only
     *
     * @param mainList
     * @param stoMainList
     * @param request
     * @param redisKeyList
     * @return ValueHolderV14
     */
    private ValueHolderV14 releaseShareSto(List<SgBShareOut> mainList, List<SgBStoOut> stoMainList,
                                           SgOmsReleaseOutRequest request, List<String> redisKeyList) {

        log.info("Start SgOmsReleaseOutService.releaseShareSto.ReceiveParams:mainList.size={},stoMainList.size={};",
                mainList.size(), stoMainList.size());

        ValueHolderV14 v14 = new ValueHolderV14();
        try {
            SgOmsReleaseOutService bean = ApplicationContextHandle.getBean(SgOmsReleaseOutService.class);
            bean.releaseShareOrSto(mainList, stoMainList, request, redisKeyList);
        } catch (Exception e) {

            StorageBasicUtils.rollbackStorage(redisKeyList, request.getLoginUser());

            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
            return v14;
        }

        return v14;
    }

    @Transactional(rollbackFor = Exception.class)
    public void releaseShareOrSto(List<SgBShareOut> mainList, List<SgBStoOut> stoMainList, SgOmsReleaseOutRequest request, List<String> redisKeyList) {

        log.info("Start SgOmsReleaseOutService.releaseShareOrSto.ReceiveParams:mainList.size={},stoMainList.size={};",
                mainList.size(), stoMainList.size());

        Map<String, SgBShareOutBillReleaseRequest> requestMap = releaseShareOut(mainList, request, redisKeyList);

        // 聚合仓ID
        List<Long> shareStoreId = new ArrayList<>();

        log.info("Start SgOmsReleaseOutService.releaseShareOrSto.ReceiveParams:requestMap:{};", JSONObject.toJSONString(requestMap));

        //更新共享占用单里面入参有聚合仓id
        if (MapUtils.isNotEmpty(requestMap)) {
            List<SgBShareOutBillReleaseRequest> sgShareOutBillReleaseRequests = new ArrayList<>(requestMap.values());
            for (SgBShareOutBillReleaseRequest share : sgShareOutBillReleaseRequests) {
                SgBShareOutReleaseRequest shareOutReleaseRequest = share.getShareOutReleaseRequest();
                if (shareOutReleaseRequest != null && shareOutReleaseRequest.getSgCShareStoreId() != null) {
                    shareStoreId.add(shareOutReleaseRequest.getSgCShareStoreId());
                }
            }
        }


        releaseStoOut(stoMainList, request, redisKeyList, shareStoreId);
    }

    /**
     * 释放逻辑占用单
     *
     * @param stoMainList  stoMainList
     * @param request      request
     * @param redisKeyList redisKeyList
     */
    private void releaseStoOut(List<SgBStoOut> stoMainList, SgOmsReleaseOutRequest request, List<String> redisKeyList, List<Long> shareStoreId) {
        List<Long> mainIds = new ArrayList<>();
        Map<Long, SgBStoOut> mainMap = new HashMap<>(16);
        for (SgBStoOut stoOut : stoMainList) {
            mainIds.add(stoOut.getId());
            mainMap.put(stoOut.getId(), stoOut);
        }

//        List<SgOmsPhyStorageOutItemRequest> itemRequestList = request.getItemRequestList();
        //相同条码合一下
        List<SgOmsPhyStorageOutItemRequest> itemRequestList = mergeItem(request.getItemRequestList());

        log.info(LogUtil.format("原入参:{},聚合后入参数:{}", "SgOmsReleaseOutService.mergeItem"),
                CollectionUtils.isEmpty(request.getItemRequestList()) ? null : JSONObject.toJSONString(request.getItemRequestList()),
                CollectionUtils.isEmpty(itemRequestList) ? null : JSONObject.toJSONString(itemRequestList));

        if (!CollectionUtils.isEmpty(itemRequestList)) {

            List<String> skuCodes = itemRequestList.stream().map(SgOmsPhyStorageOutItemRequest::getPsCSkuEcode).collect(Collectors.toList());

            LambdaQueryWrapper<SgBStoOutItem> itemWrapper = new LambdaQueryWrapper<>();
            itemWrapper.in(SgBStoOutItem::getSgBStoOutId, mainIds);
            itemWrapper.in(SgBStoOutItem::getPsCSkuEcode, skuCodes);
            //未释放量要大于0
            itemWrapper.ne(SgBStoOutItem::getQtyPreout, BigDecimal.ZERO);
            List<SgBStoOutItem> itemListAll = stoOutItemMapper.selectList(itemWrapper);

            if (!CollectionUtils.isEmpty(shareStoreId)) {
                log.info("退换货释放 聚合仓:{}", JSONObject.toJSONString(shareStoreId));
                itemListAll = SgStoreUtils.contrastByShareId(shareStoreId, itemListAll);
            }

            if (CollectionUtils.isEmpty(itemListAll)) {
                AssertUtils.logAndThrow("退换货释放逻辑占用单失败：通过对比聚合仓无合适明细！");
            }

            Map<String, List<SgBStoOutItem>> itemsMap =
                    itemListAll.stream().collect(Collectors.groupingBy(SgBStoOutItem::getPsCSkuEcode));

            Map<String, SgBStoOutBillReleaseRequest> releaseMap = new HashMap<>(16);

            //循环处理传入参数
            for (SgOmsPhyStorageOutItemRequest itemRequest : itemRequestList) {
                if (itemsMap.containsKey(itemRequest.getPsCSkuEcode())) {
                    BigDecimal qty = itemRequest.getQty();
                    if (qty == null || BigDecimal.ZERO.compareTo(qty) >= 0) {
                        continue;
                    }

                    List<SgBStoOutItem> itemList = itemsMap.get(itemRequest.getPsCSkuEcode());
                    for (SgBStoOutItem stoOutItem : itemList) {

                        if (BigDecimal.ZERO.compareTo(qty) >= 0) {
                            break;
                        }

                        SgBStoOutResultItem itemReleaseRequest = new SgBStoOutResultItem();
                        BeanUtils.copyProperties(stoOutItem, itemReleaseRequest);

                        BigDecimal qtyPreout = stoOutItem.getQtyPreout();
                        //判断是否可以释放完
                        if (qty.compareTo(qtyPreout) <= 0) {
                            itemReleaseRequest.setQty(qty);
                            qty = BigDecimal.ZERO;
                        } else {
                            itemReleaseRequest.setQty(qtyPreout);
                            qty = qty.subtract(qtyPreout);
                        }

                        if (releaseMap.containsKey(stoOutItem.getSgBStoOutId() + SgConstants.SG_CONNECTOR_MARKS_6 +
                                stoOutItem.getCpCStoreEcode() + SgConstants.SG_CONNECTOR_MARKS_6 + itemReleaseRequest.getPsCSkuId())) {
                            SgBStoOutBillReleaseRequest stoOutBillReleaseRequest = releaseMap.get(stoOutItem.getSgBStoOutId()
                                    + SgConstants.SG_CONNECTOR_MARKS_6 + stoOutItem.getCpCStoreEcode() + SgConstants.SG_CONNECTOR_MARKS_6
                                    + itemReleaseRequest.getPsCSkuId());
                            List<SgBStoOutResultItem> outResultItemList = stoOutBillReleaseRequest.getOutResultItemList();
                            outResultItemList.add(itemReleaseRequest);

                            SgBStoOutResult outResult = stoOutBillReleaseRequest.getOutResult();
                            outResult.setTotQtyOut(outResult.getTotQtyOut().add(itemReleaseRequest.getQty()));

                        } else {
                            SgBStoOutBillReleaseRequest stoOutBillReleaseRequest = new SgBStoOutBillReleaseRequest();
                            SgBStoOut stoOut = mainMap.get(stoOutItem.getSgBStoOutId());

                            SgBStoOutResult stoOutResult = new SgBStoOutResult();
                            BeanUtils.copyProperties(stoOut, stoOutResult);
                            stoOutResult.setCpCStoreId(stoOutItem.getCpCStoreId());
                            stoOutResult.setCpCStoreEcode(stoOutItem.getCpCStoreEcode());
                            stoOutResult.setCpCStoreEname(stoOutItem.getCpCStoreEname());
                            stoOutResult.setSgBStoOutId(stoOut.getId());
                            stoOutResult.setIsLast(SgConstants.IS_LAST_NO);
                            stoOutResult.setTotQtyOut(itemReleaseRequest.getQty());

                            List<SgBStoOutResultItem> outResultItems = new ArrayList<>();
                            outResultItems.add(itemReleaseRequest);

                            stoOutBillReleaseRequest.setLoginUser(request.getLoginUser());
                            stoOutBillReleaseRequest.setOutResult(stoOutResult);
                            stoOutBillReleaseRequest.setOutResultItemList(outResultItems);
                            stoOutBillReleaseRequest.setServiceNode(SgConstantsIF.SERVICE_NODE_STO_OUT_RELEASE);
                            stoOutBillReleaseRequest.setIsSplit(Boolean.TRUE);
                            stoOutBillReleaseRequest.setSourceBillId(stoOut.getSourceBillId());
                            stoOutBillReleaseRequest.setSourceBillType(stoOut.getSourceBillType());

                            stoOutBillReleaseRequest.setSgShareStoreIdList(shareStoreId);
                            stoOutBillReleaseRequest.setIsQut(Boolean.FALSE);

                            releaseMap.put(stoOut.getId() + SgConstants.SG_CONNECTOR_MARKS_6 + stoOutItem.getCpCStoreEcode()
                                    + SgConstants.SG_CONNECTOR_MARKS_6 + itemReleaseRequest.getPsCSkuId(), stoOutBillReleaseRequest);
                        }

                    }

                }

            }
            //调用释放逻辑占用单
            releaseStoOut(releaseMap, redisKeyList);
        }

    }

    /**
     * 聚合相同条码
     *
     * @param itemRequests oms入参
     * @return SgOmsPhyStorageOutItemRequest
     */
    private List<SgOmsPhyStorageOutItemRequest> mergeItem(List<SgOmsPhyStorageOutItemRequest> itemRequests) {
        List<SgOmsPhyStorageOutItemRequest> mergeItemList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(itemRequests)) {

            //分组
            Map<Long, List<SgOmsPhyStorageOutItemRequest>> itemMap = itemRequests.stream().collect(
                    Collectors.groupingBy(SgOmsPhyStorageOutItemRequest::getPsCSkuId));

            //聚合
            for (Long key : itemMap.keySet()) {

                List<SgOmsPhyStorageOutItemRequest> itemRequestList = itemMap.get(key);
                SgOmsPhyStorageOutItemRequest itemRequest = new SgOmsPhyStorageOutItemRequest();
                SgOmsPhyStorageOutItemRequest item = itemRequestList.get(0);
                BeanUtils.copyProperties(item, itemRequest);
                BigDecimal totQty = itemRequestList.stream().map(SgOmsPhyStorageOutItemRequest::getQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                itemRequest.setQty(totQty);
                mergeItemList.add(itemRequest);

            }


        }

        return mergeItemList;
    }

    /**
     * 具体调用逻辑占用单释放
     *
     * @param releaseMap
     * @param redisKeyList
     */
    private void releaseStoOut(Map<String, SgBStoOutBillReleaseRequest> releaseMap, List<String> redisKeyList) {
        if (MapUtils.isNotEmpty(releaseMap)) {
            List<SgBStoOutBillReleaseRequest> sgStoOutBillReleaseRequests = new ArrayList<>(releaseMap.values());
            if (!CollectionUtils.isEmpty(sgStoOutBillReleaseRequests)) {
                for (SgBStoOutBillReleaseRequest outBillReleaseRequest : sgStoOutBillReleaseRequests) {
                    SgBStoOutReleaseService bean = ApplicationContextHandle.getBean(SgBStoOutReleaseService.class);
                    ValueHolderV14<SgBStoOutBillReleaseResult> valueHolderV14 = bean.releaseSgBStoOut(outBillReleaseRequest);
                    if (!valueHolderV14.isOK()) {
                        AssertUtils.logAndThrow("多商品释放逻辑层库存失败:" + valueHolderV14.getMessage());
                    }

                    //记录redis流水key
                    SgBStoOutBillReleaseResult data = valueHolderV14.getData();
                    if (data != null && !CollectionUtils.isEmpty(data.getRedisBillFtpKeyList())) {
                        redisKeyList.addAll(data.getRedisBillFtpKeyList());
                    }
                }
            }
        }

    }

    /**
     * 释放共享仓占用
     *
     * @param mainList mainList
     * @param request  request
     */
    private Map<String, SgBShareOutBillReleaseRequest> releaseShareOut(List<SgBShareOut> mainList, SgOmsReleaseOutRequest request, List<String> redisKeyList) {
        List<Long> mainIds = new ArrayList<>();
        Map<Long, SgBShareOut> mainMap = new HashMap<>(16);
        for (SgBShareOut sgShareOut : mainList) {
            mainIds.add(sgShareOut.getId());
            mainMap.put(sgShareOut.getId(), sgShareOut);
        }

        Map<String, SgBShareOutBillReleaseRequest> releaseMap = new HashMap<>(16);

        List<SgOmsPhyStorageOutItemRequest> itemRequestList = request.getItemRequestList();
        if (!CollectionUtils.isEmpty(itemRequestList)) {

            List<String> skuCodes = itemRequestList.stream().map(SgOmsPhyStorageOutItemRequest::getPsCSkuEcode).collect(Collectors.toList());

            //查明细
            LambdaQueryWrapper<SgBShareOutItem> itemWrapper = new LambdaQueryWrapper<>();
            itemWrapper.in(SgBShareOutItem::getSgBShareOutId, mainIds);
            itemWrapper.in(SgBShareOutItem::getPsCSkuEcode, skuCodes);
            //释放占用要大于0
            itemWrapper.ne(SgBShareOutItem::getQtyPreout, BigDecimal.ZERO);
            List<SgBShareOutItem> itemListAll = shareOutItemMapper.selectList(itemWrapper);
            Map<String, List<SgBShareOutItem>> itemsMap =
                    itemListAll.stream().collect(Collectors.groupingBy(SgBShareOutItem::getPsCSkuEcode));


            //循环处理传入参数
            for (SgOmsPhyStorageOutItemRequest itemRequest : itemRequestList) {
                if (itemsMap.containsKey(itemRequest.getPsCSkuEcode())) {
                    BigDecimal qty = itemRequest.getQty();
                    if (qty == null || BigDecimal.ZERO.compareTo(qty) >= 0) {
                        continue;
                    }

                    List<SgBShareOutItem> itemList = itemsMap.get(itemRequest.getPsCSkuEcode());
                    for (SgBShareOutItem shareOutItem : itemList) {

                        SgBShareOut sgShareOut = mainMap.get(shareOutItem.getSgBShareOutId());

                        //主表为空
                        if (sgShareOut == null) {
                            continue;
                        }

                        if (BigDecimal.ZERO.compareTo(qty) >= 0) {
                            break;
                        }

                        SgBShareOutItemReleaseRequest itemReleaseRequest = new SgBShareOutItemReleaseRequest();

                        BigDecimal qtyPreout = shareOutItem.getQtyPreout();

                        //判断是否可以释放完
                        if (qty.compareTo(qtyPreout) <= 0) {
                            itemReleaseRequest.setQty(qty);
                            itemReleaseRequest.setSkuEcode(shareOutItem.getPsCSkuEcode());
                            itemReleaseRequest.setSkuId(shareOutItem.getPsCSkuId());
                            qty = BigDecimal.ZERO;
                        } else {
                            itemReleaseRequest.setQty(qtyPreout);
                            itemReleaseRequest.setSkuEcode(shareOutItem.getPsCSkuEcode());
                            itemReleaseRequest.setSkuId(shareOutItem.getPsCSkuId());
                            qty = qty.subtract(qtyPreout);
                        }

                        Long sourceBillId = sgShareOut.getSourceBillId();
                        String sourceBillNo = sgShareOut.getSourceBillNo();
                        Long sgShareStoreId = sgShareOut.getSgCShareStoreId();

                        //相同的放到一起释放库存
                        if (releaseMap.containsKey(sourceBillId + SgConstants.SG_CONNECTOR_MARKS_6 + sourceBillNo +
                                SgConstants.SG_CONNECTOR_MARKS_6 + sgShareStoreId)) {
                            SgBShareOutBillReleaseRequest releaseRequest = releaseMap.get(sourceBillId + SgConstants.SG_CONNECTOR_MARKS_6 + sourceBillNo +
                                    SgConstants.SG_CONNECTOR_MARKS_6 + sgShareStoreId);

                            List<SgBShareOutItemReleaseRequest> shareOutItemReleaseRequests = releaseRequest.getShareOutItemReleaseRequests();
                            shareOutItemReleaseRequests.add(itemReleaseRequest);
                        } else {

                            SgBShareOutBillReleaseRequest releaseRequest = new SgBShareOutBillReleaseRequest();

                            SgBShareOutReleaseRequest outReleaseRequest = new SgBShareOutReleaseRequest();
                            outReleaseRequest.setSgCShareStoreId(sgShareStoreId);
                            outReleaseRequest.setSourceBillid(sourceBillId);
                            outReleaseRequest.setSourceBillNo(sourceBillNo);

                            List<SgBShareOutItemReleaseRequest> list = new ArrayList<>();
                            list.add(itemReleaseRequest);

                            releaseRequest.setShareOutReleaseRequest(outReleaseRequest);
                            releaseRequest.setShareOutItemReleaseRequests(list);
                            releaseRequest.setLoginUser(request.getLoginUser());
                            releaseRequest.setIsUpdateOutqty(Boolean.TRUE);

                            releaseMap.put(sourceBillId + SgConstants.SG_CONNECTOR_MARKS_6 + sourceBillNo +
                                    SgConstants.SG_CONNECTOR_MARKS_6 + sgShareStoreId, releaseRequest);
                        }

                    }

                }

            }
            //具体调用释放逻辑
            releaseShareOut(releaseMap, redisKeyList);
        }

        return releaseMap;

    }

    /**
     * 具体调用释放逻辑
     *
     * @param releaseMap releaseMap
     */
    private void releaseShareOut(Map<String, SgBShareOutBillReleaseRequest> releaseMap, List<String> redisKeyList) {

        if (MapUtils.isNotEmpty(releaseMap)) {
            List<SgBShareOutBillReleaseRequest> sgShareOutBillReleaseRequests = new ArrayList<>(releaseMap.values());

            if (!CollectionUtils.isEmpty(sgShareOutBillReleaseRequests)) {

                SgBShareOutReleaseService bean = ApplicationContextHandle.getBean(SgBShareOutReleaseService.class);
                for (SgBShareOutBillReleaseRequest releaseRequest : sgShareOutBillReleaseRequests) {

                    //相同条码要合在一起 防止 零售发货单 相同条码2行数据，共享占用单也是2条数据，要全部释放
                    // ，共享占用单释放会有一个map会把相同sku 2条记录变成一个
                    List<SgBShareOutItemReleaseRequest> shareOutItemReleaseRequests = releaseRequest.getShareOutItemReleaseRequests();
                    List<SgBShareOutItemReleaseRequest> newShareItem = new ArrayList<>();
                    // 根据sku 分组
                    Map<Long, List<SgBShareOutItemReleaseRequest>> skuMap = shareOutItemReleaseRequests.stream()
                            .collect(Collectors.groupingBy(SgBShareOutItemReleaseRequest::getSkuId));
                    for (Long skuid : skuMap.keySet()) {
                        List<SgBShareOutItemReleaseRequest> itemReleaseRequests = skuMap.get(skuid);
                        if (itemReleaseRequests.size() > 1) {
                            BigDecimal totQty = itemReleaseRequests.stream().map(SgBShareOutItemReleaseRequest::getQty)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            SgBShareOutItemReleaseRequest itemReleaseRequest = itemReleaseRequests.get(0);
                            itemReleaseRequest.setQty(totQty);
                            newShareItem.add(itemReleaseRequest);
                        } else {
                            newShareItem.add(itemReleaseRequests.get(0));
                        }
                    }
                    releaseRequest.setShareOutItemReleaseRequests(newShareItem);
                    //调用释放接口
                    ValueHolderV14<List<String>> listValueHolderV14 = bean.releaseShareOut(releaseRequest);
                    if (!listValueHolderV14.isOK()) {
                        AssertUtils.logAndThrow("多商品释放共享层库存失败:" + listValueHolderV14.getMessage());
                    }

                    //记录redis流水key
                    List<String> data = listValueHolderV14.getData();
                    if (!CollectionUtils.isEmpty(data)) {
                        redisKeyList.addAll(data);
                    }
                }
            }
        }

    }

    /**
     * 清空共享仓占用
     */
    public void cleanShareOut(List<SgBShareOut> mainList, SgOmsReleaseOutRequest request) {
        List<Long> mainIds = mainList.stream().map(SgBShareOut::getId).collect(Collectors.toList());

        LambdaQueryWrapper<SgBShareOutItem> itemWrapper = new LambdaQueryWrapper<>();
        itemWrapper.in(SgBShareOutItem::getSgBShareOutId, mainIds);
        List<SgBShareOutItem> itemListAll = shareOutItemMapper.selectList(itemWrapper);
        Map<Long, List<SgBShareOutItem>> itemsMap =
                itemListAll.stream().collect(Collectors.groupingBy(SgBShareOutItem::getSgBShareOutId));
        //查询日志明细
        List<SgBShareOutItemLog> sgBShareOutItemLogs = shareOutItemLogMapper.selectList(Wrappers.<SgBShareOutItemLog>lambdaQuery().in(SgBShareOutItemLog::getSgBShareOutId, mainIds));
        if (CollectionUtils.isEmpty(sgBShareOutItemLogs)) {
            sgBShareOutItemLogs = new ArrayList<>();
        }
        Map<Long, List<SgBShareOutItemLog>> itemLogMap =
                sgBShareOutItemLogs.stream().collect(Collectors.groupingBy(SgBShareOutItemLog::getSgBShareOutId));

        for (SgBShareOut main : mainList) {
            List<SgBShareOutItem> itemList = itemsMap.get(main.getId());
            //新增逻辑,  当共享占用 为 0时, 明细为空
            if (CollectionUtils.isEmpty(itemList)) {

                continue;
            }
            List<SgBShareOutItem> releaseItemUpdateList = new ArrayList<>();
            List<SgBShareOutItem> releaseItemDeleteList = new ArrayList<>();
            List<SgBShareOutItemLog> releaseItemLogList = new ArrayList<>();
            List<SgBShareOutItemLog> shareOutItemLogs = itemLogMap.get(main.getId());
            if (CollectionUtils.isEmpty(shareOutItemLogs)) {
                shareOutItemLogs = new ArrayList<>();
            }
            Map<String, List<SgBShareOutItemLog>> outItemLogMap = shareOutItemLogs.stream().collect(Collectors.groupingBy(SgBShareOutItemLog::getPsCSkuEcode));
            Boolean mergeMark = main.getMergeMark();
            for (SgBShareOutItem item : itemList) {
                String psCSkuEcode = item.getPsCSkuEcode();
                List<SgBShareOutItemLog> itemLogList = outItemLogMap.get(psCSkuEcode);
                if (CollectionUtils.isEmpty(itemLogList)) {
                    itemLogList = new ArrayList<>();
                }
                //建立map映射
                List<String> reqList = new ArrayList<>();
                List<String> logList = new ArrayList<>();
                List<String> list = itemLogList.stream().map(itemLog -> itemLog.getPsCSkuEcode() + SgConstants.SG_CONNECTOR_MARKS_6 + itemLog.getSourceBillItemId()).collect(Collectors.toList());
                logList.addAll(list);
                for (SgOmsPhyStorageOutItemRequest itemRequest : request.getItemRequestList()) {

                    if (!mergeMark) {
                        // 明细可能合并,  所以这里需要修改
                        if (itemRequest.getRetailItemId().equals(item.getSourceBillItemId())
                                && itemRequest.getPsCSkuEcode().equals(item.getPsCSkuEcode())) {

                            releaseItemDeleteList.add(item);
                            break;
                        }
                    } else {
                        //合并的处理逻辑
                        if (!CollectionUtils.isEmpty(itemLogList)) {
                            for (SgBShareOutItemLog sgBShareOutItemLog : itemLogList) {
                                Long logSourceBillItemId = sgBShareOutItemLog.getSourceBillItemId();
                                if (itemRequest.getRetailItemId().equals(logSourceBillItemId)
                                        && itemRequest.getPsCSkuEcode().equals(sgBShareOutItemLog.getPsCSkuEcode())) {

                                    reqList.add(itemRequest.getPsCSkuEcode() + SgConstants.SG_CONNECTOR_MARKS_6 + itemRequest.getRetailItemId());

                                    releaseItemLogList.add(sgBShareOutItemLog);
                                    break;
                                }
                            }
                        }


                    }
                }

                if (mergeMark && !CollectionUtils.isEmpty(reqList) && !CollectionUtils.isEmpty(logList)) {

                    //判断两个集合中的元素是否相同,  当前占用单明细是否全部释放
                    boolean releaseAll = checkListEquals(reqList, logList);
                    if (releaseAll) {
                        //全部匹配   即  删除当前明细
                        releaseItemDeleteList.add(item);
                    } else {
                        //修改,  不删除
                        if (!CollectionUtils.isEmpty(shareOutItemLogs)) {
                            Set<String> psCSkuEcodes = new HashSet<>();
                            Set<Long> billItemIds = new HashSet<>();
                            reqList.stream().forEach(req -> {
                                String[] split = req.split(SgConstants.SG_CONNECTOR_MARKS_6);
                                String psCSku = split[0];
                                Long billItemId = Long.valueOf(split[1]);
                                psCSkuEcodes.add(psCSku);
                                billItemIds.add(billItemId);
                            });

                            SgBShareOutItemLog shareOutItemLog = shareOutItemLogs.stream().filter(sgBShareOutItemLog -> psCSkuEcodes.contains(sgBShareOutItemLog.getPsCSkuEcode()) &&
                                    !billItemIds.contains(sgBShareOutItemLog.getSourceBillItemId()) &&
                                    !item.getSourceBillItemId().equals(sgBShareOutItemLog.getSourceBillItemId()))
                                    .findAny().orElse(null);
                            if (!ObjectUtils.isEmpty(shareOutItemLog)) {
                                item.setSourceBillItemId(shareOutItemLog.getSourceBillItemId());
                            }
                            releaseItemUpdateList.add(item);
                        }
                    }
                }

            }


            if (releaseItemUpdateList.size() > 0 || releaseItemDeleteList.size() > 0 || releaseItemLogList.size() > 0) {
                shareOutCleanService.clean(main, releaseItemUpdateList, releaseItemDeleteList, releaseItemLogList, request.getLoginUser());
            }

        }
    }

    public boolean checkListEquals(List<String> reqList, List<String> logList) {

        if (reqList.size() != logList.size()) {

            return false;
        }
        reqList.sort(Comparator.comparing(String::hashCode));
        logList.sort(Comparator.comparing(String::hashCode));
        return reqList.toString().equals(logList.toString());
    }

    /**
     * 清空逻辑仓占用
     */
    public void cleanStoOut(List<SgBStoOut> mainList, SgOmsReleaseOutRequest request) {
        List<Long> mainIds = mainList.stream().map(SgBStoOut::getId).collect(Collectors.toList());

        LambdaQueryWrapper<SgBStoOutItem> itemWrapper = new LambdaQueryWrapper<>();
        itemWrapper.in(SgBStoOutItem::getSgBStoOutId, mainIds);
        List<SgBStoOutItem> itemListAll = stoOutItemMapper.selectList(itemWrapper);
        Map<Long, List<SgBStoOutItem>> itemsMap =
                itemListAll.stream().collect(Collectors.groupingBy(SgBStoOutItem::getSgBStoOutId));
        //查询日志明细
        List<SgBStoOutItemLog> sgBStoOutItemLogs = stoOutItemLogMapper.selectList(Wrappers.<SgBStoOutItemLog>lambdaQuery().in(SgBStoOutItemLog::getSgBStoOutId, mainIds));
        if (CollectionUtils.isEmpty(sgBStoOutItemLogs)) {
            sgBStoOutItemLogs = new ArrayList<>();
        }
        Map<Long, List<SgBStoOutItemLog>> itemLogMap =
                sgBStoOutItemLogs.stream().collect(Collectors.groupingBy(SgBStoOutItemLog::getSgBStoOutId));

        for (SgBStoOut main : mainList) {
            List<SgBStoOutItem> itemList = itemsMap.get(main.getId());
            //新增逻辑,  当逻辑占用 为 0时, 明细为空
            if (CollectionUtils.isEmpty(itemList)) {

                continue;
            }
            List<SgBStoOutItem> releaseItemList = new ArrayList<>();
            List<SgBStoOutItem> releaseItemUpdateList = new ArrayList<>();
            List<SgBStoOutItemLog> releaseItemLogList = new ArrayList<>();
            List<SgBStoOutItemLog> stoOutItemLogs = itemLogMap.get(main.getId());
            if (CollectionUtils.isEmpty(stoOutItemLogs)) {
                stoOutItemLogs = new ArrayList<>();
            }
            Map<String, List<SgBStoOutItemLog>> outItemLogMap = stoOutItemLogs.stream().collect(Collectors.groupingBy(SgBStoOutItemLog::getPsCSkuEcode));
            Boolean mergeMark = main.getMergeMark();
            for (SgBStoOutItem item : itemList) {
                String psCSkuEcode = item.getPsCSkuEcode();
                List<SgBStoOutItemLog> itemLogList = outItemLogMap.get(psCSkuEcode);
                if (CollectionUtils.isEmpty(itemLogList)) {
                    itemLogList = new ArrayList<>();
                }
                //建立map映射
                List<String> reqList = new ArrayList<>();
                List<String> logList = new ArrayList<>();
                List<String> list = itemLogList.stream().map(itemLog -> itemLog.getPsCSkuEcode() + SgConstants.SG_CONNECTOR_MARKS_6 + itemLog.getSourceBillItemId()).collect(Collectors.toList());
                logList.addAll(list);
                for (SgOmsPhyStorageOutItemRequest itemRequest : request.getItemRequestList()) {
                    //未合并
                    if (!mergeMark) {
                        if (itemRequest.getRetailItemId().equals(item.getSourceBillItemId())
                                && itemRequest.getPsCSkuEcode().equals(item.getPsCSkuEcode())) {
                            releaseItemList.add(item);
                            break;
                        }
                    } else {
                        //合并后
                        if (!CollectionUtils.isEmpty(itemLogList)) {
                            for (SgBStoOutItemLog sgBStoOutItemLog : itemLogList) {
                                Long logSourceBillItemId = sgBStoOutItemLog.getSourceBillItemId();
                                if (itemRequest.getRetailItemId().equals(logSourceBillItemId)
                                        && itemRequest.getPsCSkuEcode().equals(sgBStoOutItemLog.getPsCSkuEcode())) {

                                    reqList.add(itemRequest.getPsCSkuEcode() + SgConstants.SG_CONNECTOR_MARKS_6 + itemRequest.getRetailItemId());

                                    releaseItemLogList.add(sgBStoOutItemLog);
                                    break;
                                }
                            }
                        }


                    }
                }

                if (mergeMark && !CollectionUtils.isEmpty(reqList) && !CollectionUtils.isEmpty(logList)) {

                    //判断两个集合中的元素是否相同,  当前占用单明细是否全部释放
                    boolean releaseAll = checkListEquals(reqList, logList);
                    if (releaseAll) {
                        //全部匹配   即  删除当前明细
                        releaseItemList.add(item);
                    } else {
                        //修改,  不删除
                        if (!CollectionUtils.isEmpty(stoOutItemLogs)) {
                            Set<String> psCSkuEcodes = new HashSet<>();
                            Set<Long> billItemIds = new HashSet<>();
                            reqList.stream().forEach(req -> {
                                String[] split = req.split(SgConstants.SG_CONNECTOR_MARKS_6);
                                String psCSku = split[0];
                                Long billItemId = Long.valueOf(split[1]);
                                psCSkuEcodes.add(psCSku);
                                billItemIds.add(billItemId);
                            });

                            SgBStoOutItemLog stoOutItemLog = stoOutItemLogs.stream().filter(sgBStoOutItemLog -> psCSkuEcodes.contains(sgBStoOutItemLog.getPsCSkuEcode()) &&
                                    !billItemIds.contains(sgBStoOutItemLog.getSourceBillItemId()) &&
                                    !item.getSourceBillItemId().equals(sgBStoOutItemLog.getSourceBillItemId()))
                                    .findAny().orElse(null);
                            if (!ObjectUtils.isEmpty(stoOutItemLog)) {
                                item.setSourceBillItemId(stoOutItemLog.getSourceBillItemId());
                            }
                            releaseItemUpdateList.add(item);
                        }
                    }
                }
            }
            if (releaseItemList.size() > 0 || releaseItemUpdateList.size() > 0 || releaseItemLogList.size() > 0) {
                SgBStoOutBillCleanRequest sgBStoOutBillCleanRequest = new SgBStoOutBillCleanRequest();
                //设置删除标记
                sgBStoOutBillCleanRequest.setDeleteFlag(Boolean.FALSE);
                sgBStoOutBillCleanRequest.setLoginUser(request.getLoginUser());
                sgBStoOutBillCleanRequest.setOut(main);
                sgBStoOutBillCleanRequest.setOutItemList(releaseItemList);
                sgBStoOutBillCleanRequest.setReleaseItemUpdateList(releaseItemUpdateList);
                sgBStoOutBillCleanRequest.setReleaseItemLogList(releaseItemLogList);
                sgBStoOutBillCleanRequest.setServiceNode(request.getServiceNode());
                stoOutCleanService.cleangSgBStoOut(sgBStoOutBillCleanRequest);
            }

        }
    }

}
