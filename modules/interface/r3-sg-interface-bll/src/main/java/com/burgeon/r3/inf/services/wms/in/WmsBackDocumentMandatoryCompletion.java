package com.burgeon.r3.inf.services.wms.in;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.common.SgQiMenConstants;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Description: 单据强制完成 （奇门自定义接口）
 *
 * @Author: guo.kw
 * @Since: 2022/8/1
 * create at: 2022/8/1 17:30
 */
@Slf4j
@Component
public class WmsBackDocumentMandatoryCompletion {
    public ValueHolderV14<String> apiProcess(String msg) {
        log.info("WmsBackDocumentMandatoryCompletion apiProcess msg={}", msg);
        try {
            JSONObject request = JSONObject.parseObject(msg);
            //单据类型
            String billType = request.getString("billType");
            //单据类型等于SCRK（生产入库）和CGRK采购入库
            if (SgQiMenConstants.QM_BILL_TYPE_PRODUCTION_FLOW.equals(billType) || SgQiMenConstants.QM_BILL_TYPE_PURCHASE_IN.equals(billType)) {
                //todo 根据billNo查询入库通知单   调用其它入库单【强制完成服务】
                System.out.println("调用其它入库单【强制完成服务】");
            }
            //仓内加工 领用出库
            if (SgQiMenConstants.QM_BILL_TYPE_LYCK.equals(billType) || SgQiMenConstants.QM_BILL_TYPE_CNJG.equals(billType)) {
                //todo 查询出库通知单 调用其他出库单【强制完成服务】
                System.out.println("调用其他出库单【强制完成服务】");
            }
            //调拨入库
            if (SgQiMenConstants.QM_BILL_TYPE_LOGIC_ALLOCATION.equals(billType)) {
                //todo 查询入库通知单 调用逻辑调拨单【强制完成服务】
                System.out.println("调用逻辑调拨单【强制完成服务】");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("单据强制完成异常={}", "单据强制完成回传异常"), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }


        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("success"));
    }

}
