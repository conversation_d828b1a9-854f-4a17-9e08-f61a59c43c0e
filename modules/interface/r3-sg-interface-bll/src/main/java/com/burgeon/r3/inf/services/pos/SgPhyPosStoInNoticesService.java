package com.burgeon.r3.inf.services.pos;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInNotices;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInNoticesItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.inf.model.request.pos.SgPhyQueryStoInNoticesRequest;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInNoticesItemMapper;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInNoticesMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/15
 */
@Slf4j
@Component
public class SgPhyPosStoInNoticesService {

    @Autowired
    private SgBStoInNoticesMapper stoInNoticesMapper;

    @Autowired
    private SgBStoInNoticesItemMapper stoInNoticesItemMapper;

    public ValueHolderV14<PageInfo<SgBStoInNotices>> queryStoInNotices(SgPhyQueryStoInNoticesRequest request) {
        try {
            check(request);
        } catch (Exception e) {
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }

        CpCStore store = CommonCacheValUtils.getStoreInfoByEcode(request.getStoreEcode());

        if (Objects.isNull(store)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "未查询到店铺！");
        }
        if (Objects.isNull(store.getCpCPhyWarehouseId())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "该店铺未绑定实体仓！");
        }

        // 分页查询
        PageHelper.startPage(request.getPage(), request.getPageSize());

        LambdaQueryWrapper<SgBStoInNotices> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(SgBStoInNotices::getCpCPhyWarehouseId, store.getCpCPhyWarehouseId());
        wrapper.eq(SgBStoInNotices::getBillStatus, SgStoreConstants.BILL_NOTICES_STATUS_INIT);
        wrapper.eq(SgBStoInNotices::getIsactive, SgConstants.IS_ACTIVE_Y);

        List<SgBStoInNotices> resultList = stoInNoticesMapper.selectList(wrapper);

        return new ValueHolderV14<>(new PageInfo<>(resultList), ResultCode.SUCCESS, "success");

    }

    public void check(SgPhyQueryStoInNoticesRequest request) {
        AssertUtils.notNull(request, "请求参数不能为空！");
        AssertUtils.cannot(StringUtils.isEmpty(request.getStoreEcode()), "店铺编码不能为空");

        if (Objects.isNull(request.getPage())) {
            request.setPage(1);
        }
        if (Objects.isNull(request.getPageSize())) {
            request.setPageSize(50);
        }
    }

    public ValueHolderV14<List<SgBStoInNoticesItem>> queryStoInNoticesItem(SgPhyQueryStoInNoticesRequest request) {

        if (Objects.isNull(request.getItemId())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "请求参数不能为空！");
        }

        LambdaQueryWrapper<SgBStoInNoticesItem> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(SgBStoInNoticesItem::getSgBStoInNoticesId, request.getItemId());

        List<SgBStoInNoticesItem> resultList = stoInNoticesItemMapper.selectList(wrapper);

        return new ValueHolderV14<>(resultList, ResultCode.SUCCESS, "success");
    }
}
