package com.burgeon.r3.inf.services.wms.adjust;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.inf.mapper.SgBWmsToLsStorageMapper;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.YesNoEnum;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.wms.SgBWmsToLsStorage;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.inf.common.enums.JdInventoryTypeEnum;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.api.qimen.QimenQueryStorageByJdCmd;
import com.jackrain.nea.ip.model.qimen.StockQueryRequestModel;
import com.jackrain.nea.ip.model.result.StockQueryResult;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.api.PsCSkuQueryCmd;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/9/19 15:22
 * @Description
 */
@Slf4j
@Component
public class SgBWmsQueryStorageByJdService {

    @Autowired
    private CpCPhyWarehouseMapper cpCPhyWarehouseMapper;

    @Autowired
    private SgBWmsToLsStorageMapper sgBWmsToLsStorageMapper;

    @Reference(version = "1.4.0", group = "ip",timeout = 60000)
    private QimenQueryStorageByJdCmd qimenQueryStorageByJdCmd;

    @Reference(version = "1.0", group = "ps-ext")
    private PsCSkuQueryCmd psCSkuQueryCmd;

    @NacosValue(value = "${lts.wms.query.storage.jd.limit:100}", autoRefreshed = true)
    private Integer pageSize;

    public ValueHolderV14 execute() {
        List<SgCpCPhyWarehouse> warehouseList = cpCPhyWarehouseMapper.selectList(new LambdaQueryWrapper<SgCpCPhyWarehouse>()
                .eq(SgCpCPhyWarehouse::getWmsType, ThirdWmsTypeEnum.JDWMS.getCode())
                .eq(SgCpCPhyWarehouse::getIsactive, YesNoEnum.Y.getKey())
                .isNotNull(SgCpCPhyWarehouse::getOwnerCode)
                .isNotNull(SgCpCPhyWarehouse::getWmsWarehouseCode));

        log.info(LogUtil.format("SgBWmsQueryStorageByJdService.execute,warehouseList:{}",
                "SgBWmsQueryStorageByJdService.execute"), JSON.toJSONString(warehouseList));

        if (!CollectionUtils.isEmpty(warehouseList)) {
            for (SgCpCPhyWarehouse warehouse : warehouseList) {
                try {
                    List<StockQueryResult.Item> items = queryStorage(warehouse);
                    if (!CollectionUtils.isEmpty(items)) {
                        List<String> itemCodeList = items.stream()
                                .map(StockQueryResult.Item::getItemCode).distinct().collect(Collectors.toList());
                        Map<String, PsCSku> skuMap = new HashMap<>();
                        if (!CollectionUtils.isEmpty(itemCodeList)) {
                            try {
                                ValueHolderV14<List<PsCSku>> skuListV14 = psCSkuQueryCmd.querySKUByEcodeList(itemCodeList);
                                if (skuListV14.isOK() && !CollectionUtils.isEmpty(skuListV14.getData())) {
                                    skuMap = skuListV14.getData().stream()
                                            .collect(Collectors.toMap(PsCSku::getEcode, Function.identity(), (x, y) -> y));
                                }
                            } catch (Exception e) {
                                log.error(LogUtil.format("SgBWmsQueryStorageByJdService.querySKUByEcodeList,error:{}"
                                        ,"SgBWmsQueryStorageByJdService.querySKUByEcodeList")
                                        , Throwables.getStackTraceAsString(e));
                            }
                        }
                        Map<String, List<StockQueryResult.Item>> groupBySku =
                                items.stream().collect(Collectors.groupingBy(StockQueryResult.Item::getItemCode));
                        List<SgBWmsToLsStorage> storageList = new ArrayList<>();
                        biuldParam(storageList,groupBySku,warehouse,skuMap);
                        if (!CollectionUtils.isEmpty(storageList)) {
                            sgBWmsToLsStorageMapper.batchInsert(storageList);
                        }
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("执行京东云仓库存查询异常,仓库编码:{}，错误信息:{}"
                            ,"执行京东云仓库存查询异常"),warehouse.getEcode(),e);
                }
            }
        }

        return new ValueHolderV14(ResultCode.SUCCESS,"查询成功");
    }

    private List<StockQueryResult.Item> queryStorage(SgCpCPhyWarehouse warehouse) {
        List<StockQueryResult.Item> items = new ArrayList<>();
        StockQueryRequestModel model = new StockQueryRequestModel();
        model.setCustomerId(warehouse.getWmsAccount());
        model.setOwnerCode(warehouse.getOwnerCode());
        model.setWarehouseCode(warehouse.getWmsWarehouseCode());
        model.setPageSize(Long.valueOf(pageSize));
        int page = 1;
        boolean flag = true;
        while (flag) {
            model.setPage((long) page++);
            ValueHolderV14<StockQueryResult> v14 = qimenQueryStorageByJdCmd.queryStorage(model);
            if (!v14.isOK() || v14.getData() == null) {
                throw new NDSException("查询京云仓库存失败，仓库编码：【" + warehouse.getWmsWarehouseCode() + "】");
            }
            if (!CollectionUtils.isEmpty(v14.getData().getItems())) {
                items.addAll(v14.getData().getItems());
            }
            if (Math.toIntExact(v14.getData().getTotalCount()) < pageSize) {
                flag = false;
            }
        }
        return items;
    }

    private void biuldParam(List<SgBWmsToLsStorage> storageList, Map<String, List<StockQueryResult.Item>> groupBySku
            , SgCpCPhyWarehouse warehouse, Map<String, PsCSku> skuMap) {
        for (String skuCode : groupBySku.keySet()) {
            List<StockQueryResult.Item> items = groupBySku.get(skuCode);
            SgBWmsToLsStorage storage = new SgBWmsToLsStorage();
            storage.setId(ModelUtil.getSequence(SgConstants.SG_B_WMS_TO_LS_STORAGE));
            storage.setStockDate(DateUtils.addDays(new Date(),-1));
            storage.setWmsWarehouseCode(items.get(0).getWarehouseCode());
            storage.setCpCPhyWarehouseId(warehouse.getId());
            storage.setCpCPhyWarehouseEcode(warehouse.getEcode());
            storage.setCpCPhyWarehouseEname(warehouse.getEname());
            storage.setWmsType(warehouse.getWmsType());
            storage.setSkuEcode(skuCode);
            if (skuMap.get(skuCode) != null) {
                storage.setPsCSkuId(skuMap.get(skuCode).getId());
                storage.setPsCSkuEcode(skuMap.get(skuCode).getEcode());
            }
//            storage.setStorageType(JdInventoryTypeEnum.getValueByKey(item.getInventoryType()));
            storage.setStorageType(JdInventoryTypeEnum.ZP.getValue());
//            storage.setProduceDate(item.getProductDate());
            storage.setProduceDate("00000000");
            BigDecimal qty = BigDecimal.ZERO;
            for (StockQueryResult.Item item : items) {
                qty = qty.add(BigDecimal.valueOf(item.getQuantity()));
            }
            storage.setQty(qty);
            StorageUtils.setBModelDefalutData(storage, R3SystemUserResource.getSystemRootUser());
            storageList.add(storage);
        }
    }
}
