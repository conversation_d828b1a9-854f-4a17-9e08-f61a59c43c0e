package com.burgeon.r3.inf.services.wms.in;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.store.in.SgBWmsToOrderOutStockResult;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.in.SgBWmsItemlackReportRequestMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description: 发货单缺货通知接口
 *
 * @Author: guo.kw
 * @Since: 2022/7/14
 * create at: 2022/7/14 13:56
 */
@Slf4j
@Component
public class WmsBackItemlackReportRequest {

    @Autowired
    private SgBWmsItemlackReportRequestMapper sgBWmsItemlackReportRequestMapper;
    @Autowired
    private CpCPhyWarehouseMapper cpCPhyWarehouseMapper;

    public ValueHolderV14<String> apiProcess(String msg) {
        log.info("WmsBackItemlackReportRequest apiProcess msg={}", msg);
        CusRedisTemplate<Object, Object> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();
        String lockKsy = SgConstants.SG_BILL_LOCK_WMSRETURN;

        try {
            JSONObject request = JSONObject.parseObject(msg);

            // 出库通知单号
            String noticesBillNo = request.getString("deliveryOrderCode");
            // 仓库编码
            String warehouseCode = request.getString("warehouseCode");

            lockKsy += noticesBillNo;

            Boolean ifAbsent = redisMasterTemplate.opsForValue().setIfAbsent(lockKsy, "OK");

            SgBWmsToOrderOutStockResult sgBWmsToOrderOutStockResult = sgBWmsItemlackReportRequestMapper.selectOne(new LambdaQueryWrapper<SgBWmsToOrderOutStockResult>()
                    .eq(SgBWmsToOrderOutStockResult::getNoticesBillNo, noticesBillNo)
                    .eq(SgBWmsToOrderOutStockResult::getIsactive, "Y"));

            if (Objects.nonNull(sgBWmsToOrderOutStockResult) || ifAbsent == null || !ifAbsent) {
                log.error(LogUtil.format("发货单缺货WMS回传重复.messageBody=", "发货单缺货WMS回传重复", sgBWmsToOrderOutStockResult), msg);
            } else {
                redisMasterTemplate.expire(lockKsy, 30, TimeUnit.SECONDS);

                SgBWmsToOrderOutStockResult sgBWmsToOrderOutStockResult1 = new SgBWmsToOrderOutStockResult();

                sgBWmsToOrderOutStockResult1.setId(ModelUtil.getSequence(SgConstants.SG_B_WMS_TO_ORDER_OUT_STOCK_RESULT));
                sgBWmsToOrderOutStockResult1.setNoticesBillNo(noticesBillNo);
                sgBWmsToOrderOutStockResult1.setWarehouseCode(warehouseCode);
                sgBWmsToOrderOutStockResult1.setMessage(msg);

                if (StringUtils.isNotEmpty(warehouseCode)) {
                    SgCpCPhyWarehouse warehouse = cpCPhyWarehouseMapper.getCpCPhyWarehouseName(warehouseCode);
                    if (ThirdWmsTypeEnum.JDWMS.getCode().equals(warehouse.getWmsType())) {
                        JSONArray orderLines = request.getJSONArray("orderLines");
                        JSONArray newOrderLines = new JSONArray();
                        if (orderLines != null && orderLines.size() > 0) {
                            for (int i = 0; i < orderLines.size(); i++) {
                                JSONObject jsonObject = orderLines.getJSONObject(i);
                                JSONArray batchs = jsonObject.getJSONArray("batchs");
                                if (batchs != null && batchs.size() > 0) {
                                    for (int j = 0; j < batchs.size(); j++) {
                                        JSONObject newJsonObject = JSONObject.parseObject(jsonObject.toJSONString());

                                        JSONObject batch = batchs.getJSONObject(j);
                                        if (!StringUtils.isEmpty(batch.getString("productDate"))) {
                                            batch.put("batchCode",batch.getString("productDate"));
                                        }
                                        batch.keySet().forEach(x -> newJsonObject.put(x, batch.get(x)));
                                        newOrderLines.add(newJsonObject);
                                    }
                                }
                            }
                        }
                        request.put("orderLines",newOrderLines);
                        sgBWmsToOrderOutStockResult1.setMessage(request.toJSONString());
                    }
                }

                sgBWmsToOrderOutStockResult1.setWmsWarehouseType(ThirdWmsTypeEnum.QMWMS.getCode());
                sgBWmsToOrderOutStockResult1.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT);
                sgBWmsToOrderOutStockResult1.setFailedCount(NumberUtils.INTEGER_ZERO);
                sgBWmsToOrderOutStockResult1.setIsactive(SgConstants.IS_ACTIVE_Y);
                StorageUtils.setBModelDefalutData(sgBWmsToOrderOutStockResult1, R3SystemUserResource.getSystemRootUser());
                sgBWmsItemlackReportRequestMapper.insert(sgBWmsToOrderOutStockResult1);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("发货单缺货WMS回传异常={}", "发货单缺货WMS回传异常"), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        } finally {
            redisMasterTemplate.delete(lockKsy);
        }
        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("success"));
    }
}
