package com.burgeon.r3.inf.services.wms.in;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.store.in.SgBWmsToStoEntryInResult;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.in.SgBWmsToStoEntryInResultMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description: 奇门入库单确认
 *
 * @Author: guo.kw
 * @Since: 2022/7/13
 * create at: 2022/7/13 11:12
 */
@Slf4j
@Component
public class WmsBackEntryorderConfirmService {

    @Autowired
    private SgBWmsToStoEntryInResultMapper sgBWmsToStoEntryInResultMapper;

    @Autowired
    private CpCPhyWarehouseMapper cpCPhyWarehouseMapper;

    public ValueHolderV14<String> apiProcess(String msg) {
        log.info(LogUtil.format("B2B入库回传存中间表，报文={}", "WmsBackEntryorderConfirmService.apiProcess"), msg);
        CusRedisTemplate<Object, Object> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();
        String lockKsy = SgConstants.SG_BILL_LOCK_WMSRETURN;

        try {

            JSONObject request = JSONObject.parseObject(msg);

            JSONObject entryOrder = request.getJSONObject("entryOrder");
            //入库单号
            String entryOrderCode = entryOrder.getString("entryOrderCode");
            //仓库编码
            String warehouseCode = entryOrder.getString("warehouseCode");
            //入库单类型
            String entryOrderType = entryOrder.getString("entryOrderType");
            //wms单号
            String entryOrderId = entryOrder.getString("entryOrderId");
            //外部业务编码
            String outBizCode = entryOrder.getString("outBizCode");
            if (StringUtils.isEmpty(outBizCode)) {
                throw new NDSException("外部业务编码不能为空");
            }
            lockKsy += outBizCode;

            /*查询B2B入库回传中间表*/
            SgBWmsToStoEntryInResult sgBWmsToStoEntryInResult1 = sgBWmsToStoEntryInResultMapper.selectOne(new LambdaQueryWrapper<SgBWmsToStoEntryInResult>()
                    .eq(SgBWmsToStoEntryInResult::getOutBizCode, outBizCode)
                    .eq(SgBWmsToStoEntryInResult::getIsactive, "Y"));

            Boolean ifAbsent = redisMasterTemplate.opsForValue().setIfAbsent(lockKsy, "OK");
            if (Objects.nonNull(sgBWmsToStoEntryInResult1) || ifAbsent == null || !ifAbsent) {
                log.error(LogUtil.format("B2B入库单WMS回传重复.messageBody=", "B2B入库单WMS回传重复", sgBWmsToStoEntryInResult1), msg);
            } else {
                redisMasterTemplate.expire(lockKsy, 30, TimeUnit.SECONDS);

                SgBWmsToStoEntryInResult sgBWmsToStoEntryInResult = new SgBWmsToStoEntryInResult();
                sgBWmsToStoEntryInResult.setId(ModelUtil.getSequence(SgConstants.SG_B_WMS_TO_STO_ENTRY_IN_RESULT));
                sgBWmsToStoEntryInResult.setNoticesBillNo(entryOrderCode);
                sgBWmsToStoEntryInResult.setWarehouseCode(warehouseCode);
                sgBWmsToStoEntryInResult.setBillType(entryOrderType);
                sgBWmsToStoEntryInResult.setWmsBillCode(entryOrderId);
                sgBWmsToStoEntryInResult.setOutBizCode(outBizCode);

                SgCpCPhyWarehouse cpCPhyWarehouse = null;
                if (!StringUtils.isEmpty(warehouseCode)) {
                    cpCPhyWarehouse = cpCPhyWarehouseMapper.getCpCPhyWarehouseName(warehouseCode);
                }

                if (cpCPhyWarehouse != null) {
                    sgBWmsToStoEntryInResult.setWmsWarehouseType(cpCPhyWarehouse.getWmsType());
                    /*如果是京云仓，需要特殊处理效期*/
                    if (ThirdWmsTypeEnum.JDWMS.getCode().equals(cpCPhyWarehouse.getWmsType())) {
                        JSONArray orderLines = request.getJSONArray("orderLines");
                        JSONArray newOrderLines = new JSONArray();
                        if (orderLines != null && orderLines.size() > 0) {
                            for (int i = 0; i < orderLines.size(); i++) {
                                JSONObject jsonObject = orderLines.getJSONObject(i);
                                JSONArray batchs = jsonObject.getJSONArray("batchs");
                                if (batchs != null && batchs.size() > 0) {
                                    for (int j = 0; j < batchs.size(); j++) {
                                        JSONObject newJsonObject = JSONObject.parseObject(jsonObject.toJSONString());
                                        JSONObject batch = batchs.getJSONObject(j);
                                        if (!StringUtils.isEmpty(batch.getString("productDate"))) {
                                            batch.put("batchCode",batch.getString("productDate"));
                                        }
                                        batch.keySet().forEach(x -> newJsonObject.put(x, batch.get(x)));
                                        newJsonObject.remove("batchs");
                                        newOrderLines.add(newJsonObject);
                                    }
                                }
                            }
                        }
                        request.put("orderLines", newOrderLines);
                    }
                    /*如果是大宝仓，需要特殊处理效期和商品编码*/
                    if (ThirdWmsTypeEnum.DBWMS.getCode().equals(cpCPhyWarehouse.getWmsType())) {
                        JSONArray orderLines = request.getJSONArray("orderLines");
                        JSONArray newOrderLines = new JSONArray();

                        SimpleDateFormat beforeFormat = new SimpleDateFormat("yyyyMMdd");
                        SimpleDateFormat afterFormat = new SimpleDateFormat("yyyy-MM-dd");
                        if (orderLines != null && orderLines.size() > 0) {
                            for (int i = 0; i < orderLines.size(); i++) {
                                JSONObject jsonObject = orderLines.getJSONObject(i);
                                JSONObject newJsonObject = JSONObject.parseObject(jsonObject.toJSONString());
                                //处理商品编码
                                String itemId = jsonObject.getString("itemId");
                                String skuCode = CommonCacheValUtils.querySkuCodeByItemIdAndCustomerId(cpCPhyWarehouse.getWmsAccount(), itemId);
                                newJsonObject.put("itemCode", skuCode);
                                //处理效期
                                if (!StringUtils.isEmpty(jsonObject.getString("produceCode"))) {
                                    try {
                                        Date produceCode = beforeFormat.parse(jsonObject.getString("produceCode"));
                                        newJsonObject.put("batchCode", afterFormat.format(produceCode));
                                    } catch (ParseException e) {
                                        log.error(LogUtil.format("WmsBackEntryorderConfirmService.apiProcess.error:{}",
                                                "WmsBackEntryorderConfirmService.apiProcess.error"), Throwables.getStackTraceAsString(e));
                                    }
                                }
                                newOrderLines.add(newJsonObject);
                            }
                        }
                        request.put("orderLines", newOrderLines);
                    }
                    /*富勒仓库编码取库存地点*/
                    if (ThirdWmsTypeEnum.FLWMS.getCode().equals(cpCPhyWarehouse.getWmsType())) {
                        JSONObject extendProps = request.getJSONObject("extendProps");
                        if (extendProps != null) {
                            String storageLocation = extendProps.getString("storageLocation");
                            if (StringUtils.isNotEmpty(storageLocation)) {
                                sgBWmsToStoEntryInResult.setWarehouseCode(storageLocation);
                            }
                        }
                    }
                }
                sgBWmsToStoEntryInResult.setMessage(request.toJSONString());
                sgBWmsToStoEntryInResult.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT);
                sgBWmsToStoEntryInResult.setFailedCount(NumberUtils.INTEGER_ZERO);
                sgBWmsToStoEntryInResult.setIsactive(SgConstants.IS_ACTIVE_Y);
                StorageUtils.setBModelDefalutData(sgBWmsToStoEntryInResult, R3SystemUserResource.getSystemRootUser());
                sgBWmsToStoEntryInResultMapper.insert(sgBWmsToStoEntryInResult);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("B2B入库单WMS回传异常，异常：{}", "WmsBackEntryorderConfirmService.apiProcess"),
                    Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        } finally {
            redisMasterTemplate.delete(lockKsy);
        }

        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("success"));
    }
}
