package com.burgeon.r3.inf.handler;

import com.burgeon.r3.inf.services.wms.in.SgBWmsReturnMiddleTableSaveService;
import com.burgeon.r3.sg.inf.common.SgMQConstants;
import com.google.common.base.Throwables;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * WMS发起回传中间表 库存异动
 */
@Slf4j
@Service(SgMQConstants.WMS_RETURN_MIDDLE_TABLE_METHOD)
public class WMSReturnMiddleTableHandler implements CallBackApi {

    @Autowired
    private SgBWmsReturnMiddleTableSaveService sgBWmsReturnMiddleTableSaveService;

    @Override
    public ValueHolderV14 apiProcess(String messageBody) {
        ValueHolderV14 holder;
        try {
            holder = sgBWmsReturnMiddleTableSaveService.saveWmsReturnMiddleTable(messageBody);
            if (!holder.isOK()) {
                log.error("WMS --> sg,oms 回执失败，failMessage:{}", holder.getMessage());
            }
        } catch (Exception e) {
            log.error("库存异动 WMS --> sg,oms 回执异常，error:{}", Throwables.getStackTraceAsString(e));
            throw e;
        }
        return holder;
    }

}