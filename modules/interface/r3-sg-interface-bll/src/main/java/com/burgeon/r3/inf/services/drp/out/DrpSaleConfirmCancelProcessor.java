package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransfer;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransferItem;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.transfer.SgBStoTransferMapper;
import com.burgeon.r3.sg.store.services.transfer.SgTransferBillStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * 销售确认取消接口
 * @version 1.0
 * @date 2021/9/122 20:59
 */

@Slf4j
@Component
public class DrpSaleConfirmCancelProcessor extends AbstractDrpInterfaceProcessor<SgBStoTransfer, SgBStoTransferItem> {

    @Autowired
    SgBStoTransferMapper sgStoTransferMapper;

    @Override
    public LambdaQueryWrapper execMainWrapper() {
        LambdaQueryWrapper<SgBStoTransfer> wrapper = new LambdaQueryWrapper<>();
        //“DRP业务类型”=调拨流程、“单据状态”=“已确认未出库”，且“确认传DRP状态”=“传失败”或者 “未传”，的【逻辑调拨单
        wrapper.eq(SgBStoTransfer::getStatus, SgTransferBillStatusEnum.UN_AUDITED.getVal());
        wrapper.eq(SgBStoTransfer::getDrpBillType, SgStoreConstants.DRP_BILL_TYPE_SA);
        wrapper.eq(SgBStoTransfer::getIsactive, SgConstants.IS_ACTIVE_Y);
        wrapper.and(o -> {
            o.eq(SgBStoTransfer::getConfirmCancelDrpStatus, SgStoreConstants.SEND_DRP_STATUS_UNDECLARED);
            o.or(oo -> oo.eq(SgBStoTransfer::getConfirmCancelDrpStatus, SgStoreConstants.SEND_DRP_STATUS_FAIL).lt(SgBStoTransfer::getConfirmCancelDrpFailCount, failNum));
            return o;
        });
        return wrapper;
    }

    @Override
    public LambdaQueryWrapper<SgBStoTransferItem> execitemWrapper(Long mainId) {
        return null;
    }

    @Override
    public String drpInterfaceUrl() {
        return "erp.sale.confirmcancel";
    }

    @Override
    public String itemMainField() {
        return "sg_b_sto_transfer_id";
    }

    @Override
    public String drpStatus() {
        return "CONFIRM_CANCEL_DRP_STATUS";
    }

    @Override
    public String drpStatusFailCount() {
        return "CONFIRM_CANCEL_DRP_FAIL_COUNT";
    }

    @Override
    public String drpStatusFailReason() {
        return "CONFIRM_CANCEL_DRP_REASON";
    }

    @Override
    public JSONObject execInterfaceParam(SgBStoTransfer sgStoTransfer, List<SgBStoTransferItem> itemList) {
        JSONObject mian = new JSONObject();
        //中台单据编号
        mian.put("ZTDOCNO", sgStoTransfer.getBillNo());
        return mian;
    }

    @Override
    public void handleBysuccess(SgBStoTransfer sgStoTransfer, List<SgBStoTransferItem> z) {

    }
}
