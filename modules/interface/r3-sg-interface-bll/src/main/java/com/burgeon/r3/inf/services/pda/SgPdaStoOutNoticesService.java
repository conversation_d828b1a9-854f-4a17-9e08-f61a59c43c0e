package com.burgeon.r3.inf.services.pda;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOut;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItem;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNoticesItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.inf.model.request.pda.SgStoOutNoticesItemsPdaRequest;
import com.burgeon.r3.sg.inf.model.request.pda.SgStoOutNoticesItemsSavePdaRequest;
import com.burgeon.r3.sg.inf.model.request.pda.SgStoOutNoticesMainPdaRequest;
import com.burgeon.r3.sg.inf.model.request.pda.SgStoOutNoticesSavePdaRequest;
import com.burgeon.r3.sg.inf.model.result.pda.SgStoOutNoticesItemsPdaResult;
import com.burgeon.r3.sg.inf.model.result.pda.SgStoOutNoticesMainPdaResult;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesMapper;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutResultBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutResultItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutResultSaveRequest;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutResultBillSaveResult;
import com.burgeon.r3.sg.store.services.out.SgBStoOutResultSaveService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.table.CpCustomer;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description 库通知单信息查询，保存，审核
 * <AUTHOR>
 * @Date 2021/8/11 15:59
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgPdaStoOutNoticesService {

    @Autowired
    private SgBStoOutNoticesMapper sgBStoOutNoticesMapper;

    @Autowired
    private SgBStoOutNoticesItemMapper sgBStoOutNoticesItemMapper;

    @Autowired
    private SgBStoOutMapper sgStoOutMapper;

    @Autowired
    private SgBStoOutItemMapper sgStoOutItemMapper;

    /**
     * 查询出来通知单主表信息
     *
     * @param request
     * @return
     */
    public ValueHolderV14<PageInfo<SgStoOutNoticesMainPdaResult>> querySgBStoOutNoticesMain(SgStoOutNoticesMainPdaRequest request, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgPdaStoOutNoticesService.querySgBStoOutNoticesMain. ReceiveParams:" +
                    "request:{};", JSONObject.toJSONString(request));
        }
        ValueHolderV14 holder = new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        Date billDateBegin = request.getBillDateBegin();
        Date billDateEnd = request.getBillDateEnd();
        String billNo = request.getBillNo();
        List<String> billStatusList = request.getBillStatus();
        List<Long> sgBStoOutNoticesIds = request.getSgBStoOutNoticesId();
        List<SgStoOutNoticesMainPdaResult> resultList = new ArrayList<>();
        List<SgBStoOutNotices> sgBStoOutNoticesList = null;
        try {

            if (request.getPageSize() != null && request.getPageNum() != null) {
                /** 分页查询 **/
                PageHelper.startPage(request.getPageNum(), request.getPageSize());
            }
            //全量查询
            sgBStoOutNoticesList = sgBStoOutNoticesMapper.selectList(new LambdaQueryWrapper<SgBStoOutNotices>()
                    .in(SgBStoOutNotices::getBillStatus, billStatusList)
                    .in(!CollectionUtils.isEmpty(sgBStoOutNoticesIds), SgBStoOutNotices::getId, sgBStoOutNoticesIds)
                    .like(StringUtils.isNotEmpty(billNo), SgBStoOutNotices::getBillNo, billNo)
                    .ge(billDateBegin != null, SgBStoOutNotices::getBillDate, billDateBegin)
                    .le(billDateEnd != null, SgBStoOutNotices::getBillDate, billDateEnd));
            if (CollectionUtils.isNotEmpty(sgBStoOutNoticesList)) {
                sgBStoOutNoticesList.forEach(item -> {
                    SgStoOutNoticesMainPdaResult pdaItem = new SgStoOutNoticesMainPdaResult();
                    BeanUtils.copyProperties(item, pdaItem);
                    pdaItem.setBillStatus(item.getBillStatus().toString());
                    pdaItem.setSgBStoOutNoticesId(item.getId());
                    pdaItem.setSourceBillType("调拨单");
                    CpCustomer cpCustomerById = CommonCacheValUtils.getCpCustomerById(item.getCpCCustomerId());
                    if (log.isDebugEnabled()) {
                        log.debug("SgPdaStoOutNoticesService.querySgBStoOutNoticesMain.ReceiveParams:CpCustomer:{};", JSONObject.toJSONString(cpCustomerById));
                    }
                    if (cpCustomerById != null) {
                        pdaItem.setCpCCustomerEname(cpCustomerById.getEname());
                    }
                    resultList.add(pdaItem);
                });
            }

            if (resultList != null) {
                holder.setData(new PageInfo<>(resultList));
            } else {
                holder.setData(resultList);
            }
        } catch (Exception e) {
            log.error("SgPdaStoOutNoticesService.querySgBStoOutNoticesMain. error:{}", Throwables.getStackTraceAsString(e));
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("查询出库通知单主表信息发生异常," + e.getMessage(), loginUser.getLocale()));
        }
        return holder;
    }

    /**
     * 查询出库通知单明细
     *
     * @param request
     * @param loginUser 登录用户
     * @return
     */
    public ValueHolderV14<PageInfo<SgStoOutNoticesItemsPdaResult>> querySgBStoOutNoticesItem(SgStoOutNoticesItemsPdaRequest request, User loginUser) {
        ValueHolderV14 holder = new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        if (log.isDebugEnabled()) {
            log.debug("Start SgPdaStoOutNoticesService.querySgBStoOutNoticesItem. ReceiveParams:" +
                    "request:{};", JSONObject.toJSONString(request));
        }
        List<SgStoOutNoticesItemsPdaResult> resultList = new ArrayList<>();
        List<SgBStoOutNoticesItem> sgBStoOutNoticesItemsList = null;
        try {

            if (request.getPageSize() != null && request.getPageNum() != null) {
                /** 分页查询 **/
                PageHelper.startPage(request.getPageNum(), request.getPageSize());
            }
            //全量查询
            sgBStoOutNoticesItemsList = sgBStoOutNoticesItemMapper.selectList(new LambdaQueryWrapper<SgBStoOutNoticesItem>()
                    .in(SgBStoOutNoticesItem::getSgBStoOutNoticesId, request.getSgBStoOutNoticesId()));


            if (CollectionUtils.isNotEmpty(sgBStoOutNoticesItemsList)) {

                List<String> psCSkuEcodeLists = sgBStoOutNoticesItemsList.stream().map(SgBStoOutNoticesItem::getPsCSkuEcode).collect(Collectors.toList());

                Map<String, PsCProSkuResult> psCProSkuResultMap = CommonCacheValUtils.getSkuInfo(psCSkuEcodeLists);
                if (MapUtils.isEmpty(psCProSkuResultMap)) {
                    holder.setCode(ResultCode.FAIL);
                    holder.setMessage("条码信息查询为空");
                    return holder;
                }
                sgBStoOutNoticesItemsList.forEach(item -> {
                    SgStoOutNoticesItemsPdaResult pdaItem = new SgStoOutNoticesItemsPdaResult();
                    BeanUtils.copyProperties(item, pdaItem);
                    pdaItem.setSgBStoOutNoticesItemId(item.getId());
                    //获取条码信息
                    if (psCProSkuResultMap.containsKey(item.getPsCSkuEcode())) {
                        PsCProSkuResult psCProSkuResult = psCProSkuResultMap.get(item.getPsCSkuEcode());
                        pdaItem.setPsCSpec1Id(psCProSkuResult.getPsCSpec1objId());
                        pdaItem.setPsCSpec1Ecode(psCProSkuResult.getClrsEcode());
                        pdaItem.setPsCSpec1Ename(psCProSkuResult.getClrsEname());
                        pdaItem.setPsCSpec2Id(psCProSkuResult.getPsCSpec2objId());
                        pdaItem.setPsCSpec2Ecode(psCProSkuResult.getSizesEcode());
                        pdaItem.setPsCSpec2Ename(psCProSkuResult.getSizesEname());
                        pdaItem.setGbcode(psCProSkuResult.getGbcode());
                        pdaItem.setForcode(psCProSkuResult.getForcode());
                        pdaItem.setPsCProId(psCProSkuResult.getPsCProId());
                        pdaItem.setPsCProEname(psCProSkuResult.getPsCProEname());
                        pdaItem.setPsCProEcode(psCProSkuResult.getPsCProEcode());
                        pdaItem.setPriceList(Optional.ofNullable(psCProSkuResult.getPricelist()).orElse(BigDecimal.ZERO));
                    }
                    resultList.add(pdaItem);
                });
            }

            if (resultList != null) {
                holder.setData(new PageInfo<>(resultList));
            } else {
                holder.setData(resultList);
            }
        } catch (Exception e) {
            log.error("SgPdaStoOutNoticesService.querySgBStoOutNoticesItem. error:{}", Throwables.getStackTraceAsString(e));
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("查询出库通知单明细表信息发生异常," + e.getMessage(), loginUser.getLocale()));
        }
        return holder;
    }

    /**
     * 保存或审核出库通知单
     *
     * @param request
     * @param loginUser
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 saveOrSubmitStoOutNotices(SgStoOutNoticesSavePdaRequest request, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgPdaStoOutNoticesService.saveOrSubmitStoOutNotices. ReceiveParams:" +
                    "request:{};", JSONObject.toJSONString(request));
        }
        ValueHolderV14 holder = new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        List<String> redisBillFtpKeyList = new ArrayList<>();
        try {
            SgBStoOutNotices sgBStoOutNotices = new SgBStoOutNotices();
            if (request.getSgBStoOutNoticesId() == null) {
                sgBStoOutNotices = sgBStoOutNoticesMapper.selectOne(new LambdaQueryWrapper<SgBStoOutNotices>()
                        .eq(SgBStoOutNotices::getBillNo, request.getBillNo()));
            } else {
                sgBStoOutNotices = sgBStoOutNoticesMapper.selectById(request.getSgBStoOutNoticesId());
            }

            if (sgBStoOutNotices == null) {
                holder.setCode(ResultCode.FAIL);
                holder.setMessage(Resources.getMessage("出库通知单不存在！", loginUser.getLocale()));
                return holder;
            }
            request.setSgBStoOutNoticesId(sgBStoOutNotices.getId());
            if (!SgStoreConstants.BILL_NOTICES_STATUS_INIT.equals(sgBStoOutNotices.getBillStatus())) {
                holder.setCode(ResultCode.FAIL);
                holder.setMessage(Resources.getMessage("出库通知单状态不符，不允许保存！", loginUser.getLocale()));
                return holder;
            }

            if (CollectionUtils.isNotEmpty(request.getItem())) {
                for (SgStoOutNoticesItemsSavePdaRequest resultItem : request.getItem()) {

                    SgBStoOutNoticesItem sgBStoOutNoticesItem = sgBStoOutNoticesItemMapper.selectOne(new LambdaQueryWrapper<SgBStoOutNoticesItem>()
                            .eq(SgBStoOutNoticesItem::getId, resultItem.getSgBStoOutNoticesItemId())
                            .eq(SgBStoOutNoticesItem::getSgBStoOutNoticesId, request.getSgBStoOutNoticesId()));

                    if (sgBStoOutNoticesItem == null) {
                        holder.setCode(ResultCode.FAIL);
                        holder.setMessage(Resources.getMessage("出库通知单明细" + resultItem.getSgBStoOutNoticesItemId() + "不存在！", loginUser.getLocale()));
                        return holder;
                    }

                    // 产品要求将这个数据放在出库通知单的扫描
                    BigDecimal qtyScan = resultItem.getQtyOut() == null ? BigDecimal.ZERO : resultItem.getQtyOut();

                    SgBStoOutNoticesItem update = new SgBStoOutNoticesItem();
                    update.setQtyScan(qtyScan);
                    update.setId(resultItem.getSgBStoOutNoticesItemId());
                    update.setSgBStoOutNoticesId(request.getSgBStoOutNoticesId());
                    update.setModifierename(loginUser.getEname());
                    update.setModifiername(loginUser.getName());
                    update.setModifieddate(new Date());
                    sgBStoOutNoticesItemMapper.update(update, new LambdaQueryWrapper<SgBStoOutNoticesItem>()
                            .eq(SgBStoOutNoticesItem::getId, resultItem.getSgBStoOutNoticesItemId())
                            .eq(SgBStoOutNoticesItem::getSgBStoOutNoticesId, request.getSgBStoOutNoticesId()));
                }
                SgBStoOutNotices updateMain = new SgBStoOutNotices();
                StorageUtils.setBModelDefalutDataByUpdate(updateMain, loginUser);
                sgBStoOutNoticesMapper.update(updateMain, new LambdaQueryWrapper<SgBStoOutNotices>()
                        .eq(SgBStoOutNotices::getId, request.getSgBStoOutNoticesId()));
            }

            if (SgConstants.IS_SUBMIT_Y.equals(request.getIsSubmit())) {
                // : 审核生成逻辑出库单  调用新增并审核逻辑出库单服务；逻辑出库单的出库数量为出库通知单的扫描数量
                ValueHolderV14<SgBStoOutResultBillSaveResult> sgBStoOutResultBillSaveResult = insertSgOutResult(request.getSgBStoOutNoticesId(), redisBillFtpKeyList, loginUser);
                if (!sgBStoOutResultBillSaveResult.isOK()) {
                    AssertUtils.logAndThrow("审核逻辑出库单失败！");
                }
                redisBillFtpKeyList.addAll(sgBStoOutResultBillSaveResult.getData().getRedisBillFtpKeyList());
            }

        } catch (Exception e) {
            StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, loginUser);
            log.error("SgPdaStoOutNoticesService.querySgBStoOutNoticesItem. error:{}", Throwables.getStackTraceAsString(e));
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("保存或审核出库通知单明细表信息发生异常！" + e.getMessage(), loginUser.getLocale()));
        }

        return holder;
    }

    /**
     * 新增并审核逻辑出库单
     *
     * @param sgBStoOutNoticesId 出库通知单
     * @param loginUser
     * @return
     */
    private ValueHolderV14<SgBStoOutResultBillSaveResult> insertSgOutResult(Long sgBStoOutNoticesId, List<String> redisBillFtpKeyList, User loginUser) {
        //获取出库单信息
        SgBStoOutNotices sgBStoOutNotices = sgBStoOutNoticesMapper.selectById(sgBStoOutNoticesId);

        List<SgBStoOutNoticesItem> sgBStoOutNoticesItemList = sgBStoOutNoticesItemMapper.selectList(new LambdaQueryWrapper<SgBStoOutNoticesItem>().eq(SgBStoOutNoticesItem::getSgBStoOutNoticesId, sgBStoOutNoticesId));

        if (sgBStoOutNotices.getIsactive().equals(SgConstants.IS_ACTIVE_N)) {
            throw new NDSException(Resources.getMessage("出库通知单不存在!", loginUser.getLocale()));
        } else if (sgBStoOutNotices.getBillStatus().equals(SgStoreConstants.BILL_NOTICES_STATUS_ALL_OUT) || sgBStoOutNotices.getBillStatus().equals(SgStoreConstants.BILL_NOTICES_STATUS_VOID)) {
            throw new NDSException(Resources.getMessage("出库通知单单据状态不符合!", loginUser.getLocale()));
        }
        //获取出库通知单明细
        if (CollectionUtils.isEmpty(sgBStoOutNoticesItemList)) {
            throw new NDSException(Resources.getMessage("出库通知单明细不存在!", loginUser.getLocale()));
        }
        //通过来源单单据编号获取逻辑占用单的信息id和明细id

        SgBStoOut sgBStoOut = sgStoOutMapper.selectOne(new QueryWrapper<SgBStoOut>().lambda()
                .eq(SgBStoOut::getSourceBillNo, sgBStoOutNotices.getSourceBillNo())
                .eq(SgBStoOut::getSourceBillType, sgBStoOutNotices.getSourceBillType())
                .eq(SgBStoOut::getIsactive, SgConstants.IS_ACTIVE_Y));

        if (sgBStoOut == null) {
            AssertUtils.logAndThrow("逻辑占用单不存在！");
        }
        List<SgBStoOutItem> sgBStoOutItems = sgStoOutItemMapper.selectList(new QueryWrapper<SgBStoOutItem>().lambda()
                .eq(SgBStoOutItem::getSgBStoOutId, sgBStoOut.getId()));

        if (CollectionUtils.isEmpty(sgBStoOutItems)) {
            AssertUtils.logAndThrow("逻辑占用单明细不存在！");
        }
        //主表信息
        SgBStoOutResultSaveRequest outResultSaveRequest = new SgBStoOutResultSaveRequest();
        BeanUtils.copyProperties(sgBStoOutNotices, outResultSaveRequest);
        outResultSaveRequest.setBillNo("");
        outResultSaveRequest.setId(-1L);
        outResultSaveRequest.setSgBStoOutNoticesId(sgBStoOutNotices.getId());
        outResultSaveRequest.setSgBStoOutNoticesNo(sgBStoOutNotices.getBillNo());
        //在明细上取出逻辑仓信息
        SgBStoOutNoticesItem sgBStoOutNoticesItem = sgBStoOutNoticesItemList.get(0);
        outResultSaveRequest.setCpCStoreId(sgBStoOutNoticesItem.getCpCStoreId());
        outResultSaveRequest.setCpCStoreEcode(sgBStoOutNoticesItem.getCpCStoreEcode());
        outResultSaveRequest.setCpCStoreEname(sgBStoOutNoticesItem.getCpCStoreEname());

        outResultSaveRequest.setReceiverEcode(sgBStoOutNotices.getReceiverEcode());
        outResultSaveRequest.setReceiverName(sgBStoOutNotices.getReceiverName());

        //来源单据字段
        outResultSaveRequest.setSourceBillId(sgBStoOutNotices.getSourceBillId());
        outResultSaveRequest.setSourceBillType(sgBStoOutNotices.getSourceBillType());
        outResultSaveRequest.setSourceBillNo(sgBStoOutNotices.getSourceBillNo());
        outResultSaveRequest.setSourceBillType(sgBStoOutNotices.getSourceBillType());
        outResultSaveRequest.setIsLast(SgConstants.IS_LAST_YES);
        outResultSaveRequest.setSgBStoOutId(sgBStoOut.getId());
        //复制明细字段值
        List<SgBStoOutResultItemSaveRequest> itemList = Lists.newArrayList();
        sgBStoOutNoticesItemList.forEach(item -> {
            if (item.getQtyScan() == null || item.getQty() == null) {
                AssertUtils.logAndThrow("出库通知单明细中扫描数量或通知数量不存在！");
            }
            if (item.getQty().compareTo(item.getQtyScan()) != 0) {
                AssertUtils.logAndThrow("出库通知单明细中通知数量和扫描数量不一致！");
            }

            SgBStoOutResultItemSaveRequest itemRequest = new SgBStoOutResultItemSaveRequest();
            BeanUtils.copyProperties(item, itemRequest);
            itemRequest.setId(-1L);
            itemRequest.setQty(item.getQtyScan());
            itemRequest.setPsCSkuId(item.getPsCSkuId());
            itemRequest.setPsCProId(item.getPsCProId());
            itemList.add(itemRequest);
        });
        SgBStoOutResultBillSaveRequest request = new SgBStoOutResultBillSaveRequest();
        request.setIsAutoOut(SgConstants.IS_ACTIVE_Y);
        request.setOutResultSaveRequest(outResultSaveRequest);
        request.setOutItemResultSaveRequestList(itemList);
        request.setLoginUser(loginUser);
        request.setRedisKey(redisBillFtpKeyList);
        if (log.isDebugEnabled()) {
            log.debug("Start SgBStoOutResultSaveR3Service.insertSgOutResult.SgBStoOutResultBillSaveRequest:{};", JSONObject.toJSONString(request));
        }
        SgBStoOutResultSaveService bean = ApplicationContextHandle.getBean(SgBStoOutResultSaveService.class);
        return bean.saveSgStoOutResult(request);

    }

}
