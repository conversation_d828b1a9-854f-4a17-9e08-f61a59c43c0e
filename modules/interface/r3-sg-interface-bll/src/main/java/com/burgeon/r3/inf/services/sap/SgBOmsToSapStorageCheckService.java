package com.burgeon.r3.inf.services.sap;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.inf.mapper.SgBOmsToSapStorageCheckMapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.basic.mapper.SgBStorageChangeFtpMapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.common.SgCoreUtilsConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorage;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorageChangeFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.sap.SgBOmsToSapStorageCheck;
import com.burgeon.r3.sg.core.model.table.store.sap.SgSapStorage;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.DateUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.mapper.sap.SgSapStorageMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-08-30 13:33
 * @Description: OMS与WMS库存核对服务
 */
@Slf4j
@Component
@Deprecated
public class SgBOmsToSapStorageCheckService {

    @Autowired
    private SgBOmsToSapStorageCheckMapper checkMapper;

    @Autowired
    private SgBStorageChangeFtpMapper storageChangeFtpMapper;

    @Autowired
    private CpCStoreMapper storeMapper;

    @Autowired
    SgSapStorageMapper sgSapStorageMapper;

    private static Integer DAY = 365;

    /**
     * 系统参数 redis key
     */
    private final static String R3_SG_B_OMS_TO_SAP_STORAGE_CHECK_DAY = "business_system:r3_sg_b_oms_to_sap_storage_check_day";

    /**
     * OMS库存
     *
     * @return ValueHolderV14
     */
    public ValueHolderV14 saveOmsStorage() {

        log.info(LogUtil.format("OMS与SAP库存核对服务 查询OMS库存 start",
                "SgBOmsToSapStorageCheckService.saveOmsStorage"));
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "OMS与SAP库存核对新增oms库存成功！");
        try {

            Date pastDate = DateUtils.getPastDate(getDay(), new Date());
            int count = checkMapper.selectCount(new LambdaQueryWrapper<SgBOmsToSapStorageCheck>()
                    .lt(SgBOmsToSapStorageCheck::getStorageDate, pastDate));
            if (count > 0) {
                checkMapper.delete(new LambdaQueryWrapper<SgBOmsToSapStorageCheck>()
                        .lt(SgBOmsToSapStorageCheck::getStorageDate, pastDate));
            }

            Date yesterdayDate = DateUtils.getPastDate(1, new Date());
            log.info(LogUtil.format("系统日期前一天:{}", "pastDate"), yesterdayDate);
            //判断前一天的数据是否存在，存在删除
            int yesterday = checkMapper.selectCount(new LambdaQueryWrapper<SgBOmsToSapStorageCheck>()
                    .eq(SgBOmsToSapStorageCheck::getStorageDate, yesterdayDate));
            if (yesterday > 0) {
                checkMapper.delete(new LambdaQueryWrapper<SgBOmsToSapStorageCheck>()
                        .eq(SgBOmsToSapStorageCheck::getStorageDate, yesterdayDate));
            }

            batchInsertOmsStorage(yesterdayDate);
        } catch (Exception e) {
            log.error(LogUtil.format("SgBOmsToWmsStorageCheckService.error:{}",
                    "SgBOmsToSapStorageCheckService.error", Throwables.getStackTraceAsString(e)));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }

        log.info(LogUtil.format("OMS与SAP库存核对服务 查询OMS库存 end:{}",
                "SgBOmsToSapStorageCheckService.saveOmsStorage"), JSONObject.toJSONString(v14));
        return v14;
    }


    /**
     * 批量新增OMS库
     */
    private void batchInsertOmsStorage(Date pastDate) {

        //查逻辑仓信息
        List<SgCpCStore> sgCpStores = storeMapper.selectList(new QueryWrapper<SgCpCStore>().lambda()
                .eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y));


        log.info(LogUtil.format("逻辑仓数量:{}", "sgCpStores"),
                CollectionUtils.isNotEmpty(sgCpStores) ? sgCpStores.size() : 0);

        if (CollectionUtils.isNotEmpty(sgCpStores)) {

            for (SgCpCStore cpStore : sgCpStores) {
                try {

                    //前天的库存记录，当成昨天的期初
                    List<SgBOmsToSapStorageCheck> sgOmsToSapStorageChecks = checkMapper.selectByTwoDay(cpStore.getId());

                    log.info(LogUtil.format("期初数据:{},", "sgOmsToSapStorageChecks"),
                            CollectionUtils.isNotEmpty(sgOmsToSapStorageChecks) ? JSONObject.toJSONString(sgOmsToSapStorageChecks) : null);

                    if (CollectionUtils.isEmpty(sgOmsToSapStorageChecks)) {
                        List<SgBStorage> sgStorageList = CommonCacheValUtils.queryStorageByStoreId(cpStore.getId());
                        if (CollectionUtils.isNotEmpty(sgStorageList)) {
                            initStorage(sgStorageList, cpStore, pastDate);
                        }
                        continue;
                    }

                    //昨天的逻辑仓在库变动流水
                    List<SgBStorageChangeFtp> sgStorageChangeFtpList = storageChangeFtpMapper.selectList(new LambdaQueryWrapper<SgBStorageChangeFtp>()
                            .eq(SgBStorageChangeFtp::getChangeDate, pastDate)
                            .eq(SgBStorageChangeFtp::getCpCStoreId, cpStore.getId()));

                    log.info(LogUtil.format("期初:{},逻辑仓在库变动流水:{},逻辑仓冻结变动流水:{}", "sgStorageChangeFtpList"),
                            CollectionUtils.isNotEmpty(sgOmsToSapStorageChecks) ? sgOmsToSapStorageChecks.size() : 0,
                            CollectionUtils.isNotEmpty(sgStorageChangeFtpList) ? sgStorageChangeFtpList.size() : 0);

                    storageCheck(sgOmsToSapStorageChecks, sgStorageChangeFtpList, cpStore, pastDate);

                } catch (Exception e) {
                    log.error(LogUtil.format("SgBOmsToSAPStorageCheckService.error:{}",
                            "SgBOmsToSAPStorageCheckService.error", Throwables.getStackTraceAsString(e)));
                }
            }

        }
    }

    /**
     * 初始化期初
     *
     * @param sgStorageList 库存
     * @param cpStore       逻辑仓
     * @param pastDate      riq日期
     */
    private void initStorage(List<SgBStorage> sgStorageList, SgCpCStore cpStore, Date pastDate) {

        log.info(LogUtil.format("SgBOmsToSapStorageCheck库存期初size:{}", "initStorage"),
                CollectionUtils.isNotEmpty(sgStorageList) ? sgStorageList.size() : 0);

        List<SgBOmsToSapStorageCheck> resultList = new ArrayList<>();
        Map<String, List<SgBStorage>> storageMap = sgStorageList.stream().collect(Collectors.groupingBy(SgBStorage::getPsCSkuEcode));

        for (String key : storageMap.keySet()) {
            List<SgBStorage> sgStorages = storageMap.get(key);
            SgBStorage storage = sgStorages.get(0);

            BigDecimal totQtyStorage = sgStorages.stream().map(SgBStorage::getQtyStorage).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            SgBOmsToSapStorageCheck sgOmsToSapStorageCheck = new SgBOmsToSapStorageCheck();

            sgOmsToSapStorageCheck.setCpCStoreId(cpStore.getId());
            sgOmsToSapStorageCheck.setCpCStoreEcode(cpStore.getCpCStoreEcode());
            sgOmsToSapStorageCheck.setCpCStoreEname(cpStore.getCpCStoreEname());
            sgOmsToSapStorageCheck.setPsCSkuId(storage.getPsCSkuId());
            sgOmsToSapStorageCheck.setPsCSkuEcode(storage.getPsCSkuEcode());
            sgOmsToSapStorageCheck.setProduceDate(storage.getProduceDate());
            sgOmsToSapStorageCheck.setPsCProId(storage.getPsCProId());
            sgOmsToSapStorageCheck.setPsCProEcode(storage.getPsCProEcode());
            sgOmsToSapStorageCheck.setPsCProEname(storage.getPsCProEname());
            sgOmsToSapStorageCheck.setGbcode(storage.getGbcode());
            sgOmsToSapStorageCheck.setPsCBrandId(storage.getPsCBrandId());
            sgOmsToSapStorageCheck.setStorageType("ZP");

            sgOmsToSapStorageCheck.setStorageDate(pastDate);
            sgOmsToSapStorageCheck.setSapStotageQty(BigDecimal.ZERO);
            sgOmsToSapStorageCheck.setOmsStotageQty(totQtyStorage);
            sgOmsToSapStorageCheck.setDiffQty(sgOmsToSapStorageCheck.getSapStotageQty().subtract(sgOmsToSapStorageCheck.getOmsStotageQty()));
            sgOmsToSapStorageCheck.setResult(sgOmsToSapStorageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
            sgOmsToSapStorageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_SAP_STORAGE_CHECK));

            sgOmsToSapStorageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
            sgOmsToSapStorageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
            sgOmsToSapStorageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);
            StorageUtils.setBModelDefalutData(sgOmsToSapStorageCheck, SystemUserResource.getRootUser());
            resultList.add(sgOmsToSapStorageCheck);
        }

        log.info(LogUtil.format("SgBOmsToSapStorageCheck新增库存期初数据size:{}", "resultList"),
                CollectionUtils.isNotEmpty(resultList) ? resultList.size() : 0);

        if (CollectionUtils.isNotEmpty(resultList)) {
            List<List<SgBOmsToSapStorageCheck>> pageList = StorageUtils.getPageList(resultList, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
            for (List<SgBOmsToSapStorageCheck> page : pageList) {
                checkMapper.batchInsert(page);
            }
        }

    }

    /**
     * @param sgOmsToSapStorageChecks 前天的库存记录，当成昨天的期初
     * @param sgStorageChangeFtpList  昨天的逻辑仓在库变动流水
     * @param cpStore                 逻辑仓
     * @param pastDate                日期
     */
    private void storageCheck(List<SgBOmsToSapStorageCheck> sgOmsToSapStorageChecks,
                              List<SgBStorageChangeFtp> sgStorageChangeFtpList, SgCpCStore cpStore, Date pastDate) {

        Map<String, List<SgBStorageChangeFtp>> changeFtpMap = new HashMap<>(16);
        Map<String, List<SgBOmsToSapStorageCheck>> sgOmsToSapStorageCheckMap = new HashMap<>(16);

        if (CollectionUtils.isNotEmpty(sgStorageChangeFtpList)) {
            //条码分组
            changeFtpMap = sgStorageChangeFtpList.stream().collect(Collectors.groupingBy(SgBStorageChangeFtp::getPsCSkuEcode));
        }

        if (CollectionUtils.isNotEmpty(sgOmsToSapStorageChecks)) {
            //条码分组
            sgOmsToSapStorageCheckMap = sgOmsToSapStorageChecks.stream().collect(Collectors.groupingBy(SgBOmsToSapStorageCheck::getPsCSkuEcode));
        }

        List<SgBOmsToSapStorageCheck> insetCheck = checkChangeFtp(changeFtpMap, sgOmsToSapStorageCheckMap, cpStore, pastDate);

        log.info(LogUtil.format("新增库存流水数据size:{}", "insetCheck"),
                CollectionUtils.isNotEmpty(insetCheck) ? insetCheck.size() : 0);

        if (CollectionUtils.isNotEmpty(insetCheck)) {
            List<List<SgBOmsToSapStorageCheck>> pageList = StorageUtils.getPageList(insetCheck, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
            for (List<SgBOmsToSapStorageCheck> page : pageList) {
                checkMapper.batchInsert(page);
            }
        }
    }

    /**
     * 比对在库流水
     * ps:在库流水全部当成正品
     *
     * @param changeFtpMap              在库变动
     * @param sgOmsToSapStorageCheckMap 中间表
     * @param cpStore                   逻辑仓
     * @param pastDate                  日期
     * @return SgBOmsToWmsStorageCheck
     */
    private List<SgBOmsToSapStorageCheck> checkChangeFtp(Map<String, List<SgBStorageChangeFtp>> changeFtpMap,
                                                         Map<String, List<SgBOmsToSapStorageCheck>> sgOmsToSapStorageCheckMap,
                                                         SgCpCStore cpStore, Date pastDate) {

        List<SgBOmsToSapStorageCheck> resultList = new ArrayList<>();

        if (MapUtils.isNotEmpty(changeFtpMap)) {

            if (MapUtils.isNotEmpty(sgOmsToSapStorageCheckMap)) {
                List<String> checkMapList = new ArrayList<>(sgOmsToSapStorageCheckMap.keySet());
                List<String> changeFtpMapList = new ArrayList<>(changeFtpMap.keySet());

                List<String> mapKeyList = checkMapList.stream().filter(x -> !changeFtpMapList.contains(x)).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(mapKeyList)) {

                    mapKeyList.forEach(x -> {
                        SgBOmsToSapStorageCheck sgOmsToSapStorageCheck = new SgBOmsToSapStorageCheck();
                        List<SgBOmsToSapStorageCheck> sgOmsToSapStorageChecks = sgOmsToSapStorageCheckMap.get(x);
                        BigDecimal qtyChange = sgOmsToSapStorageChecks.stream().map(SgBOmsToSapStorageCheck::getOmsStotageQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                        BeanUtils.copyProperties(sgOmsToSapStorageChecks.get(0), sgOmsToSapStorageCheck);
                        sgOmsToSapStorageCheck.setStorageDate(pastDate);
                        sgOmsToSapStorageCheck.setStorageType(SgConstantsIF.TRADE_MARK_ZP);
                        sgOmsToSapStorageCheck.setSapStotageQty(BigDecimal.ZERO);
                        sgOmsToSapStorageCheck.setOmsStotageQty(qtyChange);
                        sgOmsToSapStorageCheck.setDiffQty(sgOmsToSapStorageCheck.getSapStotageQty().subtract(sgOmsToSapStorageCheck.getOmsStotageQty()));
                        sgOmsToSapStorageCheck.setResult(sgOmsToSapStorageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                        sgOmsToSapStorageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_SAP_STORAGE_CHECK));

                        sgOmsToSapStorageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
                        sgOmsToSapStorageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
                        sgOmsToSapStorageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);
                        StorageUtils.setBModelDefalutData(sgOmsToSapStorageCheck, SystemUserResource.getRootUser());
                        resultList.add(sgOmsToSapStorageCheck);
                    });
                }
            }

            for (String key : changeFtpMap.keySet()) {
                List<SgBStorageChangeFtp> sgStorageChangeFtpList = changeFtpMap.get(key);
                //变动量
                BigDecimal qtyChange = sgStorageChangeFtpList.stream().map(SgBStorageChangeFtp::getQtyChange).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                //条码
                SgBOmsToSapStorageCheck sgOmsToSapStorageCheck = new SgBOmsToSapStorageCheck();
                if (MapUtils.isNotEmpty(sgOmsToSapStorageCheckMap) &&
                        sgOmsToSapStorageCheckMap.containsKey(key)) {
                    List<SgBOmsToSapStorageCheck> sgOmsToSapStorageChecks = sgOmsToSapStorageCheckMap.get(key);
                    BeanUtils.copyProperties(sgOmsToSapStorageChecks.get(0), sgOmsToSapStorageCheck);
                    sgOmsToSapStorageCheck.setOmsStotageQty(sgOmsToSapStorageChecks.get(0).getOmsStotageQty());
                } else {
                    SgBStorageChangeFtp sgStorageChangeFtp = sgStorageChangeFtpList.get(0);
                    sgOmsToSapStorageCheck.setCpCStoreId(cpStore.getId());
                    sgOmsToSapStorageCheck.setCpCStoreEcode(cpStore.getCpCStoreEcode());
                    sgOmsToSapStorageCheck.setCpCStoreEname(cpStore.getCpCStoreEname());
                    sgOmsToSapStorageCheck.setPsCSkuId(sgStorageChangeFtp.getPsCSkuId());
                    sgOmsToSapStorageCheck.setPsCSkuEcode(sgStorageChangeFtp.getPsCSkuEcode());
                    sgOmsToSapStorageCheck.setProduceDate(sgStorageChangeFtp.getProduceDate());
                    sgOmsToSapStorageCheck.setPsCProId(sgStorageChangeFtp.getPsCProId());
                    sgOmsToSapStorageCheck.setPsCProEcode(sgStorageChangeFtp.getPsCProEcode());
                    sgOmsToSapStorageCheck.setPsCProEname(sgStorageChangeFtp.getPsCProEname());
                    sgOmsToSapStorageCheck.setGbcode(sgStorageChangeFtp.getGbcode());
                    sgOmsToSapStorageCheck.setPsCBrandId(sgStorageChangeFtp.getPsCBrandId());
                    sgOmsToSapStorageCheck.setOmsStotageQty(BigDecimal.ZERO);
                }
                sgOmsToSapStorageCheck.setStorageDate(pastDate);
                sgOmsToSapStorageCheck.setStorageType(SgConstantsIF.TRADE_MARK_ZP);
                sgOmsToSapStorageCheck.setSapStotageQty(BigDecimal.ZERO);
                sgOmsToSapStorageCheck.setOmsStotageQty(sgOmsToSapStorageCheck.getOmsStotageQty().add(qtyChange));
                sgOmsToSapStorageCheck.setDiffQty(sgOmsToSapStorageCheck.getSapStotageQty().subtract(sgOmsToSapStorageCheck.getOmsStotageQty()));
                sgOmsToSapStorageCheck.setResult(sgOmsToSapStorageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                sgOmsToSapStorageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_SAP_STORAGE_CHECK));

                sgOmsToSapStorageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
                sgOmsToSapStorageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
                sgOmsToSapStorageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);
                StorageUtils.setBModelDefalutData(sgOmsToSapStorageCheck, SystemUserResource.getRootUser());
                resultList.add(sgOmsToSapStorageCheck);
            }
        } else {
            if (MapUtils.isNotEmpty(sgOmsToSapStorageCheckMap)) {
                //拿出所有正品
                for (String key : sgOmsToSapStorageCheckMap.keySet()) {


                    SgBOmsToSapStorageCheck sgOmsToSapStorageCheck = new SgBOmsToSapStorageCheck();
                    List<SgBOmsToSapStorageCheck> sgOmsToSapStorageChecks = sgOmsToSapStorageCheckMap.get(key);
                    BigDecimal qtyChange = sgOmsToSapStorageChecks.stream().map(SgBOmsToSapStorageCheck::getOmsStotageQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                    BeanUtils.copyProperties(sgOmsToSapStorageChecks.get(0), sgOmsToSapStorageCheck);
                    sgOmsToSapStorageCheck.setStorageDate(pastDate);
                    sgOmsToSapStorageCheck.setStorageType(SgConstantsIF.TRADE_MARK_ZP);
                    sgOmsToSapStorageCheck.setSapStotageQty(BigDecimal.ZERO);
                    sgOmsToSapStorageCheck.setOmsStotageQty(qtyChange);
                    sgOmsToSapStorageCheck.setDiffQty(sgOmsToSapStorageCheck.getSapStotageQty().subtract(sgOmsToSapStorageCheck.getOmsStotageQty()));
                    sgOmsToSapStorageCheck.setResult(sgOmsToSapStorageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                    sgOmsToSapStorageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_SAP_STORAGE_CHECK));

                    sgOmsToSapStorageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
                    sgOmsToSapStorageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
                    sgOmsToSapStorageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);
                    StorageUtils.setBModelDefalutData(sgOmsToSapStorageCheck, SystemUserResource.getRootUser());
                    resultList.add(sgOmsToSapStorageCheck);

                }
            }
        }

        return resultList;
    }

    /**
     * sap 库存
     *
     * @return ValueHolderV14
     */
    public ValueHolderV14 sapStorageCheck() {
        log.info(LogUtil.format("OMS与SAP库存核对服务 查询SAP库存 start",
                "SgBOmsToSapStorageCheckService.sapStorageCheck"));
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "OMS与SAP库存核对成功！");
        try {

            Date pastDate = DateUtils.getPastDate(getDay(), new Date());
            Integer selectCount = sgSapStorageMapper.selectCount(new LambdaQueryWrapper<SgSapStorage>()
                    .lt(SgSapStorage::getStockDate, pastDate));
            if (selectCount > 0) {
                sgSapStorageMapper.delete(new LambdaQueryWrapper<SgSapStorage>()
                        .lt(SgSapStorage::getStockDate, pastDate));
            }

            batchInsertSapStorage();
        } catch (Exception e) {
            log.error(LogUtil.format("SgBOmsToSapStorageCheckService.sapStorageCheck.error:{}",
                    "SgBOmsToSapStorageCheckService.sapStorageCheck.error", Throwables.getStackTraceAsString(e)));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }

        log.info(LogUtil.format("OMS与SAP库存核对服务 查询SAP库存 end:{}",
                "SgBOmsToSapStorageCheckService.wmsStorageCheck"), JSONObject.toJSONString(v14));
        return v14;
    }

    /**
     * 批量新增sap库
     */
    private void batchInsertSapStorage() {
        //查逻辑仓信息
        List<SgCpCStore> sgCpStores = storeMapper.selectList(new QueryWrapper<SgCpCStore>().lambda()
                .eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y));


        log.info(LogUtil.format("逻辑仓数量:{}", "sgCpStores"),
                CollectionUtils.isNotEmpty(sgCpStores) ? sgCpStores.size() : 0);

        if (CollectionUtils.isNotEmpty(sgCpStores)) {


            for (SgCpCStore cpStore : sgCpStores) {
                try {
                    Date pastDate = DateUtils.getPastDate(1, new Date());

                    log.info(LogUtil.format("系统日期前一天:{}", "wms.pastDate"), pastDate);

                    List<SgSapStorage> sgSapStorageList = sgSapStorageMapper.selectList(new LambdaQueryWrapper<SgSapStorage>()
                            .eq(SgSapStorage::getStockDate, pastDate)
                            .eq(SgSapStorage::getCpCStoreId, cpStore.getId()));

                    List<SgBOmsToSapStorageCheck> sgOmsToSapStorageChecks = checkMapper.selectList(new LambdaQueryWrapper<SgBOmsToSapStorageCheck>()
                            .eq(SgBOmsToSapStorageCheck::getStorageDate, pastDate)
                            .eq(SgBOmsToSapStorageCheck::getCpCStoreId, cpStore.getId()));

                    log.info(LogUtil.format("SAP期初数据:{},OMS期初数据:{}", "sgSapToLsStorageList"),
                            CollectionUtils.isNotEmpty(sgSapStorageList) ? JSONObject.toJSONString(sgSapStorageList) : null,
                            CollectionUtils.isNotEmpty(sgOmsToSapStorageChecks) ? JSONObject.toJSONString(sgOmsToSapStorageChecks) : null);

                    if (CollectionUtils.isNotEmpty(sgSapStorageList)) {
                        checkWmsStorage(sgSapStorageList, sgOmsToSapStorageChecks, cpStore, pastDate);
                    }

                } catch (Exception e) {
                    log.error(LogUtil.format("SgBOmsToSapStorageCheckService.error:{}",
                            "SgBOmsToSapStorageCheckService.error", Throwables.getStackTraceAsString(e)));
                }
            }

        }
    }

    /**
     * 比较OMS库存跟SAP库存
     *
     * @param sgSapStorageList        SAP库存
     * @param sgOmsToSapStorageChecks OMS库存
     * @param cpStore                 实体仓
     * @param pastDate                日期
     */
    private void checkWmsStorage(List<SgSapStorage> sgSapStorageList, List<SgBOmsToSapStorageCheck> sgOmsToSapStorageChecks,
                                 SgCpCStore cpStore, Date pastDate) {
        //仓库编码+商品SKU
        Map<String, List<SgSapStorage>> sgSapStorageMap = sgSapStorageList.stream().collect(Collectors.groupingBy(SgSapStorage::getSkuEcode));

        //仓库编码+商品SKU
        Map<String, List<SgBOmsToSapStorageCheck>> storageCheckMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(sgOmsToSapStorageChecks)) {
            storageCheckMap = sgOmsToSapStorageChecks.stream().collect(Collectors.groupingBy(SgBOmsToSapStorageCheck::getPsCSkuEcode));
        }

        List<SgBOmsToSapStorageCheck> insertList = new ArrayList<>();
        List<SgBOmsToSapStorageCheck> updateList = new ArrayList<>();

        for (String key : sgSapStorageMap.keySet()) {
            SgSapStorage sgSapStorage = sgSapStorageMap.get(key).get(0);
            SgBOmsToSapStorageCheck storageCheck = new SgBOmsToSapStorageCheck();
            if (MapUtils.isNotEmpty(storageCheckMap) && storageCheckMap.containsKey(key)) {
                SgBOmsToSapStorageCheck sgOmsToSapStorageCheck = storageCheckMap.get(key).get(0);
                storageCheck.setId(sgOmsToSapStorageCheck.getId());
                storageCheck.setSapStotageQty(new BigDecimal(sgSapStorage.getQtyStorage()));
                storageCheck.setDiffQty(sgOmsToSapStorageCheck.getOmsStotageQty().subtract(storageCheck.getSapStotageQty()));
                storageCheck.setResult(storageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                StorageUtils.setBModelDefalutDataByUpdate(storageCheck, SystemUserResource.getRootUser());
                updateList.add(storageCheck);
            } else {
                storageCheck.setCpCStoreId(cpStore.getId());
                storageCheck.setCpCStoreEcode(cpStore.getCpCStoreEcode());
                storageCheck.setCpCStoreEname(cpStore.getCpCStoreEname());

//                storageCheck.setPsCSkuId(sgSapStorage.getPsCSkuId());
                storageCheck.setPsCSkuEcode(sgSapStorage.getSkuEcode());
//                storageCheck.setProduceDate(sgSapStorage.getProductDate());
                storageCheck.setStorageDate(pastDate);
//                storageCheck.setStorageType(sgSapStorage.getStorageType());
                storageCheck.setSapStotageQty(new BigDecimal(sgSapStorage.getQtyStorage()));
                storageCheck.setOmsStotageQty(BigDecimal.ZERO);
                storageCheck.setDiffQty(storageCheck.getSapStotageQty().subtract(storageCheck.getOmsStotageQty()));
                storageCheck.setResult(storageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                storageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_SAP_STORAGE_CHECK));

                storageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
                storageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
                storageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);
                StorageUtils.setBModelDefalutData(storageCheck, SystemUserResource.getRootUser());
                insertList.add(storageCheck);
            }
        }

        if (CollectionUtils.isNotEmpty(insertList)) {
            List<List<SgBOmsToSapStorageCheck>> pageList = StorageUtils.getPageList(insertList, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
            for (List<SgBOmsToSapStorageCheck> page : pageList) {
                checkMapper.batchInsert(page);
            }
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            for (SgBOmsToSapStorageCheck check : updateList) {
                checkMapper.updateById(check);
            }
        }

    }

    private Integer getDay() {
        try {
            String value = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(R3_SG_B_OMS_TO_SAP_STORAGE_CHECK_DAY);
            log.info(LogUtil.format("OMS与SAP库存核对服务 获取系统参数（OMS与SAP库存核对删除设置天数前数据）value：{}", "getDay"), value);
            if (StringUtils.isNotEmpty(value)) {
                return Integer.parseInt(value);
            }
            return DAY;
        } catch (Exception e) {
            log.info(LogUtil.format("OMS与SAP库存核对服务 获取系统参数（OMS与SAP库存核对删除设置天数前数据）异常：{}",
                    "getDay.error"), e.getMessage());
            return DAY;
        }
    }

    /**
     * 补偿接口
     *
     * @param pastDate  日期
     * @param storeCode 逻辑仓
     * @return ValueHolderV14
     */
    public ValueHolderV14 batchInsertSapStorage(Date pastDate, String storeCode) {

        log.info(LogUtil.format("OMS库存与SAP库存比对补偿接口:入参时间{}:入参仓:{}", "batchInsertSapStorage"), pastDate, storeCode);

        //相隔天数 正常要比当前日期少一天；
        int diffDays = DateUtils.getDiffDays(pastDate, new Date());
        if (diffDays <= 0) {
            return new ValueHolderV14(ResultCode.FAIL, "日期只能小于当前日期！");
        }

        for (int i = 0; i < diffDays; i++) {

            List<SgCpCStore> stores = new ArrayList<>();

            //是否指定仓
            if (StringUtils.isNotEmpty(storeCode)) {

                stores = storeMapper.selectList(new LambdaQueryWrapper<SgCpCStore>()
                        .eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y)
                        .eq(SgCpCStore::getCpCStoreEcode, storeCode));

            } else {

                stores = storeMapper.selectList(new QueryWrapper<SgCpCStore>().lambda()
                        .eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y));

            }

            log.info(LogUtil.format("逻辑仓数量:{}", "sgCpStores"),
                    CollectionUtils.isNotEmpty(stores) ? stores.size() : 0);

            if (CollectionUtils.isNotEmpty(stores)) {
                for (SgCpCStore store : stores) {
                    try {
                        ValueHolderV14 v14 = batchInsertOmsStorage(DateUtils.getNextDay(pastDate, i), store);
                        if (!v14.isOK()) {
                            AssertUtils.logAndThrow("batchInsertSapStorage.error:" + v14.getMessage());
                        }
                        checkSapStorage(DateUtils.getNextDay(pastDate, i), store);
                    } catch (Exception e) {
                        log.error(LogUtil.format("SgBOmsToSapStorageCheckService.batchInsertSapStorage.日期:{}.仓:{}.error:{}",
                                "SgBOmsToSapStorageCheckService.error"), pastDate, store.getCpCStoreEcode(), Throwables.getStackTraceAsString(e));
                    }

                }
            } else {
                return new ValueHolderV14(ResultCode.FAIL, "未查询到逻辑仓，请检查！");
            }
        }


        return new ValueHolderV14(ResultCode.SUCCESS, "补偿成功！");
    }

    /**
     * 重新核对oms库存
     *
     * @param pastDate pastDate
     * @param cpStore  仓
     * @return ValueHolderV14
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 batchInsertOmsStorage(Date pastDate, SgCpCStore cpStore) {

        try {

            //删除当前日期的记录
            int yesterday = checkMapper.selectCount(new LambdaQueryWrapper<SgBOmsToSapStorageCheck>()
                    .eq(SgBOmsToSapStorageCheck::getStorageDate, pastDate)
                    .eq(SgBOmsToSapStorageCheck::getCpCStoreEcode, cpStore.getCpCStoreEcode()));
            if (yesterday > 0) {
                checkMapper.delete(new LambdaQueryWrapper<SgBOmsToSapStorageCheck>()
                        .eq(SgBOmsToSapStorageCheck::getStorageDate, pastDate)
                        .eq(SgBOmsToSapStorageCheck::getCpCStoreEcode, cpStore.getCpCStoreEcode()));
            }

            //期初
            Date lastDate = DateUtils.getPastDate(1, pastDate);
            List<SgBOmsToSapStorageCheck> sgOmsToSapStorageChecks = checkMapper.selectList(new LambdaQueryWrapper<SgBOmsToSapStorageCheck>()
                    .eq(SgBOmsToSapStorageCheck::getStorageDate, lastDate)
                    .eq(SgBOmsToSapStorageCheck::getCpCStoreEcode, cpStore.getCpCStoreEcode()));

            log.info(LogUtil.format("期初数据:{},", "sgOmsToSapStorageChecks"),
                    CollectionUtils.isNotEmpty(sgOmsToSapStorageChecks) ? JSONObject.toJSONString(sgOmsToSapStorageChecks) : null);

            if (CollectionUtils.isEmpty(sgOmsToSapStorageChecks)) {
                initStorage(cpStore, pastDate);
                return new ValueHolderV14(ResultCode.SUCCESS, "初始化成功！");
            }

            //昨天的逻辑仓在库变动流水
            List<SgBStorageChangeFtp> sgStorageChangeFtpList = storageChangeFtpMapper.selectList(new LambdaQueryWrapper<SgBStorageChangeFtp>()
                    .eq(SgBStorageChangeFtp::getChangeDate, pastDate)
                    .eq(SgBStorageChangeFtp::getCpCStoreId, cpStore.getId()));

            log.info(LogUtil.format("期初:{},逻辑仓在库变动流水:{},逻辑仓冻结变动流水:{}", "sgStorageChangeFtpList"),
                    CollectionUtils.isNotEmpty(sgOmsToSapStorageChecks) ? sgOmsToSapStorageChecks.size() : 0,
                    CollectionUtils.isNotEmpty(sgStorageChangeFtpList) ? sgStorageChangeFtpList.size() : 0);

            storageCheck(sgOmsToSapStorageChecks, sgStorageChangeFtpList, cpStore, pastDate);

        } catch (Exception e) {
            log.error(LogUtil.format("SgBOmsToSAPStorageCheckService.batchInsertOmsStorage.日期:{}.仓:{}.error:{}",
                    "SgBOmsToWmsStorageCheckService.error"), pastDate, cpStore.getCpCStoreEcode(), Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrow("SgBOmsToSAPStorageCheckService.error:" + e.getMessage());
        }

        return new ValueHolderV14(ResultCode.SUCCESS, "初始化成功！");
    }

    /**
     * 重新审核
     * 初始化oms库存
     *
     * @param cpStore  生产批次
     * @param pastDate 仓
     */
    private void initStorage(SgCpCStore cpStore, Date pastDate) {

        List<SgBOmsToSapStorageCheck> resultList = new ArrayList<>();

        //逻辑仓在库变动流水 用id是因为有补的流水
        List<SgBStorageChangeFtp> sgStorageChangeFtpList = storageChangeFtpMapper.selectList(new LambdaQueryWrapper<SgBStorageChangeFtp>()
                .le(SgBStorageChangeFtp::getChangeDate, pastDate)
                .eq(SgBStorageChangeFtp::getCpCStoreId, cpStore.getId()));

        //初始化数据
        initStorageByPub(sgStorageChangeFtpList, pastDate, cpStore, resultList);

        log.info(LogUtil.format("重新审核init数据size:{}", "initStorageAndFreezeStorage"),
                CollectionUtils.isNotEmpty(resultList) ? resultList.size() : 0);

        if (CollectionUtils.isNotEmpty(resultList)) {
            List<List<SgBOmsToSapStorageCheck>> pageList = StorageUtils.getPageList(resultList, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
            for (List<SgBOmsToSapStorageCheck> page : pageList) {
                checkMapper.batchInsert(page);
            }
        }


    }

    /**
     * 初始化ooms库存，根据流水来
     *
     * @param sgStorageChangeFtpList 在库流水
     * @param pastDate               生产批次
     * @param cpStore                仓
     * @param resultList             insert
     */
    private void initStorageByPub(List<SgBStorageChangeFtp> sgStorageChangeFtpList, Date pastDate,
                                  SgCpCStore cpStore, List<SgBOmsToSapStorageCheck> resultList) {
        if (CollectionUtils.isNotEmpty(sgStorageChangeFtpList)) {
            //条码批次分组
            Map<String, List<SgBStorageChangeFtp>> changeFtp = sgStorageChangeFtpList.stream().collect(Collectors.groupingBy(x ->
                    x.getPsCSkuId() + SgConstantsIF.MAP_KEY_DIVIDER + x.getProduceDate() + SgConstantsIF.MAP_KEY_DIVIDER + x.getCpCStoreId()));

            for (String key : changeFtp.keySet()) {
                //求和，变动数量
                List<SgBStorageChangeFtp> storageChangeFtpList = changeFtp.get(key);

                BigDecimal totQtyChange = storageChangeFtpList.stream().map(SgBStorageChangeFtp::getQtyChange).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                //条码
                SgBOmsToSapStorageCheck sgOmsToSapStorageCheck = new SgBOmsToSapStorageCheck();
                SgBStorageChangeFtp sgStorageChangeFtp = storageChangeFtpList.get(0);
                sgOmsToSapStorageCheck.setCpCStoreId(cpStore.getId());
                sgOmsToSapStorageCheck.setCpCStoreEcode(cpStore.getCpCStoreEcode());
                sgOmsToSapStorageCheck.setCpCStoreEname(cpStore.getCpCStoreEname());
                sgOmsToSapStorageCheck.setPsCSkuId(sgStorageChangeFtp.getPsCSkuId());
                sgOmsToSapStorageCheck.setPsCSkuEcode(sgStorageChangeFtp.getPsCSkuEcode());
                sgOmsToSapStorageCheck.setProduceDate(sgStorageChangeFtp.getProduceDate());
                sgOmsToSapStorageCheck.setPsCProId(sgStorageChangeFtp.getPsCProId());
                sgOmsToSapStorageCheck.setPsCProEcode(sgStorageChangeFtp.getPsCProEcode());
                sgOmsToSapStorageCheck.setPsCProEname(sgStorageChangeFtp.getPsCProEname());
                sgOmsToSapStorageCheck.setGbcode(sgStorageChangeFtp.getGbcode());
                sgOmsToSapStorageCheck.setPsCBrandId(sgStorageChangeFtp.getPsCBrandId());
                sgOmsToSapStorageCheck.setOmsStotageQty(BigDecimal.ZERO);

                sgOmsToSapStorageCheck.setStorageDate(pastDate);
                sgOmsToSapStorageCheck.setStorageType(SgConstantsIF.TRADE_MARK_ZP);
                sgOmsToSapStorageCheck.setSapStotageQty(BigDecimal.ZERO);
                sgOmsToSapStorageCheck.setOmsStotageQty(totQtyChange);
                sgOmsToSapStorageCheck.setDiffQty(sgOmsToSapStorageCheck.getSapStotageQty().subtract(sgOmsToSapStorageCheck.getOmsStotageQty()));
                sgOmsToSapStorageCheck.setResult(sgOmsToSapStorageCheck.getDiffQty().compareTo(BigDecimal.ZERO) == 0 ? "库存一致" : "库存不一致");
                sgOmsToSapStorageCheck.setId(ModelUtil.getSequence(SgConstants.SG_B_OMS_TO_SAP_STORAGE_CHECK));

                sgOmsToSapStorageCheck.setAdClientId(SystemUserResource.AD_CLIENT_ID);
                sgOmsToSapStorageCheck.setAdOrgId(SystemUserResource.AD_ORG_ID);
                sgOmsToSapStorageCheck.setIsactive(SgCoreUtilsConstants.IS_ACTIVE_Y);
                StorageUtils.setBModelDefalutData(sgOmsToSapStorageCheck, SystemUserResource.getRootUser());
                resultList.add(sgOmsToSapStorageCheck);
            }

        }

    }

    /**
     * 重新比较库存
     *
     * @param pastDate pastDate
     * @param cpStore  cpStore
     */
    private void checkSapStorage(Date pastDate, SgCpCStore cpStore) {

        try {
            List<SgSapStorage> sgSapStorageList = sgSapStorageMapper.selectList(new LambdaQueryWrapper<SgSapStorage>()
                    .eq(SgSapStorage::getStockDate, pastDate)
                    .eq(SgSapStorage::getCpCStoreId, cpStore.getId()));

            List<SgBOmsToSapStorageCheck> sgOmsToSapStorageChecks = checkMapper.selectList(new LambdaQueryWrapper<SgBOmsToSapStorageCheck>()
                    .eq(SgBOmsToSapStorageCheck::getStorageDate, pastDate)
                    .eq(SgBOmsToSapStorageCheck::getCpCStoreId, cpStore.getId()));

            log.info(LogUtil.format("SAP期初数据:{},OMS期初数据:{}", "checkSapStorage"),
                    CollectionUtils.isNotEmpty(sgSapStorageList) ? JSONObject.toJSONString(sgSapStorageList) : null,
                    CollectionUtils.isNotEmpty(sgOmsToSapStorageChecks) ? JSONObject.toJSONString(sgOmsToSapStorageChecks) : null);

            if (CollectionUtils.isNotEmpty(sgSapStorageList)) {
                checkWmsStorage(sgSapStorageList, sgOmsToSapStorageChecks, cpStore, pastDate);
            }

        } catch (Exception e) {
            log.error(LogUtil.format("SgBOmsToSapStorageCheckService.checkSapStorage.error:{}",
                    "SgBOmsToSapStorageCheckService.checkSapStorage.error", Throwables.getStackTraceAsString(e)));
        }
    }

}
