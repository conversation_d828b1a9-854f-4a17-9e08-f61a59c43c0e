package com.burgeon.r3.inf.services.drp.in;

import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/9 22:01
 */
public class DrpUtils {
    
    public static User getUser() {
        UserImpl user = new UserImpl();
        user.setClientId(37);
        user.setOrgId(27);
        user.setId(100);
        user.setName("drp");
        user.setEname("drp");
        return user;
    }


}
