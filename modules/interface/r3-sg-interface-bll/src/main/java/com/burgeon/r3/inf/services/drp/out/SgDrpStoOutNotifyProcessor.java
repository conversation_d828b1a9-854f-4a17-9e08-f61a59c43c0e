package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransfer;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransferItem;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.services.transfer.SgTransferBillStatusEnum;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 调拨出库通知drp接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/23 14:34
 */
@Slf4j
@Component
public class SgDrpStoOutNotifyProcessor extends AbstractDrpInterfaceProcessor<SgBStoTransfer, SgBStoTransferItem> {

    @Autowired
    private CpCStoreMapper cpCStoreMapper;

    @Override
    public LambdaQueryWrapper<SgBStoTransfer> execMainWrapper() {


        LambdaQueryWrapper<SgBStoTransfer> outNotifyOrder = new LambdaQueryWrapper<SgBStoTransfer>()
                .ge(SgBStoTransfer::getStatus, SgTransferBillStatusEnum.AUDITED_ALL_OUT_NOT_IN.getVal())
                .le(SgBStoTransfer::getStatus, SgTransferBillStatusEnum.AUDITED_ALL_OUT_ALL_IN.getVal());
        outNotifyOrder.eq(SgBStoTransfer::getDrpBillType, SgStoreConstants.DRP_BILL_TYPE_TF);
        outNotifyOrder.eq(SgBStoTransfer::getDrpStatus,SgStoreConstants.SEND_DRP_STATUS_SUCCESS);
        outNotifyOrder.and(a -> {
            a.isNull(SgBStoTransfer::getDrpOutStatus);
            a.or(o -> o.eq(SgBStoTransfer::getDrpOutStatus, SgStoreConstants.SEND_DRP_STATUS_FAIL).lt(SgBStoTransfer::getDrpOutFailCount,
                    failNum));
            return a;
        });

        return outNotifyOrder;
    }

    @Override
    public LambdaQueryWrapper<SgBStoTransferItem> execitemWrapper(Long mainId) {
        return null;
    }

    @Override
    public String drpStatus() {
        return "DRP_OUT_STATUS";
    }

    @Override
    public String drpStatusFailCount() {
        return "DRP_OUT_FAIL_COUNT";
    }

    @Override
    public String drpStatusFailReason() {
        return "DRP_OUT_FAIL_REASON";
    }

    @Override
    public String drpInterfaceUrl() {
        return "erp.transferout.notice";
    }

    @Override
    public String itemMainField() {
        return "SG_B_STO_TRANSFER_ID";
    }

    @Override
    public JSONObject execInterfaceParam(SgBStoTransfer sgBStoTransfer, List<SgBStoTransferItem> z) {
        if (log.isDebugEnabled()) {
            log.debug("SgDrpStoOutNotifyProcessor.exec:transfer={},items={}", JSONObject.toJSONString(sgBStoTransfer), JSONObject.toJSONString(z));
        }
        JSONObject main = new JSONObject();
        //中台编号
        main.put("ZTDOCNO", sgBStoTransfer.getBillNo());
        //Erp单据编号
        main.put("DOCNO", sgBStoTransfer.getBillNo());
        //出库日期
        main.put("DATEOUT", DateUtil.format(sgBStoTransfer.getOutDate() == null ?
                new Date() : sgBStoTransfer.getOutDate(), "yyyyMMdd"));

        JSONArray items = new JSONArray();
        for (SgBStoTransferItem item : z) {
            JSONObject itemJson = new JSONObject();
            //条码
            itemJson.put("M_PRODUCTALIAS_NO", item.getPsCSkuEcode());
            //款号
            itemJson.put("M_PRODUCT_NAME", item.getPsCProEcode());
            //出库数量
            itemJson.put("QTYOUT", item.getQtyOut());
            items.add(itemJson);
        }

        main.put("items", items);
        log.info("SgDrpStoInNotifyProcessor.JSONObject:{}", main.toJSONString());
        return main;
    }

    @Override
    public void handleBysuccess(SgBStoTransfer sgBStoTransfer, List<SgBStoTransferItem> z) {

    }
}
