package com.burgeon.r3.inf.factory;

import com.burgeon.r3.inf.services.common.SgThreadPoolBuilder;
import com.burgeon.r3.sg.basic.config.ThreadPoolConfig;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Desc : oms 同步线程池
 * <AUTHOR> xiWen
 * @Date : 2020/8/14
 */
@Deprecated
public final class SgThreadPoolFactory {
    /**
     * 线程池改造，详见{@link ThreadPoolConfig}
     */

    /**
     * 获取线程
     */
    private static ThreadPoolExecutor auditOrderPool;
    private static final String TASK_AUDIT_ORDER_POOL_NAME = "R3_SG_B_WMS_TO_STO_OUT_RESULT_%d";

    public static ThreadPoolExecutor getAuditOrderPool() {
        return auditOrderPool == null
                ? (auditOrderPool = SgThreadPoolBuilder.build(TASK_AUDIT_ORDER_POOL_NAME, 200))
                : auditOrderPool;
    }

}
