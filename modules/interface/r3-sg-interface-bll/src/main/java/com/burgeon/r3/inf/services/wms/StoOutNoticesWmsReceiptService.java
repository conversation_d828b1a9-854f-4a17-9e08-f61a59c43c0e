package com.burgeon.r3.inf.services.wms;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.error.MqException;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.config.SgStorageMqConfig;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.inf.common.SgInfConstants;
import com.burgeon.r3.sg.inf.model.request.wms.out.SgPhyOutNoticesBillWMSBackRequest;
import com.burgeon.r3.sg.inf.model.result.wing.SgWingStoOutNoticesReceiptResult;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesMapper;
import com.burgeon.r3.sg.store.utils.SgNoticesToWmsCommonUtils;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 出库通知单WMS回执接口
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class StoOutNoticesWmsReceiptService {

    @Autowired
    private SgBStoOutNoticesMapper mapper;

    //    @Autowired
//    private R3MqSendHelper r3MqSendHelper;
    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private SgStorageMqConfig mqConfig;

    /**
     * 出库通知单WMS回执逻辑
     *
     * @param requestList 请求参数
     */
    public ValueHolderV14<List<SgWingStoOutNoticesReceiptResult>> execWmsReceipt(List<SgPhyOutNoticesBillWMSBackRequest> requestList) {

        ValueHolderV14<List<SgWingStoOutNoticesReceiptResult>> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "回执成功!");
        List<String> outNoticesBillNos =
                requestList.stream().map(SgPhyOutNoticesBillWMSBackRequest::getOrderNo).collect(Collectors.toList());
        //获取出库通知单map
        Map<String, SgBStoOutNotices> outNoticesHashMap = getStringSgStoOutNoticesMap(outNoticesBillNos);
        List<SgWingStoOutNoticesReceiptResult> results = new ArrayList<>();
        for (SgPhyOutNoticesBillWMSBackRequest request : requestList) {
            SgWingStoOutNoticesReceiptResult receiptResult = new SgWingStoOutNoticesReceiptResult();
            SgBStoOutNotices stoOutNotices = outNoticesHashMap.get(request.getOrderNo());
            if (stoOutNotices == null) {
                receiptResult.setBillNo(request.getOrderNo());
                receiptResult.setFailReason("对应出库通知单不存在！来源单据编号:" + request.getOrderNo());
                results.add(receiptResult);
            } else {
                //判断传wms成功状态  防止同一出库通知单wing重复配货请求
                if (stoOutNotices.getThirdPartyStatus() == SgStoreConstants.WMS_UPLOAD_STATUTS_SUCCESS) {
                    continue;
                }
                boolean isUpdateFlag = true;
                boolean isSendMQFlag = false;
                SgBStoOutNotices updateStoOutNotices = new SgBStoOutNotices();
                updateStoOutNotices.setId(stoOutNotices.getId());
                StorageUtils.setBModelDefalutDataByUpdate(updateStoOutNotices, SystemUserResource.getRootUser());
                if (Long.valueOf(SgStoreConstants.WMS_STATUS_PASSING).equals(stoOutNotices.getWmsStatus())) {
                    if (SgInfConstants.WMS_RECEIPT_STATUS_SUCCESS.equals(request.getCode())) {
                        isSendMQFlag = true;
                        // 回执状态成功
                        updateStoOutNotices.setWmsStatus(SgStoreConstants.WMS_UPLOAD_STATUTS_SUCCESS);
                        updateStoOutNotices.setWmsFailReason(Strings.EMPTY);
                        updateStoOutNotices.setWmsFailCount(0L);
                    } else if (SgInfConstants.WMS_RECEIPT_STATUS_FAIL.equals(request.getCode())) {
                        isSendMQFlag = true;
                        List<String> deliveryOrderFailMessage = SgNoticesToWmsCommonUtils.getDeliveryOrderFailMessage(stoOutNotices.getBillNo());
                        String message = request.getMessage();

                        log.info(LogUtil.format("MS失败原因:{},过滤失败原因:{}", "WMS失败原因"),
                                message, JSONObject.toJSONString(deliveryOrderFailMessage));
                        //匹配上当成功处理
                        if (deliveryOrderFailMessage.contains(message)) {
                            // 回执状态成功
                            updateStoOutNotices.setWmsStatus(SgStoreConstants.WMS_UPLOAD_STATUTS_SUCCESS);
                            updateStoOutNotices.setWmsFailReason(Strings.EMPTY);
                            updateStoOutNotices.setWmsFailCount(0L);

                        } else {
                            // 回执状态失败
                            updateStoOutNotices.setWmsFailReason(request.getMessage());
                            updateStoOutNotices.setWmsFailCount(Optional.ofNullable(stoOutNotices.getWmsFailCount()).orElse(0L) + 1);
                            updateStoOutNotices.setWmsStatus(SgStoreConstants.WMS_UPLOAD_STATUTS_FAIL);
                        }

                    } else {
                        receiptResult.setBillNo(request.getOrderNo());
                        receiptResult.setFailReason("回执状态参数不符合条件！回执状态:" + request.getCode());
                        results.add(receiptResult);
                        continue;
                    }
                } else {
                    isUpdateFlag = false;
                }

                //更新和发送mq
                StoOutNoticesWmsReceiptService receiptService =
                        ApplicationContextHandle.getBean(StoOutNoticesWmsReceiptService.class);
                receiptService.updateAndSend(request, stoOutNotices, updateStoOutNotices, results, isUpdateFlag
                        , isSendMQFlag);
            }
        }
        if (CollectionUtils.isNotEmpty(results)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("回执失败！");
            vh.setData(results);
        }
        if (log.isDebugEnabled()) {
            log.debug("Finsh WingStoOutNoticesWmsReceiptService.execWmsReceiptparam.vh={}:",
                    JSONObject.toJSONString(vh));
        }
        return vh;
    }

    /**
     * 根据单号集合获取出库通知单map
     *
     * @param outNoticesBillNos 出库通知单单号集合
     * @return Map<String, SgBStoOutNotices>
     */
    public Map<String, SgBStoOutNotices> getStringSgStoOutNoticesMap(List<String> outNoticesBillNos) {
        if (log.isDebugEnabled()) {
            log.debug("Start WingStoOutNoticesWmsReceiptService.getStringSgStoOutNoticesMap.outNoticesBillNos={}",
                    JSONObject.toJSONString(outNoticesBillNos));
        }
        List<SgBStoOutNotices> sgStoOutNotices = mapper.selectList(new LambdaQueryWrapper<SgBStoOutNotices>()
                .in(SgBStoOutNotices::getBillNo, outNoticesBillNos)
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));

        //map收集出库通知单
        Map<String, SgBStoOutNotices> outNoticesHashMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(sgStoOutNotices)) {
            sgStoOutNotices.forEach(x -> outNoticesHashMap.put(x.getBillNo(), x));
        }
        return outNoticesHashMap;
    }

    /**
     * 更新数据库 发送mq给订单中心
     *
     * @param request             请求参数
     * @param stoOutNotices       出库通知单
     * @param updateStoOutNotices 更新出库通知单
     * @param results             响应wing'results
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAndSend(SgPhyOutNoticesBillWMSBackRequest request, SgBStoOutNotices stoOutNotices
            , SgBStoOutNotices updateStoOutNotices, List<SgWingStoOutNoticesReceiptResult> results
            , boolean isUpdateFlag, boolean isSendMQFlag) {
        try {
            if (isUpdateFlag) {
                //更新出库通知单wms信息
                mapper.update(updateStoOutNotices, new LambdaUpdateWrapper<SgBStoOutNotices>()
                        .set(SgBStoOutNotices::getWmsFailReason, updateStoOutNotices.getWmsFailReason())
                        .eq(SgBStoOutNotices::getId, stoOutNotices.getId()));
            }
            if (isSendMQFlag) {
                //发送Mq【库存中心传wms回执】
                sendMsgToSourceBillNo(stoOutNotices, request);
            }
        } catch (Exception e) {
            log.error("WingStoOutNoticesWmsReceiptService.updateAndSend error:{}", Throwables.getStackTraceAsString(e));
            SgWingStoOutNoticesReceiptResult receiptResult = new SgWingStoOutNoticesReceiptResult();
            receiptResult.setBillNo(request.getOrderNo());
            receiptResult.setFailReason("更新数据发送mq失败,失败单号" + request.getOrderNo());
            results.add(receiptResult);
        }
    }

    /**
     * @param stoOutNotices
     * @param request
     */
    private void sendMsgToSourceBillNo(SgBStoOutNotices stoOutNotices,
                                       SgPhyOutNoticesBillWMSBackRequest request) {

        // 来源单据ID和来源单据类型并发送MQ消息给到【更新全渠道订单状态服务】...
        List<JSONObject> retailBodys = Lists.newArrayList();
        List<JSONObject> vipBodys = Lists.newArrayList();
        List<String> retailBillNos = Lists.newArrayList();
        List<String> vipBillNos = Lists.newArrayList();

        JSONObject body = new JSONObject();
        body.put("id", stoOutNotices.getId());
        body.put("orderId", stoOutNotices.getSourceBillId());
        body.put("orderNo", stoOutNotices.getSourceBillNo());
        body.put("noticesBillNo", stoOutNotices.getBillNo());

        // 回执结果 code:19成功 20失败
        body.put("code", request.getCode() == ResultCode.SUCCESS ? SgConstantsIF.ORDER_RESULT_CODE_WMS_SUCCESS :
                SgConstantsIF.ORDER_RESULT_CODE_WMS_FAIL);
        body.put("orderType", stoOutNotices.getSourceBillType());
        //失败原因
        body.put("flag", request.getMessage());
        body.put("outWingToWmsTime", new Date());
        body.put("outWmsReceiveTime", new Date());

        if (SgConstantsIF.BILL_TYPE_RETAIL == stoOutNotices.getSourceBillType()) {
            retailBodys.add(body);
            retailBillNos.add(stoOutNotices.getBillNo());
        } else if (SgConstantsIF.BILL_SHARE_DISTRIBUTION == stoOutNotices.getSourceBillType()) {
            vipBodys.add(body);
            vipBillNos.add(stoOutNotices.getBillNo());
        }

        if (CollectionUtils.isNotEmpty(retailBodys)) {

            JSONObject ret = new JSONObject();
            ret.put("type", SgConstants.SECOND_TO_PASS_TYPE);
            ret.put("body", retailBodys);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format(" 零售回执消息体 :{} 通知单:{};", "零售回执消息体"),
                        ret.toJSONString(), retailBillNos);
            }

            try {
//                r3MqSendHelper.sendMessage(mqConfig.getSgDefaultConfigName(),
//                        ret.toJSONString(),
//                        mqConfig.getNoticesToOmsTopic(),
//                        SgConstantsIF.MSG_TAG_OUTNOTICES_TO_OMS,
//                        UUID.randomUUID().toString().replaceAll("-", ""));
                defaultProducerSend.sendTopic(mqConfig.getMq5callbackToOms(),
                        SgConstantsIF.MSG_TAG_OUTNOTICES_TO_OMS,
                        ret.toJSONString(),
                        UUID.randomUUID().toString().replaceAll("-", ""));

            } catch (MqException e) {
                log.error("{}.sendMsgToSourceBillNo. exception_has_occured:{}", this.getClass().getName(),
                        Throwables.getStackTraceAsString(e));
            }

        }

        if (CollectionUtils.isNotEmpty(vipBodys)) {

            JSONObject ret = new JSONObject();
            ret.put("body", vipBodys);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format(" 唯品会回执消息体 :{} 通知单:{};", "唯品会回执消息体"),
                        ret.toJSONString(), vipBodys);
            }

            try {
//                r3MqSendHelper.sendMessage(mqConfig.getSgDefaultConfigName(),
//                        ret.toJSONString(),
//                        mqConfig.getNoticesToVipTopic(),
//                        SgConstantsIF.MSG_TAG_OUTNOTICES_TO_VIP,
//                        UUID.randomUUID().toString().replaceAll("-", ""));
                defaultProducerSend.sendTopic(mqConfig.getNoticesToVipTopic(),
                        SgConstantsIF.MSG_TAG_OUTNOTICES_TO_VIP,
                        ret.toJSONString(),
                        UUID.randomUUID().toString().replaceAll("-", ""));

            } catch (MqException e) {
                log.error("{}.sendMsgToSourceBillNo. exception_has_occured:{}", this.getClass().getName(),
                        Throwables.getStackTraceAsString(e));
            }
        }
    }
}
