package com.burgeon.r3.inf.services.tms;

import com.burgeon.r3.sg.store.services.tms.in.SgTmsLogisticsCallBackService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/21 14:56
 */
@Slf4j
@Component
public class SgInfTmsLogisticsCallBackService {

    @Autowired
    private SgTmsLogisticsCallBackService callBackService;

    /**
     * 物流订单回执处理
     *
     * @param param tms调用的xml参数
     * @return return
     */
    public ValueHolderV14 orderCallBack(String param) {
        ValueHolderV14<String> response = new ValueHolderV14<>();
        response.setCode(ResultCode.SUCCESS);
        response.setMessage("请求回执成功！");
        try {
            response = callBackService.orderCallBack(param);
        } catch (Exception e) {
            log.error("SgInfTmsLogisticsCallBackService.orderCallBack:回执处理出现异常！ error:{}", Throwables.getStackTraceAsString(e));
            response.setCode(ResultCode.FAIL);
            response.setMessage("请求参数转换异常!");
        }
        return response;
    }
}
