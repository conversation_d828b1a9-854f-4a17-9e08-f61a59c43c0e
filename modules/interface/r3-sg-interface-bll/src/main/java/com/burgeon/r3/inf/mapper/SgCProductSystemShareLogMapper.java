package com.burgeon.r3.inf.mapper;

import com.burgeon.r3.sg.inf.model.dto.SgCSelectionGenericDto;
import com.burgeon.r3.sg.inf.model.request.ourui.ShareLogQueryParam;
import com.burgeon.r3.sg.inf.model.table.SgCProductSystemShareLog;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@Mapper
public interface SgCProductSystemShareLogMapper extends ExtentionMapper<SgCProductSystemShareLog> {

    /**
     * 删除三天前的数据
     *
     * @param nowDate 当前时间
     */
    @Delete("delete from sg_c_product_system_share_log where datediff(#{nowDate} , bill_date)>=3 ")
    void deleteBeforeBillDate(@Param("nowDate") Date nowDate);


    @Select({"<script>" +
            "SELECT" +
            " * " +
            "FROM" +
            " sg_c_product_system_share_log l " +
            "<where> " +
            " l.isactive = 'Y' and (l.sync_status = 0 or l.sync_status = 3)" +
            "<if test = 'queryParams != null and queryParams.size > 0'>" +
            " and (l.sg_c_share_store_id,l.ps_c_sku_id ) in" +
            " <foreach item='item' collection='queryParams' separator=',' open='(' close=')'> (#{item.shareStoreId},#{item.psCSkuId})</foreach>" +
            "</if>" +
            "<if test = 'startDate != null'>" +
            " and l.creationdate &gt;= #{startDate}" +
            "</if>" +
            "<if test = 'endDate != null'>" +
            " and l.creationdate &lt;= #{endDate}" +
            "</if>" +
            "</where>" +
            "</script>"})
    List<SgCProductSystemShareLog> queryListByCondition(@Param("queryParams") List<ShareLogQueryParam> queryParams, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    //获取当天的记录
    @Select("select * from sg_c_product_system_share_log where bill_date  = DATE_FORMAT( NOW( ), '%Y-%m-%d' )")
    List<SgCProductSystemShareLog> selectListByDate();

    //查询线下自营仓选款
    @Select("select * from sg_c_offline_warehouse_selection")
    List<SgCSelectionGenericDto> selectListByOfflineWarehouse();

    //查询线下自营门店选款
    @Select("select * from sg_c_own_store_selection")
    List<SgCSelectionGenericDto> selectListByOwnStore();

    //查询SmartStore门店选款
    @Select("select * from sg_c_smart_store_selection")
    List<SgCSelectionGenericDto> selectListBySmartStore();
}