package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransfer;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransferItem;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.transfer.SgBStoTransferMapper;
import com.burgeon.r3.sg.store.services.transfer.SgTransferBillStatusEnum;
import com.jackrain.nea.cpext.model.table.CpCTranwayAssign;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * 销售通知接口
 * @date 2021/6/24 21:23
 */
@Slf4j
@Component
public class DrpSaleProcessor extends AbstractDrpInterfaceProcessor<SgBStoTransfer, SgBStoTransferItem> {

    @Autowired
    SgBStoTransferMapper sgStoTransferMapper;


    @Override
    public LambdaQueryWrapper execMainWrapper() {
        LambdaQueryWrapper<SgBStoTransfer> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(SgBStoTransfer::getStatus, SgTransferBillStatusEnum.AUDITED_NOT_OUT.getVal());
        wrapper.ne(SgBStoTransfer::getStatus, SgTransferBillStatusEnum.VOIDED.getVal());
        wrapper.eq(SgBStoTransfer::getIsactive, SgConstants.IS_ACTIVE_Y);
        wrapper.eq(SgBStoTransfer::getDrpBillType, SgStoreConstants.DRP_BILL_TYPE_SA);
        wrapper.and(o -> {
            o.isNull(SgBStoTransfer::getDrpStatus);
            o.or(oo -> oo.eq(SgBStoTransfer::getDrpStatus, SgStoreConstants.SEND_DRP_STATUS_UNDECLARED));
            o.or(oo -> oo.eq(SgBStoTransfer::getDrpStatus, SgStoreConstants.SEND_DRP_STATUS_FAIL).lt(SgBStoTransfer::getDrpFailCount, failNum));
            return o;
        });
        return wrapper;
    }

    @Override
    public LambdaQueryWrapper<SgBStoTransferItem> execitemWrapper(Long mainId) {
        return null;
    }

    @Override
    public String drpInterfaceUrl() {
        return "erp.sale.notice";
    }

    @Override
    public String itemMainField() {
        return "sg_b_sto_transfer_id";
    }


    @Override
    public String drpStatus() {
        return "DRP_STATUS";
    }

    @Override
    public String drpStatusFailCount() {
        return "DRP_FAIL_COUNT";
    }

    @Override
    public String drpStatusFailReason() {
        return "DRP_FAIL_REASON";
    }

    @Override
    public JSONObject execInterfaceParam(SgBStoTransfer sgStoTransfer, List<SgBStoTransferItem> itemList) {
        JSONObject mian = new JSONObject();
        mian.put("SALETYPE", "NOR");
        mian.put("BILLDATE", DateUtil.format(sgStoTransfer.getBillDate(), "yyyyMMdd"));
        mian.put("ZTDOCNO", sgStoTransfer.getBillNo());
        mian.put("C_STORE_CODE", sgStoTransfer.getSenderStoreEcode());
        mian.put("C_DEST_CODE", sgStoTransfer.getReceiverStoreEcode());
        mian.put("IS_TMS", "N");
        mian.put("IS_BAS", "N");
        mian.put("ORDER_TYPE", SgDrpConstantsIF.profitBillType.get("2"));
        mian.put("DEMANDTYPE", "5");
        if (sgStoTransfer.getCpCTranwayAssignId() != null) {
            CpCTranwayAssign cpTranwayAssign = CommonCacheValUtils.getCpCTranwayAssignpById(sgStoTransfer.getCpCTranwayAssignId());
            mian.put("TRANWAY_NAME", cpTranwayAssign.getEname());
        } else {
            mian.put("TRANWAY_NAME", "物流");
        }

        // 20220310 新增字段
        mian.put("QTY_BOX", sgStoTransfer.getBox());
        mian.put("BUNDLE", sgStoTransfer.getBundle());
        mian.put("DESCRIPTION", sgStoTransfer.getRemark());
        List<JSONObject> items = new ArrayList<>();
        for (SgBStoTransferItem item : itemList) {
            JSONObject itemJson = new JSONObject();
            itemJson.put("M_PRODUCTALIAS_NO", item.getPsCSkuEcode());
            itemJson.put("M_PRODUCT_NAME", item.getPsCProEcode());
            itemJson.put("QTY", item.getQty());
            items.add(itemJson);
        }
        mian.put("items", items);
        return mian;
    }

    @Override
    public void handleBysuccess(SgBStoTransfer sgStoTransfer, List<SgBStoTransferItem> z) {

    }


}
