package com.burgeon.r3.inf.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.r3.inf.handler.CallBackApi;
import com.burgeon.r3.sg.core.utils.ApplicationContextProvider;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @time 2019/5/6 10:22
 * @descript 奇门回传
 * 认养没有配置这个
 */
@Slf4j
@Component
@Deprecated
//@RocketMqMessageListener(name = "WMSBackMessageRouteListener", type = MqTypeEnum.DEFAULT)
public class WMSBackMessageRouteListener implements BaseMessageListener {

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        String method;

        try {
            if (log.isInfoEnabled()) {
                log.info(LogUtil.format("接收wms回传mq消息 messageBody：{},messageKey：{},messageTag：{},messageTopic：{}",
                        "接收wms回传mq消息"), messageBody, messageKey, messageTag, messageTopic);
            }
            JSONObject result = JSONObject.parseObject(messageBody);
            method = (String) result.get("method");
            CallBackApi callBackApi = ApplicationContextProvider.getBean(method, CallBackApi.class);
            if (StringUtils.isEmpty(callBackApi)) {
                log.warn(LogUtil.format("服务暂未开放,methed.parameter-error,method:{}"), method);
                return;
            }
            ValueHolderV14 valueHolderV14 = callBackApi.apiProcess(messageBody);
            if (valueHolderV14.isOK()) {
                return;
            } else {
                log.error(LogUtil.format("接收wms回传mq消息失败,result:{},method：{}"), JSON.toJSONString(valueHolderV14), method);
                throw new NDSException(valueHolderV14.getMessage());
            }
        } catch (Exception e) {
            log.error(LogUtil.format("WMSBackMessageRouteListener.consume.error：{}"), Throwables.getStackTraceAsString(e));
            throw e;
        }
    }
}
