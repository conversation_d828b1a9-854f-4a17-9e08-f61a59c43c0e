package com.burgeon.r3.inf.handler;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.out.SgBWmsToStoOutResult;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.inf.common.SgMQConstants;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.out.SgBWmsToStoOutResultMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * @author: 秦雄飞
 * @time: 2022/1/4 3:10 下午
 * @description: B2B出库回传
 */
@Slf4j
@Service(SgMQConstants.WMS_BACK_STOCK_OUT_METHOD)
public class WMSBackStockOutOrderHandler implements CallBackApi {

    @Autowired
    private SgBWmsToStoOutResultMapper sgBWmsToStoOutResultMapper;

    @Override
    public ValueHolderV14 apiProcess(String messageBody) {
        CusRedisTemplate<Object, Object> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();
        String lockKsy = SgConstants.SG_BILL_LOCK_WMSRETURN;

        try {
            JSONObject request = JSONObject.parseObject(messageBody).getJSONObject("request");
            JSONObject deliveryOrderObject = request.getJSONObject("deliveryOrder");
            // WMS单据编号
            String wmsBillCode = deliveryOrderObject.getString("deliveryOrderId");
            // 出库通知单号
            String noticesBillNo = deliveryOrderObject.getString("deliveryOrderCode");

            lockKsy += noticesBillNo;

            Boolean ifAbsent = redisMasterTemplate.opsForValue().setIfAbsent(lockKsy, "OK");

            List<SgBWmsToStoOutResult> wmsToStoOutResults = sgBWmsToStoOutResultMapper.selectByWmsBillCode(wmsBillCode);

            if (CollectionUtils.isNotEmpty(wmsToStoOutResults) || ifAbsent == null || !ifAbsent) {
                return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("WMS单据编号已存在"));
            } else {
                redisMasterTemplate.expire(lockKsy, 30, TimeUnit.SECONDS);

                SgBWmsToStoOutResult wmsToStoOutResult = new SgBWmsToStoOutResult();
                wmsToStoOutResult.setId(ModelUtil.getSequence(SgConstants.SG_B_WMS_TO_STO_OUT_RESULT));
                wmsToStoOutResult.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT);
                wmsToStoOutResult.setBillType(SgStoreConstantsIF.OUT_TYPE_ELECTRICITY);
                wmsToStoOutResult.setMessage(messageBody);
                wmsToStoOutResult.setNoticesBillNo(noticesBillNo);
                wmsToStoOutResult.setFailedCount(0);
                wmsToStoOutResult.setWmsBillCode(wmsBillCode);
                wmsToStoOutResult.setIsactive(SgConstants.IS_ACTIVE_Y);
                StorageUtils.setBModelDefalutData(wmsToStoOutResult, R3SystemUserResource.getSystemRootUser());
                sgBWmsToStoOutResultMapper.insert(wmsToStoOutResult);
            }
        } catch (Exception e) {
            log.error("出库通知单WMS回传异常:{}", Throwables.getStackTraceAsString(e));
            throw e;
        } finally {
            redisMasterTemplate.delete(lockKsy);
        }
        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("审核成功！"));
    }
}