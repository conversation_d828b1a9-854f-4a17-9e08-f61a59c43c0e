package com.burgeon.r3.inf.services.wing.in;

import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.inf.model.request.wing.in.SgWingJitxVoucherSelectRequest;
import com.burgeon.r3.sg.inf.model.result.wing.in.SgWingJitxVoucherSelectResult;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesMapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2021/10/28 16:17
 * <p>
 * 面单获取接口
 */
@Slf4j
@Component
public class SgWingJitxVoucherSelectService {


    @Autowired
    private SgBStoOutNoticesMapper mapper;

    public ValueHolderV14<SgWingJitxVoucherSelectResult> selectJitxVoucher(SgWingJitxVoucherSelectRequest request) {
        ValueHolderV14<SgWingJitxVoucherSelectResult> v14 =
                new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        String billNo = request.getBillNo();
        if (StringUtils.isEmpty(billNo)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("出库通知单单据编号不能为空");
            return v14;
        }
        //根据出库通知单 单据编号 查询到对应jitx面单内容
        SgBStoOutNotices sgBStoOutNotices = mapper.selectByBillNo(billNo);
        if (sgBStoOutNotices == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("未查询出库通知单");
            return v14;
        }
        String jitxVoucherContent = sgBStoOutNotices.getJitxVoucherContent();
        String logisticNumber = sgBStoOutNotices.getLogisticNumber();
        String cpCLogisticsEname = sgBStoOutNotices.getCpCLogisticsEname();
        if (StringUtils.isEmpty(jitxVoucherContent)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("当前出库通知单jitx面单内容为空");
            return v14;
        }
        if (StringUtils.isEmpty(logisticNumber)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("当前出库通知单物流单号为空");
            return v14;
        }
        if (StringUtils.isEmpty(cpCLogisticsEname)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("当前出库通知单物流公司为空");
            return v14;
        }
        SgWingJitxVoucherSelectResult result = new SgWingJitxVoucherSelectResult();
        result.setJitxVoucherContent(jitxVoucherContent);
        result.setLogisticCode(cpCLogisticsEname);
        result.setLogisticNumber(logisticNumber);
        v14.setData(result);
        return v14;
    }
}
