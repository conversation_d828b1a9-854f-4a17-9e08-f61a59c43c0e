package com.burgeon.r3.inf.services.drp.in;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.inf.config.SgDrpConfig;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.store.adjust.SgBStoAdjust;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.inf.model.request.drp.adjust.SgDrpStoAdjustSaveRequest;
import com.burgeon.r3.sg.inf.model.result.drp.in.SgDrpStoAdjustSaveResult;
import com.burgeon.r3.sg.store.mapper.adjust.SgBStoAdjustMapper;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustMainSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustSaveRequest;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSaveService;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSubmitService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * 库存调整单
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SgDrpStoAdjustSaveService {

    @Autowired
    private SgBStoAdjustSaveService saveService;

    @Autowired
    private SgBStoAdjustSubmitService submitService;

    @Autowired
    private SgDrpConfig sgDrpConfig;
    @Autowired
    private SgBStoAdjustMapper sgStoAdjustMapper;

    /**
     * 保存接口
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgDrpStoAdjustSaveResult> save(SgDrpStoAdjustSaveRequest request) {
        if (!sgDrpConfig.getEnableDrpSend()) {
            return new ValueHolderV14<>(ResultCode.FAIL, "中台系统未开放,请稍后再试!");
        }

        log.info("Start SgDrpStoAdjustSaveService.save param={}", JSONObject.toJSONString(request));

        String lockKsy =
                SgConstants.INTERFACE_DRP + ":" + SgConstants.SG_B_STO_ADJUST + ":" + request.getSourceBillNo();
        SgRedisLockUtils.lock(lockKsy);
        ValueHolderV14<SgDrpStoAdjustSaveResult> v14 =
                new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        try {
            int count = sgStoAdjustMapper.selectCount(new LambdaQueryWrapper<SgBStoAdjust>()
                    .eq(SgBStoAdjust::getBillNo, request.getBillNo())
                    .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (count > 0) {
                return new ValueHolderV14<>(ResultCode.SUCCESS, "当前单据中台已存在,请勿重复请求!");
            }
            // 主表处理
            SgBStoAdjustSaveRequest sgRequest = new SgBStoAdjustSaveRequest();
            SgBStoAdjustMainSaveRequest mainRequest = new SgBStoAdjustMainSaveRequest();
            List<SgBStoAdjustItemSaveRequest> itemList = new ArrayList<>();
            mainRequest.setRemark(request.getRemark());
            mainRequest.setCpCStoreEcode(request.getCpCStoreEcode());
            mainRequest.setSgBAdjustPropId(request.getSgBAdjustPropId());
            mainRequest.setSourceBillType(request.getSourceBillType());
            mainRequest.setSourceBillId(request.getSourceBillId());
            mainRequest.setSourceBillNo(request.getBillNo());
            mainRequest.setBillDate(DateUtil.stringToDate(request.getBillDate()));
            mainRequest.setBillType(request.getBillType());
            mainRequest.setBillNo(request.getBillNo());
            mainRequest.setDrpStatus(2);
            // 明细处理
            if (CollectionUtils.isEmpty(request.getItems())) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("库存调整单保存失败,明细信息不能为空!");
                return v14;
            }
            request.getItems().forEach(item -> {
                SgBStoAdjustItemSaveRequest itemRequest = new SgBStoAdjustItemSaveRequest();
                itemRequest.setSourceBillItemId(item.getSourceBillItemId());

                itemRequest.setQty(item.getQty());
                itemRequest.setPsCSkuEcode(item.getPsCSkuEcode());
                itemRequest.setId(-1L);
                itemList.add(itemRequest);
            });
            // sgRequest 赋值相关字段
            sgRequest.setMainRequest(mainRequest);
            sgRequest.setItems(itemList);
            sgRequest.setLoginUser(DrpUtils.getUser());
            // 执行具体逻辑
            ValueHolderV14<SgR3BaseResult> result = saveService.save(sgRequest);
            if (result.isOK()) {
                SgR3BaseRequest submitRequest = new SgR3BaseRequest();
                submitRequest.setBillNo(result.getData().getBillNo());
                submitRequest.setLoginUser(DrpUtils.getUser());
                submitService.submit(submitRequest);
                SgDrpStoAdjustSaveResult omsStoInResult = new SgDrpStoAdjustSaveResult();
                omsStoInResult.setBillNo(result.getData().getBillNo());
                v14.setData(omsStoInResult);
            }
        } catch (Exception e) {
            log.error("SgDrpStoAdjustSaveService.save. error:{}", Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrowException("库存调整单保存服务异常！", e, Locale.getDefault());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        return v14;
    }
}
