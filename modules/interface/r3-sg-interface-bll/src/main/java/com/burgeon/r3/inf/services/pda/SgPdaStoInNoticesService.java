package com.burgeon.r3.inf.services.pda;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoIn;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInNotices;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInNoticesItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.inf.model.request.pda.SgStoInNoticesItemsPdaRequest;
import com.burgeon.r3.sg.inf.model.request.pda.SgStoInNoticesItemsSavePdaRequest;
import com.burgeon.r3.sg.inf.model.request.pda.SgStoInNoticesMainPdaRequest;
import com.burgeon.r3.sg.inf.model.request.pda.SgStoInNoticesSavePdaRequest;
import com.burgeon.r3.sg.inf.model.result.pda.SgStoInNoticesItemsPdaResult;
import com.burgeon.r3.sg.inf.model.result.pda.SgStoInNoticesMainPdaResult;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInMapper;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInNoticesItemMapper;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInNoticesMapper;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultSaveRequest;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInResultBillSaveResult;
import com.burgeon.r3.sg.store.services.in.SgBStoInResultSaveService;
import com.burgeon.r3.sg.store.services.in.SgBStoInResultSubmitService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.table.CpCustomer;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 入库通知单信息查询，保存，审核
 * <AUTHOR>
 * @Date 2021/8/11 15:59
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgPdaStoInNoticesService {


    @Autowired
    private SgBStoInNoticesMapper sgBStoInNoticesMapper;

    @Autowired
    private SgBStoInNoticesItemMapper sgBStoInNoticesItemMapper;

    @Autowired
    private SgBStoInResultSubmitService stoInResultSubmitService;

    @Autowired
    private SgBStoInMapper sgBStoInMapper;


    /**
     * 查询出来通知单主表信息
     *
     * @param request
     * @return
     */
    public ValueHolderV14<PageInfo<SgStoInNoticesMainPdaResult>> querySgBStoInNoticesMain(SgStoInNoticesMainPdaRequest request, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgPdaStoInNoticesService.querySgBStoInNoticesMain. ReceiveParams:" +
                    "request:{};", JSONObject.toJSONString(request));
        }
        ValueHolderV14 holder = new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        Date billDateBegin = request.getBillDateBegin();
        Date billDateEnd = request.getBillDateEnd();
        List<String> billStatusList = request.getBillStatus();
        String billNo = request.getBillNo();
        List<Long> sgBStoInNoticesId = request.getSgBStoInNoticesId();
        List<SgStoInNoticesMainPdaResult> resultList = new ArrayList<>();
        List<SgBStoInNotices> sgBStoInNoticesList = null;
        try {

            if (request.getPageSize() != null && request.getPageNum() != null) {
                /** 分页查询 **/
                PageHelper.startPage(request.getPageNum(), request.getPageSize());
            }
            //全量查询

            sgBStoInNoticesList = sgBStoInNoticesMapper.selectList(new LambdaQueryWrapper<SgBStoInNotices>()
                    .in(SgBStoInNotices::getBillStatus, billStatusList)
                    .in(!CollectionUtils.isEmpty(sgBStoInNoticesId), SgBStoInNotices::getId, sgBStoInNoticesId)
                    .like(StringUtils.isNotEmpty(billNo), SgBStoInNotices::getBillNo, billNo)
                    .ge(billDateBegin != null, SgBStoInNotices::getBillDate, billDateBegin)
                    .le(billDateEnd != null, SgBStoInNotices::getBillDate, billDateEnd));

            if (CollectionUtils.isNotEmpty(sgBStoInNoticesList)) {
                sgBStoInNoticesList.forEach(item -> {
                    SgStoInNoticesMainPdaResult pdaItem = new SgStoInNoticesMainPdaResult();
                    BeanUtils.copyProperties(item, pdaItem);
                    pdaItem.setSgBStoInNoticesId(item.getId());
                    pdaItem.setBillStatus(item.getBillStatus().toString());
                    pdaItem.setSourceBillType("调拨单");
                    // : 发货经销商名称还未赋值
                    CpCustomer cpCustomerById = CommonCacheValUtils.getCpCustomerById(item.getCpCCustomerId());
                    if (log.isDebugEnabled()) {
                        log.debug("SgPdaStoInNoticesService.querySgBStoInNoticesMain.ReceiveParams:CpCustomer:{};", JSONObject.toJSONString(cpCustomerById));
                    }
                    if (cpCustomerById != null) {
                        pdaItem.setCpCCustomerEname(cpCustomerById.getEname());
                    }
                    resultList.add(pdaItem);
                });
            }

            if (resultList != null) {
                holder.setData(new PageInfo<>(resultList));
            } else {
                holder.setData(resultList);
            }
        } catch (Exception e) {
            log.error("SgPdaStoInNoticesService.querySgBStoInNoticesMain. error:{}", Throwables.getStackTraceAsString(e));
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("查询入库通知单主表信息发生异常," + e.getMessage(), loginUser.getLocale()));
        }
        return holder;
    }

    /**
     * 查询出库通知单明细
     *
     * @param request
     * @param loginUser 登录用户
     * @return
     */
    public ValueHolderV14<PageInfo<SgStoInNoticesItemsPdaResult>> querySgBStoInNoticesItem(SgStoInNoticesItemsPdaRequest request, User loginUser) {
        ValueHolderV14 holder = new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        if (log.isDebugEnabled()) {
            log.debug("Start SgPdaStoInNoticesService.querySgBStoInNoticesItem. ReceiveParams:" +
                    "request:{};", JSONObject.toJSONString(request));
        }
        List<SgStoInNoticesItemsPdaResult> resultList = new ArrayList<>();
        List<SgBStoInNoticesItem> sgBStoInNoticesItemsList = null;
        try {

            if (request.getPageSize() != null && request.getPageNum() != null) {
                /** 分页查询 **/
                PageHelper.startPage(request.getPageNum(), request.getPageSize());
            }
            //全量查询
            sgBStoInNoticesItemsList = sgBStoInNoticesItemMapper.selectList(new LambdaQueryWrapper<SgBStoInNoticesItem>()
                    .in(SgBStoInNoticesItem::getSgBStoInNoticesId, request.getSgBStoInNoticesId()));
            if (CollectionUtils.isNotEmpty(sgBStoInNoticesItemsList)) {

                List<String> psCSkuEcodeLists = sgBStoInNoticesItemsList.stream().map(SgBStoInNoticesItem::getPsCSkuEcode).collect(Collectors.toList());

                Map<String, PsCProSkuResult> psCProSkuResultMap = CommonCacheValUtils.getSkuInfo(psCSkuEcodeLists);
                if (MapUtils.isEmpty(psCProSkuResultMap)) {
                    holder.setCode(ResultCode.FAIL);
                    holder.setMessage("条码信息查询为空");
                    return holder;
                }
                sgBStoInNoticesItemsList.forEach(item -> {
                    SgStoInNoticesItemsPdaResult pdaItem = new SgStoInNoticesItemsPdaResult();
                    BeanUtils.copyProperties(item, pdaItem);
                    //获取条码信息
                    pdaItem.setSgBStoInNoticesItemId(item.getId());
                    if (psCProSkuResultMap.containsKey(item.getPsCSkuEcode())) {
                        PsCProSkuResult psCProSkuResult = psCProSkuResultMap.get(item.getPsCSkuEcode());

                        pdaItem.setPsCSpec1Id(psCProSkuResult.getPsCSpec1objId());
                        pdaItem.setPsCSpec1Ecode(psCProSkuResult.getClrsEcode());
                        pdaItem.setPsCSpec1Ename(psCProSkuResult.getClrsEname());
                        pdaItem.setPsCSpec2Id(psCProSkuResult.getPsCSpec2objId());
                        pdaItem.setPsCSpec2Ecode(psCProSkuResult.getSizesEcode());
                        pdaItem.setPsCSpec2Ename(psCProSkuResult.getSizesEname());
                        pdaItem.setGbcode(psCProSkuResult.getGbcode());
                        pdaItem.setForcode(psCProSkuResult.getForcode());
                        pdaItem.setPsCProId(psCProSkuResult.getPsCProId());
                        pdaItem.setPsCProEname(psCProSkuResult.getPsCProEname());
                        pdaItem.setPsCProEcode(psCProSkuResult.getPsCProEcode());
                        pdaItem.setPriceList(Optional.ofNullable(psCProSkuResult.getPricelist()).orElse(BigDecimal.ZERO));
                    }
                    resultList.add(pdaItem);
                });
            }

            if (resultList != null) {
                holder.setData(new PageInfo<>(resultList));
            } else {
                holder.setData(resultList);
            }
        } catch (Exception e) {
            log.error("SgPdaStoInNoticesService.querySgBStoInNoticesItem. error:{}", Throwables.getStackTraceAsString(e));
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("查询入库通知单明细表信息发生异常:" + e.getMessage(), loginUser.getLocale()));
        }
        return holder;
    }


    /**
     * 保存或审核入库通知单
     *
     * @param request
     * @param loginUser
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 saveOrSubmitStoInNotices(SgStoInNoticesSavePdaRequest request, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgPdaStoOutNoticesService.saveOrSubmitStoInNotices. ReceiveParams:" +
                    "request:{};", JSONObject.toJSONString(request));
        }
        ValueHolderV14 holder = new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        List<String> redisBillFtpKeyList = new ArrayList<>();
        try {
            SgBStoInNotices sgBStoInNotices = new SgBStoInNotices();
            if (request.getSgBStoInNoticesId() == null) {
                sgBStoInNotices = sgBStoInNoticesMapper.selectOne(new LambdaQueryWrapper<SgBStoInNotices>()
                        .eq(SgBStoInNotices::getBillNo, request.getBillNo()));
            } else {
                sgBStoInNotices = sgBStoInNoticesMapper.selectById(request.getSgBStoInNoticesId());
            }

            if (sgBStoInNotices == null) {
                holder.setCode(ResultCode.FAIL);
                holder.setMessage(Resources.getMessage("入库通知单不存在！", loginUser.getLocale()));
                return holder;
            }
            request.setSgBStoInNoticesId(sgBStoInNotices.getId());
            if (!SgStoreConstants.BILL_NOTICES_STATUS_INIT.equals(sgBStoInNotices.getBillStatus())) {
                holder.setCode(ResultCode.FAIL);
                holder.setMessage(Resources.getMessage("入库通知单状态不符，不允许保存！", loginUser.getLocale()));
                return holder;
            }

            if (CollectionUtils.isNotEmpty(request.getItem())) {
                for (SgStoInNoticesItemsSavePdaRequest resultItem : request.getItem()) {

                    SgBStoInNoticesItem sgBStoInNoticesItem = sgBStoInNoticesItemMapper.selectOne(new LambdaQueryWrapper<SgBStoInNoticesItem>()
                            .eq(SgBStoInNoticesItem::getId, resultItem.getSgBStoInNoticesItemId())
                            .eq(SgBStoInNoticesItem::getSgBStoInNoticesId, request.getSgBStoInNoticesId()));

                    if (sgBStoInNoticesItem == null) {
                        holder.setCode(ResultCode.FAIL);
                        holder.setMessage(Resources.getMessage("入库通知单明细" + resultItem.getSgBStoInNoticesItemId() + "不存在！", loginUser.getLocale()));
                        return holder;
                    }

                    // 产品要求将这个数据放在入库通知单的扫描
                    BigDecimal qtyScan = resultItem.getQtyIn() == null ? BigDecimal.ZERO : resultItem.getQtyIn();

                    SgBStoInNoticesItem update = new SgBStoInNoticesItem();
                    update.setQtyScan(qtyScan);
                    update.setId(resultItem.getSgBStoInNoticesItemId());
                    update.setSgBStoInNoticesId(request.getSgBStoInNoticesId());
                    update.setModifierename(loginUser.getEname());
                    update.setModifiername(loginUser.getName());
                    update.setModifieddate(new Date());
                    sgBStoInNoticesItemMapper.update(update, new LambdaQueryWrapper<SgBStoInNoticesItem>()
                            .eq(SgBStoInNoticesItem::getId, resultItem.getSgBStoInNoticesItemId())
                            .eq(SgBStoInNoticesItem::getSgBStoInNoticesId, request.getSgBStoInNoticesId()));
                }
                SgBStoInNotices updateMain = new SgBStoInNotices();
                StorageUtils.setBModelDefalutDataByUpdate(updateMain, loginUser);
                sgBStoInNoticesMapper.update(updateMain, new LambdaQueryWrapper<SgBStoInNotices>()
                        .eq(SgBStoInNotices::getId, request.getSgBStoInNoticesId()));
            }


            if (SgConstants.IS_SUBMIT_Y.equals(request.getIsSubmit())) {
                // : 审核生成逻辑入库单  调用新增并审核逻辑入库单服务；逻辑入库单的入库数量为入库通知单的扫描数量
                ValueHolderV14<SgBStoInResultBillSaveResult> sgBStoInResultBillSaveResult = insertSgInResult(request.getSgBStoInNoticesId(), loginUser);
                if (!sgBStoInResultBillSaveResult.isOK()) {
                    AssertUtils.logAndThrow("逻辑入库单审核异常！");
                }
                //逻辑入库单审核
                SgR3BaseRequest r3BaseRequest = new SgR3BaseRequest();
                r3BaseRequest.setObjId(sgBStoInResultBillSaveResult.getData().getId());
                r3BaseRequest.setLoginUser(loginUser);
                r3BaseRequest.setR3(true);
                if (log.isDebugEnabled()) {
                    log.debug("end SgBStoTransferOutResultService.stoInResultSubmitService.submitInResult.r3BaseRequest:{};", JSONObject.toJSONString(r3BaseRequest));
                }
                ValueHolderV14<SgR3BaseResult> stoInResultSubmitValueHolderV14 = stoInResultSubmitService.submitInResult(r3BaseRequest, redisBillFtpKeyList);
                if (!stoInResultSubmitValueHolderV14.isOK()) {
                    AssertUtils.logAndThrow("逻辑入库单审核服务异常：" + stoInResultSubmitValueHolderV14.getMessage());
                }

            }

        } catch (Exception e) {
            StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, loginUser);
            log.error("SgPdaStoInNoticesService.querySgBStoInNoticesItem. error:{}", Throwables.getStackTraceAsString(e));
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("保存或审核入库通知单明细表信息发生异常！" + e.getMessage(), loginUser.getLocale()));
        }


        return holder;
    }

    private ValueHolderV14<SgBStoInResultBillSaveResult> insertSgInResult(Long sgBStoInNoticesId, User loginUser) {
        //获取入库通知单
        SgBStoInNotices inNotices = sgBStoInNoticesMapper.selectById(sgBStoInNoticesId);
        AssertUtils.notNull(inNotices, "入库通知单不存在！");
        if (inNotices.getIsactive().equals(SgConstants.IS_ACTIVE_N)) {
            throw new NDSException(Resources.getMessage("入库通知单不存在!", loginUser.getLocale()));
        } else if (inNotices.getBillStatus().equals(SgStoreConstants.BILL_NOTICES_STATUS_ALL_IN) || inNotices.getBillStatus().equals(SgStoreConstants.BILL_NOTICES_STATUS_VOID)) {
            throw new NDSException(Resources.getMessage("入库通知单单据状态不符合!", loginUser.getLocale()));
        }

        List<SgBStoInNoticesItem> list = sgBStoInNoticesItemMapper.selectList(new QueryWrapper<SgBStoInNoticesItem>()
                .lambda().eq(SgBStoInNoticesItem::getSgBStoInNoticesId, sgBStoInNoticesId));

        if (CollectionUtils.isEmpty(list)) {
            throw new NDSException(Resources.getMessage("通知单明细不存在!", loginUser.getLocale()));
        }

        SgBStoIn sgBStoIn = sgBStoInMapper.selectOne(new QueryWrapper<SgBStoIn>().lambda()
                .eq(SgBStoIn::getSourceBillNo, inNotices.getSourceBillNo())
                .eq(SgBStoIn::getSourceBillType, inNotices.getSourceBillType())
                .eq(SgBStoIn::getIsactive, SgConstants.IS_ACTIVE_Y));

        if (sgBStoIn == null) {
            throw new NDSException(Resources.getMessage("逻辑在途单不存在!", loginUser.getLocale()));
        }

        SgBStoInResultSaveRequest inResultSaveRequest = new SgBStoInResultSaveRequest();
        //inResultSaveRequest
        BeanUtils.copyProperties(inNotices, inResultSaveRequest);
        //添加，把主表设置成小于0或者空
        inResultSaveRequest.setId(-1L);
        inResultSaveRequest.setBillNo("");

        inResultSaveRequest.setSgBStoInNoticesId(inNotices.getId());
        inResultSaveRequest.setSgBStoInNoticesNo(inNotices.getBillNo());
        // 上游控制是否是当前用户可操控的
        SgBStoInNoticesItem sgBStoInNoticesItem = list.get(0);

        inResultSaveRequest.setCpCStoreId(sgBStoInNoticesItem.getCpCStoreId());
        inResultSaveRequest.setCpCStoreEcode(sgBStoInNoticesItem.getCpCStoreEcode());
        inResultSaveRequest.setCpCStoreEname(sgBStoInNoticesItem.getCpCStoreEname());

        inResultSaveRequest.setIsLast(SgConstants.IS_LAST_YES);

        inResultSaveRequest.setSgBStoInId(sgBStoIn.getId());

        //复制明细字段值
        List<SgBStoInResultItemSaveRequest> itemList = Lists.newArrayList();

        list.forEach(item -> {
            if (item.getQtyScan() == null || item.getQty() == null) {
                AssertUtils.logAndThrow("入库通知单明细中扫描数量或通知数量不存在！");
            }
            /*if (item.getQty().compareTo(item.getQtyScan()) < 0) {
                AssertUtils.logAndThrow("入库通知单明细中通知数量不能少于扫描数量！");
            }*/

            SgBStoInResultItemSaveRequest itemRequest = new SgBStoInResultItemSaveRequest();
            BeanUtils.copyProperties(item, itemRequest);
            itemRequest.setId(-1L);
            //itemRequest.setSgBStoInNoticeItemId(item.getId());
            itemRequest.setQty(item.getQtyScan());
            itemRequest.setPsCSkuId(item.getPsCSkuId());
            itemRequest.setPsCProId(item.getPsCProId());
            itemList.add(itemRequest);
        });

        SgBStoInResultBillSaveRequest request = new SgBStoInResultBillSaveRequest();
        request.setLoginUser(loginUser);
        request.setInResultSaveRequest(inResultSaveRequest);
        request.setInItemResultSaveRequestList(itemList);
        SgBStoInResultSaveService service = ApplicationContextHandle.getBean(SgBStoInResultSaveService.class);
        return service.saveSgBStoInResult(request);
    }

}
