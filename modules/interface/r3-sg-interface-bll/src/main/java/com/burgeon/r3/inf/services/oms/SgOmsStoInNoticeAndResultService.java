package com.burgeon.r3.inf.services.oms;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInNotices;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoInNoticeAndResultRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoInNoticeQueryRequest;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsStoInNoticeQueryResult;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInNoticesMapper;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInNoticesBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultSaveRequest;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInNoticesBillSaveResult;
import com.burgeon.r3.sg.store.services.in.SgBStoInNoticesSaveService;
import com.burgeon.r3.sg.store.services.in.SgBStoInResultSaveAndSubmitService;
import com.google.common.base.Throwables;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lin yu
 * @date : 2022/7/27 上午10:50
 * @describe : 退货入 生成入库通知单----->生成并审核逻辑入库单 [自行校验参数]
 */

@Slf4j
@Component
public class SgOmsStoInNoticeAndResultService {


    @Autowired
    private SgBStoInResultSaveAndSubmitService stoInResultSaveAndSubmitService;


    @Autowired
    private SgBStoInNoticesSaveService stoInNoticesSaveService;

    @Autowired
    private SgBStoInNoticesMapper inNoticesMapper;


    public ValueHolderV14<String> createInNoticeAndResult(SgOmsStoInNoticeAndResultRequest request) {

        ValueHolderV14<String> result = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");

        if (log.isDebugEnabled()) {
            log.debug("SgOmsStoInNoticeAndResultService.createInNoticeAndResult Start:{}", JSONObject.toJSONString(request));
        }

        try {

            SgOmsStoInNoticeAndResultService service = ApplicationContextHandle.getBean(SgOmsStoInNoticeAndResultService.class);
            ValueHolderV14<SgBStoInNoticesBillSaveResult> saveResultV14 = service.doCreateInNoticeAndResult(request, null);
            result.setData(saveResultV14.getData().getBillNo());
        } catch (Exception ex) {
            log.error("SgOmsStoInNoticeAndResultService.createInNoticeAndResult. error:{}", Throwables.getStackTraceAsString(ex));
            result.setCode(ResultCode.FAIL);
            result.setMessage(ex.getMessage());
            return result;
        }

        return result;
    }


    public ValueHolderV14<String> createInNoticeAndResultAndFreeze(SgOmsStoInNoticeAndResultRequest request, SgBStoFreezeBillSaveRequest stoFreezeBillSaveRequest) {

        ValueHolderV14<String> result = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");

        if (log.isDebugEnabled()) {
            log.debug("SgOmsStoInNoticeAndResultService.createInNoticeAndResultAndFreeze Start:{}", JSONObject.toJSONString(request));
        }

        try {

            SgOmsStoInNoticeAndResultService service = ApplicationContextHandle.getBean(SgOmsStoInNoticeAndResultService.class);
            ValueHolderV14<SgBStoInNoticesBillSaveResult> saveResultV14 = service.doCreateInNoticeAndResult(request, stoFreezeBillSaveRequest);
            result.setData(saveResultV14.getData().getBillNo());
        } catch (Exception ex) {
            log.error("SgOmsStoInNoticeAndResultService.createInNoticeAndResultAndFreeze. error:{}", Throwables.getStackTraceAsString(ex));
            result.setCode(ResultCode.FAIL);
            result.setMessage(ex.getMessage());
            return result;
        }

        return result;
    }


    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgBStoInNoticesBillSaveResult> doCreateInNoticeAndResult(SgOmsStoInNoticeAndResultRequest request,
                                                                                   SgBStoFreezeBillSaveRequest stoFreezeBillSaveRequest) {

        SgBStoInNoticesBillSaveRequest stoInNoticesBillSaveRequest = request.getStoInNoticesBillSaveRequest();
        List<SgBStoInResultBillSaveRequest> stoInResultBillSaveRequests = request.getStoInResultBillSaveRequests();
        User user = request.getUser();

        //防止退换货单已经生成，但是无限请求
        SgBStoInNoticesBillSaveRequest.SgBStoInNoticesSaveRequest inNoticesSaveRequest = stoInNoticesBillSaveRequest.getInNoticesSaveRequest();
        Integer sourceBillType = inNoticesSaveRequest.getSourceBillType();
        if (sourceBillType != null && SgConstantsIF.BILL_TYPE_RETAIL_REF == sourceBillType) {
            String sourceBillNo = inNoticesSaveRequest.getSourceBillNo();
            AssertUtils.notNull(sourceBillNo, "来源单据编号不能为空!");
            List<SgBStoInNotices> sgStoInNotices = inNoticesMapper.selectList(new LambdaQueryWrapper<SgBStoInNotices>()
                    .eq(SgBStoInNotices::getSourceBillNo, sourceBillNo)
                    .eq(SgBStoInNotices::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (CollectionUtils.isNotEmpty(sgStoInNotices)) {
                ValueHolderV14<SgBStoInNoticesBillSaveResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "退货入库生成入库单成功：已存在入库单！");
                SgBStoInNoticesBillSaveResult sgStoInNoticesBillSaveResult = new SgBStoInNoticesBillSaveResult();
                sgStoInNoticesBillSaveResult.setBillNo(sgStoInNotices.get(0).getBillNo());
                sgStoInNoticesBillSaveResult.setId(sgStoInNotices.get(0).getId());
                v14.setData(sgStoInNoticesBillSaveResult);
                return v14;
            }
        }

        //生成入库通知单
        ValueHolderV14<SgBStoInNoticesBillSaveResult> stoInNoticesResult = stoInNoticesSaveService.addInNotices(stoInNoticesBillSaveRequest, user);

        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + ",退货生成入库通知单结果:{}", JSONObject.toJSONString(stoInNoticesResult));
        }

        AssertUtils.cannot(!stoInNoticesResult.isOK(), "入库通知单服务异常:" + stoInNoticesResult.getMessage());

        SgBStoInResultBillSaveRequest stoInResultBillSaveRequest = stoInResultBillSaveRequests.get(0);
        stoInResultBillSaveRequest.setSgBStoInNoticesId(stoInNoticesResult.getData().getId());
        stoInResultBillSaveRequest.setSgBStoInNoticesNo(stoInNoticesResult.getData().getBillNo());

        SgBStoInResultSaveRequest inResultSaveRequest = stoInResultBillSaveRequest.getInResultSaveRequest();
        inResultSaveRequest.setSgBStoInNoticesId(stoInNoticesResult.getData().getId());
        inResultSaveRequest.setSgBStoInNoticesNo(stoInNoticesResult.getData().getBillNo());

        //新增并审核逻辑入库单
        ValueHolderV14 valueHolderV14 = stoInResultSaveAndSubmitService.inAndFreezeSaveAndAuditBillWithTrans(stoInResultBillSaveRequests, stoFreezeBillSaveRequest);

        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + ",退货新增并审核逻辑入库单结果:{}", JSONObject.toJSONString(stoInNoticesResult));
        }

        AssertUtils.cannot(!valueHolderV14.isOK(), ",退货新增并审核逻辑入库单服务异常:" + valueHolderV14.getMessage());

        return stoInNoticesResult;
    }

    public ValueHolderV14<List<SgOmsStoInNoticeQueryResult>> queryInNotice(SgOmsStoInNoticeQueryRequest request) {

        ValueHolderV14<List<SgOmsStoInNoticeQueryResult>> result = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");
        log.info("SgOmsStoInNoticeAndResultService.queryInNotice Start:{}", JSONObject.toJSONString(request));
        List<SgBStoInNotices> sgBStoInNotices = inNoticesMapper.selectInNoticesBySourceBillNo(request.getReturnOrderBillNo(), SgConstantsIF.BILL_TYPE_RETAIL_REF);
        if (CollectionUtils.isEmpty(sgBStoInNotices)) {
            result.setData(new ArrayList<>());
            return result;
        }
        // 将sgBStoInNotices 组装成List<SgOmsStoInNoticeQueryResult>。SgOmsStoInNoticeQueryResult的字段不用新增 就id跟billno
        List<SgOmsStoInNoticeQueryResult> sgOmsStoInNoticeQueryResults = new ArrayList<>();
        for (SgBStoInNotices sgBStoInNotices1 : sgBStoInNotices) {
            SgOmsStoInNoticeQueryResult sgOmsStoInNoticeQueryResult = new SgOmsStoInNoticeQueryResult();
            sgOmsStoInNoticeQueryResult.setId(sgBStoInNotices1.getId());
            sgOmsStoInNoticeQueryResult.setBillNo(sgBStoInNotices1.getBillNo());
            sgOmsStoInNoticeQueryResults.add(sgOmsStoInNoticeQueryResult);
        }
        result.setData(sgOmsStoInNoticeQueryResults);
        return result;
    }

}
