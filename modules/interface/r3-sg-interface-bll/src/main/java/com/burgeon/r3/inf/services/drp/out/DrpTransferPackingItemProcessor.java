package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.inf.services.drp.out.mapper.DrpTransferPackingMapper;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransfer;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransferPackingItem;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/8/24 16:02
 * 电子装箱单明细
 */
@Slf4j
@Component
public class DrpTransferPackingItemProcessor extends AbstractDrpInterfaceProcessor<SgBStoTransferPackingItem, SgBStoTransferPackingItem> {

    @Autowired
    private DrpTransferPackingMapper transferPackingMapper;

    @Override
    public LambdaQueryWrapper<SgBStoTransferPackingItem> execMainWrapper() {
        LambdaQueryWrapper<SgBStoTransferPackingItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.and(o -> {
            o.isNull(SgBStoTransferPackingItem::getDrpStatus);
            o.or(oo -> oo.eq(SgBStoTransferPackingItem::getDrpStatus, SgStoreConstants.SEND_DRP_STATUS_UNDECLARED));
            o.or(oo -> oo.eq(SgBStoTransferPackingItem::getDrpStatus, SgStoreConstants.SEND_DRP_STATUS_FAIL).lt(SgBStoTransferPackingItem::getDrpFailCount, failNum));
            return o;
        });
        return wrapper;
    }

    @Override
    public LambdaQueryWrapper<SgBStoTransferPackingItem> execitemWrapper(Long mianId) {
        return null;
    }

    @Override
    public String drpInterfaceUrl() {
        return "erp.package.list";
    }

    @Override
    public String itemMainField() {
        return "id";
    }

    @Override
    public String drpStatus() {
        return "DRP_STATUS";
    }

    @Override
    public String drpStatusFailCount() {
        return "DRP_FAIL_COUNT";
    }

    @Override
    public String drpStatusFailReason() {
        return "DRP_FAIL_REASON";
    }

    @Override
    public JSONObject execInterfaceParam(SgBStoTransferPackingItem sgBStoTransferPackingItem, List<SgBStoTransferPackingItem> z) {
        if (log.isDebugEnabled()) {
            log.debug("DrpTransferPackingItemProcessor execInterfaceParam sgBStoTransferPackingItem {}", sgBStoTransferPackingItem);
        }
        JSONObject object = new JSONObject();
        Long stoTransferId = sgBStoTransferPackingItem.getSgBStoTransferId();
        SgBStoTransfer transfer = transferPackingMapper.selectData(new LambdaQueryWrapper<SgBStoTransfer>()
                .eq(SgBStoTransfer::getId, stoTransferId));

        object.put("ZTDOCNO", transfer.getBillNo());
        object.put("EXTERNORDERKEY", transfer.getBillNo());
        object.put("LOADKEY", sgBStoTransferPackingItem.getLoadKey());
        object.put("MBOLKEY", sgBStoTransferPackingItem.getMbolKey());
        object.put("CARTONNO", sgBStoTransferPackingItem.getCartonNo());
        object.put("LABELNO", sgBStoTransferPackingItem.getLabelNo());
        object.put("QTY", sgBStoTransferPackingItem.getQty());
        object.put("SUNUMBER", sgBStoTransferPackingItem.getQtyScan());
        object.put("SKU", sgBStoTransferPackingItem.getPsCSkuEcode());
        object.put("STYLE", sgBStoTransferPackingItem.getPsCProEcode());
        object.put("C_SIZE", sgBStoTransferPackingItem.getPsCSpec2Ename());
        object.put("UPC", sgBStoTransferPackingItem.getForcode());
        object.put("C_ORIG_ID__CODE", transfer.getSenderStoreEcode());
        object.put("C_DEST_ID__CODE", transfer.getReceiverStoreEcode());
        object.put("PACKING_STATUS", sgBStoTransferPackingItem.getScanStatus());
        if (log.isDebugEnabled()) {
            log.debug("DrpTransferPackingItemProcessor execInterfaceParam object {}", object);
        }
        return object;
    }

    @Override
    public void handleBysuccess(SgBStoTransferPackingItem sgBStoTransferPackingItem, List<SgBStoTransferPackingItem> z) {

    }
}
