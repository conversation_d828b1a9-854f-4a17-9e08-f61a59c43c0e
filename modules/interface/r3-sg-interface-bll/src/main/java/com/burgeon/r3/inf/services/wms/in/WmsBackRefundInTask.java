package com.burgeon.r3.inf.services.wms.in;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Description: 退换货单确认中间表
 *
 * @Author: guo.kw
 * @Since: 2022/7/15
 * create at: 2022/7/15 10:01
 */
@Slf4j
@Component
public class WmsBackRefundInTask {

//    @Autowired
//    private SgBRefundInTaskMapper sgBRefundInTaskMapper;


    public ValueHolderV14<String> apiProcess(String msg) {

//        CusRedisTemplate<Object, Object> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();
//        String lockKsy = SgConstants.SG_BILL_LOCK_WMSRETURN;
//
//        try {
//
//            JSONObject request = JSONObject.parseObject(msg).getJSONObject("request");
//
//            JSONObject returnOrder = request.getJSONObject("returnOrder");
//
//            // 单据类型
//            String orderType = returnOrder.getString("orderType");
//            // 仓库编码
//            String warehouseCode = returnOrder.getString("warehouseCode");
//            // 入库通知单号
//            String returnOrderCode = returnOrder.getString("returnOrderCode");
//            // WMS单据编号
//            String returnOrderId = returnOrder.getString("returnOrderId");
//
//            lockKsy += returnOrderCode;
//
//            Boolean ifAbsent = redisMasterTemplate.opsForValue().setIfAbsent(lockKsy, "OK");
//
//            SgBRefundInTask sgBRefundInTask = sgBRefundInTaskMapper.selectOne(new LambdaQueryWrapper<SgBRefundInTask>()
//                    .eq(SgBRefundInTask::getNoticesBillNo, returnOrderCode)
//                    .eq(SgBRefundInTask::getIsactive, "Y"));
//
//            if (Objects.nonNull(sgBRefundInTask) || ifAbsent == null || !ifAbsent) {
//                log.error(LogUtil.format("退换入库单WMS回传重复.messageBody=", "退换入库单WMS回传重复", sgBRefundInTask), msg);
//            } else {
//                redisMasterTemplate.expire(lockKsy, 30, TimeUnit.SECONDS);
//
//                SgBRefundInTask sgBRefundInTask1 = new SgBRefundInTask();
//                sgBRefundInTask1.setId(ModelUtil.getSequence(SgConstants.SG_B_REFUND_IN_TASK));
//                sgBRefundInTask1.setNoticesBillNo(returnOrderCode);
//                sgBRefundInTask1.setWarehouseCode(warehouseCode);
//                sgBRefundInTask1.setBillType(orderType);
//                sgBRefundInTask1.setMessage(msg);
//                sgBRefundInTask1.setWmsBillCode(returnOrderId);
//
//                sgBRefundInTask1.setWmsWarehouseType(SgConstants.WMS_WAREHOUSE_TYPE_QMWMS);
//                sgBRefundInTask1.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT);
//                sgBRefundInTask1.setFailedCount(NumberUtils.INTEGER_ZERO);
//                sgBRefundInTask1.setIsactive(SgConstants.IS_ACTIVE_Y);
//                StorageUtils.setBModelDefalutData(sgBRefundInTask1, R3SystemUserResource.getSystemRootUser());
//                sgBRefundInTaskMapper.insert(sgBRefundInTask1);
//            }
//        } catch (Exception e) {
//            redisMasterTemplate.delete(lockKsy);
//            log.error(LogUtil.format("退换入库单WMS回传异常={}", "退换入库单WMS回传异常"), Throwables.getStackTraceAsString(e));
//            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
//        }
        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("success"));
    }

}
