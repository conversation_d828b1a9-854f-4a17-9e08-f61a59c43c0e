package com.burgeon.r3.inf.services.wms;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.request.SgCallbackShareQtyCalcItemRequest;
import com.burgeon.r3.sg.basic.model.request.SgCallbackShareQtyCalcRequest;
import com.burgeon.r3.sg.basic.model.result.SgCallbackLsQtyCalcOutItemResult;
import com.burgeon.r3.sg.basic.model.result.SgCallbackShareQtyCalcResult;
import com.burgeon.r3.sg.basic.model.result.SgCallbackSsQtyCalcOutItemResult;
import com.burgeon.r3.sg.basic.services.SgLsStorageCalculationService;
import com.burgeon.r3.sg.basic.services.SgStorageCalculationService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.model.table.store.in.SgBWmsToInventoryChangeNoticeTask;
import com.burgeon.r3.sg.core.utils.DateUtils;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutMapper;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OmsReleaseStockOrderCmd;
import com.jackrain.nea.oc.oms.model.request.OmsReleaseStockRequest;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2023/3/29 13:39
 * @Version 1.0
 */
@Slf4j
@Component
public class SgWmsAutoReleaseOrderService {


    @Autowired
    private SgLsStorageCalculationService sgLsStorageCalculationService;
    @Autowired
    private SgBStoOutMapper sgBStoOutMapper;

    @Reference(group = "oc-core", version = "1.0")
    private OmsReleaseStockOrderCmd omsReleaseStockOrderCmd;

    @Autowired
    private SgStorageCalculationService sgStorageCalculationService;


    public void autoReleaseOrder(CpCStore cpCStore, List<SgBWmsToInventoryChangeNoticeTask> qtyMax, User user) {
        //查询库存
        SgCallbackShareQtyCalcRequest request = new SgCallbackShareQtyCalcRequest();
        request.setSupplyStoreId(cpCStore.getId());
        List<SgCallbackShareQtyCalcItemRequest> itemList = new ArrayList<>();
        for (SgBWmsToInventoryChangeNoticeTask itemResult : qtyMax) {
            SgCallbackShareQtyCalcItemRequest itemRequest = new SgCallbackShareQtyCalcItemRequest();
            PsCProSkuResult skuInfo = CommonCacheValUtils.getSkuInfoByEcode(itemResult.getPsCProEcode());
            itemRequest.setPsCSkuId(skuInfo.getId());
            itemRequest.setPsCSkuEcode(itemResult.getPsCProEcode());
            String produceDate = StringUtils.isEmpty(itemResult.getBatchCode()) || SgConstantsIF.DEFAULT_PRODUCE_DATE.equals(itemResult.getBatchCode()) ? SgConstantsIF.DEFAULT_PRODUCE_DATE :
                    DateUtils.formatSync8(DateUtils.parseSync8(itemResult.getBatchCode(), DateUtils.PATTERN_DATE), DateUtils.DATE_PATTERN);
            itemRequest.setProduceDate(produceDate);
            itemRequest.setQtyChange(BigDecimal.valueOf(itemResult.getQtyChange()).abs());
            itemList.add(itemRequest);
        }
        request.setItemList(itemList);
        request.setQtySharedType(1);
        ValueHolderV14<SgCallbackShareQtyCalcResult> holderV14 = sgStorageCalculationService.calcCallbackShareQty(request, user);
        if (!holderV14.isOK()) {
            throw new NDSException("查询回退异常:" + holderV14.getMessage());
        }
        String wmsBillCode = qtyMax.get(0).getWmsBillCode();
        log.info(LogUtil.format("wmsBillCode:{},param:{}",
                "SgWmsAutoReleaseOrderService.autoReleaseOrder"), wmsBillCode, JSONObject.toJSONString(holderV14));
        SgCallbackShareQtyCalcResult data = holderV14.getData();
        List<SgCallbackLsQtyCalcOutItemResult> outStockItemList = data.getOutStockItemList();
        if (CollectionUtils.isNotEmpty(outStockItemList)) {
            throw new NDSException("库存不足");
        }
        List<SgCallbackLsQtyCalcOutItemResult> shareOutReleaseItemList = data.getShareOutReleaseItemList();
        if (CollectionUtils.isNotEmpty(shareOutReleaseItemList)) {
            List<SgCallbackLsQtyCalcOutItemResult> collect = shareOutReleaseItemList.stream().filter(p -> p.getShareOutFlag()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect) && !data.getFlag() && isQty(data)) {
                StringBuilder builder = new StringBuilder();
                for (SgCallbackLsQtyCalcOutItemResult result : collect) {
                    builder.append("条码:").append(result.getPsCSkuEcode()).append("缺货数量:").append(result.getQtyOutOfStock());
                }
                throw new NDSException(builder.toString());
            }
            List<SgCallbackLsQtyCalcOutItemResult> releaseData = shareOutReleaseItemList.stream().filter(p -> !p.getShareOutFlag()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(releaseData)) {
                log.info(LogUtil.format("wmsBillCode:{},释放数据:{}",
                        "SgWmsAutoReleaseOrderService.autoReleaseOrder123"), wmsBillCode, JSONObject.toJSONString(releaseData));

                Map<Long, SgCallbackShareQtyCalcItemRequest> itemRequestMap = itemList.stream().collect(Collectors.toMap(SgCallbackShareQtyCalcItemRequest::getPsCSkuId, Function.identity(), (key1, key2) -> key2));
                List<OmsReleaseStockRequest> requestList = new ArrayList<>();
                for (SgCallbackLsQtyCalcOutItemResult itemResult : releaseData) {
                    Long psCSkuId = itemResult.getPsCSkuId();
                    BigDecimal qtyOutOfStock = itemResult.getQtyOutOfStock();
                    SgCallbackShareQtyCalcItemRequest itemRequest = itemRequestMap.get(psCSkuId);
                    String produceDate = itemRequest.getProduceDate();
                    List<Long> orderIds = sgBStoOutMapper.selectWmsAutoReleaseOrderList(psCSkuId, produceDate, qtyOutOfStock, cpCStore.getId());
                    if (CollectionUtils.isNotEmpty(orderIds)) {
                        OmsReleaseStockRequest stockRequest = new OmsReleaseStockRequest();
                        stockRequest.setOrderIds(orderIds);
                        stockRequest.setCpCSkuId(psCSkuId);
                        stockRequest.setCpCSkuEcode(itemRequest.getPsCSkuEcode());
                        stockRequest.setQty(qtyOutOfStock);
                        requestList.add(stockRequest);
                    }
                }
                log.info(LogUtil.format("param:{}", "SgWmsAutoReleaseOrderService.requestList", wmsBillCode), JSONObject.toJSONString(requestList));
                if (CollectionUtils.isEmpty(requestList)) {
                    return;
                }
                //订单释放
                ValueHolderV14 holderV141 = omsReleaseStockOrderCmd.releaseStockOrder(requestList, user);
                if (!holderV141.isOK()) {
                    throw new NDSException("订单释放库存异常:" + holderV141.getMessage());
                }
            }
        }
    }


    private boolean isQty(SgCallbackShareQtyCalcResult data) {
        List<SgCallbackLsQtyCalcOutItemResult> shareOutReleaseItemList = data.getShareOutReleaseItemList();
        List<SgCallbackSsQtyCalcOutItemResult> outSsStockItemList = data.getOutSsStockItemList();
        Map<Long, List<SgCallbackLsQtyCalcOutItemResult>> shareOutReleaseMap = shareOutReleaseItemList.stream().collect(Collectors.groupingBy(SgCallbackLsQtyCalcOutItemResult::getPsCSkuId));
        for (SgCallbackSsQtyCalcOutItemResult itemResult : outSsStockItemList) {
            Long psCSkuId = itemResult.getPsCSkuId();
            List<SgCallbackLsQtyCalcOutItemResult> sgCallbackLsQtyCalcOutItemResults = shareOutReleaseMap.get(psCSkuId);
            if (CollectionUtils.isEmpty(sgCallbackLsQtyCalcOutItemResults)) {
                continue;
            }
            BigDecimal qtyOutOfStock = sgCallbackLsQtyCalcOutItemResults.stream().map(SgCallbackLsQtyCalcOutItemResult::getQtyOutOfStock).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal qtyOutOfStock1 = itemResult.getQtyOutOfStock();
            if (qtyOutOfStock.compareTo(qtyOutOfStock1) < 0) {
                return true;
            }
        }
        return false;
    }
}
