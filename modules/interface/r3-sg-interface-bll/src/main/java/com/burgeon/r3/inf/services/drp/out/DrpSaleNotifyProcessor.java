package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransfer;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransferItem;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.services.transfer.SgTransferBillStatusEnum;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * 销售入库通知接口
 * @date 2021/8/13 15:03
 */
@Slf4j
@Component
public class DrpSaleNotifyProcessor extends AbstractDrpInterfaceProcessor<SgBStoTransfer, SgBStoTransferItem> {

    @Autowired
    private CpCStoreMapper cpCStoreMapper;

    @Override
    public LambdaQueryWrapper<SgBStoTransfer> execMainWrapper() {
        LambdaQueryWrapper<SgBStoTransfer> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SgBStoTransfer::getStatus, SgTransferBillStatusEnum.AUDITED_ALL_OUT_ALL_IN.getVal());
//        wrapper.ne(SgBStoTransfer::getStatus, SgTransferBillStatusEnum.VOIDED.getVal());
        wrapper.eq(SgBStoTransfer::getIsactive, SgConstants.IS_ACTIVE_Y);
        wrapper.eq(SgBStoTransfer::getDrpBillType, SgStoreConstants.DRP_BILL_TYPE_SA);
        wrapper.and(a -> {
            a.isNull(SgBStoTransfer::getDrpInStatus);
            a.or(oo -> oo.eq(SgBStoTransfer::getDrpInStatus, SgStoreConstants.SEND_DRP_STATUS_UNDECLARED));
            a.or(oo -> oo.eq(SgBStoTransfer::getDrpInStatus, SgStoreConstants.SEND_DRP_STATUS_FAIL).lt(SgBStoTransfer::getDrpFailCount, failNum));
            return a;
        });

        return wrapper;
    }

    private LambdaQueryWrapper<SgBStoTransfer> pageIn(LambdaQueryWrapper<SgBStoTransfer> lqw, List<List<SgCpCStore>> lists, Boolean isSender) {
        for (List<SgCpCStore> pageList : lists) {
            if (CollectionUtils.isEmpty(pageList)) {
                continue;
            }
            List<Long> cpcStoIds = pageList.stream().map(SgCpCStore::getId).collect(Collectors.toList());
            if (isSender) {
                lqw.or(oo -> {
                    oo.in(SgBStoTransfer::getSenderStoreId, cpcStoIds);
                    return oo;
                });
            } else {
                lqw.or(oo -> {
                    oo.in(SgBStoTransfer::getReceiverStoreId, cpcStoIds);
                    return oo;
                });
            }
        }
        return lqw;
    }

    @Override
    public LambdaQueryWrapper<SgBStoTransferItem> execitemWrapper(Long mainId) {
        return null;
    }

    @Override
    public String drpInterfaceUrl() {
        return "erp.salein.notice";
    }

    @Override
    public String itemMainField() {
        return "sg_b_sto_transfer_id";
    }

    @Override
    public String drpStatus() {
        return "DRP_IN_STATUS";
    }

    @Override
    public String drpStatusFailCount() {
        return "DRP_IN_FAIL_COUNT";
    }

    @Override
    public String drpStatusFailReason() {
        return "DRP_IN_FAIL_REASON";
    }


    @Override
    public JSONObject execInterfaceParam(SgBStoTransfer sgStoTransfer, List<SgBStoTransferItem> z) {
        if (log.isDebugEnabled()) {
            log.debug("SgDrpStoInNotifyProcessor.exec:transfer={},items={}", JSONObject.toJSONString(sgStoTransfer), JSONObject.toJSONString(z));
        }

        JSONObject jsonObject = new JSONObject();
        //中台编号
        jsonObject.put("ZTDOCNO", sgStoTransfer.getBillNo());
        //erp单据编号，需求确认=单据编号
//        jsonObject.put("DOCNO", sgStoTransfer.getBillNo());
        //入库日期
        jsonObject.put("DATEIN", DateUtil.format(sgStoTransfer.getInDate() == null ?
                new Date() : sgStoTransfer.getInDate(), "yyyyMMdd"));


        JSONArray items = new JSONArray();
        for (SgBStoTransferItem item : z) {
            JSONObject itemJson = new JSONObject();
            itemJson.put("M_PRODUCTALIAS_NO", item.getPsCSkuEcode());
            //款号，需求确认=商品编码
            itemJson.put("M_PRODUCT_NAME", item.getPsCProEcode());
            //入库数量
            itemJson.put("QTYIN", item.getQtyIn());
            items.add(itemJson);
        }

        jsonObject.put("items", items);
        log.info("SgDrpStoInNotifyProcessor.JSONObject:{}", jsonObject.toJSONString());
        return jsonObject;
    }

    @Override
    public void handleBysuccess(SgBStoTransfer sgStoTransfer, List<SgBStoTransferItem> z) {

    }
}
