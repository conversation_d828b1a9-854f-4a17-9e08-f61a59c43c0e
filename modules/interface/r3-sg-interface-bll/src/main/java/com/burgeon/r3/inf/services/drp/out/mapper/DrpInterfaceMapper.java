package com.burgeon.r3.inf.services.drp.out.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface DrpInterfaceMapper extends ExtentionMapper {


    @Update(" UPDATE ${table} set ${drpStatusField} = #{drpStatus},${drpFailCountField} = #{drpFailCount}," +
            " ${drpFailReasonField} = #{drpFailReason} WHERE id = #{id}")
    int updateFailCount(@Param("table") String table,
                        @Param("drpStatusField") String drpStatusField, @Param("drpStatus") Integer drpStatus,
                        @Param("drpFailCountField") String drpFialCountField, @Param("drpFailCount") Integer drpFailCount,
                        @Param("drpFailReasonField") String drpFailReasonField, @Param("drpFailReason") String drpFailReason,
                        @Param("id") Long id);

    @Update(" UPDATE ${table} set ${drpStatusField} = #{drpStatus},${drpFailCountField} =null, ${drpFailReasonField} = null" +
            "  WHERE id = #{id}")
    int updateSuccess(@Param("table") String table,
                      @Param("drpStatusField") String drpStatusField, @Param("drpStatus") Integer drpStatus,
                      @Param("drpFailCountField") String drpFialCountField, @Param("drpFailReasonField") String drpFailReasonField,
                      @Param("id") Long id);

}