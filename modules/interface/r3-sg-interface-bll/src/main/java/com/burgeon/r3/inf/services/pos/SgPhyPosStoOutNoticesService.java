package com.burgeon.r3.inf.services.pos;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNoticesItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.inf.model.request.pos.SgBPhyOutNoticesPosQryRequest;
import com.burgeon.r3.sg.inf.model.request.pos.SgPhyQueryStoOutNoticesRequest;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/15
 */
@Slf4j
@Component
public class SgPhyPosStoOutNoticesService {

    @Autowired
    private SgBStoOutNoticesMapper stoOutNoticesMapper;

    @Autowired
    private SgBStoOutNoticesItemMapper stoOutNoticesItemMapper;

    public ValueHolderV14<PageInfo<SgBStoOutNotices>> queryStoOutNotices(SgPhyQueryStoOutNoticesRequest request) {

        try {
            check(request);
        } catch (Exception e) {
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }

        CpCStore store = CommonCacheValUtils.getStoreInfoByEcode(request.getStoreEcode());

        if (Objects.isNull(store)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "未查询到店铺！");
        }
        if (Objects.isNull(store.getCpCPhyWarehouseId())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "该店铺未绑定实体仓！");
        }

        // 分页查询
        PageHelper.startPage(request.getPage(), request.getPageSize());

        LambdaQueryWrapper<SgBStoOutNotices> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(SgBStoOutNotices::getCpCPhyWarehouseId, store.getCpCPhyWarehouseId());
        wrapper.eq(SgBStoOutNotices::getBillStatus, SgStoreConstants.BILL_NOTICES_STATUS_INIT);
        wrapper.eq(SgBStoOutNotices::getIsactive, SgConstants.IS_ACTIVE_Y);

        List<SgBStoOutNotices> resultList = stoOutNoticesMapper.selectList(wrapper);

        return new ValueHolderV14<>(new PageInfo<>(resultList), ResultCode.SUCCESS, "success");
    }

    public void check(SgPhyQueryStoOutNoticesRequest request) {
        AssertUtils.notNull(request, "请求参数不能为空！");
        AssertUtils.cannot(StringUtils.isEmpty(request.getStoreEcode()), "店铺编码不能为空");

        if (Objects.isNull(request.getPage())) {
            request.setPage(1);
        }
        if (Objects.isNull(request.getPageSize())) {
            request.setPageSize(50);
        }
    }

    public ValueHolderV14<List<SgBStoOutNoticesItem>> queryStoOutNoticesItem(SgPhyQueryStoOutNoticesRequest request) {

        if (Objects.isNull(request) || Objects.isNull(request.getItemId())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "请求参数不能为空！");
        }

        LambdaQueryWrapper<SgBStoOutNoticesItem> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(SgBStoOutNoticesItem::getSgBStoOutNoticesId, request.getItemId());

        List<SgBStoOutNoticesItem> resultList = stoOutNoticesItemMapper.selectList(wrapper);

        return new ValueHolderV14<>(resultList, ResultCode.SUCCESS, "success");
    }

    /**
     * 查询全渠道发货单数据
     *
     * @param request
     * @return
     */
    public ValueHolderV14<List<SgBStoOutNotices>> querySgPayOutNoticesPos(SgBPhyOutNoticesPosQryRequest request) {
        if (Objects.isNull(request)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "请求参数不能为空！");
        }
        if (request.getBillStatus() == null && request.getOrderStatus() == null) {
            return new ValueHolderV14<>(ResultCode.FAIL, "请求参数不能同时为空！");
        }
        log.info("SgPhyPosStoOutNoticesService querySgPayOutNoticesPos getBillStatus:{},getOrderStatus:{}", request.getBillStatus(), request.getOrderStatus());
        List<SgBStoOutNotices> sgBStoOutNoticesList = stoOutNoticesMapper.selectList(new LambdaQueryWrapper<SgBStoOutNotices>()
                .eq(SgBStoOutNotices::getDeliveryMethod, SgConstantsIF.SG_STO_OUT_DELIVERY_WAY_DELIVERY)
                .eq(SgBStoOutNotices::getIsToStore, SgConstantsIF.IS_TO_STORE_Y)
                .eq(SgBStoOutNotices::getSourceBillType, SgConstantsIF.BILL_TYPE_RETAIL)
                .eq(request.getBillStatus() != null, SgBStoOutNotices::getBillStatus, request.getBillStatus())
                .eq(request.getOrderStatus() != null, SgBStoOutNotices::getOrderStatus, request.getOrderStatus()));

        log.info("SgPhyPosStoOutNoticesService querySgPayOutNoticesPos sgBStoOutNoticesList.size:{}", sgBStoOutNoticesList.size());
        return new ValueHolderV14<>(sgBStoOutNoticesList, ResultCode.SUCCESS, "success");
    }
}
