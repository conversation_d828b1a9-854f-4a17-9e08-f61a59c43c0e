package com.burgeon.r3.inf.handler;

import com.alibaba.fastjson.JSON;
import com.burgeon.r3.inf.services.wms.adjust.SgBWmsInventoryReportSaveService;
import com.burgeon.r3.sg.inf.common.SgMQConstants;
import com.google.common.base.Throwables;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @time 2019/5/6 17:52
 * @descript WMS --> sg,oms 回执 (库存盘点->回执)
 */
@Slf4j
@Service(SgMQConstants.WMS_RETURN_MIDDLE_TABLE_METHOD_INVENTORY_REPORT)
public class WMSCallBackInventoryReportOrderHandler implements CallBackApi {

    @Autowired
    private SgBWmsInventoryReportSaveService sgBWmsInventoryReportSaveService;

    @Override
    public ValueHolderV14 apiProcess(String messageBody) {
        ValueHolderV14 holder;
        try {
            holder = sgBWmsInventoryReportSaveService.saveSgBWmsInventoryReport(messageBody);
            log.info("WMS --> sg,oms 回执-库存盘点结果：{}", JSON.toJSONString(holder));
        } catch (Exception e) {
            log.error("出库通知单WMS --> sg,oms 回执异常，error:{}", Throwables.getStackTraceAsString(e));
            throw e;
        }
        return holder;
    }
}
