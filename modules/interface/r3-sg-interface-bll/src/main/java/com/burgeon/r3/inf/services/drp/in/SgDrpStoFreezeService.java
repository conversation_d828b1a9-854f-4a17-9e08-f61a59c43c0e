package com.burgeon.r3.inf.services.drp.in;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.inf.config.SgDrpConfig;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.store.freeze.SgBStoFreeze;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpFreezeItemSaveRequest;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpFreezeSaveRequest;
import com.burgeon.r3.sg.inf.model.result.drp.in.SgDrpFreezeSaveResult;
import com.burgeon.r3.sg.store.mapper.freeze.SgBStoFreezeMapper;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeSaveItemRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeSaveRequest;
import com.burgeon.r3.sg.store.services.freeze.SgBStoFreezeSaveAndSubmitService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.utils.AssertUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/6/21 11:09
 * DRP-逻辑冻结单服务
 */
@Slf4j
@Component
public class SgDrpStoFreezeService {

    @Autowired
    private SgBStoFreezeSaveAndSubmitService saveAndSubmitService;

    @Autowired
    private SgBStoFreezeMapper mapper;
    @Autowired
    private SgDrpConfig sgDrpConfig;

    /**
     * 逻辑冻结单保存
     */
    public ValueHolderV14<SgDrpFreezeSaveResult> saveDrpFreeze(SgDrpFreezeSaveRequest request) {
        if (!sgDrpConfig.getEnableDrpSend()) {
            return new ValueHolderV14<>(ResultCode.FAIL, "中台系统未开放,请稍后再试!");
        }

        log.info("Start SgDrpStoFreezeService.saveDrpFreeze param={}", JSONObject.toJSONString(request));

        String lockKsy =
                SgConstants.INTERFACE_DRP + ":" + SgConstants.SG_B_STO_FREEZE + ":" + request.getSourceBillNo();
        SgRedisLockUtils.lock(lockKsy);
        try {
            //来源单号是否已存在
            Integer count = mapper.selectCount(new LambdaQueryWrapper<SgBStoFreeze>()
                    .eq(SgBStoFreeze::getBillNo, request.getBillNo())
                    .eq(SgBStoFreeze::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (count > 0) {
                return new ValueHolderV14<>(ResultCode.SUCCESS, "当前单据中台已存在,请勿重复请求!");
            }
            checkParams(request);
            CpCStore cpStore = CommonCacheValUtils.getStoreInfoByEcode(request.getCpCStoreEcode());
            AssertUtils.notNull(cpStore, "逻辑仓不存在");
            SgDrpFreezeSaveResult result = new SgDrpFreezeSaveResult();
            SgBStoFreezeBillSaveRequest billSaveRequest = new SgBStoFreezeBillSaveRequest();
            SgBStoFreezeSaveRequest saveRequest = new SgBStoFreezeSaveRequest();
            BeanUtils.copyProperties(request, saveRequest);
            saveRequest.setCpCStoreId(cpStore.getId());
            saveRequest.setCpCStoreEcode(cpStore.getEcode());
            saveRequest.setCpCStoreEname(cpStore.getEname());
            saveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_DRP_FREEZE);
            saveRequest.setBillDate(DateUtil.stringToDate(request.getBillDate()));
            saveRequest.setDrpStatus("2");
            billSaveRequest.setLoginUser(DrpUtils.getUser());
            billSaveRequest.setObjId(-1L);
            List<SgBStoFreezeSaveItemRequest> saveItemRequests = new ArrayList<>();
            for (SgDrpFreezeItemSaveRequest item : request.getItems()) {
                SgBStoFreezeSaveItemRequest itemRequest = new SgBStoFreezeSaveItemRequest();
                BeanUtils.copyProperties(item, itemRequest);
                itemRequest.setId(-1L);
                saveItemRequests.add(itemRequest);
            }
            billSaveRequest.setFreezeSaveRequest(saveRequest);
            billSaveRequest.setSgBStoFreezeSaveItemRequests(saveItemRequests);

            ValueHolderV14<SgR3BaseResult> v14 = saveAndSubmitService.saveAndSubmit(billSaveRequest);
            if (v14.isOK()) {
                result.setBillNo(v14.getData().getBillNo());
                return new ValueHolderV14<>(result, ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
            } else {
                return new ValueHolderV14<>(result, ResultCode.FAIL, v14.getMessage());
            }
        } catch (Exception e) {
            log.error("SgDrpStoFreezeService.saveDrpFreeze. exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
    }

    /**
     * 参数检验
     */
    private void checkParams(SgDrpFreezeSaveRequest request) {
        String sourceBillNo = request.getSourceBillNo();
        String cpStoreEcode = request.getCpCStoreEcode();
        List<SgDrpFreezeItemSaveRequest> items = request.getItems();
        AssertUtils.notNull(sourceBillNo, "来源单据不能为空");
        AssertUtils.notNull(cpStoreEcode, "逻辑仓编码不能为空");
        if (CollectionUtils.isEmpty(items)) {
            AssertUtils.logAndThrow("冻结单明细不能为空");
        }
        //条码是否存在
        StringBuilder sb = new StringBuilder();
        BasicPsQueryService psQueryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
        for (SgDrpFreezeItemSaveRequest item : items) {
            SkuInfoQueryRequest skuRequest = new SkuInfoQueryRequest();
            String skuCode = item.getPsCSkuEcode();
            List<String> skuCodes = new ArrayList<>();
            skuCodes.add(skuCode);
            skuRequest.setSkuEcodeList(skuCodes);
            HashMap<String, PsCProSkuResult> skuInfoByEcode = psQueryService.getSkuInfoByEcode(skuRequest);
            PsCProSkuResult data = skuInfoByEcode.get(skuCode);
            if (data == null) {
                sb.append(skuCode + " ");
            }
        }
        if (sb.length() > 0) {
            AssertUtils.logAndThrow(sb.toString() + "条码不存在");
        }
    }
}
