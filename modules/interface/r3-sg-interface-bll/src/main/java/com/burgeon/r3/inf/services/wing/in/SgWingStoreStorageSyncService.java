package com.burgeon.r3.inf.services.wing.in;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.burgeon.r3.sg.basic.config.SgStorageMqConfig;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.customer.SgBCustomerStorageChangeFtp;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.customer.SgBCustomerStorageChangeFtpMapper;
import com.burgeon.r3.sg.store.model.request.customer.SgBWingStorageSyncItemRequest;
import com.burgeon.r3.sg.store.model.request.customer.SgBWingStorageSyncRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.data.basic.model.request.StoreInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/10/29 15:31
 */
@Slf4j
@Component
public class SgWingStoreStorageSyncService {

    @Autowired
    private SgStorageMqConfig mqConfig;
    //    @Autowired
//    private R3MqSendHelper r3MqSendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private SgBCustomerStorageChangeFtpMapper changeFtpMapper;

    /**
     * 获取库存同步参数信息 新增流水   发送mq
     * 2022-02-27
     * ps：改用自己的用的时间戳，wing传过来的不靠谱，
     *
     * @param request
     * @return
     */

    public ValueHolderV14 StoreStorageSync(SgBWingStorageSyncRequest request) {

        ValueHolderV14<String> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

        try {

            User user = R3SystemUserResource.getSystemRootUser();
            checkPram(request);

            Long requestInTime = request.getInTime();
            // 自己获取时间戳
            Long inTime = System.nanoTime();
            List<SgBWingStorageSyncItemRequest> items = request.getItems();

            log.info(" SgWingStoreStorageSyncService StoreStorageSync requestInTime:{} inTime:{} items: {}",
                    requestInTime, inTime, JSONObject.toJSONString(items));

            SgWingStoreStorageSyncService service = ApplicationContextHandle.getBean(SgWingStoreStorageSyncService.class);

            List<String> stores = service.insertAllFtp(items, inTime, user, requestInTime);

            if (log.isDebugEnabled()) {
                log.debug("SgWingStoreStorageSyncService StoreStorageSync stores {};", JSONObject.toJSONString(stores));
            }

            BasicCpQueryService cpQueryService = ApplicationContextHandle.getBean(BasicCpQueryService.class);
            StoreInfoQueryRequest queryRequest = new StoreInfoQueryRequest();
            queryRequest.setEcodes(stores);
            HashMap<String, CpCStore> storeHashMap = cpQueryService.getStoreInfoByEcode(queryRequest);

            for (String store : stores) {
                if (storeHashMap.containsKey(store)) {
                    JSONObject object = new JSONObject();
                    object.put("inTime", inTime);
                    object.put("storeEcode", store);

                    String msgKey = UUID.randomUUID().toString().replaceAll("-", "");
                    String msgbody = object.toJSONString();
//                    String msgId = r3MqSendHelper.sendMessageAsync(mqConfig.getSgDefaultConfigName(),
//                            msgbody,
//                            mqConfig.getSgThirdCallBackTopic(),
//                            SgStoreConstantsIF.MSG_TAG_WING_TO_SG,
//                            msgKey);
                    MqSendResult sendResult = defaultProducerSend.sendTopic(mqConfig.getSgThirdCallBackTopic(),
                            SgStoreConstantsIF.MSG_TAG_WING_TO_SG,
                            msgbody,
                            msgKey);
                    String msgId = sendResult.getMessageId();
                    log.info(" SgWingStoreStorageSyncService sendMsg msgId {} msgbody {} msgkey {}", msgId, msgbody, msgKey);
                } else {
                    log.error(" SgWingStoreStorageSyncService sendMsg error inTime {} 未查询到【{}】仓信息", inTime, store);
                }
            }

        } catch (Exception e) {
            log.error("SgWingStoreStorageSyncService.StoreStorageSync:库存同步接口出现异常！ error:{}", Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrowException("经销商库存同步接口异常", e);
        }
        return v14;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public List<String> insertAllFtp(List<SgBWingStorageSyncItemRequest> items, Long inTime, User user, Long requestInTime) {

        List<List<SgBWingStorageSyncItemRequest>> itemPageList = Lists.partition(items, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);

        Map<String, String> stores = new HashMap<>();

        for (List<SgBWingStorageSyncItemRequest> itemRequests : itemPageList) {

            List<String> storeEcodeList = new ArrayList<>();
            List<String> skuEcodeList = new ArrayList<>();

            for (SgBWingStorageSyncItemRequest itemRequest : itemRequests) {
                storeEcodeList.add(itemRequest.getCpCStoreEcode());
                skuEcodeList.add(itemRequest.getPsCSkuEcode());
            }

            List<SgBCustomerStorageChangeFtp> sgCustomerStorageChangeFtps = changeFtpMapper.selectList(new LambdaQueryWrapper<SgBCustomerStorageChangeFtp>()
                    .in(SgBCustomerStorageChangeFtp::getCpCStoreEcode, storeEcodeList)
                    .eq(SgBCustomerStorageChangeFtp::getVersion, inTime)
                    .in(SgBCustomerStorageChangeFtp::getPsCSkuEcode, skuEcodeList));

            Map<String, SgBCustomerStorageChangeFtp> ftpMap = new HashMap<>(16);

            if (CollectionUtils.isNotEmpty(sgCustomerStorageChangeFtps)) {
                ftpMap.putAll(sgCustomerStorageChangeFtps.stream().collect(
                        Collectors.toMap(k -> k.getPsCSkuEcode() + "_" + k.getVersion() + "_" + k.getCpCStoreEcode(),
                                v -> v, (v1, v2) -> v1)));
            }

            List<SgBCustomerStorageChangeFtp> changeFtps = new ArrayList<>();
            //批量获取id
            Long[] sequence = ModelUtil.getSequence(SgConstants.SG_B_CUSTOMER_STORAGE_CHANGE_FTP, itemRequests.size());
            JSONArray error = new JSONArray();

            for (int i = 0; i < itemRequests.size(); i++) {
                SgBWingStorageSyncItemRequest itemRequest = itemRequests.get(i);

                if (MapUtils.isNotEmpty(ftpMap)) {
                    if (ftpMap.containsKey(itemRequest.getPsCSkuEcode() + "_" + inTime + "_" + itemRequest.getCpCStoreEcode())) {
                        error.add(ftpMap.get(itemRequest.getPsCSkuEcode() + "_" + inTime + "_" + itemRequest.getCpCStoreEcode()));
                        continue;
                    }
                }

                SgBCustomerStorageChangeFtp changeFtp = new SgBCustomerStorageChangeFtp();
                String cpCStoreEcode = itemRequest.getCpCStoreEcode();
                changeFtp.setId(sequence[i]);
                changeFtp.setVersion(inTime);
                changeFtp.setPsCSkuEcode(itemRequest.getPsCSkuEcode());
                changeFtp.setCpCStoreEcode(cpCStoreEcode);
                changeFtp.setQtyStorage(itemRequest.getQtyStorage());
                StorageUtils.setBModelDefalutData(changeFtp, user);
                // wing 传的时间戳
                changeFtp.setReserveBigint10(requestInTime);
                changeFtps.add(changeFtp);

                stores.put(cpCStoreEcode, cpCStoreEcode);
            }

            if (error.size() > 0) {
                log.info("增库存同步流水 存在相同数据！ftpMap:{}", JSONObject.toJSONString(error));
            }

            if (CollectionUtils.isNotEmpty(changeFtps)) {
                int count = changeFtpMapper.batchInsert(changeFtps);
                if (changeFtps.size() != count) {
                    AssertUtils.logAndThrow("新增库存同步流水异常");
                }
            }


        }

        return new ArrayList<>(stores.values());
    }

    private void checkPram(SgBWingStorageSyncRequest syncRequest) {
        AssertUtils.notNull(syncRequest, "请求参数为空");
        Long inTime = syncRequest.getInTime();
        AssertUtils.notNull(inTime, "变动日期不能为空");

        List<SgBWingStorageSyncItemRequest> items = syncRequest.getItems();
        if (CollectionUtils.isEmpty(items)) {
            AssertUtils.logAndThrow("明细参数不能为空");
        }
    }
}
