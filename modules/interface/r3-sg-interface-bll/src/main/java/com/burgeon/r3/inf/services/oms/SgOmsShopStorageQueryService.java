package com.burgeon.r3.inf.services.oms;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.config.SgStorageControlConfig;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.basic.mapper.SgCSharePoolItemMapper;
import com.burgeon.r3.sg.basic.model.SgStorageRedisQueryLsModel;
import com.burgeon.r3.sg.basic.model.request.*;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQueryResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySaResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySsResult;
import com.burgeon.r3.sg.basic.model.result.SgSumStorageQueryResult;
import com.burgeon.r3.sg.basic.services.SgCStoreQueryService;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategyMapper;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductQueryRequest;
import com.burgeon.r3.sg.channel.model.request.strategy.SgCChannelSkuStrategyQueryInfoRequest;
import com.burgeon.r3.sg.channel.model.result.appoint.SgCChannelShopAppointWarehouseQueryResult;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelRatioStrategyQueryInfoResult;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelSkuStrategyQuerySaInfoResult;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelSkuStrategyQuerySpInfoResult;
import com.burgeon.r3.sg.channel.services.appoint.SgCChannelShopAppointWarehouseService;
import com.burgeon.r3.sg.channel.services.product.SgChannelProductQueryService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgBChannelFixedStorage;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCSharePoolItem;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.channel.appoint.SgCChannelShopAppointWarehouseAppointItem;
import com.burgeon.r3.sg.core.model.table.channel.appoint.SgCChannelShopAppointWarehouseExcludeItem;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShopStorageQueryRequest;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsShopStorageQueryItemResult;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsShopStorageQueryResult;
import com.burgeon.r3.sg.sourcing.model.request.syncgradientstrategy.SgCSyncGradientStrategyByQtyQueryRequest;
import com.burgeon.r3.sg.sourcing.model.result.syncgradientstrategy.SgCSyncGradientStrategyByQtyQueryResult;
import com.burgeon.r3.sg.sourcing.services.syncgradientstrategy.SgCSyncGradientStrategyService;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Component
public class SgOmsShopStorageQueryService {

    @Autowired
    private SgChannelProductQueryService productQueryService;

    @Autowired
    private SgStorageQueryService sgStorageQueryService;

    @Autowired
    private SgCSyncGradientStrategyService sgCSyncGradientStrategyService;

    @Autowired
    private SgCStoreQueryService sgCStoreQueryService;

    @Autowired
    private CpCStoreMapper cpCStoreMapper;

    @Autowired
    private SgCChannelRatioStrategyMapper ratioStrategyMapper;

    @Autowired
    private SgCChannelSkuStrategyMapper skuStrategyMapper;

    @Autowired
    private SgCSaStoreMapper sgCSaStoreMapper;

    @Autowired
    private SgCSharePoolItemMapper sgCSharePoolItemMapper;

    @Autowired
    private SgStorageControlConfig sgStorageControlConfig;

    @Autowired
    private SgCChannelShopAppointWarehouseService appointWarehouseQueryService;

    /**
     * 查询平台店铺可用库存
     *
     * @param request
     * @param loginUser
     * @return
     */
    public ValueHolderV14<SgOmsShopStorageQueryResult> queryOmsShopStorage(SgOmsShopStorageQueryRequest request,
                                                                           User loginUser) {

        log.info("Start SgOmsShopStorageQueryService.queryOmsShopStorage. ReceiveParams:request={};",
                JSONObject.toJSONString(request));

        ValueHolderV14<SgOmsShopStorageQueryResult> holder = new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        ValueHolderV14<List<SgStorageRedisQuerySaResult>> saStorageHolder = null;
        ValueHolderV14<List<SgStorageRedisQuerySsResult>> ssStorageHolder = null;
        ValueHolderV14<List<SgBChannelFixedStorage>> cfStorageHolder = null;
        SgOmsShopStorageQueryResult queryResult = new SgOmsShopStorageQueryResult();
        Map<Long, SgOmsShopStorageQueryItemResult> psSkuItemResultMap = new HashMap<>(16);
        Map<String, SgOmsShopStorageQueryItemResult> skuItemResultMap = new HashMap<>(16);
        Date systemData = new Date();

        //检查入参
        holder = checkServiceParam(request, loginUser);

        if (ResultCode.FAIL == holder.getCode()) {
            log.warn("SgOmsShopStorageQueryService.queryOmsShopStorage. checkServiceParam error:{};",
                    holder.getMessage());
            return holder;
        }

        try {

            //库存同步标志位
            Boolean isStockSync = Boolean.FALSE;
            if (!CollectionUtils.isEmpty(request.getSkuIdList())) {
                isStockSync = Boolean.TRUE;
            }

            SgStorageQuerySaRequest saQueryRequest = null;
            SgStorageQueryCfRequest cfQueryRequest = null;
            SgStorageQuerySsRequest ssQueryRequest = null;
            //普通比例同步策略 <配销仓ID:比例策略信息>
            Map<Long, SgCChannelRatioStrategyQueryInfoResult> ratioStrategyMap = new HashMap<>(16);
//            //特殊条码比例同步策略（配销仓） <配销仓ID_平台条码ID:特殊条码比例>
//            Map<String, BigDecimal> ratioSkuStrategySaMap = new HashMap<>(16);
//            //特殊条码比例同步策略（共享池） <平台条码ID:特殊条码比例>
//            Map<String, BigDecimal> ratioSkuStrategySpMap = new HashMap<>(16);
            Map<Long, BigDecimal> ratioStrategy = null;
            List skuIds = null;
            //普通配销仓比例数据集 <配销仓ID:比例>
            Map<Long, BigDecimal> normalSaRatioMap = new HashMap<>(16);
            //活动配销仓比例数据集 <配销仓ID:比例>
            Map<Long, BigDecimal> activitySaRatioMap = new HashMap<>(16);
            //所有配销仓比例数据集 <配销仓ID:比例>
            Map<Long, BigDecimal> allSaRatioMap = new HashMap<>(16);
            //渠道锁定库存 <平台条码ID:<配销仓ID:库存可用量>>
            Map<String, Map<Long, BigDecimal>> cfStorageMap = new HashMap<>(16);
            //比例同步策略共享池信息 <聚合仓ID:比例>
            Map<Long, BigDecimal> poolFromSaRatioMap = null;
            //渠道锁定库存 <配销仓ID:库存可用量>
            Map<Long, BigDecimal> cfToSaStorageMap = null;
            //配销仓与聚合仓关系集 <配销仓ID:聚合仓ID>
            Map<Long, Long> saToShareMap = new HashMap<>(16);
            //配销仓ID列表
            List saStoreIds = new ArrayList();
            List<Long> shareToStoreList = null;
            //平台条码与商品条码关系集 <平台条码ID:商品条码ID>
            Map<String, String> allSkuMap = new HashMap<>(16);
            Map<String, SgBChannelProduct> channelProductMap = new HashMap<>(16);

            if (!isStockSync) {
                allSkuMap = request.getPsCSkuIdList().stream().collect(
                        Collectors.toMap(k -> String.valueOf(k), k -> String.valueOf(k), (oldVal, newVal) -> oldVal));
            }

            //获取比例同步策略
            List<SgCChannelRatioStrategyQueryInfoResult> strategyInfo =
                    ratioStrategyMapper.queryRatioStrategyInfoByShopId2(request.getCpCShopId());

            if (CollectionUtils.isEmpty(strategyInfo)) {
                holder.setCode(ResultCode.FAIL);
                holder.setMessage(Resources.getMessage("比例同步策略为空！", loginUser.getLocale(),
                        request.getCpCShopId()));
                return holder;
            } else {

                if (strategyInfo.get(0).getSgCSaStoreId() != null) {
                    //设置配销仓比例同步策略信息 配销仓->结果对象(比例)
                    ratioStrategyMap = strategyInfo.stream().collect(Collectors.toMap(o -> o.getSgCSaStoreId(), Function.identity()));
                }
            }

            log.info("SgOmsShopStorageQueryService.queryOmsShopStorage. queryRatioStrategyInfo ratioStrategyMap:{};",
                    JSONObject.toJSONString(ratioStrategyMap));

            //获取共享池策略信息
            Long sgCSharePoolId = strategyInfo.get(0).getSgCSharePoolId();
            BigDecimal poolRatio = strategyInfo.get(0).getSgCSharePoolRatio();

            if (log.isDebugEnabled()) {
                log.debug("SgOmsShopStorageQueryService.queryOmsShopStorage. queryRatioStrategyInfo sgCSharePoolId:{},poolRatio:{};",
                        sgCSharePoolId, poolRatio);
            }

            //普通、活动配销仓比例拆解
            for (SgCChannelRatioStrategyQueryInfoResult item : ratioStrategyMap.values()) {

                if (SgConstants.SA_STORE_TYPE_NORMAL.equals(item.getType())) {
                    normalSaRatioMap.put(item.getSgCSaStoreId(), item.getRatio());
                } else if (SgConstants.SA_STORE_TYPE_ACTIVITY.equals(item.getType())) {
                    activitySaRatioMap.put(item.getSgCSaStoreId(), item.getRatio());
                } else {
                    log.warn("SgOmsShopStorageQueryService.queryOmsShopStorage. SaStoreType_is_empty! SgCSaStoreId:{};",
                            item.getSgCSaStoreId());
                }

                allSaRatioMap.put(item.getSgCSaStoreId(), item.getRatio());

                //添加配销仓列表
                saStoreIds.add(item.getSgCSaStoreId());

            }

            //库存同步调用的情况下
            if (isStockSync) {

                //获取平台店铺商品信息
                SgChannelProductQueryRequest queryRequest = new SgChannelProductQueryRequest();
                queryRequest.setCpCShopId(request.getCpCShopId());
                queryRequest.setSkuIdList(request.getSkuIdList());
                ValueHolderV14<List<SgBChannelProduct>> subHolder = productQueryService.queryChannelProduct(queryRequest);

                if (log.isDebugEnabled()) {
                    log.debug("SgOmsShopStorageQueryService.queryOmsShopStorage. queryChannelProduct List<SgBChannelProduct>:{};",
                            JSONObject.toJSONString(subHolder.getData()));
                }

                if (ResultCode.FAIL == subHolder.getCode() || CollectionUtils.isEmpty(subHolder.getData())) {
                    holder.setCode(ResultCode.FAIL);
                    holder.setMessage(Resources.getMessage("平台商品信息为空！", loginUser.getLocale()));
                    return holder;
                }

                if (subHolder.getData().size() != request.getSkuIdList().size()) {
                    log.warn("SgOmsShopStorageQueryService.queryOmsShopStorage. 存在获取平台店铺商品失败的平台条码! skuIdList:{};",
                            request.getSkuIdList());
                    holder.setCode(ResultCode.FAIL);
                    holder.setMessage(Resources.getMessage("存在获取平台店铺商品失败的平台条码！", loginUser.getLocale()));
                    return holder;
                }

                allSkuMap = subHolder.getData().stream().collect(
                        Collectors.toMap(k -> k.getSkuId(), k -> String.valueOf(k.getPsCSkuId()), (oldVal, newVal) -> oldVal));

                channelProductMap = subHolder.getData().stream().collect(Collectors.toMap(o -> o.getSkuId(), Function.identity()));

                //获取渠道锁定库存数量
                cfQueryRequest = new SgStorageQueryCfRequest();
                List shopIds = new ArrayList();
                shopIds.add(request.getCpCShopId());
                cfQueryRequest.setCpCShopIds(shopIds);
                cfQueryRequest.setSkuIds(request.getSkuIdList());
                cfStorageHolder = sgStorageQueryService.queryCfStorage(cfQueryRequest, loginUser);

                //设置渠道锁定库存量 SKU->{SA,可用量}
                if (ResultCode.SUCCESS == cfStorageHolder.getCode() && !CollectionUtils.isEmpty(cfStorageHolder.getData())) {
                    for (SgBChannelFixedStorage cfResult : cfStorageHolder.getData()) {
                        //添加配销仓列表
                        saStoreIds.add(cfResult.getSgCSaStoreId());

                        if (cfStorageMap.containsKey(cfResult.getSkuId())) {
                            cfStorageMap.get(cfResult.getSkuId()).put(cfResult.getSgCSaStoreId(), cfResult.getQtyAvailable());
                        } else {
                            cfToSaStorageMap = new HashMap<>(16);
                            cfToSaStorageMap.put(cfResult.getSgCSaStoreId(), cfResult.getQtyAvailable());
                            cfStorageMap.put(cfResult.getSkuId(), cfToSaStorageMap);
                        }
                    }
                }

                if (log.isDebugEnabled()) {
                    log.debug("SgOmsShopStorageQueryService.queryOmsShopStorage. queryCfStorage cfStorageMap:{};",
                            JSONObject.toJSONString(cfStorageMap));
                }
            }

            //获取配销仓所属聚合仓信息
            if (!CollectionUtils.isEmpty(saStoreIds)) {
                List<SgCSaStore> saStoreList = sgCSaStoreMapper.selectList(
                        new QueryWrapper<SgCSaStore>().lambda()
                                .in(!CollectionUtils.isEmpty(saStoreIds), SgCSaStore::getId, saStoreIds)
                                .eq(SgCSaStore::getIsactive, SgConstants.IS_ACTIVE_Y));

                if (!CollectionUtils.isEmpty(saStoreList)) {
                    //设置配销仓所属聚合仓信息 配销仓->聚合仓
                    saToShareMap = saStoreList.stream().collect(
                            Collectors.toMap(SgCSaStore::getId, SgCSaStore::getSgCShareStoreId, (oldVal, newVal) -> oldVal));
                }
            }

            if (log.isDebugEnabled()) {
                log.debug("SgOmsShopStorageQueryService.queryOmsShopStorage. sgCSaStoreMapper.selectList saToShareMap:{};",
                        JSONObject.toJSONString(saToShareMap));
            }

            //获取比例同步策略共享池信息
            if (sgCSharePoolId != null) {
                List<SgCSharePoolItem> sharePoolItemList = sgCSharePoolItemMapper.selectList(
                        new QueryWrapper<SgCSharePoolItem>().lambda()
                                .eq(SgCSharePoolItem::getSgCSharePoolId, sgCSharePoolId)
                                .eq(SgCSharePoolItem::getIsactive, SgConstants.IS_ACTIVE_Y));

                //设置比例同步策略共享池信息 聚合仓->比例
                if (!CollectionUtils.isEmpty(sharePoolItemList)) {
                    poolFromSaRatioMap = sharePoolItemList.stream().collect(
                            Collectors.toMap(SgCSharePoolItem::getSgCShareStoreId, SgCSharePoolItem::getRatio, (oldVal, newVal) -> oldVal));
                }

            }

            if (log.isDebugEnabled()) {
                log.debug("SgOmsShopStorageQueryService.queryOmsShopStorage. sgCSharePoolItemMapper.selectList poolFromSaRatioMap:{};",
                        JSONObject.toJSONString(poolFromSaRatioMap));
            }

            //需返回实体仓维度库存的情况下，获取指定聚合仓下逻辑仓信息
            if (request.getIncludePhyStorage() && request.getSgCShareStoreId() != null) {

                SgCShareStoreQueryRequest sgCShareStoreQueryRequest = new SgCShareStoreQueryRequest();
                List sgCShareStoreIds = new ArrayList();
                sgCShareStoreIds.add(request.getSgCShareStoreId());
                sgCShareStoreQueryRequest.setSgCShareStoreIds(sgCShareStoreIds);
                //聚合仓与逻辑仓关系集 <聚合仓ID:对应逻辑仓信息>
                ValueHolderV14<Map<Long, List<SgCpCStore>>> storeHolder = sgCStoreQueryService.getCpCStoreByShareId(sgCShareStoreQueryRequest,
                        loginUser);

                if (ResultCode.FAIL == storeHolder.getCode() || MapUtils.isEmpty(storeHolder.getData())) {
                    log.warn("SgOmsShopStorageQueryService.queryOmsShopStorage. getCpCStoreByShareId_is_fail! SgCShareStoreId:{};",
                            request.getSgCShareStoreId());
                    holder.setCode(ResultCode.FAIL);
                    holder.setMessage(Resources.getMessage("该聚合仓下未找到满足条件的逻辑仓！", loginUser.getLocale(),
                            request.getSgCShareStoreId()));
                    return holder;

                } else {
                    shareToStoreList = storeHolder.getData().get(request.getSgCShareStoreId()).stream().map(
                            SgCpCStore::getId).collect(Collectors.toList());
                }
            }

            if (log.isDebugEnabled()) {
                log.debug("SgOmsShopStorageQueryService.queryOmsShopStorage. getCpCStoreByShareId shareToStoreList:{};",
                        JSONObject.toJSONString(shareToStoreList));
            }

            for (String skuId : allSkuMap.keySet()) {

                Long psCSkudId = Long.valueOf(allSkuMap.get(skuId));

                if (isStockSync) {

                    if (channelProductMap.containsKey(skuId)) {
                        //获取当前条码对应的配销仓比例同步策略信息
                        if (SgConstants.SA_STORE_TYPE_NORMAL.equals(channelProductMap.get(skuId).getSaStoreType())) {
                            ratioStrategy = normalSaRatioMap;
                        } else {
                            ratioStrategy = activitySaRatioMap;
                        }
                    } else {
                        log.warn("SgOmsShopStorageQueryService.queryOmsShopStorage skuId_info_is_empty skuId:{}",
                                skuId);
                        continue;
                    }
                } else {
                    ratioStrategy = allSaRatioMap;
                }

                log.info("SgOmsShopStorageQueryService.queryOmsShopStorage. ratioStrategy:{};",
                        JSONObject.toJSONString(ratioStrategy));

                SgOmsShopStorageQueryItemResult queryItemResult = new SgOmsShopStorageQueryItemResult();
                BigDecimal shopSkuSaStorage = BigDecimal.ZERO;
                //总共享池可用量
                BigDecimal shopSkuPoolStorage = BigDecimal.ZERO;
                BigDecimal calRatio = BigDecimal.ZERO;
                Long shareStoreId = null;
                Map<Long, BigDecimal> shareStorage = null;
                Map<Long, BigDecimal> phyStorage = null;

                if (isStockSync) {

                    //处理当前条码渠道锁定库存量
                    if (cfStorageMap.containsKey(skuId)) {

                        cfToSaStorageMap = cfStorageMap.get(skuId);

                        for (Long saStoreId : cfToSaStorageMap.keySet()) {

                            if (!saToShareMap.containsKey(saStoreId)) {
                                continue;
                            }

                            shareStoreId = saToShareMap.get(saStoreId);
                            shareStorage = queryItemResult.getShareStorageInfo();

                            //设置聚合仓库存量 聚合仓->库存量
                            if (shareStorage.containsKey(shareStoreId)) {
                                shareStorage.put(shareStoreId, shareStorage.get(shareStoreId).add(cfToSaStorageMap.get(saStoreId)));
                            } else {
                                shareStorage.put(shareStoreId, cfToSaStorageMap.get(saStoreId));
                            }
                        }
                    }
                }

                if (log.isDebugEnabled()) {
                    log.debug("SgOmsShopStorageQueryService.queryOmsShopStorage. shareStorage:{};",
                            JSONObject.toJSONString(shareStorage));
                }

                //计算配销仓下按比例可用库存数量
                if (!MapUtils.isEmpty(ratioStrategy)) {

                    //获取当前商品条码在配销仓下的库存
                    saQueryRequest = new SgStorageQuerySaRequest();
                    saQueryRequest.setSgCSaStoreIds(new ArrayList<>(ratioStrategy.keySet()));
                    skuIds = new ArrayList();
                    skuIds.add(psCSkudId);
                    saQueryRequest.setSkuIds(skuIds);
                    saStorageHolder = sgStorageQueryService.querySaStorageWithRedis(saQueryRequest, loginUser);

                    if (ResultCode.FAIL == saStorageHolder.getCode() || CollectionUtils.isEmpty(saStorageHolder.getData())) {
                        log.warn("SgOmsShopStorageQueryService.queryOmsShopStorage. saStorage_is_empty! SgCSaStoreId:{},PsCSkuId:{};",
                                ratioStrategy.keySet(), psCSkudId);
                    } else {

                        log.info("SgOmsShopStorageQueryService.queryOmsShopStorage. querySaStorageWithRedis saStorageHolder:{};",
                                JSONObject.toJSONString(saStorageHolder.getData()));

                        for (SgStorageRedisQuerySaResult saResult : saStorageHolder.getData()) {

                            //渠道锁定库存量中已存在当前商品条码的情况下跳过
                            if (cfStorageMap.containsKey(skuId)) {
                                cfToSaStorageMap = cfStorageMap.get(skuId);
                                if (cfToSaStorageMap.containsKey(saResult.getSgCSaStoreId())) {
                                    continue;
                                }
                            }

                            if (BigDecimal.ZERO.compareTo(saResult.getQtyAvailable()) >= 0) {
                                continue;
                            }

                            BigDecimal saQtyAvailable = saResult.getQtyAvailable();

                            // 20220302 库存同步指定/排除逻辑仓
                            // 查询店铺指定实体仓策略
                            ValueHolderV14<SgCChannelShopAppointWarehouseQueryResult> appointValueHolder = appointWarehouseQueryService.queryShopAppointWarehouseByShop(request.getCpCShopId());
                            if (appointValueHolder.isOK()) {

                                // 查询聚合仓下所有逻辑仓集合
                                shareStoreId = saToShareMap.get(saResult.getSgCSaStoreId());
                                LambdaQueryWrapper<SgCpCStore> storeWrapper = new LambdaQueryWrapper<>();
                                storeWrapper.eq(SgCpCStore::getSgCShareStoreId, shareStoreId);
                                storeWrapper.eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y);
                                List<SgCpCStore> storeList = cpCStoreMapper.selectList(storeWrapper);

                                // 比对策略明细与逻辑仓集合明细是否有交集，无交集，直接跳过
                                SgCChannelShopAppointWarehouseQueryResult data = appointValueHolder.getData();
                                List<Long> appointOrExcludeStoreIds;
                                if (data.getItemType() == SgCChannelShopAppointWarehouseQueryResult.APPOINT_ITEM) {

                                    appointOrExcludeStoreIds = data.getAppointItemList().stream().map(SgCChannelShopAppointWarehouseAppointItem::getCpCStoreId).collect(Collectors.toList());
                                    List<SgCpCStore> appointStoreList = storeList.stream().filter(x -> appointOrExcludeStoreIds.contains(x.getId())).collect(Collectors.toList());

                                    if (CollectionUtils.isNotEmpty(appointStoreList)) {
                                        storeList = appointStoreList;
                                    }
                                } else {
                                    appointOrExcludeStoreIds = data.getExcludeItemList().stream().map(SgCChannelShopAppointWarehouseExcludeItem::getCpCStoreId).collect(Collectors.toList());
                                    storeList = storeList.stream().filter(x -> !appointOrExcludeStoreIds.contains(x.getId())).collect(Collectors.toList());
                                }

                                if (CollectionUtils.isEmpty(storeList)) {
                                    continue;
                                }

                                // 查询逻辑仓库存，与配销仓库存取最小值

                                List<SgStorageRedisQueryLsModel> lsQueryModelList = new ArrayList<>();
                                storeList.forEach(x -> {
                                    SgStorageRedisQueryLsModel model = new SgStorageRedisQueryLsModel();
                                    model.setCpCStoreId(x.getId());
                                    model.setPsCSkuId(psCSkudId);
                                    lsQueryModelList.add(model);
                                });

                                ValueHolderV14<HashMap<String, SgStorageRedisQueryResult>> lsValueHolder = sgStorageQueryService.queryLsStorageWithRedis(lsQueryModelList, loginUser);
                                if (!lsValueHolder.isOK()) {
                                    continue;
                                }
                                BigDecimal lsQtyAvailable = lsValueHolder.getData().values().stream().map(SgStorageRedisQueryResult::getQtyAvailable).reduce(BigDecimal.ZERO, BigDecimal::add);

                                saQtyAvailable = lsQtyAvailable.min(saQtyAvailable);
                            }

                            ValueHolderV14<SgCSyncGradientStrategyByQtyQueryResult> strategyQueryResult = null;

                            //获取配销仓库存梯度策略信息
                            SgCSyncGradientStrategyByQtyQueryRequest strategyRequest = new SgCSyncGradientStrategyByQtyQueryRequest();
                            strategyRequest.setCpCShopId(request.getCpCShopId());
                            strategyRequest.setStoreId(saResult.getSgCSaStoreId());
                            strategyRequest.setType(SgConstants.SYNC_GRADIENT_STRATEGY_SA);
                            strategyRequest.setTargetQty(saQtyAvailable);
                            strategyQueryResult =
                                    sgCSyncGradientStrategyService.querySyncGradientStrategyByQty(strategyRequest, loginUser);

                            if (ResultCode.FAIL == strategyQueryResult.getCode()) {

                                log.warn("SgOmsShopStorageQueryService.queryOmsShopStorage. " +
                                                "querySyncGradientStrategyByQty_is_fail! SgCSaStoreId:{},QtyAvailable:{};",
                                        saResult.getSgCSaStoreId(), saQtyAvailable);

                                continue;
                            }

                            if (log.isDebugEnabled()) {
                                log.debug("SgOmsShopStorageQueryService.queryOmsShopStorage. querySyncGradientStrategyByQty saStorageHolder:{};",
                                        JSONObject.toJSONString(strategyQueryResult.getData()));
                            }

                            if (strategyQueryResult != null && strategyQueryResult.getData() != null) {
                                calRatio = strategyQueryResult.getData().getRatio();
                            } else {
                                calRatio = ratioStrategy.get(saResult.getSgCSaStoreId()) == null ?
                                        BigDecimal.ZERO : ratioStrategy.get(saResult.getSgCSaStoreId());
                            }

                            shopSkuSaStorage = calRatio.multiply(
                                    saQtyAvailable).divide(SgConstants.NUMBER_100, 0, BigDecimal.ROUND_DOWN);

                            log.info("SgOmsShopStorageQueryService.queryOmsShopStorage. saResult:{},calRatio:{},shopSkuSaStorage:{};",
                                    JSONObject.toJSONString(saResult), calRatio, shopSkuSaStorage);

                            if (saToShareMap.containsKey(saResult.getSgCSaStoreId())) {

                                shareStoreId = saToShareMap.get(saResult.getSgCSaStoreId());
                                shareStorage = queryItemResult.getShareStorageInfo();

                                if (shareStorage.containsKey(shareStoreId)) {
                                    shareStorage.put(shareStoreId, shareStorage.get(shareStoreId).add(shopSkuSaStorage));
                                } else {
                                    shareStorage.put(shareStoreId, shopSkuSaStorage);
                                }
                            }
                        }
                    }
                }

                BigDecimal shareAvailable = null;

                //计算共享池可用库存数量
                if (!MapUtils.isEmpty(poolFromSaRatioMap)) {

                    //获取当前商品条码在共享池下的库存
                    ssQueryRequest = new SgStorageQuerySsRequest();
                    ssQueryRequest.setSgCShareStoreIds(new ArrayList<>(poolFromSaRatioMap.keySet()));
                    skuIds = new ArrayList();
                    skuIds.add(psCSkudId);
                    ssQueryRequest.setSkuIds(skuIds);
                    ssStorageHolder = sgStorageQueryService.querySsStorageWithRedis(ssQueryRequest, loginUser);

                    if (ResultCode.FAIL == ssStorageHolder.getCode() || CollectionUtils.isEmpty(ssStorageHolder.getData())) {
                        log.warn("SgOmsShopStorageQueryService.queryOmsShopStorage. ssStorage_is_empty! SgCShareStoreId:{},PsCSkuId:{};",
                                poolFromSaRatioMap.keySet(), psCSkudId);
                    } else {

                        for (SgStorageRedisQuerySsResult ssResult : ssStorageHolder.getData()) {

                            //计算各共享池分配量
                            shareAvailable = ssResult.getQtySpAvailable();
                            calRatio = poolFromSaRatioMap.get(ssResult.getSgCShareStoreId()) == null ?
                                    BigDecimal.ZERO : poolFromSaRatioMap.get(ssResult.getSgCShareStoreId());
                            shareAvailable = shareAvailable.multiply(calRatio).divide(SgConstants.NUMBER_100, 0, BigDecimal.ROUND_DOWN);

                            ssResult.setQtySpAvailable(shareAvailable);

                            //计算共享池总量
                            shopSkuPoolStorage = shopSkuPoolStorage.add(shareAvailable);

                        }

                        if (BigDecimal.ZERO.compareTo(shopSkuPoolStorage) < 0) {

                            if (log.isDebugEnabled()) {
                                log.debug("SgOmsShopStorageQueryService.queryOmsShopStorage. querySsStorageWithRedis ssResult:{},shopSkuPoolStorage:{};",
                                        JSONObject.toJSONString(ssStorageHolder.getData()), shopSkuPoolStorage);
                            }

                            ValueHolderV14<SgCSyncGradientStrategyByQtyQueryResult> strategyQueryResult = null;

                            //获取共享池库存梯度策略信息
                            SgCSyncGradientStrategyByQtyQueryRequest strategyRequest = new SgCSyncGradientStrategyByQtyQueryRequest();
                            strategyRequest.setCpCShopId(request.getCpCShopId());
                            strategyRequest.setStoreId(sgCSharePoolId);
                            strategyRequest.setType(SgConstants.SYNC_GRADIENT_STRATEGY_POOL);
                            strategyRequest.setTargetQty(shopSkuPoolStorage);
                            strategyQueryResult =
                                    sgCSyncGradientStrategyService.querySyncGradientStrategyByQty(strategyRequest, loginUser);

                            if (ResultCode.FAIL == strategyQueryResult.getCode()) {
                                log.warn("SgOmsShopStorageQueryService.queryOmsShopStorage. " +
                                                "querySyncGradientStrategyByQty_is_fail! sgCSharePoolId:{},shopSkuPoolStorage:{};",
                                        sgCSharePoolId, shopSkuPoolStorage);
                            }

                           if (strategyQueryResult != null && strategyQueryResult.getData() != null) {
                                calRatio = strategyQueryResult.getData().getRatio();
                            } else {
                                calRatio = poolRatio == null ? BigDecimal.ZERO : poolRatio;
                            }

                            log.info("SgOmsShopStorageQueryService.queryOmsShopStorage. querySyncGradientStrategyByQty calRatio:{};",
                                    calRatio);

                            for (SgStorageRedisQuerySsResult ssResult : ssStorageHolder.getData()) {

                                shareAvailable = calRatio.multiply(
                                        ssResult.getQtySpAvailable()).divide(SgConstants.NUMBER_100, 0, BigDecimal.ROUND_DOWN);

                                shareStorage = queryItemResult.getShareStorageInfo();

                                if (shareStorage.containsKey(ssResult.getSgCShareStoreId())) {
                                    shareStorage.put(ssResult.getSgCShareStoreId(),
                                            shareStorage.get(ssResult.getSgCShareStoreId()).add(shareAvailable));
                                } else {
                                    shareStorage.put(ssResult.getSgCShareStoreId(), shareAvailable);
                                }
                            }
                        }
                    }
                }

                //计算指定聚合仓情况下，实体仓维度的库存可用量
                if (!isStockSync && request.getIncludePhyStorage() && request.getSgCShareStoreId() != null) {

                    SgStorageQueryRequest storageQueryRequest = new SgStorageQueryRequest();
                    skuIds = new ArrayList();
                    skuIds.add(psCSkudId);
                    storageQueryRequest.setSkuIds(skuIds);
                    storageQueryRequest.setStoreIds(shareToStoreList);
                    ValueHolderV14<List<SgSumStorageQueryResult>> sumStorageHolder =
                            sgStorageQueryService.querySumStorageGrpPhy(storageQueryRequest, loginUser);

                    if (ResultCode.SUCCESS == sumStorageHolder.getCode() && !CollectionUtils.isEmpty(sumStorageHolder.getData())) {

                        log.info("SgOmsShopStorageQueryService.queryOmsShopStorage. querySumStorageGrpPhy sumStorageHolder:{};",
                                JSONObject.toJSONString(sumStorageHolder.getData()));

                        phyStorage = queryItemResult.getPhyStorageInfo();
                        for (SgSumStorageQueryResult sumStorageQueryResult : sumStorageHolder.getData()) {
                            phyStorage.put(sumStorageQueryResult.getCpCPhyWarehouseId(),
                                    sumStorageQueryResult.getQtyAvailable());
                        }
                    }
                }

                if (isStockSync) {
                    skuItemResultMap.put(skuId, queryItemResult);
                } else {
                    psSkuItemResultMap.put(psCSkudId, queryItemResult);
                }

            }

            queryResult.setSkuStorageResult(skuItemResultMap);
            queryResult.setPsSkuStorageResult(psSkuItemResultMap);

            holder.setData(queryResult);

            log.info("Finish SgOmsShopStorageQueryService.queryOmsShopStorage. ReturnResult:holder={};",
                    JSONObject.toJSONString(holder));

        } catch (Exception e) {
            log.error("SgOmsShopStorageQueryService.queryOmsShopStorage. exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("查询平台店铺库存信息发生异常！", loginUser.getLocale()));
        }

        return holder;
    }

    /**
     * @param request
     * @return
     */
    private ValueHolderV14<SgOmsShopStorageQueryResult> checkServiceParam(SgOmsShopStorageQueryRequest request,
                                                                          User loginUser) {

        ValueHolderV14<SgOmsShopStorageQueryResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

        if (request == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("请求体为空！"));
            return holder;
        }

        if (loginUser == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("操作用户信息不能为空！"));
            return holder;
        }

        if (request.getCpCShopId() == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("平台店铺不能为空！", loginUser.getLocale()));
        }

        if (CollectionUtils.isEmpty(request.getPsCSkuIdList()) && CollectionUtils.isEmpty(request.getSkuIdList())) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("商品条码，平台条码不能同时为空！", loginUser.getLocale())
                    .concat(" " + holder.getMessage()));
        }

        if (!CollectionUtils.isEmpty(request.getPsCSkuIdList()) &&
                request.getPsCSkuIdList().size() > sgStorageControlConfig.getMaxQueryLimit()) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("商品条码的查询件数超过最大限制！", loginUser.getLocale())
                    .concat(" " + holder.getMessage()));
        }

        if (!CollectionUtils.isEmpty(request.getSkuIdList()) &&
                request.getSkuIdList().size() > sgStorageControlConfig.getMaxQueryLimit()) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("平台条码的查询件数超过最大限制！", loginUser.getLocale())
                    .concat(" " + holder.getMessage()));
        }

        if (request.getIncludePhyStorage() && request.getSgCShareStoreId() == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("查询实体仓库存的情况下需要指定聚合仓信息！", loginUser.getLocale())
                    .concat(" " + holder.getMessage()));
        }

        return holder;

    }

}
