package com.burgeon.r3.inf.filter;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.wms.SgCTransferArrivalDaysConfig;
import com.burgeon.r3.sg.store.mapper.transfer.SgCTransferArrivalDaysConfigMapper;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleFilter;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 调拨时效配置
 *
 * <AUTHOR>
 * @since 2023-01-31 10:47
 */
@Slf4j
@Component
public class SgCTransferArrivalDaysConfigSaveFilter extends BaseSingleFilter<SgCTransferArrivalDaysConfig> {
    @Autowired
    private SgCTransferArrivalDaysConfigMapper sgCTransferArrivalDaysConfigMapper;

    @Override
    public String getFilterMsgName() {
        return "调拨时效配置";
    }

    @Override
    public Class<?> getFilterClass() {
        return SgCTransferArrivalDaysConfigSaveFilter.class;
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgCTransferArrivalDaysConfig mainObject, User loginUser) {
        log.info(LogUtil.format("SgCTransferArrivalDaysConfigSaveFilter.execBeforeMainTable,request:{}"
                , "SgCTransferArrivalDaysConfigSaveFilter.execBeforeMainTable"), JSON.toJSONString(mainObject));
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("保存成功", loginUser.getLocale()));

        if (mainObject.getId() != null && mainObject.getId() < 0) {
            //新增
            if (mainObject.getEstimateNumber() < 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("预估时间不能小于0！");
                return v14;
            }

            int exists = sgCTransferArrivalDaysConfigMapper.selectCount(new QueryWrapper<SgCTransferArrivalDaysConfig>().lambda()
                    .eq(SgCTransferArrivalDaysConfig::getSendStoreId, mainObject.getSendStoreId())
                    .eq(SgCTransferArrivalDaysConfig::getReceiveStoreId, mainObject.getReceiveStoreId())
                    .eq(SgCTransferArrivalDaysConfig::getTransportType, mainObject.getTransportType())
                    .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (exists > 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("已存在相同的时效配置(发出仓+接收仓+运输类型)！");
                return v14;
            }
        }

        // 更新
        if (mainObject.getId() != null && mainObject.getId() > 0) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("禁止更新-可作废后重新添加");
            return v14;
        }

        return v14;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgCTransferArrivalDaysConfig mainObject, User loginUser) {
        return null;
    }
}
