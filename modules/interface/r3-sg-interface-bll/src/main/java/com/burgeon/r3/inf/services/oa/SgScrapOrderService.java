package com.burgeon.r3.inf.services.oa;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.request.SgBFreezeStorageQueryRequest;
import com.burgeon.r3.sg.basic.services.SgBFreezeStorageQueryService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgBFreezeStorage;
import com.burgeon.r3.sg.core.model.table.store.adjust.SgBStoAdjust;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.inf.common.enums.StoreTypeEnum;
import com.burgeon.r3.sg.inf.model.request.oa.SgScrapOrderItemModel;
import com.burgeon.r3.sg.inf.model.request.oa.SgScrapOrderModel;
import com.burgeon.r3.sg.store.mapper.adjust.SgBStoAdjustMapper;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustMainSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustSaveRequest;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSaveService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-08-17 15:53
 * @Description: oa报废单下发生成库存调整单
 */

@Slf4j
@Component
public class SgScrapOrderService {

    @Autowired
    private SgBStoAdjustMapper adjustMapper;
    @Autowired
    private SgBFreezeStorageQueryService sgBFreezeStorageQueryService;
    @Autowired
    private SgBStoAdjustSaveService sgBStoAdjustSaveService;

    /**
     * oa报废单下发生成库存调整单
     *
     * @param model 入参
     * @return ValueHolderV14
     */
    public ValueHolderV14 callScrapOrder(SgScrapOrderModel model) {

        log.info(LogUtil.format("SgScrapOrderService.checkFreezeQty callScrapOrder:{} ", "SgScrapOrderService.callScrapOrder"),
                JSONObject.toJSONString(model));

        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "OA下发报废单处理成功！");
        try {
            String error = checkModel(model);
            if (StringUtils.isNotEmpty(error)) {
                v14.setMessage(error);
                v14.setCode(ResultCode.FAIL);
                return v14;
            }
            //生成库存调整单
            List<SgBStoAdjustSaveRequest> saveRequests = buildAdjust(model);
            if (CollectionUtils.isNotEmpty(saveRequests)) {
                batchSaveAdjust(saveRequests);
            }
        } catch (Exception e) {
            log.error("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("OA下发报废单处理异常：" + e.getMessage());
        }

        log.info(LogUtil.format("SgScrapOrderService.checkFreezeQty ValueHolderV14:{} ",
                "SgScrapOrderService.ValueHolderV14"),
                JSONObject.toJSONString(v14));

        return v14;
    }

    /**
     * 新增库存调整单
     *
     * @param saveRequests 入参
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveAdjust(List<SgBStoAdjustSaveRequest> saveRequests) {
        for (SgBStoAdjustSaveRequest saveRequest : saveRequests) {
            log.info(LogUtil.format("SgScrapOrderService batchSaveAdjust.save:{}",
                    "SgScrapOrderService.batchSaveAdjust.save"), JSONObject.toJSONString(saveRequest));
            ValueHolderV14<SgR3BaseResult> save = sgBStoAdjustSaveService.save(saveRequest);
            log.info(LogUtil.format("SgScrapOrderService batchSaveAdjust.ValueHolderV14:{}",
                    "SgScrapOrderService.batchSaveAdjust.ValueHolderV14"), JSONObject.toJSONString(save));
            AssertUtils.cannot(!save.isOK(), save.getMessage());
        }

    }

    /**
     * 构建库存调整单数据
     * 因为参数【仓库编码】、【报废性质】（报废性质对应库存调整单的“调整性质”）在明细表中，
     * 故在生成库存调整单时需要做转换，将同【仓库编码】、【报废性质】的商品生成一笔库存调整单
     *
     * @param model 入参
     * @return SgBStoAdjustSaveRequest
     */
    private List<SgBStoAdjustSaveRequest> buildAdjust(SgScrapOrderModel model) {

        List<SgBStoAdjustSaveRequest> saveRequests = new ArrayList<>();

        List<SgScrapOrderItemModel> scrapOrderItemModels = model.getScrapOrderItemModels();
        //仓库编码-报废性质 分组
        Map<String, List<SgScrapOrderItemModel>> itemModelMap = scrapOrderItemModels.stream().collect(
                Collectors.groupingBy(o -> o.getWerehousecode() + SgConstantsIF.MAP_KEY_DIVIDER + o.getBfxz()));

        for (String key : itemModelMap.keySet()) {
            String[] split = key.split(SgConstantsIF.MAP_KEY_DIVIDER);
            List<SgScrapOrderItemModel> sgScrapOrderItemModels = itemModelMap.get(key);

            SgBStoAdjustSaveRequest adjustSaveRequest = new SgBStoAdjustSaveRequest();
            SgBStoAdjustMainSaveRequest mainRequest = new SgBStoAdjustMainSaveRequest();
            //调整日期
            mainRequest.setBillDate(new Date());
            //库存日期
            mainRequest.setStockDate(model.getStockDate());
            //单据类型
            mainRequest.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL);
            CpCStore storeInfoByEcode = CommonCacheValUtils.getStoreInfoByEcode(split[0]);
            //逻辑仓
            if (storeInfoByEcode != null) {
                mainRequest.setCpCStoreId(storeInfoByEcode.getId());
                mainRequest.setCpCStoreEcode(storeInfoByEcode.getEcode());
                mainRequest.setCpCStoreEname(storeInfoByEcode.getEname());
            }
            //调整性质
            mainRequest.setSgBAdjustPropId(StoreTypeEnum.getAdjustPropId(split[1]));
            //来源单据信息
            mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_SCRAP);
            mainRequest.setSourceBillNo(model.getLcbh());
            mainRequest.setRemark("由OA报废单，OA报废单号[" + model.getLcbh() + "]传入生成");

            adjustSaveRequest.setObjId(-1L);
            adjustSaveRequest.setMainRequest(mainRequest);
            adjustSaveRequest.setItems(buildAdjustItem(sgScrapOrderItemModels));
            adjustSaveRequest.setR3(Boolean.FALSE);
            adjustSaveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());
            saveRequests.add(adjustSaveRequest);
        }

        return saveRequests;
    }


    /**
     * 构建库存调整单明细
     *
     * @param sgScrapOrderItemModels 入参
     * @return SgBStoAdjustItemSaveRequest
     */
    private List<SgBStoAdjustItemSaveRequest> buildAdjustItem(List<SgScrapOrderItemModel> sgScrapOrderItemModels) {
        List<SgBStoAdjustItemSaveRequest> itemRequests = new ArrayList<>();

        sgScrapOrderItemModels.forEach(task -> {
            SgBStoAdjustItemSaveRequest adjustItemRequest = new SgBStoAdjustItemSaveRequest();
            adjustItemRequest.setId(-1L);
            adjustItemRequest.setQty(task.getPlanqty());
            adjustItemRequest.setPsCSkuEcode(task.getWlmc1());
            adjustItemRequest.setProduceDate(task.getScrq());
            adjustItemRequest.setStorageType(StoreTypeEnum.getValue(task.getBfxz()));
//            adjustItemRequest.setStorageType(SgConstantsIF.STOCK_TYPE_GOODS);
            itemRequests.add(adjustItemRequest);
        });

        return itemRequests;
    }

    /**
     * 不用判空，上游判断
     *
     * @param model 入参
     * @return 错误信息
     */
    private String checkModel(SgScrapOrderModel model) {

        if (model == null) {
            return "入参为空！";
        }

        Integer selectCount = adjustMapper.selectCount(new LambdaQueryWrapper<SgBStoAdjust>()
                .eq(SgBStoAdjust::getSourceBillNo, model.getLcbh()));
        if (selectCount > 0) {
            return "来源单号重复";
        }

        List<SgScrapOrderItemModel> scrapOrderItemModels = model.getScrapOrderItemModels();

        List<String> storeList = new ArrayList<>();
        List<String> skuList = new ArrayList<>();

        for (int i = 0, j = scrapOrderItemModels.size(); i < j; i++) {
            SgScrapOrderItemModel itemModel = scrapOrderItemModels.get(i);
            storeList.add(itemModel.getWerehousecode());
            skuList.add(itemModel.getWlmc1());
        }

        //校验sku是否存在
        Map<String, PsCProSkuResult> skuInfo;
        if (CollectionUtils.isNotEmpty(skuList)) {
            JSONArray error = new JSONArray();
            skuInfo = CommonCacheValUtils.getSkuInfo(skuList);
            skuList.forEach(sku -> {
                if (!skuInfo.containsKey(sku)) {
                    error.add(sku);
                }
            });
            if (error.size() > 0) {
                return "条码【" + JSONArray.toJSONString(error) + "】不存在请检查！";
            }
        } else {
            return "条码信息不能为空！";
        }

        Map<String, CpCStore> cpStoreMap;
        //校验逻辑仓
        if (CollectionUtils.isNotEmpty(storeList)) {
            cpStoreMap = CommonCacheValUtils.queryStoreInfosByEcodes(storeList);
            JSONArray error = new JSONArray();
            storeList.forEach(store -> {
                if (!cpStoreMap.containsKey(store)) {
                    error.add(store);
                }
            });
            if (error.size() > 0) {
                return "仓库【" + JSONArray.toJSONString(error) + "】不存在请检查！";
            }
        } else {
            return "仓库信息不能为空！";
        }
        //[仓库编码+条码编码+报废数量+生产日期]到逻辑仓冻结库存表查找是否是否满足要求，若否则返回：“商品的冻结库存不足(条码编码:XXXX，生产日期:XX，数量:XX，冻结库存:XX)“；
        return checkFreezeQty(scrapOrderItemModels, cpStoreMap, skuInfo);
    }

    /**
     * check 冻结量
     *
     * @param scrapOrderItemModels 入参明细
     * @param cpStoreMap           店仓map
     * @param skuInfo              条码map
     * @return 错误信息
     */
    private String checkFreezeQty(List<SgScrapOrderItemModel> scrapOrderItemModels, Map<String, CpCStore> cpStoreMap,
                                  Map<String, PsCProSkuResult> skuInfo) {

        Map<String, List<SgScrapOrderItemModel>> storeGroupingByMap = scrapOrderItemModels
                .stream().collect(Collectors.groupingBy(SgScrapOrderItemModel::getWerehousecode));

        log.info(LogUtil.format("SgScrapOrderService.checkFreezeQty storeGroupingByMap.size:{} ",
                "SgScrapOrderService.checkFreezeQty"),
                MapUtils.isNotEmpty(storeGroupingByMap) ? storeGroupingByMap.size() : 0);

        JSONArray error = new JSONArray();
        for (String key : storeGroupingByMap.keySet()) {
            List<SgScrapOrderItemModel> sgScrapOrderItemModels = storeGroupingByMap.get(key);
            //正常这里不会跳过
            if (!cpStoreMap.containsKey(key)) {
                continue;
            }
            List<String> scrqList = new ArrayList<>();
            List<Long> skuList = new ArrayList<>();

            List<String> storeTypeList = new ArrayList<>();
            storeTypeList.add(StoreTypeEnum.CC.getValue());
            storeTypeList.add(StoreTypeEnum.FX.getValue());
            storeTypeList.add(StoreTypeEnum.ZJ.getValue());
            storeTypeList.add(StoreTypeEnum.DS.getValue());

            Map<String, BigDecimal> itemQtyMap = new HashMap<>(16);
            for (SgScrapOrderItemModel itemModel : sgScrapOrderItemModels) {
                scrqList.add(itemModel.getScrq());
                skuList.add(skuInfo.get(itemModel.getWlmc1()).getId());
                if (!itemQtyMap.containsKey(itemModel.getWlmc1() + SgConstantsIF.MAP_KEY_DIVIDER + itemModel.getScrq())) {
                    itemQtyMap.put(itemModel.getWlmc1() + SgConstantsIF.MAP_KEY_DIVIDER + itemModel.getScrq(), itemModel.getPlanqty().abs());
                } else {
                    BigDecimal qty = itemQtyMap.get(itemModel.getWlmc1() + SgConstantsIF.MAP_KEY_DIVIDER + itemModel.getScrq());
                    //条码-生产日期
                    itemQtyMap.put(itemModel.getWlmc1() + SgConstantsIF.MAP_KEY_DIVIDER + itemModel.getScrq(), qty.add(itemModel.getPlanqty().abs()));
                }
            }

            //查询冻结量
            SgBFreezeStorageQueryRequest freezeStorageQueryRequest = new SgBFreezeStorageQueryRequest();
            freezeStorageQueryRequest.setStoreId(cpStoreMap.get(key).getId());
            freezeStorageQueryRequest.setProduceDateList(scrqList);
            freezeStorageQueryRequest.setSkuIdList(skuList);
            freezeStorageQueryRequest.setStoreTypeList(storeTypeList);
            ValueHolderV14<List<SgBFreezeStorage>> listValueHolderV14 = sgBFreezeStorageQueryService.queryFreezeStorage(freezeStorageQueryRequest);
            if (!listValueHolderV14.isOK()) {
                return listValueHolderV14.getMessage();
            }

            List<SgBFreezeStorage> data = listValueHolderV14.getData();
            Map<String, List<SgBFreezeStorage>> freezeStorageMap = data.stream()
                    .collect(Collectors.groupingBy(o -> o.getPsCSkuEcode() + SgConstantsIF.MAP_KEY_DIVIDER + o.getProduceDate()));

            for (String storageKey : itemQtyMap.keySet()) {
                BigDecimal qty = itemQtyMap.get(storageKey);
                String[] split = storageKey.split(SgConstantsIF.MAP_KEY_DIVIDER);
                if (!freezeStorageMap.containsKey(storageKey)) {
                    error.add("仓库编码:" + key + "条码编码:" + split[0] + "，生产日期:" + split[1] + "，数量:" + qty + "，冻结库存:" + BigDecimal.ZERO);
                    continue;
                }
                List<SgBFreezeStorage> sgFreezeStorageList = freezeStorageMap.get(storageKey);
                BigDecimal freezeStorageSum = sgFreezeStorageList.stream().map(sgGroupStorage -> {
                    if (sgGroupStorage.getQtyFreeze() != null) {
                        return sgGroupStorage.getQtyFreeze();
                    }
                    return BigDecimal.ZERO;
                }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                if (freezeStorageSum.compareTo(qty) < 0) {
                    error.add("仓库编码:" + key + "条码编码:" + split[0] + "，生产日期:" + split[1] + "，数量:" + qty + "，冻结库存:" + freezeStorageSum);
                }
            }

        }

        if (error.size() > 0) {
            return "商品的冻结库存不足:" + JSONArray.toJSONString(error);
        }

        return null;
    }
}
