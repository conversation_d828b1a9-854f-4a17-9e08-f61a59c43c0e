package com.burgeon.r3.inf.services.drp.in;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.inf.config.SgDrpConfig;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpStoOutResultItemSaveRequest;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpStoOutResultSaveRequest;
import com.burgeon.r3.sg.inf.model.result.drp.in.SgDrpStoOutResultSaveResult;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutResultMapper;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutResultBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutResultItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutResultSaveRequest;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutResultBillSaveResult;
import com.burgeon.r3.sg.store.services.out.SgBStoOutResultSaveService;
import com.burgeon.r3.sg.store.services.out.SgBStoOutResultSubmitService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 逻辑出库单
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SgDrpStoOutResultService {

    @Autowired
    private SgBStoOutNoticesMapper outNoticesMapper;
    @Autowired
    private SgBStoOutResultMapper sgStoOutResultMapper;
    @Autowired
    private SgBStoOutResultSaveService saveService;
    @Autowired
    private SgBStoOutResultSubmitService submitService;
    @Autowired
    private SgDrpConfig sgDrpConfig;

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgDrpStoOutResultSaveResult> save(SgDrpStoOutResultSaveRequest request) {
        if (!sgDrpConfig.getEnableDrpSend()) {
            return new ValueHolderV14<>(ResultCode.FAIL, "中台系统未开放,请稍后再试!");
        }

        log.info("Start SgDrpStoOutResultService.save param={}", JSONObject.toJSONString(request));

        String lockKsy =
                SgConstants.INTERFACE_DRP + ":" + SgConstants.SG_B_STO_OUT_RESULT + ":" + request.getSourceBillNo();
        SgRedisLockUtils.lock(lockKsy);

        ValueHolderV14<SgDrpStoOutResultSaveResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS,
                SgConstants.MESSAGE_STATUS_SUCCESS);
        try {

            SgBStoOutResultBillSaveRequest sgRequest = new SgBStoOutResultBillSaveRequest();
            if (request.getSourceBillType() == null) {
                request.setSourceBillType(SgConstantsIF.BILL_STO_TRANSFER);
            }
            ValueHolderV14<SgBStoOutNotices> checkVh = checkParams(request);
            if (!checkVh.isOK() || Objects.isNull(checkVh.getData())) {
                v14.setCode(checkVh.getCode());
                v14.setMessage(checkVh.getMessage());
                return v14;
            }
            SgBStoOutNotices stoOutNotices = checkVh.getData();
            // 设置主表
            SgBStoOutResultSaveRequest mainTableRequest = new SgBStoOutResultSaveRequest();
            BeanUtils.copyProperties(request, mainTableRequest);
            mainTableRequest.setOutTime(DateUtil.stringToDate(request.getOutDate()));
            //            mainTableRequest.setBillDate(request.getBillDate());
            //            mainTableRequest.setSourceBillType(request.getSourceBillType());
            //            mainTableRequest.setSourceBillNo(request.getSourceBillNo());
            //            // mainTableRequest.setBillNo(request.getBillNo());
            //            mainTableRequest.setSourceBillId(request.getSourceBillId());
            //            mainTableRequest.setReceiverEcode(request.getReceiverEcode());
            //            mainTableRequest.setReceiverName(request.getReceiverName());
            //            mainTableRequest.setIsLast(request.getIsLast());
            mainTableRequest.setSgBStoOutNoticesId(stoOutNotices.getId());
            mainTableRequest.setSgBStoOutNoticesNo(stoOutNotices.getBillNo());
            if (StringUtils.isNotBlank(request.getCpCLogisticsEcode())) {
                CpLogistics cpLogistics = CommonCacheValUtils.queryByLogisticsEcode(request.getCpCLogisticsEcode());
                if (Objects.isNull(cpLogistics)) {
                    return new ValueHolderV14<>(ResultCode.FAIL, "未查询到对应物流公司 物流公司编码:" + request.getCpCLogisticsEcode());
                } else {
                    mainTableRequest.setCpCLogisticsEcode(cpLogistics.getEcode());
                    mainTableRequest.setCpCLogisticsEname(cpLogistics.getEname());
                    mainTableRequest.setCpCLogisticsId(cpLogistics.getId());
                }
            }

            CpCStore storeInfoByEcode = CommonCacheValUtils.getStoreInfoByEcode(request.getCpCStoreEcode());
            if (Objects.isNull(storeInfoByEcode)) {
                return new ValueHolderV14<>(ResultCode.FAIL, "逻辑仓编码为：" + request.getCpCStoreEcode() + "对应的逻辑仓档案不存在!");
            }

            mainTableRequest.setCpCStoreEcode(storeInfoByEcode.getEcode());
            mainTableRequest.setCpCStoreEname(storeInfoByEcode.getEname());
            mainTableRequest.setCpCStoreId(storeInfoByEcode.getId());
            //逻辑仓+条码维度合并
            List<SgDrpStoOutResultItemSaveRequest> mergeItemList = new ArrayList<>();
            // 设置明细
            List<SgBStoOutResultItemSaveRequest> itemList = new ArrayList<>();
            request.getItems().stream().collect(Collectors.groupingBy(SgDrpStoOutResultItemSaveRequest::getPsCSkuEcode))
                    .forEach((skuEcode, item) -> item.stream().reduce((j, k) -> {
                        SgDrpStoOutResultItemSaveRequest drpItemSaveRequest = new SgDrpStoOutResultItemSaveRequest();
                        drpItemSaveRequest.setPsCSkuEcode(j.getPsCSkuEcode());
                        drpItemSaveRequest.setQty(j.getQty().add(k.getQty()));
                        return drpItemSaveRequest;
                    }).ifPresent(mergeItemList::add));
            //批量设置sku信息
            CommonCacheValUtils.setSkuInfoByCode(mergeItemList);
            mergeItemList.forEach(item -> {
                SgBStoOutResultItemSaveRequest sgRequestItem = new SgBStoOutResultItemSaveRequest();
                BeanUtils.copyProperties(item, sgRequestItem);
                itemList.add(sgRequestItem);
            });

            sgRequest.setIsLast(request.getIsLast());
            sgRequest.setOutResultSaveRequest(mainTableRequest);
            sgRequest.setOutItemResultSaveRequestList(itemList);
            sgRequest.setLoginUser(DrpUtils.getUser());
            sgRequest.setIsAutoOut(SgConstants.IS_AUTO_Y);
/*        sgRequest.setIsOneClickLibrary(true);
        sgRequest.setIsAutoOut(SgConstants.IS_AUTO_Y);*/
            //drp反馈一定是最后一次出库 保存逻辑会审核 注释多余审核逻辑
            ValueHolderV14<SgBStoOutResultBillSaveResult> sgResult = saveService.saveSgStoOutResult(sgRequest);
            if (log.isDebugEnabled()) {
                log.debug("Start SgDrpStoOutResultService.save.result={}", JSONObject.toJSONString(sgResult));
            }
            //        if (sgResult.isOK()) {
            //            SgR3BaseRequest submitRquest = new SgR3BaseRequest();
            //            submitRquest.setObjId(sgResult.getData().getId());
            //            submitRquest.setLoginUser(DrpUtils.getUser());
            //            List<String> redisBillFtpKeyList = new ArrayList<>();
            //            ValueHolderV14<SgBStoOutBillSubmitResult> submitResult = submitService.submitOutResult
            //            (submitRquest, "2", redisBillFtpKeyList);
            //            if (log.isDebugEnabled()) {
            //                log.debug("Start SgDrpStoOutResultService.submit.result={}", JSONObject.toJSONString
            //                (submitResult));
            //            }
            //        }
            SgDrpStoOutResultSaveResult result = new SgDrpStoOutResultSaveResult();
            result.setBillNo(sgResult.getData().getBillNo());
        } catch (Exception e) {
            log.error("SgDrpStoOutResultService.save. error:{}", Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrowException("逻辑出库单保存服务异常！", e, Locale.getDefault());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        return v14;
    }

    /**
     * 校验入参参数
     *
     * @param request
     */
    private ValueHolderV14<SgBStoOutNotices> checkParams(SgDrpStoOutResultSaveRequest request) {
        ValueHolderV14<SgBStoOutNotices> vh = new ValueHolderV14<>(ResultCode.SUCCESS,
                SgConstants.MESSAGE_STATUS_SUCCESS);
        if (Objects.isNull(request.getSourceBillNo())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "来源单据编号不可为空!");
        }
        if (Objects.isNull(request.getSourceBillType())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "来源单据类型不可为空!");
        }
        if (Objects.isNull(request.getItems())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "明细不可为空!");
        }

        //根据逻辑仓信息获取
        CpCStore storeInfoByEcode = CommonCacheValUtils.getStoreInfoByEcode(request.getCpCStoreEcode());
        if (Objects.isNull(storeInfoByEcode)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "逻辑仓编码为：" + request.getCpCStoreEcode() + "对应的逻辑仓档案不存在！");
        }

        SgBStoOutNotices stoOutNotices = outNoticesMapper.selectOne(new LambdaQueryWrapper<SgBStoOutNotices>()
                .eq(SgBStoOutNotices::getSourceBillNo, request.getSourceBillNo())
                .eq(SgBStoOutNotices::getSourceBillType, request.getSourceBillType())
                .eq(SgBStoOutNotices::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (Objects.isNull(stoOutNotices)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "来源单据编号和来源单据类型所在的出库通知单不存在!");
        }
        if (SgStoreConstants.BILL_NOTICES_STATUS_ALL_OUT.equals(stoOutNotices.getBillStatus())) {
            return new ValueHolderV14<>(ResultCode.SUCCESS, "当前单据中台已全部出库,请勿重复请求!");
        }
        if (SgStoreConstants.BILL_NOTICES_STATUS_VOID.equals(stoOutNotices.getBillStatus())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "当前单据中台已作废!");
        }
        //提取缺少条码编码的明细
        List<SgDrpStoOutResultItemSaveRequest> missSkuEcodeItems =
                request.getItems().stream().filter(i -> Objects.isNull(i.getPsCSkuEcode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(missSkuEcodeItems)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "明细中条码不可为空!");
        }
        vh.setData(stoOutNotices);
        return vh;
    }

}
