package com.burgeon.r3.inf.services.external.in;

import com.alibaba.fastjson.JSON;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoUnfreezeItemRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoUnfreezeRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoUnfreezeSaveRequest;
import com.burgeon.r3.sg.store.model.result.freeze.SgBStoUnfreezeSaveBillResult;
import com.burgeon.r3.sg.store.services.freeze.SgBStoUnfreezeSaveAndSubmitService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/13 10:37
 * 外部调用-逻辑解冻单服务
 */
@Slf4j
@Component
public class SgExternalStoUnFreezeService {

    @Autowired
    private SgBStoUnfreezeSaveAndSubmitService saveAndSubmitService;


    /**
     * 逻辑冻结单保存
     */
    public ValueHolderV14<SgBStoUnfreezeSaveBillResult> saveExternalUnFreeze(SgBStoUnfreezeSaveRequest request) {

        log.debug(LogUtil.format("SgExternalStoUnFreezeService.saveExternalUnFreeze.start",
                "SgExternalStoUnFreezeService.saveExternalUnFreeze.request",
                JSON.toJSONString(request)));

            ValueHolderV14<SgBStoUnfreezeSaveBillResult> checkParams = checkParams(request);
            if (!checkParams.isOK()){
                return checkParams;
            }
            return saveAndSubmitService.saveAndSubmit(request);
    }

    /**
     * 参数检验
     */
    private ValueHolderV14<SgBStoUnfreezeSaveBillResult> checkParams(SgBStoUnfreezeSaveRequest request) {
        SgBStoUnfreezeRequest saveRequest = request.getSgStoUnfreezeRequest();
        List<SgBStoUnfreezeItemRequest> saveItemRequest = request.getSgStoUnfreezeItemRequest();
        if (Objects.isNull(saveRequest)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "创建逻辑解冻单主表信息不能为空!");
        }
        if (CollectionUtils.isEmpty(saveItemRequest)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "创建逻辑解冻单明细表信息不能为空!");
        }
        if (Objects.isNull(saveRequest.getCpCStoreId())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "创建逻辑解冻单逻辑仓信息不能为空!");
        }
        List<SgBStoUnfreezeItemRequest> errorItemRequests =
                saveItemRequest.stream().filter(i -> StringUtils.isBlank(i.getProduceDate()) || StringUtils.isBlank(i.getProduceDate())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(errorItemRequests)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "当前逻辑解冻单明细,生产日期或库存类型不能为空!");
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "校验通过!");
    }
}
