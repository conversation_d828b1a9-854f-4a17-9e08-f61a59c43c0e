package com.burgeon.r3.inf.services.wms.adjust;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.inf.mapper.SgBWmsInventoryReportMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.inf.adjust.SgBWmsInventoryReport;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.google.common.base.Preconditions;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * WMS发起库存盘点回传中间表
 */
@Component
@Slf4j
public class SgBWmsInventoryReportSaveService {

    @Autowired
    private SgBWmsInventoryReportMapper sgBWmsInventoryReportMapper;

    public ValueHolderV14 saveSgBWmsInventoryReport(String messageBody) {
        log.info(LogUtil.format(" 库存盘点回传中间表入参 ：{}"), JSON.toJSONString(messageBody));
        ValueHolderV14 v141 = new ValueHolderV14(ResultCode.FAIL, null);
        String outBizCode;
        try {
            Preconditions.checkArgument(StringUtils.isNotBlank(messageBody), "messageBody参数为空");
            JSONObject jsonObject = JSONObject.parseObject(messageBody).getJSONObject("data");
            Preconditions.checkNotNull(jsonObject, "数据为空");
            outBizCode = jsonObject.getString("outBizCode");
            Preconditions.checkArgument(StringUtils.isNotBlank(outBizCode), "outBizCode不能为空");
            v141 = dealSave(jsonObject, outBizCode);
        } catch (Exception e) {
            log.error(" 数据解析异常，{}/{}/{}", e.getMessage(), messageBody, Throwables.getStackTraceAsString(e));
            v141.setMessage(e.getMessage());
            return v141;
        }
        v141.setCode(ResultCode.SUCCESS);
        v141.setMessage("保存成功");
        return v141;
    }

    private ValueHolderV14 dealSave(JSONObject jsonObject, String outBizCode) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.FAIL, null);
        String lockKsy = getInventoryReportWmsCallBackRedisKey(outBizCode);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockKsy);
        try {
            if (redisLock.tryLock(RedisMasterUtils.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                LambdaQueryWrapper<SgBWmsInventoryReport> wrapper = new QueryWrapper<SgBWmsInventoryReport>().lambda()
                        .eq(SgBWmsInventoryReport::getOutBizCode, outBizCode);
                List<SgBWmsInventoryReport> sgBWmsInventoryReports = sgBWmsInventoryReportMapper.selectList(wrapper);
                if (CollectionUtils.isEmpty(sgBWmsInventoryReports)) {
                    SgBWmsInventoryReport report = buildSgBWmsInventoryReport(jsonObject);
                    int insertRecord = sgBWmsInventoryReportMapper.insert(report);
                    if (insertRecord > 0) {
                        v14.setCode(ResultCode.SUCCESS);
                        v14.setData(report.getId());
                    } else {
                        v14.setMessage("数据库保存失败" + outBizCode);
                    }
                } else {
                    v14.setMessage("数据库已存在" + outBizCode);
                }
            } else {
                throw new InterruptedException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试！!"));
            }
        } catch (InterruptedException e) {
            log.error(" 当前单据其他人在操作，请稍后再试，{}/{}", lockKsy, Throwables.getStackTraceAsString(e));
        } catch (Exception ex) {
            log.error(" 当前单据处理异常：{}/{}", lockKsy, Throwables.getStackTraceAsString(ex));
        } finally {
            redisLock.unlock();
        }
        return v14;
    }

    private SgBWmsInventoryReport buildSgBWmsInventoryReport(JSONObject obj) {
        SgBWmsInventoryReport report = new SgBWmsInventoryReport();
        report.setId(ModelUtil.getSequence(SgConstants.SG_B_WMS_INVENTORY_REPORT_TABLE));
        // 外部业务编码(消息ID;用于去重;ISV对于同一请求;分配一个唯一性的编码。用来保证因为网络等原因导致重复传输;请求不 会被重复处理)
        String outBizCode = obj.getString("outBizCode");
        if (StringUtils.isNotBlank(outBizCode)) {
            report.setOutBizCode(outBizCode);
        }
        // 仓库编码
        String warehouseCode = obj.getString("warehouseCode");
        if (StringUtils.isNotBlank(warehouseCode)) {
            report.setWmsWarehouseCode(warehouseCode);
        }
        // 参数中“盘点单编码checkOrderCode”
        String checkOrderCode = obj.getString("checkOrderCode");
        if (StringUtils.isNotBlank(checkOrderCode)) {
            report.setWmsBillNo(checkOrderCode);
        }
        // 变动类型：CHECK=盘点 ADJUST=调整
        String adjustType = obj.getString("adjustType");
        if (StringUtils.isNotBlank(adjustType)) {
            report.setWmsBillType(adjustType);
        }
        report.setMessage(JSON.toJSONString(obj));
        StorageUtils.setBModelDefalutData(report, SystemUserResource.getRootUser());
        return report;
    }


    public static String getInventoryReportWmsCallBackRedisKey(String outBizCode) {
        return SgConstants.SG_REDIS_KEY_SG_B_WMS_INVENTORY_REPORT_PREFIX + outBizCode;
    }
}