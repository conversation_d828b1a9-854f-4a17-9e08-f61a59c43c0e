package com.burgeon.r3.inf.services.drp.out;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/14 15:45
 */
public class SgDrpConstantsIF {

    public final static Map billType = new HashMap();
    public final static Map profitBillType = new HashMap();

    static {
        billType.put(7, "TF");
        billType.put(101, "LY");
        billType.put(100, "LYGH");
        billType.put(3, "NOR");

        profitBillType.put("1","INF");
        profitBillType.put("2","INS");
        profitBillType.put("3","INH");
    }
}
