package com.burgeon.r3.inf.services.drp.in;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.inf.config.SgDrpConfig;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInNotices;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInResult;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpStoInResultSaveRequest;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInNoticesMapper;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInResultMapper;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultSaveRequest;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInResultBillSaveResult;
import com.burgeon.r3.sg.store.services.in.SgBStoInResultSaveAndSubmitService;
import com.burgeon.r3.sg.store.services.in.SgBStoInResultSaveService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

/**
 * 逻辑入库单接口
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SgDrpStoInResultService {
    @Autowired
    private SgBStoInResultSaveService saveService;

    @Autowired
    private SgBStoInNoticesMapper inNoticesMapper;

    @Autowired
    private SgBStoInResultMapper sgStoInResultMapper;

    @Autowired
    SgBStoInResultSaveAndSubmitService inResultSaveAndSubmitService;

    @Autowired
    private SgDrpConfig sgDrpConfig;

    /**
     * 逻辑入库单保存接口
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgBStoInResultBillSaveResult> save(SgDrpStoInResultSaveRequest request) {
        if (!sgDrpConfig.getEnableDrpSend()) {
            return new ValueHolderV14<>(ResultCode.FAIL, "中台系统未开放,请稍后再试!");
        }

        log.info("Start SgDrpStoInResultService.save:param={}", JSONObject.toJSONString(request));
        String lockKsy =
                SgConstants.INTERFACE_DRP + ":" + SgConstants.SG_B_STO_IN_RESULT + ":" + request.getSourceBillNo();
        SgRedisLockUtils.lock(lockKsy);
        ValueHolderV14<SgBStoInResultBillSaveResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS,
                SgConstants.MESSAGE_STATUS_SUCCESS);
        try {

            SgBStoInResultBillSaveRequest sgRequest = new SgBStoInResultBillSaveRequest();
            // 构建主表信息
            SgBStoInResultSaveRequest mainTableRequest = new SgBStoInResultSaveRequest();
            //获取入库通知单
            if (Objects.isNull(request.getSourceBillNo())) {
                return new ValueHolderV14<>(ResultCode.FAIL, "来源单据编号不能为空!");
            }

            request.setSourceBillType(SgConstantsIF.BILL_STO_TRANSFER);
            SgBStoInNotices inNotices = inNoticesMapper.selectOne(new LambdaQueryWrapper<SgBStoInNotices>()
                    .eq(SgBStoInNotices::getSourceBillNo, request.getSourceBillNo())
                    .eq(SgBStoInNotices::getSourceBillType, request.getSourceBillType()));
            if (Objects.isNull(inNotices)) {
                return new ValueHolderV14<>(ResultCode.FAIL, "入库通知单不存在!");
            }
            if (inNotices.getBillStatus().equals(SgStoreConstants.BILL_STO_IN_RESULT_SUCCESS)) {
                return new ValueHolderV14<>(ResultCode.SUCCESS, "当前单据中台已全部入库,请勿重复请求!");
            }
            if (inNotices.getBillStatus().equals(SgStoreConstants.BILL_STO_IN_RESULT_VOID)) {
                return new ValueHolderV14<>(ResultCode.FAIL, "当前单据中台已作废!");
            }

            SgBStoInResult stoInResult = sgStoInResultMapper.selectOne(new LambdaQueryWrapper<SgBStoInResult>()
                    .eq(SgBStoInResult::getSourceBillNo, request.getSourceBillNo())
                    .eq(SgBStoInResult::getSgBStoInNoticesId, inNotices.getId())
                    .eq(SgBStoInResult::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (stoInResult != null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("来源单号已生成逻辑入库单,不允许重复创建!");
                return v14;
            }

            mainTableRequest.setSgBStoInNoticesNo(inNotices.getBillNo());
            mainTableRequest.setSgBStoInNoticesId(inNotices.getId());
            mainTableRequest.setBillNo(request.getBillNo());
            mainTableRequest.setBillDate(DateUtil.stringToDate(request.getBillDate()));
            mainTableRequest.setSourceBillType(request.getSourceBillType());
            mainTableRequest.setSourceBillNo(request.getSourceBillNo());
            //8.12 发现原逻辑传参来源单据id，er与中台不一致，在此覆盖
            mainTableRequest.setSourceBillId(inNotices.getSourceBillId());
            //        mainTableRequest.setSgBStoInId(request.getStoInId());
            mainTableRequest.setSenderEcode(request.getSenderEcode());
            mainTableRequest.setSenderName(request.getSenderName());
            mainTableRequest.setIsLast(request.getIsLast());
            mainTableRequest.setInTime(DateUtil.stringToDate(request.getBillDate()));

            CpCStore storeInfo = CommonCacheValUtils.getStoreInfoByEcode(request.getCpCStoreEcode());
            AssertUtils.cannot(Objects.isNull(storeInfo), "逻辑仓未查询到！");

            mainTableRequest.setCpCStoreEcode(storeInfo.getEcode());
            mainTableRequest.setCpCStoreEname(storeInfo.getEname());
            mainTableRequest.setCpCStoreId(storeInfo.getId());

            // 构建明细信息
            if (CollectionUtils.isEmpty(request.getItems())) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("逻辑入库单保存,明细信息不能为空!");
                return v14;
            }
            List<SgBStoInResultItemSaveRequest> itemList = new ArrayList<>();
            //批量设置sku信息
            CommonCacheValUtils.setSkuInfoByCode(request.getItems());
            request.getItems().forEach(item -> {
                SgBStoInResultItemSaveRequest requestItem = new SgBStoInResultItemSaveRequest();
                BeanUtils.copyProperties(item, requestItem);

                itemList.add(requestItem);
            });

            sgRequest.setInResultSaveRequest(mainTableRequest);
            sgRequest.setInItemResultSaveRequestList(itemList);
            sgRequest.setIsOneClickLibrary(true);
            //用户不互通 和产品沟通 暂时给默认用户
            sgRequest.setLoginUser(DrpUtils.getUser());
            List<SgBStoInResultBillSaveRequest> billList = new ArrayList<>();
            billList.add(sgRequest);
            v14 = inResultSaveAndSubmitService.saveAndAuditBillWithTrans(billList);
 /*       ValueHolderV14<SgBStoInResultBillSaveResult> sgResult = saveService.saveSgBStoInResult(sgRequest);
        SgDrpStoInResultSaveResult drpResult = new SgDrpStoInResultSaveResult();
        drpResult.setBillNo(sgResult.getData().getBillNo());
        v14.setData(drpResult);*/
        } catch (Exception e) {
            log.error("SgDrpStoInResultService.save. error:{}", Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrowException("逻辑入库单保存服务异常！", e, Locale.getDefault());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        return v14;
    }
}
