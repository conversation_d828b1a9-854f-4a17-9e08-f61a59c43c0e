package com.burgeon.r3.inf.services.oms;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.SgBSaStoragePreoutFtpMapper;
import com.burgeon.r3.sg.basic.mapper.SgBSpStoragePreoutFtpMapper;
import com.burgeon.r3.sg.basic.mapper.SgBStorageSharedPreoutFtpMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgBSaStoragePreoutFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgBSpStoragePreoutFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorageSharedPreoutFtp;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOut;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOutItem;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOut;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItem;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItemLog;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoTranslationBillRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoTranslationRequest;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutItemMapper;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/3 15:49
 * (时效订单)库存占用平移Service
 */
@Slf4j
@Component
@Deprecated
public class SgOmsStoTranslationVipcomService {
    @Autowired
    private SgOmsStoTranslationService sgOmsStoTranslationService;
    @Autowired
    private SgBShareOutMapper sgShareOutMapper;
    @Autowired
    private SgBShareOutItemMapper sgShareOutItemMapper;
    @Autowired
    private SgBStoOutMapper sgStoOutMapper;
    @Autowired
    private SgBStoOutItemMapper sgStoOutItemMapper;
    @Autowired
    private SgBSpStoragePreoutFtpMapper sgSpStoragePreoutFtpMapper;
    @Autowired
    private SgBSaStoragePreoutFtpMapper sgSaStoragePreoutFtpMapper;
    @Autowired
    private SgBStorageSharedPreoutFtpMapper sgStorageSharedPreoutFtpMapper;

    //TODO 待唯品功能上线后联调和测试(未使用)

    /**
     * 时效订单库存平移
     *
     * @param request 请求参数
     * @return ValueHolderV14
     */
    @Deprecated
    public ValueHolderV14 vipcomTranslation(SgOmsStoTranslationRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(" Start SgOmsStoTranslationService shareDistributionTranslation request:{}",
                    JSONObject.toJSONString(request));
        }
        ValueHolderV14 v14 = new ValueHolderV14<>();
        List<SgOmsStoTranslationBillRequest> originalOrders = request.getOriginalOrders();
        SgOmsStoTranslationBillRequest newOrder = request.getNewOrder();
        User user = new UserImpl();
        BeanUtils.copyProperties(request.getLoginUser(), user);
        try {
            sgOmsStoTranslationService.checkParam(originalOrders, newOrder);
            SgOmsStoTranslationVipcomService service =
                    ApplicationContextHandle.getBean(SgOmsStoTranslationVipcomService.class);
            v14 = service.mergeVipcom(originalOrders, newOrder, user);
        } catch (Exception e) {
            log.error("SgOmsStoTranslationService shareDistributionTranslation error:{}",
                    Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("库存平移失败！" + e.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug(" End SgOmsStoTranslationService shareDistributionTranslation return:{}",
                    JSONObject.toJSONString(v14));
        }
        return v14;
    }

    /**
     * 合并逻辑
     *
     * @param originalOrders 需要合并的配货单信息集合
     * @param newOrder       新零售单信息
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 mergeVipcom(List<SgOmsStoTranslationBillRequest> originalOrders,
                                      SgOmsStoTranslationBillRequest newOrder, User user) {
        List<Long> originalOrderIds =
                originalOrders.stream().map(SgOmsStoTranslationBillRequest::getId).collect(Collectors.toList());
        AssertUtils.cannot(CollectionUtils.isEmpty(originalOrderIds), "需要合并的时效订单ID不能为空！");
        //共享占用单处理逻辑
        mergeShareOutMainAndItems(originalOrderIds, newOrder, user);
        //逻辑占用单处理逻辑
        mergeStoOutMainAndItems(originalOrderIds, newOrder, user);
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("库存平移成功！"));
    }

    /**
     * 合并共享占用单主表 明细表逻辑
     *
     * @param originalOrderIds 合并的时效订单id集合
     * @param newOrder         新JIT配货单信息
     */
    private void mergeShareOutMainAndItems(List<Long> originalOrderIds, SgOmsStoTranslationBillRequest newOrder,
                                           User user) {
        //获取原时效订单单据编号集合 找到未被作废的共享占用单
        List<SgBShareOut> sgShareOuts =
                sgShareOutMapper.selectList(new LambdaQueryWrapper<SgBShareOut>()
                        .in(SgBShareOut::getSourceBillNo, originalOrderIds)
                        .eq(SgBShareOut::getSourceBillType, SgConstantsIF.BILL_TYPE_VIPSHOP_TIME)
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isNotEmpty(sgShareOuts)) {
            /*共享占用单ID列表*/
            List<Long> updateIds = sgShareOuts.stream().map(SgBShareOut::getId).collect(Collectors.toList());
            //共享占用单新增明细集合
            List<SgBShareOutItem> insertItemsAll = new ArrayList<>();
            //共享占用单新增集合
            List<SgBShareOut> insertMainList = new ArrayList<>();
            //共享占用单新增配销仓占用流水集合
            List<SgBSaStoragePreoutFtp> insertSaStoragePreoutFtps = new ArrayList<>();

            //根据聚合仓分组
            Map<Long, List<SgBShareOut>> groupMap =
                    sgShareOuts.stream().collect(Collectors.groupingBy(SgBShareOut::getSgCShareStoreId));
            for (Map.Entry<Long, List<SgBShareOut>> entry : groupMap.entrySet()) {
                //被合并共享占用单的id集合
                List<Long> mergeIds = entry.getValue().stream().map(SgBShareOut::getId).collect(Collectors.toList());
                //合并新单据信息 返回新单id
                SgBShareOut insertMain = sgOmsStoTranslationService.setShareData(newOrder, sgShareOuts, user);
                //需要合并共享占用单明细集合
                List<SgBShareOutItem> mergeItems =
                        sgShareOutItemMapper.selectList(new LambdaQueryWrapper<SgBShareOutItem>()
                                .in(SgBShareOutItem::getSgBShareOutId, mergeIds)
                                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
                //新旧共享占用单明细id  用于获取对应占用流水
                Map<Long, List<SgBShareOutItem>> shareOutItemIdMap = new HashMap<>(16);
                List<SgBShareOutItem> insertItems = mergeShareOutItems(insertMain.getId(), mergeItems,
                        shareOutItemIdMap);
                if (CollectionUtils.isNotEmpty(insertItems)) {
                    insertMain.setTotRowNum(insertItems.size());
                    insertItemsAll.addAll(insertItems);
                }
                insertMainList.add(insertMain);
                //配销仓占用流水
                mergeSaStoragePreoutFtp(mergeIds, insertMain, insertSaStoragePreoutFtps, newOrder,
                        shareOutItemIdMap);
            }

            try {
                //库存平移 共享占用单更新插入数据
                sgOmsStoTranslationService.insertAndUpdateShare(user, insertItemsAll, insertMainList,
                        insertSaStoragePreoutFtps, updateIds);
            } catch (Exception e) {
                AssertUtils.logAndThrowException("共享占用单更新数据失败!", e);
            }
        } else {
            AssertUtils.logAndThrow("没有符合条件的共享占用单！");
        }
    }

    /**
     * 合并共享池占用流水
     *
     * @param mergeIds          合并的当前共享占用单id集合
     * @param insertMain        新共享占用单
     * @param newOrder          新JIT配货单信息
     * @param shareOutItemIdMap 新共享占用单明细和旧共享占用单集合map
     */
    private void mergeSpStoragePreoutFtp(List<Long> mergeIds, SgBShareOut insertMain,
                                         List<SgBSpStoragePreoutFtp> insertSpStoragePreoutFtps,
                                         SgOmsStoTranslationBillRequest newOrder,
                                         Map<Long, List<SgBShareOutItem>> shareOutItemIdMap) {
        List<SgBSpStoragePreoutFtp> mergeStoragePreOutFtp =
                sgSpStoragePreoutFtpMapper.selectList(new LambdaQueryWrapper<SgBSpStoragePreoutFtp>()
                        .in(SgBSpStoragePreoutFtp::getBillId, mergeIds)
                        .eq(SgBSpStoragePreoutFtp::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isNotEmpty(mergeStoragePreOutFtp)) {
            //冲抵需要合并的流水
            for (SgBSpStoragePreoutFtp mergeStorage : mergeStoragePreOutFtp) {
                BigDecimal qtyBegin = mergeStorage.getQtyBegin();
                BigDecimal qtyChange = mergeStorage.getQtyChange();
                BigDecimal qtyEnd = mergeStorage.getQtyEnd();
                //新增一条记录平流水
                SgBSpStoragePreoutFtp offsetFtp = new SgBSpStoragePreoutFtp();
                BeanUtils.copyProperties(mergeStorage, offsetFtp);
                offsetFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SP_STORAGE_PREOUT_FTP));
                offsetFtp.setQtyBegin(qtyEnd);
                offsetFtp.setQtyChange(qtyChange.negate());
                offsetFtp.setQtyEnd(qtyBegin);
                offsetFtp.setCreationdate(new Date());
                offsetFtp.setModifieddate(new Date());
                insertSpStoragePreoutFtps.add(offsetFtp);
            }
            Map<Long, SgBSpStoragePreoutFtp> preoutFtpMap = mergeStoragePreOutFtp.stream()
                    .collect(Collectors.toMap(SgBSpStoragePreoutFtp::getBillItemId, Function.identity()));
            Set<Map.Entry<Long, List<SgBShareOutItem>>> mergeEntry = shareOutItemIdMap.entrySet();
            for (Map.Entry<Long, List<SgBShareOutItem>> entry : mergeEntry) {
                List<SgBShareOutItem> shareOutItems = entry.getValue();
                Long newBillItemId = entry.getKey();
                List<SgBSpStoragePreoutFtp> mergeFtpList = new ArrayList<>();
                //新增共享占用单流水，绑定新配货单和新共享占用单
                SgBSpStoragePreoutFtp newFtp = new SgBSpStoragePreoutFtp();
                for (SgBShareOutItem shareOutItem : shareOutItems) {
                    SgBSpStoragePreoutFtp preoutFtp = preoutFtpMap.get(shareOutItem.getId());
                    mergeFtpList.add(preoutFtp);
                }
                if (CollectionUtils.isNotEmpty(mergeFtpList)) {
                    //期末数量最大的那条流水
                    SgBSpStoragePreoutFtp maxQtyEndftp =
                            mergeFtpList.stream().max(Comparator.comparing(SgBSpStoragePreoutFtp::getQtyEnd)).get();
                    BigDecimal qtyEnd = Optional.ofNullable(maxQtyEndftp.getQtyEnd()).orElse(BigDecimal.ZERO);
                    BigDecimal qtyChange = mergeFtpList.stream().map(SgBSpStoragePreoutFtp::getQtyChange)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BeanUtils.copyProperties(maxQtyEndftp, newFtp);
                    //期末、变更、期初数量
                    newFtp.setQtyEnd(qtyEnd);
                    newFtp.setQtyChange(qtyChange);
                    newFtp.setQtyBegin(qtyEnd.subtract(qtyChange));

                    newFtp.setCreationdate(new Date());
                    newFtp.setModifieddate(new Date());
                    newFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SP_STORAGE_PREOUT_FTP));
                    newFtp.setBillId(insertMain.getId());
                    newFtp.setBillDate(insertMain.getBillDate());
                    newFtp.setBillNo(insertMain.getBillNo());
                    newFtp.setBillItemId(newBillItemId);
                    newFtp.setSourceBillNo(newOrder.getBillNo());
                    newFtp.setSourceBillId(newOrder.getId());
                    insertSpStoragePreoutFtps.add(newFtp);
                }
            }
        }
    }

    /**
     * 合并配销仓占用流水
     *
     * @param mergeIds          合并的时效订单id集合
     * @param insertMain        新共享占用单
     * @param newOrder          新JIT配货单信息
     * @param shareOutItemIdMap 新共享占用单明细和旧共享占用单集合map
     */
    private void mergeSaStoragePreoutFtp(List<Long> mergeIds, SgBShareOut insertMain,
                                         List<SgBSaStoragePreoutFtp> insertSaStoragePreoutFtps,
                                         SgOmsStoTranslationBillRequest newOrder,
                                         Map<Long, List<SgBShareOutItem>> shareOutItemIdMap) {
        List<SgBSaStoragePreoutFtp> mergeStoragePreOutFtp =
                sgSaStoragePreoutFtpMapper.selectList(new LambdaQueryWrapper<SgBSaStoragePreoutFtp>()
                        .in(SgBSaStoragePreoutFtp::getBillId, mergeIds)
                        .eq(SgBSaStoragePreoutFtp::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isNotEmpty(mergeStoragePreOutFtp)) {
            //冲抵需要合并的流水
            for (SgBSaStoragePreoutFtp mergeStorage : mergeStoragePreOutFtp) {
                BigDecimal qtyBegin = mergeStorage.getQtyBegin();
                BigDecimal qtyChange = mergeStorage.getQtyChange();
                BigDecimal qtyEnd = mergeStorage.getQtyEnd();
                //新增一条记录平流水
                SgBSaStoragePreoutFtp offsetFtp = new SgBSaStoragePreoutFtp();
                //新增配销仓占用流水，绑定新零售单和新配销仓库存占用流水
                BeanUtils.copyProperties(mergeStorage, offsetFtp);
                offsetFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SA_STORAGE_PREOUT_FTP));
                offsetFtp.setQtyBegin(qtyEnd);
                offsetFtp.setQtyChange(qtyChange.negate());
                offsetFtp.setQtyEnd(qtyBegin);
                offsetFtp.setCreationdate(new Date());
                offsetFtp.setModifieddate(new Date());
                insertSaStoragePreoutFtps.add(offsetFtp);
            }
            Map<Long, SgBSaStoragePreoutFtp> preoutFtpMap = mergeStoragePreOutFtp.stream()
                    .collect(Collectors.toMap(SgBSaStoragePreoutFtp::getBillItemId, Function.identity()));
            Set<Map.Entry<Long, List<SgBShareOutItem>>> mergeEntry = shareOutItemIdMap.entrySet();
            for (Map.Entry<Long, List<SgBShareOutItem>> entry : mergeEntry) {
                List<SgBShareOutItem> shareOutItems = entry.getValue();
                Long newBillItemId = entry.getKey();
                List<SgBSaStoragePreoutFtp> mergeFtpList = new ArrayList<>();
                //新增共享占用单流水，绑定新配货单和新共享占用单
                SgBSaStoragePreoutFtp newFtp = new SgBSaStoragePreoutFtp();
                for (SgBShareOutItem shareOutItem : shareOutItems) {
                    SgBSaStoragePreoutFtp preoutFtp = preoutFtpMap.get(shareOutItem.getId());
                    mergeFtpList.add(preoutFtp);
                }
                if (CollectionUtils.isNotEmpty(mergeFtpList)) {
                    //期末数量最大的那条流水
                    SgBSaStoragePreoutFtp maxQtyEndftp =
                            mergeFtpList.stream().max(Comparator.comparing(SgBSaStoragePreoutFtp::getQtyEnd)).get();
                    BigDecimal qtyEnd = Optional.ofNullable(maxQtyEndftp.getQtyEnd()).orElse(BigDecimal.ZERO);
                    BigDecimal qtyChange = mergeFtpList.stream().map(SgBSaStoragePreoutFtp::getQtyChange)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BeanUtils.copyProperties(maxQtyEndftp, newFtp);
                    //期末、变更、期初数量
                    newFtp.setQtyEnd(qtyEnd);
                    newFtp.setQtyChange(qtyChange);
                    newFtp.setQtyBegin(qtyEnd.subtract(qtyChange));

                    newFtp.setCreationdate(new Date());
                    newFtp.setModifieddate(new Date());
                    newFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SA_STORAGE_PREOUT_FTP));
                    newFtp.setBillId(insertMain.getId());
                    newFtp.setBillDate(insertMain.getBillDate());
                    newFtp.setBillNo(insertMain.getBillNo());
                    newFtp.setBillItemId(newBillItemId);
                    newFtp.setSourceBillNo(newOrder.getBillNo());
                    newFtp.setSourceBillId(newOrder.getId());
                    insertSaStoragePreoutFtps.add(newFtp);
                }
            }
        }
    }

    /**
     * 共享占用单明细封装
     *
     * @param mainId     主表id
     * @param mergeItems 需要合并的明细
     * @return List<SgBShareOutItem>
     */
    private List<SgBShareOutItem> mergeShareOutItems(Long mainId, List<SgBShareOutItem> mergeItems,
                                                     Map<Long, List<SgBShareOutItem>> shareOutItemIdMap) {
        List<SgBShareOutItem> insertItems = new ArrayList<>();
        Map<String, List<SgBShareOutItem>> skuItemMap =
                mergeItems.stream().collect(Collectors.groupingBy(e -> e.getSgCSaStoreId() + ":" + e.getPsCSkuId()));
        Set<Map.Entry<String, List<SgBShareOutItem>>> entries = skuItemMap.entrySet();
        for (Map.Entry<String, List<SgBShareOutItem>> entry : entries) {
            List<SgBShareOutItem> mergeItemList = entry.getValue();
            SgBShareOutItem mergeItem = mergeItemList.get(0);
            SgBShareOutItem insertItem = new SgBShareOutItem();
            BeanUtils.copyProperties(mergeItem, insertItem);
            insertItem.setCreationdate(new Date());
            insertItem.setModifieddate(new Date());
            Long shareOutItemId = ModelUtil.getSequence(SgConstants.SG_B_SHARE_OUT_ITEM);
            insertItem.setId(shareOutItemId);
            insertItem.setSgBShareOutId(mainId);
            if (mergeItemList.size() > 1) {
                BigDecimal mergeQty = mergeItemList.stream().map(SgBShareOutItem::getQty).reduce(BigDecimal.ZERO,
                        BigDecimal::add);
                BigDecimal mergeQtyPreout = mergeItemList.stream().map(SgBShareOutItem::getQtyPreout)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal mergeQtyOut = mergeItemList.stream().map(SgBShareOutItem::getQtyOut).reduce(BigDecimal.ZERO,
                        BigDecimal::add);
                insertItem.setQty(mergeQty);
                insertItem.setQtyPreout(mergeQtyPreout);
                insertItem.setQtyOut(mergeQtyOut);
            }
            //新旧共享占用单明细id  用于获取对应占用流水
            shareOutItemIdMap.put(shareOutItemId, mergeItemList);
            insertItems.add(insertItem);
        }
        return insertItems;
    }

    /**
     * 合并逻辑占用单主表 明细表逻辑
     *
     * @param originalOrderIds 合并的时效订单id集合
     * @param newOrder         新JIT配货单信息
     */
    private void mergeStoOutMainAndItems(List<Long> originalOrderIds, SgOmsStoTranslationBillRequest newOrder,
                                         User user) {
        //获取时效订单单据id集合 找到未被作废的逻辑占用单
        List<SgBStoOut> sgStoOuts =
                sgStoOutMapper.selectList(new LambdaQueryWrapper<SgBStoOut>()
                        .in(SgBStoOut::getSourceBillId, originalOrderIds)
                        .eq(SgBStoOut::getSourceBillType, SgConstantsIF.BILL_TYPE_VIPSHOP_TIME)
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isNotEmpty(sgStoOuts)) {
            List<Long> updateIds = sgStoOuts.stream().map(SgBStoOut::getId).collect(Collectors.toList());
            //逻辑占用单新增明细集合
            List<SgBStoOutItem> insertItemsAll = new ArrayList<>();
            //逻辑占用单新增集合
            List<SgBStoOut> insertMainList = new ArrayList<>();
            //逻辑占用单新增逻辑共享占用流水集合
            List<SgBStorageSharedPreoutFtp> insertStorageSharedPreoutFtps = new ArrayList<>();
            //合并返回新单据信息
            SgBStoOut insertMain = sgOmsStoTranslationService.setStoData(newOrder, user, sgStoOuts);
            //需要合并逻辑占用单明细集合
            List<SgBStoOutItem> mergeItems = sgStoOutItemMapper.selectList(new LambdaQueryWrapper<SgBStoOutItem>()
                    .in(SgBStoOutItem::getSgBStoOutId, updateIds)
                    .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
            //新旧逻辑占用单明细id  用于获取对应占用流水
            Map<Long, List<SgBStoOutItem>> stoOutItemIdMap = new HashMap<>(16);
            List<SgBStoOutItem> insertItems = mergeStoOutItems(insertMain.getId(), mergeItems, stoOutItemIdMap);
            if (CollectionUtils.isNotEmpty(insertItems)) {
                insertMain.setTotRowNum(insertItems.size());
                insertItemsAll.addAll(insertItems);
            }
            insertMainList.add(insertMain);
            //逻辑仓共享占用变动流水
            mergeStorageSharedPreoutFtp(updateIds, insertMain, insertStorageSharedPreoutFtps, newOrder,
                    stoOutItemIdMap);

            /*详细见：com.burgeon.r3.inf.services.oms.SgOmsStoTranslationService#mergeStoOutItemLogs*/
            List<SgBStoOutItemLog> newItemLogList = Collections.emptyList();
            try {
                //库存平移 逻辑占用单流程更新插入数据
                sgOmsStoTranslationService.insertAndUpdateSto(user, updateIds, insertItemsAll, insertMainList,
                        insertStorageSharedPreoutFtps, newItemLogList);
            } catch (Exception e) {
                AssertUtils.logAndThrowException("逻辑占用单更新数据失败!", e);
            }
        }
    }

    /**
     * 逻辑仓共享占用变动流水
     *
     * @param updateIds       合并的时效订单id集合
     * @param insertMain      合并后新逻辑占用单
     * @param newOrder        新JIT配货单信息
     * @param stoOutItemIdMap 新旧逻辑占用单明细id  用于获取对应占用流水
     */
    private void mergeStorageSharedPreoutFtp(List<Long> updateIds, SgBStoOut insertMain,
                                             List<SgBStorageSharedPreoutFtp> insertStorageSharedPreoutFtps,
                                             SgOmsStoTranslationBillRequest newOrder,
                                             Map<Long, List<SgBStoOutItem>> stoOutItemIdMap) {
        List<SgBStorageSharedPreoutFtp> mergeStoragePreOutFtp =
                sgStorageSharedPreoutFtpMapper.selectList(new LambdaQueryWrapper<SgBStorageSharedPreoutFtp>()
                        .in(SgBStorageSharedPreoutFtp::getBillId, updateIds)
                        .eq(SgBStorageSharedPreoutFtp::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isNotEmpty(mergeStoragePreOutFtp)) {
            //冲抵需要合并的流水
            for (SgBStorageSharedPreoutFtp mergeStorage : mergeStoragePreOutFtp) {
                BigDecimal qtyBegin = mergeStorage.getQtyBegin();
                BigDecimal qtyChange = mergeStorage.getQtyChange();
                BigDecimal qtyEnd = mergeStorage.getQtyEnd();
                //新增一条记录平流水
                SgBStorageSharedPreoutFtp offsetFtp = new SgBStorageSharedPreoutFtp();
                BeanUtils.copyProperties(mergeStorage, offsetFtp);
                offsetFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_STORAGE_SHARED_PREOUT_FTP));
                offsetFtp.setQtyBegin(qtyEnd);
                offsetFtp.setQtyChange(qtyChange.negate());
                offsetFtp.setQtyEnd(qtyBegin);
                offsetFtp.setCreationdate(new Date());
                offsetFtp.setModifieddate(new Date());
                insertStorageSharedPreoutFtps.add(offsetFtp);
            }
            Map<Long, SgBStorageSharedPreoutFtp> preoutFtpMap = mergeStoragePreOutFtp.stream()
                    .collect(Collectors.toMap(SgBStorageSharedPreoutFtp::getBillItemId, Function.identity()));
            Set<Map.Entry<Long, List<SgBStoOutItem>>> mergeEntry = stoOutItemIdMap.entrySet();
            for (Map.Entry<Long, List<SgBStoOutItem>> entry : mergeEntry) {
                List<SgBStoOutItem> stoOutItems = entry.getValue();
                Long newBillItemId = entry.getKey();
                List<SgBStorageSharedPreoutFtp> mergeFtpList = new ArrayList<>();
                //新增逻辑占用单流水，绑定新配货单和新逻辑占用单
                SgBStorageSharedPreoutFtp newFtp = new SgBStorageSharedPreoutFtp();
                for (SgBStoOutItem stoOutItem : stoOutItems) {
                    SgBStorageSharedPreoutFtp preoutFtp = preoutFtpMap.get(stoOutItem.getId());
                    mergeFtpList.add(preoutFtp);
                }
                if (CollectionUtils.isNotEmpty(mergeFtpList)) {
                    //期末数量最大的那条流水
                    SgBStorageSharedPreoutFtp maxQtyEndftp =
                            mergeFtpList.stream().max(Comparator.comparing(SgBStorageSharedPreoutFtp::getQtyEnd)).get();
                    BigDecimal qtyEnd = Optional.ofNullable(maxQtyEndftp.getQtyEnd()).orElse(BigDecimal.ZERO);
                    BigDecimal qtyChange = mergeFtpList.stream().map(SgBStorageSharedPreoutFtp::getQtyChange)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BeanUtils.copyProperties(maxQtyEndftp, newFtp);
                    //期末、变更、期初数量
                    newFtp.setQtyEnd(qtyEnd);
                    newFtp.setQtyChange(qtyChange);
                    newFtp.setQtyBegin(qtyEnd.subtract(qtyChange));

                    newFtp.setCreationdate(new Date());
                    newFtp.setModifieddate(new Date());
                    newFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_STORAGE_SHARED_PREOUT_FTP));
                    newFtp.setBillId(insertMain.getId());
                    newFtp.setBillDate(insertMain.getBillDate());
                    newFtp.setBillNo(insertMain.getBillNo());
                    newFtp.setBillItemId(newBillItemId);
                    newFtp.setSourceBillNo(newOrder.getBillNo());
                    newFtp.setSourceBillId(newOrder.getId());
                    insertStorageSharedPreoutFtps.add(newFtp);
                }
            }
        }
    }

    /**
     * 逻辑占用单明细封装
     *
     * @param mainId     主表id
     * @param mergeItems 需要合并的明细
     * @return List<SgBShareOutItem>
     */
    private List<SgBStoOutItem> mergeStoOutItems(Long mainId, List<SgBStoOutItem> mergeItems,
                                                 Map<Long, List<SgBStoOutItem>> stoOutItemIdMap) {

        List<SgBStoOutItem> insertItems = new ArrayList<>();
        Map<String, List<SgBStoOutItem>> skuItemMap =
                mergeItems.stream().collect(Collectors.groupingBy(e -> e.getCpCStoreId() + ":" + e.getPsCSkuId()));
        Set<Map.Entry<String, List<SgBStoOutItem>>> entries = skuItemMap.entrySet();
        for (Map.Entry<String, List<SgBStoOutItem>> entry : entries) {
            List<SgBStoOutItem> mergeItemList = entry.getValue();
            SgBStoOutItem mergeItem = mergeItemList.get(0);
            SgBStoOutItem insertItem = new SgBStoOutItem();
            BeanUtils.copyProperties(mergeItem, insertItem);
            Long stoItemId = ModelUtil.getSequence(SgConstants.SG_B_STO_OUT_ITEM);
            insertItem.setCreationdate(new Date());
            insertItem.setModifieddate(new Date());
            insertItem.setId(stoItemId);
            insertItem.setSgBStoOutId(mainId);
            if (mergeItemList.size() > 1) {
                BigDecimal mergeQty = mergeItemList.stream().map(SgBStoOutItem::getQty).reduce(BigDecimal.ZERO,
                        BigDecimal::add);
                BigDecimal mergeQtyPreout =
                        mergeItemList.stream().map(SgBStoOutItem::getQtyPreout).reduce(BigDecimal.ZERO,
                                BigDecimal::add);
                BigDecimal mergeQtyOut = mergeItemList.stream().map(SgBStoOutItem::getQtyOut).reduce(BigDecimal.ZERO,
                        BigDecimal::add);
                insertItem.setQty(mergeQty);
                insertItem.setQtyPreout(mergeQtyPreout);
                insertItem.setQtyOut(mergeQtyOut);
            }
            //新旧共享占用单明细id  用于获取对应占用流水
            stoOutItemIdMap.put(stoItemId, mergeItemList);
            insertItems.add(insertItem);
        }
        return insertItems;
    }
}

