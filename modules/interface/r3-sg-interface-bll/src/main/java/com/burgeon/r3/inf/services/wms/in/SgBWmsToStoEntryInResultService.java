package com.burgeon.r3.inf.services.wms.in;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.inf.utils.WmsTaskUtils;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.YesNoEnum;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.store.adjust.SgBStoAdjust;
import com.burgeon.r3.sg.core.model.table.store.adjust.SgBStoAdjustItem;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInNotices;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInNoticesItem;
import com.burgeon.r3.sg.core.model.table.store.in.SgBWmsToStoEntryInResult;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutResult;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutResultItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.DateUtils;
import com.burgeon.r3.sg.inf.common.SgInfConstants;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.adjust.SgBStoAdjustItemMapper;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInNoticesItemMapper;
import com.burgeon.r3.sg.store.mapper.in.SgBWmsToStoEntryInResultMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutResultItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutResultMapper;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustMainSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoOtherDeliveryForceCompletionItemRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoOtherDeliveryForceCompletionRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferForceCompletionItemRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferForceCompletionRequest;
import com.burgeon.r3.sg.store.model.result.in.SgBStoForceCompletionResult;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInResultBillSubmitResult;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSaveService;
import com.burgeon.r3.sg.store.services.in.SgBStoInResultSaveAndSubmitService;
import com.burgeon.r3.sg.store.services.in.SgBStoOtherDeliveryForceCompletionService;
import com.burgeon.r3.sg.store.services.transfer.SgBStoTransferForceCompletionService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-07-19 10:14
 * @Description: WMS->中台 入库结果中间表定时任务具体业务逻辑
 */

@Slf4j
@Component
public class SgBWmsToStoEntryInResultService {

    @Autowired
    private SgBWmsToStoEntryInResultMapper wmsToStoEntryInResultMapper;
    @Autowired
    private SgBStoInNoticesItemMapper stoInNoticesItemMapper;
    @Autowired
    private SgBStoAdjustItemMapper adjustItemMapper;
    @Autowired
    private SgBStoAdjustSaveService adjustSaveService;
    @Autowired
    private SgBStoInResultSaveAndSubmitService inResultSaveAndSubmitService;
    @Autowired
    private SgBStoOtherDeliveryForceCompletionService sgBStoOtherDeliveryForceCompletionService;
    @Autowired
    private SgBStoOutResultMapper sgBStoOutResultMapper;
    @Autowired
    private SgBStoOutResultItemMapper sgBStoOutResultItemMapper;
    @Autowired
    private SgBStoTransferForceCompletionService sgBStoTransferForceCompletionService;

    /**
     * 入库结果中间表定时任务
     *
     * @return ValueHolderV14
     */
    public ValueHolderV14 execute() {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "入库结果中间表（WMS->中台)定时任务成功");
        log.info(LogUtil.format("SgBWmsToStoEntryInResultService.execute start",
                "SgBWmsToStoEntryInResultService.execute"));

        List<String> wmsTypeList = new ArrayList<>();
        wmsTypeList.add(ThirdWmsTypeEnum.QMWMS.getCode());
        wmsTypeList.add(ThirdWmsTypeEnum.JDWMS.getCode());
        wmsTypeList.add(ThirdWmsTypeEnum.DBWMS.getCode());
        wmsTypeList.add(ThirdWmsTypeEnum.FLWMS.getCode());

        List<SgBWmsToStoEntryInResult> sgWmsToStoEntryInResults =
                wmsToStoEntryInResultMapper.selectList(new LambdaQueryWrapper<SgBWmsToStoEntryInResult>()
                        .lt(SgBWmsToStoEntryInResult::getFailedCount, SgConstantsIF.FAIL_COUNT)
                        .ne(SgBWmsToStoEntryInResult::getTransformStatus, SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS)
                        .in(SgBWmsToStoEntryInResult::getWmsWarehouseType, wmsTypeList)
                        .eq(SgBWmsToStoEntryInResult::getIsactive, YesNoEnum.Y.getKey()));

        if (CollectionUtils.isEmpty(sgWmsToStoEntryInResults)) {
            v14.setMessage("无处理数据！");
            return v14;
        }

        /*校验数据*/
        sgWmsToStoEntryInResults = messageFilter(sgWmsToStoEntryInResults);

        List<String> warehouseCodeList = new ArrayList<>();
        List<String> noticesBillNoList = new ArrayList<>();
        for (SgBWmsToStoEntryInResult inResult : sgWmsToStoEntryInResults) {
            warehouseCodeList.add(inResult.getWarehouseCode());
            noticesBillNoList.add(inResult.getNoticesBillNo());
        }

        //1,根据warehouseCode 查实体仓档案
        Map<String, SgCpCPhyWarehouse> warehouseCodeMap = WmsTaskUtils.getSgCpPhyWarehouseByCode(warehouseCodeList);
        //2.查入库通知单
        Map<String, SgBStoInNotices> inNoticesMap = WmsTaskUtils.getInNotices(noticesBillNoList);
        //3.查库存调整单
        Map<String, SgBStoAdjust> adjustMap = WmsTaskUtils.getAdjust(noticesBillNoList);
        //4.处理业务
        Map<String, List<SgBWmsToStoEntryInResult>> listMap = sgWmsToStoEntryInResults.stream().collect(Collectors.groupingBy(SgBWmsToStoEntryInResult::getWmsWarehouseType));
        if (MapUtils.isNotEmpty(listMap) && CollectionUtils.isNotEmpty(listMap.get(ThirdWmsTypeEnum.QMWMS.getCode()))) {
            businessLogic(listMap.get(ThirdWmsTypeEnum.QMWMS.getCode()), warehouseCodeMap, inNoticesMap, adjustMap);
        }
        if (MapUtils.isNotEmpty(listMap) && CollectionUtils.isNotEmpty(listMap.get(ThirdWmsTypeEnum.JDWMS.getCode()))) {
            businessLogicForJd(listMap.get(ThirdWmsTypeEnum.JDWMS.getCode()), warehouseCodeMap, inNoticesMap, adjustMap);
        }
        if (MapUtils.isNotEmpty(listMap) && CollectionUtils.isNotEmpty(listMap.get(ThirdWmsTypeEnum.FLWMS.getCode()))) {
            businessLogicForFl(listMap.get(ThirdWmsTypeEnum.FLWMS.getCode()), warehouseCodeMap, inNoticesMap, adjustMap);
        }
        //TODO 202303281514: 天猫打标-大宝仓
        if (MapUtils.isNotEmpty(listMap) && CollectionUtils.isNotEmpty(listMap.get(ThirdWmsTypeEnum.DBWMS.getCode()))) {
            businessLogic(listMap.get(ThirdWmsTypeEnum.DBWMS.getCode()), warehouseCodeMap, inNoticesMap, adjustMap);
        }
        return v14;
    }

    /**
     * 处理业务（富勒）
     *
     * @param sgWmsToStoEntryInResults 入参
     * @param warehouseCodeMap         实体仓档案
     * @param inNoticesMap             入库通知单
     * @param adjustMap                库存调整单
     */
    private void businessLogicForFl(List<SgBWmsToStoEntryInResult> sgWmsToStoEntryInResults, Map<String, SgCpCPhyWarehouse> warehouseCodeMap,
                                    Map<String, SgBStoInNotices> inNoticesMap, Map<String, SgBStoAdjust> adjustMap) {

        log.info(LogUtil.format("SgBWmsToStoEntryInResultService.businessLogic sgWmsToStoEntryInResults.size:{}," +
                        "warehouseCodeMap.size:{},warehouseCodeMap.size:{},adjustMap.size:{}",
                "SgBWmsToStoEntryInResultService.businessLogic"),
                sgWmsToStoEntryInResults.size(),
                MapUtils.isNotEmpty(warehouseCodeMap) ? warehouseCodeMap.size() : null,
                MapUtils.isNotEmpty(inNoticesMap) ? inNoticesMap.size() : null,
                MapUtils.isNotEmpty(adjustMap) ? adjustMap.size() : null);

        for (SgBWmsToStoEntryInResult inResult : sgWmsToStoEntryInResults) {
            try {
                String noticesBillNo = inResult.getNoticesBillNo();
                String billType = inResult.getBillType();
                //b.解析报文
                String message = inResult.getMessage();
                if (StringUtils.isEmpty(message)) {
                    updateFail(inResult, "入库单[" + noticesBillNo + "]的报文不存在！");
                    continue;
                }
                //获取库存地点
                String storageLocation = null;
                JSONObject request = JSONObject.parseObject(message);
                if (request != null && request.getJSONObject("extendProps") != null
                        && StringUtils.isNotEmpty(request.getJSONObject("extendProps").getString("storageLocation"))) {
                    storageLocation = request.getJSONObject("extendProps").getString("storageLocation");
                }
                if (StringUtils.isEmpty(storageLocation)) {
                    updateFail(inResult, "入库单[" + noticesBillNo + "]报文中库存地点不存在！");
                    continue;
                }
                //a.是否存在实体仓档案中
                if (MapUtils.isEmpty(warehouseCodeMap) || !warehouseCodeMap.containsKey(storageLocation)) {
                    updateFail(inResult, "仓库编码[" + storageLocation + "]在【实体仓档案】中不存在");
                    continue;
                }

                log.info(LogUtil.format("SgBWmsToStoEntryInResultService.messageParse message:{}",
                        "SgBWmsToStoEntryInResultService.messageParse"), message);
                List<String> itemCodeList = WmsTaskUtils.messageParseByOrderLinesReturnItemCode(message);
                log.info(LogUtil.format("SgBWmsToStoEntryInResultService.messageParse message:{}",
                        "SgBWmsToStoEntryInResultService.itemCodeList"), JSONObject.toJSONString(itemCodeList));
                if (CollectionUtils.isEmpty(itemCodeList)) {
                    updateFail(inResult, "报文无明细！");
                    continue;
                }

                //c,比对条码信息
                JSONArray array = WmsTaskUtils.compareToSku(itemCodeList);
                if (array.size() > 0) {
                    updateFail(inResult, "商品编码" + JSONArray.toJSONString(array) + "在【商品SKU】中不存在！");
                    continue;
                }

                //d.对比单据类型做出不同逻辑处理
                String error = checkBillType(inResult, inNoticesMap, adjustMap);
                if (StringUtils.isNotEmpty(error)) {
                    updateFail(inResult, error);
                    continue;
                }

                //e.生成不同单据
                if (SgInfConstants.OREDER_TYPE_SCRK.equals(billType) ||
                        SgInfConstants.OREDER_TYPE_CGRK.equals(billType) ||
                        SgInfConstants.OREDER_TYPE_DBRK.equals(billType) ||
                        SgInfConstants.OREDER_TYPE_JYRK.equals(billType)) {
                    //【新增并审核逻辑入库单】
                    ValueHolderV14 v14 = inResultSaveAndSubmit(inNoticesMap.get(noticesBillNo), message);
                    if (!v14.isOK()) {
                        updateFail(inResult, "【新增并审核逻辑入库单】失败：" + v14.getMessage());
                        continue;
                    }

                } else if (SgInfConstants.OREDER_TYPE_CXRK.equals(billType) ||
                        SgInfConstants.OREDER_TYPE_CYRK.equals(billType)) {
                    //则【更新对应库存调整单的明细并审核库存调整单】
                    ValueHolderV14 v14 = adjustSaveAndSubmit(inResult, adjustMap.get(noticesBillNo));
                    if (!v14.isOK()) {
                        updateFail(inResult, "【更新对应库存调整单的明细并审核库存调整单】失败：" + v14.getMessage());
                        continue;
                    }
                }

                //f,更新成功信息
                updateSuccess(inResult);

            } catch (Exception e) {
                log.error(LogUtil.format("exception_has_occured:{}",
                        "SgBWmsToStoEntryInResultService.error", Throwables.getStackTraceAsString(e)));
                updateFail(inResult, "异常：" + e.getMessage());
            }


        }
    }

    /**
     * 处理业务
     *
     * @param sgWmsToStoEntryInResults 入参
     * @param warehouseCodeMap         实体仓档案
     * @param inNoticesMap             入库通知单
     * @param adjustMap                库存调整单
     */
    private void businessLogic(List<SgBWmsToStoEntryInResult> sgWmsToStoEntryInResults, Map<String, SgCpCPhyWarehouse> warehouseCodeMap,
                               Map<String, SgBStoInNotices> inNoticesMap, Map<String, SgBStoAdjust> adjustMap) {

        log.info(LogUtil.format("SgBWmsToStoEntryInResultService.businessLogic sgWmsToStoEntryInResults.size:{}," +
                        "warehouseCodeMap.size:{},warehouseCodeMap.size:{},adjustMap.size:{}",
                "SgBWmsToStoEntryInResultService.businessLogic"),
                sgWmsToStoEntryInResults.size(),
                MapUtils.isNotEmpty(warehouseCodeMap) ? warehouseCodeMap.size() : null,
                MapUtils.isNotEmpty(inNoticesMap) ? inNoticesMap.size() : null,
                MapUtils.isNotEmpty(adjustMap) ? adjustMap.size() : null);

        for (SgBWmsToStoEntryInResult inResult : sgWmsToStoEntryInResults) {
            try {
                String warehouseCode = inResult.getWarehouseCode();
                String noticesBillNo = inResult.getNoticesBillNo();
                String billType = inResult.getBillType();

                //a.是否存在实体仓档案中
                if (MapUtils.isEmpty(warehouseCodeMap) || !warehouseCodeMap.containsKey(warehouseCode)) {
                    updateFail(inResult, "仓库编码[" + warehouseCode + "]在【实体仓档案】中不存在");
                    continue;
                }

                //b.解析报文
                String message = inResult.getMessage();
                if (StringUtils.isEmpty(message)) {
                    updateFail(inResult, "出库单[" + noticesBillNo + "]在报文不存在！");
                    continue;
                }
                log.info(LogUtil.format("SgBWmsToStoEntryInResultService.messageParse message:{}",
                        "SgBWmsToStoEntryInResultService.messageParse"), message);
                List<String> itemCodeList = WmsTaskUtils.messageParseByOrderLinesReturnItemCode(message);
                log.info(LogUtil.format("SgBWmsToStoEntryInResultService.messageParse message:{}",
                        "SgBWmsToStoEntryInResultService.itemCodeList"), JSONObject.toJSONString(itemCodeList));
                if (CollectionUtils.isEmpty(itemCodeList)) {
                    updateFail(inResult, "报文无明细！");
                    continue;
                }

                //c,比对条码信息
                JSONArray array = WmsTaskUtils.compareToSku(itemCodeList);
                if (array.size() > 0) {
                    updateFail(inResult, "商品编码" + JSONArray.toJSONString(array) + "在【商品SKU】中不存在！");
                    continue;
                }

                //d.对比单据类型做出不同逻辑处理
                String error = checkBillType(inResult, inNoticesMap, adjustMap);
                if (StringUtils.isNotEmpty(error)) {
                    updateFail(inResult, error);
                    continue;
                }

                //e.生成不同单据
                if (SgInfConstants.OREDER_TYPE_SCRK.equals(billType) ||
                        SgInfConstants.OREDER_TYPE_CGRK.equals(billType) ||
                        SgInfConstants.OREDER_TYPE_DBRK.equals(billType) ||
                        SgInfConstants.OREDER_TYPE_JYRK.equals(billType)) {
                    //【新增并审核逻辑入库单】
                    ValueHolderV14 v14 = inResultSaveAndSubmit(inNoticesMap.get(noticesBillNo), message);
                    if (!v14.isOK()) {
                        updateFail(inResult, "【新增并审核逻辑入库单】失败：" + v14.getMessage());
                        continue;
                    }

                } else if (SgInfConstants.OREDER_TYPE_CXRK.equals(billType) ||
                        SgInfConstants.OREDER_TYPE_CYRK.equals(billType)) {
                    //则【更新对应库存调整单的明细并审核库存调整单】
                    ValueHolderV14 v14 = adjustSaveAndSubmit(inResult, adjustMap.get(noticesBillNo));
                    if (!v14.isOK()) {
                        updateFail(inResult, "【更新对应库存调整单的明细并审核库存调整单】失败：" + v14.getMessage());
                        continue;
                    }
                }

                //f,更新成功信息
                updateSuccess(inResult);

            } catch (Exception e) {
                log.error(LogUtil.format("exception_has_occured:{}",
                        "SgBWmsToStoEntryInResultService.error", Throwables.getStackTraceAsString(e)));
                updateFail(inResult, "异常：" + e.getMessage());
            }


        }
    }

    /**
     * 校验报文，过滤
     *
     * @param msgList 数据列表
     * @return 过滤后的报文列表
     */
    private List<SgBWmsToStoEntryInResult> messageFilter(List<SgBWmsToStoEntryInResult> msgList) {
        if (CollectionUtils.isEmpty(msgList)) {
            return Collections.emptyList();
        }

        List<SgBWmsToStoEntryInResult> checkRetList = new ArrayList<>();
        for (SgBWmsToStoEntryInResult inResult : msgList) {
            if (noMessageError(inResult)) {
                checkRetList.add(inResult);
            }
        }

        return checkRetList;
    }

    /**
     * 校验单个对象，异常则更新为转化失败
     *
     * @param inResult 报文对象
     * @return 是否没有错误
     */
    private boolean noMessageError(SgBWmsToStoEntryInResult inResult) {
        String message = inResult.getMessage();
        if (StringUtils.isEmpty(message)) {
            updateFail(inResult, "入库单[" + inResult.getNoticesBillNo() + "]报文不存在！");
            return false;
        }

        JSONObject jsonObject = JSONObject.parseObject(message);
        JSONArray orderLines = jsonObject.getJSONArray("orderLines");
        if (CollectionUtils.isEmpty(orderLines)) {
            updateFail(inResult, "入库单[" + inResult.getNoticesBillNo() + "]订单行为空！");
            return false;
        }

        for (Object orderLine : orderLines) {
            JSONObject item = (JSONObject) orderLine;
            BigDecimal actualQty = item.getBigDecimal("actualQty");
            if (Objects.isNull(actualQty) || BigDecimal.ZERO.compareTo(actualQty) == 0) {
                updateFail(inResult, "入库单[" + inResult.getNoticesBillNo() + "]数量非法！");
                return false;
            }
        }

        return true;
    }


    /**
     * 处理业务
     *
     * @param sgWmsToStoEntryInResults 入参
     * @param warehouseCodeMap         实体仓档案
     * @param inNoticesMap             入库通知单
     * @param adjustMap                库存调整单
     */
    private void businessLogicForJd(List<SgBWmsToStoEntryInResult> sgWmsToStoEntryInResults, Map<String, SgCpCPhyWarehouse> warehouseCodeMap,
                                    Map<String, SgBStoInNotices> inNoticesMap, Map<String, SgBStoAdjust> adjustMap) {

        log.info(LogUtil.format("SgBWmsToStoEntryInResultService.businessLogic sgWmsToStoEntryInResults.size:{}," +
                        "warehouseCodeMap.size:{},warehouseCodeMap.size:{},adjustMap.size:{}",
                "SgBWmsToStoEntryInResultService.businessLogic"),
                sgWmsToStoEntryInResults.size(),
                MapUtils.isNotEmpty(warehouseCodeMap) ? warehouseCodeMap.size() : null,
                MapUtils.isNotEmpty(inNoticesMap) ? inNoticesMap.size() : null,
                MapUtils.isNotEmpty(adjustMap) ? adjustMap.size() : null);

        for (SgBWmsToStoEntryInResult inResult : sgWmsToStoEntryInResults) {
            try {
                String warehouseCode = inResult.getWarehouseCode();
                String noticesBillNo = inResult.getNoticesBillNo();
                String billType = inResult.getBillType(); //用于回滚类型

                //a.是否存在实体仓档案中
                if (MapUtils.isEmpty(warehouseCodeMap) || !warehouseCodeMap.containsKey(warehouseCode)) {
                    updateFail(inResult, "仓库编码[" + warehouseCode + "]在【实体仓档案】中不存在");
                    continue;
                }

                //b.解析报文
                String message = inResult.getMessage();
                if (StringUtils.isEmpty(message)) {
                    updateFail(inResult, "出库单[" + noticesBillNo + "]在报文不存在！");
                    continue;
                }
                log.info(LogUtil.format("SgBWmsToStoEntryInResultService.messageParse message:{}",
                        "SgBWmsToStoEntryInResultService.messageParse"), message);
                List<String> itemCodeList = WmsTaskUtils.messageParseByOrderLinesReturnItemCode(message);
                log.info(LogUtil.format("SgBWmsToStoEntryInResultService.messageParse message:{}",
                        "SgBWmsToStoEntryInResultService.itemCodeList"), JSONObject.toJSONString(itemCodeList));
                if (CollectionUtils.isEmpty(itemCodeList)) {
                    Integer isLast = JSONObject.parseObject(message).getJSONObject("entryOrder").getInteger("confirmType");
                    if (inNoticesMap.get(noticesBillNo) != null && isLast == 0) {
                        //无明细直接强制完成
                        forceCompletion(inNoticesMap.get(noticesBillNo), inResult);
                        //f,更新成功信息
                        inResult.setBillType(billType);
                        updateSuccess(inResult);
                        continue;
                    } else {
                        updateFail(inResult, "报文无明细！");
                        continue;
                    }
                }

                //c,比对条码信息
                JSONArray array = WmsTaskUtils.compareToSku(itemCodeList);
                if (array.size() > 0) {
                    updateFail(inResult, "商品编码" + JSONArray.toJSONString(array) + "在【商品SKU】中不存在！");
                    continue;
                }

                //d.对比单据类型做出不同逻辑处理
                String error = checkBillTypeForJd(inResult, inNoticesMap, adjustMap);
                if (StringUtils.isNotEmpty(error)) {
                    updateFail(inResult, error);
                    continue;
                }

                //e.生成不同单据
                if (SgInfConstants.OREDER_TYPE_SCRK.equals(inResult.getBillType()) ||
                        SgInfConstants.OREDER_TYPE_CGRK.equals(inResult.getBillType()) ||
                        SgInfConstants.OREDER_TYPE_DBRK.equals(inResult.getBillType()) ||
                        SgInfConstants.OREDER_TYPE_JYRK.equals(inResult.getBillType())) {
                    //【新增并审核逻辑入库单】
                    SgBWmsToStoEntryInResultService bean = ApplicationContextHandle.getBean(SgBWmsToStoEntryInResultService.class);
                    ValueHolderV14 v14 = bean.inResultSaveAndSubmitForJd(inNoticesMap.get(noticesBillNo), message, inResult);
                    if (!v14.isOK()) {
                        updateFail(inResult, "【新增并审核逻辑入库单】失败：" + v14.getMessage());
                        continue;
                    }

                } else if (SgInfConstants.OREDER_TYPE_CXRK.equals(inResult.getBillType()) ||
                        SgInfConstants.OREDER_TYPE_CYRK.equals(inResult.getBillType())) {
                    //则【更新对应库存调整单的明细并审核库存调整单】
                    ValueHolderV14 v14 = adjustSaveAndSubmit(inResult, adjustMap.get(noticesBillNo));
                    if (!v14.isOK()) {
                        updateFail(inResult, "【更新对应库存调整单的明细并审核库存调整单】失败：" + v14.getMessage());
                        continue;
                    }

                }

                //f,更新成功信息
                inResult.setBillType(billType);
                updateSuccess(inResult);

            } catch (Exception e) {
                log.error(LogUtil.format("exception_has_occured:{}",
                        "SgBWmsToStoEntryInResultService.error", Throwables.getStackTraceAsString(e)));
                updateFail(inResult, "异常：" + e.getMessage());
            }


        }
    }

    /**
     * 【新增并审核逻辑入库单】
     *
     * @param inNotices 入库通知单
     * @param msg       报文
     * @return ValueHolderV14
     */
    private ValueHolderV14 inResultSaveAndSubmit(SgBStoInNotices inNotices, String msg) {

        log.info(LogUtil.format("SgBRefundInTaskService.inResultSaveAndSubmit adjustSaveAndSubmit:{}，msg:{}",
                "SgBRefundInTaskService.inResultSaveAndSubmit"), JSONObject.toJSONString(inNotices), msg);

        SgBStoInResultBillSaveRequest sgStoInResultBillSaveRequest = new SgBStoInResultBillSaveRequest();
        SgBStoInResultSaveRequest inResultSaveRequest = new SgBStoInResultSaveRequest();

        JSONObject request = JSONObject.parseObject(msg);
        JSONObject entryOrder = request.getJSONObject("entryOrder");
        JSONArray items = request.getJSONArray("orderLines");


        inResultSaveRequest.setIsLast(entryOrder.getInteger("confirmType") == 0 ? SgConstants.IS_LAST_YES : SgConstants.IS_LAST_NO);
        inResultSaveRequest.setBillDate(entryOrder.getDate("operateTime"));
        inResultSaveRequest.setInTime(entryOrder.getDate("operateTime"));

        inResultSaveRequest.setSgBStoInNoticesId(inNotices.getId());
        inResultSaveRequest.setSgBStoInNoticesNo(inNotices.getBillNo());

        inResultSaveRequest.setSourceBillType(inNotices.getSourceBillType());
        inResultSaveRequest.setSourceBillId(inNotices.getSourceBillId());
        inResultSaveRequest.setSourceBillNo(inNotices.getSourceBillNo());
        inResultSaveRequest.setSourceBillSourceBillType(inNotices.getSourceBillSourceBillType());


        inResultSaveRequest.setSenderEcode(inNotices.getSenderEcode());
        inResultSaveRequest.setSenderName(inNotices.getSenderName());
        //wms单号
        inResultSaveRequest.setWmsBillNo(entryOrder.getString("entryOrderId"));

        List<SgBStoInNoticesItem> noticesItems = stoInNoticesItemMapper.selectList(new LambdaQueryWrapper<SgBStoInNoticesItem>()
                .eq(SgBStoInNoticesItem::getSgBStoInNoticesId, inNotices.getId()));
        if (CollectionUtils.isEmpty(noticesItems)) {
            return new ValueHolderV14(ResultCode.FAIL, "入库通知单明细为空！入库通知单id：" + inNotices.getId());
        }

        SgBStoInNoticesItem noticesItem = noticesItems.get(0);
        inResultSaveRequest.setCpCStoreId(noticesItem.getCpCStoreId());
        inResultSaveRequest.setCpCStoreEcode(noticesItem.getCpCStoreEcode());
        inResultSaveRequest.setCpCStoreEname(noticesItem.getCpCStoreEname());

        inResultSaveRequest.setWmsBillNo(entryOrder.getString("entryOrderId"));

        List<SgBStoInResultItemSaveRequest> itemSaveRequestList = new ArrayList<>();
        items.forEach(x -> {
            JSONObject item = (JSONObject) x;
            SgBStoInResultItemSaveRequest itemSaveRequest = new SgBStoInResultItemSaveRequest();
            itemSaveRequest.setId(-1L);
            itemSaveRequest.setPsCSkuEcode(item.getString("itemCode"));
            String batchCode = item.getString("batchCode");
            itemSaveRequest.setProduceDate(StringUtils.isEmpty(batchCode) || batchCode.equals(SgConstantsIF.DEFAULT_PRODUCE_DATE) ?
                    SgConstantsIF.DEFAULT_PRODUCE_DATE :
                    DateUtils.formatSync8(DateUtils.parseSync8(item.getString("batchCode"), DateUtils.PATTERN_DATE), DateUtils.DATE_PATTERN));
            itemSaveRequest.setQty(item.getBigDecimal("actualQty"));
            itemSaveRequestList.add(itemSaveRequest);
        });

        CommonCacheValUtils.setSkuInfoBySkuCodeList(itemSaveRequestList);

        sgStoInResultBillSaveRequest.setInItemResultSaveRequestList(itemSaveRequestList);
        sgStoInResultBillSaveRequest.setInResultSaveRequest(inResultSaveRequest);
        sgStoInResultBillSaveRequest.setObjId(-1L);
        sgStoInResultBillSaveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());

        List<SgBStoInResultBillSaveRequest> list = new ArrayList<>();
        list.add(sgStoInResultBillSaveRequest);
        log.info(LogUtil.format("SgBRefundInTaskService.inResultSaveAndSubmit:{}",
                "SgBRefundInTaskService.inResultSaveAndSubmit"), JSONObject.toJSONString(list));
        ValueHolderV14 v14 = inResultSaveAndSubmitService.saveAndAuditBillWithTrans(list);
        log.info(LogUtil.format("SgBRefundInTaskService.inResultSaveAndSubmit ValueHolderV14:{}",
                "SgBRefundInTaskService.inResultSaveAndSubmit.ValueHolderV14"), JSONObject.toJSONString(v14));

        return v14;
    }

    /**
     * 【新增并审核逻辑入库单】(给京云仓那大爷搞的)
     *
     * @param inNotices   入库通知单
     * @param msg         报文
     * @param oldInResult
     * @return ValueHolderV14
     */
    public ValueHolderV14 inResultSaveAndSubmitForJd(SgBStoInNotices inNotices, String msg, SgBWmsToStoEntryInResult oldInResult) {

        log.info(LogUtil.format("SgBRefundInTaskService.inResultSaveAndSubmitForJd adjustSaveAndSubmit:{}，msg:{}",
                "SgBRefundInTaskService.inResultSaveAndSubmitForJd"), JSONObject.toJSONString(inNotices), msg);

        SgBStoInResultBillSaveRequest sgStoInResultBillSaveRequest = new SgBStoInResultBillSaveRequest();
        SgBStoInResultSaveRequest inResultSaveRequest = new SgBStoInResultSaveRequest();

        JSONObject request = JSONObject.parseObject(msg);
        JSONObject entryOrder = request.getJSONObject("entryOrder");
//        JSONObject orderLines = request.getJSONObject("orderLines");
        JSONArray items = request.getJSONArray("orderLines");

        String isLast = entryOrder.getInteger("confirmType") == 0 ? SgConstants.IS_LAST_YES : SgConstants.IS_LAST_NO;
        inResultSaveRequest.setIsLast(SgConstants.IS_LAST_NO);
        inResultSaveRequest.setBillDate(entryOrder.getDate("operateTime"));
        inResultSaveRequest.setInTime(entryOrder.getDate("operateTime"));

        inResultSaveRequest.setSgBStoInNoticesId(inNotices.getId());
        inResultSaveRequest.setSgBStoInNoticesNo(inNotices.getBillNo());

        inResultSaveRequest.setSourceBillType(inNotices.getSourceBillType());
        inResultSaveRequest.setSourceBillId(inNotices.getSourceBillId());
        inResultSaveRequest.setSourceBillNo(inNotices.getSourceBillNo());
        inResultSaveRequest.setSourceBillSourceBillType(inNotices.getSourceBillSourceBillType());


        inResultSaveRequest.setSenderEcode(inNotices.getSenderEcode());
        inResultSaveRequest.setSenderName(inNotices.getSenderName());
        //wms单号
        inResultSaveRequest.setWmsBillNo(entryOrder.getString("entryOrderId"));

        List<SgBStoInNoticesItem> noticesItems = stoInNoticesItemMapper.selectList(new LambdaQueryWrapper<SgBStoInNoticesItem>()
                .eq(SgBStoInNoticesItem::getSgBStoInNoticesId, inNotices.getId()));
        if (CollectionUtils.isEmpty(noticesItems)) {
            return new ValueHolderV14(ResultCode.FAIL, "入库通知单明细为空！入库通知单id：" + inNotices.getId());
        }

        SgBStoInNoticesItem noticesItem = noticesItems.get(0);
        inResultSaveRequest.setCpCStoreId(noticesItem.getCpCStoreId());
        inResultSaveRequest.setCpCStoreEcode(noticesItem.getCpCStoreEcode());
        inResultSaveRequest.setCpCStoreEname(noticesItem.getCpCStoreEname());

        inResultSaveRequest.setWmsBillNo(entryOrder.getString("entryOrderId"));

        List<SgBStoInResultItemSaveRequest> itemSaveRequestList = new ArrayList<>();
        items.forEach(x -> {
            JSONObject item = (JSONObject) x;
            SgBStoInResultItemSaveRequest itemSaveRequest = new SgBStoInResultItemSaveRequest();
            itemSaveRequest.setId(-1L);
            itemSaveRequest.setPsCSkuEcode(item.getString("itemCode"));
            String batchCode = item.getString("batchCode");
            itemSaveRequest.setProduceDate(StringUtils.isEmpty(batchCode) || batchCode.equals(SgConstantsIF.DEFAULT_PRODUCE_DATE) ?
                    SgConstantsIF.DEFAULT_PRODUCE_DATE :
                    DateUtils.formatSync8(DateUtils.parseSync8(item.getString("batchCode"), DateUtils.PATTERN_DATE), DateUtils.DATE_PATTERN));
            itemSaveRequest.setQty(item.getBigDecimal("actualQty"));
            itemSaveRequestList.add(itemSaveRequest);
        });

        CommonCacheValUtils.setSkuInfoBySkuCodeList(itemSaveRequestList);

        sgStoInResultBillSaveRequest.setInItemResultSaveRequestList(itemSaveRequestList);
        sgStoInResultBillSaveRequest.setInResultSaveRequest(inResultSaveRequest);
        sgStoInResultBillSaveRequest.setObjId(-1L);
        sgStoInResultBillSaveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());

        List<SgBStoInResultBillSaveRequest> list = new ArrayList<>();
        list.add(sgStoInResultBillSaveRequest);
        ValueHolderV14<List<SgBStoInResultBillSubmitResult>> v14 = new ValueHolderV14();
        try {
            log.info(LogUtil.format("SgBRefundInTaskService.inResultSaveAndSubmit:{}",
                    "SgBRefundInTaskService.inResultSaveAndSubmit"), JSONObject.toJSONString(list));
            SgBStoInResultSaveAndSubmitService bean = ApplicationContextHandle.getBean(SgBStoInResultSaveAndSubmitService.class);
            v14 = bean.saveAndAuditBill(list);
            log.info(LogUtil.format("SgBRefundInTaskService.inResultSaveAndSubmit ValueHolderV14:{}",
                    "SgBRefundInTaskService.inResultSaveAndSubmit.ValueHolderV14"), JSONObject.toJSONString(v14));

            //逻辑入库单新增并审核通过且仓库是否是最后一次入库，查找所有回传结果单，当数量小于通知数量调用强制完成接口
            if (v14.isOK() && SgConstants.IS_ACTIVE_Y.equals(isLast)) {
                forceCompletion(inNotices, oldInResult);
            }
        } catch (Exception e) {
            if (v14 != null && v14.getData() != null && CollectionUtils.isNotEmpty(v14.getData())) {
                SgBStoInResultBillSubmitResult result = v14.getData().get(0);
                StorageBasicUtils.rollbackStorage(result.getRedisBillFtpKeyList(), SystemUserResource.getRootUser());
                AssertUtils.logAndThrow(e.getMessage());
            }
        }

        return v14;
    }

    private void forceCompletion(SgBStoInNotices inNotices, SgBWmsToStoEntryInResult oldInResult) {
        List<SgBWmsToStoEntryInResult> stoEntryInResultList = wmsToStoEntryInResultMapper.selectList(new LambdaQueryWrapper<SgBWmsToStoEntryInResult>()
                .eq(SgBWmsToStoEntryInResult::getNoticesBillNo, inNotices.getBillNo())
                .eq(SgBWmsToStoEntryInResult::getWmsWarehouseType, ThirdWmsTypeEnum.JDWMS.getCode()));
        if (CollectionUtils.isEmpty(stoEntryInResultList)) {
            throw new NDSException("强制完成时，根据入库通知单号未查询到入库回传结果");
        }
        List<SgBWmsToStoEntryInResult> noTransformStatusList = stoEntryInResultList.stream()
                .filter(s -> SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS != s.getTransformStatus()
                        && !s.getId().equals(oldInResult.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noTransformStatusList)) {
            throw new NDSException("有未转化成功回传入库结果单，不允许强制完成");
        }
        List<SgBStoInNoticesItem> inNoticesItemList = stoInNoticesItemMapper.selectList(new LambdaQueryWrapper<SgBStoInNoticesItem>()
                .eq(SgBStoInNoticesItem::getSgBStoInNoticesId, inNotices.getId()));
        if (CollectionUtils.isEmpty(inNoticesItemList)) {
            throw new NDSException("强制完成时，根据入库通知单号未查询到入库通知单明细");
        }
        Set<String> skuCodes = inNoticesItemList.stream().map(SgBStoInNoticesItem::getPsCSkuEcode).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(skuCodes)) {
            throw new NDSException("强制完成时，根据入库通知单号未查询到入库通知单明细或sku编码");
        }

        Map<String, BigDecimal> skuNumMap = new HashMap<>();
        Map<String, BigDecimal> skuOutNumMap = new HashMap<>();
        for (String skuCode : skuCodes) {
            skuNumMap.put(skuCode, BigDecimal.ZERO);
            skuOutNumMap.put(skuCode, BigDecimal.ZERO);
        }
        BigDecimal totalNum = BigDecimal.ZERO;
        for (SgBWmsToStoEntryInResult inResult : stoEntryInResultList) {
            JSONObject message = JSONObject.parseObject(inResult.getMessage());
            JSONArray orderLines = message.getJSONArray("orderLines");
            if (CollectionUtils.isNotEmpty(orderLines)) {
                for (Object x : orderLines) {
                    JSONObject item = (JSONObject) x;
                    String itemCode = item.getString("itemCode");
                    BigDecimal qty = item.getBigDecimal("actualQty");
                    totalNum = totalNum.add(qty);
                    BigDecimal newQty = skuNumMap.get(itemCode).add(qty);
                    skuNumMap.put(itemCode, newQty);
                }
            }
        }
        if (totalNum.compareTo(inNotices.getTotQty()) < 0) {
            if (SgConstantsIF.BILL_OTHER_DELIVERY == inNotices.getSourceBillType()) {
                SgBStoOtherDeliveryForceCompletionRequest completionRequest =
                        new SgBStoOtherDeliveryForceCompletionRequest();
                List<SgBStoOtherDeliveryForceCompletionItemRequest> itemRequestList = new ArrayList<>();
                completionRequest.setLoginUser(SystemUserResource.getRootUser());
                completionRequest.setSgBStoInNoticesBillNo(inNotices.getBillNo());
                for (String skuCode : skuCodes) {
                    SgBStoOtherDeliveryForceCompletionItemRequest itemRequest =
                            new SgBStoOtherDeliveryForceCompletionItemRequest();
                    itemRequest.setInQty(skuNumMap.get(skuCode));
                    itemRequest.setPsCSkuEcode(skuCode);
                    itemRequestList.add(itemRequest);
                }
                completionRequest.setItemRequestList(itemRequestList);
                ValueHolderV14<SgBStoForceCompletionResult> otherDeliveryForceCompletionV14 =
                        sgBStoOtherDeliveryForceCompletionService.otherDeliveryForceCompletion(completionRequest);
                if (!otherDeliveryForceCompletionV14.isOK()) {
                    throw new NDSException("其他入库单强制完成失败：" + otherDeliveryForceCompletionV14.getMessage());
                }
            }
            if (SgConstantsIF.BILL_STO_TRANSFER == inNotices.getSourceBillType()) {
                //查询逻辑出库单
                List<SgBStoOutResult> sgBStoOutResults = sgBStoOutResultMapper.selectList(new LambdaQueryWrapper<SgBStoOutResult>()
                        .eq(SgBStoOutResult::getSourceBillNo, inNotices.getSourceBillNo()));
                if (CollectionUtils.isEmpty(sgBStoOutResults)) {
                    throw new NDSException("逻辑调拨单强制完成时，未查询到逻辑出库单");
                }
                Set<Long> SgBStoOutResultIds = sgBStoOutResults.stream().map(SgBStoOutResult::getId).collect(Collectors.toSet());
                List<SgBStoOutResultItem> sgBStoOutResultItems = sgBStoOutResultItemMapper.selectList(new LambdaQueryWrapper<SgBStoOutResultItem>()
                        .in(SgBStoOutResultItem::getSgBStoOutResultId, SgBStoOutResultIds));
                if (CollectionUtils.isEmpty(sgBStoOutResultItems)) {
                    throw new NDSException("逻辑调拨单强制完成时，未查询到逻辑出库单明细");
                }
                for (SgBStoOutResultItem sgBStoOutResultItem : sgBStoOutResultItems) {
                    BigDecimal decimal = skuOutNumMap.get(sgBStoOutResultItem.getPsCSkuEcode()).add(sgBStoOutResultItem.getQty());
                    skuOutNumMap.put(sgBStoOutResultItem.getPsCSkuEcode(), decimal);
                }

                SgBStoTransferForceCompletionRequest completionRequest = new SgBStoTransferForceCompletionRequest();
                List<SgBStoTransferForceCompletionItemRequest> itemRequestList = new ArrayList<>();
                completionRequest.setLoginUser(SystemUserResource.getRootUser());
                completionRequest.setSgBStoInNoticesBillNo(inNotices.getBillNo());
                for (String skuCode : skuCodes) {
                    SgBStoTransferForceCompletionItemRequest itemRequest =
                            new SgBStoTransferForceCompletionItemRequest();
                    itemRequest.setOutQty(skuOutNumMap.get(skuCode));
                    itemRequest.setInQty(skuNumMap.get(skuCode));
                    itemRequest.setPsCSkuEcode(skuCode);
                    itemRequestList.add(itemRequest);
                }
                completionRequest.setItemRequestList(itemRequestList);
                ValueHolderV14<SgBStoForceCompletionResult> transferForceCompletionV14 =
                        sgBStoTransferForceCompletionService.transferForceCompletion(completionRequest);
                if (!transferForceCompletionV14.isOK()) {
                    throw new NDSException("逻辑调拨单强制完成失败：" + transferForceCompletionV14.getMessage());
                }
            }
        }
    }

    /**
     * 更新对应库存调整单的明细并审核库存调整单
     *
     * @param inResult 入参
     * @return ValueHolderV14
     */
    private ValueHolderV14 adjustSaveAndSubmit(SgBWmsToStoEntryInResult inResult, SgBStoAdjust adjust) {

        log.info(LogUtil.format("SgBWmsToStoEntryInResultService.adjustSaveAndSubmit inResult:{}",
                "SgBWmsToStoEntryInResultService.adjustSaveAndSubmit"), JSONObject.toJSONString(inResult));

        try {
            String msg = inResult.getMessage();
            SgBWmsToStoEntryInResultService bean = ApplicationContextHandle.getBean(SgBWmsToStoEntryInResultService.class);
            return bean.adjustSaveAndSubmit(adjust, msg);
        } catch (Exception e) {
            log.error(LogUtil.format("exception_has_occured:{}",
                    "SgBWmsToStoEntryInResultService.error", Throwables.getStackTraceAsString(e)));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }

    }

    /**
     * 更新对应库存调整单的明细并审核库存调整单
     *
     * @param adjust 库存调整单
     * @param msg    报文
     * @return return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> adjustSaveAndSubmit(SgBStoAdjust adjust, String msg) {

        log.info(LogUtil.format("SgBRefundInTaskService.adjustSaveAndSubmit adjust:{}",
                "SgBRefundInTaskService.adjustSaveAndSubmit"), JSONObject.toJSONString(adjust));

        List<SgBStoAdjustItem> sgStoAdjustItems = adjustItemMapper.selectList(new LambdaQueryWrapper<SgBStoAdjustItem>()
                .eq(SgBStoAdjustItem::getSgBStoAdjustId, adjust.getId()));
        Map<String, SgBStoAdjustItem> skuItemIdMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(sgStoAdjustItems)) {
            skuItemIdMap = sgStoAdjustItems.stream().collect(Collectors.toMap(SgBStoAdjustItem::getPsCSkuEcode,
                    item -> item, (v1, v2) -> v1));
        }
        if (sgStoAdjustItems.size() > 0) {
            adjustItemMapper.delete(new LambdaQueryWrapper<SgBStoAdjustItem>()
                    .eq(SgBStoAdjustItem::getSgBStoAdjustId, adjust.getId()));
        }

        SgBStoAdjustSaveRequest adjustSaveRequest = new SgBStoAdjustSaveRequest();
        SgBStoAdjustMainSaveRequest adjustMainSaveRequest = new SgBStoAdjustMainSaveRequest();
        List<SgBStoAdjustItemSaveRequest> itemSaveRequestList = new ArrayList<>();
        BeanUtils.copyProperties(adjust, adjustMainSaveRequest);
        adjustMainSaveRequest.setBillNo(null);
        JSONObject request = JSONObject.parseObject(msg);
        JSONArray items = request.getJSONArray("orderLines");
        JSONObject entryOrder = request.getJSONObject("entryOrder");
        adjustMainSaveRequest.setWmsBillNo(entryOrder.getString("entryOrderId"));
        for (Object x : items) {
            JSONObject item = (JSONObject) x;
            SgBStoAdjustItemSaveRequest itemSaveRequest = new SgBStoAdjustItemSaveRequest();
            //获取之前的来源单明细id，可重复
            if (skuItemIdMap.containsKey(item.getString("itemCode"))) {
                SgBStoAdjustItem itemCode = skuItemIdMap.get(item.getString("itemCode"));
                itemSaveRequest.setSourceBillItemId(itemCode.getSourceBillItemId());
                itemSaveRequest.setSourceBillNo(itemCode.getSourceBillNo());
            }
            itemSaveRequest.setQty(item.getBigDecimal("actualQty"));
            String batchCode = item.getString("batchCode");
            itemSaveRequest.setProduceDate(StringUtils.isEmpty(batchCode) || SgConstantsIF.DEFAULT_PRODUCE_DATE.equals(batchCode) ?
                    SgConstantsIF.DEFAULT_PRODUCE_DATE :
                    DateUtils.formatSync8(DateUtils.parseSync8(item.getString("batchCode"), DateUtils.PATTERN_DATE), DateUtils.DATE_PATTERN));
            itemSaveRequest.setStorageType(item.getString("inventoryType"));
            itemSaveRequest.setPsCSkuEcode(item.getString("itemCode"));
            itemSaveRequestList.add(itemSaveRequest);
        }
        adjustSaveRequest.setItems(itemSaveRequestList);
        adjustSaveRequest.setMainRequest(adjustMainSaveRequest);
        adjustSaveRequest.setObjId(adjust.getId());
        adjustSaveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());

        log.info(LogUtil.format("SgBRefundInTaskService.adjustSaveAndSubmit adjustSaveAndSubmit:{}",
                "SgBRefundInTaskService.adjustSaveAndSubmit"), JSONObject.toJSONString(adjustSaveRequest));
        ValueHolderV14<SgR3BaseResult> v14 = adjustSaveService.saveAndSubmit(adjustSaveRequest);
        log.info(LogUtil.format("SgBRefundInTaskService.adjustSaveAndSubmit ValueHolderV14:{}",
                "SgBRefundInTaskService.ValueHolderV14"), JSONObject.toJSONString(v14));

        if (!v14.isOK()) {
            AssertUtils.logAndThrow(v14.getMessage());
        }

        return v14;
    }

    /**
     * 校验订单类型
     *
     * @param inResult     入参
     * @param inNoticesMap 入库通知单
     * @param adjustMap    库存调整单
     * @return String 错误信息
     */
    private String checkBillType(SgBWmsToStoEntryInResult inResult, Map<String, SgBStoInNotices> inNoticesMap,
                                 Map<String, SgBStoAdjust> adjustMap) {
        String billType = inResult.getBillType();
        String noticesBillNo = inResult.getNoticesBillNo();
        if (StringUtils.isEmpty(billType)) {
            return "单据类型为空";
        }

        if (SgInfConstants.OREDER_TYPE_SCRK.equals(billType) ||
                SgInfConstants.OREDER_TYPE_CGRK.equals(billType) ||
                SgInfConstants.OREDER_TYPE_DBRK.equals(billType)) {

            if (!inNoticesMap.containsKey(noticesBillNo)) {
                return "入库通知单不存在";
            }

        } else if (SgInfConstants.OREDER_TYPE_CXRK.equals(billType) ||
                SgInfConstants.OREDER_TYPE_CYRK.equals(billType)) {
            if (!adjustMap.containsKey(inResult.getNoticesBillNo())) {
                return "库存调整单不存在或者状态不合法";
            }

        }

        List<String> batchCodeList = new ArrayList<>();
        List<String> itemCodeList = new ArrayList<>();
        Map<String, String> map = WmsTaskUtils.messageParseReturnSkuAndBatch(inResult.getMessage(), batchCodeList, itemCodeList);
        if (MapUtils.isEmpty(map)) {
            return "报文解析无明细不存在";
        }
        //根据“入库单编码 entryOrderCode”+“商品编码 itemCode”+“批次编号batchCode”判断对应【库存调整单】【条码明细】中是否存在对应批次库存
        if (SgInfConstants.OREDER_TYPE_CYRK.equals(billType)) {
            SgBStoAdjust stoAdjust = adjustMap.get(noticesBillNo);
            List<SgBStoAdjustItem> sgStoAdjustItems = adjustItemMapper.selectList(new LambdaQueryWrapper<SgBStoAdjustItem>()
                    .eq(SgBStoAdjustItem::getSgBStoAdjustId, stoAdjust.getId())
                    .in(SgBStoAdjustItem::getProduceDate, batchCodeList)
                    .in(SgBStoAdjustItem::getPsCSkuEcode, itemCodeList));
            if (CollectionUtils.isEmpty(sgStoAdjustItems)) {
                return "存在商品+批次，不在库存调整单发内的非法数据";
            }

            Map<String, List<SgBStoAdjustItem>> itemMap = sgStoAdjustItems.stream().collect(Collectors.groupingBy(item ->
                    item.getPsCSkuEcode() + SgConstantsIF.MAP_KEY_DIVIDER + item.getProduceDate()));

            log.info(LogUtil.format("itemMap.key:{},map.key:{}", "SgBRefundInTaskService"),
                    JSONObject.toJSONString(itemMap.keySet()), JSONObject.toJSONString(map.keySet()));

            for (String key : map.keySet()) {
                if (!itemMap.containsKey(key)) {
                    return "存在商品+批次，不在入库通知下发内的非法数据";
                }
            }

            //根据“入库单编码 entryOrderCode”+“商品编码 itemCode”+“批次编号batchCode”判断对应【入库通知单】【条码明细】中是否存在对应批次库存
        } else if (SgInfConstants.OREDER_TYPE_DBRK.equals(billType)) {
            SgBStoInNotices sgStoInNotices = inNoticesMap.get(noticesBillNo);

            List<SgBStoInNoticesItem> noticesItems = stoInNoticesItemMapper.selectList(new LambdaQueryWrapper<SgBStoInNoticesItem>()
                    .eq(SgBStoInNoticesItem::getSgBStoInNoticesId, sgStoInNotices.getId())
                    .in(SgBStoInNoticesItem::getProduceDate, batchCodeList)
                    .in(SgBStoInNoticesItem::getPsCSkuEcode, itemCodeList));

            if (CollectionUtils.isEmpty(noticesItems)) {
                return "存在商品+批次，不在入库通知下发内的非法数据";
            }

            Map<String, List<SgBStoInNoticesItem>> itemMap = noticesItems.stream().collect(Collectors.groupingBy(item ->
                    item.getPsCSkuEcode() + SgConstantsIF.MAP_KEY_DIVIDER + item.getProduceDate()));

            log.info(LogUtil.format("itemMap.key:{},map.key:{}", "SgBRefundInTaskService"),
                    JSONObject.toJSONString(itemMap.keySet()), JSONObject.toJSONString(map.keySet()));

            for (String key : map.keySet()) {
                if (!itemMap.containsKey(key)) {
                    return "存在商品+批次，不在入库通知下发内的非法数据";
                }
            }
        }


        return null;
    }

    /**
     * 校验订单类型
     *
     * @param inResult     入参
     * @param inNoticesMap 入库通知单
     * @param adjustMap    库存调整单
     * @return String 错误信息
     */
    private String checkBillTypeForJd(SgBWmsToStoEntryInResult inResult, Map<String, SgBStoInNotices> inNoticesMap,
                                      Map<String, SgBStoAdjust> adjustMap) {
        String noticesBillNo = inResult.getNoticesBillNo();
        if (StringUtils.isEmpty(inResult.getBillType())) {
            return "单据类型为空";
        }

        if (SgInfConstants.OREDER_TYPE_SCRK.equals(inResult.getBillType()) ||
                SgInfConstants.OREDER_TYPE_CGRK.equals(inResult.getBillType()) ||
                SgInfConstants.OREDER_TYPE_DBRK.equals(inResult.getBillType())) {

            if (!inNoticesMap.containsKey(noticesBillNo)) {
                inResult.setBillType(SgInfConstants.OREDER_TYPE_CXRK);
            }

        }
        if (SgInfConstants.OREDER_TYPE_CXRK.equals(inResult.getBillType()) ||
                SgInfConstants.OREDER_TYPE_CYRK.equals(inResult.getBillType())) {
            if (!adjustMap.containsKey(inResult.getNoticesBillNo())) {
                return "库存调整单不存在或者状态不合法";
            }

        }

        List<String> batchCodeList = new ArrayList<>();
        List<String> itemCodeList = new ArrayList<>();
        Map<String, String> map = WmsTaskUtils.messageParseReturnSkuAndBatch(inResult.getMessage(), batchCodeList, itemCodeList);
        if (MapUtils.isEmpty(map)) {
            return "报文解析无明细不存在";
        }
        //根据“入库单编码 entryOrderCode”+“商品编码 itemCode”+“批次编号batchCode”判断对应【库存调整单】【条码明细】中是否存在对应批次库存
        if (SgInfConstants.OREDER_TYPE_CYRK.equals(inResult.getBillType())) {
            SgBStoAdjust stoAdjust = adjustMap.get(noticesBillNo);
            List<SgBStoAdjustItem> sgStoAdjustItems = adjustItemMapper.selectList(new LambdaQueryWrapper<SgBStoAdjustItem>()
                    .eq(SgBStoAdjustItem::getSgBStoAdjustId, stoAdjust.getId())
                    .in(SgBStoAdjustItem::getProduceDate, batchCodeList)
                    .in(SgBStoAdjustItem::getPsCSkuEcode, itemCodeList));
            if (CollectionUtils.isEmpty(sgStoAdjustItems)) {
                return "存在商品+批次，不在库存调整单发内的非法数据";
            }
            for (SgBStoAdjustItem item : sgStoAdjustItems) {
                if (!map.containsKey(item.getPsCSkuEcode() + SgConstantsIF.MAP_KEY_DIVIDER + item.getProduceDate())) {
                    return "存在商品+批次，不在库存调整单发内的非法数据";
                }
            }
            //根据“入库单编码 entryOrderCode”+“商品编码 itemCode”+“批次编号batchCode”判断对应【入库通知单】【条码明细】中是否存在对应批次库存
        } else if (SgInfConstants.OREDER_TYPE_DBRK.equals(inResult.getBillType())) {
            SgBStoInNotices sgStoInNotices = inNoticesMap.get(noticesBillNo);
            List<SgBStoInNoticesItem> noticesItems = stoInNoticesItemMapper.selectList(new LambdaQueryWrapper<SgBStoInNoticesItem>()
                    .eq(SgBStoInNoticesItem::getSgBStoInNoticesId, sgStoInNotices.getId())
                    .in(SgBStoInNoticesItem::getProduceDate, batchCodeList)
                    .in(SgBStoInNoticesItem::getPsCSkuEcode, itemCodeList));

            if (CollectionUtils.isEmpty(noticesItems)) {
                return "存在商品+批次，不在入库通知下发内的非法数据";
            }
            for (SgBStoInNoticesItem item : noticesItems) {
                if (!map.containsKey(item.getPsCSkuEcode() + SgConstantsIF.MAP_KEY_DIVIDER + item.getProduceDate())) {
                    return "存在商品+批次，不在入库通知下发内的非法数据";
                }
            }
        }


        return null;
    }


    /**
     * 更新错误信息
     *
     * @param sgWmsToOrderOutStockResults 中间表数据
     * @param failedReason                失败原因
     */
    private void updateFail(SgBWmsToStoEntryInResult sgWmsToOrderOutStockResults, String failedReason) {
        SgBWmsToStoEntryInResult update = new SgBWmsToStoEntryInResult();
        update.setId(sgWmsToOrderOutStockResults.getId());
        int failedConut = Optional.ofNullable(sgWmsToOrderOutStockResults.getFailedCount()).orElse(0) + 1;
        update.setFailedCount(failedConut);
        update.setFailedReason(failedReason.length() > 500 ? failedReason.substring(0, 500) : failedReason);
        update.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED);
        wmsToStoEntryInResultMapper.updateById(update);
    }

    /**
     * 更新成功信息
     *
     * @param inTask 中间表数据
     */
    private void updateSuccess(SgBWmsToStoEntryInResult inTask) {
        SgBWmsToStoEntryInResult update = new SgBWmsToStoEntryInResult();
        update.setId(inTask.getId());
        update.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS);
        update.setTransformationData(new Date());
        wmsToStoEntryInResultMapper.update(update, new LambdaUpdateWrapper<SgBWmsToStoEntryInResult>()
                .set(SgBWmsToStoEntryInResult::getFailedReason, null)
                .eq(SgBWmsToStoEntryInResult::getId, inTask.getId()));
    }
}
