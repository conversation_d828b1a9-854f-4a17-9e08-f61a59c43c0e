package com.burgeon.r3.inf.services.jingdong;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.in.SgBWmsToStoStockOutResult;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.in.SgBWmsToStoStockOutResultMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022/7/22 16:50
 * @Description
 */
@Slf4j
@Component
public class JdrtsCompleteFeedbackMsgService {

    @Autowired
    private SgBWmsToStoStockOutResultMapper sgBWmsToStoStockOutResultMapper;

    public ValueHolderV14 rtsCompleteFeedbackMsg (String msg) {

        JSONObject bodyMsg = JSONObject.parseObject(msg).getJSONObject("bodyMsg");

        JSONObject rtsOrderModel = bodyMsg.getJSONObject("rtsOrderModel");
        //单据类型
        String rtsType = rtsOrderModel.getString("rtsType");
        //仓库编码
        String warehouseCode = rtsOrderModel.getString("warehouseCode");
        //出库单号
        String rtsOrderCode = rtsOrderModel.getString("rtsOrderCode");
        //WMS单号
        String rtsOrderId = rtsOrderModel.getString("rtsOrderId");

        try {
            SgBWmsToStoStockOutResult sgBWmsToStoStockOutResult1 = sgBWmsToStoStockOutResultMapper.selectOne(new LambdaQueryWrapper<SgBWmsToStoStockOutResult>()
                    .eq(SgBWmsToStoStockOutResult::getNoticesBillNo, rtsOrderCode)
                    .eq(SgBWmsToStoStockOutResult::getIsactive,"Y")
                    .last("limit 1"));

            if (Objects.nonNull(sgBWmsToStoStockOutResult1)) {
                log.error(LogUtil.format("京东B2B出库单WMS回传重复.messageBody={}", "京东B2B出库单WMS回传重复", sgBWmsToStoStockOutResult1), msg);
                return new ValueHolderV14<>(ResultCode.FAIL, "京东B2B出库单WMS回传重复");
            } else {

                SgBWmsToStoStockOutResult sgBWmsToStoStockOutResult = new SgBWmsToStoStockOutResult();
                sgBWmsToStoStockOutResult.setId(ModelUtil.getSequence(SgConstants.SG_B_WMS_TO_STO_ENTRY_IN_RESULT));
                sgBWmsToStoStockOutResult.setNoticesBillNo(rtsOrderCode);
                sgBWmsToStoStockOutResult.setWarehouseCode(warehouseCode);
                sgBWmsToStoStockOutResult.setBillType(rtsType);
                sgBWmsToStoStockOutResult.setWmsBillCode(rtsOrderId);
                sgBWmsToStoStockOutResult.setMessage(msg);

                sgBWmsToStoStockOutResult.setWmsWarehouseType(ThirdWmsTypeEnum.JDWMS.getCode());
                sgBWmsToStoStockOutResult.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT);
                sgBWmsToStoStockOutResult.setFailedCount(NumberUtils.INTEGER_ZERO);
                sgBWmsToStoStockOutResult.setIsactive(SgConstants.IS_ACTIVE_Y);
                StorageUtils.setBModelDefalutData(sgBWmsToStoStockOutResult, R3SystemUserResource.getSystemRootUser());
                sgBWmsToStoStockOutResultMapper.insert(sgBWmsToStoStockOutResult);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("京东B2B出库单WMS回传异常={}", "京东B2B出库单WMS回传异常"), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }

        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("success"));
    }
}
