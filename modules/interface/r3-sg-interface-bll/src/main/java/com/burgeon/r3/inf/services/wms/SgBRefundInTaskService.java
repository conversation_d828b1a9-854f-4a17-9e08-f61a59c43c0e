package com.burgeon.r3.inf.services.wms;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.inf.utils.WmsTaskUtils;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.store.adjust.SgBStoAdjust;
import com.burgeon.r3.sg.core.model.table.store.adjust.SgBStoAdjustItem;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInNotices;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.DateUtils;
import com.burgeon.r3.sg.inf.common.SgInfConstants;
import com.burgeon.r3.sg.inf.model.request.wms.in.ReturnorderConfirmRequest;
import com.burgeon.r3.sg.inf.model.request.wms.in.SgBRefundInTaskRequest;
import com.burgeon.r3.sg.inf.model.result.oms.SgBRefundInTaskResult;
import com.burgeon.r3.sg.store.mapper.adjust.SgBStoAdjustItemMapper;
import com.burgeon.r3.sg.store.mapper.adjust.SgBStoAdjustMapper;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustMainSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustSaveRequest;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSaveService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-07-15 09:44
 * @Description: 退换入库确认中间表定时任务
 */

@Slf4j
@Component
public class SgBRefundInTaskService {

    @Autowired
    private SgBStoAdjustMapper adjustMapper;
    @Autowired
    private SgBStoAdjustItemMapper adjustItemMapper;
    @Autowired
    private SgBStoAdjustSaveService adjustSaveService;

    /**
     * 退换入库确认中间表check
     * ps：oms调用
     *
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     */
    public ValueHolderV14<SgBRefundInTaskResult> execute(List<SgBRefundInTaskRequest> inTaskList) {
        ValueHolderV14<SgBRefundInTaskResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "退换入库确认中间表定时任务（WMS->中台)定时任务成功！");
        log.info(LogUtil.format("退换入库确认中间表定时转化任务参数检查-开始，size:{}", "SgBRefundInTaskService.execute"), inTaskList.size());

        if (CollectionUtils.isEmpty(inTaskList)) {
            v14.setMessage("退换入库确认中间表定时任务（WMS->中台)定时任务成功！无处理数据");
            return v14;
        }

        List<String> warehouseCodeList = new ArrayList<>();
        List<String> noticesBillNoList = new ArrayList<>();
        for (SgBRefundInTaskRequest inTask : inTaskList) {
            warehouseCodeList.add(inTask.getWarehouseCode());
            noticesBillNoList.add(inTask.getNoticesBillNo());
        }

        //2.查入库通知单
        Map<String, SgBStoInNotices> inNoticesMap = WmsTaskUtils.getInNotices(noticesBillNoList);
        //3.查库存调整单
        Map<String, SgBStoAdjust> adjustMap = WmsTaskUtils.getAdjust(noticesBillNoList);
        //4.处理业务逻辑
        SgBRefundInTaskResult sgRefundInTaskResult = businessLogic(inTaskList, inNoticesMap, adjustMap);
        v14.setData(sgRefundInTaskResult);

        log.info(LogUtil.format("退换入库确认中间表定时转化任务参数检查-结束，结果:{}",
                "SgBRefundInTaskService.execute"), JSONObject.toJSONString(v14));
        return v14;
    }

    /**
     * 退换入库确认中间表check
     * ps：oms调用
     *
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     */
    public ValueHolderV14<SgBRefundInTaskResult> executeForFl(List<SgBRefundInTaskRequest> inTaskList) {
        ValueHolderV14<SgBRefundInTaskResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "退换入库确认中间表定时任务（WMS->中台)定时任务成功！");
        log.info(LogUtil.format("退换入库确认中间表定时转化任务参数检查-开始，size:{}", "SgBRefundInTaskService.execute"), inTaskList.size());

        if (CollectionUtils.isEmpty(inTaskList)) {
            v14.setMessage("退换入库确认中间表定时任务（WMS->中台)定时任务成功！无处理数据");
            return v14;
        }

        List<String> warehouseCodeList = new ArrayList<>();
        List<String> noticesBillNoList = new ArrayList<>();
        for (SgBRefundInTaskRequest inTask : inTaskList) {
            warehouseCodeList.add(inTask.getWarehouseCode());
            noticesBillNoList.add(inTask.getNoticesBillNo());
        }

        //1,根据warehouseCode 查实体仓档案
        Map<String, SgCpCPhyWarehouse> warehouseCodeMap = WmsTaskUtils.getSgCpPhyWarehouseByCode(warehouseCodeList);
        //2.查入库通知单
        Map<String, SgBStoInNotices> inNoticesMap = WmsTaskUtils.getInNotices(noticesBillNoList);
        //3.查库存调整单
        Map<String, SgBStoAdjust> adjustMap = WmsTaskUtils.getAdjust(noticesBillNoList);
        //4.处理业务逻辑
        SgBRefundInTaskResult sgRefundInTaskResult = businessLogicForFl(inTaskList, warehouseCodeMap, inNoticesMap, adjustMap);
        v14.setData(sgRefundInTaskResult);

        log.info(LogUtil.format("退换入库确认中间表定时转化任务参数检查-结束，结果:{}",
                "SgBRefundInTaskService.execute"), JSONObject.toJSONString(v14));
        return v14;
    }

    /**
     * 处理业务
     *
     * @param inTaskList   inTaskList
     * @param inNoticesMap inNoticesMap
     * @param adjustMap    adjustMap
     */
    private SgBRefundInTaskResult businessLogic(List<SgBRefundInTaskRequest> inTaskList,
                                                Map<String, SgBStoInNotices> inNoticesMap,
                                                Map<String, SgBStoAdjust> adjustMap) {
        List<SgBRefundInTaskRequest> failList = new ArrayList<>();
        List<SgBRefundInTaskRequest> successList = new ArrayList<>();
        for (SgBRefundInTaskRequest inTask : inTaskList) {
            String warehouseCode = inTask.getWarehouseCode();
            String noticesBillNo = inTask.getNoticesBillNo();
            try {
                String message = inTask.getMessage();
                if (StringUtils.isEmpty(message)) {
                    inTask.setFailedReason("中间表[" + noticesBillNo + "]在报文不存在！");
                    log.error(LogUtil.format("中间表[" + noticesBillNo + "]在报文不存在！", "SgBRefundInTaskService.businessLogic"));
                    failList.add(inTask);
                    continue;
                }

                //b.解析报文，取出信息
                log.info(LogUtil.format("退换入库确认中间表定时转化任务参数检查-解析报文:{}",
                        "SgBRefundInTaskService.businessLogic"), message);
                ReturnorderConfirmRequest returnorderConfirmRequest = WmsTaskUtils.messageParseReturnOrderLines(message);
                if (returnorderConfirmRequest == null) {
                    inTask.setFailedReason("报文解析失败");
                    log.error(LogUtil.format("报文解析失败", "SgBRefundInTaskService.businessLogic"));
                    failList.add(inTask);
                    continue;
                }
                log.info(LogUtil.format("SgBRefundInTaskService.messageParse message:{}",
                        "SgBRefundInTaskService.itemCodeList"), JSONObject.toJSONString(returnorderConfirmRequest));


                //c.取出明细信息拿出条码
                List<ReturnorderConfirmRequest.OrderLine> itemRequestList = returnorderConfirmRequest.getOrderLines();
                if (CollectionUtils.isEmpty(itemRequestList)) {
                    inTask.setFailedReason("报文解析失败！无明细信息！");
                    log.error(LogUtil.format("报文解析失败！无明细信息！"));
                    failList.add(inTask);
                    continue;
                }

                //d,核对条码
                List<String> itemCodeList = itemRequestList.stream().map(ReturnorderConfirmRequest.OrderLine::getItemCode).collect(Collectors.toList());
                JSONArray array = WmsTaskUtils.compareToSku(itemCodeList);
                if (array.size() > 0) {
                    inTask.setFailedReason("商品编码" + JSONArray.toJSONString(array) + "在【商品SKU】中不存在！");
                    log.error(LogUtil.format("商品编码" + JSONArray.toJSONString(array) + "在【商品SKU】中不存在！", "SgBRefundInTaskService.businessLogic"));
                    failList.add(inTask);
                    continue;
                }

                //e.单据类型
                String orderType = returnorderConfirmRequest.getReturnOrder().getOrderType();
                if (SgInfConstants.OREDER_TYPE_B2BRK.equals(orderType) || SgInfConstants.OREDER_TYPE_THRK.equals(orderType)) {
                    if (MapUtils.isEmpty(inNoticesMap) || !inNoticesMap.containsKey(noticesBillNo)) {
                        inTask.setFailedReason(noticesBillNo + "入库通知单不存在！");
                        log.error(LogUtil.format(noticesBillNo + "入库通知单不存在！", "SgBRefundInTaskService.businessLogic"));
                        failList.add(inTask);
                        continue;
                    }
                } else if (SgInfConstants.OREDER_TYPE_GCXT.equals(orderType) || SgInfConstants.OREDER_TYPE_WTJRK.equals(orderType)) {
                    if (MapUtils.isEmpty(adjustMap) || !adjustMap.containsKey(noticesBillNo)) {
                        inTask.setFailedReason(noticesBillNo + "库存调整单不存在或状态不合法（非未审核状态）！");
                        log.error(LogUtil.format(noticesBillNo + "库存调整单不存在或状态不合法（非未审核状态）！", "SgBRefundInTaskService.businessLogic"));
                        failList.add(inTask);
                        continue;
                    }
                } else {
                    inTask.setFailedReason("单据类型不存在！" + orderType);
                    log.error(LogUtil.format("单据类型不存在！" + orderType, "SgBRefundInTaskService.businessLogic"));
                    failList.add(inTask);
                    continue;
                }

                //更新成功信息
                successList.add(inTask);
            } catch (Exception e) {
                log.error(LogUtil.format("退换入库确认中间表定时转化任务参数检查-参数校验异常:{}",
                        "SgBRefundInTaskService.businessLogic"), Throwables.getStackTraceAsString(e));
                inTask.setFailedReason("校验异常：" + e.getMessage());
                failList.add(inTask);
            }
        }

        SgBRefundInTaskResult sgRefundInTaskResult = new SgBRefundInTaskResult();
        sgRefundInTaskResult.setFailList(failList);
        sgRefundInTaskResult.setSuccessList(successList);
        return sgRefundInTaskResult;
    }

    /**
     * 处理业务(富勒)
     *
     * @param inTaskList       inTaskList
     * @param warehouseCodeMap warehouseCodeMap
     * @param inNoticesMap     inNoticesMap
     * @param adjustMap        adjustMap
     */
    private SgBRefundInTaskResult businessLogicForFl(List<SgBRefundInTaskRequest> inTaskList, Map<String, SgCpCPhyWarehouse> warehouseCodeMap,
                                                     Map<String, SgBStoInNotices> inNoticesMap, Map<String, SgBStoAdjust> adjustMap) {
        List<SgBRefundInTaskRequest> failList = new ArrayList<>();
        List<SgBRefundInTaskRequest> successList = new ArrayList<>();
        for (SgBRefundInTaskRequest inTask : inTaskList) {
            String warehouseCode = inTask.getWarehouseCode();
            String noticesBillNo = inTask.getNoticesBillNo();
            try {
                String message = inTask.getMessage();
                if (StringUtils.isEmpty(message)) {
                    inTask.setFailedReason("中间表[" + noticesBillNo + "]在报文不存在！");
                    log.error(LogUtil.format("中间表[" + noticesBillNo + "]在报文不存在！", "SgBRefundInTaskService.businessLogic"));
                    failList.add(inTask);
                    continue;
                }
                //获取报文库存地点
                String storageLocation = null;
                JSONObject request = JSONObject.parseObject(message);
                if (request != null && request.getJSONObject("extendProps") != null) {
                    storageLocation = request.getJSONObject("extendProps").getString("storageLocation");
                }
                if (StringUtils.isEmpty(storageLocation)) {
                    inTask.setFailedReason("报文中库存地点不存在！");
                    failList.add(inTask);
                    continue;
                }
                //a.参数中“仓库编码 warehouseCode”，在【实体仓档案】的“WMS仓库编码”中是否存在
                SgCpCPhyWarehouse warehouse = warehouseCodeMap.get(storageLocation);
                if (Objects.isNull(warehouse)) {
                    inTask.setFailedReason("仓库编码[" + storageLocation + "]在【实体仓档案】中不存在");
                    log.error(LogUtil.format("仓库编码[" + storageLocation + "]在【实体仓档案】中不存在", "SgBRefundInTaskService.businessLogic"));
                    failList.add(inTask);
                    continue;
                }
                //b.解析报文，取出信息
                log.info(LogUtil.format("退换入库确认中间表定时转化任务参数检查-解析报文:{}",
                        "SgBRefundInTaskService.businessLogic"), message);
                ReturnorderConfirmRequest returnorderConfirmRequest = WmsTaskUtils.messageParseReturnOrderLines(message);
                if (returnorderConfirmRequest == null) {
                    inTask.setFailedReason("报文解析失败");
                    log.error(LogUtil.format("报文解析失败", "SgBRefundInTaskService.businessLogic"));
                    failList.add(inTask);
                    continue;
                }
                log.info(LogUtil.format("SgBRefundInTaskService.messageParse message:{}",
                        "SgBRefundInTaskService.itemCodeList"), JSONObject.toJSONString(returnorderConfirmRequest));


                //c.取出明细信息拿出条码
                List<ReturnorderConfirmRequest.OrderLine> itemRequestList = returnorderConfirmRequest.getOrderLines();
                if (CollectionUtils.isEmpty(itemRequestList)) {
                    inTask.setFailedReason("报文解析失败！无明细信息！");
                    log.error(LogUtil.format("报文解析失败！无明细信息！"));
                    failList.add(inTask);
                    continue;
                }

                //d,核对条码
                List<String> itemCodeList = itemRequestList.stream().map(ReturnorderConfirmRequest.OrderLine::getItemCode).collect(Collectors.toList());
                JSONArray array = WmsTaskUtils.compareToSku(itemCodeList);
                if (array.size() > 0) {
                    inTask.setFailedReason("商品编码" + JSONArray.toJSONString(array) + "在【商品SKU】中不存在！");
                    log.error(LogUtil.format("商品编码" + JSONArray.toJSONString(array) + "在【商品SKU】中不存在！", "SgBRefundInTaskService.businessLogic"));
                    failList.add(inTask);
                    continue;
                }

                //e.单据类型
                String orderType = returnorderConfirmRequest.getReturnOrder().getOrderType();
                if (SgInfConstants.OREDER_TYPE_B2BRK.equals(orderType) || SgInfConstants.OREDER_TYPE_THRK.equals(orderType)) {
                    if (MapUtils.isEmpty(inNoticesMap) || !inNoticesMap.containsKey(noticesBillNo)) {
                        inTask.setFailedReason(noticesBillNo + "入库通知单不存在！");
                        log.error(LogUtil.format(noticesBillNo + "入库通知单不存在！", "SgBRefundInTaskService.businessLogic"));
                        failList.add(inTask);
                        continue;
                    }
                } else if (SgInfConstants.OREDER_TYPE_GCXT.equals(orderType) || SgInfConstants.OREDER_TYPE_WTJRK.equals(orderType)) {
                    if (MapUtils.isEmpty(adjustMap) || !adjustMap.containsKey(noticesBillNo)) {
                        inTask.setFailedReason(noticesBillNo + "库存调整单不存在或状态不合法（非未审核状态）！");
                        log.error(LogUtil.format(noticesBillNo + "库存调整单不存在或状态不合法（非未审核状态）！", "SgBRefundInTaskService.businessLogic"));
                        failList.add(inTask);
                        continue;
                    }
                } else {
                    inTask.setFailedReason("单据类型不存在！" + orderType);
                    log.error(LogUtil.format("单据类型不存在！" + orderType, "SgBRefundInTaskService.businessLogic"));
                    failList.add(inTask);
                    continue;
                }

                //更新成功信息
                successList.add(inTask);
            } catch (Exception e) {
                log.error(LogUtil.format("退换入库确认中间表定时转化任务参数检查-参数校验异常:{}",
                        "SgBRefundInTaskService.businessLogic"), Throwables.getStackTraceAsString(e));
                inTask.setFailedReason("校验异常：" + e.getMessage());
                failList.add(inTask);
            }
        }

        SgBRefundInTaskResult sgRefundInTaskResult = new SgBRefundInTaskResult();
        sgRefundInTaskResult.setFailList(failList);
        sgRefundInTaskResult.setSuccessList(successList);
        return sgRefundInTaskResult;
    }


    /**
     * oms ->sg
     * 库存调整单
     *
     * @param inTask B2C退库中间表
     * @return ValueHolderV14
     */
    public ValueHolderV14<SgR3BaseResult> adjustSaveAndSubmit(SgBRefundInTaskRequest inTask) {
        log.info(LogUtil.format("OMS驱动SG保存并审核库存调整单-入参:{}",
                "SgBRefundInTaskService.adjustSaveAndSubmit"), inTask != null ? JSONObject.toJSONString(inTask) : null);

        try {
            if (inTask == null) {
                return new ValueHolderV14<>(ResultCode.FAIL, "入参为空！");
            }

            SgBStoAdjust bStoAdjust = adjustMapper.selectOne(new LambdaQueryWrapper<SgBStoAdjust>()
                    .eq(SgBStoAdjust::getBillNo, inTask.getNoticesBillNo())
                    .eq(SgBStoAdjust::getIsactive, SgConstants.IS_ACTIVE_Y)
                    .eq(SgBStoAdjust::getStatus, SgConstants.CON_BILL_STATUS_01));

            if (bStoAdjust == null) {
                return new ValueHolderV14<>(ResultCode.FAIL, "库存调整单为空！！");
            }
            String msg = inTask.getMessage();
            ReturnorderConfirmRequest returnorderConfirmRequest = WmsTaskUtils.messageParseReturnOrderLines(msg);
            SgBRefundInTaskService bean = ApplicationContextHandle.getBean(SgBRefundInTaskService.class);
            ValueHolderV14<SgR3BaseResult> v14 = bean.adjustSaveAndSubmit(bStoAdjust, returnorderConfirmRequest);

            SgR3BaseResult data = new SgR3BaseResult();
            data.setBillId(bStoAdjust.getSourceBillId());
            data.setBillNo(bStoAdjust.getSourceBillNo());
            v14.setData(data);
            return v14;
        } catch (Exception e) {
            log.info(LogUtil.format("OMS驱动SG保存并审核库存调整单-异常:{}",
                    "SgBRefundInTaskService.adjustSaveAndSubmit"), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 更新对应库存调整单的明细并审核库存调整单
     *
     * @param adjust                    库存调整单
     * @param returnorderConfirmRequest 报问解析后数据
     * @return return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> adjustSaveAndSubmit(SgBStoAdjust adjust, ReturnorderConfirmRequest returnorderConfirmRequest) throws ParseException {
        List<ReturnorderConfirmRequest.OrderLine> itemRequestList = returnorderConfirmRequest.getOrderLines();
        ReturnorderConfirmRequest.ReturnOrder returnOrder = returnorderConfirmRequest.getReturnOrder();

        log.info(LogUtil.format("更新并审核库存调整单：adjust:{},itemRequestList:{},returnOrder:{}", "SgBRefundInTaskService.adjustSaveAndSubmit"),
                adjust != null ? JSONObject.toJSONString(adjust) : null,
                JSONObject.toJSONString(itemRequestList),
                returnOrder != null ? JSONObject.toJSONString(returnOrder) : null);

        List<SgBStoAdjustItem> sgStoAdjustItems = adjustItemMapper.selectList(new LambdaQueryWrapper<SgBStoAdjustItem>()
                .eq(SgBStoAdjustItem::getSgBStoAdjustId, adjust.getId()));

        Map<String, SgBStoAdjustItem> skuItemIdMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(sgStoAdjustItems)) {
            skuItemIdMap = sgStoAdjustItems.stream().collect(Collectors.toMap(SgBStoAdjustItem::getPsCSkuEcode,
                    item -> item, (v1, v2) -> v1));
        }
        if (sgStoAdjustItems.size() > 0) {
            adjustItemMapper.delete(new LambdaQueryWrapper<SgBStoAdjustItem>()
                    .eq(SgBStoAdjustItem::getSgBStoAdjustId, adjust.getId()));
        }

        SgBStoAdjustSaveRequest adjustSaveRequest = new SgBStoAdjustSaveRequest();
        SgBStoAdjustMainSaveRequest adjustMainSaveRequest = new SgBStoAdjustMainSaveRequest();
        List<SgBStoAdjustItemSaveRequest> itemSaveRequestList = new ArrayList<>();

        BeanUtils.copyProperties(adjust, adjustMainSaveRequest);
        adjustMainSaveRequest.setBillNo(null);
        adjustMainSaveRequest.setCpCLogisticsEcode(adjust.getCpCLogisticsEcode());
        adjustMainSaveRequest.setLogisticNumber(adjust.getLogisticNumber());

        if (returnOrder == null || StringUtils.isEmpty(returnOrder.getOrderConfirmTime())) {
            adjustMainSaveRequest.setBillDate(new Date());
        } else {
            SimpleDateFormat format = new SimpleDateFormat(DateUtils.PATTERN_DATETIME);
            adjustMainSaveRequest.setBillDate(format.parse(returnOrder.getOrderConfirmTime()));
        }

        for (ReturnorderConfirmRequest.OrderLine orderLine : itemRequestList) {
            SgBStoAdjustItemSaveRequest itemSaveRequest = new SgBStoAdjustItemSaveRequest();
            //获取之前的来源单明细id，可重复
            if (skuItemIdMap.containsKey(orderLine.getItemCode())) {
                SgBStoAdjustItem itemCode = skuItemIdMap.get(orderLine.getItemCode());
                itemSaveRequest.setSourceBillItemId(itemCode.getSourceBillItemId());
                itemSaveRequest.setSourceBillNo(itemCode.getSourceBillNo());
            }
            itemSaveRequest.setQty(new BigDecimal(orderLine.getActualQty()));
            String batchCode = orderLine.getBatchCode();
            itemSaveRequest.setProduceDate(StringUtils.isEmpty(batchCode) || SgConstantsIF.DEFAULT_PRODUCE_DATE.equals(batchCode) ?
                    SgConstantsIF.DEFAULT_PRODUCE_DATE :
                    DateUtils.formatSync8(DateUtils.parseSync8(orderLine.getBatchCode(), DateUtils.PATTERN_DATE), DateUtils.DATE_PATTERN));
            itemSaveRequest.setStorageType(orderLine.getInventoryType());
            itemSaveRequest.setPsCSkuEcode(orderLine.getItemCode());
            itemSaveRequestList.add(itemSaveRequest);
        }
        adjustSaveRequest.setItems(itemSaveRequestList);
        adjustSaveRequest.setMainRequest(adjustMainSaveRequest);
        adjustSaveRequest.setObjId(adjust.getId());
        adjustSaveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());

        log.info(LogUtil.format("更新并审核库存调整单,入参:{}",
                "SgBRefundInTaskService.adjustSaveAndSubmit"), JSONObject.toJSONString(adjustSaveRequest));
        ValueHolderV14<SgR3BaseResult> v14 = adjustSaveService.saveAndSubmit(adjustSaveRequest);
        log.info(LogUtil.format("更新并审核库存调整单，结果:{}",
                "SgBRefundInTaskService.adjustSaveAndSubmit"), JSONObject.toJSONString(v14));

        if (!v14.isOK()) {
            AssertUtils.logAndThrow(v14.getMessage());
        }
        return v14;
    }

    /**
     * 退货包裹状态通知接口check(巨沃)
     *
     * @param inTaskList 入参
     * @return ValueHolderV14
     */
    public ValueHolderV14<SgBRefundInTaskResult> refundPackageCheck(List<SgBRefundInTaskRequest> inTaskList) {
        ValueHolderV14<SgBRefundInTaskResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "退货包裹状态通知接口（WMS->中台)check成功！");
        log.info(LogUtil.format("SgBRefundInTaskService.refundPackageCheck start", "SgBRefundInTaskService.refundPackageCheck"));
        if (CollectionUtils.isEmpty(inTaskList)) {
            v14.setMessage("退货包裹状态通知接口（WMS->中台)check成功！无处理数据");
            return v14;
        }
        //1,根据warehouseCode 查实体仓档案
        List<String> warehouseCodeList = inTaskList.stream().map(SgBRefundInTaskRequest::getWarehouseCode).collect(Collectors.toList());
        Map<String, SgCpCPhyWarehouse> warehouseCodeMap = WmsTaskUtils.getSgCpPhyWarehouseByWarehouseCode(warehouseCodeList);

        //4.处理业务逻辑
        SgBRefundInTaskResult sgRefundInTaskResult = refundPackageBusinessLogic(inTaskList, warehouseCodeMap);
        v14.setData(sgRefundInTaskResult);

        log.info(LogUtil.format("SgBRefundInTaskService.refundPackageCheck ValueHolderV14:{}",
                "SgBRefundInTaskService.refundPackageCheck.ValueHolderV14"), JSONObject.toJSONString(v14));
        return v14;
    }

    /**
     * 退货包裹状态通知接口check(富勒)
     *
     * @param inTaskList 入参
     * @return ValueHolderV14
     */
    public ValueHolderV14<SgBRefundInTaskResult> refundPackageCheckFl(List<SgBRefundInTaskRequest> inTaskList) {
        ValueHolderV14<SgBRefundInTaskResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "退货包裹状态通知接口（WMS->中台)check成功！");
        log.info(LogUtil.format("SgBRefundInTaskService.refundPackageCheckFl start",
                "SgBRefundInTaskService.refundPackageCheckFl"));
        if (CollectionUtils.isEmpty(inTaskList)) {
            v14.setMessage("退货包裹状态通知接口（WMS->中台)check成功！无处理数据");
            return v14;
        }
        //1,根据warehouseCode 查实体仓档案
        List<String> warehouseCodeList = inTaskList.stream().map(SgBRefundInTaskRequest::getWarehouseCode).collect(Collectors.toList());
        Map<String, SgCpCPhyWarehouse> warehouseCodeMap = WmsTaskUtils.getSgCpPhyWarehouseByCode(warehouseCodeList);

        //4.处理业务逻辑
        SgBRefundInTaskResult sgRefundInTaskResult = refundPackageBusinessLogicForFl(inTaskList, warehouseCodeMap);
        v14.setData(sgRefundInTaskResult);

        log.info(LogUtil.format("SgBRefundInTaskService.refundPackageCheck ValueHolderV14:{}",
                "SgBRefundInTaskService.refundPackageCheck.ValueHolderV14 "), JSONObject.toJSONString(v14));
        return v14;
    }

    /**
     * 处理业务（巨沃）
     *
     * @param inTaskList       inTaskList
     * @param warehouseCodeMap warehouseCodeMap
     */
    private SgBRefundInTaskResult refundPackageBusinessLogic(List<SgBRefundInTaskRequest> inTaskList, Map<String, SgCpCPhyWarehouse> warehouseCodeMap) {

        List<SgBRefundInTaskRequest> failList = new ArrayList<>();
        List<SgBRefundInTaskRequest> successList = new ArrayList<>();
        for (SgBRefundInTaskRequest inTask : inTaskList) {
            String warehouseCode = inTask.getWarehouseCode();
            String noticesBillNo = inTask.getNoticesBillNo();
            try {
                //a.参数中“仓库编码 warehouseCode”，在【实体仓档案】的“WMS仓库编码”中是否存在
                if (!warehouseCodeMap.containsKey(warehouseCode)) {
                    inTask.setFailedReason("仓库编码[" + warehouseCode + "]在【实体仓档案】中不存在");
                    failList.add(inTask);
                    continue;
                }

                String message = inTask.getMessage();
                if (StringUtils.isEmpty(message)) {
                    inTask.setFailedReason("中间表[" + noticesBillNo + "]在报文不存在！");
                    failList.add(inTask);
                    continue;
                }

                //b.解析报文，取出信息
                log.info(LogUtil.format("SgBRefundInTaskService.refundPackageBusinessLogic message:{}",
                        "SgBRefundInTaskService.refundPackageBusinessLogic.message"), message);
                List<String> itemCodeList = WmsTaskUtils.messageParseByPackagesReturnItemCode(message);
                if (CollectionUtils.isEmpty(itemCodeList)) {
                    inTask.setFailedReason("报文解析失败");
                    failList.add(inTask);
                    continue;
                }
                log.info(LogUtil.format("SgBRefundInTaskService.messageParse message:{}",
                        "SgBRefundInTaskService.refundPackageBusinessLogic.itemCodeList"), JSONObject.toJSONString(itemCodeList));

                //c,核对条码
                JSONArray array = WmsTaskUtils.compareToSku(itemCodeList);
                if (array.size() > 0) {
                    inTask.setFailedReason("商品编码" + JSONArray.toJSONString(array) + "在【商品SKU】中不存在！");
                    failList.add(inTask);
                    continue;
                }
                //更新成功信息
                successList.add(inTask);
            } catch (Exception e) {
                log.error("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
                inTask.setFailedReason(e.getMessage());
                failList.add(inTask);
            }

        }

        SgBRefundInTaskResult sgRefundInTaskResult = new SgBRefundInTaskResult();
        sgRefundInTaskResult.setFailList(failList);
        sgRefundInTaskResult.setSuccessList(successList);
        return sgRefundInTaskResult;
    }

    /**
     * 处理业务（富勒）
     *
     * @param inTaskList       inTaskList
     * @param warehouseCodeMap warehouseCodeMap
     */
    private SgBRefundInTaskResult refundPackageBusinessLogicForFl(List<SgBRefundInTaskRequest> inTaskList, Map<String, SgCpCPhyWarehouse> warehouseCodeMap) {
        List<SgBRefundInTaskRequest> failList = new ArrayList<>();
        List<SgBRefundInTaskRequest> successList = new ArrayList<>();
        for (SgBRefundInTaskRequest inTask : inTaskList) {
            String noticesBillNo = inTask.getNoticesBillNo();
            try {
                //校验报文
                String message = inTask.getMessage();
                if (StringUtils.isEmpty(message)) {
                    inTask.setFailedReason("中间表[" + noticesBillNo + "]在报文不存在！");
                    failList.add(inTask);
                    continue;
                }
                //获取报文库存地点
                String storageLocation = null;
                JSONObject request = JSONObject.parseObject(message);
                if (request != null && request.getJSONArray("packages") != null
                        && request.getJSONArray("packages").size() > 0) {
                    storageLocation = request.getJSONArray("packages").getJSONObject(0).getString("remarks");
                }
                if (StringUtils.isEmpty(storageLocation)) {
                    inTask.setFailedReason("报文中库存地点不存在！");
                    failList.add(inTask);
                    continue;
                }
                //校验库存地点在【实体仓档案】的“WMS仓库编码”中是否存在
                if (!warehouseCodeMap.containsKey(storageLocation)) {
                    inTask.setFailedReason("仓库编码[" + storageLocation + "]在【实体仓档案】中不存在");
                    failList.add(inTask);
                    continue;
                }
                //b.解析报文，取出信息
                log.info(LogUtil.format("SgBRefundInTaskService.refundPackageBusinessLogic message:{}",
                        "SgBRefundInTaskService.refundPackageBusinessLogic.message"), message);
                List<String> itemCodeList = WmsTaskUtils.messageParseByPackagesReturnItemCode(message);
                if (CollectionUtils.isEmpty(itemCodeList)) {
                    inTask.setFailedReason("报文解析失败");
                    failList.add(inTask);
                    continue;
                }
                log.info(LogUtil.format("SgBRefundInTaskService.messageParse message:{}",
                        "SgBRefundInTaskService.refundPackageBusinessLogic.itemCodeList"), JSONObject.toJSONString(itemCodeList));
                //c,核对条码
                JSONArray array = WmsTaskUtils.compareToSku(itemCodeList);
                if (array.size() > 0) {
                    inTask.setFailedReason("商品编码" + JSONArray.toJSONString(array) + "在【商品SKU】中不存在！");
                    failList.add(inTask);
                    continue;
                }
                //更新成功信息
                successList.add(inTask);
            } catch (Exception e) {
                log.error("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
                inTask.setFailedReason(e.getMessage());
                failList.add(inTask);
            }

        }

        SgBRefundInTaskResult sgRefundInTaskResult = new SgBRefundInTaskResult();
        sgRefundInTaskResult.setFailList(failList);
        sgRefundInTaskResult.setSuccessList(successList);
        return sgRefundInTaskResult;
    }

}

