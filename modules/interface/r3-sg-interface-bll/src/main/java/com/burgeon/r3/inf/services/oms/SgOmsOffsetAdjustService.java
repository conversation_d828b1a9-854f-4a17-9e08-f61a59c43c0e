package com.burgeon.r3.inf.services.oms;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.store.adjust.SgBStoAdjust;
import com.burgeon.r3.sg.core.model.table.store.adjust.SgBStoAdjustItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.adjust.SgBStoAdjustItemMapper;
import com.burgeon.r3.sg.store.mapper.adjust.SgBStoAdjustMapper;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustMainSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustSaveRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeSaveItemRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInNoticesBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultSubmitRequest;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInNoticesBillSaveResult;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInResultBillSubmitResult;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSaveService;
import com.burgeon.r3.sg.store.services.freeze.SgBStoFreezeSaveAndSubmitService;
import com.burgeon.r3.sg.store.services.in.SgBStoInNoticesSaveService;
import com.burgeon.r3.sg.store.services.in.SgBStoInResultSaveR3Service;
import com.burgeon.r3.sg.store.services.in.SgBStoInResultSubmitService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BeanCopierUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-08-23 16:10
 * @Description: 无头件冲单
 */
@Slf4j
@Component
public class SgOmsOffsetAdjustService {

    @Autowired
    private SgBStoAdjustMapper adjustMapper;
    @Autowired
    private SgBStoAdjustItemMapper adjustItemMapper;
    @Autowired
    private SgBStoAdjustSaveService sgBStoAdjustSaveService;
    @Autowired
    private SgBStoInResultSaveR3Service stoInResultSaveR3Service;
    @Autowired
    private SgBStoInResultSubmitService submitResultService;
    @Autowired
    private SgBStoFreezeSaveAndSubmitService freezeService;

    public ValueHolderV14 offsetAdjust(JSONObject jsonObject, User user) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "冲单成功！");
        try {
            SgOmsOffsetAdjustService bean = ApplicationContextHandle.getBean(SgOmsOffsetAdjustService.class);
            v14 = bean.offsetAdjustByTransactional(jsonObject, user);
        } catch (Exception e) {
            log.error("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }

    public ValueHolderV14 offsetAdjustForFl(JSONObject jsonObject, User user) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "冲单成功！");
        try {
            SgOmsOffsetAdjustService bean = ApplicationContextHandle.getBean(SgOmsOffsetAdjustService.class);
            v14 = bean.offsetAdjustByTransactionalForFl(jsonObject, user);
        } catch (Exception e) {
            log.error("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }

    /**
     * 无头件冲单
     *
     * @param jsonObject jsonObject
     * @param user       user
     * @return ValueHolderV14
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 offsetAdjustByTransactional(JSONObject jsonObject, User user) {

        log.info(LogUtil.format("退货入库冲无头件生成库存调整单入参:{}",
                "SgOmsOffsetAdjust.offsetAdjust"), Objects.isNull(jsonObject) ? null : JSONObject.toJSONString(jsonObject));
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "冲单成功！");
        List<String> redisFtpKey = new ArrayList<>();
        try {
            if (user == null) {
                user = R3SystemUserResource.getSystemRootUser();
            }
            //退货入库id
            Long id = jsonObject.getLong("ID");
            if (Objects.isNull(id)) {
                return new ValueHolderV14(ResultCode.FAIL, "入参为空～");
            }

            // 是否冲单过
            Integer selectCount = adjustMapper.selectCount(new LambdaQueryWrapper<SgBStoAdjust>()
                    .eq(SgBStoAdjust::getSgBAdjustPropId, (int) SgConstantsIF.SERVICE_NODE_ADJUST_PROP_FLUSH_NO_SOURCE)
                    .eq(SgBStoAdjust::getSourceBillId, id)
                    .eq(SgBStoAdjust::getSourceBillType, SgConstantsIF.BILL_TYPE_PROP_NO_SOURCE_IN));
            if (selectCount > 0) {
                return new ValueHolderV14(ResultCode.SUCCESS, "已冲单！");
            }

            //查询“来源单ID”=对应入参数字段、“来源单类型”=无头件登记单，“单据状态”=已审核的【库存调整单】
            List<SgBStoAdjust> sgStoAdjusts = adjustMapper.selectList(new LambdaQueryWrapper<SgBStoAdjust>()
                    .eq(SgBStoAdjust::getSourceBillId, id)
                    .eq(SgBStoAdjust::getSourceBillType, SgConstantsIF.BILL_TYPE_PROP_NO_SOURCE_IN)
                    .eq(SgBStoAdjust::getStatus, SgConstants.CON_BILL_STATUS_02));

            if (CollectionUtils.isEmpty(sgStoAdjusts) || sgStoAdjusts.size() > 1) {
                return new ValueHolderV14(ResultCode.FAIL, "未找到满足条件的库存调整单或找到多个！～");
            }


            Boolean isSaveInNotice = Optional.ofNullable(jsonObject.getBoolean("IS_SAVE_IN_NOTICE")).orElse(Boolean.TRUE);
            if (isSaveInNotice) {
                //22-08-29 需要生成入库通知单，入库结果单，冻结单
                ValueHolderV14 resultV14 = offsetInNoticeAndResult(jsonObject, sgStoAdjusts.get(0), redisFtpKey, user);
                if (!resultV14.isOK()) {
                    AssertUtils.logAndThrow(resultV14.getMessage());
                }
                offsetFreeze(jsonObject, sgStoAdjusts.get(0), redisFtpKey, user);
            }


            //由退货入库单传入库时间
            Date inTime = jsonObject.getDate("IN_TIME");
            Date date = Objects.isNull(inTime) ? new Date() : inTime;

            ValueHolderV14<SgR3BaseResult> v141 = offsetAdjust(sgStoAdjusts.get(0), date, user);
            if (!v141.isOK()) {
//                return new ValueHolderV14(ResultCode.FAIL, v141.getMessage());
                AssertUtils.logAndThrow(v141.getMessage());
            }
            JSONArray redisFtpKeys = v141.getData().getDataJo().getJSONArray("redisFtpKeys");
            if (redisFtpKeys != null && redisFtpKeys.size() > 0) {
                redisFtpKey.addAll(redisFtpKeys.toJavaList(String.class));
            }


        } catch (Exception e) {
            // 回滚redis 事务
            StorageBasicUtils.rollbackStorage(redisFtpKey, user);
            log.error(LogUtil.format("退货入库冲无头件生成库存调整单异常:exception_has_occured:{} ",
                    "退货入库冲无头件生成库存调整单异常"), Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrow("冲单异常：" + e.getMessage());
        }

        return v14;
    }

    /**
     * 富勒逻辑的特殊逻辑，因为没有生成过正向库存调整单，这里只生成入库通知单和逻辑入库单
     *
     * @param jsonObject jsonObject
     * @param user       user
     * @return ValueHolderV14
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 offsetAdjustByTransactionalForFl(JSONObject jsonObject, User user) {
        log.info(LogUtil.format("富勒退货入库生成入库通知单和逻辑入库单入参:{}",
                "SgOmsOffsetAdjust.ForFl"), Objects.isNull(jsonObject) ? null : JSONObject.toJSONString(jsonObject));
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "生成成功！");
        List<String> redisFtpKey = new ArrayList<>();
        try {
            if (user == null) {
                user = R3SystemUserResource.getSystemRootUser();
            }
            //退货入库id
            Long id = jsonObject.getLong("ID");
            if (Objects.isNull(id)) {
                return new ValueHolderV14(ResultCode.FAIL, "入参为空～");
            }
            // 获取明细
            JSONArray items = jsonObject.getJSONArray("ITEM");
            if (items == null && items.size() == 0) {
                throw new NDSException("明细为空！");
            }
            List<SgBStoAdjustItemSaveRequest> itemSaveRequestList = JSONArray.parseArray(items.toJSONString(), SgBStoAdjustItemSaveRequest.class);
            Boolean isSaveInNotice = Optional.ofNullable(jsonObject.getBoolean("IS_SAVE_IN_NOTICE")).orElse(Boolean.TRUE);
            if (isSaveInNotice) {
                //22-08-29 需要生成入库通知单，入库结果单，冻结单
                ValueHolderV14 resultV14 = offsetInNoticeAndResultForFl(jsonObject, itemSaveRequestList, redisFtpKey, user);
                if (!resultV14.isOK()) {
                    AssertUtils.logAndThrow(resultV14.getMessage());
                }
                offsetFreezeForFl(jsonObject, itemSaveRequestList, redisFtpKey, user);
            }
        } catch (Exception e) {
            // 回滚redis 事务
            StorageBasicUtils.rollbackStorage(redisFtpKey, user);
            log.error(LogUtil.format("富勒退货入库生成入库通知单和逻辑入库单异常:exception_has_occured:{} ",
                    "富勒退货入库生成入库通知单和逻辑入库单异常"), Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrow("冲单异常：" + e.getMessage());
        }

        return v14;
    }

    /**
     * 冲单
     *
     * @param adjust 库存调整dan
     * @param user   用户
     * @return ValueHolderV14
     */
    private ValueHolderV14<SgR3BaseResult> offsetAdjust(SgBStoAdjust adjust, Date date, User user) {
        SgBStoAdjustSaveRequest adjustSaveRequest = new SgBStoAdjustSaveRequest();

        SgBStoAdjustMainSaveRequest mainRequest = new SgBStoAdjustMainSaveRequest();
        BeanUtils.copyProperties(adjust, mainRequest);
        mainRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_ADJUST_PROP_FLUSH_NO_SOURCE);
        mainRequest.setIsPassWms(NumberUtils.INTEGER_ZERO);
        mainRequest.setIsPassSap(NumberUtils.INTEGER_ZERO);
        mainRequest.setWmsStatus((int) SgStoreConstants.WMS_UPLOAD_STATUTS_NO);
        mainRequest.setRemark("无头件冲单！");
        mainRequest.setBillNo(null);
        mainRequest.setBillDate(date);

        List<SgBStoAdjustItemSaveRequest> itemRequests = new ArrayList<>();
        List<SgBStoAdjustItem> sgStoAdjustItems = adjustItemMapper.selectList(new LambdaQueryWrapper<SgBStoAdjustItem>()
                .eq(SgBStoAdjustItem::getSgBStoAdjustId, adjust.getId()));

        if (CollectionUtils.isNotEmpty(sgStoAdjustItems)) {
            sgStoAdjustItems.forEach(item -> {
                SgBStoAdjustItemSaveRequest itemSaveRequest = new SgBStoAdjustItemSaveRequest();
                BeanUtils.copyProperties(item, itemSaveRequest);
                //数量取反
                itemSaveRequest.setQty(item.getQty().negate());
                itemSaveRequest.setId(-1L);
                itemRequests.add(itemSaveRequest);
            });
        }

        adjustSaveRequest.setObjId(-1L);
        adjustSaveRequest.setMainRequest(mainRequest);
        adjustSaveRequest.setItems(itemRequests);
        adjustSaveRequest.setR3(Boolean.FALSE);
        adjustSaveRequest.setLoginUser(user);

        log.info(LogUtil.format("SgOmsOffsetAdjust.saveAndSubmit:{}",
                "SgOmsOffsetAdjust.saveAndSubmitAdjust.saveAndSubmit"), JSONObject.toJSONString(adjustSaveRequest));
        ValueHolderV14<SgR3BaseResult> v14 = sgBStoAdjustSaveService.saveAndSubmit(adjustSaveRequest);
        log.info(LogUtil.format("SgOmsOffsetAdjust.valueHolderV14:{}",
                "SgOmsOffsetAdjust.valueHolderV14"), JSONObject.toJSONString(v14));

        return v14;
    }

    private ValueHolderV14 offsetFreeze(JSONObject jsonObject, SgBStoAdjust adjust, List<String> redisFtpKey, User user) {
        List<SgBStoAdjustItem> sgStoAdjustItems = adjustItemMapper.selectList(new LambdaQueryWrapper<SgBStoAdjustItem>()
                .eq(SgBStoAdjustItem::getSgBStoAdjustId, adjust.getId())
                .ne(SgBStoAdjustItem::getStorageType, SgConstantsIF.STOCK_TYPE_GOODS));
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "生成冻结单成功！");
        if (CollectionUtils.isNotEmpty(sgStoAdjustItems)) {
            Map<String, List<SgBStoAdjustItem>> freezeStorageMap = sgStoAdjustItems.stream().collect(Collectors.groupingBy(SgBStoAdjustItem::getStorageType));
            createAndSubmitStoFreeze(jsonObject, adjust, freezeStorageMap, user, redisFtpKey);
        }

        return v14;
    }

    private ValueHolderV14 offsetFreezeForFl(JSONObject jsonObject, List<SgBStoAdjustItemSaveRequest> itemSaveRequests, List<String> redisFtpKey, User user) {
        List<SgBStoAdjustItemSaveRequest> items =
                itemSaveRequests.stream().filter(s -> !(SgConstantsIF.STOCK_TYPE_GOODS.equals(s.getStorageType()))).collect(Collectors.toList());
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "生成冻结单成功！");
        if (CollectionUtils.isNotEmpty(items)) {
            Map<String, List<SgBStoAdjustItemSaveRequest>> itemMap = items.stream().collect(Collectors.groupingBy(SgBStoAdjustItemSaveRequest::getStorageType));
            createAndSubmitStoFreezeForFl(jsonObject, itemMap, user, redisFtpKey);
        }

        return v14;
    }

    /**
     * 生成逻辑冻结单
     *
     * @param adjust           库存调整单
     * @param freezeStorageMap 明细
     * @param loginUser        用户
     * @param redisFtpKeys     流水key
     */
    private void createAndSubmitStoFreeze(JSONObject jsonObject, SgBStoAdjust adjust, Map<String, List<SgBStoAdjustItem>> freezeStorageMap, User loginUser, List<String> redisFtpKeys) {
        freezeStorageMap.forEach((k, v) -> {
            SgBStoFreezeBillSaveRequest request = new SgBStoFreezeBillSaveRequest();
            SgBStoFreezeSaveRequest mainRequest = new SgBStoFreezeSaveRequest();
            List<SgBStoFreezeSaveItemRequest> itemRequestList = new ArrayList<>();

            request.setObjId(-1L);
            mainRequest.setId(-1L);
            mainRequest.setBillDate(new Date());
//            mainRequest.setSourceBillId(adjust.getId());
//            mainRequest.setSourceBillNo(adjust.getBillNo());
            //来源单据信息 订单中心传
            mainRequest.setSourceBillNo(jsonObject.getString("SOURCE_BILL_NO"));
            mainRequest.setSourceBillId(jsonObject.getLong("SOURCE_BILL_ID"));
            mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_REF);
//            mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_ADJUST);
            if (k.equals(SgConstantsIF.STOCK_TYPE_IMPERFECT)) {
                mainRequest.setBillType(SgConstantsIF.FREEZE_BILL_TYPE_IMPERFECT_TO_GOOD);
            }
            if (k.equals(SgConstantsIF.STOCK_TYPE_CALLBACK)) {
                mainRequest.setBillType(SgConstantsIF.FREEZE_BILL_TYPE_CALLBACK_TO_GOOD);
            }
            if (k.equals(SgConstantsIF.STOCK_TYPE_QUALITY)) {
                mainRequest.setBillType(SgConstantsIF.FREEZE_BILL_TYPE_QUALITY_TO_GOOD);
            }
            if (k.equals(SgConstantsIF.STOCK_TYPE_LOSE)) {
                mainRequest.setBillType(SgConstantsIF.FREEZE_BILL_TYPE_LOSE_TO_GOOD);
            }
            mainRequest.setCpCStoreId(adjust.getCpCStoreId());
            mainRequest.setRemark(adjust.getRemark());

            v.forEach(x -> {
                SgBStoFreezeSaveItemRequest itemRequest = new SgBStoFreezeSaveItemRequest();

                itemRequest.setId(-1L);
                itemRequest.setSourceBillItemId(x.getId());
                itemRequest.setPsCSkuId(x.getPsCSkuId());
                itemRequest.setPsCSkuEcode(x.getPsCSkuEcode());
                itemRequest.setProduceDate(x.getProduceDate());
                itemRequest.setStockType(k);
                BeanCopierUtil.copy(x, itemRequest);
                itemRequestList.add(itemRequest);
            });

            request.setFreezeSaveRequest(mainRequest);
            request.setLoginUser(loginUser);
            request.setSgBStoFreezeSaveItemRequests(itemRequestList);
            ValueHolderV14<SgR3BaseResult> result = freezeService.saveAndSubmit(request);

            AssertUtils.isTrue(result.isOK(), "生成逻辑冻结单失败，失败原因：" + result.getMessage());
            redisFtpKeys.addAll((List<String>) result.getData().getDataJo().get("redisKey"));

        });
    }

    /**
     * 生成逻辑冻结单
     *
     * @param freezeStorageMap 明细
     * @param loginUser        用户
     * @param redisFtpKeys     流水key
     */
    private void createAndSubmitStoFreezeForFl(JSONObject jsonObject, Map<String, List<SgBStoAdjustItemSaveRequest>> freezeStorageMap, User loginUser, List<String> redisFtpKeys) {
        freezeStorageMap.forEach((k, v) -> {
            SgBStoFreezeBillSaveRequest request = new SgBStoFreezeBillSaveRequest();
            SgBStoFreezeSaveRequest mainRequest = new SgBStoFreezeSaveRequest();
            List<SgBStoFreezeSaveItemRequest> itemRequestList = new ArrayList<>();

            request.setObjId(-1L);
            mainRequest.setId(-1L);
            mainRequest.setBillDate(new Date());
//            mainRequest.setSourceBillId(adjust.getId());
//            mainRequest.setSourceBillNo(adjust.getBillNo());
            //来源单据信息 订单中心传
            mainRequest.setSourceBillNo(jsonObject.getString("SOURCE_BILL_NO"));
            mainRequest.setSourceBillId(jsonObject.getLong("SOURCE_BILL_ID"));
            mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_REF);
            if (k.equals(SgConstantsIF.STOCK_TYPE_IMPERFECT)) {
                mainRequest.setBillType(SgConstantsIF.FREEZE_BILL_TYPE_IMPERFECT_TO_GOOD);
            }
            if (k.equals(SgConstantsIF.STOCK_TYPE_CALLBACK)) {
                mainRequest.setBillType(SgConstantsIF.FREEZE_BILL_TYPE_CALLBACK_TO_GOOD);
            }
            if (k.equals(SgConstantsIF.STOCK_TYPE_QUALITY)) {
                mainRequest.setBillType(SgConstantsIF.FREEZE_BILL_TYPE_QUALITY_TO_GOOD);
            }
            if (k.equals(SgConstantsIF.STOCK_TYPE_LOSE)) {
                mainRequest.setBillType(SgConstantsIF.FREEZE_BILL_TYPE_LOSE_TO_GOOD);
            }
            Long cpPhyWarehouseId = jsonObject.getLong("CP_C_PHY_WAREHOUSE_ID");
            if (Objects.isNull(cpPhyWarehouseId)) {
                throw new NDSException("仓库信息为空！");
            }
            CpCPhyWarehouse wareHouseByPhyId = CommonCacheValUtils.getWareHouseByPhyId(cpPhyWarehouseId);
            if (Objects.nonNull(wareHouseByPhyId)) {
                //查逻辑仓
                Map<Long, List<CpCStore>> storeInfoByPhyId = CommonCacheValUtils.getStoreInfoByPhyId(wareHouseByPhyId.getId());
                if (MapUtils.isEmpty(storeInfoByPhyId) && storeInfoByPhyId.get(wareHouseByPhyId.getId()) == null) {
                    AssertUtils.logAndThrow("实体仓查询逻辑仓档案存在异常，请检查！");
                }
                CpCStore store = storeInfoByPhyId.get(wareHouseByPhyId.getId()).get(0);
                mainRequest.setCpCStoreId(store.getId());
                mainRequest.setCpCStoreEcode(store.getEcode());
                mainRequest.setCpCStoreEname(store.getEname());
            }
//            mainRequest.setRemark(adjust.getRemark());

            v.forEach(x -> {
                SgBStoFreezeSaveItemRequest itemRequest = new SgBStoFreezeSaveItemRequest();

                itemRequest.setId(-1L);
                itemRequest.setSourceBillItemId(x.getId());
                itemRequest.setPsCSkuId(x.getPsCSkuId());
                itemRequest.setPsCSkuEcode(x.getPsCSkuEcode());
                itemRequest.setProduceDate(x.getProduceDate());
                itemRequest.setStockType(k);
                BeanCopierUtil.copy(x, itemRequest);
                itemRequestList.add(itemRequest);
            });

            request.setFreezeSaveRequest(mainRequest);
            request.setLoginUser(loginUser);
            request.setSgBStoFreezeSaveItemRequests(itemRequestList);
            ValueHolderV14<SgR3BaseResult> result = freezeService.saveAndSubmit(request);

            AssertUtils.isTrue(result.isOK(), "生成逻辑冻结单失败，失败原因：" + result.getMessage());
            redisFtpKeys.addAll((List<String>) result.getData().getDataJo().get("redisKey"));

        });
    }

    /**
     * 生成入库通知单和逻辑入库单
     *
     * @param jsonObject  入参
     * @param adjust      库存调整单
     * @param redisFtpKey 流水key
     * @param user        用户
     * @return ValueHolderV14
     */
    private ValueHolderV14 offsetInNoticeAndResult(JSONObject jsonObject, SgBStoAdjust adjust, List<String> redisFtpKey, User user) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "冲单成功！");

        //生成入库通知单
        SgBStoInNoticesBillSaveRequest billSaveRequest = buildInNotices(jsonObject, adjust);
        log.info(LogUtil.format("新增入库通知单入参信息:{}", "offsetInAndFreeze.addInNotices"), JSONObject.toJSONString(billSaveRequest));
        SgBStoInNoticesSaveService stoInNoticesSaveService = ApplicationContextHandle.getBean(SgBStoInNoticesSaveService.class);
        ValueHolderV14<SgBStoInNoticesBillSaveResult> valueHolderV14 = stoInNoticesSaveService.addInNotices(billSaveRequest, user);
        log.info(LogUtil.format("新增入库通知单出参信息:{}", "offsetInAndFreeze.addInNotices"), JSONObject.toJSONString(valueHolderV14));

        if (!valueHolderV14.isOK()) {
            v14.setMessage(valueHolderV14.getMessage());
            v14.setCode(ResultCode.FAIL);
            return v14;
        }
        SgBStoInNoticesBillSaveResult data = valueHolderV14.getData();

        //由退货入库单传入库时间
        Date inTime = jsonObject.getDate("IN_TIME");
        //生成逻辑入库单
        Date date = Objects.isNull(inTime) ? new Date() : inTime;
        SgBStoInResultBillSaveRequest inResultBillSaveRequest = buildInResult(data, billSaveRequest, date, user);
        log.info(LogUtil.format("新增逻辑入库单入参信息:{}", "offsetInAndFreeze.saveInResult"), JSONObject.toJSONString(billSaveRequest));
        ValueHolderV14<SgR3BaseResult> v141 = stoInResultSaveR3Service.saveInResult(inResultBillSaveRequest);
        log.info(LogUtil.format("新增逻辑入库单出参信息:{}", "offsetInAndFreeze.saveInResult"), JSONObject.toJSONString(v141));

        if (!v141.isOK()) {
            v14.setMessage(v141.getMessage());
            v14.setCode(ResultCode.FAIL);
            return v14;
        }

        //逻辑入库单提交
        SgR3BaseResult baseResult = v141.getData();
        SgBStoInResultSubmitRequest sgStoInResultSubmitRequest = buildSubmitInResult(baseResult, user);
        log.info(LogUtil.format("提交逻辑入库单入参信息:{}", "offsetInAndFreeze.submitInResult"), JSONObject.toJSONString(sgStoInResultSubmitRequest));
        ValueHolderV14<List<SgBStoInResultBillSubmitResult>> holderV14 = submitResultService.submitInResult(sgStoInResultSubmitRequest);
        log.info(LogUtil.format("提交逻辑入库单出参信息:{}", "offsetInAndFreeze.submitInResult"), JSONObject.toJSONString(holderV14));
        if (!holderV14.isOK()) {
            v14.setMessage(holderV14.getMessage());
            v14.setCode(ResultCode.FAIL);
            return v14;
        }
        List<SgBStoInResultBillSubmitResult> submitResults = holderV14.getData();
        if (CollectionUtils.isNotEmpty(submitResults)) {
            redisFtpKey.addAll(submitResults.get(0).getRedisBillFtpKeyList());
        }
        return v14;
    }

    /**
     * 生成入库通知单和逻辑入库单
     *
     * @param jsonObject  入参
     * @param items
     * @param redisFtpKey 流水key
     * @param user        用户
     * @return ValueHolderV14
     */
    private ValueHolderV14 offsetInNoticeAndResultForFl(JSONObject jsonObject, List<SgBStoAdjustItemSaveRequest> items, List<String> redisFtpKey, User user) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "冲单成功！");

        //生成入库通知单
        SgBStoInNoticesBillSaveRequest billSaveRequest = buildInNoticesForFl(jsonObject, items);
        log.info(LogUtil.format("新增入库通知单入参信息:{}", "offsetInAndFreeze.addInNotices"), JSONObject.toJSONString(billSaveRequest));
        SgBStoInNoticesSaveService stoInNoticesSaveService = ApplicationContextHandle.getBean(SgBStoInNoticesSaveService.class);
        ValueHolderV14<SgBStoInNoticesBillSaveResult> valueHolderV14 = stoInNoticesSaveService.addInNotices(billSaveRequest, user);
        log.info(LogUtil.format("新增入库通知单出参信息:{}", "offsetInAndFreeze.addInNotices"), JSONObject.toJSONString(valueHolderV14));

        if (!valueHolderV14.isOK()) {
            v14.setMessage(valueHolderV14.getMessage());
            v14.setCode(ResultCode.FAIL);
            return v14;
        }
        SgBStoInNoticesBillSaveResult data = valueHolderV14.getData();

        //由退货入库单传入库时间
        Date inTime = jsonObject.getDate("IN_TIME");
        //生成逻辑入库单
        Date date = Objects.isNull(inTime) ? new Date() : inTime;
        SgBStoInResultBillSaveRequest inResultBillSaveRequest = buildInResult(data, billSaveRequest, date, user);
        log.info(LogUtil.format("新增逻辑入库单入参信息:{}", "offsetInAndFreeze.saveInResult"), JSONObject.toJSONString(billSaveRequest));
        ValueHolderV14<SgR3BaseResult> v141 = stoInResultSaveR3Service.saveInResult(inResultBillSaveRequest);
        log.info(LogUtil.format("新增逻辑入库单出参信息:{}", "offsetInAndFreeze.saveInResult"), JSONObject.toJSONString(v141));

        if (!v141.isOK()) {
            v14.setMessage(v141.getMessage());
            v14.setCode(ResultCode.FAIL);
            return v14;
        }

        //逻辑入库单提交
        SgR3BaseResult baseResult = v141.getData();
        SgBStoInResultSubmitRequest sgStoInResultSubmitRequest = buildSubmitInResult(baseResult, user);
        log.info(LogUtil.format("提交逻辑入库单入参信息:{}", "offsetInAndFreeze.submitInResult"), JSONObject.toJSONString(sgStoInResultSubmitRequest));
        ValueHolderV14<List<SgBStoInResultBillSubmitResult>> holderV14 = submitResultService.submitInResult(sgStoInResultSubmitRequest);
        log.info(LogUtil.format("提交逻辑入库单出参信息:{}", "offsetInAndFreeze.submitInResult"), JSONObject.toJSONString(holderV14));
        if (!holderV14.isOK()) {
            v14.setMessage(holderV14.getMessage());
            v14.setCode(ResultCode.FAIL);
            return v14;
        }
        List<SgBStoInResultBillSubmitResult> submitResults = holderV14.getData();
        if (CollectionUtils.isNotEmpty(submitResults)) {
            redisFtpKey.addAll(submitResults.get(0).getRedisBillFtpKeyList());
        }
        return v14;
    }

    /**
     * 构建逻辑入库单提交数据
     *
     * @param result 逻辑入库单新增返回值
     * @param user   用户
     * @return SgBStoInResultSubmitRequest
     */
    private SgBStoInResultSubmitRequest buildSubmitInResult(SgR3BaseResult result, User user) {
        SgBStoInResultSubmitRequest stoInResultSubmitRequest = new SgBStoInResultSubmitRequest();
        List<Long> arrayIdList = new ArrayList<>();
        arrayIdList.add(result.getDataJo().getLong("objid"));
        stoInResultSubmitRequest.setIds(arrayIdList);
        stoInResultSubmitRequest.setLoginUser(user);
        stoInResultSubmitRequest.setRedisBillFtpKeyList(new ArrayList<>());
        stoInResultSubmitRequest.setInterfaceTypeFlag(SgConstantsIF.Interface_FLAG_OMS_IN_NOTICES);
        return stoInResultSubmitRequest;
    }

    /**
     * 构建逻辑入库单数据
     * ps：明细自动在入库通知单带出
     *
     * @param data            入库通知单新增返回参数
     * @param billSaveRequest 入库通知单入参（取逻辑仓）
     * @param user            用户
     * @return SgBStoInResultBillSaveRequest
     */
    private SgBStoInResultBillSaveRequest buildInResult(SgBStoInNoticesBillSaveResult data,
                                                        SgBStoInNoticesBillSaveRequest billSaveRequest, Date date, User user) {

        SgBStoInResultSaveRequest inResultSaveRequest = new SgBStoInResultSaveRequest();
        inResultSaveRequest.setSgBStoInNoticesId(data.getId());
        inResultSaveRequest.setSgBStoInNoticesNo(data.getBillNo());
        List<SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest> inNoticesItemSaveRequests = billSaveRequest.getInNoticesItemSaveRequests();
        if (CollectionUtils.isNotEmpty(inNoticesItemSaveRequests)) {
            SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest itemSaveRequest = inNoticesItemSaveRequests.get(0);
            inResultSaveRequest.setCpCStoreId(itemSaveRequest.getCpCStoreId());
            inResultSaveRequest.setCpCStoreEcode(itemSaveRequest.getCpCStoreEcode());
            inResultSaveRequest.setCpCStoreEname(itemSaveRequest.getCpCStoreEname());
        }

        inResultSaveRequest.setInTime(date);
        inResultSaveRequest.setLoginUser(user);
        inResultSaveRequest.setIsLast(SgConstants.IS_LAST_YES);
        inResultSaveRequest.setId(-1L);

        SgBStoInResultBillSaveRequest inResultBillSaveRequest = new SgBStoInResultBillSaveRequest();
        inResultBillSaveRequest.setLoginUser(user);
        inResultBillSaveRequest.setObjId(-1L);
        inResultBillSaveRequest.setInResultSaveRequest(inResultSaveRequest);
        inResultBillSaveRequest.setSgBStoInNoticesId(data.getId());
        inResultBillSaveRequest.setSgBStoInNoticesNo(data.getBillNo());

        return inResultBillSaveRequest;
    }

    /**
     * 构建入库通知单
     *
     * @param jsonObject 入参
     * @param adjust     库存调整单
     * @return SgBStoInNoticesBillSaveRequest
     */
    private SgBStoInNoticesBillSaveRequest buildInNotices(JSONObject jsonObject, SgBStoAdjust adjust) {
        SgBStoInNoticesBillSaveRequest billSaveRequest = new SgBStoInNoticesBillSaveRequest();

        SgBStoInNoticesBillSaveRequest.SgBStoInNoticesSaveRequest stoInNoticesSaveRequest = new SgBStoInNoticesBillSaveRequest.SgBStoInNoticesSaveRequest();
        //来源单据信息 订单中心传
        stoInNoticesSaveRequest.setSourceBillNo(jsonObject.getString("SOURCE_BILL_NO"));
        stoInNoticesSaveRequest.setSourceBillId(jsonObject.getLong("SOURCE_BILL_ID"));
        stoInNoticesSaveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_REF);
        //实体仓信息 订单中心传订单中心不传就库存调整单上单来
        Long cpPhyWarehouseId = jsonObject.getLong("CP_C_PHY_WAREHOUSE_ID");
        if (Objects.isNull(cpPhyWarehouseId)) {
            Long cpStoreId = adjust.getCpCStoreId();
            CpCPhyWarehouse wareHouse = CommonCacheValUtils.getWareHouse(cpStoreId);
            if (Objects.nonNull(wareHouse)) {
                cpPhyWarehouseId = wareHouse.getId();
            }
        }

        CpCPhyWarehouse wareHouseByPhyId = CommonCacheValUtils.getWareHouseByPhyId(cpPhyWarehouseId);
        if (Objects.nonNull(wareHouseByPhyId)) {
            stoInNoticesSaveRequest.setCpCPhyWarehouseId(wareHouseByPhyId.getId());
            stoInNoticesSaveRequest.setCpCPhyWarehouseEcode(wareHouseByPhyId.getEcode());
            stoInNoticesSaveRequest.setCpCPhyWarehouseEname(wareHouseByPhyId.getEname());
        }

        //单据日期
        stoInNoticesSaveRequest.setBillDate(new Date());
        //单据状态
        stoInNoticesSaveRequest.setBillStatus(SgStoreConstants.BILL_NOTICES_STATUS_INIT);
        //发货方编码 订单中心传
        stoInNoticesSaveRequest.setSenderEcode(jsonObject.getString("SENDER_ECODE"));
        //发货方名称 订单中心传
        stoInNoticesSaveRequest.setSenderName(jsonObject.getString("SENDER_NAME"));
        //入库类型
        stoInNoticesSaveRequest.setInType(SgStoreConstantsIF.OUT_TYPE_BIG_GOODS);
        stoInNoticesSaveRequest.setIsPassWms(SgStoreConstants.IS_PASS_THIRD_PARTY_N);
        //查逻辑仓
        Map<Long, List<CpCStore>> storeInfoByPhyId = CommonCacheValUtils.getStoreInfoByPhyId(wareHouseByPhyId.getId());

        if (MapUtils.isEmpty(storeInfoByPhyId) && storeInfoByPhyId.get(wareHouseByPhyId.getId()) == null) {
            AssertUtils.logAndThrow("实体仓查询逻辑仓档案存在异常，请检查！");
        }
        CpCStore store = storeInfoByPhyId.get(wareHouseByPhyId.getId()).get(0);
        billSaveRequest.setInNoticesItemSaveRequests(buildInNoticesItem(adjust, store));
        billSaveRequest.setInNoticesSaveRequest(stoInNoticesSaveRequest);

        return billSaveRequest;
    }

    /**
     * 构建入库通知单
     *
     * @param jsonObject 入参
     * @param items
     * @return SgBStoInNoticesBillSaveRequest
     */
    private SgBStoInNoticesBillSaveRequest buildInNoticesForFl(JSONObject jsonObject, List<SgBStoAdjustItemSaveRequest> items) {
        SgBStoInNoticesBillSaveRequest billSaveRequest = new SgBStoInNoticesBillSaveRequest();

        SgBStoInNoticesBillSaveRequest.SgBStoInNoticesSaveRequest stoInNoticesSaveRequest = new SgBStoInNoticesBillSaveRequest.SgBStoInNoticesSaveRequest();
        //来源单据信息 订单中心传
        stoInNoticesSaveRequest.setSourceBillNo(jsonObject.getString("SOURCE_BILL_NO"));
        stoInNoticesSaveRequest.setSourceBillId(jsonObject.getLong("SOURCE_BILL_ID"));
        stoInNoticesSaveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_REF);
        //实体仓信息 订单中心传订单中心不传就库存调整单上单来
        Long cpPhyWarehouseId = jsonObject.getLong("CP_C_PHY_WAREHOUSE_ID");
        if (Objects.isNull(cpPhyWarehouseId)) {
            throw new NDSException("仓库信息为空！");
        }
        CpCPhyWarehouse wareHouseByPhyId = CommonCacheValUtils.getWareHouseByPhyId(cpPhyWarehouseId);
        if (Objects.nonNull(wareHouseByPhyId)) {
            stoInNoticesSaveRequest.setCpCPhyWarehouseId(wareHouseByPhyId.getId());
            stoInNoticesSaveRequest.setCpCPhyWarehouseEcode(wareHouseByPhyId.getEcode());
            stoInNoticesSaveRequest.setCpCPhyWarehouseEname(wareHouseByPhyId.getEname());
        }

        //单据日期
        stoInNoticesSaveRequest.setBillDate(new Date());
        //单据状态
        stoInNoticesSaveRequest.setBillStatus(SgStoreConstants.BILL_NOTICES_STATUS_INIT);
        //发货方编码 订单中心传
        stoInNoticesSaveRequest.setSenderEcode(jsonObject.getString("SENDER_ECODE"));
        //发货方名称 订单中心传
        stoInNoticesSaveRequest.setSenderName(jsonObject.getString("SENDER_NAME"));
        //入库类型
        stoInNoticesSaveRequest.setInType(SgStoreConstantsIF.OUT_TYPE_BIG_GOODS);
        stoInNoticesSaveRequest.setIsPassWms(SgStoreConstants.IS_PASS_THIRD_PARTY_N);
        //查逻辑仓
        Map<Long, List<CpCStore>> storeInfoByPhyId = CommonCacheValUtils.getStoreInfoByPhyId(wareHouseByPhyId.getId());

        if (MapUtils.isEmpty(storeInfoByPhyId) && storeInfoByPhyId.get(wareHouseByPhyId.getId()) == null) {
            AssertUtils.logAndThrow("实体仓查询逻辑仓档案存在异常，请检查！");
        }
        CpCStore store = storeInfoByPhyId.get(wareHouseByPhyId.getId()).get(0);

        billSaveRequest.setInNoticesItemSaveRequests(buildInNoticesItemForFl(items, store));
        billSaveRequest.setInNoticesSaveRequest(stoInNoticesSaveRequest);

        return billSaveRequest;
    }

    /**
     * 构建出库通知单明细
     *
     * @param adjust adjust
     * @param store  store
     * @return SgBStoInNoticesItemSaveRequest
     */
    private List<SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest> buildInNoticesItem(SgBStoAdjust adjust, CpCStore store) {


        List<SgBStoAdjustItem> sgStoAdjustItems = adjustItemMapper.selectList(new LambdaQueryWrapper<SgBStoAdjustItem>()
                .eq(SgBStoAdjustItem::getSgBStoAdjustId, adjust.getId()));

        List<SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest> itemRequests = new ArrayList<>(sgStoAdjustItems.size());

        if (CollectionUtils.isNotEmpty(sgStoAdjustItems)) {
            sgStoAdjustItems.forEach(item -> {
                SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest itemSaveRequest = new SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest();
                BeanUtils.copyProperties(item, itemSaveRequest);
                //数量取反
                itemSaveRequest.setQty(item.getQty());
                itemSaveRequest.setCpCStoreId(store.getId());
                itemSaveRequest.setCpCStoreEname(store.getEname());
                itemSaveRequest.setCpCStoreEcode(store.getEcode());
                PsCProSkuResult skuInfo = CommonCacheValUtils.getSkuInfo(itemSaveRequest.getPsCSkuId());
                itemSaveRequest.setPsCSkuId(skuInfo.getId());
                itemSaveRequest.setGbcode(skuInfo.getGbcode());
                itemSaveRequest.setPsCSkuEcode(skuInfo.getSkuEcode());
                itemSaveRequest.setPsCProId(skuInfo.getPsCProId());
                itemSaveRequest.setPsCProEcode(skuInfo.getPsCProEcode());
                itemSaveRequest.setPsCProEname(skuInfo.getPsCProEname());
                itemSaveRequest.setPsCSpec1Id(skuInfo.getPsCSpec1objId());
                itemSaveRequest.setPsCSpec1Ecode(skuInfo.getClrsEcode());
                itemSaveRequest.setPsCSpec1Ename(skuInfo.getClrsEname());
                itemSaveRequest.setPsCSpec2Id(skuInfo.getPsCSpec2objId());
                itemSaveRequest.setPsCSpec2Ecode(skuInfo.getSizesEcode());
                itemSaveRequest.setPsCSpec2Ename(skuInfo.getSizesEname());
                itemSaveRequest.setGbcode(skuInfo.getGbcode());
                itemRequests.add(itemSaveRequest);
            });
        }

        return itemRequests;
    }

    /**
     * 构建出库通知单明细(富勒)
     *
     * @param items
     * @param store store
     * @return SgBStoInNoticesItemSaveRequest
     */
    private List<SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest> buildInNoticesItemForFl(List<SgBStoAdjustItemSaveRequest> items, CpCStore store) {

        List<SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest> itemRequests = new ArrayList<>(items.size());
        if (CollectionUtils.isNotEmpty(items)) {
            items.forEach(item -> {
                SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest itemSaveRequest = new SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest();
                BeanUtils.copyProperties(item, itemSaveRequest);
                //数量取反
                itemSaveRequest.setQty(item.getQty());
                itemSaveRequest.setCpCStoreId(store.getId());
                itemSaveRequest.setCpCStoreEname(store.getEname());
                itemSaveRequest.setCpCStoreEcode(store.getEcode());
                PsCProSkuResult skuInfo = CommonCacheValUtils.getSkuInfo(itemSaveRequest.getPsCSkuId());
                itemSaveRequest.setPsCSkuId(skuInfo.getId());
                itemSaveRequest.setGbcode(skuInfo.getGbcode());
                itemSaveRequest.setPsCSkuEcode(skuInfo.getSkuEcode());
                itemSaveRequest.setPsCProId(skuInfo.getPsCProId());
                itemSaveRequest.setPsCProEcode(skuInfo.getPsCProEcode());
                itemSaveRequest.setPsCProEname(skuInfo.getPsCProEname());
                itemSaveRequest.setPsCSpec1Id(skuInfo.getPsCSpec1objId());
                itemSaveRequest.setPsCSpec1Ecode(skuInfo.getClrsEcode());
                itemSaveRequest.setPsCSpec1Ename(skuInfo.getClrsEname());
                itemSaveRequest.setPsCSpec2Id(skuInfo.getPsCSpec2objId());
                itemSaveRequest.setPsCSpec2Ecode(skuInfo.getSizesEcode());
                itemSaveRequest.setPsCSpec2Ename(skuInfo.getSizesEname());
                itemSaveRequest.setGbcode(skuInfo.getGbcode());
                itemRequests.add(itemSaveRequest);
            });
        }
        return itemRequests;
    }
}
