package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNoticesItem;
import com.burgeon.r3.sg.core.utils.DateUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesMapper;
import com.jackrain.nea.oc.oms.api.GetDetailCmd;
import com.jackrain.nea.oc.oms.api.OcBorderDetailCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 零售发货单推送接口
 * @version 1.0
 * @date 2021/8/26
 */
@Slf4j
@Component
public class DrpStoPhyOutNoticesProcessor extends AbstractDrpInterfaceProcessor<SgBStoOutNotices, SgBStoOutNoticesItem> {

    @Value("${drp.interface.failNum:3}")
    public int failNum;

    @Reference(group = "oms-fi", version = "1.0")
    private GetDetailCmd getDetailCmd;

    @Reference(group = "oms-fi", version = "1.0")
    private OcBorderDetailCmd borderDetailCmd;

    @Autowired
    SgBStoOutNoticesMapper stoOutNoticesMapper;

    @Override
    public LambdaQueryWrapper<SgBStoOutNotices> execMainWrapper() {
        LambdaQueryWrapper<SgBStoOutNotices> wrapper = new LambdaQueryWrapper<>();
        // 传第三方类型为ERP
        wrapper.eq(SgBStoOutNotices::getThirdPartyType, SgConstantsIF.THIRD_PARTY_TYPE_ERP);
        wrapper.eq(SgBStoOutNotices::getSourceBillType, SgConstantsIF.BILL_TYPE_RETAIL);
        wrapper.eq(SgBStoOutNotices::getDeliveryMethod, SgConstantsIF.SG_STO_OUT_DELIVERY_WAY_DELIVERY);
        wrapper.eq(SgBStoOutNotices::getIsToStore, SgStoreConstants.ONE);
        wrapper.eq(SgBStoOutNotices::getBillStatus, SgStoreConstants.BILL_NOTICES_STATUS_ALL_OUT);

        // 传DRP状态为未传，或者传失败
        wrapper.and(x -> x.eq(SgBStoOutNotices::getDrpStatus, SgStoreConstants.SEND_DRP_STATUS_UNDECLARED).or().eq(SgBStoOutNotices::getDrpStatus, SgStoreConstants.SEND_DRP_STATUS_FAIL).or().isNull(SgBStoOutNotices::getDrpStatus));

        // DRP失败次数小于6
        wrapper.and(x -> x.isNull(SgBStoOutNotices::getDrpFailCount).or().lt(SgBStoOutNotices::getDrpFailCount, failNum));

        wrapper.eq(SgBStoOutNotices::getIsactive, SgConstants.IS_ACTIVE_Y);

        return wrapper;
    }

    @Override
    public LambdaQueryWrapper<SgBStoOutNoticesItem> execitemWrapper(Long mainId) {
        return null;
    }

    @Override
    public String drpInterfaceUrl() {
        return "erp.o2osoout.notice";
    }

    @Override
    public String itemMainField() {
        return "sg_b_sto_out_notices_id";
    }

    @Override
    public String drpStatus() {
        return "DRP_STATUS";
    }

    @Override
    public String drpStatusFailCount() {
        return "DRP_FAIL_COUNT";
    }

    @Override
    public String drpStatusFailReason() {
        return "DRP_FAIL_REASON";
    }

    @Override
    public JSONObject execInterfaceParam(SgBStoOutNotices outNotices, List<SgBStoOutNoticesItem> itemList) {
        // 查询零售发货单
        JSONObject request = new JSONObject();
        request.put("ZTDOCNO", outNotices.getBillNo());
        String date = "";
        if (outNotices.getBillDate() != null) {
            date = DateUtils.formatSync8(outNotices.getBillDate(), DateUtils.DATE_PATTERN);
        }
        request.put("BILLDATE", date);
        request.put("EB_LOGIS_ID_COMNAME", outNotices.getCpCLogisticsEcode());
        request.put("FASTNO", outNotices.getLogisticNumber());

        List<JSONObject> requestItemList = new ArrayList<>();
        itemList.forEach(x -> {
            JSONObject requestItem = new JSONObject();
            requestItem.put("M_PRODUCTALIAS_ID_NO", x.getPsCSkuEcode());
            requestItem.put("QTYOUT", x.getQty());
            requestItemList.add(requestItem);
        });

        request.put("items", requestItemList);
        return request;
    }

    @Override
    public void handleBysuccess(SgBStoOutNotices sgBStoTransfer, List<SgBStoOutNoticesItem> z) {
    }
}
