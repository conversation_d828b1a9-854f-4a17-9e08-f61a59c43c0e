package com.burgeon.r3.inf.services.drp.out;

import com.burgeon.r3.sg.core.common.SgConstants;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.ServiceLoader;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/23 21:14
 */
@Slf4j
@Component
public class DrpInterfaceFactory implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    /**
     * OrderProcessor 列表checkCanExecuteProcess
     */
    private List<AbstractDrpInterfaceProcessor> processorList = new ArrayList<>();


    @Deprecated
    @Value("${sg.sync.channel.keepAliveTime.core:3600000}")
    private Long keepAliveTime;

    @Deprecated
    private static final String THREAD_POOL_NAME = "DrpInterfaceFactory_%d";

    @Resource
    private ThreadPoolTaskExecutor commonExecutorPool;

    /**
     * 初始化处理器Factory对象
     */
    @SuppressWarnings("ALL")
    public void initialProcessorFactory() {

        log.info("Start Construct JavaEngineFactory");

        ServiceLoader<AbstractDrpInterfaceProcessor> processorLoader = ServiceLoader.load(AbstractDrpInterfaceProcessor.class);
        Iterator<AbstractDrpInterfaceProcessor> processorIterator = processorLoader.iterator();

        while (processorIterator.hasNext()) {
            AbstractDrpInterfaceProcessor orderMsgProcessor = processorIterator.next();
            this.applicationContext.getAutowireCapableBeanFactory().autowireBean(orderMsgProcessor);
            log.info("Start Load DrpInterfaceProcessor name={}", orderMsgProcessor.getClass().getName());
            processorList.add(orderMsgProcessor);
        }

        log.info("Finish Construct JavaEngineFactory");
    }

    /**
     * 处理业务
     */
    public ValueHolderV14 execProcessor(String param) {

        /*ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNameFormat(THREAD_POOL_NAME).build();
        ThreadPoolExecutor exec = new ThreadPoolExecutor(processorList.size(), processorList.size(), keepAliveTime, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(100), namedThreadFactory, new RejectedExecutionHandler() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                log.error("DrpInterfaceFactory.execute.Error:{}", "队列阻塞");
            }
        });*/

        log.info("DrpInterfaceFactory.execProcessor.processorList.size:{})", processorList.size());

        List<Future<List<ValueHolderV14>>> futures = new ArrayList<>();
        for (AbstractDrpInterfaceProcessor processor : processorList) {

            if (StringUtils.isNotEmpty(param)) {

                log.info("DrpInterfaceFactory className:{}", processor.getClassName());

                String[] split = processor.getClassName().split("\\.");
                String name = split[split.length - 1];

                if (!Arrays.asList(param.split(",")).contains(name)) {
                    continue;
                }
            }

            Future<List<ValueHolderV14>> m = commonExecutorPool.submit(new Callable() {
                @Override
                public List<ValueHolderV14> call() throws Exception {
                    try {
                        return processor.exec();
                    } catch (Exception e) {
                        List<ValueHolderV14> resultList = new ArrayList<>();
                        ValueHolderV14 result = new ValueHolderV14();
                        result.setCode(ResultCode.FAIL);
                        result.setMessage(processor.getClass().getSimpleName() + ":"
                                + Throwables.getStackTraceAsString(e).substring(500));
                        resultList.add(result);
                        return resultList;
                    }

                }

            });
            futures.add(m);
        }
        StringBuffer sb = new StringBuffer();

        boolean flag = false;
        for (Future<List<ValueHolderV14>> future : futures) {

            try {
                List<ValueHolderV14> resultList = future.get();
                for (ValueHolderV14 result : resultList) {
                    log.info("execProcessor.result result:{}", result);
                    if (result.getCode() == ResultCode.FAIL) {
                        sb.append(result.getMessage());
                        flag = true;
                    } else {
                        if (StringUtils.isNotEmpty(result.getMessage())) {

                        }
                        sb.append(result.getMessage());
                    }
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            } catch (ExecutionException e) {
                e.printStackTrace();
            }
        }
        log.info("execProcessor.sb:{}", sb);
        if (sb != null && !StringUtils.isEmpty(sb.toString())) {
            if (flag) {
                return new ValueHolderV14<>(ResultCode.FAIL, sb.toString());
            } else {
                return new ValueHolderV14<>(ResultCode.SUCCESS, sb.toString());
            }
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
    }

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}

