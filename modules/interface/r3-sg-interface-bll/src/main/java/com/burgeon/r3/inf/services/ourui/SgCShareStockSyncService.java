package com.burgeon.r3.inf.services.ourui;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.r3.inf.mapper.SgCProductSystemShareLogMapper;
import com.burgeon.r3.inf.mapper.SgCStoreWarehouseSelectionMapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.basic.model.request.SgStorageQueryRequest;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorage;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.LogUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.inf.common.SgInfDrpNotifyConstants;
import com.burgeon.r3.sg.inf.model.dto.SgCProductSystemShareLogDto;
import com.burgeon.r3.sg.inf.model.dto.SgCSelectionGenericDto;
import com.burgeon.r3.sg.inf.model.request.ourui.SgCShareStockSyncItemRequest;
import com.burgeon.r3.sg.inf.model.request.ourui.SgCShareStockSyncRequest;
import com.burgeon.r3.sg.inf.model.result.ourui.SgCShareStockSyncResult;
import com.burgeon.r3.sg.inf.model.table.SgCProductSystemShareLog;
import com.burgeon.r3.sg.inf.model.table.SgCStoreWarehouseSelection;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/29 15:47
 */
@Service
@Slf4j
public class SgCShareStockSyncService extends ServiceImpl<SgCProductSystemShareLogMapper, SgCProductSystemShareLog> {
    @Autowired
    private SgCProductSystemShareLogMapper sgProductSystemShareLogMapper;

    @Autowired
    private CpCStoreMapper cpCStoreMapper;

    @Autowired
    private SgCStoreWarehouseSelectionMapper sgCStoreWarehouseSelectionMapper;

    @Autowired
    private SgStorageQueryService sgStorageQueryService;

    /**
     * 同步共享量
     *
     * @param requests 请求参数
     * @return ValueHolderV14
     */
    public ValueHolderV14<List<SgCShareStockSyncResult>> syncShareStock(List<SgCShareStockSyncRequest> requests) {
        ValueHolderV14<List<SgCShareStockSyncResult>> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "同步成功!");
        if (CollectionUtils.isEmpty(requests)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "请求参数不能为空!");
        }
        if(requests.size() > 1){
            LogUtils.printLog("Start.SgCShareStockSyncService.syncShareStock.requests.size={}",requests.size());
            return new ValueHolderV14<>(ResultCode.FAIL, "请求请按照单仓维度推送数据!");
        }
        List<SgCShareStockSyncRequest> unqualifiedRequests =
                requests.stream().filter(r -> Objects.isNull(r.getBillDate()) || Objects.isNull(r.getCpCStoreEcode())
                        || CollectionUtils.isEmpty(r.getItems())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(unqualifiedRequests)) {
            LogUtils.printLog(" return SgCShareStockSyncService.syncShareStock unqualifiedRequests={}",
                    JSON.toJSONString(unqualifiedRequests));
            return new ValueHolderV14<>(ResultCode.FAIL, "请求参数中单据日期、逻辑仓编码、明细信息不能为空!");
        }
        List<SgCShareStockSyncItemRequest> items = requests.get(0).getItems();
        List<String> skuCodes = items.stream().map(SgCShareStockSyncItemRequest::getPsCSkuEcode).collect(Collectors.toList());
        if(skuCodes.size() > 1000){
            LogUtils.printLog("Start.SgCShareStockSyncService.syncShareStock.requests.skuCodes={}",skuCodes.size());
            return new ValueHolderV14<>(ResultCode.FAIL, "请求SKU超过数量限制!");
        }
        try {
            SgCShareStockSyncService service = ApplicationContextHandle.getBean(SgCShareStockSyncService.class);
            vh = service.saveRequest(requests);
        } catch (Exception e) {
            log.error(" SgCShareStockSyncService.syncShareStock.saveRequest exception_has_occured:{}",
                    Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("共享量同步异常,异常原因:" + e.getMessage());
        }
        return vh;
    }

    /**
     * 保存商品系统共享日志
     *
     * @param requests 请求参数
     * @return ValueHolderV14
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<List<SgCShareStockSyncResult>> saveRequest(List<SgCShareStockSyncRequest> requests) {
        ValueHolderV14<List<SgCShareStockSyncResult>> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "同步成功!");

        List<String> storeEcodes =
                requests.stream().map(SgCShareStockSyncRequest::getCpCStoreEcode).collect(Collectors.toList());
        Map<String, CpCStore> storeMap = CommonCacheValUtils.queryStoreInfosByEcodes(storeEcodes);
        LogUtils.printLog(" Start SgCShareStockSyncService.saveRequest storeMap.valueSize={},key={}",
                storeMap.values().size(), JSON.toJSONString(storeMap.keySet()));
        List<String> skuEcodeList = new ArrayList<>();
        requests.forEach(r -> skuEcodeList.addAll(r.getItems().stream().map(SgCShareStockSyncItemRequest::getPsCSkuEcode).collect(Collectors.toList())));
        Map<String, PsCProSkuResult> skuInfoMap = CommonCacheValUtils.getSkuInfo(skuEcodeList);
        List<SgCShareStockSyncResult> erroResults = new ArrayList<>();
        //需要保存的数据
        List<SgCProductSystemShareLog> insertShareList = new ArrayList<>();
        User user = SystemUserResource.getRootUser();
        for (SgCShareStockSyncRequest request : requests) {
            Date billDate = DateUtil.stringToDate(request.getBillDate());
            String storeEcode = request.getCpCStoreEcode();
            List<String> skuEcodes =
                    request.getItems().stream().map(SgCShareStockSyncItemRequest::getPsCSkuEcode).collect(Collectors.toList());
            List<List<String>> pageSkuCodes =
                    StorageUtils.getPageList(skuEcodes, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
            for(List<String> skuCodes:pageSkuCodes){
                if (CollectionUtils.isNotEmpty(skuCodes)) {
                    //查询ID
                    List<SgCProductSystemShareLog> systemShareLogList = sgProductSystemShareLogMapper.selectList(Wrappers.<SgCProductSystemShareLog>lambdaQuery()
                            .select(SgCProductSystemShareLog::getId)
                            .eq(SgCProductSystemShareLog::getCpCStoreEcode,storeEcode)
                            .in(SgCProductSystemShareLog::getPsCSkuEcode,skuCodes)
                    );
                    //根据ID进行删除
                    List<Long> shareLogIdList = systemShareLogList.stream().map(SgCProductSystemShareLog::getId).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(shareLogIdList)){
                        //清除原表相同 单据日期+逻辑仓编码+条码编码数据(插入最新数据)
                        sgProductSystemShareLogMapper.delete(new LambdaQueryWrapper<SgCProductSystemShareLog>()
                                .in(SgCProductSystemShareLog::getId, shareLogIdList));
                    }
                }
            }

            //逻辑仓
            CpCStore storeResult = storeMap.get(storeEcode);
            boolean nullStoreInfo = Objects.isNull(storeResult);

            for (SgCShareStockSyncItemRequest item : request.getItems()) {
                if (nullStoreInfo) {
                    //如果中台的逻辑仓为作废状态或者不存在，依然进行保存，只赋值逻辑仓编码，后续计算共享量会过滤
                    storeResult = new CpCStore();
                    storeResult.setEcode(storeEcode);
                }

                PsCProSkuResult skuResult = skuInfoMap.get(item.getPsCSkuEcode());
                boolean nullSkuInfo = Objects.isNull(skuResult);
                if (nullSkuInfo) {
                    SgCShareStockSyncResult stockSyncResult = new SgCShareStockSyncResult();
                    stockSyncResult.setCpCStoreEcode(storeEcode);
                    stockSyncResult.setPsCSkuEcode(item.getPsCSkuEcode());
                    stockSyncResult.setMessage("未找到条码:" + item.getPsCSkuEcode() + "信息!");
                    erroResults.add(stockSyncResult);
                    continue;
                }
                //收集保存的数据
                SgCProductSystemShareLog insertShareLog = collectData(storeResult, skuResult, billDate, item, user);
                insertShareList.add(insertShareLog);
            }
        }
        if (CollectionUtils.isNotEmpty(insertShareList)) {
            List<List<SgCProductSystemShareLog>> pageSkuCodeList =
                    StorageUtils.getPageList(insertShareList, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
            for (List<SgCProductSystemShareLog> shareLogs : pageSkuCodeList) {
                this.saveBatch(shareLogs);
            }
        }
        if (CollectionUtils.isNotEmpty(erroResults)) {
            vh.setCode(ResultCode.FAIL);
            vh.setData(erroResults);
            vh.setMessage("同步失败!");
        }

        //门店选款的功能 不要了 12/30
//        if (vh.isOK()) {
//            List<SgCProductSystemShareLog> allSystemShareLogs = sgProductSystemShareLogMapper.selectListByDate();
//            Map<String, SgCProductSystemShareLog> shareLogMap = allSystemShareLogs.stream().collect(Collectors.toMap
//                    (shareLog -> shareLog.getCpCStoreId() + "-" + shareLog.getPsCSkuId(), Function.identity()));
//
//            shareLogMap = checkForDataB(shareLogMap, user);
//
//            checkForDataA(shareLogMap, user);
//
//        }

        LogUtils.printLog(" Finsh SgCShareStockSyncService.saveRequest return.vh={}", JSON.toJSONString(vh));
        return vh;
    }

    /**
     * 收集保存数据
     *
     * @param storeResult 逻辑仓信息
     * @param skuResult   商品条码信息
     * @param billDate    单据日期
     * @param item        请求明细信息
     * @param user        用户
     * @return SgCProductSystemShareLog
     */
    private SgCProductSystemShareLog collectData(CpCStore storeResult, PsCProSkuResult skuResult, Date billDate,
                                                 SgCShareStockSyncItemRequest item, User user) {
        SgCProductSystemShareLog insertLog = new SgCProductSystemShareLog();
        BeanUtils.copyProperties(item, insertLog);
        StorageUtils.setBModelDefalutData(insertLog, user);
        insertLog.setId(ModelUtil.getSequence(SgConstants.SG_C_PRODUCT_SYSTEM_SHARE_LOG));
        insertLog.setBillDate(billDate);
        //商品、条码信息
        insertLog.setPsCSkuId(skuResult.getId());
        insertLog.setPsCSkuEcode(skuResult.getSkuEcode());
        insertLog.setPsCProId(skuResult.getPsCProId());
        insertLog.setPsCProEcode(skuResult.getPsCProEcode());
        insertLog.setPsCProEname(skuResult.getPsCProEname());
        //逻辑仓信息
        insertLog.setCpCStoreId(storeResult.getId());
        insertLog.setCpCStoreEcode(storeResult.getEcode());
        insertLog.setCpCStoreEname(storeResult.getEname());
        //所属聚合仓id
        insertLog.setSgCShareStoreId(storeResult.getSgCShareStoreId());
        return insertLog;
    }


    /**
     * 从数据源B中校验有效数据
     * //查询【直营门店选款】（根据逻辑仓档案中的实体仓性质为直营，且店仓性质为门店）或
     * //【线下直营仓选款】（根据逻辑仓档案中的实体仓性质为直营，且店仓性质为仓库）或
     * //【SmartStore门店选款】（根据逻辑仓档案中的实体仓性质为代销，且店铺大类为新兴市场门店）
     * // 首先判断是否存在有效记录
     *
     * @param shareLogMap
     * @return
     */
    private Map<String, SgCProductSystemShareLog> checkForDataB(Map<String, SgCProductSystemShareLog> shareLogMap, User user) {
        //直营门店选款  逻辑仓
        List<SgCSelectionGenericDto> cOwnStoreLists = sgProductSystemShareLogMapper.selectListByOwnStore();
        List<SgCpCStore> cOwnSgCpCStores = new ArrayList<>();

        //线下自营仓选款
        List<SgCSelectionGenericDto> offlineWarehouseLists = sgProductSystemShareLogMapper.selectListByOfflineWarehouse();
        List<SgCpCStore> offlineSgCpCStores = new ArrayList<>();

        //SmartStore门店选款
        List<SgCSelectionGenericDto> smartStoreLists = sgProductSystemShareLogMapper.selectListBySmartStore();
        List<SgCpCStore> smartSgCpCStores = new ArrayList<>();

        //实体仓性质
        List<Long> CpCStoreNatureIds = new ArrayList<>();
        CpCStoreNatureIds.add(SgInfDrpNotifyConstants.RS_STORE_NATURE_ID);
        CpCStoreNatureIds.add(SgInfDrpNotifyConstants.JV_STORE_NATURE_ID);


        List<SgCpCStore> allSgCpCStores = cpCStoreMapper.selectList(new LambdaQueryWrapper<SgCpCStore>()
                .select(SgCpCStore::getId, SgCpCStore::getCpCStoreEcode, SgCpCStore::getCpCStoreEname,
                        SgCpCStore::getSgCShareStoreId, SgCpCStore::getStorenature,
                        SgCpCStore::getCpCStoreNatureId, SgCpCStore::getCStoreattrib13Id)
                .in(SgCpCStore::getCpCStoreNatureId, CpCStoreNatureIds)
                .eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y));

        for (SgCpCStore cpCStore : allSgCpCStores) {
            String storenature = cpCStore.getStorenature();
            Long cpCStoreNatureId = cpCStore.getCpCStoreNatureId();
            Long storeAttrib13Id = cpCStore.getCStoreattrib13Id();
            if (StringUtils.equals(storenature, SgInfDrpNotifyConstants.STORE_NATURE_STO) &&
                    cpCStoreNatureId.equals(SgInfDrpNotifyConstants.RS_STORE_NATURE_ID)) {
                //直营门店选款
                cOwnSgCpCStores.add(cpCStore);
            } else if (StringUtils.equals(storenature, SgInfDrpNotifyConstants.STORE_NATURE_WH) &&
                    cpCStoreNatureId.equals(SgInfDrpNotifyConstants.RS_STORE_NATURE_ID)) {
                //线下直营仓选款
                offlineSgCpCStores.add(cpCStore);
            } else if (cpCStoreNatureId.equals(SgInfDrpNotifyConstants.JV_STORE_NATURE_ID) &&
                    storeAttrib13Id != null && storeAttrib13Id.equals(17836L)) {
                //SmartStore门店选款   17836  新兴市场门店
                smartSgCpCStores.add(cpCStore);
            }
        }

        HashMap<Long, List<Long>> storeSkuMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
        List<SgCProductSystemShareLogDto> newInsertShareLog = new ArrayList<>();

        //直营门店选款
        setShareLog(cOwnStoreLists, cOwnSgCpCStores, user, storeSkuMap, newInsertShareLog, shareLogMap);
        //线下自营仓选款
        setShareLog(offlineWarehouseLists, offlineSgCpCStores, user, storeSkuMap, newInsertShareLog, shareLogMap);
        //SmartStore门店选款
        setShareLog(smartStoreLists, smartSgCpCStores, user, storeSkuMap, newInsertShareLog, shareLogMap);

        //查询逻辑仓-条码库存
        List<SgBStorage> allStorage = new ArrayList<>();
        for (Map.Entry<Long, List<Long>> entry : storeSkuMap.entrySet()) {
            Long storeId = entry.getKey();
            List<Long> skuIds = entry.getValue();
            //查询逻辑仓库存
            SgStorageQueryRequest queryRequest = new SgStorageQueryRequest();
            List<Long> storeIds = new ArrayList<>();
            storeIds.add(storeId);
            queryRequest.setStoreIds(storeIds);
            List<List<Long>> skuLists = Lists.partition(skuIds, SgConstants.SG_COMMON_QUERY_SIZE);
            for (List<Long> skuList : skuLists) {
                queryRequest.setSkuIds(skuList);
                ValueHolderV14<List<SgBStorage>> v14 = sgStorageQueryService.queryStorage(queryRequest, user);

                List<SgBStorage> data = v14.getData();
                if (!v14.isOK()) {
                    AssertUtils.logAndThrow("查询条码库存异常");
                }
                if (CollectionUtils.isEmpty(data)) {
                    AssertUtils.logAndThrow("查询条码库存异常");
                }
                allStorage.addAll(data);
            }
        }
        //赋值
        //在库库存：店仓的在库库存
        //共享库存：（店仓可用-安全库存）*同步比例
        //保留库存：在库库存-共享库存
        Map<String, SgBStorage> storageMap = allStorage.stream().collect(Collectors.toMap(
                storage -> storage.getCpCStoreId() + "-" + storage.getPsCSkuId(), Function.identity()));
        List<SgCProductSystemShareLog> insertShareLog = new ArrayList<>();
        for (SgCProductSystemShareLogDto shareLogDto : newInsertShareLog) {
            String key = shareLogDto.getCpCStoreId() + "-" + shareLogDto.getPsCSkuId();
            //安全库存
            BigDecimal qtySafety = shareLogDto.getQtySafety();
            //库存比例
            BigDecimal stockRatio = shareLogDto.getStockRatio();
            if (!storageMap.containsKey(key)) {
                AssertUtils.logAndThrow("获取店仓-条码库存异常");
            }
            SgBStorage sgBStorage = storageMap.get(key);
            //在库
            BigDecimal qtyStorage = sgBStorage.getQtyStorage();
            //可用
            BigDecimal qtyAvailable = sgBStorage.getQtyAvailable();

            //共享库存
            BigDecimal shareQty = qtyAvailable.subtract(qtySafety).multiply(stockRatio)
                    .divide(SgConstants.NUMBER_100, 0, BigDecimal.ROUND_DOWN);
            //保留库存
            BigDecimal minQty = qtyStorage.subtract(shareQty);
            SgCProductSystemShareLog shareLog = new SgCProductSystemShareLog();
            BeanUtils.copyProperties(shareLogDto, shareLog);
            shareLog.setQty(qtyStorage);
            shareLog.setShareQty(shareQty);
            shareLog.setMinQty(minQty);
            insertShareLog.add(shareLog);
        }
        //新增
        if (CollectionUtils.isNotEmpty(insertShareLog)) {
            List<List<SgCProductSystemShareLog>> shareLogs = Lists.partition(insertShareLog, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
            for (List<SgCProductSystemShareLog> shareLog : shareLogs) {
                sgProductSystemShareLogMapper.batchInsert(shareLog);
            }
        }

        return shareLogMap;
    }

    /**
     * //从数据源A中校验有效数据
     * 店仓维度选款
     *
     * @param shareLogMap
     * @return
     */
    private void checkForDataA(Map<String, SgCProductSystemShareLog> shareLogMap, User user) {

        List<SgCStoreWarehouseSelection> warehouseLists = sgCStoreWarehouseSelectionMapper.selectList(new LambdaQueryWrapper<SgCStoreWarehouseSelection>()
                .eq(SgCStoreWarehouseSelection::getIsactive, SgConstants.IS_ACTIVE_Y));

        List<Long> storeIds = new ArrayList<>();
        List<Long> skuIds = new ArrayList<>();
        List<SgCStoreWarehouseSelection> storeWarehouseSelections = new ArrayList<>();
        for (SgCStoreWarehouseSelection warehouseSelection : warehouseLists) {
            Long cStoreId = warehouseSelection.getCpCStoreId();
            Long psCSkuId = warehouseSelection.getPsCSkuId();
            String key = cStoreId + "-" + psCSkuId;
            if (shareLogMap.containsKey(key)) {
                continue;
            }
            if (!storeIds.contains(cStoreId)) {
                storeIds.add(cStoreId);
            }
            if (!skuIds.contains(psCSkuId)) {
                skuIds.add(psCSkuId);
            }
            storeWarehouseSelections.add(warehouseSelection);
        }

        List<SgCpCStore> allSgCpCStores = cpCStoreMapper.selectList(new LambdaQueryWrapper<SgCpCStore>()
                .select(SgCpCStore::getId, SgCpCStore::getSgCShareStoreId)
                .in(SgCpCStore::getId, storeIds));
        Map<Long, Long> storeMap = allSgCpCStores.stream().collect(Collectors.toMap(SgCpCStore::getId, SgCpCStore::getSgCShareStoreId));

        //查询逻辑仓-条码库存
        List<SgBStorage> allStorage = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(storeIds)) {
            List<List<Long>> stores = Lists.partition(storeIds, SgConstants.SG_COMMON_QUERY_SIZE);
            List<List<Long>> skus = Lists.partition(skuIds, SgConstants.SG_COMMON_QUERY_SIZE);
            for (List<Long> store : stores) {
                for (List<Long> sku : skus) {
                    SgStorageQueryRequest queryRequest = new SgStorageQueryRequest();
                    queryRequest.setStoreIds(store);
                    queryRequest.setSkuIds(sku);
                    ValueHolderV14<List<SgBStorage>> v14 = sgStorageQueryService.queryStorage(queryRequest, user);

                    List<SgBStorage> data = v14.getData();
                    if (!v14.isOK()) {
                        AssertUtils.logAndThrow("查询条码库存异常");
                    }
                    if (CollectionUtils.isEmpty(data)) {
                        AssertUtils.logAndThrow("查询条码库存异常");
                    }
                    allStorage.addAll(data);
                }
            }
        }

        Map<String, SgBStorage> storageMap = allStorage.stream().collect(Collectors.toMap(
                storage -> storage.getCpCStoreId() + "-" + storage.getPsCSkuId(), Function.identity()));
        //需要新增的商品系统共享日志记录
        List<SgCProductSystemShareLog> insertShareLogs = new ArrayList<>();

        for (SgCStoreWarehouseSelection warehouseSelection : storeWarehouseSelections) {
            Long cStoreId = warehouseSelection.getCpCStoreId();
            String key = cStoreId + "-" + warehouseSelection.getPsCSkuId();
            //安全库存
            BigDecimal qtySafety = warehouseSelection.getQtySafety();
            //库存比例
            BigDecimal stockRatio = warehouseSelection.getStockRatio();
            if (!storageMap.containsKey(key)) {
                AssertUtils.logAndThrow("获取店仓-条码库存异常");
            }
            SgBStorage sgBStorage = storageMap.get(key);
            //在库
            BigDecimal qtyStorage = sgBStorage.getQtyStorage();
            //可用
            BigDecimal qtyAvailable = sgBStorage.getQtyAvailable();

            //共享库存
            BigDecimal shareQty = qtyAvailable.subtract(qtySafety).multiply(stockRatio)
                    .divide(SgConstants.NUMBER_100, 0, BigDecimal.ROUND_DOWN);
            //保留库存
            BigDecimal minQty = qtyStorage.subtract(shareQty);
            SgCProductSystemShareLog shareLog = new SgCProductSystemShareLog();
            BeanUtils.copyProperties(warehouseSelection, shareLog);
            shareLog.setId(ModelUtil.getSequence(SgConstants.SG_C_PRODUCT_SYSTEM_SHARE_LOG));
            shareLog.setBillDate(new Date());
            shareLog.setQty(qtyStorage);
            shareLog.setShareQty(shareQty);
            shareLog.setMinQty(minQty);
            shareLog.setRemark("数据源A有效数据");
            //获取所所属聚合仓id
            shareLog.setSgCShareStoreId(storeMap.get(cStoreId));
            StorageUtils.setBModelDefalutData(shareLog, user);
            insertShareLogs.add(shareLog);
        }
        //新增
        if (CollectionUtils.isNotEmpty(insertShareLogs)) {
            List<List<SgCProductSystemShareLog>> shareLogs = Lists.partition(insertShareLogs, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
            for (List<SgCProductSystemShareLog> shareLog : shareLogs) {
                sgProductSystemShareLogMapper.batchInsert(shareLog);
            }
        }

    }


    //组装商品系统共享日志
    private void setShareLog(List<SgCSelectionGenericDto> genericLists, List<SgCpCStore> cpCStores, User user,
                             HashMap<Long, List<Long>> storeSkuMap, List<SgCProductSystemShareLogDto> newInsertShareLog,
                             Map<String, SgCProductSystemShareLog> shareLogMap) {
        for (SgCSelectionGenericDto genericDto : genericLists) {
            Long psCSkuId = genericDto.getPsCSkuId();
            for (SgCpCStore cpCStore : cpCStores) {
                Long cStoreId = cpCStore.getId();
                String key = cStoreId + "-" + psCSkuId;
                if (shareLogMap.containsKey(key)) {
                    continue;
                }
                SgCProductSystemShareLogDto newShareLog = new SgCProductSystemShareLogDto();
                newShareLog.setId(ModelUtil.getSequence(SgConstants.SG_C_PRODUCT_SYSTEM_SHARE_LOG));
                newShareLog.setBillDate(new Date());
                //商品、条码信息
                newShareLog.setPsCSkuId(genericDto.getPsCSkuId());
                newShareLog.setPsCSkuEcode(genericDto.getPsCSkuEcode());
                newShareLog.setPsCProId(genericDto.getPsCProId());
                newShareLog.setPsCProEcode(genericDto.getPsCProEcode());
                newShareLog.setPsCProEname(genericDto.getPsCProEname());
                //逻辑仓信息
                newShareLog.setCpCStoreId(cpCStore.getId());
                newShareLog.setCpCStoreEcode(cpCStore.getCpCStoreEcode());
                newShareLog.setCpCStoreEname(cpCStore.getCpCStoreEname());
                //所属聚合仓id
                newShareLog.setSgCShareStoreId(cpCStore.getSgCShareStoreId());
                //库存比例  安全库存 用于后续计算共享库存
                newShareLog.setStockRatio(genericDto.getStockRatio());
                newShareLog.setQtySafety(genericDto.getQtySafety());
                newShareLog.setRemark("数据源B有效数据");
                StorageUtils.setBModelDefalutData(newShareLog, user);

                shareLogMap.put(key, newShareLog);
                newInsertShareLog.add(newShareLog);
                if (storeSkuMap.containsKey(cStoreId)) {
                    List<Long> storeSkuList = storeSkuMap.get(cStoreId);
                    storeSkuList.add(psCSkuId);
                } else {
                    List<Long> storeSkuList = new ArrayList<>();
                    storeSkuList.add(psCSkuId);
                    storeSkuMap.put(cStoreId, storeSkuList);
                }
            }
        }
    }
}
