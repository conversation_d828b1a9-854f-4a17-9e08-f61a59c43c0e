package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransfer;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransferItem;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.transfer.SgBStoTransferMapper;
import com.burgeon.r3.sg.store.services.transfer.SgTransferBillStatusEnum;
import com.jackrain.nea.cpext.model.table.CpCTranwayAssign;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 调拨通知接口
 * @version 1.0
 * @date 2021/6/23 9:59
 */

@Slf4j
@Component
public class DrpTransferProcessor extends AbstractDrpInterfaceProcessor<SgBStoTransfer, SgBStoTransferItem> {

    @Autowired
    SgBStoTransferMapper sgBStoTransferMapper;


    @Override
    public LambdaQueryWrapper execMainWrapper() {
        LambdaQueryWrapper<SgBStoTransfer> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(SgBStoTransfer::getStatus, SgTransferBillStatusEnum.AUDITED_NOT_OUT.getVal());
        wrapper.ne(SgBStoTransfer::getStatus, SgTransferBillStatusEnum.VOIDED.getVal());
        wrapper.eq(SgBStoTransfer::getIsactive, SgConstants.IS_ACTIVE_Y);
        wrapper.eq(SgBStoTransfer::getDrpBillType, SgStoreConstants.DRP_BILL_TYPE_TF);
        wrapper.and(o -> {
            o.isNull(SgBStoTransfer::getDrpStatus);
            o.or(oo -> oo.eq(SgBStoTransfer::getDrpStatus, SgStoreConstants.SEND_DRP_STATUS_UNDECLARED));
            o.or(oo -> oo.eq(SgBStoTransfer::getDrpStatus, SgStoreConstants.SEND_DRP_STATUS_FAIL).lt(SgBStoTransfer::getDrpFailCount, failNum));
            return o;
        });
        return wrapper;
    }

    @Override
    public LambdaQueryWrapper<SgBStoTransferItem> execitemWrapper(Long mainId) {
        return null;
    }

    @Override
    public String drpInterfaceUrl() {
        return "erp.transfer.notice";
    }

    @Override
    public String itemMainField() {
        return "sg_b_sto_transfer_id";
    }

    @Override
    public String drpStatus() {
        return "DRP_STATUS";
    }

    @Override
    public String drpStatusFailCount() {
        return "DRP_FAIL_COUNT";
    }

    @Override
    public String drpStatusFailReason() {
        return "DRP_FAIL_REASON";
    }

    @Override
    public JSONObject execInterfaceParam(SgBStoTransfer sgBStoTransfer, List<SgBStoTransferItem> itemList) {
        JSONObject mian = new JSONObject();
        mian.put("TYPE", SgDrpConstantsIF.billType.get(sgBStoTransfer.getSourceBillType()));
        mian.put("BILLDATE", DateUtil.format(sgBStoTransfer.getBillDate(), "yyyyMMdd"));

        mian.put("ZTDOCNO", sgBStoTransfer.getBillNo());
        mian.put("C_ORIG_CODE", sgBStoTransfer.getSenderStoreEcode());
        mian.put("C_DEST_CODE", sgBStoTransfer.getReceiverStoreEcode());
        try {
            if (sgBStoTransfer.getCpCTranwayAssignId() != null) {
                CpCTranwayAssign cpCTranwayAssign = CommonCacheValUtils.getCpCTranwayAssignpById(sgBStoTransfer.getCpCTranwayAssignId());
                mian.put("TRANWAY", cpCTranwayAssign.getEname());
            } else {
                mian.put("TRANWAY", "物流");
            }
        } catch (Exception ex) {
            mian.put("TRANWAY", "物流");
        }

        // 20220310 新增字段
        mian.put("QTY_BOX", sgBStoTransfer.getBox());
        mian.put("BUNDLE", sgBStoTransfer.getBundle());
        mian.put("DESCRIPTION", sgBStoTransfer.getRemark());

        // 20220310 新增字段
        mian.put("QTY_BOX", sgBStoTransfer.getBox());
        mian.put("BUNDLE", sgBStoTransfer.getBundle());
        mian.put("DESCRIPTION", sgBStoTransfer.getRemark());
        mian.put("IS_TMS", "N");
        List<JSONObject> items = new ArrayList<>();
        for (SgBStoTransferItem item : itemList) {
            JSONObject itemJson = new JSONObject();
            itemJson.put("M_PRODUCTALIAS_NO", item.getPsCSkuEcode());
            itemJson.put("M_PRODUCT_NAME", item.getPsCProEcode());
            itemJson.put("QTY", item.getQty());
            items.add(itemJson);
        }
        mian.put("items", items);
        return mian;
    }

    @Override
    public void handleBysuccess(SgBStoTransfer sgBStoTransfer, List<SgBStoTransferItem> z) {

    }


}
