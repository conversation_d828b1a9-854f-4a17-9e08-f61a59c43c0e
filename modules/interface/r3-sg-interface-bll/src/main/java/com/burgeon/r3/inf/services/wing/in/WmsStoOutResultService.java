package com.burgeon.r3.inf.services.wing.in;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.utils.RedisConcurrentLockUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutResult;
import com.burgeon.r3.sg.core.model.table.store.out.SgBWmsToStoOutResult;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutResultMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBWmsToStoOutResultMapper;
import com.burgeon.r3.sg.store.model.request.SgBStoOutResultAndFreezeOutResultSaveRequest;
import com.burgeon.r3.sg.store.model.request.freeze.out.result.SgBStoFreezeOutResultBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutResultBillSaveRequest;
import com.burgeon.r3.sg.store.model.result.freeze.out.SgBStoFreezeOutBillResult;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutResultBillSaveResult;
import com.burgeon.r3.sg.store.services.freeze.out.result.SgBStoFreezeOutResultSaveAndSubmitService;
import com.burgeon.r3.sg.store.services.out.SgBStoOutResultSaveService;
import com.burgeon.r3.sg.store.services.out.SgBWmsToPhyOutResultServiceImpl;
import com.burgeon.r3.sg.store.services.out.WMSReturnMessageParseService;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.AssertUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * wing->中台 逻辑出库单创建
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WmsStoOutResultService {

    @Autowired
    private SgBStoOutResultSaveService sgStoOutResultSaveService;

    @Autowired
    private WMSReturnMessageParseService wmsReturnMessageParseService;

    @Autowired
    private SgBWmsToPhyOutResultServiceImpl wmsToPhyOutResultService;

    @NacosValue(value = "${sg.wms_to_stock_out_result_max_query_limit:200}", autoRefreshed = true)
    private Integer queryCnt;

    @NacosValue(value = "${sg.page.size:50}", autoRefreshed = true)
    private Integer size;

    @Resource
    private ThreadPoolTaskExecutor billOrderExecutorPool;

    @Autowired
    private SgBStoFreezeOutResultSaveAndSubmitService freezeOutResultSaveAndSubmitService;

    @NacosValue(value = "${sg.wms_to_stock_out_result.lock:false}", autoRefreshed = true)
    private Boolean addLock;
    @NacosValue(value = "${sg.wms_to_stock_out_result.lock.seconds:60}", autoRefreshed = true)
    private Integer lockSeconds;

    @Autowired
    private RedisConcurrentLockUtils redisConcurrentLockUtils;

    /**
     * 新增并审核逻辑出库单
     *
     * @param resultMap 请求参数
     */
    public ValueHolderV14<List<SgBWmsToStoOutResult>> saveAndSubmitStoOutResult(Map<String, List<SgBStoOutResultBillSaveRequest>> resultMap) {
        if (MapUtils.isEmpty(resultMap)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "请求参数不能为空!");
        }
        log.info(LogUtil.format("Start WmsStoOutResultService.saveAndSubmitStoOutResult. ReceiveParams:requests={}"
                , "saveAndSubmitStoOutResult"),
                JSONObject.toJSONString(resultMap));
        ValueHolderV14<List<SgBWmsToStoOutResult>> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "出库回执成功!");
        List<SgBWmsToStoOutResult> results = new ArrayList<>();
        resultMap.forEach((billNo, requestList) -> {
            SgBWmsToStoOutResult receiptResult = new SgBWmsToStoOutResult();
            results.add(receiptResult);
            receiptResult.setNoticesBillNo(billNo);
            try {
                WmsStoOutResultService service = ApplicationContextHandle.getBean(WmsStoOutResultService.class);
                service.checkAndSetData(requestList, receiptResult);
            } catch (Exception e) {
                log.error(LogUtil.format("WmsStoOutResultService.saveAndSubmitStoOutResult error:{}"
                        , "saveAndSubmitStoOutResult.error", billNo), Throwables.getStackTraceAsString(e));
                receiptResult.setFailedReason("新增并审核逻辑出库单异常!异常原因:" + e.getMessage());
                vh.setMessage("新增并审核逻辑出库单异常!异常原因:" + e.getMessage());
            }
        });
        vh.setData(results);
        log.info(LogUtil.format("Finish WmsStoOutResultService.saveAndSubmitStoOutResult.ReturnResult:{}",
                "saveAndSubmitStoOutResult.result"), JSONObject.toJSONString(vh));
        return vh;
    }

    /**
     * 新增并审核逻辑出库单的校验和赋值调用出库
     *
     * @param requestList       请求参数
     * @param wmsToStoOutResult 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void checkAndSetData(List<SgBStoOutResultBillSaveRequest> requestList,
                                SgBWmsToStoOutResult wmsToStoOutResult) {
        SgBStoOutResultMapper sgStoOutResultMapper = ApplicationContextHandle.getBean(SgBStoOutResultMapper.class);
        SgBStoOutResult stoOutResult = new SgBStoOutResult();

        List<String> billNos = Lists.newArrayList();
        List<String> billIds = Lists.newArrayList();
        for (SgBStoOutResultBillSaveRequest sgRequest : requestList) {
            // 最后一次出库
            sgRequest.setIsLast(SgConstants.IS_LAST_YES);
            ValueHolderV14<SgBStoOutResultBillSaveResult> subHolder = sgStoOutResultSaveService.saveSgStoOutResult(sgRequest);
            if (subHolder.isOK()) {
                //收集逻辑出库单的单据编号
                stoOutResult = sgStoOutResultMapper.selectOne(new LambdaQueryWrapper<SgBStoOutResult>()
                        .eq(SgBStoOutResult::getBillNo, subHolder.getData().getBillNo()));

                billNos.add(stoOutResult.getBillNo());
                billIds.add(String.valueOf(stoOutResult.getId()));
            } else {
                wmsToStoOutResult.setNoticesBillNo(sgRequest.getSgBStoOutNoticesNo());
                wmsToStoOutResult.setFailedReason("出库通知单新增逻辑出库单失败!" + "逻辑仓:" +
                        sgRequest.getOutResultSaveRequest().getCpCStoreEcode() + "失败原因:" + subHolder.getMessage());
//                return;
                AssertUtils.logAndThrow("出库通知单新增逻辑出库单失败!" + "逻辑仓:" +
                        sgRequest.getOutResultSaveRequest().getCpCStoreEcode() + "失败原因:" + subHolder.getMessage());
            }
        }
        wmsToStoOutResult.setResultId(StringUtils.join(billIds, ","));
        wmsToStoOutResult.setResultBillNo(StringUtils.join(billNos, ","));
    }

    /**
     * B2C出库回传中间表转化
     *
     * @param sharedIndex 当前分片数
     * @param sharedTotal 总分片数
     * @return
     */
    public ValueHolderV14<String> createStoOutResult(int sharedIndex, int sharedTotal) {
        try {
            log.info(LogUtil.format("WmsStoOutResultService.createStoOutResult 定时任务开启",
                    "WmsStoOutResultService.createStoOutResult"));
            SgBWmsToStoOutResultMapper resultMapper = ApplicationContextHandle.getBean(SgBWmsToStoOutResultMapper.class);
            List<Integer> status = Lists.newArrayList(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT, SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED);

            List<SgBWmsToStoOutResult> sgBWmsToStoOutResults = resultMapper.
                    selectDeliveryOrderListByShared(StringUtils.join(status, ","),
                            queryCnt,
                            SgStoreConstantsIF.B2cBillTypeEnum.ZPCK.getValue(),
                            sharedIndex, sharedTotal);

            if (CollectionUtils.isNotEmpty(sgBWmsToStoOutResults)) {
                List<Future<Boolean>> results = new ArrayList<>();
                List<List<SgBWmsToStoOutResult>> pageList = StorageUtils.getPageList(sgBWmsToStoOutResults, size);
                for (List<SgBWmsToStoOutResult> page : pageList) {
                    results.add(billOrderExecutorPool.submit(new CallableTobeAuditTaskWithResult(page)));
                }

                //线程执行结果获取
                for (Future<Boolean> futureResult : results) {
                    try {
                        Boolean aBoolean = futureResult.get();
                    } catch (Exception e) {
                        log.error(LogUtil.format("WmsStoOutResultService多线程获取ExecutionException异常: {}"), Throwables.getStackTraceAsString(e));
                    }
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("异常:{}", "解析创建逻辑出库单异常"), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "成功");
    }

    /**
     * 解析报文创建审核逻辑出库单
     *
     * @return ValueHolderV14<String>
     */
    public ValueHolderV14<String> createStoOutResult() {
        try {
            log.info(LogUtil.format("WmsStoOutResultService.createStoOutResult 定时任务开启",
                    "WmsStoOutResultService.createStoOutResult"));
            SgBWmsToStoOutResultMapper resultMapper = ApplicationContextHandle.getBean(SgBWmsToStoOutResultMapper.class);
            List<Integer> status = Lists.newArrayList(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT, SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED);
            List<SgBWmsToStoOutResult> sgBWmsToStoOutResults = resultMapper.
                    selectDeliveryOrderList(StringUtils.join(status, ","), queryCnt,
                            SgStoreConstantsIF.B2cBillTypeEnum.ZPCK.getValue());

            if (CollectionUtils.isNotEmpty(sgBWmsToStoOutResults)) {
                List<Future<Boolean>> results = new ArrayList<>();
                List<List<SgBWmsToStoOutResult>> pageList = StorageUtils.getPageList(sgBWmsToStoOutResults, size);
                for (List<SgBWmsToStoOutResult> page : pageList) {
                    results.add(billOrderExecutorPool.submit(new CallableTobeAuditTaskWithResult(page)));
                }

                //线程执行结果获取
                for (Future<Boolean> futureResult : results) {
                    try {
                        Boolean aBoolean = futureResult.get();
                    } catch (Exception e) {
                        log.error(LogUtil.format("WmsStoOutResultService多线程获取ExecutionException异常: {}"), Throwables.getStackTraceAsString(e));
                    }
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("异常:{}", "解析创建逻辑出库单异常"), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "成功");
    }

    /**
     * 新增后处理
     *
     * @param stoOutResult 新增成功结果集
     * @param requestMap   中间表Map key:通知单单号,val:中间表记录
     */
    private void batchSaveAuditAfter(ValueHolderV14<List<SgBWmsToStoOutResult>> stoOutResult,
                                     HashMap<String, SgBWmsToStoOutResult> requestMap,
                                     List<SgBWmsToStoOutResult> errorList) {
        List<SgBWmsToStoOutResult> updateList = Lists.newArrayList();
        updateList.addAll(errorList);

        Map<String, SgBWmsToStoOutResult> receiptResultMap = Maps.newHashMap();

        List<SgBWmsToStoOutResult> data = stoOutResult.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            receiptResultMap = data.stream().collect(Collectors.toMap(SgBWmsToStoOutResult::getNoticesBillNo, Function.identity()));
        }
        // 失败记录
        for (Map.Entry<String, SgBWmsToStoOutResult> stringSgBWmsToStoOutResultEntry : requestMap.entrySet()) {
            String noticesBillNo = stringSgBWmsToStoOutResultEntry.getKey();
            SgBWmsToStoOutResult wmsToStoOutResult = requestMap.get(noticesBillNo);
            if (receiptResultMap.containsKey(noticesBillNo)) {
                SgBWmsToStoOutResult outResult = receiptResultMap.get(noticesBillNo);
                String wmsFailedReason = outResult.getFailedReason();
                if (StringUtils.isNotBlank(wmsFailedReason)) {
                    String error = StorageUtils.strSubString(wmsFailedReason, SgConstants.SG_COMMON_STRING_SIZE - 1);
                    Integer wmsFailedCount = Optional.ofNullable(wmsToStoOutResult.getFailedCount()).orElse(0);

                    updateList.add(buildUpdateRecord(wmsToStoOutResult.getId(), error, wmsFailedCount + 1,
                            SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED, null, null));
                } else {
                    updateList.add(buildUpdateRecord(wmsToStoOutResult.getId(), StringUtils.SPACE, 0,
                            SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS, outResult.getResultId(), outResult.getResultBillNo()));
                }
            } else {
                String error = StorageUtils.strSubString(stoOutResult.getMessage(), SgConstants.SG_COMMON_STRING_SIZE - 1);
                Integer wmsFailedCount = Optional.ofNullable(wmsToStoOutResult.getFailedCount()).orElse(0);
                updateList.add(buildUpdateRecord(wmsToStoOutResult.getId(), error, wmsFailedCount + 1,
                        SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED, null, null));
            }
        }
        // 批量更新
        if (CollectionUtils.isNotEmpty(updateList)) {
            wmsToPhyOutResultService.updateBatchById(updateList, queryCnt);
        }
    }

    /**
     * 回传报文解析
     *
     * @param wmsToStoOutResultList 中间表数据
     * @param errorList             错误集合
     * @param requestMap            请求体结果集
     * @param stoOutResultMap       出库结果集
     * @param freezeOutResultMap    冻结出库结果集
     */
    private void messageParse(List<SgBWmsToStoOutResult> wmsToStoOutResultList,
                              List<SgBWmsToStoOutResult> errorList,
                              HashMap<String, SgBWmsToStoOutResult> requestMap,
                              Map<String, List<SgBStoOutResultBillSaveRequest>> stoOutResultMap,
                              Map<String, List<SgBStoFreezeOutResultBillSaveRequest>> freezeOutResultMap) {
        for (SgBWmsToStoOutResult wmsToStoOutResult : wmsToStoOutResultList) {
            try {
                SgBStoOutResultAndFreezeOutResultSaveRequest sgStoOutResultAndFreezeOutResultSaveRequest
                        = wmsReturnMessageParseService.parseMessageToOutResultBillSaveReq(wmsToStoOutResult.getMessage());
                AssertUtils.cannot(Objects.isNull(sgStoOutResultAndFreezeOutResultSaveRequest), "报文解析结果为空!");

                List<SgBStoOutResultBillSaveRequest> outResultBillSaveRequestList =
                        sgStoOutResultAndFreezeOutResultSaveRequest.getOutResultBillSaveRequestList();
                List<SgBStoFreezeOutResultBillSaveRequest> freezeOutResultBillSaveRequests =
                        sgStoOutResultAndFreezeOutResultSaveRequest.getFreezeOutResultBillSaveRequests();

                if (CollectionUtils.isNotEmpty(outResultBillSaveRequestList)) {
                    stoOutResultMap.put(wmsToStoOutResult.getNoticesBillNo(), outResultBillSaveRequestList);
                }

                if (CollectionUtils.isNotEmpty(freezeOutResultBillSaveRequests)) {
                    freezeOutResultMap.put(wmsToStoOutResult.getNoticesBillNo(), freezeOutResultBillSaveRequests);
                }

                requestMap.put(wmsToStoOutResult.getNoticesBillNo(), wmsToStoOutResult);
            } catch (Exception e) {
                log.error("messageParse", e);
                String error = StorageUtils.strSubString(e.getMessage(), SgConstants.SG_COMMON_STRING_SIZE - 1);
                errorList.add(buildUpdateRecord(wmsToStoOutResult.getId(), error
                        , Optional.ofNullable(wmsToStoOutResult.getFailedCount()).orElse(0) + 1,
                        SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED, null, null));
            }
        }
    }

    /**
     * 构造待更新的中间表记录
     *
     * @param id             中间表id
     * @param error          失败原因
     * @param wmsFailedCount wms失败次数
     * @param status         中间表状态
     * @param resultId       结果单id
     * @param resultBillNo   结果单单号
     */
    private SgBWmsToStoOutResult buildUpdateRecord(Long id, String error, Integer wmsFailedCount, Integer status,
                                                   String resultId, String resultBillNo) {
        SgBWmsToStoOutResult updateResult = new SgBWmsToStoOutResult();
        updateResult.setId(id);
        updateResult.setFailedReason(error);
        updateResult.setFailedCount(wmsFailedCount);
        updateResult.setTransformStatus(status);
        updateResult.setResultId(resultId);
        updateResult.setResultBillNo(resultBillNo);
        StorageUtils.setBModelDefalutDataByUpdate(updateResult, SystemUserResource.getRootUser());
        return updateResult;
    }

    /**
     * 处理冻结出库单数据
     *
     * @param freezeOutResultMap freezeOutResultMap
     * @return ValueHolderV14
     */
    private ValueHolderV14<List<SgBWmsToStoOutResult>> saveAndSubmitStoFreezeOutResult(
            Map<String, List<SgBStoFreezeOutResultBillSaveRequest>> freezeOutResultMap) {
        if (MapUtils.isEmpty(freezeOutResultMap)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "请求参数不能为空!");
        }

        log.info(LogUtil.format("Start WmsStoOutResultService.saveAndSubmitStoFreezeOutResult. ReceiveParams:requests={}"
                , "saveAndSubmitStoFreezeOutResult"),
                JSONObject.toJSONString(freezeOutResultMap));

        ValueHolderV14<List<SgBWmsToStoOutResult>> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "出库回执成功!");
        List<SgBWmsToStoOutResult> results = new ArrayList<>();
        freezeOutResultMap.forEach((billNo, requestList) -> {
            SgBWmsToStoOutResult receiptResult = new SgBWmsToStoOutResult();
            results.add(receiptResult);
            receiptResult.setNoticesBillNo(billNo);
            try {
                WmsStoOutResultService service = ApplicationContextHandle.getBean(WmsStoOutResultService.class);
                service.checkAndSetDataByFreeze(requestList, receiptResult);
            } catch (Exception e) {
                log.error(LogUtil.format("saveAndSubmitStoFreezeOutResult.saveAndSubmitStoOutResult error:{}"
                        , "saveAndSubmitStoFreezeOutResult.error", billNo), Throwables.getStackTraceAsString(e));
                receiptResult.setFailedReason("新增并审核逻辑出库单异常!异常原因:" + e.getMessage());
                vh.setMessage("新增并审核逻辑出库单异常!异常原因:" + e.getMessage());
            }
        });
        vh.setData(results);
        log.info(LogUtil.format("Finish WmsStoOutResultService.saveAndSubmitStoOutResult.ReturnResult:{}",
                "saveAndSubmitStoOutResult.result"), JSONObject.toJSONString(vh));
        return vh;
    }

    /**
     * 新增并审核冻结出库单的校验和赋值调用出库
     *
     * @param requestList       请求参数
     * @param wmsToStoOutResult 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void checkAndSetDataByFreeze(List<SgBStoFreezeOutResultBillSaveRequest> requestList,
                                        SgBWmsToStoOutResult wmsToStoOutResult) {
        List<String> billNos = Lists.newArrayList();
        List<String> billIds = Lists.newArrayList();
        List<String> redisKeyFtpList = new ArrayList<>();

        try {
            for (SgBStoFreezeOutResultBillSaveRequest sgRequest : requestList) {
                ValueHolderV14<SgBStoFreezeOutBillResult> v14 = freezeOutResultSaveAndSubmitService.saveAndSubmit(sgRequest);
                if (v14.isOK()) {
                    List<String> redisKeyFtp = v14.getData().getRedisKeyFtp();
                    if (CollectionUtils.isNotEmpty(redisKeyFtp)) {
                        redisKeyFtpList.addAll(redisKeyFtp);
                    }
                    billNos.add(v14.getData().getBillNo());
                    billIds.add(String.valueOf(v14.getData().getId()));
                } else {
                    wmsToStoOutResult.setNoticesBillNo(sgRequest.getMainRequest().getSgBStoOutNoticesNo());
                    wmsToStoOutResult.setFailedReason("出库通知单新增逻辑出库单失败!" + "逻辑仓:" +
                            sgRequest.getMainRequest().getCpCStoreEcode() + "失败原因:" + v14.getMessage());
                    AssertUtils.logAndThrow("出库通知单新增逻辑出库单失败!" + "逻辑仓:" +
                            sgRequest.getMainRequest().getCpCStoreEcode() + "失败原因:" + v14.getMessage());
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("saveAndSubmitStoFreezeOutResult.checkAndSetDataByFreeze error:{}"
                    , "checkAndSetDataByFreeze.error"), Throwables.getStackTraceAsString(e));
            StorageBasicUtils.rollbackStorage(redisKeyFtpList, requestList.get(0).getLoginUser());
            AssertUtils.logAndThrow(e.getMessage());
        }

        wmsToStoOutResult.setResultId(StringUtils.join(billIds, ","));
        wmsToStoOutResult.setResultBillNo(StringUtils.join(billNos, ","));
    }

    public ValueHolderV14<String> createFreezeStoOutResult() {
        try {
            log.info(LogUtil.format("WmsStoOutResultService.createFreezeStoOutResult 定时任务开启",
                    "WmsStoOutResultService.createFreezeStoOutResult"));
            SgBWmsToStoOutResultMapper resultMapper = ApplicationContextHandle.getBean(SgBWmsToStoOutResultMapper.class);
            List<Integer> status = Lists.newArrayList(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT, SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED);
            List<SgBWmsToStoOutResult> sgBWmsToStoOutResults = resultMapper.
                    selectDeliveryOrderList(StringUtils.join(status, ","), queryCnt, SgStoreConstantsIF.B2cBillTypeEnum.CCCK.getValue());

            if (CollectionUtils.isNotEmpty(sgBWmsToStoOutResults)) {
                executeFreezeConvert(sgBWmsToStoOutResults);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("异常:{}", "解析创建逻辑出库单异常"), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "成功");
    }

    /**
     * 开启线程类
     */
    class CallableTobeAuditTaskWithResult implements Callable<Boolean> {
        private List<SgBWmsToStoOutResult> sgBWmsToStoOutResults;

        public CallableTobeAuditTaskWithResult(List<SgBWmsToStoOutResult> sgBWmsToStoOutResults) {
            this.sgBWmsToStoOutResults = sgBWmsToStoOutResults;

        }

        @Override
        public Boolean call() throws Exception {
            if (CollectionUtils.isEmpty(sgBWmsToStoOutResults)) {
                return true;
            }

            log.info(LogUtil.format("WmsStoOutResultService.CallableTobeAuditTaskWithResult 多线程处理",
                    "WmsStoOutResultService.CallableTobeAuditTaskWithResult"));
            List<SgBWmsToStoOutResult> errorList = Lists.newArrayList();

            Map<String, List<SgBStoOutResultBillSaveRequest>> batchSaveMap = Maps.newHashMap();
            HashMap<String, SgBWmsToStoOutResult> requestMap = Maps.newHashMap();
            Map<String, List<SgBStoFreezeOutResultBillSaveRequest>> freezeOutResultMap = new HashMap<>(16);

            messageParse(sgBWmsToStoOutResults, errorList, requestMap, batchSaveMap, freezeOutResultMap);
            try {
                /*加锁，防止捞到一样的数据去处理*/
                sgBWmsToStoOutResults = redisConcurrentLockUtils.addLockList(addLock, lockSeconds, TimeUnit.SECONDS,
                        SgConstants.SG_B_WMS_TO_STO_OUT_RESULT, sgBWmsToStoOutResults, SgBWmsToStoOutResult::getId);
                if (addLock && CollectionUtils.isEmpty(sgBWmsToStoOutResults)) {
                    log.info(LogUtil.format("WmsStoOutResultService.CallableTobeAuditTaskWithResult 加锁成功的数据量为空，直接返回",
                            "WmsStoOutResultService.CallableTobeAuditTaskWithResult"));
                    return true;
                }

                ValueHolderV14<List<SgBWmsToStoOutResult>> stoOutResult = new ValueHolderV14<>();
                List<SgBWmsToStoOutResult> outResultArrayList = new ArrayList<>();
                if (MapUtils.isNotEmpty(batchSaveMap)) {
                    ValueHolderV14<List<SgBWmsToStoOutResult>> valueHolderV14 = saveAndSubmitStoOutResult(batchSaveMap);
                    List<SgBWmsToStoOutResult> data = valueHolderV14.getData();
                    if (CollectionUtils.isNotEmpty(data)) {
                        outResultArrayList.addAll(data);
                    }
                }

                if (MapUtils.isNotEmpty(freezeOutResultMap)) {
                    ValueHolderV14<List<SgBWmsToStoOutResult>> valueHolderV14 = saveAndSubmitStoFreezeOutResult(freezeOutResultMap);
                    List<SgBWmsToStoOutResult> data = valueHolderV14.getData();
                    if (CollectionUtils.isNotEmpty(data)) {
                        outResultArrayList.addAll(data);
                    }
                }

                stoOutResult.setData(outResultArrayList);
                batchSaveAuditAfter(stoOutResult, requestMap, errorList);
            } catch (Exception e) {
                throw e;
            } finally {
                /*不解锁，让它自然过期(volatile-lru,每秒清理10次，不用担心内存占用)，防止并发*/
//                redisConcurrentLockUtils.removeLockList(addLock,
//                        SgConstants.SG_B_WMS_TO_STO_OUT_RESULT, sgBWmsToStoOutResults, SgBWmsToStoOutResult::getId);
            }

            return true;
        }
    }

    /**
     * 残次转化
     *
     * @param sgBWmsToStoOutResults
     * @throws Exception
     */
    public void executeFreezeConvert(List<SgBWmsToStoOutResult> sgBWmsToStoOutResults) throws Exception {
        if (CollectionUtils.isEmpty(sgBWmsToStoOutResults)) {
            return;
        }

        List<SgBWmsToStoOutResult> errorList = Lists.newArrayList();

        Map<String, List<SgBStoOutResultBillSaveRequest>> batchSaveMap = Maps.newHashMap();
        HashMap<String, SgBWmsToStoOutResult> requestMap = Maps.newHashMap();
        Map<String, List<SgBStoFreezeOutResultBillSaveRequest>> freezeOutResultMap = new HashMap<>(16);

        messageParse(sgBWmsToStoOutResults, errorList, requestMap, batchSaveMap, freezeOutResultMap);
        try {
            /*加锁，防止捞到一样的数据去处理*/
            sgBWmsToStoOutResults = redisConcurrentLockUtils.addLockList(addLock, lockSeconds, TimeUnit.SECONDS,
                    SgConstants.SG_B_WMS_TO_STO_OUT_RESULT, sgBWmsToStoOutResults, SgBWmsToStoOutResult::getId);
            if (addLock && CollectionUtils.isEmpty(sgBWmsToStoOutResults)) {
                log.info(LogUtil.format("WmsStoOutResultService.executeConvert 加锁成功的数据量为空，直接返回",
                        "WmsStoOutResultService.executeConvert"));
                return;
            }

            ValueHolderV14<List<SgBWmsToStoOutResult>> stoOutResult = new ValueHolderV14<>();
            List<SgBWmsToStoOutResult> outResultArrayList = new ArrayList<>();

            if (MapUtils.isNotEmpty(freezeOutResultMap)) {
                ValueHolderV14<List<SgBWmsToStoOutResult>> valueHolderV14 = saveAndSubmitStoFreezeOutResult(freezeOutResultMap);
                List<SgBWmsToStoOutResult> data = valueHolderV14.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    outResultArrayList.addAll(data);
                }
            }

            stoOutResult.setData(outResultArrayList);
            batchSaveAuditAfter(stoOutResult, requestMap, errorList);
        } catch (Exception e) {
            throw e;
        } finally {
            /*不解锁，让它自然过期(volatile-lru,每秒清理10次，不用担心内存占用)，防止并发*/
//                redisConcurrentLockUtils.removeLockList(addLock,
//                        SgConstants.SG_B_WMS_TO_STO_OUT_RESULT, sgBWmsToStoOutResults, SgBWmsToStoOutResult::getId);
        }

    }
}
