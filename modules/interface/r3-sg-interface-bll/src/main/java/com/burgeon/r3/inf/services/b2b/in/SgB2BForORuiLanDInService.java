package com.burgeon.r3.inf.services.b2b.in;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.inf.model.request.b2b.in.SgCancelSaleOrTransferRequest;
import com.burgeon.r3.sg.inf.model.request.b2b.in.SgSaleOrTransferItemRequest;
import com.burgeon.r3.sg.inf.model.request.b2b.in.SgSaleOrTransferRequest;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferSaveRequest;
import com.burgeon.r3.sg.store.services.transfer.SgBStoTransferCancelSubmitService;
import com.burgeon.r3.sg.store.services.transfer.SgBStoTransferSaveAndSubmitService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Auther: huangpeide
 * @Date: 2021年9月13日18:25:34
 * @Description: B2B 对接业务 FOR 欧瑞蓝鼎
 */
@Slf4j
@Component
public class SgB2BForORuiLanDInService {


    //调拨单新增并审核
    @Autowired
    SgBStoTransferSaveAndSubmitService sgBStoTransferSaveAndSubmitService;

    //取消调拨单新增并审核
    @Autowired
    SgBStoTransferCancelSubmitService sgBStoTransferCancelSubmitService;


    /**
     * 现货销售或调拨接口 对欧睿蓝鼎 B2B
     *
     * @param request
     * @return
     */
    public ValueHolderV14 saleOrTransfer(SgSaleOrTransferRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgB2BForORuiLanDInService.saleOrTransfer.ReceiveParams:request={}", JSONObject.toJSONString(request));
        }
        ValueHolderV14<SgR3BaseResult> vh = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        try {
            SgBStoTransferBillSaveRequest data = new SgBStoTransferBillSaveRequest();
            SgBStoTransferSaveRequest transferSaveRequest = new SgBStoTransferSaveRequest();
            List<SgBStoTransferItemSaveRequest> items = new ArrayList<>();
            data.setTransferSaveRequest(transferSaveRequest);
            data.setItems(items);
            data.setLoginUser(SystemUserResource.getRootUser());
            //设置值
            transferSaveRequest.setId(-1L);
            transferSaveRequest.setLoginUser(SystemUserResource.getRootUser());
            transferSaveRequest.setTransferType(SgConstants.TRANSFER_TYPE_NORMAL);
            transferSaveRequest.setDrpStatus(SgStoreConstants.SEND_DRP_STATUS_NOT_PASS);

            transferSaveRequest.setBillDate(request.getBillDate()==null?new Date():request.getBillDate());
            transferSaveRequest.setDemandType(request.getDemandType());
            transferSaveRequest.setSenderStoreEcode(request.getSenderStoreEcode());
            transferSaveRequest.setReceiverStoreEcode(request.getReceiverStoreEcode());
            //5 销售单  6调拨单
            if ("5".equals(request.getSourceBillType())) {
                //B2B平台
                if (request.getSourceBillNo().startsWith("ST")) {
                    transferSaveRequest.setSourceBillType(SgConstantsIF.BILL_B2B_TYPE_SALE);
                }
                //蓝鼎平台
                if (request.getSourceBillNo().startsWith("REP")) {
                    transferSaveRequest.setSourceBillType(SgConstantsIF.BILL_REP_TYPE_SALE);
                }
                //欧睿平台
                if (request.getSourceBillNo().startsWith("OLBP")) {
                    transferSaveRequest.setSourceBillType(SgConstantsIF.BILL_OLBP_TYPE_SALE);
                }
                //0 调拨流程	  1 销售流程
                transferSaveRequest.setDrpBillType(SgStoreConstants.DRP_BILL_TYPE_SA);
            }
            if ("6".equals(request.getSourceBillType())) {
                //B2B平台
                if (request.getSourceBillNo().startsWith("ST")) {
                    transferSaveRequest.setSourceBillType(SgConstantsIF.BILL_B2B_STO_TRANSFER);
                }
                //蓝鼎平台
                if (request.getSourceBillNo().startsWith("REP")) {
                    transferSaveRequest.setSourceBillType(SgConstantsIF.BILL_REP_STO_TRANSFER);
                }
                //欧睿平台
                if (request.getSourceBillNo().startsWith("OLBP")) {
                    transferSaveRequest.setSourceBillType(SgConstantsIF.BILL_OLBP_STO_TRANSFER);
                }
                //0 调拨流程	  1 销售流程
                transferSaveRequest.setDrpBillType(SgStoreConstants.DRP_BILL_TYPE_TF);
            }
            transferSaveRequest.setSourceBillId(request.getSourceBillId());
            transferSaveRequest.setSourceBillNo(request.getSourceBillNo());
            transferSaveRequest.setSourceBillDate(request.getSourceBillDate());
            //transferSaveRequest.setTranswayType(request.getTranswayType());
            transferSaveRequest.setIsPassTms(request.getIsPassTms());
            transferSaveRequest.setTmsBusinessType(request.getTmsBusinessType());
            transferSaveRequest.setTmsDate(request.getTmsDate());
            transferSaveRequest.setActualListDate(request.getActualListDate());
            transferSaveRequest.setRemark(request.getRemark());
            for (SgSaleOrTransferItemRequest bean : request.getItems()) {
                SgBStoTransferItemSaveRequest tmp = new SgBStoTransferItemSaveRequest();
                BeanUtils.copyProperties(bean, tmp);
                items.add(tmp);
            }
            vh = sgBStoTransferSaveAndSubmitService.saveAndSubmit(data);
        } catch (Exception e) {
            log.error("SgB2BForORuiLanDInService.saleOrTransfer error!{}", Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            String message = e.getMessage();
            if (StringUtils.isNotBlank(message)) {
                message = message.length() > 1000 ? message.substring(0, 1000) : message;
            }
            vh.setMessage("现货销售或调拨失败!失败原因:" + message);
        }
        if (log.isDebugEnabled()) {
            log.debug("Finish SgB2BForORuiLanDInService.saleOrTransfer.ReturnResult:ValueHolder:{}",
                    JSONObject.toJSONString(vh));
        }
        return vh;
    }

    /**
     * 取消现货销售或调拨接口 对欧睿蓝鼎 B2B
     *
     * @param request
     * @return
     */
    public ValueHolderV14 cancelSaleOrTransfer(SgCancelSaleOrTransferRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgB2BForORuiLanDInService.cancelSaleOrTransfer.ReceiveParams:request={}", JSONObject.toJSONString(request));
        }
        ValueHolderV14<SgR3BaseResult> vh = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        List<String> redisBillFtpKeyList = new ArrayList<>();
        try {
            SgR3BaseRequest data = new SgR3BaseRequest();
            data.setBillNo(request.getBillNo());
            data.setLoginUser(SystemUserResource.getRootUser());
            vh = sgBStoTransferCancelSubmitService.cancelSubmitTransfer(data, false,redisBillFtpKeyList);
        } catch (Exception e) {
            log.error("SgB2BForORuiLanDInService.cancelSaleOrTransfer error!{}", Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            String message = e.getMessage();
            if (StringUtils.isNotBlank(message)) {
                message = message.length() > 1000 ? message.substring(0, 1000) : message;
            }
            vh.setMessage("取消现货销售或调拨失败!失败原因:" + message);
        }
        if (log.isDebugEnabled()) {
            log.debug("Finish SgB2BForORuiLanDInService.cancelSaleOrTransfer.ReturnResult:ValueHolder:{}",
                    JSONObject.toJSONString(vh));
        }
        return vh;
    }


}
