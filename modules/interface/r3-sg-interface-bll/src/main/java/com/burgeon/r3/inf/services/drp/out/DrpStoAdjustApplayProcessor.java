package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.adjust.SgBStoAdjust;
import com.burgeon.r3.sg.core.model.table.store.adjust.SgBStoAdjustItem;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 库存调整申请  中台--->DRP
 * <AUTHOR>
 * @Date 2021/7/26 13:35
 * @Version 1.0
 **/
@Slf4j
@Component
public class DrpStoAdjustApplayProcessor extends AbstractDrpInterfaceProcessor<SgBStoAdjust, SgBStoAdjustItem> {

    @Override
    public LambdaQueryWrapper<SgBStoAdjust> execMainWrapper() {

        //单据状态”=已审核、且“传DRP状态”=“未传”或“传失败”的ERP的【库存调整单】传入接口
        LambdaQueryWrapper<SgBStoAdjust> wrapper = new LambdaQueryWrapper<>();
        //单据状态”=已审核
        wrapper.eq(SgBStoAdjust::getStatus, SgConstants.CON_BILL_STATUS_02);
        //来源类型是：领用不归还
        //wrapper.eq(SgBStoAdjust::getSourceBillType, SgConstantsIF.BILL_TYPE_LY_NO_RETURN);
        wrapper.eq(SgBStoAdjust::getIsactive, SgConstants.IS_ACTIVE_Y);
        wrapper.and(o -> {
//            o.isNull(SgBStoAdjust::getDrpStatus);
//            o.or(oo -> oo.eq(SgBStoAdjust::getDrpStatus, ResultCode.FAIL).lt(SgBStoAdjust::getDrpFailCount, failNum));
            return o;
        });
        return wrapper;
    }

    @Override
    public LambdaQueryWrapper<SgBStoAdjustItem> execitemWrapper(Long mianId) {
        return null;
    }

    @Override
    public String drpInterfaceUrl() {
        return "erp.stockadjustment.notice";
    }

    @Override
    public String itemMainField() {
        return "SG_B_STO_ADJUST_ID";
    }

    @Override
    public String drpStatus() {
        return "DRP_STATUS";
    }

    @Override
    public String drpStatusFailCount() {
        return "DRP_FAIL_COUNT";
    }

    @Override
    public String drpStatusFailReason() {
        return "DRP_FAIL_REASON";
    }

    @Override
    public JSONObject execInterfaceParam(SgBStoAdjust sgBStoAdjust, List<SgBStoAdjustItem> sgBStoAdjustItemList) {
        if (log.isDebugEnabled()) {
            log.debug("DrpStoAdjustApplayProcessor.execInterfaceParam:sgBStoAdjust={},items={}", JSONObject.toJSONString(sgBStoAdjust), JSONObject.toJSONString(sgBStoAdjustItemList));
        }
        JSONObject main = new JSONObject();
        //单据编号
        main.put("ZTDOCNO", sgBStoAdjust.getBillNo());
        //单据日期
        main.put("BILLDATE", DateUtil.format(sgBStoAdjust.getBillDate(), "yyyyMMdd"));
        //逻辑仓
        main.put("C_STORE_ID_CODE", sgBStoAdjust.getCpCStoreEcode());
        //逻辑仓
        main.put("DESCRIPTION", StringUtils.isEmpty(sgBStoAdjust.getRemark())
                ? "由中台库存调整单生成" : sgBStoAdjust.getRemark());
        //是否按箱
        main.put("IS_BAS", "N");
        //物理调整性质
        main.put("C_OTHER_INOUTTYPE_ID_NAME", "中台调整");

        List<JSONObject> items = new ArrayList<>();
        for (SgBStoAdjustItem item : sgBStoAdjustItemList) {
            JSONObject itemJson = new JSONObject();
            //条码
            itemJson.put("M_PRODUCTALIAS_ID_NO", item.getPsCSkuEcode());
            itemJson.put("M_PRODUCTALIAS_ID_NAME", item.getPsCSkuEcode());
            //数量
            itemJson.put("QTY", item.getQty());
            items.add(itemJson);
        }
        main.put("items", items);
        log.info("DrpStoAdjustApplayProcessor.execInterfaceParam result={}", JSONObject.toJSONString(main));
        return main;
    }

    @Override
    public void handleBysuccess(SgBStoAdjust sgBStoAdjust, List<SgBStoAdjustItem> z) {

    }
}
