package com.burgeon.r3.inf.services.wms.adjust;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.inf.utils.WmsTaskUtils;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.enums.YesNoEnum;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.store.in.SgBWmsToStoAdjustResult;
import com.burgeon.r3.sg.core.utils.DateUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.in.SgBWmsToStoAdjustResultMapper;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustMainSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustSaveRequest;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSaveService;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-07-26 10:21
 * @Description: 库存盘点单结果WMS回传定时任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SgBWmsToStoAdjustResultService {
    private final SgBWmsToStoAdjustResultMapper adjustResultMapper;
    private final SgBStoAdjustSaveService adjustSaveService;
    private static final List<String> warehouseTypeList = Lists.newArrayList(ThirdWmsTypeEnum.FLWMS.getCode(), ThirdWmsTypeEnum.QMWMS.getCode());

    public ValueHolderV14 execute() {
        log.info(LogUtil.format("库存盘点单结果WMS回传定时转化任务-开始", "SgBWmsToStoAdjustResultService.start"));

        List<SgBWmsToStoAdjustResult> toStoAdjustResults = adjustResultMapper.selectList(new LambdaQueryWrapper<SgBWmsToStoAdjustResult>()
                .lt(SgBWmsToStoAdjustResult::getFailedCount, SgConstantsIF.FAIL_COUNT)
                .ne(SgBWmsToStoAdjustResult::getTransformStatus, SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS)
                .eq(SgBWmsToStoAdjustResult::getIsactive, YesNoEnum.Y.getKey())
                .in(SgBWmsToStoAdjustResult::getWmsWarehouseType, warehouseTypeList)
        );
        if (CollectionUtils.isEmpty(toStoAdjustResults)) {
            return new ValueHolderV14<>(ResultCode.SUCCESS, "无处理数据！");
        }

        /*根据仓库类型分组*/
        Map<String, List<SgBWmsToStoAdjustResult>> warehouseTypeGroup = toStoAdjustResults.stream()
                .collect(Collectors.groupingBy(SgBWmsToStoAdjustResult::getWmsWarehouseType));
        /*目前只有富勒和巨沃处理了盘点*/
        for (String warehouseType : warehouseTypeGroup.keySet()) {
            List<SgBWmsToStoAdjustResult> resultList = warehouseTypeGroup.get(warehouseType);
            if (ThirdWmsTypeEnum.FLWMS.getCode().equals(warehouseType)) {
                /*富乐WMS特殊处理(warehouseCode与ecode：一对多，ecode才是唯一的)*/
                businessLogicForFlux(resultList);
                continue;
            }
            if (ThirdWmsTypeEnum.QMWMS.getCode().equals(warehouseType)) {
                //根据warehouseCode 查实体仓档案
                List<String> wareHouseCodeList = resultList.stream().map(SgBWmsToStoAdjustResult::getWarehouseCode).collect(Collectors.toList());
                Map<String, SgCpCPhyWarehouse> warehouseCodeMap = WmsTaskUtils.getSgCpPhyWarehouseByWarehouseCode(wareHouseCodeList);
                businessLogic(resultList, warehouseCodeMap);
                continue;
            }

            throw new NDSException("库存盘点转化仓库类型非法");
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, "库存盘点单结果WMS回传定时任务转化成功！");
    }


    /**
     * 盘点具体业务逻辑-富乐
     *
     * @param resultList 盘点回传列表
     */
    private void businessLogicForFlux(List<SgBWmsToStoAdjustResult> resultList) {
        log.info(LogUtil.format("库存盘点单结果WMS回传定时任务转化执行，toStoAdjustResults.size:{}",
                "SgBWmsToStoEntryInResultService.businessLogicForFlux"), resultList.size());

        for (SgBWmsToStoAdjustResult adjustResult : resultList) {
            try {
                String adjustBillNo = adjustResult.getAdjustBillNo();

                /*解析报文*/
                String message = adjustResult.getMessage();
                log.info(LogUtil.format("库存盘点单结果WMS回传定时任务转化,解析报文明细:{}",
                        "SgBWmsToStoEntryInResultService.businessLogicForFlux"), message);

                if (StringUtils.isEmpty(message)) {
                    updateFail(adjustResult, "中间表[" + adjustBillNo + "]在报文不存在！");
                    continue;
                }
                SgCpCPhyWarehouse sgCpPhyWarehouse = WmsTaskUtils.queryByEcode(adjustResult.getWarehouseCode());
                if (Objects.isNull(sgCpPhyWarehouse)) {
                    updateFail(adjustResult, "中间表[" + adjustBillNo + "]在【实体仓档案】中不存在！");
                    continue;
                }

                List<String> itemCodeList = WmsTaskUtils.messageParseByItemsReturnItemCode(message);
                log.info(LogUtil.format("库存盘点单结果WMS回传定时任务转化,报文明细解析结果:{}",
                        "SgBWmsToStoEntryInResultService.businessLogicForFlux"), JSONObject.toJSONString(itemCodeList));
                if (CollectionUtils.isEmpty(itemCodeList)) {
                    updateFail(adjustResult, "报文无明细！");
                    continue;
                }

                //比对条码信息
                JSONArray array = WmsTaskUtils.compareToSku(itemCodeList);
                if (array.size() > 0) {
                    updateFail(adjustResult, "商品编码" + JSONArray.toJSONString(array) + "在【商品SKU】中不存在！");
                    continue;
                }

                //d.新增并审核 库存调整单--- 核心过程
                SgBWmsToStoAdjustResultService bean = ApplicationContextHandle.getBean(SgBWmsToStoAdjustResultService.class);
                ValueHolderV14 v14 = bean.adjustSaveAndSubmit(adjustResult, sgCpPhyWarehouse);
                if (!v14.isOK()) {
                    updateFail(adjustResult, v14.getMessage());
                    continue;
                }
                //更新成功信息
                updateSuccess(adjustResult);
            } catch (Exception e) {
                log.error(LogUtil.format("库存盘点单结果WMS回传定时任务转化执行报错，转化对象:{}，异常信息：{}",
                        "SgBWmsToStoEntryInResultService.businessLogicForFlux", JSON.toJSONString(adjustResult), Throwables.getStackTraceAsString(e)));
                updateFail(adjustResult, e.getMessage());
            }
        }
    }

    /**
     * 具体业务逻辑-巨沃
     *
     * @param toStoAdjustResults toStoAdjustResults
     * @param warehouseCodeMap   warehouseCodeMap
     */
    private void businessLogic(List<SgBWmsToStoAdjustResult> toStoAdjustResults, Map<String, SgCpCPhyWarehouse> warehouseCodeMap) {
        log.info(LogUtil.format("库存盘点单结果WMS回传定时任务转化执行，toStoAdjustResults.size:{}",
                "SgBWmsToStoEntryInResultService.businessLogic"), toStoAdjustResults.size());

        for (SgBWmsToStoAdjustResult adjustResult : toStoAdjustResults) {
            try {
                String warehouseCode = adjustResult.getWarehouseCode();
                String adjustBillNo = adjustResult.getAdjustBillNo();
                //a.是否存在实体仓档案中
                if (MapUtils.isEmpty(warehouseCodeMap) || !warehouseCodeMap.containsKey(warehouseCode)) {
                    updateFail(adjustResult, "仓库编码[" + warehouseCode + "]在【实体仓档案】中不存在");
                    continue;
                }
                //b.解析报文
                String message = adjustResult.getMessage();
                if (StringUtils.isEmpty(message)) {
                    updateFail(adjustResult, "中间表[" + adjustBillNo + "]在报文不存在！");
                    continue;
                }
                log.info(LogUtil.format("库存盘点单结果WMS回传定时任务转化,解析报文明细:{}",
                        "SgBWmsToStoEntryInResultService.businessLogic"), message);
                List<String> itemCodeList = WmsTaskUtils.messageParseByItemsReturnItemCode(message);
                log.info(LogUtil.format("库存盘点单结果WMS回传定时任务转化,报文明细解析结果:{}",
                        "SgBWmsToStoEntryInResultService.businessLogic"), JSONObject.toJSONString(itemCodeList));
                if (CollectionUtils.isEmpty(itemCodeList)) {
                    updateFail(adjustResult, "报文无明细！");
                    continue;
                }

                //c,比对条码信息
                JSONArray array = WmsTaskUtils.compareToSku(itemCodeList);
                if (array.size() > 0) {
                    updateFail(adjustResult, "商品编码" + JSONArray.toJSONString(array) + "在【商品SKU】中不存在！");
                    continue;
                }
                SgCpCPhyWarehouse sgCpPhyWarehouse = warehouseCodeMap.get(warehouseCode);

                //d.新增并审核 库存调整单
                SgBWmsToStoAdjustResultService bean = ApplicationContextHandle.getBean(SgBWmsToStoAdjustResultService.class);
                ValueHolderV14 v14 = bean.adjustSaveAndSubmit(adjustResult, sgCpPhyWarehouse);
                if (!v14.isOK()) {
                    updateFail(adjustResult, v14.getMessage());
                    continue;
                }
                //更新成功信息
                updateSuccess(adjustResult);
            } catch (Exception e) {
                log.error(LogUtil.format("库存盘点单结果WMS回传定时任务转化执行报错，转化对象:{}，异常信息：{}",
                        "SgBWmsToStoEntryInResultService.businessLogic", JSON.toJSONString(adjustResult), Throwables.getStackTraceAsString(e)));
                updateFail(adjustResult, e.getMessage());
            }
        }
    }

    /**
     * 新增并审核 库存调整单
     *
     * @param adjustResult      adjustResult
     * @param sgCpCPhyWarehouse sgCpCPhyWarehouse
     * @return ValueHolderV14
     */
    public ValueHolderV14 adjustSaveAndSubmit(SgBWmsToStoAdjustResult adjustResult, SgCpCPhyWarehouse sgCpCPhyWarehouse) {
        log.info(LogUtil.format("库存盘点转化-新增并审核-库存调整单，中间表:{},仓信息:{}",
                        "SgBWmsToStoAdjustResultService.adjustSaveAndSubmit"),
                JSONObject.toJSONString(adjustResult), JSONObject.toJSONString(sgCpCPhyWarehouse));

        /*通过实体仓ID，查询逻辑仓信息*/
        Map<Long, List<CpCStore>> storeInfoByPhyId = CommonCacheValUtils.getStoreInfoByPhyId(sgCpCPhyWarehouse.getId());
        if (MapUtils.isEmpty(storeInfoByPhyId) || !storeInfoByPhyId.containsKey(sgCpCPhyWarehouse.getId())) {
            return new ValueHolderV14(ResultCode.FAIL, "实体仓查逻辑仓查询无数据！");
        }

        //一头牛 实体仓对逻辑仓 1:1
        List<CpCStore> cpStores = storeInfoByPhyId.get(sgCpCPhyWarehouse.getId());
        if (CollectionUtils.isEmpty(cpStores)) {
            return new ValueHolderV14(ResultCode.FAIL, "实体仓下无逻辑仓！");
        }

        SgBStoAdjustSaveRequest adjustSaveRequest = buildAdjust(adjustResult, cpStores.get(0));
        log.info(LogUtil.format("库存盘点转化-新增并审核-库存调整单，保存与提交参数：{}",
                "SgBWmsToStoAdjustResultService.adjustSaveAndSubmit"), JSONObject.toJSONString(adjustSaveRequest));
        ValueHolderV14<SgR3BaseResult> v14 = adjustSaveService.saveAndSubmit(adjustSaveRequest);
        log.info(LogUtil.format("库存盘点转化-新增并审核-库存调整单，保存与提交结果：{}",
                "SgBWmsToStoAdjustResultService.ValueHolderV14"), JSONObject.toJSONString(v14));

        return v14;
    }

    /**
     * 设置参数
     *
     * @param adjustResult adjustResult
     * @param store        store
     * @return SgBStoAdjustSaveRequest
     */
    private SgBStoAdjustSaveRequest buildAdjust(SgBWmsToStoAdjustResult adjustResult, CpCStore store) {
        SgBStoAdjustSaveRequest adjustSaveRequest = new SgBStoAdjustSaveRequest();

        String message = adjustResult.getMessage();
        JSONObject request = JSONObject.parseObject(message);

        SgBStoAdjustMainSaveRequest adjustMainSaveRequest = new SgBStoAdjustMainSaveRequest();
        adjustMainSaveRequest.setBillDate(new Date());
        //库存日期
        adjustMainSaveRequest.setStockDate(request.getDate("checkTime"));
        //调整店仓
        adjustMainSaveRequest.setCpCStoreId(store.getId());
        adjustMainSaveRequest.setCpCStoreEcode(store.getEcode());
        adjustMainSaveRequest.setCpCStoreEname(store.getEname());
        //单据类型
        adjustMainSaveRequest.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL);
        //调整性质
        adjustMainSaveRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_ADJUST_PROP_INVENTORY);
        //来源单据类型
        adjustMainSaveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_PHY_PROFIT);
        adjustMainSaveRequest.setSourceBillNo(request.getString("checkOrderCode"));
        //备注
        adjustMainSaveRequest.setRemark(request.getString("remark"));
        //是否传WMS
        adjustMainSaveRequest.setIsPassWms(NumberUtils.INTEGER_ZERO);
        //WMS单据编号
        adjustMainSaveRequest.setWmsBillNo(request.getString("checkOrderCode"));

        List<SgBStoAdjustItemSaveRequest> itemSaveRequests = new ArrayList<>();

//        JSONObject itemObject = request.getJSONObject("items");
        JSONArray items = request.getJSONArray("items");
        items.forEach(x -> {
            JSONObject item = (JSONObject) x;
            SgBStoAdjustItemSaveRequest itemSaveRequest = new SgBStoAdjustItemSaveRequest();
            String batchCode = item.getString("batchCode");
            itemSaveRequest.setProduceDate(StringUtils.isEmpty(batchCode) || batchCode.equals(SgConstantsIF.DEFAULT_PRODUCE_DATE) ?
                    SgConstantsIF.DEFAULT_PRODUCE_DATE :
                    DateUtils.formatSync8(DateUtils.parseSync8(item.getString("batchCode"), DateUtils.PATTERN_DATE), DateUtils.DATE_PATTERN));
            itemSaveRequest.setPsCSkuEcode(item.getString("itemCode"));
            itemSaveRequest.setQty(item.getBigDecimal("quantity"));
            itemSaveRequest.setStorageType(item.getString("inventoryType"));
            itemSaveRequests.add(itemSaveRequest);
        });

        adjustSaveRequest.setItems(itemSaveRequests);
        adjustSaveRequest.setMainRequest(adjustMainSaveRequest);
        adjustSaveRequest.setObjId(-1L);
        adjustSaveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());

        return adjustSaveRequest;
    }

    /**
     * 更新错误信息
     *
     * @param sgWmsToOrderOutStockResults 中间表数据
     * @param failedReason                失败原因
     */
    private void updateFail(SgBWmsToStoAdjustResult sgWmsToOrderOutStockResults, String failedReason) {
        SgBWmsToStoAdjustResult update = new SgBWmsToStoAdjustResult();
        update.setId(sgWmsToOrderOutStockResults.getId());
        int failedConut = Optional.ofNullable(sgWmsToOrderOutStockResults.getFailedCount()).orElse(0) + 1;
        update.setFailedCount(failedConut);

        update.setFailedReason(failedReason.length() > 500 ? failedReason.substring(0, 500) : failedReason);
        update.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED);
        adjustResultMapper.updateById(update);
    }

    /**
     * 更新成功信息
     *
     * @param inTask 中间表数据
     */
    private void updateSuccess(SgBWmsToStoAdjustResult inTask) {
        SgBWmsToStoAdjustResult update = new SgBWmsToStoAdjustResult();
        update.setId(inTask.getId());
        update.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS);
        update.setTransformationData(new Date());
        adjustResultMapper.update(update, new LambdaUpdateWrapper<SgBWmsToStoAdjustResult>()
                .set(SgBWmsToStoAdjustResult::getFailedReason, null)
                .eq(SgBWmsToStoAdjustResult::getId, inTask.getId()));
    }

}
