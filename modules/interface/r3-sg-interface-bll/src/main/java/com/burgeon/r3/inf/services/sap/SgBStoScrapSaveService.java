package com.burgeon.r3.inf.services.sap;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.request.SgBFreezeStorageQueryRequest;
import com.burgeon.r3.sg.basic.services.SgBFreezeStorageQueryService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgBFreezeStorage;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustMainSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoScrapSaveRequest;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSaveService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lin yu
 * @date : 2022/6/21 下午1:08
 * @describe : 新增报废单服务
 */

@Slf4j
@Component
public class SgBStoScrapSaveService {

    @Autowired
    private SgBStoAdjustSaveService saveService;

    @Autowired
    private SgBFreezeStorageQueryService sgBFreezeStorageQueryService;

    public ValueHolderV14<SgR3BaseResult> save(SgBStoScrapSaveRequest scrapSaveRequest) {

        ValueHolderV14<SgR3BaseResult> v14 =
                new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);


        try {

            SgBStoAdjustSaveRequest request = scrapSaveRequest.getAdjustSaveRequest();

            if (log.isDebugEnabled()) {
                log.debug("Start SgBStoScrapSaveService.save request:{}",
                        JSONObject.toJSONString(scrapSaveRequest));
            }

            checkParams(request);

            queryFreezeQty(request);

            encapsulationData(request);

            v14 = saveService.save(request);

        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("库存调整单创建异常:" + e.getMessage());
            return v14;
        }

        return v14;
    }

    private void queryFreezeQty(SgBStoAdjustSaveRequest request) {
        SgBStoAdjustMainSaveRequest mainRequest = request.getMainRequest();
        List<SgBStoAdjustItemSaveRequest> items = request.getItems();

        List<Long> skuIdList = new ArrayList<>();
        List<String> produceDateList = new ArrayList<>();
        List<String> storeTypeList = new ArrayList<>();

        for (SgBStoAdjustItemSaveRequest item : items) {
            skuIdList.add(item.getPsCSkuId());
            produceDateList.add(item.getProduceDate());
            storeTypeList.add(item.getStorageType());
        }

        //查询冻结量
        SgBFreezeStorageQueryRequest freezeStorageQueryRequest = new SgBFreezeStorageQueryRequest();
        freezeStorageQueryRequest.setStoreId(mainRequest.getCpCStoreId());
        freezeStorageQueryRequest.setProduceDateList(produceDateList);
        freezeStorageQueryRequest.setSkuIdList(skuIdList);
        freezeStorageQueryRequest.setStoreTypeList(storeTypeList);

        ValueHolderV14<List<SgBFreezeStorage>> listValueHolderV14 = sgBFreezeStorageQueryService.queryFreezeStorage(freezeStorageQueryRequest);

        AssertUtils.isTrue(listValueHolderV14.isOK(), listValueHolderV14.getMessage());

        List<SgBFreezeStorage> data = listValueHolderV14.getData();
        Map<String, List<SgBFreezeStorage>> collect = data.stream().collect(Collectors.groupingBy(o -> o.getPsCSkuId() + "_" + o.getProduceDate() + "_" + o.getStockType()));

        //报废数量大于对应逻辑仓的[%调整性质%]冻结库存！条码XXXX，生产日期F，数量T，冻结库存Y
        String errorMessage = "";
        List<String> errorMessageList = new ArrayList<>();

        for (SgBStoAdjustItemSaveRequest item : items) {
            List<SgBFreezeStorage> freezeStorageList = collect.get(item.getPsCSkuId() + "_" + item.getProduceDate() + "_" + item.getStorageType());

            if (CollectionUtils.isEmpty(freezeStorageList)) {
                errorMessage = "报废数量大于对应逻辑仓的[" + 1 + "]冻结库存！条码" + item.getPsCSkuEcode()
                        + "，生产日期" + item.getProduceDate()
                        + "，数量" + item.getQty()
                        + "，无冻结库存记录";
                errorMessageList.add(errorMessage);
            } else if (freezeStorageList.get(0).getQtyFreeze().compareTo(item.getQty().abs()) < 0) {
                errorMessage = "报废数量大于对应逻辑仓的[" + 1 + "]冻结库存！条码" + item.getPsCSkuEcode()
                        + "，生产日期" + item.getProduceDate()
                        + "，数量" + item.getQty()
                        + "，冻结库存" + freezeStorageList.get(0).getQtyFreeze();
                errorMessageList.add(errorMessage);
            }
        }

        if (CollectionUtils.isNotEmpty(errorMessageList)) {
            AssertUtils.logAndThrow(errorMessageList.toString());
        }

    }

    private void encapsulationData(SgBStoAdjustSaveRequest request) {
        SgBStoAdjustMainSaveRequest mainRequest = request.getMainRequest();

        mainRequest.setObjId(-1L);
        //单据日期
        if (mainRequest.getBillDate() == null) {
            mainRequest.setBillDate(mainRequest.getStockDate());
        }
        //单据类型
        mainRequest.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL);
        //来源单据类型：报废单
        mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_SCRAP);
        //调整性质：冲销调整
        mainRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_ADJUST_PROP_WRITE_OFF);
        //是否传wms：是
        mainRequest.setIsPassWms(NumberUtils.INTEGER_ONE);
        //传wms状态：未传
        mainRequest.setWmsStatus(Integer.valueOf(String.valueOf(SgStoreConstants.WMS_UPLOAD_STATUTS_NO)));
    }

    private void checkParams(SgBStoAdjustSaveRequest request) {

        AssertUtils.notNull(request.getLoginUser(), "请登录！");

        SgBStoAdjustMainSaveRequest mainRequest = request.getMainRequest();
        List<SgBStoAdjustItemSaveRequest> items = request.getItems();

        AssertUtils.notNull(mainRequest, "主表信息不能为空！");
        AssertUtils.notNull(mainRequest.getCpCStoreEcode(), "逻辑仓不能为空！");
        AssertUtils.cannot(CollectionUtils.isEmpty(items), "明细信息不能为空！");

        CpCStore store = CommonCacheValUtils.getStoreInfoByEcode(mainRequest.getCpCStoreEcode());
        AssertUtils.notNull(store, "当前单据的逻辑仓信息不存在！");
        mainRequest.setCpCStoreId(store.getId());
        mainRequest.setCpCStoreEname(store.getEname());

        for (SgBStoAdjustItemSaveRequest item : items) {

            AssertUtils.cannot(StringUtils.isEmpty(item.getPsCSkuEcode()), "条码信息不能为空！");
            AssertUtils.cannot(StringUtils.isEmpty(item.getProduceDate()), "生产日期信息不能为空！");

            BigDecimal qty = item.getQty() == null ? BigDecimal.ZERO : item.getQty();

            AssertUtils.cannot(qty.compareTo(BigDecimal.ZERO) >= 0, "报废业务，报废数量必须小于0！");

        }

        CommonCacheValUtils.setSkuInfoBySkuCodeList(items);

    }
}
