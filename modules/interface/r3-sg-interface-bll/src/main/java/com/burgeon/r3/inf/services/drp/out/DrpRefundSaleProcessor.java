package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransfer;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransferItem;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.transfer.SgBStoTransferMapper;
import com.burgeon.r3.sg.store.services.transfer.SgTransferBillStatusEnum;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 销售退货入库通知接口
 * @version 1.0
 * @date 2021/9/12 9:59
 */

@Slf4j
@Component
public class DrpRefundSaleProcessor extends AbstractDrpInterfaceProcessor<SgBStoTransfer, SgBStoTransferItem> {

    @Autowired
    SgBStoTransferMapper sgStoTransferMapper;


    @Override
    public LambdaQueryWrapper execMainWrapper() {
        LambdaQueryWrapper<SgBStoTransfer> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SgBStoTransfer::getStatus, SgTransferBillStatusEnum.AUDITED_ALL_OUT_ALL_IN.getVal());
        wrapper.ne(SgBStoTransfer::getStatus, SgTransferBillStatusEnum.VOIDED.getVal());
        wrapper.eq(SgBStoTransfer::getIsactive, SgConstants.IS_ACTIVE_Y);
        wrapper.eq(SgBStoTransfer::getDrpBillType, SgStoreConstants.DRP_BILL_TYPE_SR);
        wrapper.and(o -> {
            o.isNull(SgBStoTransfer::getDrpInStatus);
            o.or(oo -> oo.eq(SgBStoTransfer::getDrpInStatus, SgStoreConstants.SEND_DRP_STATUS_UNDECLARED));
            o.or(oo -> oo.eq(SgBStoTransfer::getDrpInStatus, SgStoreConstants.SEND_DRP_STATUS_FAIL).lt(SgBStoTransfer::getDrpFailCount, failNum));
            return o;
        });
        return wrapper;
    }

    @Override
    public LambdaQueryWrapper<SgBStoTransferItem> execitemWrapper(Long mainId) {
        return null;
    }

    @Override
    public String drpInterfaceUrl() {
        return "erp.saleret.innotice";
    }

    @Override
    public String itemMainField() {
        return "sg_b_sto_transfer_id";
    }

    @Override
    public String drpStatus() {
        return "DRP_IN_STATUS";
    }

    @Override
    public String drpStatusFailCount() {
        return "DRP_IN_FAIL_COUNT";
    }

    @Override
    public String drpStatusFailReason() {
        return "DRP_IN_FAIL_REASON";
    }

    @Override
    public JSONObject execInterfaceParam(SgBStoTransfer sgStoTransfer, List<SgBStoTransferItem> itemList) {
        JSONObject main = new JSONObject();
        //中台编号
        main.put("ZTDOCNO", sgStoTransfer.getBillNo());
        //erp单据编号，需求确认=单据编号
        //        main.put("DOCNO", sgStoTransfer.getBillNo());
        //入库日期
        main.put("DATEIN", DateUtil.format(sgStoTransfer.getInDate() == null ?
                new Date() : sgStoTransfer.getInDate(), "yyyyMMdd"));


        JSONArray items = new JSONArray();
        for (SgBStoTransferItem item : itemList) {
            JSONObject itemJson = new JSONObject();
            itemJson.put("M_PRODUCTALIAS_NO", item.getPsCSkuEcode());
            //款号，需求确认=商品编码
            itemJson.put("M_PRODUCT_NAME", item.getPsCProEcode());
            //入库数量
            itemJson.put("QTYIN", item.getQtyIn());
            items.add(itemJson);
        }
        main.put("items", items);
        return main;
    }

    @Override
    public void handleBysuccess(SgBStoTransfer sgStoTransfer, List<SgBStoTransferItem> z) {

    }


}
