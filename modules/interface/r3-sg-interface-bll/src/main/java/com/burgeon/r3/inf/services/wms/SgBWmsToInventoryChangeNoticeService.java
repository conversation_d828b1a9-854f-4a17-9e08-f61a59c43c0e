package com.burgeon.r3.inf.services.wms;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.inf.utils.WmsTaskUtils;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.YesNoEnum;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.store.in.SgBWmsToInventoryChangeNoticeTask;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.DateUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.in.SgBWmsToInventoryChangeNoticeTaskMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesMapper;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustMainSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustSaveRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeSaveItemRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeSaveRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoUnfreezeItemRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoUnfreezeRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoUnfreezeSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutResultBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutResultItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutResultSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferSaveRequest;
import com.burgeon.r3.sg.store.model.result.freeze.SgBStoUnfreezeSaveBillResult;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSaveService;
import com.burgeon.r3.sg.store.services.freeze.SgBStoFreezeSaveAndSubmitService;
import com.burgeon.r3.sg.store.services.freeze.SgBStoUnfreezeSaveAndSubmitService;
import com.burgeon.r3.sg.store.services.out.SgBStoOutResultSaveAndSubmitService;
import com.burgeon.r3.sg.store.services.transfer.SgBStoTransferSaveAndSubmitService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-08-02 10:10
 * @Description: 库存异动中间表定时任务
 */

@Slf4j
@Component
public class SgBWmsToInventoryChangeNoticeService {

    @Autowired
    private SgBWmsToInventoryChangeNoticeTaskMapper taskMapper;
    @Autowired
    private SgBStoTransferSaveAndSubmitService transferSaveAndSubmitService;
    @Autowired
    private SgBStoUnfreezeSaveAndSubmitService unfreezeSaveAndSubmitService;
    @Autowired
    private SgBStoFreezeSaveAndSubmitService freezeSaveAndSubmitService;
    @Autowired
    private SgBStoAdjustSaveService sgBStoAdjustSaveService;
    @Autowired
    private SgBStoOutNoticesMapper noticesMapper;
    @Autowired
    private SgWmsAutoReleaseOrderService sgWmsAutoReleaseOrderService;


    private final static String TYPE = "TYPE";

    /**
     * 库存异动中间表定时任务 入口
     * KCYK
     * KCYK
     * XBCK
     * XQGL
     * OKZC/OKZR
     * <p>
     * ps:坑点：
     * SgBWmsToInventoryChangeNoticeTask里字段 psCProEcode =psCSkuEcode
     *
     * @param params 入参：{
     *               "TYPE": ["KCYK", "KCYK"]
     *               }
     * @return ValueHolderV14
     */
    public ValueHolderV14 execute(JSONObject params) {
        log.info(LogUtil.format("库存异动转化定时任务，params:{}",
                "SgBWmsToInventoryChangeNoticeService.execute"), JSONObject.toJSONString(params));

        //查数据
        LambdaQueryWrapper<SgBWmsToInventoryChangeNoticeTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(SgBWmsToInventoryChangeNoticeTask::getFailedCount, SgConstantsIF.FAIL_COUNT);
        queryWrapper.ne(SgBWmsToInventoryChangeNoticeTask::getTransformStatus, SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS);
        queryWrapper.eq(SgBWmsToInventoryChangeNoticeTask::getIsactive, YesNoEnum.Y.getKey());

        List<String> wmsTypeList = new ArrayList<>();
        wmsTypeList.add(ThirdWmsTypeEnum.QMWMS.getCode());
        wmsTypeList.add(ThirdWmsTypeEnum.JDWMS.getCode());
        wmsTypeList.add(ThirdWmsTypeEnum.FLWMS.getCode());
        queryWrapper.in(SgBWmsToInventoryChangeNoticeTask::getWmsWarehouseType, wmsTypeList);

        if (params != null && params.getJSONArray(TYPE) != null && params.getJSONArray(TYPE).size() > 0) {
            JSONArray typeArray = params.getJSONArray(TYPE);
            queryWrapper.in(SgBWmsToInventoryChangeNoticeTask::getBillType, typeArray.toJavaList(String.class));
        }

        List<SgBWmsToInventoryChangeNoticeTask> taskList = taskMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(taskList)) {
            return new ValueHolderV14<>(ResultCode.SUCCESS, "无处理数据！");
        }

        businessLogic(taskList);

        return new ValueHolderV14<>(ResultCode.SUCCESS, "库存异动转化定时任务成功！");
    }

    /**
     * 库存异动业务逻辑
     *
     * @param taskList taskList
     */
    private void businessLogic(List<SgBWmsToInventoryChangeNoticeTask> taskList) {
        Map<String, List<SgBWmsToInventoryChangeNoticeTask>> map = taskList.stream()
                .collect(Collectors.groupingBy(SgBWmsToInventoryChangeNoticeTask::getBillType));

        for (String billType : map.keySet()) {
            switch (billType) {
                case SgConstantsIF.BILL_TYPE_KCYK:
                case SgConstantsIF.BILL_TYPE_BWQZC:
                    /*跨仓移库*/
                    kckyBusinessLogic(map.get(billType));
                    break;
                case SgConstantsIF.BILL_TYPE_ZTTZ:
                    /*残次转换*/
                    zttzBusinessLogic(map.get(billType));
                    break;
                case SgConstantsIF.BILL_TYPE_XBCK: // 线边仓出库
                case SgConstantsIF.BILL_TYPE_XQGL: // 效期管理
                case SgConstantsIF.BILL_TYPE_OKZC: // O库存调整-
                case SgConstantsIF.BILL_TYPE_OKZR: // O库存调整+
                    adjustBusinessLogic(map.get(billType), billType);
                    break;
                default:
                    List<SgBWmsToInventoryChangeNoticeTask> tasks = map.get(billType);
                    if (CollectionUtils.isNotEmpty(tasks)) {
                        tasks.forEach(x -> {
                            updateFail(x, "单据类型不匹配！");
                        });
                    }
            }
        }

    }

    /**
     * 单据类型：
     * XBCK
     * XQGL
     * OKZC/OKZR
     * 处理逻辑
     *
     * @param taskList 入参
     * @param type     单据类型
     */
    private void adjustBusinessLogic(List<SgBWmsToInventoryChangeNoticeTask> taskList, String type) {
        log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService adjustBusinessLogic:{},type:{}",
                "SgBWmsToInventoryChangeNoticeService.adjustBusinessLogic", JSONObject.toJSONString(taskList), type));
        if (CollectionUtils.isNotEmpty(taskList)) {
            List<String> warehouseCodeList = taskList.stream().map(SgBWmsToInventoryChangeNoticeTask::getWarehouseCode).collect(Collectors.toList());
            Map<String, SgCpCPhyWarehouse> warehouseCodeMap = WmsTaskUtils.getSgCpPhyWarehouseByCode(warehouseCodeList);
            Map<String, List<SgBWmsToInventoryChangeNoticeTask>> listMap =
                    taskList.stream().collect(Collectors.groupingBy(SgBWmsToInventoryChangeNoticeTask::getWmsWarehouseType));
            if (MapUtils.isNotEmpty(listMap) && CollectionUtils.isNotEmpty(listMap.get(ThirdWmsTypeEnum.QMWMS.getCode()))) {
                adjustBusinessLogicJw(listMap.get(ThirdWmsTypeEnum.QMWMS.getCode()), type, warehouseCodeMap);
            }
            if (MapUtils.isNotEmpty(listMap) && CollectionUtils.isNotEmpty(listMap.get(ThirdWmsTypeEnum.FLWMS.getCode()))) {
                adjustBusinessLogicFl(listMap.get(ThirdWmsTypeEnum.FLWMS.getCode()), type, warehouseCodeMap);
            }
        }
    }

    /**
     * 巨沃处理逻辑
     *
     * @param taskList
     * @param type
     * @param warehouseCodeMap
     */
    private void adjustBusinessLogicJw(List<SgBWmsToInventoryChangeNoticeTask> taskList, String type, Map<String, SgCpCPhyWarehouse> warehouseCodeMap) {
        Map<String, List<SgBWmsToInventoryChangeNoticeTask>> wmsCodeMap = taskList.stream().collect(Collectors.groupingBy(x -> x.getWmsBillCode() + x.getWarehouseCode()));

        log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService adjustBusinessLogicJw:{},type:{}",
                "SgBWmsToInventoryChangeNoticeService.adjustBusinessLogicJw"),
                JSONObject.toJSONString(wmsCodeMap), type);

        for (String key : wmsCodeMap.keySet()) {
            List<SgBWmsToInventoryChangeNoticeTask> tasks = wmsCodeMap.get(key);

            try {
                if (SgConstantsIF.BILL_TYPE_XQGL.equals(type)) {
                    //条码+库存属性记录的“变化量”汇总是否等于0
                    Map<String, List<SgBWmsToInventoryChangeNoticeTask>> skuCodeMap = tasks.stream().collect(Collectors.groupingBy(x -> x.getPsCProEcode() + x.getStockType()));
                    skuCodeMap.forEach((k, v) -> {
                        //条件记录的“变化量”汇总是否等于0
                        Integer totQtyChang = v.stream().map(SgBWmsToInventoryChangeNoticeTask::getQtyChange).reduce(Integer::sum).orElse(NumberUtils.INTEGER_ZERO);
                        if (!NumberUtils.INTEGER_ZERO.equals(totQtyChang)) {
                            updatebatchFail(v, "WMS单号[" + key + "]出入库不一致，不满足效期调整条件！");
                            tasks.removeAll(v);
                        }
                    });
                }
                if (CollectionUtils.isEmpty(tasks)) {
                    continue;
                }
                //"仓库编码"是否存在实体仓档案
                String warehouseCode = tasks.get(0).getWarehouseCode();
                if (!warehouseCodeMap.containsKey(warehouseCode)) {
                    updatebatchFail(tasks, "仓库编码" + warehouseCode + "在【实体仓档案】中不存在！");
                    continue;
                }

                //itemCode是否存在"条码档案中"
                Map<String, List<SgBWmsToInventoryChangeNoticeTask>> itemCodeMap = tasks.stream().collect(Collectors.groupingBy(
                        SgBWmsToInventoryChangeNoticeTask::getPsCProEcode));
                List<String> itemCodeList = new ArrayList<>(itemCodeMap.keySet());
                //对比条码
                JSONArray array = WmsTaskUtils.compareToSku(itemCodeList);
                if (array.size() > 0) {
                    StringBuilder sb = new StringBuilder();
                    array.forEach(x -> sb.append("商品编码").append(x.toString()).append("在【商品SKU】中不存在！"));
                    updatebatchFail(tasks, sb.toString());
                    continue;
                }

                if (CollectionUtils.isEmpty(tasks)) {
                    continue;
                }
                //新增并审核逻辑库存调整单服务
                saveAndSubmitAdjustJw(tasks, warehouseCodeMap, type);

            } catch (Exception e) {
                log.error("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
                updatebatchFail(tasks, e.getMessage());
            }

        }
    }

    /**
     * 富勒处理逻辑
     *
     * @param taskList
     * @param type
     * @param warehouseCodeMap
     */
    private void adjustBusinessLogicFl(List<SgBWmsToInventoryChangeNoticeTask> taskList, String type, Map<String, SgCpCPhyWarehouse> warehouseCodeMap) {
        //按单号分组就已经是实体仓唯一
        Map<String, List<SgBWmsToInventoryChangeNoticeTask>> wmsCodeMap = taskList.stream().collect(Collectors.groupingBy(x -> x.getWmsBillCode() + x.getWarehouseCode()));
        log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService adjustBusinessLogicFl:{},type:{}",
                "SgBWmsToInventoryChangeNoticeService.adjustBusinessLogicFl"),
                JSONObject.toJSONString(wmsCodeMap), type);

        for (String key : wmsCodeMap.keySet()) {
            List<SgBWmsToInventoryChangeNoticeTask> tasks = wmsCodeMap.get(key);

            try {
                if (SgConstantsIF.BILL_TYPE_XQGL.equals(type)) {
                    //条码+库存属性记录的“变化量”汇总是否等于0
                    Map<String, List<SgBWmsToInventoryChangeNoticeTask>> skuCodeMap = tasks.stream().collect(Collectors.groupingBy(x -> x.getPsCProEcode() + x.getStockType()));
                    skuCodeMap.forEach((k, v) -> {
                        //条件记录的“变化量”汇总是否等于0
                        Integer totQtyChang = v.stream().map(SgBWmsToInventoryChangeNoticeTask::getQtyChange).reduce(Integer::sum).orElse(NumberUtils.INTEGER_ZERO);
                        if (!NumberUtils.INTEGER_ZERO.equals(totQtyChang)) {
                            updatebatchFail(v, "WMS单号[" + key + "]出入库不一致，不满足效期调整条件！");
                            tasks.removeAll(v);
                        }
                    });
                }
                if (CollectionUtils.isEmpty(tasks)) {
                    continue;
                }
                String message = tasks.get(0).getMessage();
                if (StringUtils.isEmpty(message)) {
                    updatebatchFail(tasks, "WMS单号[" + tasks.get(0).getWmsBillCode() + "]报文为空!");
                    continue;
                }
                //获取库存地点
                String storageLocation = null;
                JSONObject request = JSONObject.parseObject(message);
                if (request != null && request.getJSONObject("extendProps") != null) {
                    storageLocation = request.getJSONObject("extendProps").getString("storageLocation");
                }
                if (StringUtils.isEmpty(storageLocation)) {
                    updatebatchFail(tasks, "WMS单号[" + tasks.get(0).getWmsBillCode() + "]报文中库存地点为空!");
                    continue;
                }
                //"仓库编码"是否存在实体仓档案
                SgCpCPhyWarehouse warehouse = warehouseCodeMap.get(storageLocation);
                if (warehouse == null) {
                    updatebatchFail(tasks, "仓库编码" + storageLocation + "在【实体仓档案】中不存在！");
                    continue;
                }

                //itemCode是否存在"条码档案中"
                Map<String, List<SgBWmsToInventoryChangeNoticeTask>> itemCodeMap = tasks.stream().collect(Collectors.groupingBy(
                        SgBWmsToInventoryChangeNoticeTask::getPsCProEcode));
                List<String> itemCodeList = new ArrayList<>(itemCodeMap.keySet());
                //对比条码
                JSONArray array = WmsTaskUtils.compareToSku(itemCodeList);
                if (array.size() > 0) {
                    StringBuilder sb = new StringBuilder();
                    array.forEach(x -> sb.append("商品编码").append(x.toString()).append("在【商品SKU】中不存在！"));
                    updatebatchFail(tasks, sb.toString());
                    continue;
                }

                if (CollectionUtils.isEmpty(tasks)) {
                    continue;
                }
                //新增并审核逻辑库存调整单服务
                saveAndSubmitAdjustFl(tasks, warehouse, type);

            } catch (Exception e) {
                log.error("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
                updatebatchFail(tasks, e.getMessage());
            }

        }
    }

    /**
     * 新增并审核逻辑库存调整单服务(巨沃)
     *
     * @param taskList         入参
     * @param warehouseCodeMap 实体仓
     * @param type             单据类型
     */
    private void saveAndSubmitAdjustJw(List<SgBWmsToInventoryChangeNoticeTask> taskList, Map<String, SgCpCPhyWarehouse> warehouseCodeMap, String type) {
        //// 按照“WMS单号”+“仓库编码”分组 后的
        SgBWmsToInventoryChangeNoticeTask noticeTask = taskList.get(0);

        SgBStoAdjustSaveRequest adjustSaveRequest = new SgBStoAdjustSaveRequest();

        SgBStoAdjustMainSaveRequest mainRequest = new SgBStoAdjustMainSaveRequest();
        //调整日期
        mainRequest.setBillDate(noticeTask.getChangeTime());
        //库存日期
        mainRequest.setStockDate(noticeTask.getChangeTime());
        //单据类型
        mainRequest.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL);
        //ps:一头牛 实体仓跟逻辑仓关系1:1
        SgCpCPhyWarehouse cPhyWarehouse = warehouseCodeMap.get(noticeTask.getWarehouseCode());
        Map<Long, List<CpCStore>> storeMap = CommonCacheValUtils.getStoreInfoByPhyId(cPhyWarehouse.getId());
        if (MapUtils.isEmpty(storeMap)) {
            updatebatchFail(taskList, "仓库编码" + noticeTask.getWarehouseCode() + "在实体仓对应逻辑仓不存在中不存在！");
        }
        //逻辑仓
        List<CpCStore> stores = storeMap.get(cPhyWarehouse.getId());
        CpCStore cpCStore = stores.get(0);
        List<SgBWmsToInventoryChangeNoticeTask> collect = taskList.stream()
                .filter(p -> p.getQtyChange() < 0 && SgConstantsIF.STOCK_TYPE_GOODS.equals(p.getStockType())).collect(Collectors.toList());
        try {
            if (CollectionUtils.isNotEmpty(collect)) {
                sgWmsAutoReleaseOrderService.autoReleaseOrder(cpCStore, collect, R3SystemUserResource.getSystemRootUser());
            }
        } catch (Exception e) {
            updatebatchFail(taskList, e.getMessage() + "");
            return;
        }
        mainRequest.setCpCStoreId(stores.get(0).getId());
        mainRequest.setCpCStoreEcode(stores.get(0).getEcode());
        mainRequest.setCpCStoreEname(stores.get(0).getEname());
        if (SgConstantsIF.BILL_TYPE_XBCK.equals(type)) {
            //调整性质
            mainRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_LINE_EDGE_INOUT);
            //来源单据信息
            mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_LINE_EDGE_INOUT);
        } else if (SgConstantsIF.BILL_TYPE_XQGL.equals(type)) {
            //调整性质
            mainRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_VALIDITY_ADJUST);
            //来源单据信息
            mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_VALIDITY_ADJUST);
        } else if (SgConstantsIF.BILL_TYPE_OKZC.equals(type) || SgConstantsIF.BILL_TYPE_OKZR.equals(type)) {
            //调整性质
            mainRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_ZERO_STORAGE_ADJUST);
            //来源单据信息
            mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_ZERO_STORAGE_ADJUST);
            //拿供应商编码 随机拿一下不为空
            for (SgBWmsToInventoryChangeNoticeTask task : taskList) {
                String message = task.getMessage();
                JSONObject request = JSONObject.parseObject(message);
                String supplierCode = request.getString("supplierCode");
                if (StringUtils.isNotEmpty(supplierCode)) {
                    mainRequest.setCpCSupplierEcode(supplierCode);
                    break;
                }

            }


        }
        mainRequest.setWmsBillNo(noticeTask.getWmsBillCode());
        mainRequest.setSourceBillNo(noticeTask.getWmsBillCode());
        mainRequest.setRemark("由WMS库存异动接口，WMS单号[" + noticeTask.getWmsBillCode() + "]传入生成");
        mainRequest.setIsPassWms(SgStoreConstants.IS_PASS_THIRD_PARTY_N);
        mainRequest.setBillNo(noticeTask.getWmsBillCode());

        adjustSaveRequest.setObjId(-1L);
        adjustSaveRequest.setMainRequest(mainRequest);
        adjustSaveRequest.setItems(buildAdjustItem(taskList, type));
        adjustSaveRequest.setR3(Boolean.FALSE);
        adjustSaveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());

        log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService saveAndSubmitAdjust.saveAndSubmit:{}",
                "SgBWmsToInventoryChangeNoticeService.saveAndSubmitAdjust.saveAndSubmit"), JSONObject.toJSONString(adjustSaveRequest));
        ValueHolderV14<SgR3BaseResult> v14 = sgBStoAdjustSaveService.saveAndSubmit(adjustSaveRequest);
        log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService saveAndSubmitAdjust.valueHolderV14:{}",
                "SgBWmsToInventoryChangeNoticeService.saveAndSubmitAdjust.valueHolderV14"), JSONObject.toJSONString(v14));

        if (!v14.isOK()) {
            updatebatchFail(taskList, v14.getMessage());
        } else {
            taskList.forEach(this::updateSuccess);
        }

    }

    /**
     * 新增并审核逻辑库存调整单服务(富勒)
     *
     * @param taskList      入参
     * @param cPhyWarehouse 实体仓
     * @param type          单据类型
     */
    private void saveAndSubmitAdjustFl(List<SgBWmsToInventoryChangeNoticeTask> taskList, SgCpCPhyWarehouse cPhyWarehouse, String type) {
        //// 按照“WMS单号”+“仓库编码”分组 后的
        SgBWmsToInventoryChangeNoticeTask noticeTask = taskList.get(0);

        SgBStoAdjustSaveRequest adjustSaveRequest = new SgBStoAdjustSaveRequest();

        SgBStoAdjustMainSaveRequest mainRequest = new SgBStoAdjustMainSaveRequest();
        //调整日期
        mainRequest.setBillDate(noticeTask.getChangeTime());
        //库存日期
        mainRequest.setStockDate(noticeTask.getChangeTime());
        //单据类型
        mainRequest.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL);
        //ps:一头牛 实体仓跟逻辑仓关系1:1
        Map<Long, List<CpCStore>> storeMap = CommonCacheValUtils.getStoreInfoByPhyId(cPhyWarehouse.getId());
        if (MapUtils.isEmpty(storeMap)) {
            updatebatchFail(taskList, "仓库编码" + noticeTask.getWarehouseCode() + "在实体仓对应逻辑仓不存在中不存在！");
        }
        //逻辑仓
        List<CpCStore> stores = storeMap.get(cPhyWarehouse.getId());
        CpCStore cpCStore = stores.get(0);
        List<SgBWmsToInventoryChangeNoticeTask> collect = taskList.stream()
                .filter(p -> p.getQtyChange() < 0 && SgConstantsIF.STOCK_TYPE_GOODS.equals(p.getStockType())).collect(Collectors.toList());
        try {
            if (CollectionUtils.isNotEmpty(collect)) {
                sgWmsAutoReleaseOrderService.autoReleaseOrder(cpCStore, collect, R3SystemUserResource.getSystemRootUser());
            }
        } catch (Exception e) {
            updatebatchFail(taskList, e.getMessage());
            return;
        }
        mainRequest.setCpCStoreId(stores.get(0).getId());
        mainRequest.setCpCStoreEcode(stores.get(0).getEcode());
        mainRequest.setCpCStoreEname(stores.get(0).getEname());
        if (SgConstantsIF.BILL_TYPE_XBCK.equals(type)) {
            //调整性质
            mainRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_LINE_EDGE_INOUT);
            //来源单据信息
            mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_LINE_EDGE_INOUT);
        } else if (SgConstantsIF.BILL_TYPE_XQGL.equals(type)) {
            //调整性质
            mainRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_VALIDITY_ADJUST);
            //来源单据信息
            mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_VALIDITY_ADJUST);
        } else if (SgConstantsIF.BILL_TYPE_OKZC.equals(type) || SgConstantsIF.BILL_TYPE_OKZR.equals(type)) {
            //调整性质
            mainRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_ZERO_STORAGE_ADJUST);
            //来源单据信息
            mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_ZERO_STORAGE_ADJUST);
            //拿供应商编码 随机拿一下不为空
            for (SgBWmsToInventoryChangeNoticeTask task : taskList) {
                String message = task.getMessage();
                JSONObject request = JSONObject.parseObject(message);
                String supplierCode = request.getString("supplierCode");
                if (StringUtils.isNotEmpty(supplierCode)) {
                    mainRequest.setCpCSupplierEcode(supplierCode);
                    break;
                }

            }


        }
        mainRequest.setWmsBillNo(noticeTask.getWmsBillCode());
        mainRequest.setSourceBillNo(noticeTask.getWmsBillCode());
        mainRequest.setRemark("由WMS库存异动接口，WMS单号[" + noticeTask.getWmsBillCode() + "]传入生成");
        mainRequest.setIsPassWms(SgStoreConstants.IS_PASS_THIRD_PARTY_N);
        mainRequest.setBillNo(noticeTask.getWmsBillCode());

        adjustSaveRequest.setObjId(-1L);
        adjustSaveRequest.setMainRequest(mainRequest);
        adjustSaveRequest.setItems(buildAdjustItem(taskList, type));
        adjustSaveRequest.setR3(Boolean.FALSE);
        adjustSaveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());

        log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService saveAndSubmitAdjust.saveAndSubmit:{}",
                "SgBWmsToInventoryChangeNoticeService.saveAndSubmitAdjust.saveAndSubmit"), JSONObject.toJSONString(adjustSaveRequest));
        ValueHolderV14<SgR3BaseResult> v14 = sgBStoAdjustSaveService.saveAndSubmit(adjustSaveRequest);
        log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService saveAndSubmitAdjust.valueHolderV14:{}",
                "SgBWmsToInventoryChangeNoticeService.saveAndSubmitAdjust.valueHolderV14"), JSONObject.toJSONString(v14));

        if (!v14.isOK()) {
            updatebatchFail(taskList, v14.getMessage());
        } else {
            taskList.forEach(this::updateSuccess);
        }

    }

    /**
     * 构建库存调整单明细
     *
     * @param taskList 入参
     * @param type     单据类型
     * @return SgBStoAdjustItemSaveRequest
     */
    private List<SgBStoAdjustItemSaveRequest> buildAdjustItem(List<SgBWmsToInventoryChangeNoticeTask> taskList, String type) {
        List<SgBStoAdjustItemSaveRequest> itemRequests = new ArrayList<>();

        taskList.forEach(task -> {
            SgBStoAdjustItemSaveRequest adjustItemRequest = new SgBStoAdjustItemSaveRequest();
            adjustItemRequest.setId(-1L);
            adjustItemRequest.setQty(BigDecimal.valueOf(task.getQtyChange()));
//            if (SgConstantsIF.BILL_TYPE_XBCK.equals(type)) {
//                adjustItemRequest.setQty(BigDecimal.valueOf(task.getQtyChange()).negate());
//            }
            adjustItemRequest.setPsCSkuEcode(task.getPsCProEcode());
            String produceDate = StringUtils.isEmpty(task.getBatchCode()) || SgConstantsIF.DEFAULT_PRODUCE_DATE.equals(task.getBatchCode()) ? SgConstantsIF.DEFAULT_PRODUCE_DATE :
                    DateUtils.formatSync8(DateUtils.parseSync8(task.getBatchCode(), DateUtils.PATTERN_DATE), DateUtils.DATE_PATTERN);
            adjustItemRequest.setProduceDate(produceDate);
            adjustItemRequest.setStorageType(task.getStockType());
            itemRequests.add(adjustItemRequest);
        });

        return itemRequests;
    }


    /**
     * 单据类型：zttz 业务逻辑(残次转换)
     *
     * @param zttzList 入参
     */
    private void zttzBusinessLogic(List<SgBWmsToInventoryChangeNoticeTask> zttzList) {
        log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService zttzBusinessLogic:{}",
                "SgBWmsToInventoryChangeNoticeService.zttzBusinessLogic", JSONObject.toJSONString(zttzList)));

        if (CollectionUtils.isNotEmpty(zttzList)) {
            List<String> warehouseCodeList = zttzList.stream().map(SgBWmsToInventoryChangeNoticeTask::getWarehouseCode).collect(Collectors.toList());
            Map<String, SgCpCPhyWarehouse> warehouseCodeMap = WmsTaskUtils.getSgCpPhyWarehouseByCode(warehouseCodeList);
            Map<String, List<SgBWmsToInventoryChangeNoticeTask>> listMap =
                    zttzList.stream().collect(Collectors.groupingBy(SgBWmsToInventoryChangeNoticeTask::getWmsWarehouseType));
            if (MapUtils.isNotEmpty(listMap) && CollectionUtils.isNotEmpty(listMap.get(ThirdWmsTypeEnum.QMWMS.getCode()))) {
                zttzBusinessLogicJw(listMap.get(ThirdWmsTypeEnum.QMWMS.getCode()), warehouseCodeMap);
            }
            if (MapUtils.isNotEmpty(listMap) && CollectionUtils.isNotEmpty(listMap.get(ThirdWmsTypeEnum.FLWMS.getCode()))) {
                zttzBusinessLogicFl(listMap.get(ThirdWmsTypeEnum.FLWMS.getCode()), warehouseCodeMap);
            }
        }
    }

    /**
     * 残次转化（巨沃）
     *
     * @param zttzList
     * @param warehouseCodeMap
     */
    private void zttzBusinessLogicJw(List<SgBWmsToInventoryChangeNoticeTask> zttzList, Map<String, SgCpCPhyWarehouse> warehouseCodeMap) {
        Map<String, List<SgBWmsToInventoryChangeNoticeTask>> wmsCodeMap = zttzList.stream().collect(Collectors.groupingBy(x -> x.getWmsBillCode() + x.getWarehouseCode()));
        for (String key : wmsCodeMap.keySet()) {
            List<SgBWmsToInventoryChangeNoticeTask> tasks = wmsCodeMap.get(key);
            try {
                //条件记录的“变化量”汇总是否等于0
                Integer totQtyChang = tasks.stream().map(SgBWmsToInventoryChangeNoticeTask::getQtyChange).reduce(Integer::sum).orElse(NumberUtils.INTEGER_ZERO);
                if (!NumberUtils.INTEGER_ZERO.equals(totQtyChang)) {
                    updatebatchFail(tasks, "WMS单号[" + tasks.get(0).getWmsBillCode() + "]出入库不一致，不满足状态调整条件！");
                    continue;
                }

                //"仓库编码"是否存在实体仓档案
                String warehouseCode = tasks.get(0).getWarehouseCode();
                if (!warehouseCodeMap.containsKey(warehouseCode)) {
                    updatebatchFail(tasks, "仓库编码" + warehouseCode + "在【实体仓档案】中不存在！");
                    continue;
                }

                //itemCode是否存在"条码档案中"
                Map<String, List<SgBWmsToInventoryChangeNoticeTask>> itemCodeMap = tasks.stream().collect(Collectors.groupingBy(
                        SgBWmsToInventoryChangeNoticeTask::getPsCProEcode));
                List<String> itemCodeList = new ArrayList<>(itemCodeMap.keySet());
                //对比条码
                JSONArray array = WmsTaskUtils.compareToSku(itemCodeList);
                if (array.size() > 0) {
                    StringBuilder sb = new StringBuilder();
                    array.forEach(x -> sb.append("商品编码").append(x.toString()).append("在【商品SKU】中不存在！"));
                    updatebatchFail(tasks, sb.toString());
                    continue;
                }

                if (CollectionUtils.isEmpty(tasks)) {
                    continue;
                }

                //.新增并提交 逻辑解冻/冻结单
                saveFreezeOrUnFreezeJw(tasks, warehouseCodeMap);


            } catch (Exception e) {
                log.error("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
                updatebatchFail(tasks, e.getMessage());
            }
        }
    }

    /**
     * 残次转化（富勒）
     *
     * @param zttzList
     * @param warehouseCodeMap
     */
    private void zttzBusinessLogicFl(List<SgBWmsToInventoryChangeNoticeTask> zttzList, Map<String, SgCpCPhyWarehouse> warehouseCodeMap) {
        Map<String, List<SgBWmsToInventoryChangeNoticeTask>> wmsCodeMap = zttzList.stream().collect(Collectors.groupingBy(x -> x.getWmsBillCode() + x.getWarehouseCode()));
        for (String key : wmsCodeMap.keySet()) {
            List<SgBWmsToInventoryChangeNoticeTask> tasks = wmsCodeMap.get(key);
            try {
                //条件记录的“变化量”汇总是否等于0
                Integer totQtyChang = tasks.stream().map(SgBWmsToInventoryChangeNoticeTask::getQtyChange).reduce(Integer::sum).orElse(NumberUtils.INTEGER_ZERO);
                if (!NumberUtils.INTEGER_ZERO.equals(totQtyChang)) {
                    updatebatchFail(tasks, "WMS单号[" + tasks.get(0).getWmsBillCode() + "]出入库不一致，不满足状态调整条件！");
                    continue;
                }
                String message = tasks.get(0).getMessage();
                if (StringUtils.isEmpty(message)) {
                    updatebatchFail(tasks, "WMS单号[" + tasks.get(0).getWmsBillCode() + "]报文为空!");
                    continue;
                }
                //获取库存地点
                String storageLocation = null;
                JSONObject request = JSONObject.parseObject(message);
                if (request != null && request.getJSONObject("extendProps") != null) {
                    storageLocation = request.getJSONObject("extendProps").getString("storageLocation");
                }
                if (StringUtils.isEmpty(storageLocation)) {
                    updatebatchFail(tasks, "WMS单号[" + tasks.get(0).getWmsBillCode() + "]报文中库存地点为空!");
                    continue;
                }
                //"仓库编码"是否存在实体仓档案
                SgCpCPhyWarehouse warehouse = warehouseCodeMap.get(storageLocation);
                if (warehouse == null) {
                    updatebatchFail(tasks, "仓库编码" + storageLocation + "在【实体仓档案】中不存在！");
                    continue;
                }
                //itemCode是否存在"条码档案中"
                Map<String, List<SgBWmsToInventoryChangeNoticeTask>> itemCodeMap = tasks.stream().collect(Collectors.groupingBy(
                        SgBWmsToInventoryChangeNoticeTask::getPsCProEcode));
                List<String> itemCodeList = new ArrayList<>(itemCodeMap.keySet());
                //对比条码
                JSONArray array = WmsTaskUtils.compareToSku(itemCodeList);
                if (array.size() > 0) {
                    StringBuilder sb = new StringBuilder();
                    array.forEach(x -> sb.append("商品编码").append(x.toString()).append("在【商品SKU】中不存在！"));
                    updatebatchFail(tasks, sb.toString());
                    continue;
                }

                if (CollectionUtils.isEmpty(tasks)) {
                    continue;
                }
                //.新增并提交 逻辑解冻/冻结单
                saveFreezeOrUnFreezeFl(tasks, warehouse);
            } catch (Exception e) {
                log.error("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
                updatebatchFail(tasks, e.getMessage());
            }
        }
    }

    /**
     * 新增并提交逻辑解冻单 and  新增并提交逻辑冻结单(巨沃)
     *
     * @param taskList         入参
     * @param warehouseCodeMap 实体仓map
     */
    private void saveFreezeOrUnFreezeJw(List<SgBWmsToInventoryChangeNoticeTask> taskList, Map<String, SgCpCPhyWarehouse> warehouseCodeMap) {
        log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService saveFreezeOrUnFreezeJw:{}",
                "SgBWmsToInventoryChangeNoticeService.saveFreezeOrUnFreezeJw", JSONObject.toJSONString(taskList)));

        List<SgBWmsToInventoryChangeNoticeTask> tasks = taskList.stream().filter(task -> !SgConstantsIF.STOCK_TYPE_GOODS.equals(task.getStockType())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(tasks)) {
            List<SgBWmsToInventoryChangeNoticeTask> freezeList = new ArrayList<>();
            List<SgBWmsToInventoryChangeNoticeTask> unFreezeList = new ArrayList<>();
            tasks.forEach(task -> {
                Integer qtyChange = task.getQtyChange();
                if (qtyChange > 0) {
                    freezeList.add(task);
                } else {
                    unFreezeList.add(task);
                }
            });
            //同一个WMS单号下，如果变化数量>0,<0的记录同时存在，保证先生成解冻单，再生成冻结单（一个事务中）。
            List<String> redisFtpKey = new ArrayList<>();
            try {
                SgBWmsToInventoryChangeNoticeService bean = ApplicationContextHandle.getBean(SgBWmsToInventoryChangeNoticeService.class);
                bean.saveAndSubmitUnFreezeAndFreezeJw(freezeList, unFreezeList, warehouseCodeMap, redisFtpKey);
                taskList.forEach(this::updateSuccess);
            } catch (Exception e) {
                // 回滚库存
                StorageBasicUtils.rollbackStorage(redisFtpKey, R3SystemUserResource.getSystemRootUser());
                log.warn("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
                updatebatchFail(taskList, e.getMessage());
            }

        }

    }

    /**
     * 新增并提交逻辑解冻单 and  新增并提交逻辑冻结单(富勒)
     *
     * @param taskList  入参
     * @param warehouse 实体仓
     */
    private void saveFreezeOrUnFreezeFl(List<SgBWmsToInventoryChangeNoticeTask> taskList, SgCpCPhyWarehouse warehouse) {
        log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService saveFreezeOrUnFreezeFl:{}",
                "SgBWmsToInventoryChangeNoticeService.saveFreezeOrUnFreezeFl", JSONObject.toJSONString(taskList)));

        List<SgBWmsToInventoryChangeNoticeTask> tasks = taskList.stream()
                .filter(task -> !SgConstantsIF.STOCK_TYPE_GOODS.equals(task.getStockType())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(tasks)) {
            List<SgBWmsToInventoryChangeNoticeTask> freezeList = new ArrayList<>();
            List<SgBWmsToInventoryChangeNoticeTask> unFreezeList = new ArrayList<>();
            tasks.forEach(task -> {
                Integer qtyChange = task.getQtyChange();
                if (qtyChange > 0) {
                    freezeList.add(task);
                } else {
                    unFreezeList.add(task);
                }
            });
            //同一个WMS单号下，如果变化数量>0,<0的记录同时存在，保证先生成解冻单，再生成冻结单（一个事务中）。
            List<String> redisFtpKey = new ArrayList<>();
            try {
                SgBWmsToInventoryChangeNoticeService bean = ApplicationContextHandle.getBean(SgBWmsToInventoryChangeNoticeService.class);
                bean.saveAndSubmitUnFreezeAndFreezeFl(freezeList, unFreezeList, warehouse, redisFtpKey);
                taskList.forEach(this::updateSuccess);
            } catch (Exception e) {
                // 回滚库存
                StorageBasicUtils.rollbackStorage(redisFtpKey, R3SystemUserResource.getSystemRootUser());
                log.error("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
                updatebatchFail(taskList, e.getMessage());
            }

        }

    }

    /**
     * 同一个WMS单号下，如果变化数量>0,<0的记录同时存在，保证先生成解冻单，再生成冻结单（一个事务中）(巨沃)
     *
     * @param freezeIntersectionList   冻结单入参
     * @param unFreezeIntersectionList 解冻单入参
     * @param warehouseCodeMap         实体仓map
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAndSubmitUnFreezeAndFreezeJw(List<SgBWmsToInventoryChangeNoticeTask> freezeIntersectionList,
                                                 List<SgBWmsToInventoryChangeNoticeTask> unFreezeIntersectionList,
                                                 Map<String, SgCpCPhyWarehouse> warehouseCodeMap, List<String> redisFtpKey) {
        if (CollectionUtils.isNotEmpty(unFreezeIntersectionList)) {
            ValueHolderV14<SgBStoUnfreezeSaveBillResult> valueHolderV14 = saveAndSubmitUnFreezeJw(unFreezeIntersectionList, warehouseCodeMap);
            if (!valueHolderV14.isOK()) {
                AssertUtils.logAndThrow("库存异动新增并审核逻辑解冻单失败：" + valueHolderV14.getMessage());
            }
            SgBStoUnfreezeSaveBillResult data = valueHolderV14.getData();
            if (data != null && CollectionUtils.isNotEmpty(data.getRedisBillFtpKeys())) {
                redisFtpKey.addAll(data.getRedisBillFtpKeys());
            }
        }

        if (CollectionUtils.isNotEmpty(freezeIntersectionList)) {
            ValueHolderV14<SgR3BaseResult> v14 = saveAndSubmitFreezeJw(freezeIntersectionList, warehouseCodeMap);
            if (!v14.isOK()) {
                AssertUtils.logAndThrow("库存异动新增并审核逻辑冻结单失败：" + v14.getMessage());
            }
        }

    }

    /**
     * 同一个WMS单号下，如果变化数量>0,<0的记录同时存在，保证先生成解冻单，再生成冻结单（一个事务中）(富勒)
     *
     * @param freezeIntersectionList   冻结单入参
     * @param unFreezeIntersectionList 解冻单入参
     * @param warehouse                实体仓
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAndSubmitUnFreezeAndFreezeFl(List<SgBWmsToInventoryChangeNoticeTask> freezeIntersectionList,
                                                 List<SgBWmsToInventoryChangeNoticeTask> unFreezeIntersectionList,
                                                 SgCpCPhyWarehouse warehouse, List<String> redisFtpKey) {
        if (CollectionUtils.isNotEmpty(unFreezeIntersectionList)) {
            ValueHolderV14<SgBStoUnfreezeSaveBillResult> valueHolderV14 = saveAndSubmitUnFreezeFl(unFreezeIntersectionList, warehouse);
            if (!valueHolderV14.isOK()) {
                AssertUtils.logAndThrow("库存异动新增并审核逻辑解冻单失败：" + valueHolderV14.getMessage());
            }
            SgBStoUnfreezeSaveBillResult data = valueHolderV14.getData();
            if (data != null && CollectionUtils.isNotEmpty(data.getRedisBillFtpKeys())) {
                redisFtpKey.addAll(data.getRedisBillFtpKeys());
            }
        }

        if (CollectionUtils.isNotEmpty(freezeIntersectionList)) {
            ValueHolderV14<SgR3BaseResult> v14 = saveAndSubmitFreezeFl(freezeIntersectionList, warehouse);
            if (!v14.isOK()) {
                AssertUtils.logAndThrow("库存异动新增并审核逻辑冻结单失败：" + v14.getMessage());
            }
        }

    }

    /**
     * 新增并提交逻辑冻结单(巨沃)
     *
     * @param freezeIntersectionList 入参
     * @param warehouseCodeMap       实体仓map
     * @return SgR3BaseResult
     */
    private ValueHolderV14<SgR3BaseResult> saveAndSubmitFreezeJw(List<SgBWmsToInventoryChangeNoticeTask> freezeIntersectionList,
                                                                 Map<String, SgCpCPhyWarehouse> warehouseCodeMap) {
        try {
            log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService saveAndSubmitFreeze:{}",
                    "SgBWmsToInventoryChangeNoticeService.saveAndSubmitFreeze"), JSONObject.toJSONString(freezeIntersectionList));

            // 按照“WMS单号”+“仓库编码”分组 后的
            SgBWmsToInventoryChangeNoticeTask noticeTask = freezeIntersectionList.get(0);
            SgBStoFreezeBillSaveRequest saveRequest = new SgBStoFreezeBillSaveRequest();

            SgBStoFreezeSaveRequest mainRequest = new SgBStoFreezeSaveRequest();
            //异动时间
            mainRequest.setBillDate(noticeTask.getChangeTime());
            //ps:一头牛 实体仓跟逻辑仓关系1:1
            SgCpCPhyWarehouse cPhyWarehouse = warehouseCodeMap.get(noticeTask.getWarehouseCode());
            Map<Long, List<CpCStore>> storeMap = CommonCacheValUtils.getStoreInfoByPhyId(cPhyWarehouse.getId());
            if (MapUtils.isEmpty(storeMap)) {
                updatebatchFail(freezeIntersectionList, "仓库编码" + noticeTask.getWarehouseCode() + "在实体仓对应逻辑仓不存在中不存在！");
                return new ValueHolderV14<>(ResultCode.FAIL, "仓库编码" + noticeTask.getWarehouseCode() + "在实体仓对应逻辑仓不存在中不存在！");
            }
            //逻辑仓
            List<CpCStore> stores = storeMap.get(cPhyWarehouse.getId());
            CpCStore cpCStore = stores.get(0);
            List<SgBWmsToInventoryChangeNoticeTask> collect = freezeIntersectionList.stream()
                    .filter(p -> p.getQtyChange() < 0 && SgConstantsIF.STOCK_TYPE_GOODS.equals(p.getStockType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                sgWmsAutoReleaseOrderService.autoReleaseOrder(cpCStore, collect, R3SystemUserResource.getSystemRootUser());
            }
            mainRequest.setCpCStoreId(stores.get(0).getId());
            mainRequest.setCpCStoreEcode(stores.get(0).getEcode());
            mainRequest.setCpCStoreEname(stores.get(0).getEname());
            //来源系统
            mainRequest.setSourceSys(SgStoreConstants.FREEZE_SOURCE_SYS_WMS);
            //来源单据信息
            mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_STATUS_ADJUST);
            mainRequest.setSourceBillNo(noticeTask.getWmsBillCode());
            mainRequest.setBillNo(noticeTask.getWmsBillCode());
            mainRequest.setRemark("由WMS库存异动接口，WMS单号[" + noticeTask.getWmsBillCode() + "]传入生成");

            saveRequest.setR3(Boolean.FALSE);
            saveRequest.setFreezeSaveRequest(mainRequest);
            saveRequest.setSgBStoFreezeSaveItemRequests(buildFreezeItem(freezeIntersectionList));
            saveRequest.setObjId(-1L);
            saveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());

            log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService freezeSaveAndSubmitService.saveAndSubmit:{}",
                    "SgBWmsToInventoryChangeNoticeService.freezeSaveAndSubmitService.saveAndSubmit",
                    JSONObject.toJSONString(saveRequest)));
            ValueHolderV14<SgR3BaseResult> v14 = freezeSaveAndSubmitService.saveAndSubmit(saveRequest);
            log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService freezeSaveAndSubmitService.valueHolderV14:{}",
                    "SgBWmsToInventoryChangeNoticeService.freezeSaveAndSubmitService.valueHolderV14",
                    JSONObject.toJSONString(v14)));
            return v14;
        } catch (Exception e) {
            log.error("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }

    }

    /**
     * 新增并提交逻辑冻结单(富勒)
     *
     * @param freezeIntersectionList 入参
     * @param cPhyWarehouse          实体仓
     * @return SgR3BaseResult
     */
    private ValueHolderV14<SgR3BaseResult> saveAndSubmitFreezeFl(List<SgBWmsToInventoryChangeNoticeTask> freezeIntersectionList,
                                                                 SgCpCPhyWarehouse cPhyWarehouse) {
        try {
            log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService saveAndSubmitFreeze:{}",
                    "SgBWmsToInventoryChangeNoticeService.saveAndSubmitFreeze", JSONObject.toJSONString(freezeIntersectionList)));

            // 按照“WMS单号”+“仓库编码”分组 后的
            SgBWmsToInventoryChangeNoticeTask noticeTask = freezeIntersectionList.get(0);
            SgBStoFreezeBillSaveRequest saveRequest = new SgBStoFreezeBillSaveRequest();

            SgBStoFreezeSaveRequest mainRequest = new SgBStoFreezeSaveRequest();
            //异动时间
            mainRequest.setBillDate(noticeTask.getChangeTime());
            //ps:一头牛 实体仓跟逻辑仓关系1:1
            Map<Long, List<CpCStore>> storeMap = CommonCacheValUtils.getStoreInfoByPhyId(cPhyWarehouse.getId());
            if (MapUtils.isEmpty(storeMap)) {
                updatebatchFail(freezeIntersectionList, "仓库编码" + noticeTask.getWarehouseCode() + "在实体仓对应逻辑仓不存在中不存在！");
                return new ValueHolderV14<>(ResultCode.FAIL, "仓库编码" + noticeTask.getWarehouseCode() + "在实体仓对应逻辑仓不存在中不存在！");
            }
            //逻辑仓
            List<CpCStore> stores = storeMap.get(cPhyWarehouse.getId());
            CpCStore cpCStore = stores.get(0);
            List<SgBWmsToInventoryChangeNoticeTask> collect = freezeIntersectionList.stream()
                    .filter(p -> p.getQtyChange() < 0 && SgConstantsIF.STOCK_TYPE_GOODS.equals(p.getStockType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                sgWmsAutoReleaseOrderService.autoReleaseOrder(cpCStore, collect, R3SystemUserResource.getSystemRootUser());
            }
            mainRequest.setCpCStoreId(stores.get(0).getId());
            mainRequest.setCpCStoreEcode(stores.get(0).getEcode());
            mainRequest.setCpCStoreEname(stores.get(0).getEname());
            //来源系统
            mainRequest.setSourceSys(SgStoreConstants.FREEZE_SOURCE_SYS_WMS);
            //来源单据信息
            mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_STATUS_ADJUST);
            mainRequest.setSourceBillNo(noticeTask.getWmsBillCode());
            mainRequest.setBillNo(noticeTask.getWmsBillCode());
            mainRequest.setRemark("由WMS库存异动接口，WMS单号[" + noticeTask.getWmsBillCode() + "]传入生成");

            saveRequest.setR3(Boolean.FALSE);
            saveRequest.setFreezeSaveRequest(mainRequest);
            saveRequest.setSgBStoFreezeSaveItemRequests(buildFreezeItem(freezeIntersectionList));
            saveRequest.setObjId(-1L);
            saveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());

            log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService freezeSaveAndSubmitService.saveAndSubmit:{}",
                    "SgBWmsToInventoryChangeNoticeService.freezeSaveAndSubmitService.saveAndSubmit",
                    JSONObject.toJSONString(saveRequest)));
            ValueHolderV14<SgR3BaseResult> v14 = freezeSaveAndSubmitService.saveAndSubmit(saveRequest);
            log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService freezeSaveAndSubmitService.valueHolderV14:{}",
                    "SgBWmsToInventoryChangeNoticeService.freezeSaveAndSubmitService.valueHolderV14",
                    JSONObject.toJSONString(v14)));
            return v14;
        } catch (Exception e) {
            log.error("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }

    }

    /**
     * 构建逻辑冻结单 明细
     *
     * @param freezeIntersectionList 入参
     * @return SgBStoFreezeSaveItemRequest
     */
    private List<SgBStoFreezeSaveItemRequest> buildFreezeItem(List<SgBWmsToInventoryChangeNoticeTask> freezeIntersectionList) {
        List<SgBStoFreezeSaveItemRequest> itemRequests = new ArrayList<>();

        freezeIntersectionList.forEach(task -> {
            SgBStoFreezeSaveItemRequest freezeItemRequest = new SgBStoFreezeSaveItemRequest();
            freezeItemRequest.setId(-1L);
            freezeItemRequest.setQty(BigDecimal.valueOf(task.getQtyChange()));
            freezeItemRequest.setPsCSkuEcode(task.getPsCProEcode());
            String produceDate = StringUtils.isEmpty(task.getBatchCode()) || SgConstantsIF.DEFAULT_PRODUCE_DATE.equals(task.getBatchCode()) ? SgConstantsIF.DEFAULT_PRODUCE_DATE :
                    DateUtils.formatSync8(DateUtils.parseSync8(task.getBatchCode(), DateUtils.PATTERN_DATE), DateUtils.DATE_PATTERN);
            freezeItemRequest.setProduceDate(produceDate);
            freezeItemRequest.setStockType(task.getStockType());
            itemRequests.add(freezeItemRequest);
        });

        return itemRequests;
    }

    /**
     * 新增并提交逻辑解冻单(巨沃)
     *
     * @param unFreezeIntersectionList 入参
     * @param warehouseCodeMap         实体仓map
     * @return SgBStoUnfreezeSaveBillResult
     */
    private ValueHolderV14<SgBStoUnfreezeSaveBillResult> saveAndSubmitUnFreezeJw(List<SgBWmsToInventoryChangeNoticeTask> unFreezeIntersectionList,
                                                                                 Map<String, SgCpCPhyWarehouse> warehouseCodeMap) {
        try {
            log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService saveAndSubmitUnFreeze:{}",
                    "SgBWmsToInventoryChangeNoticeService.saveAndSubmitUnFreeze", JSONObject.toJSONString(unFreezeIntersectionList)));

            // 按照“WMS单号”+“仓库编码”分组 后的
            SgBWmsToInventoryChangeNoticeTask noticeTask = unFreezeIntersectionList.get(0);

            SgBStoUnfreezeSaveRequest saveRequest = new SgBStoUnfreezeSaveRequest();

            SgBStoUnfreezeRequest mainRequest = new SgBStoUnfreezeRequest();
            //异动时间
            mainRequest.setBillDate(noticeTask.getChangeTime());

            //ps:一头牛 实体仓跟逻辑仓关系1:1
            SgCpCPhyWarehouse cPhyWarehouse = warehouseCodeMap.get(noticeTask.getWarehouseCode());
            Map<Long, List<CpCStore>> storeMap = CommonCacheValUtils.getStoreInfoByPhyId(cPhyWarehouse.getId());
            if (MapUtils.isEmpty(storeMap)) {
                updatebatchFail(unFreezeIntersectionList, "仓库编码" + noticeTask.getWarehouseCode() + "在实体仓对应逻辑仓中不存在！");
                return new ValueHolderV14<>(ResultCode.FAIL, "仓库编码" + noticeTask.getWarehouseCode() + "在实体仓对应逻辑仓中不存在！");
            }
            //逻辑仓
            List<CpCStore> stores = storeMap.get(cPhyWarehouse.getId());
            mainRequest.setCpCStoreId(stores.get(0).getId());
            mainRequest.setCpCStoreEcode(stores.get(0).getEcode());
            mainRequest.setCpCStoreEname(stores.get(0).getEname());
            //来源系统
            mainRequest.setSourceSystem(SgStoreConstants.FREEZE_SOURCE_SYS_WMS);
            //来源单据信息
            mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_STATUS_ADJUST);
            mainRequest.setSourceBillNo(noticeTask.getWmsBillCode());
            mainRequest.setBillNo(noticeTask.getWmsBillCode());
            mainRequest.setRemark("由WMS库存异动接口，WMS单号[" + noticeTask.getWmsBillCode() + "]传入生成");

            saveRequest.setR3(Boolean.FALSE);
            saveRequest.setSgStoUnfreezeRequest(mainRequest);
            saveRequest.setSgStoUnfreezeItemRequest(buildUnfreezeItem(unFreezeIntersectionList));
            saveRequest.setObjId(-1L);
            saveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());

            log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService unfreezeSaveAndSubmitService.saveAndSubmit:{}",
                    "SgBWmsToInventoryChangeNoticeService.unfreezeSaveAndSubmitService.saveAndSubmit",
                    JSONObject.toJSONString(saveRequest)));
            ValueHolderV14<SgBStoUnfreezeSaveBillResult> valueHolderV14 = unfreezeSaveAndSubmitService.saveAndSubmit(saveRequest);
            log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService unfreezeSaveAndSubmitService.valueHolderV14:{}",
                    "SgBWmsToInventoryChangeNoticeService.unfreezeSaveAndSubmitService.valueHolderV14",
                    JSONObject.toJSONString(valueHolderV14)));
            return valueHolderV14;
        } catch (Exception e) {
            log.warn("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }

    }

    /**
     * 新增并提交逻辑解冻单(富勒)
     *
     * @param unFreezeIntersectionList 入参
     * @param cPhyWarehouse            实体仓
     * @return SgBStoUnfreezeSaveBillResult
     */
    private ValueHolderV14<SgBStoUnfreezeSaveBillResult> saveAndSubmitUnFreezeFl(List<SgBWmsToInventoryChangeNoticeTask> unFreezeIntersectionList,
                                                                                 SgCpCPhyWarehouse cPhyWarehouse) {
        try {
            log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService saveAndSubmitUnFreeze:{}",
                    "SgBWmsToInventoryChangeNoticeService.saveAndSubmitUnFreeze", JSONObject.toJSONString(unFreezeIntersectionList)));

            // 按照“WMS单号”+“仓库编码”分组 后的
            SgBWmsToInventoryChangeNoticeTask noticeTask = unFreezeIntersectionList.get(0);

            SgBStoUnfreezeSaveRequest saveRequest = new SgBStoUnfreezeSaveRequest();

            SgBStoUnfreezeRequest mainRequest = new SgBStoUnfreezeRequest();
            //异动时间
            mainRequest.setBillDate(noticeTask.getChangeTime());

            //ps:一头牛 实体仓跟逻辑仓关系1:1
            Map<Long, List<CpCStore>> storeMap = CommonCacheValUtils.getStoreInfoByPhyId(cPhyWarehouse.getId());
            if (MapUtils.isEmpty(storeMap)) {
                updatebatchFail(unFreezeIntersectionList, "仓库编码" + cPhyWarehouse.getEcode() + "在实体仓对应逻辑仓中不存在！");
                return new ValueHolderV14<>(ResultCode.FAIL, "仓库编码" + cPhyWarehouse.getEcode() + "在实体仓对应逻辑仓中不存在！");
            }
            //逻辑仓
            List<CpCStore> stores = storeMap.get(cPhyWarehouse.getId());
            mainRequest.setCpCStoreId(stores.get(0).getId());
            mainRequest.setCpCStoreEcode(stores.get(0).getEcode());
            mainRequest.setCpCStoreEname(stores.get(0).getEname());
            //来源系统
            mainRequest.setSourceSystem(SgStoreConstants.FREEZE_SOURCE_SYS_WMS);
            //来源单据信息
            mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_STATUS_ADJUST);
            mainRequest.setSourceBillNo(noticeTask.getWmsBillCode());
            mainRequest.setBillNo(noticeTask.getWmsBillCode());
            mainRequest.setRemark("由WMS库存异动接口，WMS单号[" + noticeTask.getWmsBillCode() + "]传入生成");

            saveRequest.setR3(Boolean.FALSE);
            saveRequest.setSgStoUnfreezeRequest(mainRequest);
            saveRequest.setSgStoUnfreezeItemRequest(buildUnfreezeItem(unFreezeIntersectionList));
            saveRequest.setObjId(-1L);
            saveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());

            log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService unfreezeSaveAndSubmitService.saveAndSubmit:{}",
                    "SgBWmsToInventoryChangeNoticeService.unfreezeSaveAndSubmitService.saveAndSubmit",
                    JSONObject.toJSONString(saveRequest)));
            ValueHolderV14<SgBStoUnfreezeSaveBillResult> valueHolderV14 = unfreezeSaveAndSubmitService.saveAndSubmit(saveRequest);
            log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService unfreezeSaveAndSubmitService.valueHolderV14:{}",
                    "SgBWmsToInventoryChangeNoticeService.unfreezeSaveAndSubmitService.valueHolderV14",
                    JSONObject.toJSONString(valueHolderV14)));
            return valueHolderV14;
        } catch (Exception e) {
            log.error("exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }

    }

    /**
     * 构建逻辑解冻单 明细
     *
     * @param unFreezeIntersectionList 入参
     * @return SgBStoUnfreezeItemRequest
     */
    private List<SgBStoUnfreezeItemRequest> buildUnfreezeItem(List<SgBWmsToInventoryChangeNoticeTask> unFreezeIntersectionList) {
        List<SgBStoUnfreezeItemRequest> itemRequests = new ArrayList<>();

        unFreezeIntersectionList.forEach(task -> {
            SgBStoUnfreezeItemRequest unfreezeItemRequest = new SgBStoUnfreezeItemRequest();
            unfreezeItemRequest.setId(-1L);
            unfreezeItemRequest.setQty(BigDecimal.valueOf(task.getQtyChange()).negate());
            unfreezeItemRequest.setPsCSkuEcode(task.getPsCProEcode());
            String produceDate = StringUtils.isEmpty(task.getBatchCode()) || SgConstantsIF.DEFAULT_PRODUCE_DATE.equals(task.getBatchCode()) ? SgConstantsIF.DEFAULT_PRODUCE_DATE :
                    DateUtils.formatSync8(DateUtils.parseSync8(task.getBatchCode(), DateUtils.PATTERN_DATE), DateUtils.DATE_PATTERN);
            unfreezeItemRequest.setProduceDate(produceDate);
            unfreezeItemRequest.setStockType(task.getStockType());
            itemRequests.add(unfreezeItemRequest);
        });


        return itemRequests;
    }

    /**
     * 单据类型 =  kcky-跨仓移库 处理逻辑
     *
     * @param kcykList 跨仓移库类型数据
     */
    private void kckyBusinessLogic(List<SgBWmsToInventoryChangeNoticeTask> kcykList) {
        log.info(LogUtil.format("跨仓移库中间表数据:{}",
                "SgBWmsToInventoryChangeNoticeService.kckyBusinessLogic", JSONObject.toJSONString(kcykList)));
        if (CollectionUtils.isEmpty(kcykList)) {
            return;
        }
        Map<String, List<SgBWmsToInventoryChangeNoticeTask>> listMap =
                kcykList.stream().collect(Collectors.groupingBy(SgBWmsToInventoryChangeNoticeTask::getWmsWarehouseType));
        if (MapUtils.isEmpty(listMap)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(listMap.get(ThirdWmsTypeEnum.QMWMS.getCode()))) {
            kcykBusinessLogicForJw(listMap.get(ThirdWmsTypeEnum.QMWMS.getCode()));
        }
        if (CollectionUtils.isNotEmpty(listMap.get(ThirdWmsTypeEnum.FLWMS.getCode()))) {
            kcykBusinessLogicForFl(listMap.get(ThirdWmsTypeEnum.FLWMS.getCode()));
        }
    }

    /**
     * 巨沃跨仓移库业务处理
     *
     * @param kcykList
     */
    @Transactional(rollbackFor = Throwable.class)
    public void kcykBusinessLogicForJw(List<SgBWmsToInventoryChangeNoticeTask> kcykList) {
        /*根据业务单号分组*/
        Map<String, List<SgBWmsToInventoryChangeNoticeTask>> wmsCodeMap = kcykList.stream().collect(Collectors.groupingBy(
                SgBWmsToInventoryChangeNoticeTask::getWmsBillCode));
        for (String key : wmsCodeMap.keySet()) {
            List<SgBWmsToInventoryChangeNoticeTask> tasks = wmsCodeMap.get(key);
            try {
                //条件记录的“变化量”汇总是否等于0
                Integer totQtyChang = tasks.stream().map(SgBWmsToInventoryChangeNoticeTask::getQtyChange).reduce(Integer::sum).orElse(NumberUtils.INTEGER_ZERO);
                if (!NumberUtils.INTEGER_ZERO.equals(totQtyChang)) {
                    updatebatchFail(tasks, "WMS单号[" + key + "]出入库不一致，不满足跨仓移库条件！");
                    continue;
                }
                //“仓库编码”除重后记录是否等于2
                Map<String, List<SgBWmsToInventoryChangeNoticeTask>> warehouseCodeGroupingByMap = tasks.stream().collect(Collectors.groupingBy(SgBWmsToInventoryChangeNoticeTask::getWarehouseCode));
                if (warehouseCodeGroupingByMap.keySet().size() != 2) {
                    updatebatchFail(tasks, "WMS单号[" + tasks.get(0).getWmsBillCode() + "]的涉及仓库个数，不满足跨仓移库条件!");
                    continue;
                }

                //"仓库编码"是否存在实体仓档案
                List<String> warehouseCodeList = new ArrayList<>(warehouseCodeGroupingByMap.keySet());
                Map<String, SgCpCPhyWarehouse> warehouseCodeMap = WmsTaskUtils.getSgCpPhyWarehouseByWarehouseCode(warehouseCodeList);
                JSONArray storeError = new JSONArray();
                for (String warehouseCodeGroupingByMapKey : warehouseCodeGroupingByMap.keySet()) {
                    if (!warehouseCodeMap.containsKey(warehouseCodeGroupingByMapKey)) {
                        storeError.add(warehouseCodeGroupingByMapKey);
                    }
                }

                if (storeError.size() > 0) {
                    updatebatchFail(tasks, "仓库编码" + storeError.toString() + "在【实体仓档案】中不存在！");
                    continue;
                }

                //itemCode是否存在"条码档案中"
                Map<String, List<SgBWmsToInventoryChangeNoticeTask>> itemCodeMap = tasks.stream().collect(Collectors.groupingBy(
                        SgBWmsToInventoryChangeNoticeTask::getPsCProEcode));
                List<String> itemCodeList = new ArrayList<>(itemCodeMap.keySet());
                //对比条码
                JSONArray array = WmsTaskUtils.compareToSku(itemCodeList);
                if (array.size() > 0) {
                    updatebatchFail(tasks, "商品编码" + array + "在【商品SKU】中不存在！");
                    continue;
                }

                SgBWmsToInventoryChangeNoticeService inventoryChangeNoticeService =
                        ApplicationContextHandle.getBean(SgBWmsToInventoryChangeNoticeService.class);
                inventoryChangeNoticeService.saveAndSubmit(tasks, warehouseCodeMap);
            } catch (Exception e) {
                log.error("跨仓移库转化失败，转化对象:{},异常信息：{}", JSON.toJSONString(tasks), Throwables.getStackTraceAsString(e));
                updatebatchFail(tasks, e.getMessage());
            }
        }
    }

    /**
     * 富勒跨仓移库业务处理
     *
     * @param kcykList
     */
    @Transactional(rollbackFor = Throwable.class)
    public void kcykBusinessLogicForFl(List<SgBWmsToInventoryChangeNoticeTask> kcykList) {
        /*根据业务单号分组*/
        Map<String, List<SgBWmsToInventoryChangeNoticeTask>> wmsCodeMap = kcykList.stream().collect(Collectors.groupingBy(
                SgBWmsToInventoryChangeNoticeTask::getWmsBillCode));
        for (String key : wmsCodeMap.keySet()) {
            List<SgBWmsToInventoryChangeNoticeTask> tasks = wmsCodeMap.get(key);
            try {
                //条件记录的“变化量”汇总是否等于0
                Integer totQtyChang = tasks.stream().map(SgBWmsToInventoryChangeNoticeTask::getQtyChange)
                        .reduce(Integer::sum).orElse(NumberUtils.INTEGER_ZERO);
                if (!NumberUtils.INTEGER_ZERO.equals(totQtyChang)) {
                    updatebatchFail(tasks, "WMS单号[" + key + "]出入库不一致，不满足跨仓移库条件！");
                    continue;
                }
                //“仓库编码”除重后记录是否等于1
                Map<String, List<SgBWmsToInventoryChangeNoticeTask>> warehouseCodeGroupingByMap =
                        tasks.stream().collect(Collectors.groupingBy(SgBWmsToInventoryChangeNoticeTask::getWarehouseCode));
                if (warehouseCodeGroupingByMap.keySet().size() != 2) {
                    updatebatchFail(tasks, "WMS单号[" + tasks.get(0).getWmsBillCode() + "]的涉及仓库个数，不满足跨仓移库条件!");
                    continue;
                }
                String message = tasks.get(0).getMessage();
                if (StringUtils.isEmpty(message)) {
                    updatebatchFail(tasks, "WMS单号[" + tasks.get(0).getWmsBillCode() + "]报文为空!");
                    continue;
                }
                //获取库存地点
                String storageLocation = null;
                String inStorageLocation = null;
                JSONObject request = JSONObject.parseObject(message);
                if (request != null && request.getJSONObject("extendProps") != null) {
                    storageLocation = request.getJSONObject("extendProps").getString("storageLocation");
                    inStorageLocation = request.getJSONObject("extendProps").getString("inStorageLocation");
                }
                if (StringUtils.isEmpty(storageLocation) || StringUtils.isEmpty(inStorageLocation)) {
                    updatebatchFail(tasks, "WMS单号[" + tasks.get(0).getWmsBillCode() + "]报文中库存地点为空!");
                    continue;
                }

                //"仓库编码"是否存在实体仓档案
                List<String> warehouseCodeList = new ArrayList<>(warehouseCodeGroupingByMap.keySet());
                Map<String, SgCpCPhyWarehouse> warehouseCodeMap = WmsTaskUtils.getSgCpPhyWarehouseByCode(warehouseCodeList);
                JSONArray storeError = new JSONArray();
                if (!warehouseCodeMap.containsKey(storageLocation)) {
                    storeError.add(storageLocation);
                }
                if (!warehouseCodeMap.containsKey(inStorageLocation)) {
                    storeError.add(inStorageLocation);
                }
                if (storeError.size() > 0) {
                    updatebatchFail(tasks, "仓库编码" + storeError + "在【实体仓档案】中不存在！");
                    continue;
                }

                //itemCode是否存在"条码档案中"
                Map<String, List<SgBWmsToInventoryChangeNoticeTask>> itemCodeMap = tasks.stream().collect(Collectors.groupingBy(
                        SgBWmsToInventoryChangeNoticeTask::getPsCProEcode));
                List<String> itemCodeList = new ArrayList<>(itemCodeMap.keySet());
                //对比条码
                JSONArray array = WmsTaskUtils.compareToSku(itemCodeList);
                if (array.size() > 0) {
                    updatebatchFail(tasks, "商品编码" + array + "在【商品SKU】中不存在！");
                    continue;
                }

                SgBWmsToInventoryChangeNoticeService inventoryChangeNoticeService =
                        ApplicationContextHandle.getBean(SgBWmsToInventoryChangeNoticeService.class);
                inventoryChangeNoticeService.saveAndSubmitForFl(tasks, warehouseCodeMap);
            } catch (Exception e) {
                log.warn("跨仓移库转化失败，转化对象:{},异常信息：{}", JSON.toJSONString(tasks), Throwables.getStackTraceAsString(e));
                updatebatchFail(tasks, e.getMessage());
            }
        }
    }

    /**
     * 跨仓移库保存提交（巨沃）
     *
     * @param tasks
     * @param warehouseCodeMap
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAndSubmit(List<SgBWmsToInventoryChangeNoticeTask> tasks, Map<String, SgCpCPhyWarehouse> warehouseCodeMap) {
        List<String> redisKey = new ArrayList<>();
        try {
            //新增并提交逻辑调拨单
            SgBStoTransferBillSaveRequest saveRequest = buildTransferRequest(tasks, warehouseCodeMap);
            if (saveRequest != null) {
                ValueHolderV14<SgR3BaseResult> v14 = transferSaveAndSubmitService.saveAndSubmit(saveRequest);
                log.info(LogUtil.format("跨仓移库-新增并提交逻辑调拨单结果：{}",
                        "SgBWmsToInventoryChangeNoticeService.saveAndSubmit"), JSONObject.toJSONString(v14));
                if (!v14.isOK()) {
                    AssertUtils.logAndThrow(v14.getMessage());
                }
                SgR3BaseResult data = v14.getData();
                if (data != null && data.getDataJo() != null) {
                    JSONArray redisFtpKey = data.getDataJo().getJSONArray("redisFtpKey");
                    log.info(LogUtil.format("跨仓移库key:{}", "跨仓移库"), JSONObject.toJSONString(redisFtpKey));
                    if (redisFtpKey != null && redisFtpKey.size() > 0) {
                        redisKey.addAll(redisFtpKey.toJavaList(String.class));
                    }
                    log.info(LogUtil.format("跨仓移库-新增并提交逻辑调拨单失败-库存回滚:{}",
                            "SgBWmsToInventoryChangeNoticeService.saveAndSubmit"), JSONObject.toJSONString(redisKey));
                }

                /*新增并提交逻辑出库单*/
                saveAndSubmitOutResult(saveRequest, tasks);

                tasks.forEach(this::updateSuccess);
            }
        } catch (Exception e) {
            StorageBasicUtils.rollbackStorage(redisKey, R3SystemUserResource.getSystemRootUser());
            AssertUtils.logAndThrowException("跨仓移库异常：", e, R3SystemUserResource.getSystemRootUser().getLocale());
        }
    }

    /**
     * 跨仓移库保存提交（富勒）
     *
     * @param tasks
     * @param warehouseCodeMap
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAndSubmitForFl(List<SgBWmsToInventoryChangeNoticeTask> tasks, Map<String, SgCpCPhyWarehouse> warehouseCodeMap) {
        List<String> redisKey = new ArrayList<>();
        try {
            //新增并提交逻辑调拨单
            SgBStoTransferBillSaveRequest saveRequest = buildTransferRequestForFl(tasks, warehouseCodeMap);
            if (saveRequest != null) {
                ValueHolderV14<SgR3BaseResult> v14 = transferSaveAndSubmitService.saveAndSubmit(saveRequest);
                log.info(LogUtil.format("跨仓移库-新增并提交逻辑调拨单结果：{}",
                        "SgBWmsToInventoryChangeNoticeService.saveAndSubmit"), JSONObject.toJSONString(v14));
                if (!v14.isOK()) {
                    AssertUtils.logAndThrow(v14.getMessage());
                }
                SgR3BaseResult data = v14.getData();
                if (data != null && data.getDataJo() != null) {
                    JSONArray redisFtpKey = data.getDataJo().getJSONArray("redisFtpKey");
                    log.info(LogUtil.format("跨仓移库key:{}", "跨仓移库"), JSONObject.toJSONString(redisFtpKey));
                    if (redisFtpKey != null && redisFtpKey.size() > 0) {
                        redisKey.addAll(redisFtpKey.toJavaList(String.class));
                    }
                    log.info(LogUtil.format("跨仓移库-新增并提交逻辑调拨单失败-库存回滚:{}",
                            "SgBWmsToInventoryChangeNoticeService.saveAndSubmit"), JSONObject.toJSONString(redisKey));
                }

                /*新增并提交逻辑出库单*/
                saveAndSubmitOutResult(saveRequest, tasks);

                tasks.forEach(this::updateSuccess);
            }
        } catch (Exception e) {
            StorageBasicUtils.rollbackStorage(redisKey, R3SystemUserResource.getSystemRootUser());
            String error = "";
            if (SgConstantsIF.BILL_TYPE_KCYK.equals(tasks.get(0).getBillType())) {
                error = "跨仓移库异常：";
            }
            if (SgConstantsIF.BILL_TYPE_BWQZC.equals(tasks.get(0).getBillType())) {
                error = "保温期转储异常：";
            }
            AssertUtils.logAndThrowException(error, e, R3SystemUserResource.getSystemRootUser().getLocale());
        }
    }

    private void saveAndSubmitOutResult(SgBStoTransferBillSaveRequest transferBillSaveRequest, List<SgBWmsToInventoryChangeNoticeTask> tasks) {
        SgBStoTransferSaveRequest transferSaveRequest = transferBillSaveRequest.getTransferSaveRequest();
        SgBStoOutNotices notices = noticesMapper.selectBySourceBillNo(transferSaveRequest.getSourceBillNo());
        AssertUtils.notNull(notices, "出库通知单未找到！请检查是否生成！");

        List<SgBWmsToInventoryChangeNoticeTask> itemTaskList = tasks.stream().filter(x -> x.getQtyChange() > 0).collect(Collectors.toList());

        SgBStoOutResultBillSaveRequest request = new SgBStoOutResultBillSaveRequest();


        SgBStoOutResultSaveRequest saveRequest = new SgBStoOutResultSaveRequest();
        saveRequest.setIsLast(R3CommonResultConstants.VALUE_Y);
        saveRequest.setSourceBillId(notices.getSourceBillId());
        saveRequest.setSourceBillType(notices.getSourceBillType());
        saveRequest.setSourceBillNo(notices.getSourceBillNo());
        saveRequest.setSourceBillDate(notices.getSourceBillDate());
        saveRequest.setSgBStoOutNoticesId(notices.getId());
        saveRequest.setSgBStoOutNoticesNo(notices.getBillNo());
        saveRequest.setOutTime(itemTaskList.get(0).getChangeTime());
        saveRequest.setLogisticNumber(notices.getLogisticNumber());
        saveRequest.setRemark("跨仓移库");
        saveRequest.setCpCStoreId(transferSaveRequest.getSenderStoreId());
        saveRequest.setCpCStoreEcode(transferSaveRequest.getSenderStoreEcode());
        saveRequest.setCpCStoreEname(transferSaveRequest.getSenderStoreEname());
        saveRequest.setWmsBillNo(notices.getSourceBillNo());

        List<SgBStoOutResultItemSaveRequest> itemSaveRequestList = new ArrayList<>();
        itemTaskList.forEach(task -> {
            SgBStoOutResultItemSaveRequest itemSaveRequest = new SgBStoOutResultItemSaveRequest();
            String produceDate = StringUtils.isEmpty(task.getBatchCode()) || SgConstantsIF.DEFAULT_PRODUCE_DATE.equals(task.getBatchCode()) ? SgConstantsIF.DEFAULT_PRODUCE_DATE :
                    DateUtils.formatSync8(DateUtils.parseSync8(task.getBatchCode(), DateUtils.PATTERN_DATE), DateUtils.DATE_PATTERN);
            itemSaveRequest.setProduceDate(produceDate);
            itemSaveRequest.setQty(BigDecimal.valueOf(task.getQtyChange()));
            itemSaveRequest.setId(-1L);
            itemSaveRequest.setPsCSkuEcode(task.getPsCProEcode());
            itemSaveRequestList.add(itemSaveRequest);
        });
        CommonCacheValUtils.setSkuInfoByCodeAndProduce(itemSaveRequestList);

        request.setSgBStoOutNoticesId(notices.getId());
        request.setLoginUser(R3SystemUserResource.getSystemRootUser());
        request.setObjId(-1L);
        request.setOutResultSaveRequest(saveRequest);
        request.setOutItemResultSaveRequestList(itemSaveRequestList);
        SgBStoOutResultSaveAndSubmitService sgStoInResultSaveAndSubmitService = ApplicationContextHandle.getBean(SgBStoOutResultSaveAndSubmitService.class);
        List<SgBStoOutResultBillSaveRequest> requestList = new ArrayList<>();
        requestList.add(request);
        log.info(LogUtil.format("新增并提交逻辑出库单入参:{}", "saveAndAuditBillWithTrans"), JSONObject.toJSONString(requestList));
        ValueHolderV14 valueHolderV14 = sgStoInResultSaveAndSubmitService.saveAndAuditBillWithTrans(requestList);
        log.info(LogUtil.format("新增并提交逻辑出库单出参:{}", "saveAndAuditBillWithTrans"), JSONObject.toJSONString(valueHolderV14));
        AssertUtils.cannot(!valueHolderV14.isOK(), valueHolderV14.getMessage());

    }

    /**
     * 返回 逻辑调拨单新增request
     *
     * @param tasks            中间表数据
     * @param warehouseCodeMap 实体仓
     * @return SgBStoTransferBillSaveRequest
     */
    private SgBStoTransferBillSaveRequest buildTransferRequest(List<SgBWmsToInventoryChangeNoticeTask> tasks, Map<String, SgCpCPhyWarehouse> warehouseCodeMap) {
        log.info(LogUtil.format("逻辑调拨单参数构建入参:{}",
                "SgBWmsToInventoryChangeNoticeService.buildTransferRequest", JSONObject.toJSONString(tasks)));

        /*入*/
        List<SgBWmsToInventoryChangeNoticeTask> qtyMax = new ArrayList<>();
        /*出*/
        List<SgBWmsToInventoryChangeNoticeTask> qtyMin = new ArrayList<>();
        tasks.forEach(task -> {
            Integer qtyChange = task.getQtyChange();
            if (qtyChange > 0) {
                qtyMax.add(task);
            } else {
                qtyMin.add(task);
            }
        });

        if (CollectionUtils.isEmpty(qtyMax) || CollectionUtils.isEmpty(qtyMax)) {
            updatebatchFail(tasks, "新增逻辑调拨单缺少发/收货仓！");
            return null;
        }

        SgBStoTransferBillSaveRequest saveRequest = new SgBStoTransferBillSaveRequest();

        SgBStoTransferSaveRequest mainRequest = new SgBStoTransferSaveRequest();
        mainRequest.setBillNo(tasks.get(0).getWmsBillCode());
        mainRequest.setBillDate(new Date());
        mainRequest.setDrpBillType(SgStoreConstants.DRP_BILL_TYPE_TF);
        //写死虚拟
        mainRequest.setCpCTranwayAssignId(6L);

        SgCpCPhyWarehouse reviceWarehouse = warehouseCodeMap.get(qtyMax.get(0).getWarehouseCode());
        SgCpCPhyWarehouse sendWarehouse = warehouseCodeMap.get(qtyMin.get(0).getWarehouseCode());
        Map<Long, List<CpCStore>> reviceStoreMap = CommonCacheValUtils.getStoreInfoByPhyId(reviceWarehouse.getId());
        Map<Long, List<CpCStore>> sendStoreMap = CommonCacheValUtils.getStoreInfoByPhyId(sendWarehouse.getId());
        if (MapUtils.isEmpty(reviceStoreMap)) {
            updatebatchFail(tasks, "仓库编码" + qtyMax.get(0).getWarehouseCode() + "在实体仓对应逻辑仓不存在！");
            return null;
        }
        if (MapUtils.isEmpty(sendStoreMap)) {
            updatebatchFail(tasks, "仓库编码" + qtyMin.get(0).getWarehouseCode() + "在实体仓对应逻辑仓不存在！");
            return null;
        }

        //收货仓 ps:一头牛 实体仓跟逻辑仓关系1:1
        List<CpCStore> receiverStores = reviceStoreMap.get(reviceWarehouse.getId());
        //CpCStore cpCStore = receiverStores.get(0);
        //发货仓
        List<CpCStore> sendStores = sendStoreMap.get(sendWarehouse.getId());
        CpCStore cpCStore = sendStores.get(0);
        List<SgBWmsToInventoryChangeNoticeTask> collect = qtyMin.stream()
                .filter(p -> p.getQtyChange() < 0 && SgConstantsIF.STOCK_TYPE_GOODS.equals(p.getStockType())).collect(Collectors.toList());
        try {
            if (CollectionUtils.isNotEmpty(collect)) {
                sgWmsAutoReleaseOrderService.autoReleaseOrder(cpCStore, collect, R3SystemUserResource.getSystemRootUser());
            }
        } catch (Exception e) {
            log.error(LogUtil.format("异常:{}",
                    "SgWmsAutoReleaseOrderService.buildTransferRequest"), Throwables.getStackTraceAsString(e));
            updatebatchFail(tasks, e.getMessage());
            return null;
        }

        mainRequest.setReceiverStoreId(receiverStores.get(0).getId());
        mainRequest.setReceiverStoreEcode(receiverStores.get(0).getEcode());
        mainRequest.setReceiverStoreEname(receiverStores.get(0).getEname());
        mainRequest.setSenderStoreId(sendStores.get(0).getId());
        mainRequest.setSenderStoreEcode(sendStores.get(0).getEcode());
        mainRequest.setSenderStoreEname(sendStores.get(0).getEname());
        //自动出入库 自动确认
        mainRequest.setIsAutoOut(SgConstants.IS_ACTIVE_N);
        mainRequest.setIsAutoIn(SgConstants.IS_ACTIVE_Y);
        mainRequest.setIsAutoConfirm(SgConstants.IS_ACTIVE_Y);
        //来源单据信息
        mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_CROSS_WAREHOUSE_TRANSFER_ORDER);
        mainRequest.setSourceBillNo(tasks.get(0).getWmsBillCode());
        mainRequest.setRemark("由WMS库存异动接口，WMS单号[" + tasks.get(0).getWmsBillCode() + "]传入生成");

        saveRequest.setR3(Boolean.FALSE);
        saveRequest.setTransferSaveRequest(mainRequest);
        saveRequest.setItems(buildTransferItemRequest(qtyMax));
        saveRequest.setObjId(-1L);
        saveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());

        return saveRequest;
    }

    /**
     * 返回 逻辑调拨单新增request（富勒）
     *
     * @param tasks            中间表数据
     * @param warehouseCodeMap 实体仓
     * @return SgBStoTransferBillSaveRequest
     */
    private SgBStoTransferBillSaveRequest buildTransferRequestForFl(List<SgBWmsToInventoryChangeNoticeTask> tasks, Map<String, SgCpCPhyWarehouse> warehouseCodeMap) {
        log.info(LogUtil.format("逻辑调拨单参数构建入参:{}",
                "SgBWmsToInventoryChangeNoticeService.buildTransferRequest", JSONObject.toJSONString(tasks)));

        /*入*/
        List<SgBWmsToInventoryChangeNoticeTask> qtyMax = new ArrayList<>();
        /*出*/
        List<SgBWmsToInventoryChangeNoticeTask> qtyMin = new ArrayList<>();
        tasks.forEach(task -> {
            Integer qtyChange = task.getQtyChange();
            if (qtyChange > 0) {
                qtyMax.add(task);
            } else {
                qtyMin.add(task);
            }
        });

        if (CollectionUtils.isEmpty(qtyMax) || CollectionUtils.isEmpty(qtyMax)) {
            updatebatchFail(tasks, "新增逻辑调拨单缺少发/收货仓！");
            return null;
        }

        SgBStoTransferBillSaveRequest saveRequest = new SgBStoTransferBillSaveRequest();

        SgBStoTransferSaveRequest mainRequest = new SgBStoTransferSaveRequest();
        mainRequest.setBillNo(tasks.get(0).getWmsBillCode());
        mainRequest.setBillDate(new Date());
        mainRequest.setDrpBillType(SgStoreConstants.DRP_BILL_TYPE_TF);
        //写死虚拟
        mainRequest.setCpCTranwayAssignId(6L);

        String message = tasks.get(0).getMessage();
        if (StringUtils.isEmpty(message)) {
            updatebatchFail(tasks, "WMS单号[" + tasks.get(0).getWmsBillCode() + "]报文为空!");
            return null;
        }
        //获取库存地点
        String storageLocation = null;
        String inStorageLocation = null;
        JSONObject request = JSONObject.parseObject(message);
        if (request != null && request.getJSONObject("extendProps") != null) {
            storageLocation = request.getJSONObject("extendProps").getString("storageLocation");
            inStorageLocation = request.getJSONObject("extendProps").getString("inStorageLocation");
        }
        if (StringUtils.isEmpty(storageLocation) || StringUtils.isEmpty(inStorageLocation)) {
            updatebatchFail(tasks, "WMS单号[" + tasks.get(0).getWmsBillCode() + "]报文中库存地点为空!");
            return null;
        }
        SgCpCPhyWarehouse reviceWarehouse = warehouseCodeMap.get(inStorageLocation);
        SgCpCPhyWarehouse sendWarehouse = warehouseCodeMap.get(storageLocation);
        Map<Long, List<CpCStore>> reviceStoreMap = CommonCacheValUtils.getStoreInfoByPhyId(reviceWarehouse.getId());
        Map<Long, List<CpCStore>> sendStoreMap = CommonCacheValUtils.getStoreInfoByPhyId(sendWarehouse.getId());
        if (MapUtils.isEmpty(reviceStoreMap)) {
            updatebatchFail(tasks, "仓库编码" + qtyMax.get(0).getWarehouseCode() + "在实体仓对应逻辑仓不存在！");
            return null;
        }
        if (MapUtils.isEmpty(sendStoreMap)) {
            updatebatchFail(tasks, "仓库编码" + qtyMin.get(0).getWarehouseCode() + "在实体仓对应逻辑仓不存在！");
            return null;
        }

        //收货仓 ps:一头牛 实体仓跟逻辑仓关系1:1
        List<CpCStore> receiverStores = reviceStoreMap.get(reviceWarehouse.getId());
        //CpCStore cpCStore = receiverStores.get(0);
        //发货仓
        List<CpCStore> sendStores = sendStoreMap.get(sendWarehouse.getId());
        CpCStore cpCStore = sendStores.get(0);
        List<SgBWmsToInventoryChangeNoticeTask> collect = qtyMin.stream()
                .filter(p -> p.getQtyChange() < 0 && SgConstantsIF.STOCK_TYPE_GOODS.equals(p.getStockType())).collect(Collectors.toList());
        try {
            if (CollectionUtils.isNotEmpty(collect)) {
                sgWmsAutoReleaseOrderService.autoReleaseOrder(cpCStore, collect, R3SystemUserResource.getSystemRootUser());
            }
        } catch (Exception e) {
            log.error(LogUtil.format("异常:{}",
                    "SgWmsAutoReleaseOrderService.buildTransferRequest"), Throwables.getStackTraceAsString(e));
            updatebatchFail(tasks, e.getMessage());
            return null;
        }

        mainRequest.setReceiverStoreId(receiverStores.get(0).getId());
        mainRequest.setReceiverStoreEcode(receiverStores.get(0).getEcode());
        mainRequest.setReceiverStoreEname(receiverStores.get(0).getEname());
        mainRequest.setSenderStoreId(sendStores.get(0).getId());
        mainRequest.setSenderStoreEcode(sendStores.get(0).getEcode());
        mainRequest.setSenderStoreEname(sendStores.get(0).getEname());
        //自动出入库 自动确认
        mainRequest.setIsAutoOut(SgConstants.IS_ACTIVE_N);
        mainRequest.setIsAutoIn(SgConstants.IS_ACTIVE_Y);
        mainRequest.setIsAutoConfirm(SgConstants.IS_ACTIVE_Y);
        //来源单据信息
        mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_CROSS_WAREHOUSE_TRANSFER_ORDER);
        if (SgConstantsIF.BILL_TYPE_BWQZC.equals(tasks.get(0).getBillType())) {
            mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_CROSS_WAREHOUSE_INSULATION_ORDER);
        }
        mainRequest.setSourceBillNo(tasks.get(0).getWmsBillCode());
        mainRequest.setRemark("由WMS库存异动接口，WMS单号[" + tasks.get(0).getWmsBillCode() + "]传入生成");

        saveRequest.setR3(Boolean.FALSE);
        saveRequest.setTransferSaveRequest(mainRequest);
        saveRequest.setItems(buildTransferItemRequest(qtyMax));
        saveRequest.setObjId(-1L);
        saveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());

        return saveRequest;
    }


    /**
     * 返回 逻辑调拨单新增明细
     *
     * @param qtyMax 大于0的中间表数据
     * @return SgBStoTransferItemSaveRequest
     */
    private List<SgBStoTransferItemSaveRequest> buildTransferItemRequest(List<SgBWmsToInventoryChangeNoticeTask> qtyMax) {
        List<SgBStoTransferItemSaveRequest> itemSaveRequests = new ArrayList<>();
        Map<String, List<SgBWmsToInventoryChangeNoticeTask>> itemCodeMap = qtyMax.stream().collect(Collectors.groupingBy(SgBWmsToInventoryChangeNoticeTask::getPsCProEcode));
        for (String key : itemCodeMap.keySet()) {
            SgBStoTransferItemSaveRequest itemSaveRequest = new SgBStoTransferItemSaveRequest();
            List<SgBWmsToInventoryChangeNoticeTask> taskList = itemCodeMap.get(key);
            Integer totQtyChang = taskList.stream().map(SgBWmsToInventoryChangeNoticeTask::getQtyChange).reduce(Integer::sum).orElse(NumberUtils.INTEGER_ZERO);
            itemSaveRequest.setQty(BigDecimal.valueOf(totQtyChang));
            itemSaveRequest.setId(-1L);
            itemSaveRequest.setPsCSkuEcode(taskList.get(0).getPsCProEcode());
            itemSaveRequests.add(itemSaveRequest);
        }
        return itemSaveRequests;
    }


    /**
     * 批量更新错误信息
     *
     * @param tasks        入参
     * @param failedReason 错误信息
     */
    private void updatebatchFail(List<SgBWmsToInventoryChangeNoticeTask> tasks, String failedReason) {
        tasks.forEach(j -> updateFail(j, failedReason));
    }

    /**
     * 更新错误信息
     *
     * @param sgWmsToOrderOutStockResults 中间表数据
     * @param failedReason                失败原因
     */
    private void updateFail(SgBWmsToInventoryChangeNoticeTask sgWmsToOrderOutStockResults, String failedReason) {
        SgBWmsToInventoryChangeNoticeTask update = new SgBWmsToInventoryChangeNoticeTask();
        update.setId(sgWmsToOrderOutStockResults.getId());
        int failedConut = Optional.ofNullable(sgWmsToOrderOutStockResults.getFailedCount()).orElse(0) + 1;
        update.setFailedCount(failedConut);
        update.setFailedReason(failedReason.length() > 500 ? failedReason.substring(0, 500) : failedReason);
        update.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED);
        taskMapper.updateById(update);
    }

    /**
     * 更新成功信息
     *
     * @param inTask 中间表数据
     */
    private void updateSuccess(SgBWmsToInventoryChangeNoticeTask inTask) {
        SgBWmsToInventoryChangeNoticeTask update = new SgBWmsToInventoryChangeNoticeTask();
        update.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS);
        update.setTransformationData(new Date());
        taskMapper.update(update, new LambdaUpdateWrapper<SgBWmsToInventoryChangeNoticeTask>()
                .set(SgBWmsToInventoryChangeNoticeTask::getFailedReason, null)
                .eq(SgBWmsToInventoryChangeNoticeTask::getId, inTask.getId()));
    }


    /**
     * 根据WMS编号更新失败次数
     * R3
     *
     * @param session session
     * @return ValueHolder
     */
    public ValueHolder batchUpdateByWmsBillNo(QuerySession session) {

        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);

        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "更新成功！");

        if (request == null) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "入参为空！");
            return valueHolder;
        }

        log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService batchUpdateByWmsBillNo.request:{}",
                "SgBWmsToInventoryChangeNoticeService.batchUpdateByWmsBillNo"), JSONObject.toJSONString(request));

        List<Long> ids = request.getIds();


        return batchUpdateByWmsBillNo(ids);

    }

    /**
     * 根据WMS编号更新失败次数
     *
     * @param ids ids
     * @return ValueHolder
     */
    public ValueHolder batchUpdateByWmsBillNo(List<Long> ids) {

        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "更新成功！");

        List<SgBWmsToInventoryChangeNoticeTask> tasks = taskMapper.selectList(new LambdaQueryWrapper<SgBWmsToInventoryChangeNoticeTask>()
                .in(SgBWmsToInventoryChangeNoticeTask::getId, ids));
        if (CollectionUtils.isEmpty(tasks) || tasks.size() != ids.size()) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "查询记录异常:未查询到记录或查询记录数量与选中数据数量不匹配！");
            return valueHolder;
        }

        List<SgBWmsToInventoryChangeNoticeTask> successList = tasks.stream().filter(x ->
                SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS == x.getTransformStatus()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(successList)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "选中记录中包含已转化记录，不允许重新转化！");
            return valueHolder;
        }


        Map<String, List<SgBWmsToInventoryChangeNoticeTask>> taskMap = tasks.stream().collect(Collectors.groupingBy(SgBWmsToInventoryChangeNoticeTask::getWmsBillCode));

        List<String> billNoList = new ArrayList<>(taskMap.keySet());

        SgBWmsToInventoryChangeNoticeTask update = new SgBWmsToInventoryChangeNoticeTask();
        update.setFailedCount(0);
        int count = taskMapper.update(update, new LambdaUpdateWrapper<SgBWmsToInventoryChangeNoticeTask>()
                .set(SgBWmsToInventoryChangeNoticeTask::getFailedReason, null)
                .in(SgBWmsToInventoryChangeNoticeTask::getWmsBillCode, billNoList));


        log.info(LogUtil.format("SgBWmsToInventoryChangeNoticeService batchUpdateByWmsBillNo.ValueHolder:{},更新WMS编号:{},更新记录:{}",
                        "SgBWmsToInventoryChangeNoticeService.ValueHolder"),
                JSONObject.toJSONString(valueHolder), JSONObject.toJSONString(billNoList), count);

        return valueHolder;
    }
}
