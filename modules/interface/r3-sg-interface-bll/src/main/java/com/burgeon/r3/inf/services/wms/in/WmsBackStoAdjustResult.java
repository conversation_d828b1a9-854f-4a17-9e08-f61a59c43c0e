package com.burgeon.r3.inf.services.wms.in;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.store.in.SgBWmsToStoAdjustResult;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.in.SgBWmsToStoAdjustResultMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description: 库存盘点单回传
 *
 * @Author: guo.kw
 * @Since: 2022/7/15
 * create at: 2022/7/15 11:00
 */
@Slf4j
@Component
public class WmsBackStoAdjustResult {

    @Autowired
    private SgBWmsToStoAdjustResultMapper sgBWmsToStoAdjustResultMapper;

    @Autowired
    private CpCPhyWarehouseMapper cpCPhyWarehouseMapper;

    public ValueHolderV14<String> apiProcess(String msg) {
        log.info(LogUtil.format("库存盘点单回传，报文：{}",
                "WmsBackStoAdjustResult.apiProcess"), msg);
        CusRedisTemplate<Object, Object> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();
        String lockKsy = SgConstants.SG_BILL_LOCK_WMSRETURN;

        try {
            JSONObject request = JSONObject.parseObject(msg);
            // 仓库编码
            String warehouseCode = request.getString("warehouseCode");
            // WMS单据编号
            String wmsBillCode = request.getString("checkOrderCode");
            //外部业务编码 用于判断唯一
            String outBizCode = request.getString("outBizCode");
            if (StringUtils.isEmpty(outBizCode)) {
                throw new NDSException("外部业务编码不能为空");
            }
            lockKsy += outBizCode;

            Boolean ifAbsent = redisMasterTemplate.opsForValue().setIfAbsent(lockKsy, "OK");
            SgBWmsToStoAdjustResult sgBWmsToStoAdjustResult = sgBWmsToStoAdjustResultMapper.selectOne(new LambdaQueryWrapper<SgBWmsToStoAdjustResult>()
                    .eq(SgBWmsToStoAdjustResult::getWmsBillNo, wmsBillCode)
                    .eq(SgBWmsToStoAdjustResult::getIsactive, "Y"));

            SgCpCPhyWarehouse cpCPhyWarehouse = null;
            if (!StringUtils.isEmpty(warehouseCode)) {
                cpCPhyWarehouse = cpCPhyWarehouseMapper.getCpCPhyWarehouseName(warehouseCode);
            }

            if (Objects.nonNull(sgBWmsToStoAdjustResult) || ifAbsent == null || !ifAbsent) {
                log.error(LogUtil.format("库存盘点单WMS回传重复.重复对象：{}，请求报文：{}", "WmsBackStoAdjustResult.apiProcess"),
                        JSON.toJSONString(sgBWmsToStoAdjustResult), msg);
            } else {
                redisMasterTemplate.expire(lockKsy, 30, TimeUnit.SECONDS);
                SgBWmsToStoAdjustResult createObj = new SgBWmsToStoAdjustResult();

                createObj.setId(ModelUtil.getSequence(SgConstants.SG_B_WMS_TO_STO_ADJUST_RESULT));
                createObj.setWarehouseCode(warehouseCode);
                createObj.setWmsBillNo(wmsBillCode);
                createObj.setOutBizCode(outBizCode);

                if (cpCPhyWarehouse != null) {
                    createObj.setWmsWarehouseType(cpCPhyWarehouse.getWmsType());
                    /*京云仓特殊处理效期*/
                    if (ThirdWmsTypeEnum.JDWMS.getCode().equals(cpCPhyWarehouse.getWmsType())) {
                        JSONArray items = request.getJSONArray("items");
                        JSONArray newItems = new JSONArray();
                        if (items != null && items.size() > 0) {
                            for (int i = 0; i < items.size(); i++) {
                                JSONObject jsonObject = items.getJSONObject(i);
                                if (!StringUtils.isEmpty(jsonObject.getString("productDate"))) {
                                    jsonObject.put("batchCode", jsonObject.getString("productDate"));
                                }
                                newItems.add(jsonObject);
                            }
                        }
                        request.put("items", newItems);
                    }
                    /*富勒仓库编码取库存地点*/
                    if (ThirdWmsTypeEnum.FLWMS.getCode().equals(cpCPhyWarehouse.getWmsType())) {
                        JSONObject extendProps = request.getJSONObject("extendProps");
                        if (extendProps != null) {
                            String storageLocation = extendProps.getString("storageLocation");
                            if (StringUtils.isNotEmpty(storageLocation)) {
                                createObj.setWarehouseCode(storageLocation);
                            }
                        }
                    }
                }
                createObj.setMessage(request.toJSONString());
                createObj.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT);
                createObj.setFailedCount(NumberUtils.INTEGER_ZERO);
                createObj.setIsactive(SgConstants.IS_ACTIVE_Y);
                StorageUtils.setBModelDefalutData(createObj, R3SystemUserResource.getSystemRootUser());
                sgBWmsToStoAdjustResultMapper.insert(createObj);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("库存盘点单WMS回传异常.异常信息：{}", "WmsBackStoAdjustResult.apiProcess"),
                    Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        } finally {
            redisMasterTemplate.delete(lockKsy);
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("success"));
    }
}
