package com.burgeon.r3.inf.filter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.inf.mapper.SgCSaSelectionMapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.inf.common.SgInfConstants;
import com.burgeon.r3.sg.inf.model.dto.SgCSaSelectionDto;
import com.burgeon.r3.sg.inf.model.table.SgCSaSelection;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ps.api.table.PsCSku;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2021/11/29 17:28
 * 配销仓选款结果表
 */
@Slf4j
@Component
public class SgCSaSelectionSaveFilter extends BaseSingleFilter<SgCSaSelectionDto> {

    @Autowired
    private SgCSaSelectionMapper saSelectionMapper;

    @Override
    public String getFilterMsgName() {
        return "配销仓选款结果";
    }

    @Override
    public Class<?> getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgCSaSelectionDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("保存成功", loginUser.getLocale()));
        Long objId = mainObject.getId();
        Long psCSkuId = mainObject.getPsCSkuId();
        Long saStoreId = mainObject.getSgCSaStoreId();
        Long psCProId =  mainObject.getPsCProId();

        if(!ObjectUtils.isEmpty(psCProId)){
            List<PsCSku> skuList = CommonCacheValUtils.getSkuInfoListByProId(psCProId);
            if (CollectionUtils.isEmpty(skuList)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("商品编码无对应条码信息");
                return v14;
            }
//            mainObject.setPsCSkuId(skuInfo.getId());
//            mainObject.setPsCSkuEcode(skuInfo.getSkuEcode());
//            mainObject.setPsCProId(skuInfo.getPsCProId());
//            mainObject.setPsCProEcode(skuInfo.getPsCProEcode());
//            mainObject.setPsCProEname(skuInfo.getPsCProEname());
        }

        Long SaShareId = null;
        if(!ObjectUtils.isEmpty(saStoreId)){
            SgCSaStore storeInfo = CommonCacheValUtils.getSaStore(saStoreId);
            if (storeInfo == null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("配销仓已不存在");
                return v14;
            }
            saStoreId = storeInfo.getSgCShareStoreId();
            mainObject.setSgCSaStoreId(storeInfo.getId());
            mainObject.setSgCSaStoreEcode(storeInfo.getEcode());
            mainObject.setSgCSaStoreEname(storeInfo.getEname());
        }

        if(!ObjectUtils.isEmpty(mainObject.getSgCShareStoreId())){
            SgCShareStore shareStore = CommonCacheValUtils.getShareStore(mainObject.getSgCShareStoreId());
            if(ObjectUtils.isEmpty(shareStore)){
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("聚合仓已不存在");
                return v14;
            }
            mainObject.setSgCShareStoreId(shareStore.getId());
            mainObject.setSgCShareStoreEcode(shareStore.getEcode());
            mainObject.setSgCShareStoreEname(shareStore.getEname());
        }
        SgCSaSelection old = mainObject;
        if (objId == null || objId < 1L) {

            if(ObjectUtils.isEmpty(mainObject.getStartTime()) || ObjectUtils.isEmpty(mainObject.getEndTime()) ||
                    ObjectUtils.isEmpty(mainObject.getShareType()) || ObjectUtils.isEmpty(mainObject.getShareRatio())){
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("开始时间、结束时间、共享维度、共享系数不能为空");
                return v14;
            }

            Integer count = saSelectionMapper.selectCount(new LambdaQueryWrapper<SgCSaSelection>()
                    .eq(SgCSaSelection::getPsCSkuId, psCSkuId)
                    .eq(SgCSaSelection::getSgCShareStoreId, mainObject.getSgCShareStoreId())
                    .eq(SgCSaSelection::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (count > 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("同一聚合仓+商品编码下只能存在一条记录");
                return v14;
            }

        }else {
            old = saSelectionMapper.selectById(mainObject.getId());
        }

        if(ObjectUtils.isEmpty(saStoreId)){
            saStoreId = old.getSgCShareStoreId();
        }

        Date startTime = Optional.ofNullable(mainObject.getStartTime()).orElse(old.getStartTime());
        Date endTime = Optional.ofNullable(mainObject.getEndTime()).orElse(old.getEndTime());

        if(startTime.compareTo(endTime) > 0){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("结束时间不能小于开始时间");
            return v14;
        }

        if(!saStoreId.equals(old.getSgCShareStoreId())){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("配销仓不属于该聚合仓");
            return v14;
        }

        Integer shareType = Optional.ofNullable(mainObject.getShareType()).orElse(old.getShareType());
        BigDecimal shareRatio = Optional.ofNullable(mainObject.getShareRatio()).orElse(old.getShareRatio());

        if(SgInfConstants.SA_SECTION_NUMBER.equals(shareType) &&
                BigDecimal.ZERO.compareTo(shareRatio) > 0){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("共享维度为【按数量】时，控制共享系数不能小于0");
            return v14;
        }

        if(SgInfConstants.SA_SECTION_RATIO.equals(shareType) &&
                (BigDecimal.ZERO.compareTo(shareRatio) > 0 ||
                        BigDecimal.ONE.compareTo(shareRatio) < 0)){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("共享维度为【按比例】时，控制共享系数不能大于1、或小于0");
            return v14;
        }

        return v14;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgCSaSelectionDto mainObject, User loginUser) {
        return null;
    }
}
