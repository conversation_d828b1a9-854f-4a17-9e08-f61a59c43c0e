package com.burgeon.r3.inf.services.sap;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.request.SgStorageLsProducePreoutItemRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageLsProducePreoutRequest;
import com.burgeon.r3.sg.basic.model.result.SgStorageLsProducePreoutItemResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageLsProducePreoutResult;
import com.burgeon.r3.sg.basic.services.SgStorageLsProducePreoutService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseBillItemBase;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustMainSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoWriteOffSaveRequest;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSaveService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lin yu
 * @date : 2022/6/20 下午4:00
 * @describe : 新增冲销单服务
 */
@Slf4j
@Component
public class SgBStoWriteOffSaveService {

    @Autowired
    private SgBStoAdjustSaveService saveService;

    @Autowired
    private SgStorageLsProducePreoutService sgStorageLsProducePreoutService;

    public ValueHolderV14<SgR3BaseResult> save(SgBStoWriteOffSaveRequest writeOffSaveRequest) {

        ValueHolderV14<SgR3BaseResult> v14 =
                new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

        try {

            SgBStoAdjustSaveRequest request = writeOffSaveRequest.getAdjustSaveRequest();

            if (log.isDebugEnabled()) {
                log.debug("Start SgBStoWriteOffSaveService.save request:{}",
                        JSONObject.toJSONString(writeOffSaveRequest));
            }

            checkParams(request);

            List<SgStorageLsProducePreoutItemResult> preoutItemResults = queryDatePreout(request);

            encapsulationData(request, preoutItemResults);

            v14 = saveService.save(request);

        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("库存调整单创建异常:" + Throwables.getStackTraceAsString(e));
            return v14;
        }

        return v14;
    }

    private List<SgStorageLsProducePreoutItemResult> queryDatePreout(SgBStoAdjustSaveRequest request) {

        SgBStoAdjustMainSaveRequest mainRequest = request.getMainRequest();
        List<SgBStoAdjustItemSaveRequest> items = request.getItems();

        if (SgConstantsIF.WRITE_OFF_Z102.equals(mainRequest.getChargeAgainstType()) ||
                SgConstantsIF.WRITE_OFF_ZP02.equals(mainRequest.getChargeAgainstType()) ||
                SgConstantsIF.WRITE_OFF_ZD02.equals(mainRequest.getChargeAgainstType())) {

            SgStorageLsProducePreoutRequest storageLsProducePreoutRequest = new SgStorageLsProducePreoutRequest();
            storageLsProducePreoutRequest.setProducePreoutType(SgConstants.SG_B_STO_PRODUCE_PREOUT_TYPE);

            Map<Long, String> skuIdAndEcodeMap = new HashMap<>(16);

            ArrayList<SgStorageLsProducePreoutItemRequest> itemRequestList = new ArrayList<>();

            for (SgBStoAdjustItemSaveRequest item : items) {

                SgStorageLsProducePreoutItemRequest storageLsProducePreoutItemRequest = new SgStorageLsProducePreoutItemRequest();
                storageLsProducePreoutItemRequest.setCpCStoreId(mainRequest.getCpCStoreId());
                storageLsProducePreoutItemRequest.setPsCSkuId(item.getPsCSkuId());
                storageLsProducePreoutItemRequest.setBeginProduceDate(item.getBeginProduceDate());
                storageLsProducePreoutItemRequest.setEndProduceDate(item.getEndProduceDate());
                storageLsProducePreoutItemRequest.setQty(item.getQty());

                itemRequestList.add(storageLsProducePreoutItemRequest);
                skuIdAndEcodeMap.put(item.getPsCSkuId(), item.getPsCSkuEcode());
            }

            storageLsProducePreoutRequest.setItemList(itemRequestList);

            if (log.isDebugEnabled()) {
                log.debug("Start SgBStoWriteOffSaveService.calcLsProducePreOut request:{}",
                        JSONObject.toJSONString(storageLsProducePreoutRequest));
            }

            ValueHolderV14<SgStorageLsProducePreoutResult> resultHolder
                    = sgStorageLsProducePreoutService.calcLsProducePreOut(storageLsProducePreoutRequest, request.getLoginUser());

            if (log.isDebugEnabled()) {
                log.debug("Finish SgBStoWriteOffSaveService.calcLsProducePreOut result:{}",
                        JSONObject.toJSONString(resultHolder));
            }

            AssertUtils.cannot(!resultHolder.isOK(), resultHolder.getMessage());

            SgStorageLsProducePreoutResult data = resultHolder.getData();
            AssertUtils.cannot(data == null, "获取生产日期占用计划失败！");

            List<SgStorageLsProducePreoutItemResult> outItemResultList = data.getOutItemResultList();

            String errorMessage = "";
            List<String> errorMessageList = new ArrayList<>();

            if (CollectionUtils.isNotEmpty(outItemResultList)) {

                for (SgStorageLsProducePreoutItemResult itemResult : outItemResultList) {
                    errorMessage = "逻辑仓：" + mainRequest.getCpCStoreEcode()
                            + "，条码：" + skuIdAndEcodeMap.get(itemResult.getPsCSkuId());
                    errorMessageList.add(errorMessage);
                }
            }

            AssertUtils.cannot(CollectionUtils.isNotEmpty(errorMessageList), "未获取到对应逻辑仓的可用库存！" + errorMessageList.toString());

            List<SgStorageLsProducePreoutItemResult> itemResultList = data.getItemResultList();
            AssertUtils.cannot(CollectionUtils.isEmpty(itemResultList), "未获取到对应逻辑仓的可用库存！");

            return itemResultList;
        }

        return null;
    }

    private void encapsulationData(SgBStoAdjustSaveRequest request,
                                   List<SgStorageLsProducePreoutItemResult> preoutItemResults) {
        SgBStoAdjustMainSaveRequest mainRequest = request.getMainRequest();
        List<SgBStoAdjustItemSaveRequest> itemSaveRequests = request.getItems();

        request.setObjId(-1L);
        mainRequest.setObjId(-1L);
        //单据类型
        mainRequest.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL);
        //来源单据类型：预留单（冲销）
        mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_CHARGE_AGAINST);
        //调整性质：冲销调整
        mainRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_ADJUST_PROP_WRITE_OFF);
        //是否传wms：是
        mainRequest.setIsPassWms(NumberUtils.INTEGER_ONE);
        //传wms状态：未传
        mainRequest.setWmsStatus(Integer.valueOf(String.valueOf(SgStoreConstants.WMS_UPLOAD_STATUTS_NO)));

        Map<Long, List<SgBStoAdjustItemSaveRequest>> collect = itemSaveRequests.stream().collect(Collectors.groupingBy(SgR3BaseBillItemBase::getPsCSkuId));

        if (CollectionUtils.isEmpty(preoutItemResults)) {

            for (SgBStoAdjustItemSaveRequest item : itemSaveRequests) {

                if (!SgConstantsIF.WRITE_OFF_Z552.equals(mainRequest.getChargeAgainstType())) {
                    item.setStorageType(SgConstantsIF.STOCK_TYPE_GOODS);
                }

                if (SgConstantsIF.WRITE_OFF_Z123.equals(mainRequest.getChargeAgainstType()) ||
                        SgConstantsIF.WRITE_OFF_Z262.equals(mainRequest.getChargeAgainstType()) ||
                        SgConstantsIF.WRITE_OFF_Z411.equals(mainRequest.getChargeAgainstType()) ||
                        SgConstantsIF.WRITE_OFF_Z202.equals(mainRequest.getChargeAgainstType()) ||
                        SgConstantsIF.WRITE_OFF_ZZ12.equals(mainRequest.getChargeAgainstType()) ||
                        SgConstantsIF.WRITE_OFF_ZZ14.equals(mainRequest.getChargeAgainstType()) ||
                        SgConstantsIF.WRITE_OFF_ZZ16.equals(mainRequest.getChargeAgainstType()) ||
                        SgConstantsIF.WRITE_OFF_ZZ18.equals(mainRequest.getChargeAgainstType()) ||
                        SgConstantsIF.WRITE_OFF_ZZ20.equals(mainRequest.getChargeAgainstType()) ||
                        SgConstantsIF.WRITE_OFF_ZZ22.equals(mainRequest.getChargeAgainstType()) ||
                        SgConstantsIF.WRITE_OFF_Z552.equals(mainRequest.getChargeAgainstType()) ||
                        SgConstantsIF.WRITE_OFF_ZZ64.equals(mainRequest.getChargeAgainstType())) {

                    item.setProduceDate(item.getBeginProduceDate());
                }
            }
        } else {

            //冲销类型=[Z102:收货入库-冲销]或[ZP02:生产入库-冲销]或[ZD02:调拨入库-冲销]需要按生产日期占用计划的返回条数封装参数

            ArrayList<SgBStoAdjustItemSaveRequest> stoAdjustItemSaveRequestList = new ArrayList<>();
            for (SgStorageLsProducePreoutItemResult preoutItemResult : preoutItemResults) {

                SgBStoAdjustItemSaveRequest stoAdjustItemSaveRequest = new SgBStoAdjustItemSaveRequest();
                List<SgBStoAdjustItemSaveRequest> itemSaveRequestsList = collect.get(preoutItemResult.getPsCSkuId());

                BeanUtils.copyProperties(itemSaveRequestsList.get(0), stoAdjustItemSaveRequest);
                stoAdjustItemSaveRequest.setQty(preoutItemResult.getQty().negate());
                stoAdjustItemSaveRequest.setProduceDate(preoutItemResult.getProduce());
                stoAdjustItemSaveRequest.setStorageType(SgConstantsIF.STOCK_TYPE_GOODS);

                stoAdjustItemSaveRequestList.add(stoAdjustItemSaveRequest);
            }

            request.setItems(stoAdjustItemSaveRequestList);

        }

    }

    private void checkParams(SgBStoAdjustSaveRequest request) {

        AssertUtils.notNull(request.getLoginUser(), "请登录！");

        SgBStoAdjustMainSaveRequest mainRequest = request.getMainRequest();
        List<SgBStoAdjustItemSaveRequest> items = request.getItems();

        AssertUtils.notNull(mainRequest, "主表信息不能为空！");
        AssertUtils.notNull(mainRequest.getCpCStoreEcode(), "逻辑仓不能为空！");
        AssertUtils.cannot(CollectionUtils.isEmpty(items), "明细信息不能为空！");

//        CpCStore store = CommonCacheValUtils.getStoreInfoByEcode(mainRequest.getCpCStoreEcode());
//        AssertUtils.notNull(store, "当前单据的逻辑仓信息不存在！");
//        mainRequest.setCpCStoreId(store.getId());
//        mainRequest.setCpCStoreEname(store.getEname());

//        CpCPhyWarehouse wareHouse = CommonCacheValUtils.getWareHouse(store.getId());
//        AssertUtils.notNull(wareHouse, "当前单据的实体仓信息不存在！");

//        AssertUtils.cannot(SgConstantsIF.QM_WMS.equals(wareHouse.getWmsType()), "冲销业务，调整店仓的WMS仓库类型不允许为京东云仓！");

        for (SgBStoAdjustItemSaveRequest item : items) {

            AssertUtils.cannot(StringUtils.isEmpty(item.getPsCSkuEcode()), "条码信息不能为空！");
        }

        CommonCacheValUtils.setSkuInfoBySkuCodeList(items);

    }
}
