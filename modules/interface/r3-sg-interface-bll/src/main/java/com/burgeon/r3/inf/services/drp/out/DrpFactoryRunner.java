package com.burgeon.r3.inf.services.drp.out;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/23 21:17
 */
@Slf4j
@Component
public class DrpFactoryRunner implements CommandLineRunner {


    private final DrpInterfaceFactory processorFactory;

    @Autowired
    public DrpFactoryRunner(DrpInterfaceFactory processorFactory) {
        this.processorFactory = processorFactory;
    }

    @Override
    public void run(String... args) {
        try {
            this.processorFactory.initialProcessorFactory();
        } catch (Exception e) {
            log.error("MqProcessorFactoryRunner.Error", e);
        }
    }
}
