package com.burgeon.r3.inf.services.ourui;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.inf.mapper.SgCSaSelectionMapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.inf.common.SgInfConstants;
import com.burgeon.r3.sg.inf.model.dto.SgCSaSelectionDto;
import com.burgeon.r3.sg.inf.model.table.SgCSaSelection;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.ps.api.table.PsCSku;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * className: SgCSaSelectionSaveService
 * description:选款结果表
 * <AUTHOR>
 * create: 2022-02-14
 * @since JDK 1.8
 */
@Component
@Slf4j
public class SgCSaSelectionSaveService {

    @Autowired
    private SgCSaSelectionMapper saSelectionMapper;


    public ValueHolderV14 save(SgCSaSelectionDto mainObject, User loginUser){

        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("保存成功", loginUser.getLocale()));
        Long objId = mainObject.getId();
        Long saStoreId = mainObject.getSgCSaStoreId();
        Long psCProId =  mainObject.getPsCProId();


        if(!ObjectUtils.isEmpty(saStoreId)){
            SgCSaStore storeInfo = CommonCacheValUtils.getSaStore(saStoreId);
            if (storeInfo == null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("配销仓已不存在");
                return v14;
            }
            saStoreId = storeInfo.getSgCShareStoreId();
            mainObject.setSgCSaStoreId(storeInfo.getId());
            mainObject.setSgCSaStoreEcode(storeInfo.getEcode());
            mainObject.setSgCSaStoreEname(storeInfo.getEname());
        }

        if(!ObjectUtils.isEmpty(mainObject.getSgCShareStoreId())){
            SgCShareStore shareStore = CommonCacheValUtils.getShareStore(mainObject.getSgCShareStoreId());
            if(ObjectUtils.isEmpty(shareStore)){
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("聚合仓已不存在");
                return v14;
            }
            mainObject.setSgCShareStoreId(shareStore.getId());
            mainObject.setSgCShareStoreEcode(shareStore.getEcode());
            mainObject.setSgCShareStoreEname(shareStore.getEname());
        }
        SgCSaSelection old = mainObject;
        List<SgCSaSelection> saSelectionList = new ArrayList<>();
        if (objId == null || objId < 1L) {

            if(ObjectUtils.isEmpty(mainObject.getStartTime()) || ObjectUtils.isEmpty(mainObject.getEndTime()) ||
                    ObjectUtils.isEmpty(mainObject.getShareType()) || ObjectUtils.isEmpty(mainObject.getShareRatio())){
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("开始时间、结束时间、共享维度、共享系数不能为空");
                return v14;
            }

            Integer count = saSelectionMapper.selectCount(new LambdaQueryWrapper<SgCSaSelection>()
                    .eq(SgCSaSelection::getPsCProId, psCProId)
                    .eq(SgCSaSelection::getSgCSaStoreId, mainObject.getSgCSaStoreId())
                    .eq(SgCSaSelection::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (count > 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("同一聚合仓+商品编码下只能存在一条配销仓记录");
                return v14;
            }

        }else {
            old = saSelectionMapper.selectById(mainObject.getId());
        }

        if(ObjectUtils.isEmpty(saStoreId)){
            saStoreId = old.getSgCShareStoreId();
        }

        Date startTime = Optional.ofNullable(mainObject.getStartTime()).orElse(old.getStartTime());
        Date endTime = Optional.ofNullable(mainObject.getEndTime()).orElse(old.getEndTime());

        if(startTime.compareTo(endTime) > 0){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("结束时间不能小于开始时间");
            return v14;
        }

        if(!saStoreId.equals(old.getSgCShareStoreId())){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("配销仓不属于该聚合仓");
            return v14;
        }

        Integer shareType = Optional.ofNullable(mainObject.getShareType()).orElse(old.getShareType());
        BigDecimal shareRatio = Optional.ofNullable(mainObject.getShareRatio()).orElse(old.getShareRatio());

        if(SgInfConstants.SA_SECTION_NUMBER.equals(shareType) &&
                BigDecimal.ZERO.compareTo(shareRatio) > 0){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("共享维度为【按数量】时，控制共享系数不能小于0");
            return v14;
        }

        if(SgInfConstants.SA_SECTION_RATIO.equals(shareType) &&
                (BigDecimal.ZERO.compareTo(shareRatio) > 0 ||
                        BigDecimal.ONE.compareTo(shareRatio) < 0)){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("共享维度为【按比例】时，控制共享系数不能大于1、或小于0");
            return v14;
        }

        if(!ObjectUtils.isEmpty(psCProId)){
            List<PsCSku> skuList = CommonCacheValUtils.getSkuInfoListByProId(psCProId);
            if (CollectionUtils.isEmpty(skuList)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("商品编码无对应条码信息");
                return v14;
            }
            skuList.forEach(sku -> {
                SgCSaSelection sgCSaSelection = new SgCSaSelection();
                BeanUtils.copyProperties(mainObject,sgCSaSelection);
                Long id = ModelUtil.getSequence("SG_C_SA_SELECTION");
                if(ObjectUtils.isEmpty(mainObject.getId()) || mainObject.getId() < 0){
                    mainObject.setId(id);
                }
                sgCSaSelection.setId(id);
                sgCSaSelection.setPsCSkuId(sku.getId());
                sgCSaSelection.setPsCSkuEcode(sku.getEcode());
                sgCSaSelection.setPsCProId(sku.getPsCProId());
                sgCSaSelection.setPsCProEcode(sku.getPsCProEcode());
                sgCSaSelection.setPsCProEname(sku.getPsCProEname());
                StorageUtils.setBModelDefalutData(sgCSaSelection,loginUser);
                saSelectionList.add(sgCSaSelection);
            });
        }else if(objId == null || objId < 1L){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("商品编码不能为空");
            return v14;
        }

        if(CollectionUtils.isNotEmpty(saSelectionList)){
            saSelectionMapper.batchInsert(saSelectionList);
        }else {
            StorageUtils.setBModelDefalutDataByUpdate(mainObject,loginUser);
            saSelectionMapper.updateById(mainObject);
        }

        return v14;
    }
}
