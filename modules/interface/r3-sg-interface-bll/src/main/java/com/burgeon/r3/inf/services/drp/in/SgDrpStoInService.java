package com.burgeon.r3.inf.services.drp.in;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.inf.config.SgDrpConfig;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoIn;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpStoInSaveRequest;
import com.burgeon.r3.sg.inf.model.result.drp.in.SgDrpStoInSaveResult;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInMapper;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInNoticesItemMapper;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInNoticesMapper;
import com.burgeon.r3.sg.store.model.request.in.*;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInBillSaveResult;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInNoticesBillSaveResult;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInResultBillSubmitResult;
import com.burgeon.r3.sg.store.services.in.SgBStoInNoticesSaveService;
import com.burgeon.r3.sg.store.services.in.SgBStoInResultSaveAndSubmitService;
import com.burgeon.r3.sg.store.services.in.SgBStoInSaveService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 逻辑在途单(暂时不用那个)
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SgDrpStoInService {

    @Autowired
    private SgBStoInSaveService saveService;
    @Autowired
    private SgBStoInNoticesMapper sgStoInNoticesMapper;
    @Autowired
    private SgBStoInNoticesItemMapper sgBStoInNoticesItemMapper;
    @Autowired
    private SgBStoInResultSaveAndSubmitService sgStoInResultSaveAndSubmitService;
    @Autowired
    private SgDrpConfig sgDrpConfig;
    @Autowired
    private SgBStoInMapper sgStoInMapper;

    /**
     * 保存接口
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgDrpStoInSaveResult> save(SgDrpStoInSaveRequest request) {
        if (!sgDrpConfig.getEnableDrpSend()) {
            return new ValueHolderV14<>(ResultCode.FAIL, "中台系统未开放,请稍后再试!");
        }

        log.info("Start SgDrpStoInService.save param={}", JSONObject.toJSONString(request));

        String lockKsy =
                SgConstants.INTERFACE_DRP + ":" + SgConstants.SG_B_STO_IN + ":" + request.getSourceBillNo();
        SgRedisLockUtils.lock(lockKsy);

        ValueHolderV14<SgDrpStoInSaveResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS,
                SgConstants.MESSAGE_STATUS_SUCCESS);
        List<String> redisFtpkeyList = new ArrayList<>();
        try {
            //【逻辑在途单创建接口】： 如果"来源单类型"=采购单、来源单号、可用=Y的【逻辑在途单】中已存在，不允许
            if (SgConstantsIF.BILL_TYPE_PUR == request.getSourceBillType()) {
                int count = sgStoInMapper.selectCount(new LambdaQueryWrapper<SgBStoIn>()
                        .eq(SgBStoIn::getSourceBillNo, request.getSourceBillNo())
                        .eq(SgBStoIn::getSourceBillType, request.getSourceBillType())
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
                if (count > 0) {
                    return new ValueHolderV14<>(ResultCode.SUCCESS, "当前单据中台已存在,请勿重复请求!");
                }
            }
            //如果接口中传入的登录用户为null，那么取系统用户
            User user = request.getLoginUser();
            if (user == null) {
                user = DrpUtils.getUser();
                request.setLoginUser(user);
            }
            // 主表处理
            SgBStoInBillSaveRequest sgRequest = new SgBStoInBillSaveRequest();
            SgBStoInSaveRequest mainTable = new SgBStoInSaveRequest();
            mainTable.setServiceNode(request.getServiceNode());
            mainTable.setSourceBillId(request.getSourceBillId());
            mainTable.setSourceBillNo(request.getSourceBillNo());
            mainTable.setSourceBillType(request.getSourceBillType());
            mainTable.setSenderEcode(request.getSenderEcode());
            mainTable.setBillDate(request.getBillDate());
            mainTable.setSenderName(request.getSenderName());

            // 明细处理
            List<SgBStoInItemSaveRequest> itemList = Collections.synchronizedList(new ArrayList<>());

            // 构建逻辑入库单明细信息
            List<SgBStoInResultItemSaveRequest> stoInResultItemList = Collections.synchronizedList(new ArrayList<>());
            // 构建明细信息
            if (CollectionUtils.isEmpty(request.getItems())) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("逻辑在途单保存,明细信息不能为空!");
                return v14;
            }
            CommonCacheValUtils.setSkuInfoByCode(request.getItems());
            request.getItems().forEach(item -> {
                SgBStoInItemSaveRequest sgStoInItemSaveRequest = new SgBStoInItemSaveRequest();
                BeanUtils.copyProperties(item, sgStoInItemSaveRequest);
                //                    sgStoInItemSaveRequest.setSourceBillItemId(item.getSourceBillItemId());
                CpCStore storeInfo = CommonCacheValUtils.getStoreInfoByEcode(item.getCpCStoreEcode());
                AssertUtils.cannot(Objects.isNull(storeInfo), "逻辑仓未查询到！");

                sgStoInItemSaveRequest.setCpCStoreId(storeInfo.getId());
                sgStoInItemSaveRequest.setCpCStoreEcode(item.getCpCStoreEcode());
                sgStoInItemSaveRequest.setCpCStoreEname(storeInfo.getEname());
                sgStoInItemSaveRequest.setQty(item.getQty());
                //sgStoInItemSaveRequest.setQtyPrein(item.getQtyPrein());
                sgStoInItemSaveRequest.setQtyPrein(item.getQty());
                itemList.add(sgStoInItemSaveRequest);

                SgBStoInResultItemSaveRequest requestStoInResultItem = new SgBStoInResultItemSaveRequest();
                BeanUtils.copyProperties(sgStoInItemSaveRequest, requestStoInResultItem);
                requestStoInResultItem.setQty(item.getQtyPrein());
                stoInResultItemList.add(requestStoInResultItem);
            });

            // sgRequest 赋值相关字段
            sgRequest.setSgStoInSaveRequest(mainTable);
            sgRequest.setSgStoInItemSaveRequestList(itemList);
            sgRequest.setServiceNode(request.getServiceNode());
            sgRequest.setLoginUser(user);
            sgRequest.setIsCancel(false);
            sgRequest.setUpdateMethod(SgConstantsIF.ITEM_UPDATE_TYPE_ALL);
            //erp传参可能为负数 开启允许负库存控制
            sgRequest.setIsNegativePrein(true);
            // 执行具体逻辑
            ValueHolderV14<SgBStoInBillSaveResult> valueHolderV14 = saveService.saveSgBStoIn(sgRequest);
            if (!valueHolderV14.isOK()) {
                return new ValueHolderV14<>(ResultCode.FAIL, valueHolderV14.getMessage());
            }

            SgBStoInBillSaveResult data = valueHolderV14.getData();
            if (data != null && CollectionUtils.isNotEmpty(data.getRedisBillFtpKeyList())) {
                redisFtpkeyList.addAll(data.getRedisBillFtpKeyList());
            }

            if (log.isDebugEnabled()) {
                log.debug("SgDrpStoInService.saveService.saveSgBStoIn sgRequest:{}",
                        JSONObject.toJSONString(sgRequest));
            }
            //生成入库通知单，调用【入库通知单服务】
            ValueHolderV14<SgBStoInNoticesBillSaveResult> resultValueHolderV14 = sgStoInNoticesSave(sgRequest, user);
            if (log.isDebugEnabled()) {
                log.debug("Start SgBStoTransferOutResultService.outResultSgStoTransfer.resultValueHolderV14={}",
                        JSONObject.toJSONString(resultValueHolderV14));
            }
            if (!resultValueHolderV14.isOK()) {
                AssertUtils.logAndThrow("入库通知单服务异常：" + resultValueHolderV14.getMessage());
            }

            List<String> list = saveAndSubmitInResult(request, valueHolderV14, stoInResultItemList,
                    resultValueHolderV14);

            if (CollectionUtils.isNotEmpty(list)) {
                redisFtpkeyList.addAll(list);
            }

            // 返回结果
            SgDrpStoInSaveResult drpStoInResult = new SgDrpStoInSaveResult();
            drpStoInResult.setBillNo(valueHolderV14.getData().getBillNo());
            v14.setData(drpStoInResult);
        } catch (Exception e) {
            StorageBasicUtils.rollbackStorage(redisFtpkeyList, request.getLoginUser());
            AssertUtils.logAndThrowException("逻辑在途单接口服务异常！", e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        return v14;
    }

    /**
     * DRP-接口使用：新增入库通知单新增服务
     *
     * @param request 接口请求信息
     * @param user    登录用户
     * @return
     */
    private ValueHolderV14<SgBStoInNoticesBillSaveResult> sgStoInNoticesSave(SgBStoInBillSaveRequest request,
                                                                             User user) {

        ValueHolderV14<SgBStoInNoticesBillSaveResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "入库通知单新增成功！");

        SgBStoInNoticesBillSaveRequest stoInNoticesBillSaveRequest = new SgBStoInNoticesBillSaveRequest();
        SgBStoInNoticesBillSaveRequest.SgBStoInNoticesSaveRequest stoInNoticesSaveRequest =
                new SgBStoInNoticesBillSaveRequest.SgBStoInNoticesSaveRequest();
        List<SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest> list = new ArrayList<>();
        SgBStoInSaveRequest sgStoInSaveRequest = request.getSgStoInSaveRequest();

        //主表
        BeanUtils.copyProperties(sgStoInSaveRequest, stoInNoticesSaveRequest);

        //stoInNoticesSaveRequest.setCpCCustomerId(stoTransfer.getReceiverCustomerId());//经销商id
        //stoInNoticesSaveRequest.setCpCSupplierId(stoTransfer.getSenderCustomerId());//供应商id
        stoInNoticesSaveRequest.setSourceBillId(sgStoInSaveRequest.getSourceBillId());//来源单据id
        stoInNoticesSaveRequest.setSourceBillNo(sgStoInSaveRequest.getSourceBillNo());//来源单据编号
        stoInNoticesSaveRequest.setSourceBillType(sgStoInSaveRequest.getSourceBillType());//来源单据类型
        stoInNoticesSaveRequest.setBillDate(sgStoInSaveRequest.getBillDate());//单据日期
        //stoInNoticesSaveRequest.setSourceBillDate(sgStoInSaveRequest.getSou());//来源单据日期
        stoInNoticesSaveRequest.setBillStatus(SgStoreConstants.BILL_NOTICES_STATUS_INIT);//单据状态
        stoInNoticesSaveRequest.setSenderEcode(sgStoInSaveRequest.getSenderEcode());//发货方编码
        stoInNoticesSaveRequest.setSenderName(sgStoInSaveRequest.getSenderName());//发货方名称
        stoInNoticesSaveRequest.setInType(SgStoreConstantsIF.OUT_TYPE_BIG_GOODS);//入库类型

        for (SgBStoInItemSaveRequest item : request.getSgStoInItemSaveRequestList()) {

            SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest stoInNoticesItemSaveRequest =
                    new SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest();
            BeanUtils.copyProperties(item, stoInNoticesItemSaveRequest);
            Long id = item.getPsCSkuId();
            stoInNoticesItemSaveRequest.setCpCStoreId(item.getCpCStoreId());
            stoInNoticesItemSaveRequest.setCpCStoreEcode(item.getCpCStoreEcode());
            stoInNoticesItemSaveRequest.setCpCStoreEname(item.getCpCStoreEname());
            //stoInNoticesItemSaveRequest.setCpCCustomerId(item.getReceiverCustomerId());//所属经销商
            stoInNoticesItemSaveRequest.setQtyIn(BigDecimal.ZERO);

            stoInNoticesItemSaveRequest.setQty(item.getQty());
            stoInNoticesItemSaveRequest.setSourceBillItemId(item.getSourceBillItemId());

            list.add(stoInNoticesItemSaveRequest);
        }
        stoInNoticesBillSaveRequest.setInNoticesSaveRequest(stoInNoticesSaveRequest);
        stoInNoticesBillSaveRequest.setInNoticesItemSaveRequests(list);
        try {
            SgBStoInNoticesSaveService stoInNoticesSaveService =
                    ApplicationContextHandle.getBean(SgBStoInNoticesSaveService.class);
            v14 = stoInNoticesSaveService.addInNotices(stoInNoticesBillSaveRequest, user);
            if (log.isDebugEnabled()) {
                log.debug("Start SgBStoTransferOutResultService.SgBStoInItem.v14={}", JSONObject.toJSONString(v14));
            }
            if (!v14.isOK()) {
                return new ValueHolderV14<>(ResultCode.FAIL, v14.getMessage());
            }
        } catch (Exception ex) {
            log.error("SgBStoTransferOutResultService.sgStoInNoticesSave. error:{}",
                    Throwables.getStackTraceAsString(ex));
            AssertUtils.logAndThrowException("新增入库通知单异常：", ex, user.getLocale());
        }
        return v14;
    }

    /**
     * 新增并审核逻辑入库单
     */
    private List<String> saveAndSubmitInResult(SgDrpStoInSaveRequest request,
                                               ValueHolderV14<SgBStoInBillSaveResult> result,
                                               List<SgBStoInResultItemSaveRequest> stoInResultItemList,
                                               ValueHolderV14<SgBStoInNoticesBillSaveResult> resultValueHolderV14) {

        List<String> redisFtpkeyList = new ArrayList<>();

        try {
            // 构建逻辑入库单主表信息
            SgBStoInResultSaveRequest stoInResultRequest = new SgBStoInResultSaveRequest();
            //构建逻辑入库单主表信息
            stoInResultRequest.setBillDate(request.getBillDate());
            stoInResultRequest.setSourceBillType(request.getSourceBillType());
            stoInResultRequest.setSourceBillNo(request.getSourceBillNo());
            stoInResultRequest.setSourceBillId(request.getSourceBillId());
            // 逻辑在途单的iD：result.getData().getId()
            stoInResultRequest.setSgBStoInId(result.getData().getId());
            //入库通知单id
            stoInResultRequest.setSgBStoInNoticesId(resultValueHolderV14.getData().getId());
            stoInResultRequest.setSgBStoInNoticesNo(resultValueHolderV14.getData().getBillNo());

            stoInResultRequest.setSenderEcode(request.getSenderEcode());
            stoInResultRequest.setSenderName(request.getSenderName());
            stoInResultRequest.setIsLast(SgConstants.IS_LAST_YES);
            stoInResultRequest.setServiceNode(request.getServiceNode());
            stoInResultRequest.setInTime(request.getBillDate());
            SgBStoInResultItemSaveRequest stoInResultItemSaveRequest = stoInResultItemList.get(0);

            stoInResultRequest.setCpCStoreId(stoInResultItemSaveRequest.getCpCStoreId());
            stoInResultRequest.setCpCStoreEcode(stoInResultItemSaveRequest.getCpCStoreEcode());
            stoInResultRequest.setCpCStoreEname(stoInResultItemSaveRequest.getCpCStoreEname());

            //构建逻辑入库单请求信息
            List<SgBStoInResultBillSaveRequest> requestList = new ArrayList<>();
            SgBStoInResultBillSaveRequest stoInResultBillRequest = new SgBStoInResultBillSaveRequest();
            stoInResultBillRequest.setInResultSaveRequest(stoInResultRequest);
            stoInResultBillRequest.setInItemResultSaveRequestList(stoInResultItemList);
            stoInResultBillRequest.setIsOneClickLibrary(true);
            stoInResultBillRequest.setLoginUser(request.getLoginUser());

            requestList.add(stoInResultBillRequest);

            SgBStoInBillSaveResult data = result.getData();
            //将上面redis流水继承下来
            if (data != null && CollectionUtils.isNotEmpty(data.getRedisBillFtpKeyList())) {
                redisFtpkeyList.addAll(data.getRedisBillFtpKeyList());
            }

            ValueHolderV14<List<SgBStoInResultBillSubmitResult>> listValueHolderV14 =
                    sgStoInResultSaveAndSubmitService.saveAndAuditBill(requestList);
            if (listValueHolderV14.getCode() == ResultCode.FAIL) {
                AssertUtils.logAndThrow("新增并审核逻辑入库单失败!" + listValueHolderV14.getMessage());
            }

            List<SgBStoInResultBillSubmitResult> listValueHolderV14Data = listValueHolderV14.getData();
            if (CollectionUtils.isNotEmpty(listValueHolderV14Data)) {
                for (SgBStoInResultBillSubmitResult resultBillSubmitResult : listValueHolderV14Data) {
                    List<String> redisBillFtpKeyList = resultBillSubmitResult.getRedisBillFtpKeyList();
                    if (CollectionUtils.isNotEmpty(redisBillFtpKeyList)) {
                        redisFtpkeyList.addAll(redisBillFtpKeyList);
                    }
                }
            }

        } catch (Exception e) {
            log.error("新增并审核逻辑入库单异常：exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            // 回滚库存
            StorageBasicUtils.rollbackStorage(redisFtpkeyList, request.getLoginUser());
            AssertUtils.logAndThrow(e.getMessage());

        }

        return redisFtpkeyList;
    }

}

