package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransfer;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransferItem;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.services.transfer.SgTransferBillStatusEnum;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 调拨入库通知drp接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/24 13:44
 */
@Slf4j
@Component
public class SgDrpStoInNotifyProcessor extends AbstractDrpInterfaceProcessor<SgBStoTransfer, SgBStoTransferItem> {

    @Autowired
    private CpCStoreMapper cpCStoreMapper;

    @Override
    public LambdaQueryWrapper<SgBStoTransfer> execMainWrapper() {


        LambdaQueryWrapper<SgBStoTransfer> inNotifyOrder = new LambdaQueryWrapper<SgBStoTransfer>()
                .eq(SgBStoTransfer::getStatus, SgTransferBillStatusEnum.AUDITED_ALL_OUT_ALL_IN.getVal());
        inNotifyOrder.eq(SgBStoTransfer::getDrpBillType, SgStoreConstants.DRP_BILL_TYPE_TF);
        inNotifyOrder.eq(SgBStoTransfer::getDrpStatus,SgStoreConstants.SEND_DRP_STATUS_SUCCESS);
        inNotifyOrder.and(a -> {
            a.isNull(SgBStoTransfer::getDrpInStatus);
            a.or(o -> o.eq(SgBStoTransfer::getDrpInStatus, SgStoreConstants.SEND_DRP_STATUS_FAIL).lt(SgBStoTransfer::getDrpInFailCount, failNum));
            return a;
        });

        return inNotifyOrder;
    }

    private LambdaQueryWrapper<SgBStoTransfer> pageIn(LambdaQueryWrapper<SgBStoTransfer> lqw, List<List<SgCpCStore>> lists, Boolean isSender) {
        for (List<SgCpCStore> pageList : lists) {
            if (CollectionUtils.isEmpty(pageList)) {
                continue;
            }
            List<Long> cpcStoIds = pageList.stream().map(SgCpCStore::getId).collect(Collectors.toList());
            if (isSender) {
                lqw.or(oo -> {
                    oo.in(SgBStoTransfer::getSenderStoreId, cpcStoIds);
                    return oo;
                });
            } else {
                lqw.or(oo -> {
                    oo.in(SgBStoTransfer::getReceiverStoreId, cpcStoIds);
                    return oo;
                });
            }
        }
        return lqw;
    }

    @Override
    public LambdaQueryWrapper<SgBStoTransferItem> execitemWrapper(Long mainId) {
        return null;
    }

    @Override
    public String drpStatus() {
        return "DRP_IN_STATUS";
    }

    @Override
    public String drpStatusFailCount() {
        return "DRP_IN_FAIL_COUNT";
    }

    @Override
    public String drpStatusFailReason() {
        return "DRP_IN_FAIL_REASON";
    }

    @Override
    public String drpInterfaceUrl() {
        return "erp.transferin.notice";
    }

    @Override
    public String itemMainField() {
        return "SG_B_STO_TRANSFER_ID";
    }

    @Override
    public JSONObject execInterfaceParam(SgBStoTransfer sgBStoTransfer, List<SgBStoTransferItem> z) {
        if (log.isDebugEnabled()) {
            log.debug("SgDrpStoInNotifyProcessor.exec:transfer={},items={}", JSONObject.toJSONString(sgBStoTransfer), JSONObject.toJSONString(z));
        }

        JSONObject jsonObject = new JSONObject();
        //中台编号
        jsonObject.put("ZTDOCNO", sgBStoTransfer.getBillNo());
        //erp单据编号，需求确认=单据编号
        jsonObject.put("DOCNO", sgBStoTransfer.getBillNo());
        //入库日期
        jsonObject.put("DATEIN", DateUtil.format(sgBStoTransfer.getInDate() == null ?
                new Date() : sgBStoTransfer.getInDate(), "yyyyMMdd"));


        JSONArray items = new JSONArray();
        for (SgBStoTransferItem item : z) {
            JSONObject itemJson = new JSONObject();
            itemJson.put("M_PRODUCTALIAS_NO", item.getPsCSkuEcode());
            //款号，需求确认=商品编码
            itemJson.put("M_PRODUCT_NAME", item.getPsCProEcode());
            //入库数量
            itemJson.put("QTYIN", item.getQtyIn());
            items.add(itemJson);
        }

        jsonObject.put("items", items);
        log.info("SgDrpStoInNotifyProcessor.JSONObject:{}", jsonObject.toJSONString());
        return jsonObject;
    }

    @Override
    public void handleBysuccess(SgBStoTransfer sgBStoTransfer, List<SgBStoTransferItem> z) {

    }
}
