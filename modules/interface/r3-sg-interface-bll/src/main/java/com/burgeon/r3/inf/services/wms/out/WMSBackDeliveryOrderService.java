package com.burgeon.r3.inf.services.wms.out;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.out.SgBWmsToStoOutResult;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.out.SgBWmsToStoOutResultMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.api.CpcPhyWareHouseQueryCmd;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.cpext.model.RedisKeyConstans;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * @author: chenhaop
 * @time: 2022/1/4 3:10 下午
 * @description: 发货确认单
 */
@Slf4j
@Component
public class WMSBackDeliveryOrderService {

    @Autowired
    private SgBWmsToStoOutResultMapper sgBWmsToStoOutResultMapper;

    @Reference(version = "1.0", group = "cp-ext")
    private CpcPhyWareHouseQueryCmd cpcPhyWareHouseQueryCmd;

    SimpleDateFormat beforeFormat = new SimpleDateFormat("yyyyMMdd");
    SimpleDateFormat afterFormat = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 发货确认单插入中间表
     *
     * @param messageBody 入参
     * @return 出参
     */
    public ValueHolderV14<String> apiProcess(String messageBody) {
        log.info(LogUtil.format("WMSBackDeliveryOrderService.apiProcess,messageBody:{}",
                "WMSBackDeliveryOrderService.apiProcess"), messageBody);
        JSONObject request = JSONObject.parseObject(messageBody);
        JSONObject deliveryOrderObject = request.getJSONObject("deliveryOrder");
        // WMS单据编号
        String wmsBillCode = deliveryOrderObject.getString("deliveryOrderId");
        // 出库通知单号
        String noticesBillNo = deliveryOrderObject.getString("deliveryOrderCode");
        // 外部业务编码
        String outBizCode = deliveryOrderObject.getString("outBizCode");
        //wms仓库编码
        String warehouseCode = deliveryOrderObject.getString("warehouseCode");
        /*出库类型*/
        String orderType = deliveryOrderObject.getString("orderType");
        SgStoreConstantsIF.OutTypeEnum orderTypeEnum = SgStoreConstantsIF.OutTypeEnum.getByCode(orderType);

        String lockKsy = SgConstants.SG_BILL_LOCK_B2C_OUT + outBizCode;
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockKsy);
        try {
            if (redisLock.tryLock(RedisMasterUtils.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                SgBWmsToStoOutResult wmsToStoOutResult = new SgBWmsToStoOutResult();
                wmsToStoOutResult.setId(ModelUtil.getSequence(SgConstants.SG_B_WMS_TO_STO_OUT_RESULT));
                wmsToStoOutResult.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT);
//                wmsToStoOutResult.setBillType(SgStoreConstantsIF.OUT_TYPE_ELECTRICITY);
                wmsToStoOutResult.setBillType(orderTypeEnum.getValue());
                wmsToStoOutResult.setMessage(messageBody);
                wmsToStoOutResult.setNoticesBillNo(noticesBillNo);
                wmsToStoOutResult.setFailedCount(NumberUtils.INTEGER_ZERO);
                wmsToStoOutResult.setWmsBillCode(wmsBillCode);
//                wmsToStoOutResult.setOutBizCode(outBizCode);
                wmsToStoOutResult.setIsactive(SgConstants.IS_ACTIVE_Y);
                CpCPhyWarehouse warehouse = queryWarehouseByWmsWarehouseCode(warehouseCode);
                if (warehouse != null && ThirdWmsTypeEnum.JDWMS.getCode().equals(warehouse.getWmsType())) {
                    JSONArray orderLines = request.getJSONArray("orderLines");
                    JSONArray newOrderLines = new JSONArray();
                    if (orderLines != null && orderLines.size() > 0) {
                        for (int i = 0; i < orderLines.size(); i++) {
                            JSONObject jsonObject = orderLines.getJSONObject(i);
                            JSONArray batchs = jsonObject.getJSONArray("batchs");
                            if (batchs != null && batchs.size() > 0) {
                                for (int j = 0; j < batchs.size(); j++) {
                                    JSONObject newJsonObject = JSONObject.parseObject(jsonObject.toJSONString());

                                    JSONObject batch = batchs.getJSONObject(j);
                                    if (!StringUtils.isEmpty(batch.getString("productDate"))) {
                                        batch.put("batchCode",batch.getString("productDate"));
                                    }
                                    batch.keySet().forEach(x -> newJsonObject.put(x, batch.get(x)));
                                    newJsonObject.remove("batchs");
                                    newOrderLines.add(newJsonObject);
                                }
                            }
                        }
                    }
                    request.put("orderLines", newOrderLines);
                    wmsToStoOutResult.setMessage(request.toJSONString());
                }
                /*大宝仓商品编码要转成大宝的*/
                if (warehouse != null && ThirdWmsTypeEnum.DBWMS.getCode().equals(warehouse.getWmsType())) {
                    JSONArray orderLines = request.getJSONArray("orderLines");
                    if (orderLines != null && orderLines.size() > 0) {
                        for (int i = 0; i < orderLines.size(); i++) {
                            JSONObject jsonObject = orderLines.getJSONObject(i);
                            String itemCode = jsonObject.getString("itemCode");
                            jsonObject.put("itemId", itemCode);
                            //produceCode转batchCode
                            try {
                                Date produceCode = beforeFormat.parse(jsonObject.getString("produceCode"));
                                jsonObject.put("batchCode", afterFormat.format(produceCode));
                            } catch (ParseException e) {
                                log.error(LogUtil.format("WMSBackDeliveryOrderService.apiProcess.error:{}",
                                        "WMSBackDeliveryOrderService.apiProcess.error"), Throwables.getStackTraceAsString(e));
                            }
                        }
                    }
                    JSONArray packages = request.getJSONArray("packages");
                    if (packages != null && packages.size() > 0) {
                        for (int i = 0; i < packages.size(); i++) {
                            JSONObject pkg = packages.getJSONObject(i);
                            JSONArray items = pkg.getJSONArray("items");
                            if (items != null && items.size() > 0) {
                                for (int j = 0; j < items.size(); j++) {
                                    JSONObject item = items.getJSONObject(j);
                                    String itemCode = item.getString("itemCode");
                                    item.put("itemId", itemCode);
                                }
                            }
                        }
                    }

                    wmsToStoOutResult.setMessage(request.toJSONString());
                }
                StorageUtils.setBModelDefalutData(wmsToStoOutResult, R3SystemUserResource.getSystemRootUser());
                sgBWmsToStoOutResultMapper.insert(wmsToStoOutResult);
            } else {
                log.error(LogUtil.format("B2C出库WMS回传重复.messageBody=", "B2C出库WMS回传重复", noticesBillNo), messageBody);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("B2C出库WMS回传异常={}", "B2C出库WMS回传异常"), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        } finally {
            redisLock.unlock();
        }
        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("success"));
    }

    private CpCPhyWarehouse queryWarehouseByWmsWarehouseCode(String wmsCode) {
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        String redisKey = RedisKeyConstans.CP_WAREHOUSE_BY_WMS_CODE_JSON + wmsCode;
        String warehouseStr = redisTemplate.opsForValue().get(redisKey);
        CpCPhyWarehouse warehouse = null;
        if (!org.springframework.util.StringUtils.isEmpty(warehouseStr)) {
            warehouse = JSONObject.parseObject(warehouseStr, CpCPhyWarehouse.class);
            log.info(LogUtil.format("根据wmsCode查询redis仓库信息，warehouse:{}"
                    ,"根据wmsCode查询redis仓库信息"), JSON.toJSONString(warehouse));
        }
        if (warehouse == null) {
            ValueHolderV14<CpCPhyWarehouse> v14 = cpcPhyWareHouseQueryCmd.queryWarehouseByWmsWarehouseCode(wmsCode);
            if (v14.isOK() && v14.getData() != null) {
                warehouse = v14.getData();
                redisTemplate.opsForValue().set(redisKey, JSON.toJSONString(warehouse));
            }
            log.info(LogUtil.format("根据wmsCode查询mysql仓库信息，warehouse:{}"
                    , "根据wmsCode查询mysql仓库信息"), JSON.toJSONString(warehouse));
        }
        return warehouse;

    }


}