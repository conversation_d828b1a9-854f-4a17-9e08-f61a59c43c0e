package com.burgeon.r3.inf.services.jingdong;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.in.SgBWmsToStoEntryInResult;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.in.SgBWmsToStoEntryInResultMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022/7/22 15:54
 * @Description
 */
@Slf4j
@Component
public class JdStockInFeedbackMsgService {

    @Autowired
    private SgBWmsToStoEntryInResultMapper sgBWmsToStoEntryInResultMapper;

    public ValueHolderV14 stockInFeedbackMsg(String msg) {

        JSONObject bodyMsg = JSONObject.parseObject(msg).getJSONObject("bodyMsg");

        JSONObject entryOrder = bodyMsg.getJSONObject("entryOrder");
        //单据类型
        String orderType = entryOrder.getString("orderType");
        //仓库编码
        String warehouseCode = entryOrder.getString("warehouseCode");
        //入库单号
        String entryOrderCode = entryOrder.getString("entryOrderCode");
        //WMS单号
        String clpsOrderCode = entryOrder.getString("clpsOrderCode");

        try {
            SgBWmsToStoEntryInResult sgBWmsToStoEntryInResult1 = sgBWmsToStoEntryInResultMapper.selectOne(new LambdaQueryWrapper<SgBWmsToStoEntryInResult>()
                    .eq(SgBWmsToStoEntryInResult::getNoticesBillNo, entryOrderCode)
                    .eq(SgBWmsToStoEntryInResult::getIsactive,"Y")
                    .last("limit 1"));

            if (Objects.nonNull(sgBWmsToStoEntryInResult1)) {
                log.error(LogUtil.format("京东B2B入库单WMS回传重复.messageBody={}", "京东B2B入库单WMS回传重复", sgBWmsToStoEntryInResult1), msg);
                return new ValueHolderV14<>(ResultCode.FAIL, "京东B2B入库单WMS回传重复");
            } else {

                SgBWmsToStoEntryInResult sgBWmsToStoEntryInResult = new SgBWmsToStoEntryInResult();
                sgBWmsToStoEntryInResult.setId(ModelUtil.getSequence(SgConstants.SG_B_WMS_TO_STO_ENTRY_IN_RESULT));
                sgBWmsToStoEntryInResult.setNoticesBillNo(entryOrderCode);
                sgBWmsToStoEntryInResult.setWarehouseCode(warehouseCode);
                sgBWmsToStoEntryInResult.setBillType(orderType);
                sgBWmsToStoEntryInResult.setWmsBillCode(clpsOrderCode);
                sgBWmsToStoEntryInResult.setMessage(msg);

                sgBWmsToStoEntryInResult.setWmsWarehouseType(ThirdWmsTypeEnum.JDWMS.getCode());
                sgBWmsToStoEntryInResult.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT);
                sgBWmsToStoEntryInResult.setFailedCount(NumberUtils.INTEGER_ZERO);
                sgBWmsToStoEntryInResult.setIsactive(SgConstants.IS_ACTIVE_Y);
                StorageUtils.setBModelDefalutData(sgBWmsToStoEntryInResult, R3SystemUserResource.getSystemRootUser());
                sgBWmsToStoEntryInResultMapper.insert(sgBWmsToStoEntryInResult);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("京东B2B入库单WMS回传异常={}", "京东B2B入库单WMS回传异常"), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }

        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("success"));
    }
}
