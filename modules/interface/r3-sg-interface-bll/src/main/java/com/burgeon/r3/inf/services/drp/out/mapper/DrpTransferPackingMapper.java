package com.burgeon.r3.inf.services.drp.out.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransfer;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @create 2021/8/24 19:03
 */
@Mapper
public interface DrpTransferPackingMapper extends ExtentionMapper {

    @Select("select * from sg_b_sto_transfer ${ew.customSqlSegment}")
    SgBStoTransfer selectData(@Param(Constants.WRAPPER) Wrapper wrapper);
}
