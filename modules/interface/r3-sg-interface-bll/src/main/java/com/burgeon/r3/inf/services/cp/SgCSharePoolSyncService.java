package com.burgeon.r3.inf.services.cp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.mapper.SgCSharePoolMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSharePool;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/10 10:47
 * <p>
 * 共享池档案主表同步到cp的服务
 */
@Slf4j
@Component
public class SgCSharePoolSyncService extends AbstractSyncService<SgCSharePool> {

    @Autowired
    private SgCSharePoolMapper sgCSharePoolMapper;

    public SgCSharePoolSyncService() {
        super(SgConstants.SG_C_SHARE_POOL);
    }

    @Override
    public List<SgCSharePool> execute(int pageIndex, int pageSize, int pageRangeTime, boolean isAllSync) {
        if (log.isDebugEnabled()) {
            log.debug("SgCSharePoolSyncService.execute :pageIndex={} ,pageSize={} ,intervalMinute={}",
                    pageIndex, pageSize, pageRangeTime);
        }
        LambdaQueryWrapper<SgCSharePool> lqw = new LambdaQueryWrapper<SgCSharePool>();

        if (!isAllSync) {
            Date now = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            calendar.add(Calendar.MINUTE, -pageRangeTime);
            lqw.gt(SgCSharePool::getModifieddate, calendar.getTime())
                    .lt(SgCSharePool::getModifieddate, now);
        }

        PageHelper.startPage(pageIndex, pageSize);
        List<SgCSharePool> sgCSharePools = sgCSharePoolMapper.selectList(lqw);
        PageInfo<SgCSharePool> pageInfo = new PageInfo<>(sgCSharePools);
        return pageInfo.getList();
    }
}
