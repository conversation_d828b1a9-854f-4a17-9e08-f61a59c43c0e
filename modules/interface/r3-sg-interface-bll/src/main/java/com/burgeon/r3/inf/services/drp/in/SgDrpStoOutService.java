package com.burgeon.r3.inf.services.drp.in;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.inf.config.SgDrpConfig;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOut;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpStoOutItemSaveRequest;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpStoOutSaveRequest;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpStoOutVoidRequest;
import com.burgeon.r3.sg.inf.model.result.drp.in.SgDrpStoOutSaveResult;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesMapper;
import com.burgeon.r3.sg.store.model.request.out.*;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutBillSaveResult;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutBillVoidResult;
import com.burgeon.r3.sg.store.services.out.SgBStoOutNoticesSaveService;
import com.burgeon.r3.sg.store.services.out.SgBStoOutSaveService;
import com.burgeon.r3.sg.store.services.out.SgBStoOutVoidService;
import com.google.common.base.Throwables;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.utils.AssertUtils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Description 逻辑占用单DRP->中台接口
 * <AUTHOR>
 * @Date 2021/6/18 15:56
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgDrpStoOutService {

    @Autowired
    private SgBStoOutVoidService sgBStoOutVoidService;

    @Autowired
    private SgBStoOutSaveService sgBStoOutSaveService;

    @Autowired
    private SgDrpConfig sgDrpConfig;

    @Autowired
    private SgBStoOutNoticesMapper noticesMapper;

    @Autowired
    SgBStoOutNoticesSaveService sgBStoOutNoticesSaveService;
    @Autowired
    private SgBStoOutMapper sgStoOutMapper;

    /**
     * 逻辑占用单创建保存
     *
     * @param request 入参
     * @return 出参
     */
    public ValueHolderV14<SgDrpStoOutSaveResult> saveSgDrpBStoOut(SgDrpStoOutSaveRequest request) {
        if (!sgDrpConfig.getEnableDrpSend()) {
            return new ValueHolderV14<>(ResultCode.FAIL, "中台系统未开放,请稍后再试!");
        }

        log.info("Start SgDrpStoOutVoidService.saveSgDrpBStoOut param={}", JSONObject.toJSONString(request));

        String lockKsy =
                SgConstants.INTERFACE_DRP + ":" + SgConstants.SG_B_STO_OUT + ":" + request.getSourceBillNo();
        SgRedisLockUtils.lock(lockKsy);

        ValueHolderV14<SgDrpStoOutSaveResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "创建成功");
        try {
            if (SgConstantsIF.BILL_TYPE_PUR_REF == request.getSourceBillType()) {
                int count = sgStoOutMapper.selectCount(new LambdaQueryWrapper<SgBStoOut>()
                        .eq(SgBStoOut::getSourceBillNo, request.getSourceBillNo())
                        .eq(SgBStoOut::getSourceBillType, request.getSourceBillType())
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
                if (count > 0) {
                    return new ValueHolderV14<>(ResultCode.SUCCESS, "当前单据中台已存在,请勿重复请求!");
                }
            }
            User user = DrpUtils.getUser();
            SgBStoOutBillSaveRequest sgStoOutBillSaveRequest = new SgBStoOutBillSaveRequest();
            SgBStoOutSaveRequest sgStoOutSaveRequest = new SgBStoOutSaveRequest();
            BeanUtils.copyProperties(request, sgStoOutSaveRequest);
            sgStoOutSaveRequest.setBillDate(DateUtil.stringToDate(request.getSourceBillDate()));
            sgStoOutBillSaveRequest.setSgBStoOutSaveRequest(sgStoOutSaveRequest);
            List<SgBStoOutItemSaveRequest> sgStoOutItemSaveRequests = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(request.getSgDrpStoInItemSaveRequests())) {
                //批量设置sku信息
                CommonCacheValUtils.setSkuInfoByCode(request.getSgDrpStoInItemSaveRequests());
                for (SgDrpStoOutItemSaveRequest itemSave : request.getSgDrpStoInItemSaveRequests()) {
                    SgBStoOutItemSaveRequest sgStoOutItemSaveRequest = new SgBStoOutItemSaveRequest();
                    BeanUtils.copyProperties(itemSave, sgStoOutItemSaveRequest);

                    //根据逻辑仓信息获取
                    CpCStore storeInfoByEcode = CommonCacheValUtils.getStoreInfoByEcode(itemSave.getCpCStoreEcode());
                    if (!StringUtils.isEmpty(storeInfoByEcode)) {
                        sgStoOutItemSaveRequest.setCpCStoreId(storeInfoByEcode.getId());
                        sgStoOutItemSaveRequest.setCpCStoreEname(storeInfoByEcode.getEname());
                    } else {
                        v14.setCode(ResultCode.FAIL);
                        v14.setMessage("逻辑仓编码为：" + itemSave.getCpCStoreEcode() + "对应的逻辑仓档案不存在！");
                        return v14;
                    }
                    sgStoOutItemSaveRequest.setQty(itemSave.getQty());
                    sgStoOutItemSaveRequest.setQtyPreout(itemSave.getQtyPreout());
                    sgStoOutItemSaveRequest.setPsCSkuEcode(itemSave.getPsCSkuEcode());
                    sgStoOutItemSaveRequest.setCpCStoreEcode(itemSave.getCpCStoreEcode());
                    sgStoOutItemSaveRequest.setSourceBillItemId(itemSave.getSourceBillItemId());

                    sgStoOutItemSaveRequests.add(sgStoOutItemSaveRequest);
                }
            } else {
                return new ValueHolderV14<>(ResultCode.FAIL, "逻辑占用单创建明细信息参数缺失!");
            }
            //drp允许负库存控制，防止更新库存失败
            sgStoOutBillSaveRequest.setIsNegativePrein(Boolean.TRUE);
            sgStoOutBillSaveRequest.setLoginUser(user);
            sgStoOutBillSaveRequest.setSgBStoOutItemSaveRequests(sgStoOutItemSaveRequests);
            sgStoOutBillSaveRequest.setIsAutoOut(Optional.ofNullable(request.getIsAutoOut()).orElse(SgConstants.IS_AUTO_N));
            ValueHolderV14<SgBStoOutBillSaveResult> valueHolderV14Result =
                    sgBStoOutSaveService.saveSgStoOut(sgStoOutBillSaveRequest);
            if (!valueHolderV14Result.isOK()) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(valueHolderV14Result.getMessage());
                return v14;
            }
            SgDrpStoOutSaveResult sgDrpStoOutSaveResult = new SgDrpStoOutSaveResult();
            sgDrpStoOutSaveResult.setOutBillNo(valueHolderV14Result.getData().getBillNo());
            sgDrpStoOutSaveResult.setSourceBillNo(request.getSourceBillNo());
            v14.setData(sgDrpStoOutSaveResult);
        } catch (Exception e) {
            log.error("SgDrpStoOutVoidService.saveSgDrpBStoOut. exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }

        return v14;
    }

    /**
     * 作废逻辑占用单
     *
     * @param request 入参
     * @return 出参
     */
    public ValueHolderV14<SgBStoOutBillVoidResult> voidSgDrpBStoOut(SgDrpStoOutVoidRequest request) {
        if (!sgDrpConfig.getEnableDrpSend()) {
            return new ValueHolderV14<>(ResultCode.FAIL, "中台系统未开放,请稍后再试!");
        }

        log.info("Start SgDrpStoOutVoidService.voidSgDrpBStoOut param={}", JSONObject.toJSONString(request));

        String lockKsy =
                SgConstants.INTERFACE_DRP + ":" + SgConstants.SG_B_STO_OUT + ":" + SgConstantsIF.VOID + ":" + request.getSourceBillNo();
        SgRedisLockUtils.lock(lockKsy);

        ValueHolderV14<SgBStoOutBillVoidResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "作废成功");
        try {
            User user = DrpUtils.getUser();
            checkParams(request);

            SgBStoOutBillVoidRequest sgBStoOutBillVoidRequest = new SgBStoOutBillVoidRequest();
            sgBStoOutBillVoidRequest.setLoginUser(user);
            sgBStoOutBillVoidRequest.setSourceBillId(request.getSourceBillId());
            sgBStoOutBillVoidRequest.setSourceBillNo(request.getSourceBillNo());
            sgBStoOutBillVoidRequest.setSourceBillType(request.getSourceBillType());
            v14 = sgBStoOutVoidService.voidSgBStoOut(sgBStoOutBillVoidRequest);

            if (!v14.isOK()) {
                return v14;
            }

            //作废通知单
            List<SgBStoOutNotices> sgStoOutNotices = noticesMapper.selectList(new QueryWrapper<SgBStoOutNotices>()
                    .lambda()
                    .eq(SgBStoOutNotices::getSourceBillId, request.getSourceBillId())
                    .eq(SgBStoOutNotices::getSourceBillNo, request.getSourceBillNo())
                    .eq(SgBStoOutNotices::getSourceBillType, request.getSourceBillType())
                    .eq(SgBStoOutNotices::getIsactive, SgConstants.IS_ACTIVE_Y));

            if (CollectionUtils.isNotEmpty(sgStoOutNotices)) {

                if (log.isDebugEnabled()) {
                    log.debug("Start SgDrpStoOutVoidService.voidSgDrpBStoOut Void delivery notice sgStoOutNotices" +
                                    ".size ={}",
                            sgStoOutNotices.size());
                }

                SgBStoOutNoticesBillVoidRequest outNoticesBillVoidRequest = new SgBStoOutNoticesBillVoidRequest();
                outNoticesBillVoidRequest.setSourceBillId(request.getSourceBillId());
                outNoticesBillVoidRequest.setSourceBillNo(request.getSourceBillNo());
                outNoticesBillVoidRequest.setSourceBillType(request.getSourceBillType());

                ValueHolderV14 voidOutNotices = sgBStoOutNoticesSaveService.voidOutNotices(outNoticesBillVoidRequest,
                        null, DrpUtils.getUser(), Boolean.TRUE);

                if (log.isDebugEnabled()) {
                    log.debug("end SgDrpStoOutVoidService.voidSgDrpBStoOut Void delivery notice ValueHolderV14.v14 ={}",
                            JSONObject.toJSONString(voidOutNotices));
                }

                if (!voidOutNotices.isOK()) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(voidOutNotices.getMessage());
                    return v14;
                }

            }
            return v14;

        } catch (Exception e) {
            log.error("SgDrpStoOutVoidService.voidSgDrpBStoOut. exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
    }

    /**
     * 参数校验和获取
     *
     * @param request
     * @return
     */
    private void checkParams(SgDrpStoOutVoidRequest request) {
        AssertUtils.notNull(request.getBillNo(), "关联逻辑占用单不可为空！");
        AssertUtils.notNull(request.getSourceBillNo(), "来源单编号不可为空！");
    }

}
