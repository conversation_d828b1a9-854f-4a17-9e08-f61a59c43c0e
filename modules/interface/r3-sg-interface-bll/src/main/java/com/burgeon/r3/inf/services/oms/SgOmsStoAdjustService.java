package com.burgeon.r3.inf.services.oms;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoAdjustSaveRequest;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsStoAdjustSaveResult;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustMainSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustSaveRequest;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSaveService;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSubmitService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 库存调整单
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SgOmsStoAdjustService {

    @Autowired
    private SgBStoAdjustSaveService saveService;

    @Autowired
    private SgBStoAdjustSubmitService submitService;

    /**
     * 创建、审核接口
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgOmsStoAdjustSaveResult> saveAndSubmit(SgOmsStoAdjustSaveRequest request) {
        log.info("start SgOmsStoAdjustService.saveAndSubmit request:{}", JSONObject.toJSONString(request));

        ValueHolderV14<SgOmsStoAdjustSaveResult> v14 =
                new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

        String billNo = request.getBillNo();

        if (Objects.isNull(request.getRequestType())) {
            request.setRequestType(SgConstants.REQUEST_TYPE_SAVE_AND_SUBMIT);
        }

        if (request.getRequestType() == SgConstants.REQUEST_TYPE_SAVE ||
                request.getRequestType() == SgConstants.REQUEST_TYPE_SAVE_AND_SUBMIT) {

            // 主表处理
            SgBStoAdjustSaveRequest sgRequest = new SgBStoAdjustSaveRequest();

            SgBStoAdjustMainSaveRequest mainRequest = new SgBStoAdjustMainSaveRequest();

            List<SgBStoAdjustItemSaveRequest> itemList = new ArrayList<>();

            mainRequest.setCpCStoreEcode(request.getCpCStoreEcode());
            mainRequest.setSgBAdjustPropId(request.getSgBAdjustPropId());
            mainRequest.setSourceBillType(request.getSourceBillType());
            mainRequest.setSourceBillId(request.getSourceBillId());
            mainRequest.setSourceBillNo(request.getSourceBillNo());
            mainRequest.setBillDate(request.getBillDate());
            mainRequest.setBillType(request.getBillType());

            // 明细处理
            request.getItems().forEach(item -> {
                SgBStoAdjustItemSaveRequest itemRequest = new SgBStoAdjustItemSaveRequest();
                itemRequest.setSourceBillItemId(item.getSourceBillItemId());

                itemRequest.setQty(item.getQty());
                itemRequest.setPsCSkuEcode(item.getPsCSkuEcode());
                itemRequest.setId(-1L);
                itemList.add(itemRequest);
            });

            // sgRequest 赋值相关字段
            sgRequest.setMainRequest(mainRequest);
            sgRequest.setItems(itemList);
            sgRequest.setLoginUser(request.getLoginUser());

            // 执行具体逻辑
            ValueHolderV14<SgR3BaseResult> result;
            try {
                result = saveService.save(sgRequest);
            } catch (Exception e) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("库存调整单创建异常");
                return v14;
            }

            // 返回结果
            SgOmsStoAdjustSaveResult omsStoInResult = new SgOmsStoAdjustSaveResult();
            billNo = result.getData().getBillNo();
            omsStoInResult.setBillNo(billNo);
            v14.setData(omsStoInResult);
        }

        if (request.getRequestType() == SgConstants.REQUEST_TYPE_SUBMIT
                || request.getRequestType() == SgConstants.REQUEST_TYPE_SAVE_AND_SUBMIT) {
            SgR3BaseRequest submitRequest = new SgR3BaseRequest();
            submitRequest.setBillNo(billNo);
            submitRequest.setLoginUser(request.getLoginUser());
            try {
                submitService.submit(submitRequest);
            } catch (Exception e) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("库存调整单审核异常");
                return v14;
            }
        }
        return v14;
    }
}
