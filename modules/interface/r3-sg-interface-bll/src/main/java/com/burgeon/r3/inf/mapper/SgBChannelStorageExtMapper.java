package com.burgeon.r3.inf.mapper;

import com.burgeon.r3.sg.core.model.table.oms.SgBChannelStorage;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR> 平台店铺商品表
 * 同步平台库存，云枢纽返回“商品删除“内容时，要根据平台条码id+条码id匹配平台店铺商品表记录，将可用修改为否，并且是否锁定更新为是；
 * Requirement<106976> 【库存中心】库存同步对应删除商品的处理
 * @version 1.0
 * @date 2021/11/24 10:59
 */
@Mapper
public interface SgBChannelStorageExtMapper extends ExtentionMapper<SgBChannelStorage> {
}
