//package com.burgeon.r3.inf.listener;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.aliyun.openservices.ons.api.Action;
//import com.aliyun.openservices.ons.api.ConsumeContext;
//import com.aliyun.openservices.ons.api.Message;
//import com.aliyun.openservices.ons.api.MessageListener;
//import com.aliyun.openservices.ons.api.impl.util.MsgConvertUtil;
//import com.burgeon.r3.inf.handler.CallBackApi;
//import com.burgeon.r3.sg.core.utils.ApplicationContextProvider;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//import org.springframework.util.StringUtils;
//
///**
// * <AUTHOR>
// * @time 2019/5/6 10:22
// * @descript 奇门回传
// */
//@Slf4j
//@Component
//public class WMSBackMessageRouteListenerBak implements MessageListener {
//
//    @Override
//    public Action consume(Message message, ConsumeContext consumeContext) {
//        String method;
//
//        try {
//            String messageBody = MsgConvertUtil.objectDeserialize(message.getBody()).toString();
//            if (log.isInfoEnabled()) {
//                log.info(LogUtil.format("接收wms回传mq消息 messageBody{},messageKey{},messageId{},messageTopic{}", "接收wms回传mq消息"), messageBody
//                        , message.getKey(), message.getMsgID(), message.getTopic());
//            }
//            JSONObject result = JSONObject.parseObject(messageBody);
//            method = (String) result.get("method");
//            CallBackApi callBackApi = ApplicationContextProvider.getBean(method, CallBackApi.class);
//            if (StringUtils.isEmpty(callBackApi)) {
//                log.error(LogUtil.format("服务暂未开放,methed.parameter-error,method:{}"), method);
//                return Action.CommitMessage;
//            }
//            ValueHolderV14 valueHolderV14 = callBackApi.apiProcess(messageBody);
//            if (valueHolderV14.isOK()) {
//                return Action.CommitMessage;
//            } else {
//                log.error(LogUtil.format("接收wms回传mq消息失败,result:{},method：{}"), JSON.toJSONString(valueHolderV14), method);
//                return Action.ReconsumeLater;
//            }
//        } catch (Exception e) {
//            log.error(LogUtil.format("WMSBackMessageRouteListener.consume.error：{}"), Throwables.getStackTraceAsString(e));
//            return Action.ReconsumeLater;
//        }
//    }
//}
