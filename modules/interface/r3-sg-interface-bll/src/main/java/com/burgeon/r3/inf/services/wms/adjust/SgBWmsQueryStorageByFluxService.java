package com.burgeon.r3.inf.services.wms.adjust;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.inf.mapper.SgBWmsToLsStorageMapper;
import com.burgeon.r3.inf.utils.FluxOssUtils;
import com.burgeon.r3.inf.utils.WmsTaskUtils;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.wms.SgBWmsToLsStorage;
import com.burgeon.r3.sg.core.utils.DateUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-08-16 10:24
 * @Description: 库存查询接口
 */
@Slf4j
@Component
public class SgBWmsQueryStorageByFluxService {

    @NacosValue("${r3.confirm.oss.file.filePath.flux:prod/wms/storage/}")
    private String filePath;

    private static final String FILE_NAME_PREFIX = "LogicWarehouselnventory-FULX-";
    private static final String FILE_NAME_SUFFIX = ".csv";

    @Autowired
    private SgBWmsToLsStorageMapper lsStorageMapper;

    @Autowired
    private FluxOssUtils fluxOssUtils;

    /**
     * 库存查询接口
     *
     * @return return
     */
    public ValueHolderV14 execute() {
        List<Map<String, String>> ossData = getOssData();
        if (CollectionUtils.isEmpty(ossData)) {
            return new ValueHolderV14(ResultCode.SUCCESS, "库存查询接口查询成功！文件解析无数据！");
        }
        log.info(LogUtil.format("库存查询接口查询,库存文件个数:{}",
                "SgBWmsQueryStorageByFluxService.execute"), ossData.size());

        /*日期减一*/
        Date date = new Date(System.currentTimeMillis() - 1000 * 60 * 60 * 24);
        Integer selectCount = lsStorageMapper.selectCount(new LambdaQueryWrapper<SgBWmsToLsStorage>()
                .eq(SgBWmsToLsStorage::getStockDate, date)
                .eq(SgBWmsToLsStorage::getWmsType, ThirdWmsTypeEnum.FLWMS.getCode()));

        if (selectCount > 0) {
            lsStorageMapper.delete(new LambdaQueryWrapper<SgBWmsToLsStorage>()
                    .eq(SgBWmsToLsStorage::getStockDate, date)
                    .eq(SgBWmsToLsStorage::getWmsType, ThirdWmsTypeEnum.FLWMS.getCode()));
        }

        List<SgBWmsToLsStorage> batchInsert = new ArrayList<>();
        for (Map<String, String> map : ossData) {
            try {
                SgBWmsToLsStorage sgWmsToLsStorage = new SgBWmsToLsStorage();
                sgWmsToLsStorage.setId(ModelUtil.getSequence(SgConstants.SG_B_WMS_TO_LS_STORAGE));
                //库存日期
                sgWmsToLsStorage.setStockDate(date);
                //仓库编码
                String ecode = map.get(String.valueOf(1));
                sgWmsToLsStorage.setWmsWarehouseCode(ecode);

                //商品编码
                String skuEcode = map.get(String.valueOf(2));
                sgWmsToLsStorage.setSkuEcode(skuEcode);

                //条码信息
                if (StringUtils.isNotEmpty(skuEcode)) {
                    PsCProSkuResult skuInfoByEcode = CommonCacheValUtils.getSkuInfoByEcode(skuEcode);
                    if (skuInfoByEcode != null) {
                        sgWmsToLsStorage.setPsCSkuId(skuInfoByEcode.getId());
                        sgWmsToLsStorage.setPsCSkuEcode(skuInfoByEcode.getSkuEcode());
                    }
                }

                //批次
                String produce = map.get(String.valueOf(3));
                String produceDate = StringUtils.isEmpty(produce) || SgConstantsIF.DEFAULT_PRODUCE_DATE.equals(produce) ?
                        SgConstantsIF.DEFAULT_PRODUCE_DATE : DateUtils.formatSync8(DateUtils.parseSync8(produce, DateUtils.PATTERN_DATE), DateUtils.DATE_PATTERN);
                sgWmsToLsStorage.setProduceDate(produceDate);

                //库存类型
                sgWmsToLsStorage.setStorageType(map.get(String.valueOf(4)));
                //在库量
                sgWmsToLsStorage.setQty(new BigDecimal(Optional.ofNullable(map.get(String.valueOf(5))).orElse("0")));

                StorageUtils.setBModelDefalutData(sgWmsToLsStorage, R3SystemUserResource.getSystemRootUser());
                batchInsert.add(sgWmsToLsStorage);
            } catch (Exception e) {
                log.error(LogUtil.format("库存查询接口查询执行报错，异常信息:{}",
                        "SgBWmsQueryStorageByFluxService.execute"), Throwables.getStackTraceAsString(e));
            }
        }

        convertWarehouseInfo(batchInsert);

        if (CollectionUtils.isNotEmpty(batchInsert)) {
            List<List<SgBWmsToLsStorage>> pageList = StorageUtils.getPageList(batchInsert, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
            pageList.forEach(x -> {
                lsStorageMapper.batchInsert(x);
            });
        }

        return new ValueHolderV14(ResultCode.SUCCESS, "库存查询接口查询成功！");
    }

    /**
     * 转化实体仓信息
     *
     * @param batchInsert 库存结果列表
     */
    private void convertWarehouseInfo(List<SgBWmsToLsStorage> batchInsert) {
        List<String> ecodeList = ListUtils.emptyIfNull(batchInsert).stream()
                .map(SgBWmsToLsStorage::getWmsWarehouseCode)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ecodeList)) {
            return;
        }

        Map<String, SgCpCPhyWarehouse> warehouseMap = WmsTaskUtils.queryMapByEcodeList(ecodeList);
        for (SgBWmsToLsStorage wmsToLsStorage : batchInsert) {
            if (StringUtils.isEmpty(wmsToLsStorage.getWmsWarehouseCode())
                    || !warehouseMap.containsKey(wmsToLsStorage.getWmsWarehouseCode())) {
                continue;
            }
            SgCpCPhyWarehouse warehouse = warehouseMap.get(wmsToLsStorage.getWmsWarehouseCode());

            wmsToLsStorage.setCpCPhyWarehouseId(warehouse.getId());
            wmsToLsStorage.setCpCPhyWarehouseEcode(warehouse.getEcode());
            wmsToLsStorage.setCpCPhyWarehouseEname(warehouse.getEname());
            wmsToLsStorage.setWmsType(warehouse.getWmsType());
        }
    }

    /**
     * 获取oss数据
     *
     * @return List<Map < String, String>>
     */
    private List<Map<String, String>> getOssData() {
        /* prod/wms/storage/LogicWarehouselnventory-FULX-yyyyMMdd.csv */
        String fileName = FILE_NAME_PREFIX + DateUtils.formatSync8(
                new Date(System.currentTimeMillis() - 1000 * 60 * 60 * 24), DateUtils.DATE_PATTERN) + FILE_NAME_SUFFIX;

        List<Map<String, String>> lines = new ArrayList<>();
        try {
            int lineNumber = 0;

            log.info(LogUtil.format("库存查询文件获取，文件名：{},文件路径:{}",
                    "SgBWmsQueryStorageByFluxService.getOssData"), fileName, filePath);
            ValueHolderV14 ossFile = fluxOssUtils.getOSSFile(Boolean.TRUE, fileName, lineNumber, filePath);
            if (!ossFile.isOK()) {
                log.warn(LogUtil.format("库存查询文件获取-获取文件数据失败：{},行号：{}",
                        "SgBWmsQueryStorageByFluxService.getOssData"), fileName, lineNumber);
                return null;
            }

            JSONObject data = (JSONObject) ossFile.getData();
            if (Objects.isNull(data)) {
                log.warn(LogUtil.format("库存查询文件获取-获取文件数据为空:{},行号：{}",
                        "SgBWmsQueryStorageByFluxService.getOssData"), fileName, lineNumber);
                return null;
            }

            boolean hasNext = true;
            lines.addAll(data.getObject("record_list", ArrayList.class));
            lineNumber = data.getInteger("line_number");

            //如果存在多行要继续解析
            while (hasNext) {
                ValueHolderV14 holderV14 = fluxOssUtils.getOSSFile(Boolean.FALSE, fileName, lineNumber, filePath);
                if (!holderV14.isOK()) {
                    log.warn(LogUtil.format("库存查询文件获取-获取文件数据失败：{},行号：{}",
                            "SgBWmsQueryStorageByFluxService.getOssData"), fileName, lineNumber);
                    return null;
                }
                JSONObject json = (JSONObject) holderV14.getData();
                if (Objects.isNull(json)) {
                    log.warn(LogUtil.format("库存查询文件获取-获取文件数据为空:{},行号：{}",
                            "SgBWmsQueryStorageByFluxService.getOssData"), fileName, lineNumber);
                    return null;
                }
                hasNext = json.getBoolean("has_next");
                List<Map<String, String>> tempList = json.getObject("record_list", ArrayList.class);
                if (CollectionUtils.isNotEmpty(tempList)) {
                    lines.addAll(tempList);
                    lineNumber = json.getInteger("line_number");
                } else {
                    hasNext = false;
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("库存查询文件获取报错，文件路径：{},异常信息:{}",
                    "SgBWmsQueryStorageByFluxService.getOssData"), fileName, Throwables.getStackTraceAsString(e));
            return null;
        }

        return lines;
    }
}
