package com.burgeon.r3.inf.mapper;

import com.burgeon.r3.sg.inf.model.table.SgCSyncShareStorageResult;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@Mapper
public interface SgCSyncShareStorageResultMapper extends ExtentionMapper<SgCSyncShareStorageResult> {

    @Select({"<script>" +
            "SELECT " +
            " r.id  " +
            "FROM " +
            " sg_c_sync_share_storage_result r " +
            "<where> " +
            " r.isactive = 'Y'" +
            " <if test = 'storageResults != null and storageResults.size >0 '>" +
            " and (r.sg_c_share_store_id,r.ps_c_sku_ecode) in" +
            " <foreach collection='storageResults' item='storageResult' open='(' separator=',' close=')'> (#{storageResult.sgCShareStoreId},#{storageResult.psCSkuEcode}) </foreach>" +
            " </if>" +
            "<if test='startDate != null'>" +
            " and r.creationdate &gt;= #{endDate}" +
            "</if>" +
            "</where>" +
            "</script>"})
    List<Long> selectListByShareStoreIdAndPsCSkuEcode(@Param("storageResults") List<SgCSyncShareStorageResult> storageResults,@Param("startDate") Date startDate,@Param("endDate") Date endDate);
}