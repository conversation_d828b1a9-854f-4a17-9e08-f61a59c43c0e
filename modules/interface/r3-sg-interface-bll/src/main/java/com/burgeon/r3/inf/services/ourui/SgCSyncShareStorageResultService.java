package com.burgeon.r3.inf.services.ourui;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.r3.inf.mapper.SgCProductSystemShareLogMapper;
import com.burgeon.r3.inf.mapper.SgCSaSelectionLogMapper;
import com.burgeon.r3.inf.mapper.SgCSaSelectionMapper;
import com.burgeon.r3.inf.mapper.SgCSyncShareStorageResultMapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.basic.mapper.SgCShareStoreMapper;
import com.burgeon.r3.sg.basic.model.request.SgBShareStorageQueryRequest;
import com.burgeon.r3.sg.basic.model.result.SgBShareStorageQueryResult;
import com.burgeon.r3.sg.basic.services.SgBShareStorageQueryService;
import com.burgeon.r3.sg.basic.services.matrix.SgStorageUtilBase;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.channel.model.result.storage.SgCSaStorageResult;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.SaCategoryEnum;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.model.table.share.autodistribution.SgCEcomActivitySetting;
import com.burgeon.r3.sg.core.model.table.share.autodistribution.SgCEcomActivitySettingProItem;
import com.burgeon.r3.sg.core.model.table.share.autodistribution.SgCEcomActivitySettingSaItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.LogUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.inf.common.SgInfConstants;
import com.burgeon.r3.sg.inf.common.enums.SaAllowReturnEnum;
import com.burgeon.r3.sg.inf.model.request.ourui.ShareLogQueryParam;
import com.burgeon.r3.sg.inf.model.table.SgCProductSystemShareLog;
import com.burgeon.r3.sg.inf.model.table.SgCSaSelection;
import com.burgeon.r3.sg.inf.model.table.SgCSaSelectionLog;
import com.burgeon.r3.sg.inf.model.table.SgCSyncShareStorageResult;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.autodistribution.SgCEcomActivitySettingMapper;
import com.burgeon.r3.sg.share.mapper.autodistribution.SgCEcomActivitySettingProItemMapper;
import com.burgeon.r3.sg.share.mapper.autodistribution.SgCEcomActivitySettingSaItemMapper;
import com.burgeon.r3.sg.share.model.request.adjust.SgBShareAdjustBillSaveRequest;
import com.burgeon.r3.sg.share.model.request.adjust.SgBShareAdjustItemSaveRequest;
import com.burgeon.r3.sg.share.model.request.adjust.SgBShareAdjustSaveRequest;
import com.burgeon.r3.sg.share.model.request.allocation.*;
import com.burgeon.r3.sg.share.model.result.allocation.SgBShareAllocationReturnSaveAndSubmitResult;
import com.burgeon.r3.sg.share.services.adjust.SgBShareAdjustSaveAndSubmitService;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationReturnSaveAndSubmitService;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationSaveAndSubmitService;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.request.SkuQueryListRequest;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SgCSyncShareStorageResultService extends ServiceImpl<SgCSyncShareStorageResultMapper, SgCSyncShareStorageResult> {

    @Autowired
    private SgBShareStorageQueryService sgBShareStorageQueryService;

    @Autowired
    private SgCSaStoreMapper sgCSaStoreMapper;

    @Autowired
    private SgCShareStoreMapper shareStoreMapper;

    @Autowired
    private SgCShareStockSyncService sgCShareStockSyncService;

    @Autowired
    private SgCProductSystemShareLogMapper sgCProductSystemShareLogMapper;

    @Autowired
    private SgCSaSelectionMapper sgCSaSelectionMapper;

    @Autowired
    private SgCSaSelectionLogMapper sgCSaSelectionLogMapper;

    @Autowired
    private SgCEcomActivitySettingMapper sgCEcomActivitySettingMapper;

    @Autowired
    private SgCEcomActivitySettingSaItemMapper sgCEcomActivitySettingSaItemMapper;

    @Autowired
    private SgCEcomActivitySettingProItemMapper sgCEcomActivitySettingProItemMapper;

    @Autowired
    private SgBShareAllocationReturnSaveAndSubmitService shareAllocationReturnSaveAndSubmitService;

    @Autowired
    private SgBShareAdjustSaveAndSubmitService sgBShareAdjustSaveAndSubmitService;

    @Autowired
    private PropertiesConf propertiesConf;

    @Autowired
    private SgBShareAllocationSaveAndSubmitService allocationSaveAndSubmitService;

    public ValueHolderV14<String> executeSyncTask(Integer threadNum, Integer remainder) {
        SgCSyncShareStorageResultService storageResultService = ApplicationContextHandle.getBean(SgCSyncShareStorageResultService.class);
        ValueHolderV14<String> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        //查询当天需要同步的数据
        List<SgCProductSystemShareLog> sgCProductSystemShareLogs = new ArrayList<>();
        Date startDate = null;
        Date endDate = null;
        String now;
        String saSelectionLastSql;
        try {
            now = DateUtil.format(new Date(), "yyyy-MM-dd");
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            endDate = simpleDateFormat.parse(now);
            startDate = DateUtil.addDate(endDate, Calendar.DATE, -1);

            int batchInsertSize = propertiesConf.getProperty("sg.ourui.sync.batch.insert.size",1000);
            int retryCount = propertiesConf.getProperty("sg.ourui.sync.retry.count",3);

            //拼接查询last sql
            String systemShareLogLastSql = " limit " + batchInsertSize;
            saSelectionLastSql = " limit " + batchInsertSize;
            if(ObjectUtils.isNotEmpty(threadNum) && ObjectUtils.isNotEmpty(remainder) && threadNum > 1){
                systemShareLogLastSql = String.format(" having sg_c_share_store_id %% %s = %s limit %s", threadNum, remainder, batchInsertSize);
                saSelectionLastSql = String.format(" and sg_c_share_store_id %% %s = %s limit %s", threadNum, remainder, batchInsertSize);
            }

            List<SgCProductSystemShareLog> shareLogs = sgCProductSystemShareLogMapper.selectList(new LambdaQueryWrapper<SgCProductSystemShareLog>()
                    .ge(SgCProductSystemShareLog::getCreationdate, startDate)
                    .lt(SgCProductSystemShareLog::getCreationdate, endDate)
                    .le(SgCProductSystemShareLog::getSyncFailedCount, retryCount)
                    .and(
                            wrapper -> wrapper.eq(SgCProductSystemShareLog::getSyncStatus, SgConstants.SYNC_STATUS)
                                    .or().eq(SgCProductSystemShareLog::getSyncStatus, SgConstants.SYNC_FAIL_STATUS)
                    )
                    .eq(SgCProductSystemShareLog::getIsactive, SgConstants.IS_ACTIVE_Y)
                    .isNotNull(SgCProductSystemShareLog::getSgCShareStoreId)
                    .isNotNull(SgCProductSystemShareLog::getPsCSkuId)
                    .groupBy(SgCProductSystemShareLog::getSgCShareStoreId,SgCProductSystemShareLog::getPsCSkuId)
                    .last(systemShareLogLastSql));

            //防止漏查
            if (CollectionUtils.isNotEmpty(shareLogs)) {
                List<ShareLogQueryParam> queryParams = new ArrayList<>();
                shareLogs.stream().filter(s-> ObjectUtils.isNotEmpty(s.getSgCShareStoreId()) && ObjectUtils.isNotEmpty(s.getPsCSkuId())).forEach(shareLog -> {
                    ShareLogQueryParam shareLogQueryParam = new ShareLogQueryParam();
                    shareLogQueryParam.setShareStoreId(shareLog.getSgCShareStoreId());
                    shareLogQueryParam.setPsCSkuId(shareLog.getPsCSkuId());
                    queryParams.add(shareLogQueryParam);
                });

                if(CollectionUtils.isNotEmpty(queryParams)){
                    sgCProductSystemShareLogs = sgCProductSystemShareLogMapper.queryListByCondition(queryParams, startDate, endDate);
                }
            }

            if(null == sgCProductSystemShareLogs){
                sgCProductSystemShareLogs = new ArrayList<>();
            }

        } catch (Exception ex) {
            LogUtils.printLog(" SgCSyncShareStorageResultService executeSyncTask sgCProductSystemShareLogMapper selectList 查询失败 cause = {}", Throwables.getStackTraceAsString(ex));
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("查询待同步数据失败 cause = " + Throwables.getStackTraceAsString(ex));
            return holderV14;
        }

        LogUtils.printLog(" SgCSyncShareStorageResultService executeSyncTask  欧睿商品系统库存查询结果 size = {}",
                sgCProductSystemShareLogs.size());

        //查询逻辑仓下所有的聚合仓
        Set<Long> shareStoreIds = sgCProductSystemShareLogs.stream().map(SgCProductSystemShareLog::getSgCShareStoreId).collect(Collectors.toSet());
        List<Long> psCSkuIds = sgCProductSystemShareLogs.stream().filter(log -> ObjectUtils.isNotEmpty(log.getPsCSkuId()))
                .map(SgCProductSystemShareLog::getPsCSkuId).distinct().collect(Collectors.toList());
        //是否修改同步中
        sgCProductSystemShareLogs.forEach(shareLog -> {
            shareLog.setSyncStatus(SgConstants.SYNC_ING_STATUS);
            shareLog.setSyncTime(new Date());
        });

        storageResultService.updateSyncStatus(sgCProductSystemShareLogs);

        //查询配销仓选款结果表
        List<SgCSaSelection> selectionList = new ArrayList<>();

        //欧睿库存日志清洗完才
        Long nowNumber = Long.valueOf(now.replace("-",""));
        if(CollectionUtils.isEmpty(sgCProductSystemShareLogs)){
            selectionList= sgCSaSelectionMapper.selectList(new LambdaQueryWrapper<SgCSaSelection>()
                    .eq(SgCSaSelection::getIsactive,SgConstants.IS_ACTIVE_Y)
                    .ge(SgCSaSelection::getEndTime, endDate)
                    .le(SgCSaSelection::getStartTime, endDate)
                    .and(w -> w.isNull(SgCSaSelection::getVersion).or().ne(SgCSaSelection::getVersion,nowNumber))
                    .last(saSelectionLastSql));
            if(CollectionUtils.isNotEmpty(selectionList)){
                shareStoreIds.addAll(selectionList.stream().map(SgCSaSelection::getSgCShareStoreId).distinct().collect(Collectors.toList()));
                psCSkuIds.addAll(selectionList.stream().map(SgCSaSelection::getPsCSkuId).distinct().collect(Collectors.toList()));
            }
            if(log.isDebugEnabled()){
                log.debug(" 查询选款结果表size：{}",selectionList.size());
            }
        }

        if(CollectionUtils.isEmpty(shareStoreIds) || CollectionUtils.isEmpty(psCSkuIds)){
            LogUtils.printLog(" SgCSyncShareStorageResultService executeSyncTask  聚合仓或者sku信息为空，聚合仓id-{}；sku-{}", shareStoreIds,psCSkuIds);
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("聚合仓或者sku信息为空");
            return holderV14;
        }

        if(log.isDebugEnabled()){
            log.debug(" 本次计算,聚合仓：{}，sku：{}",shareStoreIds,psCSkuIds);
        }

        //查询聚合仓档案
        List<SgCShareStore> sgCShareStores = shareStoreMapper.selectBatchIds(shareStoreIds);
        Map<Long, SgCShareStore> sgCShareStoreMap = sgCShareStores.stream().collect(Collectors.toMap(SgCShareStore::getId, Function.identity()));

        //根据聚合仓id 和 skuId查询聚合仓库存
        SgBShareStorageQueryRequest queryRequest = new SgBShareStorageQueryRequest();
        queryRequest.setSgCShareStoreIds(new ArrayList<Long>(shareStoreIds));
        queryRequest.setPsCSkuIds(psCSkuIds);
        ValueHolderV14<List<SgBShareStorageQueryResult>> valueHolderV14 = sgBShareStorageQueryService.queryStorage(queryRequest);
        if (!valueHolderV14.isOK()) {
            LogUtils.printLog(" SgCSyncShareStorageResultService executeSyncTask sgBShareStorageQueryService queryStorage 查询聚合仓库存失败 valueHolderV14 = {}", JSON.toJSONString(valueHolderV14));
            holderV14.setCode(valueHolderV14.getCode());
            holderV14.setMessage(valueHolderV14.getMessage());
            return holderV14;
        }
        List<SgBShareStorageQueryResult> shareStorageResult = valueHolderV14.getData();
        Map<Long, List<SgBShareStorageQueryResult>> shareIdMap = new HashMap<>();
        if (CollectionUtils.isEmpty(shareStorageResult)) {
            LogUtils.printLog(" SgCSyncShareStorageResultService executeSyncTask sgBShareStorageQueryService" +
                    " queryStorage 查询聚合仓库存结果集为空,shareIds:{},skuIds:{}",shareStoreIds,psCSkuIds);
        }else {
            shareIdMap.putAll(shareStorageResult.stream()
                    .collect(Collectors.groupingBy(SgBShareStorageQueryResult::getSgCShareStoreId)));
        }
        LogUtils.printLog(" SgCSyncShareStorageResultService executeSyncTask sgBShareStorageQueryService queryStorage 查询聚合仓库存结果集大小  shareStorageResult = {}", JSON.toJSONString(shareStorageResult));

        //查询所有配销仓
        List<SgCSaStore> sgCSaStoreList = sgCSaStoreMapper.selectList(new LambdaQueryWrapper<SgCSaStore>()
                .in(SgCSaStore::getSgCShareStoreId,shareStoreIds).eq(SgCSaStore::getIsactive, R3CommonResultConstants.VALUE_Y));
        if (CollectionUtils.isEmpty(sgCSaStoreList)) {
            LogUtils.printLog(" SgCSyncShareStorageResultService executeSyncTask sgBShareStorageQueryService queryStorage 查询聚合仓库存结果集为空,聚合仓id:{}",shareStoreIds);
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("查询配销仓结果集为空");
            return holderV14;
        }
        Map<Long,List<SgCSaStore>> saShareIdMap = sgCSaStoreList.stream().collect(Collectors.groupingBy(SgCSaStore::getSgCShareStoreId));

        //获取聚合仓 和对应条码条码下所有配销仓库存信息,  没有配销仓全当 0处理
        List<SgCSaStorageResult> saStorageList = sgCSaStoreMapper.queryStorageByShareStoreIdsAndSkuIds(new ArrayList<>(shareStoreIds), psCSkuIds);
        LogUtils.printLog(" SgCSyncShareStorageResultService executeSyncTask sgCSaStoreMapper queryStorageByShareStoreIdsAndSkuIds 查询配销仓库存结果集大小 saStorageList = {}", JSON.toJSONString(saStorageList));
        //key sgCShareStoreId 聚合仓主键id value: 聚合仓库存
        //key sgCShareStoreId 聚合仓主键id value: 该聚合仓下所有的配销库存
        Map<Long, List<SgCSaStorageResult>> saStorageResultMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(saStorageList)) {
            saStorageResultMap = saStorageList.stream().collect(Collectors.groupingBy(SgCSaStorageResult::getShareStoreId));
        }
        Map<Long, List<SgCSaStorageResult>> finalSaStorageResultMap = saStorageResultMap;

        //欧睿库存同步共享结果单
        List<SgCSyncShareStorageResult> storageResults = new ArrayList<>();
        //分货退货单请求
        List<SgBShareAllocationReturnBillSaveRequst> sgBShareAllocationReturnBillSaveRequsts = new ArrayList<>();
        // 共享调整单请求list
        List<SgBShareAdjustBillSaveRequest> sgBShareAdjustBillSaveRequests = new ArrayList<>();
        // 优先共享调整单 商品系统保留库存≥中台可售库存 并且 中台聚合仓共享分配-共享占用量不够最终共享量
        List<SgBShareAdjustBillSaveRequest> shareAdjustBillSaveRequests = new ArrayList<>();
        //  创建分货单请求参数
        List<SgBShareAllocationBillSaveRequst> shareAllocationBillSaveRequests = new ArrayList<>();

        //数据聚合, key : shareStoreId_psCSkuId
        Map<String, List<SgCProductSystemShareLog>> finalShareLogMap = new HashMap<>();
        sgCProductSystemShareLogs.forEach(shareLog -> {
            String key = shareLog.getSgCShareStoreId() + SgConstants.SG_CONNECTOR_MARKS_6 + shareLog.getPsCSkuId();
            if (!finalShareLogMap.containsKey(key)) {
                List<SgCProductSystemShareLog> systemShareLogs = new ArrayList<>();
                systemShareLogs.add(shareLog);
                finalShareLogMap.put(key, systemShareLogs);
                return;
            }
            List<SgCProductSystemShareLog> shareLogs = finalShareLogMap.get(key);
            shareLogs.add(shareLog);
        });


        finalShareLogMap.forEach((key, val) -> {
            //共享结果单对象
            SgCSyncShareStorageResult storageResult = new SgCSyncShareStorageResult();

            String[] splitStr = key.split(SgConstants.SG_CONNECTOR_MARKS_6);
            Long shareStoreId = Long.valueOf(splitStr[0]);
            Long psCSkuId = Long.valueOf(splitStr[1]);

            //主键id
            Long id = ModelUtil.getSequence(SgConstants.SG_C_SYNC_SHARE_STORAGE_RESULT);
            storageResult.setId(id);

            SgCShareStore sgCShareStore = sgCShareStoreMap.get(shareStoreId);
            if (ObjectUtils.isNotEmpty(sgCShareStore)) {
                storageResult.setSgCShareStoreEcode(sgCShareStore.getEcode());
                storageResult.setSgCShareStoreEname(sgCShareStore.getEname());
            }

            storageResult.setBillNo(SgStoreUtils.getBillNo(SgStoreConstants.SEQ_SG_C_SYNC_SHARE_STORAGE_RESULT,
                    SgConstants.SG_C_SYNC_SHARE_STORAGE_RESULT.toUpperCase(), storageResult, Locale.getDefault()));
            //storageResult.setBillNo(UUID.randomUUID().toString());

            storageResult.setSgCShareStoreId(shareStoreId);

            //单据日期
            storageResult.setBillDate(new Date());
            SgCProductSystemShareLog systemShareLog = val.get(0);
            //商品编码
            storageResult.setPsCProEcode(systemShareLog.getPsCProEcode());
            //条码编码
            storageResult.setPsCSkuEcode(systemShareLog.getPsCSkuEcode());
            //商品系统共享库存
            BigDecimal shareQty = BigDecimal.ZERO;
            //商品系统库存
            BigDecimal qty = BigDecimal.ZERO;
            //商品系统保留库存
            BigDecimal minQty = BigDecimal.ZERO;


            for (SgCProductSystemShareLog shareLog : val) {
                shareQty = shareQty.add(ObjectUtils.isEmpty(shareLog.getShareQty()) ? BigDecimal.ZERO : shareLog.getShareQty());
                qty = qty.add(ObjectUtils.isEmpty(shareLog.getQty()) ? BigDecimal.ZERO : shareLog.getQty());
                minQty = minQty.add(ObjectUtils.isEmpty(shareLog.getMinQty()) ? BigDecimal.ZERO : shareLog.getMinQty());
            }
            storageResult.setQtyProSysShareStorage(shareQty);
            storageResult.setQtyProSysStorage(qty);
            storageResult.setQtyProSysRetainStorage(minQty);

            if (ObjectUtils.isEmpty(shareStoreId)) {
                LogUtils.printLog("  SgCSyncShareStorageResultService executeSyncTask 当前逻辑仓未绑定聚合仓 cpCStoreEcode = {}", systemShareLog.getCpCStoreEcode());
                storageResult.setQtyShare(BigDecimal.ZERO);
                storageResult.setQtySaleAvailable(BigDecimal.ZERO);
                storageResult.setQtyFinalShare(BigDecimal.ZERO);

                StorageUtils.setBModelDefalutData(storageResult, R3SystemUserResource.getSystemRootUser());
                storageResults.add(storageResult);
                //直接进入下次循环
                return;
            }
            //计算中台可售库存 最新改版 中台可分配库存
            List<SgBShareStorageQueryResult> sgBShareStorageQueryResults = shareIdMap.get(shareStoreId);
            if (CollectionUtils.isEmpty(sgBShareStorageQueryResults)) {
                LogUtils.printLog("  SgCSyncShareStorageResultService executeSyncTask 当前聚合仓对应聚合仓库存为空 shareStoreId = {}", shareStoreId);

                storageResult.setQtyShare(BigDecimal.ZERO);
                storageResult.setQtySaleAvailable(BigDecimal.ZERO);
                storageResult.setQtyFinalShare(BigDecimal.ZERO);

                StorageUtils.setBModelDefalutData(storageResult, R3SystemUserResource.getSystemRootUser());
                storageResults.add(storageResult);
                return;
            }
            //对应条码的聚合仓库存
            SgBShareStorageQueryResult sgBShareStorageQueryResult = sgBShareStorageQueryResults.stream().filter(sgBShareStorageQueryResult1 -> sgBShareStorageQueryResult1.getPsCSkuId().equals(psCSkuId)).findFirst().orElse(null);
            if (ObjectUtils.isEmpty(sgBShareStorageQueryResult)) {
                LogUtils.printLog("  SgCSyncShareStorageResultService executeSyncTask 当前条码编码聚合仓对应聚合仓库存为空 shareStoreId = {} psCSkuId = {}", shareStoreId, psCSkuId);

                storageResult.setQtyShare(BigDecimal.ZERO);
                storageResult.setQtySaleAvailable(BigDecimal.ZERO);
                storageResult.setQtyFinalShare(BigDecimal.ZERO);

                StorageUtils.setBModelDefalutData(storageResult, R3SystemUserResource.getSystemRootUser());
                storageResults.add(storageResult);
                return;
            }
            if(log.isDebugEnabled()){
                log.debug(" SgCSyncShareStorageResultService当前聚合仓库存：{}", JSON.toJSONString(sgBShareStorageQueryResult));
            }
            List<SgCSaStorageResult> sgCSaStorageResults = finalSaStorageResultMap.get(shareStoreId);
            if (CollectionUtils.isEmpty(sgCSaStorageResults)) {
                LogUtils.printLog("  SgCSyncShareStorageResultService executeSyncTask 该聚合仓下面的配销仓不存在 shareStoreId = {}", shareStoreId);
                sgCSaStorageResults = new ArrayList<>();
            }

            //计算中台库存
            Map<String, List<SgCSaStorageResult>> saStorageTypeMap = calculateOmsStorage(sgCSaStorageResults,
                    storageResult,sgBShareStorageQueryResult,psCSkuId,saShareIdMap.get(shareStoreId));
            if(CollectionUtils.isEmpty(saStorageTypeMap)){
                storageResult.setQtyShare(BigDecimal.ZERO);
                storageResult.setQtySaleAvailable(BigDecimal.ZERO);
                storageResults.add(storageResult);
                return;
            }

            //共享配销仓占用量
            SgCSaStorageResult vipSaStorageResult = saStorageTypeMap.get(SaCategoryEnum.VIP_CATEGORY.getCode()).get(0);
            //非共享配销仓总占用量
            List<SgCSaStorageResult> saStorageResultList = saStorageTypeMap.get(SaCategoryEnum.NO_VIP_CATEGORY.getCode());

            //查询E-COM明细，存在配销仓明细或者商品明细，或者商品类型为赠品都不允许退回，将配销仓标记为不允许退回
            checkSku(saStorageResultList);

            //中台可继续共享库存
            BigDecimal qtyVendibilitySpec = storageResult.getQtySaleAvailableSpec();
            //中台保留库存
            BigDecimal qtyShareRetain = storageResult.getQtySaleRetain();
            //中台共享库存
            BigDecimal qtyShare = storageResult.getQtyShare();


            /*//最终共享量
            BigDecimal qtyFinalShare = this.calculateQtyFinalShare(storageResult, qtyVendibility, qtyShare);
            LogUtils.printLog(" SgCSyncShareStorageResultService executeSyncTask calculateQtyFinalShare 计算的最终共享量 qtyFinalShare = {}", qtyFinalShare);
            if (ObjectUtils.isEmpty(qtyFinalShare) || BigDecimal.ZERO.compareTo(qtyFinalShare) == 0) {

                storageResult.setQtyFinalShare(BigDecimal.ZERO);

                StorageUtils.setBModelDefalutData(storageResult, R3SystemUserResource.getSystemRootUser());
                storageResults.add(storageResult);
                return;
            }*/

            //比较最终共享量和 聚合仓的可用量  决定是否需要使用分货退货单  拉回
            //获取商品系统的保留库存, 不能为负数,前阶段有校验
            BigDecimal qtyProSysRetainStorage = storageResult.getQtyProSysRetainStorage();

            //预计分货退货数量
            BigDecimal qtyAllocationReturn = BigDecimal.ZERO;
            //预计分货数量
            BigDecimal qtyAllocation = BigDecimal.ZERO;

            //计算欧瑞商品系统商品库存需分货、分货退货数
            //如果商品系统保留库存≤中台保留库存，则再比较商品系统共享库存和中台共享库存
            if(qtyProSysRetainStorage.compareTo(qtyShareRetain) <= 0){

                // 商品系统共享库存＜中台共享库存,分货退货数量 = 中台共享库存-商品系统共享库存
                if (shareQty.compareTo(qtyShare) < 0) {
                    //分货退货数量
                    qtyAllocationReturn = qtyShare.subtract(shareQty);
                } else {
                    //商品系统共享库存>中台共享库存
                    //最终共享量,分货数量为min（商品系统共享库存-中台共享库存，min（中台保留库存-商品系统保留库存，中台可继续共享库存））
                    qtyAllocation = shareQty.subtract(qtyShare).min(qtyShareRetain.subtract(qtyProSysRetainStorage).min(qtyVendibilitySpec));
                    if(qtyAllocation.compareTo(sgBShareStorageQueryResult.getQtyShareAvailable()) > 0){
                        //：中台可分配库存优先使用中台聚合仓的可用库存，不足时需要从【配销仓性质】为【非共享配销仓】的配销仓可用中退回
                        // （仅退回允许自动退回的配销仓），配销仓的优先级越小的越优先取
                        qtyAllocation = sgBShareStorageQueryResult.getQtyShareAvailable().add(buildSgBShareAllocationReturnBillSaveRequestParam(saStorageResultList,
                                qtyAllocation.subtract(sgBShareStorageQueryResult.getQtyShareAvailable()), storageResult, sgBShareAllocationReturnBillSaveRequsts));
                        sgBShareStorageQueryResult.setQtyShareAvailable(BigDecimal.ZERO);
                    }else {
                        sgBShareStorageQueryResult.setQtyShareAvailable(sgBShareStorageQueryResult.getQtyShareAvailable().subtract(qtyAllocation));
                    }
                }
            }else {
                //如果商品系统保留库存＞中台保留库
                //分货退货数量,，调整数量取min（商品系统保留库存-中台保留库存，中台共享配销仓的可用量)
                qtyAllocationReturn = qtyProSysRetainStorage.subtract(qtyShareRetain).min(qtyShare);
            }

            if(BigDecimal.ZERO.compareTo(qtyAllocationReturn) < 0){
                //实际退货分货数，创建分货退货单
                qtyAllocationReturn = buildSgBShareAllocationReturnBillSaveRequestParam(Collections.singletonList(vipSaStorageResult),
                        qtyAllocationReturn, storageResult, sgBShareAllocationReturnBillSaveRequsts);
            }else {
                qtyAllocationReturn = BigDecimal.ZERO;
            }

            if(BigDecimal.ZERO.compareTo(qtyAllocation) < 0){
                //创建最终的分货单  聚合仓则为当前的共享量对应的聚合仓、配销仓为该聚合仓下【配销仓性质】为【共享配销仓】对应的配销仓, 同一个聚合仓下唯品或者 共享配销仓只能有一个
                buildSgBShareAllocationBillSaveRequstsParams(vipSaStorageResult, qtyAllocation, storageResult, shareAllocationBillSaveRequests);
            }else {
                qtyAllocation = BigDecimal.ZERO;
            }


            LogUtils.printLog(" SgCSyncShareStorageResultService executeSyncTask calculateQtyFinalShare 实际最终共享量 qtyFinalShare = {}",qtyAllocation);
            //最终共享量即为最终分货数量-分货退货数量
            storageResult.setQtyFinalShare(qtyAllocation.subtract(qtyAllocationReturn));

            StorageUtils.setBModelDefalutData(storageResult, R3SystemUserResource.getSystemRootUser());
            storageResults.add(storageResult);
        });


        List<SgCSaSelectionLog> insertLogList = new ArrayList<>();
        selectionList.forEach(v -> {
            //计算商品选款结果表需分货、分货退货数
            Long shareStoreId = v.getSgCShareStoreId();
            Long psCSkuId = v.getPsCSkuId();
            SgCSaSelectionLog selectionLog = new SgCSaSelectionLog();
            BeanUtils.copyProperties(v,selectionLog);
            StorageUtils.setBModelDefalutData(selectionLog,R3SystemUserResource.getSystemRootUser());
            selectionLog.setQtyShareAvailable(BigDecimal.ZERO);
            selectionLog.setQtySharePre(BigDecimal.ZERO);
            selectionLog.setQtyShareActual(BigDecimal.ZERO);
            insertLogList.add(selectionLog);

            //计算中台可售库存 最新改版 中台可分配库存
            List<SgBShareStorageQueryResult> sgBShareStorageQueryResults = shareIdMap.get(shareStoreId);
            if (CollectionUtils.isEmpty(sgBShareStorageQueryResults)) {
                LogUtils.printLog("  SgCSyncShareStorageResultService executeSyncTask 当前聚合仓对应聚合仓库存为空 shareStoreId = {}", shareStoreId);
                return;
            }
            //对应条码的聚合仓库存
            SgBShareStorageQueryResult sgBShareStorageQueryResult = sgBShareStorageQueryResults.stream().filter(sgBShareStorageQueryResult1 -> sgBShareStorageQueryResult1.getPsCSkuId().equals(psCSkuId)).findFirst().orElse(null);
            if (ObjectUtils.isEmpty(sgBShareStorageQueryResult)) {
                LogUtils.printLog("  SgCSyncShareStorageResultService executeSyncTask 当前条码编码聚合仓对应聚合仓库存为空 shareStoreId = {} psCSkuId = {}", shareStoreId, psCSkuId);
                return;
            }
            if(log.isDebugEnabled()){
                log.debug(" SgCSyncShareStorageResultService当前聚合仓库存：{}", JSON.toJSONString(sgBShareStorageQueryResult));
            }

            //配销仓库存
            List<SgCSaStorageResult> sgCSaStorageResults = finalSaStorageResultMap.get(shareStoreId);
            if (CollectionUtils.isEmpty(sgCSaStorageResults)) {
                LogUtils.printLog("  SgCSyncShareStorageResultService executeSyncTask 该聚合仓下面的配销仓不存在 shareStoreId = {}", shareStoreId);
                sgCSaStorageResults = new ArrayList<>();
            }

            //共享结果单对象,用于承接计算完成的中台库存数据
            SgCSyncShareStorageResult storageResult = new SgCSyncShareStorageResult();
            storageResult.setId(v.getId());
            storageResult.setBillNo(v.getBatchNo());
            storageResult.setBillDate(new Date());
            storageResult.setPsCSkuEcode(v.getPsCSkuEcode());
            //计算中台库存
            Map<String, List<SgCSaStorageResult>> saStorageTypeMap = calculateOmsStorage(sgCSaStorageResults,
                    storageResult,sgBShareStorageQueryResult,psCSkuId,saShareIdMap.get(shareStoreId));
            if(CollectionUtils.isEmpty(saStorageTypeMap)){
                return;
            }

            //非共享配销仓总占用量
            List<SgCSaStorageResult> saStorageResultList = saStorageTypeMap.get(SaCategoryEnum.NO_VIP_CATEGORY.getCode());

            //中台可共享库存
            BigDecimal qtyVendibilitySpec = storageResult.getQtySaleAvailableSpec();
            selectionLog.setQtyShareAvailable(qtyVendibilitySpec);

            BigDecimal qtyAllocation = BigDecimal.ZERO;

            BigDecimal qtyAllocationReturn2 = BigDecimal.ZERO;
            if(ObjectUtils.isNotEmpty(v)){
                SgCSaStorageResult saStorageResult = sgCSaStorageResults.stream().filter(s ->
                        psCSkuId.equals(s.getPsCSkuId()) && v.getSgCSaStoreId().equals(s.getSaStoreId())).findFirst().get();
                if(ObjectUtils.isNotEmpty(saStorageResult)){
                    if(SaCategoryEnum.NO_VIP_CATEGORY.getCode().equals(saStorageResult.getCategory())){
                        //非共享配销仓
                        BigDecimal shareRatio = v.getShareRatio();
                        if(SgInfConstants.SA_SECTION_RATIO.equals(v.getShareType())){
                            //系数计算结果掐掉小数
                            shareRatio = qtyVendibilitySpec.subtract(qtyAllocation)
                                    .multiply(shareRatio).setScale(0, RoundingMode.DOWN);
                        }
                        selectionLog.setQtySharePre(shareRatio);

                        //a. 如果配销可用量＞结果表的共享量，则调用分货退货单服务，
                        // 分货退货数量取值：min（配销仓S可用量，配销可用量-结果表的共享量）；
                        if(saStorageResult.getQtyAvailable().compareTo(shareRatio) > 0){
                            qtyAllocationReturn2 = saStorageResult.getQtyAvailable().min(saStorageResult.getQtyAvailable().subtract(shareRatio));
                            buildSgBShareAllocationReturnBillSaveRequestParam(Collections.singletonList(saStorageResult),
                                    qtyAllocationReturn2, storageResult, sgBShareAllocationReturnBillSaveRequsts);
                        }else {
                            //c. 如果配销可用量＜结果表的共享量，则调用分货单服务，
                            // 分货单数量取值：min（中台可继续共享库存-欧瑞系统已调整的配销仓的可用量，结果表的共享量-配销可用量）
                            BigDecimal qtyAllocationPre = qtyVendibilitySpec.subtract(qtyAllocation)
                                    .min(shareRatio.subtract(saStorageResult.getQtyAvailable()));

                            if(qtyAllocationPre.compareTo(sgBShareStorageQueryResult.getQtyShareAvailable()) > 0){
                                //：中台可分配库存优先使用中台聚合仓的可用库存，不足时需要从【配销仓性质】为【非共享配销仓】的配销仓可用中退回
                                // （仅退回允许自动退回的配销仓），配销仓的优先级越小的越优先取
                                //排除当前配销仓的
                                List<SgCSaStorageResult> excludeSelfList = saStorageResultList.stream()
                                        .filter(i -> !i.getSaStoreId().equals(v.getSgCSaStoreId())).collect(Collectors.toList());
                                qtyAllocationPre = sgBShareStorageQueryResult.getQtyShareAvailable()
                                        .add(buildSgBShareAllocationReturnBillSaveRequestParam(excludeSelfList,
                                                qtyAllocationPre.subtract(sgBShareStorageQueryResult.getQtyShareAvailable()),
                                                storageResult, sgBShareAllocationReturnBillSaveRequsts));
                                sgBShareStorageQueryResult.setQtyShareAvailable(BigDecimal.ZERO);
                            }else {
                                sgBShareStorageQueryResult.setQtyShareAvailable(sgBShareStorageQueryResult.getQtyShareAvailable().subtract(qtyAllocationPre));
                            }
                            qtyAllocation = qtyAllocation.add(qtyAllocationPre);

                        }

                    }else {
                        //共享配销仓
                        //① 计算共享结果表的共享量（如果为按数量，则直接取结果表中的共享系数，
                        // 如果为按比例，则（中台可继续共享库存）*共享系数）
                        BigDecimal shareRatio = v.getShareRatio();
                        if(SgInfConstants.SA_SECTION_RATIO.equals(v.getShareType())){
                            //系数计算结果掐掉小数
                            shareRatio = qtyVendibilitySpec.subtract(qtyAllocation)
                                    .multiply(shareRatio).setScale(0, RoundingMode.DOWN);
                        }
                        selectionLog.setQtySharePre(shareRatio);

                        //② 调用新增分货单服务，分货单数量取值：
                        // min（共享结果表的共享量，中台实际可分配量-本批次已调整过的非共享配销仓的可用量
                        BigDecimal qtyAllocationPre = shareRatio.min(qtyVendibilitySpec.subtract(qtyAllocation));

                        if(qtyAllocationPre.compareTo(sgBShareStorageQueryResult.getQtyShareAvailable()) > 0){
                            //：中台可分配库存优先使用中台聚合仓的可用库存，不足时需要从【配销仓性质】为【非共享配销仓】的配销仓可用中退回
                            // （仅退回允许自动退回的配销仓），配销仓的优先级越小的越优先取
                            qtyAllocationPre = sgBShareStorageQueryResult.getQtyShareAvailable().add(buildSgBShareAllocationReturnBillSaveRequestParam(saStorageResultList,
                                    qtyAllocationPre.subtract(sgBShareStorageQueryResult.getQtyShareAvailable()), storageResult, sgBShareAllocationReturnBillSaveRequsts));
                            sgBShareStorageQueryResult.setQtyShareAvailable(BigDecimal.ZERO);
                        }else {
                            sgBShareStorageQueryResult.setQtyShareAvailable(sgBShareStorageQueryResult.getQtyShareAvailable().subtract(qtyAllocationPre));
                        }
                        qtyAllocation = qtyAllocation.add(qtyAllocationPre);
                    }
                    if(BigDecimal.ZERO.compareTo(qtyAllocation) < 0){
                        //创建最终的分货单  聚合仓则为当前的共享量对应的聚合仓、配销仓为该聚合仓下【配销仓性质】为【共享配销仓】对应的配销仓, 同一个聚合仓下唯品或者 共享配销仓只能有一个
                        buildSgBShareAllocationBillSaveRequstsParams(saStorageResult, qtyAllocation, storageResult, shareAllocationBillSaveRequests);
                    }
                }
            }
            selectionLog.setQtyShareActual(qtyAllocation.subtract(qtyAllocationReturn2));
        });

        if(log.isDebugEnabled()){
            log.debug(" 欧瑞、选款库存计算结果，分货单：{}，分货退货单：{}",JSON.toJSONString(shareAllocationBillSaveRequests),JSON.toJSONString(sgBShareAllocationReturnBillSaveRequsts));
        }

        //数据合并  待和产品确定 数据是否合并
        List<SgBShareAllocationReturnBillSaveRequst> mergeAllocationReturnBillSaveRequsts = new ArrayList<>();
        List<SgBShareAdjustBillSaveRequest> mergeAdjustBillSaveRequests = new ArrayList<>();
        List<SgBShareAdjustBillSaveRequest> mergePriorityAdjustBillSaveRequests = new ArrayList<>();
        List<SgBShareAllocationBillSaveRequst> mergeAllocationBillSaveRequests = new ArrayList<>();
        mergeDataList(sgBShareAllocationReturnBillSaveRequsts, sgBShareAdjustBillSaveRequests, shareAdjustBillSaveRequests, mergeAllocationReturnBillSaveRequsts, mergeAdjustBillSaveRequests, mergePriorityAdjustBillSaveRequests, shareAllocationBillSaveRequests, mergeAllocationBillSaveRequests);
        //更改数据库
        try {
            storageResultService.createOuRuiShareStorageResult(storageResults, mergeAllocationReturnBillSaveRequsts, mergeAdjustBillSaveRequests, mergePriorityAdjustBillSaveRequests, mergeAllocationBillSaveRequests, startDate, endDate);
        } catch (Exception e) {
            //设置数据的状态 为同步失败
            sgCProductSystemShareLogs.stream().map(shareLog -> {
                shareLog.setSyncStatus(SgConstants.SYNC_FAIL_STATUS);
                shareLog.setSyncTime(new Date());
                shareLog.setRemark(e.getMessage());
                shareLog.setSyncFailedCount(shareLog.getSyncFailedCount() == null ? 1 : shareLog.getSyncFailedCount() + 1);
                return shareLog;
            }).collect(Collectors.toList());

            storageResultService.updateSyncStatus(sgCProductSystemShareLogs);
            AssertUtils.logAndThrowException("定时任务创建欧睿库存同步共享结果单或分货退货单或共享调整单失败 ", e, R3SystemUserResource.getSystemRootUser().getLocale());
        }

        //更新欧睿日志表同步状态为成功
        sgCProductSystemShareLogs.forEach(shareLog -> {
            shareLog.setSyncStatus(SgConstants.SYNC_SUCCESS_STATUS);
            shareLog.setSyncTime(new Date());
        });

        storageResultService.updateSyncStatus(sgCProductSystemShareLogs);


        //更新选款结果表修改时间，下次任务执行不捞取修改时间超过配置时间的记录防止重复计算
        List<Long> saSelectionIds = selectionList.stream().map(SgCSaSelection::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(saSelectionIds)){
            SgCSaSelection selection = new SgCSaSelection();
            selection.setModifierid(SystemUserResource.ROOT_USER_ID);
            selection.setModifiername(SystemUserResource.ROOT_USER_NAME);
            selection.setModifierename(SystemUserResource.ROOT_ENAME);
            selection.setModifieddate(new Date());
            selection.setVersion(nowNumber);
            sgCSaSelectionMapper.update(selection,new LambdaQueryWrapper<SgCSaSelection>().in(SgCSaSelection::getId,saSelectionIds));
        }

        //插入每日选款结果日志
        if(CollectionUtils.isNotEmpty(insertLogList)){
            sgCSaSelectionLogMapper.batchInsert(insertLogList);
        }

        return holderV14;
    }

    /**
     * 查询E-COM明细，存在配销仓明细或者商品明细，或者商品类型为赠品都不允许退回
     * @param saStorageResultList
     */
    private void checkSku(List<SgCSaStorageResult> saStorageResultList){

        if(org.apache.commons.collections4.CollectionUtils.isEmpty(saStorageResultList)){
            return;
        }

        List<Long> skuIds = saStorageResultList.stream().map(SgCSaStorageResult::getPsCSkuId).collect(Collectors.toList());
        List<Long> saIds = saStorageResultList.stream().map(SgCSaStorageResult::getSaStoreId).collect(Collectors.toList());

        List<SkuQueryListRequest> skuQueryListRequests = new ArrayList<>();
        List<PsCPro> proList = new ArrayList<>();
        try {
            skuQueryListRequests = CommonCacheValUtils.querySkuListByIds(skuIds);
            if(CollectionUtils.isNotEmpty(skuQueryListRequests)){
                proList = CommonCacheValUtils.queryProListByIds(skuQueryListRequests.stream()
                        .map(SkuQueryListRequest::getPsCProId).collect(Collectors.toList()));
            }
        }catch (Exception e){
            log.warn(" 查询商品信息异常：{}",Throwables.getStackTraceAsString(e));
        }
        if(log.isDebugEnabled()){
            log.debug(" 欧瑞库存同步查询sku结果{},pro:{}", JSON.toJSONString(skuQueryListRequests),JSON.toJSONString(proList));
        }
        if(CollectionUtils.isEmpty(skuQueryListRequests) || CollectionUtils.isEmpty(proList)){
            return;
        }

        Map<Long,Long> skuProMap = skuQueryListRequests.stream()
                .collect(Collectors.toMap(SkuQueryListRequest::getId,SkuQueryListRequest::getPsCProId));
        Map<Long,PsCPro> proMap = proList.stream().collect(Collectors.toMap(PsCPro::getId,Function.identity()));
        List<Long> proIds = skuProMap.values().stream().distinct().collect(Collectors.toList());

        Date now = new Date();
        List<Long> settingIds = new ArrayList<>();
        List<SgCEcomActivitySettingProItem> activitySettingProItems = sgCEcomActivitySettingProItemMapper
                .selectList(new LambdaQueryWrapper<SgCEcomActivitySettingProItem>()
                        .eq(SgCEcomActivitySettingProItem::getIsactive, R3CommonResultConstants.VALUE_Y)
                        .in(SgCEcomActivitySettingProItem::getPsCProId,proIds)
                        .lt(SgCEcomActivitySettingProItem::getBeginTime,now)
                        .gt(SgCEcomActivitySettingProItem::getEndTime,now));
        if(log.isDebugEnabled()){
            log.debug(" 欧瑞库存同步查询E-COM商品明细结果{}", JSON.toJSONString(activitySettingProItems));
        }
        List<SgCEcomActivitySettingSaItem> activitySettingSaItems = sgCEcomActivitySettingSaItemMapper
                .selectList(new LambdaQueryWrapper<SgCEcomActivitySettingSaItem>()
                        .eq(SgCEcomActivitySettingSaItem::getIsactive, R3CommonResultConstants.VALUE_Y)
                        .in(SgCEcomActivitySettingSaItem::getSgCSaStoreId,saIds)
                        .lt(SgCEcomActivitySettingSaItem::getBeginTime,now)
                        .gt(SgCEcomActivitySettingSaItem::getEndTime,now));
        if(log.isDebugEnabled()){
            log.debug(" 欧瑞库存同步查询E-COM配销仓明细结果{}", JSON.toJSONString(activitySettingSaItems));
        }

        if(CollectionUtils.isNotEmpty(activitySettingProItems)){
            settingIds.addAll(activitySettingProItems.stream()
                    .map(SgCEcomActivitySettingProItem::getSgCEcomActivitySettingId).collect(Collectors.toList()));
        }
        if(CollectionUtils.isNotEmpty(activitySettingSaItems)){
            settingIds.addAll(activitySettingSaItems.stream()
                    .map(SgCEcomActivitySettingSaItem::getSgCEcomActivitySettingId).collect(Collectors.toList()));
        }
        settingIds = settingIds.stream().distinct().collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(settingIds)){
            List<SgCEcomActivitySetting> activitySettings = sgCEcomActivitySettingMapper
                    .selectList(new LambdaQueryWrapper<SgCEcomActivitySetting>()
                            .in(SgCEcomActivitySetting::getId,settingIds)
                            .eq(SgCEcomActivitySetting::getStatus,2)
                            .eq(SgCEcomActivitySetting::getIsactive,R3CommonResultConstants.VALUE_Y));
            if(CollectionUtils.isNotEmpty(activitySettings)){
                settingIds = activitySettings.stream().map(SgCEcomActivitySetting::getId).collect(Collectors.toList());
            }else {
                settingIds = new ArrayList<>();
            }
            if(log.isDebugEnabled()){
                log.debug(" 欧瑞库存同步查询E-COM配销仓结果{}", JSON.toJSONString(activitySettings));
            }
        }

        List<Long> existProIds = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(activitySettingProItems)){
            for(SgCEcomActivitySettingProItem proItem : activitySettingProItems){
                if(settingIds.contains(proItem.getSgCEcomActivitySettingId())){
                    existProIds.add(proItem.getPsCProId());
                }
            }
        }
        List<Long> existSaIds = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(activitySettingSaItems)){
            for(SgCEcomActivitySettingSaItem saItem : activitySettingSaItems){
                if(settingIds.contains(saItem.getSgCEcomActivitySettingId())){
                    existSaIds.add(saItem.getSgCSaStoreId());
                }
            }
        }

        saStorageResultList.forEach(saStorage -> {
            PsCPro skuResult = proMap.get(skuProMap.get(saStorage.getPsCSkuId()));
            if(ObjectUtils.isEmpty(skuResult) || R3CommonResultConstants.VALUE_Y.equals(skuResult.getIsGift()) ||
                    existProIds.contains(skuProMap.get(saStorage.getPsCSkuId())) ||
                    existSaIds.contains(saStorage.getSaStoreId())){
                saStorage.setIsAllowReturn(0);
            }
        });

    }


    private Map<String, List<SgCSaStorageResult>> calculateOmsStorage(List<SgCSaStorageResult> sgCSaStorageResults,SgCSyncShareStorageResult storageResult,
                                                         SgBShareStorageQueryResult sgBShareStorageQueryResult, Long psCSkuId,List<SgCSaStore> saStoreList){

        if (CollectionUtils.isEmpty(saStoreList)) {
            storageResult.setQtyFinalShare(BigDecimal.ZERO);
            storageResult.setRemark("聚合仓[" + sgBShareStorageQueryResult.getSgCShareStoreId() + "]下配销仓信息为空");
            StorageUtils.setBModelDefalutData(storageResult, R3SystemUserResource.getSystemRootUser());
            return null;
        }

        List<String> existKey = sgCSaStorageResults.stream().map(r -> Optional.ofNullable(r.getSaStoreId()).orElse(0L)+
                        SgConstants.SG_CONNECTOR_MARKS_6 + Optional.ofNullable(r.getPsCSkuId()).orElse(0L))
                .distinct().collect(Collectors.toList());

        //如果没有查到对应sku的配销仓库存信息，初始化配销仓库存为0
        saStoreList.stream().filter(sa -> !existKey.contains(sa.getId() + SgConstants.SG_CONNECTOR_MARKS_6 + psCSkuId)).forEach(sa -> {
            SgCSaStorageResult initResult = new SgCSaStorageResult();
            initResult.setSaStoreId(sa.getId());
            initResult.setSaEcode(sa.getEcode());
            initResult.setSaEname(sa.getEname());
            initResult.setShareStoreId(sgBShareStorageQueryResult.getSgCShareStoreId());
            initResult.setPriority(sa.getOrderno());
            initResult.setIsAllowReturn(sa.getIsAllowReturn());
            initResult.setCategory(sa.getCategory());
            initResult.setPsCSkuId(psCSkuId);
            initResult.setQtyAvailable(BigDecimal.ZERO);
            initResult.setQtyPreout(BigDecimal.ZERO);
            initResult.setQtyStorage(BigDecimal.ZERO);
            sgCSaStorageResults.add(initResult);
        });

        //获取sku对应配销仓库存，根据配销仓类型分组, key为是否为唯品会类型
        Map<String, List<SgCSaStorageResult>> saStorageTypeMap = sgCSaStorageResults.stream()
                .filter(sgCSaStorageResult -> psCSkuId.equals(sgCSaStorageResult.getPsCSkuId()))
                .collect(Collectors.groupingBy(SgCSaStorageResult::getCategory));
        //获取唯品会配销仓总在库量
        List<SgCSaStorageResult> vipResultList = saStorageTypeMap.get(SaCategoryEnum.VIP_CATEGORY.getCode());
        if(log.isDebugEnabled()){
            log.debug(" SgCSyncShareStorageResultService当前聚合仓下配销仓库存：{}", JSON.toJSONString(sgCSaStorageResults));
        }
        if (CollectionUtils.isEmpty(vipResultList) || vipResultList.size() > 1) {
            storageResult.setQtyFinalShare(BigDecimal.ZERO);
            storageResult.setRemark("聚合仓[" + sgBShareStorageQueryResult.getSgCShareStoreId() + "]下共享配销仓不存在或存在多个,需求只能有一个");
            StorageUtils.setBModelDefalutData(storageResult, R3SystemUserResource.getSystemRootUser());
            return null;
        }
        BigDecimal qtyStorageVip;
        qtyStorageVip = vipResultList.stream().filter(sgCSaStorageResult -> ObjectUtils.isNotEmpty(sgCSaStorageResult.getQtyStorage()) &&
                psCSkuId.equals(sgCSaStorageResult.getPsCSkuId())).map(SgCSaStorageResult::getQtyStorage).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal qtyAvalibleVip = vipResultList.stream().filter(sgCSaStorageResult -> ObjectUtils.isNotEmpty(sgCSaStorageResult.getQtyStorage()) &&
                psCSkuId.equals(sgCSaStorageResult.getPsCSkuId())).map(SgCSaStorageResult::getQtyAvailable).reduce(BigDecimal.ZERO, BigDecimal::add);
        //非唯品会配销仓总占用量
        List<SgCSaStorageResult> saStorageResultList = saStorageTypeMap.get(SaCategoryEnum.NO_VIP_CATEGORY.getCode());
        BigDecimal qtyPreout = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(saStorageResultList)) {
            qtyPreout = saStorageResultList.stream().filter(sgCSaStorageResult -> ObjectUtils.isNotEmpty(sgCSaStorageResult.getQtyPreout()) && psCSkuId.equals(sgCSaStorageResult.getPsCSkuId())).map(SgCSaStorageResult::getQtyPreout).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        //可售库存
        //中台可售库存	中台聚合仓的在库量 - 冻结量 -非共享占用量 - 非唯品会共享类型的配销占用量 - 共享分配量-唯品会共享类型的配销仓在库量
        BigDecimal qtyVendibility = (ObjectUtils.isEmpty(sgBShareStorageQueryResult.getQtyStoStorage()) ? BigDecimal.ZERO : sgBShareStorageQueryResult.getQtyStoStorage())  //总在库库存
                .subtract(ObjectUtils.isEmpty(sgBShareStorageQueryResult.getQtyStoFreeze()) ? BigDecimal.ZERO : sgBShareStorageQueryResult.getQtyStoFreeze()) //总冻结
                .subtract(ObjectUtils.isEmpty(sgBShareStorageQueryResult.getQtyStoPreout()) ? BigDecimal.ZERO : sgBShareStorageQueryResult.getQtyStoPreout()) //非共享占用 (即逻辑仓占用库存)
                .subtract(ObjectUtils.isEmpty(qtyPreout) ? BigDecimal.ZERO : qtyPreout) //非唯品会类型配销仓占用
                .subtract(ObjectUtils.isEmpty(sgBShareStorageQueryResult.getQtySpAllocation()) ? BigDecimal.ZERO : sgBShareStorageQueryResult.getQtySpAllocation()) //共享分配
                .subtract(ObjectUtils.isEmpty(qtyStorageVip) ? BigDecimal.ZERO : qtyStorageVip); //唯品会配销仓在库量
        //中台可售库存
        storageResult.setQtySaleAvailable(qtyVendibility);

        //计算中台可继续共享库存: 去除不允许退回非唯品配销可用
        //中台聚合仓的在库量 - 冻结量 -非共享占用量 - 非唯品会共享类型的配销占用量(允许自动退回) - 共享分配量-唯品会共享类型的配销仓在库量 - 非唯品会共享类型的配销在库量
        //非唯品会共享类型的配销占用量(允许自动退回)
        AtomicReference<BigDecimal> qtyPreoutAllowReturn = new AtomicReference<>(BigDecimal.ZERO);
        //非唯品会共享类型的配销在库量
        AtomicReference<BigDecimal> qtyStorage = new AtomicReference<>(BigDecimal.ZERO);
        if (CollectionUtils.isNotEmpty(saStorageResultList)) {
            saStorageResultList.stream().filter(sgCSaStorageResult ->
                    {
                        if (sgCSaStorageResult.getQtyStorage() == null) {
                            sgCSaStorageResult.setQtyStorage(BigDecimal.ZERO);
                        }
                        if (sgCSaStorageResult.getQtyPreout() == null) {
                            sgCSaStorageResult.setQtyPreout(BigDecimal.ZERO);
                        }

                        return psCSkuId.equals(sgCSaStorageResult.getPsCSkuId());
                    }
            ).forEach(sgCSaStorageResult -> {
                if (SaAllowReturnEnum.YES.getCode().equals(sgCSaStorageResult.getIsAllowReturn())) {
                    //允许回退
                    qtyPreoutAllowReturn.set(qtyPreoutAllowReturn.get().add(sgCSaStorageResult.getQtyPreout()));
                }else {
                    qtyStorage.set(qtyStorage.get().add(sgCSaStorageResult.getQtyStorage()));
                }


            });
        }

        BigDecimal qtyVendibilitySpec = (ObjectUtils.isEmpty(sgBShareStorageQueryResult.getQtyStoStorage()) ? BigDecimal.ZERO : sgBShareStorageQueryResult.getQtyStoStorage())  //总在库库存
                .subtract(ObjectUtils.isEmpty(sgBShareStorageQueryResult.getQtyStoFreeze()) ? BigDecimal.ZERO : sgBShareStorageQueryResult.getQtyStoFreeze()) //总冻结
                .subtract(ObjectUtils.isEmpty(sgBShareStorageQueryResult.getQtyStoPreout()) ? BigDecimal.ZERO : sgBShareStorageQueryResult.getQtyStoPreout()) //非共享占用 (即逻辑仓占用库存)
                .subtract(ObjectUtils.isEmpty(qtyPreoutAllowReturn.get()) ? BigDecimal.ZERO : qtyPreoutAllowReturn.get()) //非唯品会共享类型的配销占用量(允许自动退回)
                .subtract(ObjectUtils.isEmpty(sgBShareStorageQueryResult.getQtySpAllocation()) ? BigDecimal.ZERO : sgBShareStorageQueryResult.getQtySpAllocation()) //共享分配
                .subtract(ObjectUtils.isEmpty(qtyStorageVip) ? BigDecimal.ZERO : qtyStorageVip) //唯品会配销仓在库量
                .subtract(ObjectUtils.isEmpty(qtyStorage.get()) ? BigDecimal.ZERO : qtyStorage.get());// - 非唯品会共享类型的配销在库量(不允许自动退回)

        storageResult.setQtySaleAvailableSpec(qtyVendibilitySpec);


        //中台共享库存 中台共享库存	中台聚合仓的共享分配量 + 唯品会共享类型配销仓的可用量
        BigDecimal qtyShare = (ObjectUtils.isEmpty(sgBShareStorageQueryResult.getQtySpAllocation()) ? BigDecimal.ZERO : sgBShareStorageQueryResult.getQtySpAllocation()) //共享分配量
                .add(ObjectUtils.isEmpty(qtyAvalibleVip) ? BigDecimal.ZERO : qtyAvalibleVip);// 唯品会配销仓可用量
        //中台共享库存
        storageResult.setQtyShare(qtyShare);

        //中台保留库存
//        BigDecimal qtyShareRetain = Optional.ofNullable(sgBShareStorageQueryResult.getQtyStoStorage())
//                .orElse(BigDecimal.ZERO).subtract(qtyShare);
        //20220226--保留库存等于可继续共享库存
        storageResult.setQtySaleRetain(qtyVendibilitySpec);

        if(log.isDebugEnabled()){
            log.debug(" SgCSyncShareStorageResultService共享结果表初步计算结果：{}", JSON.toJSONString(storageResult));
        }
        return saStorageTypeMap;
    }


    private void buildSgBShareAllocationBillSaveRequstsParams(SgCSaStorageResult cSaStorageResult, BigDecimal qtyFinalShare, SgCSyncShareStorageResult storageResult, List<SgBShareAllocationBillSaveRequst> shareAllocationBillSaveRequests) {
        if (ObjectUtils.isEmpty(cSaStorageResult)) {
            return;
        }
        // 构建request
        SgBShareAllocationBillSaveRequst request = new SgBShareAllocationBillSaveRequst();
        SgBShareAllocationSaveRequst mainRequest = new SgBShareAllocationSaveRequst();

        // 处理主表数据
        mainRequest.setBillDate(new Date());
        mainRequest.setStatus(SgShareConstants.BILL_STATUS_UNSUBMIT);
        mainRequest.setServiceNode(SgShareConstants.SHARE_STORAGE_RESULT_SERVICE_NODE);
        mainRequest.setSourceBillId(storageResult.getId());
        mainRequest.setSourceBillNo(storageResult.getBillNo());
        mainRequest.setSourceBillType(ObjectUtils.isEmpty(storageResult.getQtyProSysRetainStorage()) ?
                SgConstantsIF.BILL_TYPE_SA_SELECTION_RESULT : SgConstantsIF.BILL_OURUI_SHARE_RESULT);
        mainRequest.setSgCShareStoreId(cSaStorageResult.getShareStoreId());
        mainRequest.setTotQty(qtyFinalShare);
        mainRequest.setRemark((ObjectUtils.isEmpty(storageResult.getQtyProSysRetainStorage()) ?
                "选款结果表批次号" : "欧睿库存共享结果单编号") + "【" + storageResult.getBillNo() + "】生成");
        mainRequest.setSourceBillDate(storageResult.getBillDate());
        List<SgBShareAllocationSaveItemRequst> itemRequestList = new ArrayList<>();

        SgBShareAllocationSaveItemRequst itemRequest = new SgBShareAllocationSaveItemRequst();
        // 处理明细数据
        itemRequest.setSgCSaStoreId(cSaStorageResult.getSaStoreId());
        itemRequest.setPsCSkuId(cSaStorageResult.getPsCSkuId());
        itemRequest.setPsCSkuEcode(storageResult.getPsCSkuEcode());
        itemRequest.setQty(qtyFinalShare);

        if(ObjectUtils.isEmpty(storageResult.getSgCSaStoreIds())){
            storageResult.setSgCSaStoreIds(cSaStorageResult.getSaStoreId() + "");
        }else {
            storageResult.setSgCSaStoreIds(storageResult.getSgCSaStoreIds() + "," + cSaStorageResult.getSaStoreId());
        }

        itemRequest.setId(-1L);
        itemRequestList.add(itemRequest);
        request.setR3(true);
        request.setObjId(-1L);
        request.setLoginUser(R3SystemUserResource.getSystemRootUser());
        request.setSgBShareAllocationSaveRequst(mainRequest);
        request.setSgBShareAllocationSaveItemRequsts(itemRequestList);

        shareAllocationBillSaveRequests.add(request);

    }

    //对数据进行清洗
    public void mergeDataList(List<SgBShareAllocationReturnBillSaveRequst> allocationReturnBillSaveRequsts, List<SgBShareAdjustBillSaveRequest> sgBShareAdjustBillSaveRequests, List<SgBShareAdjustBillSaveRequest> shareAdjustBillSaveRequests,
                              List<SgBShareAllocationReturnBillSaveRequst> mergeAllocationReturnBillSaveRequsts, List<SgBShareAdjustBillSaveRequest> mergeAdjustBillSaveRequests, List<SgBShareAdjustBillSaveRequest> mergePriorityAdjustBillSaveRequests,
                              List<SgBShareAllocationBillSaveRequst> shareAllocationBillSaveRequests, List<SgBShareAllocationBillSaveRequst> mergeAllocationBillSaveRequests) {
        LogUtils.printLog(" SgCSyncShareStorageResultService executeSyncTask 合并前 allocationReturnBillSaveRequsts = {} sgBShareAdjustBillSaveRequests = {} shareAdjustBillSaveRequests = {}", JSON.toJSONString(allocationReturnBillSaveRequsts), JSON.toJSONString(sgBShareAdjustBillSaveRequests), JSON.toJSONString(shareAdjustBillSaveRequests));
        if (CollectionUtils.isNotEmpty(allocationReturnBillSaveRequsts)) {
            // 数据合并使用   key sgCShareStoreId_sgCSaStoreId
            Map<String, SgBShareAllocationReturnBillSaveRequst> temMap = new HashMap<>();
            for (SgBShareAllocationReturnBillSaveRequst allocationReturnBillSaveRequst : allocationReturnBillSaveRequsts) {
                SgBShareAllocationReturnSaveRequst allocationReturnSaveRequst = allocationReturnBillSaveRequst.getAllocationReturnSaveRequst();
                List<SgBShareAllocationReturnItemSaveRequst> newAllocationReturnItemSaveRequst = allocationReturnBillSaveRequst.getAllocationReturnItemSaveRequst();
                String key = allocationReturnSaveRequst.getSgCShareStoreId() + SgConstants.SG_CONNECTOR_MARKS_6 + allocationReturnSaveRequst.getSgCSaStoreId();
                if (MapUtils.isEmpty(temMap) || !temMap.containsKey(key)) {
                    temMap.put(key, allocationReturnBillSaveRequst);
                    continue;
                }
                SgBShareAllocationReturnBillSaveRequst sgBShareAllocationReturnBillSaveRequst = temMap.get(key);
                SgBShareAllocationReturnSaveRequst oldSaveRequest = sgBShareAllocationReturnBillSaveRequst.getAllocationReturnSaveRequst();
                String remark = oldSaveRequest.getRemark();
                if (StringUtils.isNotEmpty(remark) && remark.contains(SgConstants.SG_CONNECTOR_MARKS_9)) {
                    String[] splitStr = remark.split(SgConstants.SG_CONNECTOR_MARKS_9);
                    remark = splitStr[0] + SgConstants.SG_CONNECTOR_MARKS_3 + allocationReturnSaveRequst.getBillNo() + splitStr[1];
                }
                oldSaveRequest.setRemark(remark);

                List<SgBShareAllocationReturnItemSaveRequst> oldItemSaveRequsts = sgBShareAllocationReturnBillSaveRequst.getAllocationReturnItemSaveRequst();

                for (SgBShareAllocationReturnItemSaveRequst itemSaveRequst : newAllocationReturnItemSaveRequst) {

                    SgBShareAllocationReturnItemSaveRequst sgBShareAllocationReturnItemSaveRequst = oldItemSaveRequsts.stream().filter(oldItemSaveRequst -> itemSaveRequst.getPsCSkuId().equals(oldItemSaveRequst.getPsCSkuId())).findFirst().orElse(null);
                    if (ObjectUtils.isNotEmpty(sgBShareAllocationReturnItemSaveRequst)) {

                        sgBShareAllocationReturnItemSaveRequst.setQty(sgBShareAllocationReturnItemSaveRequst.getQty().add(itemSaveRequst.getQty()));
                    } else {

                        oldItemSaveRequsts.add(itemSaveRequst);
                    }

                }
            }

            if (MapUtils.isNotEmpty(temMap)) {
                mergeAllocationReturnBillSaveRequsts.addAll(temMap.values());
            }
        }
        if (CollectionUtils.isNotEmpty(sgBShareAdjustBillSaveRequests)) {
            mergeShareAdjustBillSaveRequest(sgBShareAdjustBillSaveRequests, mergeAdjustBillSaveRequests);
        }
        if (CollectionUtils.isNotEmpty(shareAdjustBillSaveRequests)) {
            mergeShareAdjustBillSaveRequest(shareAdjustBillSaveRequests, mergePriorityAdjustBillSaveRequests);
        }
        //分货单请求合并
        if (CollectionUtils.isNotEmpty(shareAllocationBillSaveRequests)) {
            // 数据合并使用   key sgCShareStoreId
            Map<Long, SgBShareAllocationBillSaveRequst> temMap = new HashMap<>();
            for (SgBShareAllocationBillSaveRequst allocationBillSaveRequst : shareAllocationBillSaveRequests) {
                SgBShareAllocationSaveRequst allocationSaveRequst = allocationBillSaveRequst.getSgBShareAllocationSaveRequst();
                List<SgBShareAllocationSaveItemRequst> newAllocationItemSaveRequest = allocationBillSaveRequst.getSgBShareAllocationSaveItemRequsts();
                Long key = allocationSaveRequst.getSgCShareStoreId();
                if (MapUtils.isEmpty(temMap) || !temMap.containsKey(key)) {
                    temMap.put(key, allocationBillSaveRequst);
                    continue;
                }
                SgBShareAllocationBillSaveRequst sgBShareAllocationBillSaveRequst = temMap.get(key);
                SgBShareAllocationSaveRequst oldSaveRequest = sgBShareAllocationBillSaveRequst.getSgBShareAllocationSaveRequst();
                String remark = oldSaveRequest.getRemark();
                if (StringUtils.isNotEmpty(remark) && remark.contains(SgConstants.SG_CONNECTOR_MARKS_9)) {
                    String[] splitStr = remark.split(SgConstants.SG_CONNECTOR_MARKS_9);
                    remark = splitStr[0] + SgConstants.SG_CONNECTOR_MARKS_3 + allocationSaveRequst.getBillNo() + splitStr[1];
                }
                oldSaveRequest.setRemark(remark);

                List<SgBShareAllocationSaveItemRequst> oldItemSaveRequsts = sgBShareAllocationBillSaveRequst.getSgBShareAllocationSaveItemRequsts();
                for (SgBShareAllocationSaveItemRequst itemSaveRequst : newAllocationItemSaveRequest) {

                    SgBShareAllocationSaveItemRequst sgBShareAllocationItemSaveRequst = oldItemSaveRequsts.stream().filter(oldItemSaveRequst -> itemSaveRequst.getSgCSaStoreId().equals(oldItemSaveRequst.getSgCSaStoreId()) && itemSaveRequst.getPsCSkuId().equals(oldItemSaveRequst.getPsCSkuId())).findFirst().orElse(null);
                    if (ObjectUtils.isNotEmpty(sgBShareAllocationItemSaveRequst)) {
                        sgBShareAllocationItemSaveRequst.setQty(sgBShareAllocationItemSaveRequst.getQty().add(itemSaveRequst.getQty()));
                    } else {
                        oldItemSaveRequsts.add(itemSaveRequst);
                    }
                }
            }

            if (MapUtils.isNotEmpty(temMap)) {
                mergeAllocationBillSaveRequests.addAll(temMap.values());
            }
        }
        LogUtils.printLog(" SgCSyncShareStorageResultService executeSyncTask 合并后 mergeAllocationReturnBillSaveRequsts = {} mergeAdjustBillSaveRequests = {} mergePriorityAdjustBillSaveRequests = {}", JSON.toJSONString(mergeAllocationReturnBillSaveRequsts), JSON.toJSONString(mergeAdjustBillSaveRequests), JSON.toJSONString(mergePriorityAdjustBillSaveRequests));
    }

    //合并共享调整单
    public void mergeShareAdjustBillSaveRequest(List<SgBShareAdjustBillSaveRequest> sgBShareAdjustBillSaveRequests, List<SgBShareAdjustBillSaveRequest> mergeAdjustBillSaveRequests) {
        //key   sgCShareStoreId
        Map<Long, SgBShareAdjustBillSaveRequest> temMap = new HashMap<>();
        for (SgBShareAdjustBillSaveRequest sgBShareAdjustBillSaveRequest : sgBShareAdjustBillSaveRequests) {
            SgBShareAdjustSaveRequest newAdjustSaveRequest = sgBShareAdjustBillSaveRequest.getSgBShareAdjustSaveRequest();
            List<SgBShareAdjustItemSaveRequest> newAdjustItemSaveRequest = sgBShareAdjustBillSaveRequest.getSgBShareAdjustItemSaveRequests();
            Long sgCShareStoreId = newAdjustSaveRequest.getSgCShareStoreId();
            if (MapUtils.isEmpty(temMap) || !temMap.containsKey(sgCShareStoreId)) {

                temMap.put(sgCShareStoreId, sgBShareAdjustBillSaveRequest);
                continue;
            }
            SgBShareAdjustBillSaveRequest oldAdjustBillSaveRequest = temMap.get(sgCShareStoreId);
            SgBShareAdjustSaveRequest oldAdjustSaveRequest = oldAdjustBillSaveRequest.getSgBShareAdjustSaveRequest();
            String remark = oldAdjustSaveRequest.getRemark();
            String newRemark = newAdjustSaveRequest.getRemark();
            oldAdjustSaveRequest.setTotQty(oldAdjustSaveRequest.getTotQty().add(newAdjustSaveRequest.getTotQty()));
            oldAdjustSaveRequest.setRemark(remark + SgConstants.SG_CONNECTOR_MARKS_3 + newRemark.substring(newRemark.indexOf(SgConstants.SG_CONNECTOR_MARKS_4)));

            List<SgBShareAdjustItemSaveRequest> oldAdjustItemSaveRequests = oldAdjustBillSaveRequest.getSgBShareAdjustItemSaveRequests();
            for (SgBShareAdjustItemSaveRequest sgBShareAdjustItemSaveRequest : newAdjustItemSaveRequest) {

                SgBShareAdjustItemSaveRequest itemSaveRequest = oldAdjustItemSaveRequests.stream().filter(oldAdjustItemSaveRequst -> oldAdjustItemSaveRequst.getPsCSkuId().equals(sgBShareAdjustItemSaveRequest.getPsCSkuId())).findFirst().orElse(null);
                if (ObjectUtils.isNotEmpty(itemSaveRequest)) {

                    itemSaveRequest.setQty(itemSaveRequest.getQty().add(sgBShareAdjustItemSaveRequest.getQty()));
                } else {

                    oldAdjustItemSaveRequests.add(sgBShareAdjustItemSaveRequest);
                }

            }

        }

        if (MapUtils.isNotEmpty(temMap)) {
            mergeAdjustBillSaveRequests.addAll(temMap.values());
        }
    }


    private void buildSgBShareAdjustBillSaveRequestsParams(SgBShareStorageQueryResult sgBShareStorageQueryResult, SgCSyncShareStorageResult storageResult,
                                                           BigDecimal subQty, Long psCSkuId, List<SgBShareAdjustBillSaveRequest> shareAdjustBillSaveRequests) {
        //创建共享调整单
        SgBShareAdjustBillSaveRequest request = new SgBShareAdjustBillSaveRequest();
        SgBShareAdjustSaveRequest mainRequest = new SgBShareAdjustSaveRequest();
        List<SgBShareAdjustItemSaveRequest> itemRequestList = new ArrayList<>();

        // 处理mainRequest
        mainRequest.setBillDate(new Date());
        mainRequest.setTotRowNum(1);
        mainRequest.setSgCShareStoreId(sgBShareStorageQueryResult.getSgCShareStoreId());
        mainRequest.setRemark("共享调整单 来源单号:" + storageResult.getBillNo());
        mainRequest.setTotQty(subQty);


        SgBShareAdjustItemSaveRequest itemRequest = new SgBShareAdjustItemSaveRequest();
        itemRequest.setQty(subQty);
        itemRequest.setPsCSkuId(psCSkuId);

        itemRequestList.add(itemRequest);
        request.setIsCheck(Boolean.TRUE);
        request.setR3(true);
        request.setObjId(-1L);
        request.setSgBShareAdjustSaveRequest(mainRequest);
        request.setSgBShareAdjustItemSaveRequests(itemRequestList);
        request.setLoginUser(R3SystemUserResource.getSystemRootUser());

        shareAdjustBillSaveRequests.add(request);

    }

    @Transactional(rollbackFor = Exception.class)
    public void createOuRuiShareStorageResult(List<SgCSyncShareStorageResult> storageResults, List<SgBShareAllocationReturnBillSaveRequst> sgBShareAllocationReturnBillSaveRequsts,
                                              List<SgBShareAdjustBillSaveRequest> sgBShareAdjustBillSaveRequests, List<SgBShareAdjustBillSaveRequest> shareAdjustBillSaveRequests,
                                              List<SgBShareAllocationBillSaveRequst> allocationBillSaveRequests, Date startDate, Date endDate) {
        User user = R3SystemUserResource.getSystemRootUser();
        List<String> redisFtpKeys = new ArrayList<>();
        try {
            if (CollectionUtils.isNotEmpty(shareAdjustBillSaveRequests)) {
                for (SgBShareAdjustBillSaveRequest sgBShareAdjustBillSaveRequest : shareAdjustBillSaveRequests) {

                    ValueHolderV14<SgR3BaseResult> saveAndSubmit = sgBShareAdjustSaveAndSubmitService.saveAndSubmit(sgBShareAdjustBillSaveRequest);
                    SgR3BaseResult data = saveAndSubmit.getData();
                    if (ObjectUtils.isNotEmpty(data) && CollectionUtils.isNotEmpty(data.getDataJo())) {

                        redisFtpKeys.addAll((List<String>) data.getDataJo().get("redisFtpKey"));
                    }
                    AssertUtils.isTrue(saveAndSubmit.isOK(), "创建共享调整单失败，失败原因：" + saveAndSubmit.getMessage());
                }
            }
            if (CollectionUtils.isNotEmpty(sgBShareAllocationReturnBillSaveRequsts)) {
                //如果 分货退货单不为空, 创建并审核分货退货
                ValueHolderV14<SgBShareAllocationReturnSaveAndSubmitResult> submitResult = shareAllocationReturnSaveAndSubmitService.saveAndSubmit2(sgBShareAllocationReturnBillSaveRequsts);
                SgBShareAllocationReturnSaveAndSubmitResult data = submitResult.getData();
                if (ObjectUtils.isNotEmpty(data) && CollectionUtils.isNotEmpty(data.getRedisFtpKeys())) {
                    redisFtpKeys.addAll(data.getRedisFtpKeys());
                }
                AssertUtils.isTrue(submitResult.isOK(), "分货退货单创建并审核失败，失败原因：" + submitResult.getMessage());
            }
            if (CollectionUtils.isNotEmpty(sgBShareAdjustBillSaveRequests)) {
                for (SgBShareAdjustBillSaveRequest sgBShareAdjustBillSaveRequest : sgBShareAdjustBillSaveRequests) {

                    ValueHolderV14<SgR3BaseResult> saveAndSubmit = sgBShareAdjustSaveAndSubmitService.saveAndSubmit(sgBShareAdjustBillSaveRequest);
                    SgR3BaseResult data = saveAndSubmit.getData();
                    if (ObjectUtils.isNotEmpty(data) && CollectionUtils.isNotEmpty(data.getDataJo())) {

                        redisFtpKeys.addAll((List<String>) data.getDataJo().get("redisFtpKey"));
                    }
                    AssertUtils.isTrue(saveAndSubmit.isOK(), "创建共享调整单失败，失败原因：" + saveAndSubmit.getMessage());
                }
            }
            if (CollectionUtils.isNotEmpty(allocationBillSaveRequests)) {
                for (SgBShareAllocationBillSaveRequst allocationBillSaveRequest : allocationBillSaveRequests) {
                    ValueHolderV14 valueHolderV14 = allocationSaveAndSubmitService.insertAndSubmitAllocation(allocationBillSaveRequest);
                    AssertUtils.isTrue(valueHolderV14.isOK(), "创建共享调整单失败，失败原因：" + valueHolderV14.getMessage());
                }
            }
            if (CollectionUtils.isNotEmpty(storageResults)) {

                List<Long> ids = baseMapper.selectListByShareStoreIdAndPsCSkuEcode(storageResults, startDate, endDate);
                if (CollectionUtils.isNotEmpty(ids)) {
                    baseMapper.deleteBatchIds(ids);
                }
                //创建共享同步结果单
                saveBatch(storageResults);
            }
        } catch (Exception e) {
            StorageBasicUtils.rollbackStorage(redisFtpKeys, user);
            AssertUtils.logAndThrowException("定时任务创建欧睿库存同步共享结果单失败 ", e, user.getLocale());
        }

    }

    /**
     * 计算最终共享量
     *
     * @param storageResult
     * @param qtyVendibility
     * @param qtyShare
     * @return
     */
    private BigDecimal calculateQtyFinalShare(SgCSyncShareStorageResult storageResult, BigDecimal
            qtyVendibility, BigDecimal qtyShare) {
        BigDecimal qtyFinalShare = BigDecimal.ZERO;
        //获取商品系统的保留库存, 不能为负数,前阶段有校验
        BigDecimal qtyProSysRetainStorage = storageResult.getQtyProSysRetainStorage();
        // 商品系统的共享库存
        BigDecimal qtyProSysShareStorage = storageResult.getQtyProSysShareStorage();
        // 商品系统保留库存＜中台可售库存 向上共享
        if (qtyProSysRetainStorage.compareTo(qtyVendibility) < 0) {
            //商品系统共享库存  < 中台共享库存
            if (qtyProSysShareStorage.compareTo(qtyShare) < 0) {

                qtyFinalShare = qtyShare.subtract(qtyProSysShareStorage);
            } else {
                //商品系统共享库存 >= 中台共享库存
                BigDecimal subQtyShare = qtyProSysShareStorage.subtract(qtyShare);
                BigDecimal subQtySale = qtyVendibility.subtract(qtyProSysRetainStorage);
                //最终调整数取最小值
                if (subQtySale.compareTo(subQtyShare) > 0) {

                    qtyFinalShare = subQtyShare;
                } else {

                    qtyFinalShare = subQtySale;
                }
            }
        } else {
            //商品系统保留库存≥中台可售库存   向下拉回
            BigDecimal subQty = qtyProSysRetainStorage.subtract(qtyVendibility);
            //最终调整数取最小值
            if (subQty.compareTo(qtyShare) > 0) {

                qtyFinalShare = qtyShare;
            } else {

                qtyFinalShare = subQty;
            }

        }
        return qtyFinalShare;
    }


    private BigDecimal buildSgBShareAllocationReturnBillSaveRequestParam(List<SgCSaStorageResult> results, BigDecimal subQty, SgCSyncShareStorageResult storageResult,
                                                                         List<SgBShareAllocationReturnBillSaveRequst> sgBShareAllocationReturnBillSaveRequsts) {
        if (CollectionUtils.isEmpty(results) || BigDecimal.ZERO.compareTo(subQty) >= 0) {
            return BigDecimal.ZERO;
        }
        //按优先级升序
        Collections.sort(results, Comparator.comparingInt(SgCSaStorageResult::getPriority));

        if(log.isDebugEnabled()){
            log.debug(" buildSgBShareAllocationReturnBillSaveRequestParam预计分货退货数量：{}，退货后配销仓库存：{}",
                    subQty,JSON.toJSONString(results));
        }

        //记录实际从配销仓拉回的总量
        BigDecimal actualQty = BigDecimal.ZERO;
        BigDecimal calculateSubQty = subQty;
        for (SgCSaStorageResult cSaStorageResult : results) {

            //分货退 必须当前 配销仓 允许回退 是否允许回退  0否 1是
            Integer isAllowReturn = cSaStorageResult.getIsAllowReturn();
            if (!SaAllowReturnEnum.YES.getCode().equals(isAllowReturn)) {

                LogUtils.printLog(" SgCSyncShareStorageResultService executeSyncTask  buildSgBShareAllocationReturnBillSaveRequestParam 该配销仓不允许回退 cSaStorageResult = {}", JSON.toJSONString(cSaStorageResult));
                continue;
            }
            // 构建request
            SgBShareAllocationReturnBillSaveRequst request = new SgBShareAllocationReturnBillSaveRequst();
            SgBShareAllocationReturnSaveRequst mainRequest = new SgBShareAllocationReturnSaveRequst();

            // 处理主表数据
            mainRequest.setBillDate(storageResult.getBillDate());
            mainRequest.setSourceBillId(storageResult.getId());
            mainRequest.setSourceBillNo(storageResult.getBillNo());
            mainRequest.setSourceBillType(ObjectUtils.isEmpty(storageResult.getQtyProSysRetainStorage()) ?
                    SgConstantsIF.BILL_TYPE_SA_SELECTION_RESULT : SgConstantsIF.BILL_OURUI_SHARE_RESULT);
            mainRequest.setSgCSaStoreId(cSaStorageResult.getSaStoreId());
            mainRequest.setSgCShareStoreId(cSaStorageResult.getShareStoreId());
            mainRequest.setRemark((ObjectUtils.isEmpty(storageResult.getQtyProSysRetainStorage()) ?
                    "选款结果表批次号" : "欧睿库存共享结果单编号") + "【" + storageResult.getBillNo() + "】生成");

            List<SgBShareAllocationReturnItemSaveRequst> itemRequestList = new ArrayList<>();

            SgBShareAllocationReturnItemSaveRequst itemRequest = new SgBShareAllocationReturnItemSaveRequst();
            // 处理明细数据
            itemRequest.setPsCSkuId(cSaStorageResult.getPsCSkuId());
            itemRequest.setPsCSkuEcode(storageResult.getPsCSkuEcode());
            //获取配销仓的可用
            BigDecimal qtyAvailable = cSaStorageResult.getQtyAvailable();
            if(BigDecimal.ZERO.compareTo(qtyAvailable) >= 0){
                //配销仓无可用库存跳过
                continue;
            }

            if (calculateSubQty.compareTo(qtyAvailable) <= 0) {
                BigDecimal qty = calculateSubQty;
                itemRequest.setQty(qty);
                calculateSubQty = BigDecimal.ZERO;
                actualQty = actualQty.add(qty);
            } else {
                //当前配销仓不够分配
                itemRequest.setQty(qtyAvailable);
                actualQty = actualQty.add(qtyAvailable);
                calculateSubQty = calculateSubQty.subtract(qtyAvailable);
            }

            //设置分配后剩余可用量，用于后面选款结果表计算
            cSaStorageResult.setQtyAvailable(calculateSubQty);

            itemRequest.setId(-1L);

            itemRequestList.add(itemRequest);
            request.setR3(true);
            request.setLoginUser(R3SystemUserResource.getSystemRootUser());
            request.setAllocationReturnSaveRequst(mainRequest);
            request.setAllocationReturnItemSaveRequst(itemRequestList);

            sgBShareAllocationReturnBillSaveRequsts.add(request);

            if(ObjectUtils.isEmpty(storageResult.getSgCSaStoreIds())){
                storageResult.setSgCSaStoreIds(cSaStorageResult.getSaStoreId() + "");
            }else {
                storageResult.setSgCSaStoreIds(storageResult.getSgCSaStoreIds() + "," + cSaStorageResult.getSaStoreId());
            }

            if (BigDecimal.ZERO.compareTo(calculateSubQty) == 0) {

                break;
            }

        }

        if(log.isDebugEnabled()){
            log.debug(" buildSgBShareAllocationReturnBillSaveRequestParam实际分货退货数量：{}，退货后配销仓库存：{}",
                    actualQty,JSON.toJSONString(results));
        }

        return actualQty;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateSyncStatus(List<SgCProductSystemShareLog> sgCProductSystemShareLogs) {
        if (CollectionUtils.isNotEmpty(sgCProductSystemShareLogs)) {
            sgCShareStockSyncService.updateBatchById(sgCProductSystemShareLogs);
        }
    }

}
