package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.model.table.store.freeze.SgBStoFreeze;
import com.burgeon.r3.sg.core.model.table.store.freeze.SgBStoFreezeItem;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/6/24 11:04
 * 库存冻结申请
 */
@Slf4j
@Component
public class DrpFreezeProcessor extends AbstractDrpInterfaceProcessor<SgBStoFreeze, SgBStoFreezeItem> {


    @Override
    public LambdaQueryWrapper<SgBStoFreeze> execMainWrapper() {
        //已审核”且“传DRP状态”=“传失败”或“未传”的【逻辑冻结单
        LambdaQueryWrapper<SgBStoFreeze> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SgBStoFreeze::getStatus, SgStoreConstants.BILL_STATUS_SUBMIT);
        wrapper.and(o -> {
            o.isNull(SgBStoFreeze::getDrpStatus);
            o.or(oo -> oo.eq(SgBStoFreeze::getDrpStatus, SgStoreConstants.SEND_DRP_STATUS_FAIL).lt(SgBStoFreeze::getDrpFailCount, failNum));
            return o;
        });
        return wrapper;
    }

    @Override
    public LambdaQueryWrapper<SgBStoFreezeItem> execitemWrapper(Long mainId) {
        return null;
    }

    @Override
    public String drpInterfaceUrl() {
        return "erp.stockfrozen.notice";
    }

    @Override
    public String itemMainField() {
        return "sg_b_sto_freeze_id";
    }

    @Override
    public String drpStatus() {
        return "DRP_STATUS";
    }

    @Override
    public String drpStatusFailCount() {
        return "DRP_FAIL_COUNT";
    }

    @Override
    public String drpStatusFailReason() {
        return "DRP_FAIL_REASON";
    }

    @Override
    public JSONObject execInterfaceParam(SgBStoFreeze sgBStoFreeze, List<SgBStoFreezeItem> itemList) {
        JSONObject object = new JSONObject();
        object.put("ZTDOCNO", sgBStoFreeze.getBillNo());
        object.put("BILLDATE", DateUtil.format(sgBStoFreeze.getBillDate(), "yyyyMMdd"));
        object.put("DESCRIPTION", sgBStoFreeze.getRemark());
        JSONArray items = new JSONArray();
        for (SgBStoFreezeItem freezeItem : itemList) {
            JSONObject item = new JSONObject();
            item.put("M_PRODUCTALIAS_ID_NO", freezeItem.getPsCSkuEcode());
            item.put("QTY", freezeItem.getQty());
            item.put("C_STORE_ID_CODE", sgBStoFreeze.getCpCStoreEcode());
            items.add(item);
        }
        object.put("items", items);
        log.info("DrpFreezeProcessor execInterfaceParam result ={}", JSONObject.toJSONString(object));
        return object;
    }

    @Override
    public void handleBysuccess(SgBStoFreeze sgBStoFreeze, List<SgBStoFreezeItem> z) {

    }
}
