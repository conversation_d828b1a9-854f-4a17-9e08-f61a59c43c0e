package com.burgeon.r3.inf.services.drp.in;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.inf.config.SgDrpConfig;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.freeze.SgBStoUnfreeze;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpUnFreezeItemSaveRequest;
import com.burgeon.r3.sg.inf.model.request.drp.in.SgDrpUnfreezeSaveRequest;
import com.burgeon.r3.sg.inf.model.result.drp.in.SgDrpUnFreezeSaveResult;
import com.burgeon.r3.sg.store.mapper.freeze.SgBStoUnfreezeMapper;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoUnfreezeItemRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoUnfreezeRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoUnfreezeSaveRequest;
import com.burgeon.r3.sg.store.model.result.freeze.SgBStoUnfreezeSaveBillResult;
import com.burgeon.r3.sg.store.services.freeze.SgBStoUnfreezeSaveAndSubmitService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.utils.AssertUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/6/21 15:45
 * 解冻单服务
 */
@Slf4j
@Component
public class SgDrpStoUnFreezeService {

    @Autowired
    private SgBStoUnfreezeSaveAndSubmitService saveAndSubmitService;
    @Autowired
    private SgDrpConfig sgDrpConfig;
    @Autowired
    private SgBStoUnfreezeMapper sgStoUnfreezeMapper;

    /**
     * 逻辑解冻单 保存
     */
    public ValueHolderV14<SgDrpUnFreezeSaveResult> saveDrpUnFreeze(SgDrpUnfreezeSaveRequest request) {
        if (!sgDrpConfig.getEnableDrpSend()) {
            return new ValueHolderV14<>(ResultCode.FAIL, "中台系统未开放,请稍后再试!");
        }

        log.info("Start SgDrpStoUnFreezeService.saveDrpUnFreeze param={}", JSONObject.toJSONString(request));

        String lockKsy =
                SgConstants.INTERFACE_DRP + ":" + SgConstants.SG_B_STO_UNFREEZE + ":" + request.getBillNo();
        SgRedisLockUtils.lock(lockKsy);

        try {
            int count = sgStoUnfreezeMapper.selectCount(new LambdaQueryWrapper<SgBStoUnfreeze>()
                    .eq(SgBStoUnfreeze::getBillNo, request.getBillNo())
                    .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (count > 0) {
                return new ValueHolderV14<>(ResultCode.SUCCESS, "当前单据中台已存在,请勿重复请求!");
            }
            checkParams(request);
            CpCStore cpStore = CommonCacheValUtils.getStoreInfoByEcode(request.getCpCStoreEcode());
            AssertUtils.notNull(cpStore, "逻辑仓不存在");
            SgDrpUnFreezeSaveResult result = new SgDrpUnFreezeSaveResult();
            SgBStoUnfreezeSaveRequest unfreezeSaveRequest = new SgBStoUnfreezeSaveRequest();
            SgBStoUnfreezeRequest unfreezeRequest = new SgBStoUnfreezeRequest();
            BeanUtils.copyProperties(request, unfreezeRequest);
            unfreezeRequest.setCpCStoreId(cpStore.getId());
            unfreezeRequest.setCpCStoreEcode(cpStore.getEcode());
            unfreezeRequest.setCpCStoreEname(cpStore.getEname());
            unfreezeRequest.setBillDate(DateUtil.stringToDate(request.getBillDate()));
            unfreezeRequest.setDrpStatus("2");
            unfreezeRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_DRP_UNFREEZE);
            unfreezeSaveRequest.setObjId(-1L);
            unfreezeSaveRequest.setLoginUser(DrpUtils.getUser());
            unfreezeRequest.setBillNo(request.getBillNo());
            List<SgBStoUnfreezeItemRequest> itemRequests = new ArrayList<>();
            for (SgDrpUnFreezeItemSaveRequest item : request.getItems()) {
                SgBStoUnfreezeItemRequest itemRequest = new SgBStoUnfreezeItemRequest();
                BeanUtils.copyProperties(item, itemRequest);
                itemRequest.setId(-1L);
                itemRequests.add(itemRequest);
            }
            unfreezeSaveRequest.setSgStoUnfreezeRequest(unfreezeRequest);
            unfreezeSaveRequest.setSgStoUnfreezeItemRequest(itemRequests);

            ValueHolderV14<SgBStoUnfreezeSaveBillResult> v14 = saveAndSubmitService.saveAndSubmit(unfreezeSaveRequest);
            if (v14.isOK()) {
                result.setBillNo(v14.getData().getBillNo());
                return new ValueHolderV14<>(result, ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
            } else {
                return new ValueHolderV14<>(result, ResultCode.FAIL, v14.getMessage());
            }
        } catch (Exception e) {
            log.error("SgDrpStoUnFreezeService.saveDrpUnFreeze exception_has_occured:{}",
                    Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
    }

    private void checkParams(SgDrpUnfreezeSaveRequest request) {
        AssertUtils.notNull(request, "请求参数不能为空");
        AssertUtils.notNull(request.getBillNo(), "单据编号不能为空");
        int count = sgStoUnfreezeMapper.selectCount(new LambdaQueryWrapper<SgBStoUnfreeze>()
                .eq(SgBStoUnfreeze::getBillNo, request.getBillNo())
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        String cpStoreEcode = request.getCpCStoreEcode();
        AssertUtils.cannot(count > 0, "单据编号:" + request.getBillNo() + "已存在,不允许重复创建!");
        List<SgDrpUnFreezeItemSaveRequest> items = request.getItems();
        AssertUtils.notNull(cpStoreEcode, "逻辑仓编码不能为空");
        if (CollectionUtils.isEmpty(items)) {
            AssertUtils.logAndThrow("解冻单明细不能为空");
        }

        //条码是否存在
        StringBuilder sb = new StringBuilder();
        BasicPsQueryService psQueryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
        SkuInfoQueryRequest skuRequest = new SkuInfoQueryRequest();
        List<String> skuEcodes = items.stream().map(o -> o.getPsCSkuEcode()).collect(Collectors.toList());
        skuRequest.setSkuEcodeList(skuEcodes);
        HashMap<String, PsCProSkuResult> selBySkuEcode = psQueryService.getSkuInfoByEcode(skuRequest);
        for (SgDrpUnFreezeItemSaveRequest item : items) {
            String skuEcode = item.getPsCSkuEcode();
            PsCProSkuResult data = selBySkuEcode.get(skuEcode);
            if (data == null) {
                sb.append(skuEcode + " ");
            }
        }
        if (sb.length() > 0) {
            AssertUtils.logAndThrow(sb.toString() + "条码不存在");
        }
    }
}
