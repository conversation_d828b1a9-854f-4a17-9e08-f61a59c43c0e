package com.burgeon.r3.inf.services.cp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/10 10:46
 * <p>
 * 配销仓档案主表同步到cp的服务
 */
@Slf4j
@Component
public class SgCSaStoreSyncService extends AbstractSyncService<SgCSaStore> {

    @Autowired
    private SgCSaStoreMapper sgCSaStoreMapper;

    public SgCSaStoreSyncService() {
        super(SgConstants.SG_C_SA_STORE);
    }

    @Override
    public List<SgCSaStore> execute(int pageIndex, int pageSize, int pageRangeTime, boolean isAllSync) {
        if (log.isDebugEnabled()) {
            log.debug("SgCSaStoreSyncService.execute :pageIndex={} ,pageSize={} ,intervalMinute={}",
                    pageIndex, pageSize, pageRangeTime);
        }
        LambdaQueryWrapper<SgCSaStore> lqw = new LambdaQueryWrapper<SgCSaStore>();

        if (!isAllSync) {
            Date now = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            calendar.add(Calendar.MINUTE, -pageRangeTime);
            lqw.gt(SgCSaStore::getModifieddate, calendar.getTime())
                    .lt(SgCSaStore::getModifieddate, now);
        }

        PageHelper.startPage(pageIndex, pageSize);
        List<SgCSaStore> sgCSaStores = sgCSaStoreMapper.selectList(lqw);
        PageInfo<SgCSaStore> pageInfo = new PageInfo<>(sgCSaStores);
        return pageInfo.getList();
    }
}
