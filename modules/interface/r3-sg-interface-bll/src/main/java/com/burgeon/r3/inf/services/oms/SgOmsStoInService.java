package com.burgeon.r3.inf.services.oms;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutResultItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoInItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoInRequest;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInItemMapper;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInNoticesItemMapper;
import com.burgeon.r3.sg.store.model.request.in.*;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInBillSaveResult;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInNoticesBillSaveResult;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInResultBillSaveResult;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInResultBillSubmitResult;
import com.burgeon.r3.sg.store.services.in.SgBStoInNoticesSaveService;
import com.burgeon.r3.sg.store.services.in.SgBStoInResultSaveService;
import com.burgeon.r3.sg.store.services.in.SgBStoInResultSubmitService;
import com.burgeon.r3.sg.store.services.in.SgBStoInSaveService;
import com.google.common.base.Throwables;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 【OMS】逻辑在途单创建并自动入库服务:生成逻辑在途单---->生成入库通知单----->生成逻辑入库单，----->审核逻辑入库单并释放逻辑在途单和修改入库通知单。
 * <AUTHOR>
 * @Date 2021/6/28 14:33
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgOmsStoInService {


    @Autowired
    private SgBStoInResultSaveService saveService;


    @Autowired
    private SgBStoInResultSubmitService submitResultService;



    /**
     * 创建
     *
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 saveSgOmsStoIn(SgOmsStoInRequest request) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "调用[逻辑在途单创建并自动入库服务]成功！");

        if (log.isDebugEnabled()) {
            log.debug("SgOmsStoInService.saveSgOmsStoIn Start:{}", JSONObject.toJSONString(request));
        }
        //如果接口中传入的登录用户为null，那么取系统用户
        User user = request.getLoginUser();
        if (user == null) {
            user = SystemUserResource.getRootUser();
            request.setLoginUser(user);
        }

        checkSgOmsStoIn(request);
        //构建逻辑在途单请求信息
        SgBStoInBillSaveRequest sgRequest = new SgBStoInBillSaveRequest();

        sgRequest.setLoginUser(user);
        //构建逻辑在途单主表信息
        SgBStoInSaveRequest mainTable = new SgBStoInSaveRequest();
        //构建逻辑在途单明细信息
        List<SgBStoInItemSaveRequest> itemList = Collections.synchronizedList(new ArrayList<>());
        mainTable.setServiceNode(request.getServiceNode());
        mainTable.setSourceBillId(request.getSourceBillId());
        mainTable.setSourceBillNo(request.getSourceBillNo());
        mainTable.setSourceBillType(request.getSourceBillType());
        mainTable.setSenderEcode(request.getSenderEcode());
        mainTable.setBillDate(request.getBillDate());
        mainTable.setSenderName(request.getSenderName());
        mainTable.setSenderEcode(request.getSenderEcode());


        // 构建逻辑入库单主表信息
        SgBStoInResultSaveRequest stoInResultRequest = new SgBStoInResultSaveRequest();
        // 构建逻辑入库单明细信息
        List<SgBStoInResultItemSaveRequest> stoInResultItemList = Collections.synchronizedList(new ArrayList<>());

        request.getOmsStoInItemRequests().forEach(item -> {

            SgBStoInItemSaveRequest sgStoInItemSaveRequest = new SgBStoInItemSaveRequest();
            BeanUtils.copyProperties(item, sgStoInItemSaveRequest);

            //逻辑仓赋值
            sgStoInItemSaveRequest.setCpCStoreId(item.getCpCStoreId());
            sgStoInItemSaveRequest.setCpCStoreEname(item.getCpCStoreEname());
            sgStoInItemSaveRequest.setCpCStoreEcode(item.getCpCStoreEcode());
            sgStoInItemSaveRequest.setQty(item.getQty());
            //sgStoInItemSaveRequest.setQtyPrein(item.getQtyPrein());
            sgStoInItemSaveRequest.setQtyPrein(item.getQty());
            sgStoInItemSaveRequest.setSourceBillItemId(item.getSourceBillItemId());
            itemList.add(sgStoInItemSaveRequest);
            //构建逻辑入库单的信息
            stoInResultRequest.setCpCStoreEcode(item.getCpCStoreEcode());
            stoInResultRequest.setCpCStoreEname(item.getCpCStoreEname());
            stoInResultRequest.setCpCStoreId(item.getCpCStoreId());
            SgBStoInResultItemSaveRequest requestStoInResultItem = new SgBStoInResultItemSaveRequest();
            BeanUtils.copyProperties(sgStoInItemSaveRequest, requestStoInResultItem);
            requestStoInResultItem.setQty(item.getQtyPrein());
            stoInResultItemList.add(requestStoInResultItem);
        });
        // sgRequest 赋值相关字段
        sgRequest.setSgStoInSaveRequest(mainTable);
        sgRequest.setSgStoInItemSaveRequestList(itemList);
        sgRequest.setServiceNode(request.getServiceNode());
        sgRequest.setLoginUser(user);
        sgRequest.setIsCancel(false);
        sgRequest.setUpdateMethod(SgConstantsIF.ITEM_UPDATE_TYPE_ALL);
        //保存报错时redis中需要回滚的数据
        List<String> redisBillFtpKeyList = new ArrayList<>();
        sgRequest.setRedisBillFtpKeyList(redisBillFtpKeyList);
        // 执行逻辑在途单的具体逻辑
        try {
            SgBStoInSaveService stoInSaveService = ApplicationContextHandle.getBean(SgBStoInSaveService.class);
            ValueHolderV14<SgBStoInBillSaveResult> result = stoInSaveService.saveSgBStoIn(sgRequest);

            if (!result.isOK()) {
                AssertUtils.logAndThrow("逻辑在途单创建服务异常：" + result.getMessage());
            }
            redisBillFtpKeyList.addAll(result.getData().getRedisBillFtpKeyList());
            if (log.isDebugEnabled()) {
                log.debug("SgOmsStoInService.saveSgOmsStoIn SgBStoInId:{},BillNo:{}",
                        result.getData().getId(), result.getData().getBillNo());
            }

            //生成入库通知单，调用【入库通知单服务】
            ValueHolderV14<SgBStoInNoticesBillSaveResult> resultValueHolderV14 = sgStoInNoticesSave(sgRequest, user);
            if (log.isDebugEnabled()) {
                log.debug("Start SgBStoTransferOutResultService.outResultSgStoTransfer.resultValueHolderV14={}", JSONObject.toJSONString(resultValueHolderV14));
            }
            if (!resultValueHolderV14.isOK()) {
                AssertUtils.logAndThrow("入库通知单服务异常：" + resultValueHolderV14.getMessage());
            }
            //构建逻辑入库单主表信息
            stoInResultRequest.setBillDate(request.getBillDate());
            stoInResultRequest.setSourceBillType(request.getSourceBillType());
            stoInResultRequest.setSourceBillNo(request.getSourceBillNo());
            stoInResultRequest.setSourceBillId(request.getSourceBillId());
            // 逻辑在途单的iD：result.getData().getId()
            stoInResultRequest.setSgBStoInId(result.getData().getId());
            stoInResultRequest.setSgBStoInNoticesId(resultValueHolderV14.getData().getId());
            stoInResultRequest.setSgBStoInNoticesNo(resultValueHolderV14.getData().getBillNo());
            stoInResultRequest.setSenderEcode(request.getSenderEcode());
            stoInResultRequest.setSenderName(request.getSenderName());
            stoInResultRequest.setIsLast(SgConstants.IS_LAST_YES);
            stoInResultRequest.setServiceNode(request.getServiceNode());

            //构建逻辑入库单请求信息
            SgBStoInResultBillSaveRequest stoInResultBillSRequest = new SgBStoInResultBillSaveRequest();
            stoInResultBillSRequest.setInResultSaveRequest(stoInResultRequest);
            stoInResultBillSRequest.setInItemResultSaveRequestList(stoInResultItemList);
            stoInResultBillSRequest.setIsOneClickLibrary(true);
            stoInResultBillSRequest.setLoginUser(user);
            ValueHolderV14<SgBStoInResultBillSaveResult> sgResult = saveService.saveSgBStoInResult(stoInResultBillSRequest);
            if (!sgResult.isOK()) {
                AssertUtils.logAndThrow("逻辑入库单创建服务异常：" + sgResult.getMessage());
            }
            if (log.isDebugEnabled()) {
                log.debug("SgOmsStoInService.saveSgOmsStoIn StoInResultIds:{}", sgResult.getData().getId());
            }

            //逻辑入库单审核
            SgBStoInResultSubmitRequest stoInResultSubmitRequest = new SgBStoInResultSubmitRequest();
            List<Long> arrayIdList = new ArrayList<>();
            arrayIdList.add(sgResult.getData().getId());
            stoInResultSubmitRequest.setIds(arrayIdList);
            stoInResultSubmitRequest.setLoginUser(user);
            stoInResultSubmitRequest.setRedisBillFtpKeyList(redisBillFtpKeyList);
            stoInResultSubmitRequest.setInterfaceTypeFlag(SgConstantsIF.Interface_FLAG_OMS_IN_NOTICES);

            ValueHolderV14<List<SgBStoInResultBillSubmitResult>> submitResultValueHolderV14 = submitResultService.submitInResult(stoInResultSubmitRequest);
            if (!submitResultValueHolderV14.isOK()) {
                AssertUtils.logAndThrow("逻辑入库单审核服务异常：" + submitResultValueHolderV14.getMessage());
            }
            redisBillFtpKeyList.addAll(submitResultValueHolderV14.getData().get(0).getRedisBillFtpKeyList());
        } catch (Exception ex) {
            // 回滚库存
            StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, user);
            log.error("SgOmsStoInService.saveSgOmsStoIn. error:{}", Throwables.getStackTraceAsString(ex));
            return new ValueHolderV14<>(ResultCode.FAIL, SgConstants.MESSAGE_STATUS_FAIL + ":" + ex.getMessage());
        } finally {

        }
        return v14;
    }


    /**
     * DRP-接口使用：新增入库通知单新增服务
     *
     * @param request 接口请求信息
     * @param user    登录用户
     * @return
     */
    private ValueHolderV14<SgBStoInNoticesBillSaveResult> sgStoInNoticesSave(SgBStoInBillSaveRequest request,
                                                                             User user) {

        ValueHolderV14<SgBStoInNoticesBillSaveResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "入库通知单新增成功！");

        SgBStoInNoticesBillSaveRequest stoInNoticesBillSaveRequest = new SgBStoInNoticesBillSaveRequest();
        SgBStoInNoticesBillSaveRequest.SgBStoInNoticesSaveRequest stoInNoticesSaveRequest = new SgBStoInNoticesBillSaveRequest.SgBStoInNoticesSaveRequest();
        List<SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest> list = new ArrayList<>();
        SgBStoInSaveRequest sgStoInSaveRequest = request.getSgStoInSaveRequest();

        //主表
        BeanUtils.copyProperties(sgStoInSaveRequest, stoInNoticesSaveRequest);

        //stoInNoticesSaveRequest.setCpCCustomerId(stoTransfer.getReceiverCustomerId());//经销商id
        //stoInNoticesSaveRequest.setCpCSupplierId(stoTransfer.getSenderCustomerId());//供应商id
        stoInNoticesSaveRequest.setSourceBillId(sgStoInSaveRequest.getSourceBillId());//来源单据id
        stoInNoticesSaveRequest.setSourceBillNo(sgStoInSaveRequest.getSourceBillNo());//来源单据编号
        stoInNoticesSaveRequest.setSourceBillType(sgStoInSaveRequest.getSourceBillType());//来源单据类型
        stoInNoticesSaveRequest.setBillDate(sgStoInSaveRequest.getBillDate());//单据日期
        //stoInNoticesSaveRequest.setSourceBillDate(sgStoInSaveRequest.getSou());//来源单据日期
        stoInNoticesSaveRequest.setBillStatus(SgStoreConstants.BILL_NOTICES_STATUS_INIT);//单据状态
        stoInNoticesSaveRequest.setSenderEcode(sgStoInSaveRequest.getSenderEcode());//发货方编码
        stoInNoticesSaveRequest.setSenderName(sgStoInSaveRequest.getSenderName());//发货方名称
        stoInNoticesSaveRequest.setInType(SgStoreConstantsIF.OUT_TYPE_BIG_GOODS);//入库类型


        for (SgBStoInItemSaveRequest item : request.getSgStoInItemSaveRequestList()) {

            SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest stoInNoticesItemSaveRequest = new SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest();
            BeanUtils.copyProperties(item, stoInNoticesItemSaveRequest);
            Long id = item.getPsCSkuId();
            stoInNoticesItemSaveRequest.setCpCStoreId(item.getCpCStoreId());
            stoInNoticesItemSaveRequest.setCpCStoreEcode(item.getCpCStoreEcode());
            stoInNoticesItemSaveRequest.setCpCStoreEname(item.getCpCStoreEname());
            //stoInNoticesItemSaveRequest.setCpCCustomerId(item.getReceiverCustomerId());//所属经销商
            stoInNoticesItemSaveRequest.setQtyIn(BigDecimal.ZERO);

            stoInNoticesItemSaveRequest.setQty(item.getQty());
            stoInNoticesItemSaveRequest.setSourceBillItemId(item.getSourceBillItemId());

            list.add(stoInNoticesItemSaveRequest);
        }
        stoInNoticesBillSaveRequest.setInNoticesSaveRequest(stoInNoticesSaveRequest);
        stoInNoticesBillSaveRequest.setInNoticesItemSaveRequests(list);
        try {
            SgBStoInNoticesSaveService stoInNoticesSaveService = ApplicationContextHandle.getBean(SgBStoInNoticesSaveService.class);
            v14 = stoInNoticesSaveService.addInNotices(stoInNoticesBillSaveRequest, user);
            if (log.isDebugEnabled()) {
                log.debug("Start SgBStoTransferOutResultService.SgBStoInItem.v14={}", JSONObject.toJSONString(v14));
            }
            if (!v14.isOK()) {
                return new ValueHolderV14<>(ResultCode.FAIL, v14.getMessage());
            }
        } catch (Exception ex) {
            log.error("SgBStoTransferOutResultService.sgStoInNoticesSave. error:{}", Throwables.getStackTraceAsString(ex));
            return new ValueHolderV14<>(ResultCode.FAIL, ex.getMessage());
        }
        return v14;
    }


    /**
     * 校验接口入参参数
     *
     * @param request
     */
    private void checkSgOmsStoIn(SgOmsStoInRequest request) {
        //AssertUtils.notNull(request.getLoginUser(), "用户未登录！");
        AssertUtils.notNull(request.getOmsStoInItemRequests(), "逻辑在途单明细不能为空！");
        AssertUtils.notNull(request.getSourceBillId(), "来源单据ID不能为空！");
        AssertUtils.notNull(request.getSourceBillType(), "单据类型不能为空！");
        AssertUtils.notNull(request.getSourceBillNo(), "来源单据编号为空！");
        request.getOmsStoInItemRequests().stream().forEach(item -> {
            AssertUtils.notNull(item.getSourceBillItemId(), "来源单明细ID不能为空！");
            String psCSkuEcode = item.getPsCSkuEcode();
            AssertUtils.notNull(psCSkuEcode, "条码编码不能为空！");
            AssertUtils.notNull(item.getQty(), "原单数不能为空！");
            AssertUtils.notNull(item.getQtyPrein(), "在途数不能为空！");
            AssertUtils.notNull(item.getCpCStoreId(), "[" + psCSkuEcode + "]逻辑仓ID不能为空！");
            AssertUtils.notNull(item.getCpCStoreEcode(), "[" + psCSkuEcode + "]逻辑仓编码不能为空！");
            AssertUtils.notNull(item.getCpCStoreEname(), "[" + psCSkuEcode + "]逻辑仓名称不能为空！");
            AssertUtils.notNull(item.getPsCSkuId(), "[" + psCSkuEcode + "]条码ID不能为空！");
            AssertUtils.notNull(item.getPsCProId(), "[" + psCSkuEcode + "]商品ID不能为空！");
            AssertUtils.notNull(item.getPsCProEcode(), "[" + psCSkuEcode + "]商品编码不能为空！");
            AssertUtils.notNull(item.getPsCProEname(), "[" + psCSkuEcode + "]商品名称不能为空！");
            AssertUtils.notNull(item.getPsCSpec1Id(), "[" + psCSkuEcode + "]规格1ID不能为空！");
            AssertUtils.notNull(item.getPsCSpec1Ecode(), "[" + psCSkuEcode + "]规格1编码不能为空！");
            AssertUtils.notNull(item.getPsCSpec1Ename(), "[" + psCSkuEcode + "]规格1名称不能为空！");
            AssertUtils.notNull(item.getPsCSpec2Id(), "[" + psCSkuEcode + "]规格2ID不能为空！");
            AssertUtils.notNull(item.getPsCSpec2Ecode(), "[" + psCSkuEcode + "]规格2编码不能为空！");
            AssertUtils.notNull(item.getPsCSpec2Ename(), "[" + psCSkuEcode + "]规格2名称不能为空！");
        });
    }
}
