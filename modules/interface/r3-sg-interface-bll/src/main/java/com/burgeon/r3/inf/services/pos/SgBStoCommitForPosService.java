package com.burgeon.r3.inf.services.pos;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.burgeon.r3.inf.services.drp.in.DrpUtils;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.config.SgStorageMqConfig;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.basic.mapper.SgBStorageChangeFtpMapper;
import com.burgeon.r3.sg.basic.model.param.SgAdjustParam;
import com.burgeon.r3.sg.basic.model.request.AbstractSgStorageUpdateBillItemRequest;
import com.burgeon.r3.sg.basic.model.request.SgCallbackShareQtyCalcItemRequest;
import com.burgeon.r3.sg.basic.model.request.SgCallbackShareQtyCalcRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageSingleUpdateRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageUpdateBillRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageUpdateControlRequest;
import com.burgeon.r3.sg.basic.model.request.vo.SgStorageUpdateBillItemLsRequest;
import com.burgeon.r3.sg.basic.model.result.SgCallbackShareQtyCalcItemResult;
import com.burgeon.r3.sg.basic.model.result.SgCallbackShareQtyCalcResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageUpdateResult;
import com.burgeon.r3.sg.basic.services.SgStorageCalculationService;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorageChangeFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.DateUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.inf.model.request.pos.SgPhyOutCommitForPosRequest;
import com.burgeon.r3.sg.inf.model.request.pos.SgPhyOutCommitItemForPosRequest;
import com.burgeon.r3.sg.share.model.request.adjust.SgBShareAdjustBillSaveRequest;
import com.burgeon.r3.sg.share.model.request.adjust.SgBShareAdjustItemSaveRequest;
import com.burgeon.r3.sg.share.model.request.adjust.SgBShareAdjustSaveRequest;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnBillSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnItemSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnSaveRequst;
import com.burgeon.r3.sg.share.model.result.allocation.SgBShareAllocationReturnSaveAndSubmitResult;
import com.burgeon.r3.sg.share.services.adjust.SgBShareAdjustSaveAndSubmitService;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationReturnSaveAndSubmitService;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationReturnSaveService;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutSaveRequest;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutBillSaveResult;
import com.burgeon.r3.sg.store.services.SgStoreStorageService;
import com.burgeon.r3.sg.store.services.out.SgBStoOutSaveService;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BeanCopierUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/2 17:40
 */
@Slf4j
@Component
public class SgBStoCommitForPosService {

    //    @Autowired
//    private R3MqSendHelper r3MqSendHelper;
    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private SgBStorageChangeFtpMapper storageChangeFtpMapper;

    @Autowired
    private SgStorageMqConfig storageMqConfig;

    @Autowired
    SgBShareAllocationReturnSaveService shareAllocationReturnSaveService;

    @Autowired
    private SgStorageCalculationService sgStorageCalculationService;

    @Autowired
    private SgBShareAdjustSaveAndSubmitService shareAdjustSaveService;

    @Autowired
    private SgStoreStorageService storeStorageService;

    @Autowired
    private CpCStoreMapper storeMapper;

    /**
     * 分货退货单
     */
    @Autowired
    private SgBShareAllocationReturnSaveAndSubmitService shareAllocationReturnSaveAndSubmitService;

    /**
     * pos 发起库存扣减接口
     *
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 saveAndAuditOutBills(SgPhyOutCommitForPosRequest request) {

        log.info("Start SgPhyOutCommitForPosService.saveAndAuditOutBills ReceiveParams:{};",
                JSONObject.toJSONString(request));

        if (request.getLoginUser() == null) {
            request.setLoginUser(DrpUtils.getUser());
        }
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "生成库存单据成功!");
        JSONObject paramObj = new JSONObject();
        paramObj.put("billno", request.getBillNo());
        paramObj.put("id", request.getId());
        String billNo = request.getBillNo();
        String lockKsy = SgConstants.SG_B_STO_OUT + ":" + billNo;

        List<String> redisKeys = new ArrayList<>();
        SgRedisLockUtils.lock(lockKsy);
        try {
            checkParam(request);
            LambdaQueryWrapper<SgBStorageChangeFtp> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SgBStorageChangeFtp::getBillNo, request.getBillNo());
            AssertUtils.isTrue(storageChangeFtpMapper.selectCount(wrapper) == 0, "当前单据已生成库存单据，请勿重新生成！");

            boolean isNegative = false;

            // 需生成共享占用单的明细集合
            List<SgAdjustParam> shareAdjustList = new ArrayList<>();
            // 需生成分货退货单的明细集合
            List<SgCallbackShareQtyCalcItemResult> callbackShareQtyCalcItemResultList = new ArrayList<>();

            // 调用斌哥的接口，获取调整数量
            SgCallbackShareQtyCalcRequest qtyRequest = new SgCallbackShareQtyCalcRequest();
            qtyRequest.setSupplyStoreId(request.getCpCStoreId());
            List<SgCallbackShareQtyCalcItemRequest> qtyItemRequestList = new ArrayList<>();

            Map<Long, SgPhyOutCommitItemForPosRequest> requestItemMap = request.getItems().stream().collect(
                    Collectors.toMap(SgPhyOutCommitItemForPosRequest::getPsCSkuId, x -> x, (o1, o2) -> o1));

            request.getItems().forEach(item -> {
                if (item.getQty().compareTo(BigDecimal.ZERO) > 0) {
                    SgCallbackShareQtyCalcItemRequest qtyItemRequest = new SgCallbackShareQtyCalcItemRequest();

                    qtyItemRequest.setPsCSkuId(item.getPsCSkuId());
                    qtyItemRequest.setQtyChange(item.getQty().negate());

                    qtyItemRequestList.add(qtyItemRequest);
                }
            });

            if (CollectionUtils.isNotEmpty(qtyItemRequestList)) {

                qtyRequest.setItemList(mergeItem(qtyItemRequestList));

                ValueHolderV14<SgCallbackShareQtyCalcResult> v14Result = sgStorageCalculationService
                        .calcCallbackShareQty(qtyRequest, request.getLoginUser());

                if (log.isDebugEnabled()) {
                    log.debug("SgBStoAdjustSubmitService.submit.calcCallbackShareQty result is {}",
                            JSONObject.toJSONString(v14Result));
                }

                // 对返回值进行校验
                AssertUtils.cannot(v14Result.getCode() != ResultCode.SUCCESS, v14Result.getMessage());

                SgCallbackShareQtyCalcResult qtyResult = v14Result.getData();

//                isNegative = SgConstants.ISNO_FLAG_NO == (qtyResult.getIsNegative() != null ?
//                        qtyResult.getIsNegative() : SgConstants.ISNO_FLAG_YES);

                boolean flag = isNegative;

//                if (qtyResult.getChangeUpdateResult() == SgConstantsIF.PREOUT_RESULT_OUT_STOCK) {
//                    // 获取逻辑仓档案，是否允许负库存
//                    List<SgCallbackShareQtyCalcOutItemResult> outStockItemList = qtyResult.getOutStockItemList();
//                    AssertUtils.cannot(CollectionUtils.isEmpty(outStockItemList), "异常明细为空");
//
//                    if (!isNegative) {
//                        AssertUtils.logAndThrow("条码[" + outStockItemList.get(0).getPsCSkuId() +
//                                "]调整后库存为负，不允许调整");
//                    }
//                }

                // 处理返回结果
                qtyResult.getItemResultList().forEach(resultItem -> {
                    SgPhyOutCommitItemForPosRequest requestItem = requestItemMap.get(resultItem.getPsCSkuId());
                    AssertUtils.notNull(requestItem, "获取库存异常！");

//                    if (!flag && requestItem.getQty().abs().compareTo(resultItem.getQtyAvailable()) > 0) {
//                        AssertUtils.logAndThrow("条码[" + requestItem.getPsCSkuId() +
//                                "]调整后库存为负，不允许调整");
//                    }

//                    SgCallbackShareQtyCalcSubItemResult shareStoreCalcResult = resultItem.getShareStoreCalcResult();
//                    if (Objects.nonNull(shareStoreCalcResult)) {
//                        // 生成共享调整单
//                        SgAdjustParam param = new SgAdjustParam();
//                        param.setQty(shareStoreCalcResult.getQtyStorage());
//                        param.setSkuId(resultItem.getPsCSkuId());
//                        param.setStoreId(shareStoreCalcResult.getStoreId());
//                        shareAdjustList.add(param);
//                    }

                    if (CollectionUtils.isNotEmpty(resultItem.getSaStoreCalcResults())) {
                        // 生成分货退货单
                        callbackShareQtyCalcItemResultList.add(resultItem);
                    }
                });
            }

            if (CollectionUtils.isNotEmpty(shareAdjustList)) {
                try {
                    createAdjust(request, shareAdjustList, redisKeys);
                } catch (Exception e) {
                    AssertUtils.logAndThrowException("共享量调整单创建失败！", e, request.getLoginUser().getLocale());
                }
            }
            if (CollectionUtils.isNotEmpty(callbackShareQtyCalcItemResultList)) {
                try {
                    createShareAllocationReturn(request, callbackShareQtyCalcItemResultList, redisKeys, requestItemMap);
                } catch (Exception e) {
                    AssertUtils.logAndThrowException("分货退货单创建失败！", e, request.getLoginUser().getLocale());
                }
            }
            // 直接调整库存
            doStorageSingleUpdate(request, request.getItems(), redisKeys, isNegative);

            paramObj.put("code", ResultCode.SUCCESS);
            paramObj.put("message", "success");
        } catch (Exception e) {
            // 回滚redis事务
            StorageBasicUtils.rollbackStorage(redisKeys, request.getLoginUser());

            String errMsg = "一键扣减库存失败！" + e.getMessage();
            log.error("零售单" + request.getBillNo() + errMsg);
            paramObj.put("code", ResultCode.FAIL);
            paramObj.put("message", errMsg);
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(errMsg);
        } finally {
            SgRedisLockUtils.unlock(lockKsy);
        }
        try {
            if (log.isDebugEnabled()) {
                log.debug("Retail BillNo:{},Start MQ;", request.getBillNo());
            }
//            String message = r3MqSendHelper.sendMessage(storageMqConfig.getSgDefaultConfigName(),
//                    paramObj, storageMqConfig.getRetailSyncTopic(),
//                    SgStoreConstants.TAG_RETAIL_TO_NOTICES_STOCK_UPDATE,
//                    UUID.randomUUID().toString().replaceAll("-", ""));
            MqSendResult sendResult = defaultProducerSend.sendTopic(storageMqConfig.getRetailSyncTopic(),
                    SgStoreConstants.TAG_RETAIL_TO_NOTICES_STOCK_UPDATE,
                    paramObj.toJSONString(),
                    UUID.randomUUID().toString().replaceAll("-", ""));
            String message = sendResult.getMessageId();
            if (log.isDebugEnabled()) {
                log.debug("Retail BillNo:{},MsgId:{};", request.getBillNo(), message);
            }
            v14.setData(message);
        } catch (Exception e) {
            log.error(this.getClass().getName() + ".error 发送MQ消息给到【更新零售单更新库存状态】异常：" + e.getMessage(), e);
        }

        log.info("Finish SgPhyOutCommitForPosService.saveAndAuditOutBills Return:{};",
                JSONObject.toJSONString(v14));

        return v14;
    }

    /**
     * pos 发起参数校验
     *
     * @param request
     * @return
     */
    private void checkParam(SgPhyOutCommitForPosRequest request) {
        AssertUtils.notNull(request.getLoginUser(), "用户为未登录!");
        AssertUtils.isTrue(StringUtils.isNotEmpty(request.getBillNo()), "零售单号不能为空!");
        AssertUtils.notNull(request.getCpCStoreId(), "门店id不能为空!");
        AssertUtils.notEmpty(request.getItems(), "商品信息不能为空!");

        for (SgPhyOutCommitItemForPosRequest itemForPosRequest : request.getItems()) {
            // 【条码ID】【条码编码】【商品id】【商品编码】【商品名称】【规格id】【规格编码】【规格名称】
            if (itemForPosRequest.getPsCSkuId() == null
                    || StringUtils.isEmpty(itemForPosRequest.getPsCSkuEcode())
                    || itemForPosRequest.getPsCProId() == null
                    || StringUtils.isEmpty(itemForPosRequest.getPsCProEname())
                    || StringUtils.isEmpty(itemForPosRequest.getPsCProEcode())
                    || itemForPosRequest.getPsCSpec1Id() == null
                    || StringUtils.isEmpty(itemForPosRequest.getPsCSpec1Ename())
                    || StringUtils.isEmpty(itemForPosRequest.getPsCSpec1Ecode())
                    || itemForPosRequest.getPsCSpec2Id() == null
                    || StringUtils.isEmpty(itemForPosRequest.getPsCSpec2Ename())
                    || StringUtils.isEmpty(itemForPosRequest.getPsCSpec2Ecode())
                    || itemForPosRequest.getPriceList() == null) {
                AssertUtils.logAndThrow("商品信息条码/商品/规格信息/价格金额不能为空");
            }
        }

        SgCpCStore store = storeMapper.selectById(request.getCpCStoreId());
        if (StringUtils.isEmpty(request.getCpCStoreEcode()) || StringUtils.isEmpty(request.getCpCStoreEname())) {
            request.setCpCStoreEcode(store.getCpCStoreEcode());
            request.setCpCStoreEname(store.getCpCStoreEname());
        }
        if (ObjectUtils.isEmpty(request.getBillDate())) {
            request.setBillDate(new Date());
        }

        // 最新封账日
        if (Objects.nonNull(store) && Objects.nonNull(store.getCurrentSealaccountDate())) {
            if (store.getCurrentSealaccountDate().compareTo(request.getBillDate()) > 0) {
                AssertUtils.logAndThrow("销售日期：" + DateUtils.formatSync8(request.getBillDate(), DateUtils.DATE_PATTERN)
                        + " 不允许小于等于 最新封账日期：" + DateUtils.formatSync8(store.getCurrentSealaccountDate(),
                        DateUtils.DATE_PATTERN));
            }
        }

    }

    /**
     * 创建共享调整单
     */
    private void createAdjust(SgPhyOutCommitForPosRequest posRequest, List<SgAdjustParam> itemList,
                              List<String> redisKeys) {

        Map<Long, List<SgAdjustParam>> shareAdjustMap = itemList.stream().collect(
                Collectors.groupingBy(SgAdjustParam::getStoreId));

        shareAdjustMap.forEach((k, v) -> {
            SgBShareAdjustBillSaveRequest request = new SgBShareAdjustBillSaveRequest();
            SgBShareAdjustSaveRequest mainRequest = new SgBShareAdjustSaveRequest();
            List<SgBShareAdjustItemSaveRequest> itemRequestList = new ArrayList<>();

            // 处理mainRequest
            mainRequest.setBillDate(posRequest.getBillDate());
            mainRequest.setTotRowNum(v.size());
            mainRequest.setRemark("pos零售单生成，单号：" + posRequest.getBillNo());

            mainRequest.setSgCShareStoreId(k);

            AtomicReference<BigDecimal> totQty = new AtomicReference<>(BigDecimal.ZERO);

            v.forEach(item -> {
                SgBShareAdjustItemSaveRequest itemRequest = new SgBShareAdjustItemSaveRequest();

                itemRequest.setQty(item.getQty());
                itemRequest.setPsCSkuId(item.getSkuId());

                totQty.accumulateAndGet(item.getQty(), BigDecimal::add);

                itemRequestList.add(itemRequest);
            });

            mainRequest.setTotQty(totQty.get());

            request.setObjId(-1L);
            request.setSgBShareAdjustSaveRequest(mainRequest);
            request.setSgBShareAdjustItemSaveRequests(itemRequestList);
            request.setLoginUser(posRequest.getLoginUser());
            ValueHolderV14<SgR3BaseResult> v14 = shareAdjustSaveService.saveAndSubmit(request);
            AssertUtils.isTrue(v14.getCode() == ResultCode.SUCCESS, "共享调整单创建失败！");
            if (log.isDebugEnabled()) {
                log.debug("Start SgPhyOutCommitForPosService.createAdjust ReceiveParams:{};"
                        + JSONObject.toJSONString(v14));
            }
            redisKeys.addAll((List<String>) v14.getData().getDataJo().get("redisFtpKey"));
        });
    }

    /**
     * 生成分货退货单
     *
     * @param phyRequest
     * @param callbackShareQtyCalcItemResultList
     */
    private void createShareAllocationReturn(SgPhyOutCommitForPosRequest phyRequest,
                                             List<SgCallbackShareQtyCalcItemResult> callbackShareQtyCalcItemResultList,
                                             List<String> redisKeys,
                                             Map<Long, SgPhyOutCommitItemForPosRequest> itemMap) {

        Map<String, List<SgAdjustParam>> groupMap = new HashMap<>();

        callbackShareQtyCalcItemResultList.forEach(callbackShareQtyCalcItemResult ->
                callbackShareQtyCalcItemResult.getSaStoreCalcResults().forEach(saStoreResult -> {
                    List<SgAdjustParam> value = groupMap.get(
                            callbackShareQtyCalcItemResult.getSgCShareStoreId() + ":"
                                    + saStoreResult.getStoreId());
                    if (value == null) {
                        value = new ArrayList<>();
                        SgAdjustParam param = new SgAdjustParam();
                        param.setSkuId(callbackShareQtyCalcItemResult.getPsCSkuId());
                        param.setResult(saStoreResult);
                        value.add(param);
                        groupMap.put(callbackShareQtyCalcItemResult.getSgCShareStoreId()
                                + ":" + saStoreResult.getStoreId(), value);
                    } else {
                        SgAdjustParam param = new SgAdjustParam();
                        param.setSkuId(callbackShareQtyCalcItemResult.getPsCSkuId());
                        param.setResult(saStoreResult);
                        value.add(param);
                    }
                }));

        List<SgBShareAllocationReturnBillSaveRequst> requestList = new ArrayList<>();

        groupMap.forEach((k, v) -> {
            // 构建request
            SgBShareAllocationReturnBillSaveRequst request = new SgBShareAllocationReturnBillSaveRequst();

            SgBShareAllocationReturnSaveRequst mainRequest = new SgBShareAllocationReturnSaveRequst();
            // 处理主表数据
            mainRequest.setBillDate(phyRequest.getBillDate());
            mainRequest.setSourceBillId(phyRequest.getId());
            mainRequest.setSourceBillNo(phyRequest.getBillNo());
            mainRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_POS);
            String[] split = k.split(":");
            mainRequest.setSgCSaStoreId(Long.valueOf(split[1]));
            mainRequest.setSgCShareStoreId(Long.valueOf(split[0]));
            mainRequest.setRemark("pos零售单生成，单号：" + phyRequest.getBillNo());

            List<SgBShareAllocationReturnItemSaveRequst> itemRequestList = new ArrayList<>();
            v.forEach(item -> {
                SgBShareAllocationReturnItemSaveRequst itemRequest = new SgBShareAllocationReturnItemSaveRequst();
                // 处理明细数据
                itemRequest.setPsCSkuId(item.getSkuId());
                itemRequest.setAmt(item.getResult().getQty());
                itemRequest.setPsCSkuEcode(itemMap.get(item.getSkuId()).getPsCSkuEcode());
                itemRequest.setQty(item.getResult().getQty().negate());
                itemRequest.setId(-1L);

                itemRequestList.add(itemRequest);
            });

            request.setLoginUser(phyRequest.getLoginUser());
            request.setAllocationReturnSaveRequst(mainRequest);
            request.setAllocationReturnItemSaveRequst(itemRequestList);
            request.setR3(true);
            requestList.add(request);
        });
        ValueHolderV14<SgBShareAllocationReturnSaveAndSubmitResult> shareAllocationResult =
                shareAllocationReturnSaveAndSubmitService.saveAndSubmit2(requestList);
        if (Objects.nonNull(shareAllocationResult.getData()) && CollectionUtils.isNotEmpty(shareAllocationResult.getData().getRedisFtpKeys())) {
            redisKeys.addAll(shareAllocationResult.getData().getRedisFtpKeys());
        }
        AssertUtils.isTrue(shareAllocationResult.getCode() == ResultCode.SUCCESS, "分货退货单创建失败！");
    }

    /**
     * 生成逻辑占用单/出库通知单/逻辑出库单
     */
    private void createStoOut(SgPhyOutCommitForPosRequest posRequest, List<SgPhyOutCommitItemForPosRequest> items,
                              List<String> redisKeys) {
        SgBStoOutBillSaveRequest request = new SgBStoOutBillSaveRequest();
        request.setIsAutoOut(SgConstants.IS_AUTO_Y);
        request.setLoginUser(posRequest.getLoginUser());
        request.setUpdateMethod(SgConstantsIF.ITEM_UPDATE_TYPE_ALL);
        request.setPreoutWarningType(SgConstantsIF.PREOUT_RESULT_ERROR);
        request.setIsCancel(false);
        SgBStoOutSaveRequest outSaveRequest = new SgBStoOutSaveRequest();
        outSaveRequest.setRemark(posRequest.getRemark());
        // 收货人信息
        outSaveRequest.setReceiverName(posRequest.getReceiverName());
        outSaveRequest.setReceiverMobile(posRequest.getReceiverMobile());
        outSaveRequest.setReceiverPhone(posRequest.getReceiverPhone());
        outSaveRequest.setReceiverAddress(posRequest.getReceiverAddress());
        // outSaveRequest.setReceiverZip(posRequest.getReceiverZip());
        outSaveRequest.setRemark("pos零售单生成，单号：" + posRequest.getBillNo());
        // 物流公司信息
        outSaveRequest.setCpCLogisticsId(posRequest.getCpCLogisticsId());
        outSaveRequest.setCpCLogisticsEcode(posRequest.getCpCLogisticsEcode());
        outSaveRequest.setCpCLogisticsEname(posRequest.getCpCLogisticsEname());
        outSaveRequest.setLogisticNumber(posRequest.getLogisticNumber());

        // 省市区信息
        outSaveRequest.setCpCRegionProvinceId(posRequest.getCpCRegionProvinceId());
        outSaveRequest.setCpCRegionProvinceEcode(posRequest.getCpCRegionProvinceEcode());
        outSaveRequest.setCpCRegionProvinceEname(posRequest.getCpCRegionProvinceEname());
        outSaveRequest.setCpCRegionCityId(posRequest.getCpCRegionCityId());
        outSaveRequest.setCpCRegionCityEcode(posRequest.getCpCRegionCityEcode());
        outSaveRequest.setCpCRegionCityEname(posRequest.getCpCRegionCityEname());
        outSaveRequest.setCpCRegionAreaId(posRequest.getCpCRegionAreaId());
        outSaveRequest.setCpCRegionAreaEcode(posRequest.getCpCRegionAreaEcode());
        outSaveRequest.setCpCRegionAreaEname(posRequest.getCpCRegionAreaEname());

 /*       outSaveRequest.setCpCSupplierId(posRequest.getCpCSupplierId());
        outSaveRequest.setCpCCsEcode(posRequest.getCpCCsEcode());
        outSaveRequest.setCpCCsEname(posRequest.getCpCCsEname());*/
        //TODO 出库类型？
        //  outSaveRequest.setOutType(SgConstants.OUT_TYPE_RETAIL);
        outSaveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_POS);
        outSaveRequest.setSourceBillId(posRequest.getId());
        outSaveRequest.setSourceBillNo(posRequest.getBillNo());
        outSaveRequest.setBillDate(posRequest.getBillDate());
        request.setSgBStoOutSaveRequest(outSaveRequest);
        request.setLoginUser(posRequest.getLoginUser());

        request.setIsPos(Boolean.TRUE);
        List<SgBStoOutItemSaveRequest> itemList = Lists.newArrayList();
        for (SgPhyOutCommitItemForPosRequest itemForPosRequest : items) {
            SgBStoOutItemSaveRequest itemSaveRequest = new SgBStoOutItemSaveRequest();
            BeanUtils.copyProperties(itemForPosRequest, itemSaveRequest);
            itemSaveRequest.setSourceBillItemId(itemForPosRequest.getId());
            itemSaveRequest.setCpCStoreId(posRequest.getCpCStoreId());
            itemSaveRequest.setCpCStoreEname(posRequest.getCpCStoreEname());
            itemSaveRequest.setCpCStoreEcode(posRequest.getCpCStoreEname());
            itemSaveRequest.setQtyPreout(itemForPosRequest.getQty());
            itemList.add(itemSaveRequest);
        }
        request.setSgBStoOutItemSaveRequests(itemList);
        request.setLoginUser(posRequest.getLoginUser());

        SgBStoOutSaveService noticesSaveRPCService = ApplicationContextHandle.getBean(SgBStoOutSaveService.class);
        ValueHolderV14<SgBStoOutBillSaveResult> stoOutResult = noticesSaveRPCService.saveSgStoOut(request);
        AssertUtils.isTrue(stoOutResult.getCode() == ResultCode.SUCCESS, "生成逻辑占用单失败");

        redisKeys.addAll(stoOutResult.getData().getRedisKey());
    }

    /**
     * 生成逻辑仓库存变动流水及库存变动
     *
     * @param posRequest
     * @param itemList
     */
    private void doStorageSingleUpdate(SgPhyOutCommitForPosRequest posRequest,
                                       List<SgPhyOutCommitItemForPosRequest> itemList,
                                       List<String> redisFtpKeys, boolean isNegative) {
        SgStorageSingleUpdateRequest request = new SgStorageSingleUpdateRequest();

        request.setLoginUser(posRequest.getLoginUser());

        SgStorageUpdateBillRequest billRequest = new SgStorageUpdateBillRequest();

        SgStorageUpdateControlRequest mainRequest = new SgStorageUpdateControlRequest();
        // 处理mainRequest/billRequest
        mainRequest.setPreoutOperateType(2);
        mainRequest.setNegativeAvailable(isNegative);
        mainRequest.setNegativeStorage(isNegative);

        billRequest.setBillDate(posRequest.getBillDate());
        billRequest.setBillId(posRequest.getId());
        billRequest.setSourceBillNo(posRequest.getBillNo());
        billRequest.setSourceBillId(posRequest.getId());
        billRequest.setServiceNode(SgConstantsIF.SERVICE_NODE_RETAIL_POS_SUBMIT);
        billRequest.setBillNo(posRequest.getBillNo());
        billRequest.setBillType(SgConstantsIF.BILL_TYPE_RETAIL_POS);
        billRequest.setChangeDate(posRequest.getBillDate());
        billRequest.setIsCancel(false);

        List<AbstractSgStorageUpdateBillItemRequest> itemRequestList = new ArrayList<>();
        // 处理item
        itemList.forEach(item -> {
            SgStorageUpdateBillItemLsRequest itemRequest = new SgStorageUpdateBillItemLsRequest();
            itemRequest.setBillItemId(item.getId());
            BeanCopierUtil.copy(item, itemRequest);
            BeanCopierUtil.copy(posRequest, itemRequest);
            itemRequest.setQtyStorageChange(item.getQty().negate());
            itemRequest.setControlmodel(mainRequest);

            itemRequestList.add(itemRequest);
        });

        billRequest.setItemList(itemRequestList);
        request.setBill(billRequest);
        request.setControlModel(mainRequest);

        ValueHolderV14<SgStorageUpdateResult> storageUpdateResult = storeStorageService.updatedStorage(request);
        AssertUtils.isTrue(storageUpdateResult.getCode() == ResultCode.SUCCESS, "库存调整失败！");
        redisFtpKeys.addAll(storageUpdateResult.getData().getRedisBillFtpKeyList());
    }

    private List<SgCallbackShareQtyCalcItemRequest> mergeItem(List<SgCallbackShareQtyCalcItemRequest> request) {
        HashMap<Long, SgCallbackShareQtyCalcItemRequest> map = new HashMap<>();

        request.forEach(x -> {
            SgCallbackShareQtyCalcItemRequest item = map.get(x.getPsCSkuId());
            if (item == null) {
                map.put(x.getPsCSkuId(), x);
            } else {
                item.setQtyChange(x.getQtyChange().add(item.getQtyChange()));
            }
        });

        return new ArrayList<>(map.values());
    }

}
