package com.burgeon.r3.inf.services.pda;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransfer;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransferItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.inf.model.request.pda.SgStoTransferItemsPdaRequest;
import com.burgeon.r3.sg.inf.model.request.pda.SgStoTransferItemsSavePdaRequest;
import com.burgeon.r3.sg.inf.model.request.pda.SgStoTransferMainPdaRequest;
import com.burgeon.r3.sg.inf.model.request.pda.SgStoTransferSavePdaRequest;
import com.burgeon.r3.sg.inf.model.result.pda.SgStoTransferItemsPdaResult;
import com.burgeon.r3.sg.inf.model.result.pda.SgStoTransferMainPdaResult;
import com.burgeon.r3.sg.store.mapper.transfer.SgBStoTransferItemMapper;
import com.burgeon.r3.sg.store.mapper.transfer.SgBStoTransferMapper;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferSaveRequest;
import com.burgeon.r3.sg.store.services.transfer.SgBStoTransferConfirmSubmitService;
import com.burgeon.r3.sg.store.services.transfer.SgBStoTransferSaveService;
import com.burgeon.r3.sg.store.services.transfer.SgBStoTransferSubmitService;
import com.burgeon.r3.sg.store.services.transfer.SgTransferBillStatusEnum;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.model.table.CpCustomer;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description 逻辑调拨单信息查询，保存，审核
 * <AUTHOR>
 * @Date 2021/8/11 15:59
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgPdaStoTransferService {

    @Autowired
    private SgBStoTransferMapper sgBStoTransferMapper;

    @Autowired
    private SgBStoTransferItemMapper sgBStoTransferItemMapper;

    @Autowired
    private SgBStoTransferSaveService sgBStoTransferSaveService;

    @Autowired
    private SgBStoTransferSubmitService sgBStoTransferSubmitService;

    @Autowired
    private SgBStoTransferConfirmSubmitService sgBStoTransferConfirmSubmitService;

    //运输类型：4：快递
    public final static Long CP_C_TRANWAY_ASSIGN_ID = 4L;

    /**
     * 查询逻辑调拨单主表信息
     *
     * @param request
     * @return
     */
    public ValueHolderV14<PageInfo<SgStoTransferMainPdaResult>> querySgBStoTransferMain(SgStoTransferMainPdaRequest request, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgPdaStoTransferService.querySgBStoTransferMain. ReceiveParams:" +
                    "request:{};", JSONObject.toJSONString(request));
        }
        ValueHolderV14 holder = new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        Date billDateBegin = request.getBillDateBegin();
        Date billDateEnd = request.getBillDateEnd();
        String billNo = request.getBillNo();
        List<String> billStatusList = request.getBillStatus();
        List<Long> sgBStoTransferIds = request.getSgBStoTransferId();
        List<SgStoTransferMainPdaResult> resultList = new ArrayList<>();
        List<SgBStoTransfer> sgBStoTransferList = null;
        try {

            if (request.getPageSize() != null && request.getPageNum() != null) {
                /** 分页查询 **/
                PageHelper.startPage(request.getPageNum(), request.getPageSize());
            }
            //全量查询
            sgBStoTransferList = sgBStoTransferMapper.selectList(new LambdaQueryWrapper<SgBStoTransfer>()
                    .in(SgBStoTransfer::getStatus, billStatusList)
                    .in(!CollectionUtils.isEmpty(sgBStoTransferIds), SgBStoTransfer::getId, sgBStoTransferIds)
                    .like(StringUtils.isNotEmpty(billNo), SgBStoTransfer::getBillNo, billNo)
                    .ge(billDateBegin != null, SgBStoTransfer::getBillDate, billDateBegin)
                    .le(billDateEnd != null, SgBStoTransfer::getBillDate, billDateEnd));

            if (CollectionUtils.isNotEmpty(sgBStoTransferList)) {
                sgBStoTransferList.forEach(item -> {
                    SgStoTransferMainPdaResult pdaItem = new SgStoTransferMainPdaResult();
                    BeanUtils.copyProperties(item, pdaItem);
                    pdaItem.setSgBStoTransferId(item.getId());
                    pdaItem.setBillStatus(item.getStatus().toString());
                    pdaItem.setBillType("正常调拨");
                    //发货经销商ID
                    CpCustomer senderCpCustomerById = CommonCacheValUtils.getCpCustomerById(item.getSenderCustomerId());
                    if (log.isDebugEnabled()) {
                        log.debug("SgPdaStoInNoticesService.querySgBStoInNoticesMain.ReceiveParams:senderCpCustomerById:{};", JSONObject.toJSONString(senderCpCustomerById));
                    }
                    if (senderCpCustomerById != null) {
                        pdaItem.setSenderCustomerEname(senderCpCustomerById.getEname());
                    }
                    //收货经销商名称
                    CpCustomer receiverCpCustomerById = CommonCacheValUtils.getCpCustomerById(item.getReceiverCustomerId());
                    if (log.isDebugEnabled()) {
                        log.debug("SgPdaStoInNoticesService.querySgBStoInNoticesMain.ReceiveParams:receiverCpCustomerById:{};", JSONObject.toJSONString(receiverCpCustomerById));
                    }
                    if (receiverCpCustomerById != null) {
                        pdaItem.setReceiverCustomerEname(receiverCpCustomerById.getEname());
                    }
                    resultList.add(pdaItem);
                });
            }
            if (resultList != null) {
                holder.setData(new PageInfo<>(resultList));
            } else {
                holder.setData(sgBStoTransferList);
            }
        } catch (Exception e) {
            log.error("SgPdaStoTransferService.querySgBStoTransferMain. error:{}", Throwables.getStackTraceAsString(e));
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("查询逻辑调拨单主表信息发生异常," + e.getMessage(), loginUser.getLocale()));
        }
        return holder;
    }

    /**
     * 查询出逻辑调拨单明细
     *
     * @param request
     * @param loginUser 登录用户
     * @return
     */
    public ValueHolderV14<PageInfo<SgStoTransferItemsPdaResult>> querySgBStoTransferItem(SgStoTransferItemsPdaRequest request, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgPdaStoTransferService.querySgBStoTransferItem. ReceiveParams:" +
                    "request:{};", JSONObject.toJSONString(request));
        }
        ValueHolderV14 holder = new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        List<SgBStoTransferItem> sgBStoTransferItemsList = null;
        List<SgStoTransferItemsPdaResult> resultList = new ArrayList<>();
        try {

            if (request.getPageSize() != null && request.getPageNum() != null) {
                /** 分页查询 **/
                PageHelper.startPage(request.getPageNum(), request.getPageSize());
            }
            //全量查询
            sgBStoTransferItemsList = sgBStoTransferItemMapper.selectList(new LambdaQueryWrapper<SgBStoTransferItem>()
                    .in(SgBStoTransferItem::getSgBStoTransferId, request.getSgBStoTransferId()));

            if (CollectionUtils.isNotEmpty(sgBStoTransferItemsList)) {
                sgBStoTransferItemsList.forEach(item -> {
                    SgStoTransferItemsPdaResult pdaItem = new SgStoTransferItemsPdaResult();
                    BeanUtils.copyProperties(item, pdaItem);
                    pdaItem.setSgBStoTransferItemId(item.getId());
                    resultList.add(pdaItem);
                });
            }

            if (resultList != null) {
                holder.setData(new PageInfo<>(resultList));
            } else {
                holder.setData(resultList);
            }
        } catch (Exception e) {
            log.error("SgPdaStoTransferService.querySgBStoTransferItem. error:{}", Throwables.getStackTraceAsString(e));
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("查询逻辑调拨单明细表信息发生异常," + e.getMessage(), loginUser.getLocale()));
        }
        return holder;
    }


    /**
     * 逻辑调拨单的新增、保存、审核
     *
     * @param request
     * @param loginUser
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 saveOrSubmitStoTransfer(SgStoTransferSavePdaRequest request, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgPdaStoTransferService.saveOrSubmitStoTransfer. ReceiveParams:request:{};", JSONObject.toJSONString(request));
        }
        ValueHolderV14 holder = new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        List<String> redisBillFtpKeyList = new ArrayList<>();
        try {
            //判断是否是新增：
            SgBStoTransferBillSaveRequest sgBStoTransferBillSaveRequest = new SgBStoTransferBillSaveRequest();
            SgBStoTransferSaveRequest sgBStoTransferSaveRequest = null;
            List<SgBStoTransferItemSaveRequest> sgBStoTransferItemList = new ArrayList<>();
            if (request.getBillDate() != null || request.getReceiverStoreEcode() != null || request.getSenderStoreEcode() != null || request.getRemark() != null) {
                sgBStoTransferSaveRequest = new SgBStoTransferSaveRequest();
            }
            //组装逻辑调拨单数据
            ValueHolderV14<SgBStoTransferBillSaveRequest> valueHolderV14 = assemblyStoTransfer(request, sgBStoTransferBillSaveRequest, sgBStoTransferSaveRequest, sgBStoTransferItemList, loginUser);

            if (!valueHolderV14.isOK()) {
                AssertUtils.logAndThrow("新增或保存逻辑调拨单失败！" + valueHolderV14.getMessage());
            }
            sgBStoTransferBillSaveRequest = valueHolderV14.getData();
            sgBStoTransferBillSaveRequest.setLoginUser(loginUser);
            if (sgBStoTransferSaveRequest != null || (CollectionUtils.isNotEmpty(sgBStoTransferItemList) && sgBStoTransferItemList.size() > 0)) {
                ValueHolderV14<SgR3BaseResult> sgBStoTransferInsertResult = sgBStoTransferSaveService.save(sgBStoTransferBillSaveRequest);
                if (!sgBStoTransferInsertResult.isOK()) {
                    AssertUtils.logAndThrow("新增或保存逻辑调拨单失败！" + sgBStoTransferInsertResult.getMessage());
                }
                if (request.getSgBStoTransferId() == -1) {
                    //获取调拨单id
                    Long objId = sgBStoTransferInsertResult.getData().getDataJo().getLong("objid");
                    request.setSgBStoTransferId(objId);
                }
            }
            //进行审核操作
            if (SgConstants.IS_SUBMIT_Y.equals(request.getIsSubmit())) {
                SgR3BaseRequest sgR3BaseRequest = new SgR3BaseRequest();
                sgR3BaseRequest.setLoginUser(loginUser);
                sgR3BaseRequest.setObjId(request.getSgBStoTransferId());
                sgR3BaseRequest.setR3(true);
                ValueHolderV14<SgR3BaseResult> sgBStoTransferSubmitResult =
                        sgBStoTransferSubmitService.submitTransfer(sgR3BaseRequest, false);
                if (!sgBStoTransferSubmitResult.isOK()) {
                    AssertUtils.logAndThrow("逻辑调拨单审核失败！" + sgBStoTransferSubmitResult.getMessage());
                }
                redisBillFtpKeyList.addAll((List<String>) sgBStoTransferSubmitResult.getData().getDataJo().get("redisFtpKey"));
                //是否需要传入rediskey 键
                /*ValueHolderV14<SgR3BaseResult> sgBStoTransferConfirmSubmitResult = sgBStoTransferConfirmSubmitService.submitTransferConfirmation(sgR3BaseRequest, redisBillFtpKeyList);
                if (!sgBStoTransferConfirmSubmitResult.isOK()) {
                    AssertUtils.logAndThrow("逻辑调拨单审核失败！" + sgBStoTransferSubmitResult.getMessage());
                }*/
            }
        } catch (Exception e) {
            StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, loginUser);
            log.error("SgPdaStoTransferService.saveOrSubmitStoTransfer. error:{}", Throwables.getStackTraceAsString(e));
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("保存或审核逻辑调拨单信息发生异常！" + e.getMessage(), loginUser.getLocale()));
        }
        return holder;
    }

    /**
     * 组装逻辑调拨单的数据
     *
     * @param request                       接口请求参数
     * @param sgBStoTransferBillSaveRequest 逻辑调拨单 保存服务需要传入的参数
     * @param sgBStoTransferSaveRequest     逻辑调拨单主表数据参数对象
     * @param sgBStoTransferItemList        逻辑调拨单明细表数据参数对象
     * @param loginUser                     登录用户
     * @return
     */
    private ValueHolderV14<SgBStoTransferBillSaveRequest> assemblyStoTransfer(SgStoTransferSavePdaRequest request,
                                                                              SgBStoTransferBillSaveRequest sgBStoTransferBillSaveRequest, SgBStoTransferSaveRequest sgBStoTransferSaveRequest,
                                                                              List<SgBStoTransferItemSaveRequest> sgBStoTransferItemList, User loginUser) {
        ValueHolderV14<SgBStoTransferBillSaveRequest> holder = new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        if (request.getBillDate() != null) {
            sgBStoTransferSaveRequest.setBillDate(request.getBillDate());
        }
        if (StringUtils.isNotEmpty(request.getSenderStoreEcode())) {
            CpCStore senderStore = CommonCacheValUtils.getStoreInfoByEcode(request.getSenderStoreEcode());
            if (senderStore == null) {
                AssertUtils.logAndThrow("发货店仓不存在！", loginUser.getLocale());
            }
            sgBStoTransferSaveRequest.setSenderStoreId(senderStore.getId());
            sgBStoTransferSaveRequest.setSenderStoreEname(senderStore.getEname());
            sgBStoTransferSaveRequest.setSenderCustomerId(senderStore.getCpCCustomerId());
            sgBStoTransferSaveRequest.setSenderStoreEcode(request.getSenderStoreEcode());
        }
        if (StringUtils.isNotEmpty(request.getReceiverStoreEcode())) {
            CpCStore receiverStore = CommonCacheValUtils.getStoreInfoByEcode(request.getReceiverStoreEcode());
            if (receiverStore == null) {
                AssertUtils.logAndThrow("收货店仓不存在！", loginUser.getLocale());
            }
            sgBStoTransferSaveRequest.setReceiverStoreId(receiverStore.getId());
            sgBStoTransferSaveRequest.setReceiverStoreEname(receiverStore.getEname());
            sgBStoTransferSaveRequest.setReceiverCustomerId(receiverStore.getCpCCustomerId());
            sgBStoTransferSaveRequest.setReceiverStoreEcode(request.getReceiverStoreEcode());
            sgBStoTransferSaveRequest.setRemark(request.getRemark());
        }

        if (request.getSgBStoTransferId() == -1) {
            for (SgStoTransferItemsSavePdaRequest item : request.getItem()) {
                SgBStoTransferItemSaveRequest sgBStoTransferItemSaveRequest = new SgBStoTransferItemSaveRequest();
                sgBStoTransferItemSaveRequest.setId(item.getSgBStoTransferItemId());
                CommonCacheValUtils.setSkuInfo(null, item.getPsCSkuEcode(), sgBStoTransferItemSaveRequest);
                sgBStoTransferItemSaveRequest.setQty(item.getQty());
                sgBStoTransferItemList.add(sgBStoTransferItemSaveRequest);
            }
            sgBStoTransferSaveRequest.setDrpBillType(0);
            sgBStoTransferSaveRequest.setCpCTranwayAssignId(CP_C_TRANWAY_ASSIGN_ID);
            sgBStoTransferSaveRequest.setIsAutoOut(SgConstants.IS_ACTIVE_N);
            sgBStoTransferBillSaveRequest.setObjId(-1L);
            sgBStoTransferBillSaveRequest.setItems(sgBStoTransferItemList);
        } else {
            sgBStoTransferBillSaveRequest.setObjId(request.getSgBStoTransferId());
            SgBStoTransfer sgBStoTransfer = sgBStoTransferMapper.selectById(request.getSgBStoTransferId());
            if (sgBStoTransfer == null) {
                AssertUtils.logAndThrow("当前记录已不存在！", loginUser.getLocale());
            }
            if (SgConstants.IS_ACTIVE_N.equals(sgBStoTransfer.getIsactive())) {
                AssertUtils.logAndThrow("当前单据已作废不允许保存！", loginUser.getLocale());
            }
            if (SgTransferBillStatusEnum.UN_AUDITED.getVal() != sgBStoTransfer.getStatus()) {
                AssertUtils.logAndThrow("当前单据状态不允许保存！", loginUser.getLocale());
            }
            if (CollectionUtils.isNotEmpty(request.getItem())) {
                for (SgStoTransferItemsSavePdaRequest item : request.getItem()) {
                    SgBStoTransferItemSaveRequest sgBStoTransferItemSaveRequest = new SgBStoTransferItemSaveRequest();
                    sgBStoTransferItemSaveRequest.setId(item.getSgBStoTransferItemId());
                    if (item.getSgBStoTransferItemId() < 0L) {

                        Integer selectCount = sgBStoTransferItemMapper.selectCount(new LambdaQueryWrapper<SgBStoTransferItem>()
                                .eq(SgBStoTransferItem::getSgBStoTransferId, request.getSgBStoTransferId())
                                .eq(SgBStoTransferItem::getPsCSkuEcode, item.getPsCSkuEcode()));
                        if (selectCount > 0) {
                            AssertUtils.logAndThrow("当前调拨单id:" + request.getSgBStoTransferId() + "下已经存在一条条码编码为:" +
                                    item.getPsCSkuEcode() + "的明细，不允许重复新增", loginUser.getLocale());
                        }
                        CommonCacheValUtils.setSkuInfo(null, item.getPsCSkuEcode(), sgBStoTransferItemSaveRequest);
                    }
                    sgBStoTransferItemSaveRequest.setQty(item.getQty());
                    sgBStoTransferItemList.add(sgBStoTransferItemSaveRequest);
                }
                sgBStoTransferBillSaveRequest.setItems(sgBStoTransferItemList);
            }
        }
        sgBStoTransferBillSaveRequest.setTransferSaveRequest(sgBStoTransferSaveRequest);
        holder.setData(sgBStoTransferBillSaveRequest);
        return holder;
    }
}
