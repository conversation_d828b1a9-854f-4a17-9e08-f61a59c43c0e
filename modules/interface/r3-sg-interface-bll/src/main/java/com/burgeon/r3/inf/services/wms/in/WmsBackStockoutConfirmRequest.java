package com.burgeon.r3.inf.services.wms.in;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.store.in.SgBWmsToStoStockOutResult;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.in.SgBWmsToStoStockOutResultMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description:
 *
 * @Author: guo.kw
 * @Since: 2022/7/14
 * create at: 2022/7/14 11:31
 */
@Slf4j
@Component
public class WmsBackStockoutConfirmRequest {

    @Autowired
    private SgBWmsToStoStockOutResultMapper sgBWmsToStoStockOutResultMapper;

    @Autowired
    private CpCPhyWarehouseMapper cpCPhyWarehouseMapper;

    public ValueHolderV14<String> apiProcess(String msg) {
        log.info(LogUtil.format("WmsBackStockoutConfirmRequest.apiProcess.msg={}",
                "WmsBackStockoutConfirmRequest.apiProcess"), msg);
        CusRedisTemplate<Object, Object> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();
        String lockKsy = SgConstants.SG_BILL_LOCK_WMSRETURN;

        try {
            JSONObject request = JSONObject.parseObject(msg);

            JSONObject deliveryOrderObject = request.getJSONObject("deliveryOrder");
            // 出库通知单号
            String noticesBillNo = deliveryOrderObject.getString("deliveryOrderCode");
            // 仓库编码
            String warehouseCode = deliveryOrderObject.getString("warehouseCode");
            // 出库单类型
            String orderType = deliveryOrderObject.getString("orderType");
            // WMS单据编号
            String wmsBillCode = deliveryOrderObject.getString("deliveryOrderId");
            // 外部业务编码
            String outBizCode = deliveryOrderObject.getString("outBizCode");
            if (StringUtils.isEmpty(outBizCode)) {
                throw new NDSException("外部业务编码不能为空");
            }
            lockKsy += outBizCode;

            Boolean ifAbsent = redisMasterTemplate.opsForValue().setIfAbsent(lockKsy, "OK");

            SgBWmsToStoStockOutResult sgBWmsToStoStockOutResult = sgBWmsToStoStockOutResultMapper.selectOne(new LambdaQueryWrapper<SgBWmsToStoStockOutResult>()
                    .eq(SgBWmsToStoStockOutResult::getOutBizCode, outBizCode)
                    .eq(SgBWmsToStoStockOutResult::getIsactive, "Y"));

            SgCpCPhyWarehouse cpCPhyWarehouse = null;
            if (!StringUtils.isEmpty(warehouseCode)) {
                cpCPhyWarehouse = cpCPhyWarehouseMapper.getCpCPhyWarehouseName(warehouseCode);
            }

            if (Objects.nonNull(sgBWmsToStoStockOutResult) || ifAbsent == null || !ifAbsent) {
                log.error(LogUtil.format("B2B入出库单WMS回传重复.单据信息：{},报文:{}", "WmsBackStockoutConfirmRequest.apiProcess"),
                        JSONObject.toJSONString(sgBWmsToStoStockOutResult), msg);
            } else {
                redisMasterTemplate.expire(lockKsy, 30, TimeUnit.SECONDS);

                SgBWmsToStoStockOutResult sgBWmsToStoStockOutResult1 = new SgBWmsToStoStockOutResult();
                sgBWmsToStoStockOutResult1.setId(ModelUtil.getSequence(SgConstants.SG_B_WMS_TO_STO_STOCK_OUT_RESULT));
                sgBWmsToStoStockOutResult1.setNoticesBillNo(noticesBillNo);
                sgBWmsToStoStockOutResult1.setWarehouseCode(warehouseCode);
                sgBWmsToStoStockOutResult1.setBillType(orderType);
                sgBWmsToStoStockOutResult1.setMessage(msg);
                sgBWmsToStoStockOutResult1.setOutBizCode(outBizCode);
                sgBWmsToStoStockOutResult1.setWmsBillCode(wmsBillCode);

                if (cpCPhyWarehouse != null) {
                    sgBWmsToStoStockOutResult1.setWmsWarehouseType(cpCPhyWarehouse.getWmsType());
                    /*京云仓特殊处理批次信息*/
                    if (ThirdWmsTypeEnum.JDWMS.getCode().equals(cpCPhyWarehouse.getWmsType())) {
                        JSONArray orderLines = request.getJSONArray("orderLines");
                        JSONArray newOrderLines = new JSONArray();
                        if (orderLines != null && orderLines.size() > 0) {
                            for (int i = 0; i < orderLines.size(); i++) {
                                JSONObject jsonObject = orderLines.getJSONObject(i);
                                JSONArray batchs = jsonObject.getJSONArray("batchs");
                                if (batchs != null && batchs.size() > 0) {
                                    for (int j = 0; j < batchs.size(); j++) {
                                        JSONObject newJsonObject = JSONObject.parseObject(jsonObject.toJSONString());

                                        JSONObject batch = batchs.getJSONObject(j);
                                        if (!StringUtils.isEmpty(batch.getString("productDate"))) {
                                            batch.put("batchCode",batch.getString("productDate"));
                                        }
                                        batch.keySet().forEach(x -> newJsonObject.put(x, batch.get(x)));
                                        newJsonObject.remove("batchs");
                                        newOrderLines.add(newJsonObject);
                                    }
                                }
                            }
                        }
                        request.put("orderLines", newOrderLines);
                        sgBWmsToStoStockOutResult1.setMessage(request.toJSONString());
                    }
                    /*富勒仓库编码取库存地点*/
                    if (ThirdWmsTypeEnum.FLWMS.getCode().equals(cpCPhyWarehouse.getWmsType())) {
                        JSONObject extendProps = request.getJSONObject("extendProps");
                        if (extendProps != null) {
                            String storageLocation = extendProps.getString("storageLocation");
                            if (StringUtils.isNotEmpty(storageLocation)) {
                                sgBWmsToStoStockOutResult1.setWarehouseCode(storageLocation);
                            }
                        }
                    }
                }

                sgBWmsToStoStockOutResult1.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT);
                sgBWmsToStoStockOutResult1.setFailedCount(NumberUtils.INTEGER_ZERO);
                sgBWmsToStoStockOutResult1.setIsactive(SgConstants.IS_ACTIVE_Y);
                StorageUtils.setBModelDefalutData(sgBWmsToStoStockOutResult1, R3SystemUserResource.getSystemRootUser());
                sgBWmsToStoStockOutResultMapper.insert(sgBWmsToStoStockOutResult1);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("B2B出库单WMS回传异常.异常信息：{}", "WmsBackStockoutConfirmRequest.apiProcess"),
                    Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        } finally {
            redisMasterTemplate.delete(lockKsy);
        }

        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("success"));
    }

}
