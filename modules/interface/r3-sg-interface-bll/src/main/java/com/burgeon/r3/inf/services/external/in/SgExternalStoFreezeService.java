package com.burgeon.r3.inf.services.external.in;

import com.alibaba.fastjson.JSON;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeSaveItemRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeSaveRequest;
import com.burgeon.r3.sg.store.services.freeze.SgBStoFreezeSaveAndSubmitService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/13 10:37
 * 外部调用-逻辑冻结单服务
 */
@Slf4j
@Component
public class SgExternalStoFreezeService {

    @Autowired
    private SgBStoFreezeSaveAndSubmitService saveAndSubmitService;

    /**
     * 逻辑冻结单保存
     */
    public ValueHolderV14<SgR3BaseResult> saveExternalFreeze(SgBStoFreezeBillSaveRequest request) {

        log.debug(LogUtil.format("SgExternalStoFreezeService.saveExternalFreeze.start",
                "SgExternalStoFreezeService.saveExternalFreeze.request",
                JSON.toJSONString(request)));


            ValueHolderV14<SgR3BaseResult> checkParams = checkParams(request);
            if (!checkParams.isOK()){
                return checkParams;
            }

            return saveAndSubmitService.saveAndSubmit(request);

    }

    /**
     * 参数检验
     */
    private ValueHolderV14<SgR3BaseResult> checkParams(SgBStoFreezeBillSaveRequest request) {
        SgBStoFreezeSaveRequest saveRequest = request.getFreezeSaveRequest();
        List<SgBStoFreezeSaveItemRequest> saveItemRequest = request.getSgBStoFreezeSaveItemRequests();
        if (Objects.isNull(saveRequest)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "创建逻辑冻结单主表信息不能为空!");
        }
        if (CollectionUtils.isEmpty(saveItemRequest)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "创建逻辑冻结单明细表信息不能为空!");
        }
        if (Objects.isNull(saveRequest.getCpCStoreId())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "创建逻辑冻结单逻辑仓信息不能为空!");
        }
        List<SgBStoFreezeSaveItemRequest> errorItemRequests =
                saveItemRequest.stream().filter(i -> StringUtils.isBlank(i.getProduceDate()) || StringUtils.isBlank(i.getStockType())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(errorItemRequests)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "当前逻辑冻结单明细,生产日期或库存类型不能为空!");
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "校验通过!");
    }
}
