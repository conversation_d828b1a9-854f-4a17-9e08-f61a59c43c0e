package com.burgeon.r3.inf.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.inf.services.wms.StoOutNoticesWmsReceiptService;
import com.burgeon.r3.sg.inf.common.SgMQConstants;
import com.burgeon.r3.sg.inf.model.request.wms.out.SgPhyOutNoticesBillWMSBackRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @time 2019/5/6 17:52
 * @descript WMS --> sg,oms 回执 (出库通知单->回执)
 */
@Slf4j
@Service(SgMQConstants.WMS_CALL_DELIVERY_ORDER_METHOD)
public class WMSCallDeliveryOrderHandler implements CallBackApi {

    @Autowired
    private StoOutNoticesWmsReceiptService service;

    @Override
    public ValueHolderV14 apiProcess(String messageBody) {
        ValueHolderV14 holder;
        try {
            List<SgPhyOutNoticesBillWMSBackRequest> requestList =
                    JSON.parseArray(JSONObject.parseObject(messageBody).getJSONArray("data").toJSONString(),
                            SgPhyOutNoticesBillWMSBackRequest.class);
            if (CollectionUtils.isEmpty(requestList) || requestList.get(0).getOrderNo() == null) {
                return new ValueHolderV14<>(ResultCode.FAIL, "解析data回传信息为空！");
            }
            holder = service.execWmsReceipt(requestList);
            if (!holder.isOK()) {
                log.error("WMS --> sg,oms 回执失败，failMessage:{}", holder.getMessage());
            }
        } catch (Exception e) {
            log.error("出库通知单WMS --> sg,oms 回执异常，error:{}", Throwables.getStackTraceAsString(e));
            throw e;
        }
        return holder;
    }
}
