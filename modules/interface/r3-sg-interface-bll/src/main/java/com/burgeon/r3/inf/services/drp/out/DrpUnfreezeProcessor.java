package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.model.table.store.freeze.SgBStoUnfreeze;
import com.burgeon.r3.sg.core.model.table.store.freeze.SgBStoUnfreezeItem;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.freeze.SgBStoFreezeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/6/24 13:35
 * 库存解冻申请
 */
@Slf4j
@Component
public class DrpUnfreezeProcessor extends AbstractDrpInterfaceProcessor<SgBStoUnfreeze, SgBStoUnfreezeItem> {

    @Autowired
    private SgBStoFreezeMapper mapper;

    @Override
    public LambdaQueryWrapper<SgBStoUnfreeze> execMainWrapper() {
        //已审核”且“传DRP状态”=“传失败”或“未传”的【逻辑解冻单
        LambdaQueryWrapper<SgBStoUnfreeze> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SgBStoUnfreeze::getStatus, SgStoreConstants.BILL_STATUS_SUBMIT);
        wrapper.and(o -> {
            o.isNull(SgBStoUnfreeze::getDrpStatus);
            o.or(oo -> oo.eq(SgBStoUnfreeze::getDrpStatus, SgStoreConstants.SEND_DRP_STATUS_FAIL).lt(SgBStoUnfreeze::getDrpFailCount, failNum));
            return o;
        });
        return wrapper;
    }

    @Override
    public LambdaQueryWrapper<SgBStoUnfreezeItem> execitemWrapper(Long mainId) {
        return null;
    }

    @Override
    public String drpInterfaceUrl() {
        return "erp.stockthaw.notice";
    }

    @Override
    public String itemMainField() {
        return "sg_b_sto_unfreeze_id";
    }

    @Override
    public String drpStatus() {
        return "DRP_STATUS";
    }

    @Override
    public String drpStatusFailCount() {
        return "DRP_FAIL_COUNT";
    }

    @Override
    public String drpStatusFailReason() {
        return "DRP_FAIL_REASON";
    }

    @Override
    public JSONObject execInterfaceParam(SgBStoUnfreeze sgBStoUnfreeze, List<SgBStoUnfreezeItem> itemList) {
        JSONObject object = new JSONObject();
        object.put("ZTDOCNO", sgBStoUnfreeze.getBillNo());
        object.put("DESCRIPTION", sgBStoUnfreeze.getRemark());
        JSONArray items = new JSONArray();
        for (SgBStoUnfreezeItem unfreezeItem : itemList) {
            JSONObject item = new JSONObject();
            item.put("M_PRODUCTALIAS_ID_NO", unfreezeItem.getPsCSkuEcode());
            item.put("QTY", unfreezeItem.getQty());
            item.put("C_STORECODE", sgBStoUnfreeze.getCpCStoreEcode());
            items.add(item);
        }
        object.put("items", items);
        log.info("DrpUnfreezeProcessor execInterfaceParam result ={}", JSONObject.toJSONString(object));
        return object;
    }

    @Override
    public void handleBysuccess(SgBStoUnfreeze sgBStoUnfreeze, List<SgBStoUnfreezeItem> z) {

    }
}
