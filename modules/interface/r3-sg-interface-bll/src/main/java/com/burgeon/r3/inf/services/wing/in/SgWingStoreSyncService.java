package com.burgeon.r3.inf.services.wing.in;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.store.customer.SgCCustomerStore;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.inf.model.request.wing.in.SgWingStoreSyncRequest;
import com.burgeon.r3.sg.store.mapper.customer.SgCCustomerStoreMapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @create 2021/10/29 10:54
 * 店仓档案同步接口
 */
@Slf4j
@Component
public class SgWingStoreSyncService {

    @Autowired
    private SgCCustomerStoreMapper mapper;

    @Autowired
    private CpCStoreMapper cpCStoreMapper;

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 storeSync(SgWingStoreSyncRequest request) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

        User loginUser = R3SystemUserResource.getSystemRootUser();

        String ecode = request.getEcode();
        String customerStote = request.getCustomerStote();
        if (StringUtils.isEmpty(ecode)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("店仓code不能为空");
            return v14;
        }

        if (StringUtils.isEmpty(customerStote)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("对应经销商店仓code不能为空");
            return v14;
        }

        SgCCustomerStore store = mapper.selectOne(new LambdaQueryWrapper<SgCCustomerStore>()
                .eq(SgCCustomerStore::getCpCStoreEcode, ecode)
                .eq(SgCCustomerStore::getIsactive, SgConstants.IS_ACTIVE_Y));

        SgCCustomerStore cCustomerStore = new SgCCustomerStore();
        if (store != null) {
            cCustomerStore.setId(store.getId());
            cCustomerStore.setCustomerStore(customerStote);
            StorageUtils.setBModelDefalutDataByUpdate(cCustomerStore, loginUser);
            mapper.updateById(cCustomerStore);
        } else {
            SgCpCStore sgCpCStore = cpCStoreMapper.selectOne(new LambdaQueryWrapper<SgCpCStore>()
                    .eq(SgCpCStore::getCpCStoreEcode, ecode));
            if (sgCpCStore == null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("店仓编码未查询到对应逻辑仓");
                return v14;
            }
            Long objId = ModelUtil.getSequence(SgConstants.SG_C_CUSTOMER_STORE);
            cCustomerStore.setId(objId);
            cCustomerStore.setCpCStoreId(sgCpCStore.getId());
            cCustomerStore.setCpCStoreEcode(sgCpCStore.getCpCStoreEcode());
            cCustomerStore.setCpCStoreEname(sgCpCStore.getCpCStoreEname());
            cCustomerStore.setCustomerStore(customerStote);
            StorageUtils.setBModelDefalutData(cCustomerStore, loginUser);
            mapper.insert(cCustomerStore);
        }

        return v14;
    }
}
