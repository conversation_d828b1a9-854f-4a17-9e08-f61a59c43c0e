package com.burgeon.r3.inf.mapper;

import com.burgeon.r3.sg.core.model.table.sap.SgBOmsToSapStorageCheck;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@Deprecated
public interface SgBOmsToSapStorageCheckMapper extends ExtentionMapper<SgBOmsToSapStorageCheck> {

    @Select("SELECT\n" +
            "\tid,\n" +
            "\tad_client_id,\n" +
            "\tad_org_id,\n" +
            "\tstorage_date,\n" +
            "\tcp_c_store_id,\n" +
            "\tcp_c_store_ecode,\n" +
            "\tcp_c_store_ename,\n" +
            "\tps_c_sku_id,\n" +
            "\tps_c_sku_ecode,\n" +
            "\tgbcode,\n" +
            "\tps_c_brand_id,\n" +
            "\tps_c_pro_id,\n" +
            "\tps_c_pro_ecode,\n" +
            "\tps_c_pro_ename,\n" +
            "\tps_c_spec1_id,\n" +
            "\tps_c_spec1_ecode,\n" +
            "\tps_c_spec1_ename,\n" +
            "\tps_c_spec2_id,\n" +
            "\tps_c_spec2_ecode,\n" +
            "\tps_c_spec2_ename,\n" +
            "\tps_c_spec3_id,\n" +
            "\tps_c_spec3_ecode,\n" +
            "\tps_c_spec3_ename,\n" +
            "\tstorage_type,\n" +
            "\tproduce_date,\n" +
            "\toms_stotage_qty,\n" +
            "\tsap_stotage_qty,\n" +
            "\tdiff_qty,\n" +
            "\tresult,\n" +
            "\tremark,\n" +
            "\tversion,\n" +
            "\tisactive,\n" +
            "\townerid,\n" +
            "\townerename,\n" +
            "\townername,\n" +
            "\tcreationdate,\n" +
            "\tmodifierid,\n" +
            "\tmodifierename,\n" +
            "\tmodifiername,\n" +
            "\tmodifieddate\n" +
            "FROM\n" +
            "\tsg_b_oms_to_sap_storage_check \n" +
            "WHERE\n" +
            "\tstorage_date = DATE_ADD( CURDATE( ), INTERVAL - 2 DAY ) \n" +
            "\tAND cp_c_store_id = #{phyId}")
    List<SgBOmsToSapStorageCheck> selectByTwoDay(@Param("phyId") Long phyId);
}