package com.burgeon.r3.inf.services.drp.out;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransfer;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransferItem;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.transfer.SgBStoTransferMapper;
import com.burgeon.r3.sg.store.services.transfer.SgTransferBillStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 调拨确认单通知接口
 * @version 1.0
 * @date 2021/7/27 9:59
 */

@Slf4j
@Component
public class DrpTransferConfirmProcessor extends AbstractDrpInterfaceProcessor<SgBStoTransfer, SgBStoTransferItem> {
    @Autowired
    SgBStoTransferMapper sgStoTransferMapper;

    @Override
    public LambdaQueryWrapper execMainWrapper() {
        LambdaQueryWrapper<SgBStoTransfer> wrapper = new LambdaQueryWrapper<>();
        //“DRP业务类型”=调拨流程、“单据状态”=“已确认未出库”，且“确认传DRP状态”=“传失败”或者 “未传”，的【逻辑调拨单】
        wrapper.ge(SgBStoTransfer::getStatus, SgTransferBillStatusEnum.AUDITED_NOT_OUT.getVal());
        wrapper.ne(SgBStoTransfer::getStatus, SgTransferBillStatusEnum.VOIDED.getVal());
        wrapper.eq(SgBStoTransfer::getDrpBillType, SgStoreConstants.DRP_BILL_TYPE_TF);
        wrapper.eq(SgBStoTransfer::getIsactive, SgConstants.IS_ACTIVE_Y);
        //2022-2-22 需求改为drp状态成功+确认传drp状态未传 只传中台单据
        List<String> drpStatus = new ArrayList<>();
        drpStatus.add(SgStoreConstants.SEND_DRP_STATUS_SUCCESS);
//        drpStatus.add(SgStoreConstants.SEND_DRP_STATUS_NOT_PASS);
        wrapper.in(SgBStoTransfer::getDrpStatus, drpStatus);
        wrapper.and(o -> {
            o.eq(SgBStoTransfer::getConfirmDrpStatus, SgStoreConstants.SEND_DRP_STATUS_UNDECLARED);
            o.or(oo -> oo.eq(SgBStoTransfer::getConfirmDrpStatus, SgStoreConstants.SEND_DRP_STATUS_FAIL).lt(SgBStoTransfer::getConfirmDrpFailCount, failNum));
            return o;
        });
        wrapper.orderByDesc(SgBStoTransfer::getId);
        return wrapper;
    }

    @Override
    public LambdaQueryWrapper<SgBStoTransferItem> execitemWrapper(Long mainId) {
        return null;
    }

    @Override
    public String drpInterfaceUrl() {
        return "erp.transfer.confirm";
    }

    @Override
    public String itemMainField() {
        return "sg_b_sto_transfer_id";
    }

    @Override
    public String drpStatus() {
        return "CONFIRM_DRP_STATUS";
    }

    @Override
    public String drpStatusFailCount() {
        return "CONFIRM_DRP_FAIL_COUNT";
    }

    @Override
    public String drpStatusFailReason() {
        return "CONFIRM_DRP_REASON";
    }

    @Override
    public JSONObject execInterfaceParam(SgBStoTransfer sgStoTransfer, List<SgBStoTransferItem> itemList) {
        JSONObject mian = new JSONObject();
        //中台单据编号
        mian.put("ZTDOCNO", sgStoTransfer.getBillNo());
        return mian;
    }

    @Override
    public void handleBysuccess(SgBStoTransfer sgBStoTransfer, List<SgBStoTransferItem> z) {

    }
}
