package com.burgeon.r3.sg.inf.controller.external.in;

import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.inf.api.external.in.SgExternalFreezeCmd;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeBillSaveRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/6/13 15:00
 */
@Slf4j
@RestController
@Api(value = "SgExternalStoFreezeController", description = "网关-逻辑冻结单接口")
public class SgExternalStoFreezeController {

    @DubboReference(group = "sg", version = "1.0")
    private SgExternalFreezeCmd sgExternalFreezeCmd;

    /**
     * 冻结单保存
     */
    @ApiOperation(value = "网关-逻辑冻结单创建并审核接口")
    @RequestMapping(path = "/api/cs/sg/ip/external/freeze/save", method = {RequestMethod.POST})
    public ValueHolderV14<SgR3BaseResult> freezeSave(@RequestBody SgBStoFreezeBillSaveRequest request) {
        ValueHolderV14<SgR3BaseResult> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "创建成功!");
        try {
            vh = sgExternalFreezeCmd.saveSgFreeze(request);
        } catch (Exception e) {
            log.error(LogUtil.format("SgExternalStoFreezeController.freezeSave.error",
                    "SgExternalStoFreezeController.freezeSave.exception",
                    Throwables.getStackTraceAsString(e)));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("创建逻辑冻结单异常,异常原因:" + e.getMessage());
        }
        return vh;
    }
}
