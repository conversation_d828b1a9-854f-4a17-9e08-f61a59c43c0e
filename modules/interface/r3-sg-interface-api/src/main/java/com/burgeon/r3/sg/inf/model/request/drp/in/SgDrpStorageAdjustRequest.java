package com.burgeon.r3.sg.inf.model.request.drp.in;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2021-06-25 09:33
 * @Description:
 */

@Data
@ApiModel("库存调整Model")
public class SgDrpStorageAdjustRequest implements Serializable {

    private static final long serialVersionUID = -1476786739820352549L;


    @ApiModelProperty(value = "单据编号", required = true)
    @JSONField(name = "BILL_NO")
    private String billNo;

    @ApiModelProperty(value = "来源单据类型（零售退货单：2，采购单 5，采购退货单  6,调拨单 7,销售单 3，库存调整单  9，盘亏单 120，领用单(归还)100，领用单(不归还)101）", required = true)
    @JSONField(name = "SOURCE_BILL_TYPE")
    private Integer sourceBillType;

    @ApiModelProperty(value = "来源单据编号", required = true)
    @JSONField(name = "SOURCE_BILL_NO")
    private String sourceBillNo;

    @ApiModelProperty(name = "来源单据ID")
    @JSONField(name = "SOURCE_BILL_ID")
    private Long sourceBillId;

    @ApiModelProperty(value = "逻辑仓编码", required = true)
    @JSONField(name = "CP_C_STORE_ECODE")
    private String cpCStoreEcode;

    @ApiModelProperty(value = "调成日期", required = true)
    @JSONField(name = "ADJUST_DATE")
    private Date AdjustDate;

    @ApiModelProperty(value = "明细", required = true)
    @JSONField(name = "ITEMREQUESTS")
    private List<SgDrpStorageAdjustItemRequest> itemRequests;
}

