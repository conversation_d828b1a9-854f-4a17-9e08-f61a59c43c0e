package com.burgeon.r3.sg.share.services.allocation;

import com.burgeon.r3.sg.share.api.allocation.SgBShareAllocationQueryMatrixCmd;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2021/7/30 10:22
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgBShareAllocationQueryMatrixCmdImpl extends CommandAdapter implements SgBShareAllocationQueryMatrixCmd {

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        SgBShareAllocationQueryMatrixServices bean = ApplicationContextHandle.getBean(SgBShareAllocationQueryMatrixServices.class);
        return bean.queryMatrix(session);
    }

}
