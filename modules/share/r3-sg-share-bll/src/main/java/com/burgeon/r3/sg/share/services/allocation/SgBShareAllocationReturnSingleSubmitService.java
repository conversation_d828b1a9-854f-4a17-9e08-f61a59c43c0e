package com.burgeon.r3.sg.share.services.allocation;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.basic.model.result.AbstractSgStorageOutStockResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageUpdateResult;
import com.burgeon.r3.sg.basic.services.SgBSaStorageQueryService;
import com.burgeon.r3.sg.basic.utils.BigDecimalUtils;
import com.burgeon.r3.sg.basic.utils.DingTalkUtil;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationReturn;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationReturnItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageLogUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationReturnItemMapper;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationReturnMapper;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnItemSingleSubmitRequest;
import com.burgeon.r3.sg.share.services.SgShareStorageService;
import com.burgeon.r3.sg.share.services.ryytndistribution.SgCDepartmentMonthDemandService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.enums.YesOrNoEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @description:
 * @author: hwy
 * @time: 2021/5/24 13:34
 */

@Component
@Slf4j
public class SgBShareAllocationReturnSingleSubmitService {

    @Autowired
    private SgBShareAllocationReturnMapper allocationReturnMapper;

    @Autowired
    private SgBShareAllocationReturnItemMapper itemMapper;

    @Autowired
    private SgBSaStorageQueryService sgBSaStorageQueryService;

    @Autowired
    private SgShareStorageService sgShareStorageService;

    @Autowired
    private SgCSaStoreMapper sgSaStoreMapper;


    /**
     * @param singleSubmitRequest:
     * @Description: 单个单据审核 与外部共享事务
     * @Author: hwy
     * @Date: 2021/5/24 17:14
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14
     **/
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgStorageUpdateResult> singelSubmitWhitTrans(SgBShareAllocationReturnItemSingleSubmitRequest singleSubmitRequest) {
        return singelSubmit(singleSubmitRequest);
    }


    /**
     * @param singleSubmitRequest:
     * @Description: 单个单据审核 新开事务 不被外部事务影响
     * @Author: hwy
     * @Date: 2021/5/24 17:14
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14
     **/
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public ValueHolderV14<SgStorageUpdateResult> singelSubmitWhitNewTrans(SgBShareAllocationReturnItemSingleSubmitRequest singleSubmitRequest) {
        return singelSubmit(singleSubmitRequest);
    }

    /**
     * @param singleSubmitRequest:
     * @Description: 单个单据审核 加入当前事务
     * @Author: hwy
     * @Date: 2021/5/24 17:14
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14
     **/
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgStorageUpdateResult> singelSubmitWhitNewTrans2(SgBShareAllocationReturnItemSingleSubmitRequest singleSubmitRequest) {
        return singelSubmit(singleSubmitRequest);
    }

    /**
     * @param singleSubmitRequest:
     * @Description: 单个单据审核
     * @Author: hwy
     * @Date: 2021/5/24 17:14
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14
     **/
    public ValueHolderV14<SgStorageUpdateResult> singelSubmit(SgBShareAllocationReturnItemSingleSubmitRequest singleSubmitRequest) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBShareAllocationReturnSingleSubmitService.singelSubmit param:{}", JSONObject.toJSONString(singleSubmitRequest));
        }
        ValueHolderV14 valueHolderV14 = new ValueHolderV14<>();
        User loginUser = singleSubmitRequest.getLoginUser() == null ? R3SystemUserResource.getSystemRootUser() : singleSubmitRequest.getLoginUser();
        Boolean isAuto = singleSubmitRequest.getIsAuto();
        Map<String, Object> map = checkParam(singleSubmitRequest, valueHolderV14);
        if (valueHolderV14.getCode() == ResultCode.FAIL) {
            log.warn("SgBShareAllocationReturnSingleSubmitService.singelSubmit 参数校验不通过:{}", valueHolderV14.getMessage());
            return valueHolderV14;
        }
        //User user = singleSubmitRequest.getLoginUser() == null ? R3SystemUserResource.getSystemRootUser() : singleSubmitRequest.getLoginUser();
        singleSubmitRequest.setLoginUser(loginUser);
        String lockKsy = SgConstants.SG_B_SHARE_ALLOCATION_RETURN + ":" + singleSubmitRequest.getId();
        SgRedisLockUtils.lock(lockKsy);
        try {

            SgBShareAllocationReturn selectMain = (SgBShareAllocationReturn) map.get(SgConstants.SG_B_SHARE_ALLOCATION_RETURN);
            List<SgBShareAllocationReturnItem> selectItems = (List<SgBShareAllocationReturnItem>) map.get(SgConstants.SG_B_SHARE_ALLOCATION_RETURN_ITEM);

            BigDecimal totQty = BigDecimal.ZERO;
            BigDecimal totAmt = BigDecimal.ZERO;
            //修改单据状态
            selectMain.setStatus(SgShareConstants.BILL_STATUS_SUBMIT);
            StorageUtils.setBModelDefalutDataByUpdate(selectMain, loginUser);
            selectMain.setStatusId(loginUser.getId());
            selectMain.setStatusEname(loginUser.getEname());
            selectMain.setStatusName(loginUser.getName());
            selectMain.setStatusTime(new Date());
            Integer totRowNum = selectItems.size();

            Map<String, SgBShareAllocationReturnItem> itemMap = new HashMap<>();
            for (SgBShareAllocationReturnItem item : selectItems) {
                totQty = totQty.add(item.getQty());
                totAmt = totAmt.add(item.getAmt());
                itemMap.put(item.getPsCSkuEcode(), item);
            }
            //变更库存
            ValueHolderV14<SgStorageUpdateResult> holderV14 = sgShareStorageService.sgBShareInReturnStorageChange(singleSubmitRequest, selectMain, selectItems);
            if (!holderV14.isOK()) {
                AssertUtils.logAndThrow("SgBShareAllocationReturnSingleSubmitService.singelSubmit 库存变更失败" + holderV14.getMessage());
            }
            //自动分货需要处理的逻辑
            if (isAuto) {
                List<Long> ids = new ArrayList<>();
                SgStorageUpdateResult data = holderV14.getData();
                if (data.getPreoutUpdateResult() == SgConstantsIF.PREOUT_RESULT_OUT_STOCK
                        && CollectionUtils.isNotEmpty(data.getOutStockItemList())) {
                    List<AbstractSgStorageOutStockResult> outStockItemList = data.getOutStockItemList();
                    totRowNum = totRowNum - outStockItemList.size();
                    for (AbstractSgStorageOutStockResult stockResult : outStockItemList) {
                        String psSkuEcode = stockResult.getPsCSkuEcode();
                        SgBShareAllocationReturnItem returnItem = itemMap.get(psSkuEcode);
                        totQty = totQty.subtract(returnItem.getQty());
                        totAmt = totAmt.subtract(returnItem.getAmt());
                        ids.add(returnItem.getId());
                    }
                }
                //清除缺货的明细
                if (CollectionUtils.isNotEmpty(ids)) {
                    itemMapper.deleteBatchIds(ids);
                }
            }

            selectMain.setTotRowNum(totRowNum);
            selectMain.setTotAmt(totAmt);
            selectMain.setTotQty(totQty);
            allocationReturnMapper.updateById(selectMain);

            boolean isReturnDemand = Optional.ofNullable(singleSubmitRequest.getIsReturnDemand())
                    .orElse(YesOrNoEnum.YES.getCode()).equals(YesOrNoEnum.YES.getCode());
            /*更新库存数据成功后，分货退需要变更二级部门月需求(操作失败不影响执行结果)*/
            try {
                SgCDepartmentMonthDemandService sgCDepartmentMonthDemandService = ApplicationContextHandle.getBean(SgCDepartmentMonthDemandService.class);
                sgCDepartmentMonthDemandService.modifyDemandByAllocationReturn(selectMain, selectItems, isReturnDemand, loginUser);
            } catch (Exception e) {
                log.error(LogUtil.format("分货退货单驱动的需求变更失败，请检查,异常：{}",
                        "SgBShareAllocationReturnSingleSubmitService.singelSubmit"), Throwables.getStackTraceAsString(e));
                DingTalkUtil.sendTextMsgWithContext("分货退货单驱动的需求变更失败，请检查：" + e.getMessage());
            }

            valueHolderV14.setData(holderV14.getData());
        } catch (Exception e) {
            AssertUtils.logAndThrowException("分货退货单审核异常", e, loginUser.getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
//        // 2021-10-22 丹丹哥要求添加 是否添加到平台库存手工增量
//        SgBShareAllocationReturnSingleSubmitService bean = ApplicationContextHandle.getBean(SgBShareAllocationReturnSingleSubmitService.class);
//        bean.asyncChannelStorageIncSync(map, loginUser);

        return valueHolderV14;
    }

    /**
     * @param singleSubmitRequest:
     * @param valueHolderV14:
     * @Description: 校验参数
     * @Author: hwy
     * @Date: 2021/5/24 15:58
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     **/
    private Map<String, Object> checkParam(SgBShareAllocationReturnItemSingleSubmitRequest singleSubmitRequest, ValueHolderV14 valueHolderV14) {
        Map<String, Object> resultMap = new HashMap<>();
        StringBuffer errorMessage = new StringBuffer();
        Boolean errorFlag = Boolean.FALSE;
        int errorCount = 0;
        if (singleSubmitRequest == null) {
            errorCount = StorageLogUtils.appendMsg(errorMessage, errorCount, "请求参数为空");
            errorFlag = true;
        }

        Long id = singleSubmitRequest.getId();
        if (id == null) {
            errorCount = StorageLogUtils.appendMsg(errorMessage, errorCount, "id为空");
            errorFlag = true;
        }
        SgBShareAllocationReturn sgBShareAllocationReturn = allocationReturnMapper.selectById(id);
        if (sgBShareAllocationReturn == null) {
            StorageLogUtils.appendMsg(errorMessage, errorCount, "当前记录已不存在");
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(errorMessage.toString());
            return null;
        }

        SgCSaStore sgSaStores = sgSaStoreMapper.selectById(sgBShareAllocationReturn.getSgCSaStoreId());
        AssertUtils.cannot(!SgConstants.IS_ACTIVE_Y.equals(sgSaStores.getIsactive()),
                "不可用的配销仓无法创建分货退货单:" + sgSaStores.getEcode());
        
        if (SgShareConstants.BILL_STATUS_VOID == sgBShareAllocationReturn.getStatus()) {
            errorCount = StorageLogUtils.appendMsg(errorMessage, errorCount, "当前记录已作废");
            errorFlag = true;
        }
        if (SgShareConstants.BILL_STATUS_UNSUBMIT != sgBShareAllocationReturn.getStatus()) {
            errorCount = StorageLogUtils.appendMsg(errorMessage, errorCount, "当前记录已审核,不允许重复审核");
            errorFlag = true;
        }

        List<SgBShareAllocationReturnItem> items =
                itemMapper.selectList(new QueryWrapper<SgBShareAllocationReturnItem>().lambda()
                        .eq(SgBShareAllocationReturnItem::getSgBShareAllocationReturnId, id)
                        .eq(SgBShareAllocationReturnItem::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isEmpty(items)) {
            errorCount = StorageLogUtils.appendMsg(errorMessage, errorCount, "当前记录无明细，不允许审核！");
            errorFlag = true;
        }
        for (SgBShareAllocationReturnItem item : ListUtils.emptyIfNull(items)) {
            if (BigDecimalUtils.nonPositiveInteger(item.getQty())) {
                log.warn(LogUtil.format("数量非法，sku：{},数量：{}",
                        "SgBShareAllocationReturnSingleSubmitService.checkParam"), item.getPsCSkuId(), item.getQty());
                throw new NDSException(item.getPsCSkuId() + "数量非法" + item.getQty());
            }
        }
        Boolean isAuto = singleSubmitRequest.getIsAuto();
        if (!isAuto) {
            //查询库存
//            List<Long> skuIds = items.stream().map(SgBShareAllocationReturnItem::getPsCSkuId).distinct().collect(Collectors.toList());
//            List<List<Long>> pageList = StorageUtils.getPageList(skuIds, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
//            List<SgBSaStorageQueryResult> storageList = new ArrayList<>();

//            for (List<Long> skuid : pageList) {
//                SgBSaStorageQueryRequest sgBSaStorageQueryRequest = new SgBSaStorageQueryRequest();
//                sgBSaStorageQueryRequest.setPsCSkuIds(skuid);
//                sgBSaStorageQueryRequest.setSgCSaStoreIds(Arrays.asList(sgBShareAllocationReturn.getSgCSaStoreId()));
//                ValueHolderV14<List<SgBSaStorageQueryResult>> queryStorageResult = sgBSaStorageQueryService.queryStorage(sgBSaStorageQueryRequest);
//                if (queryStorageResult.getCode() == ResultCode.FAIL) {
//                    errorCount = StorageLogUtils.appendMsg(errorMessage, errorCount, "查询库存失败:" + queryStorageResult.getMessage());
//                    errorFlag = true;
//                }
//                if (CollectionUtils.isNotEmpty(queryStorageResult.getData())) {
//                    storageList.addAll(queryStorageResult.getData());
//                }
//            }

//            Map<Long, SgBSaStorageQueryResult> storageMap = storageList.stream().collect(Collectors.toMap(SgBSaStorageQueryResult::getPsCSkuId, Function.identity()));

            List<Long> deleteIds = new ArrayList();
            Iterator<SgBShareAllocationReturnItem> iterator = items.iterator();

            while (iterator.hasNext()) {
                SgBShareAllocationReturnItem item = iterator.next();
                if (BigDecimal.ZERO.compareTo(item.getQty()) == 0) {
                    deleteIds.add(item.getId());
                    iterator.remove();
                    continue;
                }
                if (BigDecimal.ZERO.compareTo(item.getQty()) > 0) {
                    errorCount = StorageLogUtils.appendMsg(errorMessage, errorCount, "条码:" + item.getPsCSkuEcode() + "，数量小于0不允许审核");
                    errorFlag = true;
                }
//                if (!storageMap.containsKey(item.getPsCSkuId())) {
//                    errorCount = StorageLogUtils.appendMsg(errorMessage, errorCount, "条码:" + item.getPsCSkuEcode() + "，在SA仓:" + sgBShareAllocationReturn.getSgCSaStoreEcode() + " 中不存在库存记录");
//                    errorFlag = true;
//                }
//                else {
//                    SgBSaStorageQueryResult sgBSaStorageQueryResult = storageMap.get(item.getPsCSkuId());
//                    BigDecimal qtyAvailable = sgBSaStorageQueryResult.getQtyAvailable() == null ? BigDecimal.ZERO : sgBSaStorageQueryResult.getQtyAvailable();
//                    BigDecimal qty = item.getQty() == null ? BigDecimal.ZERO : item.getQty();
//                    if (qtyAvailable.compareTo(qty) < 0) {
//                        errorCount = StorageLogUtils.appendMsg(errorMessage, errorCount, "条码:" + item.getPsCSkuEcode() + "，数量 " + qty + " 大于可用库" + qtyAvailable + "不允许审核");
//                        errorFlag = true;
//                    }
//                }
            }
            if (CollectionUtils.isNotEmpty(deleteIds)) {
                itemMapper.deleteBatchIds(deleteIds);
            }
            if (errorFlag) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(errorMessage.toString());
            }
        }

        resultMap.put(SgConstants.SG_B_SHARE_ALLOCATION_RETURN, sgBShareAllocationReturn);
        resultMap.put(SgConstants.SG_B_SHARE_ALLOCATION_RETURN_ITEM, items);
        return resultMap;
    }

}