package com.burgeon.r3.sg.share.filter.transfer;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.mapper.matrix.GetMatrixMapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareSaTransfer;
import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareSaTransferItem;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareSaTransferItemMapper;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareSaTransferMapper;
import com.burgeon.r3.sg.share.model.dto.SgBShareSaTransferDto;
import com.burgeon.r3.sg.share.model.dto.SgBShareSaTransferItemDto;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleItemFilter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/6 20:46
 */
@Slf4j
@Component
public class SgBShareSaTransferSaveFilter extends BaseSingleItemFilter<SgBShareSaTransferDto, SgBShareSaTransferItemDto> {

    @Autowired
    private SgBShareSaTransferMapper mapper;
    @Autowired
    private SgBShareSaTransferItemMapper itemMapper;

    @Override
    public String getFilterMsgName() {
        return "配销仓调拨单保存";
    }

    @Override
    public Class<?> getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgBShareSaTransferDto mainObject, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("SgBShareSaTransferSaveFilter.execBeforeMainTable:{}", JSONObject.toJSONString(mainObject));
        }
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "配销仓调拨单保存");
        if (Objects.isNull(mainObject.getId()) || mainObject.getId() < 1L) {
            String billNo = SgStoreUtils.getBillNo(SgShareConstants.SEQ_SG_B_SHARE_SA_TRANSFER,
                    SgConstants.SG_B_SHARE_SA_TRANSFER, mainObject,
                    loginUser.getLocale());
            mainObject.setBillNo(billNo);
        }
        return v14;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgBShareSaTransferDto mainObject, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("SgBShareSaTransferSaveFilter.execAfterMainTable:{}", JSONObject.toJSONString(mainObject));
        }
        return null;
    }

    @Override
    public ValueHolderV14 execBeforeSubTable(SgBShareSaTransferDto mainObject, List<SgBShareSaTransferItemDto> subObjectList, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("SgBShareSaTransferSaveFilter.execBeforeSubTable:main={},sub={}", JSONObject.toJSONString(mainObject),
                    JSONObject.toJSONString(subObjectList));
        }

        for (SgBShareSaTransferItemDto subObj : subObjectList) {
            SgBShareSaTransferItem sgBShareSaTransferItem = null;

            if (Objects.nonNull(subObj.getId()) && subObj.getId() > 0L) {
                sgBShareSaTransferItem = itemMapper.selectById(subObj.getId());
            } else {
                LambdaQueryWrapper<SgBShareSaTransferItem> itemLqw = new LambdaQueryWrapper<SgBShareSaTransferItem>()
                        .eq(SgBShareSaTransferItem::getSgBShareSaTransferId, mainObject.getId())
                        .eq(SgBShareSaTransferItem::getPsCSkuId, subObj.getPsCSkuId());
                sgBShareSaTransferItem = itemMapper.selectOne(itemLqw);
            }

            if (Objects.nonNull(sgBShareSaTransferItem)) {
                //吊牌价=商品档案价格
                if (subObj.getId() < 0) {
                    BigDecimal addResult = subObj.getQty().add(sgBShareSaTransferItem.getQty());
                    subObj.setQty(addResult);
                }

                List<Long> skuIds = new ArrayList<>();
                skuIds.add(sgBShareSaTransferItem.getPsCSkuId());
                setPsCProSku(subObj, skuIds);

                subObj.setId(sgBShareSaTransferItem.getId());
            } else {
                List<Long> skuIds = new ArrayList<>();
                skuIds.add(subObj.getPsCSkuId());
                ValueHolderV14 v14 = setPsCProSku(subObj, skuIds);
                if (Objects.nonNull(v14)) {
                    return v14;
                }
            }
            BigDecimal amt = Objects.nonNull(subObj.getPriceList()) ? subObj.getPriceList().multiply(subObj.getQty()) : BigDecimal.ZERO;
            subObj.setAmt(amt);
        }
        return null;
    }

    private ValueHolderV14 setPsCProSku(SgBShareSaTransferItemDto subObj, List<Long> skuIds) {
        ValueHolderV14 v14 = null;
        BasicPsQueryService basicPsQueryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
        SkuInfoQueryRequest skuQuery = new SkuInfoQueryRequest();
        skuQuery.setSkuIdList(skuIds);
        HashMap<Long, PsCProSkuResult> skuInfo = basicPsQueryService.getSkuInfo(skuQuery);

        if (Objects.nonNull(skuInfo) && skuInfo.size() > 0) {
            PsCProSkuResult psCProSkuResult = skuInfo.get(skuIds.get(0));

            if (log.isDebugEnabled()) {
                log.debug("SgBShareSaTransferSaveFilter.setPsCProSku. request:{},result:{}",
                        JSONObject.toJSONString(skuQuery), JSONObject.toJSONString(psCProSkuResult));
            }

            if (SgConstants.IS_ACTIVE_N.equals(psCProSkuResult.getIsactive())) {
                String msg = Objects.nonNull(subObj.getPsCProEcode()) ?
                        "商品编码：[" + subObj.getPsCProEcode() + "]未启用！" :
                        "条码：[" + psCProSkuResult.getSkuEcode() + "]未启用!";
                v14 = new ValueHolderV14(ResultCode.FAIL, msg);
            }

            subObj.setPsCSkuEcode(psCProSkuResult.getSkuEcode());
            subObj.setPriceList(psCProSkuResult.getPricelist());
            subObj.setGbcode(psCProSkuResult.getGbcode());
            subObj.setPsCProId(psCProSkuResult.getPsCProId());
            subObj.setPsCProEname(psCProSkuResult.getPsCProEname());
            subObj.setPsCProEcode(psCProSkuResult.getPsCProEcode());
            subObj.setPsCSpec1Id(psCProSkuResult.getPsCSpec1objId());
            subObj.setPsCSpec1Ecode(psCProSkuResult.getClrsEcode());
            subObj.setPsCSpec1Ename(psCProSkuResult.getClrsEname());
            subObj.setPsCSpec2Id(psCProSkuResult.getPsCSpec2objId());
            subObj.setPsCSpec2Ecode(psCProSkuResult.getSizesEcode());
            subObj.setPsCSpec2Ename(psCProSkuResult.getSizesEname());
        } else {
            if (Objects.nonNull(subObj.getPsCProEcode())) {
                v14 = new ValueHolderV14(ResultCode.FAIL, "商品编码：[" + subObj.getPsCProEcode() + "]不存在！");
            } else {
                v14 = new ValueHolderV14(ResultCode.FAIL, "当前条码不存在商品信息，不允许保存！");
            }
        }
        return v14;
    }

    @Override
    public ValueHolderV14 execAfterSubTable(SgBShareSaTransferDto mainObject, List<SgBShareSaTransferItemDto> subObjectList, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("SgBShareSaTransferSaveFilter.execAfterSubTable:main={},sub={}", JSONObject.toJSONString(mainObject),
                    JSONObject.toJSONString(subObjectList));
        }
        if (mainObject.getId() > 0) {
            LambdaQueryWrapper<SgBShareSaTransferItem> itemLqw = new LambdaQueryWrapper<SgBShareSaTransferItem>()
                    .eq(SgBShareSaTransferItem::getSgBShareSaTransferId, mainObject.getId());
            List<SgBShareSaTransferItem> sgBShareSaTransferItems = itemMapper.selectList(itemLqw);
            BigDecimal reduce = sgBShareSaTransferItems.stream()
                    .filter(f -> Objects.nonNull(f.getQty()))
                    .map(SgBShareSaTransferItem::getQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            SgBShareSaTransfer sgBShareSaTransfer = new SgBShareSaTransfer();
            sgBShareSaTransfer.setId(mainObject.getId());
            sgBShareSaTransfer.setTotQty(reduce);
            sgBShareSaTransfer.setTotRowNum(sgBShareSaTransferItems.size());
            mapper.updateById(sgBShareSaTransfer);
        }
        return null;
    }
}
