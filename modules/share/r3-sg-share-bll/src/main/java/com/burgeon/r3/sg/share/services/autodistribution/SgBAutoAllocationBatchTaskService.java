package com.burgeon.r3.sg.share.services.autodistribution;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.basic.model.result.AbstractSgStorageOutStockResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageUpdateResult;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationItem;
import com.burgeon.r3.sg.core.model.table.share.autodistribution.SgBAutoAllocationBatch;
import com.burgeon.r3.sg.core.model.table.share.autodistribution.SgBAutoAllocationBatchItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.autodistribution.SgBAutoAllocationBatchItemMapper;
import com.burgeon.r3.sg.share.mapper.autodistribution.SgBAutoAllocationBatchMapper;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationBillSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnBillSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnItemSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnItemSingleSubmitRequest;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationSaveItemRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationSaveRequst;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationReturnSaveService;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationReturnSingleSubmitService;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationSaveService;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationSubmitService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/9/3 13:26
 */

@Slf4j
@Component
public class SgBAutoAllocationBatchTaskService {

    @Autowired
    private SgBAutoAllocationBatchMapper mapper;
    @Autowired
    private SgBAutoAllocationBatchItemMapper itemMapper;
    @Autowired
    private SgCSaStoreMapper sgCSaStoreMapper;

    //分货单   保存/修改
    @Autowired
    private SgBShareAllocationSaveService shareAllocationSaveService;
    @Autowired
    private SgBShareAllocationSubmitService shareAllocationSubmitService;

    //分货退货单 保存/审核
    @Autowired
    private SgBShareAllocationReturnSaveService shareAllocationReturnSaveService;

    @Autowired
    private SgBShareAllocationReturnSingleSubmitService shareAllocationReturnSubmitService;

    /**
     * 加强版自动分货
     * sg_b_auto_allocation_batch 获取最近时间的一个批次号
     * sg_b_auto_allocation_batch_item 批次号获取明细 生成(分货单(负数为分货退货单)/共享调整单)
     * <p>
     * 暂时忽略共享调整单
     *
     * @return
     */
    public ValueHolderV14 execute() {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "定时任务执行成功！");


        SgBAutoAllocationBatch batchNoByOrder = mapper.selBatchNoByOrder();
        if (batchNoByOrder == null) {
            v14.setMessage("暂无需要执行的自动分货任务");
            return v14;
        }

        User loginUser = R3SystemUserResource.getSystemRootUser();
        SgBAutoAllocationBatchTaskService bean = ApplicationContextHandle.getBean(SgBAutoAllocationBatchTaskService.class);

        String batchNo = batchNoByOrder.getBatchNo();

        Long objId = batchNoByOrder.getId();
        try {
            Integer count = itemMapper.selectCount(new LambdaQueryWrapper<SgBAutoAllocationBatchItem>().eq(SgBAutoAllocationBatchItem::getBatchNo, batchNo));
            Integer pageNum = count / SgShareConstants.AUTO_ALLOCATION_SELECT_COUNT;
            if (count % SgShareConstants.AUTO_ALLOCATION_SELECT_COUNT != 0) {
                pageNum++;
            }
            List<SgBAutoAllocationBatchItem> allBatchItems = new ArrayList<>();

            for (int i = 0; i <= pageNum; i++) {
                int start = i * SgShareConstants.AUTO_ALLOCATION_SELECT_COUNT;
                List<SgBAutoAllocationBatchItem> batchItems = itemMapper.selectListByPage(batchNo, start, SgShareConstants.AUTO_ALLOCATION_SELECT_COUNT);
                if (CollectionUtils.isNotEmpty(batchItems)) {
                    allBatchItems.addAll(batchItems);
                }
            }

//            List<SgBAutoAllocationBatchItem> batchItems = itemMapper.selectList(new LambdaQueryWrapper<SgBAutoAllocationBatchItem>()
//                    .select(SgBAutoAllocationBatchItem::getId, SgBAutoAllocationBatchItem::getPsCSkuEcode, SgBAutoAllocationBatchItem::getQty
//                            , SgBAutoAllocationBatchItem::getSgCSaStoreEcode, SgBAutoAllocationBatchItem::getSgCShareStoreEcode)
//                    .eq(SgBAutoAllocationBatchItem::getBatchNo, batchNo));
            if (CollectionUtils.isEmpty(allBatchItems)) {
                //对应批次号没有明细时 标记为成功
                bean.updBatchStatus(objId, SgShareConstants.BATCH_STATUS_SUCCESS, null);
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("批次号【" + batchNo + "】没有查询到对应明细");
                return v14;
            }
            log.info("Start SgBAutoAllocationBatchTaskService.execute batchNo {} batchItem.size {}", batchNo, allBatchItems.size());

            //更新状态为执行中
            bean.updBatchStatus(objId, SgShareConstants.BATCH_STATUS_EXECUTE, null);

            //查询配销仓(sa)信息
            List<String> saCodes = allBatchItems.stream().map(o -> o.getSgCSaStoreEcode()).distinct().collect(Collectors.toList());
            List<SgCSaStore> sgCSaStores = sgCSaStoreMapper.selectList(new LambdaQueryWrapper<SgCSaStore>()
                    .select(SgCSaStore::getId, SgCSaStore::getEcode, SgCSaStore::getSgCShareStoreId)
                    .in(SgCSaStore::getEcode, saCodes)
                    .eq(SgCSaStore::getIsactive, SgConstants.IS_ACTIVE_Y));
            Map<String, SgCSaStore> saMap = sgCSaStores.stream().collect(Collectors.toMap(o -> o.getEcode(), Function.identity()));

            //查询共享仓(sp)信息
            //HashMap<String,List<SgBAutoAllocationBatchItem>> spItemMap = new HashMap<>();

            //分货单信息map (key 聚合仓id  value 配销仓id/skuCode明细)
            HashMap<Long, List<SgBAutoAllocationBatchItem>> allocationItemMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            //分货退货单信息map
            HashMap<String, List<SgBAutoAllocationBatchItem>> allocationReturnItemMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

            //自动分货批次明细异常集合
            List<Long> errorIds = new ArrayList<>();

            for (SgBAutoAllocationBatchItem batchItem : allBatchItems) {
                Long id = batchItem.getId();
                String saEcode = batchItem.getSgCSaStoreEcode();

                BigDecimal qty = batchItem.getQty();
                if (StringUtils.isNotEmpty(saEcode)) {
                    if (!saMap.containsKey(saEcode)) {
                        errorIds.add(id);
                        continue;
                    }
                    SgCSaStore sgCSaStore = saMap.get(saEcode);
                    Long cShareStoreId = sgCSaStore.getSgCShareStoreId();
                    Long saId = sgCSaStore.getId();
                    //赋值 配销仓信息
                    batchItem.setSgCSaStoreId(saId);
                    batchItem.setSgCSaStoreEcode(sgCSaStore.getEcode());
                    batchItem.setSgCSaStoreEname(sgCSaStore.getEname());
                    //正数为分货单 负数为分货退货单
                    if (qty.compareTo(BigDecimal.ZERO) > 0) {
                        if (allocationItemMap.containsKey(cShareStoreId)) {
                            allocationItemMap.get(cShareStoreId).add(batchItem);
                        } else {
                            List<SgBAutoAllocationBatchItem> items = new ArrayList<>();
                            items.add(batchItem);
                            allocationItemMap.put(cShareStoreId, items);
                        }

                    } else {
                        String returnItemkey = cShareStoreId + "-" + saId;
                        if (allocationReturnItemMap.containsKey(returnItemkey)) {
                            allocationReturnItemMap.get(returnItemkey).add(batchItem);
                        } else {
                            List<SgBAutoAllocationBatchItem> items = new ArrayList<>();
                            items.add(batchItem);
                            allocationReturnItemMap.put(returnItemkey, items);
                        }
                    }

                } else {
                    //todo 暂时不管共享调整单
                }
            }

            //分货单
            List<SgBShareAllocationBillSaveRequst> saSaveRequsts = assemblyShareAllocationData(allocationItemMap, batchNo, loginUser);
            //分货退货单
            List<SgBShareAllocationReturnBillSaveRequst> saReturnSaveRequsts = assemblyShareAllocationReturnData(allocationReturnItemMap, batchNo, loginUser);

            log.info(" SgBAutoAllocationBatchTaskService assemblyData batchNo {} saSaveRequsts {} saReturnSaveRequsts {}",
                    batchNo, saSaveRequsts.size(), saReturnSaveRequsts.size());

            bean.autoShareAllocationAndShareAdjust(saSaveRequsts, allocationItemMap,
                    saReturnSaveRequsts, allocationReturnItemMap, errorIds, objId, batchNo, loginUser);
        } catch (Exception e) {
            //修改当前批次号主表
            bean.updBatchStatus(objId, SgShareConstants.BATCH_STATUS_FAIL, e.getMessage());
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }


    /**
     * 生成 (分货单or分货退货单)/共享调整单
     *
     * @param saSaveRequsts           分货单请求数据
     * @param allocationItemMap       根据聚合仓分组的 批次明细
     * @param saReturnSaveRequsts     分货单退货单请求数据
     * @param allocationReturnItemMap 根据聚合仓+配销仓分组的 批次明细
     * @param saSaveRequsts           共享调账单请求数据(暂时忽略)
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoShareAllocationAndShareAdjust(List<SgBShareAllocationBillSaveRequst> saSaveRequsts,
                                                  HashMap<Long, List<SgBAutoAllocationBatchItem>> allocationItemMap,
                                                  List<SgBShareAllocationReturnBillSaveRequst> saReturnSaveRequsts,
                                                  HashMap<String, List<SgBAutoAllocationBatchItem>> allocationReturnItemMap,
                                                  List<Long> errorIds, Long objId, String batchNo, User loginUser) {
        //redis流水key
        List<String> redisBillFtpKeyList = new ArrayList<>();
        try {
            SgBAutoAllocationBatchTaskService bean = ApplicationContextHandle.getBean(SgBAutoAllocationBatchTaskService.class);
            //单据明细存在缺货 标记
            Boolean isAllItemFail = false;
            //所有单据是否全部失败 如果是标记该批次号为运行失败
            Long isAllFail = 0L;
            //分货单
            if (CollectionUtils.isNotEmpty(saSaveRequsts)) {
                isAllFail = isAllFail + saSaveRequsts.size();
                for (SgBShareAllocationBillSaveRequst saveRequst : saSaveRequsts) {
                    SgBShareAllocationSaveRequst allocationSaveRequst = saveRequst.getSgBShareAllocationSaveRequst();
                    Long cShareStoreId = allocationSaveRequst.getSgCShareStoreId();
                    try {
                        //获取返回的错误明细  需要回写明细  流水返回
                        isAllItemFail = bean.insertSgShareAllocation(saveRequst, batchNo, allocationItemMap, errorIds, redisBillFtpKeyList);

                    } catch (Exception e) {
                        log.error("生成分货单异常 批次号 {} 聚合仓ID {} 错误信息 {}", batchNo, cShareStoreId,
                                Throwables.getStackTraceAsString(e));
                        //异常需要 当前明细全部标记失败
                        List<SgBAutoAllocationBatchItem> items = allocationItemMap.get(cShareStoreId);
                        //需要标记失败的明细id
                        List<Long> ids = items.stream().map(o -> o.getId()).collect(Collectors.toList());
                        errorIds.addAll(ids);
                        isAllFail--;
                    }
                }
            }
            //分货退货单
            if (CollectionUtils.isNotEmpty(saReturnSaveRequsts)) {
                isAllFail = isAllFail + saReturnSaveRequsts.size();
                for (SgBShareAllocationReturnBillSaveRequst saReturnSaveRequst : saReturnSaveRequsts) {
                    SgBShareAllocationReturnSaveRequst saveRequst = saReturnSaveRequst.getAllocationReturnSaveRequst();
                    Long cShareStoreId = saveRequst.getSgCShareStoreId();
                    Long saStoreId = saveRequst.getSgCSaStoreId();
                    try {
                        isAllItemFail = bean.insertSgShareAllocationReturn(saReturnSaveRequst, batchNo, allocationReturnItemMap, errorIds, redisBillFtpKeyList);

                    } catch (Exception e) {
                        log.error("生成分货单退货单异常 批次号 {} 聚合仓ID {} 错误信息 {}", batchNo, cShareStoreId,
                                Throwables.getStackTraceAsString(e));
                        //需要标记失败的明细id
                        String key = cShareStoreId + "-" + saStoreId;
                        List<SgBAutoAllocationBatchItem> items = allocationReturnItemMap.get(key);
                        List<Long> ids = items.stream().map(o -> o.getId()).collect(Collectors.toList());
                        errorIds.addAll(ids);
                        isAllFail--;
                    }
                }
            }

            //批次号明细执行状态 先置为成功
            SgBAutoAllocationBatchItem item = new SgBAutoAllocationBatchItem();
            item.setItemStatus(SgShareConstants.BATCH_ITEM_STATUS_SUCCESS);
            //是否全部失败
            boolean isAllflag = isAllFail.equals(0L);
            if (isAllflag) {
                //全部失败的情况 明细全标记失败
                item.setItemStatus(SgShareConstants.BATCH_ITEM_STATUS_FAIL);
            }
            itemMapper.update(item, new LambdaQueryWrapper<SgBAutoAllocationBatchItem>()
                    .eq(SgBAutoAllocationBatchItem::getBatchNo, batchNo));

            log.info("SgBAutoShareAllocationAndShareAdjust.updateBatchNoStatus batchNo:{} errorIds:{}",
                    batchNo, JSONObject.toJSONString(errorIds));

            //明细异常 回写明细执行状态
            if (CollectionUtils.isNotEmpty(errorIds) && !isAllflag) {
                item.setItemStatus(SgShareConstants.BATCH_ITEM_STATUS_FAIL);
                itemMapper.update(item, new LambdaQueryWrapper<SgBAutoAllocationBatchItem>()
                        .in(SgBAutoAllocationBatchItem::getId, errorIds));
            }
            //更新主表状态
            SgBAutoAllocationBatch batch = new SgBAutoAllocationBatch();
            batch.setId(objId);
            if (isAllItemFail) {
                batch.setBatchStatus(SgShareConstants.BATCH_STATUS_PART_SUCCESS);
            } else if (isAllflag) {
                batch.setBatchStatus(SgShareConstants.BATCH_STATUS_FAIL);
            } else {
                batch.setBatchStatus(SgShareConstants.BATCH_STATUS_SUCCESS);
            }
            mapper.updateById(batch);
        } catch (Exception e) {
            //异常库存回滚
            if (CollectionUtils.isNotEmpty(redisBillFtpKeyList)) {
                StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, loginUser);
            }
            AssertUtils.logAndThrow(e.getMessage(), loginUser.getLocale());
        }
    }

    //修改批次状态 单独事务
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updBatchStatus(Long objId, String batchStatus, String errorMsg) {
        SgBAutoAllocationBatch batch = new SgBAutoAllocationBatch();
        batch.setId(objId);
        batch.setBatchStatus(batchStatus);
        if (StringUtils.isNotEmpty(errorMsg)) {
            batch.setFailReason(errorMsg);
        }
        mapper.updateById(batch);
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Boolean insertSgShareAllocation(SgBShareAllocationBillSaveRequst saveRequst, String batchNo,
                                           HashMap<Long, List<SgBAutoAllocationBatchItem>> allocationItemMap,
                                           List<Long> errorIds,
                                           List<String> redisBillFtpKeyList) {

        SgBShareAllocationSaveRequst requst = saveRequst.getSgBShareAllocationSaveRequst();
        Long shareStoreId = requst.getSgCShareStoreId();
        //存在缺货明细 标记为部分成功
        Boolean isAllItemFail = false;
        // 新增并审核
        ValueHolderV14<SgR3BaseResult> save = shareAllocationSaveService.save(saveRequst);
        if (save.isOK()) {
            SgR3BaseResult data = save.getData();
            JSONObject dataJo = data.getDataJo();
            Long objid = dataJo.getLong("objid");
            SgR3BaseRequest request = new SgR3BaseRequest();
            request.setObjId(objid);
            request.setLoginUser(saveRequst.getLoginUser());
            ValueHolderV14<SgR3BaseResult> result = shareAllocationSubmitService.submitShareAllocation(request, true, false);

            log.info("insertSgShareAllocation.ValueHolderV14 batchNo:{} result:{}",
                    batchNo, JSONObject.toJSONString(result));

            SgR3BaseResult resultData = result.getData();
            JSONObject object = resultData.getDataJo();

            //redis流水key
            List<String> redisBillFtpKey = (List<String>) object.get("redisBillFtpKey");
            if (CollectionUtils.isNotEmpty(redisBillFtpKey)) {
                redisBillFtpKeyList.addAll(redisBillFtpKey);
            }
            //缺货的集合
            if (object.containsKey("errorList")) {
                List<SgBShareAllocationItem> errorList = (List<SgBShareAllocationItem>) object.get("errorList");
                List<SgBAutoAllocationBatchItem> items = allocationItemMap.get(shareStoreId);
                Map<String, Long> itemMap = items.stream().collect(Collectors.toMap(o -> o.getSgCSaStoreEcode() + "-" + o.getPsCSkuEcode(), o -> o.getId()));

                for (SgBShareAllocationItem allocationItem : errorList) {
                    String key = allocationItem.getSgCSaStoreEcode() + "-" + allocationItem.getPsCSkuEcode();
                    Long id = itemMap.get(key);
                    errorIds.add(id);
                }
                //存在缺货明细 标记为部分成功
                isAllItemFail = true;
            }

        }

        return isAllItemFail;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Boolean insertSgShareAllocationReturn(SgBShareAllocationReturnBillSaveRequst saReturnSaveRequst, String batchNo,
                                                 HashMap<String, List<SgBAutoAllocationBatchItem>> allocationReturnItemMap,
                                                 List<Long> errorIds,
                                                 List<String> redisBillFtpKeyList) {

        SgBShareAllocationReturnSaveRequst saveRequst = saReturnSaveRequst.getAllocationReturnSaveRequst();
        List<SgBShareAllocationReturnItemSaveRequst> itemSaveRequst = saReturnSaveRequst.getAllocationReturnItemSaveRequst();
        String sgSaStoreEcode = saveRequst.getSgCSaStoreEcode();

        Long shareStoreId = saveRequst.getSgCShareStoreId();
        Long saStoreId = saveRequst.getSgCSaStoreId();
        Boolean isAllItemFail = false;
        // 新增并审核
        ValueHolderV14<SgR3BaseResult> save = shareAllocationReturnSaveService.save(saReturnSaveRequst);
        if (save.isOK()) {
            SgR3BaseResult data = save.getData();
            JSONObject dataJo = data.getDataJo();
            Long objid = dataJo.getLong("objid");
            SgBShareAllocationReturnItemSingleSubmitRequest request = new SgBShareAllocationReturnItemSingleSubmitRequest();
            request.setId(objid);
            request.setLoginUser(saReturnSaveRequst.getLoginUser());
            request.setIsAuto(true);
            ValueHolderV14<SgStorageUpdateResult> result = shareAllocationReturnSubmitService.singelSubmitWhitTrans(request);

            log.info("insertSgShareAllocationReturn.ValueHolderV14 batchNo:{} result:{}",
                    batchNo, JSONObject.toJSONString(result));

            SgStorageUpdateResult updateResult = result.getData();
            if (updateResult.getPreoutUpdateResult() == SgConstantsIF.PREOUT_RESULT_OUT_STOCK &&
                    CollectionUtils.isNotEmpty(updateResult.getOutStockItemList())) {
                List<AbstractSgStorageOutStockResult> outStockItemList = updateResult.getOutStockItemList();

                if (outStockItemList.size() == itemSaveRequst.size()) {
                    throw new NDSException("所有明细数量都不足");
                }

                String key = shareStoreId + "-" + saStoreId;
                List<SgBAutoAllocationBatchItem> items = allocationReturnItemMap.get(key);
                Map<String, Long> itemMap = items.stream().collect(Collectors.toMap(o -> o.getPsCSkuEcode(), o -> o.getId()));
                for (AbstractSgStorageOutStockResult outStockResult : outStockItemList) {
                    String skuCode = outStockResult.getPsCSkuEcode();
                    Long id = itemMap.get(skuCode);
                    errorIds.add(id);
                }
                //存在缺货明细 标记为部分成功
                isAllItemFail = true;
            }

            List<String> redisBillFtpKey = updateResult.getRedisBillFtpKeyList();
            if (CollectionUtils.isNotEmpty(redisBillFtpKey)) {
                redisBillFtpKeyList.addAll(redisBillFtpKey);
            }

        }
        return isAllItemFail;
    }

    /**
     * 组装分货单请求数据
     *
     * @param saItemMap sa明细
     * @param batchNo   批次号
     * @param loginUser 用户
     */
    private List<SgBShareAllocationBillSaveRequst> assemblyShareAllocationData(HashMap<Long, List<SgBAutoAllocationBatchItem>> saItemMap,
                                                                               String batchNo, User loginUser) {
        List<SgBShareAllocationBillSaveRequst> saveRequsts = new ArrayList<>();
        for (Map.Entry<Long, List<SgBAutoAllocationBatchItem>> entry : saItemMap.entrySet()) {
            //聚合仓id
            Long shareId = entry.getKey();
            //sa明细信息
            List<SgBAutoAllocationBatchItem> saLists = entry.getValue();
            log.info(" SgBAutoAllocationBatchTaskService assemblyShareAllocationData batchNo {} entrykey {} entry.size {}",
                    batchNo, shareId, saLists.size());

            SgBShareAllocationBillSaveRequst billSaveRequst = new SgBShareAllocationBillSaveRequst();
            //主表
            SgBShareAllocationSaveRequst saveRequst = new SgBShareAllocationSaveRequst();
            saveRequst.setSgCShareStoreId(shareId);
            saveRequst.setBillDate(new Date());
            saveRequst.setRemark("自动配货生成-批次号:" + batchNo);
            //明细
            List<SgBShareAllocationSaveItemRequst> itemSaveRequsts = new ArrayList<>();

            for (SgBAutoAllocationBatchItem item : saLists) {
                SgBShareAllocationSaveItemRequst itemRequst = new SgBShareAllocationSaveItemRequst();
                BeanUtils.copyProperties(item, itemRequst);
                itemRequst.setId(-1L);
                itemRequst.setSgCSaStoreId(item.getSgCSaStoreId());
                itemRequst.setQty(item.getQty());
                itemRequst.setPsCSkuEcode(item.getPsCSkuEcode());
                itemSaveRequsts.add(itemRequst);
            }
            billSaveRequst.setLoginUser(loginUser);
            billSaveRequst.setObjId(-1L);
            billSaveRequst.setSgBShareAllocationSaveRequst(saveRequst);
            billSaveRequst.setSgBShareAllocationSaveItemRequsts(itemSaveRequsts);
            billSaveRequst.setR3(true);
            saveRequsts.add(billSaveRequst);
        }
        return saveRequsts;
    }

    /**
     * 组装分货退货单请求数据
     *
     * @param allocationOutItemMap
     * @param batchNo
     * @param loginUser
     */
    private List<SgBShareAllocationReturnBillSaveRequst> assemblyShareAllocationReturnData(HashMap<String, List<SgBAutoAllocationBatchItem>> allocationOutItemMap,
                                                                                           String batchNo, User loginUser) {


        List<SgBShareAllocationReturnBillSaveRequst> dataList = new ArrayList<>();
        for (Map.Entry<String, List<SgBAutoAllocationBatchItem>> entry : allocationOutItemMap.entrySet()) {
            String key = entry.getKey();
            //分割获取 聚合仓id
            String[] split = key.split("-");
            Long shareId = Long.parseLong(split[0]);
            Long saId = Long.parseLong(split[1]);

            List<SgBAutoAllocationBatchItem> items = entry.getValue();

            log.info(" SgBAutoAllocationBatchTaskService assemblyShareAllocationReturnData batchNo {} entrykey {} entry.size {}",
                    batchNo, key, items.size());

            SgBShareAllocationReturnBillSaveRequst billSaveRequst = new SgBShareAllocationReturnBillSaveRequst();

            SgBShareAllocationReturnSaveRequst saveRequst = new SgBShareAllocationReturnSaveRequst();
            saveRequst.setSgCShareStoreId(shareId);
            saveRequst.setSgCSaStoreId(saId);
            saveRequst.setBillDate(new Date());
            saveRequst.setRemark("自动配货生成-批次号:" + batchNo);

            List<SgBShareAllocationReturnItemSaveRequst> itemSaveRequsts = new ArrayList<>();

            for (SgBAutoAllocationBatchItem item : items) {
                SgBShareAllocationReturnItemSaveRequst itemSaveRequst = new SgBShareAllocationReturnItemSaveRequst();
                String psSkuEcode = item.getPsCSkuEcode();
                //数量取反
                BigDecimal negateQty = item.getQty().negate();
                itemSaveRequst.setId(-1L);
                itemSaveRequst.setQty(negateQty);
                itemSaveRequst.setPsCSkuEcode(psSkuEcode);
                itemSaveRequsts.add(itemSaveRequst);
            }
            billSaveRequst.setObjId(-1L);
            billSaveRequst.setLoginUser(loginUser);
            billSaveRequst.setAllocationReturnSaveRequst(saveRequst);
            billSaveRequst.setAllocationReturnItemSaveRequst(itemSaveRequsts);
            billSaveRequst.setR3(true);

            dataList.add(billSaveRequst);
        }

        return dataList;
    }
}
