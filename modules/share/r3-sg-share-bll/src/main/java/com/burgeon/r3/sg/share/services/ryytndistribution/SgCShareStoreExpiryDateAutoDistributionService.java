package com.burgeon.r3.sg.share.services.ryytndistribution;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.logic.SgStorageRedisQueryLogic;
import com.burgeon.r3.sg.basic.mapper.SgCShareStoreMapper;
import com.burgeon.r3.sg.basic.model.SgStorageRedisQuerySsModel;
import com.burgeon.r3.sg.basic.model.request.SgStoragePageQueryRequest;
import com.burgeon.r3.sg.basic.model.request.SgStoragePageRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageQueryRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageQuerySaRequest;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySaResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySsExtResult;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.SaCategoryEnum;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorage;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationBillSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnBillSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnItemSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationSaveItemRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationSaveRequst;
import com.burgeon.r3.sg.share.model.result.allocation.SgBShareAllocationReturnSaveAndSubmitResult;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationReturnSaveAndSubmitService;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationSaveService;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationSubmitService;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 大效期自动分货
 *
 * <AUTHOR>
 * @Date 2022/8/16
 * @Description 大效期自动分货
 */
@Slf4j
@Component
public class SgCShareStoreExpiryDateAutoDistributionService {

    @Resource
    private SgCShareStoreMapper sgShareStoreMapper;

    @Resource
    private SgStorageQueryService sgStorageQueryService;

    @Resource
    private SgBShareAllocationSaveService shareAllocationSaveService;

    @Resource
    private SgBShareAllocationReturnSaveAndSubmitService sgBShareAllocationReturnSaveAndSubmitService;

    @Resource
    private SgBShareAllocationSubmitService sgBShareAllocationSubmitService;

    @Resource
    private SgStorageRedisQueryLogic sgStorageRedisQueryLogic;

    public ValueHolderV14<Void> execute() {
        log.info(LogUtil.format("Start SgCShareStoreExpiryDateAutoDistributionService.execute"
                , "SgCShareStoreExpiryDateAutoDistributionService"));
        //查询所有可用且自动分货的聚合仓
        List<SgCShareStore> sgShareStores = queryShareStores();
        if (CollectionUtils.isEmpty(sgShareStores)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "可用且自动分货的聚合仓为空");
        }
        //逐个聚合仓处理
        for (SgCShareStore sgShareStore : sgShareStores) {
            try {
                handlerShareStore(sgShareStore);
            } catch (Exception e) {
                log.error(LogUtil.format("大效期自动分货失败，聚合仓信息：{},错误信息：{}"
                        , "大效期自动分货失败"), JSON.toJSON(sgShareStore), Throwables.getStackTraceAsString(e));
            }
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, "大效期自动分货 定时任务执行成功！");
    }

    /**
     * 按聚合仓进行大效期自动分货
     *
     * @param sgShareStore 聚合仓信息
     */
    private void handlerShareStore(SgCShareStore sgShareStore) {
        //根据聚合仓查询所有逻辑仓
        List<SgCpCStore> storeList = CommonCacheValUtils.getStoreByShareId(sgShareStore.getId());
        if (CollectionUtils.isEmpty(storeList)) {
            log.warn(LogUtil.format("根据聚合仓未查询到逻辑仓，聚合仓名称：{}"
                    , "根据聚合仓未查询到逻辑仓"), sgShareStore.getEname());
            return;
        }

        //根据逻辑仓信息查询出逻辑仓下所有sku的库存
        List<SgBStorage> sgBStorageList = querySgBStorage(storeList);
        if (CollectionUtils.isEmpty(sgBStorageList)) {
            log.warn(LogUtil.format("查询逻辑仓库存信息失败，聚合仓名称:{}"
                    , "查询逻辑仓库存信息失败"), sgShareStore.getEname());
            return;
        }
        /*根据聚合仓获取配销仓档案列表*/
        List<SgCSaStore> saStoreList = CommonCacheValUtils.getSaStoreList(sgShareStore.getId());
        if (CollectionUtils.isEmpty(saStoreList)) {
            log.warn(LogUtil.format("根据聚合仓未查询到配销仓，聚合仓名称：{}"
                    , "根据聚合仓未查询到配销仓"), sgShareStore.getEname());
            return;
        }
        //查询聚合仓下所有配销仓，按照大效期配销仓和非大效期配销仓组装两个map
        Map<String, List<SgCSaStore>> saStoreGroupByCategoryMap =
                saStoreList.stream().collect(Collectors.groupingBy(SgCSaStore::getCategory));
        //大效期配销仓
        List<SgCSaStore> saStoreExpiryList = saStoreGroupByCategoryMap.get(SaCategoryEnum.BIG_VALIDITY.getCode());
        if (CollectionUtils.isEmpty(saStoreExpiryList)) {
            log.warn(LogUtil.format("根据聚合仓未查询到大效期配销仓，聚合仓名称：{}"
                    , "根据聚合仓未查询到大效期配销仓"), sgShareStore.getEname());
            return;
        }
        if (saStoreExpiryList.size() > 1) {
            log.warn(LogUtil.format("根据聚合仓查询到多个大效期配销仓，聚合仓名称：{}"
                    , "根据聚合仓查询到多个大效期配销仓"), sgShareStore.getEname());
            return;
        }

        //非大效期配销仓
        List<SgCSaStore> saStoreNoExpiryList = saStoreGroupByCategoryMap.get(SaCategoryEnum.VIP_CATEGORY.getCode());
        if (CollectionUtils.isEmpty(saStoreNoExpiryList)) {
            log.warn(LogUtil.format("根据聚合仓未查询到非大效期配销仓，聚合仓名称：{}"
                    , "根据聚合仓未查询到非大效期配销仓"), sgShareStore.getEname());
            return;
        }

        // 所有skuId
        List<Long> skuList = sgBStorageList.stream()
                .map(SgBStorage::getPsCSkuId).distinct().collect(Collectors.toList());
        //所有逻辑仓下每个sku的大效期数量
        Map<Long, BigDecimal> storageExpirySkuNumMap = new HashMap<>(16);
        //所有逻辑仓下每个sku的非大效期数量
        Map<Long, BigDecimal> storageSkuNumMap = new HashMap<>(16);
        //根据逻辑仓库存信息获得所有大效期数量和非大效期数量
        expiryDateNum(skuList, sgBStorageList, storageExpirySkuNumMap, storageSkuNumMap);

        //查询聚合仓库存
        //查询所有聚合仓可用库存
        List<SgStorageRedisQuerySsModel> querySsModels = new ArrayList<>();
        for (Long skuId : skuList) {
            SgStorageRedisQuerySsModel model = new SgStorageRedisQuerySsModel();
            model.setSgCShareStoreId(sgShareStore.getId());
            model.setPsCSkuId(skuId);
            querySsModels.add(model);
        }
        ValueHolderV14<List<SgStorageRedisQuerySsExtResult>> v14 =
                sgStorageRedisQueryLogic.querySsStorageAvailableWithRedis(querySsModels, SystemUserResource.getRootUser());
        if (!v14.isOK() || CollectionUtils.isEmpty(v14.getData())) {
            log.warn(LogUtil.format("查询聚合仓可用库存失败，聚合仓名称：{}"
                    , "查询聚合仓可用库存失败"), sgShareStore.getEname());
            return;
        }
        Map<Long, BigDecimal> shareStoreNumMap = v14.getData().stream()
                .collect(Collectors.toMap(SgStorageRedisQuerySsExtResult::getPsCSkuId, SgStorageRedisQuerySsExtResult::getQtySsAvailable));


        //所有大效期配销仓下每个sku的数量
        Map<Long, BigDecimal> saStoreExpirySkuNumMap = new HashMap<>(16);
        //所有非大效期配销仓下每个sku的数量
        Map<Long, BigDecimal> saStoreSkuNumMap = new HashMap<>(16);
        //根据配销仓和sku集合查询所有配销仓库存信息，并根据配销仓类型分别统计库存数量，并根据sku分组
        Map<Long, List<SgStorageRedisQuerySaResult>> storeSkuNumBySaMap =
                querySaStore(saStoreList, saStoreExpirySkuNumMap, saStoreSkuNumMap, skuList);


        //执行分货
        this.allocation(sgShareStore, saStoreNoExpiryList, saStoreExpiryList, skuList, storageExpirySkuNumMap
                , storageSkuNumMap, saStoreExpirySkuNumMap, saStoreSkuNumMap, storeSkuNumBySaMap, shareStoreNumMap);
    }

    /**
     * 查询所有可用且自动分货的聚合仓
     *
     * @return sgShareStores
     */
    private List<SgCShareStore> queryShareStores() {
        return sgShareStoreMapper.selectList(new LambdaQueryWrapper<SgCShareStore>()
                .eq(SgCShareStore::getIsAutoAllocation, SgConstants.IS_ACTIVE_Y)
                .eq(SgCShareStore::getIsactive, SgConstants.IS_ACTIVE_Y));
    }

    /**
     * 根据配销仓和sku集合查询所有配销仓库存信息，并根据配销仓类型分别统计库存数量，并根据配销仓ID分组
     *
     * @param saStoreList            配销仓集合
     * @param saStoreExpirySkuNumMap 配销仓大效期数量
     * @param saStoreSkuNumMap       非大效期配销仓数量
     * @param skuList                sku集合
     * @return Map<Long, List < SgStorageRedisQuerySaResult>> 以sku分组配销仓库存
     */
    private Map<Long, List<SgStorageRedisQuerySaResult>> querySaStore(List<SgCSaStore> saStoreList,
                                                                      Map<Long, BigDecimal> saStoreExpirySkuNumMap,
                                                                      Map<Long, BigDecimal> saStoreSkuNumMap,
                                                                      List<Long> skuList) {
        //查询所有配销仓库存
        ValueHolderV14<List<SgStorageRedisQuerySaResult>> holderV14 = querySaStorageList(saStoreList, skuList);
        if (!holderV14.isOK()) {
            AssertUtils.logAndThrow("根据配销仓集合和sku集合查询配销仓库存信息失败！");
        }
        if (CollectionUtils.isEmpty(holderV14.getData())) {
            AssertUtils.logAndThrow("根据配销仓集合和sku集合查询配销仓库存信息为空！");
        }
        /*按sku分组*/
        List<SgStorageRedisQuerySaResult> querySaResultList = holderV14.getData();  //所有配销仓库存
        Map<Long, List<SgStorageRedisQuerySaResult>> storeSkuNumBySaMap = querySaResultList.stream()
                .sorted(Comparator.comparing(SgStorageRedisQuerySaResult::getQtyAvailable).reversed())
                .collect(Collectors.groupingBy(SgStorageRedisQuerySaResult::getPsCSkuId));

        //将配销仓档案和配销仓库存转成map,根据配销仓类型计算大效期配销仓和非大效期配销仓的每个sku库存
        Map<Long, SgCSaStore> saStoreMap =
                saStoreList.stream().collect(Collectors.toMap(SgCSaStore::getId, Function.identity())); //配销仓档案
        for (Long skuId : skuList) {
            //根据配销类型分别累加库存数量
            List<SgStorageRedisQuerySaResult> querySaResults = storeSkuNumBySaMap.get(skuId);
            if (CollectionUtils.isEmpty(querySaResults)) {
                continue;
            }
            BigDecimal num = saStoreSkuNumMap.getOrDefault(skuId, BigDecimal.ZERO);
            BigDecimal expiryNum = saStoreExpirySkuNumMap.getOrDefault(skuId, BigDecimal.ZERO);
            for (SgStorageRedisQuerySaResult querySaResult : querySaResults) {
                SgCSaStore saStore = saStoreMap.get(querySaResult.getSgCSaStoreId());
                if (SaCategoryEnum.VIP_CATEGORY.getCode().equals(saStore.getCategory())) {
                    num = num.add(querySaResult.getQtyAvailable());
                }
                if (SaCategoryEnum.BIG_VALIDITY.getCode().equals(saStore.getCategory())) {
                    expiryNum = expiryNum.add(querySaResult.getQtyAvailable());
                }
            }
            saStoreSkuNumMap.put(skuId, num);
            saStoreExpirySkuNumMap.put(skuId, expiryNum);
        }
        return storeSkuNumBySaMap;
    }

    /**
     * 根据配销仓和sku查询配销仓库存
     *
     * @param saStoreList 配销仓
     * @param skuList     sku
     * @return ValueHolderV14<List < SgStorageRedisQuerySaResult>> 配销仓库存信息
     */
    private ValueHolderV14<List<SgStorageRedisQuerySaResult>> querySaStorageList(List<SgCSaStore> saStoreList
            , List<Long> skuList) {
        List<Long> saStoreIds = saStoreList.stream().map(SgCSaStore::getId).collect(Collectors.toList());
        SgStorageQuerySaRequest querySaRequest = new SgStorageQuerySaRequest();
        querySaRequest.setSgCSaStoreIds(saStoreIds);
        querySaRequest.setSkuIds(skuList);
        return sgStorageQueryService.querySaStorageWithRedis(querySaRequest, R3SystemUserResource.getSystemRootUser());
    }

    /**
     * 根据逻辑仓集合查询所有sku和每个sku大效期逻辑仓汇总库存
     *
     * @param storeList 逻辑仓集合
     */
    private List<SgBStorage> querySgBStorage(List<SgCpCStore> storeList) {
        //所有逻辑仓库存
        List<SgBStorage> sgBStorageList = new ArrayList<>();

        //根据每个逻辑仓单独查询库存信息
        for (SgCpCStore sgCpCStore : storeList) {
            SgStoragePageQueryRequest sgStoragePageQueryRequest = new SgStoragePageQueryRequest();

            SgStorageQueryRequest sgStorageQueryRequest = new SgStorageQueryRequest();
            List<Long> storeIdList = new ArrayList<>();
            storeIdList.add(sgCpCStore.getId());
            sgStorageQueryRequest.setStoreIds(storeIdList);

            SgStoragePageRequest sgStoragePageRequest = new SgStoragePageRequest();
            sgStoragePageRequest.setPageNum(1);
            sgStoragePageRequest.setPageSize(SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);

            sgStoragePageQueryRequest.setPageRequest(sgStoragePageRequest);
            sgStoragePageQueryRequest.setQueryRequest(sgStorageQueryRequest);
            ValueHolderV14<PageInfo<SgBStorage>> pageInfoValueHolderV14 = sgStorageQueryService.queryStoragePage(sgStoragePageQueryRequest,
                    R3SystemUserResource.getSystemRootUser());
            AssertUtils.cannot(!pageInfoValueHolderV14.isOK(), "根据逻辑仓查逻辑仓库存异常：" + pageInfoValueHolderV14.getMessage());

            PageInfo<SgBStorage> dataInfo = pageInfoValueHolderV14.getData();
            if (dataInfo != null) {

                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dataInfo.getList())) {
                    sgBStorageList.addAll(dataInfo.getList());
                }

                //判断是否还有下一页
                if (dataInfo.isHasNextPage()) {
                    List<SgBStorage> pageStorage = getPageStorage(storeIdList, dataInfo);
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(pageStorage)) {
                        sgBStorageList.addAll(pageStorage);
                    }
                }
            }
        }

        return sgBStorageList;
    }

    /**
     * 根据每个sku比较逻辑仓和大效期配销仓的数量判断是拉回还是分货
     *
     * @param sgShareStore           聚合仓信息
     * @param saStoreNoExpiryList    非大效期配销仓集合
     * @param saStoreExpiryList      大效期配销仓集合
     * @param skuList                sku集合
     * @param storageExpirySkuNumMap 逻辑仓大效期数量[1115,13]
     * @param storageSkuNumMap       逻辑仓非大效期数量[1115,5]
     * @param saStoreExpirySkuNumMap 配销仓大效期数量[1115,0]
     * @param saStoreSkuNumMap       配销仓非大效期数量[1115,0]
     * @param storeSkuNumBySaList    配销仓库存信息，根据sku分组并根据配销仓可用数量排序[1115,可用0]
     * @param shareStoreNumMap
     */
    private void allocation(SgCShareStore sgShareStore, List<SgCSaStore> saStoreNoExpiryList,
                            List<SgCSaStore> saStoreExpiryList, List<Long> skuList,
                            Map<Long, BigDecimal> storageExpirySkuNumMap, Map<Long, BigDecimal> storageSkuNumMap,
                            Map<Long, BigDecimal> saStoreExpirySkuNumMap, Map<Long, BigDecimal> saStoreSkuNumMap,
                            Map<Long, List<SgStorageRedisQuerySaResult>> storeSkuNumBySaList, Map<Long, BigDecimal> shareStoreNumMap) {
        log.info(LogUtil.format("SgCShareStoreExpiryDateAutoDistributionService.execute.allocation" +
                        ",storageExpirySkuNumMap:{},storageSkuNumMap:{},saStoreExpirySkuNumMap:{}," +
                        "saStoreSkuNumMap:{},shareStoreNumMap:{}",
                "SgCShareStoreExpiryDateAutoDistributionService.execute.allocation")
                , JSON.toJSONString(storageExpirySkuNumMap), JSON.toJSONString(storageSkuNumMap)
                , JSON.toJSONString(saStoreExpirySkuNumMap), JSON.toJSONString(saStoreSkuNumMap)
                , JSON.toJSONString(shareStoreNumMap));

        SgCShareStoreExpiryDateAutoDistributionService bean =
                ApplicationContextHandle.getBean(SgCShareStoreExpiryDateAutoDistributionService.class);
        for (Long skuId : skuList) {
            try {
                //分货单参数
                List<SgBShareAllocationBillSaveRequst> saveRequsts = new ArrayList<>();
                //分货退货单参数
                List<SgBShareAllocationReturnBillSaveRequst> returnBillSaveRequsts = new ArrayList<>();

                //聚合仓可用数量
                BigDecimal shareSkuNum = shareStoreNumMap.getOrDefault(skuId, BigDecimal.ZERO);
                //逻辑仓大效期可用数量
                BigDecimal storageExpirySkuNum = storageExpirySkuNumMap.getOrDefault(skuId, BigDecimal.ZERO);
                //逻辑仓非大效期可用数量
                BigDecimal storageSkuNum = storageSkuNumMap.getOrDefault(skuId, BigDecimal.ZERO);
                //大效期配销仓可用数量
                BigDecimal saStoreExpirySkuNum = saStoreExpirySkuNumMap.getOrDefault(skuId, BigDecimal.ZERO);
                //非大效期配销仓可用数量
                BigDecimal saStoreSkuNum = saStoreSkuNumMap.getOrDefault(skuId, BigDecimal.ZERO);
                //当前sku每个配销仓的数量
                List<SgStorageRedisQuerySaResult> querySaResultList = storeSkuNumBySaList.get(skuId);
                //逐个sku执行分货
                allocateBySku(sgShareStore, saStoreNoExpiryList, saStoreExpiryList, storageExpirySkuNum,
                        storageSkuNum, saStoreExpirySkuNum, saStoreSkuNum, querySaResultList, saveRequsts,
                        returnBillSaveRequsts, skuId, shareSkuNum);
                //逻辑仓和配销仓数量平衡
                if (CollectionUtils.isEmpty(saveRequsts) && CollectionUtils.isEmpty(returnBillSaveRequsts)) {
                    continue;
                }
                //执行结果
                bean.executeResult(saveRequsts, returnBillSaveRequsts);
            } catch (Exception e) {
                log.error(LogUtil.format("SgCShareStoreExpiryDateAutoDistributionService.allocation,error,skuId:{},e:{}",
                        "SgCShareStoreExpiryDateAutoDistributionService.allocation,error")
                        , skuId, Throwables.getStackTraceAsString(e));
            }
        }
    }

    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRES_NEW)
    public void executeResult(List<SgBShareAllocationBillSaveRequst> saveRequsts, List<SgBShareAllocationReturnBillSaveRequst> returnBillSaveRequsts) {
        log.info(LogUtil.format("SgCShareStoreExpiryDateAutoDistributionService.executeResult,saveRequsts:{},returnBillSaveRequsts:{}"
                , "SgCShareStoreExpiryDateAutoDistributionService.executeResult"), JSON.toJSONString(saveRequsts), JSON.toJSONString(returnBillSaveRequsts));
        ValueHolderV14<SgBShareAllocationReturnSaveAndSubmitResult> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        try {
            if (!CollectionUtils.isEmpty(returnBillSaveRequsts)) {
                log.info(LogUtil.format("创建分货退货单，参数：{}", "创建分货退货单"), JSON.toJSON(returnBillSaveRequsts));
                try {
                    holderV14 = sgBShareAllocationReturnSaveAndSubmitService.saveAndSubmit2(returnBillSaveRequsts);
                } catch (Exception e) {
                    log.error(LogUtil.format("大效期分货调用分货退货单保存提交接口报错,参数：{},错误信息：{}",
                            "SgCShareStoreExpiryDateAutoDistributionService.executeReturnAndAllocation"),
                            JSON.toJSONString(returnBillSaveRequsts), Throwables.getStackTraceAsString(e));
                    //                StorageBasicUtils.rollbackStorage(result.getRedisBillFtpKeyList(), SystemUserResource.getRootUser());
                    AssertUtils.logAndThrow(e.getMessage());
                }
            }
            if (!holderV14.isOK()) {
                AssertUtils.logAndThrow(holderV14.getMessage());
            }
            if (!CollectionUtils.isEmpty(saveRequsts)) {
                for (SgBShareAllocationBillSaveRequst saveRequst : saveRequsts) {
                    try {
                        v14 = insertSgShareAllocation(saveRequst);
                        if (!v14.isOK()) {
                            throw new NDSException(v14.getMessage());
                        }
                    } catch (Exception e) {
                        log.error(LogUtil.format("SgCShareStoreExpiryDateAutoDistributionService. error:{}",
                                "SgCShareStoreExpiryDateAutoDistributionService.")
                                , Throwables.getStackTraceAsString(e));
                        AssertUtils.logAndThrow(e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            List<String> redisFtpKeys = new ArrayList<>();
            if (holderV14.getData() != null && !CollectionUtils.isEmpty(holderV14.getData().getRedisFtpKeys())) {
                redisFtpKeys.addAll(holderV14.getData().getRedisFtpKeys());
            }
            if (v14.getData() != null && v14.getData().getDataJo() != null) {
                JSONArray ftpKeyList = v14.getData().getDataJo().getJSONArray("redisBillFtpKey");
                List<String> redisBillFtpKey = JSON.parseArray(ftpKeyList.toJSONString(), String.class);
                if (!CollectionUtils.isEmpty(redisBillFtpKey)) {
                    redisFtpKeys.addAll(redisBillFtpKey);
                }
            }
            if (!CollectionUtils.isEmpty(redisFtpKeys)) {
                StorageBasicUtils.rollbackStorage(redisFtpKeys, SystemUserResource.getRootUser());
            }
            throw e;
        }
    }

    /**
     * 以sku纬度计算分货
     *
     * @param sgShareStore           集合仓信息
     * @param saStoreNoExpiryList    非大效期配销仓
     * @param saStoreExpiryList      大效期配销仓
     * @param storageExpirySkuNum    逻辑仓大效期数量
     * @param storageSkuNum          逻辑仓非大效期数量
     * @param saStoreExpirySkuNum    配销仓大效期数量
     * @param saStoreSkuNum          配销仓非大效期数量
     * @param querySaResultList      当前sku每个配销仓的数量
     * @param saveRequests           分货单请求参数
     * @param returnBillSaveRequests 分货退货单请求参数
     * @param skuId                  skuId
     * @param shareSkuNum
     */
    private void allocateBySku(SgCShareStore sgShareStore, List<SgCSaStore> saStoreNoExpiryList,
                               List<SgCSaStore> saStoreExpiryList, BigDecimal storageExpirySkuNum,
                               BigDecimal storageSkuNum, BigDecimal saStoreExpirySkuNum,
                               BigDecimal saStoreSkuNum, List<SgStorageRedisQuerySaResult> querySaResultList,
                               List<SgBShareAllocationBillSaveRequst> saveRequests,
                               List<SgBShareAllocationReturnBillSaveRequst> returnBillSaveRequests,
                               Long skuId, BigDecimal shareSkuNum) {
//
//        log.info(LogUtil.format("SgCShareStoreExpiryDateAutoDistributionService.allocateBySku" +
//                        ",skuId:{},storageExpirySkuNum:{},storageSkuNum:{},saStoreExpirySkuNum:{},saStoreSkuNum:{},shareSkuNum:{}",
//                "SgCShareStoreExpiryDateAutoDistributionService.allocateBySku"+skuId),
//                skuId,storageExpirySkuNum,storageSkuNum,saStoreExpirySkuNum,saStoreSkuNum,shareSkuNum);
        //大效期配销仓数量等于逻辑仓大效期数量，无需处理
        if (saStoreExpirySkuNum.compareTo(storageExpirySkuNum) == 0) {
            return;
        }

        //大效期配销仓数量（x）大于逻辑仓大效期数量(y)，直接创建分货退货单并提交
        if (saStoreExpirySkuNum.compareTo(storageExpirySkuNum) > 0) {
            //珂珂prd中的特殊场景二：大效期配销仓(x)大于逻辑仓配销仓(y)且x>y,则不做任何处理
            if (saStoreExpirySkuNum.compareTo(BigDecimal.ZERO) < 0
                    && storageExpirySkuNum.compareTo(BigDecimal.ZERO) < 0) {
                return;
            }
            //需要从大效期配销仓退回的数量（x-y）
            BigDecimal returnNum = saStoreExpirySkuNum.subtract(storageExpirySkuNum);
            //珂珂prd中的特殊场景一：大效期配销仓(x)大于逻辑仓配销仓(y)且y<0,则退回数量min(x,x-y)
            if (storageExpirySkuNum.compareTo(BigDecimal.ZERO) < 0 && saStoreExpirySkuNum.compareTo(returnNum) < 0) {
                returnNum = saStoreExpirySkuNum;
            }
            //退回数量大于0才构建参数
            if (returnNum.compareTo(BigDecimal.ZERO) > 0) {
                SgBShareAllocationReturnBillSaveRequst requst = buildReturnParam(saStoreExpiryList.get(0).getId(),
                        sgShareStore.getId(), skuId, returnNum);
                returnBillSaveRequests.add(requst);
            }
            return;
        }

        //大效期配销仓数量（x）小于逻辑仓大效期数量(y)，需要分货，会有两种情况，一是聚合仓可用数量够分，二是不够分
        executeAllocation(sgShareStore, saStoreNoExpiryList, saStoreExpiryList, storageExpirySkuNum, saStoreExpirySkuNum
                , saStoreSkuNum, querySaResultList, saveRequests, returnBillSaveRequests, skuId, shareSkuNum);
    }

    /**
     * 大效期配销仓数量小于逻辑仓大效期数量，需要分货
     *
     * @param sgShareStore           集合仓信息
     * @param saStoreNoExpiryList    非大效期配销仓
     * @param saStoreExpiryList      大效期配销仓
     * @param storageExpirySkuNum    逻辑仓大效期数量
     * @param saStoreExpirySkuNum    配销仓大效期数量
     * @param saStoreSkuNum          配销仓非大效期数量
     * @param querySaResultList      当前sku每个配销仓的数量
     * @param saveRequests           分货单请求参数
     * @param returnBillSaveRequests
     * @param skuId                  skuId
     * @param shareSkuNum            聚合仓数量
     */
    private void executeAllocation(SgCShareStore sgShareStore, List<SgCSaStore> saStoreNoExpiryList,
                                   List<SgCSaStore> saStoreExpiryList, BigDecimal storageExpirySkuNum,
                                   BigDecimal saStoreExpirySkuNum, BigDecimal saStoreSkuNum,
                                   List<SgStorageRedisQuerySaResult> querySaResultList,
                                   List<SgBShareAllocationBillSaveRequst> saveRequests,
                                   List<SgBShareAllocationReturnBillSaveRequst> returnBillSaveRequests,
                                   Long skuId, BigDecimal shareSkuNum) {
        //根据珂珂prd逻辑仓大效期数量（y）小于0，则不做任何处理
        if (storageExpirySkuNum.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        //需要分到大效期配销仓的数量
        BigDecimal num = storageExpirySkuNum.subtract(saStoreExpirySkuNum);

        if (shareSkuNum.compareTo(num) >= 0) {
            //当聚合仓的可用数量够分则直接创建分货单并提交
            SgBShareAllocationBillSaveRequst requst = buildParam(sgShareStore.getId(),
                    saStoreExpiryList.get(0), skuId, num, null);
            saveRequests.add(requst);
            return;
        }

        executeReturnAndAllocation(sgShareStore, saStoreNoExpiryList, saStoreExpiryList, saStoreSkuNum,
                querySaResultList, saveRequests, returnBillSaveRequests, skuId, num, shareSkuNum);
    }

    /**
     * 聚合仓可用数量不够分需要从非大效期配销仓拉回，会出现两种情况，一是非大效期足够拉回数量，二是不足够拉回数量
     *
     * @param sgShareStore           集合仓信息
     * @param saStoreNoExpiryList    非大效期配销仓
     * @param saStoreExpiryList      大效期配销仓
     * @param saStoreSkuNum          大效期配销仓
     * @param querySaResultList      当前sku每个配销仓的数量
     * @param saveRequsts            分货单请求参数
     * @param returnBillSaveRequests
     * @param skuId                  skuId
     * @param num                    分货数量
     * @param shareSkuNum            聚合仓可用
     */
    private void executeReturnAndAllocation(SgCShareStore sgShareStore, List<SgCSaStore> saStoreNoExpiryList,
                                            List<SgCSaStore> saStoreExpiryList, BigDecimal saStoreSkuNum,
                                            List<SgStorageRedisQuerySaResult> querySaResultList,
                                            List<SgBShareAllocationBillSaveRequst> saveRequsts,
                                            List<SgBShareAllocationReturnBillSaveRequst> returnBillSaveRequests,
                                            Long skuId, BigDecimal num, BigDecimal shareSkuNum) {
        //需要从非大效期配销仓退回的数量(N)
        BigDecimal returnNum = num.subtract(shareSkuNum);
        //获取非大效期配销仓id
        List<Long> saStoreNoExpiryIdList = saStoreNoExpiryList.stream()
                .map(SgCSaStore::getId).collect(Collectors.toList());
        //统计配销仓已经退回的数量
        BigDecimal totalNum = BigDecimal.ZERO;

        //非大效期配销仓数量够退回数量，按比例回退后创建分货单,但是得过滤大效期配销仓
        if (saStoreSkuNum.compareTo(returnNum) > 0) {
            for (SgStorageRedisQuerySaResult sgStorageRedisQuerySaResult : querySaResultList) {
                //每个配销仓应该退回的数量
                BigDecimal saReturnNum;
                if (saStoreNoExpiryIdList.contains(sgStorageRedisQuerySaResult.getSgCSaStoreId())) {
                    if (totalNum.compareTo(returnNum) < 0) {
                        //配销仓可用库存数量
                        BigDecimal qtyAvailable = sgStorageRedisQuerySaResult.getQtyAvailable();
                        saReturnNum = (returnNum.multiply(qtyAvailable))
                                .divide(saStoreSkuNum, 0, BigDecimal.ROUND_UP);
                        if (saReturnNum.compareTo(returnNum.subtract(totalNum)) > 0) {
                            saReturnNum = returnNum.subtract(totalNum);
                        }
                        totalNum = totalNum.add(saReturnNum);
                    } else {
                        saReturnNum = BigDecimal.ZERO;
                    }
                    if (saReturnNum.compareTo(BigDecimal.ZERO) > 0) {
                        SgBShareAllocationReturnBillSaveRequst returnRequest =
                                buildReturnParam(sgStorageRedisQuerySaResult.getSgCSaStoreId(),
                                        sgShareStore.getId(), skuId, saReturnNum);
                        returnBillSaveRequests.add(returnRequest);
                    }
                }
            }
            SgBShareAllocationBillSaveRequst requst = buildParam(sgShareStore.getId(),
                    saStoreExpiryList.get(0), skuId, num, null);
            saveRequsts.add(requst);
            return;
        }
        //非大效期配销仓数量不够退回数量或则刚好够，需全部退回后创建分货单
        for (SgStorageRedisQuerySaResult sgStorageRedisQuerySaResult : querySaResultList) {
            if (saStoreNoExpiryIdList.contains(sgStorageRedisQuerySaResult.getSgCSaStoreId())
                    && sgStorageRedisQuerySaResult.getQtyAvailable().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal qtyAvailable = sgStorageRedisQuerySaResult.getQtyAvailable();
                totalNum = totalNum.add(qtyAvailable);
                SgBShareAllocationReturnBillSaveRequst returnRequest =
                        buildReturnParam(sgStorageRedisQuerySaResult.getSgCSaStoreId()
                                , sgShareStore.getId(), skuId, qtyAvailable);
                returnBillSaveRequests.add(returnRequest);
            }
        }
        //实际分货数量
        BigDecimal allocationNum = totalNum.add(shareSkuNum);
        String remark = null;
        if (saStoreSkuNum.compareTo(returnNum) < 0) {
            remark = "应分货到大效期配销仓" + num + "，因聚合仓可用数量(" + shareSkuNum + ")和非大效期配销仓可用数量(" + totalNum + ")之和不足，只分货了" + allocationNum;
        }
        if (allocationNum.compareTo(BigDecimal.ZERO) > 0) {
            SgBShareAllocationBillSaveRequst requst = buildParam(sgShareStore.getId(),
                    saStoreExpiryList.get(0), skuId, allocationNum, remark);
            saveRequsts.add(requst);
        }
    }

    /**
     * 新增并审核 分货单
     *
     * @param saveRequst 分货单
     */
    public ValueHolderV14<SgR3BaseResult> insertSgShareAllocation(SgBShareAllocationBillSaveRequst saveRequst) {
        // 新增并审核
        ValueHolderV14<SgR3BaseResult> holderV14 = shareAllocationSaveService.save(saveRequst);
        if (holderV14.isOK()) {
            SgR3BaseResult data = holderV14.getData();
            JSONObject dataJo = data.getDataJo();
            Long objid = dataJo.getLong("objid");
            SgR3BaseRequest request = new SgR3BaseRequest();
            request.setObjId(objid);
            request.setLoginUser(saveRequst.getLoginUser());

            try {
                holderV14 = sgBShareAllocationSubmitService.submitShareAllocation(request, false, false);
            } catch (Exception e) {
                log.error(LogUtil.format("大效期分货调用分货单保存审核接口报错,参数：{},错误信息：{}", "SgCShareStoreExpiryDateAutoDistributionService.insertSgShareAllocation"),
                        JSON.toJSONString(request), Throwables.getStackTraceAsString(e));
                throw e;
            }
        }
        return holderV14;
    }

    /**
     * 构建分货单参数
     *
     * @param shareStoreId 聚合仓ID
     * @param sgCSaStore   配销仓ID
     * @param skuId        skuID
     * @param num          退回数量
     * @param remark       当从非配销仓退回数量不够分货时，添加备注
     * @return SgBShareAllocationBillSaveRequst 分货单请求参数
     */
    private SgBShareAllocationBillSaveRequst buildParam(Long shareStoreId, SgCSaStore sgCSaStore, Long skuId, BigDecimal num, String remark) {

        SgBShareAllocationBillSaveRequst requst = new SgBShareAllocationBillSaveRequst();
        //主表参数
        SgBShareAllocationSaveRequst saveRequst = new SgBShareAllocationSaveRequst();
        saveRequst.setBillDate(new Date());
        saveRequst.setSgCShareStoreId(shareStoreId);
        saveRequst.setRemark("由大效期自动分货任务自动生成！" + (StringUtils.isEmpty(remark) ? "" : remark));
        //明细参数
        List<SgBShareAllocationSaveItemRequst> saveItemRequsts = new ArrayList<>();
//        //测试环节，如果条码信息没有，就过

        SgBShareAllocationSaveItemRequst itemRequst = new SgBShareAllocationSaveItemRequst();
        try {
            //补全商品信息
            CommonCacheValUtils.setSkuInfo(skuId, null, itemRequst);
        } catch (Exception e) {
            log.error(LogUtil.format("SgCShareStoreByAutoTypeService.setSgBShareAllocation error:{},skuid:{}",
                    "SgCShareStoreExpiryDateAutoDistributionService.buildParam"),
                    Throwables.getStackTraceAsString(e), skuId);
        }
        itemRequst.setQty(num);
        itemRequst.setQtyDiff(BigDecimal.ZERO);
        itemRequst.setQtyIn(BigDecimal.ZERO);
        itemRequst.setQtyOut(BigDecimal.ZERO);
        itemRequst.setPsCSkuId(skuId);
        itemRequst.setSgCSaStoreId(sgCSaStore.getId());
        itemRequst.setSgCSaStoreEcode(sgCSaStore.getEcode());
        itemRequst.setSgCSaStoreEname(sgCSaStore.getEname());
        saveItemRequsts.add(itemRequst);


        requst.setObjId(-1L);
        requst.setLoginUser(R3SystemUserResource.getSystemRootUser());
        requst.setSgBShareAllocationSaveRequst(saveRequst);
        requst.setSgBShareAllocationSaveItemRequsts(saveItemRequsts);
        return requst;
    }

    /**
     * 构建分货退货单参数
     *
     * @param saStoreId    配销仓ID
     * @param ShareStoreId 聚合仓ID
     * @param skuId        skuID
     * @param num          分货数量
     * @return 分货退货单参数
     */
    private SgBShareAllocationReturnBillSaveRequst buildReturnParam(Long saStoreId, Long ShareStoreId, Long skuId, BigDecimal num) {
        //大效期配销仓数量大于逻辑仓数量，创建并提交分货退货单
        SgBShareAllocationReturnBillSaveRequst request = new SgBShareAllocationReturnBillSaveRequst();
        request.setR3(true);

        SgBShareAllocationReturnSaveRequst mainRequest = new SgBShareAllocationReturnSaveRequst();
        // 处理主表数据
        mainRequest.setBillDate(new Date());
//                mainRequest.setSourceBillId(vipReplenish.getId());
//                mainRequest.setSourceBillNo(vipReplenish.getBillNo());
//                mainRequest.setSourceBillType(SgConstantsIF.BILL_SG_B_SHARE_VIP_REPLENISH);
        mainRequest.setSgCSaStoreId(saStoreId);
        mainRequest.setSgCShareStoreId(ShareStoreId);
        mainRequest.setRemark("由大效期自动分货任务自动生成！");

        List<SgBShareAllocationReturnItemSaveRequst> itemRequestList = new ArrayList<>();

        SgBShareAllocationReturnItemSaveRequst itemRequest = new SgBShareAllocationReturnItemSaveRequst();
        // 处理明细数据
        itemRequest.setPsCSkuId(skuId);
        try {
            CommonCacheValUtils.setSkuInfo(skuId, null, itemRequest);
        } catch (Exception e) {
            log.error(LogUtil.format("SgCShareStoreByAutoTypeService.SgBShareAllocationReturn error:{},skuid:{}",
                    "SgCShareStoreExpiryDateAutoDistributionService.buildReturnParam"),
                    Throwables.getStackTraceAsString(e), skuId);
        }
        itemRequest.setQty(num);
        itemRequest.setId(-1L);
        itemRequestList.add(itemRequest);
        request.setLoginUser(R3SystemUserResource.getSystemRootUser());
        request.setAllocationReturnSaveRequst(mainRequest);
        request.setAllocationReturnItemSaveRequst(itemRequestList);
        return request;
    }

    /**
     * 计算每个sku在逻辑仓的大效期数量
     *
     * @param skuIds                 sku集合
     * @param sgBStorageList         逻辑仓库存集合
     * @param storageExpirySkuNumMap 所有逻辑仓下每个sku的大效期数量
     * @param storageSkuNumMap       所有逻辑仓下每个sku的非大效期数量
     */
    private void expiryDateNum(List<Long> skuIds, List<SgBStorage> sgBStorageList,
                               Map<Long, BigDecimal> storageExpirySkuNumMap, Map<Long, BigDecimal> storageSkuNumMap) {
        Map<Long, List<SgBStorage>> sgBStorageListMap =
                sgBStorageList.stream().collect(Collectors.groupingBy(SgBStorage::getPsCSkuId));
        Map<Long, String> definitionMap;

        try {
            //查询所有sku的大效期范围
            definitionMap = CommonCacheValUtils.queryValidityDefinitionProduceByPsCSkuIdList(skuIds, SgConstants.PS_C_CONTAINS_VALIDITY_TYPE_BIG_LONG);
        } catch (Exception e) {
            log.error(LogUtil.format("查询所有sku的大效期范围,参数：{},错误信息：{}", "SgCShareStoreExpiryDateAutoDistributionService.expiryDateNum"),
                    skuIds, Throwables.getStackTraceAsString(e));
            throw new NDSException("查询大效期范围失败");
        }


        //根据sku逐个计算
        for (Long skuId : skuIds) {
            List<SgBStorage> SgBStorageList = sgBStorageListMap.get(skuId);
            if (CollectionUtils.isEmpty(SgBStorageList)) {
                continue;
            }
            handlerNumBySku(storageExpirySkuNumMap, storageSkuNumMap, SgBStorageList, definitionMap.get(skuId), skuId);
        }
    }

    /**
     * 没有设置标准效期管理直接算在大效期，有效期管理两种情况算在大效期，一是效期范围满足，二是库存的效期为00000000
     *
     * @param storageExpirySkuNumMap 逻辑仓大效期数量
     * @param storageSkuNumMap       逻辑仓非大效期数量
     * @param storageList            逻辑仓库存
     * @param produceRange           商品效期管理
     * @param skuId                  skuId
     */
    private void handlerNumBySku(Map<Long, BigDecimal> storageExpirySkuNumMap, Map<Long, BigDecimal> storageSkuNumMap,
                                 List<SgBStorage> storageList, String produceRange, Long skuId) {
        if (StringUtils.isEmpty(produceRange)) {
            BigDecimal num = storageSkuNumMap.getOrDefault(skuId, BigDecimal.ZERO);
            for (SgBStorage storage : storageList) {
                num = num.add(storage.getQtyAvailable());
            }
            storageSkuNumMap.put(skuId, num);
            return;
        }

        String[] split = produceRange.split(SgConstants.SG_CONNECTOR_MARKS_6);
        String startDate = split[0];
        String endDate = split[1];

        BigDecimal expiryNum = storageExpirySkuNumMap.getOrDefault(skuId, BigDecimal.ZERO);
        BigDecimal num = storageSkuNumMap.getOrDefault(skuId, BigDecimal.ZERO);
        for (SgBStorage storage : storageList) {
            if ((storage.getProduceDate().compareTo(startDate) >= 0
                    && storage.getProduceDate().compareTo(endDate) <= 0)) {
                expiryNum = expiryNum.add(storage.getQtyAvailable());
            } else {
                num = num.add(storage.getQtyAvailable());
            }
        }
        storageExpirySkuNumMap.put(skuId, expiryNum);
        storageSkuNumMap.put(skuId, num);
    }

    /**
     * 分页查询库存
     *
     * @param storeidList 店仓id
     * @param dataInfo    分页参数
     * @return 库存
     */
    private List<SgBStorage> getPageStorage(List<Long> storeidList, PageInfo<SgBStorage> dataInfo) {
        List<SgBStorage> data = new ArrayList<>();
        //获取页面
        int pages = dataInfo.getPages();

        //从第二页开始再查
        for (int i = 2; i <= pages; i++) {
            SgStoragePageQueryRequest sgStoragePageQueryRequest = new SgStoragePageQueryRequest();

            SgStorageQueryRequest sgStorageQueryRequest = new SgStorageQueryRequest();
            sgStorageQueryRequest.setStoreIds(storeidList);

            SgStoragePageRequest sgStoragePageRequest = new SgStoragePageRequest();
            sgStoragePageRequest.setPageNum(i);
            sgStoragePageRequest.setPageSize(SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);


            sgStoragePageQueryRequest.setPageRequest(sgStoragePageRequest);
            sgStoragePageQueryRequest.setQueryRequest(sgStorageQueryRequest);
            ValueHolderV14<PageInfo<SgBStorage>> pageInfoValueHolderV14 = sgStorageQueryService.queryStoragePage(sgStoragePageQueryRequest,
                    R3SystemUserResource.getSystemRootUser());
            AssertUtils.cannot(!pageInfoValueHolderV14.isOK(), "根据逻辑仓分页查逻辑仓库存异常：" + pageInfoValueHolderV14.getMessage());

            PageInfo<SgBStorage> pageInfo = pageInfoValueHolderV14.getData();
            if (pageInfo != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(pageInfo.getList())) {
                data.addAll(pageInfo.getList());
            }
        }

        return data;
    }
}
