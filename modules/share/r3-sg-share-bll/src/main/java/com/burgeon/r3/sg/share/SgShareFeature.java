package com.burgeon.r3.sg.share;

import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.share.filter.autodistribution.*;
import com.burgeon.r3.sg.share.filter.strategy.*;
import com.burgeon.r3.sg.share.filter.transfer.*;
import com.burgeon.r3.sg.share.filter.vip.SgBShareVipReplenishSaveFilter;
import com.burgeon.r3.sg.share.validate.*;
import com.jackrain.nea.tableservice.Feature;
import com.jackrain.nea.tableservice.constants.TableServiceConstants;
import com.jackrain.nea.tableservice.feature.FeatureAnnotation;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2021/6/9
 */
@FeatureAnnotation(value = "SgShareFeature", description = "共享基础档案Feature")
public class SgShareFeature extends Feature {

    @Autowired
    private SgCOnesetSaSettingSaveFilter sgCOnesetSaSettingSaveFilter;

    @Autowired
    private SgCOnesetSettingSaveFilter sgCOnesetSettingSaveFilter;

    @Autowired
    private SgCOnesetStorageGradientQtySaveFilter sgCOnesetStorageGradientQtySaveFilter;

    @Autowired
    private SgCShopAllocationGradientSaveFilter sgCShopAllocationGradientSaveFilter;

    @Autowired
    private SgCShopAllocationSaveFilter sgCShopAllocationSaveFilter;

    @Autowired
    private SgCShopSkuSaleSettingSaveFilter sgCShopSkuSaleSettingSaveFilter;

    @Autowired
    private SgCEcomActivitySettingSaveFilter sgCEcomActivitySettingSaveFilter;

    @Autowired
    private SgCEcomActivitySettingSubmitFilter sgCEcomActivitySettingSubmitFilter;

    @Autowired
    private SgCEcomActivitySettingVoidFilter sgCEcomActivitySettingVoidFilter;

    @Autowired
    private SgBShareSaTransferSaveFilter sgBShareSaTransferSaveFilter;

    @Autowired
    private SgBShareSaTransferVoidFilter sgBShareSaTransferVoidFilter;

    @Autowired
    private SgBShareSaTransferDeleteFilter sgBShareSaTransferDeleteFilter;

    @Autowired
    private SgBShareSaTransferSaveValidate sgBShareSaTransferSaveValidate;

    @Autowired
    private SgBShareSaTransferSubmitFilter sgBShareSaTransferSubmitFilter;

    @Autowired
    private SgCEcomProductMaintSaveFilter sgCEcomProductMaintSaveFilter;

    @Autowired
    private SgBShareSaBatchTransferSaveFilter sgBShareSaBatchTransferSaveFilter;
    @Autowired
    private SgBShareSaBatchTransferSaveValidate sgBShareSaBatchTransferSaveValidate;
    @Autowired
    private SgBShareSaBatchTransferVoidFilter sgBShareSaBatchTransferVoidFilter;
    @Autowired
    private SgBShareSaBatchTransferDeleteFilter sgBShareSaBatchTransferDeleteFilter;

    @Autowired
    private SgBShareVipReplenishStrategySaveValidate sgBShareVipReplenishStrategySaveValidate;
    @Autowired
    private SgBShareVipReplenishStrategySaveFilter sgBShareVipReplenishStrategySaveFilter;
    @Autowired
    private SgBShareVipReplenishStrategySubmitFilter sgBShareVipReplenishStrategySubmitFilter;
    @Autowired
    private SgBShareVipReplenishStrategyUnSubFilter sgBShareVipReplenishStrategyUnSubFilter;

    @Autowired
    private SgBShareVipReplenishStrategyVoidFilter sgBShareVipReplenishStrategyVoidFilter;
    @Autowired
    private SgBShareVipReplenishStrategyDeleteFilter sgBShareVipReplenishStrategyDeleteFilter;

    @Autowired
    private SgBShareVipReplenishSaveFilter sgShareVipReplenishSaveFilter;
    @Autowired
    private SgBShareVipReplenishSaveValidate sgShareVipReplenishSaveValidate;

    @Autowired
    private SgBShareSaCrossTransferSaveFilter sgShareSaCrossTransferSaveFilter;
    @Autowired
    private SgBShareSaCrossTransferVoidFilter sgShareSaCrossTransferVoidFilter;
    @Autowired
    private SgBShareSaCrossTransferSubmitFilter sgShareSaCrossTransferSubmitFilter;
    @Autowired
    private SgBShareSaCrossTransferDeleteValidate sgShareSaCrossTransferDeleteValidate;

    @Autowired
    private SgBShareFromSaTransferSaveFilter sgShareFromSaTransferSaveFilter;
    @Autowired
    private SgBShareFromSaTransferSubmitFilter sgShareFromSaTransferSubmitFilter;
    @Autowired
    private SgBShareFromSaTransferVoidFilter sgShareFromSaTransferVoidFilter;
    @Autowired
    private SgBShareFromSaTransferDeleteValidate sgShareFromSaTransferDeleteValidate;
    @Autowired
    private SgBShareTransferVoidFilter sgBShareTransferVoidFilter;
    @Autowired
    private SgBShareTransferDeleteFilter sgBShareTransferDeleteFilter;

    @Autowired
    private SgBShopReplenishStrategySaveValidate sgBShopReplenishStrategySaveValidate;
    @Autowired
    private SgBShopReplenishStrategySaveFilter sgBShopReplenishStrategySaveFilter;
    @Autowired
    private SgBShopReplenishStrategyDeleteFilter sgBShopReplenishStrategyDeleteFilter;
    @Autowired
    private SgBShopReplenishStrategyVoidFilter sgBShopReplenishStrategyVoidFilter;
    @Autowired
    private SgBShopReplenishStrategySubmitFilter sgBShopReplenishStrategySubmitFilter;
    @Autowired
    private SgBShopReplenishStrategyUnSubmitFilter sgBShopReplenishStrategyUnSubmitFilter;

    @Override
    protected void initialization() {
        //唯品会商品补货策略保存
        addValidator(sgBShareVipReplenishStrategySaveValidate, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_VIP_REPLENISH_STRATEGY)
                || tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_VIP_REPLENISH_STRATEGY_ITEM))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        addFilter(sgBShareVipReplenishStrategySaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_VIP_REPLENISH_STRATEGY)
                || tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_VIP_REPLENISH_STRATEGY_ITEM))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));
        //唯品会商品补货策略审核
        addFilter(sgBShareVipReplenishStrategySubmitFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_VIP_REPLENISH_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SUBMIT)));
        //唯品会商品补货策略取消审核
        addFilter(sgBShareVipReplenishStrategyUnSubFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_VIP_REPLENISH_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_UNSUBMIT)));
        //唯品会商品补货策略删除
        addFilter(sgBShareVipReplenishStrategyDeleteFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_VIP_REPLENISH_STRATEGY)
                || tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_VIP_REPLENISH_STRATEGY_ITEM))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_DELETE)));
        //唯品会商品补货策略作废
        addFilter(sgBShareVipReplenishStrategyVoidFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_VIP_REPLENISH_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_VOID)));


//        //一手码设置保存
//        addFilter(sgCOnesetSettingSaveFilter, (tableName, actionName)
//                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_ONESET_SETTING))
//                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
//                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //一手码配销仓特殊设置保存
        addFilter(sgCOnesetSaSettingSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_ONESET_SA_SETTING))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //一手码库存阶梯数量保存
        addFilter(sgCOnesetStorageGradientQtySaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_ONESET_STORAGE_GRADIENT_QTY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //店铺商品售卖设置保存
        addFilter(sgCShopSkuSaleSettingSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_SHOP_SKU_SALE_SETTING))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //店铺铺底梯度设置保存
        addFilter(sgCShopAllocationGradientSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_SHOP_ALLOCATION_GRADIENT))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //店铺铺底梯度设置保存
        addFilter(sgCShopAllocationSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_SHOP_ALLOCATION))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //E-COM活动时间设置保存
        addFilter(sgCEcomActivitySettingSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_ECOM_ACTIVITY_SETTING))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));
        //E-COM活动时间设置审核
        addFilter(sgCEcomActivitySettingSubmitFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_ECOM_ACTIVITY_SETTING))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SUBMIT)
                || actionName.equals(TableServiceConstants.ACTION_SUBMIT)));
        //E-COM活动时间设置作废
        addFilter(sgCEcomActivitySettingVoidFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_ECOM_ACTIVITY_SETTING))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_VOID)));

        //配销仓调拨单保存
        addValidator(sgBShareSaTransferSaveValidate, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_SA_TRANSFER))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        addFilter(sgBShareSaTransferSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_SA_TRANSFER))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        addFilter(sgBShareSaTransferVoidFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_SA_TRANSFER))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_VOID)));

        addFilter(sgBShareSaTransferDeleteFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_SA_TRANSFER))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_DELETE)));

        addFilter(sgBShareSaTransferSubmitFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_SA_TRANSFER))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SUBMIT)
                || actionName.equals(TableServiceConstants.ACTION_SUBMIT)));

        //配销仓调拨单批量导入
        addValidator(sgBShareSaBatchTransferSaveValidate, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_SA_BATCH_TRANSFER))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        addFilter(sgBShareSaBatchTransferSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_SA_BATCH_TRANSFER))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        addFilter(sgBShareSaBatchTransferVoidFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_SA_BATCH_TRANSFER))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_VOID)));

        addFilter(sgBShareSaBatchTransferDeleteFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_SA_BATCH_TRANSFER))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_DELETE)));

        //电商商品属性维护
        addFilter(sgCEcomProductMaintSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_ECOM_PRODUCT_MAINT))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //唯品会商品补货信息表新增保存
        addValidator(sgShareVipReplenishSaveValidate, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_VIP_REPLENISH))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        addFilter(sgShareVipReplenishSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_VIP_REPLENISH))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //配销跨聚合仓调拨单新增保存
        addFilter(sgShareSaCrossTransferSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_SA_CROSS_TRANSFER))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //配销跨聚合仓调拨单作废
        addFilter(sgShareSaCrossTransferVoidFilter, (tableName, actionName) ->
                (SgConstants.SG_B_SHARE_SA_CROSS_TRANSFER.equalsIgnoreCase(tableName))
                        && actionName.equals(TableServiceConstants.ACTION_VOID));

        //配销跨聚合仓调拨单审核
        addFilter(sgShareSaCrossTransferSubmitFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_SA_CROSS_TRANSFER))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SUBMIT)));

        //配销跨聚合仓调拨单删除
        addValidator(sgShareSaCrossTransferDeleteValidate, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_SA_CROSS_TRANSFER))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_DELETE)));

        //配销仓到聚合仓调拨单新增保存
        addFilter(sgShareFromSaTransferSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_FROM_SA_TRANSFER))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //配销仓到聚合仓调拨单作废
        addFilter(sgShareFromSaTransferVoidFilter, (tableName, actionName) ->
                (SgConstants.SG_B_SHARE_FROM_SA_TRANSFER.equalsIgnoreCase(tableName))
                        && actionName.equals(TableServiceConstants.ACTION_VOID));

        //配销仓到聚合仓调拨单审核
        addFilter(sgShareFromSaTransferSubmitFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_FROM_SA_TRANSFER))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SUBMIT)));

        //配销仓到聚合仓调拨单删除
        addValidator(sgShareFromSaTransferDeleteValidate, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHARE_FROM_SA_TRANSFER))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_DELETE)));

        //聚合仓调拨单作废
        addFilter(sgBShareTransferVoidFilter, (tableName, actionName) ->
                (SgConstants.SG_B_SHARE_TRANSFER.equalsIgnoreCase(tableName))
                        && actionName.equals(TableServiceConstants.ACTION_VOID));

        //聚合仓调拨单删除
        addFilter(sgBShareTransferDeleteFilter, (tableName, actionName) ->
                (SgConstants.SG_B_SHARE_TRANSFER.equalsIgnoreCase(tableName))
                        && actionName.equals(TableServiceConstants.ACTION_DELETE));

        // 官网小程序库存店铺自动补货策略保存
        addValidator(sgBShopReplenishStrategySaveValidate, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHOP_REPLENISH_STRATEGY) ||
                tableName.equalsIgnoreCase(SgConstants.SG_B_SHOP_REPLENISH_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        addFilter(sgBShopReplenishStrategySaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHOP_REPLENISH_STRATEGY) ||
                tableName.equalsIgnoreCase(SgConstants.SG_B_SHOP_REPLENISH_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));


        //官网小程序库存店铺自动补货策略作废
        addFilter(sgBShopReplenishStrategyVoidFilter, (tableName, actionName) ->
                (SgConstants.SG_B_SHOP_REPLENISH_STRATEGY.equalsIgnoreCase(tableName))
                        && actionName.equals(TableServiceConstants.ACTION_VOID));
        //官网小程序库存店铺自动补货策略审核
        addFilter(sgBShopReplenishStrategySubmitFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHOP_REPLENISH_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SUBMIT)));

        //官网小程序库存店铺自动补货策略取消审核
        addFilter(sgBShopReplenishStrategyUnSubmitFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHOP_REPLENISH_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_UNSUBMIT)));


        // 官网小程序库存店铺自动补货策略删除
        addValidator(sgBShopReplenishStrategyDeleteFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_SHOP_REPLENISH_STRATEGY)
                || tableName.equalsIgnoreCase(SgConstants.SG_B_SHOP_REPLENISH_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_DELETE)));
    }
}
