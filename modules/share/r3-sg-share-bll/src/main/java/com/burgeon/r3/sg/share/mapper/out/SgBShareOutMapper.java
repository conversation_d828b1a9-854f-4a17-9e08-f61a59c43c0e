package com.burgeon.r3.sg.share.mapper.out;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOut;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutInfoBySourceResult;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutInfoResult;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutQueryShareStoreResult;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.web.face.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

@Mapper
public interface SgBShareOutMapper extends ExtentionMapper<SgBShareOut> {


    @Select("SELECT * from sg_b_share_out where STO_OUT_STATUS is null limit #{limit}  ")
    List<SgBShareOut> selectPageByStoOutStatus(@Param(value = "limit") int limit);

    @Select("SELECT * from sg_b_share_out where STO_OUT_VOID_STATUS is null AND ISACTIVE='N' limit #{limit}  ")
    List<SgBShareOut> selectPageByStoOutVoidStatus(@Param(value = "limit") int limit);

    @Select({
            "<script>",
            " /*FORCE_MASTER*/ ",
            " SELECT ",
            " A.ID AS billId, ",
            " A.BILL_NO AS billNo, ",
            " A.MERGE_MARK, ",
            " A.SOURCE_BILL_ID AS sourceBillId, ",
            " A.SG_C_SHARE_STORE_ID AS sgCShareStoreId, ",
            " A.SG_C_SHARE_STORE_ECODE AS sgCShareStoreEcode, ",
            " A.SG_C_SHARE_STORE_NAME AS sgCShareStoreEname, ",
            " B.ID AS itemId, ",
            " B.PS_C_SKU_ID AS psCSkuId, ",
            " B.SOURCE_BILL_ITEM_ID AS sourceItemId, ",
            " B.SG_C_SA_STORE_ID AS sgCSaStoreId, ",
            " B.QTY_PREOUT AS qtyPreout ",
            " FROM ",
            " SG_B_SHARE_OUT A ",
            "LEFT JOIN SG_B_SHARE_OUT_ITEM B ON A.ID = B.SG_B_SHARE_OUT_ID ",
            "WHERE ",
            " A.ISACTIVE = 'Y' AND B.ISACTIVE = 'Y' ",
            "<when test='sourceBillType!=null'>",
            " AND A.SOURCE_BILL_TYPE = #{sourceBillType,jdbcType=INTEGER}",
            "</when>",
            "<when test='sourceBillId!=null'>",
            " AND A.SOURCE_BILL_ID = #{sourceBillId,jdbcType=INTEGER}",
            "</when>",
            "</script>"
    })
    List<SgBShareOutInfoResult> selectShareOutBySourceBillId(@Param("sourceBillId") Long sourceBillId, @Param("sourceBillType") Integer sourceBillType);


    @Select({
            "<script>",
            " /*FORCE_MASTER*/ ",
            " SELECT ",
            " A.ID AS billId, ",
            " A.BILL_NO AS billNo, ",
            " A.MERGE_MARK, ",
            " A.SOURCE_BILL_ID AS sourceBillId, ",
            " A.SG_C_SHARE_STORE_ID AS sgCShareStoreId, ",
            " A.SG_C_SHARE_STORE_ECODE AS sgCShareStoreEcode, ",
            " A.SG_C_SHARE_STORE_NAME AS sgCShareStoreEname, ",
            " B.ID AS itemId, ",
            " B.PS_C_SKU_ID AS psCSkuId, ",
            " B.SOURCE_BILL_ITEM_ID AS sourceItemId, ",
            " B.SG_C_SA_STORE_ID AS sgCSaStoreId, ",
            " B.QTY_PREOUT AS qtyPreout ",
            " FROM ",
            " SG_B_SHARE_OUT A ",
            "LEFT JOIN SG_B_SHARE_OUT_ITEM_LOG B ON A.ID = B.SG_B_SHARE_OUT_ID ",
            "WHERE ",
            " A.ISACTIVE = 'Y' AND B.ISACTIVE = 'Y' ",
            " <if test='ids != null and ids.size > 0'>",
            "  and A.ID in ",
            " <foreach collection='ids' open='(' separator=',' close=')' item='id'> ",
            " #{id}",
            " </foreach>",
            " </if>",
            "</script>"
    })
    List<SgBShareOutInfoResult> selectShareOutByLogIds(@Param("ids") List<Long> ids);

    @Update("UPDATE SG_B_SHARE_OUT " +
            " SET sto_out_status = #{stoOutStatus}, sto_out_fail_reason = #{failReason}, " +
            " modifierid = #{user.id}, modifierename = #{user.ename}, modifiername=#{user.name}, " +
            " modifieddate = #{date,jdbcType=TIMESTAMP} " +
            " WHERE source_bill_id = #{sourceBillId} ")
    int updateOutStatusBySuccess(@Param("sourceBillId") Long sourceBillId, @Param("stoOutStatus") Integer stoOutStatus,
                                 @Param("failReason") String failReason, @Param("user") User user,
                                 @Param("date") Date date);

    @Update("UPDATE SG_B_SHARE_OUT " +
            " SET sto_out_status = #{stoOutStatus}, sto_out_fail_reason = #{failReason}, " +
            " modifierid = #{user.id}, modifierename = #{user.ename}, modifiername=#{user.name}, " +
            " modifieddate = #{date,jdbcType=TIMESTAMP}, sto_out_fail_num = sto_out_fail_num+1 " +
            " WHERE source_bill_id = #{sourceBillId} ")
    int updateOutStatusByFail(@Param("sourceBillId") Long sourceBillId, @Param("stoOutStatus") Integer stoOutStatus,
                              @Param("failReason") String failReason, @Param("user") User user,
                              @Param("date") Date date);

    @Update("<script>" +
            "UPDATE SG_B_SHARE_OUT " +
            " SET isactive = #{isactive} , bill_status = #{status} ,  deler_id = #{user.id}, deler_ename = #{user.ename}," +
            " deler_name=#{user.name}, del_time = #{date,jdbcType=TIMESTAMP}, modifierename = #{user.ename}, " +
            " modifiername=#{user.name}," +
            " modifieddate = #{date,jdbcType=TIMESTAMP} " +
            "<if test='ids != null and ids.size() > 0'>" +
            " WHERE id in " +
            "<foreach collection='ids' open='(' separator=',' close=')' item='shareId'> " +
            " #{shareId}" +
            "</foreach>" +
            "</if>" +
            "<if test='ids == null or ids.size() == 0'>" +
            " WHERE id = -1 " +
            "</if>" +
            "</script>")
    int updateOutBySourceBillNo(@Param("ids") List<Long> ids, @Param("user") User user,
                                @Param("isactive") String isactive, @Param("status") Integer status, @Param("date") Date date);

    @Select({
            "<script>",
            " SELECT ",
            " o.id as shareId, ",
            " o.sg_c_share_store_id as sgShareStoreId, ",
            " o.source_bill_id as sourceBillId, ",
            " o.source_bill_no as sourceBillNo, ",
            " o.source_bill_type as sourceBillType, ",
            " o.bill_no as shareBillNo, ",
            " o.merge_mark as mergeMark, ",
            " i.source_bill_item_id as sourceBillItemId, ",
            " i.sg_c_sa_store_id as saId, ",
            " i.sg_c_share_pool_id as spId, ",
            " i.id as itemId, ",
            " i.ps_c_sku_id as skuId,",
            " i.qty ",
            "FROM ",
            " sg_b_share_out o ",
            "LEFT JOIN sg_b_share_out_item i ON o.id = i.sg_b_share_out_id ",
            " where o.isactive = 'Y' and i.isactive = 'Y' ",
            "<when test='sourceBillId !=null '>",
            " and o.source_bill_id = #{sourceBillId,jdbcType=INTEGER}",
            "</when>",
            "<when test='sourceBillType!=null '>",
            " and o.source_bill_type = #{sourceBillType,jdbcType=INTEGER}",
            "</when>",
            "</script>"
    })
    List<SgBShareOutInfoBySourceResult> selectListBySource(@Param(value = "sourceBillId") Long sourceBillId, @Param(value = "sourceBillType") Integer sourceBillType);

    @Select({
            "<script>",
            " SELECT ",
            " o.id as shareId, ",
            " o.sg_c_share_store_id as sgShareStoreId, ",
            " o.source_bill_id as sourceBillId, ",
            " o.source_bill_no as sourceBillNo, ",
            " o.source_bill_type as sourceBillType, ",
            " o.bill_no as shareBillNo, ",
            " i.source_bill_item_id as sourceBillItemId, ",
            " i.sg_c_sa_store_id as saId, ",
            " i.sg_c_share_pool_id as spId, ",
            " i.id as itemId, ",
            " i.ps_c_sku_id as skuId,",
            " i.qty ",
            "FROM ",
            " sg_b_share_out o ",
            "LEFT JOIN sg_b_share_out_item_log i ON o.id = i.sg_b_share_out_id ",
            " where o.isactive = 'Y' and i.isactive = 'Y' ",
            "<when test='sourceBillId !=null '>",
            " and o.source_bill_id = #{sourceBillId,jdbcType=INTEGER}",
            "</when>",
            "<when test='sourceBillType!=null '>",
            " and o.source_bill_type = #{sourceBillType,jdbcType=INTEGER}",
            "</when>",
            "</script>"
    })
    List<SgBShareOutInfoBySourceResult> selectListByLogSource(@Param(value = "sourceBillId") Long sourceBillId, @Param(value = "sourceBillType") Integer sourceBillType);

    /**
     * 强制从主数据库查找
     *
     * @param wrapper
     * @return
     */
    @Select("/*FORCE_MASTER*/ select * from sg_b_share_out ${ew.customSqlSegment}")
    List<SgBShareOut> selectListMaster(@Param(Constants.WRAPPER) Wrapper wrapper);

    /*@Select({"<script>" +
            "SELECT" +
            " s.id billId," +
            " s.bill_no billNo," +
            " s.source_bill_id sourceBIllId, " +
            " s.sg_c_share_store_id sgCShareStoreId, " +
            " s.sg_c_share_store_ecode sgCShareStoreEcode, " +
            " s.sg_c_share_store_name sgCShareStoreName " +
            "FROM sg_b_share_out s " +
            "where " +
            " s.isactive = 'Y' " +
            "<if test='sourceBillType != null'>" +
            " AND s.source_bill_type = #{sourceBillType}" +
            "</if>" +
            "<if test='sourceBIllIds != null and sourceBIllIds.size() > 0'>" +
            " AND s.source_bill_id in " +
            "<foreach collection='sourceBIllIds' open='(' separator=',' close=')' item='sourceBIllId'> " +
            " #{sourceBIllId}" +
            "</foreach>" +
            "</if>" +
            "</script>"})
    @Results({
            @Result(id = true,property = "billId",column = "billId"),
            @Result(property = "billNo",column = "billNo"),
            @Result(property = "sourceBIllId",column = "sourceBIllId"),
            @Result(property = "sgCShareStoreId",column = "sgCShareStoreId"),
            @Result(property = "sgCShareStoreEcode",column = "sgCShareStoreEcode"),
            @Result(property = "sgCShareStoreName",column = "sgCShareStoreName"),
            @Result(property = "list",column = "billId",javaType = List.class,
            many = @Many(select = "com.burgeon.r3.sg.share.mapper.out.SgBShareOutItemMapper.selectListByShareOutId"))
    })*/
    @Select({"<script>" +
            " /*FORCE_MASTER*/ ",
            "SELECT " +
                    "  s.id billId, " +
                    "  s.bill_no billNo, " +
                    "  s.merge_mark, " +
                    "  s.source_bill_id sourceBIllId, " +
                    "  s.source_bill_no sourceBIllNo, " +
                    "  s.sg_c_share_store_id sgCShareStoreId, " +
                    "  s.sg_c_share_store_ecode sgCShareStoreEcode, " +
                    "  s.sg_c_share_store_name sgCShareStoreEname, " +
                    "  i.id itemId, " +
                    "  i.sg_c_sa_store_id sgCSaStoreId, " +
                    "  i.sg_c_sa_store_ecode sgCSaStoreEcode, " +
                    "  i.sg_c_sa_store_ename sgCSaStoreEname, " +
                    "  i.ps_c_sku_id psCSkuId,  " +
                    "  i.source_bill_item_id sourceItemId, " +
                    "  i.qty_preout qtyPreout " +
                    "FROM " +
                    "  sg_b_share_out s " +
                    "  LEFT JOIN sg_b_share_out_item i ON s.id = i.sg_b_share_out_id  " +
                    "WHERE " +
                    "  s.isactive = 'Y' AND i.isactive = 'Y' " +
                    "<if test='sourceBillType != null'>" +
                    "  AND s.source_bill_type = #{sourceBillType}" +
                    "</if>" +
                    "<if test='sourceBIllIds != null and sourceBIllIds.size() > 0'>" +
                    "  AND s.source_bill_id in " +
                    "<foreach collection='sourceBIllIds' open='(' separator=',' close=')' item='sourceBIllId'> " +
                    "  #{sourceBIllId}" +
                    "</foreach>" +
                    "</if>" +
                    "</script>"})
    List<SgBShareOutInfoResult> findShareOutAndItem(@Param("sourceBIllIds") List<Long> sourceBIllIds, @Param("sourceBillType") Integer sourceBillType);

    @Select("<script>" +
            " SELECT s.sg_c_share_store_id,s.sg_c_share_store_name,s.sg_c_share_store_ecode,si.ps_c_sku_id" +
            " FROM sg_b_share_out s LEFT JOIN sg_b_share_out_item si " +
            " ON s.id = si.sg_b_share_out_id " +
            "  WHERE s.source_bill_no = #{sourceBillNo} " +
            "  AND si.ps_c_sku_id in " +
            "<foreach collection='items' open='(' separator=',' close=')' item='id'> " +
            "  #{id}" +
            " </foreach>" +
            "</script>")
    List<SgBShareOutQueryShareStoreResult> queryShareShoreBySourceBillNo(@Param("sourceBillNo") String sourceBillNo,
                                                                         @Param("items") List<Long> items);

    @Update("<script>" +
            "UPDATE sg_b_share_out p " +
            "<set>" +
            " p.source_bill_type = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='shareOuts' item='shareOut' index='index' separator=' '>" +
            "<if test = 'shareOut.sourceBillType != null'>" +
            " when #{shareOut.id} then #{shareOut.sourceBillType}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.source_bill_id = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='shareOuts' item='shareOut' index='index' separator=' '>" +
            "<if test = 'shareOut.sourceBillId != null'>" +
            " when #{shareOut.id} then #{shareOut.sourceBillId}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.source_bill_no = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='shareOuts' item='shareOut' index='index' separator=' '>" +
            "<if test = 'shareOut.sourceBillNo != null'>" +
            " when #{shareOut.id} then #{shareOut.sourceBillNo}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.tot_qty_orign = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='shareOuts' item='shareOut' index='index' separator=' '>" +
            "<if test = 'shareOut.totQtyOrign != null'>" +
            " when #{shareOut.id} then #{shareOut.totQtyOrign}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.tot_qty_preout = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='shareOuts' item='shareOut' index='index' separator=' '>" +
            "<if test = 'shareOut.totQtyPreout != null'>" +
            " when #{shareOut.id} then #{shareOut.totQtyPreout}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.tot_qty_out = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='shareOuts' item='shareOut' index='index' separator=' '>" +
            "<if test = 'shareOut.totQtyOut != null'>" +
            " when #{shareOut.id} then #{shareOut.totQtyOut}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.tot_row_num = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='shareOuts' item='shareOut' index='index' separator=' '>" +
            "<if test = 'shareOut.totRowNum != null'>" +
            " when #{shareOut.id} then #{shareOut.totRowNum}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "</set>" +
            "<where>" +
            " p.id in " +
            " <foreach collection='shareOuts' item='shareOut' open='(' separator=',' close=')'>" +
            " #{shareOut.id} " +
            "</foreach>" +
            "</where>" +
            "</script>")
    Integer batchUpdateById(@Param("shareOuts") List<SgBShareOut> shareOuts);

}