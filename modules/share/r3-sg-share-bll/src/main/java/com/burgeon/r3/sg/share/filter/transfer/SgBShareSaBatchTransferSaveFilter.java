package com.burgeon.r3.sg.share.filter.transfer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareSaBatchTransfer;
import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareSaBatchTransferItem;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareSaBatchTransferItemMapper;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareSaBatchTransferMapper;
import com.burgeon.r3.sg.share.model.dto.SgBShareSaBatchTransferDto;
import com.burgeon.r3.sg.share.model.dto.SgBShareSaBatchTransferItemDto;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleItemFilter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/1 17:49
 */
@Component
public class SgBShareSaBatchTransferSaveFilter extends BaseSingleItemFilter<SgBShareSaBatchTransferDto,
        SgBShareSaBatchTransferItemDto> {

    @Autowired
    private SgBShareSaBatchTransferMapper mapper;
    @Autowired
    private SgBShareSaBatchTransferItemMapper itemMapper;

    @Override
    public String getFilterMsgName() {
        return "配销仓调拨单批量导入保存过滤";
    }

    @Override
    public Class<?> getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgBShareSaBatchTransferDto mainObject, User loginUser) {
        Date now = new Date();
        if (Objects.isNull(mainObject.getId()) || mainObject.getId() < 1L) {
            String billNo = SgStoreUtils.getBillNo(SgShareConstants.SEQ_SG_B_SHARE_SA_BATCH_TRANSFER,
                    SgConstants.SG_B_SHARE_SA_BATCH_TRANSFER, mainObject,
                    loginUser.getLocale());
            mainObject.setBillNo(billNo);
            if (Objects.isNull(mainObject.getBillDate())) {
                mainObject.setBillDate(now);
            }
            mainObject.setBillStatus(SgConstantsIF.SG_B_SHARE_SA_BATCH_TRANSFER_STATUS_01);

            mainObject.setCreationdate(now);
            mainObject.setOwnerid(Long.valueOf(loginUser.getId()));
            mainObject.setOwnername(loginUser.getName());
            mainObject.setOwnerename(loginUser.getEname());
        }

        mainObject.setModifieddate(now);
        mainObject.setModifierid(Long.valueOf(loginUser.getId()));
        mainObject.setModifiername(loginUser.getName());
        mainObject.setModifierename(loginUser.getEname());
        return null;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgBShareSaBatchTransferDto mainObject, User loginUser) {
        return null;
    }

    @Override
    public ValueHolderV14 execBeforeSubTable(SgBShareSaBatchTransferDto mainObject, List<SgBShareSaBatchTransferItemDto> subObjectList, User loginUser) {
        for (SgBShareSaBatchTransferItemDto subObj : subObjectList) {
            Date now = new Date();
            SgBShareSaBatchTransferItem sgBShareSaBatchTransferItem = null;
            if (Objects.nonNull(subObj.getId()) && subObj.getId() > 0L) {
                sgBShareSaBatchTransferItem = itemMapper.selectById(subObj.getId());
            } else {
                LambdaQueryWrapper<SgBShareSaBatchTransferItem> eq = new LambdaQueryWrapper<SgBShareSaBatchTransferItem>()
                        .eq(SgBShareSaBatchTransferItem::getSgBShareSaBatchTransferId, mainObject.getId())
                        .eq(SgBShareSaBatchTransferItem::getSenderSaStoreId, subObj.getSenderSaStoreId())
                        .eq(SgBShareSaBatchTransferItem::getReceiverSaStoreId, subObj.getReceiverSaStoreId())
                        .eq(SgBShareSaBatchTransferItem::getPsCSkuId, subObj.getPsCSkuId())
                        .and(a -> a.eq(SgBShareSaBatchTransferItem::getItemStatus, SgConstantsIF.SG_B_STO_BATCH_TRANSFER_ITEM_STATUS_NO)
                                .or(o -> o.eq(SgBShareSaBatchTransferItem::getItemStatus, SgConstantsIF.SG_B_STO_BATCH_TRANSFER_ITEM_STATUS_FAIL)))
                        .eq(SgBShareSaBatchTransferItem::getIsactive, SgConstants.IS_ACTIVE_Y);
                sgBShareSaBatchTransferItem = itemMapper.selectOne(eq);
            }

            if (Objects.nonNull(sgBShareSaBatchTransferItem)) {
                if (subObj.getId() < 0) {
                    BigDecimal addResult = subObj.getQty().add(sgBShareSaBatchTransferItem.getQty());
                    subObj.setQty(addResult);
                }
                subObj.setId(sgBShareSaBatchTransferItem.getId());
                setPsCProSku(subObj);
            } else {
                setPsCProSku(subObj);

                SgCSaStore senderSaStore = CommonCacheValUtils.getSaStore(subObj.getSenderSaStoreId());
                SgCSaStore receiverSaStore = CommonCacheValUtils.getSaStore(subObj.getReceiverSaStoreId());
                subObj.setSenderSaStoreEcode(senderSaStore.getEcode());
                subObj.setSenderSaStoreEname(senderSaStore.getEname());
                subObj.setReceiverSaStoreEcode(receiverSaStore.getEcode());
                subObj.setReceiverSaStoreEname(receiverSaStore.getEname());

                subObj.setItemStatus(SgConstantsIF.SG_B_STO_BATCH_TRANSFER_ITEM_STATUS_NO);
                subObj.setCreationdate(now);
                subObj.setOwnerid(Long.valueOf(loginUser.getId()));
                subObj.setOwnername(loginUser.getName());
                subObj.setOwnerename(loginUser.getEname());
            }

            subObj.setModifieddate(now);
            subObj.setModifierid(Long.valueOf(loginUser.getId()));
            subObj.setModifiername(loginUser.getName());
            subObj.setModifierename(loginUser.getEname());
        }
        return null;
    }

    @Override
    public ValueHolderV14 execAfterSubTable(SgBShareSaBatchTransferDto mainObject, List<SgBShareSaBatchTransferItemDto> subObjectList, User loginUser) {
        if (mainObject.getId() > 0) {
            LambdaQueryWrapper<SgBShareSaBatchTransferItem> eq = new LambdaQueryWrapper<SgBShareSaBatchTransferItem>()
                    .eq(SgBShareSaBatchTransferItem::getSgBShareSaBatchTransferId, mainObject.getId());
            List<SgBShareSaBatchTransferItem> sgBShareSaBatchTransferItems = itemMapper.selectList(eq);
            BigDecimal totQty = sgBShareSaBatchTransferItems.stream()
                    .filter(f -> Objects.nonNull(f.getQty()))
                    .map(SgBShareSaBatchTransferItem::getQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            List<SgBShareSaBatchTransferItem> successList = sgBShareSaBatchTransferItems.stream()
                    .filter(f -> SgConstantsIF.SG_B_STO_BATCH_TRANSFER_ITEM_STATUS_SUCCESS.equals(f.getItemStatus()))
                    .collect(Collectors.toList());
            SgBShareSaBatchTransfer sgBShareSaBatchTransfer = new SgBShareSaBatchTransfer();
            sgBShareSaBatchTransfer.setId(mainObject.getId());
            sgBShareSaBatchTransfer.setTotQty(totQty);
            sgBShareSaBatchTransfer.setTotRowNum(sgBShareSaBatchTransferItems.size());
            sgBShareSaBatchTransfer.setSuccessRowNum(successList.size());
            sgBShareSaBatchTransfer.setFailRowNum(sgBShareSaBatchTransferItems.size() - successList.size());
            mapper.updateById(sgBShareSaBatchTransfer);
        }
        return null;
    }

    private void setPsCProSku(SgBShareSaBatchTransferItemDto subObj) {
        BasicPsQueryService basicPsQueryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
        SkuInfoQueryRequest skuQuery = new SkuInfoQueryRequest();
        if (Objects.isNull(subObj.getPsCSkuId())) {
            return;
        }
        List<Long> skuIds = new ArrayList<>();
        skuIds.add(subObj.getPsCSkuId());
        skuQuery.setSkuIdList(skuIds);
        HashMap<Long, PsCProSkuResult> skuInfo = basicPsQueryService.getSkuInfo(skuQuery);

        if (Objects.nonNull(skuInfo) && skuInfo.size() > 0) {
            PsCProSkuResult psCProSkuResult = skuInfo.get(skuIds.get(0));

            subObj.setPsCSkuEcode(psCProSkuResult.getSkuEcode());
            subObj.setGbcode(psCProSkuResult.getGbcode());
            subObj.setPsCProId(psCProSkuResult.getPsCProId());
            subObj.setPsCProEname(psCProSkuResult.getPsCProEname());
            subObj.setPsCProEcode(psCProSkuResult.getPsCProEcode());
        }
    }
}
