package com.burgeon.r3.sg.share.services.allocation;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocation;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationItemMapper;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationMapper;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationDeleteRequest;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/21 19:10
 */
@Slf4j
@Component
public class SgBShareAllocationDeleteService {
    @Autowired
    SgBShareAllocationMapper mapper;
    @Autowired
    SgBShareAllocationItemMapper itemMapper;

    /**
     * 分货单删除页面
     *
     * @param session 入参
     * @return 出参
     */
    ValueHolder delete(QuerySession session) {
        SgBShareAllocationDeleteRequest request = R3ParamUtils.parseSaveObject(session, SgBShareAllocationDeleteRequest.class);
        request.setR3(true);
        SgBShareAllocationDeleteService service = ApplicationContextHandle.getBean(SgBShareAllocationDeleteService.class);
        return R3ParamUtils.convertV14WithResult(service.delete(request));
    }

    /**
     * 分货单删除页面
     *
     * @param request 入参
     * @return 出参
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> delete(SgBShareAllocationDeleteRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBShareAllocationDeleteService.delete:param={}", JSONObject.toJSONString(request));
        }
        String lockKey = SgConstants.SG_B_SHARE_ALLOCATION + ":" + request.getObjId();
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "删除成功！");
        SgRedisLockUtils.lock(lockKey);
        try {

            SgBShareAllocation sgShareAllocation = checkParams(request);

            //明细删除
            if (CollectionUtils.isNotEmpty(request.getItemIds())) {
                BigDecimal totQty = sgShareAllocation.getTotQty();
                BigDecimal totAmt = sgShareAllocation.getTotAmt();
                Integer totRowNum = sgShareAllocation.getTotRowNum();

                List<SgBShareAllocationItem> items = itemMapper.selectList(new QueryWrapper<SgBShareAllocationItem>()
                        .lambda().in(SgBShareAllocationItem::getId, request.getItemIds()));

                for (SgBShareAllocationItem item : items) {
                    if (item.getQty() != null) {
                        totQty = totQty.subtract(item.getQty());
                        totAmt = totAmt.subtract(item.getAmt());
                    }
                }

                itemMapper.delete(new QueryWrapper<SgBShareAllocationItem>()
                        .lambda().in(SgBShareAllocationItem::getId, request.getItemIds()));

                totRowNum = totRowNum - request.getItemIds().size();
                StorageUtils.setBModelDefalutDataByUpdate(sgShareAllocation, request.getLoginUser());
                sgShareAllocation.setTotRowNum(totRowNum);
                sgShareAllocation.setTotAmt(totAmt);
                sgShareAllocation.setTotQty(totQty);
                mapper.updateById(sgShareAllocation);

            } else {

                itemMapper.delete(new QueryWrapper<SgBShareAllocationItem>()
                        .lambda().eq(SgBShareAllocationItem::getSgBShareAllocationId, request.getObjId()));
                mapper.delete(new QueryWrapper<SgBShareAllocation>()
                        .lambda().eq(SgBShareAllocation::getId, request.getObjId()));

            }
        } catch (Exception e) {
            AssertUtils.logAndThrowException("分货单删除异常", e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKey, log, this.getClass().getName());

        }
        return v14;
    }

    /**
     * 参数校验
     *
     * @param request 主表id
     * @return 主表信息，汇总字段会重新计算值
     */
    public SgBShareAllocation checkParams(SgR3BaseRequest request) {
        SgBShareAllocation sgShareAllocation = mapper.selectById(request.getObjId());
        AssertUtils.notNull(sgShareAllocation, "当前记录不存在！");
        if (SgConstants.IS_ACTIVE_N.equalsIgnoreCase(sgShareAllocation.getIsactive())) {
            AssertUtils.logAndThrow("当前记录已作废,不允许删除！", request.getLoginUser().getLocale());

        } else if (SgShareConstants.BILL_STATUS_UNSUBMIT != sgShareAllocation.getStatus()) {
            AssertUtils.logAndThrow("当前单据状态不允许删除！", request.getLoginUser().getLocale());
        }
        return sgShareAllocation;
    }

}