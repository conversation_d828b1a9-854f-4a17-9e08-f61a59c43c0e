package com.burgeon.r3.sg.share.services;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.config.SgStorageControlConfig;
import com.burgeon.r3.sg.basic.model.AbstractSgStorageUpdateCommonModel;
import com.burgeon.r3.sg.basic.model.request.AbstractSgStorageUpdateBillItemRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageBatchUpdateRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageSingleUpdateRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageUpdateBillRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageUpdateControlRequest;
import com.burgeon.r3.sg.basic.model.request.vo.SgStorageUpdateBillItemCfRequest;
import com.burgeon.r3.sg.basic.model.request.vo.SgStorageUpdateBillItemSaRequest;
import com.burgeon.r3.sg.basic.model.request.vo.SgStorageUpdateBillItemSsRequest;
import com.burgeon.r3.sg.basic.model.result.AbstractSgStorageOutStockResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageBillUpdateResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageUpdateResult;
import com.burgeon.r3.sg.basic.model.result.vo.SgStorageOutStockLsResult;
import com.burgeon.r3.sg.basic.services.SgStorageBillTransUpdateService;
import com.burgeon.r3.sg.basic.services.SgStorageRedisBillBatchUpdateService;
import com.burgeon.r3.sg.basic.services.SgStorageRedisBillUpdateService;
import com.burgeon.r3.sg.basic.utils.BigDecimalUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationReturn;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationReturnItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnItemSingleSubmitRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutItemSaveRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutSaveRequest;
import com.burgeon.r3.sg.store.services.SgStoreStorageService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BeanCopierUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @description:
 * @author: hwy
 * @time: 2021/5/28 10:29
 */

@Slf4j
@Component
public class SgShareStorageService {


    @Autowired
    private SgStorageControlConfig sgStorageControlConfig;

    @Autowired
    private SgStorageBillTransUpdateService transUpdateService;
    @Autowired
    private SgStorageRedisBillUpdateService redisUpdateService;

    @Value("${sg.share_allocation_return.is_negative_storage:false}")
    private Boolean isnegative;

    /**
     * @param singleSubmitRequest: 原始参数
     * @param selectMain:          主单据
     * @param selectItems:         单据明细
     * @Description: 分货退货单审核 库存变动服务
     * @Author: hwy
     * @Date: 2021/5/28 16:38
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<com.burgeon.r3.sg.basic.model.result.SgStorageUpdateResult>
     **/
    public ValueHolderV14<SgStorageUpdateResult> sgBShareInReturnStorageChange(SgBShareAllocationReturnItemSingleSubmitRequest singleSubmitRequest,
                                                                               SgBShareAllocationReturn selectMain,
                                                                               List<SgBShareAllocationReturnItem> selectItems) {
        //组装参数
        SgStorageSingleUpdateRequest request = buildStorageChangeRequest(singleSubmitRequest, selectMain, selectItems);
        if (log.isDebugEnabled()) {
            log.debug("单据id[" + selectMain.getId() + "], 变更聚合仓库存入参:{}", JSONObject.toJSONString(request));
        }
        //变更库存
        ValueHolderV14<SgStorageUpdateResult> valueHolderV14 = updatedStorage(request);
        if (log.isDebugEnabled()) {
            log.debug("单据id[" + selectMain.getId() + "],变更聚合仓库存出参:{}" + valueHolderV14.toJSONObject().toJSONString());
        }

        return valueHolderV14;

    }

    /**
     * @param request:
     * @param selectMain:
     * @param selectItems:
     * @Description: 分货退货单审核 组装库存变动参数
     * @Author: hwy
     * @Date: 2021/5/28 16:37
     * @return: com.burgeon.r3.sg.basic.model.request.SgStorageSingleUpdateRequest
     **/
    public SgStorageSingleUpdateRequest buildStorageChangeRequest(SgBShareAllocationReturnItemSingleSubmitRequest request, SgBShareAllocationReturn selectMain, List<SgBShareAllocationReturnItem> selectItems) {
        SgStorageSingleUpdateRequest sgStorageSingleUpdateRequest = new SgStorageSingleUpdateRequest();
        SgStorageUpdateBillRequest bill = new SgStorageUpdateBillRequest();
        bill.setBillId(selectMain.getId());
        bill.setBillNo(selectMain.getBillNo());
        bill.setBillDate(selectMain.getBillDate());
        bill.setBillType(selectMain.getSourceBillType() != null ? selectMain.getSourceBillType() : SgConstantsIF.BILL_SHARE_ALLOCATION_RETURN);
        bill.setChangeDate(new Date());
        bill.setServiceNode(SgConstantsIF.SERVICE_NODE_SHARE_ALLOCATION_RETURN_SUBMIT);
        bill.setSourceBillId(selectMain.getSourceBillId() != null ? selectMain.getSourceBillId() : selectMain.getId());
        bill.setSourceBillNo(StringUtils.isNotEmpty(selectMain.getSourceBillNo()) ? selectMain.getSourceBillNo() : selectMain.getBillNo());
        List<AbstractSgStorageUpdateBillItemRequest> abstractItemList = new ArrayList<>();
        for (SgBShareAllocationReturnItem item : selectItems) {
            SgStorageUpdateBillItemSaRequest itemSaRequest = new SgStorageUpdateBillItemSaRequest();
            BeanCopierUtil.copy(item, itemSaRequest);
            if (BigDecimalUtils.nonPositiveInteger(item.getQty())) {
                log.warn(LogUtil.format("SKU:{} 数量:{} 不合法",
                        "SgShareStorageService.buildStorageChangeRequest"), item.getPsCSkuId(), item.getQty());
                throw new NDSException("数量非法");
            }

            itemSaRequest.setQtyStorageChange(item.getQty().negate());
            itemSaRequest.setSgCSaStoreId(selectMain.getSgCSaStoreId());
            itemSaRequest.setSgCSaStoreEcode(selectMain.getSgCSaStoreEcode());
            itemSaRequest.setSgCSaStoreEname(selectMain.getSgCSaStoreEname());
            itemSaRequest.setBillItemId(item.getId());
            itemSaRequest.setSourceItemId(item.getSourceBillItemId());
            abstractItemList.add(itemSaRequest);
        }
        bill.setItemList(abstractItemList);
        String msgKey = SgConstants.SG_B_SHARE_ALLOCATION_RETURN + ":" + selectMain.getBillNo();
        SgStorageUpdateControlRequest controlModel = new SgStorageUpdateControlRequest();
        //自动取货 先返回缺货信息
        if (request.getIsAuto()) {
            controlModel.setPreoutOperateType(SgConstantsIF.PREOUT_RESULT_OUT_STOCK);
        }

        if (isnegative) {
            //2021-10-16 分货单退货全部允许负库存  原因：自动分货 跟 退货单 同时 审核，会导致退货不成功
            //非共享占用是否允许负库存
            controlModel.setNegativeUnsharedPreout(Boolean.TRUE);
            //共享占用是否允许负库存
            controlModel.setNegativeSharedPreout(Boolean.TRUE);
            //在途是否允许负库存
            controlModel.setNegativePrein(Boolean.TRUE);
            //在库是否允许负库存
            controlModel.setNegativeStorage(Boolean.TRUE);
            //可用在库是否允许负库存
            controlModel.setNegativeAvailable(Boolean.TRUE);
            //冻结在库是否允许负库存
            controlModel.setNegativeFreeze(Boolean.TRUE);
            //SA可用是否允许负库存
            controlModel.setNegativeSaAvailable(Boolean.TRUE);
            //SA在库是否允许负库存 2021年12月21日-产品(珂珂)：配销仓在库不可以为负数
            controlModel.setNegativeSaStorage(Boolean.FALSE);
            //SA占用是否允许负库存
            controlModel.setNegativeSaPreout(Boolean.TRUE);
            //SA锁定是否允许负库存
            controlModel.setNegativeSaFixed(Boolean.TRUE);
            //共享分配是否允许负库存
            controlModel.setNegativeSpAllocation(Boolean.TRUE);
            //共享占用是否允许负库存
            controlModel.setNegativeSpPreout(Boolean.TRUE);
        }

        sgStorageSingleUpdateRequest.setBill(bill);
        sgStorageSingleUpdateRequest.setMessageKey(msgKey);
        sgStorageSingleUpdateRequest.setControlModel(controlModel);
        sgStorageSingleUpdateRequest.setLoginUser(request.getLoginUser());
        return sgStorageSingleUpdateRequest;
    }

    /**
     * @param request:
     * @Description: 通用库存变更服务
     * @Author: hwy
     * @Date: 2021/5/28 16:38
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<com.burgeon.r3.sg.basic.model.result.SgStorageUpdateResult>
     **/
    public ValueHolderV14<SgStorageUpdateResult> updatedStorage(SgStorageSingleUpdateRequest request) {
        ValueHolderV14<SgStorageUpdateResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        SgStorageUpdateResult updateResult = new SgStorageUpdateResult();
        ValueHolderV14<SgStorageBillUpdateResult> billUpdateResult;

        if (sgStorageControlConfig.isRedisFunction()) {
            billUpdateResult = redisUpdateService.updateStorageBill(request);
        } else {
            billUpdateResult = transUpdateService.updateStorageBill(request);
        }

        if (billUpdateResult != null && billUpdateResult.getData() != null) {
            updateResult = convertSgUpdateResult(billUpdateResult.getData());
        }

        holder.setData(updateResult);

        if (billUpdateResult != null) {
            holder.setCode(billUpdateResult.getCode());
            holder.setMessage(billUpdateResult.getMessage());
        }

        return holder;
    }

    /**
     * @param updateResult: 转换返回参数
     * @Description:
     * @Author: hwy
     * @Date: 2021/5/28 16:38
     * @return: com.burgeon.r3.sg.basic.model.result.SgStorageUpdateResult
     **/
    public static SgStorageUpdateResult convertSgUpdateResult(SgStorageBillUpdateResult updateResult) {

        SgStorageUpdateResult result = new SgStorageUpdateResult();
        List<AbstractSgStorageOutStockResult> outStockItemList = new ArrayList<>();

        if (updateResult != null) {
            result.setErrorBillItemQty(updateResult.getErrorBillItemQty());
            result.setPreoutUpdateResult(updateResult.getPreoutUpdateResult());
            result.setRedisBillFtpKeyList(updateResult.getRedisBillFtpKeyList());

            //解析缺货明细列表
            if (!CollectionUtils.isEmpty(updateResult.getOutStockItemList())) {

                for (AbstractSgStorageUpdateCommonModel commonModel : updateResult.getOutStockItemList()) {
                    SgStorageOutStockLsResult outResult = new SgStorageOutStockLsResult();
                    BeanUtils.copyProperties(commonModel, outResult);
                    outStockItemList.add(outResult);
                }
                result.setOutStockItemList(outStockItemList);
            }

        }
        return result;
    }


    /**
     * 共享占用单更新sa仓
     *
     * @param saStorageRequest    新增明细
     * @param loginUser           用户
     * @param shareOutSaveRequest 主表
     * @param action              动作
     * @return 流水key
     */
    public ValueHolderV14<SgStorageBillUpdateResult> updateSaStorage(List<SgBShareOutItemSaveRequest> saStorageRequest, User loginUser,
                                                                     SgBShareOutSaveRequest shareOutSaveRequest,
                                                                     String action, Long node) {

        SgStorageBatchUpdateRequest request = new SgStorageBatchUpdateRequest();
        List<SgStorageUpdateBillRequest> billSaRequests = new ArrayList<>();
        // 需要更新的参数明细
        List<SgStorageUpdateBillItemSaRequest> itemSaRequests = new ArrayList<>();
        //库存更新sku信息
        for (SgBShareOutItemSaveRequest item : saStorageRequest) {
            SgStorageUpdateBillItemSaRequest itemSaRequest = new SgStorageUpdateBillItemSaRequest();
            BeanUtils.copyProperties(item, itemSaRequest);
            itemSaRequest.setSgCSaStoreId(item.getSgCSaStoreId());
            // 通过单据操作，判断入参是否取反
            if (SgConstantsIF.SAVE.equals(action) || SgConstantsIF.ADD.equals(action)) {

                itemSaRequest.setQtyPreoutChange(item.getQtyPreout());
                itemSaRequest.setQtyStorageChange(BigDecimal.ZERO);

                //如果是配销仓 锁定的，要传锁定量，为占用量的负数
                if (item.getSourceStorage() != null &&
                        SgConstants.SHARE_OUT_ITEM_STOCK_SOURCE_CF.equals(item.getSourceStorage())) {
                    itemSaRequest.setQtyFixedAvailableChange(item.getQtyPreout().negate());
                }

            } else if (SgConstantsIF.VOID.equals(action)) {
                itemSaRequest.setQtyPreoutChange(item.getQtyPreout().negate());

            } else if (SgConstantsIF.SUBMIT.equals(action)) {
                itemSaRequest.setQtyPreoutChange(item.getQtyPreout().negate());
                itemSaRequest.setQtyStorageChange((Optional.ofNullable(item.getQtyOut()).orElse(BigDecimal.ZERO)).negate());
            }
            itemSaRequest.setBillItemId(item.getId());
            itemSaRequests.add(itemSaRequest);
        }
        SgStoreStorageService bean = ApplicationContextHandle.getBean(SgStoreStorageService.class);
        //封装参数
        SgStorageUpdateBillRequest sgStorageSingleUpdateRequest = bean.getSgStorageSingleUpdateRequest(shareOutSaveRequest.getId()
                , shareOutSaveRequest.getBillNo(), shareOutSaveRequest.getBillDate()
                , shareOutSaveRequest.getSourceBillType(), node, false, shareOutSaveRequest.getSourceBillId()
                , shareOutSaveRequest.getSourceBillNo(), itemSaRequests, SgConstantsIF.PREOUT_RESULT_ERROR);
        billSaRequests.add(sgStorageSingleUpdateRequest);
        request.setMessageKey(SgConstants.MSG_TAG_SHARE_OUT + ":" + shareOutSaveRequest.getBillNo());
        request.setLoginUser(loginUser);
        SgStorageUpdateControlRequest controlModel = new SgStorageUpdateControlRequest();
        controlModel.setPreoutOperateType(SgConstantsIF.PREOUT_RESULT_ERROR);
        request.setControlModel(controlModel);
        request.setBillList(billSaRequests);
        if (log.isDebugEnabled()) {
            log.debug("共享占用单{},开始更新配销仓库存，入参={}", shareOutSaveRequest.getBillNo(), JSONObject.toJSONString(request));
        }
        SgStorageRedisBillBatchUpdateService service = ApplicationContextHandle.getBean(SgStorageRedisBillBatchUpdateService.class);
        ValueHolderV14<SgStorageBillUpdateResult> valueHolderV14 = service.updateStorageBatch(request);
        if (log.isDebugEnabled()) {
            log.debug("共享占用单{},结束更新配销仓库存，出参={}", shareOutSaveRequest.getBillNo(), JSONObject.toJSONString(valueHolderV14));
        }
        if (ResultCode.FAIL == valueHolderV14.getCode()) {
            AssertUtils.logAndThrow("更新配销仓库存异常：" + valueHolderV14.getMessage());
        }
        return valueHolderV14;
    }

    /**
     * 共享占用单更新渠道锁定库存仓
     *
     * @param channalLockRequest  新增明细
     * @param loginUser           用户
     * @param shareOutSaveRequest 主表
     * @return 流水key
     */
    public ValueHolderV14<SgStorageBillUpdateResult> updateChannalLockStorage(List<SgBShareOutItemSaveRequest> channalLockRequest,
                                                                              User loginUser,
                                                                              SgBShareOutSaveRequest shareOutSaveRequest,
                                                                              Long node) {
//        SgStorageBatchUpdateRequest request = new SgStorageBatchUpdateRequest();
//        List<SgStorageUpdateBillRequest> billSaRequests = new ArrayList<>();
//        // 需要更新的参数明细
//        List<SgStorageUpdateBillItemSaRequest> itemSaRequests = new ArrayList<>();
//        List<String> redisBillFtpKeyList = new ArrayList<>();
//
//        //库存更新sku信息
//        for (SgBShareOutItemSaveRequest item : channalLockRequest) {
//            SgStorageUpdateBillItemSaRequest itemSaRequest = new SgStorageUpdateBillItemSaRequest();
//            BeanUtils.copyProperties(item, itemSaRequest);
//            itemSaRequest.setSgCSaStoreId(item.getSgCSaStoreId());
//            // 取反
//            itemSaRequest.setQtyFixedAvailableChange(item.getQtyPreout().negate());
//            itemSaRequest.setQtyStorageChange(BigDecimal.ZERO);
//            itemSaRequest.setBillItemId(item.getId());
//            itemSaRequests.add(itemSaRequest);
//        }
//
//        SgStoreStorageService bean = ApplicationContextHandle.getBean(SgStoreStorageService.class);
//        //封装参数
//        SgStorageUpdateBillRequest sgStorageSingleUpdateRequest = bean.getSgStorageSingleUpdateRequest(shareOutSaveRequest.getId()
//                , shareOutSaveRequest.getBillNo(), shareOutSaveRequest.getBillDate()
//                , shareOutSaveRequest.getSourceBillType(), node, false, shareOutSaveRequest.getSourceBillId()
//                , shareOutSaveRequest.getSourceBillNo(), itemSaRequests, SgConstantsIF.PREOUT_RESULT_ERROR);
//        billSaRequests.add(sgStorageSingleUpdateRequest);
//        request.setMessageKey(SgConstants.MSG_TAG_SHARE_OUT + ":" + shareOutSaveRequest.getBillNo());
//        request.setLoginUser(loginUser);
//        SgStorageUpdateControlRequest controlModel = new SgStorageUpdateControlRequest();
//        controlModel.setPreoutOperateType(SgConstantsIF.PREOUT_RESULT_ERROR);
//        request.setControlModel(controlModel);
//        request.setBillList(billSaRequests);
//        if (log.isDebugEnabled()) {
//            log.debug("共享占用单{},开始更新渠道锁定库存，入参={}", shareOutSaveRequest.getBillNo(), JSONObject.toJSONString(request));
//        }
//        SgStorageRedisBillBatchUpdateService service = ApplicationContextHandle.getBean(SgStorageRedisBillBatchUpdateService.class);
//        ValueHolderV14<SgStorageBillUpdateResult> valueHolderV14 = service.updateStorageBatch(request);
//        if (log.isDebugEnabled()) {
//            log.debug("共享占用单{},结束更新渠道锁定库存，出参={}", shareOutSaveRequest.getBillNo(), JSONObject.toJSONString(valueHolderV14));
//        }
//        if (ResultCode.FAIL == valueHolderV14.getCode()) {
//            AssertUtils.logAndThrow("更新渠道锁定库存异常：" + valueHolderV14.getMessage());
//        }
//
//        if (valueHolderV14.getData() != null &&
//                CollectionUtils.isNotEmpty(valueHolderV14.getData().getRedisBillFtpKeyList())) {
//            redisBillFtpKeyList.addAll(valueHolderV14.getData().getRedisBillFtpKeyList());
//        }

        //更新完渠道锁定 更新cf
        ValueHolderV14<SgStorageBillUpdateResult> v14 = updateChannelFixedStorage(channalLockRequest,
                loginUser, shareOutSaveRequest, node);

        if (!v14.isOK()) {
            AssertUtils.logAndThrow("更新渠道锁定库存异常：" + v14.getMessage());
        }

        return v14;
    }

    /**
     * 共享占用单更新ChannelFixed库存仓
     *
     * @param channalLoceRequest  新增明细
     * @param loginUser           用户
     * @param shareOutSaveRequest 主表
     * @return 流水key
     */
    private ValueHolderV14<SgStorageBillUpdateResult> updateChannelFixedStorage(List<SgBShareOutItemSaveRequest> channalLoceRequest,
                                                                                User loginUser,
                                                                                SgBShareOutSaveRequest shareOutSaveRequest,
                                                                                Long node) {

        SgStorageBatchUpdateRequest request = new SgStorageBatchUpdateRequest();
        List<SgStorageUpdateBillRequest> billSaRequests = new ArrayList<>();
        // 需要更新的参数明细
        List<SgStorageUpdateBillItemCfRequest> itemCfRequests = new ArrayList<>();

        //库存更新sku信息
        for (SgBShareOutItemSaveRequest item : channalLoceRequest) {
            SgStorageUpdateBillItemCfRequest itemCfRequest = new SgStorageUpdateBillItemCfRequest();
            BeanUtils.copyProperties(item, itemCfRequest);
            itemCfRequest.setSgCSaStoreId(item.getSgCSaStoreId());
            itemCfRequest.setCpCShopId(item.getCpShopId());
            // 取反
            itemCfRequest.setQtySoldChange(item.getQtyPreout());

            itemCfRequest.setQtyStorageChange(BigDecimal.ZERO);
            itemCfRequest.setBillItemId(item.getId());
            itemCfRequests.add(itemCfRequest);
        }

        SgStoreStorageService bean = ApplicationContextHandle.getBean(SgStoreStorageService.class);
        //封装参数
        SgStorageUpdateBillRequest sgStorageSingleUpdateRequest = bean.getSgStorageSingleUpdateRequest(shareOutSaveRequest.getId()
                , shareOutSaveRequest.getBillNo(), shareOutSaveRequest.getBillDate()
                , shareOutSaveRequest.getSourceBillType(), node, false, shareOutSaveRequest.getSourceBillId()
                , shareOutSaveRequest.getSourceBillNo(), itemCfRequests, SgConstantsIF.PREOUT_RESULT_ERROR);
        billSaRequests.add(sgStorageSingleUpdateRequest);
        request.setMessageKey(SgConstants.MSG_TAG_SHARE_OUT + ":" + shareOutSaveRequest.getBillNo());
        request.setLoginUser(loginUser);
        SgStorageUpdateControlRequest controlModel = new SgStorageUpdateControlRequest();
        controlModel.setPreoutOperateType(SgConstantsIF.PREOUT_RESULT_ERROR);
        request.setControlModel(controlModel);
        request.setBillList(billSaRequests);
        if (log.isDebugEnabled()) {
            log.debug("共享占用单{},开始更新ChannelFixed库存，入参={}", shareOutSaveRequest.getBillNo(), JSONObject.toJSONString(request));
        }
        SgStorageRedisBillBatchUpdateService service = ApplicationContextHandle.getBean(SgStorageRedisBillBatchUpdateService.class);
        ValueHolderV14<SgStorageBillUpdateResult> valueHolderV14 = service.updateStorageBatch(request);
        if (log.isDebugEnabled()) {
            log.debug("共享占用单{},结束更新ChannelFixed库存，出参={}", shareOutSaveRequest.getBillNo(), JSONObject.toJSONString(valueHolderV14));
        }
        if (ResultCode.FAIL == valueHolderV14.getCode()) {
            AssertUtils.logAndThrow("更新ChannelFixed库存异常：" + valueHolderV14.getMessage());
        }


        return valueHolderV14;
    }

    /**
     * 共享占用单更新聚合仓
     *
     * @param ssStorageRequest    新增明细
     * @param loginUser           用户
     * @param shareOutSaveRequest 主表数据
     * @param action              动作
     * @return redis流水建
     */
    public ValueHolderV14<SgStorageBillUpdateResult> updateSsStorage(List<SgBShareOutItemSaveRequest> ssStorageRequest, User loginUser,
                                                                     SgBShareOutSaveRequest shareOutSaveRequest,
                                                                     String action, Long node) {
        //最外层
        SgStorageBatchUpdateRequest updateSsRequest = new SgStorageBatchUpdateRequest();
        // 需要更新的参数
        List<SgStorageUpdateBillRequest> billSaRequests = new ArrayList<>();
        // 需要更新的参数明细
        List<SgStorageUpdateBillItemSsRequest> itemSaRequests = new ArrayList<>();
        for (SgBShareOutItemSaveRequest item : ssStorageRequest) {
            SgStorageUpdateBillItemSsRequest itemSaRequest = new SgStorageUpdateBillItemSsRequest();
            BeanUtils.copyProperties(item, itemSaRequest);
            itemSaRequest.setSgCShareStoreId(shareOutSaveRequest.getSgCShareStoreId());
            itemSaRequest.setSgCShareStoreEcode(shareOutSaveRequest.getSgCShareStoreEcode());
            itemSaRequest.setSgCShareStoreEname(shareOutSaveRequest.getSgCShareStoreName());

            if (SgConstantsIF.SAVE.equals(action) || SgConstantsIF.ADD.equals(action)) {
                itemSaRequest.setQtySpPreoutChange(item.getQtyPreout());
            } else if (SgConstantsIF.VOID.equals(action)) {
                itemSaRequest.setQtySpPreoutChange(item.getQtyPreout().negate());
                //更新需要更新共享分配量
            } else if (SgConstantsIF.SUBMIT.equals(action)) {
                itemSaRequest.setQtySpPreoutChange(item.getQtyPreout().negate());
                itemSaRequest.setQtySpAllocationChange(item.getQtyOut().negate());
            }

            itemSaRequest.setBillItemId(item.getId());
            itemSaRequests.add(itemSaRequest);
        }
        // 聚合仓request 封装
        SgStoreStorageService bean = ApplicationContextHandle.getBean(SgStoreStorageService.class);
        SgStorageUpdateBillRequest sgStorageSingleUpdateRequest = bean.getSgStorageSingleUpdateRequest(shareOutSaveRequest.getId()
                , shareOutSaveRequest.getBillNo(), shareOutSaveRequest.getBillDate()
                , shareOutSaveRequest.getSourceBillType(), node, false, shareOutSaveRequest.getSourceBillId()
                , shareOutSaveRequest.getSourceBillNo(), itemSaRequests, SgConstantsIF.PREOUT_RESULT_ERROR);
        billSaRequests.add(sgStorageSingleUpdateRequest);
        // 更新共享仓库存 参数set
        updateSsRequest.setMessageKey(SgConstants.MSG_TAG_SHARE_OUT + ":" + shareOutSaveRequest.getBillNo());
        updateSsRequest.setLoginUser(loginUser);
        SgStorageUpdateControlRequest controlModel = new SgStorageUpdateControlRequest();
        controlModel.setPreoutOperateType(SgConstantsIF.PREOUT_RESULT_ERROR);
        updateSsRequest.setControlModel(controlModel);
        updateSsRequest.setBillList(billSaRequests);
        if (log.isDebugEnabled()) {
            log.debug("共享占用单{},开始更新聚合库存，入参={}", shareOutSaveRequest.getBillNo(), JSONObject.toJSONString(updateSsRequest));
        }
        SgStorageRedisBillBatchUpdateService service = ApplicationContextHandle.getBean(SgStorageRedisBillBatchUpdateService.class);
        ValueHolderV14<SgStorageBillUpdateResult> valueHolderV14 = service.updateStorageBatch(updateSsRequest);
        // redis 流水建
        if (log.isDebugEnabled()) {
            log.debug("共享占用单{},结束更新聚合库存，出参={}", shareOutSaveRequest.getBillNo(), JSONObject.toJSONString(valueHolderV14));
        }
        if (ResultCode.FAIL == valueHolderV14.getCode()) {
            AssertUtils.logAndThrow("更新聚合库存异常：" + valueHolderV14.getMessage());
        }

        return valueHolderV14;
    }

}