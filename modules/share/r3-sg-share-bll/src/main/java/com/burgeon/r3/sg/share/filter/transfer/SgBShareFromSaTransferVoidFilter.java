package com.burgeon.r3.sg.share.filter.transfer;

import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareFromSaTransfer;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareFromSaTransferMapper;
import com.burgeon.r3.sg.share.model.dto.SgBShareFromSaTransferDto;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/12/16 19:53
 */
@Slf4j
@Component
public class SgBShareFromSaTransferVoidFilter extends BaseSingleFilter<SgBShareFromSaTransferDto> {
    @Autowired
    private SgBShareFromSaTransferMapper sgShareFromSaTransferMapper;

    @Override
    public String getFilterMsgName() {
        return "配销仓到聚合仓调拨单作废";
    }

    @Override
    public Class getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgBShareFromSaTransferDto mainObject, User loginUser) {
        SgBShareFromSaTransfer fromSaTransfer = sgShareFromSaTransferMapper.selectById(mainObject.getId());
        if (Objects.isNull(fromSaTransfer)) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("当前记录已不存在！"));
        }
        if (SgShareConstants.SHARE_SA_TRANSFER_FINSH_RETURN == fromSaTransfer.getStatus() || SgShareConstants.SHARE_SA_TRANSFER_FINSH_TRANSFERS == fromSaTransfer.getStatus()) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("当前单据状态不允许作废!"));
        }
        mainObject.setStatus(SgShareConstants.BILL_STATUS_VOID);
        mainObject.setIsactive(SgConstants.IS_ACTIVE_N);
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("作废主表成功！"));
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgBShareFromSaTransferDto mainObject, User loginUser) {
        return null;
    }
}
