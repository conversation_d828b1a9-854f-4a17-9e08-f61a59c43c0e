package com.burgeon.r3.sg.share.services.transfer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.SgStorageRedisQuerySsModel;
import com.burgeon.r3.sg.basic.model.result.SgBStorageMatrixQueryResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySsExtResult;
import com.burgeon.r3.sg.basic.rpc.RpcPsService;
import com.burgeon.r3.sg.basic.services.SgBShareStorageQueryByProService;
import com.burgeon.r3.sg.basic.services.SgBShareStorageQueryService;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocation;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationItem;
import com.burgeon.r3.sg.core.model.table.share.transfer.*;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationItemMapper;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationMapper;
import com.burgeon.r3.sg.share.mapper.transfer.*;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationBillSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationSaveItemRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationSaveRequst;
import com.burgeon.r3.sg.share.model.request.transfer.SgBShareTransferImportItemRequest;
import com.burgeon.r3.sg.share.model.request.transfer.SgBShareTransferRequest;
import com.burgeon.r3.sg.share.model.request.transfer.SgBShareTransferSaveRequest;
import com.burgeon.r3.sg.share.model.result.transfer.SgShareTransferResult;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationSaveAndSubmitService;
import com.burgeon.r3.sg.store.model.request.transfer.SgBShareSaAllocationTransferRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.psext.request.SkuQueryListRequest;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 聚合仓-配销仓调拨 审核
 * <AUTHOR>
 * @Date 2021/12/10 10:54
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgBShareSaAllocationTransferSubmitService extends ServiceImpl<SgBShareSaAllocationTransferItemMapper,
        SgBShareSaAllocationTransferItem> {
    @Autowired
    private SgBShareStorageQueryService sgBShareStorageQueryService;

    @Autowired
    private SgBShareSaAllocationTransferMapper sgBShareSaAllocationTransferMapper;

    @Autowired
    private SgBShareSaAllocationImportItemMapper importItemMapper;

    @Autowired
    private SgBShareSaAllocationTransferItemMapper transferItemMapper;

    @Autowired
    private SgBShareTransferService sgShareTransferService;

    @Autowired
    private SgBShareTransferMapper sgShareTransferMapper;

    @Autowired
    private SgBShareTransferItemMapper sgShareTransferItemMapper;

    //分货单Service
    @Autowired
    private SgBShareAllocationSaveAndSubmitService sgShareAllocationSaveAndSubmitService;

    @Autowired
    private SgBShareAllocationMapper sgShareAllocationMapper;
    @Autowired
    private SgBShareAllocationItemMapper sgShareAllocationItemMapper;
    @Autowired
    private RpcPsService rpcPsService;
    @Autowired
    private SgStorageQueryService sgStorageQueryService;

    /**
     * 前端页面调用
     *
     * @param session
     * @return
     */
    public ValueHolder submit(QuerySession session) {
        SgBShareSaAllocationTransferRequest request = R3ParamUtils.parseSaveObject(session,
                SgBShareSaAllocationTransferRequest.class);
        request.setR3(true);
        SgBShareSaAllocationTransferSubmitService service =
                ApplicationContextHandle.getBean(SgBShareSaAllocationTransferSubmitService.class);
        return R3ParamUtils.convertV14WithResult(service.submitTransfer(request));

    }

    /**
     * 聚合仓配销仓调拨审核
     *
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 submitTransfer(SgBShareSaAllocationTransferRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(" Start SgBShareSaAllocationTransferSubmitService.submitTransfer request={}",
                    JSONObject.toJSONString(request));
        }
        Long start = System.currentTimeMillis();

        ValueHolderV14 submitTransferResult = new ValueHolderV14<>(ResultCode.SUCCESS, "审核成功!");
        Long objId = request.getObjId();
        User loginUser = request.getLoginUser();
        // 检查聚合仓调拨单是否存在,是否已经作废,是否已经审核
        SgBShareSaAllocationTransfer sgBShareSaAllocationTransfer = checkSgBShareSaAllocationTransfer(objId, loginUser);

        List<SgBShareSaAllocationImportItem> sgBShareSaAllocationImportItems =
                importItemMapper.selectList(new LambdaQueryWrapper<SgBShareSaAllocationImportItem>()
                        .eq(SgBShareSaAllocationImportItem::getSgBShareSaAllocationTransferId, objId));

        if (CollectionUtils.isEmpty(sgBShareSaAllocationImportItems)) {
            AssertUtils.logAndThrow("聚合仓配销仓调拨导入明细为空，不允许审核！", request.getLoginUser().getLocale());
        }
        String lockKsy = SgConstants.SG_B_SHARE_SA_ALLOCATION_TRANSFER + ":" + request.getObjId();
        SgRedisLockUtils.lock(lockKsy);
        try {
            SgBShareSaAllocationTransferSubmitService transferSubmitService =
                    ApplicationContextHandle.getBean(SgBShareSaAllocationTransferSubmitService.class);
            // 生成聚合仓配销仓调拨结果明细
            ValueHolderV14 valueHolderV14 = generateSgBShareSaAllocationItem(sgBShareSaAllocationTransfer.getId(),
                    loginUser, sgBShareSaAllocationImportItems);
            if (!valueHolderV14.isOK()) {
                AssertUtils.logAndThrow("生成聚合仓配销仓调拨结果明细异常,:" + valueHolderV14.getMessage());
            }
            List<SgBShareSaAllocationTransferItem> sgShareSaAllocationTransferItems =
                    transferItemMapper.selectList(new LambdaQueryWrapper<SgBShareSaAllocationTransferItem>()
                            .eq(SgBShareSaAllocationTransferItem::getSgBShareSaAllocationTransferId,
                                    sgBShareSaAllocationTransfer.getId())
                            .gt(SgBShareSaAllocationTransferItem::getQtyApply, BigDecimal.ZERO));
            if (CollectionUtils.isEmpty(sgShareSaAllocationTransferItems)) {
                return new ValueHolderV14<>(ResultCode.FAIL, "未生成结果明细,审核失败!");
            }
            List<SgBShareSaAllocationTransferItem> sameItems =
                    sgShareSaAllocationTransferItems.stream().filter(i -> i.getSenderShareStoreId().equals(i.getReceiverShareStoreId())).collect(Collectors.toList());
            List<SgBShareSaAllocationTransferItem> unSameItems =
                    sgShareSaAllocationTransferItems.stream().filter(i -> !i.getSenderShareStoreId().equals(i.getReceiverShareStoreId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(unSameItems)) {
                //再新增并审核聚合仓调拨单
                transferSubmitService.saveAndSubmitShareTransfer(sgBShareSaAllocationTransfer, loginUser, unSameItems);
                //新增并审核分货单
                transferSubmitService.saveShareTransFerAndShareAllocation(sgBShareSaAllocationTransfer, loginUser,
                        unSameItems, true);
            }
            if (CollectionUtils.isNotEmpty(sameItems)) {
                //新增并审核分货单
                transferSubmitService.saveShareTransFerAndShareAllocation(sgBShareSaAllocationTransfer, loginUser,
                        sameItems, false);

            }

        } catch (Exception e) {
            log.error(" SgBShareSaAllocationTransferSubmitService submitTransfer.exception:{}",
                    Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrow("审核生成聚合仓调拨单或分货单失败!失败原因:" + e.getMessage());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        Long end = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug(" Finish SgBShareSaAllocationTransferSubmitService.submitTransfer time={}", end - start);
        }
        sgBShareSaAllocationTransferMapper.updateTotQty(request.getObjId());
        return submitTransferResult;

    }

    /**
     * 向聚合仓配销仓调拨结果单 插入数据
     *
     * @param loginUser
     * @param importItemList
     * @return
     */
    private ValueHolderV14 generateSgBShareSaAllocationItem(Long objId, User loginUser,
                                                            List<SgBShareSaAllocationImportItem> importItemList) {
        //收集发货聚合仓+收货配销仓+条码 可以和商品维度合并
        Map<String, SgBShareSaAllocationTransferItem> insertResultItemsMap = new HashMap<>(16);
        //条码维度
        List<SgBShareSaAllocationImportItem> skuImportItems =
                importItemList.stream().filter(item -> SgShareConstants.SHARE_SA_ALLOCATION_TRANSFER_TRANSFER_DIMENSION_1.equals(item.getTransferDimension())).collect(Collectors.toList());
        //商品维度
        List<SgBShareSaAllocationImportItem> proImportItems =
                importItemList.stream().filter(item -> SgShareConstants.SHARE_SA_ALLOCATION_TRANSFER_TRANSFER_DIMENSION_2.equals(item.getTransferDimension())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(skuImportItems)) {

            //条码信息
            Map<String, PsCProSkuResult> skuResultMap = CommonCacheValUtils.getSkuInfo(skuImportItems.stream()
                    .map(SgBShareSaAllocationImportItem::getPsCSkuEcode).collect(Collectors.toList()));

            for (SgBShareSaAllocationImportItem importItem : skuImportItems) {
                SgBShareSaAllocationTransferItem insertResultItem = new SgBShareSaAllocationTransferItem();

                BeanUtils.copyProperties(importItem, insertResultItem);
                insertResultItem.setId(ModelUtil.getSequence(SgConstants.SG_B_SHARE_SA_ALLOCATION_TRANSFER_ITEM));
                insertResultItem.setQtyApply(importItem.getQtyApply());
                insertResultItem.setQtyAllocation(BigDecimal.ZERO);
                insertResultItem.setQtyShareTransf(BigDecimal.ZERO);

                PsCProSkuResult skuResult = skuResultMap.get(importItem.getPsCSkuEcode());
                AssertUtils.cannot(Objects.isNull(skuResult), "条码:" + importItem.getPsCSkuEcode() + "已不存在或不可用!");
                insertResultItem.setPsCSpec1Id(skuResult.getPsCSpec1objId());
                insertResultItem.setPsCSpec1Ecode(skuResult.getClrsEcode());
                insertResultItem.setPsCSpec1Ename(skuResult.getClrsEname());
                insertResultItem.setPsCSpec2Id(skuResult.getPsCSpec2objId());
                insertResultItem.setPsCSpec2Ecode(skuResult.getSizesEcode());
                insertResultItem.setPsCSpec2Ename(skuResult.getSizesEname());
                insertResultItem.setGbcode(skuResult.getGbcode());
                insertResultItem.setPsCProId(skuResult.getPsCProId());
                insertResultItem.setPsCProEname(skuResult.getPsCProEname());
                insertResultItem.setPsCProEcode(skuResult.getPsCProEcode());
                insertResultItem.setPriceList(Optional.ofNullable(skuResult.getPricelist()).orElse(BigDecimal.ZERO));

                //收集发货聚合仓+收货配销仓+条码 可以和商品维度合并
                insertResultItemsMap.put(insertResultItem.getSenderShareStoreId() + ":" +
                        insertResultItem.getReceiverSaStoreId() + ":" +
                        insertResultItem.getPsCSkuId(), insertResultItem);
            }
        }

        if (CollectionUtils.isNotEmpty(proImportItems)) {
            List<SkuQueryListRequest> skuQueryListRequests =
                    rpcPsService.rpcQuerySkuInfoByProIds(proImportItems.stream().map(SgBShareSaAllocationImportItem::getPsCProId).collect(Collectors.toList()));
            Map<Long, SkuQueryListRequest> skuInfoMap = new HashMap<>(16);
            skuQueryListRequests.forEach(e -> skuInfoMap.put(e.getId(), e));

            SgBShareStorageQueryByProService storageQueryByProService =
                    ApplicationContextHandle.getBean(SgBShareStorageQueryByProService.class);
            //聚合仓id + 商品id ，聚合仓库存
            for (SgBShareSaAllocationImportItem item : proImportItems) {
                ValueHolderV14<List<SgBStorageMatrixQueryResult>> storageByPro =
                        storageQueryByProService.queryShareStorageByPro(item.getPsCProEcode(),
                                item.getSenderShareStoreId());
                if (log.isDebugEnabled()) {
                    log.debug(" Start SgBShareSaAllocationTransferSubmitService.generateSgBShareSaAllocationItem" +
                                    ".queryShareStorageByPro vh={}",
                            JSONObject.toJSONString(storageByPro));
                }
                if (!storageByPro.isOK()) {
                    return new ValueHolderV14<>(ResultCode.FAIL,
                            "发货聚合仓:" + item.getSenderShareStoreEname() + ",商品:" + item.getPsCProEcode() +
                                    ",查询聚合仓库存失败!" + storageByPro.getMessage());
                }
                //获取 当前商品聚合仓库存结果 可用量
                List<SgBStorageMatrixQueryResult> proAllStorageMatrixQueryResults = storageByPro.getData();

                if (CollectionUtils.isEmpty(proAllStorageMatrixQueryResults)) {
                    continue;
                }
                List<SgBStorageMatrixQueryResult> storageMatrixQueryResults =
                        proAllStorageMatrixQueryResults.stream().sorted(Comparator.comparing(SgBStorageMatrixQueryResult::getQtyAvailable)).collect(Collectors.toList());
                //所有可用库存计算权重
                BigDecimal totQtyAvailable = storageMatrixQueryResults.stream()
                        .filter(o -> (null != o.getQtyAvailable() && o.getQtyAvailable().intValue() > 0))
                        .map(SgBStorageMatrixQueryResult::getQtyAvailable)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                log.info(" SgBShareSaAllocationTransferSubmitService generateSgBShareSaAllocationItem" +
                        ".totQtyAvailable:{}", totQtyAvailable);
                if (BigDecimal.ZERO.compareTo(totQtyAvailable) == 0) {
                    continue;
                }
                //收集已计算权重数
                BigDecimal countQty = BigDecimal.ZERO;
                for (int i = 0; i < storageMatrixQueryResults.size(); i++) {
                    if (countQty.compareTo(item.getQtyApply()) >= 0) {
                        break;
                    }
                    SgBStorageMatrixQueryResult storageMatrixQueryResult = storageMatrixQueryResults.get(i);
                    BigDecimal qtyShareAvailable = storageMatrixQueryResult.getQtyAvailable();
                    BigDecimal weightQty;
                    if (i == storageMatrixQueryResults.size() - 1) {
                        weightQty = item.getQtyApply().subtract(countQty);
                    } else {
                        //当前条码在此商品下的权重系数
                        BigDecimal weight = qtyShareAvailable.divide(totQtyAvailable, 2, RoundingMode.HALF_UP);
                        weightQty = item.getQtyApply().multiply(weight).setScale(0, BigDecimal.ROUND_HALF_UP);
                    }
                    //已计算权重总数>=商品总申请数，需要特殊处理，即申请数量Z= X – sum(Z)。
                    if (countQty.add(weightQty).compareTo(item.getQtyApply()) >= 0) {
                        weightQty = item.getQtyApply().subtract(countQty);
                    }
                    countQty = countQty.add(weightQty);

                    //相同条码 数量需要合并
                    SgBShareSaAllocationTransferItem sgBShareSaAllocationTransferItem =
                            insertResultItemsMap.get(item.getSenderShareStoreId() + ":" +
                                    item.getReceiverSaStoreId() + ":" +
                                    storageMatrixQueryResult.getPsCSkuId());

                    if (Objects.nonNull(sgBShareSaAllocationTransferItem)) {
                        sgBShareSaAllocationTransferItem.setQtyApply(weightQty.add(sgBShareSaAllocationTransferItem.getQtyApply()));
                        continue;
                    }
                    SkuQueryListRequest skuQueryListRequest = skuInfoMap.get(storageMatrixQueryResult.getPsCSkuId());
                    AssertUtils.cannot(Objects.isNull(skuQueryListRequest),
                            "条码:" + storageMatrixQueryResult.getPsCSkuEcode() + "已不存在或不可用!");

                    SgBShareSaAllocationTransferItem insertResultItem = new SgBShareSaAllocationTransferItem();
                    BeanUtils.copyProperties(item, insertResultItem);
                    insertResultItem.setId(ModelUtil.getSequence(SgConstants.SG_B_SHARE_SA_ALLOCATION_TRANSFER_ITEM));
                    insertResultItem.setQtyApply(weightQty);
                    insertResultItem.setQtyAllocation(BigDecimal.ZERO);
                    insertResultItem.setQtyShareTransf(BigDecimal.ZERO);
                    insertResultItem.setPsCSkuEcode(storageMatrixQueryResult.getPsCSkuEcode());
                    insertResultItem.setPsCSkuId(storageMatrixQueryResult.getPsCSkuId());
                    insertResultItem.setPsCSpec1Id(skuQueryListRequest.getColorId());
                    insertResultItem.setPsCSpec1Ecode(skuQueryListRequest.getColorEcode());
                    insertResultItem.setPsCSpec1Ename(skuQueryListRequest.getColorName());
                    insertResultItem.setPsCSpec2Id(skuQueryListRequest.getSizeId());
                    insertResultItem.setPsCSpec2Ecode(skuQueryListRequest.getSizeEcode());
                    insertResultItem.setPsCSpec2Ename(skuQueryListRequest.getSizeName());
                    insertResultItem.setGbcode(storageMatrixQueryResult.getGbcode());
                    //                    insertResultItem.setPsCBrandId(skuSaStorage.getPsCBrandId());
                    //收集发货聚合仓+收货配销仓+条码 用于下次合并
                    insertResultItemsMap.put(insertResultItem.getSenderShareStoreId() + ":" +
                            insertResultItem.getReceiverSaStoreId() + ":" +
                            insertResultItem.getPsCSkuEcode(), insertResultItem);
                }
            }
        }
        if (MapUtils.isNotEmpty(insertResultItemsMap)) {
            if (log.isDebugEnabled()) {
                log.debug(" Start SgBShareSaAllocationTransferSubmitService.generateSgBShareSaAllocationItem" +
                                ".insertResultItems={}",
                        JSONObject.toJSONString(insertResultItemsMap.values()));
            }
            Collection<SgBShareSaAllocationTransferItem> resultItems = insertResultItemsMap.values();
            //过滤申请数量等于0的结果明细
            List<SgBShareSaAllocationTransferItem> insertResultItems =
                    resultItems.stream().filter(i -> i.getQtyApply().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            List<List<SgBShareSaAllocationTransferItem>> partition = Lists.partition(insertResultItems,
                    SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
            for (List<SgBShareSaAllocationTransferItem> items : partition) {
                transferItemMapper.batchInsert(items);
            }
        } else {
            AssertUtils.logAndThrow("明细库存不足,未生成结果明细,审核失败!");
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, "聚合仓到配销仓结果明细保存成功！");
    }

    /**
     * 新增并审核分货单
     *
     * @param sgBShareSaAllocationTransfer 聚合仓到配销仓调拨单
     * @param loginUser                    用户
     * @param shareTransferResultItems     聚合仓到配销仓调拨单结果明细
     * @param shareTransferFlag            是否已经生成聚合仓调拨单
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveShareTransFerAndShareAllocation(SgBShareSaAllocationTransfer sgBShareSaAllocationTransfer,
                                                    User loginUser,
                                                    List<SgBShareSaAllocationTransferItem> shareTransferResultItems,
                                                    boolean shareTransferFlag) {
        log.info(" SgBShareSaAllocationTransferSubmitService.saveShareTransFerAndShareAllocation" +
                        ".shareTransferResultItems.size={},shareTransferFlag={}", shareTransferResultItems.size(),
                shareTransferFlag);
        if (shareTransferFlag) {
            //生成分货单
            List<SgBShareSaAllocationTransferItem> allocationTransferItems =
                    transferItemMapper.selectList(new LambdaQueryWrapper<SgBShareSaAllocationTransferItem>()
                    .eq(SgBShareSaAllocationTransferItem::getSgBShareSaAllocationTransferId,
                            sgBShareSaAllocationTransfer.getId())
                    .gt(SgBShareSaAllocationTransferItem::getQtyShareTransf, BigDecimal.ZERO));
        }

        if (CollectionUtils.isEmpty(shareTransferResultItems)) {
            return;
        }
        log.info("SgBShareSaAllocationTransferSubmitService saveShareTransFerAndShareAllocation " +
                "shareTransferResultItems size:{}", shareTransferResultItems.size());

        //新增并审核分货单
        saveAndSubmitShareAllocatio(sgBShareSaAllocationTransfer, loginUser, shareTransferResultItems);
        //查询创建的分货单和明细更新聚合仓->配销仓主表信息 结果明细信息

        if (queryCreatShareAllocatioUpdateSaAllocation(sgBShareSaAllocationTransfer, loginUser,
                shareTransferResultItems)) {
            String failMsg = "聚合仓可用量不足!";
            writeBackResultItems(loginUser, shareTransferResultItems, failMsg);
            sgBShareSaAllocationTransfer.setStatusId(loginUser.getId());
            sgBShareSaAllocationTransfer.setStatusName(loginUser.getName());
            sgBShareSaAllocationTransfer.setStatusTime(new Date());
            sgBShareSaAllocationTransferMapper.updateById(sgBShareSaAllocationTransfer);
        }

    }

    /**
     * 新增并审核聚合仓调拨单
     *
     * @param sgBShareSaAllocationTransfer      聚合仓配销仓调拨单主表信息
     * @param sgBShareSaAllocationTransferItems 聚合仓配销仓结果明细信息
     * @param loginUser
     */
    private void saveAndSubmitShareTransFer(SgBShareSaAllocationTransfer sgBShareSaAllocationTransfer,
                                            List<SgBShareSaAllocationTransferItem> sgBShareSaAllocationTransferItems,
                                            User loginUser) {

        //主表信息
        SgBShareTransferRequest transferRequest = new SgBShareTransferRequest();
        transferRequest.setBillDate(new Date());
        transferRequest.setRemark("由聚合仓到配销仓调拨单生成,单号:" + sgBShareSaAllocationTransfer.getBillNo() + "审核生成");
        transferRequest.setSourceBillId(sgBShareSaAllocationTransfer.getId());
        transferRequest.setSourceBillNo(sgBShareSaAllocationTransfer.getBillNo());
        transferRequest.setSourceBillType(SgConstantsIF.BILL_SG_B_SHARE_SA_ALLOCATION_TRANSFER);
        //查询发货聚合仓库存
        List<SgStorageRedisQuerySsModel> sgStorageRedisQueryList = new ArrayList<>();
        sgBShareSaAllocationTransferItems.forEach(item -> {
            SgStorageRedisQuerySsModel redisQuerySsModel = new SgStorageRedisQuerySsModel();
            redisQuerySsModel.setPsCSkuId(item.getPsCSkuId());
            redisQuerySsModel.setSgCShareStoreId(item.getSenderShareStoreId());
            sgStorageRedisQueryList.add(redisQuerySsModel);
        });

        ValueHolderV14<List<SgStorageRedisQuerySsExtResult>> hashMapValueHolderV14 =
                sgStorageQueryService.querySsStorageAvailableWithRedis(sgStorageRedisQueryList.stream().distinct().collect(Collectors.toList()), loginUser);
        if (log.isDebugEnabled()) {
            log.debug(" Finish SgBShareSaAllocationTransferSubmitService.saveAndSubmitShareTransFer" +
                            ".querySsStorageAvailableWithRedis.v14={}",
                    JSONObject.toJSONString(hashMapValueHolderV14));
        }
        if (!hashMapValueHolderV14.isOK()) {
            AssertUtils.logAndThrow("查询聚合仓库存失败!失败原因:" + hashMapValueHolderV14.getMessage());
        }
        //redis查询出的发货聚合仓库存map
        List<SgStorageRedisQuerySsExtResult> storageRedisQuerySsExtResults = hashMapValueHolderV14.getData();
        Map<String, SgStorageRedisQuerySsExtResult> shareStorageMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(storageRedisQuerySsExtResults)) {
            storageRedisQuerySsExtResults.forEach(result -> shareStorageMap.put(result.getSgCShareStoreId() + SgConstants.SG_CONNECTOR_MARKS_4 + result.getPsCSkuId(), result));
        }

        Map<String, List<SgBShareSaAllocationTransferItem>> shareSaStoreMap =
                sgBShareSaAllocationTransferItems.stream().collect(Collectors.groupingBy(e -> e.getSenderShareStoreId() + ":" + e.getReceiverSaStoreId()));
        try {
            for (List<SgBShareSaAllocationTransferItem> resultItems : shareSaStoreMap.values()) {
                //聚合仓调拨请求request
                SgBShareTransferSaveRequest shareTransferSaveRequest = new SgBShareTransferSaveRequest();
                shareTransferSaveRequest.setLoginUser(loginUser);

                List<SgBShareTransferImportItemRequest> importItemRequests = new ArrayList<>();
                for (SgBShareSaAllocationTransferItem resultItem : resultItems) {
                    //聚合仓下此条码的可用库存
                    SgStorageRedisQuerySsExtResult redisQuerySsResult =
                            shareStorageMap.get(resultItem.getSenderShareStoreId() + SgConstants.SG_CONNECTOR_MARKS_4 + resultItem.getPsCSkuId());
                    if (Objects.isNull(redisQuerySsResult) || !(redisQuerySsResult.getQtySsAvailable().compareTo(BigDecimal.ZERO) > 0)) {
                        continue;
                    }
                    SgBShareTransferImportItemRequest itemRequest = new SgBShareTransferImportItemRequest();
                    BeanUtils.copyProperties(resultItem, itemRequest);
                    itemRequest.setId(-1L);
                    itemRequest.setPsCSkuOrPro(resultItem.getPsCSkuEcode());
                    itemRequest.setSourceBillItemId(resultItem.getId());
                    itemRequest.setTransferDimension(SgShareConstants.SG_B_SHARE_TRANSFER_DIMENSION_SKU);
                    //可用量
                    BigDecimal qtyAvailable =
                            redisQuerySsResult.getQtySsAvailable().compareTo(resultItem.getQtyApply()) > 0 ?
                                    resultItem.getQtyApply() : redisQuerySsResult.getQtySsAvailable();
                    //聚合仓库存可用量
                    if (Objects.isNull(resultItem.getQtyShareAvailable())) {
                        resultItem.setQtyShareAvailable(redisQuerySsResult.getQtySsAvailable());
                    }
                    //新聚合仓库存数量=当前库存数量-申请数量
                    redisQuerySsResult.setQtySsAvailable(redisQuerySsResult.getQtySsAvailable().subtract(qtyAvailable));
                    itemRequest.setQty(qtyAvailable);
                    itemRequest.setPriceList(Objects.isNull(resultItem.getPriceList()) ? BigDecimal.ZERO :
                            resultItem.getPriceList());
                    importItemRequests.add(itemRequest);

                }
                shareTransferSaveRequest.setImportItemListRequest(importItemRequests);
                shareTransferSaveRequest.setSgBShareTransferRequest(transferRequest);

                log.info("SgBShareSaAllocationTransferSubmitService saveAndSubmitShareTransFer " +
                                "shareTransferSaveRequest={}",
                        JSONObject.toJSONString(shareTransferSaveRequest));

                if (CollectionUtils.isNotEmpty(importItemRequests.stream().filter(i -> i.getQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList()))) {
                    ValueHolderV14<List<SgShareTransferResult>> submitValueHolderV14 =
                            sgShareTransferService.saveAndSubmit(shareTransferSaveRequest);
                    log.info("SgBShareSaAllocationTransferSubmitService saveAndSubmitShareTransFer " +
                                    "submitValueHolderV14:{}",
                            JSONObject.toJSONString(submitValueHolderV14));
                    if (submitValueHolderV14.isOK()) {
                        if (CollectionUtils.isNotEmpty(submitValueHolderV14.getData())) {
                            transferItemMapper.updateResultItemsByShareTransferResults(submitValueHolderV14.getData());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error(" SgBShareSaAllocationTransferSubmitService saveAndSubmitShareTransFer " +
                    "exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrow("新增并审核聚合仓调拨单失败!失败原因:" + e.getMessage());
        }
    }

    /**
     * 新增并审核分货单
     *
     * @param sgBShareSaAllocationTransfer 聚合仓配销仓调拨单
     * @param user                         用户
     * @param shareTransferResultItems     收货配销仓结果明细
     */
    private void saveAndSubmitShareAllocatio(SgBShareSaAllocationTransfer sgBShareSaAllocationTransfer, User user,
                                             List<SgBShareSaAllocationTransferItem> shareTransferResultItems) {

        Map<String, List<SgBShareSaAllocationTransferItem>> shareStoreResultMap =
                shareTransferResultItems.stream().collect(Collectors.groupingBy(e -> e.getSenderShareStoreId() + ":" + e.getReceiverSaStoreId()));

        //查询收货聚合仓库存
        List<SgStorageRedisQuerySsModel> sgStorageRedisQueryList = new ArrayList<>();
        shareTransferResultItems.forEach(item -> {
            SgStorageRedisQuerySsModel redisQuerySsModel = new SgStorageRedisQuerySsModel();
            redisQuerySsModel.setPsCSkuId(item.getPsCSkuId());
            redisQuerySsModel.setSgCShareStoreId(item.getReceiverShareStoreId());
            sgStorageRedisQueryList.add(redisQuerySsModel);
        });

        ValueHolderV14<List<SgStorageRedisQuerySsExtResult>> hashMapValueHolderV14 =
                sgStorageQueryService.querySsStorageAvailableWithRedis(sgStorageRedisQueryList.stream().distinct().collect(Collectors.toList()), user);
        if (log.isDebugEnabled()) {
            log.debug(" SgBShareSaAllocationTransferSubmitService saveAndSubmitShareAllocatio " +
                    "sgBShareStorageQueryService valueHolderV14={}", JSONObject.toJSONString(hashMapValueHolderV14));
        }
        if (!hashMapValueHolderV14.isOK()) {
            AssertUtils.logAndThrow("查询聚合仓库存失败!失败原因:" + hashMapValueHolderV14.getMessage());
        }
        //redis查询出的收货聚合仓库存map
        List<SgStorageRedisQuerySsExtResult> storageRedisQuerySsExtResults = hashMapValueHolderV14.getData();
        Map<String, SgStorageRedisQuerySsExtResult> shareStorageMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(storageRedisQuerySsExtResults)) {
            storageRedisQuerySsExtResults.forEach(result -> shareStorageMap.put(result.getSgCShareStoreId() + SgConstants.SG_CONNECTOR_MARKS_4 + result.getPsCSkuId(), result));
        }

        try {
            for (List<SgBShareSaAllocationTransferItem> resultItems : shareStoreResultMap.values()) {
                SgBShareAllocationBillSaveRequst allocationBillSaveRequst = new SgBShareAllocationBillSaveRequst();
                allocationBillSaveRequst.setLoginUser(user);
                allocationBillSaveRequst.setObjId(-1L);
                List<SgBShareAllocationSaveItemRequst> allocationSaveItemRequsts = new ArrayList<>();
                SgBShareAllocationSaveRequst allocationSaveMainRequst = new SgBShareAllocationSaveRequst();
                BeanUtils.copyProperties(sgBShareSaAllocationTransfer, allocationSaveMainRequst);
                allocationSaveMainRequst.setSourceBillId(sgBShareSaAllocationTransfer.getId());
                allocationSaveMainRequst.setSourceBillNo(sgBShareSaAllocationTransfer.getBillNo());
                allocationSaveMainRequst.setSourceBillType(SgConstantsIF.BILL_SG_B_SHARE_SA_ALLOCATION_TRANSFER);
                allocationSaveMainRequst.setId(-1L);
                allocationSaveMainRequst.setBillDate(new Date());
                allocationSaveMainRequst.setRemark("由聚合仓到配销仓调拨单,单号::" + sgBShareSaAllocationTransfer.getBillNo() +
                        "审核生成");

                //主表聚合仓信息
                allocationSaveMainRequst.setSgCShareStoreId(resultItems.get(0).getReceiverShareStoreId());
                allocationSaveMainRequst.setSgCShareStoreEcode(resultItems.get(0).getReceiverShareStoreEcode());
                allocationSaveMainRequst.setSgCShareStoreEname(resultItems.get(0).getReceiverShareStoreEname());
                allocationSaveMainRequst.setStatus(SgShareConstants.BILL_STATUS_UNSUBMIT);

                // 构建request
                for (SgBShareSaAllocationTransferItem resultItem : resultItems) {
                    //聚合仓下此条码的可用库存
                    SgStorageRedisQuerySsExtResult redisQuerySsResult =
                            shareStorageMap.get(resultItem.getReceiverShareStoreId() + SgConstants.SG_CONNECTOR_MARKS_4 + resultItem.getPsCSkuId());
                    if (Objects.isNull(redisQuerySsResult) || !(redisQuerySsResult.getQtySsAvailable().compareTo(BigDecimal.ZERO) > 0)) {
                        continue;
                    }

                    // 处理明细数据
                    SgBShareAllocationSaveItemRequst saveItemRequst = new SgBShareAllocationSaveItemRequst();
                    BeanUtils.copyProperties(resultItem, saveItemRequst);
                    saveItemRequst.setId(-1L);
                    saveItemRequst.setSourceBillItemId(resultItem.getId());
                    saveItemRequst.setSgCSaStoreId(resultItem.getReceiverSaStoreId());
                    saveItemRequst.setSgCSaStoreEcode(resultItem.getReceiverSaStoreEcode());
                    saveItemRequst.setSgCSaStoreEname(resultItem.getReceiverSaStoreEname());
                    //MIN（【调拨结果明细】的“聚合调拨数量”，在发货方【聚合仓库存】的中的“可用量”）。这里即能分货多少量则分货多少量
                    BigDecimal qtyActual;
                    if (!resultItem.getSenderShareStoreId().equals(resultItem.getReceiverShareStoreId())) {
                        qtyActual =
                                redisQuerySsResult.getQtySsAvailable().compareTo(resultItem.getQtyShareTransf()) > 0 ?
                                        resultItem.getQtyShareTransf() : redisQuerySsResult.getQtySsAvailable();
                    } else {
                        qtyActual = redisQuerySsResult.getQtySsAvailable().compareTo(resultItem.getQtyApply()) > 0 ?
                                resultItem.getQtyApply() : redisQuerySsResult.getQtySsAvailable();
                    }

                    saveItemRequst.setQty(qtyActual);
                    //聚合仓库存可用量
                    if (Objects.isNull(resultItem.getQtyShareAvailable())) {
                        resultItem.setQtyShareAvailable(redisQuerySsResult.getQtySsAvailable());
                    }
                    //新聚合仓库存数量=当前库存数量-申请数量
                    redisQuerySsResult.setQtySsAvailable(redisQuerySsResult.getQtySsAvailable().subtract(qtyActual));
                    saveItemRequst.setPriceList(Objects.isNull(resultItem.getPriceList()) ? BigDecimal.ZERO :
                            resultItem.getPriceList());
                    allocationSaveItemRequsts.add(saveItemRequst);
                    resultItem.setQtyAllocation(qtyActual);

                }
                allocationBillSaveRequst.setSgBShareAllocationSaveRequst(allocationSaveMainRequst);

                allocationBillSaveRequst.setSgBShareAllocationSaveItemRequsts(allocationSaveItemRequsts);
                log.info("SgBShareSaAllocationTransferSubmitService saveAndSubmitShareAllocatio " +
                                "allocationSaveMainRequst={},allocationSaveItemRequsts.size:{}",
                        JSONObject.toJSONString(allocationSaveMainRequst),
                        allocationSaveItemRequsts.size());

                if (CollectionUtils.isNotEmpty(allocationSaveItemRequsts.stream().filter(i -> i.getQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList()))) {
                    ValueHolderV14 v14 =
                            sgShareAllocationSaveAndSubmitService.insertAndSubmitAllocation(allocationBillSaveRequst);
                    if (!v14.isOK()) {
                        log.info("SgBShareSaAllocationTransferSubmitService saveAndSubmitShareAllocatio v14" +
                                        ".getMessage={},request.size={}", JSONObject.toJSONString(v14.getMessage()),
                                allocationSaveItemRequsts.size());
                        writeBackResultItems(user, resultItems, v14.getMessage());
                    }
                }

            }
        } catch (Exception e) {
            log.error(" SgBShareSaAllocationTransferSubmitService saveAndSubmitShareAllocatio " +
                    "exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrow("新增并审核分货单失败!失败原因:" + e.getMessage());
        }

    }

    private void batchUpdateSaAllocationTransferItems(List<SgBShareSaAllocationTransferItem> updateResultItems) {
        if (CollectionUtils.isNotEmpty(updateResultItems)) {
            List<List<SgBShareSaAllocationTransferItem>> updateResultItemList =
                    Lists.partition(updateResultItems, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
            for (List<SgBShareSaAllocationTransferItem> updateResultItem : updateResultItemList) {
                this.updateBatchById(updateResultItem);
            }
        }
    }

    /**
     * 查询创建的聚合仓调拨单和明细更新配销跨聚合仓主表信息 结果明细信息
     *
     * @param sgBShareSaAllocationTransfer      配销跨聚合仓主表
     * @param user                              用户
     * @param sgBShareSaAllocationTransferItems 配销跨聚合仓结果明细信息
     */
    private boolean queryCreatShareAllocatioUpdateSaAllocation(SgBShareSaAllocationTransfer sgBShareSaAllocationTransfer, User user,
                                                               List<SgBShareSaAllocationTransferItem> sgBShareSaAllocationTransferItems) {
        log.info(" SgBShareSaAllocationTransferSubmitService.queryCreatShareAllocatioUpdateSaAllocation");
        StorageUtils.setBModelDefalutDataByUpdate(sgBShareSaAllocationTransfer, user);
        sgBShareSaAllocationTransfer.setStatus(SgShareConstants.SG_B_SHARE_SA_ALLOCATION_TRANSFER_STATUS_3);
        sgBShareSaAllocationTransfer.setStatusId(user.getId());
        sgBShareSaAllocationTransfer.setStatusName(user.getName());
        sgBShareSaAllocationTransfer.setStatusTime(new Date());
        sgBShareSaAllocationTransferMapper.updateById(sgBShareSaAllocationTransfer);
        List<SgBShareAllocation> shareAllocationList =
                sgShareAllocationMapper.selectList(new LambdaQueryWrapper<SgBShareAllocation>()
                        .eq(SgBShareAllocation::getSourceBillId, sgBShareSaAllocationTransfer.getId())
                        .eq(SgBShareAllocation::getSourceBillType,
                                SgConstantsIF.BILL_SG_B_SHARE_SA_ALLOCATION_TRANSFER));
        if (CollectionUtils.isEmpty(shareAllocationList)) {
            return true;
        }
        //查询分货单明细
        List<SgBShareAllocationItem> shareAllocationItemList = sgShareAllocationItemMapper
                .selectList(new LambdaQueryWrapper<SgBShareAllocationItem>()
                        .in(SgBShareAllocationItem::getSgBShareAllocationId,
                                shareAllocationList.stream().map(SgBShareAllocation::getId).collect(Collectors.toList())));
        if (CollectionUtils.isEmpty(shareAllocationItemList)) {
            return true;
        }

        //更新配销跨聚合仓调拨单结果明细聚合调拨数
        Map<Long, SgBShareAllocationItem> shareAllocationItemMap = shareAllocationItemList.stream()
                .collect(Collectors.toMap(SgBShareAllocationItem::getSourceBillItemId, Function.identity()));
        log.info("SgBShareSaAllocationTransferSubmitService.queryCreatShareAllocatioUpdateSaAllocation" +
                ".shareAllocationItemMap={}", JSON.toJSONString(shareAllocationItemMap));
        for (SgBShareSaAllocationTransferItem transferResultItem : sgBShareSaAllocationTransferItems) {
            SgBShareAllocationItem shareAllocationItem =
                    shareAllocationItemMap.get(transferResultItem.getId());
            StorageUtils.setBModelDefalutDataByUpdate(transferResultItem, user);
            if (Objects.isNull(shareAllocationItem)) {
                transferResultItem.setRemark("聚合仓或逻辑仓库存可用量不足,分货失败!");
                continue;
            }
            transferResultItem.setQtyAllocation(shareAllocationItem.getQty());
        }
        batchUpdateSaAllocationTransferItems(sgBShareSaAllocationTransferItems);

        return false;
    }

    /**
     * 查询创建的聚合仓调拨单和明细更新聚合仓->配销仓主表信息 结果明细信息
     *
     * @param shareSaAllocationTransfer      聚合仓->配销仓主表
     * @param user                           用户
     * @param shareSaAllocationTransferItems 配销跨聚合仓结果明细信息s
     * @return boolean
     */
    private boolean queryCreatShareTransferUpdateSaAllocation(SgBShareSaAllocationTransfer shareSaAllocationTransfer,
                                                              User user,
                                                              List<SgBShareSaAllocationTransferItem> shareSaAllocationTransferItems) {
        List<SgBShareTransfer> sgShareTransfers =
                sgShareTransferMapper.selectList(new LambdaQueryWrapper<SgBShareTransfer>()
                        .eq(SgBShareTransfer::getSourceBillId, shareSaAllocationTransfer.getId())
                        .eq(SgBShareTransfer::getSourceBillType, SgConstantsIF.BILL_SG_B_SHARE_SA_ALLOCATION_TRANSFER));
        if (CollectionUtils.isEmpty(sgShareTransfers)) {
            updateMainTableByShareTransfer(shareSaAllocationTransfer, user, BigDecimal.ZERO);
            return true;
        }
        List<SgBShareTransfer> failShareTransfer =
                sgShareTransfers.stream().filter(e -> SgShareConstants.SG_B_SHARE_SA_ALLOCATION_TRANSFER_STATUS_1.equals(e.getStatus())).collect(Collectors.toList());
        //作废未审核的聚合仓调拨单
        updateSgShareTransferStatus(SgShareConstants.SG_B_SHARE_SA_ALLOCATION_TRANSFER_STATUS_4,
                failShareTransfer.stream().map(SgBShareTransfer::getId).collect(Collectors.toList()), user);
        if (CollectionUtils.isNotEmpty(failShareTransfer)) {
            if (failShareTransfer.size() == sgShareTransfers.size()) {
                updateMainTableByShareTransfer(shareSaAllocationTransfer, user, BigDecimal.ZERO);
                return true;
            }
        }

        //查询聚合仓调拨结果明细
        List<SgBShareTransferItem> shareTransferItems = sgShareTransferItemMapper
                .selectList(new LambdaQueryWrapper<SgBShareTransferItem>()
                        .in(SgBShareTransferItem::getSgBShareTransferId,
                                sgShareTransfers.stream().map(SgBShareTransfer::getId).collect(Collectors.toList()))
                        .eq(SgBShareTransferItem::getItemStatus, SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_SUCCESS));
        if (CollectionUtils.isEmpty(shareTransferItems)) {
            updateMainTableByShareTransfer(shareSaAllocationTransfer, user, BigDecimal.ZERO);
            return true;

        }
        //总聚合调拨数量
        BigDecimal totQtyShareTransfer = shareTransferItems.stream()
                .map(SgBShareTransferItem::getQtyOut).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (log.isDebugEnabled()) {
            log.debug(" Finish SgBShareSaAllocationTransferSubmitService.queryCreatShareTransferUpdate " +
                            "shareTransferItems={}",
                    JSONObject.toJSONString(shareTransferItems));
        }
        log.info("SgBShareSaAllocationTransferSubmitService.queryCreatShareTransferUpdate.shareTransferItems " +
                        "sourceBillItemIds={}",
                shareTransferItems.stream().map(SgBShareTransferItem::getSourceBillItemId).collect(Collectors.toList()));
        //更新配销跨聚合仓调拨单结果明细聚合调拨数 筛选出状态为成功的明细
        Map<Long, List<SgBShareTransferItem>> shareTransferItemMap = shareTransferItems.stream()
                .filter(i -> SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_SUCCESS.equals(i.getItemStatus()))
                .collect(Collectors.groupingBy(SgBShareTransferItem::getSourceBillItemId));
        for (SgBShareSaAllocationTransferItem transferResultItem : shareSaAllocationTransferItems) {
            List<SgBShareTransferItem> shareTransferItemList = shareTransferItemMap.get(transferResultItem.getId());
            StorageUtils.setBModelDefalutDataByUpdate(transferResultItem, user);
            if (CollectionUtils.isEmpty(shareTransferItemList)) {
                transferResultItem.setRemark("聚合仓或逻辑仓库存可用量不足,聚合仓信息：" + transferResultItem.getSenderShareStoreEcode() +
                        "  ,收货配销仓信息:" + transferResultItem.getReceiverSaStoreEcode() + "  条码信息：" + transferResultItem.getPsCSkuEcode());
                continue;
            } else {
                transferResultItem.setRemark("");
            }
            BigDecimal sourceBillItemIdQtyOut = shareTransferItemList.stream()
                    .map(SgBShareTransferItem::getQtyOut).reduce(BigDecimal.ZERO, BigDecimal::add);
            transferResultItem.setQtyShareTransf(sourceBillItemIdQtyOut);
            log.info("SgBShareSaAllocationTransferSubmitService queryCreatShareTransferUpdateSaAllocation " +
                            "getSenderShareStoreEcode:{},getPsCSkuEcode:{},getQtyShareTransf:{}",
                    transferResultItem.getSenderShareStoreEcode(), transferResultItem.getPsCSkuEcode(),
                    transferResultItem.getQtyShareTransf());
        }
        batchUpdateSaAllocationTransferItems(shareSaAllocationTransferItems);

        updateMainTableByShareTransfer(shareSaAllocationTransfer, user, totQtyShareTransfer);
        log.info("SgBShareSaAllocationTransferSubmitService queryCreatShareTransferUpdateSaAllocation " +
                        "shareSaAllocationTransfer={}",
                JSONObject.toJSONString(shareSaAllocationTransfer));
        return false;
    }

    private void updateMainTableByShareTransfer(SgBShareSaAllocationTransfer shareSaAllocationTransfer, User user,
                                                BigDecimal totQtyShareTransfer) {
        StorageUtils.setBModelDefalutDataByUpdate(shareSaAllocationTransfer, user);
        shareSaAllocationTransfer.setStatus(SgShareConstants.SG_B_SHARE_SA_ALLOCATION_TRANSFER_STATUS_2);
        shareSaAllocationTransfer.setTotQtyShareTransf(totQtyShareTransfer);
        shareSaAllocationTransfer.setStatusId(user.getId());
        shareSaAllocationTransfer.setStatusName(user.getName());
        shareSaAllocationTransfer.setStatusTime(new Date());
        sgBShareSaAllocationTransferMapper.updateById(shareSaAllocationTransfer);
    }

    /**
     * 回写聚合仓->配销仓调拨单调拨结果明细失败原因
     *
     * @param loginUser
     * @param resultItems
     * @param failMsg
     */
    private void writeBackResultItems(User loginUser, List<SgBShareSaAllocationTransferItem> resultItems,
                                      String failMsg) {

        SgBShareSaAllocationTransferItem updateResultItem = new SgBShareSaAllocationTransferItem();
        StorageUtils.setBModelDefalutDataByUpdate(updateResultItem, loginUser);
        updateResultItem.setRemark(failMsg);
        transferItemMapper.update(updateResultItem, new UpdateWrapper<SgBShareSaAllocationTransferItem>().lambda()
                .in(SgBShareSaAllocationTransferItem::getId,
                        resultItems.stream().map(SgBShareSaAllocationTransferItem::getId).collect(Collectors.toList())));
    }

    /**
     * 数据校验
     *
     * @param objId
     * @param loginUser
     * @return
     */
    private SgBShareSaAllocationTransfer checkSgBShareSaAllocationTransfer(Long objId, User loginUser) {

        SgBShareSaAllocationTransfer sgBShareSaAllocationTransfer =
                sgBShareSaAllocationTransferMapper.selectById(objId);

        if (sgBShareSaAllocationTransfer == null) {
            AssertUtils.logAndThrow("当前记录已不存在！", loginUser.getLocale());
        } else if (sgBShareSaAllocationTransfer.getStatus().equals(SgShareConstants.SG_B_SHARE_SA_ALLOCATION_TRANSFER_STATUS_4)) {
            AssertUtils.logAndThrow("当前记录已作废，不允许审核！", loginUser.getLocale());
        } else if (sgBShareSaAllocationTransfer.getStatus().equals(SgShareConstants.SG_B_SHARE_SA_ALLOCATION_TRANSFER_STATUS_2) ||
                sgBShareSaAllocationTransfer.getStatus().equals(SgShareConstants.SG_B_SHARE_SA_ALLOCATION_TRANSFER_STATUS_3)) {
            AssertUtils.logAndThrow("当前单据状态，不允许重复审核！", loginUser.getLocale());
        }

        return sgBShareSaAllocationTransfer;
    }

    /**
     * 只要存在 对应聚合仓调拨单（聚合仓调拨单的“状态”不是已审核或部分审核 的纪录），即单据状态=未审核。 同时，需要把 聚合仓调拨单，作废掉
     *
     * @param status 状态
     * @param objIds id集合
     * @param user   用户
     */
    public void updateSgShareTransferStatus(int status, List<Long> objIds, User user) {
        if (CollectionUtils.isEmpty(objIds)) {
            return;
        }
        SgBShareTransfer transfer = new SgBShareTransfer();
        transfer.setStatus(status);
        if (status == SgShareConstants.SG_B_SHARE_SA_ALLOCATION_TRANSFER_STATUS_4) {
            transfer.setDelerEname(user.getEname());
            transfer.setDelerId(user.getId().longValue());
            transfer.setDelerName(user.getName());
            transfer.setDelTime(new Date());
        }
        sgShareTransferMapper.update(transfer, new LambdaUpdateWrapper<SgBShareTransfer>()
                .in(SgBShareTransfer::getId, objIds));
    }

    /**
     * 再新增并审核聚合仓调拨单
     *
     * @param sgBShareSaAllocationTransfer 聚合仓配销仓调拨单
     * @param loginUser                    用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAndSubmitShareTransfer(SgBShareSaAllocationTransfer sgBShareSaAllocationTransfer, User loginUser,
                                           List<SgBShareSaAllocationTransferItem> sgShareSaAllocationTransferItems) {

        log.info("SgBShareSaAllocationTransferSubmitService saveShareTransFerAndShareAllocation " +
                        "sgBShareSaAllocationTransfer={},sgShareSaAllocationTransferItems.size:{}",
                JSONObject.toJSONString(sgBShareSaAllocationTransfer), sgShareSaAllocationTransferItems.size());
        saveAndSubmitShareTransFer(sgBShareSaAllocationTransfer, sgShareSaAllocationTransferItems, loginUser);
        //更新
        //查询创建的聚合仓调拨单和明细更新聚合仓->配销仓主表信息 结果明细信息
        if (queryCreatShareTransferUpdateSaAllocation(sgBShareSaAllocationTransfer, loginUser,
                sgShareSaAllocationTransferItems)) {
            String failMsg = "聚合仓或逻辑仓库存可用量不足!";
            writeBackResultItems(loginUser, sgShareSaAllocationTransferItems, failMsg);
            sgBShareSaAllocationTransfer.setStatusId(loginUser.getId());
            sgBShareSaAllocationTransfer.setStatusName(loginUser.getName());
            sgBShareSaAllocationTransfer.setStatusTime(new Date());
            sgBShareSaAllocationTransferMapper.updateById(sgBShareSaAllocationTransfer);

        }

    }
}
