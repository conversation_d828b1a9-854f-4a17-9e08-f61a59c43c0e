package com.burgeon.r3.sg.share.services.allocation;


import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationBillSaveRequst;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Auther: chenhao
 * @Date: 2021-12-09 17:06
 * @Description:
 */

@Slf4j
@Component
public class SgBShareAllocationSaveAndSubmitService {


    /**
     * 分货单新增并审核 返回V14
     *
     * @param saveRequst saveRequst
     * @return ValueHolderV14
     */
    public ValueHolderV14 insertAndSubmitAllocation(SgBShareAllocationBillSaveRequst saveRequst) {
        if (log.isDebugEnabled()) {
            log.debug(" Start SgBShareAllocationSaveAndSubmitService.insertAndSubmitAllocation requst={}", JSONObject.toJSONString(saveRequst));
        }
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "分货单新增并审核成功！");
        SgBShareAllocationSaveAndSubmitService bean = ApplicationContextHandle.getBean(SgBShareAllocationSaveAndSubmitService.class);
        try {
            bean.insertSgShareAllocatio(saveRequst);
        } catch (Exception e) {
            log.error("SgBShareAllocationSaveAndSubmitService.insertAndSubmitAllocation error={}", e.getMessage());
            v14.setMessage("分货单新增并审核失败：" + e.getMessage());
            v14.setCode(ResultCode.FAIL);
            return v14;
        }

        return v14;
    }


    /**
     * 新增并审核 分货单 直接抛异常
     *
     * @param saveRequst 分货单
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertSgShareAllocatio(SgBShareAllocationBillSaveRequst saveRequst) {

        // 新增并审核
        SgBShareAllocationSaveService savaBean = ApplicationContextHandle.getBean(SgBShareAllocationSaveService.class);
        ValueHolderV14<SgR3BaseResult> save = savaBean.save(saveRequst);

        if (save.isOK()) {
            SgR3BaseResult data = save.getData();
            JSONObject dataJo = data.getDataJo();
            Long objid = dataJo.getLong("objid");
            SgR3BaseRequest request = new SgR3BaseRequest();
            request.setObjId(objid);
            request.setLoginUser(saveRequst.getLoginUser());
            SgBShareAllocationSubmitService bean = ApplicationContextHandle.getBean(SgBShareAllocationSubmitService.class);
            ValueHolderV14<SgR3BaseResult> v14 = bean.submitShareAllocation(request, false, false);
            if (!v14.isOK()) {
                //需要回滚新增的单据，所以异常往外抛
                AssertUtils.logAndThrow("分货单审核异常：" + v14.getMessage());
            }
        }
    }

}
