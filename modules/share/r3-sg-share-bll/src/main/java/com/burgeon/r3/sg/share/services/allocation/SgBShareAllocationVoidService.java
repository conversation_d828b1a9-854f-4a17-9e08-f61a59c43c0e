package com.burgeon.r3.sg.share.services.allocation;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocation;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationMapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @Auther: chenhao
 * @Date: 2021-05-19 11:19
 * @Description:
 */

@Slf4j
@Component
public class SgBShareAllocationVoidService {

    @Autowired
    private SgBShareAllocationMapper mapper;

    /**
     * 作废入口
     *
     * @param session 入参
     * @return 出参
     */
    ValueHolder voidShare(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        SgBShareAllocationVoidService service = ApplicationContextHandle.getBean(SgBShareAllocationVoidService.class);
        return R3ParamUtils.convertV14WithResult(service.voidShare(request));
    }

    /**
     * 作废主业务
     *
     * @param request 入参
     * @return 出参
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> voidShare(SgR3BaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBShareAllocationVoidService.voidShare.ReceiveParams:request{}"
                    , JSONObject.toJSONString(request));
        }
        String lockKsy = SgConstants.SG_B_SHARE_ALLOCATION + ":" + request.getObjId();
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "作废成功！");
        SgRedisLockUtils.lock(lockKsy);
        try {

            checkParams(request);

            SgBShareAllocation update = new SgBShareAllocation();
            StorageUtils.setBModelDefalutDataByUpdate(update, request.getLoginUser());
            update.setStatus(SgShareConstants.BILL_STATUS_VOID);
            update.setIsactive(SgConstants.IS_ACTIVE_N);
            User user = request.getLoginUser();
            // 添加作废人相关信息
            update.setDelerId(user.getId().longValue());
            update.setDelerName(user.getName());
            update.setDelerEname(user.getEname());
            update.setDelTime(new Date());
            mapper.update(update, new QueryWrapper<SgBShareAllocation>()
                    .lambda()
                    .eq(SgBShareAllocation::getId, request.getObjId()));

        } catch (Exception e) {
            AssertUtils.logAndThrowException("分货单作废异常：", e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }

        return v14;
    }

    /**
     * 参数校验
     *
     * @param request 入参
     * @return 出参
     */
    private SgBShareAllocation checkParams(SgR3BaseRequest request) {
        SgBShareAllocation sgShareAllocation = mapper.selectById(request.getObjId());
        AssertUtils.notNull(sgShareAllocation, "当前记录已不存在！");
        if (SgConstants.IS_ACTIVE_N.equals(sgShareAllocation.getIsactive())) {
            AssertUtils.logAndThrow("当前记录已作废,不允许重复作废！", request.getLoginUser().getLocale());

        } else if (SgShareConstants.BILL_STATUS_UNSUBMIT != sgShareAllocation.getStatus()) {
            AssertUtils.logAndThrow("当前单据状态不允许作废！", request.getLoginUser().getLocale());
        }
        return sgShareAllocation;
    }
}
