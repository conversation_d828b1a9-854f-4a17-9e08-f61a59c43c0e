package com.burgeon.r3.sg.share.services.ryytndistribution.scpdemand;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 计划系统需求计划导入对象
 *
 * <AUTHOR>
 * @since 2024-07-05 15:23
 */
@Data
public class ScpDemandSyncImportParam {
    /**
     * 编号 3150516
     */
    @ExcelProperty(index = 0)
    private String scpEcode;

    /**
     * 计划编号 422337810734194688
     */
    @ExcelProperty(index = 1)
    private String scpVersion;
    /**
     * 滚动版本号 DP202406W3
     */
    @ExcelProperty(index = 2)
    private String versionPlan;
    /**
     * 二级渠道编号 FH013
     */
    @ExcelProperty(index = 4)
    private String lv2ChannelCode;
    /**
     * 产品编号 110101104303
     */
    @ExcelProperty(index = 5)
    private String psCSkuEcode;
    /**
     * 时间 20240407
     */
    @ExcelProperty(index = 6)
    private String scpDate;
    /**
     * 需求数量 123
     */
    @ExcelProperty(index = 7)
    private String scpQty;
    /**
     * 月份周数 123
     */
    @ExcelProperty(index = 8)
    private String monthWeek;

}
