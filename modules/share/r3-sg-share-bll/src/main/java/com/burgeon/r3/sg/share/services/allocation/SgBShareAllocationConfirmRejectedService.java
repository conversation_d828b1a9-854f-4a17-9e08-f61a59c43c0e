package com.burgeon.r3.sg.share.services.allocation;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocation;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationMapper;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationConfirmRejectedRequest;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Auther: chenhao
 * @Date: 2022-02-17 11:18
 * @Description: 分货确认单 驳回
 */

@Slf4j
@Component
public class SgBShareAllocationConfirmRejectedService {

    @Autowired
    private SgBShareAllocationMapper allocationMapper;

    /**
     * 驳回
     *
     * @param rejectedRequest 入参
     * @return ValueHolderV14
     */
    public ValueHolderV14 rejectedShareAllocationConfirm(SgBShareAllocationConfirmRejectedRequest rejectedRequest) {

        log.info("SgBShareAllocationConfirmRejectedService.rejectedShareAllocationConfirm rejectedRequest={}", JSONObject.toJSONString(rejectedRequest));

        ValueHolderV14 v14 = checkParam(rejectedRequest);
        if (v14.isOK()) {
            Long objId = rejectedRequest.getObjId();
            User loginUser = rejectedRequest.getLoginUser();
            if (loginUser == null) {
                loginUser = SystemUserResource.getRootUser();
            }

            SgBShareAllocation update = new SgBShareAllocation();
            StorageUtils.setBModelDefalutDataByUpdate(update, loginUser);
            update.setId(objId);
            update.setStatus(SgShareConstants.BILL_STATUS_UNSUBMIT);
            allocationMapper.update(update, new UpdateWrapper<SgBShareAllocation>().lambda()
                    .set(SgBShareAllocation::getStatusId, null)
                    .set(SgBShareAllocation::getStatusTime, null)
                    .set(SgBShareAllocation::getStatusEname, null)
                    .set(SgBShareAllocation::getStatusName, null)
                    .set(SgBShareAllocation::getRejectedReason, rejectedRequest.getRejectedReason())
                    .eq(SgBShareAllocation::getId, objId));
        }

        log.info("SgBShareAllocationConfirmRejectedService.rejectedShareAllocationConfirm ValueHolderV14={}", JSONObject.toJSONString(v14));

        return v14;
    }

    /**
     * 校验参数
     *
     * @param rejectedRequest 入参
     * @return ValueHolderV14
     */
    private ValueHolderV14 checkParam(SgBShareAllocationConfirmRejectedRequest rejectedRequest) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "分货确认单驳回成功！");
        if (rejectedRequest == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("入参为空");
            return v14;
        }

        Long objId = rejectedRequest.getObjId();
        SgBShareAllocation sgShareAllocation = allocationMapper.selectById(objId);
        if (sgShareAllocation == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("当前记录已不存在！");
            return v14;
        }

        Integer status = sgShareAllocation.getStatus();

        if (SgShareConstants.BILL_STATUS_VOID == status) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("当前记录已作废，不允许驳回！！");
            return v14;
        }
        if (SgShareConstants.BILL_STATUS_SUBMIT == status) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("当前记录已审核，不允许驳回！！");
            return v14;
        }
        if (SgShareConstants.BILL_STATUS_UNSUBMIT == status) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("当前记录未审核，不允许驳回！！");
            return v14;
        }
        return v14;
    }

}
