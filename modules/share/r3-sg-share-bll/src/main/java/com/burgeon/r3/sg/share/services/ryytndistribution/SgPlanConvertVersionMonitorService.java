package com.burgeon.r3.sg.share.services.ryytndistribution;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.utils.DingTalkTokenEnum;
import com.burgeon.r3.sg.basic.utils.DingTalkUtil;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.SgCPlanConvertStatusEnum;
import com.burgeon.r3.sg.core.enums.SgDistributionTypeEnum;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCPlanConvertVersion;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 计划系统监控任务
 *
 * <AUTHOR>
 * @since 2023-11-14 15:59
 */
@Slf4j
@Service
public class SgPlanConvertVersionMonitorService {
    private static final String LOG_OBJ = "SgPlanConvertVersionMonitorService.";
    @Resource
    private SgCPlanConvertVersionService sgCPlanConvertVersionService;

    @Resource
    private SgCDepartmentMonthDemandService sgCDepartmentMonthDemandService;

    @Resource
    private SgS2SaReturnDistributionService sgS2SaReturnDistributionService;

    @Resource
    private SgBScpDemandSyncService sgBScpDemandSyncService;

//    @Resource
//    private ThreadPoolTaskExecutor commonExecutorPool;

    @Resource
    private SgCShareStoreExpiryDateAutoDistributionService sgCShareStoreExpiryDateAutoDistributionService;
    @Resource
    private SgS2SaAllAutoDistributionService sgS2SaAllAutoDistributionService;
    @Resource
    private SgS2SaTimingDistributionService sgS2SaTimingDistributionService;

    public ValueHolderV14<Void> doMonitor(JSONObject params) {
        RedisTemplate<String, String> template = RedisMasterUtils.getStrRedisTemplate();
        ValueOperations<String, String> operations = template.opsForValue();
        String activeVersion = operations.get(SgShareConstants.PLAN_RETURN_VERSION_REDIS_KEY);
        if (StringUtils.isNotEmpty(activeVersion)) {
            /*计划系统拉回补偿*/
            String compensation = sgS2SaReturnDistributionService.compensationPlanReturnSkus(SgDistributionTypeEnum.PLAN_RETURN_COMPENSATION, activeVersion);
            return new ValueHolderV14<>(ResultCode.SUCCESS, compensation);
        }

        List<SgCPlanConvertVersion> versions = sgCPlanConvertVersionService.selectByStatus(SgCPlanConvertStatusEnum.IS_NEW);
        if (CollectionUtils.isEmpty(versions)) {
            return new ValueHolderV14<>(ResultCode.SUCCESS, "暂无没有计算完成的需求记录");
        }
        SgCPlanConvertVersion newVersion = versions.get(0);
        if (versions.size() > 1) {
            /*把其余的版本都作废掉*/
            sgCPlanConvertVersionService.disableOtherVersion(newVersion.getId());
            log.info(LogUtil.format("有多余的需求计算完结记录,版本数据:{},最新版本:{}",
                    LOG_OBJ + "doMonitor"), JSONObject.toJSONString(versions), newVersion.getId());
        }

        boolean isOmsReal = params.getBooleanValue("isOmsReal");
        if (Objects.nonNull(newVersion.getDemandCount()) && isOmsReal) {
            log.warn(LogUtil.format("该版本的净需求已计算完成，版本号：{}",
                    LOG_OBJ + "doMonitor"), newVersion.getVersionBi());
            return new ValueHolderV14<>(ResultCode.FAIL, "该版本的净需求已计算完成");
        }

        /*核心逻辑，事务*/
        String msg = doExecute(newVersion, operations, isOmsReal);

        /*触发一下任务*/
//        commonExecutorPool.execute(() -> executeTask(newVersion));
        /*默认执行，除非配置了跳过任务*/
        if (StringUtils.equals(params.getString("skipOtherTask"), SgConstants.IS_ACTIVE_Y)) {
            log.info(LogUtil.format("跳过任务执行", LOG_OBJ + "doMonitor"));
        } else {
            executeTask(newVersion);
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, msg);
    }

    @Transactional(rollbackFor = Throwable.class)
    public String doExecute(SgCPlanConvertVersion newVersion, ValueOperations<String, String> operations, boolean isOmsReal) {
        int demandCount = 0;
        if (isOmsReal) {
            try {
                demandCount = sgBScpDemandSyncService.saveScpData2Demand(null, newVersion.getVersionBi());
            } catch (Exception e) {
                log.warn(LogUtil.format("计划系统共识的需求转化成部门月分货需求报错", LOG_OBJ + "doExecute"));
                throw new NDSException(e);
            }
        }

        log.info(LogUtil.format("计划系统共识的需求转化成部门月分货需求结果：{},isOmsReal",
                LOG_OBJ + "doExecute"), demandCount, isOmsReal);
        /*过期原激活的标识,变更标识为激活*/
        SgCPlanConvertVersion oldVersion = sgCPlanConvertVersionService.disableActiveVersion();
        /*作废原有需求记录,更新当前版本为有效*/
        int forbidCount = sgCDepartmentMonthDemandService.refreshByVersionBi(oldVersion, newVersion);

        newVersion.setRemark("[作废数量：" + forbidCount + "]" + newVersion.getRemark());
        newVersion.setDemandCount(demandCount);
        sgCPlanConvertVersionService.activeNewVersion(newVersion);

        operations.set(SgShareConstants.PLAN_RETURN_VERSION_REDIS_KEY, newVersion.getVersionBi(), 180, TimeUnit.MINUTES);

        return newVersion.getRemark();
    }

    private void executeTask(SgCPlanConvertVersion newVersion) {
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            ValueHolderV14<Void> executeReturn = sgS2SaReturnDistributionService.executeReturn(newVersion);
            stopWatch.stop();

            log.info(LogUtil.format("计划驱动-拉回配销仓库存结果：{}，耗时(ms)：{}", LOG_OBJ + "executeTask"),
                    JSONObject.toJSONString(executeReturn), stopWatch.getTotalTimeMillis());
        } catch (Exception e) {
            log.warn(LogUtil.format("计划驱动-拉回配销仓库存报错，异常信息:{}",
                    LOG_OBJ + "executeTask"), Throwables.getStackTraceAsString(e));
            DingTalkUtil.sendTextMsg(DingTalkTokenEnum.SG_STORAGE_ERROR, "【重要】计划驱动-拉回配销仓库存报错:" + e.getMessage());
        }

        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            ValueHolderV14<Void> expiredExecute = sgCShareStoreExpiryDateAutoDistributionService.execute();
            stopWatch.stop();
            log.info(LogUtil.format("计划驱动-大效期分货执行结果：{}，耗时(ms)：{}", LOG_OBJ + "executeTask"),
                    JSONObject.toJSONString(expiredExecute), stopWatch.getTotalTimeMillis());
        } catch (Exception e) {
            log.warn(LogUtil.format("计划驱动-大效期分货执行报错，异常信息:{}",
                    LOG_OBJ + "executeTask"), Throwables.getStackTraceAsString(e));
            DingTalkUtil.sendTextMsg(DingTalkTokenEnum.SG_STORAGE_ERROR, "【重要】计划驱动-大效期分货执行报错:" + e.getMessage());
        }

        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            ValueHolderV14 AllExecute = sgS2SaAllAutoDistributionService.execute(newVersion);
            stopWatch.stop();
            log.info(LogUtil.format("计划驱动-百分百分货执行结果：{}，耗时(ms)：{}", LOG_OBJ + "executeTask"),
                    JSONObject.toJSONString(AllExecute), stopWatch.getTotalTimeMillis());
        } catch (Exception e) {
            log.warn(LogUtil.format("计划驱动-百分百分货执行报错，异常信息:{}",
                    LOG_OBJ + "executeTask"), Throwables.getStackTraceAsString(e));
            DingTalkUtil.sendTextMsg(DingTalkTokenEnum.SG_STORAGE_ERROR, "【重要】计划驱动-百分百分货执行报错:" + e.getMessage());
        }

        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            ValueHolderV14<Void> timeExecute = sgS2SaTimingDistributionService.execute(newVersion);
            stopWatch.stop();
            log.info(LogUtil.format("计划驱动-定时分货执行结果：{}，耗时(ms)：{}", LOG_OBJ + "executeTask"),
                    JSONObject.toJSONString(timeExecute), stopWatch.getTotalTimeMillis());
        } catch (Exception e) {
            log.warn(LogUtil.format("计划驱动-定时分货执行报错，异常信息:{}",
                    LOG_OBJ + "executeTask"), Throwables.getStackTraceAsString(e));
            DingTalkUtil.sendTextMsg(DingTalkTokenEnum.SG_STORAGE_ERROR, "【重要】计划驱动-定时分货执行报错:" + e.getMessage());
        }
    }
}
