//package com.burgeon.r3.sg.share.services.ryytndistribution;
//
//import com.burgeon.r3.sg.basic.logic.SgStorageRedisQueryLogic;
//import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySsExtResult;
//import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.tuple.Pair;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.Set;
//import java.util.stream.Collectors;
//
///**
// * 定时-自动分货服务
// * </br>
// * 已弃用，详见{@link }
// *
// * <AUTHOR>
// * @since 2022-09-19 11:39
// */
//@Slf4j
//@Component
//@Deprecated
//public class SgS2SaAutoDistributionTimingTemplate extends SgS2SaAutoDistributionAbstractTemplate {
//    /**
//     * 日志OBJ
//     */
//    private static final String LOG_OBJ = "SgS2SaAutoDistributionTimingTemplate.";
//
//    /**
//     * 自动分货-公共操作
//     */
//    @Resource
//    private SgS2SaAutoDistributionManager sgS2SaAutoDistributionManager;
//
//    @Resource
//    private SgStorageRedisQueryLogic sgStorageRedisQueryLogic;
//
//    @Override
//    public SgS2SaAutoDistributionAbstractTemplate getBean() {
//        return ApplicationContextHandle.getBean(SgS2SaAutoDistributionTimingTemplate.class);
//    }
//
//    @Override
//    public Map<Long, BigDecimal> queryStorageMap(List<SgCSaStore> saStores, Long skuId) {
//        if (CollectionUtils.isEmpty(saStores)) {
//            log.warn(LogUtil.format(genLogPrefix() + "获取聚合仓库存时配销仓列表为空，商品ID：{}", LOG_OBJ + "queryStorageMap"), skuId);
//            return Collections.emptyMap();
//        }
//
//        /*聚合仓ID列表，理论上只有一个值*/
//        Set<Long> shareStoreIds = saStores.stream().map(SgCSaStore::getSgCShareStoreId)
//                .filter(Objects::nonNull).collect(Collectors.toSet());
//        List<SgStorageRedisQuerySsExtResult> retList = sgS2SaAutoDistributionManager.queryShareStorageAvailableWithRedis(shareStoreIds, skuId);
//
//        return retList.stream()
//                .collect(Collectors.toMap(SgStorageRedisQuerySsExtResult::getSgCShareStoreId, SgStorageRedisQuerySsExtResult::getQtySsAvailable, (o1, o2) -> o1));
//    }
//
//    @Override
//    public Map<Long, BigDecimal> calculateSaActualStorage(Map<Long, Set<Long>> deptSaIdsMap, Map<Long, BigDecimal> deptActualMap, Map<Long, BigDecimal> storageMap) {
//        Map<Long, BigDecimal> saActualMap = new HashMap<>();
//        for (Long deptId : deptActualMap.keySet()) {
//            /*该部门下，本次分配的总量*/
//            BigDecimal deptTotalAfter = deptActualMap.get(deptId);
//
//            /*该部门下，所有的配销仓ID*/
//            Set<Long> saStoreIds = deptSaIdsMap.get(deptId);
//            /*该部门下仅一个配销仓*/
//            if (saStoreIds.size() == 1) {
//                saActualMap.put(saStoreIds.iterator().next(), deptTotalAfter);
//                continue;
//            }
//
//            /*配销仓-实际分配的量 = (本次分配的总量) ÷ 配销仓个数*/
//            BigDecimal actual = deptTotalAfter
//                    .divide(new BigDecimal(saStoreIds.size()), RoundingMode.HALF_UP)
//                    .setScale(0, RoundingMode.HALF_UP);
//
//            BigDecimal remain = deptTotalAfter;
//            for (Long saStoreId : saStoreIds) {
//                if (BigDecimal.ZERO.compareTo(remain) == 0) {
//                    break;
//                }
//                /*配销仓-分配之前的量*/
//                if (remain.compareTo(actual) < 0) {
//                    actual = remain;
//                }
//                remain = remain.subtract(actual);
//
//                saActualMap.put(saStoreId, actual);
//            }
//        }
//        return saActualMap;
//    }
//
//    @Override
//    public Map<Long, BigDecimal> doDistribute(Long shareStoreId, Long skuId,
//                                              Map<Long, BigDecimal> saActualMap, Map<Long, BigDecimal> storageBeforeMap) {
//        /*分货*/
//        List<Pair<Long, BigDecimal>> saStoragePairList = new ArrayList<>();
//        for (Long saStoreId : saActualMap.keySet()) {
//            BigDecimal after = saActualMap.get(saStoreId);
//            if (BigDecimal.ZERO.compareTo(after) == 0) {
//                continue;
//            }
//            saStoragePairList.add(Pair.of(saStoreId, after));
//        }
//
//        log.debug(LogUtil.format(genLogPrefix() + "分货，聚合仓ID：{}，商品ID：{}，分货映射：{}", LOG_OBJ + "doDistribute"),
//                shareStoreId, skuId, saStoragePairList);
//        sgS2SaAutoDistributionManager.doAllocation(shareStoreId, skuId, saStoragePairList, genLogPrefix());
//        return null;
//    }
//
//    @Override
//    protected void modifyDemandList(Long skuId, Map<Long, Set<Long>> deptSaIdsMap, Map<Long, BigDecimal> saIdDemandQtyMap) {
//        /*定时分货所有的库存变更都通过分货单，已经在分货单驱动了二级部门需求表的变更*/
//    }
//
//
//    @Override
//    public String genLogPrefix() {
//        return "定时-";
//    }
//
//    @Override
//    public boolean isMonthDistribution() {
//        return false;
//    }
//}
