package com.burgeon.r3.sg.share.services.allocation;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.api.SgStorageUpdateCmd;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.logic.SgStorageRedisQueryLogic;
import com.burgeon.r3.sg.basic.model.SgStorageRedisQuerySsModel;
import com.burgeon.r3.sg.basic.model.request.SgStorageBatchUpdateRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageUpdateBillRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageUpdateControlRequest;
import com.burgeon.r3.sg.basic.model.request.vo.SgStorageUpdateBillItemSaRequest;
import com.burgeon.r3.sg.basic.model.result.SgStorageBillUpdateResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySsExtResult;
import com.burgeon.r3.sg.basic.services.SgBShareStorageQueryService;
import com.burgeon.r3.sg.basic.services.SgStorageRedisBillBatchUpdateService;
import com.burgeon.r3.sg.basic.utils.BigDecimalUtils;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.DingTalkUtil;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocation;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationItemMapper;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationMapper;
import com.burgeon.r3.sg.share.services.ryytndistribution.SgCDepartmentMonthDemandService;
import com.burgeon.r3.sg.store.services.SgStoreStorageService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2021-05-20 09:48
 * @Description:
 */
@Slf4j
@Component
public class SgBShareAllocationSubmitService {

    @Autowired
    private SgBShareAllocationMapper allocationMapper;
    @Autowired
    private SgBShareAllocationItemMapper itemMapper;

    @Reference(version = "1.0", group = "sg")
    SgStorageUpdateCmd storageUpdateCmd;
    @Autowired
    private SgStoreStorageService storageService;
    @Autowired
    private SgStorageRedisBillBatchUpdateService redisBillBatchUpdateService;
    @Autowired
    private SgBShareStorageQueryService storageQueryService;
    @Autowired
    private SgStorageRedisQueryLogic redisQueryLogic;


    /**
     * 提交入口
     *
     * @param session 页面参数
     * @return 返回参数
     */
    ValueHolder submitShareAllocation(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        SgBShareAllocationSubmitService service = ApplicationContextHandle.getBean(SgBShareAllocationSubmitService.class);
        return R3ParamUtils.convertV14WithResult(service.submitShareAllocation(request, Boolean.FALSE, Boolean.FALSE));
    }

    /**
     * 提交主业务
     *
     * @param request                  入参
     * @param isAuto                   是否自动分货进来的
     * @param isShareAllocationConfirm 是否分货确认单
     * @return 出参
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> submitShareAllocation(SgR3BaseRequest request, Boolean isAuto,
                                                                Boolean isShareAllocationConfirm) {
        log.info(LogUtil.format("Start SgBShareAllocationSubmitService.submitShareAllocation:request{}",
                        "SgBShareAllocationSubmitService.submitShareAllocation"),
                JSONObject.toJSONString(request));

        Long objId = request.getObjId();
        String lockKsy = SgConstants.SG_B_SHARE_ALLOCATION + ":" + objId;
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "提交成功!");
        SgRedisLockUtils.lock(lockKsy);
        List<String> redisBillFtpKeyList = new ArrayList<>();
        User loginUser = request.getLoginUser();

        Map<String, Object> map = new HashMap<>(16);

        try {
            JSONObject checkObject = checkParam(objId, isAuto, loginUser, isShareAllocationConfirm);

            SgBShareAllocation sgShareAllocation = (SgBShareAllocation) checkObject.get("sgShareAllocation");

            SgBShareAllocation update = new SgBShareAllocation();
            StorageUtils.setBModelDefalutDataByUpdate(update, loginUser);
            update.setId(objId);
            //校验中已经重新set值
            Date date = new Date();
            update.setTotQty(sgShareAllocation.getTotQty());
            update.setTotAmt(sgShareAllocation.getTotAmt());
            update.setTotQtyDiff(sgShareAllocation.getTotQtyDiff());
            update.setTotRowNum(sgShareAllocation.getTotRowNum());
            if (!isShareAllocationConfirm) {
                update.setStatusId(loginUser.getId());
                update.setStatusEname(loginUser.getEname());
                update.setStatusName(loginUser.getName());
                update.setStatusTime(date);
            }

            // 自动确认为否 程序返回 分货单需要校验，分货确认单不需要
            if (!isShareAllocationConfirm &&
                    SgConstants.IS_ACTIVE_N.equals(Optional.ofNullable(sgShareAllocation.getIsAutoConfirm()).orElse(SgConstants.IS_ACTIVE_Y))) {
                update.setStatus(SgShareConstants.BILL_STATUS_NO_CONFIRM);
                allocationMapper.updateById(update);
                return v14;
            }

            update.setConfirmId(loginUser.getId().longValue());
            update.setConfirmTime(date);
            update.setStatus(SgShareConstants.BILL_STATUS_SUBMIT);
            allocationMapper.updateById(update);

            List<SgBShareAllocationItem> items = itemMapper.selectListMaster(new QueryWrapper<SgBShareAllocationItem>()
                    .lambda()
                    .eq(SgBShareAllocationItem::getSgBShareAllocationId, objId)
                    .ne(SgBShareAllocationItem::getQty, BigDecimal.ZERO));

            if (CollectionUtils.isEmpty(items)) {
                AssertUtils.logAndThrow("无明细或者数量全为0，不允许审核");
            }

            /*判断明细中的配销仓ID是否被作废*/
            Set<Long> saStoreIds = items.stream().map(SgBShareAllocationItem::getSgCSaStoreId)
                    .filter(Objects::nonNull).collect(Collectors.toSet());
            List<SgCSaStore> saStoreList = CommonCacheValUtils.getSaStores(saStoreIds);
            Map<Long, SgCSaStore> saStoreMap = CollectionUtil.emptyIfNull(saStoreList)
                    .stream().collect(Collectors.toMap(SgCSaStore::getId, Function.identity()));
            for (SgBShareAllocationItem item : items) {
                SgCSaStore saStore = saStoreMap.get(item.getSgCSaStoreId());
                if (Objects.isNull(saStore) || SgConstants.IS_ACTIVE_N.equals(saStore.getIsactive())) {
                    log.warn(LogUtil.format("分货配销仓为空或已被作废，配销仓:{}",
                            "SgBShareAllocationSubmitService.submitShareAllocation"), item.getSgCSaStoreEcode());
                    throw new NDSException("分货配销仓为空或已被作废：" + item.getSgCSaStoreEcode());
                }
            }

            map.put(SgConstants.SG_B_SHARE_ALLOCATION_ITEM, items);
            map.put(SgConstants.SG_B_SHARE_ALLOCATION, sgShareAllocation);

            // sa仓库存更
            ValueHolderV14<SgStorageBillUpdateResult> updateSaStorage = updateSaStorage(sgShareAllocation, items, loginUser, isAuto);

            if (!updateSaStorage.isOK()) {
                AssertUtils.logAndThrow("更新配销仓库存异常");
            }

            /*更新库存数据成功后，分货需要变更二级部门月需求(操作失败不影响执行结果)*/
            try {
                SgCDepartmentMonthDemandService sgCDepartmentMonthDemandService = ApplicationContextHandle.getBean(SgCDepartmentMonthDemandService.class);
                sgCDepartmentMonthDemandService.modifyDemandByAllocationSubmit(sgShareAllocation, items, loginUser);
            } catch (Exception e) {
                log.error(LogUtil.format("分货单驱动的需求变更失败，请检查,异常：{}",
                        "SgBShareAllocationSubmitService.submitShareAllocation"), Throwables.getStackTraceAsString(e));
                DingTalkUtil.sendTextMsgWithContext("分货单驱动的需求变更失败，请检查：" + e.getMessage());
            }

            SgStorageBillUpdateResult data = updateSaStorage.getData();
            redisBillFtpKeyList = data.getRedisBillFtpKeyList();

            log.info(LogUtil.format("Start SgBShareAllocationSubmitService submitShareAllocation sgShareAllocation {} isAuto {}",
                            "SgBShareAllocationSubmitService"),
                    JSONObject.toJSONString(sgShareAllocation), isAuto);

            //自动分货需 返回库存更新结果
            SgR3BaseResult result = new SgR3BaseResult();
            JSONObject jsonObject = new JSONObject();
            if (isAuto) {
                List<SgBShareAllocationItem> errorList = (List<SgBShareAllocationItem>) checkObject.get("errorList");
                //缺货明细
                if (CollectionUtils.isNotEmpty(errorList)) {
                    jsonObject.put("errorList", errorList);
                }
            }
            jsonObject.put("redisBillFtpKey", redisBillFtpKeyList);
            result.setDataJo(jsonObject);
            v14.setData(result);
            //共享仓库存更新  共享仓库存通过计算得出，所以这里不需要更新 更新Sa就会更新共享仓库存
            //  updateSsStorage(sgShareAllocation, items, loginUser);
        } catch (Exception e) {
            //异常库存回滚
            if (CollectionUtils.isNotEmpty(redisBillFtpKeyList)) {
                StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, loginUser);
            }
            AssertUtils.logAndThrowException("分货单提交异常", e);
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }

//        // 2021-10-22 丹丹哥要求添加 是否添加到平台库存手工增量
//        SgBShareAllocationSubmitService bean = ApplicationContextHandle.getBean(SgBShareAllocationSubmitService.class);
//        bean.asyncChannelStorageIncSync(map, loginUser);

        return v14;
    }

    /**
     * 配销仓仓库存更新
     */
    private ValueHolderV14<SgStorageBillUpdateResult> updateSaStorage(SgBShareAllocation sgShareAllocation,
                                                                      List<SgBShareAllocationItem> items,
                                                                      User loginUser, Boolean isAuto) {
        SgStorageBatchUpdateRequest request = new SgStorageBatchUpdateRequest();
        List<SgStorageUpdateBillRequest> billSaRequests = new ArrayList<>();
        // 需要更新的参数明细
        List<SgStorageUpdateBillItemSaRequest> itemSaRequests = new ArrayList<>();

        //库存更新sku信息
        for (SgBShareAllocationItem item : items) {
            SgStorageUpdateBillItemSaRequest itemSaRequest = new SgStorageUpdateBillItemSaRequest();
            BeanUtils.copyProperties(item, itemSaRequest);
            itemSaRequest.setSgCSaStoreId(item.getSgCSaStoreId());
            itemSaRequest.setQtyPreoutChange(BigDecimal.ZERO);
            itemSaRequest.setQtyStorageChange(item.getQty());
            itemSaRequest.setBillItemId(item.getId());
            itemSaRequests.add(itemSaRequest);
        }

        //封装参数 无来源单据 流水中来源单据填写 本单据
        SgStorageUpdateBillRequest sgStorageSingleUpdateRequest = storageService.getSgStorageSingleUpdateRequest(sgShareAllocation.getId()
                , sgShareAllocation.getBillNo(), sgShareAllocation.getBillDate()
                , sgShareAllocation.getSourceBillType() != null ? sgShareAllocation.getSourceBillType() : SgConstantsIF.BILL_SHARE_ALLOCATION,
                SgConstantsIF.SERVICE_NODE_SHARE_ALLOCATION_SUBMIT, false,
                sgShareAllocation.getId(), sgShareAllocation.getBillNo(), itemSaRequests,
                isAuto ? SgConstantsIF.PREOUT_RESULT_OUT_STOCK : SgConstantsIF.PREOUT_RESULT_ERROR);
        //来源单据是配销仓调拨单
        if (Objects.nonNull(sgShareAllocation.getSourceBillId()) &&
                sgShareAllocation.getSourceBillId() > 0 &&
                Objects.nonNull(sgShareAllocation.getSourceBillNo())) {
            sgStorageSingleUpdateRequest = storageService.getSgStorageSingleUpdateRequest(sgShareAllocation.getId()
                    , sgShareAllocation.getBillNo(), sgShareAllocation.getBillDate()
                    , sgShareAllocation.getSourceBillType() != null ? sgShareAllocation.getSourceBillType() : SgConstantsIF.BILL_SHARE_ALLOCATION,
                    SgConstantsIF.SERVICE_NODE_SHARE_ALLOCATION_SUBMIT, false,
                    sgShareAllocation.getSourceBillId() != null ? sgShareAllocation.getSourceBillId() : sgShareAllocation.getId(),
                    StringUtils.isNotEmpty(sgShareAllocation.getSourceBillNo()) ? sgShareAllocation.getSourceBillNo() : sgShareAllocation.getBillNo(),
                    itemSaRequests,
                    isAuto ? SgConstantsIF.PREOUT_RESULT_OUT_STOCK : SgConstantsIF.PREOUT_RESULT_ERROR);
        }

        billSaRequests.add(sgStorageSingleUpdateRequest);
        request.setMessageKey(SgConstants.MSG_TAG_SHARE_ALLOCATION + ":" + sgShareAllocation.getBillNo());
        request.setLoginUser(loginUser);
        SgStorageUpdateControlRequest controlModel = new SgStorageUpdateControlRequest();
        controlModel.setPreoutOperateType(SgConstantsIF.PREOUT_RESULT_ERROR);
        // 自动分货加强版 没有库存需要爆缺货
        if (isAuto) {
            controlModel.setPreoutOperateType(SgConstantsIF.PREOUT_RESULT_OUT_STOCK);
        }

        request.setControlModel(controlModel);
        request.setBillList(billSaRequests);

        log.info(LogUtil.format("Start SgBShareAllocationSubmitService.updateSaStorage billNo{}，request={}",
                "SgBShareAllocationSubmitService.updateSaStorage"),
                sgShareAllocation.getBillNo(), JSONObject.toJSONString(request));


        ValueHolderV14<SgStorageBillUpdateResult> valueHolderV14 = redisBillBatchUpdateService.updateStorageBatch(request);

        log.info(LogUtil.format("end SgBShareAllocationSubmitService.updateSaStorage billNo{}，ValueHolderV14={}",
                "SgBShareAllocationSubmitService.updateSaStorage"),
                sgShareAllocation.getBillNo(), JSONObject.toJSONString(valueHolderV14));

        if (ResultCode.FAIL == valueHolderV14.getCode()) {
            AssertUtils.logAndThrow("更新配销仓库存异常：" + valueHolderV14.getMessage());
        }
        return valueHolderV14;
    }


    /**
     * 参数校验
     *
     * @param objid                    主表id
     * @param isAuto                   是否自动分货 true 无需校验库存
     * @param isShareAllocationConfirm 是否分货确认单
     * @return 主表信息，汇总字段会重新计算值
     */
    @Transactional(rollbackFor = Exception.class)
    public JSONObject checkParam(Long objid, Boolean isAuto, User loginUser, Boolean isShareAllocationConfirm) {
        SgBShareAllocation sgShareAllocation = allocationMapper.selectById(objid);
        AssertUtils.notNull(sgShareAllocation, "当前记录已不存在！");
        Integer status = sgShareAllocation.getStatus();
        if (SgShareConstants.BILL_STATUS_VOID == status) {
            AssertUtils.logAndThrow("当前记录已作废，不允许提交！");
        }
        //分货确认单 和 分货单 判断不一样
        if (isShareAllocationConfirm) {
            if (SgShareConstants.BILL_STATUS_UNSUBMIT == status) {
                AssertUtils.logAndThrow("当前记录未提交，不允许确认提交！");
            }
            if (SgShareConstants.BILL_STATUS_SUBMIT == status) {
                AssertUtils.logAndThrow("当前记录已提交，不允许重复提交！");
            }
        } else {
            if (SgShareConstants.BILL_STATUS_UNSUBMIT != status) {
                AssertUtils.logAndThrow("当前记录已提交，不允许重复提交！");
            }
        }


        //删除数量为0的
        itemMapper.delete(new QueryWrapper<SgBShareAllocationItem>()
                .lambda()
                .eq(SgBShareAllocationItem::getSgBShareAllocationId, objid)
                .eq(SgBShareAllocationItem::getQty, BigDecimal.ZERO));

        List<SgBShareAllocationItem> items = itemMapper.selectList(new QueryWrapper<SgBShareAllocationItem>()
                .lambda()
                .eq(SgBShareAllocationItem::getSgBShareAllocationId, objid));
        if (CollectionUtils.isEmpty(items)) {
            AssertUtils.logAndThrow("当前记录无明细，不允许提交！");
        }
        for (SgBShareAllocationItem item : items) {
            if (BigDecimalUtils.nonPositiveInteger(item.getQty())) {
                log.warn(LogUtil.format("数量非法，sku：{},数量：{}",
                        "SgBShareAllocationSubmitService.checkParam"), item.getPsCSkuId(), item.getQty());
                throw new NDSException(item.getPsCSkuId() + "数量非法" + item.getQty());
            }
        }
        //数量小于0的报警
        JSONArray errorArray = new JSONArray();
//        //聚合仓库存不足的报警
//        JSONArray shareAvailableArray = new JSONArray();
//        //数量库存不足的报警
//        JSONArray totShareAvailableArray = new JSONArray();

        BigDecimal totQty = BigDecimal.ZERO;
        BigDecimal totAmt = BigDecimal.ZERO;

        //条码集合
        List<Long> skuIds = items.stream().map(SgBShareAllocationItem::getPsCSkuId).distinct().collect(Collectors.toList());
        List<List<Long>> pageList = StorageUtils.getPageList(skuIds, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
        List<SgStorageRedisQuerySsExtResult> data = new ArrayList<>();

        for (List<Long> skuid : pageList) {
            List<SgStorageRedisQuerySsModel> modelsList = new ArrayList<>();
            for (Long id : skuid) {
                SgStorageRedisQuerySsModel sgStorageRedisQuerySsModel = new SgStorageRedisQuerySsModel();
                sgStorageRedisQuerySsModel.setSgCShareStoreId(sgShareAllocation.getSgCShareStoreId());
                sgStorageRedisQuerySsModel.setPsCSkuId(id);
                modelsList.add(sgStorageRedisQuerySsModel);
            }

            ValueHolderV14<List<SgStorageRedisQuerySsExtResult>> valueHolderV14 = redisQueryLogic.querySsStorageAvailableWithRedis(modelsList, loginUser);
            if (ResultCode.FAIL == valueHolderV14.getCode()) {
                AssertUtils.logAndThrow("查询聚合仓库存异常：" + valueHolderV14.getMessage());
            }

            if (CollectionUtils.isEmpty(valueHolderV14.getData())) {
                AssertUtils.logAndThrow("查询聚合仓库存异常：未查到聚合仓库存信息");
            }

            data.addAll(valueHolderV14.getData());
        }

        //聚合库存可用量
        Map<Long, BigDecimal> shareAvailableMap = data.stream().collect(Collectors.toMap(SgStorageRedisQuerySsExtResult::getPsCSkuId,
                SgStorageRedisQuerySsExtResult::getQtySsAvailable, (v1, v2) -> v1));

        Map<Long, List<SgBShareAllocationItem>> skuMap = items.stream()
                .collect(Collectors.groupingBy(SgBShareAllocationItem::getPsCSkuId));

        if (log.isDebugEnabled()) {
            log.debug("聚合仓库存map：{}", JSONObject.toJSONString(shareAvailableMap));
        }

        JSONObject result = new JSONObject();
        //0915 自动分货逻辑
        if (isAuto) {
            //因为是分给配销仓 不是存在缺货的情况的 需要检验一遍聚合仓可用
            //剔除超出可用的明细记录 其他提交  所有明细都超出可用 直接异常
            List<SgBShareAllocationItem> errorList = new ArrayList<>();
            List<Long> ids = new ArrayList<>();
            for (Map.Entry<Long, List<SgBShareAllocationItem>> entry : skuMap.entrySet()) {
                Long skuId = entry.getKey();
                List<SgBShareAllocationItem> itemList = entry.getValue();
                if (!shareAvailableMap.containsKey(skuId)) {
                    List<Long> itemIds = itemList.stream().map(o -> o.getId()).collect(Collectors.toList());
                    errorList.addAll(itemList);
                    ids.addAll(itemIds);
                    continue;
                }

                BigDecimal qtyShareAvailable = shareAvailableMap.get(skuId);
                for (SgBShareAllocationItem item : itemList) {
                    //可用小于0 记录当前明细
                    qtyShareAvailable = qtyShareAvailable.subtract(item.getQty());
                    if (qtyShareAvailable.compareTo(BigDecimal.ZERO) < 0) {
                        errorList.add(item);
                        ids.add(item.getId());
                        continue;
                    }
                    totAmt = totAmt.add(item.getAmt());
                    totQty = totQty.add(item.getQty());
                }
            }
            //所有明细数量都超出可用 直接报错即可
            if (errorList.size() == items.size()) {
                AssertUtils.logAndThrow("明细数量全部缺货");
            }
            //主表 数量 总行数 总挂牌金额统计
            Integer totRow = items.size() - errorList.size();
            sgShareAllocation.setTotRowNum(totRow);
            sgShareAllocation.setTotQty(totQty);
            sgShareAllocation.setTotAmt(totAmt);
            sgShareAllocation.setTotQtyApply(totQty);
            //删除不满足可用的 明细
            // 返回结果集带上缺货明细
            if (CollectionUtils.isNotEmpty(ids)) {
                result.put("errorList", errorList);
                itemMapper.deleteBatchIds(ids);
            }
        } else {
            //原始逻辑
//            for (Long skuId : skuMap.keySet()) {
//                List<SgBShareAllocationItem> itemList = skuMap.get(skuId);
//
//                BigDecimal qty = itemList.stream().map(item -> {
//                    if (item.getQty() != null) {
//                        return item.getQty();
//                    }
//                    return BigDecimal.ZERO;
//                }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
//
//                if (shareAvailableMap.containsKey(skuId)) {
//                    BigDecimal shareAvailable = shareAvailableMap.get(skuId);
//
//                    if (shareAvailable == null || shareAvailable.compareTo(qty) < 0) {
//                        totShareAvailableArray.add("条码：[" + itemList.get(0).getPsCSkuEcode() + "]数量总和[" + qty + "]大于可用库存量["
//                                + shareAvailable + "];");
//                    }
//                }
//            }

            for (SgBShareAllocationItem item : items) {
                //判断数量是否是小于0
                if (BigDecimal.ZERO.compareTo(item.getQty()) > 0) {
                    errorArray.add(item.getPsCSkuEcode());
                }

                BigDecimal shareAvailable = Optional.ofNullable(shareAvailableMap.get(item.getPsCSkuId())).orElse(BigDecimal.ZERO);
                //判断聚合库存是否充足
                if (shareAvailable.compareTo(item.getQty()) < 0) {
                    item.setQty(shareAvailable);
                    item.setQtyDiff(item.getQtyApply().subtract(shareAvailable));
                    itemMapper.updateById(item);
                }

                //计算汇总信息
                totAmt = totAmt.add(item.getAmt());
                totQty = totQty.add(item.getQty());
            }
            sgShareAllocation.setTotRowNum(items.size());
            sgShareAllocation.setTotQty(totQty);
            sgShareAllocation.setTotAmt(totAmt);
            BigDecimal totQtyApply = Optional.ofNullable(sgShareAllocation.getTotQtyApply()).orElse(BigDecimal.ZERO);
            sgShareAllocation.setTotQtyDiff(totQtyApply.subtract(totQty));
        }

        if (errorArray.size() > 0) {
            AssertUtils.logAndThrow("条码：[" + JSONArray.toJSONString(errorArray) + "]数量小于0不允许提交");
        }

        Map<Long, List<SgBShareAllocationItem>> itemMap = items.stream()
                .collect(Collectors.groupingBy(SgBShareAllocationItem::getPsCSkuId));

        // 条码实际数量总和不能大于聚合仓库存
        JSONArray errorSkuId = new JSONArray();
        for (Long skuId : itemMap.keySet()) {
            List<SgBShareAllocationItem> itemList = itemMap.get(skuId);
            BigDecimal itemQty = itemList.stream().map(SgBShareAllocationItem::getQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            if (!shareAvailableMap.containsKey(skuId)) {
                errorSkuId.add("条码：[" + itemList.get(0).getPsCSkuEcode() + "]数量大于聚合仓库存:0");
                continue;
            }

            BigDecimal shareAvailable = shareAvailableMap.get(skuId);
            if (itemQty.compareTo(shareAvailable) > 0) {
                errorSkuId.add("条码：[" + itemList.get(0).getPsCSkuEcode() + "]数量大于聚合仓库存:" + shareAvailable);
            }
        }

        if (errorSkuId.size() > 0) {
            AssertUtils.logAndThrow(JSONArray.toJSONString(errorSkuId));
        }

        result.put("sgShareAllocation", sgShareAllocation);

        return result;
    }
}
