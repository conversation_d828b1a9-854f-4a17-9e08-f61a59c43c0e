package com.burgeon.r3.sg.share.services.ryytndistribution;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgBCommonStorageQtyQueryResult;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgBScpDemandDailyReport;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCPlanConvertVersion;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgBScpDemandDailyReportMapper;
import com.burgeon.r3.sg.share.services.ryytndistribution.dto.SgBScpDemandDailyReportQueryData;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpCDistributionOrganizationQueryCmd;
import com.jackrain.nea.cpext.model.table.CpCDistributionOrganization;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 日分货报表
 *
 * <AUTHOR>
 * @since 2024-07-15 14:49
 */
@Slf4j
@Component
public class SgBScpDemandDailyReportService {
    private final static String LOG_OBJ = "SgBScpDemandDailyReportService.";

    @Resource
    private SgBScpDemandDailyReportMapper sgBScpDemandDailyReportMapper;

    @Resource
    private SgCDepartmentMonthDemandService sgCDepartmentMonthDemandService;

    @Resource
    private SgCPlanConvertVersionService sgCPlanConvertVersionService;

    @Resource
    private SgBScpDemandDailyReportExtService sgBScpDemandDailyReportExtService;

    @Resource
    private SgBScpDemandSyncUsedQtyService sgBScpDemandSyncUsedQtyService;

    @NacosValue(value = "${r3.sg.adb.scsg:test_rpt_sc_sg}", autoRefreshed = true)
    private String sgAdbPrefix;

    @NacosValue(value = "${r3.sg.adb.order:test_rpt_order}", autoRefreshed = true)
    private String orderAdbPrefix;

    @DubboReference(group = "cp-ext", version = "1.0")
    private CpCDistributionOrganizationQueryCmd cpCDistributionOrganizationQueryCmd;

    /**
     * 报表计算月份跨度
     */
    @NacosValue(value = "${sg.scp.demand.daily.report.month:2}", autoRefreshed = true)
    private int reportMonthSpan;

    public ValueHolderV14 execute() {
        /*获取当前版本号*/
        SgCPlanConvertVersion activeVersion = sgCPlanConvertVersionService.selectActive();

        Date versionDay = DateUtil.beginOfDay(activeVersion.getModifieddate());
        Date startDay = DateUtil.beginOfMonth(activeVersion.getModifieddate());
        Date endDay = DateUtils.addMonths(startDay, reportMonthSpan);
        log.info(LogUtil.format("开始执行日分货报表任务，版本信息:{},开始时间：{},结束时间：{}，版本时间：{}",
                LOG_OBJ + "execute"), JSON.toJSONString(activeVersion), startDay, endDay, versionDay);

        List<String> skuEcodes = sgCDepartmentMonthDemandService.queryEcode(activeVersion.getVersionBi(),
                DateUtil.format(startDay, "yyyy-MM-dd"), DateUtil.format(endDay, "yyyy-MM-dd"));
        /*参与分货的部门*/
        List<Long> lv2Channels = sgCDepartmentMonthDemandService.queryLvChannelEcode(activeVersion.getVersionBi(),
                DateUtil.format(startDay, "yyyy-MM-dd"), DateUtil.format(endDay, "yyyy-MM-dd"));
        if (CollectionUtils.isEmpty(skuEcodes) || CollectionUtils.isEmpty(lv2Channels)) {
            return new ValueHolderV14(ResultCode.FAIL, "未找到该版本部门月需求");
        }

        /*判断该版本是否首次计算*/
        boolean isFirst = CollectionUtils.isEmpty(sgBScpDemandDailyReportMapper.selectList(new QueryWrapper<SgBScpDemandDailyReport>().lambda()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgBScpDemandDailyReport::getVersionBi, activeVersion.getVersionBi())
                .last("LIMIT 1")));

        /*渠道编码-ID映射*/
        Map<String, Long> channelIdMap = queryChannelIdMap(lv2Channels);

        /*刷新拆包品日生产计划*/
        SgCMonthProductionPlanService sgCMonthProductionPlanService = ApplicationContextHandle.getBean(SgCMonthProductionPlanService.class);
        sgCMonthProductionPlanService.refreshPlanData(activeVersion.getVersionBi(), DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN));

        SgBScpDemandDailyReportQueryData queryData =
                sgBScpDemandDailyReportExtService.refreshAndQueryQtyMap(activeVersion.getVersionBi(), startDay, endDay, skuEcodes);

        int total = 0;
        /*逐个SKU处理*/
        for (String skuEcode : skuEcodes) {
            try {
                SgBScpDemandDailyReportService service = ApplicationContextHandle.getBean(SgBScpDemandDailyReportService.class);
                int size = service.executeBySku(startDay, endDay, activeVersion.getVersionBi(), versionDay,
                        isFirst, skuEcode, queryData, channelIdMap);
                log.info(LogUtil.format("单个SKU执行完成，SKU编码:{}，影响行数:{}", LOG_OBJ + "execute"), skuEcode, size);

                total += size;
            } catch (Exception e) {
                log.warn(LogUtil.format("日分货报表按SKU执行时报错，SKU编码:{}，错误信息:{}",
                        LOG_OBJ + "execute"), skuEcode, Throwables.getStackTraceAsString(e));
            }
        }

        return new ValueHolderV14(ResultCode.SUCCESS, "执行成功，共影响行数：" + total);
    }

    /**
     * 逐个SKU处理
     *
     * @param startDay     开始日期
     * @param endDay       结束日期
     * @param versionBi    版本号
     * @param isFirst      是否首次计算
     * @param skuEcode     SKU编码
     * @param queryData    通用数据查询结果
     * @param channelIdMap 渠道编码与渠道ID映射
     */
    @Transactional(rollbackFor = Throwable.class)
    public int executeBySku(Date startDay, Date endDay, String versionBi, Date versionDay,
                            boolean isFirst, String skuEcode, /*boolean isWhiteSku,*/
                            SgBScpDemandDailyReportQueryData queryData, Map<String, Long> channelIdMap) {
        Long psCSkuId = queryData.getSkuEcodeIdMap().get(skuEcode);
        if (Objects.isNull(psCSkuId)) {
            log.info(LogUtil.format("未找到该SKU编码对应的ID,versionBi:{},skuEcode:{},startDay:{},endDay:{}",
                    LOG_OBJ + "executeBySku"), versionBi, skuEcode, startDay, endDay);
            return 0;
        }

        List<SgBScpDemandDailyReport> reportList = new ArrayList<>();
        List<SgBScpDemandDailyReport> beforeList = new ArrayList<>();
        if (!isFirst) {
            reportList = sgBScpDemandDailyReportMapper.selectList(new QueryWrapper<SgBScpDemandDailyReport>().lambda()
                    .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                    .eq(SgBScpDemandDailyReport::getVersionBi, versionBi)
                    .eq(SgBScpDemandDailyReport::getPsCSkuId, psCSkuId)
                    .orderByAsc(SgBScpDemandDailyReport::getPlanDate));

            if (!CollectionUtils.isEmpty(reportList)) {
                beforeList = reportList.stream().map(o -> BeanUtil.copyProperties(o, SgBScpDemandDailyReport.class)).collect(Collectors.toList());
            } else {
                isFirst = true;
            }
        }

        /*构建报表数据：需求量、实际分货量、已用量、已用量(总)、共享已发量*/
        buildReportList(reportList, isFirst, /*isWhiteSku, */versionBi, versionDay,
                psCSkuId, skuEcode, startDay, endDay, channelIdMap);

        /*滚动计算：预计分货、预计分货调整、期初、剩余可发*/
        calculateQtyByRolling(versionBi, reportList, startDay, endDay, psCSkuId, skuEcode, queryData, versionDay);

        if (isFirst) {
            StorageBasicUtils.batchInsertList(sgBScpDemandDailyReportMapper, reportList,
                    1000, "批量插入日分货报表出错", R3SystemUserResource.getSystemRootUser());
            log.info(LogUtil.format("该SKU为首次计算，SKUID:{}.SKU编码:{}，影响行数:{}",
                    LOG_OBJ + "executeBySku"), psCSkuId, skuEcode, reportList.size());
            return reportList.size();
        } else {
            int count = compareAndUpdate(beforeList, reportList);
            log.info(LogUtil.format("该SKU为更新，SKUID:{}.SKU编码:{}，影响行数:{}",
                    LOG_OBJ + "executeBySku"), psCSkuId, skuEcode, count);
            return count;
        }
    }

    private int compareAndUpdate(List<SgBScpDemandDailyReport> oldList, List<SgBScpDemandDailyReport> reportList) {
        int count = 0;

        List<SgBScpDemandDailyReport> updateList = new ArrayList<>();

        Map<Long, SgBScpDemandDailyReport> oldIdMap = ListUtils.emptyIfNull(oldList).stream()
                .collect(Collectors.toMap(SgBScpDemandDailyReport::getId, Function.identity()));
        for (SgBScpDemandDailyReport newReport : reportList) {
            SgBScpDemandDailyReport oldReport = oldIdMap.get(newReport.getId());
            if (Objects.isNull(oldReport)) {
                log.warn(LogUtil.format("计算错误，前后数据不匹配，计算后:{}", LOG_OBJ + "compareAndUpdate"),
                        JSON.toJSONString(oldReport));
                continue;
            }

            if (!equalsQty(oldReport.getExpectQtyOriginal(), newReport.getExpectQtyOriginal())
                    || !equalsQty(oldReport.getExpectQty(), newReport.getExpectQty())
                    || !equalsQty(oldReport.getExpectAdjustQty(), newReport.getExpectAdjustQty())
                    || !equalsQty(oldReport.getInitQty(), newReport.getInitQty())
                    || !equalsQty(oldReport.getDemandQty(), newReport.getDemandQty())
                    || !equalsQty(oldReport.getDemandQtyActual(), newReport.getDemandQtyActual())
                    || !equalsQty(oldReport.getActualAllocationQty(), newReport.getActualAllocationQty())
                    || !equalsQty(oldReport.getAlreadyUsedQtyTotal(), newReport.getAlreadyUsedQtyTotal())
                    || !equalsQty(oldReport.getAlreadyUsedQty(), newReport.getAlreadyUsedQty())
                    /*|| !equalsQty(oldReport.getResidualQtyTotal(), newReport.getResidualQtyTotal())*/
                    || !equalsQty(oldReport.getResidualQty(), newReport.getResidualQty())) {
                StorageUtils.setBModelDefalutDataByUpdate(newReport, R3SystemUserResource.getSystemRootUser());
                count++;

                updateList.add(newReport);
            }
        }

        if (!CollectionUtils.isEmpty(updateList)) {
            List<List<SgBScpDemandDailyReport>> list = StorageUtils.getPages(updateList, 1000);
            for (List<SgBScpDemandDailyReport> reports : list) {
                int modifyCount = sgBScpDemandDailyReportMapper.updateQtyBatchById(reports);
                if (modifyCount != reports.size()) {
                    log.warn(LogUtil.format("批量更新日分货报表出错，修改数量:{},期望数量:{}",
                            LOG_OBJ + "compareAndUpdate"), modifyCount, reports.size());
                }
            }
        }

        return count;
    }

    /**
     * 按天滚动计算：预计分货、预计分货调整、剩余可发
     *
     * @param versionBi  版本号
     * @param reportList 日分货报表
     * @param startDay   开始日期
     * @param endDay     结束日期
     * @param psCSkuId   SKUID
     * @param skuEcode   SKU编码
     * @param queryData  通用数据查询结果
     * @param versionDay
     */
    private void calculateQtyByRolling(String versionBi, List<SgBScpDemandDailyReport> reportList,
                                       Date startDay, Date endDay,
                                       Long psCSkuId, String skuEcode,
                                       SgBScpDemandDailyReportQueryData queryData,
                                       Date versionDay) {
        /*每天计划入库量*/
        Map<Date, BigDecimal> planQtyMap = queryData.getPlanQtyMapBySku().getOrDefault(skuEcode, MapUtil.empty());
        /*每天实际入库量*/
        Map<Date, BigDecimal> actualQtyMap = queryData.getActualQtyMapBySku().getOrDefault(skuEcode, MapUtil.empty());

        /*按天分组，逐天计算*/
        Map<Date, List<SgBScpDemandDailyReport>> dateListMap = reportList.stream()
                .collect(Collectors.groupingBy(SgBScpDemandDailyReport::getPlanDate));

        /*每个渠道当前剩余未消耗完的生产计划量-的数据列表*/
        List<SgBScpDemandDailyReportCalculateDto> expectQtyList = new ArrayList<>();
        /*每个渠道当前剩余未消耗完的实际入库量-的数据列表*/
        List<SgBScpDemandDailyReportCalculateDto> actualQtyList = new ArrayList<>();

        Map<Long, BigDecimal> initQtyByChannelMap = new HashMap<>();
        boolean isFirst = true;

        /*前一天剩余的计划入库量*/
        BigDecimal residualPlanQty = BigDecimal.ZERO;
        /*前一天剩余的实际入库量*/
        BigDecimal residualActualQty = BigDecimal.ZERO;
        Date executeDay = startDay;
        while (executeDay.before(endDay)) {
            List<SgBScpDemandDailyReport> dayReportList = dateListMap.get(executeDay);

            /*今天之前的数据，先算期初：因为其实还没有计算【预计分货量】*/
//            if (executeDay.before(today)) {
//                /*期初、剩余可发*/
//                initQtyByChannelMap = calculateInitAndResidualQty(executeDay, psCSkuId, dayReportList, isFirst, initQtyByChannelMap);
//            }

            /*版本日与版本日前，计算期初、剩余可发*/
            if (!executeDay.after(versionDay)) {
                /*期初、剩余可发*/
                Map<Long, BigDecimal> orgQtyMap = calculateExpectQtyBeforeVersionDay(isFirst, initQtyByChannelMap,
                        dayReportList, psCSkuId,
                        executeDay, versionDay);
                if (DateUtil.isSameDay(executeDay, versionDay)) {
                    /*removeExpect(planQtyMap, initExpectQty);*/
                    removeDemand(dateListMap, orgQtyMap, versionDay, endDay);
                }
            }

            /*版本日之后*/
            if (executeDay.after(versionDay)) {
                /*预计分货*/
                residualPlanQty = calculateQtyByRatio(residualPlanQty,
                        expectQtyList,
                        planQtyMap.getOrDefault(executeDay, BigDecimal.ZERO),
                        dayReportList,
                        SgBScpDemandDailyReport::getExpectQtyOriginal, SgBScpDemandDailyReport::setExpectQtyOriginal);

                /*预计分货调整*/
                residualActualQty = calculateQtyByRatio(residualActualQty,
                        actualQtyList,
                        actualQtyMap.getOrDefault(executeDay, BigDecimal.ZERO),
                        dayReportList,
                        SgBScpDemandDailyReport::getExpectAdjustQty, SgBScpDemandDailyReport::setExpectAdjustQty);
            }

//            /*今天之后的期初，在计算完预计分货后计算*/
//            if (!executeDay.before(today)) {
//                /*期初、剩余可发*/
//                initQtyByChannelMap = calculateInitAndResidualQty(executeDay, psCSkuId, dayReportList, isFirst, initQtyByChannelMap);
//            }

            /*计算：【预计分货量(替换)】【期初】【剩余可发】*/
            processDayLast(versionBi, psCSkuId, executeDay, initQtyByChannelMap, dayReportList, versionDay);

            isFirst = false;
            executeDay = DateUtils.addDays(executeDay, 1);
        }
    }

    /**
     * 根据分货日当天的预计分货量，扣减掉月生产计划中的量(按日期从小到大扣减)
     *
     * @param dateListMap 分货数据
     * @param orgQtyMap   分货日当天的预计分货量
     * @param beginDay    开始扣减日期（从开始日）
     * @param endDay      结束日（到这一天没扣完也不扣了）
     */
    private void removeDemand(Map<Date, List<SgBScpDemandDailyReport>> dateListMap, Map<Long, BigDecimal> orgQtyMap,
                              Date beginDay, Date endDay) {
        while (beginDay.before(endDay)) {
            /*标志，记录是否所有组织的数量都已经为零*/
            boolean allOrgQtyZero = true;

            List<SgBScpDemandDailyReport> dailyReports = dateListMap.getOrDefault(beginDay, ListUtil.empty());
            Map<Long, List<SgBScpDemandDailyReport>> orgGroupList = dailyReports.stream()
                    .collect(Collectors.groupingBy(SgBScpDemandDailyReport::getCpCDistributionOrgId));
            for (Map.Entry<Long, List<SgBScpDemandDailyReport>> orgEntry : orgGroupList.entrySet()) {
                BigDecimal qty = orgQtyMap.getOrDefault(orgEntry.getKey(), BigDecimal.ZERO);
                if (BigDecimal.ZERO.compareTo(qty) >= 0) {
                    continue;
                }
                allOrgQtyZero = false;

                for (SgBScpDemandDailyReport report : orgEntry.getValue()) {
                    BigDecimal demandQty = report.getDemandQtyActual();
                    if (BigDecimal.ZERO.compareTo(demandQty) >= 0) {
                        continue;
                    }
                    if (qty.compareTo(demandQty) >= 0) {
                        qty = qty.subtract(demandQty);
                        report.setDemandQtyActual(BigDecimal.ZERO);
                    } else {
                        report.setDemandQtyActual(demandQty.subtract(qty));
                        qty = BigDecimal.ZERO;
                    }
                    if (BigDecimal.ZERO.compareTo(qty) >= 0) {
                        break;
                    }
                }

                orgQtyMap.put(orgEntry.getKey(), qty);
            }

            /*如果map中的所有数量已经为零，提前结束循环*/
            if (dailyReports.size() > 0 && allOrgQtyZero) {
                break;
            }
            beginDay = DateUtils.addDays(beginDay, 1);
        }
    }

    /**
     * 根据分货日当天的预计分货量，扣减掉月生产计划中的量(按日期从小到大扣减)
     *
     * @param planQtyMap      月生产计划量
     * @param residualPlanQty 分货日当天的预计分货量汇总
     */
    @Deprecated
    private void removeExpect(Map<Date, BigDecimal> planQtyMap, BigDecimal residualPlanQty) {
        if (CollectionUtils.isEmpty(planQtyMap)
                || residualPlanQty == null || BigDecimal.ZERO.compareTo(residualPlanQty) == 0) {
            return;
        }

        /*日期从小到大扣减*/
        List<Map.Entry<Date, BigDecimal>> entryList = new ArrayList<>(planQtyMap.entrySet());
        entryList.sort(Map.Entry.comparingByKey());

        /*扣减*/
        for (Map.Entry<Date, BigDecimal> entry : entryList) {
            Date date = entry.getKey();
            BigDecimal currentQty = entry.getValue();

            /*如果residualPlanQty大于当前日期的数量，全部扣掉*/
            if (residualPlanQty.compareTo(currentQty) >= 0) {
                planQtyMap.put(date, BigDecimal.ZERO);
                residualPlanQty = residualPlanQty.subtract(currentQty);

                if (residualPlanQty.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
            } else {
                /*不够扣*/
                planQtyMap.put(date, currentQty.subtract(residualPlanQty));
                break;
            }
        }
    }

    /**
     * 计算版本日与版本日之前的【期初、剩余可发】
     *
     * @param isFirst             是否是第一天
     * @param initQtyByChannelMap 前一天的期初，渠道->数量映射
     * @param dayReportList       该天的数据
     * @param psCSkuId            执行SKUID
     * @param executeDay          执行日期
     * @param versionDay          版本日
     * @return 需要被扣减掉的【生产计划量】
     */
    private Map<Long, BigDecimal> calculateExpectQtyBeforeVersionDay(boolean isFirst, Map<Long, BigDecimal> initQtyByChannelMap,
                                                                     List<SgBScpDemandDailyReport> dayReportList,
                                                                     Long psCSkuId, Date executeDay, Date versionDay) {
        /*如果是第一天计算，取在库流水，计算期初量，取版本日所在月之前的在库流水汇总计算*/
        if (isFirst) {
            initQtyByChannelMap.putAll(queryInitQtyMap(psCSkuId, DateUtil.beginOfMonth(versionDay)));
        }

        boolean isVersionDay = DateUtil.isSameDay(versionDay, executeDay);
        Date today = DateUtil.beginOfDay(new Date());
        for (SgBScpDemandDailyReport report : dayReportList) {
            BigDecimal initQty = initQtyByChannelMap.getOrDefault(report.getCpCDistributionOrgId(), BigDecimal.ZERO);
            report.setInitQty(initQty);

            if (executeDay.before(today)) {
                /*今天之前：剩余可发 = 期初+实际分货-已发*/
                report.setResidualQty(initQty
                        .add(Optional.ofNullable(report.getActualAllocationQty()).orElse(BigDecimal.ZERO))
                        .subtract(Optional.ofNullable(report.getAlreadyUsedQty()).orElse(BigDecimal.ZERO)));
                /*总*/
                report.setResidualQtyTotal(initQty
                        .add(Optional.ofNullable(report.getActualAllocationQty()).orElse(BigDecimal.ZERO))
                        .subtract(Optional.ofNullable(report.getAlreadyUsedQtyTotal()).orElse(BigDecimal.ZERO)));
            } else {
                /*今天之后：剩余可发 = 期初+预计分货*/
                report.setResidualQty(initQty
                        .add(Objects.isNull(report.getExpectQtyOriginal()) ? BigDecimal.ZERO : report.getExpectQtyOriginal()));
                /*总*/
                report.setResidualQtyTotal(initQty
                        .add(Objects.isNull(report.getExpectQtyOriginal()) ? BigDecimal.ZERO : report.getExpectQtyOriginal()));
            }

            initQtyByChannelMap.put(report.getCpCDistributionOrgId(), report.getResidualQty());
            /*版本日这一天的【预计分货值】，用【初始值+实际分货】替换*/
            if (isVersionDay) {
                report.setExpectQty(report.getInitQty().add(report.getActualAllocationQty()));
            }
        }

        if (isVersionDay) {
//            return dayReportList.stream().map(SgBScpDemandDailyReport::getExpectQty)
//                    .filter(Objects::nonNull)
//                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            return dayReportList.stream()
                    .collect(Collectors.toMap(SgBScpDemandDailyReport::getCpCDistributionOrgId, SgBScpDemandDailyReport::getExpectQty));
        }

        return MapUtil.empty();
    }


    /**
     * 每天的值计算完，替换需要的值
     * 计算：【预计分货量(替换)】【期初(只计算版本日后)】【剩余可发(只计算版本日后)】
     *
     * @param versionBi           版本号
     * @param psCSkuId            执行SKUID
     * @param executeDay          执行日期
     * @param initQtyByChannelMap 前一天的期初，渠道->数量映射
     * @param dayReportList       日分货报表
     */
    private void processDayLast(String versionBi, Long psCSkuId,
                                Date executeDay,
                                Map<Long, BigDecimal> initQtyByChannelMap,
                                List<SgBScpDemandDailyReport> dayReportList,
                                Date versionDay) {
        boolean isVersionDay = DateUtil.isSameDay(versionDay, executeDay);
//        /*版本日的需求数，用净需求计算时的占用量替换*/
//        Map<Long, BigDecimal> preQtyMap = null;
//        if (isVersionDay) {
//            preQtyMap = sgBScpDemandSyncUsedQtyService.queryByReport(versionBi, psCSkuId);
//        }

        Date today = DateUtil.beginOfDay(new Date());
        for (SgBScpDemandDailyReport report : dayReportList) {
            if (report.getPlanDate().after(versionDay)) {
                BigDecimal initQty = initQtyByChannelMap.getOrDefault(report.getCpCDistributionOrgId(), BigDecimal.ZERO);
                report.setInitQty(initQty);

                if (executeDay.before(today)) {
                    /*今天之前：剩余可发 = 期初+实际分货-已发*/
                    report.setResidualQty(initQty
                            .add(Optional.ofNullable(report.getActualAllocationQty()).orElse(BigDecimal.ZERO))
                            .subtract(Optional.ofNullable(report.getAlreadyUsedQty()).orElse(BigDecimal.ZERO)));
                    /*总*/
                    report.setResidualQtyTotal(initQty
                            .add(Optional.ofNullable(report.getActualAllocationQty()).orElse(BigDecimal.ZERO))
                            .subtract(Optional.ofNullable(report.getAlreadyUsedQtyTotal()).orElse(BigDecimal.ZERO)));
                } else {
                    /*今天之后：剩余可发 = 期初+预计分货*/
                    report.setResidualQty(initQty
                            .add(Objects.isNull(report.getExpectQtyOriginal()) ? BigDecimal.ZERO : report.getExpectQtyOriginal()));
                    /*总*/
                    report.setResidualQtyTotal(initQty
                            .add(Objects.isNull(report.getExpectQtyOriginal()) ? BigDecimal.ZERO : report.getExpectQtyOriginal()));
                }

                initQtyByChannelMap.put(report.getCpCDistributionOrgId(), report.getResidualQty());
            }

//            /*版本日的需求数，用 【净需求计算时的占用量+部门需求表的需求量合并值】 替换*/
//            if (isVersionDay) {
//                report.setDemandQty(Optional.ofNullable(report.getDemandQty()).orElse(BigDecimal.ZERO)
//                        .add(preQtyMap.getOrDefault(report.getCpCDistributionOrgId(), BigDecimal.ZERO)));
//            }

            /*版本日的预计分货值，用【预计分货值+预计分货原始值】替换，因为版本日的【预计分货值】是被替换过的*/
            if (isVersionDay) {
                report.setExpectQty(report.getExpectQtyOriginal().add(report.getExpectQty()));
            } else {
                report.setExpectQty(report.getExpectQtyOriginal());
            }

            /*版本日(不包含)之前的预计分货=已用量(总)*/
            if (report.getPlanDate().before(versionDay)) {
                report.setExpectQty(report.getAlreadyUsedQtyTotal());
            }
        }
    }

    /**
     * 按天逐步根据需求量满足 预计分货
     *
     * @param residualQty   之前的剩余量
     * @param demandList    剩余未满足的数据列表-->这个列表会被滚动复用
     * @param curDateQty    当前进来的量
     * @param dayReportList 当前日期的报表数据
     * @return 本次计算后的剩余量
     */
    @Deprecated
    private BigDecimal calculateExpectQty(BigDecimal residualQty,
                                          List<SgBScpDemandDailyReportCalculateDto> demandList,
                                          BigDecimal curDateQty,
                                          List<SgBScpDemandDailyReport> dayReportList) {
        for (SgBScpDemandDailyReport report : dayReportList) {
            if (Optional.ofNullable(report.getDemandQtyActual()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            demandList.add(SgBScpDemandDailyReportCalculateDto.builder()
                    .cpCDistributionOrgId(report.getCpCDistributionOrgId())
                    .planDate(report.getPlanDate())
                    .qty(report.getDemandQtyActual().subtract(Optional.ofNullable(report.getExpectQtyOriginal()).orElse(BigDecimal.ZERO)))
                    .id(report.getId())
                    .build());
        }

        BigDecimal residual = residualQty.add(curDateQty);
        if (residual.compareTo(BigDecimal.ZERO) <= 0) {
            return residual;
        }

        if (CollectionUtils.isEmpty(demandList)) {
            return residual;
        }

        /*各个部门计算后可以被满足的量*/
        Map<Long, BigDecimal> orgQtyMap = new HashMap<>();

        /*按天分组，这里过滤了没有需求量的数据*/
        Map<Date, List<SgBScpDemandDailyReportCalculateDto>> demandDateMap = demandList.stream()
                .filter(o -> Optional.ofNullable(o.getQty()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.groupingBy(SgBScpDemandDailyReportCalculateDto::getPlanDate, TreeMap::new, Collectors.toList()));
        for (Map.Entry<Date, List<SgBScpDemandDailyReportCalculateDto>> entry : demandDateMap.entrySet()) {
            if (residual.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }

            List<SgBScpDemandDailyReportCalculateDto> dateDemandList = entry.getValue();
            BigDecimal totalDemandQty = dateDemandList.stream()
                    .map(SgBScpDemandDailyReportCalculateDto::getQty)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            /*全部满足*/
            if (residual.compareTo(totalDemandQty) >= 0) {
                for (SgBScpDemandDailyReportCalculateDto demand : dateDemandList) {
                    residual = residual.subtract(demand.getQty());

                    orgQtyMap.put(demand.getCpCDistributionOrgId(),
                            orgQtyMap.getOrDefault(demand.getCpCDistributionOrgId(), BigDecimal.ZERO).add(demand.getQty()));
                    demandList.remove(demand);
                }
                continue;
            }

            /*按比满足*/
            BigDecimal totalResidualQty = residual;
            for (int i = 0; i < dateDemandList.size(); i++) {
                BigDecimal needQty;
                SgBScpDemandDailyReportCalculateDto demand = dateDemandList.get(i);
                /*分配到最后一个，剩下的量全给它*/
                if (i == dateDemandList.size() - 1) {
                    needQty = residual;
                } else {
                    /*按比计算：分配量 = （可分配的总量 × 当前需求量）÷ 当天总需求量 */
                    needQty = totalResidualQty.multiply(demand.getQty())
                            .divide(totalDemandQty, RoundingMode.HALF_DOWN).setScale(0, RoundingMode.HALF_DOWN);
                }

                residual = residual.subtract(needQty);

                orgQtyMap.put(demand.getCpCDistributionOrgId(),
                        orgQtyMap.getOrDefault(demand.getCpCDistributionOrgId(), BigDecimal.ZERO).add(needQty));

                /*按比也可能被完全满足*/
                if (needQty.compareTo(demand.getQty()) >= 0) {
                    demandList.remove(demand);
                } else {
                    demand.setQty(demand.getQty().subtract(needQty));
                }
            }

            residual = BigDecimal.ZERO;
            break;
        }

        if (CollectionUtils.isEmpty(orgQtyMap)) {
            return residual;
        }

        /*结果体现到当天*/
        for (SgBScpDemandDailyReport report : dayReportList) {
            BigDecimal finalQty = orgQtyMap.getOrDefault(report.getCpCDistributionOrgId(), BigDecimal.ZERO);
            if (finalQty.compareTo(BigDecimal.ZERO) > 0) {
                report.setExpectQtyOriginal(report.getExpectQtyOriginal().add(finalQty));
            }
        }
        return residual;
    }

    /**
     * 按天逐步根据需求量满足
     *
     * @param residualQty   之前的剩余量
     * @param demandList    剩余未满足的数据列表-->这个列表会被滚动复用
     * @param curDateQty    当前进来的量
     * @param dayReportList 当前日期的报表数据
     * @param getMethod     获取当前量
     * @param setMethod     设置当前量
     * @return 本次计算后的剩余量
     */
    private BigDecimal calculateQtyByRatio(BigDecimal residualQty,
                                           List<SgBScpDemandDailyReportCalculateDto> demandList,
                                           BigDecimal curDateQty,
                                           List<SgBScpDemandDailyReport> dayReportList,
                                           Function<SgBScpDemandDailyReport, BigDecimal> getMethod,
                                           BiConsumer<SgBScpDemandDailyReport, BigDecimal> setMethod) {
        for (SgBScpDemandDailyReport report : dayReportList) {
            if (Optional.ofNullable(report.getDemandQtyActual()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            demandList.add(SgBScpDemandDailyReportCalculateDto.builder()
                    .cpCDistributionOrgId(report.getCpCDistributionOrgId())
                    .planDate(report.getPlanDate())
                    .qty(report.getDemandQtyActual().subtract(Optional.ofNullable(getMethod.apply(report)).orElse(BigDecimal.ZERO)))
                    .id(report.getId())
                    .build());
        }

        BigDecimal residual = residualQty.add(curDateQty);
        if (residual.compareTo(BigDecimal.ZERO) <= 0) {
            return residual;
        }

        if (CollectionUtils.isEmpty(demandList)) {
            return residual;
        }

        /*各个部门计算后可以被满足的量*/
        Map<Long, BigDecimal> orgQtyMap = new HashMap<>();
        /*每个部门未满足量*/
        Map<Long, BigDecimal> needQtyMap = demandList.stream()
                .filter(o -> Optional.ofNullable(o.getQty()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toMap(SgBScpDemandDailyReportCalculateDto::getCpCDistributionOrgId,
                        SgBScpDemandDailyReportCalculateDto::getQty, BigDecimal::add));

        /*按天分组，这里过滤了没有需求量的数据*/
        Map<Date, List<SgBScpDemandDailyReportCalculateDto>> demandDateMap = demandList.stream()
                .filter(o -> Optional.ofNullable(o.getQty()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.groupingBy(SgBScpDemandDailyReportCalculateDto::getPlanDate, TreeMap::new, Collectors.toList()));
        for (Map.Entry<Date, List<SgBScpDemandDailyReportCalculateDto>> entry : demandDateMap.entrySet()) {
            if (residual.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }

            List<SgBScpDemandDailyReportCalculateDto> dateDemandList = entry.getValue();
            BigDecimal totalDemandQty = dateDemandList.stream()
                    .map(SgBScpDemandDailyReportCalculateDto::getQty)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            /*全部满足*/
            if (residual.compareTo(totalDemandQty) >= 0) {
                for (SgBScpDemandDailyReportCalculateDto demand : dateDemandList) {
                    residual = residual.subtract(demand.getQty());

                    orgQtyMap.put(demand.getCpCDistributionOrgId(),
                            orgQtyMap.getOrDefault(demand.getCpCDistributionOrgId(), BigDecimal.ZERO).add(demand.getQty()));
                    demandList.remove(demand);
                }
                continue;
            }

            /*按比满足*/
            BigDecimal totalResidualQty = residual;
            for (int i = 0; i < dateDemandList.size(); i++) {
                BigDecimal needQty;
                SgBScpDemandDailyReportCalculateDto demand = dateDemandList.get(i);
//                /*分配到最后一个，剩下的量全给它*/
//                if (i == dateDemandList.size() - 1) {
//                    needQty = residual;
//                } else {
                /*按比计算：分配量 = （可分配的总量 × 当前需求量）÷ 当天总需求量 */
                needQty = totalResidualQty.multiply(demand.getQty())
                        .divide(totalDemandQty, RoundingMode.HALF_DOWN).setScale(0, RoundingMode.HALF_DOWN);
//                }

                residual = residual.subtract(needQty);

                orgQtyMap.put(demand.getCpCDistributionOrgId(),
                        orgQtyMap.getOrDefault(demand.getCpCDistributionOrgId(), BigDecimal.ZERO).add(needQty));
                /*计算后，剩余的待满足量*/
                needQtyMap.put(demand.getCpCDistributionOrgId(),
                        needQtyMap.getOrDefault(demand.getCpCDistributionOrgId(), BigDecimal.ZERO).subtract(needQty));

                /*按比也可能被完全满足*/
                if (needQty.compareTo(demand.getQty()) >= 0) {
                    demandList.remove(demand);
                } else {
                    demand.setQty(demand.getQty().subtract(needQty));
                }

                /*分配到最后一个，按未满足的量倒序满足，直到剩余为0*/
                if (i == dateDemandList.size() - 1) {
                    calculateLast(residual, orgQtyMap, needQtyMap);
                }
            }

            residual = BigDecimal.ZERO;
            break;
        }

        if (CollectionUtils.isEmpty(orgQtyMap)) {
            return residual;
        }

        /*结果体现到当天*/
        for (SgBScpDemandDailyReport report : dayReportList) {
            BigDecimal finalQty = orgQtyMap.getOrDefault(report.getCpCDistributionOrgId(), BigDecimal.ZERO);
            if (finalQty.compareTo(BigDecimal.ZERO) > 0) {
                setMethod.accept(report, getMethod.apply(report).add(finalQty));
            }
        }
        return residual;
    }

    /**
     * 分配到最后一个，按未满足的量倒序满足，直到剩余可分配量为0
     *
     * @param curRemain     当前剩余可分配量
     * @param deptActualMap 每个部门的已分配量
     * @param needQtyMap    每个部门未分配量
     */
    private void calculateLast(BigDecimal curRemain, Map<Long, BigDecimal> deptActualMap, Map<Long, BigDecimal> needQtyMap) {
        /*按未满足的量倒序*/
        List<Map.Entry<Long, BigDecimal>> sortedNeedQty = new ArrayList<>(needQtyMap.entrySet());
        sortedNeedQty.sort((entry1, entry2) -> entry2.getValue().compareTo(entry1.getValue()));

        for (Map.Entry<Long, BigDecimal> entry : sortedNeedQty) {
            Long orgId = entry.getKey();
            BigDecimal needQty = entry.getValue();

            /*如果剩余可分配量为0，直接结束*/
            if (curRemain.compareTo(BigDecimal.ZERO) <= 0) {
                return;
            }

            /*本行记录可分配的量：是未分配量和当前剩余可分配量的最小值*/
            BigDecimal qty = needQty.min(curRemain);

            /*将分配的量加上去*/
            deptActualMap.put(orgId, deptActualMap.getOrDefault(orgId, BigDecimal.ZERO).add(qty));
            needQtyMap.put(orgId, needQty.subtract(qty));

            /*减剩余可分配量，进行下次循环*/
            curRemain = curRemain.subtract(qty);
        }
    }


    /**
     * 计算某一天的：剩余的分货量【今天之前】
     *
     * @param executeDay    执行日期
     * @param psCSkuId      执行SKUID
     * @param dayReportList 日分货报表
     * @param isFirst       是否本次计算的第一天
     * @return 各渠道今天的剩余
     */
    @Deprecated
    private Map<Long, BigDecimal> calculateInitAndResidualQty(Date executeDay, Long psCSkuId,
                                                              List<SgBScpDemandDailyReport> dayReportList,
                                                              boolean isFirst, Map<Long, BigDecimal> initQtyByChannelMap) {
        /*如果是第一天计算，取在库流水，计算期初量*/
        if (isFirst) {
            initQtyByChannelMap = queryInitQtyMap(psCSkuId, DateUtil.beginOfMonth(new Date()));
        }

        Date today = DateUtil.beginOfDay(new Date());
        for (SgBScpDemandDailyReport report : dayReportList) {
            BigDecimal initQty = initQtyByChannelMap.getOrDefault(report.getCpCDistributionOrgId(), BigDecimal.ZERO);
            report.setInitQty(initQty);

            if (executeDay.before(today)) {
                /*今天之前：剩余可发 = 期初+实际分货-已发*/
                report.setResidualQtyTotal(initQty
                        .add(Optional.ofNullable(report.getActualAllocationQty()).orElse(BigDecimal.ZERO))
                        .subtract(Optional.ofNullable(report.getAlreadyUsedQtyTotal()).orElse(BigDecimal.ZERO)));
                report.setResidualQty(initQty
                        .add(Optional.ofNullable(report.getActualAllocationQty()).orElse(BigDecimal.ZERO))
                        .subtract(Optional.ofNullable(report.getAlreadyUsedQty()).orElse(BigDecimal.ZERO)));
            } else {
                /*今天之后：剩余可发 = 期初+预计分货*/
                report.setResidualQtyTotal(initQty
                        .add(Objects.isNull(report.getExpectQtyOriginal()) ? BigDecimal.ZERO : report.getExpectQtyOriginal()));
                report.setResidualQty(initQty
                        .add(Objects.isNull(report.getExpectQtyOriginal()) ? BigDecimal.ZERO : report.getExpectQtyOriginal()));
            }

            initQtyByChannelMap.put(report.getCpCDistributionOrgId(), report.getResidualQtyTotal());
        }
        return initQtyByChannelMap;
    }



    /**
     * 查询期初数据
     *
     * @param psCSkuId SKUID
     * @return 期初数据：key：渠道ID，value：期初量
     */
    private Map<Long, BigDecimal> queryInitQtyMap(Long psCSkuId, Date beginOfMonth) {
        SgBScpDemandDailyReportService bean = ApplicationContextHandle.getBean(SgBScpDemandDailyReportService.class);
        try {
            Future<List<SgBCommonStorageQtyQueryResult>> listFuture = bean.queryByAdb(
                    () -> sgBScpDemandDailyReportMapper.selectInitQty(sgAdbPrefix, psCSkuId, beginOfMonth));
            List<SgBCommonStorageQtyQueryResult> dateQtyList = listFuture.get();

            return ListUtils.emptyIfNull(dateQtyList).stream()
                    .collect(Collectors.toMap(SgBCommonStorageQtyQueryResult::getCpCDistributionOrgId,
                            SgBCommonStorageQtyQueryResult::getQty));
        } catch (Exception e) {
            log.error(LogUtil.format("期初量-异步使用ADb查询库存失败，异常：{}",
                    LOG_OBJ + "queryInitQtyMap"), Throwables.getStackTraceAsString(e));
            throw new NDSException("期初量-异步使用ADb查询库存失败：" + e.getMessage());
        }
    }


    /**
     * 构建报表数据：需求量、实际分货量、已用量
     *
     * @param isFirst      是否首次
     * @param versionBi    版本号
     * @param psCSkuId     SKUID
     * @param skuEcode     SKU编码
     * @param startDay     开始日期
     * @param endDay       结束日期
     * @param channelIdMap 渠道编码与渠道ID映射
     * @return 报表数据
     */
    private List<SgBScpDemandDailyReport> buildReportList(List<SgBScpDemandDailyReport> reportList,
                                                          boolean isFirst, /*boolean isWhiteSku,*/ String versionBi, Date versionDay,
                                                          Long psCSkuId, String skuEcode,
                                                          Date startDay, Date endDay,
                                                          Map<String, Long> channelIdMap) {
        /*已实际分货量：渠道->[日期->数量]*/
        Map<Long, Map<Date, BigDecimal>> actualAllocationQtyMapByChannel = queryActualAllocationQty(psCSkuId, startDay, isFirst);
        /*已发量(根据部门下的配销仓查询配销仓在库变动流水获得)：渠道ID->[日期->数量]*/
        Map<Long, Map<Date, BigDecimal>> alreadyQtyMapByChannel = queryAlreadyUsedQtyBySa(psCSkuId, startDay);
        /*已发量-总(根据订单明细的分货二级筛选获得)：渠道CODE->[日期->数量]*/
        Map<String, Map<Date, BigDecimal>> alreadyQtyMapByChannelTotal = queryAlreadyUsedQtyTotal(psCSkuId, startDay);

        /*需求量如果取月需求提报里面的净需求，是有可能被修改的，界面上就可以新增或废除*/
        Map<Long, Map<Date, BigDecimal>> demandQtyMapByChannel = sgCDepartmentMonthDemandService.queryDemandBySku(versionBi, skuEcode,
                DateUtil.format(startDay, "yyyy-MM-dd"), DateUtil.format(endDay, "yyyy-MM-dd"));
        /*版本日的需求数，用净需求计算时的占用量替换*/
        Map<Long, BigDecimal> preQtyMap = sgBScpDemandSyncUsedQtyService.queryByReport(versionBi, psCSkuId);
        if (isFirst) {
            /*逐个渠道处理*/
            for (Map.Entry<String, Long> channelEntry : channelIdMap.entrySet()) {
                /*需求量*/
                Map<Date, BigDecimal> demandQtyMap = demandQtyMapByChannel.getOrDefault(channelEntry.getValue(), MapUtil.empty());
                /*已分货量*/
                Map<Date, BigDecimal> actualAllocationQtyMap = actualAllocationQtyMapByChannel.getOrDefault(channelEntry.getValue(), MapUtil.empty());
                /*已用量*/
                Map<Date, BigDecimal> alreadyQtyMapTotal = alreadyQtyMapByChannelTotal.getOrDefault(channelEntry.getKey(), MapUtil.empty());
                Map<Date, BigDecimal> alreadyQtyMap = alreadyQtyMapByChannel.getOrDefault(channelEntry.getValue(), MapUtil.empty());

                Date date = startDay;
                while (date.before(endDay)) {
                    SgBScpDemandDailyReport report = new SgBScpDemandDailyReport();
                    report.setVersionBi(versionBi);
                    report.setCpCDistributionOrgId(channelEntry.getValue());
                    report.setLv2ChannelCode(channelEntry.getKey());
                    report.setPsCSkuId(psCSkuId);
                    report.setPsCSkuEcode(skuEcode);
                    report.setPlanDate(date);

                    report.setActualAllocationQty(actualAllocationQtyMap.getOrDefault(date, BigDecimal.ZERO));
                    report.setAlreadyUsedQtyTotal(alreadyQtyMapTotal.getOrDefault(date, BigDecimal.ZERO).abs());
                    report.setAlreadyUsedQty(alreadyQtyMap.getOrDefault(date, BigDecimal.ZERO).abs());

                    /*如果是版本日之前（不包括版本日）需求量 = 已用量，否则取需求量；*/
                    if (!date.before(versionDay)) {
                        report.setDemandQty(demandQtyMap.getOrDefault(date, BigDecimal.ZERO));
                    } else {
                        report.setDemandQty(report.getAlreadyUsedQtyTotal());
                    }
                    /*版本日的需求数，用 【净需求计算时的占用量+部门需求表的需求量合并值】 替换*/
                    if (DateUtil.isSameDay(versionDay, date)) {
                        report.setDemandQty(Optional.ofNullable(report.getDemandQty()).orElse(BigDecimal.ZERO)
                                .add(preQtyMap.getOrDefault(report.getCpCDistributionOrgId(), BigDecimal.ZERO)));
                    }
                    report.setDemandQtyActual(report.getDemandQty());

                    /*需要计算的内容默认为零*/
                    report.setExpectQtyOriginal(BigDecimal.ZERO);
                    report.setExpectAdjustQty(BigDecimal.ZERO);
                    report.setResidualQtyTotal(BigDecimal.ZERO);
                    report.setInitQty(BigDecimal.ZERO);

                    report.setRemark(channelEntry.getKey() + "_ADD");

                    StorageUtils.setBModelDefalutData(report, R3SystemUserResource.getSystemRootUser());
                    reportList.add(report);

                    date = DateUtils.addDays(date, 1);
                }
            }

            return reportList;
        }

        Map<Long, List<SgBScpDemandDailyReport>> channelReportListMap = ListUtils.emptyIfNull(reportList).stream()
                .collect(Collectors.groupingBy(SgBScpDemandDailyReport::getCpCDistributionOrgId));
        /*逐个渠道处理*/
        for (Map.Entry<String, Long> channelEntry : channelIdMap.entrySet()) {
            /*需求量*/
            Map<Date, BigDecimal> demandQtyMap = demandQtyMapByChannel.getOrDefault(channelEntry.getValue(), MapUtil.empty());
            /*已分货量*/
            Map<Date, BigDecimal> actualAllocationQtyMap = actualAllocationQtyMapByChannel.getOrDefault(channelEntry.getValue(), MapUtil.empty());
            /*已用量*/
            Map<Date, BigDecimal> alreadyQtyMapTotal = alreadyQtyMapByChannelTotal.getOrDefault(channelEntry.getKey(), MapUtil.empty());
            Map<Date, BigDecimal> alreadyQtyMap = alreadyQtyMapByChannel.getOrDefault(channelEntry.getValue(), MapUtil.empty());

            List<SgBScpDemandDailyReport> channelReportList = channelReportListMap.getOrDefault(channelEntry.getValue(), ListUtil.empty());
            for (SgBScpDemandDailyReport report : channelReportList) {
                report.setActualAllocationQty(actualAllocationQtyMap.getOrDefault(report.getPlanDate(), BigDecimal.ZERO));
                report.setAlreadyUsedQtyTotal(alreadyQtyMapTotal.getOrDefault(report.getPlanDate(), BigDecimal.ZERO).abs());
                report.setAlreadyUsedQty(alreadyQtyMap.getOrDefault(report.getPlanDate(), BigDecimal.ZERO).abs());

                /*如果需求日期不在版本日期之前，取净需求量*/
                if (!report.getPlanDate().before(versionDay)) {
                    report.setDemandQty(demandQtyMap.getOrDefault(report.getPlanDate(), BigDecimal.ZERO));
                } else {
                    report.setDemandQty(report.getAlreadyUsedQtyTotal());
                }
                /*版本日的需求数，用 【净需求计算时的占用量+部门需求表的需求量合并值】 替换*/
                if (DateUtil.isSameDay(versionDay, report.getPlanDate())) {
                    report.setDemandQty(Optional.ofNullable(report.getDemandQty()).orElse(BigDecimal.ZERO)
                            .add(preQtyMap.getOrDefault(report.getCpCDistributionOrgId(), BigDecimal.ZERO)));
                }
                report.setDemandQtyActual(report.getDemandQty());

                /*每次都需要重算，所以在这里清零*/
                report.setExpectQtyOriginal(BigDecimal.ZERO);
                report.setExpectAdjustQty(BigDecimal.ZERO);
                report.setResidualQtyTotal(BigDecimal.ZERO);
                report.setInitQty(BigDecimal.ZERO);

                /*数值没变动时，备注不会被修改*/
                report.setRemark(channelEntry.getKey() + "_SAVE");
                StorageUtils.setBModelDefalutDataByUpdate(report, R3SystemUserResource.getSystemRootUser());
            }
        }
        return reportList;
    }

    /**
     * 获取已用量（取零售发货单明细）
     *
     * @param psCSkuId skuID
     * @param startDay 开始日期
     * @return 渠道编码->[日期->数量]
     */
    private Map<String, Map<Date, BigDecimal>> queryAlreadyUsedQtyTotal(Long psCSkuId, Date startDay) {
        SgBScpDemandDailyReportService bean = ApplicationContextHandle.getBean(SgBScpDemandDailyReportService.class);
        try {
            Future<List<SgBCommonStorageQtyQueryResult>> listFuture = bean.queryByAdb(
                    () -> sgBScpDemandDailyReportMapper.selectAlreadyUsedQtyTotal(orderAdbPrefix, psCSkuId, startDay));
            List<SgBCommonStorageQtyQueryResult> dataQtyList = listFuture.get();

            return ListUtils.emptyIfNull(dataQtyList).stream()
                    .filter(o -> StringUtils.isNotEmpty(o.getLv2ChannelCode()))
                    .collect(Collectors.groupingBy(
                            SgBCommonStorageQtyQueryResult::getLv2ChannelCode,
                            Collectors.toMap(SgBCommonStorageQtyQueryResult::getMDate,
                                    SgBCommonStorageQtyQueryResult::getQty, BigDecimal::add)));
        } catch (Exception e) {
            log.error(LogUtil.format("已用量-异步使用ADb查询库存失败，异常：{}",
                    LOG_OBJ + "queryActualAllocationQty2"), Throwables.getStackTraceAsString(e));
            throw new NDSException("已用量-异步使用ADb查询库存失败：" + e.getMessage());
        }
    }

    /**
     * 获取已用量（取配销仓在库变动流水）
     *
     * @param psCSkuId skuID
     * @param startDay 开始日期
     * @return 渠道->[日期->数量]
     */
    private Map<Long, Map<Date, BigDecimal>> queryAlreadyUsedQtyBySa(Long psCSkuId, Date startDay) {
        SgBScpDemandDailyReportService bean = ApplicationContextHandle.getBean(SgBScpDemandDailyReportService.class);
        try {
            Future<List<SgBCommonStorageQtyQueryResult>> listFuture = bean.queryByAdb(
                    () -> sgBScpDemandDailyReportMapper.selectAlreadyUsedQtyBySa(sgAdbPrefix, psCSkuId, startDay));
            List<SgBCommonStorageQtyQueryResult> dateQtyList = listFuture.get();

            return ListUtils.emptyIfNull(dateQtyList).stream()
                    .collect(Collectors.groupingBy(
                            SgBCommonStorageQtyQueryResult::getCpCDistributionOrgId,
                            Collectors.toMap(SgBCommonStorageQtyQueryResult::getMDate,
                                    SgBCommonStorageQtyQueryResult::getQty, BigDecimal::add)));
        } catch (Exception e) {
            log.error(LogUtil.format("已用量-异步使用ADb查询库存失败，异常：{}",
                    LOG_OBJ + "queryActualAllocationQty"), Throwables.getStackTraceAsString(e));
            throw new NDSException("已用量-异步使用ADb查询库存失败：" + e.getMessage());
        }
    }

    /**
     * 获取已分货量
     *
     * @param psCSkuId skuID
     * @param startDay 开始日期
     * @return 渠道->[日期->数量]
     */
    private Map<Long, Map<Date, BigDecimal>> queryActualAllocationQty(Long psCSkuId, Date startDay, boolean isVersionDay) {
        SgBScpDemandDailyReportService bean = ApplicationContextHandle.getBean(SgBScpDemandDailyReportService.class);
        try {
            Future<List<SgBCommonStorageQtyQueryResult>> listFuture = bean.queryByAdb(
                    () -> sgBScpDemandDailyReportMapper.selectActualAllocationQty(sgAdbPrefix, psCSkuId, startDay,
                            isVersionDay));
            List<SgBCommonStorageQtyQueryResult> dateQtyList = listFuture.get();

            return ListUtils.emptyIfNull(dateQtyList).stream()
                    .collect(Collectors.groupingBy(
                            SgBCommonStorageQtyQueryResult::getCpCDistributionOrgId,
                            Collectors.toMap(SgBCommonStorageQtyQueryResult::getMDate,
                                    SgBCommonStorageQtyQueryResult::getQty, BigDecimal::add)));
        } catch (Exception e) {
            log.error(LogUtil.format("已分货量-异步使用ADb查询库存失败，异常：{}",
                    LOG_OBJ + "queryActualAllocationQty"), Throwables.getStackTraceAsString(e));
            throw new NDSException("已分货量-异步使用ADb查询库存失败：" + e.getMessage());
        }
    }

    @TargetDataSource(name = "adb")
    @Async(value = "adbAsyncExecutorPool")
    public Future<List<SgBCommonStorageQtyQueryResult>> queryByAdb(Callable<List<SgBCommonStorageQtyQueryResult>> callable) {
        try {
            return AsyncResult.forValue(callable.call());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 查询分货组织架构
     *
     * @param channelList 渠道部门编码列表
     * @return 二级渠道编码->分货组织架构信息
     */
    private Map<String, Long> queryChannelIdMap(List<Long> channelList) {
        if (CollectionUtils.isEmpty(channelList)) {
            log.warn(LogUtil.format("二级渠道不会有空值", LOG_OBJ + "queryChannelOrgMap"));
            throw new NDSException("二级渠道不会有空值");
        }

        /*分货组织架构：CP_C_DISTRIBUTION_ORGANIZATION*/
        ValueHolderV14<List<CpCDistributionOrganization>> holderV14 = cpCDistributionOrganizationQueryCmd.queryByIds(channelList);
        if (!holderV14.isOK() || CollectionUtils.isEmpty(holderV14.getData())) {
            log.warn(LogUtil.format("查询查询分货组织架构失败，入参:{},异常：{}",
                    LOG_OBJ + "queryChannelOrgMap"), channelList, holderV14.getMessage());
            return Collections.emptyMap();
        }

        return ListUtils.emptyIfNull(holderV14.getData()).stream()
                .collect(Collectors.toMap(CpCDistributionOrganization::getEcode, CpCDistributionOrganization::getId));
    }


    /**
     * 比较两个BigDecimal是否相等
     */
    private boolean equalsQty(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) {
            return true;
        }
        if (a == null || b == null) {
            return false;
        }

        return a.compareTo(b) == 0;
    }

    /**
     * 临时对象，用于计算报表数据时存储未满足的需求量
     */
    @Data
    @Builder
    static class SgBScpDemandDailyReportCalculateDto {
        private Long id;
        private Date planDate;
        private Long cpCDistributionOrgId;
        private BigDecimal qty;
    }
}
