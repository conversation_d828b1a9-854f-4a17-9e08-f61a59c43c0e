package com.burgeon.r3.sg.share.services.adjust;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.share.adjust.SgBShareAdjust;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.adjust.SgBShareAdjustItemMapper;
import com.burgeon.r3.sg.share.mapper.adjust.SgBShareAdjustMapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2021/5/28 14:43
 */
@Slf4j
@Component
@Deprecated
public class SgBShareAdjustVoidService {

    @Autowired
    private SgBShareAdjustMapper mapper;

    @Autowired
    private SgBShareAdjustItemMapper itemMapper;

    /**
     * R3页面
     */
    ValueHolder voidAdjust(QuerySession session) {
        SgR3BaseRequest reqeust = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        reqeust.setR3(true);
        SgBShareAdjustVoidService service = ApplicationContextHandle.getBean(SgBShareAdjustVoidService.class);
        return R3ParamUtils.convertV14WithResult(service.voidAdjust(reqeust));
    }

    /**
     * 共享调整单作废
     *
     * @param request 请求参数
     * @return 结果信息
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> voidAdjust(SgR3BaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBShareAdjustVoidService.voidAdjust.ReceiveParams:request{}"
                    , JSONObject.toJSONString(request));
        }
        SgBShareAdjust adjust = checkParams(request);
        User loginUser = request.getLoginUser();
        String lockKey = SgConstants.SG_B_SHARE_ADJUST + ":" + request.getObjId();
        SgRedisLockUtils.lock(lockKey);
        try {

            StorageUtils.setBModelDefalutDataByUpdate(adjust, request.getLoginUser());
            adjust.setStatus(SgShareConstants.BILL_STATUS_VOID);
            adjust.setIsactive(SgConstants.IS_ACTIVE_N);
            adjust.setDelerId(loginUser.getId().longValue());
            adjust.setDelerName(loginUser.getName());
            adjust.setDelerEname(loginUser.getEname());
            adjust.setDelTime(new Date());
            mapper.updateById(adjust);
        } catch (Exception e) {
            AssertUtils.logAndThrowException(e.getMessage(), e, loginUser.getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKey, log, this.getClass().getName());
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "作废成功!");
    }

    /**
     * 参数校验
     *
     * @param reqeust 请求参数
     * @return 主表信息
     */
    private SgBShareAdjust checkParams(SgR3BaseRequest reqeust) {
        AssertUtils.notNull(reqeust, "请求参数不能为空");
        SgStoreUtils.checkR3BModelDefalut(reqeust);
        Long objId = reqeust.getObjId();
        AssertUtils.notNull(objId, "主表ID不能为空");
        SgBShareAdjust adjust = mapper.selectById(objId);
        AssertUtils.notNull(adjust, "当前记录已不存在！");
        if (SgShareConstants.BILL_STATUS_SUBMIT == adjust.getStatus()) {
            AssertUtils.logAndThrow("当前记录已审核，不允许作废！");
        }
        if (SgShareConstants.BILL_STATUS_VOID == adjust.getStatus()) {
            AssertUtils.logAndThrow("当前记录已作废，不允许重复作废！");
        }
        return adjust;
    }
}
