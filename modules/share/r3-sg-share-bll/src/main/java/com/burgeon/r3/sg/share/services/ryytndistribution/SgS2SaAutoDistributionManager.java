package com.burgeon.r3.sg.share.services.ryytndistribution;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.logic.SgStorageRedisQueryLogic;
import com.burgeon.r3.sg.basic.mapper.SgBSaStorageMapper;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.basic.mapper.SgCShareStoreMapper;
import com.burgeon.r3.sg.basic.model.SgStorageRedisQuerySsModel;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySsExtResult;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.R3ParamConstants;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.SaCategoryEnum;
import com.burgeon.r3.sg.core.enums.SgDistributionTypeEnum;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgBSaStorage;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationBillSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnBillSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnItemSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationSaveItemRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationSaveRequst;
import com.burgeon.r3.sg.share.model.result.allocation.SgBShareAllocationReturnSaveAndSubmitResult;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationReturnSaveAndSubmitService;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationSaveService;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationSubmitService;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 自动分货
 *
 * <AUTHOR>
 * @since 2022-09-20 15:07
 */
@Slf4j
@Component
public class SgS2SaAutoDistributionManager {
    /**
     * 日志OBJ
     */
    private static final String LOG_OBJ = "SgS2SaAutoDistributionManager.";

    /**
     * 聚合仓信息
     */
    @Resource
    private SgCShareStoreMapper sgShareStoreMapper;

    /**
     * 配销仓
     */
    @Resource
    private SgCSaStoreMapper sgCSaStoreMapper;
    /**
     * 配销仓库存
     */
    @Resource
    private SgBSaStorageMapper sgBSaStorageMapper;

    /**
     * 分货单   保存/修改
     */
    @Resource
    private SgBShareAllocationSaveService shareAllocationSaveService;
    @Resource
    private SgBShareAllocationSubmitService shareAllocationSubmitService;

    /**
     * 分货退货单 保存/提审
     */
    @Autowired
    private SgBShareAllocationReturnSaveAndSubmitService sgBShareAllocationReturnSaveAndSubmitService;

    /**
     * 聚合仓可用库存
     */
    @Resource
    private SgStorageRedisQueryLogic sgStorageRedisQueryLogic;

    @Resource
    private SgCDistributionMonthLogService sgCDistributionMonthLogService;


    /**
     * 分货单（分货单创建与修改、库存变更）
     *
     * @param shareStoreId             聚合仓ID
     * @param skuId                    商品ID
     * @param storageNumAllocationList 配销仓ID+分货量 映射列表
     * @param prefix                   备注前缀（用于区分月初或定时）
     */
    @Transactional(rollbackFor = Throwable.class)
    public void doAllocation(Long shareStoreId, Long skuId, List<Pair<Long, BigDecimal>> storageNumAllocationList, String prefix) {
        if (CollectionUtils.isEmpty(storageNumAllocationList)) {
            log.info(LogUtil.format(prefix + "执行分货数量为空，聚合仓ID:{},商品ID:{}", LOG_OBJ + "doAllocation"), shareStoreId, skuId);
            return;
        }

        SgBShareAllocationBillSaveRequst saveRequest = new SgBShareAllocationBillSaveRequst();
        /*主表参数*/
        SgBShareAllocationSaveRequst saveRequst = new SgBShareAllocationSaveRequst();
        saveRequst.setBillDate(new Date());
        saveRequst.setSgCShareStoreId(shareStoreId);
        saveRequst.setRemark(prefix + "由二级部门需求自动分货任务自动生成");
        saveRequest.setObjId(-1L);
        saveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());
        saveRequest.setSgBShareAllocationSaveRequst(saveRequst);
        /*明细参数*/
        List<SgBShareAllocationSaveItemRequst> saveItemReqList = new ArrayList<>();
        for (Pair<Long, BigDecimal> pair : storageNumAllocationList) {
            SgBShareAllocationSaveItemRequst itemRequest = new SgBShareAllocationSaveItemRequst();
            CommonCacheValUtils.setSkuInfo(skuId, null, itemRequest);
            itemRequest.setQtyDiff(BigDecimal.ZERO);
            itemRequest.setQtyIn(BigDecimal.ZERO);
            itemRequest.setQtyOut(BigDecimal.ZERO);
            itemRequest.setPsCSkuId(skuId);

            itemRequest.setQty(pair.getRight());
            itemRequest.setSgCSaStoreId(pair.getLeft());
            saveItemReqList.add(itemRequest);
        }
        saveRequest.setSgBShareAllocationSaveItemRequsts(saveItemReqList);


        ValueHolderV14<SgR3BaseResult> saveRet;
        try {
            saveRet = shareAllocationSaveService.save(saveRequest);
        } catch (Exception e) {
            log.error(LogUtil.format(prefix + "调用分货保存接口出错，参数：{}，错误信息：[{}]", LOG_OBJ + "doAllocation"),
                    JSON.toJSONString(saveRequest), Throwables.getStackTraceAsString(e));
            throw new NDSException(prefix + "调用分货保存接口出错:" + e.getMessage());
        }

        if (!saveRet.isOK()) {
            log.warn(LogUtil.format(prefix + "保存分货单执行出错,param:{},result:{}", LOG_OBJ + "doAllocation"),
                    JSON.toJSONString(saveRequest), JSON.toJSONString(saveRet));
            throw new NDSException(prefix + "保存分货单执行出错:" + saveRet.getMessage());
        }
        SgR3BaseResult saveRetData = saveRet.getData();
        JSONObject dataJo = saveRetData.getDataJo();
        Long objId = dataJo.getLong(R3ParamConstants.OBJID);

        SgR3BaseRequest submitRequest = new SgR3BaseRequest();
        submitRequest.setObjId(objId);
        submitRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());

        ValueHolderV14<SgR3BaseResult> submitRet;
        try {
            submitRet = shareAllocationSubmitService.submitShareAllocation(submitRequest, true, false);
        } catch (Exception e) {
            log.error(LogUtil.format(prefix + "调用分货提交接口出错，保存参数：【{}】,保存结果：【{}】，提交参数：【{}】，错误信息：[{}]", LOG_OBJ + "doAllocation"),
                    JSON.toJSONString(saveRequest), JSON.toJSONString(saveRet), JSON.toJSONString(submitRequest), Throwables.getStackTraceAsString(e));
            throw new NDSException(prefix + "调用分货提交接口出错:" + e.getMessage());
        }
        if (!submitRet.isOK()) {
            log.warn(LogUtil.format(prefix + "提交分货单执行出错，保存参数：【{}】,保存结果：【{}】,提交参数：【{}】，提交结果：【{}】", LOG_OBJ + "doAllocation"),
                    JSON.toJSONString(saveRequest), JSON.toJSONString(saveRet),
                    JSON.toJSONString(submitRequest), JSON.toJSONString(submitRet));
            throw new NDSException(prefix + "提交分货单执行出错:" + submitRet.getMessage());
        }
    }

    /**
     * 分货退货单（分货退货单创建与修改、库存变更）
     *
     * @param shareStoreId         聚合仓ID
     * @param skuId                商品ID
     * @param storageNumReturnList 配销仓ID+退货分货量 映射列表
     * @param typeEnum             执行类型
     * @return redis的key（回滚依据）
     */
    @Transactional(rollbackFor = Throwable.class)
    public List<String> doAllocationReturn(Long shareStoreId, Long skuId, List<Pair<Long, BigDecimal>> storageNumReturnList,
                                           SgDistributionTypeEnum typeEnum, String versionBi) {
        /*无论是否生成单据，都插入数据库（一方面防止没有库存所以没拉回，但是需要执行定时分货；另一方面保留拉回记录）*/
        sgCDistributionMonthLogService.createMonthLog(shareStoreId, skuId, storageNumReturnList, typeEnum, versionBi);

        if (CollectionUtils.isEmpty(storageNumReturnList)) {
            log.info(LogUtil.format(typeEnum + "执行分货退货数量为空，聚合仓ID:{},商品ID:{}", LOG_OBJ + "doAllocationReturn"), shareStoreId, skuId);
            return Collections.emptyList();
        }

        /*本地环境测试,不生成退货单*/
//        if ("local".equals(System.getProperty("env"))) {
//            throw new RuntimeException("本地环境测试需要回滚数据");
//        }

        List<SgBShareAllocationReturnBillSaveRequst> returnRequestList = new ArrayList<>();
        for (Pair<Long, BigDecimal> pair : storageNumReturnList) {
            SgBShareAllocationReturnBillSaveRequst request = new SgBShareAllocationReturnBillSaveRequst();
            request.setR3(true);

            SgBShareAllocationReturnSaveRequst mainRequest = new SgBShareAllocationReturnSaveRequst();
            mainRequest.setBillDate(new Date());
            mainRequest.setSgCShareStoreId(shareStoreId);
            mainRequest.setSgCSaStoreId(pair.getLeft());
            mainRequest.setRemark(typeEnum.getLogPrefix() + "由二级部门需求自动分货任务自动生成！");


            SgBShareAllocationReturnItemSaveRequst itemRequest = new SgBShareAllocationReturnItemSaveRequst();
            itemRequest.setPsCSkuId(skuId);

            try {
                CommonCacheValUtils.setSkuInfo(skuId, null, itemRequest);
            } catch (Exception e) {
                log.error(LogUtil.format(typeEnum + "分货退货单设置sku基本信息失败，skuId:{}，错误信息：[{}]", LOG_OBJ + "doAllocationReturn"),
                        skuId, Throwables.getStackTraceAsString(e));
            }

            itemRequest.setQty(pair.getRight());
            itemRequest.setId(-1L);
            List<SgBShareAllocationReturnItemSaveRequst> itemRequestList = new ArrayList<>();
            itemRequestList.add(itemRequest);
            request.setAllocationReturnItemSaveRequst(itemRequestList);
            request.setLoginUser(R3SystemUserResource.getSystemRootUser());
            request.setAllocationReturnSaveRequst(mainRequest);

            returnRequestList.add(request);
        }

        ValueHolderV14<SgBShareAllocationReturnSaveAndSubmitResult> ret;
        try {
            ret = sgBShareAllocationReturnSaveAndSubmitService.saveAndSubmit2(returnRequestList);
        } catch (Exception e) {
            log.warn(LogUtil.format(typeEnum.getLogPrefix() + "调用分货退货保存并提交接口出错，参数：{}，错误信息：[{}]", LOG_OBJ + "doAllocationReturn"),
                    JSON.toJSONString(returnRequestList), Throwables.getStackTraceAsString(e));
            throw new NDSException(typeEnum.getLogPrefix() + "调用分货退货保存并提交接口出错:" + e.getMessage());
        }

        SgBShareAllocationReturnSaveAndSubmitResult retData = ret.getData();
        if (!ret.isOK()) {
            log.warn(LogUtil.format(typeEnum.getLogPrefix() + "分货退货单执行出错,param:{},result:{}", LOG_OBJ + "doAllocationReturn"),
                    JSON.toJSONString(returnRequestList), JSON.toJSONString(ret));
            /*执行出错，回滚库存*/
            if (Objects.nonNull(retData) && !CollectionUtils.isEmpty(retData.getRedisFtpKeys())) {
                StorageBasicUtils.rollbackStorage(retData.getRedisFtpKeys(), SystemUserResource.getRootUser());
            }

            throw new NDSException(typeEnum.getLogPrefix() + "分货退货单执行出错:" + ret.getMessage());
        }

        if (Objects.isNull(retData) || CollectionUtils.isEmpty(retData.getRedisFtpKeys())) {
            return Collections.emptyList();
        }

        log.info(LogUtil.format(typeEnum.getLogPrefix() + "调用分货退货保存并提交接口成功，参数：{}，redisFtpKeys", LOG_OBJ + "doAllocationReturn"),
                JSON.toJSONString(returnRequestList), JSON.toJSONString(retData.getRedisFtpKeys()));
        return retData.getRedisFtpKeys();
    }


    /**
     * 获取所有可用聚合仓
     *
     * @return 聚合仓列表
     */
    public List<SgCShareStore> querySgShareStores() {
        /*查询【聚合仓档案】中【自动分货】为Y的记录对应的【聚合仓库存】中的记录*/
        List<SgCShareStore> shareStores = sgShareStoreMapper.selectList(new LambdaQueryWrapper<SgCShareStore>()
                .eq(SgCShareStore::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCShareStore::getIsAutoAllocation, SgConstants.IS_AUTO_Y));
        if (CollectionUtils.isEmpty(shareStores)) {
            log.warn(LogUtil.format("获取可用聚合仓信息列表为空", LOG_OBJ + "querySgShareStores"));
            return Collections.emptyList();
        }
        return shareStores;
    }

    /**
     * 获取聚合仓下配销仓数据
     *
     * @param shareStores 聚合仓列表
     * @param isAutoAllocation  是否自动分货
     * @return 聚合仓ID->配销仓列表映射
     */
    public Map<Long, List<SgCSaStore>> querySaStoresMap(List<SgCShareStore> shareStores, boolean isAutoAllocation) {
        if (CollectionUtils.isEmpty(shareStores)) {
            return Collections.emptyMap();
        }
        Set<Long> cShareIds = shareStores.stream().map(SgCShareStore::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<SgCSaStore> wrapper = new LambdaQueryWrapper<SgCSaStore>()
                .eq(SgCSaStore::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCSaStore::getCategory, SaCategoryEnum.VIP_CATEGORY.getCode())
                .in(SgCSaStore::getSgCShareStoreId, cShareIds);
        if (isAutoAllocation) {
            /*是否自动分货：拉回时不判断，自动分货时才过滤掉非自动分货的配销仓*/
            wrapper.eq(SgCSaStore::getIsAutoAllocation, SgConstants.IS_AUTO_Y);
        }
        List<SgCSaStore> saStores = sgCSaStoreMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(shareStores)) {
            log.warn(LogUtil.format("获取可用聚合仓下配销仓数据映射为空，参数：{}", LOG_OBJ + "querySaStores"),
                    JSON.toJSONString(shareStores));
            return Collections.emptyMap();
        }
        return saStores.stream().collect(Collectors.groupingBy(SgCSaStore::getSgCShareStoreId));
    }

    /**
     * 获取聚合仓下配销仓数据
     *
     * @param shareStoreId 聚合仓ID
     * @return 配销仓列表
     */
    public List<SgCSaStore> querySaStores2Return(Long shareStoreId, boolean isAutoAllocation) {
        if (Objects.isNull(shareStoreId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SgCSaStore> wrapper = new LambdaQueryWrapper<SgCSaStore>()
                .eq(SgCSaStore::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCSaStore::getCategory, SaCategoryEnum.VIP_CATEGORY.getCode())
                .eq(SgCSaStore::getSgCShareStoreId, shareStoreId);
        if (isAutoAllocation) {
            /*是否自动分货：拉回时不判断，自动分货时才过滤掉非自动分货的配销仓*/
            wrapper.eq(SgCSaStore::getIsAutoAllocation, SgConstants.IS_AUTO_Y);
        }

        List<SgCSaStore> saStores = sgCSaStoreMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(saStores)) {
            log.warn(LogUtil.format("获取可用聚合仓下配销仓数据映射为空，参数：{}", LOG_OBJ + "querySaStores"),
                    shareStoreId);
            return Collections.emptyList();
        }
        return saStores;
    }


    /**
     * 获取指定部门下配销仓数据映射
     *
     * @param deptIds 部门ID列表
     * @return 部门ID -> 配销仓列表 映射
     */
    public Map<Long, Set<Long>> queryDeptSaStoreIdsMap(Set<Long> deptIds) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return Collections.emptyMap();
        }

        List<SgCSaStore> saStores = sgCSaStoreMapper.selectList(new LambdaQueryWrapper<SgCSaStore>()
                .eq(SgCSaStore::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCSaStore::getCategory, SaCategoryEnum.VIP_CATEGORY.getCode())
                .in(SgCSaStore::getCpCDistributionOrgId, deptIds));
        if (CollectionUtils.isEmpty(saStores)) {
            log.warn(LogUtil.format("获取指定部门下配销仓数据映射为空，部门ID列表：{}", LOG_OBJ + "queryDeptSaStoreMap"),
                    deptIds);
            return Collections.emptyMap();
        }

        return saStores.stream()
                .collect(Collectors.groupingBy(SgCSaStore::getCpCDistributionOrgId,
                        Collectors.mapping(SgCSaStore::getId, Collectors.toSet())));
    }


    /**
     * 查询聚合仓可用库存-走redis
     *
     * @param shareStoreIds 聚合仓ID
     * @param skuId         SKU
     * @return 库存信息
     */
    public List<SgStorageRedisQuerySsExtResult> queryShareStorageAvailableWithRedis(Set<Long> shareStoreIds, Long skuId) {
        if (CollectionUtils.isEmpty(shareStoreIds) || Objects.isNull(skuId)) {
            return Collections.emptyList();
        }

        List<SgStorageRedisQuerySsModel> requestList = shareStoreIds.stream()
                .map(shareStoreId -> {
                    SgStorageRedisQuerySsModel req = new SgStorageRedisQuerySsModel();
                    req.setPsCSkuId(skuId);
                    req.setSgCShareStoreId(shareStoreId);
                    return req;
                }).collect(Collectors.toList());

        ValueHolderV14<List<SgStorageRedisQuerySsExtResult>> ret;
        try {
            ret = sgStorageRedisQueryLogic.querySsStorageAvailableWithRedis(requestList, R3SystemUserResource.getSystemRootUser());
        } catch (Exception e) {
            log.error(LogUtil.format("调用获取聚合仓可用库存接口出错,参数：{},错误信息：[{}]", LOG_OBJ + "queryShareStorageAvailableWithRedis"),
                    JSON.toJSONString(requestList), Throwables.getStackTraceAsString(e));
            throw e;
        }
        if (!ret.isOK()) {
            log.warn(LogUtil.format("获取聚合仓可用库存出错,聚合仓ID:{},商品ID:{}", LOG_OBJ + "queryShareStorageAvailableWithRedis"),
                    JSON.toJSONString(shareStoreIds), skuId);
            throw new NDSException("获取聚合仓可用库存出错:" + ret.getMessage());
        }

        if (CollectionUtils.isEmpty(ret.getData())) {
            log.warn(LogUtil.format("获取聚合仓库存数据为空，param:{},result:{}", LOG_OBJ + "queryShareStorageAvailableWithRedis"), requestList, ret);
            return Collections.emptyList();
        }
        return ret.getData();
    }

    /**
     * 查询聚合仓可用库存-走redis
     *
     * @param shareStoreId 聚合仓ID
     * @param skuIds       SKU列表
     * @return 库存信息
     */
    public List<SgStorageRedisQuerySsExtResult> queryShareStorageAvailableWithRedis(Long shareStoreId, Collection<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds) || Objects.isNull(shareStoreId)) {
            return Collections.emptyList();
        }

        List<SgStorageRedisQuerySsModel> requestList = skuIds.stream()
                .map(skuId -> {
                    SgStorageRedisQuerySsModel req = new SgStorageRedisQuerySsModel();
                    req.setPsCSkuId(skuId);
                    req.setSgCShareStoreId(shareStoreId);
                    return req;
                }).collect(Collectors.toList());

        ValueHolderV14<List<SgStorageRedisQuerySsExtResult>> ret;
        try {
            ret = sgStorageRedisQueryLogic.querySsStorageAvailableWithRedis(requestList, R3SystemUserResource.getSystemRootUser());
        } catch (Exception e) {
            log.error(LogUtil.format("调用获取聚合仓可用库存接口出错,参数：{},错误信息：[{}]", LOG_OBJ + "queryShareStorageAvailableWithRedis"),
                    JSON.toJSONString(requestList), Throwables.getStackTraceAsString(e));
            throw e;
        }
        if (!ret.isOK()) {
            log.warn(LogUtil.format("获取聚合仓可用库存出错,聚合仓ID:{},商品ID:{}", LOG_OBJ + "queryShareStorageAvailableWithRedis"),
                    shareStoreId, JSON.toJSONString(skuIds));
            throw new NDSException("获取聚合仓可用库存出错:" + ret.getMessage());
        }

        if (CollectionUtils.isEmpty(ret.getData())) {
            log.warn(LogUtil.format("获取聚合仓库存数据为空，param:{},result:{}", LOG_OBJ + "queryShareStorageAvailableWithRedis"), requestList, ret);
            return Collections.emptyList();
        }
        return ret.getData();
    }

//    /**
//     * 获取配销仓可用库存
//     *
//     * @param skuId      商品ID
//     * @param saStoreIds 配销仓ID列表
//     * @return
//     */
//    public List<SgBSaStorage> querySaStorage(Long skuId, Set<Long> saStoreIds) {
//        if (Objects.isNull(skuId) || CollectionUtils.isEmpty(saStoreIds)) {
//            log.error(LogUtil.format("获取配销仓可用库存列表参数非法，商品ID：{},配销仓ID列表：{}", LOG_OBJ + "querySaStorage"),
//                    skuId, saStoreIds);
//            throw new NDSException("获取配销仓可用库存列表参数非法");
//        }
//        List<SgBSaStorage> saStorages = sgBSaStorageMapper.selectList(new LambdaQueryWrapper<SgBSaStorage>()
//                .eq(SgBSaStorage::getIsactive, SgConstants.IS_ACTIVE_Y)
//                .eq(SgBSaStorage::getPsCSkuId, skuId)
//                .in(SgBSaStorage::getSgCSaStoreId, saStoreIds));
//        if (CollectionUtils.isEmpty(saStorages)) {
//            log.warn(LogUtil.format("获取配销仓可用库存列表为空,商品ID：{},配销仓ID列表：{}", LOG_OBJ + "querySaStorage"),
//                    skuId, JSON.toJSONString(saStoreIds));
//            return Collections.emptyList();
//        }
//        return saStorages;
//    }


//    /**
//     * 查询配销仓可用库存大于0的所有SKU与库存
//     *
//     * @param saStoreIds 配销仓ID列表
//     * @return 库存信息列表（注意对象只有三个属性：PS_C_SKU_ID、SG_C_SA_STORE_ID、QTY_AVAILABLE）
//     */
//    public List<SgBSaStorage> querySaStorageMap(Set<Long> saStoreIds) {
//        List<SgBSaStorage> saStorages = sgBSaStorageMapper.selectSaSkuStorageMap(saStoreIds);
//        if (CollectionUtils.isEmpty(saStorages)) {
//            log.warn(LogUtil.format("获取配销仓可用库存列表为空,配销仓ID列表：{}", LOG_OBJ + "querySaStorageMap"),
//                    JSON.toJSONString(saStoreIds));
//            return Collections.emptyList();
//        }
//        return saStorages;
//    }


    /**
     * 获取配销仓剩余库存大于0的所有SKU
     *
     * @param excludeSku 排除掉的SKU
     * @param saStoreIds 配销仓ID列表
     * @return 配销仓库存信息
     */
    public List<SgBSaStorage> querySaStorageMap2Back(Set<Long> excludeSku, Set<Long> saStoreIds) {
        LambdaQueryWrapper<SgBSaStorage> wrapper = new LambdaQueryWrapper<SgBSaStorage>()
                .eq(SgBSaStorage::getIsactive, SgConstants.IS_ACTIVE_Y)
                .gt(SgBSaStorage::getQtyAvailable, BigDecimal.ZERO);
        if (!CollectionUtils.isEmpty(saStoreIds)) {
            wrapper.in(SgBSaStorage::getSgCSaStoreId, saStoreIds);
        }
        if (!CollectionUtils.isEmpty(excludeSku)) {
            wrapper.notIn(SgBSaStorage::getPsCSkuId, excludeSku);
        }
        List<SgBSaStorage> saStorages = sgBSaStorageMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(saStorages)) {
            log.warn(LogUtil.format("获取配销仓剩余库存大于0的所有SKU,排除的SKU列表：{},配销仓ID列表：{}", LOG_OBJ + "querySaStorageMap2Back"),
                    JSON.toJSONString(excludeSku), JSON.toJSONString(saStoreIds));
            return Collections.emptyList();
        }
        return saStorages;
    }

    /**
     * 获取该SKU下所有库存大于0的配销仓库存信息
     *
     * @param skuId      SKU
     * @param saStoreIds 配销仓ID列表
     * @return 配销仓库存信息
     */
    public List<SgBSaStorage> querySaStorage2Back(Long skuId, Set<Long> saStoreIds) {
        List<SgBSaStorage> saStorages = sgBSaStorageMapper.selectList(new LambdaQueryWrapper<SgBSaStorage>()
                .eq(SgBSaStorage::getIsactive, SgConstants.IS_ACTIVE_Y)
                .gt(SgBSaStorage::getQtyAvailable, BigDecimal.ZERO)
                .in(SgBSaStorage::getSgCSaStoreId, saStoreIds)
                .eq(SgBSaStorage::getPsCSkuId, skuId));
        if (CollectionUtils.isEmpty(saStorages)) {
            log.warn(LogUtil.format("获取该SKU下所有库存大于0的配销仓库存信息,SKUID：{}，配销仓ID列表:{}", LOG_OBJ + "qeurySaStorage2Back"),
                    skuId, JSON.toJSONString(saStoreIds));
            return Collections.emptyList();
        }
        return saStorages;
    }

    /**
     * 执行分货退货，sku->聚合仓级别
     *
     * @param skuId        sku
     * @param shareStoreId 聚合仓ID
     * @param saStores     配销仓列表
     * @param typeEnum     执行类型
     */
    @Transactional(rollbackFor = Throwable.class)
    public void allocationReturnSkuByShareStore(Long skuId, Long shareStoreId, List<SgCSaStore> saStores,
                                                SgDistributionTypeEnum typeEnum, String versionBi) {
        /*配销仓ID汇总*/
        Set<Long> saIds = ListUtils.emptyIfNull(saStores).stream().map(SgCSaStore::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(saIds)) {
            log.info(LogUtil.format("当前聚合仓下无配销仓，聚合仓ID:{}", LOG_OBJ + "handlerByShareStore"), shareStoreId);
            return;
        }
        /*获取该SKU下所有库存大于0的配销仓库存信息（用于拉回）*/
        List<SgBSaStorage> storageList = querySaStorage2Back(skuId, saIds);
        /*配销仓ID->可用库存*/
        List<Pair<Long, BigDecimal>> pairList = CollectionUtils.emptyIfNull(storageList).stream()
                .map(obj -> Pair.of(obj.getSgCSaStoreId(), obj.getQtyAvailable())).collect(Collectors.toList());
        try {
            /*生成并审核分货退货单*/
            doAllocationReturn(shareStoreId, skuId, pairList, typeEnum, versionBi);
        } catch (Exception e) {
            log.warn(LogUtil.format("拉回存时出错，聚合仓ID：{},商品ID：{}，配销仓ID+退货分货量映射列表：{},异常信息：{}", LOG_OBJ + "execute"),
                    shareStoreId, skuId, JSON.toJSONString(pairList), Throwables.getStackTraceAsString(e));
            throw e;
        }
    }

}
