package com.burgeon.r3.sg.share.services.ryytndistribution;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCDepartmentMonthDemandLog;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgCDepartmentMonthDemandLogMapper;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 需求满足日志
 *
 * <AUTHOR>
 * @since 2024-12-25 15:29
 */
@Slf4j
@Component
public class SgCDepartmentMonthDemandLogService {
    @Resource
    private SgCDepartmentMonthDemandLogMapper sgCDepartmentMonthDemandLogMapper;

    public void createLogList(List<SgCDepartmentMonthDemandLog> demandLogList, User loginUser) {
        if (CollectionUtils.isEmpty(demandLogList)) {
            return;
        }
        for (SgCDepartmentMonthDemandLog demandLog : demandLogList) {
            demandLog.setId(ModelUtil.getSequence(SgConstants.SG_C_DEPARTMENT_MONTH_DEMAND_LOG));
            StorageUtils.setBModelDefalutData(demandLog, loginUser);
        }

        StorageBasicUtils.batchInsertList(sgCDepartmentMonthDemandLogMapper, demandLogList,
                1000, "批量插入需求满足日志出错", R3SystemUserResource.getSystemRootUser());
    }

    public List<SgCDepartmentMonthDemandLog> queryForConvert(String versionBi) {
        return sgCDepartmentMonthDemandLogMapper.selectList(new QueryWrapper<SgCDepartmentMonthDemandLog>().lambda()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCDepartmentMonthDemandLog::getSourceType, SgConstantsIF.BILL_SHARE_ALLOCATION)
                .eq(SgCDepartmentMonthDemandLog::getVersionBi, versionBi)
                .isNotNull(SgCDepartmentMonthDemandLog::getConvertConfigId));
    }
}
