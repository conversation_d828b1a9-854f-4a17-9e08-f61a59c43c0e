package com.burgeon.r3.sg.share.filter.transfer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.SgBSaStorageMapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgBSaStorage;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareSaTransfer;
import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareSaTransferItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareSaTransferItemMapper;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareSaTransferMapper;
import com.burgeon.r3.sg.share.model.dto.SgBShareSaTransferDto;
import com.burgeon.r3.sg.share.model.dto.SgBShareSaTransferItemDto;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationBillSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnBillSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnItemSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationSaveItemRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationSaveRequst;
import com.burgeon.r3.sg.share.model.result.allocation.SgBShareAllocationReturnSaveAndSubmitResult;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationReturnSaveAndSubmitService;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationSaveService;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationSubmitService;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleItemFilter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/7 19:28
 */
@Slf4j
@Component
public class SgBShareSaTransferSubmitFilter extends BaseSingleItemFilter<SgBShareSaTransferDto, SgBShareSaTransferItemDto> {

    @Autowired
    private SgBShareAllocationSaveService sgBShareAllocationSaveService;
    @Autowired
    private SgBShareAllocationSubmitService sgBShareAllocationSubmitService;

    @Autowired
    private SgBShareAllocationReturnSaveAndSubmitService sgBShareAllocationReturnSaveAndSubmitService;

    @Autowired
    private SgBShareSaTransferMapper mapper;
    @Autowired
    private SgBShareSaTransferItemMapper itemMapper;

    @Autowired
    private SgBSaStorageMapper sgBSaStorageMapper;

    @Override
    public String getFilterMsgName() {
        return "配销仓调拨单审核";
    }

    @Override
    public Class<?> getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgBShareSaTransferDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "审核成功");
        SgBShareSaTransfer sgBShareSaTransfer = null;

        if (Objects.nonNull(mainObject.getId()) && mainObject.getId() > 0L) {
            sgBShareSaTransfer = mapper.selectById(mainObject.getId());
            v14.setCode(ResultCode.FAIL);

            if (Objects.isNull(sgBShareSaTransfer)) {
                v14.setMessage(Resources.getMessage("当前记录已不存在！"));
                return v14;
            }

            if (SgStoreConstants.BILL_STATUS_VOID == sgBShareSaTransfer.getStatus()) {
                v14.setMessage(Resources.getMessage("当前记录已作废，不允许审核！"));
                return v14;
            }

            if (SgStoreConstants.BILL_STATUS_SUBMIT == sgBShareSaTransfer.getStatus()) {
                v14.setMessage(Resources.getMessage("当前记录已审核，不允许重复审核！"));
                return v14;
            }
        }

        LambdaQueryWrapper<SgBShareSaTransferItem> itemLqw = new LambdaQueryWrapper<SgBShareSaTransferItem>()
                .eq(SgBShareSaTransferItem::getSgBShareSaTransferId, mainObject.getId());
        List<SgBShareSaTransferItem> sgBShareSaTransferItems = itemMapper.selectList(itemLqw);
        BigDecimal totItemQty = BigDecimal.ZERO;
        BigDecimal totItemAmt = BigDecimal.ZERO;
        Integer itemSize = sgBShareSaTransferItems.size();

        List<String> messages = new ArrayList<>();
        for (SgBShareSaTransferItem item : sgBShareSaTransferItems) {
            LambdaQueryWrapper<SgBSaStorage> eq = new LambdaQueryWrapper<SgBSaStorage>()
                    .eq(SgBSaStorage::getSgCSaStoreId, sgBShareSaTransfer.getSenderSaStoreId())
                    .eq(SgBSaStorage::getPsCSkuId, item.getPsCSkuId());
            SgBSaStorage sgBSaStorage = sgBSaStorageMapper.selectOne(eq);
            if (Objects.isNull(sgBSaStorage) ||
                    Objects.isNull(sgBSaStorage.getQtyAvailable()) ||
                    sgBSaStorage.getQtyAvailable().compareTo(item.getQty()) < 0) {
                messages.add(item.getPsCSkuEcode());
            }
            totItemQty = totItemQty.add(item.getQty());
            totItemAmt = totItemAmt.add(item.getAmt());
        }

        if (itemSize < 1 || totItemQty.compareTo(BigDecimal.ZERO) == 0) {
            v14.setMessage(Resources.getMessage("当前记录无明细，不允许审核！"));
            return v14;
        }

        if (messages.size() > 0) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(Resources.getMessage("[" + String.join(",", messages) + "]调拨数量大于可用数量，不允许审核！"));
            return v14;
        }

        mainObject.setTotRowNum(itemSize);
        mainObject.setTotQty(totItemQty);
        mainObject.setTotAmt(totItemAmt);

        //删除调拨数量是0的
        for (SgBShareSaTransferItem item : sgBShareSaTransferItems) {
            if (BigDecimal.ZERO.compareTo(item.getQty()) >= 0) {
                itemMapper.deleteById(item);
            }
        }

        mainObject.setStatus(SgStoreConstants.BILL_STATUS_SUBMIT);
        Date now = new Date();
        mainObject.setModifierid(loginUser.getId().longValue());
        mainObject.setModifiername(loginUser.getName());
        mainObject.setModifierename(loginUser.getEname());
        mainObject.setModifieddate(now);
        mainObject.setStatusId(loginUser.getId());
        mainObject.setStatusName(loginUser.getName());
        mainObject.setStatusEname(loginUser.getEname());
        mainObject.setStatusTime(now);

        SgBShareSaTransferSubmitFilter bean = ApplicationContextHandle.getBean(SgBShareSaTransferSubmitFilter.class);
        try {
            bean.createAndSubmitSaBill(sgBShareSaTransfer, loginUser);
        } catch (Exception ex) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(Resources.getMessage(ex.getMessage()));
            return v14;
        }

        return null;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgBShareSaTransferDto mainObject, User loginUser) {
        return null;
    }

    @Override
    public ValueHolderV14 execBeforeSubTable(SgBShareSaTransferDto mainObject, List<SgBShareSaTransferItemDto> subObjectList, User loginUser) {
        return null;
    }

    @Override
    public ValueHolderV14 execAfterSubTable(SgBShareSaTransferDto mainObject, List<SgBShareSaTransferItemDto> subObjectList, User loginUser) {
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 execSubmit(SgBShareSaTransferDto mainObject, User loginUser) {
        ValueHolderV14 v14 = execBeforeMainTable(mainObject, loginUser);
        return v14;
    }

    /**
     * 审批配销调拨单，创建并审核【分货退货单】后创建并审核【分货单】
     *
     * @param sgBShareSaTransfer
     * @param loginUser
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 createAndSubmitSaBill(SgBShareSaTransfer sgBShareSaTransfer, User loginUser) {
        //redis流水key
        List<String> redisBillFtpKeyList = new ArrayList<>();
        try {
            //先生成分货退货单，保证聚合仓有货
            ValueHolderV14<SgBShareAllocationReturnSaveAndSubmitResult> andSubmitAllocationReturn =
                    createAndSubmitAllocationReturn(sgBShareSaTransfer, loginUser, redisBillFtpKeyList);
            redisBillFtpKeyList.addAll(andSubmitAllocationReturn.getData().getRedisFtpKeys());

            if (andSubmitAllocationReturn.isOK()) {
                //后生成分货单
                createAndSubmitAllocation(sgBShareSaTransfer, loginUser, redisBillFtpKeyList);
            } else {
                AssertUtils.logAndThrow("生成分货退货单异常！");
            }
        } catch (Exception ex) {
            //异常库存回滚
            if (CollectionUtils.isNotEmpty(redisBillFtpKeyList)) {
                StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, loginUser);
            }
            AssertUtils.logAndThrow(ex.getMessage(), loginUser.getLocale());
        }

        return new ValueHolderV14(ResultCode.SUCCESS, "生成分货和分货退货单成功！");
    }

    /**
     * 生成分货单并自动审核服务
     *
     * @param sgBShareSaTransfer  配销仓调拨单
     * @param loginUser           loginuser
     * @param redisBillFtpKeyList 回滚的key
     * @return return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> createAndSubmitAllocation(SgBShareSaTransfer sgBShareSaTransfer,
                                                                    User loginUser,
                                                                    List<String> redisBillFtpKeyList) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("配销调拨单审核-生成分货单并自动审核服务，入参:{}",
                    "SgBShareSaTransferSubmitFilter.createAndSubmitAllocation"), JSONObject.toJSONString(sgBShareSaTransfer));
        }

        /*分货单保存*/
        SgBShareAllocationBillSaveRequst billSaveRequst = new SgBShareAllocationBillSaveRequst();
        SgBShareAllocationSaveRequst saveRequst = new SgBShareAllocationSaveRequst();
        List<SgBShareAllocationSaveItemRequst> itemList = new ArrayList<>();
        SgR3BaseRequest submitRequest = new SgR3BaseRequest();

        billSaveRequst.setObjId(-1L);
        saveRequst.setId(-1L);
        saveRequst.setRemark("由配销仓调拨单：[" + sgBShareSaTransfer.getBillNo() + "]审核生成！");
        saveRequst.setSourceBillId(sgBShareSaTransfer.getId());
        saveRequst.setSourceBillNo(sgBShareSaTransfer.getBillNo());
        saveRequst.setSourceBillType(SgConstantsIF.BILL_TYPE_SHARE_SA_TRANSFER);
        SgCSaStore saStore = CommonCacheValUtils.getSaStore(sgBShareSaTransfer.getReceiverSaStoreId());
        SgCShareStore shareStore = CommonCacheValUtils.getShareStore(saStore.getSgCShareStoreId());
        saveRequst.setSgCShareStoreId(shareStore.getId());
        saveRequst.setSgCShareStoreEcode(shareStore.getEcode());
        saveRequst.setSgCShareStoreEname(shareStore.getEname());
        saveRequst.setBillDate(new Date());

        LambdaQueryWrapper<SgBShareSaTransferItem> itemLqw = new LambdaQueryWrapper<SgBShareSaTransferItem>()
                .eq(SgBShareSaTransferItem::getSgBShareSaTransferId, sgBShareSaTransfer.getId());
        List<SgBShareSaTransferItem> sgBShareSaTransferItems = itemMapper.selectList(itemLqw);
        for (SgBShareSaTransferItem item : sgBShareSaTransferItems) {
            SgBShareAllocationSaveItemRequst itemRequst = new SgBShareAllocationSaveItemRequst();
            itemRequst.setId(-1L);
            itemRequst.setSgCSaStoreId(saStore.getId());
            itemRequst.setSgCSaStoreEcode(saStore.getEcode());
            itemRequst.setSgCSaStoreEname(saStore.getEname());
            itemRequst.setPsCSkuId(item.getPsCSkuId());
            itemRequst.setPsCSkuEcode(item.getPsCSkuEcode());
            itemRequst.setPsCProId(item.getPsCProId());
            itemRequst.setPsCProEcode(item.getPsCProEcode());
            itemRequst.setPsCProEname(item.getPsCProEname());
            itemRequst.setPsCSpec1Id(item.getPsCSpec1Id());
            itemRequst.setPsCSpec1Ecode(item.getPsCSpec1Ecode());
            itemRequst.setPsCSpec1Ename(item.getPsCSpec1Ename());
            itemRequst.setPsCSpec2Id(item.getPsCSpec2Id());
            itemRequst.setPsCSpec2Ecode(item.getPsCSpec2Ecode());
            itemRequst.setPsCSpec2Ename(item.getPsCSpec2Ename());
            itemRequst.setAmt(item.getAmt());
            itemRequst.setQty(item.getQty());
            itemRequst.setPriceList(item.getPriceList());
            itemRequst.setSourceBillItemId(sgBShareSaTransfer.getId());
            itemList.add(itemRequst);
        }
        billSaveRequst.setLoginUser(loginUser);
        billSaveRequst.setSgBShareAllocationSaveRequst(saveRequst);
        billSaveRequst.setSgBShareAllocationSaveItemRequsts(itemList);
        ValueHolderV14<SgR3BaseResult> saveRet;
        try {
            saveRet = sgBShareAllocationSaveService.save(billSaveRequst);
        } catch (Exception e) {
            log.error(LogUtil.format("配销调拨单审核-调用分货保存接口出错，参数：{}，错误信息：[{}]", "SgBShareSaTransferSubmitFilter.createAndSubmitAllocation"),
                    JSON.toJSONString(billSaveRequst), Throwables.getStackTraceAsString(e));
            throw new NDSException("配销调拨单审核-调用分货保存接口出错:" + e.getMessage());
        }
        if (!saveRet.isOK()) {
            log.warn(LogUtil.format("配销调拨单审核-保存分货单执行出错,param:{},result:{}", "SgBShareSaTransferSubmitFilter.createAndSubmitAllocation"),
                    JSON.toJSONString(billSaveRequst), JSON.toJSONString(saveRet));
            throw new NDSException("配销调拨单审核-保存分货单执行出错:" + saveRet.getMessage());
        }

        /*分货单审核*/
        JSONObject dataJo = saveRet.getData().getDataJo();
        Long objid = dataJo.getLong("objid");
        List<Long> submitIds = new ArrayList<>();
        submitIds.add(objid);
        submitRequest.setLoginUser(loginUser);
        submitRequest.setIds(submitIds);
        submitRequest.setObjId(objid);

        ValueHolderV14<SgR3BaseResult> submitRet;
        try {
            submitRet = sgBShareAllocationSubmitService.submitShareAllocation(submitRequest, false, false);
        } catch (Exception e) {
            log.error(LogUtil.format("配销调拨单审核-调用分货提交接口出错，保存参数：【{}】,保存结果：【{}】，提交参数：【{}】，错误信息：[{}]",
                            "SgBShareSaTransferSubmitFilter.createAndSubmitAllocation"),
                    JSON.toJSONString(billSaveRequst), JSON.toJSONString(saveRet), JSON.toJSONString(submitRequest), Throwables.getStackTraceAsString(e));
            throw new NDSException("配销调拨单审核-调用分货提交接口出错:" + e.getMessage());
        }
        if (!submitRet.isOK()) {
            log.warn(LogUtil.format("配销调拨单审核-提交分货单执行出错，保存参数：【{}】,保存结果：【{}】,提交参数：【{}】，提交结果：【{}】",
                            "SgBShareSaTransferSubmitFilter.createAndSubmitAllocation"),
                    JSON.toJSONString(billSaveRequst), JSON.toJSONString(saveRet),
                    JSON.toJSONString(submitRequest), JSON.toJSONString(submitRet));
            throw new NDSException("配销调拨单审核-提交分货单执行出错:" + submitRet.getMessage());
        }

        if (submitRet.getData() != null) {
            List<String> redisBillFtpKey = (List<String>) dataJo.get("redisBillFtpKey");
            if (CollectionUtils.isNotEmpty(redisBillFtpKey)) {
                redisBillFtpKeyList.addAll(redisBillFtpKey);
            }
        }

        return new ValueHolderV14(ResultCode.SUCCESS, "创建并审核分货单成功！");
    }

    /**
     * 生成分货退货单并自动审核服务
     *
     * @param sgBShareSaTransfer  配销仓调拨单
     * @param loginUser           loginuser
     * @param redisBillFtpKeyList 回滚的key
     * @return return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgBShareAllocationReturnSaveAndSubmitResult> createAndSubmitAllocationReturn(SgBShareSaTransfer sgBShareSaTransfer,
                                                                                                       User loginUser,
                                                                                                       List<String> redisBillFtpKeyList) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("生成分货退货单并自动审核服务,入参:{}",
                    "SgBShareSaTransferSubmitFilter.createAndSubmitAllocationReturn"), JSONObject.toJSONString(sgBShareSaTransfer));
        }
        SgBShareAllocationReturnBillSaveRequst returnBillSaveRequst = new SgBShareAllocationReturnBillSaveRequst();
        SgBShareAllocationReturnSaveRequst returnSaveRequst = new SgBShareAllocationReturnSaveRequst();
        List<SgBShareAllocationReturnItemSaveRequst> itemList = new ArrayList<>();

        SgCSaStore saStore = CommonCacheValUtils.getSaStore(sgBShareSaTransfer.getSenderSaStoreId());
        returnBillSaveRequst.setObjId(-1L);
        returnSaveRequst.setId(-1L);
        returnSaveRequst.setSgCSaStoreId(saStore.getId());
        returnSaveRequst.setSgCSaStoreEcode(saStore.getEcode());
        returnSaveRequst.setSgCSaStoreEname(saStore.getEname());
        SgCShareStore shareStore = CommonCacheValUtils.getShareStore(saStore.getSgCShareStoreId());
        returnSaveRequst.setSgCShareStoreId(shareStore.getId());
        returnSaveRequst.setSgCShareStoreEcode(shareStore.getEcode());
        returnSaveRequst.setSgCShareStoreEname(shareStore.getEname());
        returnSaveRequst.setSourceBillType(SgConstantsIF.BILL_TYPE_SHARE_SA_TRANSFER);
        returnSaveRequst.setSourceBillNo(sgBShareSaTransfer.getBillNo());
        returnSaveRequst.setSourceBillId(sgBShareSaTransfer.getId());
        returnSaveRequst.setRemark("由配销仓调拨单：[" + sgBShareSaTransfer.getBillNo() + "]审核生成！");
        returnSaveRequst.setTotQty(sgBShareSaTransfer.getTotQty());
        returnSaveRequst.setBillDate(new Date());
        returnSaveRequst.setIsReturnDemand(sgBShareSaTransfer.getIsReturnDemand());

        LambdaQueryWrapper<SgBShareSaTransferItem> itemLqw = new LambdaQueryWrapper<SgBShareSaTransferItem>()
                .eq(SgBShareSaTransferItem::getSgBShareSaTransferId, sgBShareSaTransfer.getId());
        List<SgBShareSaTransferItem> sgBShareSaTransferItems = itemMapper.selectList(itemLqw);
        for (SgBShareSaTransferItem item : sgBShareSaTransferItems) {
            SgBShareAllocationReturnItemSaveRequst returnItemSaveRequst = new SgBShareAllocationReturnItemSaveRequst();
            returnItemSaveRequst.setId(-1L);
            returnItemSaveRequst.setPsCSkuId(item.getPsCSkuId());
            returnItemSaveRequst.setPsCSkuEcode(item.getPsCSkuEcode());
            returnItemSaveRequst.setPsCProId(item.getPsCProId());
            returnItemSaveRequst.setPsCProEcode(item.getPsCProEcode());
            returnItemSaveRequst.setPsCProEname(item.getPsCProEname());
            returnItemSaveRequst.setPsCSpec1Id(item.getPsCSpec1Id());
            returnItemSaveRequst.setPsCSpec1Ecode(item.getPsCSpec1Ecode());
            returnItemSaveRequst.setPsCSpec1Ename(item.getPsCSpec1Ename());
            returnItemSaveRequst.setPsCSpec2Id(item.getPsCSpec2Id());
            returnItemSaveRequst.setPsCSpec2Ecode(item.getPsCSpec2Ecode());
            returnItemSaveRequst.setPsCSpec2Ename(item.getPsCSpec2Ename());
            returnItemSaveRequst.setQty(item.getQty());
            returnItemSaveRequst.setAmt(item.getAmt());
            returnItemSaveRequst.setPriceList(item.getPriceList());
            returnItemSaveRequst.setSourceBillItemId(sgBShareSaTransfer.getId());
            itemList.add(returnItemSaveRequst);
        }

        returnBillSaveRequst.setLoginUser(loginUser);
        returnBillSaveRequst.setAllocationReturnSaveRequst(returnSaveRequst);
        returnBillSaveRequst.setAllocationReturnItemSaveRequst(itemList);

        List<SgBShareAllocationReturnBillSaveRequst> requestList = new ArrayList<>();
        requestList.add(returnBillSaveRequst);

        ValueHolderV14<SgBShareAllocationReturnSaveAndSubmitResult> ret;
        try {
            ret = sgBShareAllocationReturnSaveAndSubmitService.saveAndSubmit2(requestList);
        } catch (Exception e) {
            log.error(LogUtil.format("配销调拨单审核-调用分货退货保存并提交接口出错，参数：{}，错误信息：[{}]",
                            "SgBShareSaTransferSubmitFilter.createAndSubmitAllocationReturn"),
                    JSON.toJSONString(requestList), Throwables.getStackTraceAsString(e));
            throw new NDSException("配销调拨单审核-调用分货退货保存并提交接口出错:" + e.getMessage());
        }

        SgBShareAllocationReturnSaveAndSubmitResult retData = ret.getData();
        if (!ret.isOK()) {
            log.warn(LogUtil.format("配销调拨单审核-分货退货单执行出错,param:{},result:{}",
                            "SgBShareSaTransferSubmitFilter.createAndSubmitAllocationReturn"),
                    JSON.toJSONString(requestList), JSON.toJSONString(ret));
            /*执行出错，回滚库存*/
            if (Objects.nonNull(retData) && CollectionUtils.isNotEmpty(retData.getRedisFtpKeys())) {
                redisBillFtpKeyList.addAll((retData.getRedisFtpKeys()));
            }

            throw new NDSException("配销调拨单审核-分货退货单执行出错:" + ret.getMessage());
        }

        return ret;
    }
}
