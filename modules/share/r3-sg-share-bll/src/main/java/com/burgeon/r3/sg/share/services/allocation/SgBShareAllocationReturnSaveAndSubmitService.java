package com.burgeon.r3.sg.share.services.allocation;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.model.result.SgStorageUpdateResult;
import com.burgeon.r3.sg.core.common.R3ParamConstants;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnBillSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnItemSingleSubmitRequest;
import com.burgeon.r3.sg.share.model.result.allocation.SgBShareAllocationReturnSaveAndSubmitResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: hwy
 * @time: 2021/5/25 13:25
 */
@Component
@Slf4j
public class SgBShareAllocationReturnSaveAndSubmitService {

    @Autowired
    private SgBShareAllocationReturnSaveService sgBShareAllocationReturnSaveService;

    @Autowired
    private SgBShareAllocationReturnSingleSubmitService sgBShareAllocationReturnSingleSubmitService;

    /**
     * @param requestList:
     * @Description: 新增并审核分货退货单
     * @Author: hwy
     * @Date: 2021/5/25 13:30
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14
     **/
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<List<Long>> saveAndSubmit(List<SgBShareAllocationReturnBillSaveRequst> requestList) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBShareAllocationReturnSaveAndSubmitService.saveAndSubmit param:{}", JSONObject.toJSONString(requestList));
        }
        ValueHolderV14<List<Long>> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        checkParam(requestList);
        int errorCount = 0;
        int successCount = 0;
        List<Long> ids = new ArrayList<>();
        for (SgBShareAllocationReturnBillSaveRequst request : requestList) {
            try {
                ValueHolderV14<SgR3BaseResult> saveResult = sgBShareAllocationReturnSaveService.saveWithNewTrans(request);
                if (!saveResult.isOK()) {
                    log.warn("SgBShareAllocationReturnSaveAndSubmitService.saveAndSubmit  新增分货单失败:{} 失败数量：{}",
                            saveResult.getMessage(),
                            errorCount++
                    );
                    continue;
                }
                SgR3BaseResult data = saveResult.getData();
                JSONObject dataJo = data.getDataJo();
                Long id = dataJo.getLong(R3ParamConstants.OBJID);
                SgBShareAllocationReturnItemSingleSubmitRequest submitRequest = new SgBShareAllocationReturnItemSingleSubmitRequest();
                submitRequest.setId(id);
                submitRequest.setLoginUser(request.getLoginUser());
                ValueHolderV14 sumbitResult = sgBShareAllocationReturnSingleSubmitService.singelSubmitWhitNewTrans(submitRequest);
                if (!sumbitResult.isOK()) {
                    log.warn("SgBShareAllocationReturnSaveAndSubmitService.saveAndSubmit 来源单据id:{} 审核分货单失败:{}",
                            sumbitResult.getMessage(),
                            errorCount++
                    );
                    continue;
                }
                successCount++;
                ids.add(id);
            } catch (Exception e) {
                errorCount++;
                log.warn("SgBShareAllocationReturnSaveAndSubmitService.saveAndSubmit. exception_has_occured:{}",
                        Throwables.getStackTraceAsString(e));
                valueHolderV14.setMessage("批量新增审核分货退货单异常,成功数量:"
                        + successCount + " 失败数量:" + errorCount + ",异常：" + e.getMessage());
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setData(ids);
                return valueHolderV14;
            }
        }
        valueHolderV14.setMessage("批量新增审核分货退货单,成功数量:" + successCount + " 失败数量:" + errorCount);
        valueHolderV14.setData(ids);
        return valueHolderV14;
    }

    /**
     * 加入当前事务
     *
     * @param requestList:
     * @Description: 新增并审核分货退货单
     * @Author: hwy
     * @Date: 2021/5/25 13:30
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14
     **/
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgBShareAllocationReturnSaveAndSubmitResult> saveAndSubmit2(List<SgBShareAllocationReturnBillSaveRequst> requestList) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("新增并审核分货退货单，param:{}",
                    "SgBShareAllocationReturnSaveAndSubmitService.saveAndSubmit2"), JSONObject.toJSONString(requestList));
        }
        ValueHolderV14<SgBShareAllocationReturnSaveAndSubmitResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        checkParam(requestList);
        int errorCount = 0;
        int successCount = 0;

        SgBShareAllocationReturnSaveAndSubmitResult result = new SgBShareAllocationReturnSaveAndSubmitResult();

        List<Long> ids = new ArrayList<>();
        List<String> redisFtpKeys = new ArrayList<>();
        for (SgBShareAllocationReturnBillSaveRequst request : requestList) {
            try {
                ValueHolderV14<SgR3BaseResult> saveResult = sgBShareAllocationReturnSaveService.saveWithNewTrans2(request);
                if (!saveResult.isOK()) {
                    log.debug(LogUtil.format("新增分货单失败:{} 失败数量：{}", "SgBShareAllocationReturnSaveAndSubmitService.saveAndSubmit2"),
                            saveResult.getMessage(), errorCount++);
                    continue;
                }
                SgR3BaseResult data = saveResult.getData();
                JSONObject dataJo = data.getDataJo();
                Long id = dataJo.getLong(R3ParamConstants.OBJID);
                SgBShareAllocationReturnItemSingleSubmitRequest submitRequest = new SgBShareAllocationReturnItemSingleSubmitRequest();
                submitRequest.setId(id);
                submitRequest.setIsReturnDemand(request.getAllocationReturnSaveRequst().getIsReturnDemand());
                ValueHolderV14<SgStorageUpdateResult> submitResult = sgBShareAllocationReturnSingleSubmitService.singelSubmitWhitNewTrans2(submitRequest);

                AssertUtils.isTrue(submitResult.isOK(), "分货退货单审核失败，失败原因：" + submitResult.getMessage());
                successCount++;
                ids.add(id);
                redisFtpKeys.addAll(submitResult.getData().getRedisBillFtpKeyList());
            } catch (Exception e) {
                errorCount++;
                log.error("SgBShareAllocationReturnSaveAndSubmitService.saveAndSubmit exception_has_occured:{}",
                        Throwables.getStackTraceAsString(e));
                valueHolderV14.setMessage("批量新增审核分货退货单异常，成功数量:"
                        + successCount + " 失败数量:" + errorCount + ",异常：" + e.getMessage());
                valueHolderV14.setCode(ResultCode.FAIL);
                result.setIds(ids);
                result.setRedisFtpKeys(redisFtpKeys);
                valueHolderV14.setData(result);
                return valueHolderV14;
            }
        }
        valueHolderV14.setMessage("批量新增审核分货退货单 成功数量:" + successCount + " 失败数量:" + errorCount);

        result.setIds(ids);
        result.setRedisFtpKeys(redisFtpKeys);
        valueHolderV14.setData(result);
        return valueHolderV14;
    }

    /**
     * @param requestList:
     * @Description: 参数校验
     * @Author: hwy
     * @Date: 2021/5/25 14:38
     * @return: void
     **/
    private void checkParam(List<SgBShareAllocationReturnBillSaveRequst> requestList) {
        AssertUtils.notNull(requestList, "入参不能为空！");
        for (SgBShareAllocationReturnBillSaveRequst request : requestList) {
            AssertUtils.notNull(request.getAllocationReturnSaveRequst(), "主表信息不能为空！");
            AssertUtils.notNull(request.getAllocationReturnItemSaveRequst(), "明细信息不能为空！");
        }
    }

}