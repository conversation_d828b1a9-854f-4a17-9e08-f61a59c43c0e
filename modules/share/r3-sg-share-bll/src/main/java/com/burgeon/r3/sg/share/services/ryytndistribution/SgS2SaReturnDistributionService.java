package com.burgeon.r3.sg.share.services.ryytndistribution;

import com.alibaba.fastjson.JSON;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.enums.SgDistributionTypeEnum;
import com.burgeon.r3.sg.core.enums.SgMonthDemandFromEnum;
import com.burgeon.r3.sg.core.model.table.basic.SgBSaStorage;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCPlanConvertVersion;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.share.common.DistributionDingTalkTableRobot;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 分货拉回服务（拉回所有配销仓）
 *
 * <AUTHOR>
 * @since 2023-03-01 10:38
 */
@Slf4j
@Service
public class SgS2SaReturnDistributionService {
    /**
     * 日志OBJ
     */
    private static final String LOG_OBJ = "SgS2SaReturnDistributionService.";

    @Resource
    private SgS2SaAutoDistributionManager sgS2SaAutoDistributionManager;

    @Resource
    private SgCDistributionMonthLogService sgCDistributionMonthLogService;


    @Resource
    private SgCDepartmentMonthDemandService sgCDepartmentMonthDemandService;

    public ValueHolderV14<Void> executeReturn(SgCPlanConvertVersion version) {
        int successSkuCnt = 0;
        int totalSkuCount = 0;
        CusRedisTemplate<String, String> template = RedisMasterUtils.getStrRedisTemplate();
        DistributionDingTalkTableRobot robot = new DistributionDingTalkTableRobot(SgDistributionTypeEnum.PLAN_RETURN);

        /*获取所有可用聚合仓*/
        List<SgCShareStore> shareStoreList = sgS2SaAutoDistributionManager.querySgShareStores();
        /*获取聚合仓下所有配销仓，聚合仓ID->配销仓列表*/
        Map<Long, List<SgCSaStore>> shareIdSaStoreMap = sgS2SaAutoDistributionManager.querySaStoresMap(shareStoreList, false);

        /*不滚动的SKU需要加到拉回记录表，但是拉回量是空的*/
        Set<Long> otherSkuIds = sgCDepartmentMonthDemandService.queryOtherSku2ForceReturn(version.getVersionBi());
        /*逐个聚合仓处理*/
        for (Map.Entry<Long, List<SgCSaStore>> shareSaStoreEntry : shareIdSaStoreMap.entrySet()) {
            Long shareStoreId = shareSaStoreEntry.getKey();
            List<SgCSaStore> saStores = shareSaStoreEntry.getValue();
            /*配销仓ID汇总*/
            Set<Long> saIds = ListUtils.emptyIfNull(saStores).stream().map(SgCSaStore::getId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(saIds)) {
                log.info(LogUtil.format("当前聚合仓下无配销仓，聚合仓ID:{}", LOG_OBJ + "execute"), shareStoreId);
                continue;
            }

            List<Long> deptIds = saStores.stream().map(SgCSaStore::getCpCDistributionOrgId).distinct()
                    .filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(deptIds)) {
                log.info(LogUtil.format("当前聚合仓下配销仓无所属部门，聚合仓ID:{}", LOG_OBJ + "execute"), shareStoreId);
                continue;
            }
            /*需求表中该聚合仓部门的所有SKU，真正拉回的SKU会比这个多，这里查这个是为了防止需求表中的品没被拉回*/
            Set<Long> demandSkus = sgCDepartmentMonthDemandService.querySku2Return(deptIds, version.getVersionBi(), SgMonthDemandFromEnum.SCP);

            /*已经被拉回的SKU不再次操作，幂等判断*/
            Set<Long> returnedSkuIds = sgCDistributionMonthLogService.querySkuByShareStoreId(shareStoreId, version.getVersionBi());
            /*获取配销仓下所有可用库存大于0的SKU对应的库存信息列表：这里是直接查询DB的配销仓库存，注意DB与redis差异会导致审核失败，以后可以考虑查redis*/
            List<SgBSaStorage> storageList = sgS2SaAutoDistributionManager.querySaStorageMap2Back(returnedSkuIds, saIds);
            /*根据SKU分组，逐个SKU处理,不拉回的SKU要给过滤掉*/
            Map<Long, List<SgBSaStorage>> skuStorageMap = storageList.stream()
                    .filter(obj -> Objects.nonNull(obj.getPsCSkuId()))
                    .filter(obj -> !otherSkuIds.contains(obj.getPsCSkuId()))
                    .collect(Collectors.groupingBy(SgBSaStorage::getPsCSkuId));

            totalSkuCount += skuStorageMap.size();
            /*逐个SKU处理，执行分货退货单（一SKU多配销仓）逻辑*/
            for (Map.Entry<Long, List<SgBSaStorage>> skuStorage : skuStorageMap.entrySet()) {
                /*配销仓ID->可用库存*/
                List<Pair<Long, BigDecimal>> pairList = skuStorage.getValue().stream()
                        .map(obj -> Pair.of(obj.getSgCSaStoreId(), obj.getQtyAvailable())).collect(Collectors.toList());
                try {
                    /*生成并审核分货退货单*/
                    sgS2SaAutoDistributionManager.doAllocationReturn(shareStoreId, skuStorage.getKey(), pairList,
                            SgDistributionTypeEnum.PLAN_RETURN, version.getVersionBi());
                    /*成功就移除：需求记录*/
                    demandSkus.remove(skuStorage.getKey());
                    /*成功就移除：补偿的SKU*/
                    removeFailFromRedis(template, skuStorage.getKey(), shareStoreId, version.getVersionBi());
                    successSkuCnt++;
                } catch (Exception e) {
                    log.warn(LogUtil.format("月初拉回存时出错，聚合仓ID：{},商品ID：{}，配销仓ID+退货分货量映射列表：{},异常信息：{}", LOG_OBJ + "execute"),
                            shareStoreId, skuStorage.getKey(), JSON.toJSONString(pairList), Throwables.getStackTraceAsString(e));

                    /*手工拉回出错的数据到REDIS*/
                    addFail2Redis(template, skuStorage.getKey(), shareStoreId, version.getVersionBi());

                    /*添加到告警信息*/
                    robot.addErrorSku(skuStorage.getKey(), shareStoreId, e.getMessage());
                }
            }

            if (CollectionUtils.isNotEmpty(otherSkuIds)) {
                demandSkus.addAll(otherSkuIds);
            }
            /*假如这个聚合仓某个SKU上个版本所有配销仓都没有库存，但是这次有需求，也是需要插入到拉回记录表的，只不过拉回量是空的*/
            sgCDistributionMonthLogService.createMonthByForce(demandSkus, shareStoreId, SgDistributionTypeEnum.PLAN_RETURN, version.getVersionBi());
        }
        /*发送钉钉告警*/
        robot.sendTableMsg();

        log.info(LogUtil.format("拉回库存，成功SKU数：{},总SKU数：{}", LOG_OBJ + "execute"), successSkuCnt, totalSkuCount);
        return new ValueHolderV14<>(ResultCode.SUCCESS, "执行完成");
    }

    /**
     * 拉回redis中错误的sku
     * 定时执行前，会先调用一次
     *
     * @param typeEnum 执行类型
     */
    public String compensationPlanReturnSkus(SgDistributionTypeEnum typeEnum, String versionBi) {
        try {
            return returnRedisSku(typeEnum, versionBi);
        } catch (Exception e) {
            log.error(LogUtil.format("计划系统拉回补偿执行报错", LOG_OBJ + "compensationPlanReturnSkus"));
        }
        return "计划系统拉回补偿执行报错";
    }

    private String returnRedisSku(SgDistributionTypeEnum typeEnum, String versionBi) {
        DistributionDingTalkTableRobot robot = new DistributionDingTalkTableRobot(typeEnum);

        /*手工拉回出错的数据到REDIS*/
        CusRedisTemplate<String, String> template = RedisMasterUtils.getStrRedisTemplate();
        SetOperations<String, String> opsForSet = template.opsForSet();
        Set<String> members = opsForSet.members(SgShareConstants.DISTRIBUTION_BACK_REDIS_KEY + versionBi);
        if (CollectionUtils.isEmpty(members)) {
            return "待补偿拉回SKU数量:0";
        }
        log.info(LogUtil.format("从REDIS拉回出错的数据:{}", LOG_OBJ + "returnRedisSku", "members"), JSON.toJSONString(members));

        int success = 0;
        for (String member : members) {
            String[] arrStr = member.split(SgConstantsIF.MAP_KEY_DIVIDER);
            Long skuId = Long.valueOf(arrStr[0]);
            Long shareId = Long.valueOf(arrStr[1]);

            try {
                /*配销仓列表*/
                List<SgCSaStore> saStoreList = sgS2SaAutoDistributionManager.querySaStores2Return(shareId, false);
                if (CollectionUtils.isEmpty(saStoreList)) {
                    log.info(LogUtil.format("拉回失败，配销仓列表为空，skuId:{},shareId:{}", LOG_OBJ + "returnRedisSku"), skuId, shareId);
                    continue;
                }

                sgS2SaAutoDistributionManager.allocationReturnSkuByShareStore(skuId, shareId, saStoreList, typeEnum, versionBi);
                /*成功就删掉*/
                removeFailFromRedis(opsForSet, skuId, shareId, versionBi);
                success++;
            } catch (Exception e) {
                log.warn(LogUtil.format("分货拉回失败,拉回类型:{},sku:{},shareId:{},err:{}",
                        LOG_OBJ + "returnRedisSku"), typeEnum.getDesc(), skuId, shareId, Throwables.getStackTraceAsString(e));
                String message = e.getMessage();

                if (e instanceof NDSException) {
                    BasicPsQueryService service = ApplicationContextHandle.getBean(BasicPsQueryService.class);
                    SkuInfoQueryRequest req = new SkuInfoQueryRequest();
                    req.setSkuIdList(Arrays.asList(skuId));
                    Map<Long, PsCProSkuResult> skuResultHashMap = service.getSkuInfo(req);
                    if (MapUtils.isEmpty(skuResultHashMap)) {
                        /*如果是SKU被作废了，就删掉*/
                        removeFailFromRedis(opsForSet, skuId, shareId, versionBi);
                        message = message + "【SKU被作废,不再重试】";
                    }
                }

                /*手工拉回出错的数据到REDIS*/
                /*addFail2Redis(template, skuId, shareId, versionBi);*/
                /*添加到告警信息*/
                robot.addErrorSku(skuId, shareId, message);
            }
        }
        /*发送钉钉告警*/
        robot.sendTableMsg();

        return "待补偿拉回SKU数量：" + members.size() + ",成功数量：" + success;
    }


    /**
     * 将执行失败的的聚合仓与sku存到redis
     *
     * @param template  template
     * @param skuId     sku
     * @param shareId   聚合仓ID
     * @param versionBi
     */
    private void addFail2Redis(CusRedisTemplate<String, String> template, Long skuId, Long shareId, String versionBi) {
        SetOperations<String, String> opsForSet = template.opsForSet();
        opsForSet.add(SgShareConstants.DISTRIBUTION_BACK_REDIS_KEY + versionBi, skuId + SgConstantsIF.MAP_KEY_DIVIDER + shareId);

        /*template.expire(SgShareConstants.DISTRIBUTION_BACK_REDIS_KEY + versionBi, genMonthLastMilliseconds(), TimeUnit.MILLISECONDS);*/
        template.expire(SgShareConstants.DISTRIBUTION_BACK_REDIS_KEY + versionBi, 86400 * 3, TimeUnit.SECONDS);
    }

    /**
     * 将执行成功的的聚合仓与sku从redis移除
     *
     * @param template  template
     * @param skuId     sku
     * @param shareId   聚合仓ID
     * @param versionBi
     */
    private void removeFailFromRedis(CusRedisTemplate<String, String> template, Long skuId, Long shareId, String versionBi) {
        removeFailFromRedis(template.opsForSet(), skuId, shareId, versionBi);
    }

    /**
     * 将执行成功的的聚合仓与sku从redis移除
     *
     * @param opsForSet opsForSet
     * @param skuId     sku
     * @param shareId   聚合仓ID
     */
    private void removeFailFromRedis(SetOperations<String, String> opsForSet, Long skuId, Long shareId, String versionBi) {
        opsForSet.remove(SgShareConstants.DISTRIBUTION_BACK_REDIS_KEY + versionBi, skuId + SgConstantsIF.MAP_KEY_DIVIDER + shareId);
    }


    /**
     * 分货拉回-根据SKU执行
     *
     * @param skuId    skuID
     * @param typeEnum 执行类型
     */
    public void allocationReturnBySkuId(Long skuId, SgDistributionTypeEnum typeEnum, String versionBi) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        int successCount = 0;
        CusRedisTemplate<String, String> template = RedisMasterUtils.getStrRedisTemplate();
        DistributionDingTalkTableRobot robot = new DistributionDingTalkTableRobot(SgDistributionTypeEnum.BY_USER);

        /*获取所有可用聚合仓*/
        List<SgCShareStore> shareStoreList = sgS2SaAutoDistributionManager.querySgShareStores();
        /*获取聚合仓下所有配销仓，聚合仓ID->配销仓列表*/
        Map<Long, List<SgCSaStore>> shareIdSaStoreMap = sgS2SaAutoDistributionManager.querySaStoresMap(shareStoreList, false);
        log.info(LogUtil.format("开始-执行手工拉回，聚合仓数量：{}，SKU：{}", LOG_OBJ + "allocationReturnBySkuId"),
                shareStoreList.size(), skuId);
        for (Map.Entry<Long, List<SgCSaStore>> entry : shareIdSaStoreMap.entrySet()) {
            try {
                sgS2SaAutoDistributionManager.allocationReturnSkuByShareStore(skuId, entry.getKey(), entry.getValue(), typeEnum, versionBi);
                successCount++;
                /*成功就删掉*/
                removeFailFromRedis(template, skuId, entry.getKey(), versionBi);
            } catch (Exception e) {
                log.warn(LogUtil.format("执行手工分货失败,聚合仓ID:{},SKUID:{},错误信息：{}", LOG_OBJ + "allocationReturnBySkuId"),
                        entry.getKey(), skuId, Throwables.getStackTraceAsString(e));

                /*手工拉回出错的数据到REDIS*/
                addFail2Redis(template, skuId, entry.getKey(), versionBi);
                /*添加到告警信息*/
                
                robot.addErrorSku(skuId, entry.getKey(), e.getMessage());
            }
        }
        /*发送钉钉告警*/
        robot.sendTableMsg();

        stopWatch.stop();
        log.info(LogUtil.format("结束-执行手工拉回，聚合仓数量：{}，SKU：{}，成功聚合仓数：{},耗时：{}", LOG_OBJ + "allocationReturnBySkuId"),
                shareStoreList.size(), skuId, successCount, stopWatch.getTotalTimeMillis());
    }


    /**
     * 当前时间距离本月最后一毫秒的毫秒数
     *
     * @return 当前时间距离本月最后一毫秒的毫秒数
     */
    @Deprecated
    public long genMonthLastMilliseconds() {
        long currentTimeMillis = System.currentTimeMillis();
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTimeInMillis() - currentTimeMillis;
    }


    /**
     * 跳过拉回-直接参与定时分货
     *
     * @param skuId    skuId
     * @param typeEnum 操作类型
     */
    @Transactional(rollbackFor = Throwable.class)
    public void skipMonthReturn(Long skuId, SgDistributionTypeEnum typeEnum, String versionBi) {
        /*获取所有可用聚合仓*/
        List<SgCShareStore> shareStoreList = sgS2SaAutoDistributionManager.querySgShareStores();
        if (CollectionUtils.isEmpty(shareStoreList)) {
            log.info(LogUtil.format("结束-跳过拉回：聚合仓数量为0，SKU：{}", LOG_OBJ + "skipMonthReturn"), skuId);
            return;
        }
        List<Long> shareStoreIdList = shareStoreList.stream().map(SgCShareStore::getId).collect(Collectors.toList());
        sgCDistributionMonthLogService.createBySkip(skuId, shareStoreIdList, typeEnum, versionBi);
    }
}
