package com.burgeon.r3.sg.share.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.SgCShareStoreMapper;
import com.burgeon.r3.sg.basic.model.request.SgBShareStorageQueryRequest;
import com.burgeon.r3.sg.basic.model.request.SgStoragePageQueryRequest;
import com.burgeon.r3.sg.basic.model.request.SgStoragePageRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageQueryRequest;
import com.burgeon.r3.sg.basic.model.result.SgBShareStorageQueryResult;
import com.burgeon.r3.sg.basic.services.SgBShareStorageQueryService;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorage;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.model.request.adjust.SgBShareAdjustBillSaveRequest;
import com.burgeon.r3.sg.share.model.request.adjust.SgBShareAdjustItemSaveRequest;
import com.burgeon.r3.sg.share.model.request.adjust.SgBShareAdjustSaveRequest;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationBillSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationSaveItemRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationSaveRequst;
import com.burgeon.r3.sg.share.services.adjust.SgBShareAdjustSaveService;
import com.burgeon.r3.sg.share.services.adjust.SgBShareAdjustSubmitService;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationSaveService;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationSubmitService;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2021-08-03 21:33
 * @Description: 聚合仓档案----根据聚合仓分货类型生成已审核分货单和已审核共享调整单
 */

@Slf4j
@Component
@Deprecated
public class SgCShareStoreByAutoTypeService {

    @Autowired
    private SgCShareStoreMapper sgShareStoreMapper;

    @Autowired
    private SgStorageQueryService sgStorageQueryService;
    @Autowired
    private SgBShareAdjustSaveService saveService;

    @Autowired
    private SgBShareAdjustSubmitService submitService;

    @Autowired
    private SgBShareStorageQueryService sgShareStorageQueryService;
    @Autowired
    private SgBShareAllocationSaveService shareAllocationSaveService;
    @Autowired
    private SgBShareAllocationSubmitService shareAllocationSubmitService;


    /**
     * 根据聚合仓分货类型生成已审核分货单和已审核共享调整单
     * 聚合仓分货类型 1 配销仓 2 共享池
     * 配销仓：生成已审核分货单
     * 共享池：生成已审核共享调整单
     * 查询条件：聚合仓档案---聚合仓分货类型不为null and 【聚合仓库存】可用量 >0
     */
    public ValueHolderV14 execute() {

        if (log.isDebugEnabled()) {
            log.debug("Start SgCShareStoreByAutoTypeService.execute ");
        }

        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS,
                "【聚合仓档案】根据聚合仓分货类型生成已审核分货单和已审核共享调整单 定时任务执行成功！");

        List<Integer> list = new ArrayList<>();
        list.add(SgConstantsIF.AUTO_TYPR_SA);
        list.add(SgConstantsIF.AUTO_TYPR_SS);
        // 2022年3月5日 珂珂：去掉自动分货判断条件
        List<SgCShareStore> sgShareStores = sgShareStoreMapper.selectList(new LambdaQueryWrapper<SgCShareStore>()
                .eq(SgCShareStore::getIsactive, SgConstants.IS_ACTIVE_Y)
                .isNotNull(SgCShareStore::getAutoType)
                .in(SgCShareStore::getAutoType, list));
//                .eq(SgCShareStore::getIsAutoAllocation, SgConstants.IS_AUTO_Y));

        if (CollectionUtils.isNotEmpty(sgShareStores)) {

            //聚合仓id集合
            List<Long> ssIds = new ArrayList<>();
            //聚合仓分货类型 1 配销仓 集合
            List<SgCShareStore> sgShareStoresBySa = new ArrayList<>();
            //聚合仓分货类型 2 共享池 集合
            List<SgCShareStore> sgShareStoresBySs = new ArrayList<>();

            for (SgCShareStore shareStore : sgShareStores) {

                Integer autoType = shareStore.getAutoType();
                ssIds.add(shareStore.getId());
                //根据聚合仓分货类型 分类
                if (SgConstantsIF.AUTO_TYPR_SA.equals(autoType)) {
                    sgShareStoresBySa.add(shareStore);
                } else if (SgConstantsIF.AUTO_TYPR_SS.equals(autoType)) {
                    sgShareStoresBySs.add(shareStore);
                }

            }

            //根据聚合仓查询逻辑仓信息
            Map<Long, List<SgCpCStore>> storeList = CommonCacheValUtils.getStoreList(ssIds);
            // 查找聚合仓下所有的条码
            Map<Long, List<Long>> skuBySsMap = querySkuByStore(storeList);
            List<Long> mapKeyList = skuBySsMap.entrySet().stream().map(Map.Entry::getKey).collect(Collectors.toList());
            if (log.isDebugEnabled()) {
                log.debug("SgCShareStoreByAutoTypeService.querySkuByStore.mapKeyList:{}", JSONObject.toJSONString(mapKeyList));
            }
            autoBySa(sgShareStoresBySa, skuBySsMap);
            autoBySs(sgShareStoresBySs, skuBySsMap);
        }

        return v14;
    }


    /**
     * 聚合仓档案 聚合仓分货类型=配销仓（1） 生成已审核的分货单
     *
     * @param sgShareStoresBySa 聚合仓档案中聚合仓分货类型=配销仓的集合
     * @param skuBySsMap        聚合仓下所有的条码集合
     * @return 处理结果
     */
    private ValueHolderV14 autoBySa(List<SgCShareStore> sgShareStoresBySa, Map<Long, List<Long>> skuBySsMap) {

        if (log.isDebugEnabled()) {
            log.debug("Start SgCShareStoreByAutoTypeService.autoBySa  sgShareStoresBySa.size = {}", sgShareStoresBySa.size());
        }

        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "聚合仓分货类型=配销仓,生成分货单成功");

        //全部需要新增分货单
        List<SgBShareAllocationBillSaveRequst> saveRequsts = new ArrayList<>();

        for (SgCShareStore shareStore : sgShareStoresBySa) {

            // 报错不停止
            try {
                Long ssid = shareStore.getId();
                if (skuBySsMap.containsKey(ssid)) {
                    //获取聚合仓对应条码
                    List<Long> skuIds = skuBySsMap.get(ssid);
                    List<Long> ssidList = new ArrayList<>();
                    ssidList.add(ssid);

                    List<List<Long>> pageList = StorageUtils.getPageList(skuIds, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
                    List<SgBShareStorageQueryResult> data = new ArrayList<>();
                    for (List<Long> skuid : pageList) {
                        //查询聚合仓库存
                        SgBShareStorageQueryRequest queryRequest = new SgBShareStorageQueryRequest();
                        queryRequest.setPsCSkuIds(skuid);
                        queryRequest.setSgCShareStoreIds(ssidList);
                        ValueHolderV14<List<SgBShareStorageQueryResult>> listValueHolderV14 = sgShareStorageQueryService.queryStorageByAuto(queryRequest);
                        if (listValueHolderV14.isOK()) {
                            if (CollectionUtils.isNotEmpty(listValueHolderV14.getData())) {
                                data.addAll(listValueHolderV14.getData());
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(data)) {
                        saveRequsts.addAll(setSgBShareAllocation(shareStore, data));

                    }
                }
            } catch (Exception e) {
                log.error("自动分货设置参数异常：error:{}", Throwables.getStackTraceAsString(e));
            }

        }

        if (CollectionUtils.isNotEmpty(saveRequsts)) {
            if (log.isDebugEnabled()) {
                log.debug("SgCShareStoreByAutoTypeService.autoBySa   saveRequsts.size= {}", saveRequsts.size());
            }
            for (SgBShareAllocationBillSaveRequst saveRequst : saveRequsts) {
                SgCShareStoreByAutoTypeService bean = ApplicationContextHandle.getBean(SgCShareStoreByAutoTypeService.class);

                try {
                    //报错不停，继续下一个
                    bean.insertSgShareAllocatio(saveRequst);
                } catch (Exception e) {
                    log.error("SgCShareStoreByAutoTypeService. error:{}", Throwables.getStackTraceAsString(e));
                }
            }
        }

        return v14;
    }


    /**
     * 新增并审核 分货单
     *
     * @param saveRequst 分货单
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertSgShareAllocatio(SgBShareAllocationBillSaveRequst saveRequst) {

        // 新增并审核
        ValueHolderV14<SgR3BaseResult> save = shareAllocationSaveService.save(saveRequst);

        if (save.isOK()) {
            SgR3BaseResult data = save.getData();
            JSONObject dataJo = data.getDataJo();
            Long objid = dataJo.getLong("objid");
            SgR3BaseRequest request = new SgR3BaseRequest();
            request.setObjId(objid);
            request.setLoginUser(saveRequst.getLoginUser());
            SgBShareAllocationSubmitService bean = ApplicationContextHandle.getBean(SgBShareAllocationSubmitService.class);
            bean.submitShareAllocation(request, false, false);
        }
    }


    /**
     * @param shareStore 聚合仓信息
     * @param data       集合仓库存明细
     * @return 分货单 新增集合
     */
    private List<SgBShareAllocationBillSaveRequst> setSgBShareAllocation(SgCShareStore shareStore, List<SgBShareStorageQueryResult> data) {
        List<SgBShareAllocationBillSaveRequst> saveRequsts = new ArrayList<>();
        //批量生成
        List<List<SgBShareStorageQueryResult>> pageList = StorageUtils.getPageList(data, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);

//        List<SgCSaStore> saStoreList = CommonCacheValUtils.getSaStoreList(shareStore.getId());
        Long sgSaStoreId = shareStore.getSgCSaStoreId();
        AssertUtils.cannot(sgSaStoreId == null, "聚合仓 [" + shareStore.getEcode() + "]分货配销仓为空！");

        //获取配销仓
        SgCSaStore saStore = CommonCacheValUtils.getSaStore(sgSaStoreId);

        AssertUtils.cannot(saStore == null,
                "聚合仓 [" + shareStore.getEcode() + "]分货配销仓 【" + saStore + "】对应配销仓档案不存在！");

        for (List<SgBShareStorageQueryResult> queryResults : pageList) {
            SgBShareAllocationBillSaveRequst requst = new SgBShareAllocationBillSaveRequst();
            //主表参数
            SgBShareAllocationSaveRequst saveRequst = new SgBShareAllocationSaveRequst();
            saveRequst.setBillDate(new Date());
            saveRequst.setSgCShareStoreId(shareStore.getId());
            saveRequst.setRemark("由系统自动分货任务生成！");
            //明细参数
            List<SgBShareAllocationSaveItemRequst> saveItemRequsts = new ArrayList<>();
            for (SgBShareStorageQueryResult queryResult : queryResults) {
                //测试环节，如果条码信息没有，就过
                try {
                    SgBShareAllocationSaveItemRequst itemRequst = new SgBShareAllocationSaveItemRequst();
                    //补全商品信息
                    CommonCacheValUtils.setSkuInfo(queryResult.getPsCSkuId(), null, itemRequst);
                    itemRequst.setQty(queryResult.getQtyShareAvailable());
                    itemRequst.setQtyDiff(BigDecimal.ZERO);
                    itemRequst.setQtyIn(BigDecimal.ZERO);
                    itemRequst.setQtyOut(BigDecimal.ZERO);
                    itemRequst.setPsCSkuId(queryResult.getPsCSkuId());
                    itemRequst.setSgCSaStoreId(saStore.getId());
                    itemRequst.setSgCSaStoreEcode(saStore.getEcode());
                    itemRequst.setSgCSaStoreEname(saStore.getEname());
                    saveItemRequsts.add(itemRequst);
                } catch (Exception e) {
                    log.error("SgCShareStoreByAutoTypeService.setSgBShareAllocation error:{},skuid:{}", Throwables.getStackTraceAsString(e), queryResult.getPsCSkuId());
                }
            }

            requst.setObjId(-1L);
            requst.setLoginUser(R3SystemUserResource.getSystemRootUser());
            requst.setSgBShareAllocationSaveRequst(saveRequst);
            requst.setSgBShareAllocationSaveItemRequsts(saveItemRequsts);
            saveRequsts.add(requst);

        }

        return saveRequsts;
    }

    /**
     * @param sgShareStoresBySs 聚合仓档案中聚合仓聚合仓分货类型=共享池的集合
     * @param skuBySsMap        聚合仓下所有的条码集合： key = 聚合仓id value = 条码id集合
     * @return ValueHolderV14
     */
    private ValueHolderV14 autoBySs(List<SgCShareStore> sgShareStoresBySs, Map<Long, List<Long>> skuBySsMap) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "聚合仓分货类型=共享池,生成共享调整单成功");
        if (log.isDebugEnabled()) {
            log.debug("Start SgCShareStoreByAutoTypeService.autoBySs.sgShareStoresBySs.size:{},skuBySsMap.size:{}", sgShareStoresBySs.size(), skuBySsMap.size());
        }
        //redis库存流水键
        List<SgBShareAdjustBillSaveRequest> sgBShareAdjustBillSaveRequests = new ArrayList<>();
        for (SgCShareStore shareStore : sgShareStoresBySs) {
            if (skuBySsMap.containsKey(shareStore.getId())) {
                //Share仓id list
                List<Long> sgCShareStoreIds = new ArrayList<>();
                //条码id list
                List<Long> psCSkuIds = new ArrayList<>();
                sgCShareStoreIds.add(shareStore.getId());
                psCSkuIds.addAll(skuBySsMap.get(shareStore.getId()));

                List<List<Long>> pageList = StorageUtils.getPageList(psCSkuIds, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
                List<SgBShareStorageQueryResult> queryStorageResultListsData = new ArrayList<>();
                for (List<Long> skuid : pageList) {
                    //获取共享调整数量
                    SgBShareStorageQueryRequest sgBShareStorageQueryRequest = new SgBShareStorageQueryRequest();
                    sgBShareStorageQueryRequest.setSgCShareStoreIds(sgCShareStoreIds);
                    sgBShareStorageQueryRequest.setPsCSkuIds(skuid);
                    //聚合参库存查询
                    ValueHolderV14<List<SgBShareStorageQueryResult>> queryStorageResultLists = sgShareStorageQueryService.queryStorageByAuto(sgBShareStorageQueryRequest);
                    if (queryStorageResultLists.isOK()) {
                        //获取当前聚合仓下各个条码id下的数量
                        if (CollectionUtils.isNotEmpty(queryStorageResultLists.getData())) {
                            queryStorageResultListsData.addAll(queryStorageResultLists.getData());
                        }

                    }
                }

                if (CollectionUtils.isNotEmpty(queryStorageResultListsData)) {
                    if (log.isDebugEnabled()) {
                        log.debug("SgCShareStoreByAutoTypeService.autoBySs ReceiveParams:queryStorageResultListsData.size:{}", queryStorageResultListsData.size());
                    }
                    sgBShareAdjustBillSaveRequests.addAll(setSgBShareAdjust(shareStore, queryStorageResultListsData));
                    if (log.isDebugEnabled()) {
                        log.debug("SgCShareStoreByAutoTypeService.autoBySs.sgBShareAdjustBillSaveRequests.size:{}", sgBShareAdjustBillSaveRequests.size());
                    }
                }

            }
        }
        if (CollectionUtils.isNotEmpty(sgBShareAdjustBillSaveRequests)) {
            for (SgBShareAdjustBillSaveRequest sgBShareAdjustBillSaveRequest : sgBShareAdjustBillSaveRequests) {
                try {
                    createAndSubmitSgBShareAdjust(sgBShareAdjustBillSaveRequest);
                } catch (Exception e) {
                    log.error("共享调整单创建审核失败:SgCShareStoreByAutoTypeService. error:{}",
                            Throwables.getStackTraceAsString(e));
                }
            }
        }
        return v14;
    }


    /**
     * 创建并审核共享调整单
     *
     * @param sgBShareAdjustBillSaveRequest
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void createAndSubmitSgBShareAdjust(SgBShareAdjustBillSaveRequest sgBShareAdjustBillSaveRequest) {
        //生成共享调整单
        ValueHolderV14<SgR3BaseResult> sgBShareAdjustSave = saveService.save(sgBShareAdjustBillSaveRequest);
        if (sgBShareAdjustSave.isOK()) {
            JSONObject dataJo = sgBShareAdjustSave.getData().getDataJo();
            SgR3BaseRequest submitRuqest = new SgR3BaseRequest();
            Long objId = sgBShareAdjustSave.getData().getDataJo().getLong("objid");
            submitRuqest.setObjId(objId);
            submitRuqest.setLoginUser(R3SystemUserResource.getSystemRootUser());
            submitService.submit(submitRuqest);
        }
    }


    /**
     * 组装数据共享调整单数据
     *
     * @param shareStore
     * @param queryStorageResultListsData
     * @return
     */
    private List<SgBShareAdjustBillSaveRequest> setSgBShareAdjust(SgCShareStore shareStore, List<SgBShareStorageQueryResult> queryStorageResultListsData) {
        List<SgBShareAdjustBillSaveRequest> sgBShareAdjustBillSaveRequests = new ArrayList<>();
        //批量生成
        List<List<SgBShareStorageQueryResult>> pageList = StorageUtils.getPageList(queryStorageResultListsData, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
        for (List<SgBShareStorageQueryResult> queryResults : pageList) {
            SgBShareAdjustBillSaveRequest sgBShareAdjustBillSaveRequest = new SgBShareAdjustBillSaveRequest();
            //共享调整单主表数据组装
            SgBShareAdjustSaveRequest sgBShareAdjustSaveRequest = new SgBShareAdjustSaveRequest();
            sgBShareAdjustSaveRequest.setBillDate(new Date());
            sgBShareAdjustSaveRequest.setSgCShareStoreId(shareStore.getId());
            sgBShareAdjustSaveRequest.setSgCShareStoreEname(shareStore.getEname());
            sgBShareAdjustSaveRequest.setSgCShareStoreEcode(shareStore.getEcode());
            sgBShareAdjustSaveRequest.setRemark("由系统自动分货任务生成！");
            sgBShareAdjustBillSaveRequest.setObjId(-1L);
            sgBShareAdjustBillSaveRequest.setSgBShareAdjustSaveRequest(sgBShareAdjustSaveRequest);
            //共享调整单明细表数据组装
            List<SgBShareAdjustItemSaveRequest> sgBShareAdjustItemSaveRequests = new ArrayList<>();
            for (SgBShareStorageQueryResult queryResult : queryResults) {
                //测试环节，如果条码信息没有，就过
                try {
                    SgBShareAdjustItemSaveRequest sgBShareAdjustItemSaveRequest = new SgBShareAdjustItemSaveRequest();
                    //根据条码id获取条码信息
                    CommonCacheValUtils.setSkuInfo(queryResult.getPsCSkuId(), null, sgBShareAdjustItemSaveRequest);
                    sgBShareAdjustItemSaveRequest.setQty(queryResult.getQtyShareAvailable());
                    sgBShareAdjustItemSaveRequests.add(sgBShareAdjustItemSaveRequest);
                } catch (Exception e) {
                    log.error("SgCShareStoreByAutoTypeService.setSgBShareAdjust error:{},skuid:{}", Throwables.getStackTraceAsString(e), queryResult.getPsCSkuId());
                }
            }
            sgBShareAdjustBillSaveRequest.setSgBShareAdjustItemSaveRequests(sgBShareAdjustItemSaveRequests);
            sgBShareAdjustBillSaveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());
            sgBShareAdjustBillSaveRequests.add(sgBShareAdjustBillSaveRequest);
        }
        return sgBShareAdjustBillSaveRequests;
    }

    /**
     * 查找聚合仓下所有的条码
     * 根据逻辑仓档案查找逻辑仓库存 目的是为了拿出所有的条码
     *
     * @param storeList 逻辑仓档案集合 key = 聚合仓id value = 逻辑仓档案集合
     * @return 返回map key = 聚合仓id value = 条码id集合
     */
    private Map<Long, List<Long>> querySkuByStore(Map<Long, List<SgCpCStore>> storeList) {

        // key 聚合仓id value 条码id集合
        Map<Long, List<Long>> map = new HashMap<>(16);

        for (Long ssid : storeList.keySet()) {
            List<SgCpCStore> stores = storeList.get(ssid);
            List<Long> storeIds = stores.stream().map(SgCpCStore::getId).collect(Collectors.toList());

            List<SgBStorage> data = new ArrayList<>();

            //查询逻辑仓库存 这里是为了拿出所有的条码
            for (Long storeid : storeIds) {
                SgStoragePageQueryRequest sgStoragePageQueryRequest = new SgStoragePageQueryRequest();

                SgStorageQueryRequest sgStorageQueryRequest = new SgStorageQueryRequest();
                List<Long> storeidList = new ArrayList<>();
                storeidList.add(storeid);
                sgStorageQueryRequest.setStoreIds(storeidList);

                SgStoragePageRequest sgStoragePageRequest = new SgStoragePageRequest();
                sgStoragePageRequest.setPageNum(1);
                sgStoragePageRequest.setPageSize(SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
//                sgStoragePageRequest.setPageSize(5);

                sgStoragePageQueryRequest.setPageRequest(sgStoragePageRequest);
                sgStoragePageQueryRequest.setQueryRequest(sgStorageQueryRequest);
                ValueHolderV14<PageInfo<SgBStorage>> pageInfoValueHolderV14 = sgStorageQueryService.queryStoragePage(sgStoragePageQueryRequest,
                        R3SystemUserResource.getSystemRootUser());
                AssertUtils.cannot(!pageInfoValueHolderV14.isOK(), "根据逻辑仓查逻辑仓库存异常：" + pageInfoValueHolderV14.getMessage());

                PageInfo<SgBStorage> dataInfo = pageInfoValueHolderV14.getData();
                if (dataInfo != null) {

                    if (CollectionUtils.isNotEmpty(dataInfo.getList())) {
                        data.addAll(dataInfo.getList());
                    }

                    //判断是否还有下一页
                    if (dataInfo.isHasNextPage()) {
                        List<SgBStorage> pageStorage = getPageStorage(storeidList, dataInfo);
                        if (CollectionUtils.isNotEmpty(pageStorage)) {
                            data.addAll(pageStorage);
                        }
                    }
                }
            }


            if (CollectionUtils.isNotEmpty(data)) {
                //取出所有的sku
                List<Long> skuIds = data.stream().map(SgBStorage::getPsCSkuId).collect(Collectors.toList());
                // 去重复
                skuIds = skuIds.stream().distinct().collect(Collectors.toList());
                map.put(ssid, skuIds);
            }
        }
        return map;
    }

    /**
     * 分页查询库存
     *
     * @param storeidList 店仓id
     * @param dataInfo    分页参数
     * @return 库存
     */
    private List<SgBStorage> getPageStorage(List<Long> storeidList, PageInfo<SgBStorage> dataInfo) {
        List<SgBStorage> data = new ArrayList<>();
        //获取页面
        int pages = dataInfo.getPages();

        //从第二页开始再查
        for (int i = 2; i <= pages; i++) {

            SgStoragePageQueryRequest sgStoragePageQueryRequest = new SgStoragePageQueryRequest();

            SgStorageQueryRequest sgStorageQueryRequest = new SgStorageQueryRequest();
            sgStorageQueryRequest.setStoreIds(storeidList);

            SgStoragePageRequest sgStoragePageRequest = new SgStoragePageRequest();
            sgStoragePageRequest.setPageNum(i);
            sgStoragePageRequest.setPageSize(SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);


            sgStoragePageQueryRequest.setPageRequest(sgStoragePageRequest);
            sgStoragePageQueryRequest.setQueryRequest(sgStorageQueryRequest);
            ValueHolderV14<PageInfo<SgBStorage>> pageInfoValueHolderV14 = sgStorageQueryService.queryStoragePage(sgStoragePageQueryRequest,
                    R3SystemUserResource.getSystemRootUser());
            AssertUtils.cannot(!pageInfoValueHolderV14.isOK(), "根据逻辑仓分页查逻辑仓库存异常：" + pageInfoValueHolderV14.getMessage());

            PageInfo<SgBStorage> pageInfo = pageInfoValueHolderV14.getData();
            if (pageInfo != null && CollectionUtils.isNotEmpty(pageInfo.getList())) {
                data.addAll(pageInfo.getList());
            }
        }

        return data;
    }

}
