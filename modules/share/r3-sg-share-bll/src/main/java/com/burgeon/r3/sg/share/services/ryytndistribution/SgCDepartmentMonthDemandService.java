package com.burgeon.r3.sg.share.services.ryytndistribution;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.basic.services.log.LogCommonService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.SgMonthDemandFromEnum;
import com.burgeon.r3.sg.core.enums.SgMonthDemandStatusEnum;
import com.burgeon.r3.sg.core.model.result.SgBCommonStorageQtyQueryResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCOperationLog;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocation;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationItem;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationReturn;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationReturnItem;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCDepartmentMonthDemand;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCDepartmentMonthDemandLog;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCPlanConvertVersion;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgCDepartmentMonthDemandMapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 部门月需求提报
 *
 * <AUTHOR>
 * @since 2022-10-17 15:28
 */
@Slf4j
@Component
public class SgCDepartmentMonthDemandService extends ServiceImpl<SgCDepartmentMonthDemandMapper, SgCDepartmentMonthDemand> {
    /**
     * 日志OBJ
     */
    private static final String LOG_OBJ = "SgCDepartmentMonthDemandService.";

    /**
     * 部门月需求
     */
    @Resource
    private SgCDepartmentMonthDemandMapper sgCDepartmentMonthDemandMapper;

    @Resource
    private SgCDepartmentMonthDemandLogService sgCDepartmentMonthDemandLogService;


    /**
     * 配销仓
     */
    @Resource
    private SgCSaStoreMapper sgCSaStoreMapper;

    @Resource
    private SgCPlanConvertVersionService sgCPlanConvertVersionService;

    @Resource
    private LogCommonService logCommonService;


    /**
     * 分货提交时，更新二级部门月需求
     *
     * @param sgShareAllocation 分货单信息
     * @param items             分货单明细
     * @param loginUser         当前用户
     */
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRES_NEW)
    public void modifyDemandByAllocationSubmit(SgBShareAllocation sgShareAllocation, List<SgBShareAllocationItem> items, User loginUser) {
        log.debug(LogUtil.format("分货单提交时，更新二级部门月需求,分货单：【{}】,分货明细：【{}】，当前用户：【{}】", LOG_OBJ + "modifyDemandByManual"),
                JSON.toJSONString(sgShareAllocation), JSON.toJSONString(items), JSON.toJSONString(loginUser));
        if (CollectionUtils.isEmpty(items)) {
            log.info(LogUtil.format("分货单明细为空，分货单信息:{}", LOG_OBJ), JSON.toJSONString(sgShareAllocation));
            return;
        }

        /*配销仓ID->分货明细映射*/
        Map<Long, List<SgBShareAllocationItem>> saStoreItemMap = items.stream()
                .collect(Collectors.groupingBy(SgBShareAllocationItem::getSgCSaStoreId));
        List<SgCSaStore> saStoreList = querySaStoreByIds(saStoreItemMap.keySet()).stream()
                .filter(sa -> Objects.nonNull(sa.getCpCDistributionOrgId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(saStoreList)) {
            log.info(LogUtil.format("分货单明细配销仓无对应二级部门，分货单明细：{}", LOG_OBJ + "modifyDemandByManual"), JSON.toJSONString(items));
            return;
        }
        /*部门ID->(商品ID->分货明细列表)*/
        Map<Long, Map<Long, List<SgBShareAllocationItem>>> deptSkuItemMap = genItemMap(saStoreList, saStoreItemMap);
        SgCPlanConvertVersion activeVersion = sgCPlanConvertVersionService.selectActive();

        List<SgCDepartmentMonthDemand> createList = new ArrayList<>();
        List<SgCDepartmentMonthDemand> modifyList = new ArrayList<>();
        List<SgCDepartmentMonthDemandLog> demandLogList = new ArrayList<>();
        handlerDemandByItems(deptSkuItemMap, createList, modifyList, activeVersion, sgShareAllocation, demandLogList);
        log.debug(LogUtil.format("分货单计算需更新的需求列表数据：{},需创建的需求列表数据：{}", LOG_OBJ + "modifyDemandByManual"),
                JSON.toJSONString(modifyList), JSON.toJSONString(createList));

        batchModifyDemand(modifyList, loginUser);
        batchCreateDemand(createList, loginUser, activeVersion, sgShareAllocation, demandLogList);
        sgCDepartmentMonthDemandLogService.createLogList(demandLogList, loginUser);
    }

    private void batchCreateDemand(List<SgCDepartmentMonthDemand> createList, User loginUser,
                                   SgCPlanConvertVersion activeVersion, SgBShareAllocation sgShareAllocation,
                                   List<SgCDepartmentMonthDemandLog> demandLogList) {
        if (CollectionUtils.isEmpty(createList)) {
            log.warn(LogUtil.format("新增二级部门月需求列表数量为空", LOG_OBJ + "batchModifyDemand"));
            return;
        }

        for (SgCDepartmentMonthDemand demand : createList) {
            /*demand.setId(ModelUtil.getSequence(SgConstants.SG_C_DEPARTMENT_MONTH_DEMAND));*/

            demand.setModifierid(Long.valueOf(loginUser.getId()));
            demand.setCreationdate(new Date());
            demand.setModifieddate(new Date());
            demand.setModifiername(loginUser.getName());
            demand.setModifierename(loginUser.getEname());

            demand.setMonthWeek(SgCPlanConvertVersionService.MONTH_WEEK_BY_HAND);
            demand.setVersionPlan(activeVersion.getVersionPlan());
            demand.setVersionBi(activeVersion.getVersionBi());
            demand.setRemark("超额满足自动生成");
            demand.setDemandFrom(SgMonthDemandFromEnum.SCP.getValue());
        }
        sgCDepartmentMonthDemandMapper.batchInsert(createList);

        /*构建满足日志*/
        for (SgCDepartmentMonthDemand demand : createList) {
            SgCDepartmentMonthDemandLog demandLog = new SgCDepartmentMonthDemandLog();
            /*demandLog.setConvertConfigId(demand.getConvertConfigId());*/
            /*demandLog.setConvertDayPercentage(demand.getConvertDayPercentage());*/
            demandLog.setSgCDepartmentMonthDemandId(demand.getId());
            demandLog.setDemandDate(demand.getDemandDate());
            demandLog.setPsCSkuId(demand.getPsCSkuId());
            demandLog.setSourceBillNo(sgShareAllocation.getBillNo());
            demandLog.setSourceType(SgConstantsIF.BILL_SHARE_ALLOCATION);
            demandLog.setVersionBi(demand.getVersionBi());

            demandLog.setChangeQty(demand.getActualInQty());
            demandLogList.add(demandLog);
        }
    }

    /**
     * 批量修改-二级部门月需求
     *
     * @param modifyDemandList 月需求列表
     * @param loginUser        操作人信息
     */
    public void batchModifyDemand(List<SgCDepartmentMonthDemand> modifyDemandList, User loginUser) {
        if (CollectionUtils.isEmpty(modifyDemandList)) {
            log.warn(LogUtil.format("修改二级部门月需求列表数量为空", LOG_OBJ + "batchModifyDemand"));
            return;
        }

        for (SgCDepartmentMonthDemand demand : modifyDemandList) {
            Date dateBeforeUpdate = demand.getModifieddate();
            demand.setModifierid(Long.valueOf(loginUser.getId()));
            demand.setModifieddate(new Date());
            demand.setModifiername(loginUser.getName());
            demand.setModifierename(loginUser.getEname());

            /*batchUpdate批量更新，使用这个字段传参数，未测试未使用*/
//            demand.setCreationdate(dateBeforeUpdate);
//            modifyList.add(demand);

            /*如果时间不一致说明有其他程序做了变更*/
            int update = sgCDepartmentMonthDemandMapper.update(demand, new LambdaUpdateWrapper<SgCDepartmentMonthDemand>()
                    .eq(SgCDepartmentMonthDemand::getModifieddate, dateBeforeUpdate)
                    .eq(SgCDepartmentMonthDemand::getId, demand.getId()));
            if (update != 1) {
                log.error(LogUtil.format("更新二级部门分货需求失败，影响行数错误，更新内容：{}，更新前日期：{}，影响行数：{}", LOG_OBJ + "batchModifyDemand"),
                        JSON.toJSONString(demand), dateBeforeUpdate, update);
                throw new NDSException("更新二级部门分货需求失败");
            }
        }

        /*未测试未使用，这里还可以对list进行拆分，防止一条sql更新太多*/
//        int count = 0;
//        if (!CollectionUtils.isEmpty(modifyDemandList)) {
//            try {
//                count = sgCDepartmentMonthDemandMapper.batchInsert(modifyDemandList);
//            } catch (Exception e) {
//                log.error(LogUtil.format("更新二级部门分货需求失败，更新内容：{}，错误信息：{}", LOG_OBJ + "batchModifyDemand"),
//                        JSON.toJSONString(modifyDemandList), Throwables.getStackTraceAsString(e));
//                throw new NDSException("更新二级部门分货需求失败");
//            }
//            if (count != modifyDemandList.size()) {
//                log.error(LogUtil.format("更新二级部门分货需求失败，影响行数错误，更新内容：{}，更新前日期：{}，影响行数：{}", LOG_OBJ + "batchModifyDemand"),
//                        JSON.toJSONString(modifyDemandList), count);
//                throw new NDSException("更新二级部门分货需求出错");
//            }
//        }
    }

    /**
     * 执行计算逻辑
     *
     * @param deptSkuItemMap    部门ID->(商品ID->分货明细列表)
     * @param createList        创建列表
     * @param modifyList        修改列表
     * @param activeVersion     当前激活的版本
     * @param sgShareAllocation 分货单信息
     * @param demandLogList     分货日志列表
     */
    private void handlerDemandByItems(Map<Long, Map<Long, List<SgBShareAllocationItem>>> deptSkuItemMap,
                                      List<SgCDepartmentMonthDemand> createList, List<SgCDepartmentMonthDemand> modifyList,
                                      SgCPlanConvertVersion activeVersion, SgBShareAllocation sgShareAllocation,
                                      List<SgCDepartmentMonthDemandLog> demandLogList) {
        /*逐个部门处理*/
        for (Long deptId : deptSkuItemMap.keySet()) {
            Map<Long, List<SgBShareAllocationItem>> skuItemsMap = deptSkuItemMap.get(deptId);
            if (MapUtils.isEmpty(skuItemsMap)) {
                continue;
            }
            /*逐个部门、商品进行处理*/
            for (Map.Entry<Long, List<SgBShareAllocationItem>> entry : deptSkuItemMap.get(deptId).entrySet()) {
                Long skuId = entry.getKey();
                BigDecimal totalQty = ListUtils.emptyIfNull(entry.getValue()).stream()
                        .map(SgBShareAllocationItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (totalQty.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                /*该部门、该商品->需求列表(按日期排序)*/
                /*20241217-不滚动的超额满足默认为临时动作，不产生超额需求*/
                List<SgCDepartmentMonthDemand> skuDemandList = queryDemandByDistributionSubmit(deptId, skuId, activeVersion.getVersionBi());
                /*计算分配（可超分）*/
                calculateDemandList(skuDemandList, totalQty, createList, modifyList, sgShareAllocation, demandLogList);
            }
        }
    }

    /**
     * 根据二级部门和sku查询部门当月需求(有未满足获取未满足，都满足了获取最后一条)
     *
     * @param deptId    二级部门
     * @param skuId     商品ID
     * @param versionBi 版本号
     * @return 部门需求列表
     */
    private List<SgCDepartmentMonthDemand> queryDemandByDistributionSubmit(Long deptId, Long skuId, String versionBi) {
        if (Objects.isNull(deptId) || Objects.isNull(skuId)) {
            log.warn(LogUtil.format("根据二级部门和sku查询部门当月需求参数非法", LOG_OBJ + "queryDemandByDistributionSubmit"));
            return Collections.emptyList();
        }

        List<SgCDepartmentMonthDemand> monthDemands =
                sgCDepartmentMonthDemandMapper.selectList(new LambdaQueryWrapper<SgCDepartmentMonthDemand>()
                        .eq(SgCDepartmentMonthDemand::getIsactive, SgConstants.IS_ACTIVE_Y)
                        .eq(SgCDepartmentMonthDemand::getVersionBi, versionBi)
                        /*.ne(SgCDepartmentMonthDemand::getDemandStatus, SgMonthDemandStatusEnum.COMPLETED.getValue())*/
                        .eq(SgCDepartmentMonthDemand::getCpCDistributionOrgId, deptId)
                        .eq(SgCDepartmentMonthDemand::getPsCSkuId, skuId)
                        .orderByDesc(SgCDepartmentMonthDemand::getDemandDate));

        if (CollectionUtils.isEmpty(monthDemands)) {
            return Collections.emptyList();
        }

        /*有未满足获取未满足*/
        List<SgCDepartmentMonthDemand> noCompleteDemandList = ListUtils.emptyIfNull(monthDemands).stream()
                .filter(demand -> !SgMonthDemandStatusEnum.COMPLETED.getValue().equals(demand.getDemandStatus()))
                .collect(Collectors.toList());
        /*都满足了获取最后一条*/
        if (CollectionUtils.isEmpty(noCompleteDemandList)) {
            return Collections.singletonList(monthDemands.get(monthDemands.size() - 1));
        }

        return noCompleteDemandList;
    }

    /**
     * 分配需求-仅计算（不更新）
     *
     * @param skuDemandList     该部门、该商品->需求列表
     * @param totalQty          分配量
     * @param createList        创建列表
     * @param modifyList        修改列表
     * @param sgShareAllocation 分货单信息
     * @param demandLogList     分货日志列表
     */
    private void calculateDemandList(List<SgCDepartmentMonthDemand> skuDemandList, BigDecimal totalQty,
                                     List<SgCDepartmentMonthDemand> createList, List<SgCDepartmentMonthDemand> modifyList,
                                     SgBShareAllocation sgShareAllocation, List<SgCDepartmentMonthDemandLog> demandLogList) {
        if (CollectionUtils.isEmpty(skuDemandList) || Objects.isNull(totalQty)) {
            log.debug(LogUtil.format("二级部门需求为空或分配量为空", LOG_OBJ + "calculateDemandList"));
            return;
        }

        /*按日期从小到大排序*/
        skuDemandList = skuDemandList.stream()
                .sorted(Comparator.comparing(SgCDepartmentMonthDemand::getDemandDate)).collect(Collectors.toList());

        /*总需求>=当前分配量：按日期排序，逐日满足将数据分配下去即可*/
        for (SgCDepartmentMonthDemand demand : skuDemandList) {
            if (SgMonthDemandStatusEnum.COMPLETED.getValue().equals(demand.getDemandStatus())
                    || totalQty.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            SgCDepartmentMonthDemandLog demandLog = new SgCDepartmentMonthDemandLog();
            demandLog.setConvertConfigId(demand.getConvertConfigId());
            demandLog.setSgCDepartmentMonthDemandId(demand.getId());
            demandLog.setDemandDate(demand.getDemandDate());
            demandLog.setPsCSkuId(demand.getPsCSkuId());
            demandLog.setSourceBillNo(sgShareAllocation.getBillNo());
            demandLog.setSourceType(SgConstantsIF.BILL_SHARE_ALLOCATION);
            demandLog.setVersionBi(demand.getVersionBi());
            demandLogList.add(demandLog);

            BigDecimal waitActual = demand.getDemandQty().subtract(demand.getActualInQty());
            /*可满足该条*/
            if (totalQty.subtract(waitActual).compareTo(BigDecimal.ZERO) >= 0) {
                totalQty = totalQty.subtract(waitActual);
                demand.setActualInQty(demand.getDemandQty());
                demand.setDemandStatus(SgMonthDemandStatusEnum.COMPLETED.getValue());
                modifyList.add(demand);
                demandLog.setChangeQty(waitActual);
                continue;
            }

            /*不够满足该条*/
            demand.setActualInQty(demand.getActualInQty().add(totalQty));
            Integer status = demand.getActualInQty().compareTo(demand.getDemandQty()) >= 0 ?
                    SgMonthDemandStatusEnum.COMPLETED.getValue() :
                    SgMonthDemandStatusEnum.PARTIALLY_DONE.getValue();
            demand.setDemandStatus(status);
            demandLog.setChangeQty(totalQty);

            modifyList.add(demand);
            totalQty = BigDecimal.ZERO;
            break;
        }

        /*分到最后还剩，创建一个超额满足的记录*/
        if (totalQty.compareTo(BigDecimal.ZERO) > 0) {
            SgCDepartmentMonthDemand createDemand = new SgCDepartmentMonthDemand();
            BeanUtils.copyProperties(skuDemandList.get(0), createDemand);

            createDemand.setDemandDate(new Date());
            createDemand.setDemandMonth(DateFormatUtils.format(createDemand.getDemandDate(), "yyyyMM"));
            createDemand.setDemandQty(totalQty);
            createDemand.setActualInQty(totalQty);
            createDemand.setMonthWeek(SgCPlanConvertVersionService.MONTH_WEEK_BY_HAND);
            createDemand.setDemandStatus(SgMonthDemandStatusEnum.COMPLETED.getValue());
            /*createDemand.setRemark("超额满足自动生成");*/

            /*超额满足不参与计算，不设置这个值*/
            createDemand.setConvertConfigId(null);
            createDemand.setConvertDayPercentage(null);

            createList.add(createDemand);
        }
    }

//    /**
//     * 分配需求-仅计算
//     *
//     * @param skuDemandList 该部门、该商品->需求列表
//     * @param totalQty      分配量
//     * @return 更新的二级部门需求
//     */
//    @Deprecated
//    public List<SgCDepartmentMonthDemand> distributeDemand(List<SgCDepartmentMonthDemand> skuDemandList, BigDecimal totalQty) {
//        if (CollectionUtils.isEmpty(skuDemandList) || Objects.isNull(totalQty) || totalQty.compareTo(BigDecimal.ZERO) <= 0) {
//            log.debug(LogUtil.format("二级部门需求为空或满足量为零", LOG_OBJ + "distributeDemand"));
//            return Collections.emptyList();
//        }
//
//        skuDemandList = skuDemandList.stream()
//                .sorted(Comparator.comparing(SgCDepartmentMonthDemand::getDemandDate))
//                .collect(Collectors.toList());
//
//        List<SgCDepartmentMonthDemand> modifyList = new ArrayList<>();
//        /*总需求>=当前分配量：按日期排序，逐日满足将数据分配下去即可*/
//        for (SgCDepartmentMonthDemand demand : skuDemandList) {
//            BigDecimal actual = demand.getDemandQty().subtract(demand.getActualInQty());
//            /*可满足该条*/
//            if (totalQty.subtract(actual).compareTo(BigDecimal.ZERO) >= 0) {
//                totalQty = totalQty.subtract(actual);
//                demand.setActualInQty(demand.getDemandQty());
//                demand.setDemandStatus(SgMonthDemandStatusEnum.COMPLETED.getValue());
//                modifyList.add(demand);
//                continue;
//            }
//
//            /*不够满足该条*/
//            demand.setActualInQty(demand.getActualInQty().add(totalQty));
//            if (demand.getActualInQty().compareTo(BigDecimal.ZERO) <= 0) {
//                continue;
//            }
//            Integer status = demand.getActualInQty().compareTo(demand.getDemandQty()) >= 0 ?
//                    SgMonthDemandStatusEnum.COMPLETED.getValue() :
//                    SgMonthDemandStatusEnum.PARTIALLY_DONE.getValue();
//            demand.setDemandStatus(status);
//            modifyList.add(demand);
//            break;
//        }
//        return modifyList;
//    }

    /**
     * 明细根据部门与sku分组
     *
     * @param saStoreList    部门列表
     * @param saStoreItemMap 配销仓ID->分货明细映射
     * @return 部门ID->(商品ID->分货明细列表)
     */
    private Map<Long, Map<Long, List<SgBShareAllocationItem>>> genItemMap(List<SgCSaStore> saStoreList,
                                                                          Map<Long, List<SgBShareAllocationItem>> saStoreItemMap) {
        /*配销仓ID->部门ID 映射*/
        Map<Long, Long> saIdDeptIdMap = saStoreList.stream().collect(Collectors.toMap(SgCSaStore::getId, SgCSaStore::getCpCDistributionOrgId));
        /*部门ID->分货明细列表 映射*/
        Map<Long, List<SgBShareAllocationItem>> deptItemMap = new HashMap<>();
        for (Long saStoreId : saStoreItemMap.keySet()) {
            List<SgBShareAllocationItem> saItemList = saStoreItemMap.get(saStoreId);
            Long deptId = saIdDeptIdMap.get(saStoreId);
            List<SgBShareAllocationItem> deptItemList = deptItemMap.getOrDefault(deptId, new ArrayList<>());
            deptItemList.addAll(saItemList);
            deptItemMap.put(deptId, deptItemList);
        }

        Map<Long, Map<Long, List<SgBShareAllocationItem>>> deptSkuItemMap = new HashMap<>();
        for (Long deptId : deptItemMap.keySet()) {
            List<SgBShareAllocationItem> deptItemList = deptItemMap.get(deptId);
            Map<Long, List<SgBShareAllocationItem>> skuItemMap = deptItemList.stream().collect(Collectors.groupingBy(SgBShareAllocationItem::getPsCSkuId));
            deptSkuItemMap.put(deptId, skuItemMap);
        }

        return deptSkuItemMap;
    }


    /**
     * 根据配销仓ID列表获取配销仓数据
     *
     * @param ids 配销仓ID列表
     * @return 配销仓列表
     */
    private List<SgCSaStore> querySaStoreByIds(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<SgCSaStore> saStores = sgCSaStoreMapper.selectList(new LambdaQueryWrapper<SgCSaStore>()
                .eq(SgCSaStore::getIsactive, SgConstants.IS_ACTIVE_Y)
                .in(SgCSaStore::getId, ids));
        if (CollectionUtils.isEmpty(saStores)) {
            log.warn(LogUtil.format("根据配销仓ID列表获取配销仓数据为空,参数：{}", LOG_OBJ + "querySaStoreByIds"),
                    JSON.toJSONString(ids));
            return Collections.emptyList();
        }
        return saStores;
    }


    /**
     * 查询当前月份不重复的SKU，根据部门ID列表
     *
     * @param deptIds   部门信息列表
     * @param versionBi 版本号
     * @return SKU列表
     */
    public Set<Long> querySkuBySaStore(Set<Long> deptIds, String versionBi) {
        if (CollectionUtils.isEmpty(deptIds)) {
            log.warn(LogUtil.format("配销仓对应二级部门列表为空", LOG_OBJ + "querySkuBySaStore"));
            return Collections.emptySet();
        }

        deptIds = deptIds.stream().filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> skuIds = sgCDepartmentMonthDemandMapper.selectSkuByDeptIds(deptIds, versionBi);
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptySet();
        }
        return skuIds;
    }


//    /**
//     * 获取配销仓对应月需求计划
//     *
//     * @param saStores 配销仓信息
//     * @param skuId    商品ID
//     * @return 月需求列表
//     */
//    public List<SgCDepartmentMonthDemand> queryMonthDemandBySaAndSku(List<SgCSaStore> saStores, Long skuId) {
//        if (CollectionUtils.isEmpty(saStores)) {
//            log.warn(LogUtil.format("配销仓数量为空", LOG_OBJ + "queryMonthDemandBySaAndSku"));
//            return Collections.emptyList();
//        }
//        /*获取配销仓对应二级部门ID*/
//        Set<Long> deptIds = saStores.stream().map(SgCSaStore::getCpCStoredimItemId)
//                .filter(Objects::nonNull).collect(Collectors.toSet());
//        if (CollectionUtils.isEmpty(deptIds)) {
//            log.warn(LogUtil.format("配销仓对应二级部门列表为空", LOG_OBJ + "queryMonthDemandBySaAndSku"));
//            return Collections.emptyList();
//        }
//
//        return queryByDeptAndSku(deptIds, Collections.singleton(skuId));
//    }

//    /**
//     * 根据二级部门和sku查询部门当月需求(默认当月非完成状态)
//     *
//     * @param deptIds 二级部门列表
//     * @param skuIds  商品ID列表
//     * @return 部门需求列表
//     */
//    public List<SgCDepartmentMonthDemand> queryByDeptAndSku(Set<Long> deptIds, Set<Long> skuIds) {
//        if (CollectionUtils.isEmpty(deptIds) || CollectionUtils.isEmpty(skuIds)) {
//            log.warn(LogUtil.format("配销仓对应二级部门列表或商品ID列表查询参数为空", LOG_OBJ + "queryByDeptAndSku"));
//            return Collections.emptyList();
//        }
//
//        List<SgCDepartmentMonthDemand> monthDemands =
//                sgCDepartmentMonthDemandMapper.selectList(new LambdaQueryWrapper<SgCDepartmentMonthDemand>()
//                        .eq(SgCDepartmentMonthDemand::getIsactive, SgConstants.IS_ACTIVE_Y)
//                        .eq(SgCDepartmentMonthDemand::getDemandMonth, DateFormatUtils.format(new Date(), "yyyyMM"))
//                        /*.ge(SgCDepartmentMonthDemand::getDemandDate, DateFormatUtils.format(new Date(), "yyyy-MM-dd"))*/
//                        .ne(SgCDepartmentMonthDemand::getDemandStatus, SgMonthDemandStatusEnum.COMPLETED.getValue())
//                        .in(SgCDepartmentMonthDemand::getCStoreattrib2Id, deptIds)
//                        .in(SgCDepartmentMonthDemand::getPsCSkuId, skuIds)
//                );
//
//        if (CollectionUtils.isEmpty(monthDemands)) {
//            return Collections.emptyList();
//        }
//        return monthDemands;
//    }

    /**
     * 查询部门某个每天需求映射
     *
     * @param skuId  商品ID
     * @param deptId 部门ID
     * @param month  需求月
     * @return 日期->需求量 映射
     */
    public Map<Date, BigDecimal> queryDateDemandQtyMap(Long skuId, Long deptId, String month) {
        List<Map<String, Object>> mapList = sgCDepartmentMonthDemandMapper.selectDateDemandQtyListMap(skuId, deptId, month);
        if (CollectionUtils.isEmpty(mapList)) {
            return Collections.emptyMap();
        }

        Map<Date, BigDecimal> dateDecimalMap = new HashMap<>();
        for (Map<String, Object> map : mapList) {
            dateDecimalMap.put((Date) map.get("key"), (BigDecimal) map.get("value"));
        }
        return dateDecimalMap;
    }

    /**
     * 查找当月提报了需求的SKU
     *
     * @param versionBi 版本号
     * @return SKU列表
     */
    public Set<Long> querySkuByVersionBi(String versionBi) {
        Set<Long> skuIds = sgCDepartmentMonthDemandMapper.selectSkuByVersionBi(versionBi);
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptySet();
        }
        return skuIds;
    }

//    /**
//     * 查找当月提报了需求的 SKU->部门ID列表 映射
//     *
//     * @param demandMonth 月份
//     * @return SKU->部门ID列表 映射
//     */
//    public Map<Long, Set<Long>> querySkuDeptMapByMonth(String demandMonth) {
//        List<SgCDepartmentMonthDemand> demandList = sgCDepartmentMonthDemandMapper.selectList(new LambdaQueryWrapper<SgCDepartmentMonthDemand>()
//                .eq(SgCDepartmentMonthDemand::getIsactive, SgConstants.IS_ACTIVE_Y)
//                .eq(SgCDepartmentMonthDemand::getDemandMonth, demandMonth));
//        if (CollectionUtils.isEmpty(demandList)) {
//            return Collections.emptyMap();
//        }
//
//        return demandList.stream()
//                .collect(Collectors.groupingBy(SgCDepartmentMonthDemand::getPsCSkuId,
//                        Collectors.mapping(SgCDepartmentMonthDemand::getCpCDistributionOrgId, Collectors.toSet())));
//    }

    /**
     * 查询本月需要被拉回的SKU
     *
     * @param deptIds 部门ID列表
     * @return SKU列表
     */
    public Set<Long> querySku2Return(List<Long> deptIds, String versionBi, SgMonthDemandFromEnum fromEnum) {
        List<Object> pscSkuIds = sgCDepartmentMonthDemandMapper.selectObjs(new QueryWrapper<SgCDepartmentMonthDemand>()
                .select("DISTINCT ps_c_sku_id").lambda()
                .eq(SgCDepartmentMonthDemand::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCDepartmentMonthDemand::getVersionBi, versionBi)
                .eq(SgCDepartmentMonthDemand::getDemandFrom, fromEnum.getValue())
                .in(SgCDepartmentMonthDemand::getCpCDistributionOrgId, deptIds));

        if (CollectionUtils.isEmpty(pscSkuIds)) {
            return new HashSet<>();
        }
        return pscSkuIds.stream().map(o -> (Long) o).collect(Collectors.toSet());
    }

    /**
     * 查询不需要被拉回的SKU
     *
     * @return SKU列表
     */
    public Set<Long> queryOtherSku2ForceReturn(String versionBi) {
        List<Object> pscSkuIds = sgCDepartmentMonthDemandMapper.selectObjs(new QueryWrapper<SgCDepartmentMonthDemand>()
                .select("DISTINCT ps_c_sku_id").lambda()
                .eq(SgCDepartmentMonthDemand::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCDepartmentMonthDemand::getDemandFrom, SgMonthDemandFromEnum.OTHER.getValue())
                .eq(SgCDepartmentMonthDemand::getVersionBi, versionBi));

        if (CollectionUtils.isEmpty(pscSkuIds)) {
            return new HashSet<>();
        }
        return pscSkuIds.stream().map(o -> (Long) o).collect(Collectors.toSet());
    }

    /**
     * 查询该SKU在该部门下所有需求（如果全部满足也需要分货）
     *
     * @param saStores 二级部门列表
     * @param skuId    商品ID
     * @return 部门需求列表
     */
    public List<SgCDepartmentMonthDemand> queryDemand(List<SgCSaStore> saStores, Long skuId, String verionBi) {
        if (CollectionUtils.isEmpty(saStores)) {
            log.warn(LogUtil.format("配销仓数量为空", LOG_OBJ + "queryDemand"));
            return Collections.emptyList();
        }
        /*获取配销仓对应二级部门ID*/
        Set<Long> deptIds = saStores.stream().map(SgCSaStore::getCpCDistributionOrgId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(deptIds)) {
            log.warn(LogUtil.format("配销仓对应二级部门列表为空", LOG_OBJ + "queryDemand"));
            return Collections.emptyList();
        }

        LambdaQueryWrapper<SgCDepartmentMonthDemand> wrapper = new LambdaQueryWrapper<SgCDepartmentMonthDemand>()
                .eq(SgCDepartmentMonthDemand::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCDepartmentMonthDemand::getVersionBi, verionBi)
                .in(SgCDepartmentMonthDemand::getCpCDistributionOrgId, deptIds)
                .eq(SgCDepartmentMonthDemand::getPsCSkuId, skuId);

        List<SgCDepartmentMonthDemand> monthDemands = sgCDepartmentMonthDemandMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(monthDemands)) {
            return Collections.emptyList();
        }
        return monthDemands;
    }

    /**
     * 查询该SKU在该部门下所有需求
     *
     * @param deptId    二级部门ID
     * @param skuIds    商品ID集合
     * @param versionBi 版本号
     * @return 部门需求列表
     */
    private List<SgCDepartmentMonthDemand> queryDemandSatisfy(Long deptId, Set<Long> skuIds, String versionBi) {
        if (CollectionUtils.isEmpty(skuIds) || Objects.isNull(deptId)) {
            log.warn(LogUtil.format("查询参数非法", LOG_OBJ + "queryDemand"));
            return Collections.emptyList();
        }

        LambdaQueryWrapper<SgCDepartmentMonthDemand> wrapper = new LambdaQueryWrapper<SgCDepartmentMonthDemand>()
                .eq(SgCDepartmentMonthDemand::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCDepartmentMonthDemand::getVersionBi, versionBi)
                /*.ne(SgCDepartmentMonthDemand::getDemandStatus, SgMonthDemandStatusEnum.NO_START.getValue())*/
                .eq(SgCDepartmentMonthDemand::getCpCDistributionOrgId, deptId)
                .in(SgCDepartmentMonthDemand::getPsCSkuId, skuIds);

        List<SgCDepartmentMonthDemand> monthDemands = sgCDepartmentMonthDemandMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(monthDemands)) {
            return Collections.emptyList();
        }
        return monthDemands;
    }

    /**
     * 分货退货单退回时，需要扣减已满足的量
     *
     * @param returnMain     分货退货单信息
     * @param returnItems    分货退货单明细信息
     * @param isReturnDemand
     * @param loginUser      操作人
     */
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRES_NEW)
    public void modifyDemandByAllocationReturn(SgBShareAllocationReturn returnMain,
                                               List<SgBShareAllocationReturnItem> returnItems,
                                               boolean isReturnDemand, User loginUser) {
        if (Objects.isNull(returnMain) || CollectionUtils.isEmpty(returnItems)) {
            log.warn(LogUtil.format("分货退货单信息非法", LOG_OBJ + "modifyDemandByAllocationReturn"));
            throw new NDSException("分货退货单信息非法");
        }

        SgCPlanConvertVersion activeVersion = sgCPlanConvertVersionService.selectActive();

        Long saStoreId = returnMain.getSgCSaStoreId();
        List<SgCSaStore> saStoreList = querySaStoreByIds(Collections.singleton(saStoreId));
        if (CollectionUtils.isEmpty(saStoreList)) {
            log.warn(LogUtil.format("未找到对应配销仓信息，配销仓ID：{}", LOG_OBJ + "modifyDemandByAllocationReturn"), saStoreId);
            throw new NDSException("未找到对应配销仓信息");
        }
        SgCSaStore saStore = saStoreList.get(0);
        Long deptId = saStore.getCpCDistributionOrgId();
        if (Objects.isNull(deptId)) {
            log.info(LogUtil.format("配销仓无归属部门信息，配销仓ID：{}", LOG_OBJ + "modifyDemandByAllocationReturn"), saStoreId);
            return;
        }

        Map<Long, BigDecimal> skuQtyMap = returnItems.stream()
                .collect(Collectors.groupingBy(SgBShareAllocationReturnItem::getPsCSkuId,
                        Collectors.mapping(SgBShareAllocationReturnItem::getQty, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        List<SgCDepartmentMonthDemand> monthDemands = queryDemandSatisfy(deptId, skuQtyMap.keySet(), activeVersion.getVersionBi());
        if (CollectionUtils.isEmpty(monthDemands)) {
            log.info(LogUtil.format("未找到已满足的需求，配销仓ID：{},部门ID：{},skuQtyMap:{}", LOG_OBJ + "modifyDemandByAllocationReturn"),
                    saStoreId, deptId, JSON.toJSONString(skuQtyMap));
            return;
        }

        List<SgCDepartmentMonthDemand> monthDemandList = monthDemands.stream()
                .filter(d -> !SgMonthDemandStatusEnum.NO_START.getValue().equals(d.getDemandStatus()))
                .collect(Collectors.toList());
        /*扣减满足量*/
        if (CollectionUtil.isNotEmpty(monthDemandList)) {
            log.info(LogUtil.format("分货退货单退回已满足的需求量，退货单ID:{}，配销仓ID：{},对应部门ID：{}，明细：{},sku与退回数量映射：{}",
                            LOG_OBJ + "modifyDemandByAllocationReturn"),
                    returnMain.getId(), saStoreId, deptId, JSON.toJSONString(returnItems), JSON.toJSONString(skuQtyMap));
            doModifyDemandByAllocationReturn(monthDemandList, skuQtyMap, loginUser, returnMain);
        }

        if (!isReturnDemand) {
            List<SgCDepartmentMonthDemand> allDemands = queryDemandSatisfy(deptId, skuQtyMap.keySet(), activeVersion.getVersionBi());
            log.info(LogUtil.format("分货退货单变更需求量，退货单ID:{}，配销仓ID：{},对应部门ID：{}，明细：{},sku与退回数量映射：{}",
                            LOG_OBJ + "modifyDemandByAllocationReturn"),
                    returnMain.getId(), saStoreId, deptId, JSON.toJSONString(returnItems), JSON.toJSONString(skuQtyMap));
            doModifyDemandByAllocationReturnByTransfer(allDemands, skuQtyMap, loginUser, returnMain);
        }
    }

    private void doModifyDemandByAllocationReturnByTransfer(List<SgCDepartmentMonthDemand> monthDemandList,
                                                            Map<Long, BigDecimal> skuQtyMap,
                                                            User loginUser,
                                                            SgBShareAllocationReturn returnMain) {
        List<SgCDepartmentMonthDemand> modifyEntityList = new ArrayList<>();
        List<SgCOperationLog> operationLogList = new ArrayList<>();

        Map<Long, List<SgCDepartmentMonthDemand>> skuDemandListMap = monthDemandList.stream()
                .collect(Collectors.groupingBy(SgCDepartmentMonthDemand::getPsCSkuId));
        for (Map.Entry<Long, List<SgCDepartmentMonthDemand>> entry : skuDemandListMap.entrySet()) {
            List<SgCDepartmentMonthDemand> demandList = entry.getValue().stream()
                    .sorted(Comparator.comparing(SgCDepartmentMonthDemand::getDemandDate).reversed())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(demandList)) {
                continue;
            }

            BigDecimal returnQty = skuQtyMap.get(entry.getKey());
            log.info(LogUtil.format("分货退货单扣减需求量，sku：{},分货退回量：{}", LOG_OBJ + "doModifyDemandByAllocationReturn"),
                    entry.getKey(), returnQty);

            for (SgCDepartmentMonthDemand demand : demandList) {
                if (BigDecimal.ZERO.compareTo(returnQty) >= 0) {
                    break;
                }

                modifyEntityList.add(demand);

                BigDecimal demandQty = demand.getDemandQty();
                if (returnQty.compareTo(demandQty) >= 0) {
                    demand.setDemandQty(BigDecimal.ZERO);
                    demand.setDemandStatus(SgMonthDemandStatusEnum.COMPLETED.getValue());

                    returnQty = returnQty.subtract(demandQty);

                    SgCOperationLog operationLog = logCommonService.getOperationLog(SgConstants.SG_C_DEPARTMENT_MONTH_DEMAND.toUpperCase(),
                            OperationTypeEnum.MOD.getOperationValue(),
                            demand.getId(),
                            "分货退[" + returnMain.getBillNo() + "]扣减需求量,来源单据：[" + returnMain.getSourceBillNo() + "]",
                            "需求量",
                            demandQty.toString(),
                            demand.getDemandQty().toString(),
                            loginUser);
                    operationLogList.add(operationLog);
                    continue;
                }

                demand.setDemandQty(demandQty.subtract(returnQty));
                SgCOperationLog operationLog = logCommonService.getOperationLog(SgConstants.SG_C_DEPARTMENT_MONTH_DEMAND.toUpperCase(),
                        OperationTypeEnum.MOD.getOperationValue(),
                        demand.getId(),
                        "分货退[" + returnMain.getBillNo() + "]扣减需求量,来源单据：[" + returnMain.getSourceBillNo() + "]",
                        "需求量",
                        demandQty.toString(),
                        demand.getDemandQty().toString(),
                        loginUser);
                operationLogList.add(operationLog);
                break;
            }
        }

        batchModifyDemand(modifyEntityList, loginUser);

        logCommonService.batchInsertLog(operationLogList);
    }

    /**
     * 需要扣减已满足的量
     *
     * @param monthDemands 二级部门月需求列表
     * @param skuQtyMap    sku->需要拉回的数量
     * @param loginUser    当前用户
     * @param returnMain
     */
    private void doModifyDemandByAllocationReturn(List<SgCDepartmentMonthDemand> monthDemands, Map<Long, BigDecimal> skuQtyMap,
                                                  User loginUser, SgBShareAllocationReturn returnMain) {
        List<SgCDepartmentMonthDemand> modifyEntityList = new ArrayList<>();
        List<SgCDepartmentMonthDemandLog> demandLogList = new ArrayList<>();

        /*根据SKU分组执行*/
        Map<Long, List<SgCDepartmentMonthDemand>> skuDemandListMap = monthDemands.stream()
                .collect(Collectors.groupingBy(SgCDepartmentMonthDemand::getPsCSkuId));
        for (Map.Entry<Long, List<SgCDepartmentMonthDemand>> entry : skuDemandListMap.entrySet()) {
            /*按需求日期排序*/
            List<SgCDepartmentMonthDemand> demandList = entry.getValue().stream()
                    .sorted(Comparator.comparing(SgCDepartmentMonthDemand::getDemandDate).reversed())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(demandList)) {
                continue;
            }

            /*分货退回量*/
            BigDecimal returnQty = skuQtyMap.get(entry.getKey());
            /*已满足量*/
            BigDecimal totalActualInQty = demandList.stream()
                    .map(SgCDepartmentMonthDemand::getActualInQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            log.info(LogUtil.format("分货退货单扣减满足量，sku：{},分货退回量：{},已满足量：{}", LOG_OBJ + "doModifyDemandByAllocationReturn"),
                    entry.getKey(), returnQty, totalActualInQty);

            for (SgCDepartmentMonthDemand demand : demandList) {
                modifyEntityList.add(demand);
                SgCDepartmentMonthDemandLog demandLog = new SgCDepartmentMonthDemandLog();
                /*demandLog.setConvertConfigId(demand.getConvertConfigId());*/
                /*demandLog.setConvertDayPercentage(demand.getConvertDayPercentage());*/
                demandLog.setSgCDepartmentMonthDemandId(demand.getId());
                demandLog.setDemandDate(demand.getDemandDate());
                demandLog.setPsCSkuId(demand.getPsCSkuId());
                demandLog.setSourceBillNo(returnMain.getBillNo());
                demandLog.setSourceType(SgConstantsIF.BILL_SHARE_ALLOCATION_RETURN);
                demandLog.setVersionBi(demand.getVersionBi());
                demandLogList.add(demandLog);

                /*已满足量*/
                BigDecimal alreadyInQty = demand.getActualInQty();
                /*需要退的数量<=已经分的数量：判断未开始或部分满足*/
                if (returnQty.compareTo(alreadyInQty) <= 0) {
                    /*全都退回去*/
                    demandLog.setChangeQty(returnQty);

                    /*当前的满足量 = 已满足量 - 分货退回量*/
                    BigDecimal actualQty = alreadyInQty.subtract(returnQty);
                    demand.setActualInQty(actualQty);
                    demand.setDemandStatus(demand.getActualInQty().compareTo(BigDecimal.ZERO) == 0 ?
                            SgMonthDemandStatusEnum.NO_START.getValue() : SgMonthDemandStatusEnum.PARTIALLY_DONE.getValue());
                    // returnQty = BigDecimal.ZERO;
                    break;
                }

                demandLog.setChangeQty(demand.getActualInQty());
                /*需要退的数量>已经分的数量：直接重置为0-未开始*/
                returnQty = returnQty.subtract(demand.getActualInQty());
                demand.setActualInQty(BigDecimal.ZERO);
                demand.setDemandStatus(SgMonthDemandStatusEnum.NO_START.getValue());
            }
        }
        sgCDepartmentMonthDemandLogService.createLogList(demandLogList, loginUser);
        batchModifyDemand(modifyEntityList, loginUser);
    }

    /**
     * 查找当月提报了需求的部门
     *
     * @param demandMonth 月份
     * @param skuId       商品ID，可为空
     * @return 部门ID 列表
     */
    public Set<Long> queryDeptByMonth(String demandMonth, Long skuId) {
        Set<Long> deptIds = sgCDepartmentMonthDemandMapper.selectDeptByMonth(demandMonth, skuId);
        if (CollectionUtils.isEmpty(deptIds)) {
            return Collections.emptySet();
        }
        return deptIds;
    }

    public List<SgCDepartmentMonthDemand> queryByMonth(String month) {
        return sgCDepartmentMonthDemandMapper.selectList(new QueryWrapper<SgCDepartmentMonthDemand>().lambda()
                .eq(SgCDepartmentMonthDemand::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCDepartmentMonthDemand::getDemandMonth, month));
    }


    public int refreshByVersionBi(SgCPlanConvertVersion oldVersion, SgCPlanConvertVersion newVersion) {
        User user = SystemUserResource.getRootUser();

        /*非来自计划系统的需求，要保留，直接变更版本号就好了*/
        SgCDepartmentMonthDemand noFromScp = new SgCDepartmentMonthDemand();
        noFromScp.setVersionBi(newVersion.getVersionBi());
        noFromScp.setVersionPlan(newVersion.getVersionPlan());
        noFromScp.setMonthWeek("-");
        StorageUtils.setBModelDefalutDataByUpdate(noFromScp, user);
        sgCDepartmentMonthDemandMapper.update(noFromScp, new LambdaUpdateWrapper<SgCDepartmentMonthDemand>()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                /*.eq(SgCDepartmentMonthDemand::getVersionBi, oldVersion.getVersionBi())*/
                .eq(SgCDepartmentMonthDemand::getDemandFrom, SgMonthDemandFromEnum.OTHER.getValue()));

        /*把老版本的需求作废掉*/
        SgCDepartmentMonthDemand entity = new SgCDepartmentMonthDemand();
        entity.setDelerId(Long.valueOf(user.getId()));
        entity.setDelTime(new Date());
        entity.setDelerName(user.getName());
        entity.setDelerEname(user.getEname());

        entity.setIsactive(SgConstants.IS_ACTIVE_N);
        LambdaUpdateWrapper<SgCDepartmentMonthDemand> disableWrapper = new LambdaUpdateWrapper<SgCDepartmentMonthDemand>()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y);
        /*如果老版本不为空，则说明不是第一次（以前没有老版本），需要加上版本号，否则作废其他的*/
        if (Objects.nonNull(oldVersion)) {
            disableWrapper.eq(SgCDepartmentMonthDemand::getVersionBi, oldVersion.getVersionBi());
        } else {
            disableWrapper.ne(SgCDepartmentMonthDemand::getVersionBi, newVersion.getVersionBi());
        }

        return sgCDepartmentMonthDemandMapper.update(entity, disableWrapper);
    }

    /**
     * 参与分货的SKU
     *
     * @param versionBi versionBi
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return SKU编码列表
     */
    public List<String> queryEcode(String versionBi, String startDate, String endDate) {
        return ListUtils.emptyIfNull(sgCDepartmentMonthDemandMapper.selectObjs(new QueryWrapper<SgCDepartmentMonthDemand>()
                        .select("DISTINCT ps_c_sku_ecode").lambda()
                        .eq(SgCDepartmentMonthDemand::getVersionBi, versionBi)
                        .between(SgCDepartmentMonthDemand::getDemandDate, startDate, endDate)
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)))
                .stream().map(o -> (String) o).collect(Collectors.toList());
    }

    /**
     * 参与计算的渠道部门
     *
     * @param versionBi versionBi
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 渠道部门列表
     */
    public List<Long> queryLvChannelEcode(String versionBi, String startDate, String endDate) {
        return ListUtils.emptyIfNull(sgCDepartmentMonthDemandMapper.selectObjs(new QueryWrapper<SgCDepartmentMonthDemand>()
                        .select("DISTINCT cp_c_distribution_org_id").lambda()
                        .eq(SgCDepartmentMonthDemand::getVersionBi, versionBi)
                        .between(SgCDepartmentMonthDemand::getDemandDate, startDate, endDate)
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)))
                .stream().map(o -> (Long) o).collect(Collectors.toList());
    }

    /**
     * 查询SKU需求
     *
     * @param versionBi   versionBi
     * @param psCSkuEcode sku编码
     * @param startDate   开始时间
     * @param endDate     结束时间
     * @return 渠道ID->[需求日期->需求量]
     */
    public Map<Long, Map<Date, BigDecimal>> queryDemandBySku(String versionBi, String psCSkuEcode, String startDate, String endDate) {
        List<SgBCommonStorageQtyQueryResult> dateQtyList = sgCDepartmentMonthDemandMapper.selectDateQtyBySku(versionBi, psCSkuEcode, startDate, endDate);
        return ListUtils.emptyIfNull(dateQtyList).stream()
                .collect(Collectors.groupingBy(
                        SgBCommonStorageQtyQueryResult::getCpCDistributionOrgId,
                        Collectors.toMap(SgBCommonStorageQtyQueryResult::getMDate,
                                SgBCommonStorageQtyQueryResult::getQty, BigDecimal::add)));
    }

    /**
     * 查询拆包生成的需求
     *
     * @param versionBi 版本号
     * @return 需求列表
     */
    public List<SgCDepartmentMonthDemand> queryConvertDemandByVersionBi(String versionBi) {
        return sgCDepartmentMonthDemandMapper.selectList(new QueryWrapper<SgCDepartmentMonthDemand>().lambda()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCDepartmentMonthDemand::getVersionBi, versionBi)
                .eq(SgCDepartmentMonthDemand::getDemandFrom, SgMonthDemandFromEnum.SCP.getValue())
                .isNotNull(SgCDepartmentMonthDemand::getConvertConfigId));
    }

}
