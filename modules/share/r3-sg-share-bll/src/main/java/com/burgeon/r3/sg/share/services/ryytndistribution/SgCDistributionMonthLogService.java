package com.burgeon.r3.sg.share.services.ryytndistribution;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.SgDistributionTypeEnum;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCDistributionMonthLog;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgCDistributionMonthLogMapper;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 拉回记录表
 *
 * <AUTHOR>
 * @since 2023-03-01 15:26
 */
@Slf4j
@Service
public class SgCDistributionMonthLogService {
    @Resource
    private SgCDistributionMonthLogMapper sgCDistributionMonthLogMapper;

    public Set<Long> querySkuByShareStoreId(Long shareStoreId, String versionBi) {
        List<Object> pscSkuIds = sgCDistributionMonthLogMapper.selectObjs(
                new QueryWrapper<SgCDistributionMonthLog>().select("DISTINCT ps_c_sku_id").lambda()
                        .eq(SgCDistributionMonthLog::getShareStoreId, shareStoreId)
                        .eq(SgCDistributionMonthLog::getVersionBi, versionBi)
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (org.springframework.util.CollectionUtils.isEmpty(pscSkuIds)) {
            return Collections.emptySet();
        }
        return pscSkuIds.stream().map(o -> (Long) o).collect(Collectors.toSet());
    }

    public void createMonthLog(Long shareStoreId, Long skuId, List<Pair<Long, BigDecimal>> pairList, SgDistributionTypeEnum typeEnum, String versionBi) {
        SgCDistributionMonthLog monthLog = new SgCDistributionMonthLog();
        monthLog.setId(ModelUtil.getSequence(SgConstants.SG_C_DISTRIBUTION_MONTH_LOG));

        monthLog.setShareStoreId(shareStoreId);
        monthLog.setPsCSkuId(skuId);
        monthLog.setExeMonth(DateFormatUtils.format(new Date(), "yyyyMM"));
        monthLog.setVersionBi(versionBi);
        monthLog.setOptType(typeEnum.getType());
        monthLog.setRemark(typeEnum.getDesc());

        SgCDistributionMonthLog.ExtData extData = new SgCDistributionMonthLog.ExtData();
        extData.setPairList(convertKey2String(pairList));
        monthLog.setExtData(JSON.toJSONString(extData));

        //设置用户信息字段
        StorageUtils.setBModelDefalutData(monthLog, R3SystemUserResource.getSystemRootUser());
        sgCDistributionMonthLogMapper.insert(monthLog);
    }

    /**
     * @param pairList 配销仓ID+退货分货量 映射列表
     * @return 转map存到数据库
     */
    private List<Pair<String, BigDecimal>> convertKey2String(List<Pair<Long, BigDecimal>> pairList) {
        if (CollectionUtils.isEmpty(pairList)) {
            return Collections.emptyList();
        }

        return pairList.stream()
                .map(pair -> Pair.of(String.valueOf(pair.getLeft()), pair.getRight()))
                .collect(Collectors.toList());
    }

    /**
     * 创建拉回记录（拉回内容为空，跳过拉回直接分货）
     *
     * @param skuId            skuId
     * @param shareStoreIdList 聚合仓ID列表
     * @param typeEnum         操作类型
     */
    public void createBySkip(Long skuId, List<Long> shareStoreIdList, SgDistributionTypeEnum typeEnum, String versionBi) {
        if (CollectionUtils.isEmpty(shareStoreIdList)) {
            return;
        }

        List<SgCDistributionMonthLog> logList = new ArrayList<>();
        for (Long shareStoreId : shareStoreIdList) {
            SgCDistributionMonthLog monthLog = new SgCDistributionMonthLog();
            monthLog.setId(ModelUtil.getSequence(SgConstants.SG_C_DISTRIBUTION_MONTH_LOG));

            monthLog.setShareStoreId(shareStoreId);
            monthLog.setPsCSkuId(skuId);
            monthLog.setExeMonth(DateFormatUtils.format(new Date(), "yyyyMM"));
            monthLog.setVersionBi(versionBi);
            monthLog.setOptType(typeEnum.getType());
            monthLog.setRemark(typeEnum.getDesc());

            //设置用户信息字段
            StorageUtils.setBModelDefalutData(monthLog, R3SystemUserResource.getSystemRootUser());
            monthLog.setIssystem(SgConstants.IS_ACTIVE_N);
            logList.add(monthLog);
        }

        sgCDistributionMonthLogMapper.batchInsert(logList);
    }

    /**
     * 创建拉回记录（拉回内容为空，跳过拉回直接分货）
     *
     * @param forceSku     skuId列表
     * @param shareStoreId 聚合仓ID
     * @param typeEnum     操作类型
     */
    public void createMonthByForce(Set<Long> forceSku, Long shareStoreId, SgDistributionTypeEnum typeEnum, String versionBi) {
        if (CollectionUtils.isEmpty(forceSku)) {
            return;
        }

        List<SgCDistributionMonthLog> logList = new ArrayList<>();
        for (Long skuId : forceSku) {
            SgCDistributionMonthLog monthLog = new SgCDistributionMonthLog();
            monthLog.setId(ModelUtil.getSequence(SgConstants.SG_C_DISTRIBUTION_MONTH_LOG));

            monthLog.setShareStoreId(shareStoreId);
            monthLog.setPsCSkuId(skuId);
            monthLog.setExeMonth(DateFormatUtils.format(new Date(), "yyyyMM"));
            monthLog.setVersionBi(versionBi);
            monthLog.setOptType(typeEnum.getType());
            monthLog.setRemark(typeEnum.getDesc());

            //设置用户信息字段
            StorageUtils.setBModelDefalutData(monthLog, R3SystemUserResource.getSystemRootUser());
            monthLog.setIssystem(SgConstants.IS_ACTIVE_N);
            logList.add(monthLog);
        }

        sgCDistributionMonthLogMapper.batchInsert(logList);
    }
}
