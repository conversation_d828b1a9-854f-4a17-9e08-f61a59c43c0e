package com.burgeon.r3.sg.share.filter.transfer;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareFromSaTransferImportItem;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareFromSaTransferImportItemMapper;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareFromSaTransferMapper;
import com.burgeon.r3.sg.share.model.dto.SgBShareFromSaTransferDto;
import com.burgeon.r3.sg.share.model.dto.SgBShareFromSaTransferImportItemDto;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.ps.api.table.PsCPro;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleItemFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/13 10:46
 */
@Slf4j
@Component
public class SgBShareFromSaTransferSaveFilter extends BaseSingleItemFilter<SgBShareFromSaTransferDto,
        SgBShareFromSaTransferImportItemDto> {

    @Autowired
    private SgBShareFromSaTransferImportItemMapper importItemMapper;
    @Autowired
    private SgBShareFromSaTransferMapper shareFromSaTransferMapper;

    @Override
    public String getFilterMsgName() {
        return "配销仓到聚合仓调拨单新增保存";
    }

    @Override
    public Class getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgBShareFromSaTransferDto mainObject, User loginUser) {
        return null;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgBShareFromSaTransferDto mainObject, User loginUser) {
        return null;
    }

    @Override
    public ValueHolderV14 execBeforeSubTable(SgBShareFromSaTransferDto mainObject,
                                             List<SgBShareFromSaTransferImportItemDto> subObjectList, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("SgBShareFromSaTransferSaveFilter.execBeforeSubTable param={}", JSONObject.toJSONString(subObjectList));
        }
        List<SgBShareFromSaTransferImportItemDto> filterQtyList =
                subObjectList.stream().filter(s -> s.getQty().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filterQtyList)) {
            return new ValueHolderV14<>(ResultCode.FAIL,
                    Resources.getMessage(filterQtyList.stream().map(SgBShareFromSaTransferImportItemDto::getPsCSkuOrPro)
                            .collect(Collectors.toList()) + "申请数量不允许小于0!", loginUser.getLocale()));
        }

        List<SgBShareFromSaTransferImportItemDto> inserSubList =
                subObjectList.stream().filter(sub -> sub.getId() < 0L).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(inserSubList) && inserSubList.size() > 1) {
            //导入场景时 根据属性分组 若出现多个提示合并
            Map<String, List<SgBShareFromSaTransferImportItemDto>> stringListMap =
                    inserSubList.parallelStream().collect(Collectors.groupingBy(e -> e.getId() + ":" + e.getSenderSaStoreId() + ":" + e.getReceiverShareStoreId() + ":" + e.getPsCSkuOrPro()));
            for (List<SgBShareFromSaTransferImportItemDto> importItemDtos : stringListMap.values()) {
                if (importItemDtos.size() > 1) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage(
                            "款号或条码:" + importItemDtos.get(0).getPsCSkuOrPro() + "有重复数据,请检查数据或合并数量后操作!",
                            loginUser.getLocale()));
                }
            }
        }
        //商品和条码信息Map
        Map<String, PsCProSkuResult> skuResultMap = new HashMap<>(16);
        Map<String, PsCPro> proResultMap = new HashMap<>(16);
        //配销仓信息map
        Map<Long, SgCSaStore> saStoreMap = new HashMap<>(16);
        //聚合仓信息map
        Map<Long, SgCShareStore> shareStoreMap = new HashMap<>(16);
        //聚合仓id对应逻辑仓信息map  聚合仓对应逻辑仓是 1对多
        Map<Long, List<SgCpCStore>> storeMap = new HashMap<>(16);
        //逻辑仓id对应实体仓id对应关系Map
        Map<Long, Long> storeIdWithPhyWarehouseIdMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(inserSubList)) {
            List<String> skuOrProEcodes =
                    inserSubList.stream().map(SgBShareFromSaTransferImportItemDto::getPsCSkuOrPro).distinct().collect(Collectors.toList());
            //商品和条码信息
            skuResultMap = CommonCacheValUtils.getSkuInfo(skuOrProEcodes);
            proResultMap = CommonCacheValUtils.getProInfoMapByEcodes(skuOrProEcodes);
            List<Long> senderSaStoreIds =
                    inserSubList.stream().map(SgBShareFromSaTransferImportItemDto::getSenderSaStoreId).distinct().collect(Collectors.toList());
            List<Long> receiveShareStoreIds =
                    inserSubList.stream().map(SgBShareFromSaTransferImportItemDto::getReceiverShareStoreId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(senderSaStoreIds) ) {
                //收集配销仓信息
                saStoreMap = CommonCacheValUtils.querySaStoreByIds(senderSaStoreIds);
                if (MapUtils.isEmpty(saStoreMap)) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("当前发货配销仓信息不存在!",
                            loginUser.getLocale()));
                }
                List<Long> shareStoreIds =
                        saStoreMap.values().stream().map(SgCSaStore::getSgCShareStoreId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(receiveShareStoreIds)){
                    shareStoreIds.addAll(receiveShareStoreIds);
                }

                //收集聚合仓信息
                shareStoreMap = CommonCacheValUtils.queryShareStoreByIds(shareStoreIds);
                if (MapUtils.isEmpty(shareStoreMap)) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("当前发货聚合仓和收货聚合仓对应的聚合仓信息不存在!",
                            loginUser.getLocale()));
                }
                storeMap = CommonCacheValUtils.queryStoreByShareStoreIds(shareStoreMap.keySet());
                if (MapUtils.isEmpty(storeMap)) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("当前发货配销仓和收货聚合仓对应的逻辑仓信息不存在或不可用!",
                            loginUser.getLocale()));
                }
                for (List<SgCpCStore> stores : storeMap.values()) {
                    //收集逻辑仓id对应实体仓id对应关系
                    stores.forEach(store -> storeIdWithPhyWarehouseIdMap.put(store.getId(),
                            store.getCpCPhyWarehouseId()));
                }
            }
        }

        for (SgBShareFromSaTransferImportItemDto importItemDto : subObjectList) {
            //更新
            if (importItemDto.getId() > 0) {
                StorageUtils.setBModelDefalutDataByUpdate(importItemDto, loginUser);
                continue;
            }
            PsCProSkuResult skuResult = skuResultMap.get(importItemDto.getPsCSkuOrPro());
            PsCPro proResult = proResultMap.get(importItemDto.getPsCSkuOrPro());
            if (Objects.isNull(skuResult) && Objects.isNull(proResult)) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("输入的商品或条码:" +
                        importItemDto.getPsCSkuOrPro() + ",不存在,请确认!", loginUser.getLocale()));
            }
            //发货配销仓信息
            SgCSaStore senderSaStore = saStoreMap.get(importItemDto.getSenderSaStoreId());
            //收货聚合仓信息
            SgCShareStore receiverShareStore = shareStoreMap.get(importItemDto.getReceiverShareStoreId());
            if (Objects.isNull(senderSaStore) || Objects.isNull(receiverShareStore)) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("输入的发货配销仓或收货聚合仓信息已不存在,请确认!",
                        loginUser.getLocale()));
            }
            //发货聚合仓信息
            SgCShareStore senderShareStore = shareStoreMap.get(senderSaStore.getSgCShareStoreId());
            if (Objects.isNull(senderShareStore)) {
                return new ValueHolderV14<>(ResultCode.FAIL,
                        Resources.getMessage("输入的发货配销仓:" + senderSaStore.getEcode() +
                                "所属聚合仓信息已不存在,请确认!", loginUser.getLocale()));
            }
//            if (senderShareStore.getId().equals(receiverShareStore.getId())) {
//                return new ValueHolderV14<>(ResultCode.FAIL,
//                        Resources.getMessage("[发货配销仓]的所属聚合仓与[收货聚合仓]相同，请使用【分货退货单】制单!", loginUser.getLocale()));
//            }
            //查询是否存在 相同发货配销仓+收货聚合仓+款号或条码明细 存在则更新
            SgBShareFromSaTransferImportItem haveImportItem =
                    importItemMapper.selectOne(new LambdaQueryWrapper<SgBShareFromSaTransferImportItem>()
                            .eq(SgBShareFromSaTransferImportItem::getSgBShareFromSaTransferId, mainObject.getId())
                            .eq(SgBShareFromSaTransferImportItem::getSenderSaStoreId,
                                    importItemDto.getSenderSaStoreId())
                            .eq(SgBShareFromSaTransferImportItem::getReceiverShareStoreId,
                                    importItemDto.getReceiverShareStoreId())
                            .eq(SgBShareFromSaTransferImportItem::getPsCSkuOrPro, importItemDto.getPsCSkuOrPro()));
            if (Objects.nonNull(haveImportItem)) {
                importItemDto.setId(haveImportItem.getId());
                importItemDto.setQty(haveImportItem.getQty().add(importItemDto.getQty()));
                StorageUtils.setBModelDefalutDataByUpdate(importItemDto, loginUser);
                continue;
            }

            if (Objects.nonNull(skuResult)) {
                importItemDto.setGbcode(skuResult.getGbcode());
                importItemDto.setPsCSkuId(skuResult.getId());
                importItemDto.setPsCSkuEcode(skuResult.getSkuEcode());
                importItemDto.setPsCBrandId(skuResult.getPsCBrandId());
                importItemDto.setPsCSpec1Id(skuResult.getPsCSpec1objId());
                importItemDto.setPsCSpec1Ecode(skuResult.getClrsEcode());
                importItemDto.setPsCSpec1Ename(skuResult.getClrsEname());
                importItemDto.setPsCSpec2Id(skuResult.getPsCSpec2objId());
                importItemDto.setPsCSpec2Ecode(skuResult.getSizesEcode());
                importItemDto.setPsCSpec2Ename(skuResult.getSizesEname());
                importItemDto.setTransferDimension(SgShareConstants.SG_B_SHARE_TRANSFER_DIMENSION_SKU);
                importItemDto.setPriceList(Optional.ofNullable(skuResult.getPricelist()).orElse(BigDecimal.ZERO));
            }
            if (Objects.nonNull(proResult)) {
                importItemDto.setPriceList(Optional.ofNullable(proResult.getPricelist()).orElse(BigDecimal.ZERO));
                importItemDto.setPsCProId(proResult.getId());
                importItemDto.setPsCProEcode(proResult.getEcode());
                importItemDto.setPsCProEname(proResult.getEname());
                importItemDto.setPsCBrandId(proResult.getPsCBrandId());
                importItemDto.setTransferDimension(SgShareConstants.SG_B_SHARE_TRANSFER_DIMENSION_PRO);
            }

            //冗余发货配销仓信息
            importItemDto.setSenderSaStoreEcode(senderSaStore.getEcode());
            importItemDto.setSenderSaStoreEname(senderSaStore.getEname());
            //发货聚合仓和收货聚合仓
            importItemDto.setSenderShareStoreId(senderShareStore.getId());
            importItemDto.setSenderShareStoreEcode(senderShareStore.getEcode());
            importItemDto.setSenderShareStoreEname(senderShareStore.getEname());
            importItemDto.setReceiverShareStoreId(receiverShareStore.getId());
            importItemDto.setReceiverShareStoreEcode(receiverShareStore.getEcode());
            importItemDto.setReceiverShareStoreEname(receiverShareStore.getEname());

            List<SgCpCStore> senderStores = storeMap.get(senderShareStore.getId());
            if (CollectionUtils.isEmpty(senderStores)) {
                return new ValueHolderV14<>(ResultCode.FAIL,
                        Resources.getMessage("输入的发货配销仓:" + senderSaStore.getEcode() +
                                "所属逻辑仓信息不可用或不存在,请确认!", loginUser.getLocale()));
            }
            List<SgCpCStore> receiverStores = storeMap.get(receiverShareStore.getId());
            if (CollectionUtils.isEmpty(receiverStores)) {
                return new ValueHolderV14<>(ResultCode.FAIL,
                        Resources.getMessage("输入的收货聚合仓:" + receiverShareStore.getEcode() +
                                "所属逻辑仓信息不可用或不存在,请确认!", loginUser.getLocale()));
            }
            Set<Long> phyIdsByMap = storeIdWithPhyWarehouseIdMap.keySet();

            List<Long> senderPhyIds = new ArrayList<>();
            for (SgCpCStore sendStoreId : senderStores) {
                if (phyIdsByMap.contains(sendStoreId.getId())) {
                    senderPhyIds.add(storeIdWithPhyWarehouseIdMap.get(sendStoreId.getId()));
                }
            }
            if (CollectionUtils.isEmpty(senderPhyIds)) {
                return new ValueHolderV14<>(ResultCode.FAIL,
                        Resources.getMessage("输入的发货配销仓:" + senderSaStore.getEcode() +
                                "所属实体仓信息不可用或不存在,请确认!", loginUser.getLocale()));
            }
            List<Long> receiverPhyIds = new ArrayList<>();
            for (SgCpCStore receiverStore : receiverStores) {
                if (phyIdsByMap.contains(receiverStore.getId())) {
                    receiverPhyIds.add(storeIdWithPhyWarehouseIdMap.get(receiverStore.getId()));
                }
            }

            if (CollectionUtils.isEmpty(receiverPhyIds)) {
                return new ValueHolderV14<>(ResultCode.FAIL,
                        Resources.getMessage("输入的收货聚合仓:" + receiverShareStore.getEcode() +
                                "所属实体仓信息不可用或不存在,请确认!", loginUser.getLocale()));
            }

            Collection retainAll = CollectionUtils.retainAll(senderPhyIds, receiverPhyIds);
            if (CollectionUtils.isEmpty(retainAll)) {
                return new ValueHolderV14<>(ResultCode.FAIL,
                        Resources.getMessage("收发货聚合仓间不存在相同实体仓记录，不允许调拨", loginUser.getLocale()));
            }
            StorageUtils.setBModelDefalutData(importItemDto, loginUser);
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "保存成功！");
    }

    @Override
    public ValueHolderV14 execAfterSubTable(SgBShareFromSaTransferDto mainObject,
                                            List<SgBShareFromSaTransferImportItemDto> subObjectList, User loginUser) {
        return null;
    }
}
