package com.burgeon.r3.sg.share.services.ryytndistribution;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.utils.SgCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgDeptDemandPlanReportQtyResult;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCDepartmentDemandPlan;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgCDepartmentDemandPlanMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.ps.api.table.PsCPro;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 部门日需求计划报表服务
 *
 * <AUTHOR>
 * @since 2022-10-26 17:28
 */
@Slf4j
@Component
public class SgCDepartmentDemandPlanService {
    /**
     * 日志OBJ
     */
    private static final String LOG_OBJ = "SgCDepartmentDemandPlanService.";

    @Resource
    private SgCDepartmentDemandPlanMapper sgCDepartmentDemandPlanMapper;

//    @Resource
//    private SgS2SaAutoDistributionManager sgS2SaAutoDistributionManager;
//    @Resource
//    private SgCDepartmentMonthDemandService sgCDepartmentMonthDemandService;
//    @Resource
//    private SgCMonthProductionPlanService sgCMonthProductionPlanService;

    @Resource
    private ThreadPoolTaskExecutor asyncExecutorPool;

    @NacosValue(value = "${r3.sg.adb.scsg:test_rpt_sc_sg}", autoRefreshed = true)
    private String sgAdbPrefix;


//    @Deprecated
//    public ValueHolderV14<Void> execute(Date exeDate, boolean isNew) {
//        if (isNew) {
//            return executeNew(exeDate);
//        }
//        return executeOld(exeDate);
//    }

//    /**
//     * 执行任务
//     *
//     * @param exeDate 需要计算的日期
//     * @return 任务返回信息
//     */
//    @Deprecated
//    public ValueHolderV14<Void> executeOld(Date exeDate) {
//        SgCDepartmentDemandPlanService service = ApplicationContextHandle.getBean(SgCDepartmentDemandPlanService.class);
//        int successSkuCnt = 0;
//        int totalSkuCount = 0;
//
//        /*获取所有可用聚合仓*/
//        List<SgCShareStore> shareStoreList = sgS2SaAutoDistributionManager.querySgShareStores();
//        /*获取聚合仓下所有配销仓，聚合仓ID->配销仓列表*/
//        Map<Long, List<SgCSaStore>> shareIdSaStoreMap = sgS2SaAutoDistributionManager.querySaStoresMap(shareStoreList, true);
//        /*逐个聚合仓处理*/
//        for (Map.Entry<Long, List<SgCSaStore>> shareStoreEntry : shareIdSaStoreMap.entrySet()) {
//            /*聚合仓ID*/
//            Long shareStoreId = shareStoreEntry.getKey();
//            /*配销仓列表(过滤无归属部门的配销仓)*/
//            List<SgCSaStore> caStoreList = ListUtils.emptyIfNull(shareStoreEntry.getValue()).stream()
//                    .filter(obj -> Objects.nonNull(obj.getCpCStoredimItemId())).collect(Collectors.toList());
//            if (CollectionUtils.isEmpty(caStoreList)) {
//                log.warn(LogUtil.format("聚合仓下无部门下的配销仓信息，聚合仓ID：{}", LOG_OBJ + "execute"), shareStoreId);
//                continue;
//            }
//            /*部门ID->配销仓ID列表 映射*/
//            Map<Long, Set<Long>> deptSaIdsMap = caStoreList.stream()
//                    .filter(obj -> Objects.nonNull(obj.getCpCStoredimItemId()))
//                    .collect(Collectors.groupingBy(SgCSaStore::getCpCStoredimItemId, Collectors.mapping(SgCSaStore::getId, Collectors.toSet())));
//            /*获取部门需求的SKU列表*/
//            Set<Long> skuIds = sgCDepartmentMonthDemandService.querySkuBySaStore(deptSaIdsMap.keySet(), DateFormatUtils.format(exeDate, "yyyyMM"));
//            if (CollectionUtils.isEmpty(skuIds)) {
//                log.debug(LogUtil.format("获取部门需求的SKU列表为空，部门ID列表：{},商品ID列表：{}", LOG_OBJ + "execute"),
//                        deptSaIdsMap.keySet(), skuIds);
//                continue;
//            }
//
//            totalSkuCount += skuIds.size();
//            for (Long skuId : skuIds) {
//                /*逐个SKU处理*/
//                try {
//                    service.executeBySku(shareStoreId, skuId, deptSaIdsMap, exeDate);
//                    successSkuCnt++;
//                } catch (Exception e) {
//                    log.error(LogUtil.format("部门日需求计划报表SKU级别执行出错，部门ID列表：{}，商品ID：{},执行时间：{},错误信息:{}", LOG_OBJ + "execute"),
//                            deptSaIdsMap.keySet(), skuId, exeDate, Throwables.getStackTraceAsString(e));
//                }
//            }
//        }
//        if (successSkuCnt != totalSkuCount) {
//            log.warn(LogUtil.format("部门日需求计划报表任务部分成功部分失败，成功SKU数：{},总SKU数：{}", LOG_OBJ + "execute"), successSkuCnt, totalSkuCount);
//        }
//
//        return new ValueHolderV14<>(ResultCode.SUCCESS, "部门日需求计划任务执行！,成功SKU数：" + successSkuCnt + "/" + totalSkuCount);
//    }

//    /**
//     * 执行任务
//     *
//     * @param exeDate 需要计算的日期
//     * @return 任务返回信息
//     */
//    @Deprecated
//    public ValueHolderV14<Void> executeNew(Date exeDate) {
//        CusRedisTemplate<String, Long> redisTemplate = RedisMasterUtils.getObjRedisTemplate();
//        SetOperations<String, Long> redisSetOperations = redisTemplate.opsForSet();
//
//        SgCDepartmentDemandPlanService service = ApplicationContextHandle.getBean(SgCDepartmentDemandPlanService.class);
//        int successSkuCnt = 0;
//        int totalSkuCount = 0;
//
//        /*获取所有可用聚合仓*/
//        List<SgCShareStore> shareStoreList = sgS2SaAutoDistributionManager.querySgShareStores();
//        /*获取聚合仓下所有配销仓，聚合仓ID->配销仓列表*/
//        Map<Long, List<SgCSaStore>> shareIdSaStoreMap = sgS2SaAutoDistributionManager.querySaStoresMap(shareStoreList, true);
//        /*逐个聚合仓处理*/
//        for (Map.Entry<Long, List<SgCSaStore>> shareStoreEntry : shareIdSaStoreMap.entrySet()) {
//            /*聚合仓ID*/
//            Long shareStoreId = shareStoreEntry.getKey();
//            /*配销仓列表(过滤无归属部门的配销仓)*/
//            List<SgCSaStore> caStoreList = ListUtils.emptyIfNull(shareStoreEntry.getValue()).stream()
//                    .filter(obj -> Objects.nonNull(obj.getCpCStoredimItemId())).collect(Collectors.toList());
//            if (CollectionUtils.isEmpty(caStoreList)) {
//                log.warn(LogUtil.format("聚合仓下无部门下的配销仓信息，聚合仓ID：{}", LOG_OBJ + "execute"), shareStoreId);
//                continue;
//            }
//            /*部门ID->配销仓ID列表 映射*/
//            Map<Long, Set<Long>> deptSaIdsMap = caStoreList.stream()
//                    .filter(obj -> Objects.nonNull(obj.getCpCStoredimItemId()))
//                    .collect(Collectors.groupingBy(SgCSaStore::getCpCStoredimItemId, Collectors.mapping(SgCSaStore::getId, Collectors.toSet())));
//            /*获取部门需求的SKU列表*/
//            Set<Long> skuIds = sgCDepartmentMonthDemandService.querySkuBySaStore(deptSaIdsMap.keySet(), DateFormatUtils.format(exeDate, "yyyyMM"));
//            /*需要被执行的SKU*/
//            Set<Long> exeSkuIds = filterExeSkuByRedis(shareStoreEntry.getKey(), redisSetOperations, skuIds);
//            if (CollectionUtils.isEmpty(exeSkuIds)) {
//                log.debug(LogUtil.format("获取部门需求的SKU列表为空，部门ID列表：{},商品ID列表：{}", LOG_OBJ + "execute"),
//                        deptSaIdsMap.keySet(), exeSkuIds);
//                continue;
//            }
//
//            totalSkuCount += exeSkuIds.size();
//            for (Long skuId : exeSkuIds) {
//                /*逐个SKU处理*/
//                try {
//                    service.executeBySku(shareStoreId, skuId, deptSaIdsMap, exeDate);
//                    successSkuCnt++;
//                } catch (Exception e) {
//                    log.error(LogUtil.format("部门日需求计划报表SKU级别执行出错，部门ID列表：{}，商品ID：{},执行时间：{},错误信息:{}", LOG_OBJ + "execute"),
//                            deptSaIdsMap.keySet(), skuId, exeDate, Throwables.getStackTraceAsString(e));
//                }
//            }
//        }
//        if (successSkuCnt != totalSkuCount) {
//            log.warn(LogUtil.format("部门日需求计划报表任务部分成功部分失败，成功SKU数：{},总SKU数：{}", LOG_OBJ + "execute"), successSkuCnt, totalSkuCount);
//        }
//
//        return new ValueHolderV14<>(ResultCode.SUCCESS, "部门日需求计划任务执行！,成功SKU数：" + successSkuCnt + "/" + totalSkuCount);
//    }

//    private Set<Long> filterExeSkuByRedis(Long shareId, SetOperations<String, Long> redisSetOperations, Set<Long> skuIds) {
//        Set<Long> monthSuccessSkuIds = Optional.ofNullable(redisSetOperations.members(SgShareConstants.DISTRIBUTION_MONTH_SUCCESS_SKU_SET_KEY + shareId))
//                .orElse(Collections.emptySet());
//        /*定时分货-每次处理成功的SKU（过滤掉失败的）*/
//        return skuIds.stream().filter(monthSuccessSkuIds::contains).collect(Collectors.toSet());
//    }


//    /**
//     * 逐个SKU处理
//     *
//     * @param shareStoreId 聚合仓ID
//     * @param skuId        商品ID
//     * @param deptSaIdsMap 部门ID->配销仓ID列表 映射
//     * @param exeDate      执行日期
//     */
//    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRES_NEW)
//    public void executeBySku(Long shareStoreId, Long skuId, Map<Long, Set<Long>> deptSaIdsMap, Date exeDate) {
//        List<SgCDepartmentDemandPlan> allDeptPlanList = new ArrayList<>();
//        /*逐个部门处理*/
//        for (Map.Entry<Long, Set<Long>> deptSaIdsEntry : deptSaIdsMap.entrySet()) {
//            /*逐个部门处理*/
//            List<SgCDepartmentDemandPlan> planList = executeBySkuDept(skuId, exeDate, deptSaIdsEntry.getKey(), deptSaIdsEntry.getValue());
//            allDeptPlanList.addAll(planList);
//        }
//
//        /*计算计划入库量*/
//        calculatePlanQty(shareStoreId, skuId, exeDate, allDeptPlanList);
//
//        /*计算期初和结余*/
//        calculateBeforeQty(allDeptPlanList, skuId, deptSaIdsMap, exeDate);
//
//        /*创建或修改日需求计划明细*/
//        createOrModify(allDeptPlanList, skuId, deptSaIdsMap.keySet(), exeDate);
//
//        if ("local".equals(System.getProperty("env"))) {
//            throw new RuntimeException("本地环境测试需要回滚数据");
//        }
//    }

    /**
     * 创建或修改
     *
     * @param allDeptPlanList 计划列表
     * @param skuId           商品ID
     * @param deptIds         部门ID列表
     * @param exeDate         执行日期
     */
    public void createOrModify(List<SgCDepartmentDemandPlan> allDeptPlanList, Long skuId, Set<Long> deptIds, Date exeDate) {
        List<SgCDepartmentDemandPlan> createList = new ArrayList<>();
        List<SgCDepartmentDemandPlan> modifyList = new ArrayList<>();

        User user = R3SystemUserResource.getSystemRootUser();
        for (SgCDepartmentDemandPlan plan : allDeptPlanList) {
            if (Objects.isNull(plan.getId())) {
                plan.setId(ModelUtil.getSequence("SG_C_DEPARTMENT_DEMAND_PLAN"));
                StorageUtils.setBModelDefalutData(plan, user);
                createList.add(plan);
                continue;
            }

            StorageUtils.setBModelDefalutDataByUpdate(plan, user);
            modifyList.add(plan);
        }

        if (CollectionUtils.isNotEmpty(modifyList)) {
            batchModifyDemandPlan(modifyList, skuId, deptIds, exeDate);
        }

        if (CollectionUtils.isNotEmpty(createList)) {
            StorageUtils.batchInsertTeus(createList, "部门日需求计划明细",
                    SgConstants.SG_COMMON_ITEM_INSERT_PAGE_SIZE, sgCDepartmentDemandPlanMapper);
        }
    }

    /**
     * 批量修改
     *
     * @param modifyList 修改明细
     * @param skuId      商品ID
     * @param deptIds    部门ID列表
     * @param exeDate    执行时间
     */
    private void batchModifyDemandPlan(List<SgCDepartmentDemandPlan> modifyList, Long skuId, Set<Long> deptIds, Date exeDate) {
        List<SgCDepartmentDemandPlan> demandPlanList = sgCDepartmentDemandPlanMapper.selectList(new LambdaQueryWrapper<SgCDepartmentDemandPlan>()
                .eq(SgCDepartmentDemandPlan::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCDepartmentDemandPlan::getPlanMonth, DateFormatUtils.format(exeDate, "yyyyMM"))
                .in(SgCDepartmentDemandPlan::getCpCDistributionOrgId, deptIds)
                .eq(SgCDepartmentDemandPlan::getPsCSkuId, skuId));
        if (CollectionUtils.isEmpty(demandPlanList) || modifyList.size() != demandPlanList.size()) {
            log.debug(LogUtil.format("获取到明细信息与计算到的明细数量信息不匹配，查询到列表:{},修改列表：{},商品ID：{},部门列表：{},执行时间：{}", LOG_OBJ + "batchModifyDemandPlan"),
                    JSON.toJSONString(demandPlanList), JSON.toJSONString(modifyList), skuId, deptIds, exeDate);
            throw new NDSException("获取到明细信息与计算到的明细数量信息不匹配");
        }

        Map<Long, SgCDepartmentDemandPlan> idMap = demandPlanList.stream()
                .collect(Collectors.toMap(SgCDepartmentDemandPlan::getId, Function.identity()));

        int count = 0;
        User loginUser = SystemUserResource.getRootUser();
        /*未测试未使用*/
//        List<SgCDepartmentDemandPlan> needModifyList = new ArrayList<>();
        for (SgCDepartmentDemandPlan plan : modifyList) {
            SgCDepartmentDemandPlan exist = idMap.get(plan.getId());
            if (Objects.isNull(exist)) {
                log.debug(LogUtil.format("获取到明细信息与计算到的明细信息不匹配，查询到列表:{},修改列表：{},商品ID：{},部门列表：{},执行日期：{}", LOG_OBJ + "batchModifyDemandPlan"),
                        JSON.toJSONString(demandPlanList), JSON.toJSONString(modifyList), skuId, deptIds, exeDate);
                throw new NDSException("获取到明细信息与计算到的明细信息不匹配");
            }

            boolean isEquals = exist.getDemandQty().compareTo(plan.getDemandQty()) == 0
                    && exist.getRemainQty().compareTo(plan.getRemainQty()) == 0
                    && exist.getPlanQty().compareTo(plan.getPlanQty()) == 0
                    && exist.getActualQty().compareTo(plan.getActualQty()) == 0
                    && exist.getSharePreOutQty().compareTo(plan.getSharePreOutQty()) == 0
                    && exist.getSaleQty().compareTo(plan.getSaleQty()) == 0
                    && exist.getBeforeRemainQty().compareTo(plan.getBeforeRemainQty()) == 0;

            if (!isEquals) {
                plan.setModifierid(Long.valueOf(loginUser.getId()));
                plan.setModifieddate(new Date());
                plan.setModifiername(loginUser.getName());
                plan.setModifierename(loginUser.getEname());
                count += sgCDepartmentDemandPlanMapper.updateById(plan);
                /*未测试未使用*/
//                needModifyList.add(plan);
            }
        }

        log.debug(LogUtil.format("更新部门日需求明细，变更条数/总条数：{}/{},商品ID：{},部门列表：{},执行日期：{}", LOG_OBJ + "batchModifyDemandPlan"),
                count, modifyList.size(), skuId, deptIds, exeDate);

        /*未测试未使用，这里还可以对list进行拆分，防止一条sql更新太多*/
//        int count = 0;
//        if (!CollectionUtils.isEmpty(needModifyList)) {
//            int count = 0;
//            try {
//                count = sgCDepartmentDemandPlanMapper.batchUpdate(needModifyList);
//            } catch (Exception e) {
//                log.error(LogUtil.format("更新部门日需求明细失败，更新内容：{}，错误信息：{}", LOG_OBJ + "batchModifyDemandPlan"),
//                        JSON.toJSONString(needModifyList), Throwables.getStackTraceAsString(e));
//                throw new NDSException("更新部门日需求明细失败");
//            }
//            if (count != needModifyList.size()) {
//                log.error(LogUtil.format("更新部门日需求明细失败，影响行数错误，更新内容：{}，影响行数：{}", LOG_OBJ + "batchModifyDemandPlan"),
//                        JSON.toJSONString(needModifyList), count);
//                throw new NDSException("更新部门日需求明细出错");
//            }
//        }
    }

//    /**
//     * 计算期初和剩余
//     *
//     * @param allDeptPlanList 日计划明细
//     * @param skuId           商品ID
//     * @param deptSaIdsMap    部门ID->配销仓ID列表 映射
//     * @param exeDate         执行日期
//     */
//    private void calculateBeforeQty(List<SgCDepartmentDemandPlan> allDeptPlanList, Long skuId, Map<Long, Set<Long>> deptSaIdsMap, Date exeDate) {
//        /*部门ID->配销仓ID列表*/
//        Map<Long, List<SgCDepartmentDemandPlan>> deptPlanList = allDeptPlanList.stream()
//                .collect(Collectors.groupingBy(SgCDepartmentDemandPlan::getCStoreattrib2Id));
//        /*逐个部门计算*/
//        for (Map.Entry<Long, List<SgCDepartmentDemandPlan>> entry : deptPlanList.entrySet()) {
//            /*按天排序，按天计算*/
//            List<SgCDepartmentDemandPlan> planList = entry.getValue().stream()
//                    .sorted(Comparator.comparing(SgCDepartmentDemandPlan::getPlanDate)).collect(Collectors.toList());
//            for (int i = 0; i < planList.size(); i++) {
//                SgCDepartmentDemandPlan plan = planList.get(i);
//                if (i == 0) {
//                    if (Objects.isNull(plan.getId())) {
//                        /*如果第一天不存在结余，取当前配销仓可用库存*/
//                        BigDecimal firstRemainQty = querySaStorage(skuId, deptSaIdsMap.get(entry.getKey()));
//                        plan.setRemainQty(firstRemainQty);
//                    }
//                } else {
//                    /*前一天的剩余等于后一天的结余*/
//                    plan.setRemainQty(planList.get(i - 1).getBeforeRemainQty());
//                }
//                if (plan.getPlanDate().after(exeDate)) {
//                    /*今天之后的数据（没有实际入库）：期初库存+生产计划量-销售出库量*/
//                    plan.setBeforeRemainQty(plan.getRemainQty().add(plan.getPlanQty()).subtract(plan.getSaleQty()));
//                } else {
//                    /*今天和今天前的数据（取实际入库）：期初库存+实际入库量-销售出库量*/
//                    plan.setBeforeRemainQty(plan.getRemainQty().add(plan.getActualQty()).subtract(plan.getSaleQty()));
//                }
//            }
//        }
//    }

//    /**
//     * 获取部门下所有配销仓可用库存总和
//     *
//     * @param skuId      商品ID
//     * @param saStoreIds 配销仓ID列表
//     * @return 可用库存总和
//     */
//    private BigDecimal querySaStorage(Long skuId, Set<Long> saStoreIds) {
//        List<SgBSaStorage> storageList = sgS2SaAutoDistributionManager.querySaStorage(skuId, saStoreIds);
//        if (CollectionUtils.isEmpty(storageList)) {
//            log.debug(LogUtil.format("获取部门下所有配销仓可用库存列表为空，商品ID：{},配销仓ID列表：{}", LOG_OBJ + "querySaStorage"),
//                    skuId, saStoreIds);
//            return BigDecimal.ZERO;
//        }
//        return storageList.stream().map(SgBSaStorage::getQtyAvailable).filter(Objects::nonNull)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//    }


//    /**
//     * 计算计划入库量
//     *
//     * @param shareStoreId    聚合仓ID
//     * @param skuId           商品ID
//     * @param exeDate         运行日期
//     * @param allDeptPlanList 所有部门日需求计划明细
//     */
//    private void calculatePlanQty(Long shareStoreId, Long skuId, Date exeDate, List<SgCDepartmentDemandPlan> allDeptPlanList) {
//        /*按日期排序*/
//        List<Date> dateList = allDeptPlanList.stream().map(SgCDepartmentDemandPlan::getPlanDate)
//                .distinct().sorted(Date::compareTo).collect(Collectors.toList());
//        /*按日期分组*/
//        Map<Date, List<SgCDepartmentDemandPlan>> datePlanListMap = allDeptPlanList.stream().collect(Collectors.groupingBy(SgCDepartmentDemandPlan::getPlanDate));
//        /*聚合仓月计划生产量*/
//        BigDecimal remainQty = sgCMonthProductionPlanService.queryProductPlan(shareStoreId, skuId, DateFormatUtils.format(exeDate, "yyyyMM"));
//        for (Date date : dateList) {
//            List<SgCDepartmentDemandPlan> planList = datePlanListMap.get(date);
//            /*剩余量为0，所有都是0*/
//            if (remainQty.compareTo(BigDecimal.ZERO) <= 0) {
//                for (SgCDepartmentDemandPlan plan : planList) {
//                    plan.setPlanQty(BigDecimal.ZERO);
//                }
//                break;
//            }
//
//            BigDecimal todayDemandQty = planList.stream().map(SgCDepartmentDemandPlan::getDemandQty).reduce(BigDecimal.ZERO, BigDecimal::add);
//            /*够分*/
//            if (remainQty.subtract(todayDemandQty).compareTo(BigDecimal.ZERO) >= 0) {
//                for (SgCDepartmentDemandPlan plan : planList) {
//                    plan.setPlanQty(plan.getDemandQty());
//                }
//                remainQty = remainQty.subtract(todayDemandQty);
//                continue;
//            }
//
//            BigDecimal available2Distribute = remainQty;
//            /*不够分*/
//            for (int i = 0; i < planList.size(); i++) {
//                SgCDepartmentDemandPlan plan = planList.get(i);
//                BigDecimal calculateRet;
//                /*分配到最后一个，剩下的量全给它*/
//                if (i == planList.size() - 1) {
//                    calculateRet = remainQty;
//                } else {
//                    /*按比计算：分配量 = （可分配的量 × 当前需求量）÷ 当天总需求量 */
//                    calculateRet = available2Distribute.multiply(plan.getDemandQty())
//                            .divide(todayDemandQty, RoundingMode.HALF_UP).setScale(0, RoundingMode.HALF_UP);
//                }
//                plan.setPlanQty(calculateRet);
//                remainQty = remainQty.subtract(calculateRet);
//            }
//            remainQty = BigDecimal.ZERO;
//        }
//    }

//    /**
//     * 逐个部门处理
//     *
//     * @param skuId      商品ID
//     * @param exeDate    执行日期
//     * @param deptId     部门ID
//     * @param saStoreIds 配销仓ID列表
//     * @return 部门下所有日需求计划明细
//     */
//    private List<SgCDepartmentDemandPlan> executeBySkuDept(Long skuId, Date exeDate, Long deptId, Set<Long> saStoreIds) {
//        /*根据分货单获取当天实际入库量*/
//        BigDecimal actualQty = queryAllocationQty(skuId, saStoreIds, exeDate);
//        /*根据配销占用单获取当月的实际出库量*/
//        Map<Date, BigDecimal> saleDateQtyMap = queryShareOut(skuId, saStoreIds, exeDate);
//        /*日期->需求量 映射*/
//        Map<Date, BigDecimal> dateDemandQtyMap = sgCDepartmentMonthDemandService.queryDateDemandQtyMap(skuId, deptId, DateFormatUtils.format(exeDate, "yyyyMM"));
//
//        /*创建或查询某个部门某个商品的日需求计划明细-当天和当天之后（月结前）*/
//        List<SgCDepartmentDemandPlan> demandPlanList = createIfAbsentDemandPlanList(skuId, deptId, exeDate);
//        /*按日期聚合*/
//        Map<Date, SgCDepartmentDemandPlan> datePlanMap = demandPlanList.stream()
//                .collect(Collectors.toMap(SgCDepartmentDemandPlan::getPlanDate, Function.identity(), (a, b) -> a));
//        /*设置：当天的实际入库量、之前的销售出库量*/
//        for (Date planDate : datePlanMap.keySet()) {
//            SgCDepartmentDemandPlan plan = datePlanMap.get(planDate);
//            if (DateUtils.isSameDay(planDate, exeDate)) {
//                plan.setActualQty(actualQty);
//            }
//
//            plan.setSaleQty(saleDateQtyMap.getOrDefault(planDate, BigDecimal.ZERO));
//            plan.setDemandQty(dateDemandQtyMap.getOrDefault(planDate, BigDecimal.ZERO));
//        }
//        return demandPlanList;
//    }

    /**
     * 使用ADB查询某天的实际入库量(分货单-分货退货单)
     *
     * @param skuId      商品ID
     * @param saStoreIds 配销仓ID列表
     * @param exeDate    日期
     * @return 入库量
     */
    public BigDecimal queryAllocationQty(Long skuId, Set<Long> saStoreIds, Date exeDate) {
        SgCDepartmentDemandPlanService bean = ApplicationContextHandle.getBean(SgCDepartmentDemandPlanService.class);
        Future<BigDecimal> future = bean.queryAllocationQtyByAdb(skuId, saStoreIds, exeDate);
        try {
            return future.get();
        } catch (Exception e) {
            log.error("使用ADB查询某天的实际入库量异常，skuId：{},saStoreIds：{},exeDate：{},异常信息：{}",
                    skuId, saStoreIds, exeDate, Throwables.getStackTraceAsString(e));
            throw new NDSException("使用ADB查询某天的实际入库量异常");
        }
    }

    /**
     * 异步-使用ADB查询某天的实际入库量(分货单-分货退货单)
     *
     * @param skuId      商品ID
     * @param saStoreIds 配销仓ID列表
     * @param exeDate    日期
     * @return 入库量
     */
    @TargetDataSource(name = "adb")
    @Async(value = "adbAsyncExecutorPool")
    public Future<BigDecimal> queryAllocationQtyByAdb(Long skuId, Set<Long> saStoreIds, Date exeDate) {
        BigDecimal allocationQty = Optional.ofNullable(sgCDepartmentDemandPlanMapper.selectAllocationQtyByAdb(sgAdbPrefix, skuId, saStoreIds, exeDate))
                .orElse(BigDecimal.ZERO);
        BigDecimal returnQty = Optional.ofNullable(sgCDepartmentDemandPlanMapper.selectAllocationReturnQtyByAdb(sgAdbPrefix, skuId, saStoreIds, exeDate))
                .orElse(BigDecimal.ZERO);

        BigDecimal actualQty = allocationQty.subtract(returnQty);
        log.debug(LogUtil.format("使用ADB查询某天的实际入库量(分货单)结果为空，商品ID：{},配销仓ID列表：{},执行日期：{},分货总量：{},分货退总量：{},返回结果：{}",
                LOG_OBJ + "queryAllocationQtyByAdb"), skuId, saStoreIds, exeDate, allocationQty, returnQty, actualQty);
        return AsyncResult.forValue(actualQty);
    }

    /**
     * 使用ADB查询某月的实际出库量、占用量(配销占用单)
     *
     * @param skuId      商品ID
     * @param saStoreIds 配销仓ID列表
     * @param month      月份
     * @return 日期->出库量 映射
     */
    public Map<Date, Map<Integer, BigDecimal>> queryShareOut(Long skuId, Set<Long> saStoreIds, Date month) {
        SgCDepartmentDemandPlanService planService = ApplicationContextHandle.getBean(SgCDepartmentDemandPlanService.class);
        Future<Map<Date, Map<Integer, BigDecimal>>> future = planService.queryShareOutByAdb(skuId, saStoreIds, month);
        try {
            return future.get();
        } catch (Exception e) {
            log.error("使用ADB查询某月的实际出库量与占用量异常，skuId：{},saStoreIds：{},exeDate：{},异常信息：{}",
                    skuId, saStoreIds, month, Throwables.getStackTraceAsString(e));
            throw new NDSException("使用ADB查询某月的实际出库量与占用量异常");
        }
    }

    /**
     * 异步使用ADB查询某月的实际出库量、占用量(配销占用单)
     *
     * @param skuId      商品ID
     * @param saStoreIds 配销仓ID列表
     * @param month      月份
     * @return 日期->出库量 映射
     */
    @TargetDataSource(name = "adb")
    @Async(value = "adbAsyncExecutorPool")
    public Future<Map<Date, Map<Integer, BigDecimal>>> queryShareOutByAdb(Long skuId, Set<Long> saStoreIds, Date month) {
        List<SgDeptDemandPlanReportQtyResult> retList = sgCDepartmentDemandPlanMapper.selectShareOutByAdb(sgAdbPrefix, skuId, saStoreIds, month);
        if (CollectionUtils.isEmpty(retList)) {
            log.debug(LogUtil.format("使用ADB查询某月的实际出库量与占用量(配销占用单)结果为空，商品ID：{},配销仓ID列表：{},执行月份：{}",
                    LOG_OBJ + "queryShareOutByAdb"), skuId, saStoreIds, month);
            return AsyncResult.forValue(Collections.emptyMap());
        }

        Map<Date, Map<Integer, BigDecimal>> retMap = retList.stream()
                .collect(Collectors.groupingBy(SgDeptDemandPlanReportQtyResult::getDate,
                        Collectors.toMap(SgDeptDemandPlanReportQtyResult::getStatus, SgDeptDemandPlanReportQtyResult::getQty)));
        return AsyncResult.forValue(retMap);
    }

    /**
     * 创建或查询某个部门某个商品的日需求计划明细-当天和当天之后（月结前）
     *
     * @param skuId   商品ID
     * @param deptId  部门ID
     * @param exeDate 执行日期
     * @return 日需求计划明细
     */
    public List<SgCDepartmentDemandPlan> createIfAbsentDemandPlanList(Long skuId, Long deptId, Date exeDate) {
        List<SgCDepartmentDemandPlan> demandPlanList = sgCDepartmentDemandPlanMapper.selectList(new LambdaQueryWrapper<SgCDepartmentDemandPlan>()
                .eq(SgCDepartmentDemandPlan::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCDepartmentDemandPlan::getPlanMonth, DateFormatUtils.format(exeDate, "yyyyMM"))
                .eq(SgCDepartmentDemandPlan::getCpCDistributionOrgId, deptId)
                .eq(SgCDepartmentDemandPlan::getPsCSkuId, skuId)
                .orderByAsc(SgCDepartmentDemandPlan::getPlanDate));
        if (CollectionUtils.isEmpty(demandPlanList)) {
            log.debug(LogUtil.format("查询日需求计划明细结果为空，需要创建，商品ID：{},部门ID：{},执行日期：{}", LOG_OBJ + "createIfAbsentDemandPlanList"),
                    skuId, deptId, exeDate);
            return createDemandPlan(deptId, skuId, exeDate);
        }
        return demandPlanList;
    }

    /**
     * 创建日需求计划明细-当天和当天之后（月结前）
     *
     * @param deptId  部门ID
     * @param skuId   商品ID
     * @param exeDate 执行日期
     * @return 日需求计划明细
     */
    private List<SgCDepartmentDemandPlan> createDemandPlan(Long deptId, Long skuId, Date exeDate) {
        List<SgCDepartmentDemandPlan> planList = new ArrayList<>();
        String month = DateFormatUtils.format(exeDate, "yyyyMM");

        /*查询商品信息*/
        PsCProSkuResult skuInfo = SgCacheValUtils.querySkuById(skuId);
        PsCPro proInfo = SgCacheValUtils.queryProById(skuInfo.getPsCProId());

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(exeDate);
        int remainDays = calendar.getActualMaximum(Calendar.DAY_OF_MONTH) - calendar.get(Calendar.DAY_OF_MONTH);

        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date date = calendar.getTime();
        for (int i = 0; i <= remainDays; i++) {
            SgCDepartmentDemandPlan plan = new SgCDepartmentDemandPlan();
            /*部门ID*/
            plan.setCpCDistributionOrgId(deptId);

            /*商品信息*/
            plan.setPsCSkuId(skuId);
            plan.setPsCSkuEcode(skuInfo.getSkuEcode());
            plan.setPsCProId(skuInfo.getPsCProId());
            plan.setPsCProEcode(skuInfo.getPsCProEcode());
            plan.setPsCProEname(skuInfo.getPsCProEname());

            /*商品属性信息*/
            plan.setMDim4Id(proInfo.getMDim4Id());
            plan.setMDim6Id(proInfo.getMDim6Id());

            /*时间*/
            plan.setPlanMonth(month);
            plan.setPlanDate(DateUtils.addDays(date, i));

            /*数量与状态（状态暂时没用到）*/
            plan.setDemandQty(BigDecimal.ZERO);
            plan.setRemainQty(BigDecimal.ZERO);
            plan.setPlanQty(BigDecimal.ZERO);
            plan.setActualQty(BigDecimal.ZERO);
            plan.setSaleQty(BigDecimal.ZERO);
            plan.setBeforeRemainQty(BigDecimal.ZERO);
            plan.setPlanStatus(0);

            /*基础信息*/
            plan.setIssystem(SgConstants.IS_ACTIVE_N);
            plan.setIsactive(SgConstants.IS_ACTIVE_Y);

            planList.add(plan);
        }
        return planList;
    }

}
