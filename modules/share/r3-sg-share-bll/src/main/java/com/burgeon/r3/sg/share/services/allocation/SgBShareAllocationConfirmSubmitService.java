package com.burgeon.r3.sg.share.services.allocation;

import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Auther: chenhao
 * @Date: 2022-02-17 10:48
 * @Description: 分货确认单 审核
 */

@Slf4j
@Component
public class SgBShareAllocationConfirmSubmitService {

    /**
     * 审核入口
     *
     * @param session 页面参数
     * @return 返回参数
     */
    ValueHolder confirmSubmitShareAllocation(QuerySession session) {

        log.info("SgBShareAllocationConfirmSubmitService.confirmSubmitShareAllocation Start to submit the confirmation of delivery confirmation");

        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        SgBShareAllocationSubmitService service = ApplicationContextHandle.getBean(SgBShareAllocationSubmitService.class);
        return R3ParamUtils.convertV14WithResult(service.submitShareAllocation(request, Boolean.FALSE, Boolean.TRUE));
    }

}
