package com.burgeon.r3.sg.share.validate;

import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.share.model.dto.SgBShareVipReplenishDto;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/05 17:28
 */
@Component
public class SgBShareVipReplenishSaveValidate extends BaseSingleValidator<SgBShareVipReplenishDto> {

    @Override
    public String getValidatorMsgName() {
        return "唯品会商品补货信息表新增保存";
    }

    @Override
    public Class getValidatorClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 validateMainTable(SgBShareVipReplenishDto mainObject, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        if (Optional.ofNullable(mainObject.getQtyNeedReplenish()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) < 0) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("需补货数量不能小于0!", loginUser.getLocale()));
        }
        if (Objects.nonNull(mainObject.getCpCShopId())) {
            CpShop shopInfo = CommonCacheValUtils.getShopInfo(mainObject.getCpCShopId());
            if(Objects.isNull(shopInfo)){
                  return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("当前店铺信息已不存在",
                          loginUser.getLocale()));
            }
        }
        return vh;
    }

}
