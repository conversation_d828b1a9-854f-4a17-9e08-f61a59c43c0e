package com.burgeon.r3.sg.share.services.ryytndistribution;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.request.SgStoragePageQueryRequest;
import com.burgeon.r3.sg.basic.model.request.SgStoragePageRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageQueryRequest;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySsExtResult;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.SgDistributionTypeEnum;
import com.burgeon.r3.sg.core.model.request.SgR3BaseBillItemBase;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorage;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCPlanConvertVersion;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.DistributionDingTalkTableRobot;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationBillSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationSaveItemRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationSaveRequst;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationSaveService;
import com.burgeon.r3.sg.share.services.allocation.SgBShareAllocationSubmitService;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 百分百自动分货服务
 *
 * <AUTHOR>
 * @since 2022-10-31 15:59
 */
@Slf4j
@Component
public class SgS2SaAllAutoDistributionService {
    /**
     * 日志OBJ
     */
    private static final String LOG_OBJ = "SgS2SaAllAutoDistributionService.";
    /*库存查询*/
    @Autowired
    private SgStorageQueryService sgStorageQueryService;

//    /*聚合参库存查询*/
//    @Autowired
//    private SgBShareStorageQueryService sgShareStorageQueryService;

    /*分货单保存*/
    @Autowired
    private SgBShareAllocationSaveService shareAllocationSaveService;

    @Resource
    private SgS2SaAutoDistributionManager sgS2SaAutoDistributionManager;

    /*二级部门需求*/
    @Resource
    private SgCDepartmentMonthDemandService sgCDepartmentMonthDemandService;


    /**
     * 百分百自动分货
     * 所有当月不存在不在二级部门需求表中的数据，都默认走百分百自动分货
     * 从聚合仓分配到聚合仓对应的配销仓
     *
     * @return 分货结果
     */
    public ValueHolderV14 execute(SgCPlanConvertVersion version) {
        /*所有聚合仓*/
        List<SgCShareStore> sgShareStores = sgS2SaAutoDistributionManager.querySgShareStores().stream()
                .filter(store -> Objects.nonNull(store.getAutoType())
                        && SgConstantsIF.AUTO_TYPR_SA.equals(store.getAutoType())
                        && Objects.nonNull(store.getSgCSaStoreId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sgShareStores)) {
            return new ValueHolderV14(ResultCode.SUCCESS, "百分百自动分货执行成功");
        }

        //聚合仓id集合
        List<Long> shareStoreIds = sgShareStores.stream().map(SgCShareStore::getId).distinct().collect(Collectors.toList());
        //根据聚合仓查询逻辑仓信息
        Map<Long, List<SgCpCStore>> storeList = CommonCacheValUtils.getStoreList(shareStoreIds);
        // 查找聚合仓下所有的条码
        Map<Long, List<Long>> skuBySsMap = querySkuByStore(storeList);

        /*查找当月提报了需求的SKU*/
        Set<Long> excludeSkus = sgCDepartmentMonthDemandService.querySkuByVersionBi(version.getVersionBi());
        return autoBySa(sgShareStores, skuBySsMap, excludeSkus);
    }


    /**
     * 分货-生成已审核的分货单
     *
     * @param sgShareStoresBySa 聚合仓列表
     * @param skuBySsMap        聚合仓下所有的条码集合,聚合仓ID->SKUID列表
     * @param excludeSkus       不走百分百分货的SKU列表
     */
    private ValueHolderV14 autoBySa(List<SgCShareStore> sgShareStoresBySa, Map<Long, List<Long>> skuBySsMap, Set<Long> excludeSkus) {
        int successSkuCnt = 0;
        int totalSkuCount = 0;
        DistributionDingTalkTableRobot robot = new DistributionDingTalkTableRobot(SgDistributionTypeEnum.ALL);

        //全部需要新增分货单
        List<SgBShareAllocationBillSaveRequst> saveRequsts = new ArrayList<>();
        /*逐个聚合仓处理*/
        for (SgCShareStore shareStore : sgShareStoresBySa) {
            // 报错不停止
            try {
                if (!skuBySsMap.containsKey(shareStore.getId())) {
                    continue;
                }
                //获取聚合仓对应条码(过滤掉不走百分百分货的SKU)
                List<Long> skuIds = ListUtils.emptyIfNull(skuBySsMap.get(shareStore.getId())).stream()
                        .filter(skuId -> !excludeSkus.contains(skuId)).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(skuIds)) {
                    continue;
                }
                /*查询聚合仓可用库存-走redis*/
                List<SgStorageRedisQuerySsExtResult> skuStorageRedisRetList = sgS2SaAutoDistributionManager.queryShareStorageAvailableWithRedis(shareStore.getId(), skuIds);
                List<SgStorageRedisQuerySsExtResult> filterStorageRetList = skuStorageRedisRetList.stream()
                        .filter(ret -> BigDecimal.ZERO.compareTo(ret.getQtySsAvailable()) < 0)
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(filterStorageRetList)) {
                    /*构建分货单参数*/
                    saveRequsts.addAll(buildSgBShareAllocationParam(shareStore, filterStorageRetList));
                    /*需要分货的SKU数*/
                    totalSkuCount += filterStorageRetList.size();
                }
            } catch (Exception e) {
                log.error(LogUtil.format("百分百自动分货构建参数异常：error:{}", LOG_OBJ + "autoBySa"), Throwables.getStackTraceAsString(e));
            }
        }
        if (CollectionUtils.isEmpty(saveRequsts)) {
            return new ValueHolderV14(ResultCode.SUCCESS, "百分百自动分货执行成功,总SKU数：" + totalSkuCount);
        }

        for (SgBShareAllocationBillSaveRequst saveRequst : saveRequsts) {
            SgS2SaAllAutoDistributionService bean = ApplicationContextHandle.getBean(SgS2SaAllAutoDistributionService.class);
            try {
                //报错不停，继续下一个
                bean.insertSgShareAllocation(saveRequst);
                long count = saveRequst.getSgBShareAllocationSaveItemRequsts().stream()
                        .map(SgR3BaseBillItemBase::getPsCSkuId)
                        .distinct().count();
                /*分货成功SKU数*/
                successSkuCnt += count;
            } catch (Exception e) {
                log.error(LogUtil.format("百分百自动分货分货单操作异常：error:{}", LOG_OBJ + "autoBySa"), Throwables.getStackTraceAsString(e));
                /*添加到告警信息*/
                addErrorMsg(robot, saveRequst, e);
            }
        }
        /*发送钉钉告警*/
        robot.sendTableMsg();

        return new ValueHolderV14(ResultCode.SUCCESS, "百分百自动分货执行成功,成功SKU数：" + successSkuCnt + "/" + totalSkuCount);
    }

    private void addErrorMsg(DistributionDingTalkTableRobot robot, SgBShareAllocationBillSaveRequst saveRequst, Exception e) {
        Long shareId = saveRequst.getSgBShareAllocationSaveRequst().getSgCShareStoreId();
        for (SgBShareAllocationSaveItemRequst itemRequst : saveRequst.getSgBShareAllocationSaveItemRequsts()) {
            robot.addErrorSku(itemRequst.getPsCSkuId(), shareId, e.getMessage());
        }
    }


    /**
     * 新增并审核 分货单
     *
     * @param saveRequst 分货单参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertSgShareAllocation(SgBShareAllocationBillSaveRequst saveRequst) {
        /*分货单保存*/
        ValueHolderV14<SgR3BaseResult> save = shareAllocationSaveService.save(saveRequst);
        if (!save.isOK()) {
            return;
        }

        SgR3BaseResult data = save.getData();
        JSONObject dataJo = data.getDataJo();
        Long objid = dataJo.getLong("objid");
        SgR3BaseRequest request = new SgR3BaseRequest();
        request.setObjId(objid);
        request.setLoginUser(saveRequst.getLoginUser());

        /*分货单提交*/
        SgBShareAllocationSubmitService bean = ApplicationContextHandle.getBean(SgBShareAllocationSubmitService.class);
        bean.submitShareAllocation(request, false, false);

//        if ("local".equals(System.getProperty("env"))) {
//            throw new RuntimeException("本地环境测试需要回滚数据");
//        }
    }


    /**
     * 构建分货单参数
     *
     * @param shareStore 聚合仓信息
     * @param data       集合仓库存明细
     * @return 分货单 新增集合
     */
    private List<SgBShareAllocationBillSaveRequst> buildSgBShareAllocationParam(SgCShareStore shareStore, List<SgStorageRedisQuerySsExtResult> data) {
        List<SgBShareAllocationBillSaveRequst> saveRequsts = new ArrayList<>();
        //批量生成
        List<List<SgStorageRedisQuerySsExtResult>> pageList = StorageUtils.getPageList(data, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);

        Long sgSaStoreId = shareStore.getSgCSaStoreId();
        AssertUtils.cannot(sgSaStoreId == null, "聚合仓 [" + shareStore.getEcode() + "]分货配销仓为空！");

        //获取配销仓
        SgCSaStore saStore = CommonCacheValUtils.getSaStore(sgSaStoreId);
        if (SgConstants.IS_ACTIVE_N.equals(saStore.getIsactive())) {
            log.warn(LogUtil.format("聚合仓 [" + shareStore.getEcode() + "]的百分百分货配销仓为非激活状态，请先激活！", LOG_OBJ + "autoBySa"));
            throw new NDSException("聚合仓[" + shareStore.getEcode() + "]的百分百分货配销仓[" + saStore.getEcode() + "]为非激活状态");
        }

        AssertUtils.cannot(saStore == null,
                "聚合仓 [" + shareStore.getEcode() + "]分货配销仓 【" + saStore + "】对应配销仓档案不存在！");

        for (List<SgStorageRedisQuerySsExtResult> queryResults : pageList) {
            SgBShareAllocationBillSaveRequst requst = new SgBShareAllocationBillSaveRequst();
            //主表参数
            SgBShareAllocationSaveRequst saveRequst = new SgBShareAllocationSaveRequst();
            saveRequst.setBillDate(new Date());
            saveRequst.setSgCShareStoreId(shareStore.getId());
            saveRequst.setRemark("百分百自动分货任务自动生成！");
            //明细参数
            List<SgBShareAllocationSaveItemRequst> saveItemRequsts = new ArrayList<>();
            for (SgStorageRedisQuerySsExtResult queryResult : queryResults) {
                //测试环节，如果条码信息没有，就过
                try {
                    SgBShareAllocationSaveItemRequst itemRequst = new SgBShareAllocationSaveItemRequst();
                    //补全商品信息
                    CommonCacheValUtils.setSkuInfo(queryResult.getPsCSkuId(), null, itemRequst);
                    itemRequst.setQty(queryResult.getQtySsAvailable());
                    itemRequst.setQtyDiff(BigDecimal.ZERO);
                    itemRequst.setQtyIn(BigDecimal.ZERO);
                    itemRequst.setQtyOut(BigDecimal.ZERO);
                    itemRequst.setPsCSkuId(queryResult.getPsCSkuId());
                    itemRequst.setSgCSaStoreId(saStore.getId());
                    itemRequst.setSgCSaStoreEcode(saStore.getEcode());
                    itemRequst.setSgCSaStoreEname(saStore.getEname());
                    saveItemRequsts.add(itemRequst);
                } catch (Exception e) {
                    log.error(LogUtil.format("百分百自动分货条码信息补充异常：error:{},skuid:{}", LOG_OBJ + "buildSgBShareAllocationParam"),
                            Throwables.getStackTraceAsString(e), queryResult.getPsCSkuId());
                }
            }

            requst.setObjId(-1L);
            requst.setLoginUser(R3SystemUserResource.getSystemRootUser());
            requst.setSgBShareAllocationSaveRequst(saveRequst);
            requst.setSgBShareAllocationSaveItemRequsts(saveItemRequsts);
            saveRequsts.add(requst);
        }

        return saveRequsts;
    }


    /**
     * 查找聚合仓下所有的条码
     * 根据逻辑仓档案查找逻辑仓库存 目的是为了拿出所有的条码
     *
     * @param storeList 逻辑仓档案集合 key = 聚合仓id value = 逻辑仓档案集合
     * @return 返回map key = 聚合仓id value = 条码id集合
     */
    private Map<Long, List<Long>> querySkuByStore(Map<Long, List<SgCpCStore>> storeList) {
        // key 聚合仓id value 条码id集合
        Map<Long, List<Long>> retMap = new HashMap<>(16);
        /*逐个聚合仓处理*/
        for (Long shareStoreId : storeList.keySet()) {
            /*逻辑仓列表*/
            List<SgCpCStore> stores = storeList.get(shareStoreId);
            List<Long> cStoreIds = stores.stream().map(SgCpCStore::getId).distinct().collect(Collectors.toList());

            /*逻辑仓库存列表*/
            List<SgBStorage> bStorageList = new ArrayList<>();

            //查询逻辑仓库存 这里是为了拿出所有的条码
            for (Long storeId : cStoreIds) {
                SgStoragePageQueryRequest sgStoragePageQueryRequest = new SgStoragePageQueryRequest();

                SgStorageQueryRequest sgStorageQueryRequest = new SgStorageQueryRequest();
                List<Long> storeIdList = new ArrayList<>();
                storeIdList.add(storeId);
                sgStorageQueryRequest.setStoreIds(storeIdList);
                sgStoragePageQueryRequest.setQueryRequest(sgStorageQueryRequest);

                SgStoragePageRequest sgStoragePageRequest = new SgStoragePageRequest();
                sgStoragePageRequest.setPageNum(1);
                sgStoragePageRequest.setPageSize(SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
                sgStoragePageQueryRequest.setPageRequest(sgStoragePageRequest);

                ValueHolderV14<PageInfo<SgBStorage>> pageInfoValueHolderV14 = sgStorageQueryService.queryStoragePage(sgStoragePageQueryRequest,
                        R3SystemUserResource.getSystemRootUser());
                AssertUtils.cannot(!pageInfoValueHolderV14.isOK(), "根据逻辑仓查逻辑仓库存异常：" + pageInfoValueHolderV14.getMessage());

                PageInfo<SgBStorage> dataInfo = pageInfoValueHolderV14.getData();
                if (dataInfo != null) {
                    if (CollectionUtils.isNotEmpty(dataInfo.getList())) {
                        bStorageList.addAll(dataInfo.getList());
                    }

                    //判断是否还有下一页
                    if (dataInfo.isHasNextPage()) {
                        List<SgBStorage> pageStorage = queryPageStorage(storeIdList, dataInfo);
                        if (CollectionUtils.isNotEmpty(pageStorage)) {
                            bStorageList.addAll(pageStorage);
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(bStorageList)) {
                //取出所有的sku
                List<Long> skuIds = bStorageList.stream().map(SgBStorage::getPsCSkuId).distinct().collect(Collectors.toList());
                retMap.put(shareStoreId, skuIds);
            }
        }
        return retMap;
    }

    /**
     * 分页查询库存
     *
     * @param storeIdList 店仓id
     * @param dataInfo    分页参数
     * @return 库存
     */
    private List<SgBStorage> queryPageStorage(List<Long> storeIdList, PageInfo<SgBStorage> dataInfo) {
        List<SgBStorage> retDataList = new ArrayList<>();

        //获取页面
        int pages = dataInfo.getPages();
        //从第二页开始再查
        for (int i = 2; i <= pages; i++) {
            SgStoragePageQueryRequest sgStoragePageQueryRequest = new SgStoragePageQueryRequest();

            SgStorageQueryRequest sgStorageQueryRequest = new SgStorageQueryRequest();
            sgStorageQueryRequest.setStoreIds(storeIdList);
            sgStoragePageQueryRequest.setQueryRequest(sgStorageQueryRequest);

            SgStoragePageRequest sgStoragePageRequest = new SgStoragePageRequest();
            sgStoragePageRequest.setPageNum(i);
            sgStoragePageRequest.setPageSize(SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
            sgStoragePageQueryRequest.setPageRequest(sgStoragePageRequest);

            ValueHolderV14<PageInfo<SgBStorage>> pageValueHolder
                    = sgStorageQueryService.queryStoragePage(sgStoragePageQueryRequest, R3SystemUserResource.getSystemRootUser());
            AssertUtils.cannot(!pageValueHolder.isOK(), "百分百分货-根据逻辑仓分页查逻辑仓库存异常：" + pageValueHolder.getMessage());

            PageInfo<SgBStorage> pageInfo = pageValueHolder.getData();
            if (pageInfo != null && CollectionUtils.isNotEmpty(pageInfo.getList())) {
                retDataList.addAll(pageInfo.getList());
            }
        }

        return retDataList;
    }

}
