package com.burgeon.r3.sg.share.validate;

import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareSaCrossTransfer;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareSaCrossTransferMapper;
import com.burgeon.r3.sg.share.model.dto.SgBShareSaCrossTransferDto;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/6 20:50
 */
@Component
public class SgBShareSaCrossTransferDeleteValidate extends BaseSingleValidator<SgBShareSaCrossTransferDto> {
    @Autowired
    private SgBShareSaCrossTransferMapper sgShareSaCrossTransferMapper;

    @Override
    public String getValidatorMsgName() {
        return "配销跨聚合仓调拨单删除";
    }

    @Override
    public Class<?> getValidatorClass() {
        return SgBShareSaCrossTransferDeleteValidate.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgBShareSaCrossTransferDto mainObject, User loginUser) {
        SgBShareSaCrossTransfer saCrossTransfer = sgShareSaCrossTransferMapper.selectById(mainObject.getId());
        if (Objects.isNull(saCrossTransfer)) {
            return new ValueHolderV14<>(ResultCode.FAIL,
                    Resources.getMessage("当前记录已不存在,删除失败!", loginUser.getLocale()));
        } else {
            if (saCrossTransfer.getStatus() == SgShareConstants.BILL_STATUS_VOID) {
                return new ValueHolderV14<>(ResultCode.FAIL,
                        Resources.getMessage("当前记录已作废,不允许删除!", loginUser.getLocale()));
            } else if (saCrossTransfer.getStatus() == SgShareConstants.BILL_STATUS_SUBMIT) {
                return new ValueHolderV14<>(ResultCode.FAIL,
                        Resources.getMessage("当前记录已审核,不允许删除!", loginUser.getLocale()));
            } else if (saCrossTransfer.getStatus() == SgShareConstants.SHARE_SA_TRANSFER_FINSH_RETURN) {
                return new ValueHolderV14<>(ResultCode.FAIL,
                        Resources.getMessage("当前记录已完成分货退,不允许删除!", loginUser.getLocale()));
            }
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "删除成功!");
    }

}
