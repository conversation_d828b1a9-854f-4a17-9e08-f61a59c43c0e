//package com.burgeon.r3.sg.share.services.ryytndistribution;
//
//import com.alibaba.fastjson.JSON;
//import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
//import com.burgeon.r3.sg.core.enums.SgDistributionTypeEnum;
//import com.burgeon.r3.sg.core.model.table.basic.SgBSaStorage;
//import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
//import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCDepartmentMonthDemand;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.utility.LogUtil;
//import com.jackrain.nea.web.face.impl.R3SystemUserResource;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.tuple.Pair;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.Set;
//import java.util.stream.Collectors;
//
///**
// * 月初自动分货服务
// * </br>
// * 已弃用，详见{@link }
// *
// * <AUTHOR>
// * @since 2022-09-19 11:40
// */
//@Slf4j
//@Component
//@Deprecated
//public class SgS2SaAutoDistributionMonthTemplate extends SgS2SaAutoDistributionAbstractTemplate {
//    /**
//     * 日志OBJ
//     */
//    private static final String LOG_OBJ = "SgS2SaAutoDistributionMonthTemplate.";
//
//    /**
//     * 自动分货-公共操作
//     */
//    @Resource
//    private SgS2SaAutoDistributionManager sgS2SaAutoDistributionManager;
//
//    @Resource
//    private SgCDepartmentMonthDemandService sgCDepartmentMonthDemandService;
//
//    @Override
//    public SgS2SaAutoDistributionAbstractTemplate getBean() {
//        return ApplicationContextHandle.getBean(SgS2SaAutoDistributionMonthTemplate.class);
//    }
//
//    @Override
//    public Map<Long, BigDecimal> queryStorageMap(List<SgCSaStore> saStores, Long skuId) {
//        if (CollectionUtils.isEmpty(saStores)) {
//            log.warn(LogUtil.format("获取配销仓可用库存时配销仓列表为空，商品ID：{}", LOG_OBJ + "querySaStorageMap"), skuId);
//            return Collections.emptyMap();
//        }
//
//        /*配销仓ID列表*/
//        Set<Long> saStoreIds = saStores.stream().map(SgCSaStore::getId)
//                .filter(Objects::nonNull).collect(Collectors.toSet());
//        List<SgBSaStorage> saStorages = sgS2SaAutoDistributionManager.querySaStorage(skuId, saStoreIds);
//
//        return saStorages.stream()
//                .collect(Collectors.toMap(SgBSaStorage::getSgCSaStoreId, SgBSaStorage::getQtyAvailable, (a, b) -> a));
//    }
//
//    @Override
//    public Map<Long, BigDecimal> calculateSaActualStorage(Map<Long, Set<Long>> deptSaIdsMap, Map<Long, BigDecimal> deptActualMap, Map<Long, BigDecimal> storageBeforeMap) {
//        Map<Long, BigDecimal> saActualMap = new HashMap<>();
//        for (Long deptId : deptActualMap.keySet()) {
//            /*该部门下，本次分配的总量*/
//            BigDecimal deptTotalAfter = deptActualMap.get(deptId);
//
//            /*该部门下，所有的配销仓ID*/
//            Set<Long> saStoreIds = deptSaIdsMap.get(deptId);
//            /*该部门下仅一个配销仓*/
//            if (saStoreIds.size() == 1) {
//                saActualMap.put(saStoreIds.iterator().next(), deptTotalAfter);
//                continue;
//            }
//
//            /*该部门下，所有配销仓分配之前的量*/
//            BigDecimal deptTotalBefore = storageBeforeMap.entrySet().stream()
//                    .filter(entry -> saStoreIds.contains(entry.getKey()))
//                    .map(Map.Entry::getValue)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            if (BigDecimal.ZERO.compareTo(deptTotalBefore) == 0) {
//                log.debug(LogUtil.format("该二级部门下所有配销仓原库存总和为零，按比分配量应为零，二级部门ID：{}，配销仓ID列表：{}", LOG_OBJ + "calculateSaActualStorage"),
//                        deptId, JSON.toJSONString(saStoreIds));
//                continue;
//            }
//
//            BigDecimal remain = deptTotalAfter;
//            for (Long saStoreId : saStoreIds) {
//                if (BigDecimal.ZERO.compareTo(remain) == 0) {
//                    break;
//                }
//                /*配销仓-分配之前的量*/
//                BigDecimal before = storageBeforeMap.get(saStoreId);
//
//                /*配销仓-实际分配的量 = (分配之前的量 × 本次分配的总量) ÷ 分配前的总量*/
//                BigDecimal actual = before.multiply(deptTotalAfter)
//                        .divide(deptTotalBefore, RoundingMode.HALF_UP).setScale(0, RoundingMode.HALF_UP);
//
//                if (remain.compareTo(actual) < 0) {
//                    actual = remain;
//                }
//                remain = remain.subtract(actual);
//
//                saActualMap.put(saStoreId, actual);
//            }
//        }
//        return saActualMap;
//    }
//
//    @Override
//    public Map<Long, BigDecimal> doDistribute(Long shareStoreId, Long skuId,
//                                              Map<Long, BigDecimal> saActualMap, Map<Long, BigDecimal> storageBeforeMap) {
//        /*分货*/
//        List<Pair<Long, BigDecimal>> saStoragePairList = new ArrayList<>();
//        /*分货退货*/
//        List<Pair<Long, BigDecimal>> saStorageReturnPairList = new ArrayList<>();
//
//        Map<Long, BigDecimal> saIdDemandQtyMap = new HashMap<>();
//
//        for (Long saStoreId : saActualMap.keySet()) {
//            /*分配前*/
//            BigDecimal before = storageBeforeMap.getOrDefault(saStoreId,BigDecimal.ZERO);
//            /*分配后*/
//            BigDecimal after = saActualMap.getOrDefault(saStoreId,BigDecimal.ZERO);
//            /*分货前=分货后，无需操作，满足量=分货前/分货后*/
//            if (before.compareTo(after) == 0) {
//                if (after.compareTo(BigDecimal.ZERO) > 0) {
//                    saIdDemandQtyMap.put(saStoreId, after);
//                }
//                continue;
//            }
//            /*分货前>分货后：需要拉回，拉回数量=分货前-分货后，对应配销满足量=(分货前-拉回数)或（分货后）*/
//            if (before.compareTo(after) > 0) {
//                saStorageReturnPairList.add(Pair.of(saStoreId, before.subtract(after)));
//                if (after.compareTo(BigDecimal.ZERO) > 0) {
//                    saIdDemandQtyMap.put(saStoreId, after);
//                }
//            } else {
//                /*分货前<分货后：需要分配，分配数量=分货后-分货前，对应配销满足量=分货后*/
//                saStoragePairList.add(Pair.of(saStoreId, after.subtract(before)));
//                if (before.compareTo(BigDecimal.ZERO) > 0) {
//                    saIdDemandQtyMap.put(saStoreId, before);
//                }
//            }
//        }
//
//        try {
//            /*分货退货*/
//            log.debug(LogUtil.format(genLogPrefix() + "分货退货，聚合仓ID：{}，商品ID：{}，分货退货映射：{}", LOG_OBJ + "doDistribute"),
//                    shareStoreId, skuId, saStorageReturnPairList);
//            List<String> redisKeyList = sgS2SaAutoDistributionManager.doAllocationReturn(shareStoreId, skuId, saStorageReturnPairList, SgDistributionTypeEnum.MONTH);
//
//            /*分货*/
//            log.debug(LogUtil.format(genLogPrefix() + "分货，聚合仓ID：{}，商品ID：{}，分货映射：{}", LOG_OBJ + "doDistribute"),
//                    shareStoreId, skuId, saStoragePairList);
//            try {
//                sgS2SaAutoDistributionManager.doAllocation(shareStoreId, skuId, saStoragePairList, genLogPrefix());
//            } catch (Exception e) {
//                /*分货失败回滚分货退货*/
//                StorageBasicUtils.rollbackStorage(redisKeyList, R3SystemUserResource.getSystemRootUser());
//                throw e;
//            }
//        } catch (Exception e) {
//            log.error(LogUtil.format(genLogPrefix() + "执行新增分货单/分货退货单并审核出错，错误信息：{}", LOG_OBJ + "doDistribute"),
//                    Throwables.getStackTraceAsString(e));
//            throw e;
//        }
//
//        return saIdDemandQtyMap;
//    }
//
//    @Override
//    protected void modifyDemandList(Long skuId, Map<Long, Set<Long>> deptSaIdsMap, Map<Long, BigDecimal> saIdDemandQtyMap) {
//        if (CollectionUtils.isEmpty(saIdDemandQtyMap)) {
//            log.debug(LogUtil.format(genLogPrefix() + "自动分货，需要更新满足量的计算结果为空，商品ID：{}", LOG_OBJ), skuId);
//            return;
//        }
//
//        /*部门ID->需求量*/
//        Map<Long, BigDecimal> deptQtyMap = new HashMap<>();
//        for (Map.Entry<Long, Set<Long>> entry : deptSaIdsMap.entrySet()) {
//            for (Long saId : entry.getValue()) {
//                BigDecimal saDemandQty = saIdDemandQtyMap.getOrDefault(saId, BigDecimal.ZERO);
//                if (saDemandQty.compareTo(BigDecimal.ZERO) > 0) {
//                    deptQtyMap.put(entry.getKey(), deptQtyMap.getOrDefault(entry.getKey(), BigDecimal.ZERO).add(saDemandQty));
//                }
//            }
//        }
//
//        /*重新查询二级部门需求列表*/
//        List<SgCDepartmentMonthDemand> demandList =
//                sgCDepartmentMonthDemandService.queryByDeptAndSku(deptQtyMap.keySet(), Collections.singleton(skuId));
//        /*获取需要被更新的部门需求，并根据部门ID分组*/
//        Map<Long, List<SgCDepartmentMonthDemand>> deptDemandListMap = demandList.stream()
//                /*.filter(obj -> deptQtyMap.containsKey(obj.getCStoreattrib2Id()))*/
//                .collect(Collectors.groupingBy(SgCDepartmentMonthDemand::getCStoreattrib2Id));
//        if (CollectionUtils.isEmpty(deptDemandListMap)) {
//            log.debug(LogUtil.format(genLogPrefix() + "自动分货，过滤二级部门需求数据为空，商品ID:{}", LOG_OBJ + "modifyDemandList"), skuId);
//            return;
//        }
//
//        List<SgCDepartmentMonthDemand> modifyList = new ArrayList<>();
//        for (Map.Entry<Long, List<SgCDepartmentMonthDemand>> entry : deptDemandListMap.entrySet()) {
//            List<SgCDepartmentMonthDemand> demands =
//                    sgCDepartmentMonthDemandService.distributeDemand(entry.getValue(), deptQtyMap.get(entry.getKey()));
//            if (!CollectionUtils.isEmpty(demands)) {
//                modifyList.addAll(demands);
//            }
//        }
//
//        log.debug(LogUtil.format(genLogPrefix() + "自动分货，更新二级部门需求表，部门->分货量：{},变更列表：{}", LOG_OBJ + "modifyDemandList"),
//                JSON.toJSONString(deptQtyMap), JSON.toJSONString(modifyList));
//        sgCDepartmentMonthDemandService.batchModifyDemand(modifyList, R3SystemUserResource.getSystemRootUser());
//    }
//
//
//    @Override
//    public String genLogPrefix() {
//        return "月度-";
//    }
//
//    @Override
//    public boolean isMonthDistribution() {
//        return true;
//    }
//}
