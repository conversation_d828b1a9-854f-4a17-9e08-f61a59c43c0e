package com.burgeon.r3.sg.share.services.autodistribution;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.model.table.share.autodistribution.SgCOnesetSetting;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.mapper.autodistribution.SgCOnesetSettingMapper;
import com.burgeon.r3.sg.share.model.request.autodistribution.SgCOnesetSettingBillSaveRequest;
import com.burgeon.r3.sg.share.model.request.autodistribution.SgCOnesetSettingSaveRequest;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/9/13 16:42
 */
@Slf4j
@Component
public class SgCOnesetSettingSaveService {

    @Autowired
    private SgCOnesetSettingMapper settingMapper;

    /**
     * 一手码保存（R3前端)
     */
    ValueHolder save(QuerySession session) {
        SgCOnesetSettingBillSaveRequest request = R3ParamUtils.parseSaveObject(session, SgCOnesetSettingBillSaveRequest.class);
        request.setR3(true);
        SgCOnesetSettingSaveService service = ApplicationContextHandle.getBean(SgCOnesetSettingSaveService.class);
        return R3ParamUtils.convertV14WithResult(service.save(request));
    }


    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> save(SgCOnesetSettingBillSaveRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgCOnesetSettingSaveService param {}", JSONObject.toJSONString(request));
        }

        SgCOnesetSetting sgOnesetSetting = checkParams(request);
        if (request.getObjId() == null || request.getObjId() < 0) {
            return insert(sgOnesetSetting);
        } else {
            return update(sgOnesetSetting);
        }
    }

    /**
     * 一手码设置新增
     */
    public ValueHolderV14<SgR3BaseResult> insert(SgCOnesetSetting sgOnesetSetting) {
        settingMapper.insert(sgOnesetSetting);
        //mybatis puls 新增完后 对象自动返回主键id
        Long objId = sgOnesetSetting.getId();
        SgR3BaseResult baseResult = new SgR3BaseResult();
        baseResult.setDataJo(objId, SgConstants.SG_C_ONESET_SETTING.toUpperCase());
        return new ValueHolderV14<>(baseResult, ResultCode.SUCCESS, "保存成功!");
    }

    /**
     * 一手码设置修改
     */
    public ValueHolderV14<SgR3BaseResult> update(SgCOnesetSetting sgOnesetSetting) {
        Long id = sgOnesetSetting.getId();
        String lockKsy = SgConstants.SG_C_ONESET_SETTING + ":" + id;
        SgRedisLockUtils.lock(lockKsy);
        try {
            settingMapper.updateById(sgOnesetSetting);
        } catch (Exception e) {
            AssertUtils.logAndThrowException("一手码保存异常", e);
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "保存成功!");
    }

    private SgCOnesetSetting checkParams(SgCOnesetSettingBillSaveRequest request) {
        Long objId = request.getObjId();
        User loginUser = request.getLoginUser();
        SgCOnesetSettingSaveRequest saveRequest = request.getSgCOnesetSettingSaveRequest();

        Long cShareStoreId = saveRequest.getSgCShareStoreId();
        Long psCSkuId = saveRequest.getPsCSkuId();
        BigDecimal qty = saveRequest.getQty();

        SgCOnesetSetting sgCOnesetSetting = new SgCOnesetSetting();
        BeanUtils.copyProperties(saveRequest, sgCOnesetSetting);
        if (cShareStoreId != null) {
            SgCShareStore shareStore = CommonCacheValUtils.getShareStore(cShareStoreId);
            AssertUtils.notNull(shareStore, "聚合仓信息有误");
            sgCOnesetSetting.setSgCShareStoreId(shareStore.getId());
            sgCOnesetSetting.setSgCShareStoreEcode(shareStore.getEcode());
            sgCOnesetSetting.setSgCShareStoreEname(shareStore.getEname());
        }

        if (objId == null || objId < 0) {
            BasicPsQueryService psQueryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
            SkuInfoQueryRequest skuRequest = new SkuInfoQueryRequest();
            List<Long> skuIds = new ArrayList<>();
            skuIds.add(psCSkuId);
            skuRequest.setSkuIdList(skuIds);
            HashMap<Long, PsCProSkuResult> map = psQueryService.getSkuInfo(skuRequest);
            PsCProSkuResult data = map.get(psCSkuId);
            AssertUtils.notNull(data, "未查询到条码档案");

            sgCOnesetSetting.setPsCSkuId(data.getId());
            sgCOnesetSetting.setPsCSkuEcode(data.getSkuEcode());
            sgCOnesetSetting.setPsCProId(data.getPsCProId());
            sgCOnesetSetting.setPsCProEcode(data.getPsCProEcode());
            sgCOnesetSetting.setPsCProEname(data.getPsCProEname());
            sgCOnesetSetting.setPsCSpec1Id(data.getPsCSpec1objId());
            sgCOnesetSetting.setPsCSpec1Ecode(data.getClrsEcode());
            sgCOnesetSetting.setPsCSpec1Ename(data.getClrsEname());
            sgCOnesetSetting.setPsCSpec2Id(data.getPsCSpec2objId());
            sgCOnesetSetting.setPsCSpec2Ecode(data.getSizesEcode());
            sgCOnesetSetting.setPsCSpec2Ename(data.getSizesEname());
            StorageUtils.setBModelDefalutData(sgCOnesetSetting, loginUser);
        } else {
            SgCOnesetSetting setting = settingMapper.selectById(objId);
            AssertUtils.notNull(setting, "当前记录已不存在");
            psCSkuId = setting.getPsCSkuId();
            sgCOnesetSetting.setId(objId);
            StorageUtils.setBModelDefalutDataByUpdate(sgCOnesetSetting, loginUser);
        }

        Integer count = settingMapper.selectCount(new LambdaQueryWrapper<SgCOnesetSetting>()
                .eq(SgCOnesetSetting::getSgCShareStoreId, cShareStoreId)
                .eq(SgCOnesetSetting::getPsCSkuId, psCSkuId)
                .eq(SgCOnesetSetting::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (count > 0) {
            AssertUtils.logAndThrow("当前记录聚合仓+条码已存在", request.getLoginUser().getLocale());
        }

        return sgCOnesetSetting;
    }
}
