package com.burgeon.r3.sg.share.services.ryytndistribution;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.rpc.RpcCpUserService;
import com.burgeon.r3.sg.basic.services.SgBSaStorageQueryService;
import com.burgeon.r3.sg.basic.utils.DingTalkTokenEnum;
import com.burgeon.r3.sg.basic.utils.DingTalkUtil;
import com.burgeon.r3.sg.basic.utils.EnvEnum;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.DeptSaStorageWarnMsgDTO;
import com.burgeon.r3.sg.core.model.result.SgCDeptWarnSaStorageQtyQueryResult;
import com.burgeon.r3.sg.core.model.table.basic.SgBSaStorage;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCDeptWarnSaStorage;
import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgCDeptWarnSaStorageMapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpCDistributionOrganizationQueryCmd;
import com.jackrain.nea.cpext.api.CpCStoreDimItemQueryCmd;
import com.jackrain.nea.cpext.model.table.CpCDistributionOrganization;
import com.jackrain.nea.cpext.model.table.CpUsers;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 库存-部门配销仓库存告警
 *
 * <AUTHOR>
 * @since 2023-10-08 17:30
 */
@Slf4j
@Service
public class SgCDeptWarnSaStorageService {
    /**
     * 日志OBJ
     */
    private static final String LOG_OBJ = "SgCDeptWarnSaStorageService.";

    @Resource
    private SgS2SaAutoDistributionManager sgS2SaAutoDistributionManager;

    @Resource
    private SgCDeptWarnSaStorageMapper sgCDeptWarnSaStorageMapper;

//    @DubboReference(group = "cp-ext", version = "1.0")
//    private CpGetUsersCmd cpGetUsersCmd;

    @Resource
    private RpcCpUserService rpcCpUserService;

    @DubboReference(group = "cp-ext", version = "1.0")
    private CpCStoreDimItemQueryCmd cpCStoreDimItemQueryCmd;

    @DubboReference(group = "cp-ext", version = "1.0")
    private CpCDistributionOrganizationQueryCmd cpCDistributionOrganizationQueryCmd;

    /**
     * 配销仓库存
     */
    @Resource
    private SgBSaStorageQueryService sgBSaStorageQueryService;

    /**
     * 成品共享配销仓，需要加上这个仓下的库存也不足的时候才告警
     */
    @NacosValue(value = "${sg.department.warn.storage.shared.saStoreId:1}", autoRefreshed = true)
    private Long sharedSaStoreId;

    /**
     * 执行-部门配销仓库存告警
     *
     * @return 执行结果
     */
    public ValueHolderV14<Boolean> executeSaStorageWarn() {
        List<SgCDeptWarnSaStorage> warnList = sgCDeptWarnSaStorageMapper.selectList(new QueryWrapper<SgCDeptWarnSaStorage>().lambda()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isEmpty(warnList)) {
            return new ValueHolderV14<>(Boolean.TRUE, ResultCode.SUCCESS, "没有需要关注的品相");
        }

        List<DeptSaStorageWarnMsgDTO> msgDtoList = new ArrayList<>();

        /*成品共享配销仓-库存*/
        Map<Long, BigDecimal> sharedSaStorage = querySharedSaStorage(sharedSaStoreId);

        /*根据部门ID分组(必须是提报了需求的部门)*/
        Map<Long, List<SgCDeptWarnSaStorage>> deptGroupMap = warnList.stream()
                .collect(Collectors.groupingBy(SgCDeptWarnSaStorage::getCpCDistributionOrgId));
        /*获取部门ID与所属配销仓列表数据映射*/
        Map<Long, Set<Long>> deptSaStoreIdsMap = sgS2SaAutoDistributionManager.queryDeptSaStoreIdsMap(deptGroupMap.keySet());
        /*逐个部门处理*/
        for (Map.Entry<Long, Set<Long>> deptSaStoreEntry : deptSaStoreIdsMap.entrySet()) {
            List<SgCDeptWarnSaStorage> deptWarnList = deptGroupMap.get(deptSaStoreEntry.getKey());
            if (CollectionUtils.isEmpty(deptWarnList)) {
                continue;
            }

            /*根据SKU分组*/
            Map<Long, List<SgCDeptWarnSaStorage>> skuIdDeptWarnsMap = deptWarnList.stream()
                    .collect(Collectors.groupingBy(SgCDeptWarnSaStorage::getPsCSkuId));
            /*商品ID->库存量 映射*/
            Map<Long, BigDecimal> skuQtyMap = querySaStorage(skuIdDeptWarnsMap.keySet(), deptSaStoreEntry.getValue());

            /*逐个SKU处理*/
            for (Map.Entry<Long, BigDecimal> skuQtyEntry : skuQtyMap.entrySet()) {
                /*判断是否需要告警：逐个配销仓库存比对*/
                for (SgCDeptWarnSaStorage saStorage : skuIdDeptWarnsMap.get(skuQtyEntry.getKey())) {
                    /*告警量大于或等于当前量时告警*/
                    BigDecimal availiableQty = skuQtyEntry.getValue().add(sharedSaStorage.getOrDefault(skuQtyEntry.getKey(), BigDecimal.ZERO));
                    if (saStorage.getWarnQty().compareTo(availiableQty) >= 0) {
                        msgDtoList.add(DeptSaStorageWarnMsgDTO.builder()
                                .deptId(saStorage.getCpCDistributionOrgId())
                                .skuId(saStorage.getPsCSkuId())
                                .skuEcode(saStorage.getPsCSkuEcode())
                                .skuEname(saStorage.getPsCProEname())
                                .userId(saStorage.getWarnUserId())
                                .warnQty(saStorage.getWarnQty())
                                .curQty(skuQtyEntry.getValue())
                                .sharedSaStoreQty(sharedSaStorage.getOrDefault(skuQtyEntry.getKey(), null))
                                .build());
                    }
                }
            }
        }
        /*发送消息*/
        sendDingTalkMsg(msgDtoList);
        return new ValueHolderV14<>(null, ResultCode.SUCCESS, "执行成功");
    }

    /**
     * 发送告警消息</br>
     * 群消息限制了每分钟的发送条数，后续需要考虑优化一下
     *
     * @param msgDtoList 消息对象列表
     */
    private void sendDingTalkMsg(List<DeptSaStorageWarnMsgDTO> msgDtoList) {
        if (CollectionUtils.isEmpty(msgDtoList)) {
            return;
        }

        fillUserMobile(msgDtoList);
        fillDeptName(msgDtoList);

        Map<String, List<DeptSaStorageWarnMsgDTO>> mobileGroup = msgDtoList.stream()
                .collect(Collectors.groupingBy(DeptSaStorageWarnMsgDTO::getMobile));
        for (Map.Entry<String, List<DeptSaStorageWarnMsgDTO>> entry : mobileGroup.entrySet()) {
            StringBuilder sb = new StringBuilder(EnvEnum.getEnv().name() + ":\n");
            int idx = 1;
            for (DeptSaStorageWarnMsgDTO msgDTO : entry.getValue()) {
                sb.append(idx++).append(". ").append(msgDTO.genMsg()).append("\n");
            }
            sb.append("@" + entry.getKey());

            log.debug(LogUtil.format("发送钉钉消息：{}", "SgCDeptWarnSaStorageService.sendDingTalkMsg"), sb);
            if (EnvEnum.isProd()) {
                DingTalkUtil.sendTextMsg(DingTalkTokenEnum.DEPT_WARN_SA_STORAGE, sb.toString(), Collections.singletonList(entry.getKey()));
            }
        }
    }

    /**
     * 获取用户信息
     *
     * @param msgDtoList 告警对象列表
     */
    private void fillUserMobile(List<DeptSaStorageWarnMsgDTO> msgDtoList) {
        List<Long> userIds = msgDtoList.stream().map(DeptSaStorageWarnMsgDTO::getUserId).distinct().collect(Collectors.toList());
        Map<Long, CpUsers> idUserMap = rpcCpUserService.queryUserInfoByIds(userIds);

        log.warn(LogUtil.format("部门配销仓库存告警-补充告警手机号，用户ID列表：{},获取用户信息:{}",
                "SgCDeptWarnSaStorageService.fillUserMobile"), userIds, JSON.toJSONString(idUserMap));

        /*如果手机号不存在则无法告警，需要提示出来*/
        for (DeptSaStorageWarnMsgDTO dto : msgDtoList) {
            CpUsers user = idUserMap.getOrDefault(dto.getUserId(), new CpUsers());
            dto.setMobile(Objects.isNull(user) ? "用户不存在" :
                    StringUtils.isEmpty(user.getMobil()) ? user.getEname() + "-手机号未设置" : user.getMobil());
        }
    }

    /**
     * 获取部门信息
     *
     * @param msgDtoList 告警对象列表
     */
    private void fillDeptName(List<DeptSaStorageWarnMsgDTO> msgDtoList) {
        List<Long> deptIds = msgDtoList.stream().map(DeptSaStorageWarnMsgDTO::getDeptId).distinct().collect(Collectors.toList());
        ValueHolderV14<List<CpCDistributionOrganization>> holderV14 = cpCDistributionOrganizationQueryCmd.queryByIds(deptIds);

        log.warn(LogUtil.format("部门配销仓库存告警-补充告警部门名称，部门ID列表：{},获取部门信息:{}",
                "SgCDeptWarnSaStorageService.fillDeptName"), deptIds, JSON.toJSONString(holderV14));
        if (Objects.isNull(holderV14) || !holderV14.isOK()) {
            throw new NDSException("获取部门信息错误");
        }

        Map<Long, String> idEnameap = ListUtils.emptyIfNull(holderV14.getData()).stream()
                .collect(Collectors.toMap(CpCDistributionOrganization::getId,
                        CpCDistributionOrganization::getEname, (a, b) -> a));
        for (DeptSaStorageWarnMsgDTO dto : msgDtoList) {
            dto.setDeptName(idEnameap.getOrDefault(dto.getDeptId(), dto.getDeptId() + "-未配置部门名称"));
        }
    }

    /**
     * 查询配销仓库存【后续量大可考虑迁移到ADB】
     *
     * @param skuIds     商品ID列表
     * @param saStoreIds 配销仓ID列表
     * @return 商品ID->库存量 映射
     */
    public Map<Long, BigDecimal> querySaStorage(Set<Long> skuIds, Set<Long> saStoreIds) {
        if (CollectionUtils.isEmpty(skuIds) || CollectionUtils.isEmpty(saStoreIds)) {
            log.info(LogUtil.format("查询配销仓库存时参数不能为空，skuIds：{},saStoreIds：{}",
                    LOG_OBJ + "querySaStorage"), skuIds, saStoreIds);
            return Collections.emptyMap();
        }

        List<SgCDeptWarnSaStorageQtyQueryResult> queryResults = sgCDeptWarnSaStorageMapper.selectSaStorage(skuIds, saStoreIds);
        return ListUtils.emptyIfNull(queryResults).stream()
                .collect(Collectors.toMap(SgCDeptWarnSaStorageQtyQueryResult::getSkuId, SgCDeptWarnSaStorageQtyQueryResult::getQty, (a, b) -> a));
    }

    /**
     * 查询配销仓库存
     *
     * @param saStoreId 成品共享配销仓，写死1
     * @return 品：库存
     */
    private Map<Long, BigDecimal> querySharedSaStorage(Long saStoreId) {
        if (Objects.isNull(saStoreId)) {
            return Collections.emptyMap();
        }

        List<SgBSaStorage> storageList = sgBSaStorageQueryService.queryBySaStoreId(saStoreId);
        if (CollectionUtils.isEmpty(storageList)) {
            return Collections.emptyMap();
        }

        return storageList.stream()
                .collect(Collectors.toMap(SgBSaStorage::getPsCSkuId, SgBSaStorage::getQtyAvailable, (a, b) -> a));
    }

}
