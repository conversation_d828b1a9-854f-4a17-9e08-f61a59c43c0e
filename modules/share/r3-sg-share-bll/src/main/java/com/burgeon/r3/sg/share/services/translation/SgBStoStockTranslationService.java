package com.burgeon.r3.sg.share.services.translation;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.SgBSaStoragePreoutFtpMapper;
import com.burgeon.r3.sg.basic.mapper.SgBSpStoragePreoutFtpMapper;
import com.burgeon.r3.sg.basic.mapper.SgBStorageSharedPreoutFtpMapper;
import com.burgeon.r3.sg.basic.model.param.SgStockTranslationShareOutParam;
import com.burgeon.r3.sg.basic.model.param.SgStockTranslationStoOutParam;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgBSaStoragePreoutFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgBSpStoragePreoutFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorageSharedPreoutFtp;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOut;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOutItem;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOut;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutItemMapper;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutMapper;
import com.burgeon.r3.sg.share.model.request.translation.SgBStoStockTranslationListRequest;
import com.burgeon.r3.sg.share.model.request.translation.SgBStoStockTranslationRequest;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutMapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BeanCopierUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 库存平移（订单）
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SgBStoStockTranslationService {

    @Autowired
    private SgBShareOutMapper shareOutMapper;
    @Autowired
    private SgBShareOutItemMapper shareOutItemMapper;
    @Autowired
    private SgBStoOutMapper stoOutMapper;
    @Autowired
    private SgBStoOutItemMapper stoOutItemMapper;
    @Autowired
    private SgBSpStoragePreoutFtpMapper spStoragePreoutFtpMapper;
    @Autowired
    private SgBSaStoragePreoutFtpMapper saStoragePreoutFtpMapper;
    @Autowired
    private SgBStorageSharedPreoutFtpMapper sharedPreoutFtpMapper;

    /**
     * 库存平移（订单）
     *
     * @param request
     * @return
     */
    public ValueHolderV14<SgR3BaseResult> stockTranslation(SgBStoStockTranslationRequest request) {
        SgBStoStockTranslationService bean = ApplicationContextHandle.getBean(SgBStoStockTranslationService.class);
        SgBStoStockTranslationListRequest listRequest = new SgBStoStockTranslationListRequest();
        List<SgBStoStockTranslationRequest> mainList = new ArrayList<>();
        mainList.add(request);
        listRequest.setMainList(mainList);
        listRequest.setLoginUser(request.getLoginUser());
        try {
            return bean.doStockTranslation(listRequest);
        } catch (Exception e) {
            log.error(e.getMessage());
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 库存平移（订单）
     *
     * @param request
     * @return
     */
    public ValueHolderV14<SgR3BaseResult> stockTranslation(SgBStoStockTranslationListRequest request) {
        SgBStoStockTranslationService bean = ApplicationContextHandle.getBean(SgBStoStockTranslationService.class);
        try {
            return bean.doStockTranslation(request);
        } catch (Exception e) {
            log.error(e.getMessage());
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 库存平移（订单）
     *
     * @param httpRequest
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> doStockTranslation(SgBStoStockTranslationListRequest httpRequest) {
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "平移成功！");

        if (Objects.isNull(httpRequest)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请求参数为空");
            return v14;
        }

        if (log.isDebugEnabled()) {
            log.debug("SgBStoStockTranslationService.doStockTranslation.request:{}", JSONObject.toJSONString(httpRequest));
        }

        checkParam(httpRequest);

        // 批量生成主表、明细
        List<SgBShareOut> createShareOutList = new ArrayList<>();
        List<SgBShareOutItem> createShareOutItemList = new ArrayList<>();

        // 批量创建sa/sp 流水
        List<SgBSaStoragePreoutFtp> createSaStorageFtpList = new ArrayList<>();
        List<SgBSpStoragePreoutFtp> createSpStorageFtpList = new ArrayList<>();

        // 批量创建sto
        List<SgBStoOut> createStoOutList = new ArrayList<>();
        List<SgBStoOutItem> createStoOutItemList = new ArrayList<>();
        List<SgBStorageSharedPreoutFtp> createStorageSharedPeroutFtpList = new ArrayList<>();

        User user = httpRequest.getLoginUser();

        httpRequest.getMainList().forEach(request -> {
            SgBStoStockTranslationRequest.SgBStoStockTranslationMainRequest mainRequest = request.getMainRequest();
            List<SgBStoStockTranslationRequest.SgBStoStockTranslationItemRequest> itemRequestList = request.getItemRequestList();

            if (log.isDebugEnabled()) {
                log.debug("start SgBStoStockTranslationService.stockTranslation. request = {}", JSONObject.toJSONString(request.getMainRequest()));
            }

            Integer newBillType = request.getMainRequest().getNewBillType();

            // 对请求参数数据进行查询并分组，用于后续处理数据
            // key: SA/SP_{s}_{sa/sp}   vk:SA/SP_s_sa/sp_sku  处理结果，按照一层分组生成单据，按照二层分组生成明细
            Map<String, Map<String, List<SgStockTranslationShareOutParam>>> shareMap = new HashMap<>();
            // key: l_sku
            Map<String, List<SgStockTranslationStoOutParam>> stoMap = new HashMap<>();

            // 处理需要生成共享占用单、逻辑占用单数据
            itemRequestList.forEach(item -> {
                // 处理共享占用单数据
                getShareMapByItem(item, shareMap);

                // 处理逻辑占用单数据
                getStoMapByItem(item, stoMap);
            });

            shareMap.values().forEach(v -> {
                // 创建共享占用单
                SgBShareOut shareOut = new SgBShareOut();
                Long mainId = ModelUtil.getSequence(SgConstants.SG_B_SHARE_OUT);

                String billNo = SgStoreUtils.getBillNo(SgShareConstants.SEQ_SG_B_SHARE_OUT,
                        SgConstants.SG_B_SHARE_OUT.toUpperCase(), shareOut, user.getLocale());
                AtomicReference<BigDecimal> totQty = new AtomicReference<>(BigDecimal.ZERO);

                // 这里取生成单据
                v.values().forEach(vv -> {
                    SgStockTranslationShareOutParam shareOutParam = vv.get(0);

                    // 对主表进行赋值
                    if (Objects.isNull(shareOut.getId())) {
                        BeanCopierUtil.copy(shareOutParam, shareOut);
                    }

                    // 合单情况下，对数据进行求和
                    AtomicReference<BigDecimal> qtyOutSum = new AtomicReference<>(BigDecimal.ZERO);

                    boolean isPool = Objects.nonNull(shareOutParam.getSgCSharePoolId());

                    SgBSaStoragePreoutFtp newSaStorageFtp = new SgBSaStoragePreoutFtp();
                    SgBSpStoragePreoutFtp newSpStorageFtp = new SgBSpStoragePreoutFtp();

                    Map<Long, BigDecimal> totQtyPreoutMap = vv.stream().collect(Collectors.toMap(SgStockTranslationShareOutParam::getMainId, SgStockTranslationShareOutParam::getTotQtyPreout, (k1, k2) -> k1));

                    vv.forEach(param -> {
                        qtyOutSum.accumulateAndGet(param.getNewQty(), BigDecimal::add);

                        // 作废（减数据）数据 、 冲流水
                        SgBShareOutItem oldItem = new SgBShareOutItem();
                        oldItem.setId(param.getItemId());

                        // 原数据进行扣减
                        oldItem.setQtyPreout(param.getQtyPreout().subtract(param.getNewQty()));

                        if (param.getQtyPreout().compareTo(param.getNewQty()) <= 0) {
                            // 直接作废原数据
                            oldItem.setIsactive(SgConstants.IS_ACTIVE_N);
                        }

                        BigDecimal totQtyPreout = totQtyPreoutMap.get(param.getMainId());
                        totQtyPreoutMap.put(param.getMainId(), totQtyPreout.subtract(param.getNewQty()));

                        shareOutItemMapper.updateById(oldItem);

                        // 查询共享池占用流水
                        if (isPool) {
                            LambdaQueryWrapper<SgBSpStoragePreoutFtp> spStorageFtpWrapper = new LambdaQueryWrapper<>();
                            spStorageFtpWrapper.eq(SgBSpStoragePreoutFtp::getBillNo, param.getBillNo());
                            spStorageFtpWrapper.eq(SgBSpStoragePreoutFtp::getPsCSkuId, param.getPsCSkuId());
                            spStorageFtpWrapper.eq(SgBSpStoragePreoutFtp::getSgCShareStoreId, param.getSgCSharePoolId());

                            spStorageFtpWrapper.orderByDesc(SgBSpStoragePreoutFtp::getCreationdate);
                            spStorageFtpWrapper.last(" limit 1");
                            SgBSpStoragePreoutFtp spStorageFtp = spStoragePreoutFtpMapper.selectOne(spStorageFtpWrapper);

                            AssertUtils.notNull(spStorageFtp, "获取流水失败！");

                            spStorageFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SP_STORAGE_PREOUT_FTP));
                            spStorageFtp.setQtyBegin(spStorageFtp.getQtyEnd());
                            spStorageFtp.setQtyChange(param.getNewQty().negate());
                            spStorageFtp.setQtyEnd(spStorageFtp.getQtyBegin().add(spStorageFtp.getQtyChange()));
                            StorageUtils.setBModelDefalutData(spStorageFtp, request.getLoginUser());
                            spStorageFtp.setServiceNode(SgConstantsIF.SERVICE_NODE_SHARE_OUT_VOID);

                            createSpStorageFtpList.add(spStorageFtp);

                            if (Objects.isNull(newSpStorageFtp.getId())) {
                                BeanCopierUtil.copy(spStorageFtp, newSpStorageFtp);
                            }

                        } else {
                            // 查询配销仓占用流水
                            LambdaQueryWrapper<SgBSaStoragePreoutFtp> saStorageFtpWrapper = new LambdaQueryWrapper<>();
                            saStorageFtpWrapper.eq(SgBSaStoragePreoutFtp::getBillNo, param.getBillNo());
                            saStorageFtpWrapper.eq(SgBSaStoragePreoutFtp::getPsCSkuId, param.getPsCSkuId());
                            saStorageFtpWrapper.eq(SgBSaStoragePreoutFtp::getSgCSaStoreId, param.getSgCSaStoreId());

                            saStorageFtpWrapper.orderByDesc(SgBSaStoragePreoutFtp::getCreationdate);
                            saStorageFtpWrapper.last(" limit 1");
                            SgBSaStoragePreoutFtp saStorageFtp = saStoragePreoutFtpMapper.selectOne(saStorageFtpWrapper);

                            AssertUtils.notNull(saStorageFtp, "获取流水失败！");

                            saStorageFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SA_STORAGE_PREOUT_FTP));
                            saStorageFtp.setQtyBegin(saStorageFtp.getQtyEnd());
                            saStorageFtp.setQtyChange(param.getNewQty().negate());
                            saStorageFtp.setQtyEnd(saStorageFtp.getQtyBegin().add(saStorageFtp.getQtyChange()));
                            StorageUtils.setBModelDefalutData(saStorageFtp, request.getLoginUser());
                            saStorageFtp.setServiceNode(SgConstantsIF.SERVICE_NODE_SHARE_OUT_VOID);

                            createSaStorageFtpList.add(saStorageFtp);

                            if (Objects.isNull(newSpStorageFtp.getId())) {
                                BeanCopierUtil.copy(saStorageFtp, newSaStorageFtp);
                            }
                        }
                    });

                    totQtyPreoutMap.forEach((id, qtyPreout) -> {
                        SgBShareOut oldMain = new SgBShareOut();
                        oldMain.setId(id);
                        oldMain.setTotQtyPreout(qtyPreout);
                        if (qtyPreout.compareTo(BigDecimal.ZERO) <= 0) {
                            oldMain.setIsactive(SgConstants.IS_ACTIVE_N);
                            oldMain.setBillStatus(SgShareConstants.SHARE_OUT_BILL_STATUS_VOID);
                        }
                        shareOutMapper.updateById(oldMain);
                    });

                    // 对明细字段进行赋值
                    SgBShareOutItem shareOutItem = new SgBShareOutItem();
                    BeanCopierUtil.copy(shareOutParam, shareOutItem);

                    Long itemId = ModelUtil.getSequence(SgConstants.SG_B_SHARE_OUT_ITEM);
                    shareOutItem.setId(itemId);
                    shareOutItem.setSgBShareOutId(mainId);
                    shareOutItem.setQtyPreout(qtyOutSum.get());
                    shareOutItem.setQty(shareOutItem.getQtyPreout());
                    StorageUtils.setBModelDefalutData(shareOutItem, user);
                    shareOutItem.setIsactive(SgConstants.IS_ACTIVE_Y);

                    createShareOutItemList.add(shareOutItem);

                    totQty.accumulateAndGet(shareOutItem.getQtyPreout(), BigDecimal::add);

                    if (isPool) {
                        AssertUtils.notNull(newSpStorageFtp.getId(), "程序异常");
                        // 覆盖原id
                        newSpStorageFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SP_STORAGE_PREOUT_FTP));
                        newSpStorageFtp.setQtyBegin(newSpStorageFtp.getQtyEnd());
                        newSpStorageFtp.setQtyChange(shareOutParam.getNewQty());
                        newSpStorageFtp.setQtyEnd(newSpStorageFtp.getQtyBegin().add(newSpStorageFtp.getQtyChange()));
                        newSpStorageFtp.setBillNo(billNo);
                        newSpStorageFtp.setBillId(mainId);
                        newSpStorageFtp.setSourceBillId(shareOutParam.getSourceBillId());
                        newSpStorageFtp.setSourceBillNo(shareOutParam.getSourceBillNo());
                        newSpStorageFtp.setBillItemId(itemId);
                        newSpStorageFtp.setBillType(mainRequest.getNewBillType());
                        StorageUtils.setBModelDefalutData(newSpStorageFtp, request.getLoginUser());
                        newSpStorageFtp.setServiceNode(SgConstantsIF.SERVICE_NODE_SHARE_OUT_SAVE);

                        createSpStorageFtpList.add(newSpStorageFtp);
                    } else {
                        AssertUtils.notNull(newSaStorageFtp.getId(), "程序异常");
                        // 覆盖原id
                        newSaStorageFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SA_STORAGE_PREOUT_FTP));
                        newSaStorageFtp.setQtyBegin(newSaStorageFtp.getQtyEnd());
                        newSaStorageFtp.setQtyChange(shareOutParam.getNewQty());
                        newSaStorageFtp.setQtyEnd(newSaStorageFtp.getQtyBegin().add(newSaStorageFtp.getQtyChange()));
                        newSaStorageFtp.setBillNo(billNo);
                        newSaStorageFtp.setBillId(mainId);
                        newSaStorageFtp.setBillItemId(itemId);
                        newSaStorageFtp.setSourceBillId(shareOutParam.getSourceBillId());
                        newSaStorageFtp.setSourceBillNo(shareOutParam.getSourceBillNo());
                        newSaStorageFtp.setBillType(mainRequest.getNewBillType());
                        StorageUtils.setBModelDefalutData(newSaStorageFtp, request.getLoginUser());
                        newSaStorageFtp.setServiceNode(SgConstantsIF.SERVICE_NODE_SHARE_OUT_SAVE);

                        createSaStorageFtpList.add(newSaStorageFtp);
                    }
                });

                // 对主表字段进行赋值
                shareOut.setId(mainId);
                shareOut.setBillNo(billNo);
                shareOut.setTotQtyPreout(totQty.get());
                shareOut.setTotQtyOrign(shareOut.getTotQtyPreout());
                shareOut.setTotQtyOut(BigDecimal.ZERO);
                shareOut.setSourceBillId(mainRequest.getNewId());
                shareOut.setSourceBillNo(mainRequest.getNewBillNo());
                shareOut.setTotRowNum(v.size());
                StorageUtils.setBModelDefalutData(shareOut, user);
                shareOut.setIsactive(SgConstants.IS_ACTIVE_Y);
                if (SgConstantsIF.BILL_TYPE_RETAIL == mainRequest.getNewBillType()) {
                    shareOut.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
                    shareOut.setTid(mainRequest.getTid());
                } else {
                    shareOut.setSourceBillType(newBillType);
                }

                createShareOutList.add(shareOut);
            });

            // 创建逻辑占用单
            SgBStoOut stoOut = new SgBStoOut();
            Long mainId = ModelUtil.getSequence(SgConstants.SG_B_STO_OUT);

            String billNo = SgStoreUtils.getBillNo(SgStoreConstants.SEQ_STO_OUT,
                    SgConstants.SG_B_STO_OUT.toUpperCase(), stoOut, user.getLocale());
            AtomicReference<BigDecimal> totQtyPreOut = new AtomicReference<>(BigDecimal.ZERO);

            stoMap.values().forEach(vv -> {
                SgStockTranslationStoOutParam shareOutParam = vv.get(0);

                // 对主表进行赋值
                if (Objects.isNull(stoOut.getId())) {
                    BeanCopierUtil.copy(shareOutParam, stoOut);
                }

                // 合单情况下，对数据进行求和
                AtomicReference<BigDecimal> qtyOutSum = new AtomicReference<>(BigDecimal.ZERO);

                SgBStorageSharedPreoutFtp newStoStorageFtp = new SgBStorageSharedPreoutFtp();

                Map<Long, BigDecimal> totQtyPreoutMap = vv.stream().collect(Collectors.toMap(SgStockTranslationStoOutParam::getMainId, SgStockTranslationStoOutParam::getTotQtyPreout, (k1, k2) -> k1));

                vv.forEach(param -> {
                    qtyOutSum.accumulateAndGet(param.getNewQty(), BigDecimal::add);

                    // 作废（减数据）数据 、 冲流水
                    SgBStoOutItem oldItem = new SgBStoOutItem();
                    oldItem.setId(param.getItemId());

                    // 原数据进行扣减
                    oldItem.setQtyPreout(param.getQtyPreout().subtract(param.getNewQty()));

                    if (param.getQtyPreout().compareTo(param.getNewQty()) <= 0) {
                        // 直接作废原数据
                        oldItem.setIsactive(SgConstants.IS_ACTIVE_N);
                    }

                    BigDecimal totQtyPreout = totQtyPreoutMap.get(param.getMainId());
                    totQtyPreoutMap.put(param.getMainId(), totQtyPreout.subtract(param.getNewQty()));

                    stoOutItemMapper.updateById(oldItem);

                    // 冲流水
                    LambdaQueryWrapper<SgBStorageSharedPreoutFtp> storageSharedFtpWrapper = new LambdaQueryWrapper<>();
                    storageSharedFtpWrapper.eq(SgBStorageSharedPreoutFtp::getBillNo, param.getBillNo());
                    storageSharedFtpWrapper.eq(SgBStorageSharedPreoutFtp::getPsCSkuId, param.getPsCSkuId());
                    storageSharedFtpWrapper.eq(SgBStorageSharedPreoutFtp::getCpCStoreId, param.getCpCStoreId());

                    storageSharedFtpWrapper.orderByDesc(SgBStorageSharedPreoutFtp::getCreationdate);
                    storageSharedFtpWrapper.last(" limit 1");
                    SgBStorageSharedPreoutFtp stoStorageFtp = sharedPreoutFtpMapper.selectOne(storageSharedFtpWrapper);

                    AssertUtils.notNull(stoStorageFtp, "获取流水失败！");

                    stoStorageFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_STORAGE_SHARED_PREOUT_FTP));
                    stoStorageFtp.setQtyBegin(stoStorageFtp.getQtyEnd());
                    stoStorageFtp.setQtyChange(param.getNewQty().negate());
                    stoStorageFtp.setQtyEnd(stoStorageFtp.getQtyBegin().add(stoStorageFtp.getQtyChange()));
                    StorageUtils.setBModelDefalutData(stoStorageFtp, request.getLoginUser());
                    stoStorageFtp.setServiceNode(SgConstantsIF.SERVICE_NODE_STO_OUT_VOID);

                    createStorageSharedPeroutFtpList.add(stoStorageFtp);

                    if (Objects.isNull(newStoStorageFtp.getId())) {
                        BeanCopierUtil.copy(stoStorageFtp, newStoStorageFtp);
                    }

                });

                totQtyPreoutMap.forEach((id, qtyPreout) -> {
                    SgBStoOut oldMain = new SgBStoOut();
                    oldMain.setId(id);
                    oldMain.setTotQtyPreout(qtyPreout);
                    if (qtyPreout.compareTo(BigDecimal.ZERO) <= 0) {
                        // 逻辑占用单作废状态：is_active:N
                        oldMain.setIsactive(SgConstants.IS_ACTIVE_N);
                        oldMain.setBillStatus(SgStoreConstants.BILL_STO_OUT_STATUS_CANCELED);
                    }
                    stoOutMapper.updateById(oldMain);
                });
                // 对明细字段进行赋值
                SgBStoOutItem stoOutItem = new SgBStoOutItem();
                BeanCopierUtil.copy(shareOutParam, stoOutItem);

                Long itemId = ModelUtil.getSequence(SgConstants.SG_B_STO_OUT_ITEM);
                stoOutItem.setId(itemId);
                stoOutItem.setSgBStoOutId(mainId);
                stoOutItem.setQtyPreout(qtyOutSum.get());
                stoOutItem.setQty(stoOutItem.getQtyPreout());
                StorageUtils.setBModelDefalutData(stoOutItem, user);
                stoOutItem.setIsactive(SgConstants.IS_ACTIVE_Y);

                createStoOutItemList.add(stoOutItem);

                totQtyPreOut.accumulateAndGet(stoOutItem.getQtyPreout(), BigDecimal::add);

                AssertUtils.notNull(newStoStorageFtp.getId(), "程序异常");
                // 覆盖原id
                newStoStorageFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_STORAGE_SHARED_PREOUT_FTP));
                newStoStorageFtp.setQtyBegin(newStoStorageFtp.getQtyEnd());
                newStoStorageFtp.setQtyChange(shareOutParam.getNewQty());
                newStoStorageFtp.setQtyEnd(newStoStorageFtp.getQtyBegin().add(newStoStorageFtp.getQtyChange()));
                newStoStorageFtp.setBillNo(billNo);
                newStoStorageFtp.setBillId(mainId);
                newStoStorageFtp.setBillItemId(itemId);
                newStoStorageFtp.setSourceBillId(shareOutParam.getSourceBillId());
                newStoStorageFtp.setSourceBillNo(shareOutParam.getSourceBillNo());
                newStoStorageFtp.setBillType(mainRequest.getNewBillType());
                StorageUtils.setBModelDefalutData(newStoStorageFtp, request.getLoginUser());
                newStoStorageFtp.setServiceNode(SgConstantsIF.SERVICE_NODE_STO_OUT_SAVE);

                createStorageSharedPeroutFtpList.add(newStoStorageFtp);
            });

            // 对主表字段进行赋值
            stoOut.setId(mainId);
            stoOut.setBillNo(billNo);
            stoOut.setSourceBillId(mainRequest.getNewId());
            stoOut.setSourceBillNo(mainRequest.getNewBillNo());
            stoOut.setTotQtyPreout(totQtyPreOut.get());
            stoOut.setTotQtyOrign(stoOut.getTotQtyPreout());
            stoOut.setTotQtyOut(BigDecimal.ZERO);
            stoOut.setTotRowNum(stoMap.size());
            StorageUtils.setBModelDefalutData(stoOut, user);
            stoOut.setIsactive(SgConstants.IS_ACTIVE_Y);
            if (SgConstantsIF.BILL_TYPE_RETAIL == mainRequest.getNewBillType()) {
                stoOut.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
                stoOut.setTid(mainRequest.getTid());
            } else {
                stoOut.setSourceBillType(newBillType);
            }

            // 创建
            createStoOutList.add(stoOut);
        });

        // 批量生成相关数据
        shareOutMapper.batchInsert(createShareOutList);
        shareOutItemMapper.batchInsert(createShareOutItemList);

        if (CollectionUtils.isNotEmpty(createSaStorageFtpList)) {
            saStoragePreoutFtpMapper.batchInsert(createSaStorageFtpList);
        }
        if (CollectionUtils.isNotEmpty(createSpStorageFtpList)) {
            spStoragePreoutFtpMapper.batchInsert(createSpStorageFtpList);
        }
        stoOutMapper.batchInsert(createStoOutList);
        stoOutItemMapper.batchInsert(createStoOutItemList);

        sharedPreoutFtpMapper.batchInsert(createStorageSharedPeroutFtpList);

        return v14;
    }

    private void getStoMapByItem(SgBStoStockTranslationRequest.SgBStoStockTranslationItemRequest item,
                                 Map<String, List<SgStockTranslationStoOutParam>> stoMap) {

        // 查询逻辑占用单相关数据
        LambdaQueryWrapper<SgBStoOut> stoOutWrapper = new LambdaQueryWrapper<>();
        stoOutWrapper.eq(SgBStoOut::getSourceBillType, SgConstantsIF.BILL_TYPE_FOR_WAREHOUSE);
        stoOutWrapper.eq(SgBStoOut::getSourceBillId, item.getOriginalId());
        stoOutWrapper.eq(StringUtils.isNotEmpty(item.getStoOutBillNo()), SgBStoOut::getBillNo, item.getStoOutBillNo());
        stoOutWrapper.eq(SgBStoOut::getIsactive, SgConstants.IS_ACTIVE_Y);
        stoOutWrapper.last(" limit 1");
        SgBStoOut stoOut = stoOutMapper.selectOne(stoOutWrapper);
        AssertUtils.notNull(stoOut, "未查询到逻辑占用单");

        LambdaQueryWrapper<SgBStoOutItem> stoOutItemWrapper = new LambdaQueryWrapper<>();
        stoOutItemWrapper.eq(SgBStoOutItem::getSgBStoOutId, stoOut.getId());
        stoOutItemWrapper.eq(SgBStoOutItem::getSourceBillItemId, item.getOriginalItemId());
        stoOutItemWrapper.eq(SgBStoOutItem::getIsactive, SgConstants.IS_ACTIVE_Y);
        stoOutItemWrapper.last("limit 1");
        SgBStoOutItem stoOutItem = stoOutItemMapper.selectOne(stoOutItemWrapper);

        AssertUtils.notNull(stoOutItem, "未查询到逻辑占用单明细");
        AssertUtils.isTrue(stoOut.getId().equals(stoOutItem.getSgBStoOutId()), "逻辑占用单与明细不匹配");

        AssertUtils.isTrue(item.getQty().compareTo(stoOutItem.getQtyPreout()) <= 0,
                "新逻辑占用数量不能大于原占用数量");

        SgStockTranslationStoOutParam stoOutParam = new SgStockTranslationStoOutParam();
        BeanCopierUtil.copy(stoOut, stoOutParam);
        BeanCopierUtil.copy(stoOutItem, stoOutParam);
        stoOutParam.setNewQty(item.getQty());
        stoOutParam.setSourceBillItemId(item.getNewItemId());
        stoOutParam.setMainId(stoOut.getId());
        stoOutParam.setItemId(stoOutItem.getId());

        String stoKey = stoOutParam.getCpCStoreId() + SgConstants.SG_CONNECTOR_MARKS_6 + stoOutParam.getPsCSkuId();

        List<SgStockTranslationStoOutParam> stoOutMapValue = stoMap.get(stoKey);
        if (Objects.isNull(stoOutMapValue)) {
            stoOutMapValue = new ArrayList<>();
            stoOutMapValue.add(stoOutParam);
            stoMap.put(stoKey, stoOutMapValue);
        } else {
            stoOutMapValue.add(stoOutParam);
        }
    }

    private void getShareMapByItem(SgBStoStockTranslationRequest.SgBStoStockTranslationItemRequest item,
                                   Map<String, Map<String, List<SgStockTranslationShareOutParam>>> shareMap) {
        // 查询共享占用单相关数据
        LambdaQueryWrapper<SgBShareOut> shareOutWrapper = new LambdaQueryWrapper<>();
        shareOutWrapper.eq(SgBShareOut::getSourceBillId, item.getTimeOrderId());
        shareOutWrapper.eq(SgBShareOut::getSourceBillType, SgConstantsIF.BILL_TYPE_VIPSHOP_TIME);
        shareOutWrapper.eq(SgBShareOut::getIsactive, SgConstants.IS_ACTIVE_Y);
        shareOutWrapper.last("limit 1");
        SgBShareOut shareOut = shareOutMapper.selectOne(shareOutWrapper);
        AssertUtils.notNull(shareOut, "未查询到共享占用单");
        LambdaQueryWrapper<SgBShareOutItem> shareOutItemWrapper = new LambdaQueryWrapper<>();
        shareOutItemWrapper.eq(SgBShareOutItem::getSgBShareOutId, shareOut.getId());
        shareOutItemWrapper.eq(SgBShareOutItem::getPsCSkuId, item.getPsCSkuId());
        shareOutItemWrapper.eq(SgBShareOutItem::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgBShareOutItem> shareOutItemList = shareOutItemMapper.selectList(shareOutItemWrapper);

        AssertUtils.cannot(CollectionUtils.isEmpty(shareOutItemList), "未查询到共享占用单明细");
        AssertUtils.isTrue(shareOutItemList.size() == 1, "共享占用单明细有误");
        SgBShareOutItem shareOutItem = shareOutItemList.get(0);

        AssertUtils.isTrue(item.getQty().compareTo(shareOutItem.getQtyPreout()) <= 0,
                "新共享占用数量不能大于原占用数量！");

        SgStockTranslationShareOutParam shareOutParam = new SgStockTranslationShareOutParam();
        BeanCopierUtil.copy(shareOut, shareOutParam);
        BeanCopierUtil.copy(shareOutItem, shareOutParam);

        shareOutParam.setMainId(shareOut.getId());
        shareOutParam.setItemId(shareOutItem.getId());
        shareOutParam.setNewQty(item.getQty());
        shareOutParam.setSourceBillItemId(item.getNewItemId());

        boolean isPool = Objects.isNull(shareOutParam.getSgCSaStoreId());
        String shareMapKey = (isPool ? SgConstantsIF.STORAGE_TYPE_SP : SgConstantsIF.STORAGE_TYPE_SA) + SgConstants.SG_CONNECTOR_MARKS_6 + shareOutParam.getSgCShareStoreId() + SgConstants.SG_CONNECTOR_MARKS_6 + (isPool ? shareOutParam.getSgCSharePoolId() : shareOutParam.getSgCSaStoreId());
        String shareMapValueKey = (isPool ? SgConstantsIF.STORAGE_TYPE_SP : SgConstantsIF.STORAGE_TYPE_SA) + SgConstants.SG_CONNECTOR_MARKS_6 + shareOutParam.getSgCShareStoreId() + SgConstants.SG_CONNECTOR_MARKS_6 + (isPool ? shareOutParam.getSgCSharePoolId() : shareOutParam.getSgCSaStoreId()) + SgConstants.SG_CONNECTOR_MARKS_6 + shareOutParam.getPsCSkuId();

        if (log.isDebugEnabled()) {
            log.debug("SgBStoStockTranslationService.stockTranslation. shareMapKey:{} , shareMapValueKey:{}", shareMapKey, shareMapValueKey);
        }

        Map<String, List<SgStockTranslationShareOutParam>> shareMapValue = shareMap.get(shareMapKey);
        // 这一段逻辑，表示双重分组
        if (Objects.isNull(shareMapValue)) {
            shareMapValue = new HashMap<>();
            List<SgStockTranslationShareOutParam> list = new ArrayList<>();
            list.add(shareOutParam);
            shareMapValue.put(shareMapValueKey, list);
            shareMap.put(shareMapKey, shareMapValue);
        } else {
            List<SgStockTranslationShareOutParam> vv = shareMapValue.get(shareMapValueKey);
            if (Objects.isNull(vv)) {
                vv = new ArrayList<>();
                vv.add(shareOutParam);
                shareMapValue.put(shareMapValueKey, vv);
            } else {
                vv.add(shareOutParam);
            }
        }
    }

    private void checkParam(SgBStoStockTranslationListRequest httpRequest) {

        AssertUtils.notEmpty(httpRequest.getMainList(), "请求参数不能为空");
        AssertUtils.notNull(httpRequest.getLoginUser(), "用户不能为空");

        httpRequest.getMainList().forEach(request -> {
            SgBStoStockTranslationRequest.SgBStoStockTranslationMainRequest mainRequest = request.getMainRequest();
            List<SgBStoStockTranslationRequest.SgBStoStockTranslationItemRequest> itemRequestList = request.getItemRequestList();


            AssertUtils.notNull(mainRequest, "主表信息不能为空");
            AssertUtils.cannot(CollectionUtils.isEmpty(itemRequestList), "明细不能为空");

            AssertUtils.notNull(mainRequest.getNewId(), "newId 字段不能为空");
            AssertUtils.cannot(StringUtils.isEmpty(mainRequest.getNewBillNo()), "newBillNo 字段不能为空");
            AssertUtils.cannot(Objects.isNull(mainRequest.getNewBillType()), "newBillType 字段不能为空");

            itemRequestList.forEach(item -> {
                AssertUtils.notNull(item.getNewItemId(), "newItemId 字段不能为空");
                AssertUtils.notNull(item.getPsCSkuId(), "PsCSkuId 字段不能为空");
                AssertUtils.notNull(item.getQty(), "qty 字段不能为空");
                AssertUtils.notNull(item.getTimeOrderId(), "timeOrderId 字段不能为空");
                AssertUtils.notNull(item.getOriginalItemId(), "originalItemId 字段不能为空");
                AssertUtils.notNull(item.getOriginalId(), "originalId 字段不能为空");

            });
        });
    }
}
