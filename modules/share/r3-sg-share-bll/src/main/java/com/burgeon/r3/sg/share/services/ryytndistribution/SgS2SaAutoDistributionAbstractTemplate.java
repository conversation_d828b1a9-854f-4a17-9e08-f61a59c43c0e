//package com.burgeon.r3.sg.share.services.ryytndistribution;
//
//import com.alibaba.fastjson.JSON;
//import com.burgeon.r3.sg.core.enums.SgDistributionTypeEnum;
//import com.burgeon.r3.sg.core.model.table.basic.SgBSaStorage;
//import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
//import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
//import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCDepartmentMonthDemand;
//import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
//import com.burgeon.r3.sg.basic.utils.DingTalkUtil;
//import com.burgeon.r3.sg.share.common.SgShareConstants;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.constants.ResultCode;
//import com.jackrain.nea.redis.config.CusRedisTemplate;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.util.DateUtil;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.collections.MapUtils;
//import org.apache.commons.lang.time.DateFormatUtils;
//import org.apache.commons.lang3.tuple.Pair;
//import org.assertj.core.util.Lists;
//import org.assertj.core.util.Sets;
//import org.springframework.data.redis.core.SetOperations;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Propagation;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.util.Calendar;
//import java.util.Collections;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.Optional;
//import java.util.Set;
//import java.util.concurrent.TimeUnit;
//import java.util.stream.Collectors;
//
///**
// * 根据二级部门月需求量自动分货
// * </br>
// * 已弃用，详见{@link }
// *
// * <AUTHOR>
// * @since 2022-09-21 14:03
// */
//@Slf4j
//@Service
//@Deprecated
//public abstract class SgS2SaAutoDistributionAbstractTemplate {
//    /**
//     * 日志OBJ
//     */
//    private static final String LOG_OBJ = "SgS2SaAutoDistributionAbstractTemplate.";
//    /**
//     * 自动分货-公共操作
//     */
//    @Resource
//    private SgS2SaAutoDistributionManager sgS2SaAutoDistributionManager;
//
//    @Resource
//    private SgCDepartmentMonthDemandService sgCDepartmentMonthDemandService;
//
//    /*部门日需求计划报表*/
//    @Resource
//    private SgCDepartmentDemandPlanService sgCDepartmentDemandPlanService;
//
//    public ValueHolderV14<Void> execute(boolean isNew) {
//        if (isNew) {
//            return executeNew();
//        }
//        return executeOld();
//    }
//
//    /**
//     * 执行分配
//     *
//     * @return 分配内容
//     */
//    public ValueHolderV14<Void> executeOld() {
//        SgS2SaAutoDistributionAbstractTemplate service = getBean();
//        int successSkuCnt = 0;
//        int totalSkuCount = 0;
//        /*获取所有成品聚合仓*/
//        List<SgCShareStore> shareStores = sgS2SaAutoDistributionManager.querySgShareStores();
//        /*获取聚合仓下配销仓数据*/
//        Map<Long, List<SgCSaStore>> shareIdSaStoreMap = sgS2SaAutoDistributionManager.querySaStoresMap(shareStores, true);
//        /*逐个聚合仓处理*/
//        for (Map.Entry<Long, List<SgCSaStore>> shareSaStoreEntry : shareIdSaStoreMap.entrySet()) {
//            List<SgCSaStore> saStores = shareSaStoreEntry.getValue();
//            /*部门ID->配销仓ID列表 映射*/
//            Map<Long, Set<Long>> deptSaIdsMap = saStores.stream()
//                    .filter(obj -> Objects.nonNull(obj.getCpCStoredimItemId()))
//                    .collect(Collectors.groupingBy(SgCSaStore::getCpCStoredimItemId, Collectors.mapping(SgCSaStore::getId, Collectors.toSet())));
//            /*月需求中当月所有SKU*/
//            Set<Long> demandSkuIds = sgCDepartmentMonthDemandService.querySkuBySaStore(deptSaIdsMap.keySet(), DateFormatUtils.format(new Date(), "yyyyMM"));
//            /*获取月需求：商品ID->月需求列表*/
//            totalSkuCount += demandSkuIds.size();
//            for (Long skuId : demandSkuIds) {
//                try {
//                    service.distributeBySku(shareSaStoreEntry.getKey(), skuId, saStores, deptSaIdsMap);
//                    successSkuCnt++;
//                } catch (Exception e) {
//                    log.error(LogUtil.format(genLogPrefix() + "自动分货出错,聚合仓ID:{},商品ID:{},错误信息：[{}]", LOG_OBJ + "execute"),
//                            shareSaStoreEntry.getKey(), skuId, Throwables.getStackTraceAsString(e));
//                }
//            }
//            /*期初分货时，如果二级部门当月没有提报需求，则退回*/
//            try {
//                service.backIfCurrentMonthHasNoDemandWhenMonthDistributionNew(shareSaStoreEntry.getKey(), deptSaIdsMap, demandSkuIds);
//            } catch (Exception e) {
//                log.error(LogUtil.format(genLogPrefix() + "退回没有提报需求的配销仓库存时出错，聚合仓ID：{},部门ID->配销仓ID列表表：{},月需求中当月所有SKU:{},异常信息：{}", LOG_OBJ + "execute"),
//                        shareSaStoreEntry.getKey(), JSON.toJSONString(deptSaIdsMap), JSON.toJSONString(demandSkuIds), Throwables.getStackTraceAsString(e));
//            }
//        }
//        if (successSkuCnt != totalSkuCount) {
//            log.warn(LogUtil.format(genLogPrefix() + "自动分货，任务部分成功部分失败，成功SKU数：{},总SKU数：{}", LOG_OBJ + "execute"), successSkuCnt, totalSkuCount);
//        }
//
//        return new ValueHolderV14<>(ResultCode.SUCCESS, genLogPrefix() + "自动分货：任务执行完成！,成功SKU数：" + successSkuCnt + "/" + totalSkuCount);
//    }
//
//    /**
//     * 月初分货时-将没有填报需求的货品全部退回到聚合仓
//     *
//     * @param shareStoreId 聚合仓ID
//     * @param deptSaIdsMap 部门ID->配销仓ID列表 映射
//     * @param demandSkuIds 月需求中当月所有SKU
//     */
//    @Transactional(rollbackFor = Throwable.class)
//    public void backIfCurrentMonthHasNoDemandWhenMonthDistributionOld(Long shareStoreId, Map<Long, Set<Long>> deptSaIdsMap, Set<Long> demandSkuIds) {
//        if (!isMonthDistribution() || MapUtils.isEmpty(deptSaIdsMap)) {
//            return;
//        }
//        int totalNum = 0;
//        int successNum = 0;
//        /*逐个部门处理*/
//        for (Map.Entry<Long, Set<Long>> deptSaStoreEntry : deptSaIdsMap.entrySet()) {
//            /*获取配销仓下所有可用库存大于0的SKU和数量映射*/
//            List<SgBSaStorage> storageList = sgS2SaAutoDistributionManager.querySaStorageMap(deptSaStoreEntry.getValue());
//            Map<Long, List<SgBSaStorage>> skuStorageMap = storageList.stream()
//                    .filter(obj -> !demandSkuIds.contains(obj.getPsCSkuId()))
//                    .collect(Collectors.groupingBy(SgBSaStorage::getPsCSkuId));
//            totalNum += skuStorageMap.size();
//            for (Map.Entry<Long, List<SgBSaStorage>> entry : skuStorageMap.entrySet()) {
//                List<Pair<Long, BigDecimal>> pairList = entry.getValue().stream()
//                        .map(obj -> Pair.of(obj.getSgCSaStoreId(), obj.getQtyAvailable())).collect(Collectors.toList());
//                try {
//                    sgS2SaAutoDistributionManager.doAllocationReturn(shareStoreId, entry.getKey(), pairList, SgDistributionTypeEnum.MONTH);
//                    successNum++;
//                } catch (Exception e) {
//                    log.error(LogUtil.format(genLogPrefix() + "退回没有提报需求的配销仓库存时出错，聚合仓ID：{},商品ID：{}，配销仓ID+退货分货量映射列表：{},异常信息：{}", LOG_OBJ + "backIfCurrentMonthHasNoDemandWhenMonthDistribution"),
//                            shareStoreId, entry.getKey(), JSON.toJSONString(pairList), Throwables.getStackTraceAsString(e));
//                }
//            }
//        }
//
//        log.info(LogUtil.format("月初分货时-将没有填报需求的货品全部退回到聚合仓，聚合仓ID：{}，部门ID->配销仓ID列表 映射:{},月需求中当月所有SKU:{},成功SKU数:{}/{}",
//                        LOG_OBJ + "backIfCurrentMonthHasNoDemandWhenMonthDistribution"),
//                shareStoreId, JSON.toJSONString(deptSaIdsMap), JSON.toJSONString(demandSkuIds), successNum, totalNum);
//
//        if ("local".equals(System.getProperty("env"))) {
//            throw new RuntimeException("本地环境测试需要回滚数据");
//        }
//    }
//
//    /**
//     * 执行分配
//     *
//     * @return 分配内容
//     */
//    public ValueHolderV14<Void> executeNew() {
//        CusRedisTemplate<String, Long> redisTemplate = RedisMasterUtils.getObjRedisTemplate();
//        SetOperations<String, Long> redisSetOperations = redisTemplate.opsForSet();
//        List<String> errorSkuList = Lists.newArrayList();
//
//        SgS2SaAutoDistributionAbstractTemplate service = getBean();
//        int successSkuCnt = 0;
//        int totalSkuCount = 0;
//        /*获取所有成品聚合仓*/
//        List<SgCShareStore> shareStores = sgS2SaAutoDistributionManager.querySgShareStores();
//        /*获取聚合仓下配销仓数据*/
//        Map<Long, List<SgCSaStore>> shareIdSaStoreMap = sgS2SaAutoDistributionManager.querySaStoresMap(shareStores, true);
//        /*逐个聚合仓处理*/
//        for (Map.Entry<Long, List<SgCSaStore>> shareSaStoreEntry : shareIdSaStoreMap.entrySet()) {
//            List<SgCSaStore> saStores = shareSaStoreEntry.getValue();
//            /*部门ID->配销仓ID列表 映射*/
//            Map<Long, Set<Long>> deptSaIdsMap = saStores.stream()
//                    .filter(obj -> Objects.nonNull(obj.getCpCStoredimItemId()))
//                    .collect(Collectors.groupingBy(SgCSaStore::getCpCStoredimItemId, Collectors.mapping(SgCSaStore::getId, Collectors.toSet())));
//            /*月需求中当月所有SKU*/
//            Set<Long> demandSkuIds = sgCDepartmentMonthDemandService.querySkuBySaStore(deptSaIdsMap.keySet(), DateFormatUtils.format(new Date(), "yyyyMM"));
//            /*需要被执行的SKU*/
//            Set<Long> exeSkuIds = filterExeSkuByRedis(shareSaStoreEntry.getKey(), redisSetOperations, demandSkuIds);
//            /*数量=聚合仓数*SKU数*/
//            totalSkuCount += exeSkuIds.size();
//            for (Long skuId : exeSkuIds) {
//                try {
//                    service.distributeBySku(shareSaStoreEntry.getKey(), skuId, saStores, deptSaIdsMap);
//                    successSkuCnt++;
//                    if (isMonthDistribution()) {
//                        redisSetOperations.add(SgShareConstants.DISTRIBUTION_MONTH_SUCCESS_SKU_SET_KEY + shareSaStoreEntry.getKey(), skuId);
//                        redisTemplate.expire(SgShareConstants.DISTRIBUTION_MONTH_SUCCESS_SKU_SET_KEY + shareSaStoreEntry.getKey(), genExpireMilliseconds(), TimeUnit.MILLISECONDS);
//                    }
//                } catch (Exception e) {
//                    errorSkuList.add(shareSaStoreEntry.getKey() + "-" + skuId);
//                    log.error(LogUtil.format(genLogPrefix() + "自动分货出错,聚合仓ID:{},商品ID:{},错误信息：[{}]", LOG_OBJ + "execute"),
//                            shareSaStoreEntry.getKey(), skuId, Throwables.getStackTraceAsString(e));
//                }
//            }
//            /*期初分货时，如果二级部门当月没有提报需求，则退回*/
//            try {
//                service.backIfCurrentMonthHasNoDemandWhenMonthDistributionNew(shareSaStoreEntry.getKey(), deptSaIdsMap, demandSkuIds);
//            } catch (Exception e) {
//                log.error(LogUtil.format(genLogPrefix() + "退回没有提报需求的配销仓库存时出错，聚合仓ID：{},部门ID->配销仓ID列表表：{},月需求中当月所有SKU:{},异常信息：{}", LOG_OBJ + "execute"),
//                        shareSaStoreEntry.getKey(), JSON.toJSONString(deptSaIdsMap), JSON.toJSONString(demandSkuIds), Throwables.getStackTraceAsString(e));
//            }
//        }
//        if (successSkuCnt != totalSkuCount) {
//            DingTalkUtil.sendDistributionMarkdownMsg(genLogPrefix(), JSON.toJSONString(errorSkuList));
//            log.warn(LogUtil.format(genLogPrefix() + "自动分货，任务部分成功部分失败，成功SKU数：{},总SKU数：{}", LOG_OBJ + "execute"), successSkuCnt, totalSkuCount);
//        }
//
//        return new ValueHolderV14<>(ResultCode.SUCCESS, genLogPrefix() + "自动分货：任务执行完成！,成功SKU数：" + successSkuCnt + "/" + totalSkuCount);
//    }
//
//    /**
//     * 获取需要被执行的SKU，获取月初分货成功的SKU
//     * <br/>定时分货每次处理成功的SKU（获取成功的SKU列表，过滤掉失败的）
//     * <br/>月初分货处理失败的SKU（过滤掉成功的SKU）
//     *
//     * @param shareId            聚合仓ID
//     * @param redisSetOperations redisSetOperations
//     * @param demandSkuIds       需要分货的SKU
//     * @return 需要分货的
//     */
//    private Set<Long> filterExeSkuByRedis(Long shareId, SetOperations<String, Long> redisSetOperations, Set<Long> demandSkuIds) {
//        /*当月已成功执行月初分货的SKU集合*/
//        Set<Long> monthSuccessSkuIds = Optional.ofNullable(redisSetOperations.members(SgShareConstants.DISTRIBUTION_MONTH_SUCCESS_SKU_SET_KEY + shareId))
//                .orElse(Collections.emptySet());
//        log.debug(LogUtil.format(genLogPrefix() + "当月已成功执行月初分货的SKU集合:{}", LOG_OBJ + "filterExeSkuByRedis"), JSON.toJSONString(monthSuccessSkuIds));
//
//        Set<Long> exeSku;
//        /*月初分货-处理失败的SKU*/
//        Set<Long> filterSku = Sets.newHashSet(demandSkuIds);
//        if (isMonthDistribution()) {
//            exeSku = filterSku.stream().filter(sku -> !monthSuccessSkuIds.contains(sku)).collect(Collectors.toSet());
//            log.debug(LogUtil.format(genLogPrefix() + "参与分货的SKU集合：{},本次分货需要执行的SKU集合:{}", LOG_OBJ + "filterExeSkuByRedis"),
//                    JSON.toJSON(demandSkuIds), JSON.toJSONString(exeSku));
//            return exeSku;
//        }
//
//        /*定时分货-每次处理成功的SKU*/
//        exeSku = filterSku.stream().filter(monthSuccessSkuIds::contains).collect(Collectors.toSet());
//        log.debug(LogUtil.format(genLogPrefix() + "参与分货的SKU集合：{},本次分货需要执行的SKU集合:{}", LOG_OBJ + "filterExeSkuByRedis"),
//                JSON.toJSON(demandSkuIds), JSON.toJSONString(exeSku));
//        return exeSku;
//    }
//
//    /**
//     * 月初分货时-将没有填报需求的货品全部退回到聚合仓
//     *
//     * @param shareStoreId 聚合仓ID
//     * @param deptSaIdsMap 部门ID->配销仓ID列表 映射
//     * @param demandSkuIds 月需求中当月所有SKU
//     */
//    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRES_NEW)
//    public void backIfCurrentMonthHasNoDemandWhenMonthDistributionNew(Long shareStoreId, Map<Long, Set<Long>> deptSaIdsMap, Set<Long> demandSkuIds) {
//        if (!isMonthDistribution() || MapUtils.isEmpty(deptSaIdsMap)) {
//            return;
//        }
//        CusRedisTemplate<String, Long> redisTemplate = RedisMasterUtils.getObjRedisTemplate();
//        SetOperations<String, Long> redisSetOperations = redisTemplate.opsForSet();
//        List<String> errorSkuList = Lists.newArrayList();
//
//        /*该聚合仓下，已经成功拉回的SKU*/
//        Set<Long> backSuccessSkuIds = Optional.ofNullable(redisSetOperations.members(SgShareConstants.DISTRIBUTION_MONTH_BACK_SUCCESS_SKU_SET_KEY + shareStoreId))
//                .orElse(Collections.emptySet());
//        /*需要被移除的SKU，本次不参与拉回*/
//        Set<Long> excludeSku = Sets.newHashSet(demandSkuIds);
//        excludeSku.addAll(backSuccessSkuIds);
//        /*deptSaIdsMap中的配销仓ID汇总*/
//        Set<Long> saIds = Sets.newHashSet();
//        deptSaIdsMap.values().forEach(saIds::addAll);
//
//        log.debug(LogUtil.format(genLogPrefix() + "参与分货的SKU集合：{},本次不参与拉回的SKU集合:{}", LOG_OBJ + "backIfCurrentMonthHasNoDemandWhenMonthDistributionNew"),
//                JSON.toJSON(demandSkuIds), JSON.toJSONString(excludeSku));
//
//        /*获取配销仓下所有可用库存大于0的SKU对应的库存信息列表*/
//        List<SgBSaStorage> storageList = sgS2SaAutoDistributionManager.querySaStorageMap2Back(excludeSku, saIds);
//        /*根据SKU分组，逐个SKU处理*/
//        Map<Long, List<SgBSaStorage>> skuStorageMap = storageList.stream().collect(Collectors.groupingBy(SgBSaStorage::getPsCSkuId));
//        if (MapUtils.isEmpty(skuStorageMap)) {
//            return;
//        }
//
//        int totalNum = skuStorageMap.size();
//        int successNum = 0;
//
//        for (Map.Entry<Long, List<SgBSaStorage>> skuStorage : skuStorageMap.entrySet()) {
//            /*配销仓ID->可用库存*/
//            List<Pair<Long, BigDecimal>> pairList = null;
//            try {
//                pairList = skuStorage.getValue().stream()
//                        .map(obj -> Pair.of(obj.getSgCSaStoreId(), obj.getQtyAvailable())).collect(Collectors.toList());
//                sgS2SaAutoDistributionManager.doAllocationReturn(shareStoreId, skuStorage.getKey(), pairList, SgDistributionTypeEnum.MONTH);
//                successNum++;
//                /*执行成功后将拉回成功的SKU放入redis*/
//                redisSetOperations.add(SgShareConstants.DISTRIBUTION_MONTH_BACK_SUCCESS_SKU_SET_KEY + shareStoreId, skuStorage.getKey());
//                redisTemplate.expire(SgShareConstants.DISTRIBUTION_MONTH_BACK_SUCCESS_SKU_SET_KEY + shareStoreId, genExpireMilliseconds(), TimeUnit.MILLISECONDS);
//            } catch (Exception e) {
//                errorSkuList.add(shareStoreId + "-" + skuStorage.getKey());
//                log.error(LogUtil.format(genLogPrefix() + "退回没有提报需求的配销仓库存时出错，聚合仓ID：{},商品ID：{}，配销仓ID+退货分货量映射列表：{},异常信息：{}", LOG_OBJ + "backIfCurrentMonthHasNoDemandWhenMonthDistribution"),
//                        shareStoreId, skuStorage.getKey(), JSON.toJSONString(pairList), Throwables.getStackTraceAsString(e));
//            }
//        }
//
//        log.info(LogUtil.format("月初分货时-将没有填报需求的货品全部退回到聚合仓，聚合仓ID：{}，部门ID->配销仓ID列表 映射:{},月需求中当月所有SKU:{},成功SKU数:{}/{}",
//                        LOG_OBJ + "backIfCurrentMonthHasNoDemandWhenMonthDistribution"),
//                shareStoreId, JSON.toJSONString(deptSaIdsMap), JSON.toJSONString(demandSkuIds), successNum, totalNum);
//        if (CollectionUtils.isNotEmpty(errorSkuList)) {
//            DingTalkUtil.sendDistributionMarkdownMsg("月初分货拉回", JSON.toJSONString(errorSkuList));
//        }
//
//        if ("local".equals(System.getProperty("env"))) {
//            throw new RuntimeException("本地环境测试需要回滚数据");
//        }
//    }
//
//    /**
//     * 按SKU执行分配
//     * <br/>
//     * 事务操作，SKU维度,外层事务不影响内层事务提交或回滚
//     *
//     * @param shareStoreId 聚合仓ID
//     * @param skuId        商品ID
//     * @param saStores     配销仓信息列表
//     * @param deptSaIdsMap 部门ID->配销仓ID列表 映射
//     */
//    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRES_NEW)
//    public void distributeBySku(Long shareStoreId, Long skuId, List<SgCSaStore> saStores,
//                                Map<Long, Set<Long>> deptSaIdsMap) {
//        /*同商品下，可分配的仓ID->可分配库存*/
//        Map<Long, BigDecimal> storageBeforeMap = queryStorageMap(saStores, skuId);
//        /*天->二级部门月需求列表,按日期从小到大排序*/
//        List<SgCDepartmentMonthDemand> demandList = sgCDepartmentMonthDemandService.queryMonthDemandBySaAndSku(saStores, skuId);
//
//        /*可分配的总库存*/
//        BigDecimal totalAvailable2Distribute = storageBeforeMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
//        if (BigDecimal.ZERO.compareTo(totalAvailable2Distribute) >= 0) {
//            log.info(LogUtil.format(genLogPrefix() + "按SKU分货-获取可分配的可用库存小于或等于零，该商品不进行分配，聚合仓ID:{}，商品ID:{}", LOG_OBJ + "distributeBySku"), shareStoreId, skuId);
//            /*月初分货，初始化该SKU报表*/
//            executeInitDemandPlanIfMonthDistribution(shareStoreId, skuId, deptSaIdsMap, demandList);
//            return;
//        }
//
//        Map<Date, List<SgCDepartmentMonthDemand>> dateDemandMap = demandList.stream()
//                .collect(Collectors.groupingBy(SgCDepartmentMonthDemand::getDemandDate));
//        List<Date> dateList = dateDemandMap.keySet().stream().sorted(Date::compareTo).collect(Collectors.toList());
//
//        /*同商品下，部门ID->本次任务分配量*/
//        Map<Long, BigDecimal> deptActualMap = new HashMap<>();
//        /*List<SgCDepartmentMonthDemand> modifyDemandList = new ArrayList<>();*/
//        BigDecimal available2Distribute = totalAvailable2Distribute;
//        for (Date date : dateList) {
//            List<SgCDepartmentMonthDemand> dailyDemandList = dateDemandMap.get(date);
//            /*按天计算分配量，返回：剩余可分配的量*/
//            available2Distribute = distributeByDay(deptActualMap, available2Distribute, dailyDemandList/*, modifyDemandList*/);
//            /*余量为零，不继续分配*/
//            if (available2Distribute.compareTo(BigDecimal.ZERO) <= 0) {
//                log.info(LogUtil.format("本次分配结束，聚合仓ID:{}，商品ID:{}，分配需求日期:{}", LOG_OBJ + "distributeBySku"),
//                        shareStoreId, skuId, DateUtil.format(date, "yyyyMMdd"));
//                break;
//            }
//        }
//
//        /*log.debug(LogUtil.format(genLogPrefix() + "自动分货计算需更新的二级部门列表数据：{}", LOG_OBJ+"distributeBySku), JSON.toJSONString(modifyDemandList));
//        batchModifyDemand(modifyDemandList);*/
//
//        /*计算配销仓实际分配量：配销仓ID->本次任务实际分配量 映射*/
//        Map<Long, BigDecimal> saActualMap = calculateSaActualStorageByAverage(deptSaIdsMap, deptActualMap, storageBeforeMap);
//
//        /*生成单据，变更库存：配销仓ID->配销仓更新满足量*/
//        Map<Long, BigDecimal> saIdDemandQtyMap = doDistribute(shareStoreId, skuId, saActualMap, storageBeforeMap);
//        /*更新二级部门需求量（分货退的需求，没有分货单驱动更新需求表，需要手工更新需求表）*/
//        modifyDemandList(skuId, deptSaIdsMap, saIdDemandQtyMap);
//
//        log.info(LogUtil.format(genLogPrefix() + "按SKU分货-执行结束,商品ID:{},可分配的总库存：{},部门ID->配销仓ID列表映射:{}," +
//                                "部门ID->本次任务分配量映射：{},仓ID->可分配库存映射：{}," +
//                                "配销仓ID->本次任务实际分配量映射:{},配销仓ID->配销仓更新满足量:{}",
//                        LOG_OBJ + "distributeBySku"),
//                skuId, totalAvailable2Distribute, JSON.toJSONString(deptSaIdsMap),
//                JSON.toJSONString(deptActualMap), JSON.toJSONString(storageBeforeMap),
//                JSON.toJSONString(saActualMap), JSON.toJSONString(saIdDemandQtyMap));
//
//        /*月初分货，初始化该SKU报表*/
//        executeInitDemandPlanIfMonthDistribution(shareStoreId, skuId, deptSaIdsMap, demandList);
//
//        if ("local".equals(System.getProperty("env"))) {
//            throw new RuntimeException("本地环境测试需要回滚数据");
//        }
//    }
//
//    /**
//     * 月初分货，初始化该SKU报表
//     *
//     * @param shareStoreId 聚合仓ID
//     * @param skuId        商品ID
//     * @param deptSaIdsMap 部门ID->配销仓ID列表 映射
//     * @param demandList   部门需求列表
//     */
//    private void executeInitDemandPlanIfMonthDistribution(Long shareStoreId, Long skuId, Map<Long, Set<Long>> deptSaIdsMap, List<SgCDepartmentMonthDemand> demandList) {
//        if (!isMonthDistribution() || CollectionUtils.isEmpty(demandList)) {
//            return;
//        }
//
//        Date exeDate = new Date();
//        try {
//            sgCDepartmentDemandPlanService.executeBySku(shareStoreId, skuId, deptSaIdsMap, exeDate);
//        } catch (Exception e) {
//            log.error(LogUtil.format("期初分货任务，生成部门日需求计划报表时出错，聚合仓ID：{}，商品ID：{},执行时间：{},错误信息:{}", LOG_OBJ + "executeInitDemandPlan"),
//                    deptSaIdsMap.keySet(), skuId, exeDate, Throwables.getStackTraceAsString(e));
//        }
//    }
//
//
//    /**
//     * 按天计算分配量
//     *
//     * @param deptActualMap        二级部门ID->本次任务分配量
//     * @param available2Distribute 可分配的总库存
//     * @param dailyDemandList      部门日需求列表
//     * @param modifyDemandList     被变更的日需求列表
//     * @return 可分配的总库存在当天分配后的结余
//     */
//    private BigDecimal distributeByDay(Map<Long, BigDecimal> deptActualMap, BigDecimal available2Distribute,
//                                       List<SgCDepartmentMonthDemand> dailyDemandList/*,
//                                       List<SgCDepartmentMonthDemand> modifyDemandList*/) {
//        /*日需求总量*/
//        BigDecimal dailyTotalDemand = dailyDemandList.stream()
//                .map(demand -> demand.getDemandQty().subtract(demand.getActualInQty()))
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//        /*计算是否够分*/
//        BigDecimal remainStorage = available2Distribute.subtract(dailyTotalDemand);
//
//        /*够分*/
//        if (remainStorage.compareTo(BigDecimal.ZERO) >= 0) {
//            enough2Distribute(deptActualMap, dailyDemandList/*, modifyDemandList*/);
//            return remainStorage;
//        }
//
//        /*不够分*/
//        proportion2Distribute(deptActualMap, dailyDemandList/*, modifyDemandList*/, dailyTotalDemand, available2Distribute);
//        return BigDecimal.ZERO;
//    }
//
//
//    /**
//     * 可供分配量 >= 当天需求量:够分
//     *
//     * @param deptActualMap    二级部门ID->本次任务分配量
//     * @param dailyDemandList  部门日需求列表
//     * @param modifyDemandList 被变更的日需求列表
//     */
//    private void enough2Distribute(Map<Long, BigDecimal> deptActualMap,
//                                   List<SgCDepartmentMonthDemand> dailyDemandList/*, List<SgCDepartmentMonthDemand> modifyDemandList*/) {
//        for (SgCDepartmentMonthDemand demand : dailyDemandList) {
//            BigDecimal actualQty = demand.getDemandQty().subtract(demand.getActualInQty());
//            /*demand.setActualInQty(demand.getDemandQty());
//            demand.setStatus(SgMonthDemandStatusEnum.COMPLETED.getValue());
//            modifyDemandList.add(demand);*/
//
//            deptActualMap.put(demand.getCStoreattrib2Id(),
//                    deptActualMap.getOrDefault(demand.getCStoreattrib2Id(), BigDecimal.ZERO).add(actualQty));
//        }
//    }
//
//    /**
//     * 按比例：不够分/全部分配
//     * <br/>
//     * 可供分配量 < 当天需求量:不够分
//     * <br/>
//     * 可供分配量 > 当天需求量：全部分配
//     *
//     * @param deptActualMap        二级部门ID->本次任务分配量
//     * @param dailyDemandList      部门日需求列表
//     * @param modifyDemandList     被变更的日需求列表
//     * @param dailyTotalDemand     当天需求总量
//     * @param available2Distribute 当前剩余可分配总量
//     */
//    private void proportion2Distribute(Map<Long, BigDecimal> deptActualMap,
//                                       List<SgCDepartmentMonthDemand> dailyDemandList, /*List<SgCDepartmentMonthDemand> modifyDemandList,*/
//                                       BigDecimal dailyTotalDemand, BigDecimal available2Distribute) {
//        /*剩余可分配量，防止超分*/
//        BigDecimal curRemain = available2Distribute;
//        for (int i = 0; i < dailyDemandList.size(); i++) {
//            SgCDepartmentMonthDemand demand = dailyDemandList.get(i);
//
//            BigDecimal calculateRet;
//            /*分配到最后一个，剩下的量全给它*/
//            if (i == dailyDemandList.size() - 1) {
//                calculateRet = curRemain;
//            } else {
//                /*分配量 = （可分配的量 × 当前需求量（即：需求量-实际分配量））÷ 当天总需求量 */
//                calculateRet = available2Distribute.multiply(demand.getDemandQty().subtract(demand.getActualInQty()))
//                        .divide(dailyTotalDemand, RoundingMode.HALF_UP).setScale(0, RoundingMode.HALF_UP);
//            }
//            if (BigDecimal.ZERO.compareTo(calculateRet) == 0) {
//                continue;
//            }
//            /*当前剩余小于计算分配量 则 将当前剩余全给到计算分配,防止不够（四舍五入有可能超出剩余量）*/
//            if (curRemain.compareTo(calculateRet) < 0) {
//                calculateRet = curRemain;
//            }
//
//            /*变更实际入库量 = 当前入库量 + 本次计算分配量*/
//            /*BigDecimal actualQty = demand.getActualInQty().add(calculateRet);*/
//            /*demand.setActualInQty(actualQty);
//            Integer status = demand.getActualInQty().compareTo(demand.getDemandQty()) >= 0 ?
//                    SgMonthDemandStatusEnum.COMPLETED.getValue() :
//                    SgMonthDemandStatusEnum.PARTIALLY_DONE.getValue();
//            demand.setStatus(status);*/
//            /*modifyDemandList.add(demand);*/
//
//            /*部门实际入库量*/
//            deptActualMap.put(demand.getCStoreattrib2Id(),
//                    deptActualMap.getOrDefault(demand.getCStoreattrib2Id(), BigDecimal.ZERO).add(calculateRet));
//            curRemain = curRemain.subtract(calculateRet);
//            if (BigDecimal.ZERO.compareTo(curRemain) >= 0) {
//                break;
//            }
//        }
//    }
//
//
//    /**
//     * 计算配销仓实际分配量
//     * <br/>
//     * 平均分配
//     *
//     * @param deptSaIdsMap     二级部门ID->配销仓ID列表 映射
//     * @param deptActualMap    二级部门ID->本次分配总量 映射
//     * @param storageBeforeMap 配销仓ID->分配前可用库存 映射
//     * @return 配销仓ID->本次任务实际分配量 映射
//     */
//    private Map<Long, BigDecimal> calculateSaActualStorageByAverage(Map<Long, Set<Long>> deptSaIdsMap, Map<Long, BigDecimal> deptActualMap, Map<Long, BigDecimal> storageBeforeMap) {
//        Map<Long, BigDecimal> saActualMap = new HashMap<>();
//        for (Long deptId : deptActualMap.keySet()) {
//            /*该部门下，本次分配的总量*/
//            BigDecimal deptTotalAfter = deptActualMap.get(deptId);
//
//            /*该部门下，所有的配销仓ID*/
//            Set<Long> saStoreIds = deptSaIdsMap.get(deptId);
//            /*该部门下仅一个配销仓*/
//            if (saStoreIds.size() == 1) {
//                saActualMap.put(saStoreIds.iterator().next(), deptTotalAfter);
//                continue;
//            }
//
//            /*配销仓-实际分配的量 = (本次分配的总量) ÷ 配销仓个数*/
//            BigDecimal actual = deptTotalAfter
//                    .divide(new BigDecimal(saStoreIds.size()), RoundingMode.HALF_UP)
//                    .setScale(0, RoundingMode.HALF_UP);
//
//            BigDecimal remain = deptTotalAfter;
//            for (Long saStoreId : saStoreIds) {
//                if (BigDecimal.ZERO.compareTo(remain) == 0) {
//                    break;
//                }
//                /*配销仓-分配之前的量*/
//                if (remain.compareTo(actual) < 0) {
//                    actual = remain;
//                }
//                remain = remain.subtract(actual);
//
//                saActualMap.put(saStoreId, actual);
//            }
//        }
//        return saActualMap;
//    }
//
//    /**
//     * 获取过期毫秒数
//     *
//     * @return 当前时间距离本月最后一毫秒的毫秒数
//     */
//    private long genExpireMilliseconds() {
//        long currentTimeMillis = System.currentTimeMillis();
//        Calendar calendar = Calendar.getInstance();
//        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
//        calendar.set(Calendar.HOUR_OF_DAY, 23);
//        calendar.set(Calendar.MINUTE, 59);
//        calendar.set(Calendar.SECOND, 59);
//        calendar.set(Calendar.MILLISECOND, 999);
//        return calendar.getTimeInMillis() - currentTimeMillis;
//    }
//
//
//    /**
//     * 获取Bean
//     *
//     * @return 返回自己
//     */
//    public abstract SgS2SaAutoDistributionAbstractTemplate getBean();
//
//    /**
//     * 获取同一聚合仓下可用于分配的库存
//     * <br/>
//     * 月度：聚合仓下所有配销仓与可用库存 映射
//     * <br/>
//     * 定时：聚合仓下所有逻辑仓可用库存汇总
//     *
//     * @param saStores 配销仓列表
//     * @param skuId    商品ID
//     * @return 仓ID（定时：聚合仓ID）（月出：聚合仓ID）->可用库存 映射
//     */
//    public abstract Map<Long, BigDecimal> queryStorageMap(List<SgCSaStore> saStores, Long skuId);
//
//
//    /**
//     * 计算配销仓实际分配量
//     * <br/>
//     * 月度：按原配销仓库存比例分配
//     * <br/>
//     * 定时：平均分配
//     * <br/>
//     * <br/>
//     * <b>现在全是平均分配了{@link SgS2SaAutoDistributionAbstractTemplate#calculateSaActualStorageByAverage(Map, Map, Map)}<b/>
//     *
//     * @param deptSaIdsMap     二级部门ID->配销仓ID列表 映射
//     * @param deptActualMap    二级部门ID->本次分配总量 映射
//     * @param storageBeforeMap 配销仓ID->分配前可用库存 映射
//     * @return 配销仓ID->本次任务实际分配量 映射
//     */
//    @Deprecated
//    public abstract Map<Long, BigDecimal> calculateSaActualStorage(Map<Long, Set<Long>> deptSaIdsMap,
//                                                                   Map<Long, BigDecimal> deptActualMap,
//                                                                   Map<Long, BigDecimal> storageBeforeMap);
//
//    /**
//     * 执行分货：分发单据，变更库存
//     *
//     * @param shareStoreId     共享仓ID
//     * @param skuId            需要处理的商品ID
//     * @param saActualMap      配销仓ID->本次任务实际分配量 映射
//     * @param storageBeforeMap 配销仓ID->分配前可用库存
//     * @return 配销仓ID->配销仓更新满足量
//     */
//    public abstract Map<Long, BigDecimal> doDistribute(Long shareStoreId,
//                                                       Long skuId,
//                                                       Map<Long, BigDecimal> saActualMap,
//                                                       Map<Long, BigDecimal> storageBeforeMap);
//
//    /**
//     * 更新二级部门需求表
//     *
//     * @param skuId            需要处理的商品ID
//     * @param deptSaIdsMap     部门ID->配销仓ID列表 映射
//     * @param saIdDemandQtyMap 需要更新需求表的量[配销仓ID->配销仓更新满足量]
//     */
//    protected abstract void modifyDemandList(Long skuId, Map<Long, Set<Long>> deptSaIdsMap,
//                                             Map<Long, BigDecimal> saIdDemandQtyMap);
//
//    /**
//     * 生成日志前缀（区分分配策略）
//     *
//     * @return 日志前缀
//     */
//    public abstract String genLogPrefix();
//
//    /**
//     * 是否月初分货
//     *
//     * @return 是/否
//     */
//    public abstract boolean isMonthDistribution();
//}
