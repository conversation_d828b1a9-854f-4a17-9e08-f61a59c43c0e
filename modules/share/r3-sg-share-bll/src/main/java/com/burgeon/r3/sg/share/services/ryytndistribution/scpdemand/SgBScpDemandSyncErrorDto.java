package com.burgeon.r3.sg.share.services.ryytndistribution.scpdemand;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-07-05 15:50
 */
@Data
public class SgBScpDemandSyncErrorDto {
    private Integer rowNum;
    private String scpEcode;
    private String errMsg;

    public SgBScpDemandSyncErrorDto(Integer rowNum, String scpEcode, String errMsg) {
        this.rowNum = rowNum;
        this.scpEcode = scpEcode;
        this.errMsg = errMsg;
    }
}
