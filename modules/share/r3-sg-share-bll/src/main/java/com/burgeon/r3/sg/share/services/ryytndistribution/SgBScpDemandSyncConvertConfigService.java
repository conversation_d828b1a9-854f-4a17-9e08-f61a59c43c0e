package com.burgeon.r3.sg.share.services.ryytndistribution;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgBScpDemandSyncConvertConfig;
import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgBScpDemandSyncConvertConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 净需求计算拆组包配置
 *
 * <AUTHOR>
 * @since 2024-12-27 10:35
 */
@Slf4j
@Service
public class SgBScpDemandSyncConvertConfigService {
    @Resource
    private SgBScpDemandSyncConvertConfigMapper sgBScpDemandSyncConvertConfigMapper;

    /**
     * 根据ID查询，未过滤作废状态的数据
     *
     * @param ids 主键，注意这里参数个数
     * @return 拆包配置
     */
    public List<SgBScpDemandSyncConvertConfig> queryListByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }

        return sgBScpDemandSyncConvertConfigMapper.selectList(new QueryWrapper<SgBScpDemandSyncConvertConfig>().lambda()
                /*.eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)*/
                .in(SgBScpDemandSyncConvertConfig::getId, ids));
    }
}
