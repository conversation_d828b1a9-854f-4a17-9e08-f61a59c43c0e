package com.burgeon.r3.sg.share.services.ryytndistribution;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.aliyuncs.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgBCommonStorageQtyQueryResult;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgBScpDemandDailyReportExt;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgBScpDemandDailyReportExtMapper;
import com.burgeon.r3.sg.share.services.ryytndistribution.dto.SgBScpDemandDailyReportQueryData;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 分货日报表计算辅助列
 *
 * <AUTHOR>
 * @since 2024-07-17 16:00
 */
@Slf4j
@Component
public class SgBScpDemandDailyReportExtService {
    private final static String LOG_OBJ = "SgBScpDemandDailyReportExtService.";

    @Resource
    private SgBScpDemandDailyReportExtMapper sgBScpDemandDailyReportExtMapper;


    @Resource
    private SgCMonthProductionPlanService sgCMonthProductionPlanService;

    @NacosValue(value = "${r3.sg.adb.scsg:test_rpt_sc_sg}", autoRefreshed = true)
    private String sgAdbPrefix;


    /**
     * 计算并刷新 实际入/计划入
     *
     * @param isFirst   是否第一次计算：不是第一次计算会把之前的数据作废
     * @param versionBi 版本号
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param skuEcodes skuId列表
     * @return 查询结果封装，包含两个qtyMap
     */
    public SgBScpDemandDailyReportQueryData refreshAndQueryQtyMap(String versionBi, Date startDate, final Date endDate,
                                                                  List<String> skuEcodes) {
        if (StringUtils.isEmpty(versionBi) || CollectionUtils.isEmpty(skuEcodes)) {
            log.warn(LogUtil.format("版本号或SKUID不能为空", LOG_OBJ + "refresh"), versionBi, skuEcodes);
            return null;
        }

        /*把之前的计算结果作废*/
        SgBScpDemandDailyReportExt noActive = new SgBScpDemandDailyReportExt();
        noActive.setIsactive(SgConstants.IS_ACTIVE_N);
        StorageUtils.setBModelDefalutDataByUpdate(noActive, R3SystemUserResource.getSystemRootUser());
        sgBScpDemandDailyReportExtMapper.update(noActive, new UpdateWrapper<SgBScpDemandDailyReportExt>().lambda()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgBScpDemandDailyReportExt::getVersionBi, versionBi));

        /*有计划生产入库的逻辑仓编码*/
        List<String> factoryCodes = sgCMonthProductionPlanService.queryAllFactoryCode();
        /*出保天数：仓、品：天数*/
        List<SgBCommonStorageQtyQueryResult> productionDays = sgBScpDemandDailyReportExtMapper.selectProductionDays(startDate);
        Map<String, Integer> productionDaysMap = ListUtils.emptyIfNull(productionDays).stream()
                .collect(Collectors.toMap(o -> o.getCpCStoreEcode() + "_" + o.getPsCSkuEcode(), SgBCommonStorageQtyQueryResult::getProductionDays, (a, b) -> a));

        /*实际入库量*/
        List<SgBCommonStorageQtyQueryResult> actualQtyList = queryActualQty(sgAdbPrefix, DateUtil.format(startDate, "yyyyMMdd"), factoryCodes, skuEcodes);

        /*加上出保天数*/
        actualQtyList = ListUtils.emptyIfNull(actualQtyList).stream()
                .peek(o -> o.setPlanDate(DateUtils.addDays(DateUtil.parse(o.getProduceDate(), "yyyyMMdd"),
                        productionDaysMap.getOrDefault(o.getCpCStoreEcode() + "_" + o.getPsCSkuEcode(), 0))))
                .collect(Collectors.toList());
        /*按SKU分组*/
        Map<String, List<SgBCommonStorageQtyQueryResult>> ecodeActualMap = ListUtils.emptyIfNull(actualQtyList).stream()
                .collect(Collectors.groupingBy(SgBCommonStorageQtyQueryResult::getPsCSkuEcode));

        /*生产计划量*/
        List<SgBCommonStorageQtyQueryResult> planQtyList = queryPlanQty(sgAdbPrefix, DateUtil.format(startDate, "yyyy-MM-dd"), skuEcodes);
        /*按SKU分组*/
        Map<String, List<SgBCommonStorageQtyQueryResult>> ecodePlanMap = ListUtils.emptyIfNull(planQtyList).stream()
                .collect(Collectors.groupingBy(SgBCommonStorageQtyQueryResult::getPsCSkuEcode));

        /*ECODE转SKU*/
        Map<String, Long> skuEcodeIdMap = new HashMap<>();
        skuEcodeIdMap.putAll(ListUtils.emptyIfNull(actualQtyList).stream()
                .collect(Collectors.toMap(SgBCommonStorageQtyQueryResult::getPsCSkuEcode, SgBCommonStorageQtyQueryResult::getPsCSkuId, (a, b) -> a)));
        skuEcodeIdMap.putAll(ListUtils.emptyIfNull(planQtyList).stream()
                .collect(Collectors.toMap(SgBCommonStorageQtyQueryResult::getPsCSkuEcode, SgBCommonStorageQtyQueryResult::getPsCSkuId, (a, b) -> a)));


        List<SgBScpDemandDailyReportExt> extList = new ArrayList<>();
        Map<String, Map<Date, BigDecimal>> actualQtyMapBySku = new HashMap<>();
        Map<String, Map<Date, BigDecimal>> planQtyMapBySku = new HashMap<>();
        for (String skuEcode : skuEcodes) {
            Long psCSkuId = skuEcodeIdMap.get(skuEcode);
            if (Objects.isNull(psCSkuId)) {
                log.info(LogUtil.format("SKU编码不存在,重新查询,SKUECODE:{}", LOG_OBJ + "refresh"), skuEcode);
                List<PsCSku> psCSkus = CommonCacheValUtils.querySkuListByCode(Collections.singletonList(skuEcode));
                if (CollectionUtils.isEmpty(psCSkus)) {
                    log.warn(LogUtil.format("SKU编码不存在,且根据SKU编码未查询到SKUID,SKUECODE:{}", LOG_OBJ + "refresh"), skuEcode);
                    continue;
                }
                skuEcodeIdMap.put(skuEcode, psCSkus.get(0).getId());
            }

            List<SgBCommonStorageQtyQueryResult> actualQtyListBySku = ecodeActualMap.get(skuEcode);
            List<SgBCommonStorageQtyQueryResult> planQtyListBySku = ecodePlanMap.get(skuEcode);
            if (CollectionUtils.isEmpty(actualQtyListBySku) && CollectionUtils.isEmpty(planQtyListBySku)) {
                continue;
            }

            Map<Date, BigDecimal> actualQtyMap = ListUtils.emptyIfNull(actualQtyListBySku).stream()
                    .collect(Collectors.toMap(SgBCommonStorageQtyQueryResult::getPlanDate,
                            SgBCommonStorageQtyQueryResult::getQty,
                            BigDecimal::add));

            Map<Date, BigDecimal> planQtyMap = ListUtils.emptyIfNull(planQtyListBySku).stream()
                    .collect(Collectors.toMap(SgBCommonStorageQtyQueryResult::getPlanDate, SgBCommonStorageQtyQueryResult::getQty));

            actualQtyMapBySku.put(skuEcode, actualQtyMap);
            planQtyMapBySku.put(skuEcode, planQtyMap);

            Date date = startDate;
            while (date.before(endDate)) {
                if ((!actualQtyMap.containsKey(date)) && (!planQtyMap.containsKey(date))) {
                    date = DateUtils.addDays(date, 1);
                    continue;
                }
                SgBScpDemandDailyReportExt ext = new SgBScpDemandDailyReportExt();
                ext.setVersionBi(versionBi);
                ext.setPlanDate(date);

                ext.setPsCSkuId(psCSkuId);
                ext.setPsCSkuEcode(skuEcode);

                ext.setActualQty(actualQtyMap.getOrDefault(date, BigDecimal.ZERO));
                ext.setPlanQty(planQtyMap.getOrDefault(date, BigDecimal.ZERO));

                StorageUtils.setBModelDefalutData(ext, R3SystemUserResource.getSystemRootUser());
                extList.add(ext);

                date = DateUtils.addDays(date, 1);
            }
        }
        StorageBasicUtils.batchInsertList(sgBScpDemandDailyReportExtMapper, extList, 1000,
                "批量创建分货日报表计算辅助列异常", R3SystemUserResource.getSystemRootUser());

        SgBScpDemandDailyReportQueryData queryData = new SgBScpDemandDailyReportQueryData();
        queryData.setActualQtyMapBySku(actualQtyMapBySku);
        queryData.setPlanQtyMapBySku(planQtyMapBySku);
        queryData.setSkuEcodeIdMap(skuEcodeIdMap);
        return queryData;
    }

    private List<SgBCommonStorageQtyQueryResult> queryPlanQty(String sgAdbPrefix, String startDate, List<String> skuEcodes) {
        try {
            SgBScpDemandDailyReportExtService bean = ApplicationContextHandle.getBean(SgBScpDemandDailyReportExtService.class);
            Future<List<SgBCommonStorageQtyQueryResult>> listFuture = bean.queryPlanQtyByAdb(sgAdbPrefix, startDate, skuEcodes);
            return listFuture.get();
        } catch (Exception e) {
            log.error(LogUtil.format("计划入库量-异步使用ADb查询库存失败，异常：{}",
                    LOG_OBJ + "queryPlanQty"), Throwables.getStackTraceAsString(e));
            throw new NDSException("计划入库量-异步使用ADb查询库存失败：" + e.getMessage());
        }
    }

    @TargetDataSource(name = "adb")
    @Async(value = "adbAsyncExecutorPool")
    public Future<List<SgBCommonStorageQtyQueryResult>> queryPlanQtyByAdb(String sgAdbPrefix, String startDate, List<String> skuEcodes) {
        return AsyncResult.forValue(sgBScpDemandDailyReportExtMapper.selectPlanQty(sgAdbPrefix, startDate, skuEcodes));
    }

    private List<SgBCommonStorageQtyQueryResult> queryActualQty(String sgAdbPrefix, String startDate, List<String> factoryCodes, List<String> skuEcodes) {
        try {
            SgBScpDemandDailyReportExtService bean = ApplicationContextHandle.getBean(SgBScpDemandDailyReportExtService.class);
            Future<List<SgBCommonStorageQtyQueryResult>> listFuture = bean.queryActualQtyAdb(sgAdbPrefix, startDate, factoryCodes, skuEcodes);
            return listFuture.get();
        } catch (Exception e) {
            log.error(LogUtil.format("实际入库量-异步使用ADb查询库存失败，异常：{}",
                    LOG_OBJ + "queryActualQty"), Throwables.getStackTraceAsString(e));
            throw new NDSException("实际入库量-异步使用ADb查询库存失败：" + e.getMessage());
        }
    }

    @TargetDataSource(name = "adb")
    @Async(value = "adbAsyncExecutorPool")
    public Future<List<SgBCommonStorageQtyQueryResult>> queryActualQtyAdb(String sgAdbPrefix, String startDate, List<String> factoryCodes, List<String> skuEcodes) {
        return AsyncResult.forValue(sgBScpDemandDailyReportExtMapper.selectActualQty(sgAdbPrefix, startDate, factoryCodes, skuEcodes));
    }

}
