package com.burgeon.r3.sg.share.services.ryytndistribution;

import com.aliyuncs.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.sg.basic.utils.DingTalkUtil;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.SgCPlanConvertStatusEnum;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCPlanConvertVersion;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgCPlanConvertVersionMapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @since 2023-11-14 14:51
 */
@Slf4j
@Service
public class SgCPlanConvertVersionService {
    /**
     * 手工创建的需求固定周数
     */
    public static final String MONTH_WEEK_BY_HAND = "BY_USER";

    @Resource
    private SgCPlanConvertVersionMapper sgCPlanConvertVersionMapper;

    public List<SgCPlanConvertVersion> selectByStatus(SgCPlanConvertStatusEnum statusEnum) {
        List<SgCPlanConvertVersion> list = sgCPlanConvertVersionMapper.selectList(new QueryWrapper<SgCPlanConvertVersion>().lambda()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCPlanConvertVersion::getStatus, statusEnum.getValue())
                .orderByDesc(SgCPlanConvertVersion::getId));

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    public SgCPlanConvertVersion selectActive() {
        List<SgCPlanConvertVersion> list = sgCPlanConvertVersionMapper.selectList(new QueryWrapper<SgCPlanConvertVersion>().lambda()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCPlanConvertVersion::getStatus, SgCPlanConvertStatusEnum.ACTIVE.getValue()));
        if (CollectionUtils.isEmpty(list)) {
            throw new NDSException("未找到激活状态的完结标识");
        }

        if (list.size() > 1) {
            DingTalkUtil.sendTextMsg("激活状态的完结标识条数非法");
            throw new NDSException("激活状态的完结标识条数非法");
        }

        SgCPlanConvertVersion activeVersion = list.get(0);
        if (Objects.isNull(activeVersion)
                || StringUtils.isEmpty(activeVersion.getVersionBi())) {
            DingTalkUtil.sendTextMsg("激活状态的完结标识数据错误");
            throw new NDSException("激活状态的完结标识数据错误");
        }
        return activeVersion;
    }

    public SgCPlanConvertVersion disableActiveVersion() {
        SgCPlanConvertVersion oldActive = null;
        List<SgCPlanConvertVersion> oldActives = selectByStatus(SgCPlanConvertStatusEnum.ACTIVE);
        if (CollectionUtils.isEmpty(oldActives)) {
            return null;
        }

        if (!CollectionUtils.isEmpty(oldActives)) {
            oldActive = oldActives.get(0);
        }

        SgCPlanConvertVersion entity = new SgCPlanConvertVersion();
        StorageUtils.setBModelDefalutDataByUpdate(entity, SystemUserResource.getRootUser());
        entity.setStatus(SgCPlanConvertStatusEnum.EXPIRED.getValue());

        sgCPlanConvertVersionMapper.update(entity, new LambdaUpdateWrapper<SgCPlanConvertVersion>()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCPlanConvertVersion::getVersionBi, oldActive.getVersionBi()));

        return oldActive;
    }

    public void activeNewVersion(SgCPlanConvertVersion newFlag) {
        SgCPlanConvertVersion entity = new SgCPlanConvertVersion();
        StorageUtils.setBModelDefalutDataByUpdate(entity, SystemUserResource.getRootUser());
        entity.setRemark(newFlag.getRemark());
        entity.setStatus(SgCPlanConvertStatusEnum.ACTIVE.getValue());
        entity.setDemandCount(newFlag.getDemandCount());

        sgCPlanConvertVersionMapper.update(entity, new LambdaUpdateWrapper<SgCPlanConvertVersion>()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCPlanConvertVersion::getStatus, SgCPlanConvertStatusEnum.IS_NEW.getValue())
                .eq(SgCPlanConvertVersion::getVersionBi, newFlag.getVersionBi()));
    }

    public void save(SgCPlanConvertVersion version) {
        StorageUtils.setBModelDefalutData(version, SystemUserResource.getRootUser());
        sgCPlanConvertVersionMapper.insert(version);
    }


    /**
     * 禁用其他版本
     *
     * @param excludeId 需要被激活的版本ID
     */
    public void disableOtherVersion(Long excludeId) {
        if (Objects.isNull(excludeId)) {
            return;
        }
        SgCPlanConvertVersion entity = new SgCPlanConvertVersion();
        StorageUtils.setBModelDefalutDataByUpdate(entity, SystemUserResource.getRootUser());
        entity.setRemark("[非最新版本自动作废]" + entity.getRemark());
        entity.setIsactive(SgConstants.IS_ACTIVE_N);

        sgCPlanConvertVersionMapper.update(entity, new LambdaUpdateWrapper<SgCPlanConvertVersion>()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCPlanConvertVersion::getStatus, SgCPlanConvertStatusEnum.IS_NEW.getValue())
                .ne(SgCPlanConvertVersion::getId, excludeId));
    }

    public SgCPlanConvertVersion selectByVersionBi(String versionBi) {
        List<SgCPlanConvertVersion> list = sgCPlanConvertVersionMapper.selectList(new QueryWrapper<SgCPlanConvertVersion>().lambda()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCPlanConvertVersion::getVersionBi, versionBi));
        if (CollectionUtils.isEmpty(list) || list.size() > 1) {
            log.warn(LogUtil.format("版本数据非法，版本号:{}",
                    "SgCPlanConvertVersionService.selectByVersionBi"), versionBi);
            throw new NDSException("版本数据非法");
        }

        return list.get(0);
    }

    public void updateById(SgCPlanConvertVersion versionInfo) {
        sgCPlanConvertVersionMapper.updateById(versionInfo);
    }
}
