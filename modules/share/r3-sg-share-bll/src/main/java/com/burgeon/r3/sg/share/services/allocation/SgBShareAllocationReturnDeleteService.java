package com.burgeon.r3.sg.share.services.allocation;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationReturn;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationReturnItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationReturnItemMapper;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationReturnMapper;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnDeleteRequest;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/21 13:26
 */
@Slf4j
@Component
public class SgBShareAllocationReturnDeleteService {
    @Autowired
    SgBShareAllocationReturnMapper mapper;
    @Autowired
    SgBShareAllocationReturnItemMapper itemMapper;

    ValueHolder delete(QuerySession session) {
        SgBShareAllocationReturnDeleteRequest request = R3ParamUtils.parseSaveObject(session, SgBShareAllocationReturnDeleteRequest.class);
        request.setR3(true);
        SgBShareAllocationReturnDeleteService service = ApplicationContextHandle.getBean(SgBShareAllocationReturnDeleteService.class);
        return R3ParamUtils.convertV14WithResult(service.delete(request));
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> delete(SgBShareAllocationReturnDeleteRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBStoTransfeDeleteService.delete. param={}", JSONObject.toJSONString(request));
        }
        String lockKey = SgConstants.SG_B_SHARE_ALLOCATION_RETURN + ":" + request.getObjId();
        SgRedisLockUtils.lock(lockKey);
        try {

            SgBShareAllocationReturn allocationReturn = checkParams(request);
            //明细删除
            if (CollectionUtils.isNotEmpty(request.getItemIds())) {
                BigDecimal totQty = allocationReturn.getTotQty();
                BigDecimal totAmt = allocationReturn.getTotAmt();
                Integer totRowNum = allocationReturn.getTotRowNum();
                List<SgBShareAllocationReturnItem> itemList = itemMapper.selectList(new QueryWrapper<SgBShareAllocationReturnItem>()
                        .lambda().in(SgBShareAllocationReturnItem::getId, request.getItemIds()));
                for (SgBShareAllocationReturnItem item : itemList) {
                    if (item.getQty() != null) {
                        totQty = totQty.subtract(item.getQty());
                        totAmt = totAmt.subtract(item.getAmt());
                    }
                }
                itemMapper.delete(new QueryWrapper<SgBShareAllocationReturnItem>()
                        .lambda().in(SgBShareAllocationReturnItem::getId, request.getItemIds()));
                totRowNum = totRowNum - request.getItemIds().size();
                StorageUtils.setBModelDefalutDataByUpdate(allocationReturn, request.getLoginUser());
                allocationReturn.setTotRowNum(totRowNum);
                allocationReturn.setTotAmt(totAmt);
                allocationReturn.setTotQty(totQty);

                mapper.updateById(allocationReturn);
            } else {

                itemMapper.delete(new QueryWrapper<SgBShareAllocationReturnItem>()
                        .lambda().eq(SgBShareAllocationReturnItem::getSgBShareAllocationReturnId, request.getObjId()));
                mapper.delete(new QueryWrapper<SgBShareAllocationReturn>()
                        .lambda().eq(SgBShareAllocationReturn::getId, request.getObjId()));
            }
        } catch (Exception e) {
            AssertUtils.logAndThrowException(e.getMessage(), e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKey, log, this.getClass().getName());

        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "删除成功！");
    }

    public SgBShareAllocationReturn checkParams(SgR3BaseRequest request) {
        SgBShareAllocationReturn allocationReturn = mapper.selectById(request.getObjId());
        if (SgConstants.IS_ACTIVE_N.equalsIgnoreCase(allocationReturn.getIsactive())) {
            AssertUtils.logAndThrow("当前记录已作废,不允许删除！", request.getLoginUser().getLocale());

        } else if (SgShareConstants.BILL_STATUS_UNSUBMIT != allocationReturn.getStatus()) {
            AssertUtils.logAndThrow("当前单据状态不允许删除！", request.getLoginUser().getLocale());
        }
        return allocationReturn;
    }

}
