package com.burgeon.r3.sg.share.validate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareSaTransfer;
import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareSaTransferItem;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareSaTransferItemMapper;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareSaTransferMapper;
import com.burgeon.r3.sg.share.model.dto.SgBShareSaTransferDto;
import com.burgeon.r3.sg.share.model.dto.SgBShareSaTransferItemDto;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleItemValidator;
import com.jackrain.nea.web.face.User;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/6 20:50
 */
@Component
public class SgBShareSaTransferSaveValidate extends BaseSingleItemValidator<SgBShareSaTransferDto, SgBShareSaTransferItemDto> {

    @Autowired
    private SgBShareSaTransferMapper mapper;
    @Autowired
    private SgBShareSaTransferItemMapper itemMapper;
    @Autowired
    private SgCSaStoreMapper sgCSaStoreMapper;

    @Override
    public String getValidatorMsgName() {
        return "配销仓调拨单保存验证";
    }

    @Override
    public Class<?> getValidatorClass() {
        return SgBShareSaTransferSaveValidate.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgBShareSaTransferDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14();
        boolean b = checkMainTable(mainObject, v14);
        if (b) {
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage(Resources.getMessage("保存成功！"));
        }
        return v14;
    }

    @Override
    public ValueHolderV14 validateSubTable(SgBShareSaTransferDto mainObject, List<SgBShareSaTransferItemDto> subObjectList, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("保存成功！"));
        if (subObjectList.size() > 0 && Objects.nonNull(subObjectList.get(0).getId()) && subObjectList.get(0).getId() > 0) {
            LambdaQueryWrapper<SgBShareSaTransferItem> itemLqw = new LambdaQueryWrapper<SgBShareSaTransferItem>()
                    .eq(SgBShareSaTransferItem::getId, subObjectList.get(0).getId());
            SgBShareSaTransferItem sgBShareSaTransferItem = itemMapper.selectOne(itemLqw);
            if (Objects.isNull(sgBShareSaTransferItem)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("当前记录已不存在！"));
                return v14;
            }
        }
        return v14;
    }

    private boolean checkMainTable(SgBShareSaTransferDto mainObject, ValueHolderV14 v14) {
        SgBShareSaTransfer sgBShareSaTransfer = null;

        if (Objects.nonNull(mainObject.getId()) && mainObject.getId() > 0L) {
            sgBShareSaTransfer = mapper.selectById(mainObject.getId());
            v14.setCode(ResultCode.FAIL);

            if (Objects.isNull(sgBShareSaTransfer)) {
                v14.setMessage(Resources.getMessage("当前记录已不存在！"));
                return false;
            }

            if (SgStoreConstants.BILL_STATUS_VOID == sgBShareSaTransfer.getStatus()) {
                v14.setMessage(Resources.getMessage("当前记录已作废，不允许编辑！"));
                return false;
            }

            if (SgStoreConstants.BILL_STATUS_SUBMIT == sgBShareSaTransfer.getStatus()) {
                v14.setMessage(Resources.getMessage("当前记录已审核，不允许编辑！"));
                return false;
            }

            if (Objects.isNull(mainObject.getSenderSaStoreId())) {
                mainObject.setSenderSaStoreId(sgBShareSaTransfer.getSenderSaStoreId());
            }
            if (Objects.isNull(mainObject.getReceiverSaStoreId())) {
                mainObject.setReceiverSaStoreId(sgBShareSaTransfer.getReceiverSaStoreId());
            }
        }

        boolean isSameSaStoreId = Objects.nonNull(mainObject.getSenderSaStoreId()) &&
                Objects.nonNull(mainObject.getReceiverSaStoreId()) &&
                mainObject.getSenderSaStoreId().equals(mainObject.getReceiverSaStoreId());
        if (isSameSaStoreId) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(Resources.getMessage("发货店仓和收货店仓相同，不允许新增！"));
            return false;
        }

        boolean nonNullSaStoreId = Objects.nonNull(mainObject.getSenderSaStoreId()) &&
                Objects.nonNull(mainObject.getReceiverSaStoreId());
        if (nonNullSaStoreId) {
            SgCSaStore sender = sgCSaStoreMapper.selectById(mainObject.getSenderSaStoreId());
            SgCSaStore receiver = sgCSaStoreMapper.selectById(mainObject.getReceiverSaStoreId());
            boolean isSameShareStoreId = Objects.nonNull(sender) &&
                    Objects.nonNull(receiver) &&
                    Objects.nonNull(sender.getSgCShareStoreId()) &&
                    Objects.nonNull(receiver.getSgCShareStoreId()) &&
                    sender.getSgCShareStoreId().equals(receiver.getSgCShareStoreId());
            if (!isSameShareStoreId) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("发货店仓和收货店仓的所属聚合仓不相同，不允许新增！"));
                return false;
            }
        }

        return true;
    }
}
