package com.burgeon.r3.sg.share.services.ryytndistribution;

import com.burgeon.r3.sg.basic.utils.AbstractDingTalkTableRobot;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCDepartmentDemandPlan;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 部门日需求计划报表服务
 *
 * <AUTHOR>
 * @since 2022-10-26 17:28
 */
@Slf4j
@Component
@Deprecated
public class SgCDeptDemandPlanReportService {
    /**
     * 日志OBJ
     */
    private static final String LOG_OBJ = "SgCDeptDemandPlanReportService.";

    @Resource
    private SgCDepartmentDemandPlanService sgCDepartmentDemandPlanService;

    @Resource
    private SgS2SaAutoDistributionManager sgS2SaAutoDistributionManager;
    @Resource
    private SgCDepartmentMonthDemandService sgCDepartmentMonthDemandService;
    @Resource
    private SgCMonthProductionPlanService sgCMonthProductionPlanService;


    /**
     * 执行任务
     *
     * @param exeDate 需要计算的日期
     * @return 任务返回信息
     */
    public ValueHolderV14<Void> execute(Date exeDate) {
        SgCDeptDemandPlanReportService service = ApplicationContextHandle.getBean(SgCDeptDemandPlanReportService.class);
        int successSkuCnt = 0;
        int totalSkuCount = 0;
        AbstractDingTalkTableRobot<Map<String, Object>> robot = new AbstractDingTalkTableRobot<Map<String, Object>>() {
            @Override
            protected LinkedHashMap<String, Function<Map<String, Object>, Object>> genKeyMap() {
                LinkedHashMap<String, Function<Map<String, Object>, Object>> keyMap = new LinkedHashMap<>();
                keyMap.put("商品ID", map -> map.get("skuId"));
                keyMap.put("错误信息", map -> map.get("errMsg"));
                return keyMap;
            }
        };

        Set<Long> skuIds = sgCDepartmentMonthDemandService.querySkuByVersionBi(DateFormatUtils.format(exeDate, "yyyyMM"));
        totalSkuCount += skuIds.size();

        /*逐个SKU处理*/
        for (Long skuId : skuIds) {
            /*逐个SKU处理*/
            try {
                service.executeBySku(skuId, exeDate);
                successSkuCnt++;
            } catch (Exception e) {
                log.error(LogUtil.format("部门日需求计划报表SKU级别执行出错，商品ID：{},执行时间：{},错误信息:{}", LOG_OBJ + "execute"),
                        skuId, exeDate, Throwables.getStackTraceAsString(e));

                Map<String, Object> errMsgMap = new HashMap<>();
                errMsgMap.put("skuId", skuId);
                errMsgMap.put("errMsg", e.getMessage());
                /*添加到告警信息*/
                robot.addMsg(errMsgMap);
            }
        }

        robot.sendTableMsg();
        if (successSkuCnt != totalSkuCount) {
            log.warn(LogUtil.format("部门日需求计划报表任务部分成功部分失败，成功SKU数：{},总SKU数：{}", LOG_OBJ + "execute"), successSkuCnt, totalSkuCount);
        }

        /*发送钉钉告警*/
        return new ValueHolderV14<>(ResultCode.SUCCESS, "部门日需求计划任务执行！,成功SKU数：" + successSkuCnt + "/" + totalSkuCount);
    }

    /**
     * 逐个SKU处理
     *
     * @param skuId   商品ID
     * @param exeDate 执行日期
     */
    @Transactional(rollbackFor = Throwable.class)
    public void executeBySku(Long skuId, Date exeDate) {
        /*获取所有提报了需求的部门*/
        Set<Long> deptIds = sgCDepartmentMonthDemandService.queryDeptByMonth(DateFormatUtils.format(exeDate, "yyyyMM"), skuId);
        /*获取所有提报了需求的 部门->部门下配销仓ID 映射*/
        Map<Long, Set<Long>> deptSaStoreIdsMap = sgS2SaAutoDistributionManager.queryDeptSaStoreIdsMap(deptIds);

        List<SgCDepartmentDemandPlan> allDeptPlanList = new ArrayList<>();
        /*逐个部门处理*/
        for (Map.Entry<Long, Set<Long>> deptSaIdsEntry : deptSaStoreIdsMap.entrySet()) {
            /*逐个部门处理*/
            /*创建或查询某个部门某个商品的日需求计划明细-当天和当天之后（月结前）*/
            List<SgCDepartmentDemandPlan> demandPlanList = sgCDepartmentDemandPlanService.createIfAbsentDemandPlanList(skuId, deptSaIdsEntry.getKey(), exeDate);

            /*部门级别：计算实际入库量、销售出库量、需求量*/
            calculateByDept(skuId, exeDate, deptSaIdsEntry.getKey(), deptSaIdsEntry.getValue(), demandPlanList);
            allDeptPlanList.addAll(demandPlanList);
        }

        /*计算计划入库量*/
        calculatePlanQty(skuId, exeDate, allDeptPlanList);

        /*计算期初和结余*/
        calculateBeforeQty(allDeptPlanList, exeDate);

        /*创建或修改日需求计划明细*/
        sgCDepartmentDemandPlanService.createOrModify(allDeptPlanList, skuId, deptSaStoreIdsMap.keySet(), exeDate);

//        if ("local".equals(System.getProperty("env"))) {
//            throw new RuntimeException("本地环境测试需要回滚数据");
//        }
    }

    /**
     * 逐个部门处理
     *
     * @param skuId          商品ID
     * @param exeDate        执行日期
     * @param deptId         部门ID
     * @param saStoreIds     配销仓ID列表
     * @param demandPlanList 该部门下报表列表
     * @return 部门下所有日需求计划明细
     */
    private void calculateByDept(Long skuId, Date exeDate, Long deptId, Set<Long> saStoreIds, List<SgCDepartmentDemandPlan> demandPlanList) {
        /*根据分货单获取当天实际入库量-ADB*/
        BigDecimal actualQty = sgCDepartmentDemandPlanService.queryAllocationQty(skuId, saStoreIds, exeDate);
        /*根据配销占用单获取当月的实际出库量、占用量-ADB*/
        Map<Date, Map<Integer, BigDecimal>> dateStatusQtyMap = sgCDepartmentDemandPlanService.queryShareOut(skuId, saStoreIds, exeDate);
        /*日期->需求量 映射*/
        Map<Date, BigDecimal> dateDemandQtyMap = sgCDepartmentMonthDemandService.queryDateDemandQtyMap(skuId, deptId, DateFormatUtils.format(exeDate, "yyyyMM"));

        /*按日期聚合*/
        Map<Date, SgCDepartmentDemandPlan> datePlanMap = demandPlanList.stream()
                .collect(Collectors.toMap(SgCDepartmentDemandPlan::getPlanDate, Function.identity(), (a, b) -> a));

        /*设置：当天的实际入库量、之前的销售出库量*/
        for (Date planDate : datePlanMap.keySet()) {
            SgCDepartmentDemandPlan plan = datePlanMap.get(planDate);
            /*实际入库量-仅更新当天的*/
            if (DateUtils.isSameDay(planDate, exeDate)) {
                plan.setActualQty(actualQty);
            }

            /*销售出库量、占用量-每次刷整个月，因为有可能变更*/
            Map<Integer, BigDecimal> statusQtyMap = dateStatusQtyMap.getOrDefault(planDate, new HashMap<>());
            plan.setSharePreOutQty(statusQtyMap.getOrDefault(1, BigDecimal.ZERO));
            plan.setSaleQty(statusQtyMap.getOrDefault(3, BigDecimal.ZERO));

            /*需求量-每次刷整个月，因为有可能变更*/
            plan.setDemandQty(dateDemandQtyMap.getOrDefault(planDate, BigDecimal.ZERO));
        }
    }

    /**
     * 计算期初和剩余
     *
     * @param allDeptPlanList 日计划明细
     * @param exeDate         执行日期
     */
    private void calculateBeforeQty(List<SgCDepartmentDemandPlan> allDeptPlanList, Date exeDate) {
        /*部门ID->配销仓ID列表*/
        Map<Long, List<SgCDepartmentDemandPlan>> deptPlanList = allDeptPlanList.stream()
                .collect(Collectors.groupingBy(SgCDepartmentDemandPlan::getCpCDistributionOrgId));
        /*逐个部门计算*/
        for (Map.Entry<Long, List<SgCDepartmentDemandPlan>> entry : deptPlanList.entrySet()) {
            /*按天排序，按天计算*/
            List<SgCDepartmentDemandPlan> planList = entry.getValue().stream()
                    .sorted(Comparator.comparing(SgCDepartmentDemandPlan::getPlanDate)).collect(Collectors.toList());
            for (int i = 0; i < planList.size(); i++) {
                SgCDepartmentDemandPlan plan = planList.get(i);
                /*设置期初库存量*/
                if (i == 0) {
                    /*第一天(id不存在则表示是第一次计算)理论上来说是要被全部拉回的，不存在结余，直接写死0*/
                    plan.setRemainQty(BigDecimal.ZERO);
                } else {
                    /*前一天的剩余等于后一天的结余*/
                    plan.setRemainQty(planList.get(i - 1).getBeforeRemainQty());
                }

                /*设置结余库存量*/
                if (plan.getPlanDate().after(exeDate)) {
                    /*今天之后的数据（没有实际入库）：期初库存+生产计划量-销售出库量*/
                    plan.setBeforeRemainQty(plan.getRemainQty().add(plan.getPlanQty()).subtract(plan.getSaleQty()));
                } else {
                    /*今天和今天前的数据（取实际入库）：期初库存+实际入库量-销售出库量*/
                    plan.setBeforeRemainQty(plan.getRemainQty().add(plan.getActualQty()).subtract(plan.getSaleQty()));
                }
            }
        }
    }

    /**
     * 计算计划入库量
     *
     * @param skuId           商品ID
     * @param exeDate         运行日期
     * @param allDeptPlanList 所有部门日需求计划明细
     */
    private void calculatePlanQty(Long skuId, Date exeDate, List<SgCDepartmentDemandPlan> allDeptPlanList) {
        /*按日期排序*/
        List<Date> dateList = allDeptPlanList.stream().map(SgCDepartmentDemandPlan::getPlanDate)
                .distinct().sorted(Date::compareTo).collect(Collectors.toList());
        /*按日期分组*/
        Map<Date, List<SgCDepartmentDemandPlan>> datePlanListMap = allDeptPlanList.stream().collect(Collectors.groupingBy(SgCDepartmentDemandPlan::getPlanDate));
        /*聚合仓月计划生产量*/
        BigDecimal remainQty = sgCMonthProductionPlanService.queryProductPlan(skuId, DateFormatUtils.format(exeDate, "yyyyMM"));
        for (Date date : dateList) {
            List<SgCDepartmentDemandPlan> planList = datePlanListMap.get(date);
            /*剩余量为0，所有都是0*/
            if (remainQty.compareTo(BigDecimal.ZERO) <= 0) {
                for (SgCDepartmentDemandPlan plan : planList) {
                    plan.setPlanQty(BigDecimal.ZERO);
                }
                break;
            }

            BigDecimal todayDemandQty = planList.stream().map(SgCDepartmentDemandPlan::getDemandQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            /*够分*/
            if (remainQty.subtract(todayDemandQty).compareTo(BigDecimal.ZERO) >= 0) {
                for (SgCDepartmentDemandPlan plan : planList) {
                    plan.setPlanQty(plan.getDemandQty());
                }
                remainQty = remainQty.subtract(todayDemandQty);
                continue;
            }

            BigDecimal available2Distribute = remainQty;
            /*不够分*/
            for (int i = 0; i < planList.size(); i++) {
                SgCDepartmentDemandPlan plan = planList.get(i);
                BigDecimal calculateRet;
                /*分配到最后一个，剩下的量全给它*/
                if (i == planList.size() - 1) {
                    calculateRet = remainQty;
                } else {
                    /*按比计算：分配量 = （可分配的量 × 当前需求量）÷ 当天总需求量 */
                    calculateRet = available2Distribute.multiply(plan.getDemandQty())
                            .divide(todayDemandQty, RoundingMode.HALF_UP).setScale(0, RoundingMode.HALF_UP);
                }
                plan.setPlanQty(calculateRet);
                remainQty = remainQty.subtract(calculateRet);
            }
            remainQty = BigDecimal.ZERO;
        }
    }

}
