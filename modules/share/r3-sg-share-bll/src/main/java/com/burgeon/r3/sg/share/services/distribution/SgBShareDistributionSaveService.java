package com.burgeon.r3.sg.share.services.distribution;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.model.table.share.distribution.SgBShareDistribution;
import com.burgeon.r3.sg.core.model.table.share.distribution.SgBShareDistributionItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.distribution.SgBShareDistributionItemMapper;
import com.burgeon.r3.sg.share.mapper.distribution.SgBShareDistributionMapper;
import com.burgeon.r3.sg.share.model.result.distribution.SgBShareDistributionBillSaveRequest;
import com.burgeon.r3.sg.share.model.result.distribution.SgBShareDistributionItemSaveRequest;
import com.burgeon.r3.sg.share.model.result.distribution.SgBShareDistributionSaveRequest;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/18 17:22
 */
@Slf4j
@Component
public class SgBShareDistributionSaveService {

    @Autowired
    SgBShareDistributionMapper mapper;

    @Autowired
    SgBShareDistributionItemMapper itemMapper;

    /**
     * 配货单保存（前端）
     */
    public ValueHolder save(QuerySession session) {
        SgBShareDistributionBillSaveRequest request = R3ParamUtils.parseSaveObject(session, SgBShareDistributionBillSaveRequest.class);
        request.setR3(true);
        SgBShareDistributionSaveService service = ApplicationContextHandle.getBean(SgBShareDistributionSaveService.class);
        return R3ParamUtils.convertV14WithResult(service.save(request));
    }

    /**
     * 配货单保存
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> save(SgBShareDistributionBillSaveRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBShareDistributionSaveService.save:param={}", JSONObject.toJSONString(request));
        }
        SgBShareDistribution distribution = checkParams(request);
        if (request.getObjId() == null || request.getObjId() < 0) {
            return insert(request);
        } else {
            return update(request, distribution);
        }
    }

    /**
     * 配货单新增
     */
    public ValueHolderV14<SgR3BaseResult> insert(SgBShareDistributionBillSaveRequest request) {

        SgBShareDistributionSaveRequest distributionSaveRequest = request.getDistributionSaveRequest();
        SgBShareDistribution insertDistribution = new SgBShareDistribution();
        BeanUtils.copyProperties(distributionSaveRequest, insertDistribution);

        StorageUtils.setBModelDefalutData(insertDistribution, request.getLoginUser());
        List<SgBShareDistributionItemSaveRequest> items = request.getItems();
        BigDecimal totQty = BigDecimal.ZERO;
        BigDecimal totAmt = BigDecimal.ZERO;
        Integer totRowNum = 0;
        Long objId = ModelUtil.getSequence(SgConstants.SG_B_SHARE_DISTRIBUTION);
        insertDistribution.setId(objId);
        insertDistribution.setStatus(SgShareConstants.BILL_STATUS_UNSUBMIT);
        String billNo = SgStoreUtils.getBillNo(SgShareConstants.SEQ_SG_B_SHARE_DISTRIBUTION,
                SgConstants.SG_B_SHARE_DISTRIBUTION.toUpperCase().toUpperCase(),
                insertDistribution, request.getLoginUser().getLocale());
        insertDistribution.setBillNo(billNo);
        List<SgBShareDistributionItem> insertItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(items)) {
            for (SgBShareDistributionItemSaveRequest item : items) {
                CommonCacheValUtils.setSkuInfo(null, item.getPsCSkuEcode(), item);
                SgBShareDistributionItem insertItem = new SgBShareDistributionItem();
                BeanUtils.copyProperties(item, insertItem);
                Long itemObjId = ModelUtil.getSequence(SgConstants.SG_B_SHARE_DISTRIBUTION_ITEM);
                insertItem.setId(itemObjId);
                insertItem.setSgBShareDistributionId(objId);
                if (insertItem.getQty() == null) {
                    insertItem.setQty(BigDecimal.ONE);
                }
                StorageUtils.setBModelDefalutData(insertItem, request.getLoginUser());
                insertItem.setAmt(insertItem.getQty().multiply(insertItem.getPriceList()));
                totQty = totQty.add(insertItem.getQty());
                totAmt = totAmt.add(insertItem.getAmt());
                totRowNum++;
                insertItemList.add(insertItem);
            }
        }
        insertDistribution.setTotRowNum(totRowNum);
        insertDistribution.setTotAmt(totAmt);
        insertDistribution.setTotQty(totQty);
        mapper.insert(insertDistribution);
        /*批量新增500/次*/
        List<List<SgBShareDistributionItem>> insertPageList =
                StorageUtils.getBaseModelPageList(insertItemList, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
        for (List<SgBShareDistributionItem> pageList : insertPageList) {
            if (CollectionUtils.isEmpty(pageList)) {
                continue;
            }
            int insertResult = itemMapper.batchInsert(pageList);
            if (insertResult != pageList.size()) {
                AssertUtils.logAndThrow("保存配货单明细异常！", request.getLoginUser().getLocale());
            }
        }

        SgR3BaseResult baseResult = new SgR3BaseResult();
        baseResult.setDataJo(objId, SgConstants.SG_B_SHARE_DISTRIBUTION.toUpperCase());
        return new ValueHolderV14<>(baseResult, ResultCode.SUCCESS, "保存成功!");
    }

    /**
     * 配货单修改
     */
    public ValueHolderV14<SgR3BaseResult> update(SgBShareDistributionBillSaveRequest request, SgBShareDistribution distribution) {
        String lockKsy = SgConstants.SG_B_SHARE_DISTRIBUTION + ":" + request.getObjId();
        SgRedisLockUtils.lock(lockKsy);
        try {

            SgBShareDistributionSaveRequest distributionSaveRequest = request.getDistributionSaveRequest();
            SgBShareDistribution update = new SgBShareDistribution();
            update.setTotRowNum(distribution.getTotRowNum());
            update.setTotAmt(distribution.getTotAmt());
            update.setTotQty(distribution.getTotQty());
            if (distributionSaveRequest != null) {
                BeanUtils.copyProperties(distributionSaveRequest, update);
            }
            update.setId(request.getObjId());
            update = updateItem(request, update);
            log.info("SgBShareDistributionSaveService.update.end totQty={} totAmt={} totRowNum={}",
                    update.getTotQty(), update.getTotAmt(), update.getTotRowNum());
            update.setId(request.getObjId());

            StorageUtils.setBModelDefalutDataByUpdate(update, request.getLoginUser());
            mapper.updateById(update);
        } catch (Exception e) {
            AssertUtils.logAndThrowException("配货单保存异常", e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, "保存成功！");
    }

    /**
     * 配货单明细修改
     */
    public SgBShareDistribution updateItem(SgBShareDistributionBillSaveRequest request, SgBShareDistribution update) {
        BigDecimal totQty = update.getTotQty();
        BigDecimal totAmt = update.getTotAmt();
        Integer totRowNum = update.getTotRowNum();
        List<SgBShareDistributionItemSaveRequest> items = request.getItems();
        if (CollectionUtils.isNotEmpty(items)) {
            List<SgBShareDistributionItem> itemList = itemMapper.selectList(new QueryWrapper<SgBShareDistributionItem>()
                    .lambda().eq(SgBShareDistributionItem::getSgBShareDistributionId, request.getObjId()));
            Map<String, SgBShareDistributionItem> map = itemList.stream().collect(Collectors.toMap(SgBShareDistributionItem::getPsCSkuEcode, item -> item));
            Map<Long, SgBShareDistributionItem> mapId = itemList.stream().collect(Collectors.toMap(SgBShareDistributionItem::getId, item -> item));
            List<SgBShareDistributionItem> insertItemList = new ArrayList<>();
            for (SgBShareDistributionItemSaveRequest item : items) {
                Long itemId = item.getId();
                if (item.getQty() == null) {
                    item.setQty(BigDecimal.ONE);
                }
                if (item.getQty().compareTo(BigDecimal.ZERO) < 0) {
                    AssertUtils.logAndThrow("条码：" + item.getPsCSkuEcode() + "，数量小于0不允！", request.getLoginUser().getLocale());
                }
                //编辑行编辑，直接覆盖
                if (itemId != null && itemId > 0) {
                    SgBShareDistributionItem queryItem = mapId.get(item.getId());
                    if (queryItem != null) {
                        SgBShareDistributionItem updateItem = new SgBShareDistributionItem();
                        updateItem.setId(queryItem.getId());
                        BigDecimal qty = item.getQty();
                        updateItem.setQty(qty);
                        updateItem.setAmt(updateItem.getQty().multiply(queryItem.getPriceList()));
                        StorageUtils.setBModelDefalutDataByUpdate(updateItem, request.getLoginUser());
                        itemMapper.updateById(updateItem);
                        totAmt = totAmt.add(updateItem.getAmt().subtract(queryItem.getAmt()));
                        totQty = totQty.add(updateItem.getQty().subtract(queryItem.getQty() == null
                                ? BigDecimal.ZERO : queryItem.getQty()));
                    } else {
                        AssertUtils.logAndThrow("当前明细不存在！", request.getLoginUser().getLocale());
                    }
                } else if (map.get(item.getPsCSkuEcode()) != null) {

                    SgBShareDistributionItem queryItem = map.get(item.getPsCSkuEcode());
                    SgBShareDistributionItem updateItem = new SgBShareDistributionItem();

                    updateItem.setId(queryItem.getId());
                    BigDecimal qty = queryItem.getQty().add(item.getQty());
                    //代表矩阵录入需要覆盖
                    if (!StringUtils.isEmpty(item.getPsCProEcode())) {
                        qty = item.getQty();
                    }
                    updateItem.setQty(qty);
                    updateItem.setAmt(updateItem.getQty().multiply(queryItem.getPriceList()));
                    StorageUtils.setBModelDefalutDataByUpdate(updateItem, request.getLoginUser());
                    itemMapper.updateById(updateItem);
                    totAmt = totAmt.add(updateItem.getAmt().subtract(queryItem.getAmt()));
                    totQty = totQty.add(updateItem.getQty().subtract(queryItem.getQty() == null
                            ? BigDecimal.ZERO : queryItem.getQty()));
                } else {

                    CommonCacheValUtils.setSkuInfo(null, item.getPsCSkuEcode(), item);
                    SgBShareDistributionItem insertItem = new SgBShareDistributionItem();
                    BeanUtils.copyProperties(item, insertItem);
                    insertItem.setSgBShareDistributionId(request.getObjId());
                    Long itemObjId = ModelUtil.getSequence(SgConstants.SG_B_SHARE_DISTRIBUTION_ITEM);
                    insertItem.setId(itemObjId);
                    StorageUtils.setBModelDefalutData(insertItem, request.getLoginUser());
                    if (insertItem.getQtyIn() == null) {
                        insertItem.setQtyIn(BigDecimal.ZERO);
                    }
                    if (insertItem.getQtyOut() == null) {
                        insertItem.setQtyOut(BigDecimal.ZERO);
                    }
                    if (insertItem.getQty() == null) {
                        insertItem.setQty(BigDecimal.ONE);
                    }
                    insertItem.setAmtIn(insertItem.getQtyIn().multiply(insertItem.getPriceList()));
                    insertItem.setAmtOut(insertItem.getQtyOut().multiply(insertItem.getPriceList()));
                    insertItem.setAmt(insertItem.getQty().multiply(insertItem.getPriceList()));
                    insertItemList.add(insertItem);
                    totQty = totQty.add(insertItem.getQty());
                    totAmt = totAmt.add(insertItem.getAmt());
                    totRowNum++;
                }
            }

            /*批量新增500/次*/
            List<List<SgBShareDistributionItem>> insertPageList =
                    StorageUtils.getBaseModelPageList(insertItemList, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
            for (List<SgBShareDistributionItem> pageList : insertPageList) {
                if (CollectionUtils.isEmpty(pageList)) {
                    continue;
                }
                int insertResult = itemMapper.batchInsert(pageList);
                if (insertResult != pageList.size()) {
                    AssertUtils.logAndThrow("保存配货单明细异常！", request.getLoginUser().getLocale());
                }
            }
            update.setTotRowNum(totRowNum);
            update.setTotAmt(totAmt);
            update.setTotQty(totQty);

        }
        return update;
    }

    /**
     * 操作前check验证
     */
    public SgBShareDistribution checkParams(SgBShareDistributionBillSaveRequest request) {
        SgBShareDistributionSaveRequest distributionSaveRequest = request.getDistributionSaveRequest();
        SgBShareDistribution distribution = null;

        if (request.getObjId() != null && request.getObjId() > 0) {
            distribution = mapper.selectById(request.getObjId());
            if (distribution == null) {
                AssertUtils.logAndThrow("当前记录已不存在！", request.getLoginUser().getLocale());
            }
            if (SgConstants.IS_ACTIVE_N.equals(distribution.getIsactive())) {
                AssertUtils.logAndThrow("当前单据已作废不允许保存！", request.getLoginUser().getLocale());
            }
            if (SgShareConstants.BILL_STATUS_UNSUBMIT != distribution.getStatus()) {
                AssertUtils.logAndThrow("当前单据状态不允许保存！", request.getLoginUser().getLocale());
            }
        }

        if (distributionSaveRequest != null) {
            if (distributionSaveRequest.getCpCStoreId() != null) {
                CpCStore receiverStore = CommonCacheValUtils.getStoreInfo(distributionSaveRequest.getCpCStoreId());

                if (receiverStore != null) {
                    distributionSaveRequest.setCpCStoreEcode(receiverStore.getEcode());
                    distributionSaveRequest.setCpCStoreEname(receiverStore.getEname());
                }
            }

            if (distributionSaveRequest.getSgCShareStoreId() != null) {

                SgCShareStore shareStore = CommonCacheValUtils.getShareStore(distributionSaveRequest.getSgCShareStoreId());
                if (log.isDebugEnabled()) {
                    log.debug("Start SgBShareDistributionSaveService.shareStore:param={}", JSONObject.toJSONString(shareStore));
                }
                if (shareStore != null) {
                    distributionSaveRequest.setSgCShareStoreEcode(shareStore.getEcode());
                    distributionSaveRequest.setSgCShareStoreEname(shareStore.getEname());
                }

            }
        }
        List<SgBShareDistributionItemSaveRequest> items = request.getItems();
        if (CollectionUtils.isNotEmpty(items)) {
            for (SgBShareDistributionItemSaveRequest itemSaveRequest : items) {
                if (itemSaveRequest.getQty() == null) {
                    itemSaveRequest.setQty(BigDecimal.ONE);
                }

                AssertUtils.cannot(BigDecimal.ZERO.compareTo(itemSaveRequest.getQty()) > 0, "数量小于0不允许保存");
            }
        }
        return distribution;
    }
}
