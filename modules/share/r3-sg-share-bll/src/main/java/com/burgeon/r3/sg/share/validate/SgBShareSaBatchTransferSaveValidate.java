package com.burgeon.r3.sg.share.validate;

import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareSaBatchTransfer;
import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareSaBatchTransferItem;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareSaBatchTransferItemMapper;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareSaBatchTransferMapper;
import com.burgeon.r3.sg.share.model.dto.SgBShareSaBatchTransferDto;
import com.burgeon.r3.sg.share.model.dto.SgBShareSaBatchTransferItemDto;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleItemValidator;
import com.jackrain.nea.web.face.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/1 17:28
 */
@Component
public class SgBShareSaBatchTransferSaveValidate extends BaseSingleItemValidator<SgBShareSaBatchTransferDto,
        SgBShareSaBatchTransferItemDto> {

    @Autowired
    private SgBShareSaBatchTransferMapper mapper;

    @Autowired
    private SgBShareSaBatchTransferItemMapper itemMapper;

    @Override
    public String getValidatorMsgName() {
        return "配销仓调拨单批量导入保存验证";
    }

    @Override
    public Class<?> getValidatorClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 validateMainTable(SgBShareSaBatchTransferDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14();
        Boolean isPass = checkMainTable(mainObject, v14);
        if (!isPass) {
            return v14;
        }
        return null;
    }

    @Override
    public ValueHolderV14 validateSubTable(SgBShareSaBatchTransferDto mainObject, List<SgBShareSaBatchTransferItemDto> subObjectList, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14();
        for (SgBShareSaBatchTransferItemDto itemDto : subObjectList) {
            SgBShareSaBatchTransferItem oldItemObj = null;
            if (Objects.nonNull(itemDto.getId()) && itemDto.getId() > 0) {
                oldItemObj = itemMapper.selectById(itemDto.getId());
                if (Objects.isNull(oldItemObj)) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(Resources.getMessage("当前记录已不存在！"));
                    return v14;
                }

                if (SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_SUCCESS.equals(oldItemObj.getItemStatus())) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(Resources.getMessage("当前明细记录的状态等于成功，不允许编辑！"));
                    return v14;
                }
            } else {
                boolean storeIsNull = Objects.isNull(itemDto.getSenderSaStoreId()) || Objects.isNull(itemDto.getReceiverSaStoreId());
                if (storeIsNull) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(Resources.getMessage("发货配销仓和收货配销仓不能为空！"));
                    return v14;
                }

                if (itemDto.getSenderSaStoreId().equals(itemDto.getReceiverSaStoreId())) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(Resources.getMessage("发货配销仓和收货配销仓相同，不允许新增！"));
                    return v14;
                }

                SgCSaStore senderSaStore = CommonCacheValUtils.getSaStore(itemDto.getSenderSaStoreId());
                SgCSaStore receiverSaStore = CommonCacheValUtils.getSaStore(itemDto.getReceiverSaStoreId());
                if (Objects.isNull(senderSaStore.getSgCShareStoreId()) || Objects.isNull(receiverSaStore.getSgCShareStoreId())) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(Resources.getMessage("未查询到对应的配销仓所属聚合仓"));
                    return v14;
                }

                if (!senderSaStore.getSgCShareStoreId().equals(receiverSaStore.getSgCShareStoreId())) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(Resources.getMessage(String.format("发货配销仓：[%s]和收货配销仓：[%s]对应聚合仓不同，不允许新增！",
                            senderSaStore.getEname(), receiverSaStore.getEname())));
                    return v14;
                }
            }
        }
        return null;
    }

    private Boolean checkMainTable(SgBShareSaBatchTransferDto mainObject, ValueHolderV14 v14) {
        SgBShareSaBatchTransfer oldMainObj = null;
        if (Objects.nonNull(mainObject.getId()) && mainObject.getId() > 0) {
            oldMainObj = mapper.selectById(mainObject.getId());
            if (Objects.isNull(oldMainObj)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("当前记录已不存在！"));
                return false;
            }

            if (SgConstantsIF.SG_B_SHARE_SA_BATCH_TRANSFER_STATUS_03.equals(oldMainObj.getBillStatus())) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("当前记录已全部审核，不允许编辑！"));
                return false;
            }

            if (SgConstants.IS_ACTIVE_N.equalsIgnoreCase(oldMainObj.getIsactive())) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("当前记录已作废，不允许编辑！"));
                return false;
            }
        }
        return true;
    }
}
