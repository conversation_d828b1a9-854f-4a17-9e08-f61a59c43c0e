package com.burgeon.r3.sg.share.services.ryytndistribution;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.rpc.RpcCpUserService;
import com.burgeon.r3.sg.basic.utils.DingTalkTokenEnum;
import com.burgeon.r3.sg.basic.utils.DingTalkUtil;
import com.burgeon.r3.sg.basic.utils.EnvEnum;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgCStoreWarnStorageMsgDTO;
import com.burgeon.r3.sg.core.model.result.SgCStoreWarnStorageQtyQueryResult;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCStoreWarnStorage;
import com.burgeon.r3.sg.inf.common.enums.SgCStoreWarnStorageBatchTypeEnum;
import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgCStoreWarnStorageMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.utils.DateUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpUsers;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 库存-逻辑仓库存告警
 *
 * <AUTHOR>
 * @since 2023-10-08 17:30
 */
@Slf4j
@Service
public class SgCStoreWarnStorageService {
    /**
     * 日志OBJ
     */
    private static final String LOG_OBJ = "SgCStoreWarnStorageService.";

    @NacosValue(value = "${r3.sg.adb.scsg:test_rpt_sc_sg}", autoRefreshed = true)
    private String sgAdbPrefix;

    @Resource
    private SgCStoreWarnStorageMapper sgCStoreWarnStorageMapper;

    //    @DubboReference(group = "cp-ext", version = "1.0")
//    private CpGetUsersCmd cpGetUsersCmd;
    @Resource
    private RpcCpUserService rpcCpUserService;


    /**
     * 执行-部门逻辑仓库存告警
     *
     * @return 执行结果
     */
    public ValueHolderV14<Boolean> executeLsStorageWarn() {
        List<SgCStoreWarnStorage> warnList = sgCStoreWarnStorageMapper.selectList(new QueryWrapper<SgCStoreWarnStorage>().lambda()
                .eq(SgCStoreWarnStorage::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isEmpty(warnList)) {
            return new ValueHolderV14<>(Boolean.TRUE, ResultCode.SUCCESS, "没有需要关注的品相");
        }

        /*核心，查询逻辑仓库存*/
        Map<Long, SgCStoreWarnStorageQtyQueryResult> storageQtyMap = queryStorageMap(warnList);

        List<SgCStoreWarnStorageMsgDTO> msgDtoList = new ArrayList<>();

        /*根据告警名称分组逐个处理*/
        Map<String, List<SgCStoreWarnStorage>> nameGroupMap = warnList.stream()
                .collect(Collectors.groupingBy(SgCStoreWarnStorage::getWarnName));
        for (Map.Entry<String, List<SgCStoreWarnStorage>> entry : nameGroupMap.entrySet()) {
            List<SgCStoreWarnStorage> nameWarnList = nameGroupMap.get(entry.getKey());
            if (CollectionUtils.isEmpty(nameWarnList)) {
                continue;
            }

            SgCStoreWarnStorage warnInfo = nameWarnList.get(0);

            /*告警量大于或等于当前量时告警*/
            BigDecimal totalAvailableQty = nameWarnList.stream()
                    .map(SgCStoreWarnStorage::getId)
                    .map(storageQtyMap::get).filter(Objects::nonNull)
                    .map(SgCStoreWarnStorageQtyQueryResult::getQty).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (warnInfo.getWarnQty().compareTo(totalAvailableQty) >= 0) {
                SgCStoreWarnStorageMsgDTO msgDTO = SgCStoreWarnStorageMsgDTO.builder()
                        .warnName(warnInfo.getWarnName())
                        .skuId(warnInfo.getPsCSkuId())
                        .skuEcode(warnInfo.getPsCSkuEcode())
                        .skuEname(warnInfo.getPsCProEname())
                        .userId(warnInfo.getWarnUserId())
                        .warnQty(warnInfo.getWarnQty())
                        .curQty(totalAvailableQty)
                        .build();
                for (SgCStoreWarnStorage storage : nameWarnList) {
                    SgCStoreWarnStorageQtyQueryResult queryResult = storageQtyMap.get(storage.getId());
                    msgDTO.addStoreInfo(queryResult, storage.getCpCStoreId());
                }

                msgDtoList.add(msgDTO);
            }
        }
        /*发送消息*/
        sendDingTalkMsg(msgDtoList);
        return new ValueHolderV14<>(null, ResultCode.SUCCESS, "执行成功");
    }

    /**
     * 查询逻辑仓库存
     */
    private Map<Long, SgCStoreWarnStorageQtyQueryResult> queryStorageMap(List<SgCStoreWarnStorage> warnList) {
        Map<Long, SgCStoreWarnStorageQtyQueryResult> retMap = new HashMap<>();

        /*没有效期范围的库存查询*/
        List<SgCStoreWarnStorage> noBatchList = warnList.stream()
                .filter(o -> SgCStoreWarnStorageBatchTypeEnum.NO_RANGE.getCode().equals(o.getBatchType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noBatchList)) {
            queryStorageNoBatchRange(noBatchList, retMap);
        }

        /*有效期范围的库存查询：包括按天数和按日期*/
        List<SgCStoreWarnStorage> batchList = warnList.stream()
                .filter(o -> !SgCStoreWarnStorageBatchTypeEnum.NO_RANGE.getCode().equals(o.getBatchType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noBatchList)) {
            Date curDate = new Date();
            for (SgCStoreWarnStorage warn : batchList) {
                if (SgCStoreWarnStorageBatchTypeEnum.DATE_RANGE.getCode().equals(warn.getBatchType())) {
                    continue;
                }
                /*注意转换天的时候是反的*/
                warn.setStartDate(DateUtil.addDays(curDate, -warn.getEndDays()));
                warn.setEndDate(DateUtil.addDays(curDate, -warn.getStartDays()));
            }
            queryStorageByBatchRange(batchList, retMap);
        }

        return retMap;
    }

    /**
     * 查询逻辑仓库存-无效期范围
     */
    private void queryStorageNoBatchRange(List<SgCStoreWarnStorage> noBatchList, Map<Long, SgCStoreWarnStorageQtyQueryResult> retMap) {
        List<SgCStoreWarnStorageQtyQueryResult> noBatchStorageList;
        try {
            SgCStoreWarnStorageService bean = ApplicationContextHandle.getBean(SgCStoreWarnStorageService.class);
            Future<List<SgCStoreWarnStorageQtyQueryResult>> listFuture = bean.queryNoBatchListByADB(noBatchList);
            noBatchStorageList = listFuture.get();
        } catch (Exception e) {
            log.error(LogUtil.format("异步使用ADb查询逻辑仓库存时失败，异常：{}",
                    LOG_OBJ + "queryStorageNoBatchRange"), Throwables.getStackTraceAsString(e));
            return;
        }

        /*Map<String, SgCStoreWarnStorageQtyQueryResult> qtyUnBatchMap = ListUtils.emptyIfNull(noBatchStorageList)
                .stream().collect(Collectors.toMap(o -> o.getCpCStoreId() + "_" + o.getSkuId(), Function.identity()));*/
        Map<String, List<SgCStoreWarnStorageQtyQueryResult>> qtyUnBatchMap =
                ListUtils.emptyIfNull(noBatchStorageList).stream().collect(Collectors.groupingBy(o -> o.getCpCStoreId() + "_" + o.getSkuId()));
        for (SgCStoreWarnStorage warnStorage : noBatchList) {
            List<SgCStoreWarnStorageQtyQueryResult> qtyResultList = qtyUnBatchMap.getOrDefault(warnStorage.getCpCStoreId() + "_" + warnStorage.getPsCSkuId(), new ArrayList<>());
            /*没有库存记录*/
            if (CollectionUtils.isEmpty(qtyResultList)) {
                retMap.put(warnStorage.getId(), new SgCStoreWarnStorageQtyQueryResult());
                continue;
            }

            /*常规结果*/
            if (qtyResultList.size() == 1) {
                retMap.put(warnStorage.getId(), qtyResultList.get(0));
                continue;
            }

            /*逻辑仓库存中仓名称有可能名称重复*/
            BigDecimal totalQty = qtyResultList.stream()
                    .map(SgCStoreWarnStorageQtyQueryResult::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            SgCStoreWarnStorageQtyQueryResult queryRet = qtyResultList.get(0);
            queryRet.setQty(totalQty);
            retMap.put(warnStorage.getId(), queryRet);
        }
    }

    /**
     * 使用ADB查询库存
     *
     * @param noBatchList 没有效期范围的告警数据
     * @return 库存列表
     */
    @TargetDataSource(name = "adb")
    @Async(value = "adbAsyncExecutorPool")
    public Future<List<SgCStoreWarnStorageQtyQueryResult>> queryNoBatchListByADB(List<SgCStoreWarnStorage> noBatchList) {
        Set<Long> psCSkuIds = ListUtils.emptyIfNull(noBatchList).stream().map(SgCStoreWarnStorage::getPsCSkuId).collect(Collectors.toSet());
        Set<Long> cpcStoreIds = ListUtils.emptyIfNull(noBatchList).stream().map(SgCStoreWarnStorage::getCpCStoreId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(cpcStoreIds) || CollectionUtils.isEmpty(psCSkuIds)) {
            log.info(LogUtil.format("查询逻辑仓库存时参数不能为空，skuIds：{},cpcStoreIds：{}",
                    LOG_OBJ + "queryNoBatchListByADB"), psCSkuIds, cpcStoreIds);
            return AsyncResult.forValue(Collections.emptyList());
        }
        List<SgCStoreWarnStorageQtyQueryResult> noBatchStorageList = sgCStoreWarnStorageMapper.selectStorageListByNoBatchDays(sgAdbPrefix, cpcStoreIds, psCSkuIds);
        return AsyncResult.forValue(noBatchStorageList);
    }

    /**
     * 查询逻辑仓库存-有效期范围
     * </br>
     * 先查询大范围的数据，再进行组合
     */
    private void queryStorageByBatchRange(List<SgCStoreWarnStorage> warnStorageList, Map<Long, SgCStoreWarnStorageQtyQueryResult> retMap) {
        Set<SgCStoreWarnStorageQtyQueryResult> batchStorageList;
        try {
            /*分组循环查询库存数据，走ADB*/
            SgCStoreWarnStorageService bean = ApplicationContextHandle.getBean(SgCStoreWarnStorageService.class);
            Future<Set<SgCStoreWarnStorageQtyQueryResult>> listFuture = bean.queryBatchListByADB(warnStorageList);
            batchStorageList = listFuture.get();
        } catch (Exception e) {
            log.error(LogUtil.format("异步使用ADb查询逻辑仓库存时失败，异常：{}",
                    LOG_OBJ + "queryStorageByBatchRange"), Throwables.getStackTraceAsString(e));
            return;
        }

        Map<String, List<SgCStoreWarnStorageQtyQueryResult>> storeSkuMapList = SetUtils.emptyIfNull(batchStorageList)
                .stream().collect(Collectors.groupingBy(o -> o.getCpCStoreId() + "_" + o.getSkuId()));

        for (SgCStoreWarnStorage warnStorage : warnStorageList) {
            List<SgCStoreWarnStorageQtyQueryResult> storageList = storeSkuMapList.getOrDefault(warnStorage.getCpCStoreId() + "_" + warnStorage.getPsCSkuId(),
                    Collections.emptyList());
            if (CollectionUtils.isEmpty(storageList)) {
                retMap.put(warnStorage.getId(), new SgCStoreWarnStorageQtyQueryResult());
                continue;
            }

            BigDecimal availableQty = storageList.stream()
                    .filter(o -> Integer.parseInt(o.getProduceDate()) >= Integer.parseInt(DateFormatUtils.format(warnStorage.getStartDate(), "yyyyMMdd")))
                    .filter(o -> Integer.parseInt(o.getProduceDate()) <= Integer.parseInt(DateFormatUtils.format(warnStorage.getEndDate(), "yyyyMMdd")))
                    .map(SgCStoreWarnStorageQtyQueryResult::getQty)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            SgCStoreWarnStorageQtyQueryResult result = new SgCStoreWarnStorageQtyQueryResult();
            result.setQty(availableQty);
            result.setCpCStoreId(storageList.get(0).getCpCStoreId());
            result.setSkuId(storageList.get(0).getSkuId());
            result.setCpCStoreEcode(storageList.get(0).getCpCStoreEcode());
            result.setCpCStoreEname(storageList.get(0).getCpCStoreEname());
            retMap.put(warnStorage.getId(), result);
        }
    }

    /**
     * 使用ADB查询库存
     *
     * @param batchList 有效期范围的告警数据
     * @return 库存列表
     */
    @TargetDataSource(name = "adb")
    @Async(value = "adbAsyncExecutorPool")
    public Future<Set<SgCStoreWarnStorageQtyQueryResult>> queryBatchListByADB(List<SgCStoreWarnStorage> batchList) {
        Map<String, List<SgCStoreWarnStorage>> daysGroup = ListUtils.emptyIfNull(batchList).stream()
                .collect(Collectors.groupingBy(o -> DateFormatUtils.format(o.getStartDate(), "yyyyMMdd")
                        + "_" + DateFormatUtils.format(o.getEndDate(), "yyyyMMdd")));

        Set<SgCStoreWarnStorageQtyQueryResult> retList = new HashSet<>();
        /*根据批次范围分组查询*/
        for (Map.Entry<String, List<SgCStoreWarnStorage>> entry : daysGroup.entrySet()) {
            Set<Long> psCSkuIds = ListUtils.emptyIfNull(entry.getValue()).stream().map(SgCStoreWarnStorage::getPsCSkuId).collect(Collectors.toSet());
            Set<Long> cpcStoreIds = ListUtils.emptyIfNull(entry.getValue()).stream().map(SgCStoreWarnStorage::getCpCStoreId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(cpcStoreIds) || CollectionUtils.isEmpty(psCSkuIds)) {
                log.warn(LogUtil.format("查询逻辑仓库存时参数不能为空，skuIds：{},cpcStoreIds：{}",
                        LOG_OBJ + "queryBatchListByADB"), psCSkuIds, cpcStoreIds);
                continue;
            }

            String[] split = entry.getKey().split("_");
            List<SgCStoreWarnStorageQtyQueryResult> batchStorageList =
                    sgCStoreWarnStorageMapper.selectStorageDetailList(sgAdbPrefix, cpcStoreIds, psCSkuIds, split[0], split[1]);
            retList.addAll(batchStorageList);
        }
        return AsyncResult.forValue(retList);
    }

    /**
     * 发送告警消息
     * </br>
     * 群消息限制了每分钟的发送条数，后续需要考虑优化一下
     *
     * @param msgDtoList 消息对象列表
     */
    private void sendDingTalkMsg(List<SgCStoreWarnStorageMsgDTO> msgDtoList) {
        if (CollectionUtils.isEmpty(msgDtoList)) {
            return;
        }

        fillUserMobile(msgDtoList);

        Map<String, List<SgCStoreWarnStorageMsgDTO>> mobileGroup = msgDtoList.stream()
                .collect(Collectors.groupingBy(SgCStoreWarnStorageMsgDTO::getMobile));
        for (Map.Entry<String, List<SgCStoreWarnStorageMsgDTO>> entry : mobileGroup.entrySet()) {
            StringBuilder sb = new StringBuilder(EnvEnum.getEnv().name() + ":\n");
            int idx = 1;
            for (SgCStoreWarnStorageMsgDTO msgDTO : entry.getValue()) {
                sb.append(idx++).append(". ").append(msgDTO.genMsg()).append("\n");
            }
            sb.append("@" + entry.getKey());

            log.debug(LogUtil.format("发送钉钉消息：{}", "SgCStoreWarnStorageService.sendDingTalkMsg"), sb);

            DingTalkUtil.sendTextMsg(DingTalkTokenEnum.STORE_WARN_STORAGE, sb.toString(), Collections.singletonList(entry.getKey()));
        }
    }

    /**
     * 获取用户信息
     *
     * @param msgDtoList 告警对象列表
     */
    private void fillUserMobile(List<SgCStoreWarnStorageMsgDTO> msgDtoList) {
        List<Long> userIds = msgDtoList.stream().map(SgCStoreWarnStorageMsgDTO::getUserId).distinct().collect(Collectors.toList());
        Map<Long, CpUsers> idUserMap = rpcCpUserService.queryUserInfoByIds(userIds);
        log.warn(LogUtil.format("部门逻辑仓库存告警-补充告警手机号，用户ID列表：{},获取用户信息:{}",
                "SgCStoreWarnStorageService.fillUserMobile"), userIds, JSON.toJSONString(idUserMap));

        /*如果手机号不存在则无法告警，需要提示出来*/
        for (SgCStoreWarnStorageMsgDTO dto : msgDtoList) {
            CpUsers user = idUserMap.getOrDefault(dto.getUserId(), new CpUsers());
            dto.setMobile(Objects.isNull(user) ? "用户不存在" :
                    StringUtils.isEmpty(user.getMobil()) ? user.getEname() + "-手机号未设置" : user.getMobil());
        }
    }

}
