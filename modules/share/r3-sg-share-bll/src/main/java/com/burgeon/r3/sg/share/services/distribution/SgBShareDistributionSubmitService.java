package com.burgeon.r3.sg.share.services.distribution;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.share.distribution.SgBShareDistribution;
import com.burgeon.r3.sg.core.model.table.share.distribution.SgBShareDistributionItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.distribution.SgBShareDistributionItemMapper;
import com.burgeon.r3.sg.share.mapper.distribution.SgBShareDistributionMapper;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareData;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.model.request.SgLogicOccupyPlanPriorityRequest;
import com.burgeon.r3.sg.store.model.request.SgLogicOccupyPlanRequest;
import com.burgeon.r3.sg.store.model.request.SgLogicOccupyPlanSkuItemRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferSaveRequest;
import com.burgeon.r3.sg.store.model.result.SgLogicOccupyPlanItemResult;
import com.burgeon.r3.sg.store.model.result.SgLogicOccupyPlanResult;
import com.burgeon.r3.sg.store.services.SgLogicStorageOccupyPlanService;
import com.burgeon.r3.sg.store.services.transfer.SgBStoTransferSaveAndSubmitService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/19 14:07
 */
@Slf4j
@Component
public class SgBShareDistributionSubmitService {
    @Autowired
    SgBShareDistributionMapper mapper;

    @Autowired
    SgBShareDistributionItemMapper itemMapper;
    @Autowired
    SgBStoTransferSaveAndSubmitService tansferSaveAndSubmitService;

    @Autowired
    private SgLogicStorageOccupyPlanService occupyPlanService;

    /**
     * 配货单审核（前端）
     */
    ValueHolder submitDistribution(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        SgBShareDistributionSubmitService service = ApplicationContextHandle.getBean(SgBShareDistributionSubmitService.class);
        return R3ParamUtils.convertV14WithResult(service.submitDistribution(request));

    }

    /**
     * 配货单审核
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> submitDistribution(SgR3BaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBShareDistributionSubmitService.submitDistribution:request{}", JSONObject.toJSONString(request));
        }
        String lockKsy = SgConstants.SG_B_SHARE_DISTRIBUTION + ":" + request.getObjId();
        SgBShareDistribution distribution = checkParams(request);
        User loginUser = request.getLoginUser();
        SgRedisLockUtils.lock(lockKsy);
        try {

            List<SgBShareDistributionItem> itemList = itemMapper.selectList(new QueryWrapper<SgBShareDistributionItem>()
                    .lambda().eq(SgBShareDistributionItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                    .eq(SgBShareDistributionItem::getSgBShareDistributionId, request.getObjId()));
            if (itemList.size() == 0) {
                AssertUtils.logAndThrow("当前单据无明细,不允许审核！", loginUser.getLocale());
            }

            SgBShareDistribution update = new SgBShareDistribution();
            StorageUtils.setBModelDefalutDataByUpdate(update, loginUser);
            update.setStatus(SgShareConstants.BILL_STATUS_SUBMIT);
            update.setId(request.getObjId());
            mapper.updateById(update);

            Map<Long, List<SgBShareData>> cupyPlanMap = cupyPlan(distribution, itemList, loginUser);
            log.debug("submitDistribution cupyPlanMap = {}", JSONObject.toJSONString(cupyPlanMap));
            for (Map.Entry<Long, List<SgBShareData>> entry : cupyPlanMap.entrySet()) {
                Long key = entry.getKey();
                List<SgBShareData> itemData = entry.getValue();
                SgBStoTransferBillSaveRequest transferBillSaveRequest = new SgBStoTransferBillSaveRequest();
                SgBStoTransferSaveRequest transferSaveRequest = new SgBStoTransferSaveRequest();
                transferSaveRequest.setSenderStoreId(key);
                transferSaveRequest.setBillDate(distribution.getBillDate());
                transferSaveRequest.setSourceBillId(distribution.getId());
                transferSaveRequest.setSourceBillNo(distribution.getBillNo());
                transferSaveRequest.setSourceBillType(SgConstantsIF.BILL_SHARE_DISTRIBUTION);
                transferSaveRequest.setReceiverStoreId(distribution.getCpCStoreId());
                transferSaveRequest.setRemark("由配货单" + distribution.getBillNo() + "审核生成");
                transferSaveRequest.setDrpBillType(SgStoreConstants.DRP_BILL_TYPE_TF);
                transferBillSaveRequest.setTransferSaveRequest(transferSaveRequest);

                List<SgBStoTransferItemSaveRequest> requestList = new ArrayList<>();
                for (SgBShareData item : itemData) {
                    SgBStoTransferItemSaveRequest itemSaveRequest = new SgBStoTransferItemSaveRequest();
                    BeanUtils.copyProperties(item, itemSaveRequest);
                    itemSaveRequest.setSourceBillItemId(item.getSourceItemId());
                    requestList.add(itemSaveRequest);
                }
                transferBillSaveRequest.setItems(requestList);
                transferBillSaveRequest.setLoginUser(request.getLoginUser());
                log.debug("tansferSaveAndSubmitService billSaveRequests = {}", JSONObject.toJSONString(transferBillSaveRequest));
                tansferSaveAndSubmitService.saveAndSubmit(transferBillSaveRequest);
            }
        } catch (Exception e) {
            AssertUtils.logAndThrowException("配货单审核异常", e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "审核成功!");
    }

    /**
     * 操作前check验证
     */
    public SgBShareDistribution checkParams(SgR3BaseRequest request) {
        SgBShareDistribution distribution = mapper.selectById(request.getObjId());
        if (distribution == null) {
            AssertUtils.logAndThrow("当前记录已不存在！", request.getLoginUser().getLocale());
        }
        if (SgConstants.IS_ACTIVE_N.equals(distribution.getIsactive())) {
            AssertUtils.logAndThrow("当前记录已作废,不允许审核！", request.getLoginUser().getLocale());

        } else if (SgShareConstants.BILL_STATUS_UNSUBMIT != distribution.getStatus()) {
            AssertUtils.logAndThrow("当前单据状态不允许审核！", request.getLoginUser().getLocale());
        }
        return distribution;
    }

    /**
     * 寻源
     *
     * @param distribution 主表
     * @param itemList     明细
     */
    private Map<Long, List<SgBShareData>> cupyPlan(SgBShareDistribution distribution, List<SgBShareDistributionItem> itemList, User loginUser) {
        //聚合仓
        Long cShareStoreId = distribution.getSgCShareStoreId();
        //收货逻辑仓
        Long cpStoreId = distribution.getCpCStoreId();
        CpCStoreMapper storeMapper = ApplicationContextHandle.getBean(CpCStoreMapper.class);
        List<SgCpCStore> sgCpStores = storeMapper.selectList(new QueryWrapper<SgCpCStore>().lambda()
                .select(SgCpCStore::getId, SgCpCStore::getPriority)
                .in(SgCpCStore::getSgCShareStoreId, cShareStoreId)
                .eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y));

        SgLogicOccupyPlanRequest cupyPlanRequest = new SgLogicOccupyPlanRequest();
        List<SgLogicOccupyPlanPriorityRequest> priorityRequests = new ArrayList<>();
        //剔除收货逻辑仓
        for (SgCpCStore sgCpStore : sgCpStores) {
            if (cpStoreId.equals(sgCpStore.getId())) {
                continue;
            }
            SgLogicOccupyPlanPriorityRequest priorityRequest = new SgLogicOccupyPlanPriorityRequest();
            priorityRequest.setStoreId(sgCpStore.getId());
            priorityRequest.setPriority(sgCpStore.getPriority());
            priorityRequests.add(priorityRequest);
        }
        //明细
        List<SgLogicOccupyPlanSkuItemRequest> skuItemRequests = new ArrayList<>();
        for (SgBShareDistributionItem distributionItem : itemList) {
            SgLogicOccupyPlanSkuItemRequest skuItemRequest = new SgLogicOccupyPlanSkuItemRequest();
            skuItemRequest.setSourceItemId(distributionItem.getId());
            skuItemRequest.setPsCSkuId(distributionItem.getPsCSkuId());
            skuItemRequest.setQtyChange(distributionItem.getQty());
            skuItemRequests.add(skuItemRequest);
            SgBShareDistributionItem update = new SgBShareDistributionItem();
            update.setId(distributionItem.getId());
            update.setQtyOut(distributionItem.getQty());
            itemMapper.updateById(update);

        }

        cupyPlanRequest.setLoginUser(loginUser);
        cupyPlanRequest.setSourceBillId(distribution.getSourceBillId());
        cupyPlanRequest.setPriorityRequests(priorityRequests);
        cupyPlanRequest.setSkuItemRequests(skuItemRequests);
        if (log.isDebugEnabled()) {
            log.debug("寻源 getOccupyPlanByShare :request{}", JSONObject.toJSONString(cupyPlanRequest));
        }
        ValueHolderV14<SgLogicOccupyPlanResult> v14 = occupyPlanService.getOccupyPlan(cupyPlanRequest);
        if (log.isDebugEnabled()) {
            log.debug("寻源 getOccupyPlanByShare :result{}", JSONObject.toJSONString(v14));
        }
        if (!v14.isOK()) {
            AssertUtils.logAndThrow(v14.getMessage(), loginUser.getLocale());
        }
        SgLogicOccupyPlanResult resultData = v14.getData();
        if (!SgConstants.PREOUT_RESULT_All_SUCCESS.equals(resultData.getOccupyPlanResult())) {
            AssertUtils.logAndThrow("未满足当前配货单所有明细的数量", loginUser.getLocale());
        }
        List<SgLogicOccupyPlanItemResult> resultList = resultData.getItemResultList();

        Map<Long, List<SgBShareData>> map = new HashMap<>();
        for (SgLogicOccupyPlanItemResult result : resultList) {
            Long psSkuId = result.getPsCSkuId();
            Map<Long, BigDecimal> storePlan = result.getStorePlan();
            for (Long storeId : storePlan.keySet()) {
                List<SgBShareData> list;
                SgBShareData data = new SgBShareData();
                data.setQty(storePlan.get(storeId));
                data.setPsCSkuId(psSkuId);
                data.setSourceItemId(result.getSourceItemId());
                if (map.containsKey(storeId)) {
                    list = map.get(storeId);
                    list.add(data);
                } else {
                    list = new ArrayList<>();
                    list.add(data);
                    map.put(storeId, list);
                }
            }
        }
        return map;
    }
}
