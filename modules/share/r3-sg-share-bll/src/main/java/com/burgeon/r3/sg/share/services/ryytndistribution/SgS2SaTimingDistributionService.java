package com.burgeon.r3.sg.share.services.ryytndistribution;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.aliyuncs.utils.StringUtils;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySsExtResult;
import com.burgeon.r3.sg.basic.utils.BusinessSyetemParamConfigUtils;
import com.burgeon.r3.sg.core.enums.SgDistributionTypeEnum;
import com.burgeon.r3.sg.core.enums.SgMonthDemandStatusEnum;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCDepartmentMonthDemand;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCPlanConvertVersion;
import com.burgeon.r3.sg.share.common.DistributionDingTalkTableRobot;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 定时分货服务
 *
 * <AUTHOR>
 * @since 2023-03-01 10:41
 */
@Slf4j
@Service
public class SgS2SaTimingDistributionService {
    /**
     * 日志OBJ
     */
    private static final String LOG_OBJ = "SgS2SaTimingDistributionService.";

    @Resource
    private SgS2SaAutoDistributionManager sgS2SaAutoDistributionManager;
    @Resource
    private SgCDepartmentMonthDemandService sgCDepartmentMonthDemandService;
    @Resource
    private SgCDistributionMonthLogService sgCDistributionMonthLogService;

    @Resource
    private SgCPlanConvertVersionService sgCPlanConvertVersionService;

    /**
     * 定时分货允许的聚合仓编码列表
     */
    @Deprecated
    @NacosValue(value = "${sg.distribution.timing.share.ecodes:}", autoRefreshed = true)
    private String timingShareEcodes;

    public ValueHolderV14<Void> execute(SgCPlanConvertVersion version) {
        DistributionDingTalkTableRobot robot = new DistributionDingTalkTableRobot(SgDistributionTypeEnum.TIMING);

        int successSkuCnt = 0;
        int totalSkuCount = 0;
        /*获取所有聚合仓*/
        List<SgCShareStore> shareStores = sgS2SaAutoDistributionManager.querySgShareStores();
        /*过滤聚合仓，只有配置的聚合仓才可以参与定时分货（没配置就全部聚合仓都分）*/
        /*Set<String> allowTimingShareStoreEcodes = buildAllowTimingShareStoreEcodes();*/
        Set<String> allowTimingShareStoreEcodes = BusinessSyetemParamConfigUtils.timingDistributionSharedScope();
        if (CollectionUtils.isNotEmpty(allowTimingShareStoreEcodes)) {
            shareStores = shareStores.stream()
                    .filter(obj -> allowTimingShareStoreEcodes.contains(obj.getEcode()))
                    .collect(Collectors.toList());
            log.info(LogUtil.format("过滤分货聚合仓，允许分货的聚合仓编码列表:{}",
                    LOG_OBJ + "querySgShareStores"), allowTimingShareStoreEcodes);
        }
        if (CollectionUtils.isEmpty(shareStores)) {
            return new ValueHolderV14<>();
        }

        /*获取聚合仓下配销仓数据*/
        Map<Long, List<SgCSaStore>> shareIdSaStoreMap = sgS2SaAutoDistributionManager.querySaStoresMap(shareStores, true);

        /*聚合仓ID->聚合仓信息*/
        Map<Long, SgCShareStore> shareStoreIdMap = shareStores.stream()
                .collect(Collectors.toMap(SgCShareStore::getId, Function.identity(), (a, b) -> a));
        /*逐个聚合仓处理*/
        for (Map.Entry<Long, List<SgCSaStore>> shareSaStoreEntry : shareIdSaStoreMap.entrySet()) {
            SgCShareStore shareStore = shareStoreIdMap.get(shareSaStoreEntry.getKey());
            List<SgCSaStore> saStores = shareSaStoreEntry.getValue();
            /*部门ID->配销仓ID列表 映射*/
            Map<Long, Set<Long>> deptSaIdsMap = saStores.stream()
                    .filter(obj -> Objects.nonNull(obj.getCpCDistributionOrgId()))
                    .collect(Collectors.groupingBy(SgCSaStore::getCpCDistributionOrgId, Collectors.mapping(SgCSaStore::getId, Collectors.toSet())));
            if (MapUtils.isEmpty(deptSaIdsMap)) {
                log.info(LogUtil.format("定时-该聚合仓下所有配销仓均无归属部门，聚合仓ID:{}", LOG_OBJ + "execute"), shareStore.getId());
                continue;
            }

            /*只有被拉回过的SKU才执行定时分货，根据聚合仓查询*/
            Set<Long> returnedSkuIds = sgCDistributionMonthLogService.querySkuByShareStoreId(shareStore.getId(), version.getVersionBi());
            if (CollectionUtils.isEmpty(returnedSkuIds)) {
                log.info(LogUtil.format("定时-只有被拉回过的SKU才执行定时分货，该聚合仓下当月无拉回记录，聚合仓ID:{}", LOG_OBJ + "execute"), shareStore.getId());
                continue;
            }
            /*月需求中当月所有SKU，聚合仓维度*/
            Set<Long> demandSkuIds = sgCDepartmentMonthDemandService.querySkuBySaStore(deptSaIdsMap.keySet(), version.getVersionBi());
            demandSkuIds = SetUtils.emptyIfNull(demandSkuIds).stream().filter(returnedSkuIds::contains).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(demandSkuIds)) {
                log.info(LogUtil.format("定时-该聚合仓下无需要分货的SKU，聚合仓ID:{}", LOG_OBJ + "execute"), shareStore.getId());
                continue;
            }

            /*获取月需求：商品ID->月需求列表*/
            totalSkuCount += demandSkuIds.size();
            for (Long skuId : demandSkuIds) {
                try {
                    distributeBySku(shareStore, skuId, saStores, deptSaIdsMap, version);
                    successSkuCnt++;
                } catch (Exception e) {
                    log.error(LogUtil.format("定时-自动分货出错,聚合仓ID:{},商品ID:{},错误信息：[{}]", LOG_OBJ + "execute"),
                            shareStore.getId(), skuId, Throwables.getStackTraceAsString(e));
                    /*添加到告警信息*/
                    robot.addErrorSku(skuId, shareStore.getId(), e.getMessage());
                }
            }
        }
        /*发送钉钉告警*/
        robot.sendTableMsg();

        if (successSkuCnt != totalSkuCount) {
            log.warn(LogUtil.format("定时-自动分货，任务部分成功部分失败，成功SKU数：{},总SKU数：{}", LOG_OBJ + "execute"), successSkuCnt, totalSkuCount);
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, "定时-自动分货：任务执行完成！,成功SKU数：" + successSkuCnt + "/" + totalSkuCount);
    }

    /**
     * 获取允许的聚合仓Ecode列表
     *
     * @return 允许的聚合仓Ecode列表
     */
    @Deprecated
    private Set<String> buildAllowTimingShareStoreEcodes() {
        if (StringUtils.isEmpty(timingShareEcodes)) {
            return SetUtils.emptySet();
        }

        return Stream.of(timingShareEcodes.split(","))
                .map(String::trim)
                .collect(Collectors.toSet());
    }

    /**
     * 按SKU执行分配
     * <br/>
     * 事务操作，SKU维度,外层事务不影响内层事务提交或回滚
     *
     * @param shareStore   聚合仓信息
     * @param skuId        商品ID
     * @param saStores     配销仓信息列表
     * @param deptSaIdsMap 部门ID->配销仓ID列表 映射
     */
    @Transactional(rollbackFor = Throwable.class)
    public void distributeBySku(SgCShareStore shareStore, Long skuId, List<SgCSaStore> saStores,
                                Map<Long, Set<Long>> deptSaIdsMap, SgCPlanConvertVersion version) {
        /*天->二级部门月需求列表,按日期从小到大排序*/
        List<SgCDepartmentMonthDemand> demandList = sgCDepartmentMonthDemandService.queryDemand(saStores, skuId, version.getVersionBi());
        if (CollectionUtils.isEmpty(demandList)) {
            log.info(LogUtil.format("定时-按SKU分货-获取部门分货需求记录为空，该商品不进行分配，聚合仓ID:{}，商品ID:{}",
                    LOG_OBJ + "distributeBySku"), shareStore.getId(), skuId);
            return;
        }
        /*可分配的总库存*/
        BigDecimal totalAvailable2Distribute = queryStorage(shareStore.getId(), skuId);
        if (BigDecimal.ZERO.compareTo(totalAvailable2Distribute) >= 0) {
            log.info(LogUtil.format("定时-按SKU分货-获取可分配的可用库存小于或等于零，该商品不进行分配，聚合仓ID:{}，商品ID:{}",
                    LOG_OBJ + "distributeBySku"), shareStore.getId(), skuId);
            return;
        }

        /*根据时间按天分组*/
        Map<Date, List<SgCDepartmentMonthDemand>> dateDemandMap = demandList.stream()
                .collect(Collectors.groupingBy(SgCDepartmentMonthDemand::getDemandDate));
        List<Date> dateList = dateDemandMap.keySet().stream().sorted(Date::compareTo).collect(Collectors.toList());

        /*同商品下，部门ID->本次任务分配量*/
        Map<Long, BigDecimal> deptActualMap = new HashMap<>();
        BigDecimal available2Distribute = totalAvailable2Distribute;
        for (Date date : dateList) {
            List<SgCDepartmentMonthDemand> dailyDemandList = dateDemandMap.get(date);
            /*按天计算分配量，返回：剩余可分配的量*/
            available2Distribute = distributeByDay(deptActualMap, available2Distribute, dailyDemandList);
            /*余量为零，不继续分配*/
            if (available2Distribute.compareTo(BigDecimal.ZERO) <= 0) {
                log.info(LogUtil.format("本次分配结束，聚合仓ID:{}，商品ID:{}，分配需求日期:{}", LOG_OBJ + "distributeBySku"),
                        shareStore.getId(), skuId, DateUtil.format(date, "yyyyMMdd"));
                break;
            }
        }

        /*计算配销仓实际分配量：配销仓ID->本次任务实际分配量 映射*/
        Map<Long, BigDecimal> saActualMap = calculateSaActualStorageByAverage(deptSaIdsMap, deptActualMap);

        /*分货有剩余，将剩余库存分货到：冗余库存分货配销仓*/
        if (available2Distribute.compareTo(BigDecimal.ZERO) > 0 && Objects.nonNull(shareStore.getRedundancySaStoreId())) {
            log.info(LogUtil.format("定时-多余库存分配到冗余库存分货配销仓，聚合仓ID:{}，配销仓ID:{}，商品ID:{}", LOG_OBJ + "distributeBySku"),
                    shareStore.getId(), shareStore.getRedundancySaStoreId(), skuId);
            Long redundancySaSoreId = shareStore.getRedundancySaStoreId();
            saActualMap.put(redundancySaSoreId, saActualMap.getOrDefault(redundancySaSoreId, BigDecimal.ZERO).add(available2Distribute));
        }

        /*生成分货单，变更库存：配销仓ID->配销仓更新满足量*/
        doDistribute(shareStore.getId(), skuId, saActualMap);

        log.info(LogUtil.format("定时-按SKU分货-执行结束,聚合仓ID：{},商品ID:{},可分配的总库存：{},部门ID->配销仓ID列表映射:{}," +
                                "部门ID->本次任务分配量映射：{},配销仓ID->本次任务实际分配量映射:{}",
                        LOG_OBJ + "distributeBySku"),
                shareStore.getId(), skuId, totalAvailable2Distribute, JSON.toJSONString(deptSaIdsMap),
                JSON.toJSONString(deptActualMap), JSON.toJSONString(saActualMap));

//        if ("local".equals(System.getProperty("env"))) {
//            throw new RuntimeException("本地环境测试需要回滚数据");
//        }
    }

    /**
     * 获取聚合仓下可用于分配的库存
     * <br/>
     * 定时：聚合仓下所有逻辑仓可用库存汇总
     *
     * @param shareStoreId 聚合仓ID
     * @param skuId        商品ID
     * @return 聚合仓下该SKU可用库存
     */
    private BigDecimal queryStorage(Long shareStoreId, Long skuId) {
        List<SgStorageRedisQuerySsExtResult> retList = sgS2SaAutoDistributionManager.queryShareStorageAvailableWithRedis(Collections.singleton(shareStoreId), skuId);
        if (CollectionUtils.isEmpty(retList)) {
            return BigDecimal.ZERO;
        }

        return retList.stream().filter(ret -> shareStoreId.equals(ret.getSgCShareStoreId()))
                .map(SgStorageRedisQuerySsExtResult::getQtySsAvailable)
                .findAny().orElse(BigDecimal.ZERO);
    }

    /**
     * 按天计算分配量
     *
     * @param deptActualMap        二级部门ID->本次任务分配量（执行结果存放）
     * @param available2Distribute 可分配的总库存
     * @param dailyDemandList      部门日需求列表
     * @return 可分配的总库存在当天分配后的结余
     */
    private BigDecimal distributeByDay(Map<Long, BigDecimal> deptActualMap, BigDecimal available2Distribute,
                                       List<SgCDepartmentMonthDemand> dailyDemandList) {
        /*日需求总量*/
        BigDecimal dailyTotalDemand = dailyDemandList.stream()
                .filter(demand -> !SgMonthDemandStatusEnum.COMPLETED.getValue().equals(demand.getDemandStatus()))
                .map(demand -> demand.getDemandQty().subtract(demand.getActualInQty()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        /*计算是否够分*/
        BigDecimal remainStorage = available2Distribute.subtract(dailyTotalDemand);

        /*够分*/
        if (remainStorage.compareTo(BigDecimal.ZERO) >= 0) {
            enough2Distribute(deptActualMap, dailyDemandList);
            return remainStorage;
        }

        /*不够分*/
        proportion2Distribute(deptActualMap, dailyDemandList, dailyTotalDemand, available2Distribute);
        return BigDecimal.ZERO;
    }

    /**
     * 可供分配量 >= 当天需求量:够分
     *
     * @param deptActualMap   二级部门ID->本次任务分配量（执行结果存放）
     * @param dailyDemandList 部门日需求列表
     */
    private void enough2Distribute(Map<Long, BigDecimal> deptActualMap,
                                   List<SgCDepartmentMonthDemand> dailyDemandList) {
        for (SgCDepartmentMonthDemand demand : dailyDemandList) {
            if (SgMonthDemandStatusEnum.COMPLETED.getValue().equals(demand.getDemandStatus())) {
                continue;
            }
            BigDecimal actualQty = demand.getDemandQty().subtract(demand.getActualInQty());

            deptActualMap.put(demand.getCpCDistributionOrgId(),
                    deptActualMap.getOrDefault(demand.getCpCDistributionOrgId(), BigDecimal.ZERO).add(actualQty));
        }
    }

    /**
     * 按比例：不够分/全部分配
     * <br/>
     * 可供分配量 < 当天需求量:不够分
     *
     * @param deptActualMap        二级部门ID->本次任务分配量
     * @param dailyDemandList      部门日需求列表
     * @param dailyTotalDemand     当天需求总量
     * @param available2Distribute 当前剩余可分配总量
     */
    private void proportion2Distribute(Map<Long, BigDecimal> deptActualMap,
                                       List<SgCDepartmentMonthDemand> dailyDemandList, /*List<SgCDepartmentMonthDemand> modifyDemandList,*/
                                       BigDecimal dailyTotalDemand, BigDecimal available2Distribute) {
        List<SgCDepartmentMonthDemand> demandList = dailyDemandList.stream()
                .filter(demand -> !SgMonthDemandStatusEnum.COMPLETED.getValue().equals(demand.getDemandStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(demandList)) {
            return;
        }

        /*每个部门未满足量*/
        Map<Long, BigDecimal> needQtyMap = dailyDemandList.stream()
                .collect(Collectors.toMap(SgCDepartmentMonthDemand::getCpCDistributionOrgId,
                        demand -> demand.getDemandQty().subtract(demand.getActualInQty()), BigDecimal::add));

        /*剩余可分配量，防止超分*/
        BigDecimal curRemain = available2Distribute;
        for (int i = 0; i < dailyDemandList.size(); i++) {
            SgCDepartmentMonthDemand demand = dailyDemandList.get(i);
            BigDecimal calculateRet;
//            if (i == dailyDemandList.size() - 1) {
//                calculateRet = curRemain;
//            } else {
            /*分配量 = （可分配的量 × 当前需求量（即：需求量-实际分配量））÷ 当天总需求量 */
            calculateRet = available2Distribute.multiply(demand.getDemandQty().subtract(demand.getActualInQty()))
                    .divide(dailyTotalDemand, RoundingMode.HALF_UP).setScale(0, RoundingMode.HALF_UP);
//            }
            /*当前剩余小于计算分配量 则 将当前剩余全给到计算分配,防止不够（四舍五入有可能超出剩余量）*/
            if (curRemain.compareTo(calculateRet) < 0) {
                calculateRet = curRemain;
            }
            /*部门实际入库量*/
            deptActualMap.put(demand.getCpCDistributionOrgId(),
                    deptActualMap.getOrDefault(demand.getCpCDistributionOrgId(), BigDecimal.ZERO).add(calculateRet));
            /*计算后，剩余的待满足量*/
            needQtyMap.put(demand.getCpCDistributionOrgId(),
                    needQtyMap.getOrDefault(demand.getCpCDistributionOrgId(), BigDecimal.ZERO).subtract(calculateRet));

            /*剩余量小于等于0了*/
            curRemain = curRemain.subtract(calculateRet);
            if (BigDecimal.ZERO.compareTo(curRemain) >= 0) {
                return;
            }
            /*分配到最后一个，按未满足的量倒序满足，直到剩余为0*/
            if (i == dailyDemandList.size() - 1) {
                calculateLast(curRemain, deptActualMap, needQtyMap);
            }
        }
    }


    /**
     * 分配到最后一个，按未满足的量倒序满足，直到剩余可分配量为0
     *
     * @param curRemain     当前剩余可分配量
     * @param deptActualMap 每个部门的已分配量
     * @param needQtyMap    每个部门未分配量
     */
    private void calculateLast(BigDecimal curRemain, Map<Long, BigDecimal> deptActualMap, Map<Long, BigDecimal> needQtyMap) {
        /*按未满足的量倒序*/
        List<Map.Entry<Long, BigDecimal>> sortedNeedQty = new ArrayList<>(needQtyMap.entrySet());
        sortedNeedQty.sort((entry1, entry2) -> entry2.getValue().compareTo(entry1.getValue()));

        for (Map.Entry<Long, BigDecimal> entry : sortedNeedQty) {
            Long orgId = entry.getKey();
            BigDecimal needQty = entry.getValue();

            /*如果剩余可分配量为0，直接结束*/
            if (curRemain.compareTo(BigDecimal.ZERO) <= 0) {
                return;
            }

            /*本行记录可分配的量：是未分配量和当前剩余可分配量的最小值*/
            BigDecimal qty = needQty.min(curRemain);

            /*将分配的量加上去*/
            deptActualMap.put(orgId, deptActualMap.getOrDefault(orgId, BigDecimal.ZERO).add(qty));
            needQtyMap.put(orgId, needQty.subtract(qty));

            /*减剩余可分配量，进行下次循环*/
            curRemain = curRemain.subtract(qty);
        }
    }

    /**
     * 计算配销仓实际分配量(将二级部门的分货量均摊到二级部门下面的配销仓)
     * <br/>
     * 平均分配
     *
     * @param deptSaIdsMap  二级部门ID->配销仓ID列表 映射
     * @param deptActualMap 二级部门ID->本次分配总量 映射
     * @return 配销仓ID->本次任务实际分配量 映射
     */
    private Map<Long, BigDecimal> calculateSaActualStorageByAverage(Map<Long, Set<Long>> deptSaIdsMap, Map<Long, BigDecimal> deptActualMap) {
        Map<Long, BigDecimal> saActualMap = Maps.newHashMap();
        for (Long deptId : deptActualMap.keySet()) {
            /*该部门下，本次分配的总量*/
            BigDecimal deptTotalAfter = deptActualMap.get(deptId);

            /*该部门下，所有的配销仓ID*/
            List<Long> saStoreIds = new ArrayList<>(deptSaIdsMap.get(deptId));
            /*该部门下仅一个配销仓*/
            if (saStoreIds.size() == 1) {
                saActualMap.put(saStoreIds.iterator().next(), deptTotalAfter);
                continue;
            }

            /*配销仓-实际分配的量 = (本次分配的总量) ÷ 配销仓个数*/
            BigDecimal actual = deptTotalAfter
                    .divide(new BigDecimal(saStoreIds.size()), RoundingMode.HALF_UP)
                    .setScale(0, RoundingMode.HALF_UP);

            BigDecimal remain = deptTotalAfter;
            for (int i = 0; i < saStoreIds.size(); i++) {
                if (BigDecimal.ZERO.compareTo(remain) == 0) {
                    break;
                }

                Long saStoreId = saStoreIds.get(i);
                if (i == saStoreIds.size() - 1) {
                    saActualMap.put(saStoreId, remain);
                    break;
                }
                remain = remain.subtract(actual);

                saActualMap.put(saStoreId, actual);
            }

        }
        return saActualMap;
    }

    /**
     * 执行分货：生成分货单变更库存（分货单驱动变更部门分货需求表）
     *
     * @param shareStoreId 聚合仓ID
     * @param skuId        需要处理的商品ID
     * @param saActualMap  配销仓ID->本次任务实际分配量
     */
    private void doDistribute(Long shareStoreId, Long skuId, Map<Long, BigDecimal> saActualMap) {
        List<Pair<Long, BigDecimal>> saStoragePairList = Lists.newArrayList();
        for (Long saStoreId : saActualMap.keySet()) {
            BigDecimal after = saActualMap.get(saStoreId);
            if (BigDecimal.ZERO.compareTo(after) == 0) {
                continue;
            }
            saStoragePairList.add(Pair.of(saStoreId, after));
        }

        log.debug(LogUtil.format("定时-分货，聚合仓ID：{}，商品ID：{}，分货映射：{}", LOG_OBJ + "doDistribute"),
                shareStoreId, skuId, saStoragePairList);
        sgS2SaAutoDistributionManager.doAllocation(shareStoreId, skuId, saStoragePairList, "定时分货-");
    }
}
