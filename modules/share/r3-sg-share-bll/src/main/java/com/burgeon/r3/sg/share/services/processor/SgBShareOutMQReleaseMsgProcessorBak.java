//package com.burgeon.r3.sg.share.services.processor;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.burgeon.r3.sg.core.common.MQConstantsIF;
//import com.burgeon.r3.sg.core.model.request.mq.SgBShareOutMQReleaseRequest;
//import com.burgeon.r3.sg.share.services.out.SgBShareOutMQReleaseService;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.config.Resources;
//import com.jackrain.nea.constants.ResultCode;
//import com.jackrain.nea.r3.mq.exception.ProcessMqException;
//import com.jackrain.nea.r3.mq.processor.AbstractMqProcessor;
//import com.jackrain.nea.r3.mq.processor.MqProcessResult;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.web.face.User;
//import com.jackrain.nea.web.face.impl.UserImpl;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.context.ApplicationContext;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @date 2021/6/14 20:27
// */
//@Slf4j
//@Component
//public class SgBShareOutMQReleaseMsgProcessorBak extends AbstractMqProcessor {
//    @Override
//    public MqProcessResult startProcess(String messageTopic, String messageKey, String messageBody, String messageTag) throws ProcessMqException {
//        try {
//            SgBShareOutMQReleaseRequest sendMsgRequest = JSON.parseObject(messageBody, SgBShareOutMQReleaseRequest.class);
//            if (sendMsgRequest == null) {
//                return new MqProcessResult(false, Resources.getMessage("messageBody解析为null"));
//            }
//            JSONObject jo = JSON.parseObject(messageBody);
//            User user = JSON.parseObject(jo.getString("loginUser"), UserImpl.class);
//            sendMsgRequest.setLoginUser(user);
//
//            SgBShareOutMQReleaseService service = ApplicationContextHandle.getBean(SgBShareOutMQReleaseService.class);
//            ValueHolderV14 v14 = service.release(sendMsgRequest);
//            if (v14.getCode() == ResultCode.SUCCESS) {
//                return new MqProcessResult(false, Resources.getMessage(v14.getMessage()));
//            } else {
//                log.warn("SgBShareOutMQReleaseMsgProcessor.startProcess. error :{}",
//                        Resources.getMessage(v14.getMessage()));
//                return new MqProcessResult(true, Resources.getMessage(v14.getMessage()));
//            }
//
//        } catch (Exception e) {
//            log.error("SgBShareOutMQReleaseMsgProcessor.startProcess. error :{}",
//                    Throwables.getStackTraceAsString(e));
//            return new MqProcessResult(true, Resources.getMessage("处理内容非法：" + e.getMessage()));
//        }
//    }
//
//    @Override
//    public boolean checkCanExecuteProcess(String messageTopic, String messageKey, String messageBody, String messageTag) {
//        if (log.isDebugEnabled()) {
//            log.debug("{}.debug,接收占用单释放消息：" +
//                            "messageTopic{}," +
//                            "messageTag{}," +
//                            "messageKey{}," +
//                            "messageBody{}",
//                    this.getClass().getName(),
//                    messageTopic, messageTag, messageKey, messageBody);
//        }
//        return StringUtils.equalsIgnoreCase(messageTag, MQConstantsIF.TAG_SG_B_SHARE_OUT_RELEASE_POSTBACK);
//    }
//
//    @Override
//    public void initialMqOrderProcessor(ApplicationContext applicationContext) {
//        if (log.isDebugEnabled()) {
//            log.debug(this.getClass().getName() + ".start");
//        }
//    }
//}
