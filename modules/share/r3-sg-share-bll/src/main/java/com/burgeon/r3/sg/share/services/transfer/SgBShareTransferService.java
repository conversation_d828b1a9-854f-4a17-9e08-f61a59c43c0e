package com.burgeon.r3.sg.share.services.transfer;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.SgCShareStoreMapper;
import com.burgeon.r3.sg.basic.utils.BigDecimalUtils;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareTransfer;
import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareTransferImportItem;
import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareTransferItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareTransferImportItemMapper;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareTransferItemMapper;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareTransferMapper;
import com.burgeon.r3.sg.share.model.dto.SgBShareTransferImportItemDto;
import com.burgeon.r3.sg.share.model.request.transfer.SgBShareTransferImportItemRequest;
import com.burgeon.r3.sg.share.model.request.transfer.SgBShareTransferRequest;
import com.burgeon.r3.sg.share.model.request.transfer.SgBShareTransferSaveRequest;
import com.burgeon.r3.sg.share.model.result.transfer.SgShareTransferResult;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferSaveRequest;
import com.burgeon.r3.sg.store.services.transfer.SgBStoTransferSaveAndSubmitService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCTranwayAssign;
import com.jackrain.nea.cpext.model.table.CpCustomer;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.ps.api.result.ProSkuResult;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * 聚合仓调拨单
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component
public class SgBShareTransferService {

    @Autowired
    SgBShareTransferMapper mapper;
    @Autowired
    SgBShareTransferImportItemMapper importItemMapper;

    @Autowired
    SgBShareTransferItemMapper itemMapper;

    @Autowired
    private BasicPsQueryService basicPsQueryService;

    @Autowired
    private SgBStoTransferSaveAndSubmitService sgBStoTransferSaveAndSubmitService;

    @Autowired
    private SgCShareStoreMapper sgCShareStoreMapper;

    // 测试方法
    // @PostConstruct
    public void testRun() {
        UserImpl user = new UserImpl();
        user.setClientId(37);
        user.setOrgId(27);
        user.setId(83);
        user.setName("系统管理员");
        user.setEname("系统管理员");

        SgBShareTransferSaveRequest request = new SgBShareTransferSaveRequest();
        request.setR3(true);
        request.setLoginUser(user);
        request.setObjId(193L);

        submit(null, request);
    }

    /**
     * 聚合仓调拨单保存并审核（主要用于配销仓跨聚合仓调拨）
     *
     * @return return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<List<SgShareTransferResult>> saveAndSubmit(SgBShareTransferSaveRequest saveRequest) throws Exception {
        SgBShareTransferSaveRequest request = new SgBShareTransferSaveRequest();

        BeanUtils.copyProperties(saveRequest, request);
        request.setR3(false);
        // 设置聚合仓调拨单新增时主表主键objId为空
        request.setObjId(null);
        ValueHolderV14<SgR3BaseResult> baseResult = save(request);
        if (baseResult.getCode() == ResultCode.FAIL) {
            return new ValueHolderV14<>(request.getSgShareTransferResults(), ResultCode.FAIL, request.getMsg());
        }
        if (null != baseResult && null != baseResult.getData()) {
            JSONObject json = baseResult.getData().getDataJo();
            if (null != json) {
                String objid = json.getString("objid");
                request.setObjId(StringUtils.isBlank(objid) ? null : Long.parseLong(objid));
            }
        }
        submit(null, request);
        if (request.getCode() == ResultCode.FAIL) {
            return new ValueHolderV14<>(request.getSgShareTransferResults(), ResultCode.FAIL, request.getMsg());
        }
        return new ValueHolderV14<>(request.getSgShareTransferResults(), ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
    }

    /**
     * 聚合仓调拨单保存（前端页面保存入口）
     *
     * @param session session
     * @return return
     */
    public ValueHolder save(QuerySession session) {
        SgBShareTransferSaveRequest request = R3ParamUtils.parseSaveObject(session, SgBShareTransferSaveRequest.class);
        request.setR3(true);
        SgBShareTransferService service = ApplicationContextHandle.getBean(SgBShareTransferService.class);
        return R3ParamUtils.convertV14WithResult(service.save(request));
    }

    /**
     * 聚合仓调拨单审核（前端页面审核入口）
     *
     * @param session session
     * @return return
     */
    public ValueHolder submit(QuerySession session, SgBShareTransferSaveRequest request) throws NDSException {

        if (request.isR3()) {
            request = R3ParamUtils.parseSaveObject(session, SgBShareTransferSaveRequest.class);
            request.setR3(true);
        }

        if (request != null) {
            log.info("聚合仓调拨单审核入口参数:{}", JSON.toJSONString(request));
        }

        Long objId = request.getObjId();
        User loginUser = request.getLoginUser();
        // 检查聚合仓调拨单是否存在,是否已经作废,是否已经审核
        SgBShareTransfer sgBShareTransfer = checkSgBShareTransfer(request, objId, true);

        //校验不通过直接返回
        if (!request.isR3() && request.getCode() == ResultCode.FAIL) {
            return R3ParamUtils.convertV14WithResult(new ValueHolderV14<>(null, ResultCode.FAIL, request.getMsg()));
        }

        LambdaQueryWrapper<SgBShareTransferImportItem> query = new LambdaQueryWrapper<>();
        query.eq(SgBShareTransferImportItem::getSgBShareTransferId, objId);
        List<SgBShareTransferImportItem> importItemList = importItemMapper.selectList(query);

        if (CollectionUtils.isEmpty(importItemList)) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("聚合仓调拨单导入明细无记录不允许审核！", request.getLoginUser().getLocale());
            } else {
                request.setCode(ResultCode.FAIL);
                request.setMsg("聚合仓调拨单导入明细无记录不允许审核！");
                return R3ParamUtils.convertV14WithResult(new ValueHolderV14<>(null, ResultCode.FAIL, SgConstants.MESSAGE_STATUS_FAIL));
            }
        }

        // 过滤掉聚合仓调拨单结果明细单据状态:失败,未处理的情况【排除聚合仓调拨单结果明细已经处理或者有生成记录的数据】
        List<SgBShareTransferImportItem> inserImportItemList = importItemMapper.findFailOrNoDealImportItem(sgBShareTransfer.getId());

        // 封装生成调拨单失败信息
        List<SgShareTransferResult> resultList = new ArrayList<>();
        // 点击审核按钮 先将未审核状态设置成部分审核状态1
        if ((SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_00.equals(sgBShareTransfer.getStatus()) ||
                SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_01.equals(sgBShareTransfer.getStatus()))
                && CollectionUtils.isNotEmpty(inserImportItemList)) {

            List<SgBShareTransferItem> insertItemList = new ArrayList<>();

            // 生成聚合仓调拨结果明细
            for (SgBShareTransferImportItem item : inserImportItemList) {
                //重点看这个方法
                generateSgBShareTransferItem(request, insertItemList, item, resultList);
            }

            // 保存聚合仓调拨结果明细到数据库
            if (CollectionUtils.isNotEmpty(insertItemList)) {
                String lockKsy = SgConstants.SG_B_SHARE_TRANSFER_ITEM + ":insert:" + sgBShareTransfer.getBillNo();
                SgRedisLockUtils.lock(lockKsy);
                try {
                    saveSgBShareTransferItem(loginUser, sgBShareTransfer, insertItemList);
                } catch (Exception e) {
                    log.error("聚合仓调拨单结果明细保存到数据库. error:{}", Throwables.getStackTraceAsString(e));
                    if (!request.isR3()) {
                        request.setCode(ResultCode.FAIL);
                        request.setMsg("聚合仓调拨单结果明细保存到数据库失败！");
                        AssertUtils.logAndThrowException("聚合仓调拨单保存并审核保存结果明细异常！", e, Locale.getDefault());
                    }
                } finally {
                    SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
                }
            }
        }

        // 调用逻辑调拨单
        saveTransfer(sgBShareTransfer, loginUser);

        // 回写主表下明细的调拨状态
        updateImportItemStatus(request, sgBShareTransfer);

        // 更新聚合仓调拨单审核汇总信息
        updateSgBShareTransferSubmitInfo(request, sgBShareTransfer);

        // 封装生成调拨单失败信息
        if (CollectionUtils.isNotEmpty(resultList) && !request.isR3()) {
            request.setSgShareTransferResults(resultList);
            if (CollectionUtils.isNotEmpty(inserImportItemList) && resultList.size() == inserImportItemList.size()) {
                request.setCode(ResultCode.FAIL);
                request.setMsg("聚合仓调拨单生成结果明细失败");
            }
        }

        return R3ParamUtils.convertV14WithResult(new ValueHolderV14<>(null, ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS));
    }

    /**
     * 回写主表下明细的调拨状态
     */
    private void updateImportItemStatus(SgBShareTransferSaveRequest request, SgBShareTransfer sgBShareTransfer) {
        LambdaQueryWrapper<SgBShareTransferImportItem> query = new LambdaQueryWrapper<>();
        query.eq(SgBShareTransferImportItem::getSgBShareTransferId, sgBShareTransfer.getId());
        List<SgBShareTransferImportItem> importItemList = importItemMapper.selectList(query);
        if (CollectionUtils.isEmpty(importItemList)) {
            return;
        }

        LambdaQueryWrapper<SgBShareTransferItem> query1 = new LambdaQueryWrapper<>();
        query1.eq(SgBShareTransferItem::getSgBShareTransferId, sgBShareTransfer.getId());
        List<SgBShareTransferItem> outItemList = itemMapper.selectList(query1);
        if (CollectionUtils.isEmpty(outItemList)) {
            return;
        }
        SgBShareTransferImportItem updateImportItem = null;
        List<SgBShareTransferItem> outItemNewList;
        for (SgBShareTransferImportItem item : importItemList) {
            updateImportItem = new SgBShareTransferImportItem();
            updateImportItem.setId(item.getId());
            updateImportItem.setRemark(item.getRemark());

            // 匹配上导入明细和结果明细的状态字段
            outItemNewList = outItemList.stream()
                    .filter(sgBShareTransferItem -> sgBShareTransferItem.getSenderShareStoreId().equals(item.getSenderShareStoreId()))
                    .filter(sgBShareTransferItem -> sgBShareTransferItem.getReceiverShareStoreId().equals(item.getReceiverShareStoreId()))
                    .filter(sgBShareTransferItem -> sgBShareTransferItem.getSourceBillItemId().equals(item.getSourceBillItemId()))
                    .collect(Collectors.toList());
            // 1、条码 2、款号
            switch (item.getTransferDimension()) {
                // 条码维度数据查询
                case SgShareConstants.SG_B_SHARE_TRANSFER_DIMENSION_SKU:
                    outItemNewList = outItemNewList.stream().filter(sgBShareTransferItem -> sgBShareTransferItem.getPsCSkuEcode().equals(item.getPsCSkuEcode()))
                            .collect(Collectors.toList());
                    break;
                // 商品维度数据查询
                case SgShareConstants.SG_B_SHARE_TRANSFER_DIMENSION_PRO:
                    outItemNewList = outItemNewList.stream().filter(sgBShareTransferItem -> sgBShareTransferItem.getPsCProEcode().equals(item.getPsCProEcode()))
                            .collect(Collectors.toList());
                    break;
                default:
                    break;
            }
            if (CollectionUtils.isEmpty(outItemNewList)) {
                continue;
            }

            List<SgBShareTransferItem> successList = outItemNewList.stream().filter(sgBShareTransferItem ->
                            SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_SUCCESS.equals(sgBShareTransferItem.getItemStatus()))
                    .collect(Collectors.toList());
            List<SgBShareTransferItem> failList = outItemNewList.stream().filter(sgBShareTransferItem ->
                            SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_NO.equals(sgBShareTransferItem.getItemStatus()))
                    .filter(sgBShareTransferItem ->
                            SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_FAIL.equals(sgBShareTransferItem.getItemStatus()))
                    .collect(Collectors.toList());

            // 调拨结果明细 若有失败 直接更新为失败
            if (CollectionUtils.isNotEmpty(successList) && CollectionUtils.isEmpty(failList)) {
                updateImportItem.setItemStatus(SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_SUCCESS);
            } else if (CollectionUtils.isEmpty(successList) && CollectionUtils.isNotEmpty(failList)) {
                updateImportItem.setItemStatus(SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_FAIL);
            } else if (CollectionUtils.isNotEmpty(successList) && CollectionUtils.isNotEmpty(failList)) {
                updateImportItem.setItemStatus(SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_UNSUCCESS);
            } else if (CollectionUtils.isEmpty(successList) && CollectionUtils.isEmpty(failList)) {
                updateImportItem.setItemStatus(SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_NO);
            }

            updateImportItem.setModifierid(request.getLoginUser().getId().longValue());
            updateImportItem.setModifiername(request.getLoginUser().getName());
            updateImportItem.setModifieddate(new Date());

            importItemMapper.updateById(updateImportItem);
        }
    }

    /**
     * 更新聚合仓调拨单审核汇总信息
     */
    public void updateSgBShareTransferSubmitInfo(SgBShareTransferSaveRequest request, SgBShareTransfer sgBShareTransfer) {
        SgBShareTransfer updateTransfer = new SgBShareTransfer();
        // 总行数
        int totRowNum;
        // 总预计调拨数量
        BigDecimal totQty;
        // 总实际调拨数量
        BigDecimal totQtyOut;
        // 总吊牌金额
        BigDecimal totAmt;
        // 成功行数
        int successRowNum;

        LambdaQueryWrapper<SgBShareTransferImportItem> query = new LambdaQueryWrapper<>();
        query.eq(SgBShareTransferImportItem::getSgBShareTransferId, sgBShareTransfer.getId());
        List<SgBShareTransferImportItem> importItemList = importItemMapper.selectList(query);

        // 没有导入明细直接返回
        if (CollectionUtils.isEmpty(importItemList)) {
            return;
        }
        LambdaQueryWrapper<SgBShareTransferItem> queryItem = new LambdaQueryWrapper<>();
        queryItem.eq(SgBShareTransferItem::getSgBShareTransferId, sgBShareTransfer.getId());
        List<SgBShareTransferItem> itemList = itemMapper.selectList(queryItem);

        List<SgBShareTransferItem> successItemList = null;
        if (CollectionUtils.isNotEmpty(itemList)) {
            successItemList = itemList.stream().
                    filter(o -> SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_SUCCESS.equals(o.getItemStatus())).collect(Collectors.toList());
        }

        List<SgBShareTransferImportItem> failImportItemList = importItemList.stream().
                filter(o -> SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_FAIL.equals(o.getItemStatus())
                        || SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_NO.equals(o.getItemStatus())).collect(Collectors.toList());

        totQty = importItemList.stream().map(SgBShareTransferImportItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (CollectionUtils.isNotEmpty(successItemList)) {
            totQtyOut = successItemList.stream().map(SgBShareTransferItem::getQtyOut).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 成功记录的求吊牌金额总价
            // totAmt = successItemList.stream().map(SgBShareTransferItem::getPriceList).reduce(BigDecimal.ZERO, BigDecimal::multiply);
            totAmt = successItemList.stream().reduce(BigDecimal.ZERO, (x, y) -> {
                return x.add(y.getPriceList().multiply(y.getQty()));
            }, BigDecimal::add);
        } else {
            totQtyOut = BigDecimal.ZERO;
            totAmt = BigDecimal.ZERO;
        }

        successRowNum = CollectionUtils.isEmpty(successItemList) ? 0 : successItemList.size();

        totRowNum = CollectionUtils.isEmpty(itemList) ? 0 : itemList.size();

        updateTransfer.setRemark(sgBShareTransfer.getRemark());
        // 总预计调拨数量
        updateTransfer.setTotQty(totQty);
        // 总实际调拨数量
        updateTransfer.setTotQtyOut(totQtyOut);
        // 总调拨成功的金额
        updateTransfer.setTotAmt(totAmt);
        // 设置总行数
        updateTransfer.setTotRowNum(totRowNum);
        // 设置成功行数
        updateTransfer.setSuccessRowNum(new BigDecimal(successRowNum));
        // 根据成功行数设置审核状态为  全部审核/部分审核/未审核
        if (CollectionUtils.isNotEmpty(successItemList) && CollectionUtils.isEmpty(failImportItemList)) {
            updateTransfer.setStatus(SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_02);
        } else if (CollectionUtils.isNotEmpty(successItemList) && CollectionUtils.isNotEmpty(failImportItemList)) {
            // 设置部分审核
            updateTransfer.setStatus(SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_00);
        } else {
            // 设置未审核
            updateTransfer.setStatus(SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_01);
        }
        // 设置失败行数
        updateTransfer.setFailRowNum(new BigDecimal(totRowNum - successRowNum));
        updateTransfer.setTotAmt(totAmt);
        updateTransfer.setId(sgBShareTransfer.getId());

        // 设置审核人信息
        updateTransfer.setStatusId(request.getLoginUser().getId());
        updateTransfer.setStatusName(request.getLoginUser().getName());
        updateTransfer.setStatusTime(new Date());

        // 设置修改人信息
        updateTransfer.setModifierid(request.getLoginUser().getId().longValue());
        updateTransfer.setModifiername(request.getLoginUser().getName());
        updateTransfer.setModifierename(request.getLoginUser().getEname());
        updateTransfer.setModifieddate(new Date());
        mapper.updateById(updateTransfer);

    }

    /**
     * 保存聚合仓调拨结果明细updateById到数据库
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveSgBShareTransferItem(User loginUser, SgBShareTransfer sgBShareTransfer, List<SgBShareTransferItem> insertItemList) {
        if (CollectionUtils.isEmpty(insertItemList)) {
            return;
        }
        // 填充聚合仓调拨单导入明细主键id
        insertItemList.forEach(item -> {
            item.setId(ModelUtil.getSequence(SgConstants.SG_B_SHARE_TRANSFER_ITEM));
        });
        itemMapper.batchInsert(insertItemList);

        sgBShareTransfer.setStatusId(loginUser.getId());
        sgBShareTransfer.setStatusName(loginUser.getName());
        sgBShareTransfer.setStatusEname(loginUser.getEname());
        sgBShareTransfer.setStatusTime(new Date());
        mapper.updateById(sgBShareTransfer);

    }

    /**
     * 生成逻辑调拨单
     */
    //@Transactional(rollbackFor = Exception.class)
    public void saveTransfer(SgBShareTransfer sgBShareTransfer, User loginUser)
            throws NDSException {

        LambdaQueryWrapper<SgBShareTransferItem> query = new LambdaQueryWrapper<>();
        query.eq(SgBShareTransferItem::getSgBShareTransferId, sgBShareTransfer.getId());
        // 过滤查询失败未处理状态的结果明细数据
        query.le(SgBShareTransferItem::getItemStatus, SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_NO);
        List<SgBShareTransferItem> batchShareTransferItems = itemMapper.selectList(query);

        if (CollectionUtils.isEmpty(batchShareTransferItems)) {
            return;
        }

        // 获取虚拟运输类型
        CpCTranwayAssign cpCTranwayAssign;
        cpCTranwayAssign = CommonCacheValUtils.getCpCTranwayAssignpByName(SgShareConstants.CP_C_TRANWAY_ASSIGN_VIRTUAL_TYPE);

        // 同一张逻辑调拨单一个sku只能出现一次【聚合仓调拨单会有不同明细存在相同收发货逻辑仓及sku的情形】
        Map<String, List<SgBShareTransferItem>> storeSkuMap =
                batchShareTransferItems.stream().collect(Collectors.groupingBy(e -> e.getSenderStoreId()
                        + e.getReceiverStoreId() + e.getPsCSkuEcode()));

        List<SgBShareTransferItem> batchShareTransferItemsList = new ArrayList<>();

        Map<Long, List<Long>> itemIdMap = new HashMap<>();
        for (String storeSku : storeSkuMap.keySet()) {
            List<SgBShareTransferItem> itemList = storeSkuMap.get(storeSku);
            SgBShareTransferItem item = itemList.get(0);
            if (itemList.size() == 1) {
                batchShareTransferItemsList.add(item);
                continue;
            }
            BigDecimal qtyOut = item.getQtyOut();

            List<Long> itemIdList = new ArrayList<>();
            for (SgBShareTransferItem qtyOutItem : itemList) {
                if (Objects.equals(qtyOutItem.getId(), item.getId())) {
                    continue;
                }
                qtyOut = qtyOut.add(qtyOutItem.getQtyOut());
                itemIdList.add(qtyOutItem.getId());
            }
            item.setQtyOut(qtyOut);
            itemIdMap.put(item.getId(), itemIdList);
            batchShareTransferItemsList.add(item);
        }

        //按照收货仓发货仓分组  一组一个逻辑调拨单
        Map<Long, Map<Long, List<SgBShareTransferItem>>> itemGroupingByMapByStore = batchShareTransferItemsList.parallelStream()
                .filter(sgBShareTransferItem -> sgBShareTransferItem.getSenderStoreId() != null)
                .filter(sgBShareTransferItem -> sgBShareTransferItem.getReceiverStoreId() != null)
                .collect(Collectors.groupingBy(SgBShareTransferItem::getSenderStoreId, groupingBy(SgBShareTransferItem::getReceiverStoreId)));
        if (MapUtils.isEmpty(itemGroupingByMapByStore)) {
            return;
        }
        //循环明细 生成逻辑调拨单
        for (Long senderStoreId : itemGroupingByMapByStore.keySet()) {
            Map<Long, List<SgBShareTransferItem>> receiverStoreMap = itemGroupingByMapByStore.get(senderStoreId);
            if (MapUtils.isEmpty(receiverStoreMap)) {
                continue;
            }
            for (Long receiverStoreId : receiverStoreMap.keySet()) {
                SgBStoTransferBillSaveRequest insertTransfer = new SgBStoTransferBillSaveRequest();
                //生成逻辑调拨单
                List<SgBShareTransferItem> batchTransferItems = receiverStoreMap.get(receiverStoreId);
                SgBShareTransferItem transferItem = batchTransferItems.get(0);

                setSgBStoTransferSaveRequest(sgBShareTransfer, loginUser, cpCTranwayAssign, transferItem, insertTransfer);

                List<SgBStoTransferItemSaveRequest> saveItems = new ArrayList<>();

                setSgBShareTransferItemList(batchTransferItems, saveItems);

                insertTransfer.setItems(saveItems);
                insertTransfer.setLoginUser(loginUser);
                insertTransfer.setObjId(-1L);
                //会决定是否传drp,true传
                insertTransfer.setR3(Boolean.TRUE);
                // 是否允许生成负库存 有疑问
                insertTransfer.setNegativePrein(Boolean.FALSE);

                ValueHolderV14<SgR3BaseResult> save = null;
                boolean isExcept = false;
                String failReason = null;

                try {
                    // 前端页面进来正常保存报错信息到结果明细
                    save = sgBStoTransferSaveAndSubmitService.saveAndSubmit(insertTransfer);
                    if (save == null) {
                        failReason = "聚合仓调拨单：" + sgBShareTransfer.getBillNo() +
                                "调用逻辑调拨单保存并审核方法未返回任何数据";
                        isExcept = true;
                    }
                } catch (Exception e) {
                    isExcept = true;
                    failReason = "聚合仓调拨单：" + sgBShareTransfer.getBillNo() +
                            "生成逻辑调拨单失败:" +
                            (null == save || StringUtils.isBlank(save.getMessage()) ? e.getMessage() : save.getMessage());
                }
                // 更新聚合仓调拨结果明细状态：成功或失败
                SgR3BaseResult data = null;
                int code = ResultCode.FAIL;
                if (null != save && ResultCode.SUCCESS == save.getCode()) {
                    code = ResultCode.SUCCESS;
                }
                if (!isExcept) {
                    data = save.getData();
                    if (code == ResultCode.FAIL) {
                        failReason = save.getMessage();
                    }
                }

                for (SgBShareTransferItem item : batchTransferItems) {
                    SgBShareTransferItem updateItem = new SgBShareTransferItem();
                    updateItem.setId(item.getId());
                    updateItem.setItemStatus(code);
                    updateItem.setFailReason(failReason);
                    if (failReason != null) {
                        updateItem.setItemStatus(SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_FAIL);
                        // updateItem.setQtyOut(BigDecimal.ZERO);
                    } else {
                        updateItem.setFailReason("");
                        updateItem.setItemStatus(SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_SUCCESS);
                    }
                    if (null != data) {
                        updateItem.setSgBStoTransferId(data.getDataJo().getLong("objid"));
                        updateItem.setSgBStoTransferBillNo(data.getBillNo());
                    }
                    itemMapper.updateById(updateItem);

                    List<Long> itemIds = itemIdMap.get(item.getId());
                    if (CollectionUtils.isNotEmpty(itemIds)) {
                        for (Long id : itemIds) {
                            updateItem.setId(id);
                            itemMapper.updateById(updateItem);
                        }
                    }
                }
            }

        }
    }

    /**
     * 设置逻辑调拨单明细
     */
    private void setSgBShareTransferItemList(List<SgBShareTransferItem> batchTransferItems, List<SgBStoTransferItemSaveRequest> saveItems) {
        for (SgBShareTransferItem item : batchTransferItems) {
            SgBStoTransferItemSaveRequest itemSaveRequest = new SgBStoTransferItemSaveRequest();
            //实际调拨数量等于逻辑调拨单的调拨数量
            itemSaveRequest.setQty(item.getQtyOut());
            itemSaveRequest.setQtyOut(BigDecimal.ZERO);
            itemSaveRequest.setQtyIn(BigDecimal.ZERO);
            itemSaveRequest.setSourceBillItemId(item.getId());
            itemSaveRequest.setQtyDiff(BigDecimal.ZERO);
            // 吊牌价 吊牌金额设置是否正确需要注意
            itemSaveRequest.setAmt(item.getAmt());
            itemSaveRequest.setPriceList(item.getPriceList());
            itemSaveRequest.setPsCSpec1Id(item.getPsCSpec1Id());
            itemSaveRequest.setPsCSpec1Ecode(item.getPsCSpec1Ecode());
            itemSaveRequest.setPsCSpec1Ename(item.getPsCSpec1Ename());
            itemSaveRequest.setPsCSpec2Id(item.getPsCSpec2Id());
            itemSaveRequest.setPsCSpec2Ecode(item.getPsCSpec2Ecode());
            itemSaveRequest.setPsCSpec2Ename(item.getPsCSpec2Ename());
            itemSaveRequest.setPsCSkuId(item.getPsCSkuId());
            itemSaveRequest.setPsCSkuEcode(item.getPsCSkuEcode());
            itemSaveRequest.setPsCProId(item.getPsCProId());
            itemSaveRequest.setPsCProEcode(item.getPsCProEcode());
            itemSaveRequest.setPsCProEname(item.getPsCProEname());
            saveItems.add(itemSaveRequest);
        }
    }

    /**
     * 设置调拨单主表记录信息
     */
    private void setSgBStoTransferSaveRequest(SgBShareTransfer sgBShareTransfer, User loginUser, CpCTranwayAssign cpCTranwayAssign,
                                              SgBShareTransferItem transferItem, SgBStoTransferBillSaveRequest insertTransfer) {
        SgBStoTransferSaveRequest transferSaveRequest = new SgBStoTransferSaveRequest();
        transferSaveRequest.setId(-1L);
        transferSaveRequest.setBillDate(new Date());
        transferSaveRequest.setSourceBillId(sgBShareTransfer.getId());
        transferSaveRequest.setSourceBillNo(sgBShareTransfer.getBillNo());
        // 设置值有疑问【元数据需要配置成39】
        transferSaveRequest.setSourceBillType(SgConstantsIF.BILL_SG_B_SHARE_TRANSFER);
        transferSaveRequest.setSourceBillDate(sgBShareTransfer.getBillDate());
        transferSaveRequest.setLoginUser(loginUser);
        transferSaveRequest.setTransferType(SgConstants.TRANSFER_TYPE_NORMAL);
        transferSaveRequest.setRemark("由聚合仓调拨单：" + sgBShareTransfer.getBillNo() + "审核生成！");
        // 值设置有疑问[不设置传drp状态一定会传]
        // transferSaveRequest.setDrpStatus(SgStoreConstants.SEND_DRP_STATUS_NOT_PASS);
        transferSaveRequest.setDrpBillType(SgStoreConstants.DRP_BILL_TYPE_TF);

        //918新增的字段
        transferSaveRequest.setIsAutoConfirm(sgBShareTransfer.getIsAutoConfirm());
        //transferSaveRequest.setTmsDate(item.getTmsDate());
        // transferSaveRequest.setReceiverAddress(item.getReceiverAddress());
        // transferSaveRequest.setDemandType(item.getDemandType() != null ?
        //        item.getDemandType().toString() : null);
        // 设置运输类型:值为虚拟
        transferSaveRequest.setCpCTranwayAssignId(cpCTranwayAssign.getId());
        // transferSaveRequest.setIsTemporaryAddress(item.getIsTemporaryAddress());
     /*   transferSaveRequest.setIsAutoOut(transferItem.getIsAutoOut());
        transferSaveRequest.setIsAutoIn(transferItem.getIsAutoIn());*/
        // 经产品确认，设置自动出入库为Y
        transferSaveRequest.setIsAutoIn("Y");
        transferSaveRequest.setIsAutoOut("Y");
        // 设置发货逻辑仓id
        transferSaveRequest.setSenderStoreId(transferItem.getSenderStoreId());
        // 设置发货逻辑仓编码
        transferSaveRequest.setSenderStoreEcode(transferItem.getSenderStoreEcode());
        // 设置发货逻辑仓名称
        transferSaveRequest.setSenderStoreEname(transferItem.getSenderStoreEname());
        // 设置发货经销商id
        transferSaveRequest.setSenderCustomerId(transferItem.getSenderCustomerId());
        // 设置收货逻辑仓id
        transferSaveRequest.setReceiverStoreId(transferItem.getReceiverStoreId());
        // 设置收货逻辑仓编码
        transferSaveRequest.setReceiverStoreEcode(transferItem.getReceiverStoreEcode());
        // 设置收货逻辑仓名称
        transferSaveRequest.setReceiverStoreEname(transferItem.getReceiverStoreEname());
        // 设置收货逻辑仓经销商id
        transferSaveRequest.setReceiverCustomerId(transferItem.getReceiverCustomerId());
        insertTransfer.setTransferSaveRequest(transferSaveRequest);
    }

    /**
     * 检查聚合仓调拨单主表记录是否存在
     *
     * @param isSubmit 是否审核操作
     */
    private SgBShareTransfer checkSgBShareTransfer(SgBShareTransferSaveRequest request, Long objId, boolean isSubmit) {
        // 聚合仓调拨单主表主键不存在
        SgBShareTransfer sgBShareTransfer = mapper.selectById(objId);
        if (sgBShareTransfer == null) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("当前主表记录已不存在！", request.getLoginUser().getLocale());
            } else {
                request.setCode(ResultCode.FAIL);
                request.setMsg("当前主表记录已不存在！");
                return null;
            }
        }

        Integer status = null;
        if (null != sgBShareTransfer) {
            status = sgBShareTransfer.getStatus();
        }

        if (null == status) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("当前主表记录单据状态不能为空！", request.getLoginUser().getLocale());
            } else {
                request.setCode(ResultCode.FAIL);
                request.setMsg("当前主表记录单据状态不能为空！");
                return null;
            }
        }
        if (SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_02.equals(status)) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("当前主表记录已审核，不允许编辑！", request.getLoginUser().getLocale());
            } else {
                request.setCode(ResultCode.FAIL);
                request.setMsg("当前主表记录已审核，不允许编辑！");
                return null;
            }
        }

        // 当前行代码是否多余 是否有删除的必要
        if (!isSubmit && SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_02.equals(status)) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("当前主表记录已审核，不允许编辑！", request.getLoginUser().getLocale());
            } else {
                request.setCode(ResultCode.FAIL);
                request.setMsg("当前主表记录已审核，不允许编辑！");
                return null;
            }
        }

        if (!isSubmit && SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_00.equals(status)) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("当前主表记录已审核，不允许编辑！", request.getLoginUser().getLocale());
            } else {
                request.setCode(ResultCode.FAIL);
                request.setMsg("当前主表记录已审核，不允许编辑！");
                return null;
            }
        }

        if (SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_04.equals(status)) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("当前主表记录已作废，不允许编辑！", request.getLoginUser().getLocale());
            } else {
                request.setCode(ResultCode.FAIL);
                request.setMsg("当前主表记录已作废，不允许编辑！");
                return null;
            }
        }
        return sgBShareTransfer;
    }

    /**
     * 生成聚合仓调拨单结果明细
     */
    private void generateSgBShareTransferItem(SgBShareTransferSaveRequest request, List<SgBShareTransferItem> insertItemList,
                                              SgBShareTransferImportItem item, List<SgShareTransferResult> resultList) {
        // 条码维度预计调拨量
        int qty = item.getQty().intValue();
        // 商品款号预计调拨量
        int itemQty = qty;
        // 某个条码的可用库存
        int qtyAvailable;
        // 条码或者款号维度【1：条码 2:款号,同一个款号下面可能包含多个条码】
        Integer transferLatitude = item.getTransferDimension();

        List<SgBShareTransferImportItemDto> sendStoreList = null;

        // true【商品维度】 false【条码维度】
        boolean isProLatitude = false;
        switch (transferLatitude) {
            // 条码维度数据查询
            case SgShareConstants.SG_B_SHARE_TRANSFER_DIMENSION_SKU:
                sendStoreList = importItemMapper.selectSendStoreQtyAvailableByShareStoreId(item.getSenderShareStoreId(),
                        item.getReceiverShareStoreId(), item.getPsCSkuEcode(), null);
                break;
            // 商品维度数据查询
            case SgShareConstants.SG_B_SHARE_TRANSFER_DIMENSION_PRO:
                sendStoreList = importItemMapper.selectSendStoreQtyAvailableByShareStoreId(item.getSenderShareStoreId(),
                        item.getReceiverShareStoreId(), null, item.getPsCProEcode());
                isProLatitude = true;
                break;
            default:
                break;

        }

        // 条码或款号无可用库存记录直接返回
        if (CollectionUtils.isEmpty(sendStoreList)) {
            SgBShareTransferImportItem updateImportItem = new SgBShareTransferImportItem();
            updateImportItem.setId(item.getId());
            updateImportItem.setRemark(StringUtils.isBlank(item.getRemark()) ? "[逻辑仓无可用库存]" : item.getRemark().replace("[逻辑仓无可用库存]", "")
                    + "[逻辑仓无可用库存]");
            updateImportItem.setItemStatus(SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_FAIL);
            updateImportItem.setModifierid(request.getLoginUser().getId().longValue());
            updateImportItem.setModifiername(request.getLoginUser().getName());
            updateImportItem.setModifieddate(new Date());
            importItemMapper.updateById(updateImportItem);

            if (!request.isR3()) {
                SgShareTransferResult result = new SgShareTransferResult();
                result.setSourceBillItemId(item.getSourceBillItemId());
                result.setMsg("逻辑仓无可用库存");
                result.setSku(item.getPsCSkuOrPro());
                resultList.add(result);
            }
            return;
        }
        // 获取条码去重集合
        List<String> psCSkuEcodeList = sendStoreList.stream().map(SgBShareTransferImportItemDto::getPsCSkuEcode)
                .collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        // 可用库存总和 SUM(Y)
        int sumY = sendStoreList.stream()
                .filter(o -> (null != o.getQtyAvailable() && o.getQtyAvailable().intValue() > 0))
                .map(SgBShareTransferImportItemDto::getQtyAvailable)
                .reduce(BigDecimal.ZERO, BigDecimal::add).intValue();

        // lastQty最后一行调拨量
        int lastQty = 0;
        int sumZ = 0;

        for (String sku : psCSkuEcodeList) {
            if (isProLatitude) {
                // 款号维度 需要特殊处理
                qty = 0;
                int availableQty = sendStoreList.stream().filter(o -> sku.equals(o.getPsCSkuEcode())
                                && o.getQtyAvailable().intValue() > 0)
                        .map(SgBShareTransferImportItemDto::getQtyAvailable)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).intValue();
                // [Y/sum(Y)] * X 【该商品下各条码的可调拨量Y; 获取该商品的可调拨量 sum(Y) 聚合仓调拨导入明细数量 X;
                if (sumY > 0 && availableQty > 0) {
                    qty = new BigDecimal(availableQty).multiply(new BigDecimal(itemQty))
                            .divide(new BigDecimal(sumY), 0, BigDecimal.ROUND_HALF_UP).intValue();
                    // 款号维度预计调拨量
                    sumZ = qty;
                }

                // 获取list最后一条记录
                String lastSku = psCSkuEcodeList.get(psCSkuEcodeList.size() - 1);
                // 判断当前sku是否最后最后一个sku
                if (lastSku.equals(sku)) {
                    // 【最后一个条码，需要特殊处理，即预计调拨数量Z= X – 除最后一行的sum(Z)】
                    qty = itemQty - lastQty;
                    // 设置款号维度对应条码预计调拨数量
                    sumZ = qty;
                } else {
                    lastQty = lastQty + qty;
                }

            }

            //每生成一条明细都要生成新的id
            for (SgBShareTransferImportItemDto dto : sendStoreList) {
                // 款号维度包含多个不同sku,只处理当前循环的sku数据
                if (!sku.equals(dto.getPsCSkuEcode())) {
                    continue;
                }

                // 已经满足预调拨数,不继续往下走
                if (qty <= 0) {
                    continue;
                }

                qtyAvailable = (null == dto.getQtyAvailable() ? 0 : (dto.getQtyAvailable().intValue()));

                // qtyAvailable大于0
                if (qtyAvailable <= 0) {
                    break;
                }
                SgBShareTransferItem insertItem = new SgBShareTransferItem();
                BeanUtils.copyProperties(dto, insertItem);
                insertItem.setRemark(StringUtils.isBlank(item.getRemark()) ? "" : item.getRemark().replace("[逻辑仓无可用库存]", ""));
                if (StringUtils.isNotBlank(item.getRemark()) && item.getRemark().indexOf("[逻辑仓无可用库存]") > 0) {
                    SgBShareTransferImportItem updateImportItem2 = new SgBShareTransferImportItem();
                    updateImportItem2.setId(item.getId());
                    updateImportItem2.setRemark(item.getRemark().replace("[逻辑仓无可用库存]", ""));
                    importItemMapper.updateById(updateImportItem2);
                }
                // 先设置自动入库为空 后续设置收货逻辑仓环节设置是否自动出入库
                insertItem.setIsAutoIn(null);
                insertItem.setSgBShareTransferId(request.getObjId());
                StorageUtils.setBModelDefalutData(insertItem, request.getLoginUser());
                // 设置预计调拨数量唔
                if (qtyAvailable >= qty) {
                    // 设置预计调拨数量
                    // insertItem.setQty(isProLatitude ? new BigDecimal(sumZ) : item.getQty());
                    insertItem.setQty(isProLatitude ? new BigDecimal(sumZ) : new BigDecimal(qty));
                    // 设置实际需要调拨数量
                    insertItem.setQtyOut(new BigDecimal(qty));
                    // 设置吊牌金额
                    insertItem.setAmt(new BigDecimal(qty).multiply(dto.getPriceList()));
                    qty = 0;
                } else {
                    qty = qty - qtyAvailable;
                    // 设置预计调拨数量
                    // insertItem.setQty(isProLatitude ? new BigDecimal(sumZ) : item.getQty());
                    insertItem.setQty(isProLatitude ? new BigDecimal(sumZ) : new BigDecimal(qtyAvailable));
                    // 设置实际需要调拨数量
                    insertItem.setQtyOut(new BigDecimal(qtyAvailable));
                    // 设置吊牌金额
                    insertItem.setAmt(new BigDecimal(qtyAvailable).multiply(dto.getPriceList()));
                }
                SgBShareTransferImportItemDto receiveStore;
                receiveStore = importItemMapper.selectReceiveStoreByShareStoreId(item.getReceiverShareStoreId(),
                        dto.getCpCPhyWarehouseId(), dto.getSenderStoreId());

                // 设置收货逻辑仓
                if (receiveStore != null) {
                    insertItem.setReceiverShareStoreId(receiveStore.getReceiverShareStoreId());
                    insertItem.setReceiverShareStoreEcode(receiveStore.getReceiverShareStoreEcode());
                    insertItem.setReceiverShareStoreEname(receiveStore.getReceiverShareStoreEname());
                    insertItem.setReceiverStoreId(receiveStore.getReceiverStoreId());
                    insertItem.setReceiverStoreEcode(receiveStore.getReceiverStoreEcode());
                    insertItem.setReceiverStoreEname(receiveStore.getReceiverStoreEname());
                    insertItem.setReceiverCustomerId(receiveStore.getReceiverCustomerId());
                    insertItem.setReceiverCustomerEcode(receiveStore.getReceiverCustomerEcode());
                    insertItem.setReceiverCustomerEname(receiveStore.getReceiverCustomerEname());
                    insertItem.setIsAutoIn(receiveStore.getIsAutoIn());
                    insertItem.setTransferDimension(item.getTransferDimension());
                    insertItem.setSourceBillItemId(item.getSourceBillItemId());
                    insertItem.setItemStatus(SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_NO);
                }

                // insertItem.setId(ModelUtil.getSequence(SgConstants.SG_B_SHARE_TRANSFER_ITEM));
                StorageUtils.setBModelDefalutData(insertItem, request.getLoginUser());
                insertItemList.add(insertItem);

            }
        }
    }

    /**
     * 聚合仓调拨单保存或更新
     *
     * @param request 入参
     * @return return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> save(SgBShareTransferSaveRequest request) {

        log.info("开始调用聚合仓保存方法request:{}", JSON.toJSONString(request));

        if (request.getObjId() == null || request.getObjId() < 0) {
            checkParams(request, true);
            //主表校验不通过的直接返回错误信息
            if (request.getCode() == ResultCode.FAIL && (!request.isFlag())) {
                return new ValueHolderV14<>(ResultCode.FAIL, SgConstants.MESSAGE_STATUS_FAIL);
            }
            try {
                return insert(request);
            } catch (Exception e) {
                log.error("聚合仓调拨单导入明细保存到数据库. error:{}", Throwables.getStackTraceAsString(e));
                return new ValueHolderV14<>(ResultCode.FAIL, SgConstants.MESSAGE_STATUS_FAIL);
            }
        } else {
            SgBShareTransferSaveRequest saveRequest = checkParams(request, false);
            //主表校验不通过的直接返回错误信息
            if (request.getCode() == ResultCode.FAIL && (!request.isFlag())) {
                return new ValueHolderV14<>(ResultCode.FAIL, SgConstants.MESSAGE_STATUS_FAIL);
            }
            return update(request, saveRequest.getSgBShareTransfer());
        }
    }

    /**
     * 聚合仓调拨单更新及聚合仓调拨单导入明细保存
     */
    private ValueHolderV14<SgR3BaseResult> update(SgBShareTransferSaveRequest request, SgBShareTransfer transfer) {

        if (transfer == null) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("当前调拨单未保存！",
                        request.getLoginUser().getLocale());
            } else {
                request.setCode(ResultCode.FAIL);
                request.setMsg("当前调拨单未保存！");
                return null;
            }
        }

        // 插入或者更新明细表
        List<SgBShareTransferImportItemRequest> importItems = request.getImportItemListRequest();
        List<SgBShareTransferImportItem> insertItemList = new ArrayList<>();
        List<SgBShareTransferImportItem> updateItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(importItems)) {
            for (SgBShareTransferImportItemRequest item : importItems) {
                if (null != item.getId() && item.getId() > 0) {
                    SgBShareTransferImportItem updateItem = new SgBShareTransferImportItem();
                    updateItem.setId(item.getId());
                    updateItem.setQty(item.getQty());
                    updateItemList.add(updateItem);
                    continue;
                }

                // 设置新增记录
                addInserItem(request, transfer, insertItemList, item);
            }
        }

        if (CollectionUtils.isNotEmpty(insertItemList)) {
            importItemMapper.batchInsert(insertItemList);
        }

        if (CollectionUtils.isNotEmpty(updateItemList)) {
            for (SgBShareTransferImportItem item : updateItemList) {
                importItemMapper.updateById(item);
            }
        }

        // 更新主表
        updateSgBShareTransfer(request, transfer);

        SgR3BaseResult baseResult = new SgR3BaseResult();
        baseResult.setDataJo(transfer.getId(), SgConstants.SG_B_SHARE_TRANSFER.toUpperCase());
        baseResult.setBillNo(transfer.getBillNo());
        return new ValueHolderV14<>(baseResult, ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
    }

    /**
     * 更新主表
     *
     * @param request
     * @param transfer
     */
    private void updateSgBShareTransfer(SgBShareTransferSaveRequest request, SgBShareTransfer transfer) {

        // 调拨单预计调拨数量，也是明细表中预计调拨数总和
        BigDecimal totQty = BigDecimal.ZERO;
        // 总吊牌金额
        BigDecimal totAmt = BigDecimal.ZERO;

        User loginUser = request.getLoginUser();
        if (null != request.getSgBShareTransferRequest()) {
            transfer.setRemark(request.getSgBShareTransferRequest().getRemark());
            transfer.setIsAutoConfirm(request.getSgBShareTransferRequest().getIsAutoConfirm());
            transfer.setBillDate(request.getSgBShareTransferRequest().getBillDate());
        }
        transfer.setModifierid(loginUser.getId().longValue());
        transfer.setModifiername(loginUser.getName());
        transfer.setModifierename(loginUser.getEname());
        transfer.setModifieddate(new Date());

        LambdaQueryWrapper<SgBShareTransferImportItem> query = new LambdaQueryWrapper<>();
        query.eq(SgBShareTransferImportItem::getSgBShareTransferId, transfer.getId());
        List<SgBShareTransferImportItem> importItemList = importItemMapper.selectList(query);

        if (CollectionUtils.isNotEmpty(importItemList)) {
            for (SgBShareTransferImportItem item : importItemList) {
                // 设置总吊牌价
                if (item.getTransferDimension() == null) {
                    continue;
                }
                switch (item.getTransferDimension()) {
                    // 条码维度数据查询
                    case SgShareConstants.SG_B_SHARE_TRANSFER_DIMENSION_SKU:
                        List<String> skuEcodeList = new ArrayList<>(1);
                        skuEcodeList.add(item.getPsCSkuOrPro());
                        SkuInfoQueryRequest skuInfoRequest = new SkuInfoQueryRequest();
                        skuInfoRequest.setSkuEcodeList(skuEcodeList);
                        HashMap<String, PsCProSkuResult> skuInfo = basicPsQueryService.getSkuInfoByEcode(skuInfoRequest);
                        if (null != skuInfo && !skuInfo.isEmpty()) {
                            PsCProSkuResult psCProSkuResult = skuInfo.get(item.getPsCSkuOrPro());
                            if (psCProSkuResult != null) {
                                totAmt = item.getQty().multiply(psCProSkuResult.getPricelist()).add(totAmt);
                            }
                        }
                        break;
                    // 商品维度数据查询
                    case SgShareConstants.SG_B_SHARE_TRANSFER_DIMENSION_PRO:
                        ProSkuResult proResult = basicPsQueryService.getSkuInfoByProEcode(item.getPsCProEcode());
                        if (null != proResult && CollectionUtils.isNotEmpty(proResult.getProSkuList())) {
                            PsCProSkuResult psCProResult = proResult.getProSkuList().get(0);
                            totAmt = item.getQty().multiply(psCProResult.getPricelist()).add(totAmt);
                        }
                        break;
                    default:
                        break;
                }
                // 循环遍历 汇总预计调拨数
                totQty = item.getQty().add(totQty);
            }
        }


        // 预计调拨数量
        transfer.setTotQty(totQty);
        // 实际调拨数量
        transfer.setTotQtyOut(BigDecimal.ZERO);
        // 总吊牌金额
        transfer.setTotAmt(totAmt);
        // 总记录行数
        transfer.setTotRowNum(importItemList.size());
        mapper.updateById(transfer);
    }

    /**
     * 聚合仓调拨单新增
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> insert(SgBShareTransferSaveRequest request) {
        SgBShareTransferRequest transferRequest = request.getSgBShareTransferRequest();

        SgBShareTransfer transfer = new SgBShareTransfer();
        BeanUtils.copyProperties(transferRequest, transfer);
        String billNo = SgStoreUtils.getBillNo(SgStoreConstants.SEQ_SG_B_SHARE_TRANSFER,
                SgConstants.SG_B_SHARE_TRANSFER, transfer,
                request.getLoginUser().getLocale());
        String lockKsy = SgConstants.SG_B_SHARE_TRANSFER_IMPORT_ITEM + ":insert:" + billNo;
        SgRedisLockUtils.lock(lockKsy);
        Long objId = null;
        log.info("聚合仓调拨单导入明细保存入库参数:{}", JSON.toJSONString(request));
        try {
            transfer.setBillNo(billNo);
            transfer.setStatus(SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_01);
            objId = ModelUtil.getSequence(SgConstants.SG_B_SHARE_TRANSFER);
            transfer.setId(objId);
            StorageUtils.setBModelDefalutData(transfer, request.getLoginUser());
            transfer.setOwnerename( request.getLoginUser().getEname());
            transfer.setModifierename( request.getLoginUser().getEname());
            mapper.insert(transfer);

            // 插入或者更新明细表
            List<SgBShareTransferImportItemRequest> importItems = request.getImportItemListRequest();
            List<SgBShareTransferImportItem> insertItemList = new ArrayList<>();

            // 调拨单预计调拨数量，也是明细表中预计调拨数总和
            BigDecimal totQty = BigDecimal.ZERO;
            // 总吊牌金额
            BigDecimal totAmt = BigDecimal.ZERO;
            // 总记录行数
            int totRowNum = 0;

            if (CollectionUtils.isNotEmpty(importItems)) {
                for (SgBShareTransferImportItemRequest item : importItems) {
                    addInserItem(request, transfer, insertItemList, item);
                    // 每条明细的总价汇总
                    totAmt = item.getQty().multiply(item.getPriceList()).add(totAmt);
                    // 循环遍历 汇总预计调拨数
                    totQty = item.getQty().add(totQty);
                    totRowNum += 1;
                }
            }

            // 插入明细表后 回写主表的预计调拨数量、实际调拨数量、总吊牌金额、总记录行数
            // 预计调拨数量
            transfer.setTotQty(totQty);
            // 实际调拨数量
            transfer.setTotQtyOut(totQty);
            // 总吊牌金额
            transfer.setTotAmt(totAmt);
            // 总记录行数
            transfer.setTotRowNum(totRowNum);
            //
            transfer.setIsAutoConfirm(transferRequest.getIsAutoConfirm());
            //更新主表数据
            mapper.updateById(transfer);

            if (CollectionUtils.isNotEmpty(insertItemList)) {
                importItemMapper.batchInsert(insertItemList);
            }
        } catch (Exception e) {
            AssertUtils.logAndThrowException("聚合仓调拨单保存失败！", e, Locale.getDefault());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        SgR3BaseResult baseResult = new SgR3BaseResult();
        baseResult.setDataJo(objId, SgConstants.SG_B_SHARE_TRANSFER.toUpperCase());
        baseResult.setBillNo(transfer.getBillNo());
        return new ValueHolderV14<>(baseResult, ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
    }

    private void addInserItem(SgBShareTransferSaveRequest request, SgBShareTransfer transfer,
                              List<SgBShareTransferImportItem> insertItemList, SgBShareTransferImportItemRequest item) {
        SgBShareTransferImportItem insertItem = new SgBShareTransferImportItem();
        BeanUtils.copyProperties(item, insertItem);
        Long itemObjId = ModelUtil.getSequence(SgConstants.SG_B_SHARE_TRANSFER_IMPORT_ITEM);
        insertItem.setId(itemObjId);
        //SgBStorage sgBStorage = sgBStorageMapper.selectByPsCSkuEcode(item.getPsCSkuEcode());
        insertItem.setSgBShareTransferId(transfer.getId());
        //insertItem.setPsCProEname(sgBStorage.getPsCProEname());
        insertItem.setItemStatus(item.getItemStatus());
        // 设置聚合仓调拨单界面新增的数据SourceBillItemId字段值跟当前明细主键id一致
        if (request.isR3()) {
            insertItem.setSourceBillItemId(itemObjId);
        }
        StorageUtils.setBModelDefalutData(insertItem, request.getLoginUser());
        insertItemList.add(insertItem);
    }


    /**
     * 检查参数合法性
     */
    private SgBShareTransferSaveRequest checkParams(SgBShareTransferSaveRequest request, Boolean isSave) {
        AssertUtils.notNull(request, "请求参数不能为空");

        SgBShareTransferSaveRequest resultRequest = new SgBShareTransferSaveRequest();
        SgBShareTransfer sgBShareTransfer;
        Long objId = request.getObjId();
        User loginUser = request.getLoginUser();
        if (!isSave) {
            sgBShareTransfer = checkSgBShareTransfer(request, objId, false);
            resultRequest.setSgBShareTransfer(sgBShareTransfer);
            if (request.getCode() == ResultCode.FAIL) {
                request.setFlag(false);
                return resultRequest;
            }
        }

        List<SgBShareTransferImportItemRequest> importItems = request.getImportItemListRequest();
        if (CollectionUtils.isEmpty(importItems)) {
            if (request.isR3()) {
                return resultRequest;
            } else {
                request.setCode(ResultCode.FAIL);
                request.setMsg("没有可保存的明细记录");
                request.setFlag(false);
                return request;
            }
        }

        // 填充收发聚合仓编码
        fillShareStoreEcode(importItems);
        StringBuffer msg = new StringBuffer();
        List<SgBShareTransferImportItemRequest> successImportItems = new ArrayList<>();
        List<SgShareTransferResult> sgShareTransferResults = new ArrayList<>();
        int i = 0;
        importItems.forEach(item -> {
            Long itemId = item.getId();
            SgBShareTransferImportItem importItem = null;
            if (itemId != null && itemId > 0) {
                importItem = importItemMapper.selectById(itemId);
            }
            if (importItem == null && itemId != null && itemId > 0) {
                if (request.isR3()) {
                    AssertUtils.logAndThrow("聚合发货仓: " + item.getSenderShareStoreEcode() + " 聚合收货仓: "
                                    + item.getReceiverShareStoreEcode() + " 商品或款号：" + item.getPsCSkuOrPro() + "，当前明细记录已不存在！",
                            request.getLoginUser().getLocale());
                } else {
                    // 返回封装错误信息
                    SgShareTransferResult sgShareTransferResult = new SgShareTransferResult();
                    sgShareTransferResult.setSourceBillItemId(item.getSourceBillItemId());
                    sgShareTransferResult.setMsg("第" + (i + 1) + "行记录聚合发货仓: " + item.getSenderShareStoreEcode() + " 聚合收货仓: "
                            + item.getReceiverShareStoreEcode() + " 商品或款号：" + item.getPsCSkuOrPro() + "，当前明细记录已不存在！");
                    sgShareTransferResult.setSku(item.getPsCSkuOrPro());
                    sgShareTransferResults.add(sgShareTransferResult);
                    return;
                }
            }
            // 检查新增或者修改参数
            if (importItem == null) {
                //校验不通过，当前循环不执行
                boolean isFalg = checkInsertParam(request, loginUser, item, i, sgShareTransferResults);
                if (!isFalg) {
                    return;
                }

            }
            if (Objects.nonNull(importItem)) {
                importItem.setSenderShareStoreEcode(item.getSenderShareStoreEcode());
                importItem.setSenderShareStoreEname(item.getSenderShareStoreEname());
                importItem.setReceiverShareStoreEcode(item.getReceiverShareStoreEcode());
                importItem.setReceiverShareStoreEname(item.getReceiverShareStoreEname());
                boolean isFalg = checkUpdateParam(request, loginUser, importItem, i, sgShareTransferResults);
                if (!isFalg) {
                    return;
                }
            }

            // 设置聚合仓调拨单导入明细条码或者款号维度
            setImportItemTransferDimension(request, item, loginUser);

            // 更新聚合仓调拨单导入明细的数量【聚合发货仓id,聚合收货仓id,款号或者条码字段一样】
            SgBShareTransferImportItem queryImportItem = getSgBShareTransferImportItem(objId, item, request.isR3());
            if (null != queryImportItem && Objects.nonNull(queryImportItem.getQty()) && Objects.nonNull(item.getQty())) {
                item.setId(queryImportItem.getId());
                item.setQty(new BigDecimal(queryImportItem.getQty().intValue() + item.getQty().intValue()));
            }
            successImportItems.add(item);

        });

        resultRequest.setImportItemListRequest(successImportItems);
        if (CollectionUtils.isNotEmpty(sgShareTransferResults)) {
            request.setCode(ResultCode.FAIL);
            request.setSgShareTransferResults(sgShareTransferResults);
            request.setFlag(false);
            return request;
            //if (sgShareTransferResults.size() == importItems.size()) {
            // request.setFlag(false);
            //  return request;
            // }
        }
        return resultRequest;
    }

    // 检查新增
    private boolean checkInsertParam(SgBShareTransferSaveRequest request, User loginUser,
                                     SgBShareTransferImportItemRequest item, int i, List<SgShareTransferResult> sgShareTransferResults) {
        if (null == item.getSenderShareStoreId() || null == item.getReceiverShareStoreId()) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("聚合收发货仓不允许空！", loginUser.getLocale());
            } else {
                SgShareTransferResult sgShareTransferResult = new SgShareTransferResult();
                sgShareTransferResult.setSourceBillItemId(request.getSgBShareTransfer().getSourceBillId());
                sgShareTransferResult.setMsg("第" + (i + 1) + "行记录聚合发货仓或收货仓为空，无法保存明细！");
                sgShareTransferResult.setSku(item.getPsCSkuOrPro());
                sgShareTransferResults.add(sgShareTransferResult);
                return false;
            }
        }
        if (StringUtils.isBlank(item.getPsCSkuOrPro())) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("商品或款号不允许为空！", loginUser.getLocale());
            } else {
                SgShareTransferResult sgShareTransferResult = new SgShareTransferResult();
                sgShareTransferResult.setSourceBillItemId(item.getSourceBillItemId());
                sgShareTransferResult.setMsg("第" + (i + 1) + "行记录商品或款号不允许为空！");
                sgShareTransferResult.setSku(item.getPsCSkuOrPro());
                sgShareTransferResults.add(sgShareTransferResult);
                return false;
            }
        }
        if (item.getQty() == null) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("聚合发货仓: " + item.getSenderShareStoreEcode() + " 聚合收货仓: "
                                + item.getReceiverShareStoreEcode() + " 商品或款号：" + item.getPsCSkuOrPro() + "，数量不允许空！",
                        loginUser.getLocale());
            } else {
                SgShareTransferResult sgShareTransferResult = new SgShareTransferResult();
                sgShareTransferResult.setSourceBillItemId(item.getSourceBillItemId());
                sgShareTransferResult.setMsg("第" + (i + 1) + "行记录聚合发货仓: " + item.getSenderShareStoreEcode() + " 聚合收货仓: "
                        + item.getReceiverShareStoreEcode() + " 商品或款号：" + item.getPsCSkuOrPro() + "，数量不允许空！");
                sgShareTransferResult.setSku(item.getPsCSkuOrPro());
                sgShareTransferResults.add(sgShareTransferResult);
                return false;
            }
        }
        if (item.getQty().intValue() < 0) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("聚合发货仓: " + item.getSenderShareStoreEcode() + " 聚合收货仓: "
                                + item.getReceiverShareStoreEcode() + " 商品或款号：" + item.getPsCSkuOrPro() + "，数量小于0不允许！",
                        loginUser.getLocale());
            } else {
                SgShareTransferResult sgShareTransferResult = new SgShareTransferResult();
                sgShareTransferResult.setSourceBillItemId(item.getSourceBillItemId());
                sgShareTransferResult.setMsg("第" + (i + 1) + "行记录聚合发货仓: " + item.getSenderShareStoreEcode() + " 聚合收货仓: "
                        + item.getReceiverShareStoreEcode() + " 商品或款号：" + item.getPsCSkuOrPro() + "，数量小于0不允许！");
                sgShareTransferResult.setSku(item.getPsCSkuOrPro());
                sgShareTransferResults.add(sgShareTransferResult);
                return false;
            }
        }
        if (item.getSenderShareStoreId().equals(item.getReceiverShareStoreId())) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("聚合发货仓: " + item.getSenderShareStoreEcode() + " 聚合收货仓: "
                        + item.getReceiverShareStoreEcode() + " 商品或条码：" + item.getPsCSkuOrPro()
                        + "，输入的聚合收发货仓相同，不允许保存！", loginUser.getLocale());
            } else {
                SgShareTransferResult sgShareTransferResult = new SgShareTransferResult();
                sgShareTransferResult.setSourceBillItemId(item.getSourceBillItemId());
                sgShareTransferResult.setMsg("第" + (i + 1) + "行记录聚合发货仓: " + item.getSenderShareStoreEcode() + " 聚合收货仓: "
                        + item.getReceiverShareStoreEcode() + " 商品或款号：" + item.getPsCSkuOrPro() + "，输入的聚合收发货仓相同，不允许保存！");
                sgShareTransferResult.setSku(item.getPsCSkuOrPro());
                sgShareTransferResults.add(sgShareTransferResult);
                return false;
            }
        }

        List<SgBShareTransferImportItemDto> importItemlist;
        importItemlist = importItemMapper.selectListByShareStoreId(item.getSenderShareStoreId(), item.getReceiverShareStoreId());
        // 过滤本级经销商级别是总部或者当前经销商【上级经销商级别】是总部
        // dealCpCustomerData(importItemlist);

        //  收发货聚合仓，不存在相同实体仓记录，不允许调拨[聚合收发货仓取交集,不存在记录]
        if (CollectionUtils.isEmpty(importItemlist)) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("聚合发货仓: " + item.getSenderShareStoreEcode() + " 聚合收货仓: "
                        + item.getReceiverShareStoreEcode() + " 商品或条码：" + item.getPsCSkuOrPro()
                        + "，收发货聚合仓不存在相同实体仓记录，不允许调拨！", loginUser.getLocale());
            } else {
                SgShareTransferResult sgShareTransferResult = new SgShareTransferResult();
                sgShareTransferResult.setSourceBillItemId(item.getSourceBillItemId());
                sgShareTransferResult.setMsg("第" + (i + 1) + "行记录聚合发货仓: " + item.getSenderShareStoreEcode() +
                        " 聚合收货仓: " + item.getReceiverShareStoreEcode() + " 商品或条码：" + item.getPsCSkuOrPro()
                        + "，收发货聚合仓不存在相同实体仓记录，不允许调拨！");
                sgShareTransferResult.setSku(item.getPsCSkuOrPro());
                sgShareTransferResults.add(sgShareTransferResult);
                return false;
            }
        }

        return true;
    }

    // 过滤本级经销商级别是总部或者当前经销商【上级经销商级别】是总部
    private void dealCpCustomerData(List<SgBShareTransferImportItemDto> importItemlist) {
        Iterator<SgBShareTransferImportItemDto> iterator = importItemlist.iterator();
        while (iterator.hasNext()) {
            SgBShareTransferImportItemDto importItemDto = iterator.next();

            CpCustomer cpCustomer = CommonCacheValUtils.getCpCustomerById(importItemDto.getCpCCustomerId());
            if (cpCustomer == null) {
                continue;
            }
            String cpCCustomerRankEname = cpCustomer.getCpCCustomerRankEname();
            if (cpCCustomerRankEname.equals("总部")) {
                continue;
            }

            // 拿到上级经销商id去校验
            CpCustomer upCpCustomer = CommonCacheValUtils.getCpCustomerById(Long.parseLong(cpCustomer.getCpCCustomerupId()));
            if (upCpCustomer == null) {
                continue;
            }
            if (upCpCustomer.getCpCCustomerRankEname().equals("总部")) {
                continue;
            }
            iterator.remove();
        }
    }

    // 检查修改参数
    private boolean checkUpdateParam(SgBShareTransferSaveRequest request, User loginUser,
                                     SgBShareTransferImportItem item, int i, List<SgShareTransferResult> sgShareTransferResults) {

        if (item.getQty() == null) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("聚合发货仓: " + item.getSenderShareStoreEcode() + " 聚合收货仓: "
                                + item.getReceiverShareStoreEcode() + " 商品或款号：" + item.getPsCSkuOrPro() + "，数量不允许空！",
                        loginUser.getLocale());
            } else {
                SgShareTransferResult sgShareTransferResult = new SgShareTransferResult();
                sgShareTransferResult.setSourceBillItemId(item.getSourceBillItemId());
                sgShareTransferResult.setMsg("第" + (i + 1) + "行记录聚合发货仓: " + item.getSenderShareStoreEcode() + " 聚合收货仓: "
                        + item.getReceiverShareStoreEcode() + " 商品或款号：" + item.getPsCSkuOrPro() + "，数量不允许空！");
                sgShareTransferResult.setSku(item.getPsCSkuOrPro());
                sgShareTransferResults.add(sgShareTransferResult);
                return false;
            }
        }
        if (item.getQty() != null && item.getQty().intValue() < 0) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("聚合发货仓: " + item.getSenderShareStoreEcode() + " 聚合收货仓: "
                                + item.getReceiverShareStoreEcode() + " 商品或款号：" + item.getPsCSkuOrPro() + "，数量小于0不允许！",
                        loginUser.getLocale());
            } else {
                SgShareTransferResult sgShareTransferResult = new SgShareTransferResult();
                sgShareTransferResult.setSourceBillItemId(item.getSourceBillItemId());
                sgShareTransferResult.setMsg("第" + (i + 1) + "行记录聚合发货仓: " + item.getSenderShareStoreEcode() + " 聚合收货仓: "
                        + item.getReceiverShareStoreEcode() + " 商品或款号：" + item.getPsCSkuOrPro() + "，数量小于0不允许！");
                sgShareTransferResult.setSku(item.getPsCSkuOrPro());
                sgShareTransferResults.add(sgShareTransferResult);
                return false;
            }
        }
        return true;
    }

    /**
     * 设置聚合仓调拨单导入明细条码或者款号维度【先查询是否条码,条码没有找到在查询是否款号】
     */
    private void setImportItemTransferDimension(SgBShareTransferSaveRequest
                                                        request, SgBShareTransferImportItemRequest item,
                                                User loginUser) {
        boolean isPsCSkuOrPro = false;

        // 配销仓跨聚合仓调拨单场景，参数校验
        if (StringUtils.isBlank(item.getPsCSkuOrPro())) {
            return;
        }
        if (BigDecimalUtils.nonPositiveInteger(item.getQty())) {
            AssertUtils.logAndThrow("明细数量非法！" + item.getQty(), loginUser.getLocale());
        }

        List<String> skuEcodeList = new ArrayList<>(1);
        skuEcodeList.add(item.getPsCSkuOrPro());
        SkuInfoQueryRequest skuInfoRequest = new SkuInfoQueryRequest();
        skuInfoRequest.setSkuEcodeList(skuEcodeList);
        HashMap<String, PsCProSkuResult> skuInfo = basicPsQueryService.getSkuInfoByEcode(skuInfoRequest);

        // 设置条码维度【条码维度:不设置商品id,商品名称】
        if (null != skuInfo && !skuInfo.isEmpty()) {
            PsCProSkuResult psCProSkuResult = skuInfo.get(item.getPsCSkuOrPro());
            if (psCProSkuResult != null) {
                item.setTransferDimension(SgShareConstants.SG_B_SHARE_TRANSFER_DIMENSION_SKU);
                item.setPsCSkuId(psCProSkuResult.getId());
                item.setPsCSkuEcode(psCProSkuResult.getSkuEcode());
                item.setPriceList(psCProSkuResult.getPricelist());
                item.setPsCProId(null);
                item.setPsCProEname(null);
                item.setItemStatus(SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_NO);
                isPsCSkuOrPro = true;
            }
        }

        // 设置款号维度【如果条码没有找到,查询是否款号】
        if (!isPsCSkuOrPro) {
            ProSkuResult proSkuResult = basicPsQueryService.getSkuInfoByProEcode(item.getPsCSkuOrPro());
            if (proSkuResult != null && CollectionUtils.isNotEmpty(proSkuResult.getProSkuList())) {
                // 款号维度
                PsCProSkuResult psCProSkuResult = proSkuResult.getProSkuList().get(0);
                if (null != psCProSkuResult && null != psCProSkuResult.getPsCProId() &&
                        StringUtils.isNotBlank(psCProSkuResult.getPsCProEcode())) {
                    item.setTransferDimension(SgShareConstants.SG_B_SHARE_TRANSFER_DIMENSION_PRO);
                    //item.setPsCSkuId(psCProSkuResult.getId());
                    item.setPsCProId(psCProSkuResult.getPsCProId());
                    item.setPsCProEcode(psCProSkuResult.getPsCProEcode());
                    item.setPriceList(psCProSkuResult.getPricelist());
                    item.setPsCProEname(psCProSkuResult.getPsCProEname());
                    item.setItemStatus(SgConstantsIF.SG_B_SHARE_TRANSFER_STATUS_NO);
                    isPsCSkuOrPro = true;
                }
            }
        }
        if (!isPsCSkuOrPro) {
            if (request.isR3()) {
                AssertUtils.logAndThrow("聚合发货仓: " + item.getSenderShareStoreEcode() + " 聚合收货仓: " +
                        item.getReceiverShareStoreEcode() + " 输入的商品或条码：" + item.getPsCSkuOrPro()
                        + "，不存在，请确认！", loginUser.getLocale());
            }
        }
    }

    /**
     * 获取聚合仓调拨单具体导入明细
     */
    private SgBShareTransferImportItem getSgBShareTransferImportItem(Long objId, SgBShareTransferImportItemRequest
            item, boolean isR3) {

        // 后端调用不走getSgBShareTransferImportItem方法后续判断逻辑
        if (!isR3) {
            return null;
        }
        if (item.getTransferDimension() == null) {
            return null;
        }
        LambdaQueryWrapper<SgBShareTransferImportItem> query = new LambdaQueryWrapper<>();

        query.eq(SgBShareTransferImportItem::getSenderShareStoreId, item.getSenderShareStoreId())
                .eq(SgBShareTransferImportItem::getReceiverShareStoreId, item.getReceiverShareStoreId())
                .eq(SgBShareTransferImportItem::getPsCSkuOrPro, item.getPsCSkuOrPro())
                .eq(SgBShareTransferImportItem::getSgBShareTransferId, objId);

        if (query == null) {
            return null;
        }
        return importItemMapper.selectOne(query);
    }

    // 填充收发聚合仓编码
    private void fillShareStoreEcode(List<SgBShareTransferImportItemRequest> importItems) {
        if (CollectionUtils.isEmpty(importItems)) {
            return;
        }
        List<Long> senderShareStoreIdList = importItems.stream()
                .map(SgBShareTransferImportItemRequest::getSenderShareStoreId).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<Long> receiverShareStoreIdList = importItems.stream()
                .map(SgBShareTransferImportItemRequest::getReceiverShareStoreId).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<Long> shareStoreIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(senderShareStoreIdList)) {
            shareStoreIdList.addAll(senderShareStoreIdList);
        }
        if (CollectionUtils.isNotEmpty(receiverShareStoreIdList)) {
            shareStoreIdList.addAll(receiverShareStoreIdList);
        }


        List<SgCShareStore> shareStoreList = new ArrayList<>();
        //去重收发货聚合仓id
        if (CollectionUtils.isNotEmpty(shareStoreIdList)) {
            shareStoreIdList = shareStoreIdList.stream().distinct().collect(Collectors.toList());
            List<List<Long>> queryShareStoreIds = ListUtils.partition(shareStoreIdList, 20);
            for (List<Long> ids : queryShareStoreIds) {
                LambdaQueryWrapper<SgCShareStore> query = new LambdaQueryWrapper<>();
                query.in(SgCShareStore::getId, ids);
                List<SgCShareStore> storeList = sgCShareStoreMapper.selectList(query);
                if (CollectionUtils.isNotEmpty(storeList)) {
                    shareStoreList.addAll(storeList);
                }
            }
        }

        Map<Long, SgCShareStore> shareEcodeMap = null;
        if (CollectionUtils.isNotEmpty(shareStoreList)) {
            shareEcodeMap = shareStoreList.stream().collect(Collectors.toMap(SgCShareStore::getId, Function.identity()));
        }
        if (shareEcodeMap == null || shareEcodeMap.isEmpty()) {
            return;
        }


        Map<Long, SgCShareStore> finalShareEcodeMap = shareEcodeMap;
        importItems.forEach(item -> {
            // 发货聚合仓
            SgCShareStore sendSgCShareStore = null;
            // 收货聚合仓
            SgCShareStore receiverSgCShareStore = null;
            Long senderShareStoreId = item.getSenderShareStoreId();
            Long receiverShareStoreId = item.getReceiverShareStoreId();
            if (senderShareStoreId != null) {
                sendSgCShareStore = finalShareEcodeMap.get(senderShareStoreId);
            }

            if (receiverShareStoreId != null) {
                receiverSgCShareStore = finalShareEcodeMap.get(receiverShareStoreId);
            }

            if (sendSgCShareStore != null) {
                item.setSenderShareStoreEcode(sendSgCShareStore.getEcode());
                item.setSenderShareStoreEname(sendSgCShareStore.getEname());
            }

            if (receiverSgCShareStore != null) {
                item.setReceiverShareStoreEcode(receiverSgCShareStore.getEcode());
                item.setReceiverShareStoreEname(receiverSgCShareStore.getEname());
            }

        });
    }

}
