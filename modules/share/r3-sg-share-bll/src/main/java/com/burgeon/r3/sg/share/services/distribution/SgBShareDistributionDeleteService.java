package com.burgeon.r3.sg.share.services.distribution;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.share.distribution.SgBShareDistribution;
import com.burgeon.r3.sg.core.model.table.share.distribution.SgBShareDistributionItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.distribution.SgBShareDistributionItemMapper;
import com.burgeon.r3.sg.share.mapper.distribution.SgBShareDistributionMapper;
import com.burgeon.r3.sg.share.model.result.distribution.SgBShareDistributionDeleteRequest;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/19 11:13
 */
@Slf4j
@Component
public class SgBShareDistributionDeleteService {
    @Autowired
    SgBShareDistributionMapper mapper;
    @Autowired
    SgBShareDistributionItemMapper itemMapper;

    /**
     * 配货单删除（前端）
     */
    ValueHolder delete(QuerySession session) {
        SgBShareDistributionDeleteRequest request = R3ParamUtils.parseSaveObject(session, SgBShareDistributionDeleteRequest.class);
        request.setR3(true);
        SgBShareDistributionDeleteService service = ApplicationContextHandle.getBean(SgBShareDistributionDeleteService.class);
        return R3ParamUtils.convertV14WithResult(service.delete(request));
    }

    /**
     * 配货单删除
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> delete(SgBShareDistributionDeleteRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBStoTransfeDeleteService.delete:param={}", JSONObject.toJSONString(request));
        }
        String lockKey = SgConstants.SG_B_SHARE_DISTRIBUTION + ":" + request.getObjId();
        SgRedisLockUtils.lock(lockKey);
        try {

            SgBShareDistribution distribution = checkParams(request);
            //明细删除
            if (CollectionUtils.isNotEmpty(request.getItemIds())) {
                BigDecimal totQty = distribution.getTotQty();
                BigDecimal totAmt = distribution.getTotAmt();
                Integer totRowNum = distribution.getTotRowNum();
                List<SgBShareDistributionItem> itemList = itemMapper.selectList(new QueryWrapper<SgBShareDistributionItem>()
                        .lambda().in(SgBShareDistributionItem::getId, request.getItemIds()));
                for (SgBShareDistributionItem item : itemList) {
                    if (item.getQty() != null) {
                        totQty = totQty.subtract(item.getQty());
                        totAmt = totAmt.subtract(item.getAmt());
                    }
                }
                itemMapper.delete(new QueryWrapper<SgBShareDistributionItem>()
                        .lambda().in(SgBShareDistributionItem::getId, request.getItemIds()));
                totRowNum = totRowNum - request.getItemIds().size();
                StorageUtils.setBModelDefalutDataByUpdate(distribution, request.getLoginUser());
                distribution.setTotRowNum(totRowNum);
                distribution.setTotAmt(totAmt);
                distribution.setTotQty(totQty);

                mapper.updateById(distribution);
            } else {

                itemMapper.delete(new QueryWrapper<SgBShareDistributionItem>()
                        .lambda().eq(SgBShareDistributionItem::getSgBShareDistributionId, request.getObjId()));
                mapper.delete(new QueryWrapper<SgBShareDistribution>()
                        .lambda().eq(SgBShareDistribution::getId, request.getObjId()));
            }
        } catch (Exception e) {
            AssertUtils.logAndThrowException("配货单删除异常", e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKey, log, this.getClass().getName());

        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "删除成功！");
    }

    /**
     * 操作前check验证
     */
    public SgBShareDistribution checkParams(SgR3BaseRequest request) {
        SgBShareDistribution distribution = mapper.selectById(request.getObjId());
        if (distribution == null) {
            AssertUtils.logAndThrow("当前记录已不存在！", request.getLoginUser().getLocale());
        }
        if (SgConstants.IS_ACTIVE_N.equalsIgnoreCase(distribution.getIsactive())) {
            AssertUtils.logAndThrow("当前记录已作废,不允许删除！", request.getLoginUser().getLocale());

        } else if (SgShareConstants.BILL_STATUS_UNSUBMIT != distribution.getStatus()) {
            AssertUtils.logAndThrow("当前单据状态不允许删除！", request.getLoginUser().getLocale());
        }
        return distribution;
    }

}
