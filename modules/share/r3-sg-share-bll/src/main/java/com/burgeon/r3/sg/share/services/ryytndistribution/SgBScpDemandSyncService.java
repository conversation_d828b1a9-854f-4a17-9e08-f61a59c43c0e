package com.burgeon.r3.sg.share.services.ryytndistribution;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.utils.AbstractDingTalkTableRobot;
import com.burgeon.r3.sg.basic.utils.BusinessSyetemParamConfigUtils;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.DingTalkUtil;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.SgCPlanConvertStatusEnum;
import com.burgeon.r3.sg.core.enums.SgMonthDemandFromEnum;
import com.burgeon.r3.sg.core.enums.SgMonthDemandStatusEnum;
import com.burgeon.r3.sg.core.model.result.SgBCommonStorageQtyQueryResult;
import com.burgeon.r3.sg.core.model.result.SgBSaStorageQtyQueryResult;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgBScpDemandSync;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgBScpDemandSyncConvertConfig;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCDepartmentMonthDemand;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCPlanConvertVersion;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgBScpDemandSyncConvertConfigMapper;
import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgBScpDemandSyncMapper;
import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgCDepartmentMonthDemandMapper;
import com.burgeon.r3.sg.share.model.request.ryytndistribution.SgBScpDemandSyncRequest;
import com.burgeon.r3.sg.share.services.ryytndistribution.scpdemand.ScpDemandSyncImportListener;
import com.burgeon.r3.sg.share.services.ryytndistribution.scpdemand.ScpDemandSyncImportParam;
import com.burgeon.r3.sg.share.services.ryytndistribution.scpdemand.ScpDemandSyncImportProperties;
import com.burgeon.r3.sg.share.services.ryytndistribution.scpdemand.SgBScpDemandSyncErrorDto;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpCDistributionOrganizationQueryCmd;
import com.jackrain.nea.cpext.model.table.CpCDistributionOrganization;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-07-08 17:13
 */
@Slf4j
@Component
public class SgBScpDemandSyncService {
    @DubboReference(group = "cp-ext", version = "1.0")
    private CpCDistributionOrganizationQueryCmd cpCDistributionOrganizationQueryCmd;

    private static final String DEFAULT_REMARK = "计划项目";
    private static final String LOG_OBJ = "SgBScpDemandSyncService.";
    @Resource
    private SgBScpDemandSyncMapper sgBScpDemandSyncMapper;
    @Resource
    private SgBScpDemandSyncConvertConfigMapper sgBScpDemandSyncConvertConfigMapper;

    @Resource
    private SgS2SaAutoDistributionManager sgS2SaAutoDistributionManager;

    @NacosValue(value = "${r3.sg.adb.scsg:test_rpt_sc_sg}", autoRefreshed = true)
    private String sgAdbPrefix;
    @NacosValue(value = "${r3.sg.adb.order:test_rpt_order}", autoRefreshed = true)
    private String orderAdbPrefix;

    @Resource
    private SgCDepartmentMonthDemandMapper sgCDepartmentMonthDemandMapper;

    @Resource
    private SgCPlanConvertVersionService sgCPlanConvertVersionService;
    @Resource
    private SgBScpDemandSyncUsedQtyService sgBScpDemandSyncUsedQtyService;

    /**
     * 是否将今天的开始时间作为流水查询时限制的结束时间
     */
    @NacosValue(value = "${sg.scp.demand.sync.convert.limit.time:true}", autoRefreshed = true)
    private boolean isLimitEndTime;

    /**
     * 允许拆包的二级渠道部门ID
     */
    @NacosValue(value = "${sg.scp.demand.sync.channel.id:41}", autoRefreshed = true)
    private Long scpSyncChannelId;

    /*发货单零售发货单状态*/
    private static final List<Integer> DELIVERY_ORDER_STATUS_LIST = Arrays.asList(5, 6);
    /*寻源后的零售发货单状态*/
    private static final List<Integer> FIND_SOURCE_ORDER_STATUS_LIST = Arrays.asList(1, 3, 4, 21);

    @Transactional(rollbackFor = Throwable.class)
    public ValueHolderV14 doSyncDemand(List<SgBScpDemandSyncRequest> requestList) {
        log.info(LogUtil.format("需求文件解析开始,请求入参:{}",
                "SgBScpDemandSyncService.doSyncDemand"), JSON.toJSONString(requestList));

        List<SgBScpDemandSyncErrorDto> errorDtoList = new ArrayList<>();
        String versionBi = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
        int totalCount = 0;
        int successCount = 0;
        for (SgBScpDemandSyncRequest request : requestList) {
            ScpDemandSyncImportProperties properties = new ScpDemandSyncImportProperties(sgBScpDemandSyncMapper, request.getType(), versionBi, errorDtoList);
            read2Table(request.getUrl(), properties);
            totalCount += properties.getTotalCount();
            successCount += properties.getSuccessCount();
            log.info(LogUtil.format("需求文件解析完成,请求入参:{},解析记录数:{},成功记录数:{}",
                            "SgBScpDemandSyncService.doSyncDemand"),
                    JSON.toJSONString(request), properties.getTotalCount(), properties.getSuccessCount());
            if (!Objects.equals(properties.getTotalCount(), request.getCount())) {
                throw new NDSException("总行数与文件行数不一致");
            }

            sendError2DingTalk(request.getUrl(), properties.getErrorDtoList());
        }

        SgCPlanConvertVersion versionInfo = new SgCPlanConvertVersion();
        versionInfo.setVersionBi(versionBi);
        versionInfo.setStatus(SgCPlanConvertStatusEnum.IS_NEW.getValue());
        versionInfo.setDataCount(successCount);
        versionInfo.setRemark(JSON.toJSONString(requestList));

        //TODO 202407080933:是否存储请求参数到备注？一次请求算一个oms分货版本？所有的version_plan都是一样的？
//        versionInfo.setVersionPlan();

        sgCPlanConvertVersionService.save(versionInfo);
        log.info(LogUtil.format("需求文件解析完成,总文件数:{}，总行数:{}，总成功数:{}，版本号：{}",
                "SgBScpDemandSyncService.doSyncDemand"), requestList.size(), totalCount, successCount, versionBi);
        return new ValueHolderV14(ResultCode.SUCCESS, "写入完成：" + versionBi);
    }

    private void sendError2DingTalk(String url, List<SgBScpDemandSyncErrorDto> errorDtoList) {
        if (CollectionUtils.isEmpty(errorDtoList)) {
            return;
        }

        AbstractDingTalkTableRobot<SgBScpDemandSyncErrorDto> robot = new AbstractDingTalkTableRobot<SgBScpDemandSyncErrorDto>(errorDtoList) {
            @Override
            protected LinkedHashMap<String, Function<SgBScpDemandSyncErrorDto, Object>> genKeyMap() {
                LinkedHashMap<String, Function<SgBScpDemandSyncErrorDto, Object>> keyMap = new LinkedHashMap<>();
                keyMap.put("行号", SgBScpDemandSyncErrorDto::getRowNum);
                keyMap.put("编码", SgBScpDemandSyncErrorDto::getScpEcode);
                keyMap.put("错误信息", SgBScpDemandSyncErrorDto::getErrMsg);
                return keyMap;
            }

            @Override
            public String getDesc() {
                return "计划系统推送共识后需求转化异常，推送文件：" + url;
            }
        };
        
        robot.sendTableMsg();
    }

    /**
     * 读取文件，写入数据库
     *
     * @param fileUrl    文件地址
     * @param properties 读取属性
     */
    private void read2Table(String fileUrl, ScpDemandSyncImportProperties properties) {
        URL url;
        try {
            url = new URL(fileUrl);
        } catch (MalformedURLException e) {
            log.warn(LogUtil.format("文件地址解析异常:{},异常信息:{}",
                    "SgBScpDemandSyncService.read2Table"), fileUrl, Throwables.getStackTraceAsString(e));
            throw new NDSException("文件地址解析异常");
        }

        try (InputStream inputStream = url.openStream()) {
            EasyExcel.read(inputStream)
                    .head(ScpDemandSyncImportParam.class)
                    .headRowNumber(1)
                    .registerReadListener(new ScpDemandSyncImportListener(properties))
                    .sheet()
                    .doRead();
        } catch (IOException e) {
            log.warn(LogUtil.format("需求文件解析异常:{},异常信息:{}",
                    "SgBScpDemandSyncService.read2Table"), fileUrl, Throwables.getStackTraceAsString(e));
            throw new NDSException("需求文件解析异常");
        }
    }

    /**
     * 计划系统共识的需求转化成部门月分货需求
     * 执行净需求计算、白名单过滤、拆包
     *
     * @param type      类型
     * @param versionBi 数中版本号
     * @return 计算得到的净需求列表
     */
    @Transactional(rollbackFor = Throwable.class)
    public int saveScpData2Demand(Integer type, String versionBi) {
        long startTime = System.currentTimeMillis();
        log.info(LogUtil.format("开始-执行计划系统共识需求转化成部门月分货需求,类型:{}，版本号:{}",
                LOG_OBJ + "convert2DemandPlanList"), type, versionBi);
        Date weekStartDate = DateUtil.beginOfWeek(new Date());
        List<SgBScpDemandSync> dataList;
        if (BusinessSyetemParamConfigUtils.isDistributionWhitelistEnabled()) {
            dataList = sgBScpDemandSyncMapper.selectSyncListInSkuConfig(type, versionBi, DateUtil.format(weekStartDate, "yyyy-MM-dd"));
        } else {
            dataList = sgBScpDemandSyncMapper.selectSyncList(type, versionBi, DateUtil.format(weekStartDate, "yyyy-MM-dd"));
        }
        if (CollectionUtils.isEmpty(dataList)) {
            log.info(LogUtil.format("未查询到任何共识后需求数据", LOG_OBJ + "convert2DemandPlanList"));
            return 0;
        }

        Map<String, SgBScpDemandSyncConvertConfig> convertConfigMap = queryConvertConfigMap();
        Map<String, PsCSku> skuMap = querySkuEcodeMap(dataList, convertConfigMap);
        List<SgCDepartmentMonthDemand> retDemandList;

        try {
            /*查询分货组织架构*/
            Map<String, CpCDistributionOrganization> channelMap = queryChannelOrgMap(dataList);

            /*构建需求数据对象*/
            List<SgCDepartmentMonthDemand> demandList = buildCommonInfo(skuMap, dataList, channelMap, versionBi);
            /*计算需求数据*/
//            retDemandList = calculateDemandQty(demandList, weekStartDate);
            retDemandList = calculateDemandQty2(versionBi, demandList, channelMap, weekStartDate);
            /*转化拆包*/
            convertConfigSku(retDemandList, skuMap, convertConfigMap);

            /*保存需求数据*/
            StorageBasicUtils.batchInsertList(sgCDepartmentMonthDemandMapper, retDemandList, SgConstants.SG_COMMON_INSERT_PAGE_SIZE,
                    "批量插入需求数据失败", R3SystemUserResource.getSystemRootUser());
        } catch (Exception e) {
            DingTalkUtil.sendTextMsg("净需求数据计算异常,异常信息：" + Throwables.getStackTraceAsString(e));
            throw new NDSException("需求数据计算异常", e);
        }

        log.info(LogUtil.format("完成-执行计划系统共识需求转化成部门月分货需求,共识数:{}，需求数:{}，耗时:{}",
                        LOG_OBJ + "convert2DemandPlanList"),
                dataList.size(), retDemandList.size(), (System.currentTimeMillis() - startTime));
        return retDemandList.size();
    }

    /**
     * 转化拆包
     *
     * @param retDemandList    需求数据
     * @param skuMap           SKU信息
     * @param convertConfigMap 转化配置
     * @return 需求数据
     */
    private void convertConfigSku(List<SgCDepartmentMonthDemand> retDemandList,
                                  Map<String, PsCSku> skuMap, Map<String, SgBScpDemandSyncConvertConfig> convertConfigMap) {
        if (CollectionUtils.isEmpty(retDemandList)) {
            return;
        }

        List<SgCDepartmentMonthDemand> retList = new ArrayList<>();
        for (SgCDepartmentMonthDemand demand : retDemandList) {
            /*拆包会多一条记录*/
            SgBScpDemandSyncConvertConfig convertConfig = convertConfigMap.getOrDefault(demand.getPsCSkuEcode(), null);
            if (Objects.isNull(convertConfig)) {
                continue;
            }

            SgCDepartmentMonthDemand convertDemand = BeanUtil.copyProperties(demand, SgCDepartmentMonthDemand.class);
            convertDemand.setPsCSkuEcode(convertConfig.getInPsCSkuEcode());
            convertDemand.setPsCSkuId(convertConfig.getInPsCSkuId());

            PsCSku psCSku = skuMap.getOrDefault(convertDemand.getPsCSkuEcode(), null);
            if (Objects.isNull(psCSku)) {
                log.warn(LogUtil.format("拆包转化-该商品编码未查询到有效商品信息，商品编码:{}",
                        LOG_OBJ + "buildCommonInfo"), demand.getPsCSkuEcode());
                psCSku = new PsCSku();
            }
            convertDemand.setPsCProId(psCSku.getPsCProId());
            convertDemand.setPsCProEcode(psCSku.getPsCProEcode());
            convertDemand.setPsCProEname(psCSku.getPsCProEname());

            convertDemand.setDemandQty(demand.getDemandQty()
                    .divide(convertConfig.getCoefficient(), 0, RoundingMode.UP));
            convertDemand.setCpCDistributionOrgId(scpSyncChannelId);
            convertDemand.setRemark(convertDemand.getRemark() + "_拆包配置ID[" + convertConfig.getId() + "]");
            /*存储转化来源的SKU*/
            convertDemand.setConvertConfigId(convertConfig.getId());

            retList.add(convertDemand);
        }
        /*转化后计算拆包下的当天的需求比例系数*/
        handleConvertDayPercentage(retList);

        retDemandList.addAll(retList);
    }

    /**
     * 计算转化后的日需求比例
     *
     * @param retList 转化后需求列表（总，包含其他需求）
     */
    private void handleConvertDayPercentage(List<SgCDepartmentMonthDemand> retList) {
        if (CollectionUtils.isEmpty(retList)) {
            return;
        }

        /*逐天处理，这里都是拆包产生的需求*/
        Map<Date, List<SgCDepartmentMonthDemand>> convertDateMap = retList.stream()
                /*.filter(demand -> demand.getConvertConfigId() != null)*/
                .collect(Collectors.groupingBy(SgCDepartmentMonthDemand::getDemandDate));
        for (Map.Entry<Date, List<SgCDepartmentMonthDemand>> dayEntry : convertDateMap.entrySet()) {
            /*逐个SKU处理*/
            Map<Long, List<SgCDepartmentMonthDemand>> daySkuGroup = dayEntry.getValue().stream()
                    .collect(Collectors.groupingBy(SgCDepartmentMonthDemand::getPsCSkuId));
            for (Map.Entry<Long, List<SgCDepartmentMonthDemand>> daySkuEntry : daySkuGroup.entrySet()) {
                Optional<BigDecimal> totalQtyOpt = daySkuEntry.getValue().stream()
                        .map(SgCDepartmentMonthDemand::getDemandQty).reduce(BigDecimal::add);
                /*防止计算比例时，出现除数为0的情况*/
                if (!totalQtyOpt.isPresent()) {
                    continue;
                }

                BigDecimal totalQty = totalQtyOpt.get();
                for (SgCDepartmentMonthDemand demand : daySkuEntry.getValue()) {
                    demand.setConvertDayPercentage(demand.getDemandQty()
                            .divide(totalQty, 4, RoundingMode.HALF_UP));
                }
            }
        }
    }


    /**
     * 构建需求计划保存对象
     *
     * @param skuMap          sku信息
     * @param allDeptPlanList 供应链计划公示后需求
     * @param channelMap      分货组织架构
     * @param versionBi       数中版本号
     * @return 需求计划保存对象列表
     */
    private List<SgCDepartmentMonthDemand> buildCommonInfo(Map<String, PsCSku> skuMap,
                                                           List<SgBScpDemandSync> allDeptPlanList,
                                                           Map<String, CpCDistributionOrganization> channelMap,
                                                           String versionBi) {
        List<SgCDepartmentMonthDemand> demandList = new ArrayList<>();
        for (SgBScpDemandSync data : allDeptPlanList) {
            SgCDepartmentMonthDemand demand = new SgCDepartmentMonthDemand();
            StorageUtils.setBModelDefalutData(demand, R3SystemUserResource.getSystemRootUser());
            demand.setIssystem(SgConstants.IS_ACTIVE_N);

            demand.setDemandFrom(SgMonthDemandFromEnum.SCP.getValue());
            demand.setDemandQty(data.getScpQty());
            demand.setPsCSkuEcode(data.getPsCSkuEcode());

            /*补充品相信息*/
            PsCSku psCSku = skuMap.getOrDefault(demand.getPsCSkuEcode(), null);
            if (Objects.isNull(psCSku)) {
                log.warn(LogUtil.format("该商品编码未查询到有效商品信息，商品编码:{}",
                        LOG_OBJ + "buildCommonInfo"), data.getPsCSkuEcode());
                continue;
            }
            demand.setPsCSkuId(psCSku.getId());
            demand.setPsCProId(psCSku.getPsCProId());
            demand.setPsCProEcode(psCSku.getPsCProEcode());
            demand.setPsCProEname(psCSku.getPsCProEname());
            /*需求信息*/
            demand.setDemandDate(data.getScpDate());
            demand.setDemandMonth(DateUtils.format(demand.getDemandDate(), "yyyyMM"));
            demand.setActualInQty(BigDecimal.ZERO);
            demand.setDemandStatus(SgMonthDemandStatusEnum.NO_START.getValue());

            /*版本信息*/
            demand.setMonthWeek(data.getMonthWeek());
            demand.setVersionPlan(data.getVersionPlan());
            demand.setVersionBi(versionBi);
            demand.setRemark(DEFAULT_REMARK + "_" + data.getLv2ChannelCode() + "_SCP同步ID[" + data.getId() + "]");

            /*部门ID*/
            demand.setCpCDistributionOrgId(channelMap.getOrDefault(data.getLv2ChannelCode(), new CpCDistributionOrganization()).getId());
            if (Objects.isNull(demand.getCpCDistributionOrgId())) {
                log.warn(LogUtil.format("该组织编码未查询到有效分货组织信息，组织编码:{}",
                        LOG_OBJ + "buildCommonInfo"), data.getLv2ChannelCode());
                continue;
            }

            demandList.add(demand);
        }

        return demandList;
    }

    /**
     * 计算净需求【核心】(已用量取零售发货单明细)
     *
     * @param versionBi  版本号
     * @param demandList 需求列表
     * @param channelMap 分货组织架构
     * @return 计算净需求后需求列表（已过滤满足的数据）
     */
    private List<SgCDepartmentMonthDemand> calculateDemandQty2(String versionBi, List<SgCDepartmentMonthDemand> demandList,
                                                               Map<String, CpCDistributionOrganization> channelMap,
                                                               Date weekStartDate) {
        /*
         * 1. 按部门信息分组，逐个部门处理
         * 2. 迭代查询每个SKU的已用量【零售发货单在库流水量+配销仓库存占用量】
         * 3. 扣减掉已消耗量
         * 4. 过滤已满足
         */
        List<SgCDepartmentMonthDemand> retList = new ArrayList<>();
        Map<Long, String> channelIdEcodeMap = channelMap.values().stream()
                .collect(Collectors.toMap(CpCDistributionOrganization::getId, CpCDistributionOrganization::getEcode, (a, b) -> a));

        Map<Long, List<SgCDepartmentMonthDemand>> orgIdDemandListMap = demandList.stream()
                .collect(Collectors.groupingBy(SgCDepartmentMonthDemand::getCpCDistributionOrgId));
        /*按部门分组处理*/
        for (Map.Entry<Long, List<SgCDepartmentMonthDemand>> entry : orgIdDemandListMap.entrySet()) {
            Set<Long> skuIds = entry.getValue().stream().map(SgCDepartmentMonthDemand::getPsCSkuId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(skuIds)) {
                log.warn(LogUtil.format("该部门未查询到有效商品信息，部门ID:{}，商品信息:{}",
                        LOG_OBJ + "calculateDemandQty"), entry.getKey(), skuIds);
                continue;
            }

            String channelEcode = channelIdEcodeMap.get(entry.getKey());
            if (StringUtils.isEmpty(channelEcode)) {
                log.warn(LogUtil.format("部门渠道编码为空，部门ID:{}，商品信息:{}",
                        LOG_OBJ + "calculateDemandQty"), entry.getKey(), skuIds);
                retList.addAll(entry.getValue());
                continue;
            }

            /*商品ID->已满足量*/
            Map<Long, BigDecimal> skuQtyMap = calculateUsedQtyMap2(versionBi, entry.getKey(), channelEcode, skuIds, weekStartDate);

            /*计算后的净需求列表*/
            List<SgCDepartmentMonthDemand> filteredDemandList = filterDemand(skuQtyMap, entry.getValue());
            retList.addAll(filteredDemandList);
        }

        return retList;
    }

    /**
     * 计算净需求【核心】(已用量取配销仓在库变动流水)
     * 20241015 标记过期{@link #calculateDemandQty2}
     *
     * @param demandList 需求列表
     * @return 计算净需求后需求列表（已过滤满足的数据）
     */
    @Deprecated
    private List<SgCDepartmentMonthDemand> calculateDemandQty(List<SgCDepartmentMonthDemand> demandList, Date weekStartDate) {
        /*
         * 1. 获取部门对应的Sa仓列表
         * 2. 迭代查询每个SKU的已用量【零售发货单在库流水量+配销仓库存占用量】
         * 3. 扣减掉已消耗量
         * 4. 过滤已满足
         */
        List<SgCDepartmentMonthDemand> retList = new ArrayList<>();
        Map<Long, List<SgCDepartmentMonthDemand>> orgIdDemandListMap = demandList.stream()
                .collect(Collectors.groupingBy(SgCDepartmentMonthDemand::getCpCDistributionOrgId));
        Map<Long, Set<Long>> deptSaStoreIdsMap = sgS2SaAutoDistributionManager.queryDeptSaStoreIdsMap(orgIdDemandListMap.keySet());
        /*按部门分组处理*/
        for (Map.Entry<Long, List<SgCDepartmentMonthDemand>> entry : orgIdDemandListMap.entrySet()) {
            Set<Long> saIds = deptSaStoreIdsMap.get(entry.getKey());
            Set<Long> skuIds = entry.getValue().stream().map(SgCDepartmentMonthDemand::getPsCSkuId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(skuIds)) {
                log.warn(LogUtil.format("该部门未查询到有效商品信息，部门ID:{}，商品信息:{}",
                        LOG_OBJ + "calculateDemandQty"), saIds, skuIds);
                continue;
            }

            if (CollectionUtils.isEmpty(saIds)) {
                log.info(LogUtil.format("该部门未查询到有效配销仓信息，部门ID:{}，商品信息:{}",
                        LOG_OBJ + "calculateDemandQty"), saIds, skuIds);

                retList.addAll(entry.getValue());
                continue;
            }

            /*商品ID->已满足量*/
            Map<Long, BigDecimal> skuQtyMap = calculateUsedQtyMap(saIds, skuIds, weekStartDate);

            /*计算后的净需求列表*/
            List<SgCDepartmentMonthDemand> filteredDemandList = filterDemand(skuQtyMap, entry.getValue());
            retList.addAll(filteredDemandList);
        }

        return retList;
    }

    /**
     * 计算净需求过程【核心】
     *
     * @param skuQtyMap  sku与已消耗数量映射
     * @param demandList 需求列表
     * @return 计算后的净需求列表
     */
    private List<SgCDepartmentMonthDemand> filterDemand(Map<Long, BigDecimal> skuQtyMap, List<SgCDepartmentMonthDemand> demandList) {
        List<SgCDepartmentMonthDemand> retList = new ArrayList<>();

        Map<Long, List<SgCDepartmentMonthDemand>> skuDemandListMap = demandList.stream()
                .collect(Collectors.groupingBy(SgCDepartmentMonthDemand::getPsCSkuId));
        /*按SKU分组处理*/
        for (Map.Entry<Long, List<SgCDepartmentMonthDemand>> entry : skuDemandListMap.entrySet()) {
            /*按日期从小到大排序*/
            List<SgCDepartmentMonthDemand> sortedDemandList = entry.getValue().stream()
                    .filter(demand -> BigDecimal.ZERO.compareTo(demand.getDemandQty()) < 0)
                    .sorted(Comparator.comparing(SgCDepartmentMonthDemand::getDemandDate)).collect(Collectors.toList());

            BigDecimal skuQty = skuQtyMap.getOrDefault(entry.getKey(), BigDecimal.ZERO);
            for (SgCDepartmentMonthDemand demand : sortedDemandList) {
                if (BigDecimal.ZERO.compareTo(skuQty) == 0) {
                    retList.add(demand);
                    continue;
                }

                /*需求量<已使用量*/
                if (demand.getDemandQty().compareTo(skuQty) < 0) {
                    skuQty = skuQty.subtract(demand.getDemandQty());
                    continue;
                }
                /*需求量=已使用量*/
                if (demand.getDemandQty().compareTo(skuQty) == 0) {
                    skuQty = BigDecimal.ZERO;
                    continue;
                }
                /*需求量>已使用量*/
                if (demand.getDemandQty().compareTo(skuQty) > 0) {
                    demand.setDemandQty(demand.getDemandQty().subtract(skuQty));
                    skuQty = BigDecimal.ZERO;
                    retList.add(demand);
                }
            }
        }

        return retList;
    }


    /**
     * 获取已满足量
     *
     * @param versionBi
     * @param channelId    渠道ID
     * @param channelEcode 渠道编码
     * @param skuIds       商品ID列表
     * @return 商品ID->已满足量【-来源类型为零售发货单的配销仓在库变动流水+配销仓库存当前占用量】 映射
     */
    private Map<Long, BigDecimal> calculateUsedQtyMap2(String versionBi, Long channelId, String channelEcode, Set<Long> skuIds, Date weekStartDate) {
        if (StringUtils.isEmpty(channelEcode) || CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }

        /*已占用量:所有的占用，不限制时间*/
        Map<Long, BigDecimal> skuPreQtyMap = queryOrderItemPreQtyByChannel(channelEcode, skuIds);
        /*已出库量：本周开始到当前时间*/
        Map<Long, BigDecimal> skuFtpQtyMap = queryOrderItemRealQtyByChannel(channelEcode, skuIds, weekStartDate);

        /*保存计算的中间值：占用、已发*/
        sgBScpDemandSyncUsedQtyService.saveUsedQty(versionBi, channelId, channelEcode, skuIds, skuPreQtyMap, skuFtpQtyMap);

        for (Long skuId : skuIds) {
            skuFtpQtyMap.put(skuId,
                    skuFtpQtyMap.getOrDefault(skuId, BigDecimal.ZERO).abs()
                            .add(skuPreQtyMap.getOrDefault(skuId, BigDecimal.ZERO)));
        }
        return skuFtpQtyMap;
    }

    /**
     * 获取已满足量
     *
     * @param saIds  配销仓ID列表
     * @param skuIds 商品ID列表
     * @return 商品ID->已满足量【-来源类型为零售发货单的配销仓在库变动流水+配销仓库存当前占用量】 映射
     */
    private Map<Long, BigDecimal> calculateUsedQtyMap(Set<Long> saIds, Set<Long> skuIds, Date weekStartDate) {
        if (CollectionUtils.isEmpty(saIds) || CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }

        Date endDate = null;
        Map<Long, BigDecimal> skuPreQtyMap;
        if (isLimitEndTime) {
            endDate = DateUtil.beginOfDay(new Date());

            log.info(LogUtil.format("已启用限制截止日期，截止日期:{}", LOG_OBJ + "calculateUsedQtyMap"), endDate);
            skuPreQtyMap = querySaPreFtpByDept(saIds, skuIds, endDate);
        } else {
            log.info(LogUtil.format("不限制截止日期，截止日期:{}", LOG_OBJ + "calculateUsedQtyMap"), endDate);
            skuPreQtyMap = querySaPreStorageByDept(saIds, skuIds);
        }

        Map<Long, BigDecimal> skuFtpQtyMap = querySaChangeFtpByDept(saIds, skuIds, weekStartDate, endDate);

        for (Long skuId : skuIds) {
            skuFtpQtyMap.put(skuId,
                    skuFtpQtyMap.getOrDefault(skuId, BigDecimal.ZERO).abs()
                            .add(skuPreQtyMap.getOrDefault(skuId, BigDecimal.ZERO)));
        }
        return skuFtpQtyMap;
    }


    /**
     * 查询渠道已占用库存
     *
     * @param channelEcode 渠道编码
     * @return @return 商品ID->占用量
     */
    private Map<Long, BigDecimal> queryOrderItemPreQtyByChannel(String channelEcode, Set<Long> skuIds) {
        List<SgBCommonStorageQtyQueryResult> skuQtyResultList;
        try {
            SgBScpDemandSyncService bean = ApplicationContextHandle.getBean(SgBScpDemandSyncService.class);
            Future<List<SgBCommonStorageQtyQueryResult>> listFuture = bean.queryOrderItemPreQtyByChannelByADB(channelEcode, skuIds);
            skuQtyResultList = listFuture.get();
        } catch (Exception e) {
            log.error(LogUtil.format("异步使用ADb查询配销仓占用库存失败，异常：{}",
                    LOG_OBJ + "querySaPreStorageByDept"), Throwables.getStackTraceAsString(e));
            throw new NDSException("异步使用ADb查询配销仓占用库存失败：" + e.getMessage());
        }

        return ListUtils.emptyIfNull(skuQtyResultList).stream()
                .filter(o -> Objects.nonNull(o.getPsCSkuId()) && Objects.nonNull(o.getQty()))
                .collect(Collectors.toMap(SgBCommonStorageQtyQueryResult::getPsCSkuId, SgBCommonStorageQtyQueryResult::getQty));
    }

    /**
     * 查询渠道已发库存
     *
     * @param channelEcode  渠道编码
     * @param skuIds        商品ID列表
     * @param weekStartDate 结束日期
     * @return @return 商品ID->占用量
     */
    private Map<Long, BigDecimal> queryOrderItemRealQtyByChannel(String channelEcode, Set<Long> skuIds, Date weekStartDate) {
        List<SgBCommonStorageQtyQueryResult> skuQtyResultList;
        try {
            SgBScpDemandSyncService bean = ApplicationContextHandle.getBean(SgBScpDemandSyncService.class);
            Future<List<SgBCommonStorageQtyQueryResult>> listFuture = bean.queryOrderItemRealQtyByChannelByADB(channelEcode, skuIds, weekStartDate);
            skuQtyResultList = listFuture.get();
        } catch (Exception e) {
            log.error(LogUtil.format("异步使用ADB查询渠道已发库存失败，异常：{}",
                    LOG_OBJ + "querySaPreStorageByDept"), Throwables.getStackTraceAsString(e));
            throw new NDSException("异步使用ADB查询渠道已发库存失败：" + e.getMessage());
        }

        return ListUtils.emptyIfNull(skuQtyResultList)
                .stream().collect(Collectors.toMap(SgBCommonStorageQtyQueryResult::getPsCSkuId, SgBCommonStorageQtyQueryResult::getQty));
    }

    /**
     * 查询配销仓占用库存
     *
     * @param saIds   配销仓ID列表
     * @param skuIds  商品ID列表
     * @param endDate 结束日期
     * @return @return 商品ID->占用量
     */
    private Map<Long, BigDecimal> querySaPreFtpByDept(Set<Long> saIds, Set<Long> skuIds, Date endDate) {
        List<SgBSaStorageQtyQueryResult> skuQtyResultList;
        try {
            SgBScpDemandSyncService bean = ApplicationContextHandle.getBean(SgBScpDemandSyncService.class);
            Future<List<SgBSaStorageQtyQueryResult>> listFuture = bean.querySaPreFtpByADB(saIds, skuIds, endDate);
            skuQtyResultList = listFuture.get();
        } catch (Exception e) {
            log.error(LogUtil.format("异步使用ADb查询配销仓占用库存失败，异常：{}",
                    LOG_OBJ + "querySaPreStorageByDept"), Throwables.getStackTraceAsString(e));
            throw new NDSException("异步使用ADb查询配销仓占用库存失败：" + e.getMessage());
        }

        return ListUtils.emptyIfNull(skuQtyResultList)
                .stream().collect(Collectors.toMap(SgBSaStorageQtyQueryResult::getPsCSkuId, SgBSaStorageQtyQueryResult::getQty));
    }


    @TargetDataSource(name = "adb")
    @Async(value = "adbAsyncExecutorPool")
    public Future<List<SgBCommonStorageQtyQueryResult>> queryOrderItemPreQtyByChannelByADB(String channelEcode, Set<Long> skuIds) {
        if (StringUtils.isEmpty(channelEcode) || CollectionUtils.isEmpty(skuIds)) {
            return AsyncResult.forValue(Collections.emptyList());
        }

        List<SgBCommonStorageQtyQueryResult> retList = new ArrayList<>();
        List<List<Long>> skuIdsList = StorageUtils.getPages(new ArrayList<>(skuIds), 1000);
        for (List<Long> skuList : skuIdsList) {
            retList.addAll(sgBScpDemandSyncMapper.selectSkuPreQtyByOrderItem(orderAdbPrefix, channelEcode, skuList));
        }

        return AsyncResult.forValue(retList);
    }

    @TargetDataSource(name = "adb")
    @Async(value = "adbAsyncExecutorPool")
    public Future<List<SgBCommonStorageQtyQueryResult>> queryOrderItemRealQtyByChannelByADB(String channelEcode, Set<Long> skuIds, Date startDate) {
        if (StringUtils.isEmpty(channelEcode) || CollectionUtils.isEmpty(skuIds)) {
            return AsyncResult.forValue(Collections.emptyList());
        }

        List<SgBCommonStorageQtyQueryResult> retList = new ArrayList<>();
        List<List<Long>> skuIdsList = StorageUtils.getPages(new ArrayList<>(skuIds), 1000);
        for (List<Long> skuList : skuIdsList) {
            retList.addAll(sgBScpDemandSyncMapper.selectSkuUsedQtyByOrderItem(orderAdbPrefix, channelEcode, skuList, startDate));
        }

        return AsyncResult.forValue(retList);
    }

    @Deprecated
    @TargetDataSource(name = "adb")
    @Async(value = "adbAsyncExecutorPool")
    public Future<List<SgBSaStorageQtyQueryResult>> querySaPreFtpByADB(Set<Long> saIds, Set<Long> skuIds, Date endDate) {
        if (CollectionUtils.isEmpty(saIds) || CollectionUtils.isEmpty(skuIds)) {
            return AsyncResult.forValue(Collections.emptyList());
        }

        List<SgBSaStorageQtyQueryResult> retList = new ArrayList<>();
        List<List<Long>> skuIdsList = StorageUtils.getPages(new ArrayList<>(skuIds), 1000);
        for (List<Long> skuList : skuIdsList) {
            retList.addAll(sgBScpDemandSyncMapper.selectSaPreFtpByADB(sgAdbPrefix, saIds, skuList, endDate));
        }

        return AsyncResult.forValue(retList);
    }

    /**
     * 查询配销仓占用库存
     *
     * @param saIds  配销仓ID列表
     * @param skuIds 商品ID列表
     * @return @return 商品ID->占用量
     */
    private Map<Long, BigDecimal> querySaPreStorageByDept(Set<Long> saIds, Set<Long> skuIds) {
        List<SgBSaStorageQtyQueryResult> skuQtyResultList;
        try {
            SgBScpDemandSyncService bean = ApplicationContextHandle.getBean(SgBScpDemandSyncService.class);
            Future<List<SgBSaStorageQtyQueryResult>> listFuture = bean.querySaPreStorageByADB(saIds, skuIds);
            skuQtyResultList = listFuture.get();
        } catch (Exception e) {
            log.error(LogUtil.format("异步使用ADb查询配销仓占用库存失败，异常：{}",
                    LOG_OBJ + "querySaPreStorageByDept"), Throwables.getStackTraceAsString(e));
            throw new NDSException("异步使用ADb查询配销仓占用库存失败：" + e.getMessage());
        }

        return ListUtils.emptyIfNull(skuQtyResultList)
                .stream().collect(Collectors.toMap(SgBSaStorageQtyQueryResult::getPsCSkuId, SgBSaStorageQtyQueryResult::getQty));
    }

    @TargetDataSource(name = "adb")
    @Async(value = "adbAsyncExecutorPool")
    public Future<List<SgBSaStorageQtyQueryResult>> querySaPreStorageByADB(Set<Long> saIds, Set<Long> skuIds) {
        if (CollectionUtils.isEmpty(saIds) || CollectionUtils.isEmpty(skuIds)) {
            return AsyncResult.forValue(Collections.emptyList());
        }

        List<SgBSaStorageQtyQueryResult> retList = new ArrayList<>();
        List<List<Long>> skuIdsList = StorageUtils.getPages(new ArrayList<>(skuIds), 1000);
        for (List<Long> skuList : skuIdsList) {
            retList.addAll(sgBScpDemandSyncMapper.selectSaPreStorageByADB(sgAdbPrefix, saIds, skuList));
        }

        return AsyncResult.forValue(retList);
    }

    /**
     * 已出库量
     *
     * @param saIds   配销仓ID列表
     * @param skuIds  商品ID列表
     * @param endDate 结束日期
     * @return @return 商品ID->出库量
     */
    private Map<Long, BigDecimal> querySaChangeFtpByDept(Set<Long> saIds, Set<Long> skuIds, Date weekStartDate, Date endDate) {
        List<SgBSaStorageQtyQueryResult> skuQtyResultList;
        try {
            SgBScpDemandSyncService bean = ApplicationContextHandle.getBean(SgBScpDemandSyncService.class);
            Future<List<SgBSaStorageQtyQueryResult>> listFuture = bean.querySaChangeFtpByADB(saIds, skuIds, weekStartDate, endDate);
            skuQtyResultList = listFuture.get();
        } catch (Exception e) {
            log.error(LogUtil.format("异步使用ADB查询零售发货单在库变动流水失败，异常：{}",
                    LOG_OBJ + "querySaChangeFtpByDept"), Throwables.getStackTraceAsString(e));
            throw new NDSException("异步使用ADB查询零售发货单在库变动流水失败：" + e.getMessage());
        }

        return ListUtils.emptyIfNull(skuQtyResultList)
                .stream().collect(Collectors.toMap(SgBSaStorageQtyQueryResult::getPsCSkuId, SgBSaStorageQtyQueryResult::getQty));
    }


    @TargetDataSource(name = "adb")
    @Async(value = "adbAsyncExecutorPool")
    public Future<List<SgBSaStorageQtyQueryResult>> querySaChangeFtpByADB(Set<Long> saIds, Set<Long> skuIds, Date weekStartDate, Date endDate) {
        if (CollectionUtils.isEmpty(saIds) || CollectionUtils.isEmpty(skuIds)) {
            return AsyncResult.forValue(Collections.emptyList());
        }

        List<SgBSaStorageQtyQueryResult> retList = new ArrayList<>();
        List<List<Long>> skuIdsList = StorageUtils.getPages(new ArrayList<>(skuIds), 1000);
        for (List<Long> skuList : skuIdsList) {
            retList.addAll(sgBScpDemandSyncMapper.selectSaChangeFtpByADB(sgAdbPrefix, saIds, skuList, weekStartDate, endDate));
        }

        return AsyncResult.forValue(retList);
    }


    /**
     * 查询分货组织架构
     *
     * @param allDeptPlanList 供应链计划公示后需求
     * @return 二级渠道编码->分货组织架构信息
     */
    private Map<String, CpCDistributionOrganization> queryChannelOrgMap(List<SgBScpDemandSync> allDeptPlanList) {
        List<String> channelCodeList = allDeptPlanList.stream().map(SgBScpDemandSync::getLv2ChannelCode)
                .distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(channelCodeList)) {
            log.warn(LogUtil.format("二级渠道编码不会有空值", LOG_OBJ + "queryChannelOrgMap"));
            throw new NDSException("二级渠道编码不会有空值");
        }

        /*分货组织架构：CP_C_DISTRIBUTION_ORGANIZATION*/
        ValueHolderV14<List<CpCDistributionOrganization>> holderV14 = cpCDistributionOrganizationQueryCmd.queryListByCodes(channelCodeList);
        if (!holderV14.isOK() || CollectionUtils.isEmpty(holderV14.getData())) {
            log.warn(LogUtil.format("查询查询分货组织架构失败，入参:{},异常：{}",
                    LOG_OBJ + "queryChannelOrgMap"), channelCodeList, holderV14.getMessage());
            return Collections.emptyMap();
        }

        return ListUtils.emptyIfNull(holderV14.getData()).stream()
                .collect(Collectors.toMap(CpCDistributionOrganization::getEcode, Function.identity()));
    }

    /**
     * 查询商品信息
     *
     * @param allDeptPlanList 供应链计划公示后需求
     * @return 商品编码编码->商品信息
     */
    private Map<String, PsCSku> querySkuEcodeMap(List<SgBScpDemandSync> allDeptPlanList, Map<String, SgBScpDemandSyncConvertConfig> convertConfigMap) {
        Set<String> skuEcodeList = allDeptPlanList.stream().map(SgBScpDemandSync::getPsCSkuEcode)
                .collect(Collectors.toSet());
        Set<String> inSkuEcodes = convertConfigMap.values().stream()
                .map(SgBScpDemandSyncConvertConfig::getInPsCSkuEcode).collect(Collectors.toSet());

        List<String> ecodeList = new ArrayList<>(skuEcodeList);
        ecodeList.addAll(inSkuEcodes);

        if (CollectionUtils.isEmpty(skuEcodeList)) {
            log.warn(LogUtil.format("SKU编码不会有空值", LOG_OBJ + "querySkuEcodeMap"));
            throw new NDSException("SKU编码不会有空值");
        }

        List<PsCSku> psCSkus = CommonCacheValUtils.querySkuListByCode(ecodeList);
        return ListUtils.emptyIfNull(psCSkus).stream()
                .collect(Collectors.toMap(PsCSku::getEcode, Function.identity()));
    }

    /**
     * 获取拆包转化配置
     * 配置中【出SKU】的【数量】除【系数】=需求中【入SKU】的【数量】
     *
     * @return SKU编码与配置映射
     */
    private Map<String, SgBScpDemandSyncConvertConfig> queryConvertConfigMap() {
        List<SgBScpDemandSyncConvertConfig> configList = sgBScpDemandSyncConvertConfigMapper.selectList(
                new QueryWrapper<SgBScpDemandSyncConvertConfig>().lambda()
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isEmpty(configList)) {
            log.info(LogUtil.format("未找到拆包转化配置", LOG_OBJ + "queryConvertConfigMap"));
            return Collections.emptyMap();
        }

        return configList.stream()
                .collect(Collectors.toMap(SgBScpDemandSyncConvertConfig::getOutPsCSkuEcode, Function.identity(), (a, b) -> a));
    }


    /**
     * 参与计算的SKU
     *
     * @param versionBi versionBi
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return SKU编码列表
     */
    public List<String> queryEcode(String versionBi, String startDate, String endDate) {
        return ListUtils.emptyIfNull(sgBScpDemandSyncMapper.selectObjs(new QueryWrapper<SgBScpDemandSync>()
                        .select("DISTINCT ps_c_sku_ecode").lambda()
                        .eq(SgBScpDemandSync::getVersionBi, versionBi)
                        .between(SgBScpDemandSync::getScpDate, startDate, endDate)
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)))
                .stream().map(o -> (String) o).collect(Collectors.toList());
    }

    /**
     * 参与计算的渠道部门
     *
     * @param versionBi versionBi
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 渠道部门列表
     */
    public List<String> queryLvChannelEcode(String versionBi, String startDate, String endDate) {
        return ListUtils.emptyIfNull(sgBScpDemandSyncMapper.selectObjs(new QueryWrapper<SgBScpDemandSync>()
                        .select("DISTINCT lv2_channel_code").lambda()
                        .eq(SgBScpDemandSync::getVersionBi, versionBi)
                        .between(SgBScpDemandSync::getScpDate, startDate, endDate)
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)))
                .stream().map(o -> (String) o).collect(Collectors.toList());
    }

    /**
     * 查询SKU需求
     *
     * @param versionBi   versionBi
     * @param psCSkuEcode sku编码
     * @param startDate   开始时间
     * @param endDate     结束时间
     * @return 渠道编码->[需求日期->需求量]
     */
    public Map<String, Map<Date, BigDecimal>> queryDemandBySku(String versionBi, String psCSkuEcode, String startDate, String endDate) {
        List<SgBCommonStorageQtyQueryResult> dateQtyList = sgBScpDemandSyncMapper.selectDateQtyBySku(versionBi, psCSkuEcode, startDate, endDate);
        return ListUtils.emptyIfNull(dateQtyList).stream()
                .collect(Collectors.groupingBy(
                        SgBCommonStorageQtyQueryResult::getLv2ChannelCode,
                        Collectors.toMap(SgBCommonStorageQtyQueryResult::getPlanDate,
                                SgBCommonStorageQtyQueryResult::getQty, BigDecimal::add)));
    }
}
