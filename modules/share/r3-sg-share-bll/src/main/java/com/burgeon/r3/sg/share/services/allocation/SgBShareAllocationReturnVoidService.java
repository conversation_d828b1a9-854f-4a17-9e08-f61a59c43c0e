package com.burgeon.r3.sg.share.services.allocation;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationReturn;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationReturnItemMapper;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationReturnMapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/21 13:40
 */
@Slf4j
@Component
public class SgBShareAllocationReturnVoidService {

    @Autowired
    SgBShareAllocationReturnMapper mapper;
    @Autowired
    SgBShareAllocationReturnItemMapper itemMapper;


    ValueHolder voidAllocationReturn(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        SgBShareAllocationReturnVoidService service = ApplicationContextHandle.getBean(SgBShareAllocationReturnVoidService.class);
        return R3ParamUtils.convertV14WithResult(service.voidAllocationReturn(request));
    }

    public ValueHolderV14<SgR3BaseResult> voidAllocationReturn(SgR3BaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBShareDistributionVoidService.voidDistribution.ReceiveParams:request{}"
                    , JSONObject.toJSONString(request));
        }
        String lockKsy = SgConstants.SG_B_SHARE_ALLOCATION_RETURN + ":" + request.getObjId();
        SgRedisLockUtils.lock(lockKsy);
        try {
            checkParams(request);

            SgBShareAllocationReturn update = new SgBShareAllocationReturn();
            StorageUtils.setBModelDefalutDataByUpdate(update, request.getLoginUser());
            update.setStatus(SgShareConstants.BILL_STATUS_VOID);
            update.setIsactive(SgConstants.IS_ACTIVE_N);
            User user = request.getLoginUser();
            // 添加作废人相关信息
            update.setDelerId(user.getId().longValue());
            update.setDelerName(user.getName());
            update.setDelerEname(user.getEname());
            update.setDelTime(new Date());
            mapper.update(update, new QueryWrapper<SgBShareAllocationReturn>().lambda().eq(SgBShareAllocationReturn::getId, request.getObjId()));
        } catch (Exception e) {
            AssertUtils.logAndThrowException(e.getMessage(), e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "作废成功！");
    }


    public SgBShareAllocationReturn checkParams(SgR3BaseRequest request) {
        SgBShareAllocationReturn allocationReturn = mapper.selectById(request.getObjId());

        if (SgConstants.IS_ACTIVE_N.equals(allocationReturn.getIsactive())) {
            AssertUtils.logAndThrow("当前记录已作废,不允许重复作废！", request.getLoginUser().getLocale());

        } else if (SgShareConstants.BILL_STATUS_UNSUBMIT != allocationReturn.getStatus()) {
            AssertUtils.logAndThrow("当前单据状态不允许作废！", request.getLoginUser().getLocale());
        }
        return allocationReturn;
    }
}
