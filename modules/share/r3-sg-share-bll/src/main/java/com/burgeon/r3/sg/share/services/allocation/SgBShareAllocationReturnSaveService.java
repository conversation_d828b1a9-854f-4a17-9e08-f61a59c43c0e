package com.burgeon.r3.sg.share.services.allocation;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.utils.BigDecimalUtils;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationReturn;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationReturnItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationReturnItemMapper;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationReturnMapper;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnBillSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnItemSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnSaveRequst;
import com.burgeon.r3.sg.store.services.transfer.SgTransferBillStatusEnum;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/21 10:48
 */
@Slf4j
@Component
public class SgBShareAllocationReturnSaveService {

    @Autowired
    SgBShareAllocationReturnMapper mapper;
    @Autowired
    SgBShareAllocationReturnItemMapper itemMapper;


    public ValueHolder save(QuerySession session) {
        SgBShareAllocationReturnBillSaveRequst request = R3ParamUtils.parseSaveObject(session, SgBShareAllocationReturnBillSaveRequst.class);
        if (log.isDebugEnabled()) {
            log.debug("Start SgBStoTransferSaveService.save:param={}", JSONObject.toJSONString(request));
        }
        request.setR3(true);
        SgBShareAllocationReturnSaveService service = ApplicationContextHandle.getBean(SgBShareAllocationReturnSaveService.class);
        return R3ParamUtils.convertV14WithResult(service.save(request));
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> save(SgBShareAllocationReturnBillSaveRequst request) {
        SgBShareAllocationReturn allocationReturn = checkParams(request);
        if (request.getObjId() == null || request.getObjId() < 0) {
            return insert(request);
        } else {
            return update(request, allocationReturn);
        }
    }

    /**
     * @param request:
     * @Description: 保存单据 独立事物事物
     * @Author: hwy
     * @Date: 2021/5/25 14:24
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<com.burgeon.r3.sg.core.model.result.SgR3BaseResult>
     **/
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public ValueHolderV14<SgR3BaseResult> saveWithNewTrans(SgBShareAllocationReturnBillSaveRequst request) {
        SgBShareAllocationReturn allocationReturn = checkParams(request);
        if (request.getObjId() == null || request.getObjId() < 0) {
            return insert(request);
        } else {
            return update(request, allocationReturn);
        }
    }

    /**
     * @param request:
     * @Description: 保存单据 加入当前事务
     * @Author: hwy
     * @Date: 2021/5/25 14:24
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<com.burgeon.r3.sg.core.model.result.SgR3BaseResult>
     **/
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> saveWithNewTrans2(SgBShareAllocationReturnBillSaveRequst request) {
        SgBShareAllocationReturn allocationReturn = checkParams(request);
        if (request.getObjId() == null || request.getObjId() < 0) {
            return insert(request);
        } else {
            return update(request, allocationReturn);
        }
    }

    public ValueHolderV14<SgR3BaseResult> insert(SgBShareAllocationReturnBillSaveRequst request) {
        SgBShareAllocationReturnSaveRequst allocationReturnSaveRequst = request.getAllocationReturnSaveRequst();
        //冗余配销仓信息
        SgCSaStore saStore = CommonCacheValUtils.getSaStore(allocationReturnSaveRequst.getSgCSaStoreId());
        AssertUtils.notNull(saStore, "未查询到对应配销仓");
        allocationReturnSaveRequst.setSgCSaStoreEcode(saStore.getEcode());
        allocationReturnSaveRequst.setSgCSaStoreEname(saStore.getEname());

        SgBShareAllocationReturn allocationReturn = new SgBShareAllocationReturn();
        BeanUtils.copyProperties(allocationReturnSaveRequst, allocationReturn);
        StorageUtils.setBModelDefalutData(allocationReturn, request.getLoginUser());
        allocationReturn.setOwnerename(request.getLoginUser().getEname());
        allocationReturn.setModifierename(request.getLoginUser().getEname());
        Long id = ModelUtil.getSequence(SgConstants.SG_B_SHARE_ALLOCATION_RETURN);

        allocationReturn.setId(id);
        allocationReturn.setStatus(SgTransferBillStatusEnum.UN_AUDITED.getVal());

        String billNo = SgStoreUtils.getBillNo(SgShareConstants.SEQ_SG_B_SHARE_ALLOCATION_RETURN,
                SgConstants.SG_B_SHARE_ALLOCATION_RETURN.toUpperCase().toUpperCase(),
                allocationReturn, request.getLoginUser().getLocale());
        allocationReturn.setBillNo(billNo);

        List<SgBShareAllocationReturnItemSaveRequst> items = request.getAllocationReturnItemSaveRequst();
        BigDecimal totQty = BigDecimal.ZERO;
        BigDecimal totAmt = BigDecimal.ZERO;
        Integer totRowNum = 0;

        List<SgBShareAllocationReturnItem> insertItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(items)) {
            CommonCacheValUtils.setSkuInfoByCode(items);
            for (SgBShareAllocationReturnItemSaveRequst item : items) {
                if (BigDecimalUtils.nonPositiveInteger(item.getQty())) {
                    AssertUtils.logAndThrow("明细数量非法，sku:" + item.getPsCSkuId() + ",qty：" + item.getQty(),
                            request.getLoginUser().getLocale());
                }
                SgBShareAllocationReturnItem insertItem = new SgBShareAllocationReturnItem();
                BeanUtils.copyProperties(item, insertItem);

                Long itemObjId = ModelUtil.getSequence(SgConstants.SG_B_SHARE_ALLOCATION_RETURN_ITEM);
                insertItem.setId(itemObjId);
                insertItem.setSgBShareAllocationReturnId(id);
                StorageUtils.setBModelDefalutData(insertItem, request.getLoginUser());

                BigDecimal priceList = Optional.ofNullable(insertItem.getPriceList()).orElse(BigDecimal.ZERO);
                insertItem.setAmt(insertItem.getQty().multiply(priceList));
                totQty = totQty.add(insertItem.getQty());
                totAmt = totAmt.add(insertItem.getAmt());
                totRowNum++;
                insertItemList.add(insertItem);
            }
        }
        allocationReturn.setTotRowNum(totRowNum);
        allocationReturn.setTotAmt(totAmt);
        allocationReturn.setTotQty(totQty);
        mapper.insert(allocationReturn);
        /*批量新增500/次*/
        List<List<SgBShareAllocationReturnItem>> insertPageList =
                StorageUtils.getBaseModelPageList(insertItemList, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
        for (List<SgBShareAllocationReturnItem> pageList : insertPageList) {
            if (CollectionUtils.isEmpty(pageList)) {
                continue;
            }
            int insertResult = itemMapper.batchInsert(pageList);
            if (insertResult != pageList.size()) {
                AssertUtils.logAndThrow("保存分货退货单明细异常！", request.getLoginUser().getLocale());
            }
        }

        SgR3BaseResult baseResult = new SgR3BaseResult();
        baseResult.setDataJo(id, SgConstants.SG_B_SHARE_ALLOCATION_RETURN.toUpperCase());
        return new ValueHolderV14<>(baseResult, ResultCode.SUCCESS, "保存分货单成功！");
    }


    public ValueHolderV14<SgR3BaseResult> update(SgBShareAllocationReturnBillSaveRequst request,
                                                 SgBShareAllocationReturn allocationReturn) {
        String lockKsy = SgConstants.SG_B_SHARE_ALLOCATION_RETURN + ":" + request.getObjId();
        SgRedisLockUtils.lock(lockKsy);
        try {
            SgBShareAllocationReturnSaveRequst transferSaveRequest = request.getAllocationReturnSaveRequst();
            SgBShareAllocationReturn update = new SgBShareAllocationReturn();
            if (transferSaveRequest != null) {
                BeanUtils.copyProperties(transferSaveRequest, update);
            }
            update.setId(request.getObjId());

            List<SgBShareAllocationReturnItemSaveRequst> items = request.getAllocationReturnItemSaveRequst();
            if (CollectionUtils.isNotEmpty(items)) {
                List<SgBShareAllocationReturnItem> itemList = itemMapper.selectList(new QueryWrapper<SgBShareAllocationReturnItem>()
                        .lambda().eq(SgBShareAllocationReturnItem::getSgBShareAllocationReturnId, request.getObjId()));
                Map<String, SgBShareAllocationReturnItem> map = new HashMap<>();
                Map<Long, SgBShareAllocationReturnItem> mapId = new HashMap<>();
                for (SgBShareAllocationReturnItem item : itemList) {
                    map.put(item.getPsCSkuEcode(), item);
                    mapId.put(item.getId(), item);
                }

                BigDecimal totQty = allocationReturn.getTotQty();
                BigDecimal totAmt = allocationReturn.getTotAmt();
                Integer totRowNum = allocationReturn.getTotRowNum();
                List<SgBShareAllocationReturnItem> insertItemList = new ArrayList<>();
                for (SgBShareAllocationReturnItemSaveRequst item : items) {
                    if (BigDecimalUtils.nonPositiveInteger(item.getQty())) {
                        AssertUtils.logAndThrow("明细数量非法，sku:" + item.getPsCSkuId() + ",qty：" + item.getQty(),
                                request.getLoginUser().getLocale());
                    }

                    Long itemId = item.getId();

                    //编辑行编辑，直接覆盖
                    if (itemId != null && itemId > 0) {
                        SgBShareAllocationReturnItem queryItem = mapId.get(item.getId());
                        if (queryItem != null) {
                            SgBShareAllocationReturnItem updateItem = new SgBShareAllocationReturnItem();
                            updateItem.setId(queryItem.getId());

                            BigDecimal qty = item.getQty();
                            updateItem.setQty(qty);
                            updateItem.setAmt(updateItem.getQty().multiply(queryItem.getPriceList()));
                            StorageUtils.setBModelDefalutDataByUpdate(updateItem, request.getLoginUser());
                            itemMapper.updateById(updateItem);

                            totAmt = totAmt.add(updateItem.getAmt().subtract(queryItem.getAmt()));
                            totQty = totQty.add(updateItem.getQty().subtract(queryItem.getQty() == null
                                    ? BigDecimal.ZERO : queryItem.getQty()));
                        } else {
                            AssertUtils.logAndThrow("当前明细不存在！", request.getLoginUser().getLocale());
                        }
                    } else if (map.get(item.getPsCSkuEcode()) != null) {
                        SgBShareAllocationReturnItem queryItem = map.get(item.getPsCSkuEcode());
                        SgBShareAllocationReturnItem updateItem = new SgBShareAllocationReturnItem();
                        updateItem.setId(queryItem.getId());

                        BigDecimal qty;
                        //代表矩阵录入需要覆盖
                        if (!StringUtils.isEmpty(item.getPsCProEcode())) {
                            qty = item.getQty();
                        } else {//调拨是条码明细录入条码信息后回车，需要累加
                            qty = queryItem.getQty().add(item.getQty() == null ? BigDecimal.ONE : item.getQty());
                        }
                        updateItem.setQty(qty);
                        updateItem.setAmt(updateItem.getQty().multiply(queryItem.getPriceList()));
                        StorageUtils.setBModelDefalutDataByUpdate(updateItem, request.getLoginUser());
                        itemMapper.updateById(updateItem);

                        totAmt = totAmt.add(updateItem.getAmt().subtract(queryItem.getAmt()));
                        totQty = totQty.add(updateItem.getQty().subtract(queryItem.getQty() == null
                                ? BigDecimal.ZERO : queryItem.getQty()));
                    } else {
                        CommonCacheValUtils.setSkuInfo(null, item.getPsCSkuEcode(), item);
                        SgBShareAllocationReturnItem insertItem = new SgBShareAllocationReturnItem();
                        BeanUtils.copyProperties(item, insertItem);
                        insertItem.setSgBShareAllocationReturnId(request.getObjId());
                        Long itemObjId = ModelUtil.getSequence(SgConstants.SG_B_SHARE_ALLOCATION_RETURN_ITEM);
                        insertItem.setId(itemObjId);
                        StorageUtils.setBModelDefalutData(insertItem, request.getLoginUser());
                        if (insertItem.getQtyIn() == null) {
                            insertItem.setQtyIn(BigDecimal.ZERO);
                        }
                        if (insertItem.getQtyOut() == null) {
                            insertItem.setQtyOut(BigDecimal.ZERO);
                        }
                        if (insertItem.getQty() == null) {
                            insertItem.setQty(BigDecimal.ONE);
                        }
                        insertItem.setAmtIn(insertItem.getQtyIn().multiply(insertItem.getPriceList()));
                        insertItem.setAmtOut(insertItem.getQtyOut().multiply(insertItem.getPriceList()));
                        insertItem.setAmt(insertItem.getQty().multiply(insertItem.getPriceList()));
                        totQty = totQty.add(insertItem.getQty());
                        totAmt = totAmt.add(insertItem.getAmt());
                        insertItemList.add(insertItem);
                        totRowNum++;
                    }
                }

                update.setId(request.getObjId());
                if (transferSaveRequest != null) {
                    BeanUtils.copyProperties(transferSaveRequest, update);
                }
                update.setTotRowNum(totRowNum);
                update.setTotAmt(totAmt);
                update.setTotQty(totQty);

                /*批量新增500/次*/
                List<List<SgBShareAllocationReturnItem>> insertPageList =
                        StorageUtils.getBaseModelPageList(insertItemList, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
                for (List<SgBShareAllocationReturnItem> pageList : insertPageList) {
                    if (CollectionUtils.isEmpty(pageList)) {
                        continue;
                    }
                    int insertResult = itemMapper.batchInsert(pageList);
                    if (insertResult != pageList.size()) {
                        AssertUtils.logAndThrow("保存分货退货单明细异常！", request.getLoginUser().getLocale());
                    }
                }
            }

            StorageUtils.setBModelDefalutDataByUpdate(update, request.getLoginUser());
            mapper.updateById(update);
        } catch (Exception e) {
            AssertUtils.logAndThrowException(e.getMessage(), e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, "保存成功！");
    }

    public SgBShareAllocationReturn checkParams(SgBShareAllocationReturnBillSaveRequst request) {
        if (!request.isR3()) {
            SgStoreUtils.checkR3BModelDefalut(request);
            AssertUtils.notNull(request.getAllocationReturnSaveRequst(), "主表信息不能为空！");
            AssertUtils.notNull(request.getAllocationReturnItemSaveRequst(), "明细信息不能为空！");
            for (SgBShareAllocationReturnItemSaveRequst itemSaveRequest : request.getAllocationReturnItemSaveRequst()) {
                String psSkuEcode = itemSaveRequest.getPsCSkuEcode();
                AssertUtils.notNull(psSkuEcode, "条码编码不能为空！");
                AssertUtils.notNull(itemSaveRequest.getSourceBillItemId(), "[" + psSkuEcode + "]来源单据ID不能为空！");
                AssertUtils.notNull(itemSaveRequest.getPsCSkuId(), "[" + psSkuEcode + "]条码ID不能为空！");
                AssertUtils.notNull(itemSaveRequest.getPsCProId(), "[" + psSkuEcode + "]商品ID不能为空！");
                AssertUtils.notNull(itemSaveRequest.getPsCProEcode(), "[" + psSkuEcode + "]商品编码不能为空！");
                AssertUtils.notNull(itemSaveRequest.getPsCProEname(), "[" + psSkuEcode + "]商品名称不能为空！");
                AssertUtils.notNull(itemSaveRequest.getPsCSpec1Id(), "[" + psSkuEcode + "]规格1ID不能为空！");
                AssertUtils.notNull(itemSaveRequest.getPsCSpec1Ecode(), "[" + psSkuEcode + "]规格1编码不能为空！");
                AssertUtils.notNull(itemSaveRequest.getPsCSpec1Ename(), "[" + psSkuEcode + "]规格1名称不能为空！");
                AssertUtils.notNull(itemSaveRequest.getPsCSpec2Id(), "[" + psSkuEcode + "]规格2ID不能为空！");
                AssertUtils.notNull(itemSaveRequest.getPsCSpec2Ecode(), "[" + psSkuEcode + "]规格2编码不能为空！");
                AssertUtils.notNull(itemSaveRequest.getPsCSpec2Ename(), "[" + psSkuEcode + "]规格2名称不能为空！");
                AssertUtils.cannot(BigDecimal.ZERO.compareTo(itemSaveRequest.getQty()) > 0, "["
                        + psSkuEcode + "]数量小于0不允许");
            }
        } else {
            //页面保存数量不能小于0
            List<SgBShareAllocationReturnItemSaveRequst> allocationReturnItemSaveRequst = request.getAllocationReturnItemSaveRequst();
            if (CollectionUtils.isNotEmpty(allocationReturnItemSaveRequst)) {
                for (SgBShareAllocationReturnItemSaveRequst itemSaveRequest : allocationReturnItemSaveRequst) {
                    if (itemSaveRequest.getId() < 0L) {
                        AssertUtils.notNull(itemSaveRequest.getPsCSkuEcode(), "条码不能为空");
                    }
                    if (itemSaveRequest.getQty() == null) {
                        itemSaveRequest.setQty(BigDecimal.ONE);
                    }
                    if (itemSaveRequest.getQty().compareTo(BigDecimal.ZERO) < 0) {
                        AssertUtils.logAndThrow("当前分货退货单明细，数量不允许为负数！");
                    }
                }
            }
        }

        SgBShareAllocationReturn allocationReturn = null;
        if (request.getObjId() != null && request.getObjId() > 0) {
            allocationReturn = mapper.selectById(request.getObjId());
            if (SgConstants.IS_ACTIVE_N.equals(allocationReturn.getIsactive())) {
                AssertUtils.logAndThrow("当前单据已作废不允许保存！", request.getLoginUser().getLocale());
            } else if (SgShareConstants.BILL_STATUS_UNSUBMIT != allocationReturn.getStatus()) {
                AssertUtils.logAndThrow("当前单据状态不允许保存！", request.getLoginUser().getLocale());
            }
        }
        return allocationReturn;
    }
}
