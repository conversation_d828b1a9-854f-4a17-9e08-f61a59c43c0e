package com.burgeon.r3.sg.share.services.ryytndistribution.scpdemand;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.CollectionUtils;
import com.aliyuncs.utils.StringUtils;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgBScpDemandSync;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class ScpDemandSyncImportListener extends AnalysisEventListener<ScpDemandSyncImportParam> {
    private static final int BATCH_SIZE = 1000;
    private ScpDemandSyncImportProperties properties;

    private int successCount = 0;
    private int totalCount = 0;
    /**
     * 已读数据
     */
    private List<SgBScpDemandSync> dataList = new ArrayList<>();

    public ScpDemandSyncImportListener(ScpDemandSyncImportProperties properties) {
        this.properties = properties;
    }

    @Override
    public void invoke(ScpDemandSyncImportParam data, AnalysisContext context) {
        totalCount++;

        if (!validateParam(data, context.readRowHolder().getRowIndex())) {
            /*失败了记录日志，继续执行*/
            return;
        }

        try {
            dataList.add(convertDto(data));
        } catch (Exception e) {
            log.warn(LogUtil.format("第{}行数据校验失败，原因：{}",
                    "ScpDemandSyncImportListener.invoke"), context.readRowHolder().getRowIndex(), e.getMessage());
            properties.addError(new SgBScpDemandSyncErrorDto(context.readRowHolder().getRowIndex(),
                    data.getScpEcode(), e.getMessage()));
            return;
        }

        successCount++;

        if (dataList.size() >= BATCH_SIZE) {
            handleDataList();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (!CollectionUtils.isEmpty(dataList)) {
            handleDataList();
        }

        properties.setTotalCount(totalCount);
        properties.setSuccessCount(successCount);
    }

    private SgBScpDemandSync convertDto(ScpDemandSyncImportParam data) {
        SgBScpDemandSync dto = new SgBScpDemandSync();
        dto.setScpEcode(data.getScpEcode());
        dto.setScpVersion(data.getScpVersion());
        dto.setLv2ChannelCode(data.getLv2ChannelCode());
        dto.setPsCSkuEcode(data.getPsCSkuEcode());
        dto.setVersionPlan(data.getVersionPlan());
        dto.setMonthWeek(data.getMonthWeek());

        dto.setScpDate(DateUtil.parse(data.getScpDate(), DatePattern.PURE_DATE_PATTERN));
        dto.setScpQty(new BigDecimal(data.getScpQty()));

        dto.setType(properties.getType());
        dto.setVersionBi(properties.getVersionBi());
        StorageUtils.setBModelDefalutData(dto, R3SystemUserResource.getSystemRootUser());
        return dto;
    }


    /**
     * 验证参数
     *
     * @param data     参数对象
     * @param rowIndex EXCEL行号
     */
    private boolean validateParam(ScpDemandSyncImportParam data, Integer rowIndex) {
        boolean isValid = true;
        StringBuilder errMsg = new StringBuilder();
        if (StringUtils.isEmpty(data.getScpEcode())) {
            errMsg.append("SCP编码不能为空;");
            isValid = false;
        }
        if (StringUtils.isEmpty(data.getScpVersion())) {
            errMsg.append("SCP版本不能为空;");
            isValid = false;
        }
        if (StringUtils.isEmpty(data.getVersionPlan())) {
            errMsg.append("版本计划不能为空;");
            isValid = false;
        }
        if (StringUtils.isEmpty(data.getLv2ChannelCode())) {
            errMsg.append("二级渠道编码不能为空;");
            isValid = false;
        }
        if (StringUtils.isEmpty(data.getPsCSkuEcode())) {
            errMsg.append("SKU编码不能为空;");
            isValid = false;
        }
        if (StringUtils.isEmpty(data.getScpDate())) {
            errMsg.append("SCP日期不能为空;");
            isValid = false;
        }
        if (StringUtils.isEmpty(data.getMonthWeek())) {
            errMsg.append("计划周数不能为空;");
            isValid = false;
        }

        try {
            if (StringUtils.isEmpty(data.getScpQty())
                    || BigDecimal.ZERO.compareTo(new BigDecimal(data.getScpQty())) == 0) {
                errMsg.append("SCP数量不能为空或零;");
                isValid = false;
            }
        } catch (Exception e) {
            errMsg.append("SCP数量非法;");
            isValid = false;
        }

        if (!isValid) {
            properties.addError(new SgBScpDemandSyncErrorDto(rowIndex, data.getScpEcode(), errMsg.toString()));
        }
        return isValid;
    }


    /**
     * 处理数据，执行数据库操作
     */
    private void handleDataList() {
        properties.getSgBScpDemandSyncMapper().batchInsert(dataList);
        dataList.clear();
    }

}