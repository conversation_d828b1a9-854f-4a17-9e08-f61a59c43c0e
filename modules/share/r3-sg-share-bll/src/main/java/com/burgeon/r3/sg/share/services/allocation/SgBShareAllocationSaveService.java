package com.burgeon.r3.sg.share.services.allocation;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.basic.model.request.SgStoreInfoQueryRequest;
import com.burgeon.r3.sg.basic.services.SgCStoreQueryService;
import com.burgeon.r3.sg.basic.utils.BigDecimalUtils;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocation;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocationItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationItemMapper;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationMapper;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationBillSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationSaveItemRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationSaveRequst;
import com.burgeon.r3.sg.share.model.result.allocation.SgBShareAllocationSaveResult;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2021-05-17 14:13
 * @Description:
 */
@Slf4j
@Component
public class SgBShareAllocationSaveService {

    @Autowired
    private SgBShareAllocationMapper allocationMapper;
    @Autowired
    private SgBShareAllocationItemMapper allocationItemMapper;

    @Autowired
    private SgCSaStoreMapper sgSaStoreMapper;

    /**
     * 页面保存 入口
     *
     * @param session 入参
     * @return 出参
     */
    ValueHolder save(QuerySession session) {
        SgBShareAllocationBillSaveRequst request = R3ParamUtils.parseSaveObject(session, SgBShareAllocationBillSaveRequst.class);
        if (log.isDebugEnabled()) {
            log.debug("Start SgBShareAllocationSaveService.save ReceiveParams:request={};",
                    JSONObject.toJSONString(request));
        }
        Long objId = request.getObjId();
        AssertUtils.notNull(objId, "主建id为空");
        request.setR3(true);
        SgBShareAllocationSaveService service = ApplicationContextHandle.getBean(SgBShareAllocationSaveService.class);
        ValueHolderV14<SgR3BaseResult> save = service.save(request);
        return R3ParamUtils.convertV14WithResult(save);
    }

    /**
     * 保存业务
     *
     * @param request 入参
     * @return 出参
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> save(SgBShareAllocationBillSaveRequst request) {
        SgBShareAllocationSaveResult result = new SgBShareAllocationSaveResult(-1L, ResultCode.FAIL, "新增分货单失败！");
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "保存成功!");
        try {
            checkParam(request);
            Long objId = request.getObjId();
            if (objId != null && objId > -1) {
                result = update(request);
            } else {
                result = insert(request);
            }
        } catch (Exception e) {
            AssertUtils.logAndThrowException("分货单保存异常", e, request.getLoginUser().getLocale());
        }

        SgR3BaseResult baseResult = new SgR3BaseResult();
        baseResult.setDataJo(result.getId(), SgConstants.SG_B_SHARE_ALLOCATION.toUpperCase());
        if (result.getCode() == ResultCode.FAIL) {
            v14 = new ValueHolderV14<>(ResultCode.FAIL, SgConstants.MESSAGE_STATUS_FAIL);
        }
        v14.setData(baseResult);
        return v14;
    }

    /**
     * 批量新增or保存分货单
     *
     * @param requsts 入参
     * @return 出参
     */
//    public ValueHolderV14<List<SgBShareAllocationSaveResult>> batchSaveSgShareAllocation(List<SgBShareAllocationBillSaveRequst> requsts) {
//        ValueHolderV14<List<SgBShareAllocationSaveResult>> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "批量新增/保存成功！");
//        if (requsts == null) {
//            v14.setCode(ResultCode.FAIL);
//            v14.setMessage("批量新增/保存分货单入参为空！");
//            return v14;
//        }
//        if (log.isDebugEnabled()) {
//            log.debug("Start.{}.batchSaveSgShareAllocation,requsts.size={}", this.getClass().getName(),
//                    JSONObject.toJSONString(requsts.size()));
//        }
//        List<SgBShareAllocationSaveResult> list = new ArrayList<>();
//        try {
//            SgBShareAllocationSaveService bean = ApplicationContextHandle.getBean(SgBShareAllocationSaveService.class);
//            for (SgBShareAllocationBillSaveRequst requst : requsts) {
//                SgBShareAllocationSaveResult result = bean.saveSgShareAllocation(requst);
//                list.add(result);
//            }
//        } catch (Exception e) {
//            AssertUtils.logAndThrowException(e.getMessage(), e);
//        }
//        v14.setData(list);
//        if (log.isDebugEnabled()) {
//            log.debug("end.{}.batchSaveSgShareAllocation,return.v14={}", this.getClass().getName(),
//                    JSONObject.toJSONString(v14));
//        }
//        return v14;
//    }

    /**
     * 单个新增or保存分货单
     *
     * @param requst 入参
     * @return 出参
     */
//    @Transactional(rollbackFor = Exception.class)
//    public SgBShareAllocationSaveResult saveSgShareAllocation(SgBShareAllocationBillSaveRequst requst) {
//        if (log.isDebugEnabled()) {
//            log.debug("Start.{}.saveSgShareAllocation,requst.param={}", this.getClass().getName(),
//                    JSONObject.toJSONString(requst));
//        }
//        SgBShareAllocationSaveResult result = new SgBShareAllocationSaveResult(-1L, ResultCode.FAIL, "新增分货单失败！");
//        try {
//            checkParam(requst);
//            Long objId = requst.getObjId();
//            if (objId != null && objId > -1) {
//                result = update(requst);
//            } else {
//                result = insert(requst);
//            }
//        } catch (Exception e) {
//            AssertUtils.logAndThrowException(e.getMessage(), e);
//        }
//        if (log.isDebugEnabled()) {
//            log.debug("end.{}.saveSgShareAllocation,return.result={}", this.getClass().getName(),
//                    JSONObject.toJSONString(result));
//        }
//        return result;
//    }

    /**
     * 新增分货单
     *
     * @param requst 入参
     * @return 出参
     */
    private SgBShareAllocationSaveResult insert(SgBShareAllocationBillSaveRequst requst) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBShareAllocationSaveService.insert ReceiveParams:request={};",
                    JSONObject.toJSONString(requst));
        }

        SgBShareAllocationSaveRequst saveRequst = requst.getSgBShareAllocationSaveRequst();
        User loginUser = requst.getLoginUser();
        //明细数据
        List<SgBShareAllocationSaveItemRequst> itemRequstList = requst.getSgBShareAllocationSaveItemRequsts();
        List<SgBShareAllocationItem> itemList = new ArrayList<>();
        BigDecimal totQty = BigDecimal.ZERO;
        BigDecimal totAmt = BigDecimal.ZERO;

        SgBShareAllocation insert = new SgBShareAllocation();
        BeanUtils.copyProperties(saveRequst, insert);
        //单据ID
        Long id = ModelUtil.getSequence(SgConstants.SG_B_SHARE_ALLOCATION);
        insert.setId(id);
        //获取单据编号
        String billNo = SgStoreUtils.getBillNo(SgShareConstants.SEQ_SG_B_SHARE_ALLOCATION,
                SgConstants.SG_B_SHARE_ALLOCATION.toUpperCase().toUpperCase(), insert, loginUser.getLocale());
        insert.setBillNo(billNo);

        // 聚合仓信息 sgStoreInfoQueryRequest查询聚合仓信息入参
        SgStoreInfoQueryRequest sgStoreInfoQueryRequest = new SgStoreInfoQueryRequest();
        List<Long> shareIds = new ArrayList<>();
        shareIds.add(saveRequst.getSgCShareStoreId());
        sgStoreInfoQueryRequest.setIds(shareIds);
        SgCStoreQueryService queryService = ApplicationContextHandle.getBean(SgCStoreQueryService.class);
        HashMap<Long, SgCShareStore> sgShareStoreHashMap = queryService.getShareStoreInfoById(sgStoreInfoQueryRequest);

        SgCShareStore sgShareStore = sgShareStoreHashMap.get(saveRequst.getSgCShareStoreId());
        AssertUtils.notNull(sgShareStore, "当前分货单所关联聚合仓不存在，请检查！");
        insert.setSgCShareStoreId(sgShareStore.getId());
        insert.setSgCShareStoreEname(sgShareStore.getEname());
        insert.setSgCShareStoreEcode(sgShareStore.getEcode());
        //设置头表用户信息字段
        StorageUtils.setBModelDefalutData(insert, loginUser);
        insert.setOwnerename(loginUser.getEname());
        insert.setModifierename(loginUser.getEname());
        if (CollectionUtils.isNotEmpty(itemRequstList)) {
            List<Long> saId = itemRequstList.stream().map(SgBShareAllocationSaveItemRequst::getSgCSaStoreId).distinct().collect(Collectors.toList());
            // 覆盖上面一层 ids，把聚合仓id变成sa仓id查sa仓信息
            sgStoreInfoQueryRequest.setIds(saId);
            HashMap<Long, SgCSaStore> saStoreMap = queryService.getSaStoreInfoById(sgStoreInfoQueryRequest);
            //批量补全商品信息 根据条码code
            CommonCacheValUtils.setSkuInfoBySkuCodeList(itemRequstList);
            for (SgBShareAllocationSaveItemRequst itemRequst : itemRequstList) {
                if (BigDecimalUtils.nonPositiveInteger(itemRequst.getQty())) {
                    AssertUtils.logAndThrow(itemRequst.getPsCSkuEcode() + "分货单明细数量非法！" + itemRequst.getQty(), loginUser.getLocale());
                }

                SgBShareAllocationItem item = setItemData(itemRequst, saStoreMap.get(itemRequst.getSgCSaStoreId()), id,
                        loginUser);
                itemList.add(item);
                //头表汇总字段
                totQty = totQty.add(itemRequst.getQty());
//                totAmt = totAmt.add(itemRequst.getPriceList().multiply(itemRequst.getQty()));
                totAmt = totAmt.add(Optional.ofNullable(itemRequst.getQty()).orElse(BigDecimal.ZERO)
                        .multiply(Optional.ofNullable(itemRequst.getPriceList()).orElse(BigDecimal.ZERO)));
            }
        }

        insert.setTotAmt(totAmt);
        insert.setTotQty(totQty);
        insert.setTotQtyApply(totQty);
        insert.setTotQtyDiff(BigDecimal.ZERO);
        insert.setStatus(SgShareConstants.BILL_STATUS_UNSUBMIT);
        insert.setTotRowNum(itemList.size());
        insert.setIsactive(SgConstants.IS_ACTIVE_Y);

        if (allocationMapper.insert(insert) > 0) {
            if (CollectionUtils.isNotEmpty(itemList)) {
                /*批量新增500/次*/
                List<List<SgBShareAllocationItem>> insertPageList =
                        StorageUtils.getBaseModelPageList(itemList, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
                for (List<SgBShareAllocationItem> pageList : insertPageList) {
                    if (CollectionUtils.isEmpty(pageList)) {
                        continue;
                    }
                    int insertResult = allocationItemMapper.batchInsert(pageList);
                    if (insertResult != pageList.size()) {
                        AssertUtils.logAndThrow("保存分货单明细异常！", loginUser.getLocale());
                    }
                }
            }
        }

        return new SgBShareAllocationSaveResult(id, ResultCode.SUCCESS, "新增分货单成功！");
    }

    /**
     * 保存分货单
     *
     * @param requst 入参
     * @return 出参
     */
    private SgBShareAllocationSaveResult update(SgBShareAllocationBillSaveRequst requst) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBShareAllocationSaveService.update ReceiveParams:request={};",
                    JSONObject.toJSONString(requst));
        }

        SgBShareAllocationSaveRequst saveRequst = requst.getSgBShareAllocationSaveRequst();
        User loginUser = requst.getLoginUser();
        Long objId = requst.getObjId();
        SgBShareAllocation sgShareAllocation = allocationMapper.selectById(objId);

        SgBShareAllocation update = new SgBShareAllocation();
        //明细和头表可能只有一方修改也有可能都修改
        if (saveRequst != null) {
            BeanUtils.copyProperties(saveRequst, update);
        }
        update.setId(objId);
        StorageUtils.setBModelDefalutDataByUpdate(update, loginUser);

        List<SgBShareAllocationSaveItemRequst> itemRequstList = requst.getSgBShareAllocationSaveItemRequsts();
        if (CollectionUtils.isNotEmpty(itemRequstList)) {
            /*
            批量500/次
            理由：防止多次请求数据库，
            */
            List<List<SgBShareAllocationSaveItemRequst>> itemPageList =
                    StorageUtils.getPageList(itemRequstList, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
            for (List<SgBShareAllocationSaveItemRequst> itemRequsts : itemPageList) {
                saveItem(itemRequsts, objId, loginUser, update, sgShareAllocation);
            }
        }
        allocationMapper.updateById(update);

        return new SgBShareAllocationSaveResult(objId, ResultCode.SUCCESS, "新增分货单成功！");
    }

    /**
     * 保存分货单时，明细的处理
     *
     * @param itemRequsts       带有新数据的明细
     * @param objId             主表id
     * @param loginUser         用户
     * @param update            主表更新的数据
     * @param sgShareAllocation 原来的主表数据
     */
    private void saveItem(List<SgBShareAllocationSaveItemRequst> itemRequsts, Long objId, User loginUser,
                          SgBShareAllocation update, SgBShareAllocation sgShareAllocation) {
        BigDecimal totQty = sgShareAllocation.getTotQty();
//        BigDecimal totAmt = sgShareAllocation.getTotAmt();
        BigDecimal totAmt = Optional.ofNullable(sgShareAllocation.getTotAmt()).orElse(BigDecimal.ZERO);
        //存放全部需要新增的明细统一处理
        List<SgBShareAllocationSaveItemRequst> insertItemRequest = new ArrayList<>();
        List<Long> saids = itemRequsts.stream().map(SgBShareAllocationSaveItemRequst::getSgCSaStoreId).collect(Collectors.toList());

        //查询明细是否存在 存在累加更新不存在新增
        List<SgBShareAllocationItem> items = allocationItemMapper.selectList(new QueryWrapper<SgBShareAllocationItem>()
                .lambda()
                .eq(SgBShareAllocationItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgBShareAllocationItem::getSgBShareAllocationId, objId));

        //全部新增
        if (CollectionUtils.isEmpty(items)) {
            insertItemRequest.addAll(itemRequsts);
            //部分新增 部分更新
        } else {
            Map<String, SgBShareAllocationItem> map = new HashMap<>(16);
            Map<Long, SgBShareAllocationItem> itemMap = new HashMap<>(16);

            //把根据sa仓+条码 查出来的数据放到map 后面用
            for (SgBShareAllocationItem item : items) {
                map.put(item.getPsCSkuEcode() + ":" + item.getSgCSaStoreId(), item);
                itemMap.put(item.getId(), item);
            }

            for (SgBShareAllocationSaveItemRequst itemRequst : itemRequsts) {
                SgBShareAllocationItem item = itemMap.get(itemRequst.getId());
                SgBShareAllocationItem allocationItem = map.get(itemRequst.getPsCSkuEcode() + ":"
                        + itemRequst.getSgCSaStoreId());
                //新增明细 页面上的2种情况都考虑进去
                if (item == null && allocationItem == null) {
                    insertItemRequest.add(itemRequst);
                    //保存明细
                } else {
                    SgBShareAllocationItem updateItem = new SgBShareAllocationItem();
                    //带sa仓+条码 累加  直接改数量覆盖
                    if (item != null) {
                        updateItem.setId(item.getId());
                        updateItem.setQty(itemRequst.getQty());
//                        updateItem.setAmt(updateItem.getQty().multiply(item.getPriceList()));
                        updateItem.setAmt(Optional.ofNullable(updateItem.getQty()).orElse(BigDecimal.ZERO)
                                .multiply(Optional.ofNullable(item.getPriceList()).orElse(BigDecimal.ZERO)));
                        totQty = (totQty.subtract(item.getQty())).add(itemRequst.getQty());
//                        totAmt = (totAmt.subtract(item.getAmt())).add(itemRequst.getQty().multiply(item.getPriceList()));
                        totAmt = (totAmt.subtract(Optional.ofNullable(item.getAmt()).orElse(BigDecimal.ZERO)))
                                .add(Optional.ofNullable(itemRequst.getQty()).orElse(BigDecimal.ZERO)
                                        .multiply(Optional.ofNullable(itemRequst.getQty()).orElse(item.getPriceList())));
                    } else {
                        updateItem.setId(allocationItem.getId());
                        updateItem.setQty(allocationItem.getQty().add(itemRequst.getQty()));
//                        updateItem.setAmt(updateItem.getQty().multiply(allocationItem.getPriceList()));
                        updateItem.setAmt(Optional.ofNullable(updateItem.getQty()).orElse(BigDecimal.ZERO)
                                .multiply(Optional.ofNullable(allocationItem.getPriceList()).orElse(BigDecimal.ZERO)));
                        totQty = totQty.add(itemRequst.getQty());
//                        totAmt = totAmt.add(itemRequst.getQty().multiply(allocationItem.getPriceList()));
                        totAmt = totAmt.add(Optional.ofNullable(itemRequst.getQty()).orElse(BigDecimal.ZERO)
                                .multiply(Optional.ofNullable(allocationItem.getPriceList()).orElse(BigDecimal.ZERO)));
                    }
                    //申请数量
                    updateItem.setQtyApply(updateItem.getQty());
                    if (BigDecimalUtils.nonPositiveInteger(updateItem.getQty())) {
                        AssertUtils.logAndThrow(updateItem.getPsCSkuEcode() + "分货单明细数量非法！" + updateItem.getQty(), loginUser.getLocale());
                    }

                    StorageUtils.setBModelDefalutDataByUpdate(updateItem, loginUser);
                    allocationItemMapper.updateById(updateItem);
                }
            }
        }

        //处理需要新增的明细
        if (CollectionUtils.isNotEmpty(insertItemRequest)) {
            List<SgBShareAllocationItem> insertItemList = new ArrayList<>();
            //sa仓信息，新增明细需要
            SgStoreInfoQueryRequest sgStoreInfoQueryRequest = new SgStoreInfoQueryRequest();
            sgStoreInfoQueryRequest.setIds(saids);
            SgCStoreQueryService queryService = ApplicationContextHandle.getBean(SgCStoreQueryService.class);
            HashMap<Long, SgCSaStore> saStoreMap = queryService.getSaStoreInfoById(sgStoreInfoQueryRequest);

            //批量补全商品信息
            CommonCacheValUtils.setSkuInfoBySkuCodeList(insertItemRequest);
            for (SgBShareAllocationSaveItemRequst itemRequst : insertItemRequest) {
                SgBShareAllocationItem item = setItemData(itemRequst, saStoreMap.get(itemRequst.getSgCSaStoreId()),
                        objId, loginUser);
                insertItemList.add(item);
                //头表汇总字段
                totQty = totQty.add(itemRequst.getQty());
//                totAmt = totAmt.add(itemRequst.getPriceList().multiply(itemRequst.getQty()));
                totAmt = totAmt.add(Optional.ofNullable(itemRequst.getQty()).orElse(BigDecimal.ZERO)
                        .multiply(Optional.ofNullable(itemRequst.getPriceList()).orElse(BigDecimal.ZERO)));
            }

            //批量新增明细 注：这里在上一层（save方法）已经做过分批次，这里数量不会超过500
            if (CollectionUtils.isNotEmpty(insertItemList)) {
                allocationItemMapper.batchInsert(insertItemList);
                update.setTotRowNum(sgShareAllocation.getTotRowNum() + insertItemList.size());
            }
        }

        update.setTotQty(totQty);
        //申请数量 新增保存跟数量一致
        update.setTotQtyApply(totQty);
        update.setTotAmt(totAmt);
    }

    /**
     * 明细新增封装数据
     *
     * @param itemRequst 明细数据
     * @param sgSaStore  sa仓
     * @param objId      主表id
     * @param loginUser  用户
     * @return 出参
     */
    private SgBShareAllocationItem setItemData(SgBShareAllocationSaveItemRequst itemRequst, SgCSaStore sgSaStore,
                                               Long objId, User loginUser) {
        SgBShareAllocationItem insertItem = new SgBShareAllocationItem();

        BeanUtils.copyProperties(itemRequst, insertItem);
        Long itemId = ModelUtil.getSequence(SgConstants.SG_B_SHARE_ALLOCATION_ITEM);
        insertItem.setId(itemId);
        //吊牌金额
//        insertItem.setAmt(itemRequst.getPriceList().multiply(itemRequst.getQty()));
        insertItem.setAmt(Optional.ofNullable(itemRequst.getQty()).orElse(BigDecimal.ZERO)
                .multiply(Optional.ofNullable(itemRequst.getPriceList()).orElse(BigDecimal.ZERO)));
        //申请数量
        insertItem.setQtyApply(itemRequst.getQty());
        //差异数量 保存时候申请数量 = 数量
        insertItem.setQtyDiff(BigDecimal.ZERO);
        //SA仓信息
        insertItem.setSgCSaStoreId(itemRequst.getSgCSaStoreId());
        insertItem.setSgCSaStoreEcode(sgSaStore.getEcode());
        insertItem.setSgCSaStoreEname(sgSaStore.getEname());
        //所属分货单
        insertItem.setSgBShareAllocationId(objId);
        //设置明细用户信息字段
        StorageUtils.setBModelDefalutData(insertItem, loginUser);
        insertItem.setIsactive(SgConstants.IS_ACTIVE_Y);
        return insertItem;
    }

    /**
     * 参数校验
     *
     * @param requst 入参
     */
    private void checkParam(SgBShareAllocationBillSaveRequst requst) {
        SgStoreUtils.checkR3BModelDefalut(requst);
        AssertUtils.notNull(requst, "入参为空！");
        AssertUtils.notNull(requst.getLoginUser(), "用户为空！");
        List<SgBShareAllocationSaveItemRequst> saveItemRequsts = requst.getSgBShareAllocationSaveItemRequsts();
        Long objId = requst.getObjId();
        AssertUtils.notNull(objId, "主表id为空！");
        // 不可编辑，新增主表页面上传，编辑表里面取
        Long newStoreId;

        //判断新增or保存
        if (objId > -1) {
            SgBShareAllocation sgShareAllocation = allocationMapper.selectById(objId);
            AssertUtils.notNull(sgShareAllocation, "当前记录不存在！");
            Integer status = sgShareAllocation.getStatus();
            if (SgShareConstants.BILL_STATUS_VOID == status) {
                AssertUtils.logAndThrow("当前记录已作废，不允许编辑！");
            }
            if (SgShareConstants.BILL_STATUS_UNSUBMIT != status) {
                AssertUtils.logAndThrow("当前记录已提交，不允许编辑！");
            }
            newStoreId = sgShareAllocation.getSgCShareStoreId();

        } else {
            SgBShareAllocationSaveRequst saveRequst = requst.getSgBShareAllocationSaveRequst();
            newStoreId = saveRequst.getSgCShareStoreId();
            //新增不可以为空
            AssertUtils.notNull(saveRequst, "主表入参为空！");
            AssertUtils.notNull(CollectionUtils.isEmpty(saveItemRequsts), "明细参数为空！");
            AssertUtils.notNull(newStoreId, "聚合仓信息为空");
        }

        if (CollectionUtils.isNotEmpty(saveItemRequsts)) {
            List<Long> saId = saveItemRequsts.stream().map(SgBShareAllocationSaveItemRequst::getSgCSaStoreId).distinct()
                    .collect(Collectors.toList());
            AssertUtils.cannot(CollectionUtils.isEmpty(saId), "明细未关联SA仓");

            List<SgCSaStore> sgSaStores = sgSaStoreMapper.selectBatchIds(saId);
            Map<Long, SgCSaStore> saStoreMap = sgSaStores.stream().collect(Collectors.toMap(SgCSaStore::getId,
                    sgSaStore -> sgSaStore, (v1, v2) -> v1));

            for (SgBShareAllocationSaveItemRequst saveItemRequst : saveItemRequsts) {
                String psSkuEcode = saveItemRequst.getPsCSkuEcode();
                if (!requst.isR3()) {
                    AssertUtils.notNull(psSkuEcode, newStoreId + "[条码编码不能为空！]" + saveItemRequst.getPsCSkuId());
                    AssertUtils.notNull(saveItemRequst.getPsCSkuId(), newStoreId + "[" + psSkuEcode + "]条码ID不能为空！" + saveItemRequst.getPsCSkuId());
//            AssertUtils.notNull(saveItemRequst.getSourceBillItemId(), "[" + psSkuEcode + "]来源单据ID不能为空！");
                    AssertUtils.notNull(saveItemRequst.getPsCProId(), newStoreId + "[" + psSkuEcode + "]商品ID不能为空！" + saveItemRequst.getPsCSkuId());
                    AssertUtils.notNull(saveItemRequst.getPsCProEcode(), newStoreId + "[" + psSkuEcode + "]商品编码不能为空！" + saveItemRequst.getPsCSkuId());
                    AssertUtils.notNull(saveItemRequst.getPsCProEname(), newStoreId + "[" + psSkuEcode + "]商品名称不能为空！" + saveItemRequst.getPsCSkuId());
                    AssertUtils.notNull(saveItemRequst.getPsCSpec1Id(), newStoreId + "[" + psSkuEcode + "]规格1ID不能为空！" + saveItemRequst.getPsCSkuId());
                    AssertUtils.notNull(saveItemRequst.getPsCSpec1Ecode(), newStoreId + "[" + psSkuEcode + "]规格1编码不能为空！" + saveItemRequst.getPsCSkuId());
                    AssertUtils.notNull(saveItemRequst.getPsCSpec1Ename(), newStoreId + "[" + psSkuEcode + "]规格1名称不能为空！" + saveItemRequst.getPsCSkuId());
                    AssertUtils.notNull(saveItemRequst.getPsCSpec2Id(), newStoreId + "[" + psSkuEcode + "]规格2ID不能为空！" + saveItemRequst.getPsCSkuId());
                    AssertUtils.notNull(saveItemRequst.getPsCSpec2Ecode(), newStoreId + "[" + psSkuEcode + "]规格2编码不能为空！" + saveItemRequst.getPsCSkuId());
                    AssertUtils.notNull(saveItemRequst.getPsCSpec2Ename(), newStoreId + "[" + psSkuEcode + "]规格2名称不能为空！" + saveItemRequst.getPsCSkuId());
                    AssertUtils.notNull(saveItemRequst.getSgCSaStoreId(), newStoreId + "[" + psSkuEcode + "]SA仓不能为空！" + saveItemRequst.getPsCSkuId());
                    /*AssertUtils.notNull(saveItemRequst.getPriceList(), "[" + psSkuEcode + "]吊牌价不能为空！");*/
                    saveItemRequst.setPriceList(Optional.ofNullable(saveItemRequst.getPriceList()).orElse(BigDecimal.ZERO));
                }

                if (BigDecimalUtils.nonPositiveInteger(saveItemRequst.getQty())) {
                    AssertUtils.logInfoAndThrow("[" + psSkuEcode + "]数量必须为整数，qty：" + saveItemRequst.getQty(),
                            requst.getLoginUser().getLocale());
                }

                if (saveItemRequst.getId() != null && saveItemRequst.getId() < 0) {
                    SgCSaStore sgSaStore = saStoreMap.get(saveItemRequst.getSgCSaStoreId());
                    AssertUtils.notNull(sgSaStore, "明细关联SA仓未找到！");
                    AssertUtils.cannot(!newStoreId.equals(sgSaStore.getSgCShareStoreId()),
                            "当前SA仓所属于聚合仓和头表聚合仓不同，不允许进行分货！");
                    AssertUtils.cannot(!SgConstants.IS_ACTIVE_Y.equals(sgSaStore.getIsactive()),
                            "不可用的配销仓无法创建分货单:" + sgSaStore.getEcode());
                }
                AssertUtils.notNull(saveItemRequst.getQty(), "[" + psSkuEcode + "]数量不能为空！");
                AssertUtils.cannot(BigDecimal.ZERO.compareTo(saveItemRequst.getQty()) > 0, "["
                        + psSkuEcode + "]数量小于0不允许");
            }
        }
    }

}
