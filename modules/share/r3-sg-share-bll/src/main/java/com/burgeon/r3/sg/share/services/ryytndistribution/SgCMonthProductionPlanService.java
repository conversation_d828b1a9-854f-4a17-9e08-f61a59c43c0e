package com.burgeon.r3.sg.share.services.ryytndistribution;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.rpc.RpcPsService;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.SgMonthProductionPlanTypeEnum;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgBScpDemandSyncConvertConfig;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCDepartmentMonthDemand;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCDepartmentMonthDemandLog;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCMonthProductionPlan;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgCMonthProductionPlanMapper;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 月生产计划
 *
 * <AUTHOR>
 * @since 2023-03-17 13:45
 */
@Slf4j
@Service
public class SgCMonthProductionPlanService {
    @Resource
    private SgCMonthProductionPlanMapper sgCMonthProductionPlanMapper;

    @Resource
    private SgCDepartmentMonthDemandService sgCDepartmentMonthDemandService;
    @Resource
    private SgCDepartmentMonthDemandLogService sgCDepartmentMonthDemandLogService;
    @Resource
    private SgBScpDemandSyncConvertConfigService sgBScpDemandSyncConvertConfigService;

    @Resource
    private RpcPsService rpcPsService;

    /**
     * 生产计划默认仓编码
     */
    @NacosValue(value = "${sg.share.month.demand.production.plan.factoryCode:CB01}", autoRefreshed = true)
    private String factoryCode;

    /**
     * 查询某聚合仓生产计划总量
     *
     * @param skuId 商品ID
     * @param month 月份
     */
    @Deprecated
    public BigDecimal queryProductPlan(Long skuId, String month) {
        List<SgCMonthProductionPlan> productionPlanList = sgCMonthProductionPlanMapper.selectList(new LambdaQueryWrapper<SgCMonthProductionPlan>()
                .eq(SgCMonthProductionPlan::getPlanMonth, month)
                .eq(SgCMonthProductionPlan::getPsCSkuId, skuId));
        if (CollectionUtils.isEmpty(productionPlanList)) {
            log.debug(LogUtil.format("查询聚合仓生产计划列表，商品ID：{},执行日期：{}", "SgCMonthProductionPlanService.queryProductPlan"),
                    skuId, month);
            return BigDecimal.ZERO;
        }

        return productionPlanList.stream().map(SgCMonthProductionPlan::getPlanInQty)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 有计划生产入库的逻辑仓编码
     *
     * @return 逻辑仓编码列表
     */
    public List<String> queryAllFactoryCode() {
        return ListUtils.emptyIfNull(sgCMonthProductionPlanMapper.selectObjs(new QueryWrapper<SgCMonthProductionPlan>()
                        .select("DISTINCT factory_code").lambda()
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)))
                .stream().map(o -> (String) o).collect(Collectors.toList());
    }


    /**
     * 刷新拆包品日生产计划
     *
     * @param versionBi 版本号
     * @param batchNo   报表执行批次号
     */
    @Transactional(rollbackFor = Throwable.class)
    public void refreshPlanData(String versionBi, String batchNo) {
        int removeCount = removeExpiredPlan();
        int createCount = createConvertPlanDataByReport(versionBi, batchNo);

        log.info(LogUtil.format("刷新拆包品月生产计划数据，版本：{},批次号:{},删除行数：{},创建行数：{}",
                "SgCMonthProductionPlanService.refreshPlanData"), versionBi, batchNo, removeCount, createCount);
    }

    /**
     * 根据分货满足日志，计算并创建月生产计划数据用于分货报表
     *
     * @param versionBi 版本
     * @param batchNo   批次号
     * @return 变更行数
     */
    private int createConvertPlanDataByReport(String versionBi, String batchNo) {
        List<SgCDepartmentMonthDemand> allDemandList = sgCDepartmentMonthDemandService.queryConvertDemandByVersionBi(versionBi);
        if (CollectionUtils.isEmpty(allDemandList)) {
            return 0;
        }
        Map<String, List<SgCDepartmentMonthDemand>> demandSkuDateGroup = allDemandList.stream()
                .collect(Collectors.groupingBy(demand -> demand.getPsCSkuId() + SgConstantsIF.MAP_KEY_DIVIDER + demand.getDemandDate()));

        /*注意这里是一次性全部查出来的，后续注意数据量*/
        List<SgCDepartmentMonthDemandLog> demandLogList = sgCDepartmentMonthDemandLogService.queryForConvert(versionBi);
        if (CollectionUtils.isEmpty(demandLogList)) {
            return 0;
        }
        Set<Long> configIds = demandLogList.stream()
                .map(SgCDepartmentMonthDemandLog::getConvertConfigId).collect(Collectors.toSet());

        /*如果配置被作废，也还是要生成生产计划的*/
        List<SgBScpDemandSyncConvertConfig> convertConfigList = sgBScpDemandSyncConvertConfigService.queryListByIds(configIds);
        Map<Long, SgBScpDemandSyncConvertConfig> convertConfigMap = ListUtils.emptyIfNull(convertConfigList).stream()
                .collect(Collectors.toMap(SgBScpDemandSyncConvertConfig::getId, Function.identity()));

        /*计算*/
        List<ProductionPlanLog> planLogList = computePlanLog(demandSkuDateGroup, demandLogList);
        /*转化成月生产计划对象*/
        List<SgCMonthProductionPlan> retList = convertProductionPlan(planLogList, convertConfigMap);
        /*保存（赋默认值）*/
        return saveProductionPlanList(retList, batchNo);
    }

    /**
     * 转化月生产计划对象
     *
     * @param planLogList      需求满足日转化计算结果列表
     * @param convertConfigMap 拆包配置映射
     * @return 月生产计划列表
     */
    private List<SgCMonthProductionPlan> convertProductionPlan(List<ProductionPlanLog> planLogList,
                                                               Map<Long, SgBScpDemandSyncConvertConfig> convertConfigMap) {
        if (CollectionUtils.isEmpty(planLogList)) {
            return Collections.emptyList();
        }

        List<SgCMonthProductionPlan> retList = new ArrayList<>();
        Map<Long, List<ProductionPlanLog>> configIdGroup = planLogList.stream()
                .collect(Collectors.groupingBy(ProductionPlanLog::getConvertConfigId));
        for (Map.Entry<Long, List<ProductionPlanLog>> configIdEntry : configIdGroup.entrySet()) {
            SgBScpDemandSyncConvertConfig convertConfig = convertConfigMap.get(configIdEntry.getKey());
            if (Objects.isNull(convertConfig)) {
                log.warn(LogUtil.format("拆包转化-该配置ID未查询到有效配置信息，配置ID:{}",
                        "SgCMonthProductionPlanService.convertProductionPlan"), configIdEntry.getKey());
                continue;
            }

            for (ProductionPlanLog planLog : configIdEntry.getValue()) {
                Date productionDate = planLog.getCreateDate();
                Date planDate = DateUtils.addDays(productionDate, convertConfig.getProductionConvertDays());
                String planMonth = DateUtil.format(planDate, DatePattern.SIMPLE_MONTH_PATTERN);

                SgCMonthProductionPlan retPlan = new SgCMonthProductionPlan();
                retPlan.setPsCSkuId(convertConfig.getOutPsCSkuId());
                retPlan.setPlanDate(planDate);
                retPlan.setProductionDays(convertConfig.getProductionConvertDays());
                retPlan.setProductionDate(productionDate);
                retPlan.setPlanMonth(planMonth);

                /*【产出品的生产计划量=分货量*日需求占比*生产转换比例*拆包转化系数】结果向上取整*/
                retPlan.setPlanInQty(planLog.getChangeQty()
                        .multiply(convertConfig.getProductionConvertRatio())
                        .multiply(convertConfig.getCoefficient())
                        .setScale(0, RoundingMode.HALF_UP));
                retPlan.setRemark("投入品[" + convertConfig.getInPsCSkuEcode() +
                        "]满足日[" + DateUtil.format(planLog.getCreateDate(), DatePattern.NORM_DATE_PATTERN) +
                        "]需求日[" + DateUtil.format(planLog.getDemandDate(), DatePattern.NORM_DATE_PATTERN) +
                        "]需求日需求占比[" + planLog.getConvertDayPercentage() +
                        "]满足日满足总量[" + planLog.getTotalQty() +
                        "]满足日转化量[" + planLog.getChangeQty() +
                        "]生产转换比例[" + convertConfig.getProductionConvertRatio() +
                        "]拆包转化系数[" + convertConfig.getCoefficient() +
                        "]需求行ID[" + planLog.getSgCDepartmentMonthDemandId() +
                        "]拆组包配置ID[" + planLog.getConvertConfigId() + "]");
                retList.add(retPlan);
            }
        }

        return retList;
    }

    /**
     * 计算比例
     *
     * @param demandSkuDateGroup 需求按日分组
     * @param demandLogList      需求满足记录
     * @return 需求满足日转化计算结果列表
     */
    private List<ProductionPlanLog> computePlanLog(Map<String, List<SgCDepartmentMonthDemand>> demandSkuDateGroup,
                                                   List<SgCDepartmentMonthDemandLog> demandLogList) {
        List<ProductionPlanLog> productionPlanLogList = new ArrayList<>();

        /*逐天-满足日*/
        Map<Date, List<SgCDepartmentMonthDemandLog>> logCreateDateGroup = demandLogList.stream()
                .peek(demandLog -> demandLog.setCreationdate(DateUtil.beginOfDay(demandLog.getCreationdate())))
                .collect(Collectors.groupingBy(SgCDepartmentMonthDemandLog::getCreationdate));
        for (Map.Entry<Date, List<SgCDepartmentMonthDemandLog>> logCreateDateEntry : logCreateDateGroup.entrySet()) {
            /*这个满足日下面的需求日*/
            Map<Date, List<SgCDepartmentMonthDemandLog>> demandDateLogGroup = logCreateDateEntry.getValue()
                    .stream().collect(Collectors.groupingBy(SgCDepartmentMonthDemandLog::getDemandDate));
            for (Map.Entry<Date, List<SgCDepartmentMonthDemandLog>> demandDateLogEntry : demandDateLogGroup.entrySet()) {
                /*逐个品*/
                Map<Long, List<SgCDepartmentMonthDemandLog>> skuLogGroup = demandDateLogEntry.getValue().stream()
                        .collect(Collectors.groupingBy(SgCDepartmentMonthDemandLog::getPsCSkuId));
                for (Map.Entry<Long, List<SgCDepartmentMonthDemandLog>> skuEntry : skuLogGroup.entrySet()) {
                    /*这个满足日里面，满足这一天的需求，这个品，对应的需求列表*/
                    List<SgCDepartmentMonthDemand> demandSkuDateList =
                            demandSkuDateGroup.get(skuEntry.getKey() + SgConstantsIF.MAP_KEY_DIVIDER + demandDateLogEntry.getKey());
                    if (CollectionUtils.isEmpty(demandSkuDateList)) {
                        log.warn(LogUtil.format("【有问题】满足日志里面这一天的这个品没有对应的需求，商品ID：{},需求日：{}",
                                "SgCMonthProductionPlanService.computePlan"), skuEntry.getKey(), demandDateLogEntry.getKey());
                        continue;
                    }
                    /*这个品，这个满足日，这个需求日，所有需求记录*/
                    BigDecimal totalQty = skuEntry.getValue().stream().map(SgCDepartmentMonthDemandLog::getChangeQty)
                            .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    for (SgCDepartmentMonthDemand demand : demandSkuDateList) {
                        /*这个满足日，对这个品，每个需求记录对应的转化量 = 【日需求量占比 * 这个满足日满足的这个品的总满足量】*/
                        productionPlanLogList.add(new ProductionPlanLog(demand.getId(),
                                demand.getConvertConfigId(),
                                logCreateDateEntry.getKey(),
                                demand.getConvertDayPercentage().multiply(totalQty),
                                demandDateLogEntry.getKey(),
                                demand.getConvertDayPercentage(),
                                totalQty));
                    }
                }
            }
        }
        return productionPlanLogList;
    }

    /**
     * 每次执行前把旧数据删除
     */
    private int removeExpiredPlan() {
        SgCMonthProductionPlan param = new SgCMonthProductionPlan();
        param.setIsactive(SgConstants.IS_ACTIVE_N);
        StorageUtils.setBModelDefalutDataByUpdate(param, R3SystemUserResource.getSystemRootUser());

        return sgCMonthProductionPlanMapper.update(param, new QueryWrapper<SgCMonthProductionPlan>().lambda()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCMonthProductionPlan::getType, SgMonthProductionPlanTypeEnum.PACKAGE.getValue()));
    }

    /**
     * 补充SKU信息，并批量保存
     *
     * @param retList 待保存数据
     */
    private int saveProductionPlanList(List<SgCMonthProductionPlan> retList, String batchNo) {
        if (CollectionUtils.isEmpty(retList)) {
            return 0;
        }
        Set<Long> skuIds = retList.stream()
                .map(SgCMonthProductionPlan::getPsCSkuId).collect(Collectors.toSet());
        Map<Long, PsCSku> skuMap = rpcPsService.queryMapBySkuIds(skuIds);

        for (SgCMonthProductionPlan plan : retList) {
            PsCSku psCSku = skuMap.get(plan.getPsCSkuId());
            if (Objects.isNull(psCSku)) {
                log.warn(LogUtil.format("该商品ID未查询到有效商品信息，商品编码:{}",
                        "SgCMonthProductionPlanService.saveProductionPlanList"), plan.getPsCSkuId());
                continue;
            }
            plan.setPsCSkuEcode(psCSku.getEcode());
            plan.setPsCProId(psCSku.getPsCProId());
            plan.setPsCProEcode(psCSku.getPsCProEcode());
            plan.setPsCProEname(psCSku.getPsCProEname());

            plan.setFactoryCode(factoryCode);
            plan.setType(SgMonthProductionPlanTypeEnum.PACKAGE.getValue());
            plan.setReportNo(batchNo);
            plan.setId(ModelUtil.getSequence(SgConstants.SG_C_MONTH_PRODUCTION_PLAN));

            StorageUtils.setBModelDefalutData(plan, R3SystemUserResource.getSystemRootUser());
            plan.setOwnerename(R3SystemUserResource.getSystemRootUser().getEname());
            plan.setModifierename(plan.getOwnerename());
            plan.setIssystem(SgConstants.IS_YES_OR_NO_N);
        }
        StorageBasicUtils.batchInsertList(sgCMonthProductionPlanMapper, retList,
                1000, "批量插入生产计划出错", R3SystemUserResource.getSystemRootUser());
        return retList.size();
    }


    /**
     * 需求满足日转化计算结果
     */
    @Data
    @AllArgsConstructor
    static class ProductionPlanLog {
        /**
         * 需求记录ID
         */
        private Long sgCDepartmentMonthDemandId;
        /**
         * 转化配置ID
         */
        private Long convertConfigId;
        /**
         * 满足日
         */
        private Date createDate;
        /**
         * 满足日转化量
         */
        private BigDecimal changeQty;
        /**
         * 满足日
         */
        private Date demandDate;
        /**
         * 需求日需求占比
         */
        private BigDecimal convertDayPercentage;
        /**
         * 满足日满足总量
         */
        private BigDecimal totalQty;
    }
}
