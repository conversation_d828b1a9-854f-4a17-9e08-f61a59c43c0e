package com.burgeon.r3.sg.share.services.autodistribution;


import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.mapper.SgCShareStoreMapper;
import com.burgeon.r3.sg.basic.model.result.SgCOnesetSettingUpdateQtyResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: chenhao
 * @Date: 2022-02-27 16:59
 * @Description:
 */
@Slf4j
@Component
public class SgCOnesetSettingQueryAdbService {

    @Value("${r3.sg.channel.adb.data.check:data_check}")
    private String dataCheck;
    @Value("${r3.sg.channel.adb.r3.oms.sg:r3_oms_sg}")
    private String rOmsSg;
    @Value("${r3.sg.channel.adb.base:r3_base}")
    private String base;
    @Value("${r3.sg.channel.adb.prodim:'配饰'}")
    private String prodim;

    @Autowired
    private SgCShareStoreMapper sgShareStoreMapper;

    /**
     * 查ADB 一手码更新shul数量
     *
     * @param sgShareStores 聚合仓集合
     * @return SgCOnesetSettingUpdateQtyResult
     */
    @TargetDataSource(name = "adb")
    public ValueHolderV14<Map<Long, List<SgCOnesetSettingUpdateQtyResult>>> queryAdbSettingQty(List<SgCShareStore> sgShareStores) {
        ValueHolderV14<Map<Long, List<SgCOnesetSettingUpdateQtyResult>>> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "查ADB 一手码更新shul数量 成功");

        log.info("SgCOnesetSettingQueryAdbService.queryAdbSettingQty 查ADB 一手码更新shul数量");

        if (CollectionUtils.isEmpty(sgShareStores)) {
            v14.setMessage("入餐为空！！");
            return v14;
        }

        Map<Long, List<SgCOnesetSettingUpdateQtyResult>> map = new HashMap<>();

        sgShareStores.forEach(s -> {
            List<SgCOnesetSettingUpdateQtyResult> resultList = sgShareStoreMapper.queryAdbSettingQty(
                    dataCheck, rOmsSg, base, prodim, s.getId());
            if (CollectionUtils.isNotEmpty(resultList)) {

                log.info("SgCOnesetSettingQueryAdbService.queryAdbSettingQty resultList={}", JSONObject.toJSONString(resultList));

                if (map.containsKey(s.getId())) {
                    List<SgCOnesetSettingUpdateQtyResult> qtyResults = map.get(s.getId());
                    qtyResults.addAll(resultList);
                    map.put(s.getId(), qtyResults);
                } else {
                    map.put(s.getId(), resultList);
                }
            }
        });

        v14.setData(map);
        return v14;
    }
}
