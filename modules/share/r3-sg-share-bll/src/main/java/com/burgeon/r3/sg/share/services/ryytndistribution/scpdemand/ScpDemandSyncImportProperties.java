package com.burgeon.r3.sg.share.services.ryytndistribution.scpdemand;

import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgBScpDemandSyncMapper;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-05 15:37
 */
@Data
public class ScpDemandSyncImportProperties {
    private SgBScpDemandSyncMapper sgBScpDemandSyncMapper;
    private Integer type;
    private String versionBi;
    private List<SgBScpDemandSyncErrorDto> errorDtoList;

    private Integer totalCount;
    private Integer successCount;

    public ScpDemandSyncImportProperties(SgBScpDemandSyncMapper sgBScpDemandSyncMapper,
                                         Integer type, String versionBi,
                                         List<SgBScpDemandSyncErrorDto> errorDtoList) {
        this.sgBScpDemandSyncMapper = sgBScpDemandSyncMapper;
        this.type = type;
        this.versionBi = versionBi;
        this.errorDtoList = errorDtoList;
    }

    public void addError(SgBScpDemandSyncErrorDto errorDto) {
        this.errorDtoList.add(errorDto);
    }
}
