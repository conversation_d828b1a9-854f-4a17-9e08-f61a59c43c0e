package com.burgeon.r3.sg.share.services.out;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.request.SgStoreInfoQueryRequest;
import com.burgeon.r3.sg.basic.model.result.SgStorageBillUpdateResult;
import com.burgeon.r3.sg.basic.services.SgCStoreQueryService;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOut;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOutItem;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOutItemLog;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutItemLogMapper;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutItemMapper;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutMapper;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutItemReleaseRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutItemSaveRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutSaveRequest;
import com.burgeon.r3.sg.share.services.SgShareStorageService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/30 15:40
 */
@Slf4j
@Component
public class SgBShareOutCleanService {

    @Autowired
    private SgBShareOutMapper mapper;
    @Autowired
    private SgBShareOutItemMapper itemMapper;
    @Autowired
    private SgBShareOutItemLogMapper itemLogMapper;


    /**
     * 清空贡献占用单
     *
     * @param shareOut
     * @param releaseItemUpdateList
     * @param releaseItemDeleteList
     * @param releaseItemLogList
     * @param loginUser
     * @return
     */
    public ValueHolderV14 clean(SgBShareOut shareOut,
                                List<SgBShareOutItem> releaseItemUpdateList,
                                List<SgBShareOutItem> releaseItemDeleteList,
                                List<SgBShareOutItemLog> releaseItemLogList,
                                User loginUser) {

        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "共享占用单更新成功");

        log.info("Start SgBShareOutReleaseService.releaseShareOut ReceiveParams:shareOut={};"
                , JSONObject.toJSONString(shareOut));

        String lockKsy = SgConstants.SG_B_SHARE_OUT + ":" + shareOut.getSourceBillNo();
        //锁上
        SgRedisLockUtils.lock(lockKsy);
        try {

            //主表更新
            SgBShareOut update = new SgBShareOut();
            update.setId(shareOut.getId());
            StorageUtils.setBModelDefalutDataByUpdate(update, loginUser);
            //总占用数
            BigDecimal totQtyPreout = shareOut.getTotQtyPreout();
            BigDecimal totQtyOrign = shareOut.getTotQtyOrign();

            Boolean mergeMark = shareOut.getMergeMark();
            if (mergeMark && CollectionUtils.isNotEmpty(releaseItemLogList)) {
                totQtyPreout = totQtyPreout.subtract(releaseItemLogList.stream()
                        .map(SgBShareOutItemLog::getQtyPreout).reduce(BigDecimal.ZERO, BigDecimal::add));
                totQtyOrign = totQtyOrign.subtract(releaseItemLogList.stream()
                        .map(SgBShareOutItemLog::getQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            } else {
                totQtyPreout = totQtyPreout.subtract(releaseItemDeleteList.stream()
                        .map(SgBShareOutItem::getQtyPreout).reduce(BigDecimal.ZERO, BigDecimal::add));
                totQtyOrign = totQtyOrign.subtract(releaseItemDeleteList.stream()
                        .map(SgBShareOutItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add));

            }
            //明细处理 map 后面拿值用
            Map<Long, SgBShareOutItemReleaseRequest> map = new HashMap<>(16);
            update.setTotQtyPreout(totQtyPreout);
            update.setTotQtyOrign(totQtyOrign);
            batchReleaseItem(mergeMark, releaseItemUpdateList, releaseItemDeleteList, releaseItemLogList, shareOut, loginUser);

            if (CollectionUtils.isNotEmpty(releaseItemDeleteList)) {

                update.setTotRowNum(shareOut.getTotRowNum() - releaseItemDeleteList.size());
            }
            if (mapper.updateById(update) <= 0) {
                AssertUtils.logAndThrow("更新异常！更新共享占用单主表数据失败！");
            }
        } catch (Exception e) {
            AssertUtils.logAndThrowException("共享占用单更新异常", e, loginUser.getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        return v14;
    }

    /**
     * 批量处理明细业务
     *
     * @param releaseItemUpdateList 传入条码查出来的明细
     * @param releaseItemDeleteList 传入条码查出来的明细
     * @param shareOut              主表
     * @param loginUser             用户
     */
    private void batchReleaseItem(Boolean mergeMark, List<SgBShareOutItem> releaseItemUpdateList, List<SgBShareOutItem> releaseItemDeleteList,
                                  List<SgBShareOutItemLog> releaseItemLogList, SgBShareOut shareOut, User loginUser) {
        log.info("Start SgBShareOutReleaseService.batchReleaseItem Batch update details;");

        // 更新聚合仓
        List<SgBShareOutItemSaveRequest> ssStorageRequest = new ArrayList<>();
        // 更新配销仓
        List<SgBShareOutItemSaveRequest> saStorageRequest = new ArrayList<>();

        List<SgBShareOutItem> sgShareOutItemAll = new ArrayList<>();
        sgShareOutItemAll.addAll(releaseItemDeleteList);
        sgShareOutItemAll.addAll(releaseItemUpdateList);

        //根据条码进行分组
        Map<Long, List<SgBShareOutItem>> itemsMap =
                sgShareOutItemAll.stream().collect(Collectors.groupingBy(SgBShareOutItem::getPsCSkuId));
        Set<Long> itemIds = new HashSet<>();
        for (Long skuid : itemsMap.keySet()) {

            List<SgBShareOutItem> shareOutItems = itemsMap.get(skuid);
            AssertUtils.cannot(CollectionUtils.isEmpty(shareOutItems), "当前条码为找到记录，请检查！");
            //配销仓集合
            List<Long> saids = new ArrayList<>();
            //item  共享池容器
            SgBShareOutItem item = null;
            //配销仓map
            Map<Long, SgBShareOutItem> saMap = new HashMap<>(16);
            //原单数量
            BigDecimal qty = BigDecimal.ZERO;
            //占用数量
            BigDecimal qtyPreout = BigDecimal.ZERO;
            for (SgBShareOutItem outItem : shareOutItems) {
                itemIds.add(outItem.getId());
                if (!mergeMark) {
                    qty = qty.add(outItem.getQty());
                    qtyPreout = qtyPreout.add(outItem.getQtyPreout());
                } else {
                    //根据主明细获取所有的日志明细
                    List<SgBShareOutItemLog> outItemLogList = releaseItemLogList.stream().filter(shareOutItemLog ->
                            shareOutItemLog.getSgBShareOutId().equals(outItem.getSgBShareOutId()) &&
                                    shareOutItemLog.getPsCSkuId().equals(outItem.getPsCSkuId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(outItemLogList)) {
                        for (SgBShareOutItemLog sgBShareOutItemLog : outItemLogList) {
                            qty = qty.add(sgBShareOutItemLog.getQty());
                            qtyPreout = qtyPreout.add(sgBShareOutItemLog.getQtyPreout());
                        }
                    }

                }
                if (outItem.getSgCSaStoreId() != null) {
                    saids.add(outItem.getSgCSaStoreId());
                    saMap.put(outItem.getSgCSaStoreId(), outItem);
                }
                if (outItem.getSgCSharePoolId() != null) {
                    item = outItem;
                }
            }

            //配销仓为空 查找共享池 item  共享池容器
            if (CollectionUtils.isNotEmpty(saids)) {
                //查找配销仓
                SgStoreInfoQueryRequest sgStoreInfoQueryRequest = new SgStoreInfoQueryRequest();
                sgStoreInfoQueryRequest.setIds(saids);
                SgCStoreQueryService storeQueryService = ApplicationContextHandle.getBean(SgCStoreQueryService.class);
                HashMap<Long, SgCSaStore> saStoreInfoById = storeQueryService.getSaStoreInfoById(sgStoreInfoQueryRequest);
                AssertUtils.cannot(saStoreInfoById.isEmpty(), "未找到对应的配销仓，请检查！");

                //一次性返回所以sa仓 按照优先级升序
                List<SgCSaStore> saStoreList = new ArrayList<>(saStoreInfoById.values());
                //按照优先级排序(降序)
                saStoreList.sort(Comparator.comparing(SgCSaStore::getOrderno).reversed());
                for (SgCSaStore saStore : saStoreList) {
                    // 传入的占用数量大于0，小于0表示无可占用数 跳过
                    if (BigDecimal.ZERO.compareTo(qty) >= 0) {
                        break;
                    }
                    //该配销仓 在共享占用单中单明细 skuid+said
                    SgBShareOutItem outItem = saMap.get(saStore.getId());
                    //占用数  传入qty大于明细的占用数，就取明细全部占用数，小于则有多少占用多少
                    BigDecimal preout;
                    BigDecimal updateQty;
                    BigDecimal outQtyPreout = outItem.getQtyPreout();
                    BigDecimal outQty = outItem.getQty();
                    if (qty.compareTo(outQty) > 0) {
                        updateQty = outQty;
                        qty = qty.subtract(outQty);
                    } else {
                        updateQty = qty;
                        qty = BigDecimal.ZERO;
                    }
                    if (qtyPreout.compareTo(outQtyPreout) > 0) {
                        preout = outQtyPreout;
                        qtyPreout = qtyPreout.subtract(outQtyPreout);
                    } else {
                        preout = qtyPreout;
                        qtyPreout = BigDecimal.ZERO;
                    }

                    saStorageRequest.add(updateItem(outItem, loginUser, updateQty, preout));
                }
            }
            //共享池是否还有占用
            if (!mergeMark && item != null && BigDecimal.ZERO.compareTo(qtyPreout) < 0 && BigDecimal.ZERO.compareTo(qty) < 0) {
                BigDecimal updateQty = qty.compareTo(item.getQty()) > 0 ? item.getQty() : qty;
                BigDecimal updateQtyPreout = qtyPreout.compareTo(item.getQtyPreout()) > 0 ? item.getQtyPreout() : qtyPreout;
                ssStorageRequest.add(updateItem(item, loginUser, updateQty, updateQtyPreout));
            }
        }

        if (CollectionUtils.isNotEmpty(releaseItemDeleteList)) {
            itemIds = releaseItemDeleteList.stream().map(SgBShareOutItem::getId).collect(Collectors.toSet());
            LambdaQueryWrapper<SgBShareOutItem> itemLambdaQueryWrapper = new LambdaQueryWrapper<>();
            itemLambdaQueryWrapper.in(SgBShareOutItem::getId, itemIds);
            itemMapper.delete(itemLambdaQueryWrapper);
        }
        if (CollectionUtils.isNotEmpty(releaseItemLogList)) {
            itemIds = releaseItemLogList.stream().map(SgBShareOutItemLog::getId).collect(Collectors.toSet());
            LambdaQueryWrapper<SgBShareOutItemLog> itemLambdaQueryWrapper = new LambdaQueryWrapper<>();
            itemLambdaQueryWrapper.in(SgBShareOutItemLog::getId, itemIds);
            itemLogMapper.delete(itemLambdaQueryWrapper);
        }
        //更新库存
        updateStorage(ssStorageRequest, saStorageRequest, shareOut, loginUser);
    }

    /**
     * 更新明细并返回封装好的更新库存request
     *
     * @param item      明细
     * @param loginUser 用户
     * @param qty       数量
     * @return 更新库存request
     */
    private SgBShareOutItemSaveRequest updateItem(SgBShareOutItem item, User loginUser, BigDecimal qty, BigDecimal qtyPreout) {
        //更新明细
        SgBShareOutItem updateItem = new SgBShareOutItem();
        updateItem.setId(item.getId());
        StorageUtils.setBModelDefalutDataByUpdate(updateItem, loginUser);
        updateItem.setQtyPreout(item.getQtyPreout().subtract(qtyPreout));
        updateItem.setQty(item.getQty().subtract(qty));
        itemMapper.updateById(updateItem);
        //预备更新库存
        SgBShareOutItemSaveRequest itemSaveRequest = new SgBShareOutItemSaveRequest();
        BeanUtils.copyProperties(item, itemSaveRequest);
        itemSaveRequest.setQty(qty);
        itemSaveRequest.setQtyPreout(qtyPreout);
        return itemSaveRequest;
    }

    /**
     * 更新库存
     *
     * @param ssStorageRequest 更新聚合仓库存集合
     * @param saStorageRequest 更新配销仓库存集合
     * @param shareOut         主表参数
     * @param loginUser        用户
     */
    private void updateStorage
    (List<SgBShareOutItemSaveRequest> ssStorageRequest, List<SgBShareOutItemSaveRequest> saStorageRequest,
     SgBShareOut shareOut, User loginUser) {
        log.info("Start SgBShareOutReleaseService.batchReleaseItem Batch update storage;");

        SgShareStorageService bean = ApplicationContextHandle.getBean(SgShareStorageService.class);
        SgBShareOutSaveRequest shareOutSaveRequest = new SgBShareOutSaveRequest();
        BeanUtils.copyProperties(shareOut, shareOutSaveRequest);
        List<String> redisFtpKey = new ArrayList<>();

        try {
            if (CollectionUtils.isNotEmpty(ssStorageRequest)) {
                log.info("SgBShareOutReleaseService.updateSgShareStorage size={};", ssStorageRequest.size());
                ValueHolderV14<SgStorageBillUpdateResult> valueHolderV14 = bean.updateSsStorage(ssStorageRequest, loginUser,
                        shareOutSaveRequest, SgConstantsIF.VOID, SgConstantsIF.SERVICE_NODE_SHARE_OUT_CLEAN);
                log.info("SgBShareOutReleaseService.updateSgShareStorage ValueHolderV14={};", JSONObject.toJSONString(valueHolderV14));
                redisFtpKey.addAll(valueHolderV14.getData().getRedisBillFtpKeyList());
            }

            if (CollectionUtils.isNotEmpty(saStorageRequest)) {
                log.info("SgBShareOutReleaseService.updateSgSaStoreStorage size={};", saStorageRequest.size());
                ValueHolderV14<SgStorageBillUpdateResult> valueHolderV14 = bean.updateSaStorage(saStorageRequest, loginUser,
                        shareOutSaveRequest, SgConstantsIF.VOID, SgConstantsIF.SERVICE_NODE_SHARE_OUT_CLEAN);
                log.info("SgBShareOutReleaseService.updateSgSaStoreStorage ValueHolderV14={};;", JSONObject.toJSONString(valueHolderV14));
                redisFtpKey.addAll(valueHolderV14.getData().getRedisBillFtpKeyList());
            }

        } catch (Exception e) {
            StorageBasicUtils.rollbackStorage(redisFtpKey, loginUser);
            AssertUtils.logAndThrowException("共享占用单更新(释放)异常：", e);
        }
    }


}
