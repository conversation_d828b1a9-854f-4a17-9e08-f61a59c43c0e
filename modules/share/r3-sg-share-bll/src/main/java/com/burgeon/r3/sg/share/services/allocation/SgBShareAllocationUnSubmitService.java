package com.burgeon.r3.sg.share.services.allocation;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.table.share.allocation.SgBShareAllocation;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.allocation.SgBShareAllocationMapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Auther: chenhao
 * @Date: 2022-02-17 09:54
 * @Description:
 */

@Slf4j
@Component
public class SgBShareAllocationUnSubmitService {

    @Autowired
    private SgBShareAllocationMapper allocationMapper;

    /**
     * 取消审核入口
     *
     * @param session 页面参数
     * @return 返回参数
     */
    ValueHolder unSubmitShareAllocation(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        return R3ParamUtils.convertV14WithResult(unSubmitShareAllocation(request));
    }

    /**
     * 取消审核
     *
     * @param request SgR3BaseRequest
     * @return ValueHolderV14
     */
    private ValueHolderV14 unSubmitShareAllocation(SgR3BaseRequest request) {

        log.info("SgBShareAllocationUnSubmitService.unSubmitShareAllocation request={}", JSONObject.toJSONString(request));

        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "分货单取消成功");
        try {
            Long objId = request.getObjId();
            User loginUser = request.getLoginUser();

            checkParam(objId);

            SgBShareAllocation update = new SgBShareAllocation();
            StorageUtils.setBModelDefalutDataByUpdate(update, loginUser);
            update.setId(objId);
            update.setStatus(SgShareConstants.BILL_STATUS_UNSUBMIT);
            allocationMapper.update(update, new UpdateWrapper<SgBShareAllocation>().lambda()
                    .set(SgBShareAllocation::getStatusId, null)
                    .set(SgBShareAllocation::getStatusTime, null)
                    .set(SgBShareAllocation::getStatusEname, null)
                    .set(SgBShareAllocation::getStatusName, null)
                    .eq(SgBShareAllocation::getId, objId));

        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("分货单取消审核异常：" + e.getMessage());
            return v14;
        }

        log.info("SgBShareAllocationUnSubmitService.unSubmitShareAllocation ValueHolderV14={}", JSONObject.toJSONString(v14));

        return v14;
    }

    /**
     * 参数校验
     *
     * @param objid 主表id
     * @return SgBShareAllocation
     */
    private void checkParam(Long objid) {
        SgBShareAllocation sgShareAllocation = allocationMapper.selectById(objid);
        AssertUtils.notNull(sgShareAllocation, "当前记录已不存在！");
        Integer status = sgShareAllocation.getStatus();
        if (SgShareConstants.BILL_STATUS_VOID == status) {
            AssertUtils.logAndThrow("当前记录已作废，不允许取消审核！");
        }
        if (SgShareConstants.BILL_STATUS_SUBMIT == status) {
            AssertUtils.logAndThrow("当前记录已审核，不允许取消审核！");
        }

    }


}
