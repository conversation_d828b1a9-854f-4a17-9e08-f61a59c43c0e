package com.burgeon.r3.sg.share.filter.transfer;

import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.share.transfer.SgBShareSaBatchTransfer;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareSaBatchTransferItemMapper;
import com.burgeon.r3.sg.share.mapper.transfer.SgBShareSaBatchTransferMapper;
import com.burgeon.r3.sg.share.model.dto.SgBShareSaBatchTransferDto;
import com.burgeon.r3.sg.share.model.dto.SgBShareSaBatchTransferItemDto;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleItemFilter;
import com.jackrain.nea.web.face.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/2 15:41
 */
@Component
public class SgBShareSaBatchTransferVoidFilter extends BaseSingleItemFilter<SgBShareSaBatchTransferDto,
        SgBShareSaBatchTransferItemDto> {

    @Autowired
    private SgBShareSaBatchTransferMapper mapper;
    @Autowired
    private SgBShareSaBatchTransferItemMapper itemMapper;

    @Override
    public String getFilterMsgName() {
        return "配销仓调拨单批量导入作废过滤";
    }

    @Override
    public Class<?> getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgBShareSaBatchTransferDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14();
        SgBShareSaBatchTransfer sgBShareSaBatchTransfer = mapper.selectById(mainObject.getId());
        if (Objects.isNull(sgBShareSaBatchTransfer)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(Resources.getMessage("当前记录已不存在！"));
            return v14;
        }
        if (!SgConstantsIF.SG_B_SHARE_SA_BATCH_TRANSFER_STATUS_01.equals(sgBShareSaBatchTransfer.getBillStatus())) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(Resources.getMessage("当前记录的单据状态，不允许作废！"));
            return v14;
        }

        Date now = new Date();
        mainObject.setBillStatus(SgConstantsIF.SG_B_SHARE_SA_BATCH_TRANSFER_STATUS_04);
//        mainObject.setIsactive(SgConstants.IS_ACTIVE_N);
        mainObject.setDelTime(now);
        mainObject.setDelerId(Long.valueOf(loginUser.getId()));
        mainObject.setDelerName(loginUser.getName());
        mainObject.setDelerEname(loginUser.getEname());
        return null;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgBShareSaBatchTransferDto mainObject, User loginUser) {
        return null;
    }

    @Override
    public ValueHolderV14 execBeforeSubTable(SgBShareSaBatchTransferDto mainObject, List<SgBShareSaBatchTransferItemDto> subObjectList, User loginUser) {
        return null;
    }

    @Override
    public ValueHolderV14 execAfterSubTable(SgBShareSaBatchTransferDto mainObject, List<SgBShareSaBatchTransferItemDto> subObjectList, User loginUser) {
        return null;
    }
}
