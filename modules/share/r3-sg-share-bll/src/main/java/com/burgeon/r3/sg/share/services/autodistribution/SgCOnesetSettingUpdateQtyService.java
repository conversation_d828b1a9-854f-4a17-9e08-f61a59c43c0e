package com.burgeon.r3.sg.share.services.autodistribution;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.SgCShareStoreMapper;
import com.burgeon.r3.sg.basic.model.result.SgCOnesetSettingUpdateQtyResult;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.model.table.share.autodistribution.SgCOnesetSetting;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.api.autodistribution.SgCOnesetSettingUpdateQtyCmd;
import com.burgeon.r3.sg.share.mapper.autodistribution.SgCOnesetSettingMapper;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-02-27 15:58
 * @Description: 更新一手码数量
 */


@Slf4j
@Component
public class SgCOnesetSettingUpdateQtyService {

    @Autowired
    private SgCShareStoreMapper sgShareStoreMapper;

    @Autowired
    private SgCOnesetSettingMapper settingMapper;

    /**
     * 更新一手码数量 定时任务
     *
     * @return ValueHolderV14
     */
    public ValueHolderV14 execute() {

        log.info("SgCOnesetSettingUpdateQtyService execute Update the number of single-hand codes for scheduled task execution");

        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "更新一手码数量 定时任务执行成功！");

        //1。查询【聚合仓档案】中【自动分货】为Y的记录对应的【聚合仓库存】中的记录
        List<SgCShareStore> sgShareStores = sgShareStoreMapper.selectList(new LambdaQueryWrapper<SgCShareStore>()
                .eq(SgCShareStore::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCShareStore::getIsAutoAllocation, SgConstants.IS_AUTO_Y));
        if (CollectionUtils.isEmpty(sgShareStores)) {
            v14.setMessage("【定时任务】更新一手码数量 没有满足条件的聚合仓！");
            return v14;
        }

        Map<Long, SgCShareStore> shareStoreMap = sgShareStores.stream().collect(Collectors.toMap(SgCShareStore::getId, Function.identity()));

        SgCOnesetSettingUpdateQtyCmd refer = (SgCOnesetSettingUpdateQtyCmd) ReferenceUtil.refer(
                ApplicationContextHandle.getApplicationContext(),
                SgCOnesetSettingUpdateQtyCmd.class.getName(), SgConstantsIF.GROUP, SgConstantsIF.VERSION);

        ValueHolderV14<Map<Long, List<SgCOnesetSettingUpdateQtyResult>>> mapValueHolderV14 = refer.queryAdbSettingQty(sgShareStores);

        if (!mapValueHolderV14.isOK()) {
            v14.setData(ResultCode.FAIL);
            v14.setMessage("【定时任务】更新一手码数量 查ADB异常！" + mapValueHolderV14.getMessage());
            return v14;
        }
        Map<Long, List<SgCOnesetSettingUpdateQtyResult>> data = mapValueHolderV14.getData();
        if (MapUtils.isEmpty(data)) {
            v14.setData(ResultCode.SUCCESS);
            v14.setMessage("【定时任务】更新一手码数量 无操作资源！");
            return v14;
        }
//        Map<Long, List<SgCOnesetSettingUpdateQtyResult>> data = setTestData();
        ValueHolderV14 v141 = updateOnesetSetting(data, shareStoreMap);
        if (!v141.isOK()) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("【定时任务】更新一手码数量 失败！" + v141.getMessage());
            return v14;
        }

        return v14;
    }

    //10056164	TMK01	16	L120W081-0058	322753	L120W081-005899	749547	5.0000	0.0000	0.0000	0.0000	Y	37	27			893	系统管理员	root	2021-09-18 19:09:50	893	系统管理员	root
    private Map<Long, List<SgCOnesetSettingUpdateQtyResult>> setTestData() {
        Map<Long, List<SgCOnesetSettingUpdateQtyResult>> map = new HashMap<>(16);

        SgCOnesetSettingUpdateQtyResult qtyResult = new SgCOnesetSettingUpdateQtyResult();
        qtyResult.setId(10056164L);
        qtyResult.setSgCShareStoreEcode("TMK01");
        qtyResult.setSgCShareStoreId(16L);
        qtyResult.setPsCProEcode("L120W081-0058");
        qtyResult.setPsCProId(322753L);
        qtyResult.setPsCSkuEcode("L120W081-005899");
        qtyResult.setPsCSkuId(749547L);
        qtyResult.setInCodeQty(BigDecimal.ONE);

        List<SgCOnesetSettingUpdateQtyResult> list = new ArrayList<>();
        list.add(qtyResult);

        map.put(16L, list);
        return map;
    }

    /**
     * 处理 adb 查出来的数据
     *
     * @param data          adb 查出来的数据
     * @param shareStoreMap 聚合仓信息
     * @return ValueHolderV14
     */
    private ValueHolderV14 updateOnesetSetting(Map<Long, List<SgCOnesetSettingUpdateQtyResult>> data, Map<Long, SgCShareStore> shareStoreMap) {

        log.info("SgCOnesetSettingUpdateQtyService updateOnesetSetting 处理adb查出来的数据");

        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "处理adb查出来的数据 成功！");

        for (Long key : data.keySet()) {
            List<SgCOnesetSettingUpdateQtyResult> qtyResults = data.get(key);

            List<List<SgCOnesetSettingUpdateQtyResult>> pageList = StorageUtils.getPageList(qtyResults, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);

            List<SgCOnesetSetting> insert = new ArrayList<>();
            List<SgCOnesetSetting> update = new ArrayList<>();

            for (List<SgCOnesetSettingUpdateQtyResult> qtyResultList : pageList) {
                List<Long> skuList = qtyResultList.stream().map(SgCOnesetSettingUpdateQtyResult::getPsCSkuId).collect(Collectors.toList());

                List<SgCOnesetSetting> sgOnesetSettings = settingMapper.selectList(new LambdaQueryWrapper<SgCOnesetSetting>()
                        .eq(SgCOnesetSetting::getSgCShareStoreId, key)
                        .in(SgCOnesetSetting::getPsCSkuId, skuList));

                // 聚合仓+条码
                Map<String, SgCOnesetSetting> sgChannelStoreMap = new HashMap<>(16);
                if (CollectionUtils.isNotEmpty(sgOnesetSettings)) {
                    for (SgCOnesetSetting setting : sgOnesetSettings) {
                        sgChannelStoreMap.put(setting.getSgCShareStoreId() + ":" + setting.getPsCSkuId(), setting);
                    }
                }

                for (SgCOnesetSettingUpdateQtyResult qtyResult : qtyResultList) {
                    if (MapUtils.isEmpty(sgChannelStoreMap) || !sgChannelStoreMap.containsKey(qtyResult.getSgCShareStoreId() + ":" + qtyResult.getPsCSkuId())) {
                        SgCOnesetSetting insertSetting = new SgCOnesetSetting();
                        insertSetting.setId(ModelUtil.getSequence(SgConstants.SG_C_ONESET_SETTING));
                        insertSetting.setQty(qtyResult.getInCodeQty());
                        insertSetting.setSgCShareStoreId(qtyResult.getSgCShareStoreId());
                        insertSetting.setPsCSkuId(qtyResult.getPsCSkuId());
                        insertSetting.setPsCSkuEcode(qtyResult.getPsCSkuEcode());
                        SgCShareStore shareStore = shareStoreMap.get(qtyResult.getSgCShareStoreId());
                        if (shareStore != null) {
                            insertSetting.setSgCShareStoreEname(shareStore.getEname());
                            insertSetting.setSgCShareStoreEcode(shareStore.getEcode());
                        }
                        StorageUtils.setBModelDefalutData(insertSetting, SystemUserResource.getRootUser());
                        insert.add(insertSetting);
                    } else {
                        SgCOnesetSetting sgOnesetSetting = sgChannelStoreMap.get(qtyResult.getSgCShareStoreId() + ":" + qtyResult.getPsCSkuId());
                        SgCOnesetSetting updateSetting = new SgCOnesetSetting();
                        updateSetting.setId(sgOnesetSetting.getId());
                        updateSetting.setQty(qtyResult.getInCodeQty());
                        StorageUtils.setBModelDefalutDataByUpdate(updateSetting, SystemUserResource.getRootUser());
                        update.add(updateSetting);
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(update)) {
                for (SgCOnesetSetting updateSet : update) {
                    settingMapper.updateById(updateSet);
                }
            }

            if (CollectionUtils.isNotEmpty(insert)) {
                List<List<SgCOnesetSetting>> insertPageList = StorageUtils.getPageList(insert, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
                for (List<SgCOnesetSetting> insertList : insertPageList) {
                    try {
                        //冗余条码信息
                        setSkuInfoBySkuIdList(insertList);
                        settingMapper.batchInsert(insertList);
                    } catch (Exception e) {
                        log.error("新增一手码设置有异常，当前不保存，跳过下一次！异常信息：{}", e.getMessage());
                    }


                }
            }

        }

        return v14;
    }

    /**
     * 条码信息
     *
     * @param items items
     */
    private void setSkuInfoBySkuIdList(List<SgCOnesetSetting> items) {
        List<Long> skuIds = new ArrayList<>();

        items.forEach(item -> {
            if (item.getPsCSpec1Id() == null || item.getPsCProEcode() == null) {
                skuIds.add(item.getPsCSkuId());
            }
        });


        BasicPsQueryService psQueryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
        HashMap<Long, PsCProSkuResult> skuInfoMap = new HashMap<>(16);
        if (!CollectionUtils.isEmpty(skuIds)) {
            //买一增一
            List<Long> collect = skuIds.stream().distinct().collect(Collectors.toList());
            //分批次去查
            List<List<Long>> pageSkuIdList = StorageUtils.getPageList(collect, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
            for (List<Long> skuids : pageSkuIdList) {
                SkuInfoQueryRequest request = new SkuInfoQueryRequest();
                request.setSkuIdList(skuids);
                HashMap<Long, PsCProSkuResult> skuInfo = psQueryService.getSkuInfo(request);
                if (MapUtils.isNotEmpty(skuInfo)) {
                    skuInfoMap.putAll(skuInfo);
                }
            }
        }

        if (MapUtils.isNotEmpty(skuInfoMap)) {
            items.forEach(sourceModel -> {
                if (skuInfoMap.containsKey(sourceModel.getPsCSkuId())) {
                    PsCProSkuResult data = skuInfoMap.get(sourceModel.getPsCSkuId());
                    if (sourceModel.getPsCSkuId() == null) {
                        sourceModel.setPsCSkuId(data.getId());
                    }

                    if (sourceModel.getPsCSkuEcode() == null) {

                        sourceModel.setPsCSkuEcode(data.getSkuEcode());
                    }
                    if (sourceModel.getPsCProId() == null) {
                        sourceModel.setPsCProId(data.getPsCProId());
                    }

                    if (StringUtils.isEmpty(sourceModel.getPsCProEcode())) {
                        sourceModel.setPsCProEcode(data.getPsCProEcode());
                    }

                    if (StringUtils.isEmpty(sourceModel.getPsCProEname())) {
                        sourceModel.setPsCProEname(data.getPsCProEname());
                    }

                    if (StringUtils.isEmpty(sourceModel.getPsCSpec1Id())) {
                        sourceModel.setPsCSpec1Id(data.getPsCSpec1objId());
                    }

                    if (StringUtils.isEmpty(sourceModel.getPsCSpec1Ecode())) {
                        sourceModel.setPsCSpec1Ecode(data.getClrsEcode());
                    }
                    if (StringUtils.isEmpty(sourceModel.getPsCSpec1Ename())) {
                        sourceModel.setPsCSpec1Ename(data.getClrsEname());
                    }

                    if (StringUtils.isEmpty(sourceModel.getPsCSpec2Id())) {
                        sourceModel.setPsCSpec2Id(data.getPsCSpec2objId());
                    }

                    if (StringUtils.isEmpty(sourceModel.getPsCSpec2Ecode())) {
                        sourceModel.setPsCSpec2Ecode(data.getSizesEcode());
                    }
                    if (StringUtils.isEmpty(sourceModel.getPsCSpec2Ename())) {
                        sourceModel.setPsCSpec2Ename(data.getSizesEname());
                    }
                }
            });
        }
    }

}
