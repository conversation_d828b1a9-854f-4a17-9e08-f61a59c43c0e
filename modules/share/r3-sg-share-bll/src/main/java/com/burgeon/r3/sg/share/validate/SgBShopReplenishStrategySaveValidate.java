package com.burgeon.r3.sg.share.validate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.share.shop.SgBShopReplenishStrategy;
import com.burgeon.r3.sg.core.model.table.share.shop.SgBShopReplenishStrategyItem;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.shop.SgBShopReplenishStrategyItemMapper;
import com.burgeon.r3.sg.share.mapper.shop.SgBShopReplenishStrategyMapper;
import com.burgeon.r3.sg.share.model.dto.SgBShopRelenishStrategyDto;
import com.burgeon.r3.sg.share.model.dto.SgBShopReplenishStrategyItemDto;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleItemValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 官网小程序库存店铺自动补货策略保存
 * @date 2022/2/17 14:26
 */
@Slf4j
@Component
public class SgBShopReplenishStrategySaveValidate extends BaseSingleItemValidator<SgBShopRelenishStrategyDto, SgBShopReplenishStrategyItemDto> {

    @Autowired
    private SgBShopReplenishStrategyMapper strategyMapper;

    @Autowired
    private SgBShopReplenishStrategyItemMapper strategyItemMapper;


    @Autowired
    private SgCSaStoreMapper sgCSaStoreMapper;


    @Override
    public String getValidatorMsgName() {
        return "小程序官网店铺自动补货策略";
    }

    @Override
    public Class<?> getValidatorClass() {
        return SgBShopReplenishStrategySaveValidate.class;
    }


    @Override
    public ValueHolderV14 validateSubTable(SgBShopRelenishStrategyDto mainObject, List<SgBShopReplenishStrategyItemDto> subObjectList, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("校验通过"));

        if (CollectionUtils.isEmpty(subObjectList)) {
            return v14;
        }

        // 主要用于新增是否存在【配销仓id】的多条记录
        HashMap<String, Object> strategyItemMap = new HashMap<>();

        // 优先级重复判断
        HashMap<String, Object> priorityMap = new HashMap<>();
        List<SgBShopReplenishStrategyItem> itemList = strategyItemMapper.selectList(new QueryWrapper<SgBShopReplenishStrategyItem>()
                .select("DISTINCT SG_C_SA_STORE_ID,PRIORITY").lambda()
                .eq(SgBShopReplenishStrategyItem::getSgBShopAutoAllocationStrategyId, mainObject.getId())
                .eq(SgBShopReplenishStrategyItem::getIsactive, "Y"));
        Map<Long, Integer> sgCSaStoreIdPriorityMap = CollectionUtils.isEmpty(itemList) ? null :
                itemList.stream().collect(Collectors.toMap(SgBShopReplenishStrategyItem::getSgCSaStoreId, SgBShopReplenishStrategyItem::getPriority));
        if (sgCSaStoreIdPriorityMap == null) {
            sgCSaStoreIdPriorityMap = new HashMap<>();
        }
        // 自动补货策略明细表校验

        for (SgBShopReplenishStrategyItemDto strategyItemDTO : subObjectList) {

            if (Objects.nonNull(strategyItemDTO.getId()) && strategyItemDTO.getId() > 0) {
                SgBShopReplenishStrategyItem oldItemObj = strategyItemMapper.selectById(strategyItemDTO.getId());
                if (Objects.isNull(oldItemObj)) {
                    v14.setCode(com.jackrain.nea.constants.ResultCode.FAIL);
                    v14.setMessage(Resources.getMessage("当前记录已不存在！"));
                    return v14;
                }
                if (Objects.isNull(strategyItemDTO.getSgCSaStoreId())) {
                    strategyItemDTO.setSgCSaStoreId(oldItemObj.getSgCSaStoreId());
                }
            } else {
                // 新增明细
                Integer priority = strategyItemDTO.getPriority();
                if (priority == null || priority < 0) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(Resources.getMessage("优先级不允许为负数或为空值", loginUser.getLocale()));
                    return v14;
                }

                SgCSaStore saStore = CommonCacheValUtils.getSaStore(strategyItemDTO.getSgCSaStoreId());
                if (Objects.isNull(saStore)) {
                    saStore = sgCSaStoreMapper.selectById(strategyItemDTO.getSgCSaStoreId());
                }
                if (saStore == null) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(Resources.getMessage("配销仓不存在", loginUser.getLocale()));
                    return v14;
                }

                // 配销仓不容许重复
                if (strategyItemMap.containsKey(strategyItemDTO.getSgCSaStoreId().toString())) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(Resources.getMessage("配销仓不允许重复！", loginUser.getLocale()));
                    return v14;
                } else {
                    strategyItemMap.put(strategyItemDTO.getSgCSaStoreId().toString(), strategyItemDTO.getSgCSaStoreId().toString());
                }
            }

            // 优先级不容许重复
            if (Objects.nonNull(strategyItemDTO.getPriority())) {
                sgCSaStoreIdPriorityMap.put(strategyItemDTO.getSgCSaStoreId(), strategyItemDTO.getPriority());
            }

        }

        if (MapUtils.isNotEmpty(sgCSaStoreIdPriorityMap)) {
            for (Map.Entry entry : sgCSaStoreIdPriorityMap.entrySet()) {
                if (priorityMap.containsKey(entry.getValue().toString())) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(Resources.getMessage("优先级不允许重复！", loginUser.getLocale()));
                    return v14;
                } else {
                    priorityMap.put(entry.getValue().toString(), entry.getValue().toString());
                }
            }

        }

        return v14;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgBShopRelenishStrategyDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("保存成功"));

        Date endTime = mainObject.getEndTime();
        Date startTime = mainObject.getStartTime();

        if (mainObject.getId() < 0) {
            if (Objects.isNull(startTime)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("开始时间不能为空", loginUser.getLocale()));
                return v14;
            }

            if (Objects.isNull(endTime)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("结束时间不能为空", loginUser.getLocale()));
                return v14;
            }

            if (!startTime.before(endTime)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("开始时间不能大于结束时间", loginUser.getLocale()));
                return v14;
            }

            CpShop shopInfo = CommonCacheValUtils.getShopInfo(mainObject.getCpCShopId());
            if (shopInfo == null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("平台店铺信息有误！", loginUser.getLocale()));
                return v14;
            }

            SgBShopReplenishStrategy shopStrategy = strategyMapper.selectOne(new LambdaQueryWrapper<SgBShopReplenishStrategy>()
                    .eq(SgBShopReplenishStrategy::getCpCShopId, shopInfo.getCpCShopId())
                    .ne(SgBShopReplenishStrategy::getStatus, SgShareConstants.SHOP_REPLENISH_STRATEGY_STATUS_CLOSE)
                    .ne(SgBShopReplenishStrategy::getStatus, SgShareConstants.SHOP_REPLENISH_STRATEGY_STATUS_VOID)
                    .ne(SgBShopReplenishStrategy::getIsactive, SgShareConstants.IS_PROCESSED_N));
            if (shopStrategy != null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("已存在相同的店铺自动补货店铺！", loginUser.getLocale()));
                return v14;
            }

            Integer miniReplenishQty = mainObject.getMiniReplenishQty();
            if (miniReplenishQty == null || miniReplenishQty <= 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("库存最低补货量不允许小于等于0或为空值", loginUser.getLocale()));
                return v14;
            }

            Integer miniStockQty = mainObject.getMiniStockQty();
            if (miniStockQty == null || miniStockQty <= 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("保底库存不允许小于等于0或为空值", loginUser.getLocale()));
                return v14;
            }

            Integer saStoreMiniStockQty = mainObject.getSgCSaStoreMiniStockQty();
            if (saStoreMiniStockQty == null || saStoreMiniStockQty < 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("配销仓保底不允许为负数或为空值", loginUser.getLocale()));
                return v14;
            }

            Date now = new Date();
            if (endTime.before(now)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("当前时间已经超过结束时间，不允许新增该记录！", loginUser.getLocale()));
                return v14;
            }
        } else {
            //更新
            SgBShopReplenishStrategy replenishStrategy = strategyMapper.selectById(mainObject.getId());
            // v14.setCode(ResultCode.FAIL);

            // 修改后的开始时间
            Date modifyStartTime = null;
            // 修改后的结束时间
            Date modifyEndTime = null;

            //当前时间
            Date now = new Date();

            // 若有修改则取页面传参的时间，无修改则取原表的时间
            modifyStartTime = (mainObject.getStartTime() == null) ? replenishStrategy.getStartTime() : mainObject.getStartTime();

            modifyEndTime = (mainObject.getEndTime() == null) ? replenishStrategy.getEndTime() : mainObject.getEndTime();

            if (!modifyStartTime.before(modifyEndTime)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("开始时间不能大于结束时间", loginUser.getLocale()));
                return v14;
            }

            if (modifyEndTime.before(now)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("当前修改的结束时间已过，不允许保存！", loginUser.getLocale()));
                return v14;
            }
            if (Objects.isNull(replenishStrategy)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("当前记录不存在", loginUser.getLocale()));
                return v14;
            }

            Integer miniReplenishQty = mainObject.getMiniReplenishQty();
            if (Objects.nonNull(miniReplenishQty) && miniReplenishQty <= 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("库存最低补货量不允许小于等于0", loginUser.getLocale()));
                return v14;
            }

            Integer miniStockQty = mainObject.getMiniStockQty();
            if (Objects.nonNull(miniStockQty) && miniStockQty <= 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("保底库存不允许小于等于0", loginUser.getLocale()));
                return v14;
            }

            Integer saStoreMiniStockQty = mainObject.getSgCSaStoreMiniStockQty();
            if (Objects.nonNull(saStoreMiniStockQty) && saStoreMiniStockQty < 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("配销仓保底不允许为负数", loginUser.getLocale()));
                return v14;
            }

            if (SgShareConstants.SHOP_REPLENISH_STRATEGY_STATUS_SUB == replenishStrategy.getStatus()) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("当前记录已审核，不允许编辑！"));
                return v14;
            }

            if (SgShareConstants.SHOP_REPLENISH_STRATEGY_STATUS_CLOSE == replenishStrategy.getStatus()) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("当前记录已结案，不允许编辑！"));
                return v14;
            }
        }
        return v14;
    }

}