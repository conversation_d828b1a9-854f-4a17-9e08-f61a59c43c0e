package com.burgeon.r3.sg.share.services.ryytndistribution;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.rpc.RpcCpUserService;
import com.burgeon.r3.sg.basic.utils.DingTalkTokenEnum;
import com.burgeon.r3.sg.basic.utils.DingTalkUtil;
import com.burgeon.r3.sg.basic.utils.EnvEnum;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgCStoreWarnProductionMsgDTO;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgCStoreWarnProduction;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOtherOutResult;
import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgCStoreWarnProductionMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOtherOutResultMapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpUsers;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 库存-拆组包监控告警配置
 *
 * <AUTHOR>
 * @since 2024-10-11 15:32
 */
@Slf4j
@Service
public class SgCStoreWarnProductionService {
    /**
     * 日志OBJ
     */
    private static final String LOG_OBJ = "SgCStoreWarnProductionService.";

    @Resource
    private SgCStoreWarnProductionMapper sgCStoreWarnProductionMapper;

    @Resource
    private SgBStoOtherOutResultMapper sgBStoOtherOutResultMapper;

    @Resource
    private RpcCpUserService rpcCpUserService;


    /**
     * 执行-拆组包监控告警
     *
     * @return 执行结果
     */
    public ValueHolderV14<Boolean> executeMonitor() {
        List<SgCStoreWarnProduction> warnList = sgCStoreWarnProductionMapper.selectList(new QueryWrapper<SgCStoreWarnProduction>().lambda()
                .eq(SgCStoreWarnProduction::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isEmpty(warnList)) {
            return new ValueHolderV14<>(Boolean.TRUE, ResultCode.SUCCESS, "拆组包监控告警配置为空，无需监控");
        }

        /*核心，查询未完结单据*/
        List<SgBStoOtherOutResult> outResultList = queryBillList(warnList);
        if (CollectionUtils.isEmpty(outResultList)) {
            return new ValueHolderV14<>(Boolean.TRUE, ResultCode.SUCCESS, "无未完结单据，无需告警");
        }
        Map<Long, List<SgBStoOtherOutResult>> warnBillMap = outResultList.stream()
                .collect(Collectors.groupingBy(SgBStoOtherOutResult::getCpCStoreId));

        List<SgCStoreWarnProductionMsgDTO> msgDtoList = new ArrayList<>();
        for (SgCStoreWarnProduction warn : warnList) {
            List<SgBStoOtherOutResult> billList = warnBillMap.get(warn.getCpCStoreId());
            if (CollectionUtils.isEmpty(billList)) {
                continue;
            }

            msgDtoList.addAll(billList.stream()
                    .map(bill -> SgCStoreWarnProductionMsgDTO.builder()
                            .billId(bill.getId())
                            .cpCStoreId(bill.getCpCStoreId())
                            .cpCStoreEcode(bill.getCpCStoreEcode())
                            .cpCStoreEname(bill.getCpCStoreEname())
                            .billNo(bill.getBillNo())
                            .status(bill.getStatus())
                            .createDate(bill.getCreationdate())
                            .creatUserId(bill.getOwnerid())
                            .warnUserId(warn.getWarnUserId()).build())
                    .collect(Collectors.toList()));
        }

        /*创建人也要被告警到*/
        Set<String> warnDataList = msgDtoList.stream()
                .map(warn -> warn.getBillId() + "_" + warn.getWarnUserId())
                .collect(Collectors.toSet());
        List<SgCStoreWarnProductionMsgDTO> ownerList = outResultList.stream()
                .filter(bill -> !warnDataList.contains(bill.getId() + "_" + bill.getOwnerid()))
                .map(bill -> SgCStoreWarnProductionMsgDTO.builder()
                        .billId(bill.getId())
                        .cpCStoreId(bill.getCpCStoreId())
                        .cpCStoreEcode(bill.getCpCStoreEcode())
                        .cpCStoreEname(bill.getCpCStoreEname())
                        .billNo(bill.getBillNo())
                        .status(bill.getStatus())
                        .createDate(bill.getCreationdate())
                        .creatUserId(bill.getOwnerid())
                        .warnUserId(bill.getOwnerid()).build())
                .collect(Collectors.toList());
        msgDtoList.addAll(ownerList);

        /*发送消息*/
        sendDingTalkMsg(msgDtoList);
        return new ValueHolderV14<>(null, ResultCode.SUCCESS, "执行成功");
    }

    /**
     * 查询-未完结单据
     */
    private List<SgBStoOtherOutResult> queryBillList(List<SgCStoreWarnProduction> warnList) {
        List<Long> storeIds = warnList.stream()
                .map(SgCStoreWarnProduction::getCpCStoreId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeIds)) {
            log.info(LogUtil.format("拆组包监控告警配置配置异常，仓信息为空，配置信息数据:{}",
                    LOG_OBJ + "queryBillMap"), JSON.toJSONString(warnList));
            return Collections.emptyList();
        }

        List<SgBStoOtherOutResult> outResultList = sgBStoOtherOutResultMapper.queryProductionBill2Monitor(storeIds);
        if (CollectionUtils.isEmpty(outResultList)) {
            log.info(LogUtil.format("无未完结的生产流程拆组包单据:{}", LOG_OBJ + "queryBillMap"));
            return Collections.emptyList();
        }
        return outResultList;
    }


    /**
     * 发送告警消息
     * </br>
     * 群消息限制了每分钟的发送条数，后续需要考虑优化一下
     *
     * @param msgDtoList 消息对象列表
     */
    private void sendDingTalkMsg(List<SgCStoreWarnProductionMsgDTO> msgDtoList) {
        if (CollectionUtils.isEmpty(msgDtoList)) {
            return;
        }

        /*补充手机号*/
        Map<Long, CpUsers> idUserMap = fillUserMobile(msgDtoList);
        /*补充创建人名称，创建人也会被告警，所以idUserMap中一定有创建人信息*/
        fillCreateUserName(msgDtoList, idUserMap);

        Map<String, List<SgCStoreWarnProductionMsgDTO>> mobileGroup = msgDtoList.stream()
                .collect(Collectors.groupingBy(SgCStoreWarnProductionMsgDTO::getMobile));
        for (Map.Entry<String, List<SgCStoreWarnProductionMsgDTO>> entry : mobileGroup.entrySet()) {
            entry.getValue().sort(Comparator.comparing(SgCStoreWarnProductionMsgDTO::getCpCStoreEcode));

            StringBuilder sb = new StringBuilder(EnvEnum.getEnv().name() + ":\n");
            int idx = 1;
            for (SgCStoreWarnProductionMsgDTO msgDTO : entry.getValue()) {
                sb.append(idx++).append(". ").append(msgDTO.genMsg()).append("\n");
            }
            sb.append("@").append(entry.getKey());

            log.debug(LogUtil.format("发送钉钉消息：{}", LOG_OBJ + "sendDingTalkMsg"), sb);

            DingTalkUtil.sendTextMsg(DingTalkTokenEnum.STORE_WARN_PRODUCTION, sb.toString(), Collections.singletonList(entry.getKey()));
        }

        log.info(LogUtil.format("告警消息发送完毕，告警对象列表：{}",
                LOG_OBJ + "sendDingTalkMsg"), JSON.toJSONString(mobileGroup));
    }

    /**
     * 补充创建人名称
     *
     * @param msgDtoList 告警对象列表
     * @param idUserMap  用户ID->用户信息
     */
    private void fillCreateUserName(List<SgCStoreWarnProductionMsgDTO> msgDtoList, Map<Long, CpUsers> idUserMap) {
        for (SgCStoreWarnProductionMsgDTO dto : msgDtoList) {
            CpUsers user = idUserMap.get(dto.getCreatUserId());
            dto.setCreatUserName(Objects.isNull(user) ? "用户不存在" :
                    StringUtils.isEmpty(user.getEname()) ? "用户名为空" : user.getEname());
        }
    }

    /**
     * 获取用户信息
     *
     * @param msgDtoList 告警对象列表
     * @return 用户信息
     */
    private Map<Long, CpUsers> fillUserMobile(List<SgCStoreWarnProductionMsgDTO> msgDtoList) {
        List<Long> userIds = msgDtoList.stream()
                .map(SgCStoreWarnProductionMsgDTO::getWarnUserId).distinct().collect(Collectors.toList());
        Map<Long, CpUsers> idUserMap = rpcCpUserService.queryUserInfoByIds(userIds);
        log.warn(LogUtil.format("拆组包监控告警配置-补充告警手机号，用户ID列表：{},获取用户信息:{}",
                LOG_OBJ + "fillUserMobile"), userIds, JSON.toJSONString(idUserMap));

        /*如果手机号不存在则无法告警，需要提示出来*/
        for (SgCStoreWarnProductionMsgDTO dto : msgDtoList) {
            CpUsers user = idUserMap.getOrDefault(dto.getWarnUserId(), new CpUsers());
            dto.setMobile(Objects.isNull(user) ? "用户不存在" :
                    StringUtils.isEmpty(user.getMobil()) ? user.getEname() + "-手机号未设置" : user.getMobil());
        }
        
        return idUserMap;
    }

}
