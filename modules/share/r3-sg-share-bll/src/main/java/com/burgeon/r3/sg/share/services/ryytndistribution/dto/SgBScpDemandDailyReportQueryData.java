package com.burgeon.r3.sg.share.services.ryytndistribution.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * 报表查询临时对象
 *
 * <AUTHOR>
 * @since 2024-07-16 15:19
 */
@Data
public class SgBScpDemandDailyReportQueryData {
    /**
     * 实际入库量：生产入库、库存调整、采购入库
     */
    private Map<String, Map<Date, BigDecimal>> actualQtyMapBySku;
    /**
     * 计划入库量：月生产计划
     */
    private Map<String, Map<Date, BigDecimal>> planQtyMapBySku;
    /**
     * sku编码与ID映射「这里有一个前提，所有出现过的品都是在月生产计划中有的」
     */
    private Map<String, Long> skuEcodeIdMap;
}
