package com.burgeon.r3.sg.share.services.allocation;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.common.R3ParamConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnItemSingleSubmitRequest;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnItemSubmitRequest;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @description: 分货退货单审核
 * @author: hwy
 * @time: 2021/5/24 13:11
 */

@Component
@Slf4j
public class SgBShareAllocationReturnSubmitService {


    @Autowired
    private SgBShareAllocationReturnSingleSubmitService singleSubmitService;

    /**
     * @param session:
     * @Description: R3元数据审核
     * @Author: hwy
     * @Date: 2021/5/24 17:11
     * @return: com.jackrain.nea.util.ValueHolder
     **/
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder submitByR3(QuerySession session) {
        ValueHolder valueHolder = new ValueHolder();
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        if (log.isDebugEnabled()) {
            log.debug("Start SgBShareAllocationReturnSubmitService.submitByR3 param:{}", JSONObject.toJSONString(request));

        }
        SgBShareAllocationReturnItemSingleSubmitRequest singleSubmitRequest = new SgBShareAllocationReturnItemSingleSubmitRequest();
        singleSubmitRequest.setId(request.getObjId());
        singleSubmitRequest.setLoginUser(request.getLoginUser());
        ValueHolderV14 valueHolderV14 = singleSubmitService.singelSubmitWhitTrans(singleSubmitRequest);
        valueHolder.put(R3ParamConstants.CODE, valueHolderV14.getCode());
        valueHolder.put(R3ParamConstants.MESSAGE, valueHolderV14.getMessage());
        return valueHolder;
    }


    /**
     * @param request:
     * @Description: 分货退货单批量审核
     * @Author: hwy
     * @Date: 2021/5/24 16:54
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14
     **/
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 submit(SgBShareAllocationReturnItemSubmitRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBShareAllocationReturnSubmitService.submit param:{}", JSONObject.toJSONString(request));
        }
        ValueHolderV14 valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "审核成功！");
        checkParam(request, valueHolderV14);
        if (valueHolderV14.getCode() == ResultCode.FAIL) {
            log.warn("SgBShareAllocationReturnSubmitService.submit 请求参数未通过校验:{}", valueHolderV14.getMessage());
            return valueHolderV14;
        }
        List<Long> ids = request.getIds();
        StringBuffer sb = new StringBuffer();
        for (Long id : ids) {
            SgBShareAllocationReturnItemSingleSubmitRequest singleSubmitRequest = new SgBShareAllocationReturnItemSingleSubmitRequest();
            singleSubmitRequest.setId(id);
            singleSubmitRequest.setLoginUser(request.getLoginUser());
            ValueHolderV14 result = singleSubmitService.singelSubmitWhitNewTrans(singleSubmitRequest);
            if (result.getCode() == ResultCode.FAIL) {
                sb.append(" id:" + id + "审核异常:" + result.getMessage());
                continue;
            }
        }
        if (sb.length() > 0) {
            valueHolderV14.setMessage(sb.toString());
        }
        return valueHolderV14;
    }

    /**
     * @param request:
     * @param valueHolderV14:
     * @Description: 入参校验
     * @Author: hwy
     * @Date: 2021/5/24 16:55
     * @return: void
     **/
    private void checkParam(SgBShareAllocationReturnItemSubmitRequest request, ValueHolderV14 valueHolderV14) {
        if (request == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("请求参数不能为空");
            return;
        }
        if (CollectionUtils.isEmpty(request.getIds())) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("请求审核的单据id为空");
            return;
        }
    }
}