package com.burgeon.r3.sg.share.services.distribution;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.share.distribution.SgBShareDistribution;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.distribution.SgBShareDistributionItemMapper;
import com.burgeon.r3.sg.share.mapper.distribution.SgBShareDistributionMapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/19 11:21
 */
@Slf4j
@Component
public class SgBShareDistributionVoidService {

    @Autowired
    SgBShareDistributionMapper mapper;
    @Autowired
    SgBShareDistributionItemMapper itemMapper;

    /**
     * 配货单作废（前端）
     */
    ValueHolder voidDistribution(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        SgBShareDistributionVoidService service = ApplicationContextHandle.getBean(SgBShareDistributionVoidService.class);
        return R3ParamUtils.convertV14WithResult(service.voidDistribution(request));
    }

    /**
     * 配货单作废
     */
    public ValueHolderV14<SgR3BaseResult> voidDistribution(SgR3BaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBShareDistributionVoidService.voidDistribution.ReceiveParams:request{}"
                    , JSONObject.toJSONString(request));
        }
        String lockKsy = SgConstants.SG_B_SHARE_DISTRIBUTION + ":" + request.getObjId();
        checkParams(request);
        SgRedisLockUtils.lock(lockKsy);
        try {

            SgBShareDistribution update = new SgBShareDistribution();
            StorageUtils.setBModelDefalutDataByUpdate(update, request.getLoginUser());
            update.setStatus(SgShareConstants.BILL_STATUS_VOID);
            update.setIsactive(SgConstants.IS_ACTIVE_N);
            User user = request.getLoginUser();
            // 添加作废人相关信息
            update.setDelerId(user.getId().longValue());
            update.setDelerName(user.getName());
            update.setDelerEname(user.getEname());
            update.setDelTime(new Date());
            mapper.update(update, new QueryWrapper<SgBShareDistribution>().lambda().eq(SgBShareDistribution::getId, request.getObjId()));
        } catch (Exception e) {
            AssertUtils.logAndThrowException("配合单作废异常", e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "作废成功!");
    }

    /**
     * 操作前check验证
     */
    public SgBShareDistribution checkParams(SgR3BaseRequest request) {
        SgBShareDistribution distribution = mapper.selectById(request.getObjId());
        if (distribution == null) {
            AssertUtils.logAndThrow("当前记录已不存在！", request.getLoginUser().getLocale());
        }
        if (SgConstants.IS_ACTIVE_N.equals(distribution.getIsactive())) {
            AssertUtils.logAndThrow("当前记录已作废,不允许重复作废！", request.getLoginUser().getLocale());

        } else if (SgShareConstants.BILL_STATUS_UNSUBMIT != distribution.getStatus()) {
            AssertUtils.logAndThrow("当前单据状态不允许作废！", request.getLoginUser().getLocale());
        }
        return distribution;
    }
}
