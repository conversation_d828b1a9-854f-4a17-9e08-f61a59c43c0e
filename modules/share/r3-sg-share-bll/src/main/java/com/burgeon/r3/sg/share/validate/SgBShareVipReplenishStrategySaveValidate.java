package com.burgeon.r3.sg.share.validate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.basic.mapper.SgCShareStoreMapper;
import com.burgeon.r3.sg.core.common.SgCoreUtilsConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.model.table.share.strategy.SgBShareVipReplenishStrategy;
import com.burgeon.r3.sg.core.model.table.share.strategy.SgBShareVipReplenishStrategyItem;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.strategy.SgBShareVipReplenishStrategyItemMapper;
import com.burgeon.r3.sg.share.mapper.strategy.SgBShareVipReplenishStrategyMapper;
import com.burgeon.r3.sg.share.model.dto.SgBShareVipReplenishStrategyDto;
import com.burgeon.r3.sg.share.model.dto.SgBShareVipReplenishStrategyItemDto;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleItemValidator;
import com.jackrain.nea.web.face.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description 唯品会商品补货策略保存
 * <AUTHOR>
 * @Date 2021/11/5 14:41
 * @Version 1.0
 **/
@Component
public class SgBShareVipReplenishStrategySaveValidate extends BaseSingleItemValidator<SgBShareVipReplenishStrategyDto, SgBShareVipReplenishStrategyItemDto> {

    @Autowired
    private SgBShareVipReplenishStrategyMapper vipReplenishStrategyMapper;
    @Autowired
    private SgBShareVipReplenishStrategyItemMapper vipReplenishStrategyItemMapper;
    @Autowired
    private SgCSaStoreMapper sgCSaStoreMapper;
    @Autowired
    private SgCShareStoreMapper sgCShareStoreMapper;

    @Override
    public String getValidatorMsgName() {
        return "唯品会商品补货策略保存";
    }

    @Override
    public Class<?> getValidatorClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 validateMainTable(SgBShareVipReplenishStrategyDto mainObject, User loginUser) {


        Date beginTime = mainObject.getBeginTime();
        Date endTime = mainObject.getEndTime();

        if (mainObject.getId() > 0L) {
            SgBShareVipReplenishStrategy sgBShareVipReplenishStrategyOld = vipReplenishStrategyMapper.selectById(mainObject.getId());

            if (sgBShareVipReplenishStrategyOld.getStatus() != SgShareConstants.VIP_REPLENISH_STRATEGY_STATUS_UN_SUB) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("单据状态不为未审核，不允许保存！", loginUser.getLocale()));
            }

            if (beginTime != null && endTime == null) {
                if (beginTime.after(sgBShareVipReplenishStrategyOld.getEndTime())) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("开始时间大于结束时间，不允许保存！", loginUser.getLocale()));
                }
            } else if (beginTime == null && endTime != null) {
                if (endTime.before(sgBShareVipReplenishStrategyOld.getBeginTime())) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("结束时间小于开始时间，不允许保存！", loginUser.getLocale()));
                }
            } else if (beginTime != null && endTime != null) {
                if (beginTime.after(endTime)) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("结束时间小于开始时间，不允许保存！", loginUser.getLocale()));
                }
            }
        } else {
            if (mainObject.getCpCShopId() == null) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("店铺不可以为空", loginUser.getLocale()));
            }
            if (beginTime == null || endTime == null) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("开始时间或结束时间不可以为空", loginUser.getLocale()));
            }
            if (beginTime.after(endTime)) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("开始时间大于结束时间，不允许保存！", loginUser.getLocale()));
            }
            Integer vipReplenishStrategyCount = vipReplenishStrategyMapper.selectCountByBeginAndEndTime(mainObject.getCpCShopId(),
                    SgShareConstants.VIP_REPLENISH_STRATEGY_STATUS_SUB, beginTime, endTime);
            if (vipReplenishStrategyCount > 0) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("当前店铺已存在有效记录，不允许保存！", loginUser.getLocale()));
            }
        }


        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过", loginUser.getLocale()));
    }

    @Override
    public ValueHolderV14 validateSubTable(SgBShareVipReplenishStrategyDto mainObject, List<SgBShareVipReplenishStrategyItemDto> subObjectList, User loginUser) {
        Long mainObjectId = mainObject.getId();
        for (SgBShareVipReplenishStrategyItemDto item : subObjectList) {
            Long itemId = item.getId();

            if (itemId < 0L) {
                if (item.getPriority() == null) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("优先级不可以为空！", loginUser.getLocale()));
                }
                if (item.getSgCSaStoreId() == null) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("可调入配销仓不可以为空！", loginUser.getLocale()));
                }
                if (item.getSgCShareStoreId() == null) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("可调出聚合仓不可以为空！", loginUser.getLocale()));
                }

                SgCSaStore sgCSaStore = sgCSaStoreMapper.selectById(item.getSgCSaStoreId());
                if (sgCSaStore == null || sgCSaStore.getIsactive().equals(SgCoreUtilsConstants.IS_ACTIVE_N)) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("该可调入配销仓不存在或不可用！", loginUser.getLocale()));
                }
                //OMID-1423 修改
                //if (!SaCategoryEnum.VIP_CATEGORY.getCode().equals(sgCSaStore.getCategory())) {
                //    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("该可调入配销仓是非唯品会性质的配销仓！", loginUser.getLocale()));
                //}
                if (sgCSaStore.getSgCShareStoreId() == null) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("该可调入配销仓无所属聚合仓！", loginUser.getLocale()));
                }

                SgCShareStore sgCShareStore = sgCShareStoreMapper.selectById(item.getSgCShareStoreId());
                if (sgCShareStore == null || sgCShareStore.getIsactive().equals(SgCoreUtilsConstants.IS_ACTIVE_N)) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("该可调出聚合仓不存在或不可用！", loginUser.getLocale()));
                }

                //调入配销仓所属聚合仓
                Long sgCShareStoreId = sgCSaStore.getSgCShareStoreId();
                if (!sgCShareStoreId.equals(sgCShareStore.getId())) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("该调入配销仓所属的聚合仓和该调出聚合仓不一致，不允许保存", loginUser.getLocale()));
                }

                Integer selectCount = vipReplenishStrategyItemMapper.selectCount(new LambdaQueryWrapper<SgBShareVipReplenishStrategyItem>()
                        .eq(SgBShareVipReplenishStrategyItem::getSgBShareVipReplenishStrategyId, mainObject.getId())
                        .eq(SgBShareVipReplenishStrategyItem::getSgCSaStoreId, item.getSgCSaStoreId())
                        .eq(SgBShareVipReplenishStrategyItem::getSgCShareStoreId, item.getSgCShareStoreId()));
                if (selectCount > 0) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("调出聚合仓：" + sgCShareStore.getEname() + "和 调入配销仓：" + sgCSaStore.getEname() + "在该店铺下已存在相同记录，不允许保存", loginUser.getLocale()));
                }

            }
            if (item.getPriority() != null) {
                boolean ltZero = item.getPriority().compareTo(BigDecimal.ZERO) < 0;
                if (ltZero) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("优先级不可以小于0！", loginUser.getLocale()));
                }
                Integer selectPriorityCount = vipReplenishStrategyItemMapper.selectCount(new LambdaQueryWrapper<SgBShareVipReplenishStrategyItem>()
                        .eq(SgBShareVipReplenishStrategyItem::getSgBShareVipReplenishStrategyId, mainObjectId)
                        .eq(SgBShareVipReplenishStrategyItem::getPriority, item.getPriority()));
                if (selectPriorityCount > 0) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("该优先级:" + item.getPriority() + " 已存在，不可以重复设置！", loginUser.getLocale()));
                }
            }
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过", loginUser.getLocale()));
    }
}
