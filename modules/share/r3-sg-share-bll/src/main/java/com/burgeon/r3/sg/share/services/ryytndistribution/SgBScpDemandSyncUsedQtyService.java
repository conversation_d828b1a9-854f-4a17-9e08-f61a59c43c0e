package com.burgeon.r3.sg.share.services.ryytndistribution;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.share.ryytndistribution.SgBScpDemandSyncUsedQty;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.mapper.ryytndistribution.SgBScpDemandSyncUsedQtyMapper;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-10-29 10:53
 */
@Slf4j
@Component
public class SgBScpDemandSyncUsedQtyService {
    @Resource
    private SgBScpDemandSyncUsedQtyMapper sgBScpDemandSyncUsedQtyMapper;

    /**
     * 保存计算的中间值：占用、已发
     *
     * @param versionBi    版本
     * @param channelId    渠道ID
     * @param channelEcode 渠道编码
     * @param skuIds       SKUID列表
     * @param skuPreQtyMap SKU->占用值
     * @param skuFtpQtyMap SKU->已发值
     */
    public void saveUsedQty(String versionBi, Long channelId, String channelEcode, Set<Long> skuIds,
                            Map<Long, BigDecimal> skuPreQtyMap, Map<Long, BigDecimal> skuFtpQtyMap) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }
        Map<Long, BigDecimal> preQtyMap = MapUtils.emptyIfNull(skuPreQtyMap);
        Map<Long, BigDecimal> usedQtyMap = MapUtils.emptyIfNull(skuFtpQtyMap);

        List<SgBScpDemandSyncUsedQty> dataList = new ArrayList<>();
        for (Long skuId : skuIds) {
            SgBScpDemandSyncUsedQty data = new SgBScpDemandSyncUsedQty();
            data.setVersionBi(versionBi);
            data.setCpCDistributionOrgId(channelId);
            data.setLv2ChannelCode(channelEcode);
            data.setPsCSkuId(skuId);

            data.setPreQty(preQtyMap.getOrDefault(skuId, BigDecimal.ZERO));
            data.setUsedQty(usedQtyMap.getOrDefault(skuId, BigDecimal.ZERO));

            StorageUtils.setBModelDefalutData(data, R3SystemUserResource.getSystemRootUser());
            dataList.add(data);
        }

        StorageBasicUtils.batchInsertList(sgBScpDemandSyncUsedQtyMapper, dataList,
                1000, "批量保存计算的中间值出错", R3SystemUserResource.getSystemRootUser());
    }

    /**
     * 查询占用值-报表计算时用
     *
     * @param versionBi 版本ID
     * @param psCSkuId  SKUID
     * @return 渠道ID->占用值
     */
    public Map<Long, BigDecimal> queryByReport(String versionBi, Long psCSkuId) {
        List<SgBScpDemandSyncUsedQty> qtyList = sgBScpDemandSyncUsedQtyMapper.selectList(new QueryWrapper<SgBScpDemandSyncUsedQty>().lambda()
                .eq(SgBScpDemandSyncUsedQty::getVersionBi, versionBi)
                .eq(SgBScpDemandSyncUsedQty::getPsCSkuId, psCSkuId)
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        
        return ListUtils.emptyIfNull(qtyList).stream()
                .collect(Collectors.toMap(SgBScpDemandSyncUsedQty::getCpCDistributionOrgId, SgBScpDemandSyncUsedQty::getPreQty, (a, b) -> b));
    }
}
