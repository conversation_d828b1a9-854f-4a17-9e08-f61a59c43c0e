<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.burgeon.r3</groupId>
        <artifactId>r3-sg</artifactId>
        <version>3.0.0-SNAPSHOT</version>
        <relativePath>../../../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.burgeon.r3</groupId>
    <artifactId>r3-sg-share-bll</artifactId>
    <packaging>jar</packaging>
    <version>3.0.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sg-core-model</artifactId>
            <version>${r3.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sg-core-utils</artifactId>
            <version>${r3.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sg-share-api</artifactId>
            <version>${r3.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-service-api-model</artifactId>
            <version>${r3.framework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.21.0-GA</version>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-ad-util</artifactId>
            <version>${project.stable.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>raincloud-sysapi</artifactId>
                    <groupId>org.syman</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sg-basic-bll</artifactId>
            <version>${r3.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sg-store-bll</artifactId>
            <version>${r3.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sg-store-api</artifactId>
            <version>${r3.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sg-stocksync-api</artifactId>
            <version>${r3.project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>