package com.burgeon.r3.sg.share.model.request.translation;

import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 库存平移接口（订单）
 *
 * <AUTHOR>
 */
@Data
public class SgBStoStockTranslationListRequest extends SgR3BaseRequest {

    private List<SgBStoStockTranslationRequest> mainList;

}
