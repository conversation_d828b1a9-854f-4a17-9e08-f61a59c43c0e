package com.burgeon.r3.sg.share.api.adjust;

import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.share.model.request.adjust.SgBShareAdjustBillSaveRequest;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * <AUTHOR>
 * @create 2021/5/26 17:36
 */
@Deprecated
public interface SgBShareAdjustSaveR3Cmd extends Command {

    /**
     * 共享调整单-新增并审核接口
     *
     * @param request
     * @return
     */
    ValueHolderV14<SgR3BaseResult> saveAndSubmit(SgBShareAdjustBillSaveRequest request);

}
