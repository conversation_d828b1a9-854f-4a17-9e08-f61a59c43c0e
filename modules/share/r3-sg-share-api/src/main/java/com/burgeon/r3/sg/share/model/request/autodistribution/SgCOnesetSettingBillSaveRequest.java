package com.burgeon.r3.sg.share.model.request.autodistribution;

import com.alibaba.fastjson.annotation.JSONField;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2021/9/13 20:55
 */
@Data
public class SgCOnesetSettingBillSaveRequest extends SgR3BaseRequest implements Serializable {

    private static final long serialVersionUID = -6853697572684064732L;

    @J<PERSON><PERSON>ield(name = "SG_C_ONESET_SETTING")
    private SgCOnesetSettingSaveRequest sgCOnesetSettingSaveRequest;
}
