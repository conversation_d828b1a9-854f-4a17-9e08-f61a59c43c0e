package com.burgeon.r3.sg.share.model.request.autodistribution;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2021/9/13 16:49
 */
@Data
public class SgCOnesetSettingSaveRequest implements Serializable {

    private static final long serialVersionUID = -5565145792264697209L;

    private Long id;

    private Long adClientId;

    private Long adOrgId;

    private Long sgCShareStoreId;

    private String sgCShareStoreEcode;

    private String sgCShareStoreEname;
    //是否主码
    private String isMaincode;

    private Long psCSkuId;

    private String psCSkuEcode;

    private BigDecimal qty;

    private Long psCProId;

    private String psCProEcode;

    private String psCProEname;

    private String remark;
}
