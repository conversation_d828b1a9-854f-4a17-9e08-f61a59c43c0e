package com.burgeon.r3.sg.share.model.request.ryytndistribution;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 计划系统需求同步入参
 *
 * <AUTHOR>
 * @since 2024-07-04 16:12
 */
@Data
public class SgBScpDemandSyncRequest implements Serializable {
    /**
     * 类型
     */
    private Integer type;

    /**
     * 总数量
     */
    private Integer count;

    /**
     * 路径
     */
    private String url;

    /**
     * 超时时间
     */
    private Date timeout;
}
