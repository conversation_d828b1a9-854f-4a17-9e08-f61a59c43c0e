package com.burgeon.r3.sg.store.model.result.freeze.out;

import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.store.freeze.out.SgBStoFreezeOut;
import com.burgeon.r3.sg.core.model.table.store.freeze.out.SgBStoFreezeOutItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Auther: chenhao
 * @Date: 2023-03-08 14:45
 * @Description:
 */

@Data
public class SgBStoFreezeOutQueryResult extends SgR3BaseResult implements Serializable {

    private static final long serialVersionUID = -388821085327678766L;
    private SgBStoFreezeOut main;

    private List<SgBStoFreezeOutItem> itemList;


}
