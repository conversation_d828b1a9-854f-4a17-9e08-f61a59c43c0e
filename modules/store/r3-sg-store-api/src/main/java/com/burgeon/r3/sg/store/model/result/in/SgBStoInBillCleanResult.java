package com.burgeon.r3.sg.store.model.result.in;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * 逻辑在途单清空出参
 * @date 2021/4/7 16:19
 */
@Data
public class SgBStoInBillCleanResult implements Serializable {
    private static final long serialVersionUID = -3181335677500223910L;
    /**
     * 占用更新结果
     */
    private int preoutUpdateResult;
    /**
     * 库存redis流水
     */
    private List<String> redisBillFtpKeyList;
}
