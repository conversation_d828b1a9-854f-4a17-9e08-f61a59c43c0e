package com.burgeon.r3.sg.store.mapper.transfer;

import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoMultiChannelTransfer;
import com.burgeon.r3.sg.store.model.result.transfer.SgBStoMultiChannelTransferResult;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface SgBStoMultiChannelTransferMapper extends ExtentionMapper<SgBStoMultiChannelTransfer> {
    /**
     * 查询多渠道调拨单
     *
     * @param version 版本号
     * @return List<SgBStoMultiChannelTransferResult>
     */
    @Select("SELECT id,sender_ecode,sender_ename,receiver_ecode,receiver_ename,ps_c_sku_or_pro,qty," +
            "cp_c_tranway_assign_ename,transfer_bill_id,transfer_bill_type,transfer_bill_no, " +
            "DATE_FORMAT(tms_date, '%Y%m%d') tms_date," +
            "demand_describe as demand_type,receiver_address,is_temporary_address,remark,hint from sg_b_sto_multi_channel_transfer " +
            "where version=#{version}")
    List<SgBStoMultiChannelTransferResult> queryByVersion(@Param("version") Long version);

    @Select("SELECT BILL_NO FROM ${table} WHERE id = #{id};")
    String selectBillNoByTable(@Param("table") String table, @Param("id") Long id);

    /**
     * 查询创建小于等于此时间以前的所有id
     * @param queryTime 时间
     * @return ids
     */
    @Select("<script>" +
            "SELECT id FROM sg_b_sto_multi_channel_transfer WHERE creationdate &lt;=#{queryTime} ;"+
            "</script>")
    List<Long> queryBeforeOneDayIdList(@Param("queryTime") String queryTime);
}