package com.burgeon.r3.sg.store.services.transfer;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOut;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItem;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransfer;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutMapper;
import com.burgeon.r3.sg.store.mapper.transfer.SgBStoTransferMapper;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutResultBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutResultSaveRequest;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutNoticesBillSaveResult;
import com.burgeon.r3.sg.store.services.DrpRestHttpClient;
import com.burgeon.r3.sg.store.services.out.SgBStoOutResultSaveR3Service;
import com.burgeon.r3.sg.store.services.out.SgBStoOutSaveService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/22 9:34
 * 逻辑调拨确认单审核逻辑
 */
@Slf4j
@Component
public class SgBStoTransferConfirmSubmitService {
    @Autowired
    private SgBStoTransferMapper mapper;

    @Autowired
    private SgBStoOutMapper sgStoOutMapper;

    @Autowired
    private SgBStoOutItemMapper sgStoOutItemMapper;

    @Autowired
    private SgBStoOutSaveService stoOutSaveService;

    @Autowired
    private SgBStoOutResultSaveR3Service stoOutResultSaveR3Service;

    @Autowired
    DrpRestHttpClient dpRestHttpClient;

    /**
     * 调拨确认单审核(前端)
     *
     * @param session session
     * @return return
     */
    ValueHolder submit(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        SgBStoTransferConfirmSubmitService service =
                ApplicationContextHandle.getBean(SgBStoTransferConfirmSubmitService.class);
        List<String> redisKey = new ArrayList<>();
        return R3ParamUtils.convertV14WithResult(service.submitTransferConfirmation(request, redisKey));

    }

    /**
     * 调拨单审核
     *
     * @param request request
     * @return return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> submitTransferConfirmation(SgR3BaseRequest request, List<String> redisKey) {
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "审核成功!");

        log.info("Start SgBStoTransferConfirmationSubmitService.submitTransferConfirmation:objid{}",
                Optional.ofNullable(request.getObjId()).orElse(null));

        String lockKsy = SgConstants.SG_B_STO_TRANSFER_CONFIRMATION + ":" + request.getObjId();
        SgBStoTransfer transfer = checkParams(request);
        SgRedisLockUtils.lock(lockKsy);
        try {
            SgBStoTransfer update = new SgBStoTransfer();
            StorageUtils.setBModelDefalutDataByUpdate(update, request.getLoginUser());
            update.setStatus(SgTransferBillStatusEnum.AUDITED_NOT_OUT.getVal());
            User user = request.getLoginUser();
            update.setStatusId(user.getId().longValue());
            update.setStatusEname(user.getName());
            update.setStatusName(user.getName());
            update.setStatusTime(new Date());
            update.setConfirmId(user.getId().longValue());
            update.setConfirmTime(new Date());
            //若来源单据为差异单或erp发起的审核 确认传drp状态为不传;
//            update.setConfirmDrpStatus(SgStoreConstants.SEND_DRP_STATUS_NOT_PASS);
//            if (SgConstantsIF.BILL_TYPE_DIFF != transfer.getSourceBillType()  && SgStoreConstants.DRP_BILL_TYPE_TF.equals(transfer.getDrpBillType())
//                    && !SgStoreConstants.SEND_DRP_STATUS_NOT_PASS.equals(transfer.getConfirmDrpStatus())) {
//                JSONObject mian = new JSONObject();
//                //中台单据编号
//                mian.put("ZTDOCNO", transfer.getBillNo());
//                ValueHolderV14 drpResult = dpRestHttpClient.postResultByValueHolder(
//                        "erp.transfer.confirm", mian);
//                if (!drpResult.isOK()) {
//                    AssertUtils.logAndThrow(drpResult.getMessage(), user.getLocale());
//                }
//                update.setConfirmDrpStatus(SgStoreConstants.SEND_DRP_STATUS_SUCCESS);
//                if (log.isDebugEnabled()) {
//                    log.debug("SgBStoTransferConfirmationSubmitService.submitTransferConfirmation DRP接口传输成功");
//                }
//            }
            Integer sourceBillType = transfer.getSourceBillType();
            //2021.8.30 实时接口恢复成定时
            if (sourceBillType != null && SgConstantsIF.BILL_TYPE_DIFF == sourceBillType) {
                update.setConfirmDrpStatus(SgStoreConstants.SEND_DRP_STATUS_NOT_PASS);
            } else {
                update.setConfirmDrpStatus(SgStoreConstants.SEND_DRP_STATUS_UNDECLARED);
            }
            mapper.update(update, new UpdateWrapper<SgBStoTransfer>().lambda()
                    .eq(SgBStoTransfer::getId, request.getObjId())
                    .set(SgBStoTransfer::getConfirmDrpStatus, update.getConfirmDrpStatus()));

            SgBStoOutBillSaveRequest stoOutBillSaveRequest = new SgBStoOutBillSaveRequest();
            stoOutBillSaveRequest.setUpdateMethod(SgConstantsIF.ITEM_UPDATE_TYPE_ALL);
            stoOutBillSaveRequest.setIsCancel(false);
            stoOutBillSaveRequest.setPreoutWarningType(SgConstantsIF.PREOUT_RESULT_ERROR);
            stoOutBillSaveRequest.setIsNegativePrein(false);
            stoOutBillSaveRequest.setServiceNode(SgConstantsIF.SERVICE_NODE_STO_TRANSFER_SUBMIT);
            stoOutBillSaveRequest.setLoginUser(request.getLoginUser());
            //是否自动出库
            String isAutoOut = transfer.getIsAutoOut();
            if (StringUtils.isNotEmpty(isAutoOut)) {
                stoOutBillSaveRequest.setIsAutoOut(isAutoOut);
            }
            if (log.isDebugEnabled()) {
                log.debug("Start SgBStoTransferSubmitService.submitTransfer:isAutoOut:{}", isAutoOut);
            }
            creatOutNotices(request, v14, stoOutBillSaveRequest, redisKey);
        } catch (Exception e) {
            AssertUtils.logAndThrowException("逻辑调拨单审核异常", e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        return v14;
    }

    /**
     * 创建出库通知单 、逻辑出库单
     *
     * @param request               R3请求参数
     * @param v14                   返回v14
     * @param stoOutBillSaveRequest 逻辑占用单参数
     */
    private void creatOutNotices(SgR3BaseRequest request, ValueHolderV14 v14,
                                 SgBStoOutBillSaveRequest stoOutBillSaveRequest,
                                 List<String> redisKey) {
        if (log.isDebugEnabled()) {
            log.debug("SgBStoTransferConfirmationSubmitService.creatOutNotices.stoOutBillSaveRequest:{}",
                    JSONObject.toJSONString(stoOutBillSaveRequest));
        }
        Long objId = request.getObjId();
        SgBStoOut sgStoOut = sgStoOutMapper.selectOne(new LambdaQueryWrapper<SgBStoOut>()
                .eq(SgBStoOut::getSourceBillType, SgConstantsIF.BILL_STO_TRANSFER)
                .eq(SgBStoOut::getSourceBillId, objId)
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (sgStoOut != null) {
            List<SgBStoOutItem> stoOutItems = sgStoOutItemMapper.selectList(new LambdaQueryWrapper<SgBStoOutItem>()
                    .eq(SgBStoOutItem::getSgBStoOutId, sgStoOut.getId())
                    .eq(SgBStoOutItem::getIsactive, SgConstants.IS_ACTIVE_Y));
            //生成出库通知单
            ValueHolderV14<SgBStoOutNoticesBillSaveResult> stoOutNoticesHolderV14 =
                    stoOutSaveService.saveOutResult(sgStoOut, stoOutItems, request.getLoginUser(), null);
            if (!stoOutNoticesHolderV14.isOK()) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(stoOutNoticesHolderV14.getMessage());
            }
            if (log.isDebugEnabled()) {
                log.debug("Start SgBStoTransferConfirmationSubmitService.creatOutNotices 是否自动出库 " +
                        "stoOutBillSaveRequest:{};", JSONObject.toJSONString(stoOutBillSaveRequest));
            }
            //是否生成逻辑出库单
            if (SgConstants.IS_ACTIVE_Y.equals(stoOutBillSaveRequest.getIsAutoOut())) {
                SgBStoOutResultBillSaveRequest stoOutResultBillSaveRequest = new SgBStoOutResultBillSaveRequest();
                SgBStoOutResultSaveRequest saveRequest = new SgBStoOutResultSaveRequest();
                saveRequest.setSgBStoOutNoticesId(stoOutNoticesHolderV14.getData().getId());
                if (StringUtils.isNotEmpty(stoOutBillSaveRequest.getInterfaceTypeFlag()) && stoOutBillSaveRequest.getInterfaceTypeFlag().equals(SgConstantsIF.Interface_FLAG_DRP_OUT_NOTICES)) {
                    saveRequest.setBillNo(stoOutBillSaveRequest.getSgBStoOutSaveRequest().getBillNo());
                }
                stoOutResultBillSaveRequest.setOutResultSaveRequest(saveRequest);
                stoOutResultBillSaveRequest.setLoginUser(stoOutBillSaveRequest.getLoginUser());
                stoOutResultBillSaveRequest.setIsAutoOut(stoOutBillSaveRequest.getIsAutoOut());
                stoOutResultBillSaveRequest.setInterfaceTypeFlag(stoOutBillSaveRequest.getInterfaceTypeFlag());
                stoOutResultBillSaveRequest.setRedisKey(redisKey);

                ValueHolderV14<SgR3BaseResult> resultValueHolderV14 =
                        stoOutResultSaveR3Service.saveOutResult(stoOutResultBillSaveRequest);
                if (log.isDebugEnabled()) {
                    log.debug("end SgBStoTransferConfirmationSubmitService.stoOutResultSaveR3Service.saveOutResult." +
                            "resultValueHolderV14:{};", JSONObject.toJSONString(resultValueHolderV14));
                }
                if (!resultValueHolderV14.isOK()) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(resultValueHolderV14.getMessage());
                }
            }
        }
    }

    /**
     * 校验参数
     */
    public SgBStoTransfer checkParams(SgR3BaseRequest request) {
        SgBStoTransfer transfer = mapper.selectById(request.getObjId());
        if (transfer == null) {
            AssertUtils.logAndThrow("当前记录已不存在！", request.getLoginUser().getLocale());
        } else if (SgConstants.IS_ACTIVE_N.equals(transfer.getIsactive())) {
            AssertUtils.logAndThrow("当前记录已作废,不允许审核！", request.getLoginUser().getLocale());

        } else if (SgTransferBillStatusEnum.AUDITED_NOT_CONFIRM.getVal() != transfer.getStatus()) {
            AssertUtils.logAndThrow("当前单据状态，不允许确认审核！", request.getLoginUser().getLocale());
        }
        return transfer;
    }
}
