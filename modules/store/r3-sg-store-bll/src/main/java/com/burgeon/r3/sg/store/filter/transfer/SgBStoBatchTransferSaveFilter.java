package com.burgeon.r3.sg.store.filter.transfer;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoBatchTransfer;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoBatchTransferItem;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoBatchTransferProItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.transfer.SgBStoBatchTransferItemMapper;
import com.burgeon.r3.sg.store.mapper.transfer.SgBStoBatchTransferMapper;
import com.burgeon.r3.sg.store.mapper.transfer.SgBStoBatchTransferProItemMapper;
import com.burgeon.r3.sg.store.model.dto.transfer.SgBStoBatchTransferDto;
import com.burgeon.r3.sg.store.model.dto.transfer.SgBStoBatchTransferItemDto;
import com.burgeon.r3.sg.store.model.dto.transfer.SgBStoBatchTransferProItemDto;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.data.basic.model.request.ProInfoQueryRequest;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.ps.api.table.PsCPro;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseMultiItemsFilter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/18 10:53
 */
@Slf4j
@Component
public class SgBStoBatchTransferSaveFilter extends BaseMultiItemsFilter<SgBStoBatchTransferDto> {

    @Autowired
    private SgBStoBatchTransferMapper mapper;
    @Autowired
    private SgBStoBatchTransferItemMapper itemMapper;
    @Autowired
    private SgBStoBatchTransferProItemMapper proItemMapper;

    @Override
    public String getFilterMsgName() {
        return "批量逻辑调拨单保存过滤";
    }

    @Override
    public Class<?> getFilterClass() {
        return this.getClass();
    }

    @Override
    public Class<?> getSubTableClass(String subTableName) {
        if (SgConstants.SG_B_STO_BATCH_TRANSFER_ITEM.equalsIgnoreCase(subTableName)) {
            return SgBStoBatchTransferItemDto.class;
        } else if (SgConstants.SG_B_STO_BATCH_TRANSFER_PRO_ITEM.equalsIgnoreCase(subTableName)) {
            return SgBStoBatchTransferProItemDto.class;
        }
        return null;
    }

    @Override
    public ValueHolderV14 execBeforeTable(SgBStoBatchTransferDto mainObject, Map subObjectMap, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "虚拟逻辑调拨单保存");
        if (Objects.isNull(mainObject.getId()) || mainObject.getId() < 1L) {
            String billNo = SgStoreUtils.getBillNo(SgStoreConstants.SEQ_SG_B_STO_BATCH_TRANSFER,
                    SgConstants.SG_B_STO_BATCH_TRANSFER, mainObject,
                    loginUser.getLocale());
            Date now = new Date();
            mainObject.setBillNo(billNo);
            if (Objects.isNull(mainObject.getBillDate())) {
                mainObject.setBillDate(now);
            }
            mainObject.setStatus(SgConstantsIF.SG_B_STO_BATCH_TRANSFER_STATUS_01);
        }
        if (MapUtils.isNotEmpty(subObjectMap)) {
            Object itemObj = subObjectMap.get(SgConstants.SG_B_STO_BATCH_TRANSFER_ITEM.toUpperCase());
            if (itemObj != null) {
                List<SgBStoBatchTransferItemDto> listItem = (List<SgBStoBatchTransferItemDto>) itemObj;
                if (CollectionUtils.isNotEmpty(listItem)) {
                    ValueHolderV14 v141 = execBeforeItemTable(mainObject, listItem, loginUser);
                    if (Objects.nonNull(v141)) {
                        return v141;
                    }
                }
            }
            Object proItemObj = subObjectMap.get(SgConstants.SG_B_STO_BATCH_TRANSFER_PRO_ITEM.toUpperCase());
            if (proItemObj != null) {
                List<SgBStoBatchTransferProItemDto> listItem = (List<SgBStoBatchTransferProItemDto>) proItemObj;
                if (CollectionUtils.isNotEmpty(listItem)) {
                    ValueHolderV14 v141 = execBeforeProItemTable(mainObject, listItem, loginUser);
                    if (Objects.nonNull(v141)) {
                        return v141;
                    }
                }
            }
        }
        return v14;
    }

    @Override
    public ValueHolderV14 execAfterTable(SgBStoBatchTransferDto mainObject, Map subObjectMap, User loginUser) {
        if (MapUtils.isNotEmpty(subObjectMap)) {
            Object itemObj = subObjectMap.get(SgConstants.SG_B_STO_BATCH_TRANSFER_ITEM.toUpperCase());
            if (itemObj != null) {
                List<SgBStoBatchTransferItemDto> listItem = (List<SgBStoBatchTransferItemDto>) itemObj;
                if (CollectionUtils.isNotEmpty(listItem)) {
                    ValueHolderV14 v141 = execAfterItemTable(mainObject, listItem, loginUser);
                    if (Objects.nonNull(v141)) {
                        return v141;
                    }
                }
            }
            Object proItemObj = subObjectMap.get(SgConstants.SG_B_STO_BATCH_TRANSFER_PRO_ITEM.toUpperCase());
            if (proItemObj != null) {
                execAfterProItemTable(mainObject, loginUser);
            }
        }
        return null;
    }

    private ValueHolderV14 execBeforeItemTable(SgBStoBatchTransferDto mainObject, List<SgBStoBatchTransferItemDto> subObjectList, User loginUser) {
        HashMap<String, SgBStoBatchTransferItemDto> aggMap = new HashMap<>(subObjectList.size());
        for (SgBStoBatchTransferItemDto subObj : subObjectList) {
            if (Objects.isNull(subObj.getId()) || subObj.getId() < 1L) {
                String key = String.format("%d_%d_%d", subObj.getSenderStoreId(), subObj.getReceiverStoreId(), subObj.getPsCSkuId());
                if (Objects.isNull(aggMap.get(key))) {
                    aggMap.put(key, subObj);
                } else {
                    SgBStoBatchTransferItemDto itemDto = aggMap.get(key);
                    itemDto.setQty(itemDto.getQty().add(subObj.getQty()));
                    aggMap.put(key, itemDto);
                }
            }
        }

        if (aggMap.size() > 0) {
            subObjectList = new ArrayList<>(aggMap.values());
        }

        for (SgBStoBatchTransferItemDto subObj : subObjectList) {
            SgBStoBatchTransferItem stoBatchTransferItem = null;
            if (Objects.nonNull(subObj.getId()) && subObj.getId() > 0L) {
                stoBatchTransferItem = itemMapper.selectById(subObj.getId());
            } else {
                LambdaQueryWrapper<SgBStoBatchTransferItem> eq = new LambdaQueryWrapper<SgBStoBatchTransferItem>()
                        .ne(SgBStoBatchTransferItem::getItemStatus, SgConstantsIF.SG_B_STO_BATCH_TRANSFER_ITEM_STATUS_SUCCESS)
                        .eq(SgBStoBatchTransferItem::getSenderStoreId, subObj.getSenderStoreId())
                        .eq(SgBStoBatchTransferItem::getReceiverStoreId, subObj.getReceiverStoreId())
                        .eq(SgBStoBatchTransferItem::getPsCSkuId, subObj.getPsCSkuId())
                        .eq(SgBStoBatchTransferItem::getSgBStoBatchTransferId, mainObject.getId())
                        .eq(SgBStoBatchTransferItem::getIsactive, SgConstants.IS_ACTIVE_Y);

                stoBatchTransferItem = itemMapper.selectOne(eq);
            }

            if (Objects.nonNull(stoBatchTransferItem)) {
                if (subObj.getId() < 0) {
                    BigDecimal addQty = subObj.getQty().add(stoBatchTransferItem.getQty());
                    subObj.setQty(addQty);
                }
                subObj.setId(stoBatchTransferItem.getId());
                List<Long> skuIds = new ArrayList<>();
                skuIds.add(stoBatchTransferItem.getPsCSkuId());
                setPsCProSku(subObj, skuIds);
            } else {
                subObj.setCreationdate(new Date());
                subObj.setItemStatus(SgConstantsIF.SG_B_STO_BATCH_TRANSFER_ITEM_STATUS_NO);
                List<Long> skuIds = new ArrayList<>();
                skuIds.add(subObj.getPsCSkuId());
                setPsCProSku(subObj, skuIds);
            }
            setRedundantStore(subObj, loginUser);

            if (Objects.nonNull(subObj.getPriceList()) && Objects.nonNull(subObj.getQty())) {
                subObj.setAmt(subObj.getPriceList().multiply(subObj.getQty()));
            }
            subObj.setModifierid(Long.valueOf(SystemUserResource.getRootUser().getId()));
            subObj.setModifieddate(new Date());
            subObj.setModifiername(SystemUserResource.getRootUser().getName());
            subObj.setModifierename(SystemUserResource.getRootUser().getEname());
        }
        return null;
    }

    private ValueHolderV14 execAfterItemTable(SgBStoBatchTransferDto mainObject, List<SgBStoBatchTransferItemDto> subObjectList, User loginUser) {
        if (mainObject.getId() > 0) {
            LambdaQueryWrapper<SgBStoBatchTransferItem> eq = new LambdaQueryWrapper<SgBStoBatchTransferItem>()
                    .eq(SgBStoBatchTransferItem::getSgBStoBatchTransferId, mainObject.getId());
            List<SgBStoBatchTransferItem> sgBStoBatchTransferItems = itemMapper.selectList(eq);
            BigDecimal totQty = sgBStoBatchTransferItems.stream()
                    .filter(f -> Objects.nonNull(f.getQty()))
                    .map(SgBStoBatchTransferItem::getQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totPriceList = sgBStoBatchTransferItems.stream().filter(f -> Objects.nonNull(f.getPriceList()))
                    .map(SgBStoBatchTransferItem::getPriceList)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            List<SgBStoBatchTransferItem> successCollect = sgBStoBatchTransferItems.stream()
                    .filter(f -> Objects.nonNull(f) &&
                            SgConstantsIF.SG_B_STO_BATCH_TRANSFER_ITEM_STATUS_SUCCESS.equals(f.getItemStatus()))
                    .collect(Collectors.toList());
            SgBStoBatchTransfer sgBStoBatchTransfer = new SgBStoBatchTransfer();
            sgBStoBatchTransfer.setTotRowNum(sgBStoBatchTransferItems.size());
            sgBStoBatchTransfer.setTotQty(totQty);
            sgBStoBatchTransfer.setTotAmt(totPriceList);
            sgBStoBatchTransfer.setSuccessRowNum(BigDecimal.valueOf(successCollect.size()));
            sgBStoBatchTransfer.setFailRowNum(BigDecimal.valueOf(sgBStoBatchTransferItems.size())
                    .subtract(BigDecimal.valueOf(successCollect.size())));
            sgBStoBatchTransfer.setId(mainObject.getId());
            sgBStoBatchTransfer.setModifierid(Long.valueOf(SystemUserResource.getRootUser().getId()));
            sgBStoBatchTransfer.setModifieddate(new Date());
            sgBStoBatchTransfer.setModifiername(SystemUserResource.getRootUser().getName());
            sgBStoBatchTransfer.setModifierename(SystemUserResource.getRootUser().getEname());
            mapper.updateById(sgBStoBatchTransfer);
        }
        return null;
    }

    private void setPsCProSku(SgBStoBatchTransferItemDto subObj, List<Long> skuIds) {
        BasicPsQueryService basicPsQueryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
        SkuInfoQueryRequest skuQuery = new SkuInfoQueryRequest();
        skuQuery.setSkuIdList(skuIds);
        HashMap<Long, PsCProSkuResult> skuInfo = basicPsQueryService.getSkuInfo(skuQuery);

        if (Objects.nonNull(skuInfo) && skuInfo.size() > 0) {
            PsCProSkuResult psCProSkuResult = skuInfo.get(skuIds.get(0));
            subObj.setPsCSkuEcode(psCProSkuResult.getSkuEcode());

            subObj.setPsCProId(psCProSkuResult.getPsCProId());
            subObj.setPsCProEname(psCProSkuResult.getPsCProEname());
            subObj.setPsCProEcode(psCProSkuResult.getPsCProEcode());

            subObj.setGbcode(psCProSkuResult.getGbcode());

            subObj.setPsCSpec1Id(psCProSkuResult.getPsCSpec1objId());
            subObj.setPsCSpec1Ecode(psCProSkuResult.getClrsEcode());
            subObj.setPsCSpec1Ename(psCProSkuResult.getClrsEname());

            subObj.setPsCSpec2Id(psCProSkuResult.getPsCSpec2objId());
            subObj.setPsCSpec2Ecode(psCProSkuResult.getSizesEcode());
            subObj.setPsCSpec2Ename(psCProSkuResult.getSizesEname());

            subObj.setPriceList(Objects.isNull(psCProSkuResult.getPricelist()) ? BigDecimal.ZERO : psCProSkuResult.getPricelist());
        }
    }

    private void setPsCPro(SgBStoBatchTransferProItemDto subObj, List<Long> proId) {
        if (Objects.nonNull(proId) && proId.size() > 0) {
            BasicPsQueryService basicPsQueryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
            ProInfoQueryRequest proInfoQueryRequest = new ProInfoQueryRequest();
            proInfoQueryRequest.setProIdList(proId);
            List<PsCPro> proInfo = basicPsQueryService.getProInfo(proInfoQueryRequest);
            if (CollectionUtils.isNotEmpty(proInfo)) {
                PsCPro psCPro = proInfo.get(0);
                subObj.setPsCProEcode(psCPro.getEcode());
                subObj.setPsCProEname(psCPro.getEname());
            }
        }
    }

    private void setRedundantStore(SgBStoBatchTransferItemDto subObj, User loginUser) {
        //冗余字段获取 存在就无需获取
        if (StringUtils.isEmpty(subObj.getReceiverStoreEname())) {
            CpCStore receiverStore = null;
            if (subObj.getReceiverStoreId() != null) {
                receiverStore = CommonCacheValUtils.getStoreInfo(subObj.getReceiverStoreId());
            } else if (!StringUtils.isEmpty(subObj.getReceiverStoreEcode())) {
                receiverStore = CommonCacheValUtils.getStoreInfoByEcode(subObj.getReceiverStoreEcode());
            }

            if (receiverStore != null) {
                subObj.setReceiverStoreEcode(receiverStore.getEcode());
                subObj.setReceiverStoreEname(receiverStore.getEname());
                subObj.setReceiverCustomerId(receiverStore.getCpCCustomerId());
                subObj.setReceiverCustomerEcode(receiverStore.getCpCCustomerEcode());
                subObj.setReceiverCustomerEname(receiverStore.getCpCCustomerEname());

                if (SgConstantsIF.CP_C_TRANWAY_ASSIGN_ID_3.equals(subObj.getCpCTranwayAssignId()) && StringUtils.isEmpty(subObj.getReceiverAddress())) {
                    subObj.setReceiverAddress(receiverStore.getDestAddr());
                }
            }
        }
        if (StringUtils.isEmpty(subObj.getSenderStoreEname())) {
            if (subObj.getSenderStoreId() != null) {
                CpCStore senderStore = CommonCacheValUtils.getStoreInfo(subObj.getSenderStoreId());
                if (senderStore != null) {
                    subObj.setSenderStoreEcode(senderStore.getEcode());
                    subObj.setSenderStoreEname(senderStore.getEname());
                    subObj.setSenderCustomerId(senderStore.getCpCCustomerId());
                    subObj.setSenderCustomerEcode(senderStore.getCpCCustomerEcode());
                    subObj.setSenderCustomerEname(senderStore.getCpCCustomerEname());
                }
            } else if (!StringUtils.isEmpty(subObj.getSenderStoreEcode())) {
                CpCStore senderStore = CommonCacheValUtils.getStoreInfoByEcode(subObj.getSenderStoreEcode());
                if (senderStore != null) {
                    subObj.setSenderStoreId(senderStore.getId());
                    subObj.setSenderStoreEcode(senderStore.getEcode());
                    subObj.setSenderStoreEname(senderStore.getEname());
                    subObj.setSenderCustomerId(senderStore.getCpCCustomerId());
                    subObj.setSenderCustomerEcode(senderStore.getCpCCustomerEcode());
                    subObj.setSenderCustomerEname(senderStore.getCpCCustomerEname());
                } else {
                    AssertUtils.logAndThrow("当前发货店仓不存在！", loginUser.getLocale());
                }
            }
        }
    }

    private void setProRedundantStore(SgBStoBatchTransferProItemDto subObj, User loginUser) {
        //冗余字段获取 存在就无需获取
        if (StringUtils.isEmpty(subObj.getReceiverStoreEname())) {
            CpCStore receiverStore = null;
            if (subObj.getReceiverStoreId() != null) {
                 receiverStore = CommonCacheValUtils.getStoreInfo(subObj.getReceiverStoreId());
            } else if (!StringUtils.isEmpty(subObj.getReceiverStoreEcode())) {
                 receiverStore = CommonCacheValUtils.getStoreInfoByEcode(subObj.getReceiverStoreEcode());
            }

            if (receiverStore != null) {
                if (log.isDebugEnabled()) {
                    log.debug("SgBStoBatchTransferSaveFilter.setProRedundantStore receiverStore:{}", JSONObject.toJSONString(receiverStore));
                }
                subObj.setReceiverStoreEcode(receiverStore.getEcode());
                subObj.setReceiverStoreEname(receiverStore.getEname());
                subObj.setReceiverCustomerId(receiverStore.getCpCCustomerId());
                subObj.setReceiverCustomerEcode(receiverStore.getCpCCustomerEcode());
                subObj.setReceiverCustomerEname(receiverStore.getCpCCustomerEname());

                if (SgConstantsIF.CP_C_TRANWAY_ASSIGN_ID_3.equals(subObj.getCpCTranwayAssignId()) && StringUtils.isEmpty(subObj.getReceiverAddress())) {
                    subObj.setReceiverAddress(receiverStore.getDestAddr());
                }
            }
        }
        if (StringUtils.isEmpty(subObj.getSenderStoreEname())) {
            if (subObj.getSenderStoreId() != null) {
                CpCStore senderStore = CommonCacheValUtils.getStoreInfo(subObj.getSenderStoreId());
                if (senderStore != null) {
                    subObj.setSenderStoreEcode(senderStore.getEcode());
                    subObj.setSenderStoreEname(senderStore.getEname());
                    subObj.setSenderCustomerId(senderStore.getCpCCustomerId());
                    subObj.setSenderCustomerEcode(senderStore.getCpCCustomerEcode());
                    subObj.setSenderCustomerEname(senderStore.getCpCCustomerEname());
                }
            } else if (!StringUtils.isEmpty(subObj.getSenderStoreEcode())) {
                CpCStore senderStore = CommonCacheValUtils.getStoreInfoByEcode(subObj.getSenderStoreEcode());
                if (senderStore != null) {
                    subObj.setSenderStoreId(senderStore.getId());
                    subObj.setSenderStoreEcode(senderStore.getEcode());
                    subObj.setSenderStoreEname(senderStore.getEname());
                    subObj.setSenderCustomerId(senderStore.getCpCCustomerId());
                    subObj.setSenderCustomerEcode(senderStore.getCpCCustomerEcode());
                    subObj.setSenderCustomerEname(senderStore.getCpCCustomerEname());
                } else {
                    AssertUtils.logAndThrow("当前发货店仓不存在！", loginUser.getLocale());
                }
            }
        }
    }

    private ValueHolderV14 execBeforeProItemTable(SgBStoBatchTransferDto mainObject, List<SgBStoBatchTransferProItemDto> subObjectList, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBStoBatchTransferSaveFilter.execBeforeProItemTable. main={},sub={}",
                    JSONObject.toJSONString(mainObject), JSONObject.toJSONString(subObjectList));
        }
        HashMap<String, SgBStoBatchTransferProItemDto> aggMap = new HashMap<>(subObjectList.size());
        for (SgBStoBatchTransferProItemDto subObj : subObjectList) {
            if (Objects.isNull(subObj.getId()) || subObj.getId() < 1L) {
                String key = String.format("%d_%d_%s", subObj.getSenderStoreId(), subObj.getReceiverStoreId(), subObj.getSkuEcode());
                if (Objects.isNull(aggMap.get(key))) {
                    aggMap.put(key, subObj);
                } else {
                    SgBStoBatchTransferProItemDto itemDto = aggMap.get(key);
                    itemDto.setQty(itemDto.getQty().add(subObj.getQty()));
                    aggMap.put(key, itemDto);
                }
            }
        }

        if (aggMap.size() > 0) {
            subObjectList = new ArrayList<>(aggMap.values());
        }

        List<String> skuEcodeList = subObjectList.stream().map(SgBStoBatchTransferProItemDto::getSkuEcode).collect(Collectors.toList());
        Map<String, com.jackrain.nea.ps.api.table.PsCPro> proResultMap = new HashMap<>(skuEcodeList.size());
        Map<String, PsCProSkuResult> psCProSkuResultMap = new HashMap<>(skuEcodeList.size());
        if (CollectionUtils.isNotEmpty(subObjectList) && subObjectList.get(0).getId() < 0) {
            //按输入条码筛选商品sku和商品信息
            BasicPsQueryService basicPsQueryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
            SkuInfoQueryRequest skuQueryRequest = new SkuInfoQueryRequest();
            skuQueryRequest.setSkuEcodeList(skuEcodeList);
            psCProSkuResultMap = basicPsQueryService.getSkuInfoByEcode(skuQueryRequest);
            proResultMap = CommonCacheValUtils.getProInfoMapByEcodes(skuEcodeList);

        }

        for (SgBStoBatchTransferProItemDto subObj : subObjectList) {
            SgBStoBatchTransferProItem stoBatchTransferProItem = null;

            if (Objects.nonNull(subObj.getId()) && subObj.getId() > 0L) {
                stoBatchTransferProItem = proItemMapper.selectById(subObj.getId());
            } else {
                LambdaQueryWrapper<SgBStoBatchTransferProItem> eq = new LambdaQueryWrapper<SgBStoBatchTransferProItem>()
                        .eq(SgBStoBatchTransferProItem::getSenderStoreId, subObj.getSenderStoreId())
                        .eq(SgBStoBatchTransferProItem::getReceiverStoreId, subObj.getReceiverStoreId())
                        .eq(SgBStoBatchTransferProItem::getSkuEcode, subObj.getSkuEcode())
                        .eq(SgBStoBatchTransferProItem::getSgBStoBatchTransferId, mainObject.getId())
                        .eq(SgBStoBatchTransferProItem::getIsactive, SgConstants.IS_ACTIVE_Y);

                stoBatchTransferProItem = proItemMapper.selectOne(eq);

                PsCProSkuResult psCProSkuResult = psCProSkuResultMap.get(subObj.getSkuEcode().toUpperCase());
                boolean skuNotActive = Objects.isNull(psCProSkuResult) || SgConstants.IS_ACTIVE_N.equals(psCProSkuResult.getIsactive());
                PsCPro psCPro = proResultMap.get(subObj.getSkuEcode().toUpperCase());
                boolean proNotActive = Objects.isNull(psCPro) || SgConstants.IS_ACTIVE_N.equals(psCPro.getIsactive());
                if (skuNotActive && proNotActive) {
                    ValueHolderV14 v14 = new ValueHolderV14(ResultCode.FAIL, String.format("输入的商品或条码：%s，不存在，请确认", subObj.getSkuEcode()));
                    return v14;
                }
                if (Objects.nonNull(psCProSkuResult)) {
                    subObj.setPsCSkuId(psCProSkuResult.getId());
                    subObj.setPsCSkuEcode(subObj.getSkuEcode());
                    subObj.setPsCProId(psCProSkuResult.getPsCProId());
                    subObj.setPsCProEcode(psCProSkuResult.getPsCProEcode());
                    subObj.setPsCProEname(psCProSkuResult.getPsCProEname());
                    subObj.setTransferDimension(SgConstantsIF.TRANSFER_DIMENSION_1);
                }else {
                    subObj.setPsCProId(psCPro.getId());
                    subObj.setPsCProEcode(subObj.getSkuEcode());
                    subObj.setPsCProEname(psCPro.getEname());
                    subObj.setTransferDimension(SgConstantsIF.TRANSFER_DIMENSION_2);
                }
            }

            if (Objects.nonNull(stoBatchTransferProItem)) {
                if (subObj.getId() < 0) {
                    BigDecimal addQty = subObj.getQty().add(stoBatchTransferProItem.getQty());
                    subObj.setQty(addQty);
                }
                subObj.setId(stoBatchTransferProItem.getId());
                if (Objects.nonNull(subObj.getCpCTranwayAssignId()) &&
                        SgConstantsIF.CP_C_TRANWAY_ASSIGN_ID_3.equals(subObj.getCpCTranwayAssignId())) {
                    CpCStore storeInfo = CommonCacheValUtils.getStoreInfo(stoBatchTransferProItem.getReceiverStoreId());
                    if (Objects.nonNull(storeInfo)) {
                        if (log.isDebugEnabled()) {
                            log.debug("SgBStoBatchTransferSaveFilter.execBeforeProItemTable. proItem={},storeInfo={}",
                                    JSONObject.toJSONString(stoBatchTransferProItem), JSONObject.toJSONString(storeInfo));
                        }
                        subObj.setReceiverAddress(storeInfo.getDestAddr());
                    }
                }
            } else {
//                List<Long> listProId = new ArrayList<>();
//                listProId.add(subObj.getPsCProId());
//                setPsCPro(subObj, listProId);
                subObj.setCreationdate(new Date());
                subObj.setItemStatus(SgConstantsIF.SG_B_STO_BATCH_TRANSFER_ITEM_STATUS_NO);
                if (Objects.nonNull(subObj.getCpCTranwayAssignId()) &&
                        SgConstantsIF.CP_C_TRANWAY_ASSIGN_ID_3.equals(subObj.getCpCTranwayAssignId())) {
                    CpCStore storeInfo = CommonCacheValUtils.getStoreInfo(subObj.getReceiverStoreId());
                    if (Objects.nonNull(storeInfo)) {
                        if (log.isDebugEnabled()) {
                            log.debug("SgBStoBatchTransferSaveFilter.execBeforeProItemTable. proItem={},storeInfo={}",
                                    JSONObject.toJSONString(subObj), JSONObject.toJSONString(storeInfo));
                        }
                        subObj.setReceiverAddress(storeInfo.getDestAddr());
                    }
                }
            }
            setProRedundantStore(subObj, loginUser);

            subObj.setModifierid(Long.valueOf(SystemUserResource.getRootUser().getId()));
            subObj.setModifieddate(new Date());
            subObj.setModifiername(SystemUserResource.getRootUser().getName());
            subObj.setModifierename(SystemUserResource.getRootUser().getEname());
        }
        return null;
    }

    private ValueHolderV14 execAfterProItemTable(SgBStoBatchTransferDto mainObject, User loginUser) {
        if (mainObject.getId() > 0) {
            LambdaQueryWrapper<SgBStoBatchTransferProItem> eq = new LambdaQueryWrapper<SgBStoBatchTransferProItem>()
                    .eq(SgBStoBatchTransferProItem::getSgBStoBatchTransferId, mainObject.getId());
            List<SgBStoBatchTransferProItem> sgBStoBatchTransferProItems = proItemMapper.selectList(eq);
            BigDecimal totQty = sgBStoBatchTransferProItems.stream()
                    .filter(f -> Objects.nonNull(f.getQty()))
                    .map(SgBStoBatchTransferProItem::getQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            SgBStoBatchTransfer sgBStoBatchTransfer = new SgBStoBatchTransfer();
            sgBStoBatchTransfer.setTotQty(totQty);
            sgBStoBatchTransfer.setId(mainObject.getId());
            sgBStoBatchTransfer.setProTotRowNum(sgBStoBatchTransferProItems.size());

            sgBStoBatchTransfer.setModifierid(Long.valueOf(loginUser.getId()));
            sgBStoBatchTransfer.setModifieddate(new Date());
            sgBStoBatchTransfer.setModifiername(loginUser.getName());
            sgBStoBatchTransfer.setModifierename(loginUser.getEname());
            mapper.updateById(sgBStoBatchTransfer);
        }
        return null;
    }
}
