package com.burgeon.r3.sg.store.services.out;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.freeze.out.SgBStoFreezeOut;
import com.burgeon.r3.sg.core.model.table.store.freeze.out.SgBStoFreezeOutItem;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInNotices;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInNoticesItem;
import com.burgeon.r3.sg.core.model.table.store.in.SgBWmsToStoreprocessConfirmResult;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOtherOutResult;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNoticesItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.inf.common.enums.StoreTypeEnum;
import com.burgeon.r3.sg.store.common.OrderTypeEnum;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.freeze.out.SgBStoFreezeOutItemMapper;
import com.burgeon.r3.sg.store.mapper.freeze.out.SgBStoFreezeOutMapper;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInNoticesItemMapper;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInNoticesMapper;
import com.burgeon.r3.sg.store.mapper.in.SgBWmsToStoreprocessConfirmResultMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesMapper;
import com.burgeon.r3.sg.store.model.request.freeze.out.result.SgBStoFreezeOutResultBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.freeze.out.result.SgBStoFreezeOutResultSaveItemRequest;
import com.burgeon.r3.sg.store.model.request.freeze.out.result.SgBStoFreezeOutResultSaveMainRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInNoticesBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutResultBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutResultItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutResultSaveRequest;
import com.burgeon.r3.sg.store.model.result.freeze.out.SgBStoFreezeOutBillResult;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInNoticesBillSaveResult;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInResultBillSubmitResult;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutResultBillSaveResult;
import com.burgeon.r3.sg.store.services.freeze.out.result.SgBStoFreezeOutResultSaveAndSubmitService;
import com.burgeon.r3.sg.store.services.in.SgBStoInNoticesSaveService;
import com.burgeon.r3.sg.store.services.in.SgBStoInResultSaveAndSubmitService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Component
public class SgBToStoreProcessConfirmFromWmsService extends ServiceImpl<SgBWmsToStoreprocessConfirmResultMapper,
        SgBWmsToStoreprocessConfirmResult> {

    private final SgBStoOutNoticesMapper stoOutNoticesMapper;
    private final SgBStoOutNoticesItemMapper stoOutNoticesItemMapper;
    private final SgBStoInNoticesMapper sgStoInNoticesMapper;
    private final SgBStoInNoticesItemMapper sgStoInNoticesItemMapper;
    private final SgBWmsToStoreprocessConfirmResultMapper sgWmsToStoreprocessConfirmResultMapper;
    private final SgBStoOutResultSaveService sgStoOutResultSaveService;
    private final SgBStoInNoticesSaveService sgStoInNoticesSaveService;
    private final SgBStoInResultSaveAndSubmitService sgBStoInResultSaveAndSubmitService;
    private final SgBStoFreezeOutResultSaveAndSubmitService sgBStoFreezeOutResultSaveAndSubmitService;

    private final SgBStoOtherOutResultService sgBStoOtherOutResultService;
    private final SgBStoFreezeOutMapper sgBStoFreezeOutMapper;
    private final SgBStoFreezeOutItemMapper sgBStoFreezeOutItemMapper;


    @NacosValue(value = "${lts.wms.taskQuery.limit:100}", autoRefreshed = true)
    private Integer limit;


    public ValueHolderV14<String> confirmConvert(JSONObject params) throws NDSException {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start SgBStockoutConfirmFromJuWoService.stockOutConfirmConvert.params={};" +
                    "limit:{}", "仓内加工单中间表转换定时任务"), JSONObject.toJSONString(params), limit);
        }
        List<Integer> transformStatus = new ArrayList<>();
        transformStatus.add(0);
        transformStatus.add(1);
        List<SgBWmsToStoreprocessConfirmResult> confirmResults =
                sgWmsToStoreprocessConfirmResultMapper.selectList(new LambdaQueryWrapper<SgBWmsToStoreprocessConfirmResult>()
                        .in(SgBWmsToStoreprocessConfirmResult::getWmsWarehouseType, Arrays.asList(ThirdWmsTypeEnum.QMWMS.getCode(), ThirdWmsTypeEnum.FLWMS.getCode()))
                        .in(SgBWmsToStoreprocessConfirmResult::getTransformStatus, transformStatus)
                        .lt(SgBWmsToStoreprocessConfirmResult::getFailedCount, 6));
        if (CollectionUtils.isEmpty(confirmResults)) {
            return new ValueHolderV14<>(ResultCode.SUCCESS, "仓内加工单中间表无符合条件数据,任务结束!");
        }
        queryAndConvert(confirmResults);
        return new ValueHolderV14<>(ResultCode.SUCCESS, "仓内加工单中间表转换成功!");
    }

    /**
     * 查询数据转换
     *
     * @param confirmResults 中间表
     */
    private void queryAndConvert(List<SgBWmsToStoreprocessConfirmResult> confirmResults) {
        List<String> messages =
                confirmResults.stream().map(SgBWmsToStoreprocessConfirmResult::getMessage).collect(Collectors.toList());
        List<ReturnItem> items = new ArrayList<>();
        for (String message : messages) {
            String materialitems = JSONObject.parseObject(message).getString("materialitems");
            if (StringUtils.isNotBlank(materialitems)) {
                List<ReturnItem> lines = JSON.parseArray(materialitems, ReturnItem.class);
                items.addAll(lines);
            }
            String productitems = JSONObject.parseObject(message).getString("productitems");
            if (StringUtils.isNotBlank(productitems)) {
                List<ReturnItem> lines = JSON.parseArray(productitems, ReturnItem.class);
                items.addAll(lines);
            }
        }
        //条码信息
        List<String> skuEcodes =
                items.stream().map(ReturnItem::getItemCode).distinct().collect(Collectors.toList());
        log.info(LogUtil.format("SgBToStoreProcessConfirmFromWmsService.queryAndConvert",
                "SgBToStoreProcessConfirmFromWmsService.queryAndConvert.skuEcodes={}"),
                JSONObject.toJSONString(skuEcodes));
        Map<String, PsCProSkuResult> skuInfo = CommonCacheValUtils.getSkuInfo(skuEcodes);

        convert(skuInfo, confirmResults);
    }

    /**
     * 转换
     *
     * @param skuInfo        条码map
     * @param confirmResults 中间表
     */
    private void convert(Map<String, PsCProSkuResult> skuInfo,
                         List<SgBWmsToStoreprocessConfirmResult> confirmResults) {
        List<String> billNos =
                confirmResults.stream().map(SgBWmsToStoreprocessConfirmResult::getNoticesBillNo).collect(Collectors.toList());

        List<SgBStoOutNotices> outNotices =
                stoOutNoticesMapper.selectList(new LambdaQueryWrapper<SgBStoOutNotices>()
                        .in(SgBStoOutNotices::getBillNo, billNos)
                        .eq(SgBStoOutNotices::getIsactive, SgConstants.IS_ACTIVE_Y));
        Map<String, SgBStoOutNotices> outNoticesMap = new HashMap<>(16);
        Map<Long, List<SgBStoOutNoticesItem>> outNoticesItemMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(outNotices)) {
            outNotices.forEach(o -> outNoticesMap.put(o.getBillNo(), o));
            List<Long> outNoticeIds = outNotices.stream().map(SgBStoOutNotices::getId).collect(Collectors.toList());
            List<SgBStoOutNoticesItem> outNoticesItems =
                    stoOutNoticesItemMapper.selectList(new LambdaQueryWrapper<SgBStoOutNoticesItem>()
                            .in(SgBStoOutNoticesItem::getSgBStoOutNoticesId, outNoticeIds));
            outNoticesItemMap =
                    outNoticesItems.stream().collect(Collectors.groupingBy(SgBStoOutNoticesItem::getSgBStoOutNoticesId));
        }

        for (SgBWmsToStoreprocessConfirmResult confirmResult : confirmResults) {
            SgBStoOutNotices notices = outNoticesMap.get(confirmResult.getNoticesBillNo());
            if (Objects.isNull(notices)) {
                setUpdateParam(confirmResult, "出库通知单不存在", false);
                continue;
            }
            List<SgBStoOutNoticesItem> outNoticesItems = outNoticesItemMap.get(notices.getId());
            if (CollectionUtils.isEmpty(outNoticesItems)) {
                setUpdateParam(confirmResult, "出库通知单" + confirmResult.getNoticesBillNo() + "无明细", false);
                continue;
            }

            SgBToStoreProcessConfirmFromWmsService confirmFromWmsService =
                    ApplicationContextHandle.getBean(SgBToStoreProcessConfirmFromWmsService.class);
            try {
                //创建单据
                confirmFromWmsService.createBills(skuInfo, confirmResult, notices, outNoticesItems);
            } catch (Exception e) {
                log.error("SgBToStoreProcessConfirmFromWmsService.convert.error={}",
                        Throwables.getStackTraceAsString(e));
                setUpdateParam(confirmResult, e.getMessage(), false);
            }

        }
        this.updateBatchById(confirmResults);
    }

    /**
     * 创建单据
     *
     * @param skuInfo         条码map
     * @param confirmResult   中间表
     * @param notices         通知单
     * @param outNoticesItems 通知单明细map
     */
    @Transactional(rollbackFor = Exception.class)
    public void createBills(Map<String, PsCProSkuResult> skuInfo,
                            SgBWmsToStoreprocessConfirmResult confirmResult,
                            SgBStoOutNotices notices,
                            List<SgBStoOutNoticesItem> outNoticesItems) {
        //redis库存流水键
        List<String> redisBillFtpKeyList = new ArrayList<>();
        try {
            String message = confirmResult.getMessage();
            JSONObject jsonObject = JSONObject.parseObject(message);
            String materialitems = jsonObject.getString("materialitems");

            String productitems = jsonObject.getString("productitems");

            if (StringUtils.isBlank(materialitems)) {
                setUpdateParam(confirmResult, "报文无出库商品明细信息", false);
                return;
            }
            if (StringUtils.isBlank(productitems)) {
                setUpdateParam(confirmResult, "报文无入库商品明细信息", false);
                return;
            }
            Map<String, List<SgBStoOutNoticesItem>> outNoticesItemSkuMap =
                    outNoticesItems.stream()
                            .collect(Collectors.groupingBy(o -> o.getPsCSkuEcode() + SgConstantsIF.MAP_KEY_DIVIDER + o.getStorageType()));
            //回传条码信息
            List<ReturnItem> outItems = JSON.parseArray(materialitems, ReturnItem.class);
            List<ReturnItem> inItems = JSON.parseArray(productitems, ReturnItem.class);

            /*校验明细行是否一致*/
            checkOutItems(outItems, outNoticesItems);

            /*新增冻结出库单并审核*/
            saveAndSubmitFreezeOutResult(outItems, skuInfo, notices, outNoticesItems, outNoticesItemSkuMap, jsonObject, redisBillFtpKeyList);

            //新增逻辑出库单（并审核）
            SgBStoOutResultBillSaveRequest sgStoOutResultSaveRequest = buildSgStoOutResultSaveRequest(outItems, skuInfo,
                    notices, outNoticesItemSkuMap, jsonObject);
            //check判断是否有同批次同条码的数据
            SgBStoOutResultBillSaveRequest resultBillSaveRequest = checkOutResultSaveRequest(sgStoOutResultSaveRequest);
            ValueHolderV14<SgBStoOutResultBillSaveResult> stoOutResultV14 =
                    sgStoOutResultSaveService.saveSgStoOutResult(resultBillSaveRequest);
            log.info(LogUtil.format("SgBToStoreProcessConfirmFromWmsService.createBills.stoOutResultV14={}",
                    "SgBToStoreProcessConfirmFromWmsService.createBills", JSONObject.toJSONString(stoOutResultV14)));
            AssertUtils.cannot(!stoOutResultV14.isOK(), stoOutResultV14.getMessage());
            if (stoOutResultV14.getData() != null && CollectionUtils.isNotEmpty(stoOutResultV14.getData().getRedisBillFtpKeyList())) {
                redisBillFtpKeyList.addAll(stoOutResultV14.getData().getRedisBillFtpKeyList());
            }

            String wmsBillCode = jsonObject.getString("processOrderId");

            // 新增入库通知单
            SgBStoInNoticesBillSaveRequest stoInNoticesBillSaveRequest = buildSgBStoInNotices(inItems, skuInfo,
                    notices, outNoticesItems, wmsBillCode);

            ValueHolderV14<SgBStoInNoticesBillSaveResult> stoInNoticesV14 =
                    sgStoInNoticesSaveService.addInNotices(stoInNoticesBillSaveRequest, SystemUserResource.getRootUser());
            log.info(LogUtil.format("SgBToStoreProcessConfirmFromWmsService.createBills.stoInNoticesV14={}",
                    "SgBToStoreProcessConfirmFromWmsService.createBills", JSONObject.toJSONString(stoInNoticesV14)));
            AssertUtils.cannot(!stoInNoticesV14.isOK(), stoInNoticesV14.getMessage());
            SgBStoInNoticesBillSaveResult inNoticesV14Data = stoInNoticesV14.getData();
            // 新增逻辑入库单
            List<SgBStoInResultBillSaveRequest> stoInResultBillSaveRequests = buildSgBStoInResult(inNoticesV14Data, wmsBillCode);
            ValueHolderV14<List<SgBStoInResultBillSubmitResult>> stoInResultV14 =
                    sgBStoInResultSaveAndSubmitService.saveAndAuditBill(stoInResultBillSaveRequests);
            log.info(LogUtil.format("SgBToStoreProcessConfirmFromWmsService.createBills.stoInResultV14={}",
                    "SgBToStoreProcessConfirmFromWmsService.createBills", JSONObject.toJSONString(stoInResultV14)));
            AssertUtils.cannot(!stoInResultV14.isOK(), stoInResultV14.getMessage());
            if (CollectionUtils.isNotEmpty(stoInResultV14.getData())) {
                for (SgBStoInResultBillSubmitResult datum : stoInResultV14.getData()) {
                    if (datum != null && CollectionUtils.isNotEmpty(datum.getRedisBillFtpKeyList())) {
                        redisBillFtpKeyList.addAll(datum.getRedisBillFtpKeyList());
                    }
                }
            }
            setUpdateParam(confirmResult, " ", true);
        } catch (Exception e) {
            log.error(LogUtil.format("SgBToStoreProcessConfirmFromWmsService.createBills error:{},redisBillFtpKeyList:{}",
                            "SgBToStoreProcessConfirmFromWmsService.createBills"),
                    Throwables.getStackTraceAsString(e), JSONObject.toJSONString(redisBillFtpKeyList));
            // 回滚库存
            StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, SystemUserResource.getRootUser());
            AssertUtils.logAndThrowException("仓内加工转化异常：", e, SystemUserResource.getRootUser().getLocale());
        }
    }

    /**
     * 构建冻结出库单
     *
     * @param outItems            出库商品明细
     * @param skuInfo             SKU信息
     * @param notices             出库通知单
     * @param outNoticesItems     出库通知单明细
     * @param jsonObject          原始报文
     * @param redisBillFtpKeyList 回滚库存标识
     */
    private void saveAndSubmitFreezeOutResult(List<ReturnItem> outItems,
                                              Map<String, PsCProSkuResult> skuInfo,
                                              SgBStoOutNotices notices,
                                              List<SgBStoOutNoticesItem> outNoticesItems,
                                              Map<String, List<SgBStoOutNoticesItem>> outNoticesItemSkuMap,
                                              JSONObject jsonObject,
                                              List<String> redisBillFtpKeyList) {
        /*目前只处理返修类型*/
        outItems = ListUtils.emptyIfNull(outItems).stream()
                .filter(item -> StoreTypeEnum.FX.getValue().equals(item.getInventoryType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outItems)) {
            return;
        }

        SgBStoOtherOutResult otherOutResult = null;
        /*判断来源是否为【其他出库单-生产流程订单-换箱加工】*/
        if (OrderTypeEnum.SOURCE_BILL_TYPE_160.getValue().equals(notices.getSourceBillType())
                && String.valueOf(OrderTypeEnum.SOURCE_BILL_TYPE_180.getValue()).equals(notices.getSourceBillSourceBillType())) {
            otherOutResult = sgBStoOtherOutResultService.queryActiveById(notices.getSourceBillId());
        }
        if (Objects.isNull(otherOutResult)
                || !SgConstants.OTHER_OUT_RESULT_PRODUCTION_PROCESS_TYPE_CHANGE_BOX.equals(otherOutResult.getProcessType())) {
            return;
        }

        /*查询冻结占用单生成对应的冻结出库单，理论上来说只有一个*/
        List<SgBStoFreezeOut> freezeOutList = sgBStoFreezeOutMapper.selectList(new QueryWrapper<SgBStoFreezeOut>().lambda()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgBStoFreezeOut::getSourceBillType, OrderTypeEnum.SOURCE_BILL_TYPE_160.getValue())
                .eq(SgBStoFreezeOut::getSourceBillId, otherOutResult.getId()));
        if (CollectionUtils.isEmpty(freezeOutList)) {
            return;
        }
        if (freezeOutList.size() > 1) {
            log.warn(LogUtil.format("查询到的冻结占用单数量大于1，其他出库单ID:{},其他出库单单号:{}",
                            "SgBToStoreProcessConfirmFromWmsService.saveAndSubmitFreezeOutResult"),
                    otherOutResult.getId(), otherOutResult.getBillNo());
            throw new NDSException("查询到的冻结占用单数量大于1");
        }

        SgBStoFreezeOut freezeOut = freezeOutList.get(0);
        if (!SgStoreConstants.BILL_STO_FREEZE_OUT_STATUS_CREATE.equals(freezeOut.getBillStatus())) {
            log.warn(LogUtil.format("查询到的冻结占用单状态不为【创建】，其他出库单ID:{},其他出库单单号:{}",
                            "SgBToStoreProcessConfirmFromWmsService.saveAndSubmitFreezeOutResult"),
                    otherOutResult.getId(), otherOutResult.getBillNo());
            throw new NDSException("占用了残次库存的库内加工单不支持多次出库");
        }

        List<SgBStoFreezeOutItem> freezeOutItemList = sgBStoFreezeOutItemMapper.selectList(new QueryWrapper<SgBStoFreezeOutItem>().lambda()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgBStoFreezeOutItem::getSgBStoFreezeOutId, freezeOut.getId()));
        if (CollectionUtils.isEmpty(freezeOutItemList)) {
            log.warn(LogUtil.format("查询不到冻结占用单明细，其他出库单ID:{},冻结占用单ID:{}",
                            "SgBToStoreProcessConfirmFromWmsService.saveAndSubmitFreezeOutResult"),
                    otherOutResult.getId(), freezeOut.getId());
            throw new NDSException("查询不到冻结占用单明细");
        }

        /*构建参数*/
        SgBStoFreezeOutResultBillSaveRequest request = new SgBStoFreezeOutResultBillSaveRequest();
        request.setObjId(-1L);
        request.setLoginUser(R3SystemUserResource.getSystemRootUser());

        /*构建主表参数*/
        SgBStoFreezeOutResultSaveMainRequest saveRequest = new SgBStoFreezeOutResultSaveMainRequest();
        BeanUtils.copyProperties(notices, saveRequest);
        saveRequest.setIsLast(SgConstants.IS_LAST_YES);
        saveRequest.setSourceBillId(notices.getSourceBillId());
        saveRequest.setOutTime(jsonObject.getDate("orderCompleteTime"));
        saveRequest.setWmsBillNo(jsonObject.getString("processOrderId"));
        saveRequest.setRemark(jsonObject.getString("remark"));
        saveRequest.setSourceBillType(notices.getSourceBillType());
        saveRequest.setSourceBillNo(notices.getSourceBillNo());
        saveRequest.setSourceBillDate(notices.getSourceBillDate());
        saveRequest.setSgBStoOutNoticesId(notices.getId());
        saveRequest.setSgBStoOutNoticesNo(notices.getBillNo());
        saveRequest.setReceiverName(notices.getReceiverName());
        saveRequest.setReceiverEcode(notices.getReceiverEcode());
        saveRequest.setSourceBillSourceBillType(notices.getSourceBillSourceBillType());
        saveRequest.setCpCStoreId(outNoticesItems.get(0).getCpCStoreId());
        saveRequest.setCpCStoreEcode(outNoticesItems.get(0).getCpCStoreEcode());
        saveRequest.setCpCStoreEname(outNoticesItems.get(0).getCpCStoreEname());
        request.setMainRequest(saveRequest);

        /*构建明细参数*/
        List<SgBStoFreezeOutResultSaveItemRequest> itemSaveRequestList = new ArrayList<>();
        StringBuffer errorBuffer = new StringBuffer();
        for (ReturnItem line : outItems) {
            PsCProSkuResult psProSkuResult = skuInfo.get(line.getItemCode());
            if (Objects.isNull(psProSkuResult)) {
                errorBuffer.append(line.getItemCode()).append("在【商品SKU】中不存在;");
                continue;
            }
            List<SgBStoOutNoticesItem> noticesItems = outNoticesItemSkuMap.get(line.getItemCode() + SgConstantsIF.MAP_KEY_DIVIDER + line.getInventoryType());
            if (CollectionUtils.isEmpty(noticesItems)) {
                errorBuffer.append(line.getItemCode()).append("在【出库通知单明细】中不存在;");
                continue;
            }

            SgBStoFreezeOutResultSaveItemRequest itemSaveRequest = new SgBStoFreezeOutResultSaveItemRequest();
            SgBStoOutNoticesItem noticesItem = noticesItems.get(0);
            saveRequest.setCpCStoreId(noticesItem.getCpCStoreId());
            saveRequest.setCpCStoreEcode(noticesItem.getCpCStoreEcode());
            saveRequest.setCpCStoreEname(noticesItem.getCpCStoreEname());
            BeanUtils.copyProperties(noticesItems.get(0), itemSaveRequest);
            itemSaveRequest.setId(-1L);
            itemSaveRequest.setQty(line.getQuantity());
            if (StringUtils.isNotBlank(line.getBatchCode())) {
                itemSaveRequest.setProduceDate(line.getBatchCode().replace("-", ""));
            } else {
                itemSaveRequest.setProduceDate(SgConstantsIF.DEFAULT_PRODUCE_DATE);
            }
            itemSaveRequestList.add(itemSaveRequest);
        }
        if (errorBuffer.length() > 0) {
            log.warn(LogUtil.format("库内加工冻结出库明细构建失败，异常条码:{}",
                    "SgBToStoreProcessConfirmFromWmsService.saveAndSubmitFreezeOutResult"), errorBuffer);
            throw new NDSException("库内加工冻结出库明细构建失败");
        }
        request.setItemRequests(itemSaveRequestList);

        ValueHolderV14<SgBStoFreezeOutBillResult> freezeOutBillResult = sgBStoFreezeOutResultSaveAndSubmitService.saveAndSubmit(request);
        if (Objects.nonNull(freezeOutBillResult)
                && freezeOutBillResult.isOK()
                && Objects.nonNull(freezeOutBillResult.getData())) {
            redisBillFtpKeyList.addAll(freezeOutBillResult.getData().getRedisKeyFtp());
        } else {
            log.warn(LogUtil.format("库内加工冻结出库失败，入参:{}，结果:{}",
                            "SgBToStoreProcessConfirmFromWmsService.saveAndSubmitFreezeOutResult"),
                    JSON.toJSONString(request), JSON.toJSONString(freezeOutBillResult));
            throw new NDSException("库内加工冻结出库失败");
        }
    }

    /**
     * 报文中的残次与出库通知单明细中的残次必须一致
     *
     * @param outItems        出库明细
     * @param outNoticesItems 出库通知单明细
     */
    private void checkOutItems(List<ReturnItem> outItems, List<SgBStoOutNoticesItem> outNoticesItems) {
        List<String> outItemCodes = ListUtils.emptyIfNull(outItems).stream()
                .map(o -> o.getInventoryType() + SgConstantsIF.MAP_KEY_DIVIDER + o.getItemCode())
                .collect(Collectors.toList());
        List<String> noticeSkus = ListUtils.emptyIfNull(outNoticesItems).stream()
                .map(o -> o.getStorageType() + SgConstantsIF.MAP_KEY_DIVIDER + o.getPsCSkuEcode())
                .collect(Collectors.toList());

        List<String> outItemCodes2 = outItemCodes.stream()
                .filter(item -> !noticeSkus.contains(item)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outItemCodes2)) {
            log.info(LogUtil.format("报文有但是出库通知单明细没有的明细列表:{},",
                    "SgBToStoreProcessConfirmFromWmsService.checkOutItems", JSONObject.toJSONString(outItemCodes2)));
            throw new NDSException("报文有但是出库通知单明细没有的明细列表:" + outItemCodes2);
        }

        List<String> noticeSkus2 = noticeSkus.stream()
                .filter(item -> !outItemCodes.contains(item)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noticeSkus2)) {
            log.info(LogUtil.format("出库通知单明细有但是报文没有的明细列表:{},",
                    "SgBToStoreProcessConfirmFromWmsService.checkOutItems", JSONObject.toJSONString(noticeSkus2)));
            throw new NDSException("出库通知单明细有但是报文没有的明细列表:" + noticeSkus2);
        }
    }

    /**
     * check判断是否有同批次同条码的数据
     *
     * @param sgStoOutResultSaveRequest sgStoOutResultSaveRequest
     * @return SgBStoOutResultBillSaveRequest
     */
    private SgBStoOutResultBillSaveRequest checkOutResultSaveRequest(SgBStoOutResultBillSaveRequest sgStoOutResultSaveRequest) {
        List<SgBStoOutResultItemSaveRequest> outItemResultSaveRequestList = sgStoOutResultSaveRequest.getOutItemResultSaveRequestList();

        log.info(LogUtil.format("SgBToStoreProcessConfirmFromWmsService.checkOutResultSaveRequest.outItemResultSaveRequestList.size{}",
                "SgBToStoreProcessConfirmFromWmsService.checkOutResultSaveRequest"), outItemResultSaveRequestList.size());

        if (CollectionUtils.isNotEmpty(outItemResultSaveRequestList)) {
            List<SgBStoOutResultItemSaveRequest> mergeList = new ArrayList<>();
            Map<String, List<SgBStoOutResultItemSaveRequest>> map = outItemResultSaveRequestList.stream().collect(Collectors.groupingBy(x -> x.getProduceDate() + x.getPsCSkuEcode()));
            for (String key : map.keySet()) {
                List<SgBStoOutResultItemSaveRequest> itemSaveRequestList = map.get(key);
                SgBStoOutResultItemSaveRequest itemSaveRequest = itemSaveRequestList.get(0);
                if (itemSaveRequestList.size() > 1) {
                    BigDecimal totQty = itemSaveRequestList.stream().map(SgBStoOutResultItemSaveRequest::getQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    itemSaveRequest.setQty(totQty);
                }
                mergeList.add(itemSaveRequest);
            }
            log.info(LogUtil.format("SgBToStoreProcessConfirmFromWmsService.checkOutResultSaveRequest.mergeList.size{}",
                    "SgBToStoreProcessConfirmFromWmsService.mergeList"), mergeList.size());
            sgStoOutResultSaveRequest.setOutItemResultSaveRequestList(mergeList);
        }

        return sgStoOutResultSaveRequest;
    }

    /**
     * 构建入库通知单
     */
    private SgBStoInNoticesBillSaveRequest buildSgBStoInNotices(List<ReturnItem> inItems,
                                                                Map<String, PsCProSkuResult> skuInfo,
                                                                SgBStoOutNotices notices,
                                                                List<SgBStoOutNoticesItem> outNoticesItems, String wmsBillCode) {
        SgBStoInNoticesBillSaveRequest stoInNoticesBillSaveRequest = new SgBStoInNoticesBillSaveRequest();
        SgBStoInNoticesBillSaveRequest.SgBStoInNoticesSaveRequest inNoticesSaveRequest =
                new SgBStoInNoticesBillSaveRequest.SgBStoInNoticesSaveRequest();
        List<SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest> inNoticesItemSaveRequests =
                Lists.newArrayList();

        SgBStoOutNoticesItem noticesItem = outNoticesItems.get(0);
        // 构建入库通知单主表
        BeanUtils.copyProperties(notices, inNoticesSaveRequest);
        inNoticesSaveRequest.setWmsBillNo(wmsBillCode);
        inNoticesSaveRequest.setSourceBillId(notices.getSourceBillId());
        inNoticesSaveRequest.setSourceBillNo(notices.getSourceBillNo());
        inNoticesSaveRequest.setSourceBillType(notices.getSourceBillType());
        inNoticesSaveRequest.setIsPassWms(SgStoreConstants.IS_PASS_THIRD_PARTY_N);
        inNoticesSaveRequest.setInType(SgStoreConstantsIF.OUT_TYPE_BIG_GOODS);//入库类型
        inNoticesSaveRequest.setSourceBillSourceBillType(notices.getSourceBillSourceBillType());

        StringBuffer skuInfoBuffer = null;
        // 构建入库通知单明细
        for (ReturnItem line : inItems) {
            PsCProSkuResult psProSkuResult = skuInfo.get(line.getItemCode());
            if (Objects.isNull(psProSkuResult)) {
                if (skuInfoBuffer == null) {
                    skuInfoBuffer = new StringBuffer();
                }
                skuInfoBuffer.append(line.getItemCode());
                continue;
            }
            SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest stoInNoticesItemSaveRequest =
                    new SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest();
            stoInNoticesItemSaveRequest.setQty(line.getQuantity());
            stoInNoticesItemSaveRequest.setQtyIn(BigDecimal.ZERO);
            stoInNoticesItemSaveRequest.setPsCSkuId(psProSkuResult.getId());
            if (StringUtils.isNotBlank(line.getBatchCode())) {
                stoInNoticesItemSaveRequest.setProduceDate(line.getBatchCode().replace("-", ""));
            } else {
                stoInNoticesItemSaveRequest.setProduceDate(SgConstantsIF.DEFAULT_PRODUCE_DATE);
            }
            stoInNoticesItemSaveRequest.setCpCStoreEcode(noticesItem.getCpCStoreEcode());
            stoInNoticesItemSaveRequest.setCpCStoreId(noticesItem.getCpCStoreId());
            stoInNoticesItemSaveRequest.setCpCStoreEname(noticesItem.getCpCStoreEname());
            inNoticesItemSaveRequests.add(stoInNoticesItemSaveRequest);
        }
        if (StringUtils.isNotBlank(skuInfoBuffer)) {
            AssertUtils.logAndThrow("构建入库通知单失败,失败原因:" + "条码编码" + skuInfoBuffer.toString() + "在【商品SKU】中不存在");
        }

        stoInNoticesBillSaveRequest.setInNoticesSaveRequest(inNoticesSaveRequest);
        stoInNoticesBillSaveRequest.setInNoticesItemSaveRequests(inNoticesItemSaveRequests);
        return stoInNoticesBillSaveRequest;
    }

    /**
     * 构建逻辑入库单
     *
     * @return SgBStoInResultBillSaveRequest
     */
    private List<SgBStoInResultBillSaveRequest> buildSgBStoInResult(SgBStoInNoticesBillSaveResult inNoticesV14Data, String wmsBillCode) {
        List<SgBStoInResultBillSaveRequest> stoInSaveRequest = Lists.newArrayList();
        SgBStoInNotices stoInNotices = sgStoInNoticesMapper.selectById(inNoticesV14Data.getId());
        List<SgBStoInNoticesItem> stoInNoticesItems =
                sgStoInNoticesItemMapper.selectList(new LambdaQueryWrapper<SgBStoInNoticesItem>()
                        .eq(SgBStoInNoticesItem::getSgBStoInNoticesId, inNoticesV14Data.getId()));
        Map<Long, List<SgBStoInNoticesItem>> storeItemMap =
                stoInNoticesItems.stream().collect(Collectors.groupingBy(SgBStoInNoticesItem::getCpCStoreId));
        for (List<SgBStoInNoticesItem> inNoticesItems : storeItemMap.values()) {
            SgBStoInNoticesItem inNoticesItem = inNoticesItems.get(0);
            SgBStoInResultBillSaveRequest inResultBillSaveRequest = new SgBStoInResultBillSaveRequest();

            SgBStoInResultSaveRequest inResultSaveRequest = new SgBStoInResultSaveRequest();
            List<SgBStoInResultItemSaveRequest> inItemResultSaveRequestList = Lists.newArrayList();
            // 构建入库通知单主表
            BeanUtils.copyProperties(stoInNotices, inResultSaveRequest);
            inResultSaveRequest.setId(-1L);
            // 构建逻辑入库单主表
            inResultSaveRequest.setWmsBillNo(wmsBillCode);
            inResultSaveRequest.setCpCStoreId(inNoticesItem.getCpCStoreId());
            inResultSaveRequest.setCpCStoreEcode(inNoticesItem.getCpCStoreEcode());
            inResultSaveRequest.setCpCStoreEname(inNoticesItem.getCpCStoreEname());
            inResultSaveRequest.setSgBStoInNoticesId(inNoticesV14Data.getId());
            inResultSaveRequest.setSgBStoInNoticesNo(inNoticesV14Data.getBillNo());
            inResultSaveRequest.setIsLast(SgConstants.IS_LAST_YES);

            // 构建逻辑入库单明细
            for (SgBStoInNoticesItem item : inNoticesItems) {
                SgBStoInResultItemSaveRequest inResultItemSaveRequest = new SgBStoInResultItemSaveRequest();
                BeanUtils.copyProperties(item, inResultItemSaveRequest);
                inResultItemSaveRequest.setId(-1L);
                inResultItemSaveRequest.setSourceBillItemId(item.getId());
                inItemResultSaveRequestList.add(inResultItemSaveRequest);
            }
            inResultBillSaveRequest.setLoginUser(SystemUserResource.getRootUser());
            inResultBillSaveRequest.setInResultSaveRequest(inResultSaveRequest);
            inResultBillSaveRequest.setInItemResultSaveRequestList(inItemResultSaveRequestList);
            stoInSaveRequest.add(inResultBillSaveRequest);
        }
        return stoInSaveRequest;
    }

    /**
     * 逻辑出库单主子表信息
     *
     * @param skuInfo    条码map
     * @param notices    出库通知单
     * @param jsonObject 封装报文
     * @return SgBStoOutResultSaveRequest
     */
    private SgBStoOutResultBillSaveRequest buildSgStoOutResultSaveRequest(List<ReturnItem> outItems,
                                                                          Map<String, PsCProSkuResult> skuInfo,
                                                                          SgBStoOutNotices notices,
                                                                          Map<String, List<SgBStoOutNoticesItem>> outNoticesItemSkuMap,
                                                                          JSONObject jsonObject) {
        SgBStoOutResultBillSaveRequest request = new SgBStoOutResultBillSaveRequest();
        request.setSgBStoOutNoticesId(notices.getId());
        request.setLoginUser(SystemUserResource.getRootUser());
        request.setObjId(-1L);
        //orderCompleteTime
        Date orderCompleteTime = jsonObject.getDate("orderCompleteTime");
        // WMS单据编号
        String wmsBillCode = jsonObject.getString("processOrderId");
        String remark = jsonObject.getString("remark");
        SgBStoOutResultSaveRequest saveRequest = new SgBStoOutResultSaveRequest();
        saveRequest.setIsLast(SgConstants.IS_LAST_NO);
        saveRequest.setSourceBillId(notices.getSourceBillId());
        saveRequest.setOutTime(orderCompleteTime);
        saveRequest.setWmsBillNo(wmsBillCode);
        saveRequest.setRemark(remark);
        saveRequest.setSourceBillType(notices.getSourceBillType());
        saveRequest.setSourceBillNo(notices.getSourceBillNo());
        saveRequest.setSourceBillDate(notices.getSourceBillDate());
        saveRequest.setSgBStoOutNoticesId(notices.getId());
        saveRequest.setSgBStoOutNoticesNo(notices.getBillNo());
        saveRequest.setReceiverName(notices.getReceiverName());
        saveRequest.setReceiverEcode(notices.getReceiverEcode());
        saveRequest.setSourceBillSourceBillType(notices.getSourceBillSourceBillType());
        //明细表信息集合
        List<SgBStoOutResultItemSaveRequest> itemSaveRequestList = new ArrayList<>();
        StringBuffer skuInfoBuffer = null;
        StringBuffer outNoticeItemBuffer = null;
        for (ReturnItem line : outItems) {
            if (!StoreTypeEnum.ZP.getValue().equals(line.getInventoryType())) {
                log.info(LogUtil.format("非正品，不处理:{}", line.getItemCode()));
                continue;
            }
            PsCProSkuResult psProSkuResult = skuInfo.get(line.getItemCode());
            if (Objects.isNull(psProSkuResult)) {
                if (skuInfoBuffer == null) {
                    skuInfoBuffer = new StringBuffer();
                }
                skuInfoBuffer.append(line.getItemCode());
                continue;
            }
            List<SgBStoOutNoticesItem> noticesItems = outNoticesItemSkuMap.get(line.getItemCode() + SgConstantsIF.MAP_KEY_DIVIDER + line.getInventoryType());
            if (CollectionUtils.isEmpty(noticesItems)) {
                if (outNoticeItemBuffer == null) {
                    outNoticeItemBuffer = new StringBuffer();
                }
                outNoticeItemBuffer.append(line.getItemCode());
                continue;
            }

            SgBStoOutResultItemSaveRequest itemSaveRequest = new SgBStoOutResultItemSaveRequest();
            SgBStoOutNoticesItem noticesItem = noticesItems.get(0);
            saveRequest.setCpCStoreId(noticesItem.getCpCStoreId());
            saveRequest.setCpCStoreEcode(noticesItem.getCpCStoreEcode());
            saveRequest.setCpCStoreEname(noticesItem.getCpCStoreEname());
            BeanUtils.copyProperties(noticesItems.get(0), itemSaveRequest);
            itemSaveRequest.setId(-1L);
            itemSaveRequest.setQty(line.getQuantity());
            if (StringUtils.isNotBlank(line.getBatchCode())) {
                itemSaveRequest.setProduceDate(line.getBatchCode().replace("-", ""));
            } else {
                itemSaveRequest.setProduceDate(SgConstantsIF.DEFAULT_PRODUCE_DATE);
            }
            itemSaveRequestList.add(itemSaveRequest);
        }
        if (CollectionUtils.isEmpty(itemSaveRequestList)) {
            AssertUtils.logAndThrow("构建逻辑出库单失败,失败原因:没有正品的出库明细");
        }

        if (StringUtils.isNotBlank(skuInfoBuffer)) {
            AssertUtils.logAndThrow("构建出库通知单失败,失败原因:" + "条码编码" + skuInfoBuffer.toString() + "在【商品SKU】中不存在");
        }
        if (StringUtils.isNotBlank(outNoticeItemBuffer)) {
            AssertUtils.logAndThrow("构建出库通知单失败,失败原因:" + "条码编码" + outNoticeItemBuffer.toString() + "在【出库通知单明细】中不存在");
        }
        request.setOutResultSaveRequest(saveRequest);
        request.setOutItemResultSaveRequestList(itemSaveRequestList);
        request.setIsAutoOut(SgConstants.IS_AUTO_Y);
        return request;
    }

    /**
     * 中间表更新参数赋值
     *
     * @param outNoticeResult 中间表
     * @param message         转换信息
     * @param isSuccess       是否成功
     */
    private void setUpdateParam(SgBWmsToStoreprocessConfirmResult outNoticeResult, String message, boolean isSuccess) {
        outNoticeResult.setTransformationData(new Date());
        if (isSuccess) {
            outNoticeResult.setFailedReason(message);
            outNoticeResult.setTransformStatus(2);
        } else {
            Integer failedCount = outNoticeResult.getFailedCount();
            failedCount = Optional.ofNullable(failedCount).orElse(0);
            outNoticeResult.setFailedCount(failedCount + 1);
            outNoticeResult.setFailedReason(StringUtils.length(message) > 500 ? message.substring(0, 500) : message);
            outNoticeResult.setTransformStatus(1);
        }
    }

    @Data
    private static class ReturnItem {

        /**
         * 商品编码
         */
        private String itemCode;

        /**
         * 商品名称
         */
        private String itemName;

        /**
         * 库存类型(ZP=正品;CC=残次;JS=机损;XS=箱损;默认为ZP)
         */
        private String inventoryType;

        /**
         * 批次编码
         */
        private String batchCode;
        /**
         * 数量
         */
        private BigDecimal quantity;

    }
}