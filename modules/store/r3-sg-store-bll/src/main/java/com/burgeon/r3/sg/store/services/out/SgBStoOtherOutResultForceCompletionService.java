package com.burgeon.r3.sg.store.services.out;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.freeze.out.SgBStoFreezeOut;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOtherOutResult;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOtherOutResultItem;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.OrderTypeEnum;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.freeze.out.SgBStoFreezeOutMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOtherOutResultItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOtherOutResultMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesMapper;
import com.burgeon.r3.sg.store.model.request.freeze.out.SgBStoFreezeOutRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOtherOutResultForceCompletionItemRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOtherOutResultForceCompletionRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutForceCompleteRequest;
import com.burgeon.r3.sg.store.model.result.freeze.out.SgBStoFreezeOutBillResult;
import com.burgeon.r3.sg.store.model.result.in.SgBStoForceCompletionResult;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutBillForceCompleteResult;
import com.burgeon.r3.sg.store.services.freeze.out.SgBStoFreezeOutVoidService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/23
 * 其他出库单强制完成服务
 */
@Slf4j
@Component
public class SgBStoOtherOutResultForceCompletionService {

    @Autowired
    private SgBStoOtherOutResultMapper otherOutResultMapper;
    @Autowired
    private SgBStoOtherOutResultItemMapper otherOutResultItemMapper;
    @Autowired
    private SgBStoOutForceCompleteService outForceCompleteService;
    @Autowired
    private SgBStoOutNoticesMapper sgStoOutNoticesMapper;
    @Autowired
    private SgBStoFreezeOutMapper sgBStoFreezeOutMapper;
    @Autowired
    private SgBStoFreezeOutVoidService sgBStoFreezeOutVoidService;

    /**
     * 其他出库单强制完成服务
     *
     * @param request 入参
     * @return ValueHolderV14
     */
    public ValueHolderV14<SgBStoForceCompletionResult> otherOutResultForceCompletion(SgBStoOtherOutResultForceCompletionRequest request) {
        log.info(LogUtil.format("SgBStoOtherOutResultForceCompletionService.otherOutResultForceCompletion start",
                "otherOutResultForceCompletion"));

        ValueHolderV14<SgBStoForceCompletionResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "其他出库单强制完成成功！");

        try {
            SgBStoOtherOutResultForceCompletionService bean =
                    ApplicationContextHandle.getBean(SgBStoOtherOutResultForceCompletionService.class);
            return bean.forceCompletion(request);
        } catch (Exception e) {
            log.error(LogUtil.format("SgBStoOtherOutResultForceCompletionService.otherOutResultForceCompletion" +
                            ".error:{}",
                    "SgBStoInForceCompletionService.otherOutResultForceCompletion"), e.getMessage());
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }

        return v14;
    }

    /**
     * 其他出库单强制完成服务
     * 带事务
     *
     * @param request 入参
     * @return ValueHolderV14
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgBStoForceCompletionResult> forceCompletion(SgBStoOtherOutResultForceCompletionRequest request) {
        log.info(LogUtil.format("SgBStoOtherOutResultForceCompletionService.forceCompletion request:{}",
                "SgBStoOtherOutResultForceCompletionService.forceCompletion"), JSONObject.toJSONString(request));
        ValueHolderV14<SgBStoForceCompletionResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "强制完成成功！");

        User user = request.getLoginUser();
        List<String> redisFtpKey = request.getRedisFtpKey();
        if (Objects.isNull(redisFtpKey)) {
            redisFtpKey = new ArrayList<>();
        }

        ValueHolderV14<SgBStoOtherOutResult> checkV14 = checkParam(request, user);
        if (!checkV14.isOK()) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(checkV14.getMessage());
            return v14;
        }
        SgBStoOtherOutResult sgStoOtherOutResult = checkV14.getData();
        //20220815   如果OMS已经出库完成， 则直接返回，强制完成成功，更新结案状态=已结案
        if (sgStoOtherOutResult == null) {
            return v14;
        }

        String lockKey = SgConstants.SG_B_STO_OTHER_OUT_RESULT + ":" + sgStoOtherOutResult.getId();
        SgRedisLockUtils.lock(lockKey);
        try {
            updateClosedStatus(user, sgStoOtherOutResult);

            //调用【强制完成逻辑占用单服务】
            SgBStoOutForceCompleteRequest completionRequest = new SgBStoOutForceCompleteRequest();
            completionRequest.setRedisBillFtpKeyList(redisFtpKey);
            completionRequest.setLoginUser(user);
            completionRequest.setSourceBillId(sgStoOtherOutResult.getId());
            completionRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_OTHER_OUT_RESULT);
            ValueHolderV14<SgBStoOutBillForceCompleteResult> valueHolderV14 =
                    outForceCompleteService.forceCompleteSgBStoOut(completionRequest);
            if (!valueHolderV14.isOK()) {
                AssertUtils.logAndThrow("调用逻辑占用单强制完成服务失败：" + valueHolderV14.getMessage());
            }

            forceCompletionForFreezed(sgStoOtherOutResult, user, redisFtpKey);

            //返回值
            redisFtpKey.addAll(valueHolderV14.getData().getRedisBillFtpKeyList());
            SgBStoForceCompletionResult result = new SgBStoForceCompletionResult();
            result.setRedisFtpKey(redisFtpKey);
            v14.setData(result);

        } catch (Exception e) {
            log.error(LogUtil.format("SgBStoOtherOutResultForceCompletionService.forceCompletion.error:{}",
                    "SgBStoOtherOutResultForceCompletionService.error"), Throwables.getStackTraceAsString(e));
            // 回滚库存
            StorageBasicUtils.rollbackStorage(redisFtpKey, user);
            AssertUtils.logAndThrow("其他出库单强制完成失败：" + e.getMessage());
        } finally {
            SgRedisLockUtils.unlock(lockKey, log, this.getClass().getName());
        }

        return v14;
    }

    /**
     * 【其他出库单-生产流程订单-换箱加工】作废冻结占用单
     *
     * @param sgStoOtherOutResult 其他出库单
     * @param loginUser           用户信息
     * @param redisFtpKey         回滚key
     */
    private void forceCompletionForFreezed(SgBStoOtherOutResult sgStoOtherOutResult, User loginUser, List<String> redisFtpKey) {
        if (!SgConstants.OTHER_OUT_RESULT_PRODUCTION_PROCESS_TYPE_CHANGE_BOX.equals(sgStoOtherOutResult.getProcessType())) {
            return;
        }

        int count = sgBStoFreezeOutMapper.selectCount(new QueryWrapper<SgBStoFreezeOut>().lambda()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                .in(SgBStoFreezeOut::getBillStatus,
                        SgStoreConstants.BILL_STO_FREEZE_OUT_STATUS_CREATE)
                .eq(SgBStoFreezeOut::getSourceBillId, sgStoOtherOutResult.getId())
                .eq(SgBStoFreezeOut::getSourceBillType, OrderTypeEnum.SOURCE_BILL_TYPE_160.getValue()));
        if (count == 0) {
            log.info(LogUtil.format("该单据没有【创建】状态的冻结占用单，不用强制完成冻结占用单，单据ID:{},单号:{}",
                            "SgBStoOtherOutResultForceCompletionService.forceCompletionForFreezed"),
                    sgStoOtherOutResult.getId(), sgStoOtherOutResult.getBillNo());
            return;
        }

        SgBStoFreezeOutRequest request = new SgBStoFreezeOutRequest();
        request.setLoginUser(loginUser);
        request.setSourceBillId(sgStoOtherOutResult.getId());
        request.setSourceBillType(OrderTypeEnum.SOURCE_BILL_TYPE_160.getValue());
        request.setRedisKeyFtp(redisFtpKey);
        ValueHolderV14<SgBStoFreezeOutBillResult> voidResult = sgBStoFreezeOutVoidService.freezeOutVoid(request);
        if (!voidResult.isOK()) {
            log.warn(LogUtil.format("作废冻结占用单失败，单据ID:{},单号:{}，作废结果:{}",
                            "SgBStoOtherOutResultForceCompletionService.forceCompletionForFreezed"),
                    sgStoOtherOutResult.getId(), sgStoOtherOutResult.getBillNo(), JSON.toJSONString(voidResult));
            throw new NDSException("【强制完成】作废冻结占用单失败");
        }
    }

    private void updateClosedStatus(User user, SgBStoOtherOutResult sgStoOtherOutResult) {
        //不触发明细更新，不更新汇总信息
        SgBStoOtherOutResult update = new SgBStoOtherOutResult();
        update.setId(sgStoOtherOutResult.getId());
        StorageUtils.setBModelDefalutDataByUpdate(update, user);
        update.setStatus(SgConstants.SG_B_STO_OTHER_OUT_RESULT_STATUS_ALL_OUT);
        update.setClosedStatus(SgConstants.CLOSE_STATUS_CLOSE);
        update.setClosedTime(new Date());
        update.setClosedId(Long.valueOf(user.getId()));
        update.setClosedEname(user.getEname());
        update.setClosedName(user.getName());
        otherOutResultMapper.updateById(update);
    }

    /**
     * 校验参数
     *
     * @param request request
     * @return ValueHolderV14<SgBStoOtherOutResult>
     */
    private ValueHolderV14<SgBStoOtherOutResult> checkParam(SgBStoOtherOutResultForceCompletionRequest request,
                                                            User user) {
        ValueHolderV14<SgBStoOtherOutResult> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "校验成功!");
        String stoOtherOutResultBillNo = request.getSgBStoOtherOutResultBillNo();
        String sourceBillNo = request.getSourceBillNo();
        if (StringUtils.isNotEmpty(request.getSgBStoOutNoticesBillNo())) {
            SgBStoOutNotices notices = sgStoOutNoticesMapper.selectOne(new LambdaQueryWrapper<SgBStoOutNotices>()
                    .select(SgBStoOutNotices::getSourceBillNo)
                    .eq(SgBStoOutNotices::getBillNo, request.getSgBStoOutNoticesBillNo()));
            AssertUtils.cannot(notices == null, "出库通知单" + request.getSgBStoOutNoticesBillNo() + "记录不存在");
            stoOtherOutResultBillNo = notices.getSourceBillNo();
        }
        boolean haveBillNo = StringUtils.isNotEmpty(stoOtherOutResultBillNo);
        if (!haveBillNo && StringUtils.isEmpty(sourceBillNo)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "单据编号不能为空!");
        }
        List<SgBStoOtherOutResultForceCompletionItemRequest> itemRequestList = request.getItemRequestList();
        if (CollectionUtils.isEmpty(itemRequestList)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "其他出库单强制完成服务入参明细不能为空!");
        }

        SgBStoOtherOutResult sgStoOtherOutResult =
                otherOutResultMapper.selectOne(new LambdaQueryWrapper<SgBStoOtherOutResult>()
                        .eq(haveBillNo, SgBStoOtherOutResult::getBillNo, stoOtherOutResultBillNo)
                        .eq(!haveBillNo, SgBStoOtherOutResult::getSourceBillNo, sourceBillNo)
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (sgStoOtherOutResult == null) {
            return new ValueHolderV14<>(ResultCode.FAIL, "当前记录不存在，不允许强制完成");
        }
        Integer status = sgStoOtherOutResult.getStatus();
        Integer closedStatus = sgStoOtherOutResult.getClosedStatus();
        if (SgConstants.CLOSE_STATUS_CLOSE.equals(closedStatus)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "当前记录已强制完成，不允许强制完成!");
        }
        if (SgConstants.SG_B_STO_OTHER_OUT_RESULT_STATUS_UN_SUBMIT.equals(status)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "当前记录未审核，不允许强制完成!");
        } else if (SgConstants.SG_B_STO_OTHER_OUT_RESULT_STATUS_ALL_OUT.equals(status)) {
            updateClosedStatus(user, sgStoOtherOutResult);
            //20220815   如果OMS已经出库完成， 则直接返回，强制完成成功，更新结案状态=已结案
            return new ValueHolderV14<>(ResultCode.SUCCESS, "当前记录已出库完成，不允许强制完成!");
        }
        JSONArray error = new JSONArray();
        Map<String, BigDecimal> itemRequestMap = new HashMap<>(16);
        List<String> skuList = new ArrayList<>();

        for (SgBStoOtherOutResultForceCompletionItemRequest itemRequest : itemRequestList) {
            skuList.add(itemRequest.getPsCSkuEcode());
            if (itemRequestMap.containsKey(itemRequest.getPsCSkuEcode())) {
                BigDecimal outQty = itemRequestMap.get(itemRequest.getPsCSkuEcode());
                itemRequestMap.put(itemRequest.getPsCSkuEcode(), outQty.add(itemRequest.getOutQty()));
            } else {
                itemRequestMap.put(itemRequest.getPsCSkuEcode(), itemRequest.getOutQty());
            }

        }
        //分批次去比较明细是否一致
        List<List<String>> pageSkuCodeList = StorageUtils.getPageList(skuList, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);

        for (List<String> page : pageSkuCodeList) {
            List<SgBStoOtherOutResultItem> sgStoOtherOutResultItems =
                    otherOutResultItemMapper.selectList(new LambdaQueryWrapper<SgBStoOtherOutResultItem>()
                            .in(SgBStoOtherOutResultItem::getPsCSkuEcode, page)
                            .eq(SgBStoOtherOutResultItem::getSgBStoOtherOutResultId, sgStoOtherOutResult.getId()));
            if (CollectionUtils.isEmpty(sgStoOtherOutResultItems)) {
                error.addAll(JSONObject.parseArray(JSONObject.toJSONString(page)));
            } else {
                Map<String, List<SgBStoOtherOutResultItem>> itemMap = sgStoOtherOutResultItems.stream()
                        .collect(Collectors.groupingBy(SgBStoOtherOutResultItem::getPsCSkuEcode));
                for (String skuEcode : page) {
                    //先比较记录
                    if (!itemMap.containsKey(skuEcode)) {
                        error.add(skuEcode);
                        continue;
                    }

                    List<SgBStoOtherOutResultItem> sgStoOtherOutResultItemList = itemMap.get(skuEcode);
                    //在比较出库数量
                    BigDecimal itemOutQty = sgStoOtherOutResultItemList.stream().map(SgBStoOtherOutResultItem::getQtyOut).
                            reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    BigDecimal outQty = itemRequestMap.get(skuEcode);
                    if (outQty.compareTo(itemOutQty) != 0) {
                        error.add(skuEcode);
                    }
                }
            }
        }
        if (error.size() > 0) {
            return new ValueHolderV14<>(ResultCode.FAIL,
                    "条码：" + JSONObject.toJSONString(error) + "出库数量不一致，不允许强制完成！");
        }
        vh.setData(sgStoOtherOutResult);
        return vh;
    }
}
