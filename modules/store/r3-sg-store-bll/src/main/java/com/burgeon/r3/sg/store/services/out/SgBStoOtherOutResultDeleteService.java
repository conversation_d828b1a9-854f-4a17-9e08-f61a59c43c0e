package com.burgeon.r3.sg.store.services.out;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOtherOutResult;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOtherOutResultItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOtherOutResultItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOtherOutResultMapper;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOtherOutResultDeleteRequest;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/28
 */
@Slf4j
@Component
public class SgBStoOtherOutResultDeleteService {

    @Autowired
    private SgBStoOtherOutResultMapper mapper;
    @Autowired
    private SgBStoOtherOutResultItemMapper itemMapper;
    @Autowired
    private SgBStoOtherOutResultSaveService sgStoOtherOutResultSaveService;

    /**
     * 其他出库单保存（前端页面保存入口）
     *
     * @param session session
     * @return return
     */
    public ValueHolder delete(QuerySession session) {
        SgBStoOtherOutResultDeleteRequest request = R3ParamUtils.parseSaveObject(session, SgBStoOtherOutResultDeleteRequest.class);
        request.setR3(true);
        SgBStoOtherOutResultDeleteService service = ApplicationContextHandle.getBean(this.getClass());
        return R3ParamUtils.convertV14WithResult(service.delete(request));
    }

    /**
     * 其他出库单删除
     *
     * @param request 删除入参
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> delete(SgBStoOtherOutResultDeleteRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start SgBStoOtherOutResultDeleteService.delete:param={}",
                    "SgBStoOtherOutResultDeleteService.delete", request.getBillNo()), JSONObject.toJSONString(request));
        }
        String lockKey = SgConstants.SG_B_STO_OTHER_OUT_RESULT + ":" + request.getObjId();
        SgRedisLockUtils.lock(lockKey);
        try {

            SgBStoOtherOutResult otherOutResult = checkParams(request);
            LambdaQueryWrapper<SgBStoOtherOutResultItem> itemWrapper = new LambdaQueryWrapper<>();
            // 主表删除 || 明细删除
            if (CollectionUtils.isNotEmpty(request.getItemIds())) {
                // 先获取明细，处理数量
                itemWrapper.in(SgBStoOtherOutResultItem::getId, request.getItemIds());
                List<SgBStoOtherOutResultItem> itemList = itemMapper.selectList(itemWrapper);

                AssertUtils.isTrue(itemList.size() == request.getItemIds().size(), "当前记录已不存在！");

            } else if (CollectionUtils.isNotEmpty(request.getPsCProIds())) {
                itemWrapper.in(SgBStoOtherOutResultItem::getPsCProId, request.getPsCProIds());
            }
            // 删除明细
            itemMapper.delete(itemWrapper);
            List<SgBStoOtherOutResultItem> otherOutResultItems =
                    itemMapper.selectList(new LambdaQueryWrapper<SgBStoOtherOutResultItem>()
                            .eq(SgBStoOtherOutResultItem::getSgBStoOtherOutResultId, otherOutResult.getId()));
            // 设置主表属性
            StorageUtils.setBModelDefalutDataByUpdate(otherOutResult, request.getLoginUser());
            sgStoOtherOutResultSaveService.recountOrderAmount(otherOutResult,otherOutResultItems,null);
            mapper.updateById(otherOutResult);
        } catch (Exception e) {
            AssertUtils.logAndThrowException("其他出库单删除异常", e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKey, log, this.getClass().getName());
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "删除成功！");
    }


    /**
     * 其他出库单删除前check
     *
     * @param request 删除入参
     */
    public SgBStoOtherOutResult checkParams(SgR3BaseRequest request) {
        SgBStoOtherOutResult otherOutResult = mapper.selectById(request.getObjId());

        AssertUtils.cannot(Objects.isNull(otherOutResult), "当前记录已不存在！");
        AssertUtils.cannot(SgConstants.IS_ACTIVE_N.equalsIgnoreCase(otherOutResult.getIsactive()),
                "当前记录已作废，不允许删除明细！");
        AssertUtils.cannot(SgConstants.SG_B_STO_OTHER_OUT_RESULT_STATUS_VOID.equals(otherOutResult.getStatus())
                , "当前记录已作废，不允许删除明细！");
        AssertUtils.cannot(SgConstants.SG_B_STO_OTHER_OUT_RESULT_STATUS_SUBMIT_NO_OUT.equals(otherOutResult.getStatus()),
                "当前记录已审核，不允许删除明细！",
                request.getLoginUser().getLocale());
        AssertUtils.cannot(SgConstants.SG_B_STO_OTHER_OUT_RESULT_STATUS_ALL_OUT.equals(otherOutResult.getStatus())
                        || SgConstants.SG_B_STO_OTHER_OUT_RESULT_STATUS_PART_OUT.equals(otherOutResult.getStatus()),
                "当前记录已出库，不允许删除明细！",
                request.getLoginUser().getLocale());


        return otherOutResult;
    }
}
