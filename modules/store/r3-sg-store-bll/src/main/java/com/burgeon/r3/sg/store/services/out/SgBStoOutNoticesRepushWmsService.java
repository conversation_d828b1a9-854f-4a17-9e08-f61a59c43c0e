package com.burgeon.r3.sg.store.services.out;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.burgeon.r3.sg.core.enums.YesNoEnum;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesMapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2022/12/10 10:36
 * @Description
 */
@Slf4j
@Component
public class SgBStoOutNoticesRepushWmsService {

    @Resource
    private SgBStoOutNoticesMapper sgBStoOutNoticesMapper;

    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        JSONArray voidArray = R3ParamUtils.getIdArrayByParam(param);
        if (voidArray.size() == 1) {
            repushAction(voidArray.getLong(0));
        } else {
            int success = 0;
            int fail = 0;
            for (int i = 0; i < voidArray.size(); i++) {
                Long id = voidArray.getLong(i);
                try {
                    repushAction(id);
                    success++;
                } catch (Exception e) {
                    fail++;
                }
            }
            if (success != voidArray.size()) {
                return ValueHolderUtils.getFailValueHolder("出库通知单重推WMS成功" + success + "条,失败" + fail + "条");
            }
        }

        return ValueHolderUtils.getSuccessValueHolder("出库通知单重推WMS成功");
    }

    private void repushAction(Long id) {
        SgBStoOutNotices outNotices = sgBStoOutNoticesMapper.selectById(id);
        if (outNotices == null) {
            throw new NDSException("当前记录不存在！");
        }
        if (!(YesNoEnum.Y.getKey().equals(outNotices.getIsactive())
                && YesNoEnum.Y.getVal().equals(outNotices.getIsPassWms())
                && SgStoreConstants.WMS_UPLOAD_STATUTS_FAIL == outNotices.getWmsStatus()
                && outNotices.getWmsFailCount() >= 6L)) {
            throw new NDSException("当前状态不允许重推！");
        }
        SgBStoOutNotices updateOutNotices = new SgBStoOutNotices();
        updateOutNotices.setId(outNotices.getId());
        updateOutNotices.setWmsFailCount(0L);
        sgBStoOutNoticesMapper.updateById(updateOutNotices);
    }
}
