package com.burgeon.r3.sg.store.services.out;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.config.SgStorageMqConfig;
import com.burgeon.r3.sg.basic.model.result.SgStorageUpdateResult;
import com.burgeon.r3.sg.basic.services.SgCStoreQueryService;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.MQConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.mq.SgBShareOutMQReleaseItemRequest;
import com.burgeon.r3.sg.core.model.request.mq.SgBShareOutMQReleaseRequest;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOut;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItem;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutResult;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutResultItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutMapper;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutBillReleaseRequest;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutBillReleaseResult;
import com.burgeon.r3.sg.store.services.SgStoreStorageService;
import com.burgeon.r3.sg.store.services.update.SgStoreStorageBatchService;
import com.burgeon.r3.sg.store.utils.QtyUtils;
import com.burgeon.r3.sg.store.utils.ShareFtpUtils;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2021-04-12 15:53
 * @Description: 逻辑占用单 更新（释放）服务
 */

@Slf4j
@Component
public class SgBStoOutReleaseService {

    @Autowired
    private SgBStoOutMapper sgStoOutMapper;
    @Autowired
    private SgStoreStorageService storageService;
    @Autowired
    private SgBStoOutItemMapper sgStoOutItemMapper;
    @Autowired
    private SgCStoreQueryService queryService;

    //    @Autowired
//    private R3MqSendHelper r3MqSendHelper;
    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private PropertiesConf pconf;

    @Autowired
    SgStorageMqConfig sgStorageMqConfig;

    @Autowired
    private SgStoreStorageBatchService sgStoreStorageBatchService;

    /**
     * 更新（释放）逻辑占用单
     *
     * @param request 入参
     * @return 出参
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgBStoOutBillReleaseResult> releaseSgBStoOut(SgBStoOutBillReleaseRequest request) {

        checkParams(request);
        if (log.isDebugEnabled()) {
            // 逻辑占用单更新（释放）入参
            log.debug("Start SgBStoOutReleaseService.releaseSgBStoOut ReceiveParams:request:{};"
                    , JSONObject.toJSONString(request));
        }
        ValueHolderV14<SgBStoOutBillReleaseResult> v14 = new ValueHolderV14<>();
        //redis库存流水键
        List<String> redisBillFtpKeyList = new ArrayList<>();
        if (request.getRedisBillFtpKeyList() != null) {
            redisBillFtpKeyList = request.getRedisBillFtpKeyList();
        }

        SgBStoOutResult outResult = request.getOutResult();

        Long id = outResult.getSgBStoOutId();
        User user = request.getLoginUser();
        Locale locale = user.getLocale();


        //默认最后一次出库
        String last = outResult.getIsLast();
        if (StringUtils.isEmpty(last)) {
            last = SgConstants.IS_LAST_YES;
        }

        Boolean isEdge = Optional.ofNullable(request.getIsEdge()).orElse(Boolean.FALSE);
        boolean isLast = last.equals(SgConstants.IS_LAST_YES);
        Long sourceBillId = request.getSourceBillId();
        Integer sourceBillType = request.getSourceBillType();
        String lockKsy = SgConstants.SG_B_STO_OUT + ":" + sourceBillId + ":" + sourceBillType;

        log.info("Start SgBStoOutReleaseService releaseSgBStoOut sourceBillId = {},sourceBillType = {}",
                sourceBillId, sourceBillType);

        if (SgRedisLockUtils.lock(lockKsy)) {
            try {
                //更新逻辑占用单表
                SgBStoOut stoOut = sgStoOutMapper.selectById(id);
                AssertUtils.notNull(stoOut, "该记录已不存在！", user.getLocale());
                SgBStoOut update = new SgBStoOut();
                update.setId(stoOut.getId());
                update.setTotQtyPreout(isLast ? BigDecimal.ZERO : stoOut.getTotQtyPreout().subtract(outResult.getTotQtyOut()));
                Boolean isSplit = Optional.ofNullable(request.getIsSplit()).orElse(Boolean.FALSE);
                //判断是否是意哥调用 意哥调用不会更状态 拆单也不需要更新状态
                if (!isEdge && !isSplit) {
                    if (update.getTotQtyPreout().compareTo(BigDecimal.ZERO) == 0 || isLast) {
                        update.setBillStatus(SgStoreConstants.BILL_STO_IN_STATUS_ALL_IN);
                    } else {
                        update.setBillStatus(SgStoreConstants.BILL_STO_IN_STATUS_PART_IN);
                    }
                }

                StorageUtils.setBModelDefalutDataByUpdate(update, user);
                update.setModifierename(user.getEname());


                List<SgBStoOutItem> orgSgStoreOutItems;
                if (isSplit) {
                    orgSgStoreOutItems = sgStoOutItemMapper.selectList(new QueryWrapper<SgBStoOutItem>().lambda()
                            .eq(SgBStoOutItem::getSgBStoOutId, id)
                            .ne(SgBStoOutItem::getQtyPreout, BigDecimal.ZERO));
                } else {
                    orgSgStoreOutItems = sgStoOutItemMapper.selectList(new QueryWrapper<SgBStoOutItem>().lambda()
                            .eq(SgBStoOutItem::getSgBStoOutId, id)
                            .eq(SgBStoOutItem::getCpCStoreEcode, outResult.getCpCStoreEcode())
                            .ne(SgBStoOutItem::getQtyPreout, BigDecimal.ZERO));
                }

                AssertUtils.cannot(CollectionUtils.isEmpty(orgSgStoreOutItems), "明细不存在或无可释放量明细！");


                List<SgBStoOutResultItem> inResultItemList = request.getOutResultItemList();
                List<SgBStoOutItem> storageItemList = Lists.newArrayList();
                List<SgBShareOutMQReleaseItemRequest> mqReleaseItemRequests = new ArrayList<>();
                //处理明细
                boolean isUpdateQtyOut = !isEdge && !isSplit;
                List<Long> sgShareStoreIdList = request.getSgShareStoreIdList();
                Map<String, Long> skuIdMap = handleItem(inResultItemList, orgSgStoreOutItems, storageItemList,
                        mqReleaseItemRequests, sgShareStoreIdList, outResult, user, isLast, isUpdateQtyOut);

                //更新库存
                //共享 or 非共享 true = 共享  false = 非共享
                boolean isRetail = ShareFtpUtils.isShare(stoOut.getSourceBillType());
                v14 = updateStoragePreOut(stoOut, request,
                        storageItemList, isRetail, redisBillFtpKeyList, skuIdMap);

                SgBStoOutBillReleaseResult data = v14.getData();
                List<String> list = data.getRedisBillFtpKeyList();
                if (list != null) {
                    redisBillFtpKeyList.addAll(list);
                }
                data.setRedisBillFtpKeyList(redisBillFtpKeyList);
                //求总出库数量 总未释放量
                SgBStoOutItem item = sgStoOutItemMapper.selectSum(id);
                update.setTotQtyPreout(item.getQtyPreout());
                update.setTotQtyOut(item.getQtyOut());
                sgStoOutMapper.updateById(update);

                //拆单自己释放共享占用单
                if (ShareFtpUtils.isShare(stoOut.getSourceBillType()) &&
                        !isSplit) {
                    SgBShareOutMQReleaseRequest mqReleaseRequest = new SgBShareOutMQReleaseRequest();
                    mqReleaseRequest.setSgStoId(id);
                    mqReleaseRequest.setLoginUser(request.getLoginUser());
                    mqReleaseRequest.setReleaseItemRequests(mergeMqItem(mqReleaseItemRequests));
                    Map<Long, SgCpCStore> map = data.getNegativeStock();
                    for (Long key : map.keySet()) {
                        SgCpCStore store = map.get(key);
                        mqReleaseRequest.setSgCShareStoreId(store.getSgCShareStoreId());
                    }
//                    r3MqSendHelper.sendMessage(MQConstantsIF.CONFIG_DEFAUALT,
//                            JSONObject.toJSONString(mqReleaseRequest),
//                            sgStorageMqConfig.getSgCallBackTopic(),
//                            MQConstantsIF.TAG_SG_B_SHARE_OUT_RELEASE_POSTBACK,
//                            stoOut.getId() + DateUtil.getDateTime(MQConstantsIF.MQ_FORMAT));
                    defaultProducerSend.sendTopic(sgStorageMqConfig.getMq5callback(),
                            MQConstantsIF.TAG_SG_B_SHARE_OUT_RELEASE_POSTBACK,
                            JSONObject.toJSONString(mqReleaseRequest),
                            stoOut.getId() + DateUtil.getDateTime(MQConstantsIF.MQ_FORMAT));
                }
                v14.setData(data);
            } catch (Exception e) {
                // 回滚库存
                StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, user);
                AssertUtils.logAndThrowException("释放逻辑占用单失败", e, locale);
            } finally {
                SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
            }
        } else {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("释放逻辑占用单，请稍后重试！");
        }
        if (log.isDebugEnabled()) {
            log.debug("end SgBStoOutReleaseService.releaseSgBStoOut ValueHolderV14={}", JSONObject.toJSONString(v14));
        }
        return v14;
    }

    /**
     * 相同条码合并
     *
     * @param mqReleaseItemRequests 配销层释放明细
     * @return SgBShareOutMQReleaseItemRequest
     */
    private List<SgBShareOutMQReleaseItemRequest> mergeMqItem(List<SgBShareOutMQReleaseItemRequest> mqReleaseItemRequests) {

        log.info(LogUtil.format("合并前长度mqReleaseItemRequests.size:{}", "mqReleaseItemRequests"), mqReleaseItemRequests.size());

        List<SgBShareOutMQReleaseItemRequest> returnList = new ArrayList<>();
        Map<Long, List<SgBShareOutMQReleaseItemRequest>> collect = mqReleaseItemRequests.stream().collect(Collectors.groupingBy(SgBShareOutMQReleaseItemRequest::getSkuId));
        collect.forEach((k, v) -> {
            SgBShareOutMQReleaseItemRequest itemRequest = v.get(0);
            if (v.size() > 1) {
                SgBShareOutMQReleaseItemRequest item = new SgBShareOutMQReleaseItemRequest();
                item.setSkuId(itemRequest.getSkuId());
                item.setSkuEcode(itemRequest.getSkuEcode());

                BigDecimal reduce = v.stream().map(SgBShareOutMQReleaseItemRequest::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                item.setQty(reduce);
                returnList.add(item);
            } else {
                returnList.add(itemRequest);
            }

        });
        log.info(LogUtil.format("合并后长度returnList.size:{}", "mqReleaseItemRequests"), returnList.size());
        return returnList;
    }

    /**
     * 处理明细
     *
     * <p>
     * 区分聚合仓是因为配销层共享占用单释放时，逻辑占用单也需要
     * 场景：2张共享占用单2个聚合仓 条码一样 逻辑占用单一张2条明细逻辑仓不一样
     * 不带聚合仓会随机释放容易交叉释放,导致另一个释放不了
     * </p>
     *
     * @param outResultItemList     出库单明细
     * @param orgSgStoreOutItems    占用单明细
     * @param storageItemList       库存集合
     * @param mqReleaseItemRequests mq集合
     * @param sgShareStoreIdList    聚合仓id
     * @param user                  用户
     * @param isLast                最后一次出库
     * @param isUpdateQtyOut        是否更新出库数量  true 更新
     */
    private Map<String, Long> handleItem(List<SgBStoOutResultItem> outResultItemList, List<SgBStoOutItem> orgSgStoreOutItems,
                                         List<SgBStoOutItem> storageItemList, List<SgBShareOutMQReleaseItemRequest> mqReleaseItemRequests,
                                         List<Long> sgShareStoreIdList, SgBStoOutResult outResult,
                                         User user, boolean isLast, boolean isUpdateQtyOut) {

        log.info(LogUtil.format("Start SgBStoOutReleaseService handleItem isUpdateQtyOut = {}",
                "handleItem"),
                isUpdateQtyOut);

        // 2022-03-16 新建逻辑,要根据逻辑仓所属聚合仓来区分明细，有就区分，么有就走原逻辑
        List<SgBStoOutItem> itemList = SgStoreUtils.contrastByShareId(sgShareStoreIdList, orgSgStoreOutItems);
        if (CollectionUtils.isEmpty(itemList)) {
            AssertUtils.logAndThrow("没有合适的释放占用明细！请检查！");
        }
        Map<String, List<SgBStoOutItem>> itemQtyMap = itemList.stream().collect(Collectors.groupingBy(SgBStoOutItem::getPsCSkuEcode));

        Map<String, Long> skuIdMap = new HashMap<>(16);

        //去除出库数量为0的明细
        outResultItemList = outResultItemList.stream().filter(x -> BigDecimal.ZERO.compareTo(x.getQty()) != 0).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(outResultItemList)) {
            Map<String, BigDecimal> qtyMap = checkItem(outResultItemList, skuIdMap, itemQtyMap, mqReleaseItemRequests);

            //更新出库
            if (isUpdateQtyOut) {
                updateQtyOut(qtyMap, outResultItemList, itemList.get(0).getSgBStoOutId(), outResult.getCpCStoreEcode());
            }

            //最后一次全部释放
            if (isLast) {
                itemList.forEach(item -> {
                    SgBStoOutItem updateItem = new SgBStoOutItem();
                    updateItem.setId(item.getId());
                    StorageUtils.setBModelDefalutDataByUpdate(updateItem, user);
                    updateItem.setQtyPreout(BigDecimal.ZERO);
                    sgStoOutItemMapper.updateById(updateItem);
                });
                //最后一次出库有多少放多少
                storageItemList.addAll(itemList);
            } else {
                List<SgBStoOutItem> stoOutItemList = unIsLastItem(outResultItemList, itemQtyMap, user);

                if (CollectionUtils.isNotEmpty(stoOutItemList)) {
                    //查是为了释放在途正确
                    List<SgBStoOutItem> stoOutItems = sgStoOutItemMapper.selectList(new QueryWrapper<SgBStoOutItem>().lambda()
                            .eq(SgBStoOutItem::getSgBStoOutId, itemList.get(0).getSgBStoOutId()));

                    Map<Long, BigDecimal> qtyPreOutMap = stoOutItems.stream().collect(Collectors.toMap(SgBStoOutItem::getId, SgBStoOutItem::getQtyPreout, (v1, v2) -> v1));

                    Map<Long, List<SgBStoOutItem>> itemListMap = stoOutItemList.stream().collect(Collectors.groupingBy(SgBStoOutItem::getId));

                    for (Long key : itemListMap.keySet()) {
                        List<SgBStoOutItem> outItems = itemListMap.get(key);
                        SgBStoOutItem updateItem = outItems.get(0);
                        SgBStoOutItem item = new SgBStoOutItem();
                        item.setId(key);
                        BigDecimal itemQtyPreout = Optional.ofNullable(qtyPreOutMap.get(updateItem.getId())).orElse(BigDecimal.ZERO);
                        if (outItems.size() > 0) {
                            BigDecimal totOutQty = outItems.stream().map(SgBStoOutItem::getQtyOut).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                            item.setQtyPreout(itemQtyPreout.subtract(totOutQty));
                            qtyPreOutMap.put(updateItem.getId(), item.getQtyPreout());
                            //后面释放在途用的 preinout字段 不是最后一次入库，所以这里入多少放多少
                            updateItem.setQtyPreout(totOutQty);
                        } else {
                            item.setQtyPreout(itemQtyPreout.subtract(updateItem.getQtyOut()));
                            qtyPreOutMap.put(updateItem.getId(), item.getQtyPreout());
                            //后面释放在途用的 preinout字段 不是最后一次入库，所以这里入多少放多少
                            updateItem.setQtyPreout(updateItem.getQtyOut());

                        }
                        storageItemList.add(updateItem);
                        sgStoOutItemMapper.updateById(item);
                    }

                }
            }

        }

        return skuIdMap;
    }

    /**
     * 更新逻辑占用单出库数量
     *
     * @param qtyMap            出库数量
     * @param outResultItemList 出库明细
     * @param sgStoOutId        逻辑占用单id
     * @param cpStoreEcode      仓库code
     */
    private void updateQtyOut(Map<String, BigDecimal> qtyMap, List<SgBStoOutResultItem> outResultItemList, Long sgStoOutId, String cpStoreEcode) {
        List<String> skuList = new ArrayList<>();
        List<String> produceList = new ArrayList<>();
        for (SgBStoOutResultItem outResultItem : outResultItemList) {
            skuList.add(outResultItem.getPsCSkuEcode());
            produceList.add(outResultItem.getProduceDate());
        }
        List<SgBStoOutItem> stoOutItems = sgStoOutItemMapper.selectList(new QueryWrapper<SgBStoOutItem>().lambda()
                .eq(SgBStoOutItem::getSgBStoOutId, sgStoOutId)
                .eq(SgBStoOutItem::getCpCStoreEcode, cpStoreEcode)
                .in(SgBStoOutItem::getPsCSkuEcode, skuList)
                .in(SgBStoOutItem::getProduceDate, produceList));

        if (CollectionUtils.isNotEmpty(stoOutItems)) {
            log.info(LogUtil.format("Start SgBStoOutReleaseService releaseSgBStoOut size = {},qtyMap={}",
                    "getSgBStoOutId"),
                    stoOutItems.size(), JSONObject.toJSONString(qtyMap));

            for (SgBStoOutItem x : stoOutItems) {
                if (qtyMap.containsKey(x.getProduceDate() + SgConstantsIF.MAP_KEY_DIVIDER + x.getPsCSkuEcode())) {
                    BigDecimal qty = qtyMap.get(x.getProduceDate() + SgConstantsIF.MAP_KEY_DIVIDER + x.getPsCSkuEcode());
                    SgBStoOutItem item = new SgBStoOutItem();
                    item.setId(x.getId());
                    BigDecimal itemQtyOut = Optional.ofNullable(x.getQtyOut()).orElse(BigDecimal.ZERO);
                    item.setQtyOut(itemQtyOut.add(qty));

                    log.info(LogUtil.format("Start SgBStoOutReleaseService releaseSgBStoOut qty = {},itemQtyOut={}",
                            "getSgBStoOutId"),
                            qty, itemQtyOut);

                    sgStoOutItemMapper.updateById(item);
                }
            }
        }

    }

    /**
     * 不是最后一次出库逻辑
     *
     * @param outResultItemList outResultItemList
     * @param itemQtyMap        itemQtyMap
     * @param user              user
     * @return return
     */
    private List<SgBStoOutItem> unIsLastItem(List<SgBStoOutResultItem> outResultItemList, Map<String, List<SgBStoOutItem>> itemQtyMap, User user) {
        List<SgBStoOutItem> storageItemList = new ArrayList<>();

        for (SgBStoOutResultItem outResultItem : outResultItemList) {
            String produceDate = outResultItem.getProduceDate();
            BigDecimal qty = outResultItem.getQty();
            //之前check过，这里应该都存在
            if (itemQtyMap.containsKey(outResultItem.getPsCSkuEcode())) {
                List<SgBStoOutItem> outItemList = itemQtyMap.get(outResultItem.getPsCSkuEcode());
                // identicalList相同批次
                List<SgBStoOutItem> identicalList = new ArrayList<>();
                List<SgBStoOutItem> unIdenticalList = new ArrayList<>();
                outItemList.forEach(item -> {
                    if (produceDate.equals(item.getProduceDate())) {
                        identicalList.add(item);
                    } else {
                        unIdenticalList.add(item);
                    }
                });

                if (CollectionUtils.isNotEmpty(identicalList)) {
                    for (SgBStoOutItem outItem : identicalList) {
                        BigDecimal qtyPreout = outItem.getQtyPreout();
                        if (BigDecimal.ZERO.compareTo(qty) >= 0) {
                            break;
                        }
                        if (BigDecimal.ZERO.compareTo(qtyPreout) >= 0) {
                            continue;
                        }
                        qty = setOutItem(qty, outItem, storageItemList, user);
                    }
                }

                if (CollectionUtils.isNotEmpty(unIdenticalList)) {
                    unIdenticalList.sort(Comparator.comparing(SgBStoOutItem::getProduceDate));
                    for (SgBStoOutItem outItem : unIdenticalList) {
                        BigDecimal qtyPreout = outItem.getQtyPreout();
                        if (BigDecimal.ZERO.compareTo(qty) >= 0) {
                            break;
                        }
                        if (BigDecimal.ZERO.compareTo(qtyPreout) >= 0) {
                            continue;
                        }
                        qty = setOutItem(qty, outItem, storageItemList, user);
                    }
                }

            }
        }
        log.info(LogUtil.format("SgBStoOutReleaseService.storageItemList returnList:{}", "SgBStoOutReleaseService.storageItemList"),
                JSONObject.toJSONString(storageItemList));
        return storageItemList;
    }

    /**
     * 设置值
     * 在途量设置的时候拿 原单量-出库量，后面更新占用有用
     *
     * @param qty        出库数量
     * @param outItem    通知单明细
     * @param returnList 返回集
     * @param user       用户
     * @return 剩余出库数量
     */
    private BigDecimal setOutItem(BigDecimal qty, SgBStoOutItem outItem, List<SgBStoOutItem> returnList,
                                  User user) {
        log.info(LogUtil.format("SgBStoOutReleaseService.setOutItem qty:{},outItemId:{},outItemProduceDate:{},outItemQtyPre:{},outItemQtyOut:{}",
                "SgBStoOutReleaseService.setOutItem")
                , qty, outItem.getId(), outItem.getProduceDate(), outItem.getQtyPreout(), outItem.getQtyOut());
        //在途数量-入库数量=可释放数量
        BigDecimal qtyIn = outItem.getQtyPreout();
        StorageUtils.setBModelDefalutDataByUpdate(outItem, user);
        outItem.setModifierename(user.getEname());
        if (qty.compareTo(qtyIn) > 0) {
            outItem.setQtyOut(qtyIn);
            outItem.setQtyPreout(BigDecimal.ZERO);
            qty = qty.subtract(qtyIn);
        } else {
            outItem.setQtyOut(qty);
            outItem.setQtyPreout(qtyIn.subtract(qty));
            qty = BigDecimal.ZERO;
        }
        //创建新对象，防止一行占用参与多次释放时值会被覆盖
        SgBStoOutItem sgBStoOutItem = new SgBStoOutItem();
        BeanUtils.copyProperties(outItem, sgBStoOutItem);
        returnList.add(sgBStoOutItem);
        return qty;
    }

    /**
     * 明细chekc
     *
     * @param outResultItemList     出库单明细
     * @param skuIdMap              存放来源单明细id
     * @param itemQtyMap            占用单明细分组
     * @param mqReleaseItemRequests mq集合
     * @return 出库数量
     */
    private Map<String, BigDecimal> checkItem(List<SgBStoOutResultItem> outResultItemList, Map<String, Long> skuIdMap,
                                              Map<String, List<SgBStoOutItem>> itemQtyMap,
                                              List<SgBShareOutMQReleaseItemRequest> mqReleaseItemRequests) {
        JSONArray error = new JSONArray();
        JSONArray skuError = new JSONArray();
        Map<String, BigDecimal> qtyMap = new HashMap<>(16);

        Map<String, List<SgBStoOutResultItem>> outItemMap = outResultItemList.stream().collect(Collectors.groupingBy(SgBStoOutResultItem::getPsCSkuEcode));
        for (String key : outItemMap.keySet()) {
            List<SgBStoOutResultItem> outResultItems = outItemMap.get(key);
            if (itemQtyMap.containsKey(key)) {
                List<SgBStoOutItem> outItemList = itemQtyMap.get(key);
                BigDecimal qty = BigDecimal.ZERO;
                //check 出库数量
                for (SgBStoOutResultItem item : outResultItems) {
                    skuIdMap.put(SgStoreStorageService.SkuKeyBuildUtil.buildKey(item), item.getId());
                    qtyMap.put(item.getProduceDate() + SgConstantsIF.MAP_KEY_DIVIDER + item.getPsCSkuEcode(), item.getQty());
                    qty = qty.add(item.getQty());
                    //mq封装
                    SgBShareOutMQReleaseItemRequest releaseItemRequest = new SgBShareOutMQReleaseItemRequest();
                    releaseItemRequest.setQty(item.getQty());
                    releaseItemRequest.setSkuEcode(item.getPsCSkuEcode());
                    releaseItemRequest.setSkuId(item.getPsCSkuId());
                    mqReleaseItemRequests.add(releaseItemRequest);
                }
                //逻辑占用单可释放数量
                BigDecimal skuQtyPreout = outItemList.stream().map(SgBStoOutItem::getQtyPreout).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (qty.compareTo(skuQtyPreout) > 0) {
                    error.add(key);
                }
            } else {
                skuError.add(key);
            }
        }

        if (error.size() > 0) {
            AssertUtils.logAndThrow("出库数量大于占用数量：" + JSONArray.toJSONString(error));
        }
        if (skuError.size() > 0) {
            AssertUtils.logAndThrow("占用单不存在条码：" + JSONArray.toJSONString(skuError));
        }

        return qtyMap;
    }

    /**
     * 处理明细
     *
     * @param inResultItemList      明细
     * @param orgSgStoreOutItems    明细
     * @param storageItemList       库存集合
     * @param mqReleaseItemRequests mq集合
     * @param sgShareStoreIdList    聚合仓id
     * @param user                  用户
     */
    private Map<String, Long> handleItem(List<SgBStoOutResultItem> inResultItemList, List<SgBStoOutItem> orgSgStoreOutItems,
                                         List<SgBStoOutItem> storageItemList, List<SgBShareOutMQReleaseItemRequest> mqReleaseItemRequests,
                                         List<Long> sgShareStoreIdList,
                                         User user, boolean isLast, boolean isTransfer, boolean isSplit, Boolean isEdge) {
        // 2022-03-16 新建逻辑,要根据逻辑仓所属聚合仓来区分明细，有就区分，么有就走原逻辑
        List<SgBStoOutItem> itemList = SgStoreUtils.contrastByShareId(sgShareStoreIdList, orgSgStoreOutItems);

        if (CollectionUtils.isEmpty(itemList)) {
            AssertUtils.logAndThrow("没有合适的释放占用明细！请检查！");
        }

        Map<String, List<SgBStoOutItem>> itemQtyMap = itemList.stream()
                .collect(Collectors.groupingBy(o -> o.getPsCSkuEcode()));

        Map<String, Long> skuIdMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(inResultItemList)) {
            JSONArray error = new JSONArray();

            for (SgBStoOutResultItem item : inResultItemList) {

                skuIdMap.put(SgStoreStorageService.SkuKeyBuildUtil.buildKey(item), item.getId());
                if (itemQtyMap.containsKey(item.getPsCSkuEcode())) {
                    List<SgBStoOutItem> outItemList = itemQtyMap.get(item.getPsCSkuEcode());

                    BigDecimal qtyOut = item.getQty();
                    //传入为0不处理
                    if (qtyOut.compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }
                    BigDecimal skuQtyPreout =
                            outItemList.stream().map(SgBStoOutItem::getQtyPreout).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (qtyOut.compareTo(skuQtyPreout) > 0) {
                        error.add(item.getPsCSkuEcode());
                        continue;
                    }
                    //占用单明细：QtyPreout更新为0，QtyOut更新为实际出库数
                    //更新库存占用数逻辑：原占用数量全部释放
                    //优先匹配相同sku+生产日期。
                    final Optional<SgBStoOutItem> match = outItemList.stream().filter(o -> o.getProduceDate().equals(item.getProduceDate())).findFirst();
                    if (match.isPresent()) {
                        SgBStoOutItem outItem = match.get();

                        SgBStoOutItem updateItem = new SgBStoOutItem();
                        updateItem.setId(outItem.getId());
                        BigDecimal itemQtyOut = QtyUtils.getItemQtyout(qtyOut, outItem.getQtyPreout(), outItem.getQtyOut());
                        updateItem.setQtyOut(outItem.getQtyOut().add(qtyOut));
                        BigDecimal updateQtyPreout = isLast ? BigDecimal.ZERO : outItem.getQtyPreout().subtract(itemQtyOut);
                        updateItem.setQtyPreout(updateQtyPreout);

                        StorageUtils.setBModelDefalutDataByUpdate(updateItem, user);
                        if (isSplit) {
                            updateItem.setQtyOut(BigDecimal.ZERO);
                        }
                        sgStoOutItemMapper.updateById(updateItem);

                        //更新占用数
                        outItem.setQtyOut(BigDecimal.ZERO);
                        storageItemList.add(outItem);

                        qtyOut = qtyOut.subtract(itemQtyOut);
                        outItemList.remove(match.get());
                    }
                    //循环剩下的同sku的不同生产日期的数据
                    if (outItemList != null) {

                        //排序 升序
                        outItemList.sort(Comparator.comparing(SgBStoOutItem::getProduceDate));
                        for (SgBStoOutItem outItem : outItemList) {

                            if (qtyOut.compareTo(BigDecimal.ZERO) <= 0) {
                                break;
                            }
                            SgBStoOutItem updateItem = new SgBStoOutItem();
                            updateItem.setId(outItem.getId());

                            BigDecimal itemQtyOut = QtyUtils.getItemQtyout(qtyOut, outItem.getQtyPreout(), outItem.getQtyOut());

                            // 意哥传过来的不更新发货数量
                            if (isEdge) {
                                updateItem.setQtyPreout(outItem.getQtyPreout().subtract(itemQtyOut));
                                outItem.setQtyPreout(itemQtyOut);
                                outItem.setQtyOut(BigDecimal.ZERO);
                            } else {
                                //最后一次出库 全部释放
                                if (isLast) {
                                    updateItem.setQtyPreout(BigDecimal.ZERO);
                                    outItem.setQtyOut(itemQtyOut);
                                } else {
                                    updateItem.setQtyPreout(outItem.getQtyPreout().subtract(itemQtyOut));
                                    outItem.setQtyOut(itemQtyOut);
                                    outItem.setQtyPreout(itemQtyOut);
                                }
                            }
                            qtyOut = qtyOut.subtract(itemQtyOut);
                            StorageUtils.setBModelDefalutDataByUpdate(updateItem, user);


                            if (isSplit) {
                                outItem.setQtyOut(BigDecimal.ZERO);
//                                updateItem.setQtyOut(BigDecimal.ZERO);
                            }

                            sgStoOutItemMapper.updateById(updateItem);
                            storageItemList.add(outItem);
                        }
                    }
                }

                //mq封装
                SgBShareOutMQReleaseItemRequest releaseItemRequest = new SgBShareOutMQReleaseItemRequest();
                releaseItemRequest.setQty(item.getQty());
                releaseItemRequest.setSkuEcode(item.getPsCProEcode());
                releaseItemRequest.setSkuId(item.getPsCSkuId());
                mqReleaseItemRequests.add(releaseItemRequest);
            }
            if (error.size() > 0) {
                AssertUtils.logAndThrow("出库数量大于占用数量：" + JSONArray.toJSONString(error));
            }
        }

        //2021-09-30 珂珂：来源单据类型=逻辑调拨单,采购退货单 最后一次入库 全部释放   只释放不更新库存
        //场景：WMS仓库没扫描到或者就是没有扫描不到
        //长度一样默认为 入库单里面的明细跟在途单明细是一样的
        if (CollectionUtils.isNotEmpty(storageItemList) && isLast && isTransfer
                && orgSgStoreOutItems.size() != storageItemList.size()) {

            //这里是入库单需要更新库存的明细
            Map<String, List<SgBStoOutItem>> itemMap = storageItemList.stream().collect(
                    Collectors.groupingBy(SgBStoOutItem::getPsCSkuEcode));

            for (SgBStoOutItem outItem : orgSgStoreOutItems) {
                if (!itemMap.containsKey(outItem.getPsCSkuEcode())) {
                    // 只释放不更新库存
                    outItem.setQtyOut(BigDecimal.ZERO);
                    storageItemList.add(outItem);

                    //更新明细
                    SgBStoOutItem updateItem = new SgBStoOutItem();
                    updateItem.setId(outItem.getId());
                    updateItem.setQtyPreout(BigDecimal.ZERO);
                    StorageUtils.setBModelDefalutDataByUpdate(updateItem, user);
                    sgStoOutItemMapper.updateById(updateItem);

                    //mq封装
                    SgBShareOutMQReleaseItemRequest releaseItemRequest = new SgBShareOutMQReleaseItemRequest();
                    releaseItemRequest.setQty(outItem.getQty());
                    releaseItemRequest.setSkuEcode(outItem.getPsCProEcode());
                    releaseItemRequest.setSkuId(outItem.getPsCSkuId());
                    mqReleaseItemRequests.add(releaseItemRequest);

                }
            }
        }

        return skuIdMap;
    }


    /**
     * 2021-5-24 新增逻辑，共享量修改 根据零售发货单 生成非共享or共享流水
     *
     * @param sgStoreOut      逻辑占用单主表
     * @param sgStoreOutItems 逻辑占用单明细
     * @param request         逻辑占用单主表如入参
     */
//    private ValueHolderV14<SgBStoOutBillReleaseResult> outSgStorageFtp(SgBStoOut sgStoreOut,
//                                                                       SgBStoOutBillReleaseRequest request,
//                                                                       List<SgBStoOutItem> sgStoreOutItems,
//                                                                       List<String> redisBillFtpKeyList, Map<String, Long> skuIdMap) {
//
//        Integer sourceBillType = sgStoreOut.getSourceBillType();
//        //共享 or 非共享 true = 共享  false = 非共享
//        boolean isRetail = ShareFtpUtils.isShare(sourceBillType);
//        try {
//            // 更新【逻辑仓库存查询】
//            ValueHolderV14<SgBStoOutBillReleaseResult> valueHolderV14 = updateStoragePreOut(sgStoreOut, request,
//                    sgStoreOutItems, isRetail, redisBillFtpKeyList, skuIdMap);
//            return valueHolderV14;
//        } catch (Exception e) {
//            AssertUtils.logAndThrowException("逻辑占用单释放异常：", e);
//        }
//        return null;
//    }

    /**
     * 更新库存
     */
    public ValueHolderV14<SgBStoOutBillReleaseResult> updateStoragePreOut(SgBStoOut sgStoreOut,
                                                                          SgBStoOutBillReleaseRequest request,
                                                                          List<SgBStoOutItem> sgStoreOutItems
            , boolean share, List<String> redisBillFtpKeyList, Map<String, Long> skuIdMap) {


        ValueHolderV14<SgBStoOutBillReleaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS,
                SgConstants.MESSAGE_STATUS_SUCCESS);
        List<Long> stores = sgStoreOutItems.stream()
                .map(SgBStoOutItem::getCpCStoreId).distinct().collect(Collectors.toList());
        if (log.isDebugEnabled()) {
            // 逻辑占用单更新（释放）入参
            log.debug("Start SgBStoOutReleaseService.releaseSgBStoOut " +
                            "outSgStorageFtp:sgStoreOut:{},request:{}.sgStoreOutItems;{}.skuIdMap:{}"

                    , JSONObject.toJSONString(sgStoreOut), JSONObject.toJSONString(request),
                    JSONObject.toJSONString(sgStoreOutItems), JSONObject.toJSONString(skuIdMap));
        }

        AssertUtils.cannot(CollectionUtils.isEmpty(stores), "逻辑占用单明细对应逻辑仓为空！");

        Map<Long, SgCpCStore> negativeStock = SgStoreUtils.getNegativeStock(stores);

        SgBStoOutResult result = request.getOutResult();
        Boolean isQut = request.getIsQut();
        ValueHolderV14<SgStorageUpdateResult> storageResult =
                storageService.updateStoOutStoragePreout(sgStoreOut, sgStoreOutItems, request, null,
                        isQut ? SgStoreConstants.SUBMIT : SgStoreConstants.VOID, request.getLoginUser(), negativeStock, Boolean.FALSE,
                        request.getServiceNode(), share, request.getChangeDate(), skuIdMap, result);
        if (log.isDebugEnabled()) {
            // 逻辑占用单更新（释放）入参
            log.debug("Start SgBStoOutReleaseService.releaseSgBStoOut outSgStorageFtp:storageResult:{};"
                    , JSONObject.toJSONString(storageResult));
        }
        if (storageResult.isOK()) {
            SgBStoOutBillReleaseResult data = new SgBStoOutBillReleaseResult();
            data.setNegativeStock(negativeStock);
            if (storageResult.getData() != null &&
                    CollectionUtils.isNotEmpty(storageResult.getData().getRedisBillFtpKeyList())) {
                redisBillFtpKeyList.addAll(storageResult.getData().getRedisBillFtpKeyList());
                data.setRedisBillFtpKeyList(redisBillFtpKeyList);
                data.setPreoutUpdateResult(storageResult.getData().getPreoutUpdateResult());
            }
            v14.setData(data);
        } else {
            AssertUtils.logAndThrow("逻辑占用单释放库存失败:" + storageResult.getMessage());
        }
        return v14;

    }

    /**
     * 参数校验
     *
     * @param request 入参
     */
    public void checkParams(SgBStoOutBillReleaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("end SgBStoOutReleaseService.checkParams SgBStoOutBillReleaseRequest={}", JSONObject.toJSONString(request));
        }
        SgStoreUtils.checkR3BModelDefalut(request);
        SgBStoOutResult outResult = request.getOutResult();
        AssertUtils.notNull(outResult, "逻辑出库单信息不能为空！");

        AssertUtils.notNull(outResult.getSourceBillId(), "当前来源单据信息不能为空！");
        AssertUtils.notNull(outResult.getSourceBillType(), "当前来源单据信息不能为空！");


        List<SgBStoOut> sgStoOuts = sgStoOutMapper.selectList(new QueryWrapper<SgBStoOut>().lambda()
                .eq(SgBStoOut::getSourceBillNo, outResult.getSourceBillNo())
                .eq(SgBStoOut::getSourceBillType, outResult.getSourceBillType())
                .eq(SgBStoOut::getIsactive, SgConstants.IS_ACTIVE_Y));

        if (CollectionUtils.isEmpty(sgStoOuts)) {
            AssertUtils.logAndThrow("逻辑占用单不存在！");
        }
        SgBStoOut sgStoOut = sgStoOuts.get(0);
        Integer billStatus = sgStoOut.getBillStatus();
        if (SgStoreConstants.BILL_STO_OUT_STATUS_ALL_OUT.equals(billStatus) ||
                SgStoreConstants.BILL_STO_OUT_STATUS_CANCELED.equals(billStatus)) {
            AssertUtils.logAndThrow("逻辑占用单当前单据状态不允许更新！");
        }
        AssertUtils.notNull(request.getOutResultItemList(), "逻辑出库单明细信息不能为空！");
        //将逻辑出库单的id放入request中
        request.getOutResult().setSgBStoOutId(sgStoOuts.get(0).getId());


    }
}
