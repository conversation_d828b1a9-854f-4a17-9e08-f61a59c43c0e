package com.burgeon.r3.sg.store.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.config.SgStorageControlConfig;
import com.burgeon.r3.sg.basic.mapper.SgBFreezeStorageMapper;
import com.burgeon.r3.sg.basic.model.AbstractSgStorageUpdateCommonModel;
import com.burgeon.r3.sg.basic.model.request.AbstractSgStorageUpdateBillItemRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageBatchUpdateRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageSingleUpdateRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageUpdateBillRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageUpdateControlRequest;
import com.burgeon.r3.sg.basic.model.request.vo.SgStorageUpdateBillItemLsRequest;
import com.burgeon.r3.sg.basic.model.request.vo.SgStorageUpdateBillItemSaRequest;
import com.burgeon.r3.sg.basic.model.request.vo.SgStorageUpdateBillItemSsRequest;
import com.burgeon.r3.sg.basic.model.request.vo.SgStorageUpdateBillSaRequest;
import com.burgeon.r3.sg.basic.model.result.AbstractSgStorageOutStockResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageBillUpdateResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageUpdateResult;
import com.burgeon.r3.sg.basic.model.result.vo.SgStorageOutStockLsResult;
import com.burgeon.r3.sg.basic.services.SgStorageBillTransUpdateService;
import com.burgeon.r3.sg.basic.services.SgStorageRedisBillBatchUpdateService;
import com.burgeon.r3.sg.basic.services.SgStorageRedisBillUpdateService;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.store.freeze.SgBStoFreeze;
import com.burgeon.r3.sg.core.model.table.store.freeze.SgBStoFreezeItem;
import com.burgeon.r3.sg.core.model.table.store.freeze.SgBStoUnfreeze;
import com.burgeon.r3.sg.core.model.table.store.freeze.SgBStoUnfreezeItem;
import com.burgeon.r3.sg.core.model.table.store.freeze.out.SgBStoFreezeOut;
import com.burgeon.r3.sg.core.model.table.store.freeze.out.SgBStoFreezeOutItem;
import com.burgeon.r3.sg.core.model.table.store.freeze.out.SgBStoFreezeOutResult;
import com.burgeon.r3.sg.core.model.table.store.freeze.out.SgBStoFreezeOutResultItem;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoIn;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInItem;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInResult;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInResultItem;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOut;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItem;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutResult;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutResultItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoUnfreezeSubmitRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutBillReleaseRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutItemSaveRequest;
import com.burgeon.r3.sg.store.model.result.SgStoreStorageResult;
import com.burgeon.r3.sg.store.services.out.SgBStoOutSaveService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BeanCopierUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * 库存更新服务
 * @date 2021/4/27 13:28
 */
@Slf4j
@Component
public class SgStoreStorageService {
    @Value("${sg.control.is_negative_receive:false}")
    private Boolean isNegativeInStorage;

    @Autowired
    private SgStorageBillTransUpdateService transUpdateService;

    @Autowired
    private SgStorageRedisBillUpdateService redisUpdateService;

    @Autowired
    private SgStorageControlConfig sgStorageControlConfig;

    @Autowired
    private SgBFreezeStorageMapper sgFreezeStorageMapper;

    /**
     * @param request:
     * @param sgBStoUnfreeze:
     * @param changeItems:
     * @param loginUser:
     * @Description:
     * @Author: hwy
     * @Date: 2021/5/10 16:43
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<com.burgeon.r3.sg.basic.model.result.SgStorageUpdateResult>
     **/
    public ValueHolderV14<SgStorageUpdateResult> updateSgBStoUnfreeze(SgBStoUnfreezeSubmitRequest request,
                                                                      SgBStoUnfreeze sgBStoUnfreeze,
                                                                      List<SgBStoUnfreezeItem> changeItems,
                                                                      User loginUser) {
        ValueHolderV14<SgStorageUpdateResult> valueHolderV14 =
                new ValueHolderV14<SgStorageUpdateResult>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        try {
            SgStorageUpdateBillRequest billRequest = new SgStorageUpdateBillRequest();
            //单据信息
            billRequest.setBillId(sgBStoUnfreeze.getId());
            billRequest.setBillNo(sgBStoUnfreeze.getBillNo());
            billRequest.setBillDate(sgBStoUnfreeze.getBillDate());
            billRequest.setChangeDate(sgBStoUnfreeze.getBillDate());

            billRequest.setBillType(SgConstantsIF.BILL_TYPE_UNFREEZE);
            billRequest.setServiceNode(SgConstantsIF.SERVICE_NODE_UNFREEZE_SUBMIT);
            billRequest.setSourceBillId(sgBStoUnfreeze.getSourceBillId());
            billRequest.setSourceBillNo(sgBStoUnfreeze.getSourceBillNo());

            List<AbstractSgStorageUpdateBillItemRequest> itemList = buildUnfreezeStorageItems(changeItems,
                    sgBStoUnfreeze);
            billRequest.setItemList(itemList);
            //设置 主表是否允许负库存
            Long cpStoreId = sgBStoUnfreeze.getCpCStoreId();
            List<Long> stores = new ArrayList<>();
            stores.add(cpStoreId);
            Map<Long, SgCpCStore> negativeStock = SgStoreUtils.getNegativeStock(stores);
            SgStorageUpdateControlRequest controlRequest = getSgStorageUpdateControlRequest(false, negativeStock, cpStoreId);
            //更新库存参数
            SgStorageSingleUpdateRequest updateRequest = new SgStorageSingleUpdateRequest();
            updateRequest.setBill(billRequest);
            String msgKey = SgConstants.SG_B_STO_UNFREEZE + ":" + sgBStoUnfreeze.getBillNo();
            updateRequest.setMessageKey(msgKey);
            updateRequest.setLoginUser(loginUser);
            updateRequest.setControlModel(controlRequest);
            if (log.isDebugEnabled()) {
                log.debug("单据编号:{}  开始解冻库存 入参:{}", sgBStoUnfreeze.getBillNo(), JSONObject.toJSONString(updateRequest));
            }
            valueHolderV14 = updatedStorage(updateRequest);
            if (log.isDebugEnabled()) {
                log.debug("单据编号:{} 解冻库存 出参:{}", sgBStoUnfreeze.getBillNo(), JSONObject.toJSONString(valueHolderV14));
            }
            //单据明细信息
        } catch (Exception e) {
            AssertUtils.logAndThrowException("库存解冻-同步库存接口服务异常！", e, loginUser.getLocale());
        }

        return valueHolderV14;
    }

    private List<AbstractSgStorageUpdateBillItemRequest> buildUnfreezeStorageItems(List<SgBStoUnfreezeItem> changeItems, SgBStoUnfreeze sgBStoUnfreeze) {
        List<AbstractSgStorageUpdateBillItemRequest> itemList = new ArrayList<>();
        changeItems.stream().forEach(o -> {
            SgStorageUpdateBillItemLsRequest sgStorageUpdateBillItemRequest = new SgStorageUpdateBillItemLsRequest();
            BeanCopierUtil.copy(o, sgStorageUpdateBillItemRequest);
            sgStorageUpdateBillItemRequest.setBillItemId(o.getId());
            sgStorageUpdateBillItemRequest.setCpCStoreId(sgBStoUnfreeze.getCpCStoreId());
            sgStorageUpdateBillItemRequest.setCpCStoreEcode(sgBStoUnfreeze.getCpCStoreEcode());
            sgStorageUpdateBillItemRequest.setCpCStoreEname(sgBStoUnfreeze.getCpCStoreEname());
            sgStorageUpdateBillItemRequest.setQtyFreezeChange(o.getQty().negate());
            itemList.add(sgStorageUpdateBillItemRequest);
        });

        return itemList;
    }

    /**
     * 冻结占用单-操作冻结占用量
     *
     * @param sgStoFreezeOutResult      冻结出库单-主表
     * @param sgStoFreezeOutResultItems 冻结出库单-明细
     * @param serviceNode               业务节点
     * @param isCancel                  是否取反
     * @param user                      用户
     * @return ValueHolderV14
     */
    public ValueHolderV14<SgStorageUpdateResult> updateStorageFreeze(SgBStoFreezeOutResult sgStoFreezeOutResult,
                                                                     List<SgBStoFreezeOutResultItem> sgStoFreezeOutResultItems,
                                                                     Long serviceNode, Boolean isCancel,
                                                                     User user) {
        ValueHolderV14<SgStorageUpdateResult> valueHolderV14 =
                new ValueHolderV14<SgStorageUpdateResult>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

        try {
            SgStorageSingleUpdateRequest updateRequest = new SgStorageSingleUpdateRequest();
            SgStorageUpdateBillRequest billRequest = new SgStorageUpdateBillRequest();

            billRequest.setBillId(sgStoFreezeOutResult.getId());
            billRequest.setBillNo(sgStoFreezeOutResult.getBillNo());
            billRequest.setBillDate(sgStoFreezeOutResult.getBillDate());

            billRequest.setBillType(SgConstantsIF.BILL_TYPE_FREEZE_OUT_RESULT);
            billRequest.setChangeDate(sgStoFreezeOutResult.getBillDate());

            billRequest.setServiceNode(serviceNode);
            billRequest.setSourceBillId(sgStoFreezeOutResult.getSourceBillId());
            billRequest.setSourceBillNo(sgStoFreezeOutResult.getSourceBillNo());
            billRequest.setIsCancel(isCancel);
            //设置 主表是否允许负库存
            List<Long> storeIds = new ArrayList<>();
            storeIds.add(sgStoFreezeOutResult.getCpCStoreId());
            Map<Long, SgCpCStore> negativeStock = SgStoreUtils.getNegativeStock(storeIds);
            SgStorageUpdateControlRequest controlRequest = getSgStorageUpdateControlRequest(false,
                    negativeStock, sgStoFreezeOutResult.getCpCStoreId());

            List<AbstractSgStorageUpdateBillItemRequest> itemList = new ArrayList<>();
            for (SgBStoFreezeOutResultItem item : sgStoFreezeOutResultItems) {
                SgStorageUpdateBillItemLsRequest updateBillItemRequest = new SgStorageUpdateBillItemLsRequest();
                BeanUtils.copyProperties(item, updateBillItemRequest);
                updateBillItemRequest.setBillItemId(item.getId());
                updateBillItemRequest.setQtyFreezeChange(item.getQty());
                updateBillItemRequest.setCpCStoreId(sgStoFreezeOutResult.getCpCStoreId());
                updateBillItemRequest.setCpCStoreEcode(sgStoFreezeOutResult.getCpCStoreEcode());
                updateBillItemRequest.setCpCStoreEname(sgStoFreezeOutResult.getCpCStoreEname());
                updateBillItemRequest.setStockType(item.getStorageType());
                //是否允许负库存
                updateBillItemRequest.setControlmodel(controlRequest);
                updateBillItemRequest.setQtyStorageChange(item.getQty());
                itemList.add(updateBillItemRequest);
            }

            billRequest.setItemList(itemList);

            String msgKey = SgConstants.SG_B_STO_FREEZE_OUT_RESULT + ":" + sgStoFreezeOutResult.getBillNo();
            updateRequest.setMessageKey(msgKey);
            updateRequest.setBill(billRequest);
            updateRequest.setLoginUser(user);
            updateRequest.setControlModel(controlRequest);

            log.info(LogUtil.format("单据编号:{},开始冻结出库入参:{}", "updateStorageFreeze"),
                    sgStoFreezeOutResult.getBillNo(), JSONObject.toJSONString(updateRequest));
            valueHolderV14 = updatedStorage(updateRequest);

            log.info(LogUtil.format("单据编号:{},冻结出库出参:{}", "updateStorageFreeze"),
                    sgStoFreezeOutResult.getBillNo(), JSONObject.toJSONString(valueHolderV14));

        } catch (Exception e) {
            AssertUtils.logAndThrowException("库存冻结-同步库存接口服务异常！", e, user.getLocale());
        }
        return valueHolderV14;
    }

    /**
     * 冻结占用单-操作冻结占用量
     *
     * @param sgStoFreezeOut 主表
     * @param outItemList    明细
     * @param serviceNode    业务节点
     * @param isCancel       是否取反
     * @param user           用户
     * @return ValueHolderV14
     */
    public ValueHolderV14<SgStorageUpdateResult> updateStorageFreezePreout(SgBStoFreezeOut sgStoFreezeOut,
                                                                           List<SgBStoFreezeOutItem> outItemList,
                                                                           Long serviceNode, Boolean isCancel,
                                                                           User user) {
        ValueHolderV14<SgStorageUpdateResult> valueHolderV14 =
                new ValueHolderV14<SgStorageUpdateResult>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

        try {
            SgStorageSingleUpdateRequest updateRequest = new SgStorageSingleUpdateRequest();
            SgStorageUpdateBillRequest billRequest = new SgStorageUpdateBillRequest();

            billRequest.setBillId(sgStoFreezeOut.getId());
            billRequest.setBillNo(sgStoFreezeOut.getBillNo());
            billRequest.setBillDate(sgStoFreezeOut.getBillDate());

            billRequest.setBillType(sgStoFreezeOut.getSourceBillType());
            billRequest.setChangeDate(sgStoFreezeOut.getBillDate());

            billRequest.setServiceNode(serviceNode);
            billRequest.setSourceBillId(sgStoFreezeOut.getSourceBillId());
            billRequest.setSourceBillNo(sgStoFreezeOut.getSourceBillNo());
            billRequest.setIsCancel(isCancel);
            //设置 主表是否允许负库存

            List<Long> storeIds = outItemList.stream().map(SgBStoFreezeOutItem::getCpCStoreId).collect(Collectors.toList());
            Map<Long, SgCpCStore> negativeStock = SgStoreUtils.getNegativeStock(storeIds);

            List<AbstractSgStorageUpdateBillItemRequest> itemList = new ArrayList<>();
            for (SgBStoFreezeOutItem item : outItemList) {
                SgStorageUpdateBillItemLsRequest updateBillItemRequest = new SgStorageUpdateBillItemLsRequest();
                BeanUtils.copyProperties(item, updateBillItemRequest);
                updateBillItemRequest.setBillItemId(item.getId());
                updateBillItemRequest.setQtyFreezePreoutChange(item.getQtyPreout());
                updateBillItemRequest.setCpCStoreId(item.getCpCStoreId());
                updateBillItemRequest.setCpCStoreEcode(item.getCpCStoreEcode());
                updateBillItemRequest.setCpCStoreEname(item.getCpCStoreEname());
                updateBillItemRequest.setStockType(item.getStorageType());
                //是否允许负库存
                SgStorageUpdateControlRequest controlRequest = getSgStorageUpdateControlRequest(false,
                        negativeStock, item.getCpCStoreId());
                updateBillItemRequest.setControlmodel(controlRequest);
                //冻结占用库存不走redis，也不用这个控制类
                updateRequest.setControlModel(controlRequest);
                itemList.add(updateBillItemRequest);
            }

            billRequest.setItemList(itemList);

            String msgKey = SgConstants.SG_B_STO_FREEZE_OUT + ":" + sgStoFreezeOut.getBillNo();
            updateRequest.setMessageKey(msgKey);
            updateRequest.setBill(billRequest);
            updateRequest.setLoginUser(user);

            log.info(LogUtil.format("单据编号:{},开始冻结占用入参:{}", "updateStorageFreezePreout"),
                    sgStoFreezeOut.getBillNo(), JSONObject.toJSONString(updateRequest));

            ValueHolderV14<SgStorageBillUpdateResult> valueHolderV141 = transUpdateService.updateStorageBill(updateRequest);

            log.info(LogUtil.format("单据编号:{},冻结结占用出参:{}", "updateStorageFreezePreout"),
                    sgStoFreezeOut.getBillNo(), JSONObject.toJSONString(valueHolderV141));

            SgStorageUpdateResult updateResult = new SgStorageUpdateResult();
            if (valueHolderV141 != null && valueHolderV141.getData() != null) {

                updateResult = convertSgUpdateResult(valueHolderV141.getData());

                //冻结占用流水跟正常流水不一样特殊处理
                List<AbstractSgStorageUpdateCommonModel> channelMqItemList = valueHolderV141.getData().getChannelMqItemList();
                if (CollectionUtils.isNotEmpty(channelMqItemList)) {
                    List<String> redisFtpKey = new ArrayList<>();
                    for (AbstractSgStorageUpdateCommonModel commonModel : channelMqItemList) {
                        String redisBillFtpKey = commonModel.getRedisBillFtpKey();
                        if (StringUtils.isNotEmpty(redisBillFtpKey)) {
                            redisFtpKey.add(redisBillFtpKey);
                        }
                    }
                    List<String> redisBillFtpKeyList = updateResult.getRedisBillFtpKeyList();
                    if (CollectionUtils.isNotEmpty(redisFtpKey)) {
                        if (CollectionUtils.isNotEmpty(redisBillFtpKeyList)) {
                            redisBillFtpKeyList.addAll(redisFtpKey);
                        } else {
                            redisBillFtpKeyList = redisFtpKey;
                        }
                    }
                    updateResult.setRedisBillFtpKeyList(redisBillFtpKeyList);
                }
            }

            log.info(LogUtil.format("冻结结占用流水信息:{}", "updateStorageFreezePreout"), JSONObject.toJSONString(updateResult));

            valueHolderV14.setData(updateResult);

            if (valueHolderV141 != null) {
                valueHolderV14.setCode(valueHolderV141.getCode());
                valueHolderV14.setMessage(valueHolderV141.getMessage());
            }
        } catch (Exception e) {
            AssertUtils.logAndThrowException("库存冻结-同步库存接口服务异常！", e, user.getLocale());
        }
        return valueHolderV14;
    }

    /**
     * @param sgStoFreeze      冻结单主表
     * @param sgStoFreezeItems 明细
     * @param loginUser
     * @return
     */
    public ValueHolderV14<SgStorageUpdateResult> updateSgBStoFreeze(SgBStoFreeze sgStoFreeze,
                                                                    List<SgBStoFreezeItem> sgStoFreezeItems,
                                                                    User loginUser, Long isNegative) {
        ValueHolderV14<SgStorageUpdateResult> valueHolderV14 =
                new ValueHolderV14<SgStorageUpdateResult>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        try {
            SgStorageSingleUpdateRequest updateRequest = new SgStorageSingleUpdateRequest();
            SgStorageUpdateBillRequest billRequest = new SgStorageUpdateBillRequest();

            billRequest.setBillId(sgStoFreeze.getId());
            billRequest.setBillNo(sgStoFreeze.getBillNo());
            billRequest.setBillDate(sgStoFreeze.getBillDate());

            billRequest.setBillType(SgConstantsIF.BILL_TYPE_FREEZE);
            billRequest.setChangeDate(sgStoFreeze.getBillDate());

            billRequest.setServiceNode(SgConstantsIF.SERVICE_NODE_FREEZE_SUBMIT);
            billRequest.setSourceBillId(sgStoFreeze.getSourceBillId());
            billRequest.setSourceBillNo(sgStoFreeze.getSourceBillNo());
            Long cStoreId = sgStoFreeze.getCpCStoreId();
            Map<Long, SgCpCStore> storeMap = new HashMap<>();
            SgCpCStore cStore = new SgCpCStore();
            cStore.setId(cStoreId);
            cStore.setIsnegative(isNegative);
            storeMap.put(cStoreId, cStore);
            //设置 主表是否允许负库存
            SgStorageUpdateControlRequest controlRequest = getSgStorageUpdateControlRequest(false, storeMap, cStoreId);
            List<AbstractSgStorageUpdateBillItemRequest> itemList = new ArrayList<>();
            for (SgBStoFreezeItem item : sgStoFreezeItems) {
                SgStorageUpdateBillItemLsRequest updateBillItemRequest = new SgStorageUpdateBillItemLsRequest();
                BeanUtils.copyProperties(item, updateBillItemRequest);
                updateBillItemRequest.setBillItemId(item.getId());
                updateBillItemRequest.setQtyFreezeChange(item.getQty());
                updateBillItemRequest.setCpCStoreId(sgStoFreeze.getCpCStoreId());
                updateBillItemRequest.setCpCStoreEcode(sgStoFreeze.getCpCStoreEcode());
                updateBillItemRequest.setCpCStoreEname(sgStoFreeze.getCpCStoreEname());
                updateBillItemRequest.setStockType(item.getStockType());
                itemList.add(updateBillItemRequest);
            }
            billRequest.setItemList(itemList);

            String msgKey = SgConstants.SG_B_STO_FREEZE + ":" + sgStoFreeze.getBillNo();
            updateRequest.setMessageKey(msgKey);
            updateRequest.setBill(billRequest);
            updateRequest.setControlModel(controlRequest);
            updateRequest.setLoginUser(loginUser);
            if (log.isDebugEnabled()) {
                log.debug("单据编号:{}  开始冻结库存 入参:{}", sgStoFreeze.getBillNo(), JSONObject.toJSONString(updateRequest));
            }
            valueHolderV14 = updatedStorage(updateRequest);
            if (log.isDebugEnabled()) {
                log.debug("单据编号:{} 冻结库存结束 出参:{}", sgStoFreeze.getBillNo(), JSONObject.toJSONString(valueHolderV14));
            }
        } catch (Exception e) {
            AssertUtils.logAndThrowException("库存冻结-同步库存接口服务异常！", e, loginUser.getLocale());
        }
        return valueHolderV14;
    }

    /**
     * 逻辑在途单更新库存
     *
     * @param sgStoreIn         逻辑在途单
     * @param user              用户
     * @param orgSgStoreInItems 逻辑在途单明细
     * @param negativeStock     负库存控制
     * @param serviceNode       业务节点
     * @param action            动作定义
     * @return ValueHolderV14
     */
    public ValueHolderV14<SgStorageUpdateResult> updateStoInStoragePrein(SgBStoIn sgStoreIn,
                                                                         User user,
                                                                         List<SgBStoInItem> orgSgStoreInItems,
                                                                         Map<Long, SgCpCStore> negativeStock, Long serviceNode, String action) {
        ValueHolderV14<SgStorageUpdateResult> storageResult = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        List<AbstractSgStorageUpdateBillItemRequest> itemList = new ArrayList<>();
        for (SgBStoInItem item : orgSgStoreInItems) {
            BigDecimal qtyPrein = item.getQtyPrein();

            if (0 == BigDecimal.ZERO.compareTo(qtyPrein)) {
                continue;
            }

            SgStorageUpdateBillItemLsRequest storage = new SgStorageUpdateBillItemLsRequest();
            BeanUtils.copyProperties(item, storage);
            storage.setBillItemId(item.getId());
            storage.setQtyStorageChange(qtyPrein);

            SgStorageUpdateControlRequest controlRequest = getSgStorageUpdateControlRequest(
                    Boolean.FALSE, negativeStock, item.getCpCStoreId());

            storage.setControlmodel(controlRequest);
            //加上生产日期
            storage.setProduceDate(item.getProduceDate());
            itemList.add(storage);
        }

        //明细为空不更新库存
        if (CollectionUtils.isNotEmpty(itemList)) {
            String msgKey = SgConstants.SG_B_STO_IN + ":" + sgStoreIn.getBillNo();
            SgStorageSingleUpdateRequest updateRequest = getSgStorageSingleUpdateRequest(sgStoreIn.getId(),
                    sgStoreIn.getBillNo(), sgStoreIn.getBillDate(), sgStoreIn.getSourceBillType(), serviceNode,
                    false, sgStoreIn.getSourceBillId(),
                    sgStoreIn.getSourceBillNo(),
                    itemList, SgConstantsIF.PREOUT_RESULT_ERROR
                    , msgKey, action, user, new Date());

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("单据逻辑在途单[{}],开始更新逻辑仓在库入参:{}", "updateStoInStoragePrein"),
                        sgStoreIn.getBillNo(), JSONObject.toJSONString(updateRequest));
            }
            storageResult = updatedStorage(updateRequest);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("单据逻辑在途单[{}],开始更新逻辑仓在库出参:{}", "updateStoInStoragePrein"),
                        sgStoreIn.getBillNo(), JSONObject.toJSONString(storageResult));
            }
        }

        return storageResult;
    }

    /**
     * 逻辑入库单入库
     * 更新逻辑仓库存
     *
     * @param negativeStock    负库存控制
     * @param inResultItemList 入库明细
     * @param inResult         逻辑入库单
     * @param request          request
     * @param changeDate       变动时间
     * @param user             user
     * @return ValueHolderV14
     */
    public ValueHolderV14<SgStorageUpdateResult> updateSgBStoInResultStoarge(Map<Long, SgCpCStore> negativeStock,
                                                                             List<SgBStoInResultItem> inResultItemList,
                                                                             SgBStoInResult inResult,
                                                                             SgBStoInBillSaveRequest request, Date changeDate, User user) {
        ValueHolderV14<SgStorageUpdateResult> storageResult = new ValueHolderV14<>();

        try {
            List<AbstractSgStorageUpdateBillItemRequest> itemList = new ArrayList<>();

            for (SgBStoInResultItem item : inResultItemList) {
                BigDecimal qty = item.getQty();

                if (0 == BigDecimal.ZERO.compareTo(qty)) {
                    continue;
                }

                SgStorageUpdateBillItemLsRequest storage = new SgStorageUpdateBillItemLsRequest();
                BeanUtils.copyProperties(item, storage);
                storage.setBillItemId(item.getId());
                storage.setQtyStorageChange(qty);

//                storage.setSubBillItemId(item.getId());
//                storage.setSubBillNo(inResult.getBillNo());
//                storage.setSubBillId(inResult.getId());

                storage.setCpCStoreId(inResult.getCpCStoreId());
                storage.setCpCStoreEcode(inResult.getCpCStoreEcode());
                storage.setCpCStoreEname(inResult.getCpCStoreEname());

                SgStorageUpdateControlRequest controlRequest = getSgStorageUpdateControlRequest(
                        Boolean.FALSE, negativeStock, inResult.getCpCStoreId());

                storage.setControlmodel(controlRequest);
                //加上生产日期
                storage.setProduceDate(item.getProduceDate());
                itemList.add(storage);
            }


            //明细为空不更新库存
            if (CollectionUtils.isNotEmpty(itemList)) {

                String msgKey = SgConstants.SG_B_STO_IN_RESULT + ":" + inResult.getBillNo();
                SgStorageSingleUpdateRequest updateRequest = getSgStorageSingleUpdateRequest(inResult.getId(),
                        inResult.getBillNo(), inResult.getBillDate(), inResult.getSourceBillType(), SgConstantsIF.SERVICE_NODE_STO_IN_RESULT_SUBMIT,
                        request != null && request.getIsCancel() != null ? request.getIsCancel() : false, inResult.getSourceBillId(),
                        inResult.getSourceBillNo(),
                        itemList, request != null && request.getPreoutWarningType() != null ? request.getPreoutWarningType() : SgConstantsIF.PREOUT_RESULT_ERROR
                        , msgKey, SgStoreConstants.SUBMIT, user, changeDate);

                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("单据逻辑入库单[{}],开始更新逻辑仓在库入参:{}", "updateSgBStoInResultStoarge"),
                            inResult.getBillNo(), JSONObject.toJSONString(updateRequest));

                }
                storageResult = updatedStorage(updateRequest);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("单据逻辑入库单[{}],更新逻辑仓在库出参:{}", "updateSgBStoInResultStoarge"),
                            inResult.getBillNo(), JSONObject.toJSONString(storageResult));
                }

            }
        } catch (Exception e) {
            AssertUtils.logAndThrowException("同步库存占用接口服务异常！", e, user.getLocale());
        }


        return storageResult;

    }

    /**
     * 逻辑入库单更新-整单
     *
     * @param receive               逻辑收货单id
     * @param stoInItems            逻辑收货单有占用变化明细
     * @param request               request-model
     * @param map                   原逻辑收货单明细 k-v   <skuid+逻辑仓id , 明细>
     * @param action                动作 - 审核、作废用
     * @param loginUser             登录用户
     * @param isLast                是否最后一次入库标志
     * @param negativeStockStoreMap 逻辑仓map 主要用来判断是否允许负库存
     * @param isNegativePrein       占用是否允许负库存
     * @param serviceNode           业务节点
     */
    public ValueHolderV14<SgStorageUpdateResult> updateSgBStoInPrein(SgBStoIn receive,
                                                                     List<SgBStoInItem> stoInItems,
                                                                     SgBStoInBillSaveRequest request,
                                                                     HashMap<String, SgBStoInItem> map,
                                                                     String action,
                                                                     User loginUser, Boolean isLast, Map<Long,
            SgCpCStore> negativeStockStoreMap,
                                                                     Boolean isNegativePrein, Long serviceNode, Date changeDate
            , Map<String, Long> skuIdMap, SgBStoInResult result) {
        ValueHolderV14<SgStorageUpdateResult> storageResult = new ValueHolderV14<>();
        try {
            //单据明细信息
            List<AbstractSgStorageUpdateBillItemRequest> itemList = request != null ?
                    buildStoInItemsBySave(request, map, negativeStockStoreMap, skuIdMap, result) :
                    buildStoInItems(stoInItems, action, isLast, negativeStockStoreMap, isNegativePrein, skuIdMap, result);

            //明细为空不更新库存
            if (CollectionUtils.isNotEmpty(itemList)) {
                String msgKey = SgConstants.SG_B_STO_IN + ":" + receive.getBillNo();
                SgStorageSingleUpdateRequest updateRequest = getSgStorageSingleUpdateRequest(receive.getId(),
                        receive.getBillNo(), receive.getBillDate(), receive.getSourceBillType(), serviceNode,
                        request != null && request.getIsCancel() != null ? request.getIsCancel() : false, receive.getSourceBillId(),
                        receive.getSourceBillNo(),
                        itemList, request != null && request.getPreoutWarningType() != null ? request.getPreoutWarningType() : SgConstantsIF.PREOUT_RESULT_ERROR
                        , msgKey, action, loginUser, changeDate);

                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("来源单据[{}],开始更新库存在途入参:{}", "updateSgBStoInPrein"),
                            receive.getBillNo(), JSONObject.toJSONString(updateRequest));
                }
                storageResult = updatedStorage(updateRequest);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("来源单据[{}],开始更新库存在途出参:{}", "updateSgBStoInPrein"),
                            receive.getBillNo(), JSONObject.toJSONString(storageResult));
                }
            }
        } catch (Exception e) {
            AssertUtils.logAndThrowException("同步库存占用接口服务异常！", e, loginUser.getLocale());
        }
        return storageResult;
    }

    private List<AbstractSgStorageUpdateBillItemRequest> buildStoInItems(List<SgBStoInItem> stoInItems,
                                                                         String action, Boolean islast,
                                                                         Map<Long, SgCpCStore> negativeStockStoreMap,
                                                                         Boolean isNegativePrein, Map<String, Long> skuIdMap, SgBStoInResult result) {
        List<AbstractSgStorageUpdateBillItemRequest> itemList = new ArrayList<>();

        for (SgBStoInItem item : stoInItems) {
            BigDecimal preinQtyChange = item.getQtyPrein().negate();
//            BigDecimal storageQtyChange = item.getQtyIn();
            if (!SgStoreConstants.SUBMIT.equals(action)) {
                if (BigDecimal.ZERO.compareTo(preinQtyChange) == 0) {
                    continue;
                }
                SgStorageUpdateBillItemLsRequest prein = new SgStorageUpdateBillItemLsRequest();
                BeanUtils.copyProperties(item, prein);
                prein.setBillItemId(item.getId());
                prein.setQtyPreinChange(preinQtyChange);
                SgStorageUpdateControlRequest controlRequest = getSgStorageUpdateControlRequest(
                        isNegativePrein, negativeStockStoreMap, item.getCpCStoreId());
                prein.setControlmodel(controlRequest);
//                if (skuIdMap != null && result != null) {
//                    prein.setSubBillItemId(skuIdMap.get(item.getPsCSkuEcode() + SgConstantsIF.MAP_KEY_DIVIDER + item.getProduceDate()));
//                    prein.setSubBillNo(result.getBillNo());
//                    prein.setSubBillId(result.getId());
//                }
                //加上生产日期
                prein.setProduceDate(item.getProduceDate());
                itemList.add(prein);
            }
            // 在途释放改造，释放不动库存
            if (SgStoreConstants.SUBMIT.equals(action) || islast) {
//                if (0 == BigDecimal.ZERO.compareTo(preinQtyChange) &&
//                        0 == BigDecimal.ZERO.compareTo(storageQtyChange)) {
//                    continue;
//                }

                if (0 == BigDecimal.ZERO.compareTo(preinQtyChange)) {
                    continue;
                }

                SgStorageUpdateBillItemLsRequest storage = new SgStorageUpdateBillItemLsRequest();
                BeanUtils.copyProperties(item, storage);
                storage.setBillItemId(item.getId());
                storage.setQtyPreinChange(preinQtyChange);
//                storage.setQtyStorageChange(storageQtyChange);
                storage.setQtyStorageChange(BigDecimal.ZERO);
//                if (skuIdMap != null && result != null) {
//                    storage.setSubBillItemId(skuIdMap.get(item.getPsCSkuEcode() + SgConstantsIF.MAP_KEY_DIVIDER + item.getProduceDate()));
//                    storage.setSubBillNo(result.getBillNo());
//                    storage.setSubBillId(result.getId());
//                }
                SgStorageUpdateControlRequest controlRequest = getSgStorageUpdateControlRequest(
                        isNegativePrein, negativeStockStoreMap, item.getCpCStoreId());
                storage.setControlmodel(controlRequest);
                //加上生产日期
                storage.setProduceDate(item.getProduceDate());
                itemList.add(storage);
            }
        }

        return itemList;
    }

    private List<AbstractSgStorageUpdateBillItemRequest> buildStoInItemsBySave(SgBStoInBillSaveRequest request,
                                                                               HashMap<String, SgBStoInItem> map,
                                                                               Map<Long, SgCpCStore> negativeStockStoreMap,
                                                                               Map<String, Long> skuIdMap, SgBStoInResult result) {
        List<AbstractSgStorageUpdateBillItemRequest> itemList = new ArrayList<>();
        //全量，要先把原本扣得库存给加回来
        if (SgConstantsIF.ITEM_UPDATE_TYPE_ALL == request.getUpdateMethod()) {
            //逆向冲单明细数据
            request.getSgStoInItemSaveRequestList().forEach(itemSaveRequest -> {
                if (itemSaveRequest.getIsSgBStoInStorage()) {
                    //原逻辑在途单明细
                    SgBStoInItem receiveItem =
                            map.get(itemSaveRequest.getPsCSkuId() + SgConstantsIF.MAP_KEY_DIVIDER + itemSaveRequest.getCpCStoreId()
                                    + SgConstantsIF.MAP_KEY_DIVIDER + itemSaveRequest.getSourceBillItemId() +
                                    SgConstantsIF.MAP_KEY_DIVIDER + itemSaveRequest.getProduceDate());
                    //原逻辑在途单历史在途不为0
                    if (receiveItem != null && 0 != BigDecimal.ZERO.compareTo(receiveItem.getQtyPrein())) {
                        SgStorageUpdateBillItemLsRequest item = new SgStorageUpdateBillItemLsRequest();
                        BeanUtils.copyProperties(receiveItem, item);
                        item.setBillItemId(receiveItem.getId());
                        if (skuIdMap != null && result != null) {
                            item.setSubBillItemId(skuIdMap.get(item.getPsCSkuEcode() + SgConstantsIF.MAP_KEY_DIVIDER + item.getProduceDate()));
                            item.setSubBillNo(result.getBillNo());
                            item.setSubBillId(result.getId());
                        }
                        //原在途库存取反
                        item.setQtyPreinChange(receiveItem.getQtyPrein().negate());
                        SgStorageUpdateControlRequest controlRequest = getSgStorageUpdateControlRequest(
                                request.getIsNegativePrein(), negativeStockStoreMap, receiveItem.getCpCStoreId());
                        item.setControlmodel(controlRequest);
                        //加上生产日期
                        item.setProduceDate(receiveItem.getProduceDate());
                        itemList.add(item);
                    }
                }
            });
        }

        request.getSgStoInItemSaveRequestList().forEach(itemSaveRequest -> {
            if (itemSaveRequest.getIsSgBStoInStorage() && 0 != BigDecimal.ZERO.compareTo(itemSaveRequest.getQtyPrein())) {
                SgStorageUpdateBillItemLsRequest item = new SgStorageUpdateBillItemLsRequest();
                BeanUtils.copyProperties(itemSaveRequest, item);
                item.setBillItemId(itemSaveRequest.getId());
                item.setQtyPreinChange(itemSaveRequest.getQtyPrein());

                SgStorageUpdateControlRequest controlRequest =
                        getSgStorageUpdateControlRequest(request.getIsNegativePrein(), negativeStockStoreMap,
                                itemSaveRequest.getCpCStoreId());
                item.setControlmodel(controlRequest);
                item.setProduceDate(itemSaveRequest.getProduceDate());
                itemList.add(item);
            }
        });

        return itemList;
    }


    public SgStorageUpdateControlRequest getSgStorageUpdateControlRequest(Boolean isNegativePrein,
                                                                          Map<Long, SgCpCStore> negativeStockStoreMap
            , Long storeId) {
        SgStorageUpdateControlRequest controlRequest = new SgStorageUpdateControlRequest();
        Long isnegative = 1L;
        //单据的是否允许负库存优先级最高
        if ((isNegativePrein != null && isNegativePrein) || isNegativeInStorage) {
            isnegative = 0L;
        } else {
            if (MapUtils.isNotEmpty(negativeStockStoreMap)) {
                SgCpCStore cpCStore = negativeStockStoreMap.get(storeId);
                isnegative = cpCStore.getIsnegative();
                //1 开启负库存控制（不允许负库存）0 允许负库存 2 仅可用负库存
                isnegative = isnegative == null ? 1L : isnegative;
            }
        }
        //非共享占用是否允许负库存
        controlRequest.setNegativeUnsharedPreout(isnegative == 0);
        //共享占用是否允许负库存
        controlRequest.setNegativeSharedPreout(isnegative == 0);
        //在途是否允许负库存
        controlRequest.setNegativePrein(isnegative == 0);
        //在库是否允许负库存
        controlRequest.setNegativeStorage(isnegative == 0);
        //可用在库是否允许负库存 / 仅可用负库存
        controlRequest.setNegativeAvailable(isnegative == 0 || isnegative == 2);
        //冻结在库是否允许负库存
        controlRequest.setNegativeFreeze(isnegative == 0);

        return controlRequest;
    }

    public SgStorageUpdateControlRequest getSgStorageUpdateControlNegativAvailableRequest(Boolean isNegativePrein,
                                                                                          Map<Long, SgCpCStore> negativeStockStoreMap
            , Long storeId) {
        SgStorageUpdateControlRequest controlRequest = new SgStorageUpdateControlRequest();
        Long isnegative = 1L;
        //单据的是否允许负库存优先级最高
        if ((isNegativePrein != null && isNegativePrein) || isNegativeInStorage) {
            isnegative = 0L;
        } else {
            if (MapUtils.isNotEmpty(negativeStockStoreMap)) {
                SgCpCStore cpCStore = negativeStockStoreMap.get(storeId);
                isnegative = cpCStore.getIsnegative();
                //1 开启负库存控制（不允许负库存）0 允许负库存 2 仅可用负库存
                isnegative = isnegative == null ? 1L : isnegative;
            }
        }
        //非共享占用是否允许负库存
        controlRequest.setNegativeUnsharedPreout(isnegative == 0);
        //共享占用是否允许负库存
        controlRequest.setNegativeSharedPreout(isnegative == 0);
        //在途是否允许负库存
        controlRequest.setNegativePrein(isnegative == 0);
        //在库是否允许负库存
        controlRequest.setNegativeStorage(isnegative == 0);
        //可用在库是否允许负库存 / 仅可用负库存
        controlRequest.setNegativeAvailable(false);
        //冻结在库是否允许负库存
        controlRequest.setNegativeFreeze(isnegative == 0);

        return controlRequest;
    }

    /**
     * 库存占用+库存变动更新-整单
     * 逻辑占用单：新增，更新，释放，清空，作废，强制完成
     *
     * @param send             逻辑发货单id
     * @param stoOutItems      逻辑发货单有占用变化明细
     * @param request          request-model
     * @param map              原逻发货单明细 k-v   <skuid+逻辑仓id , 明细>
     * @param action           动作 - 审核、作废、清空用
     * @param loginUser        登录用户
     * @param negativeStock    逻辑仓map 主要用来判断是否允许负库存
     * @param isNegativePreout 占用是否允许负库存
     * @param serviceNode      业务节点
     * @param share            共享or非共享 true = 共享  false = 非共享
     * @return 库存占用更新结果
     */
    public ValueHolderV14<SgStorageUpdateResult> updateStoOutStoragePreout(SgBStoOut send,
                                                                           List<SgBStoOutItem> stoOutItems,
                                                                           SgR3BaseRequest request,
                                                                           HashMap<String, SgBStoOutItem> map,
                                                                           String action, User loginUser,
                                                                           Map<Long, SgCpCStore> negativeStock,
                                                                           Boolean isNegativePreout, Long serviceNode,
                                                                           boolean share, Date changeDate,
                                                                           Map<String, Long> skuIdMap, SgBStoOutResult result) {
        ValueHolderV14<SgStorageUpdateResult> storageResult = new ValueHolderV14<>(ResultCode.SUCCESS,
                "库存占用更新成功");

        List<AbstractSgStorageUpdateBillItemRequest> positiveItemList = new ArrayList<>();
        List<AbstractSgStorageUpdateBillItemRequest> negativeItemList = new ArrayList<>();
        List<AbstractSgStorageUpdateBillItemRequest> outResultItemList = new ArrayList<>();
        List<String> redisBillFtpKeyList = new ArrayList<>();

        SgBStoOutBillSaveRequest saveRequest = null;
        SgBStoOutBillReleaseRequest releaseRequest = null;
        if (action.equals(SgStoreConstants.SAVE)) {
            saveRequest = (SgBStoOutBillSaveRequest) request;
        } else if (action.equals(SgStoreConstants.SUBMIT)) {
            releaseRequest = (SgBStoOutBillReleaseRequest) request;
        }

        //新增/更新
        if (action.equals(SgStoreConstants.SAVE)) {
            SgStoreStorageResult sgStoreStorageResult = buildSendStorageItems(saveRequest, map, negativeStock, share, skuIdMap, result);
            List<AbstractSgStorageUpdateBillItemRequest> negativeItems = sgStoreStorageResult.getNegativeItemList();
            List<AbstractSgStorageUpdateBillItemRequest> positiveItems = sgStoreStorageResult.getPositiveItemList();
            if (CollectionUtils.isNotEmpty(negativeItems)) {
                negativeItemList.addAll(negativeItems);
            }
            if (CollectionUtils.isNotEmpty(positiveItems)) {
                positiveItemList.addAll(positiveItems);
            }
        } else {//释放，清空，作废，强制完成
            //作废，清空，强制完成：构建占用数+库存，以逻辑占用单的明细为准
            //释放：只构建占用数，以逻辑占用单的明细为准
            List<AbstractSgStorageUpdateBillItemRequest> itemRequests = buildSendStorageItems(stoOutItems, negativeStock, isNegativePreout,
                    share, skuIdMap, result);
            if (CollectionUtils.isNotEmpty(itemRequests)) {
                positiveItemList.addAll(itemRequests);
            }
            //释放时，出库的以逻辑出库单明细为准
            if (action.equals(SgStoreConstants.SUBMIT)) {
                List<AbstractSgStorageUpdateBillItemRequest> itemRequestList = buildSendStorageItems(releaseRequest, action, negativeStock, isNegativePreout, skuIdMap, result);
                outResultItemList.addAll(itemRequestList);
            }
        }

        log.info(LogUtil.format("释放逻辑占用单开始，negativeItemList：{},positiveItemList:{},outResultItemList:{}",
                        "SgStoreStorageService.updateStoOutStoragePreout"),
                JSON.toJSONString(negativeItemList), JSON.toJSONString(positiveItemList), JSON.toJSONString(outResultItemList));

        try {
            String msgKey = SgConstants.MSG_TAG_STO_OUT + ":" + send.getBillNo();
            if (CollectionUtils.isNotEmpty(negativeItemList)) {
                SgStorageSingleUpdateRequest updateRequest = getSgStorageSingleUpdateRequest(send.getId(),
                        send.getBillNo(), send.getBillDate(), send.getSourceBillType(), serviceNode,
                        saveRequest != null && saveRequest.getIsCancel() != null ? saveRequest.getIsCancel() : Boolean.FALSE,
                        send.getSourceBillId(), send.getSourceBillNo(), negativeItemList,
                        saveRequest != null && saveRequest.getPreoutWarningType() != null ? saveRequest.getPreoutWarningType() : SgConstantsIF.PREOUT_RESULT_ERROR,
                        msgKey, action, loginUser, changeDate);

                log.info("来源单据id[" + send.getSourceBillId() + "],开始占用库存占用入参(反向)" + JSONObject.toJSONString(updateRequest));
                ValueHolderV14<SgStorageUpdateResult> valueHolderV14 = updatedStorage(updateRequest);
                log.info("来源单据id[" + send.getSourceBillId() + "],占用库存占用出参(反向)" + storageResult.toJSONObject().toJSONString());
                //失败直接返回
                if (!valueHolderV14.isOK()) {
                    return valueHolderV14;
                }

                if (valueHolderV14.getData() != null &&
                        CollectionUtils.isNotEmpty(valueHolderV14.getData().getRedisBillFtpKeyList())) {
                    redisBillFtpKeyList.addAll(storageResult.getData().getRedisBillFtpKeyList());
                }
            }

            if (CollectionUtils.isNotEmpty(positiveItemList)) {
                SgStorageSingleUpdateRequest updateRequest = getSgStorageSingleUpdateRequest(send.getId(),
                        send.getBillNo(), send.getBillDate(), send.getSourceBillType(), serviceNode,
                        saveRequest != null && saveRequest.getIsCancel() != null ? saveRequest.getIsCancel() : Boolean.FALSE,
                        send.getSourceBillId(), send.getSourceBillNo(), positiveItemList,
                        saveRequest != null && saveRequest.getPreoutWarningType() != null ? saveRequest.getPreoutWarningType() : SgConstantsIF.PREOUT_RESULT_ERROR,
                        msgKey, action, loginUser, changeDate);

                log.info("来源单据id[" + send.getSourceBillId() + "],开始占用库存占用入参(正向)" + JSONObject.toJSONString(updateRequest));
                storageResult = updatedStorage(updateRequest);
                log.info("来源单据id[" + send.getSourceBillId() + "],占用库存占用出参(正向)" + storageResult.toJSONObject().toJSONString());
                //占用失败直接返回
                if (!storageResult.isOK()) {
                    // 回滚库存
                    StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, loginUser);
                    return storageResult;
                }
                SgStorageUpdateResult resultData = storageResult.getData();
                if (resultData != null) {
                    if (CollectionUtils.isNotEmpty(resultData.getRedisBillFtpKeyList())) {
                        redisBillFtpKeyList.addAll(resultData.getRedisBillFtpKeyList());
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(outResultItemList)) {
                SgStorageSingleUpdateRequest updateRequest = getSgStorageSingleUpdateRequest(result.getId(),
                        result.getBillNo(), result.getBillDate(), send.getSourceBillType(), serviceNode,
                        saveRequest != null && saveRequest.getIsCancel() != null ? saveRequest.getIsCancel() : Boolean.FALSE,
                        send.getSourceBillId(), send.getSourceBillNo(), outResultItemList,
                        saveRequest != null && saveRequest.getPreoutWarningType() != null ? saveRequest.getPreoutWarningType() : SgConstantsIF.PREOUT_RESULT_ERROR,
                        msgKey, action, loginUser, changeDate);

                log.info("来源单据id[" + send.getSourceBillId() + "],开始出库更新库存入参" + JSONObject.toJSONString(updateRequest));
                storageResult = updatedStorage(updateRequest);
                log.info("来源单据id[" + send.getSourceBillId() + "],开始出库更新库存出参" + storageResult.toJSONObject().toJSONString());
                //占用失败直接返回
                if (!storageResult.isOK()) {
                    // 回滚库存
                    StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, loginUser);
                    return storageResult;
                }
                SgStorageUpdateResult resultData = storageResult.getData();
                if (resultData != null) {
                    if (CollectionUtils.isNotEmpty(resultData.getRedisBillFtpKeyList())) {
                        redisBillFtpKeyList.addAll(resultData.getRedisBillFtpKeyList());
                    }
                }
            }
        } catch (Exception e) {
            // 回滚库存
            StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, SystemUserResource.getRootUser());
            AssertUtils.logAndThrowException("释放逻辑占用单失败", e);
        }

        SgStorageUpdateResult sgStorageUpdateResult = new SgStorageUpdateResult();
        sgStorageUpdateResult.setRedisBillFtpKeyList(redisBillFtpKeyList);
        storageResult.setData(sgStorageUpdateResult);

        return storageResult;
    }

    /**
     * 构建更新库存占用数的入参
     *
     * @param stoOutItems
     * @param negativeStock
     * @param isNegativePreout
     * @param share
     * @param skuIdMap
     * @param result
     * @return
     */
    private List<AbstractSgStorageUpdateBillItemRequest> buildSendStorageItems(List<SgBStoOutItem> stoOutItems,
                                                                               Map<Long, SgCpCStore> negativeStock,
                                                                               boolean isNegativePreout,
                                                                               Boolean share
            , Map<String, Long> skuIdMap, SgBStoOutResult result) {
        List<AbstractSgStorageUpdateBillItemRequest> itemList = new ArrayList<>();
        stoOutItems.forEach(stoOutItem -> {
            BigDecimal preoutQtyChange = stoOutItem.getQtyPreout().negate();
            if (0 != BigDecimal.ZERO.compareTo(preoutQtyChange)) {
                SgStorageUpdateBillItemLsRequest item = new SgStorageUpdateBillItemLsRequest();
                BeanUtils.copyProperties(stoOutItem, item);
                item.setBillItemId(stoOutItem.getId());
                item.setSourceItemId(stoOutItem.getSourceBillItemId());
                // 根据来源单据是否零售发货单 判断 是共享or非共享
                if (share) {
                    item.setQtySharedPreoutChange(preoutQtyChange);
                } else {
                    item.setQtyUnsharedPreoutChange(preoutQtyChange);
                }
                setIsNegativePreout(item, negativeStock, isNegativePreout, SgConstantsIF.PREOUT_RESULT_ERROR);
                itemList.add(item);
            }
        });

        return itemList;
    }

    /**
     * 占用单释放时构建库存变动的参数
     *
     * @param releaseRequest
     * @param action
     * @param negativeStock
     * @param isNegativePreout
     * @param skuIdMap
     * @param result
     * @return
     */
    private List<AbstractSgStorageUpdateBillItemRequest> buildSendStorageItems(SgBStoOutBillReleaseRequest releaseRequest,
                                                                               String action,
                                                                               Map<Long, SgCpCStore> negativeStock,
                                                                               boolean isNegativePreout
            , Map<String, Long> skuIdMap, SgBStoOutResult result) {
        SgBStoOutResult outResult = releaseRequest.getOutResult();
        List<SgBStoOutResultItem> outResultItemList = releaseRequest.getOutResultItemList();
        List<AbstractSgStorageUpdateBillItemRequest> itemList = new ArrayList<>();
        outResultItemList.forEach(stoOutItem -> {
            BigDecimal storageQtyChange = stoOutItem.getQty().negate();
            if (SgStoreConstants.SUBMIT.equals(action) && 0 != BigDecimal.ZERO.compareTo(storageQtyChange)) {
                SgStorageUpdateBillItemLsRequest item = new SgStorageUpdateBillItemLsRequest();
                BeanUtils.copyProperties(stoOutItem, item);
                item.setBillItemId(stoOutItem.getId());
                item.setSourceItemId(stoOutItem.getSourceBillItemId());
//                if (skuIdMap != null && result != null) {
//                    item.setSubBillItemId(skuIdMap.get(SkuKeyBuildUtil.buildKey(item)));
//                    item.setSubBillNo(result.getBillNo());
//                    item.setSubBillId(result.getId());
//                }
                item.setQtyStorageChange(storageQtyChange);

                item.setCpCStoreId(outResult.getCpCStoreId());
                item.setCpCStoreEcode(outResult.getCpCStoreEcode());
                item.setCpCStoreEname(outResult.getCpCStoreEname());

                setIsNegativePreout(item, negativeStock, isNegativePreout, SgConstantsIF.PREOUT_RESULT_ERROR);
                itemList.add(item);
            }
        });

        return itemList;
    }

    private SgStoreStorageResult buildSendStorageItems(SgBStoOutBillSaveRequest request,
                                                       HashMap<String, SgBStoOutItem> map,
                                                       Map<Long, SgCpCStore> negativeStock,
                                                       Boolean share, Map<String, Long> skuIdMap,
                                                       SgBStoOutResult result) {
        List<AbstractSgStorageUpdateBillItemRequest> positiveItemList = new ArrayList<>();
        List<AbstractSgStorageUpdateBillItemRequest> negativeItemList = new ArrayList<>();
        if (SgConstantsIF.ITEM_UPDATE_TYPE_ALL == request.getUpdateMethod()) { //全量
            //逆向冲单明细数据
            for (SgBStoOutItemSaveRequest itemSaveRequest : request.getSgBStoOutItemSaveRequests()) {
                if (itemSaveRequest.getIssgBStoInStorage()) {
                    //原逻辑发货单明细
                    SgBStoOutItem sendItem =
                            map.get(SgBStoOutSaveService.SkuProduceDateSaveParam.buildItemExistsKey(itemSaveRequest));
                    if (sendItem != null && 0 != BigDecimal.ZERO.compareTo(sendItem.getQtyPreout().negate())) {
                        SgStorageUpdateBillItemLsRequest item = new SgStorageUpdateBillItemLsRequest();
                        BeanUtils.copyProperties(sendItem, item);
                        item.setBillItemId(sendItem.getId());
                        item.setSourceItemId(sendItem.getSourceBillItemId());
                        item.setCpCStoreId(sendItem.getCpCStoreId());
//                        if (skuIdMap != null && result != null) {
//                            item.setSubBillItemId(skuIdMap.get(SkuKeyBuildUtil.buildKey(item)));
//                            item.setSubBillNo(result.getBillNo());
//                            item.setSubBillId(result.getId());
//                        }

                        // 根据来源单据是否零售发货单 判断 是共享or非共享
                        if (share) {
                            item.setQtySharedPreoutChange(sendItem.getQtyPreout().negate());
                        } else {
                            item.setQtyUnsharedPreoutChange(sendItem.getQtyPreout().negate());
                        }
                        setIsNegativePreout(item, negativeStock, request.getIsNegativePrein(),
                                request.getPreoutWarningType());
                        negativeItemList.add(item);
                    }
                }
            }
        }

        for (SgBStoOutItemSaveRequest itemSaveRequest : request.getSgBStoOutItemSaveRequests()) {
            if (itemSaveRequest.getIssgBStoInStorage() && 0 != BigDecimal.ZERO.compareTo(itemSaveRequest.getQtyPreout())) {
                SgStorageUpdateBillItemLsRequest item = new SgStorageUpdateBillItemLsRequest();
                BeanUtils.copyProperties(itemSaveRequest, item);
                item.setBillItemId(itemSaveRequest.getId());
                item.setSourceItemId(itemSaveRequest.getSourceBillItemId());
//                if (skuIdMap != null && result != null) {
//                    item.setSubBillItemId(skuIdMap.get(SkuKeyBuildUtil.buildKey(item)));
//                    item.setSubBillNo(result.getBillNo());
//                    item.setSubBillId(result.getId());
//                }
                // 根据来源单据是否零售发货单 判断 是共享or非共享
                if (share) {
                    item.setQtySharedPreoutChange(itemSaveRequest.getQtyPreout());
                } else {
                    item.setQtyUnsharedPreoutChange(itemSaveRequest.getQtyPreout());
                }
                setIsNegativePreout(item, negativeStock, request.getIsNegativePrein(), request.getPreoutWarningType());
                positiveItemList.add(item);
            }
        }
        SgStoreStorageResult sgStoreStorageResult = new SgStoreStorageResult();
        sgStoreStorageResult.setPositiveItemList(positiveItemList);
        sgStoreStorageResult.setNegativeItemList(negativeItemList);
        return sgStoreStorageResult;
    }

    public SgStorageSingleUpdateRequest getSgStorageSingleUpdateRequest(Long billId, String billNo, Date billDate,
                                                                        Integer billType,
                                                                        Long node, Boolean isCancel, Long sourceBillId,
                                                                        String sourceBillNo,
                                                                        List<AbstractSgStorageUpdateBillItemRequest> itemList,
                                                                        int preoutOperateType,
                                                                        String msgKey,
                                                                        String action,
                                                                        User user, Date changeDate) {
        SgStorageUpdateBillRequest billRequest = new SgStorageUpdateBillRequest();
        //单据信息
        billRequest.setBillId(billId);
        billRequest.setBillNo(billNo);
        billRequest.setBillDate(billDate);
        billRequest.setBillType(billType);
        if (changeDate == null) {
            billRequest.setChangeDate(new Timestamp(System.currentTimeMillis()));
        } else {
            billRequest.setChangeDate(changeDate);
        }

        billRequest.setServiceNode(node);
        billRequest.setIsCancel(isCancel);
        billRequest.setSourceBillId(sourceBillId);
        billRequest.setSourceBillNo(sourceBillNo);

        billRequest.setItemList(itemList);

//        SgStorageUpdateControlRequest controlModel = new SgStorageUpdateControlRequest();
//        controlModel.setPreoutOperateType(preoutOperateType);
//        controlModel.setNegativeUnsharedPreout(true);
//        controlModel.setNegativePrein(true);
        SgStorageSingleUpdateRequest updateRequest = new SgStorageSingleUpdateRequest();
        updateRequest.setBill(billRequest);
        if (!SgStoreConstants.VOID.equals(action)) {
            msgKey += System.currentTimeMillis();
        }
        updateRequest.setMessageKey(msgKey);
        updateRequest.setLoginUser(user);
//        updateRequest.setControlModel(controlModel);
        return updateRequest;
    }

    public void setIsNegativePreout(SgStorageUpdateBillItemLsRequest item,
                                    Map<Long, SgCpCStore> negativeStock,
                                    Boolean isNegativePrein,
                                    Integer preoutWarningType) {
        if (MapUtils.isNotEmpty(negativeStock)) {
            SgStorageUpdateControlRequest controlRequest = new SgStorageUpdateControlRequest();
            SgCpCStore store = negativeStock.get(item.getCpCStoreId());
            Long isnegative = null;
            if (store != null) {
                isnegative = store.getIsnegative();
            }

            isnegative = isnegative == null ? 1L : isnegative;
            if (isNegativePrein != null && isNegativePrein) {
                isnegative = 0L;
            }

            log.info(LogUtil.format("setIsNegativePreout.isnegative:{}", "setIsNegativePreout", isnegative));

            //非共享占用是否允许负库存
            controlRequest.setNegativeUnsharedPreout(isnegative == 0);
            //共享占用是否允许负库存
            controlRequest.setNegativeSharedPreout(isnegative == 0);
            //在途是否允许负库存
            controlRequest.setNegativePrein(isnegative == 0);
            //在库是否允许负库存
            controlRequest.setNegativeStorage(isnegative == 0);
            //可用在库是否允许负库存 仅负可用
            controlRequest.setNegativeAvailable(isnegative == 0 || isnegative == 2);
            //冻结在库是否允许负库存
            controlRequest.setNegativeFreeze(isnegative == 0);

            controlRequest.setPreoutOperateType(preoutWarningType);
            item.setControlmodel(controlRequest);
        }
    }

    public ValueHolderV14<SgStorageUpdateResult> updatedStorage(SgStorageSingleUpdateRequest request) {
        ValueHolderV14<SgStorageUpdateResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        SgStorageUpdateResult updateResult = new SgStorageUpdateResult();

        ValueHolderV14<SgStorageBillUpdateResult> billUpdateResult;
        if (sgStorageControlConfig.isRedisFunction()) {
            billUpdateResult = redisUpdateService.updateStorageBill(request);
        } else {
            billUpdateResult = transUpdateService.updateStorageBill(request);
        }

        if (billUpdateResult != null && billUpdateResult.getData() != null) {
            updateResult = convertSgUpdateResult(billUpdateResult.getData());
        }

        holder.setData(updateResult);
        if (billUpdateResult != null) {
            holder.setCode(billUpdateResult.getCode());
            holder.setMessage(billUpdateResult.getMessage());
        }

        return holder;
    }

    public static SgStorageUpdateResult convertSgUpdateResult(SgStorageBillUpdateResult updateResult) {
        SgStorageUpdateResult result = new SgStorageUpdateResult();
        List<AbstractSgStorageOutStockResult> outStockItemList = new ArrayList<>();

        if (updateResult != null) {
            result.setErrorBillItemQty(updateResult.getErrorBillItemQty());
            result.setPreoutUpdateResult(updateResult.getPreoutUpdateResult());
            result.setRedisBillFtpKeyList(updateResult.getRedisBillFtpKeyList());

            //解析缺货明细列表
            if (!CollectionUtils.isEmpty(updateResult.getOutStockItemList())) {

                for (AbstractSgStorageUpdateCommonModel commonModel : updateResult.getOutStockItemList()) {
                    SgStorageOutStockLsResult outResult = new SgStorageOutStockLsResult();
                    BeanUtils.copyProperties(commonModel, outResult);
                    outStockItemList.add(outResult);
                }
                result.setOutStockItemList(outStockItemList);
            }

        }
        return result;
    }

    public SgStorageUpdateBillSaRequest getSgSaStorageSingleUpdateRequest(Long billId, String billNo, Date billDate,
                                                                          Integer billType,
                                                                          Long node, Boolean isCancel,
                                                                          Long sourceBillId,
                                                                          String sourceBillNo,
                                                                          List<SgStorageUpdateBillItemSaRequest> itemList,
                                                                          int preoutOperateType) {
        SgStorageUpdateBillSaRequest billRequest = new SgStorageUpdateBillSaRequest();
        //单据信息
        billRequest.setBillId(billId);
        billRequest.setBillNo(billNo);
        billRequest.setBillDate(billDate);
        billRequest.setBillType(billType);
        billRequest.setChangeDate(new Timestamp(System.currentTimeMillis()));
        billRequest.setServiceNode(node);
        billRequest.setIsCancel(isCancel);
        billRequest.setSourceBillId(sourceBillId);
        billRequest.setSourceBillNo(sourceBillNo);
        billRequest.setItemList(itemList);
        SgStorageUpdateControlRequest controlModel = new SgStorageUpdateControlRequest();
        controlModel.setPreoutOperateType(preoutOperateType);
        return billRequest;
    }

    public SgStorageUpdateBillRequest getSgStorageSingleUpdateRequest(Long billId, String billNo, Date billDate,
                                                                      Integer billType,
                                                                      Long node, Boolean isCancel, Long sourceBillId,
                                                                      String sourceBillNo,
                                                                      List itemList,
                                                                      int preoutOperateType) {
        SgStorageUpdateBillRequest billRequest = new SgStorageUpdateBillRequest();
        //单据信息
        billRequest.setBillId(billId);
        billRequest.setBillNo(billNo);
        billRequest.setBillDate(billDate);
        billRequest.setBillType(billType);
        billRequest.setChangeDate(new Timestamp(System.currentTimeMillis()));
        billRequest.setServiceNode(node);
        billRequest.setIsCancel(isCancel);
        billRequest.setSourceBillId(sourceBillId);
        billRequest.setSourceBillNo(sourceBillNo);
        billRequest.setItemList(itemList);
        SgStorageUpdateControlRequest controlModel = new SgStorageUpdateControlRequest();
        controlModel.setPreoutOperateType(preoutOperateType);
        return billRequest;
    }

    public void updateStoOutSsStoragePreout(SgBStoOut sgStoreOut,
                                            User loginUser,
                                            List<SgBStoOutItem> sgStoreOutItems,
                                            HashMap<Long, SgCShareStore> sgShareStoreHashMap,
                                            Map<Long, SgCpCStore> negativeStock, List<String> redisBillFtpKeyList) {
        try {
            //最外层
            SgStorageBatchUpdateRequest updateSsRequest = new SgStorageBatchUpdateRequest();
            // 需要更新的参数
            List<SgStorageUpdateBillRequest> billSaRequests = new ArrayList<>();
            // 需要更新的参数明细
            List<SgStorageUpdateBillItemSsRequest> itemSaRequests = new ArrayList<>();

            for (SgBStoOutItem item : sgStoreOutItems) {
                SgStorageUpdateBillItemSsRequest itemSaRequest = new SgStorageUpdateBillItemSsRequest();
                BeanUtils.copyProperties(item, itemSaRequest);
                SgCShareStore sgShareStore =
                        sgShareStoreHashMap.get(negativeStock.get(item.getCpCStoreId()).getSgCShareStoreId());
                itemSaRequest.setSgCShareStoreId(sgShareStore.getId());
                itemSaRequest.setSgCShareStoreEcode(sgShareStore.getEcode());
                itemSaRequest.setSgCShareStoreEname(sgShareStore.getEname());
                itemSaRequest.setQtySpAllocationChange(item.getQty());
                itemSaRequest.setBillItemId(item.getId());
                itemSaRequests.add(itemSaRequest);
            }

            // 聚合仓request 封装
            SgStoreStorageService bean = ApplicationContextHandle.getBean(SgStoreStorageService.class);
            SgStorageUpdateBillRequest sgStorageSingleUpdateRequest =
                    bean.getSgStorageSingleUpdateRequest(sgStoreOut.getId()
                            , sgStoreOut.getBillNo(), sgStoreOut.getBillDate()
                            , SgConstantsIF.BILL_STO_OUT, SgConstantsIF.SERVICE_NODE_STO_OUT_RELEASE, false,
                            sgStoreOut.getSourceBillId()
                            , sgStoreOut.getSourceBillNo(), itemSaRequests, SgConstantsIF.PREOUT_RESULT_ERROR);
            billSaRequests.add(sgStorageSingleUpdateRequest);
            // 更新共享仓库存 参数set
            updateSsRequest.setMessageKey(SgConstants.MSG_TAG_STO_OUT + ":" + sgStoreOut.getBillNo());
            updateSsRequest.setLoginUser(loginUser);
            SgStorageUpdateControlRequest controlModel = new SgStorageUpdateControlRequest();
            controlModel.setPreoutOperateType(SgConstantsIF.PREOUT_RESULT_ERROR);
            updateSsRequest.setControlModel(controlModel);
            updateSsRequest.setBillList(billSaRequests);

            if (log.isDebugEnabled()) {
                log.debug("逻辑占用单{},开始更新聚合库存，入参={}", sgStoreOut.getBillNo(), JSONObject.toJSONString(updateSsRequest));
            }
            SgStorageRedisBillBatchUpdateService service =
                    ApplicationContextHandle.getBean(SgStorageRedisBillBatchUpdateService.class);
            ValueHolderV14<SgStorageBillUpdateResult> valueHolderV14 = service.updateStorageBatch(updateSsRequest);
            // redis 流水建
            redisBillFtpKeyList.addAll(valueHolderV14.getData().getRedisBillFtpKeyList());
            if (log.isDebugEnabled()) {
                log.debug("逻辑占用单{},结束更新聚合库存，出参={}", sgStoreOut.getBillNo(), JSONObject.toJSONString(valueHolderV14));
            }
            if (ResultCode.FAIL == valueHolderV14.getCode()) {
                AssertUtils.logAndThrow("更新聚合库存异常：" + valueHolderV14.getMessage());
            }

        } catch (Exception e) {
            AssertUtils.logAndThrowException("逻辑占用单更新聚合库存异常：" + e.getMessage(), e);
        }
    }

    /**
     * 设置单据明细
     *
     * @param billRequest
     * @param abstractItemList
     */
    private void convertAbstractItemList(SgStorageUpdateBillRequest billRequest,
                                         List abstractItemList) {
        billRequest.setItemList(abstractItemList);
    }

    public static class SkuKeyBuildUtil {
        public static String buildKey(SgBStoOutResultItem item) {
            return item.getPsCSkuEcode() + SgStoreConstants.join + item.getProduceDate();
        }

        public static String buildKey(SgStorageUpdateBillItemLsRequest item) {
            return item.getPsCSkuEcode() + SgStoreConstants.join + item.getProduceDate();
        }
    }
}

