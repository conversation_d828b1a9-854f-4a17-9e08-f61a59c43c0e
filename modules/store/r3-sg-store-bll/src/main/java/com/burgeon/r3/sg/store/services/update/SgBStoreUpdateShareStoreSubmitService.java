package com.burgeon.r3.sg.store.services.update;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.logic.SgStorageRedisQueryLogic;
import com.burgeon.r3.sg.basic.model.SgStorageRedisQuerySaModel;
import com.burgeon.r3.sg.basic.model.SgStorageRedisQuerySsModel;
import com.burgeon.r3.sg.basic.model.request.SgCallbackShareQtyCalcItemRequest;
import com.burgeon.r3.sg.basic.model.request.SgCallbackShareQtyCalcShareRequest;
import com.burgeon.r3.sg.basic.model.request.SgShareStorageInitRequest;
import com.burgeon.r3.sg.basic.model.request.SgStoragePageQueryRequest;
import com.burgeon.r3.sg.basic.model.request.SgStoragePageRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageQueryRequest;
import com.burgeon.r3.sg.basic.model.result.SgCallbackLsQtyCalcOutItemResult;
import com.burgeon.r3.sg.basic.model.result.SgCallbackShareQtyCalcItemResult;
import com.burgeon.r3.sg.basic.model.result.SgCallbackShareQtyCalcResult;
import com.burgeon.r3.sg.basic.model.result.SgShareStorageInitResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySaResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySsExtResult;
import com.burgeon.r3.sg.basic.services.SgShareStorageInitService;
import com.burgeon.r3.sg.basic.services.SgStorageCalculationService;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorage;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.store.update.SgBStoStoreUpdateShareStore;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.api.allocation.SgBShareAllocationReturnSaveAndSubmitCmd;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnBillSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnItemSaveRequst;
import com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnSaveRequst;
import com.burgeon.r3.sg.share.model.result.allocation.SgBShareAllocationReturnSaveAndSubmitResult;
import com.burgeon.r3.sg.sourcing.api.sourcestrategy.SgCChannelSourceStrategySaveCmd;
import com.burgeon.r3.sg.sourcing.model.request.sourcestrategy.SgCChannelSourceStrategyItemSaveRequest;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.update.SgBStoStoreUpdateShareStoreMapper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.api.CstoreSaveCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-01-13 14:59
 * @Description:
 */
@Slf4j
@Component
public class SgBStoreUpdateShareStoreSubmitService {


    @Autowired
    private SgBStoStoreUpdateShareStoreMapper mainMapeer;
    @Autowired
    private SgStorageQueryService sgStorageQueryService;
    @Autowired
    private SgStorageCalculationService storageCalculationService;
    @Autowired
    private SgStorageRedisQueryLogic redisQueryLogic;
    @Autowired
    private SgShareStorageInitService shareStorageInitService;
    @Autowired
    private SgBStoreUpdateShareStoreSaveService saveService;
    //    @Reference(group = "sg", version = "1.0")
//    private SgBShareAllocationReturnSaveAndSubmitCmd shareAllocationReturnSaveAndSubmitCmd;
    @Reference(group = "sg", version = "1.0")
    private SgCChannelSourceStrategySaveCmd sgChannelSourceStrategySaveCmd;


    /**
     * R3页面审核
     *
     * @param session session
     * @return ValueHolder
     */
    public ValueHolder submitUpdateShareStoreByR3(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        log.info("SgBStoreUpdateShareStoreSubmitService.submitUpdateShareStoreByR3 request={}", JSONObject.toJSONString(request));
        SgBStoreUpdateShareStoreSubmitService service = ApplicationContextHandle.getBean(SgBStoreUpdateShareStoreSubmitService.class);
        ValueHolderV14 v14 = service.submitUpdateShareStore(request.getObjId(), request.getLoginUser(), session.getEvent());
        log.info("SgBStoreUpdateShareStoreSubmitService.submitUpdateShareStoreByR3 ValueHolderV14={}", JSONObject.toJSONString(v14));
        return R3ParamUtils.convertV14WithResult(v14);
    }

    /**
     * 审核
     *
     * @param objid     主表id
     * @param loginUser 用户
     * @return ValueHolderV14
     */
    public ValueHolderV14 submitUpdateShareStore(Long objid, User loginUser, DefaultWebEvent event) {

        log.info("SgBStoreUpdateShareStoreSubmitService.submitUpdateShareStore objid={}", objid);

        ValueHolderV14 v14 = checkParam(objid);
        if (!v14.isOK()) {
            return v14;
        }

        //1.调用保存服务，重新刷新【未完成单据明细】
        SgBStoStoreUpdateShareStore sgStoStoreUpdateShareStore = mainMapeer.selectById(objid);
        ValueHolderV14<Integer> holderV14 = saveService.logicStoreUpdateShareStoreItem(objid, sgStoStoreUpdateShareStore.getCpCStoreId(), loginUser);
        if (!holderV14.isOK()) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("逻辑仓修改聚合仓审核重新刷新未完成单据明细失败：" + holderV14.getMessage());
            return v14;
        }

        Integer data = holderV14.getData();
        if (data != null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("逻辑仓修改聚合仓审核失败：需处理完未完成单据，才能修改逻辑仓的所属聚合仓！");
            return v14;
        }

        // 2.向[单据状态]=已审核、结束时间>系统当前时间的【寻源策略定义】-
        // 【强制寻源规则sg_c_channel_source_strategy_force_item】明细表中写入：已存在当前逻辑仓，则不处理，不存在则新增一行
        ValueHolderV14<List<Long>> v141 = insertStrategyForceItem(sgStoStoreUpdateShareStore, loginUser);
        if (!v141.isOK()) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("逻辑仓修改聚合仓审核强制寻源规则写入失败：" + v141.getMessage());
            return v14;
        }

        //3.d)更新当前单据的[单据状态]=回退中，修改人、修改时间：当前系统操作人、操作时间
        SgBStoStoreUpdateShareStore updateShareStore = new SgBStoStoreUpdateShareStore();
        updateShareStore.setId(objid);
        StorageUtils.setBModelDefalutDataByUpdate(updateShareStore, loginUser);
        updateShareStore.setStatus(SgStoreConstants.SG_B_STORE_UPDATE_SHARE_STORE_STATUS_03);
        updateShareStore.setStatusId(loginUser.getId());
        updateShareStore.setStatusEname(loginUser.getEname());
        updateShareStore.setStatusName(loginUser.getName());
        updateShareStore.setStatusTime(new Date());
        mainMapeer.updateById(updateShareStore);

        // 从这里开始，发生异常就要改成状态为断联中
        List<String> redisFtpKey = new ArrayList<>();
        try {

            //4.如果原聚合仓不为空，则需要将原聚合仓的库存进行扣减
            Long oldSgShareStoreId = sgStoStoreUpdateShareStore.getOldSgCShareStoreId();
            if (oldSgShareStoreId != null) {
                ValueHolderV14 v142 = returnStorage(sgStoStoreUpdateShareStore, loginUser, redisFtpKey);
                if (!v142.isOK()) {
                    AssertUtils.logAndThrow("逻辑仓修改聚合仓审核原聚合仓的库存进行扣减失败:" + v142.getMessage());
                }
            }
            //5
            //则新聚合仓的库存增加：针对修改后的聚合仓的库存进行初始化，将逻辑仓档案的有库存信息的条码信息，写入到聚合仓库存表中，如果聚合仓库存表中已存在，则跳过

            // 修改逻辑仓档案
            ValueHolder valueHolder = updateStore(sgStoStoreUpdateShareStore, loginUser, event);
            if (!valueHolder.isOK()) {
                AssertUtils.logAndThrow("逻辑仓修改聚合仓审核修改逻辑仓失败:" + valueHolder.get("message"));
            }

            Long newSgCShareStoreId = sgStoStoreUpdateShareStore.getNewSgCShareStoreId();
            if (newSgCShareStoreId != null) {
                //聚合仓的库存进行初始化 SgShareStorageInitService.initShareStorage
                ValueHolderV14<SgShareStorageInitResult> valueHolderV14 = initShareStorage(sgStoStoreUpdateShareStore, loginUser);
                if (!valueHolderV14.isOK()) {
                    AssertUtils.logAndThrow("逻辑仓修改聚合仓审核聚合仓的库存进行初始化失败:" + valueHolderV14.getMessage());
                }
            }
        } catch (Exception e) {

            log.error("发生异常单据断联，异常：{}", Throwables.getStackTraceAsString(e));
            SgBStoStoreUpdateShareStore update = new SgBStoStoreUpdateShareStore();
            updateShareStore.setId(objid);
            StorageUtils.setBModelDefalutDataByUpdate(update, loginUser);
            updateShareStore.setStatus(SgStoreConstants.SG_B_STORE_UPDATE_SHARE_STORE_STATUS_02);
            mainMapeer.updateById(updateShareStore);

            // 回滚库存
            StorageBasicUtils.rollbackStorage(redisFtpKey, loginUser);

            v14.setCode(ResultCode.FAIL);
            v14.setMessage("逻辑仓修改聚合仓审核失败：" + e.getMessage());
            return v14;
        }

        //6。h)删除“单据状态”=已审核的【寻源策略定义】-【强制寻源规则sg_c_channel_source_strategy_force_item】明细表中的“数据来源”=当前单据编号记录。
        List<Long> forceItemIds = v141.getData();
        if (CollectionUtils.isNotEmpty(forceItemIds)) {
            sgChannelSourceStrategySaveCmd.deleteSourceStrategyItem(forceItemIds);
        }

        //7。i)更新主表字段
        SgBStoStoreUpdateShareStore updateStoStore = new SgBStoStoreUpdateShareStore();
        updateShareStore.setId(objid);
        StorageUtils.setBModelDefalutDataByUpdate(updateStoStore, loginUser);
        updateShareStore.setStatus(SgStoreConstants.SG_B_STORE_UPDATE_SHARE_STORE_STATUS_04);
        mainMapeer.updateById(updateShareStore);

        log.info("SgBStoreUpdateShareStoreSubmitService.submitUpdateShareStore ValueHolderV14={}", JSONObject.toJSONString(v14));

        return v14;
    }

    /**
     * 初始化 聚合仓库存
     *
     * @param sgStoStoreUpdateShareStore 主表
     * @param user                       用户
     * @return ValueHolderV14<SgShareStorageInitResult>
     */
    private ValueHolderV14<SgShareStorageInitResult> initShareStorage(SgBStoStoreUpdateShareStore sgStoStoreUpdateShareStore, User user) {
        log.info("SgBStoreUpdateShareStoreSubmitService.initShareStorage");

        ValueHolderV14<SgShareStorageInitResult> valueHolderV14 = new ValueHolderV14<>();
        //初始化新聚合仓
        Long newSgCShareStoreId = sgStoStoreUpdateShareStore.getNewSgCShareStoreId();
        if (newSgCShareStoreId != null) {
            log.info("SgBStoreUpdateShareStoreSubmitService.initShareStorage  初始化新聚合仓{} 单据：{}", newSgCShareStoreId,
                    sgStoStoreUpdateShareStore.getBillNo());
            SgShareStorageInitRequest request = new SgShareStorageInitRequest();
            request.setLoginUser(user);
            List<Long> ids = new ArrayList<>();
            ids.add(newSgCShareStoreId);
            List<String> ecodes = new ArrayList<>();
            ecodes.add(sgStoStoreUpdateShareStore.getNewSgCShareStoreEcode());
            request.setShareStoreIds(ids);
            request.setShareStoreEcodes(ecodes);
            valueHolderV14 = shareStorageInitService.initShareStorage(request);
            if (!valueHolderV14.isOK()) {
                return valueHolderV14;
            }
        }

        //初始化原聚合仓
        Long oldSgCShareStoreId = sgStoStoreUpdateShareStore.getOldSgCShareStoreId();
        if (oldSgCShareStoreId != null) {
            log.info("SgBStoreUpdateShareStoreSubmitService.initShareStorage  初始化原聚合仓{} 单据：{}", oldSgCShareStoreId,
                    sgStoStoreUpdateShareStore.getBillNo());
            SgShareStorageInitRequest request = new SgShareStorageInitRequest();
            request.setLoginUser(user);
            List<Long> ids = new ArrayList<>();
            ids.add(oldSgCShareStoreId);
            List<String> ecodes = new ArrayList<>();
            ecodes.add(sgStoStoreUpdateShareStore.getOldSgCShareStoreEcode());
            request.setShareStoreIds(ids);
            request.setShareStoreEcodes(ecodes);
            valueHolderV14 = shareStorageInitService.initShareStorage(request);
            if (!valueHolderV14.isOK()) {
                return valueHolderV14;
            }
        }


        return valueHolderV14;
    }

    /**
     * 修改逻辑仓档案 直接调用页面保存接口
     * 入参：
     * {
     * "fixcolumn": {
     * "CP_C_WAREHOUSE": {
     * "SG_C_SHARE_STORE_ID": "55"
     * }* 	},
     * "objid": 59365,
     * "fkcolumn": {},
     * "beforevalue": {
     * "CP_C_WAREHOUSE": {}
     * }    ,
     * "dsptable": {
     * "CP_C_STORE": "ID"
     * },
     * "realtables": {
     * "CP_C_WAREHOUSE": "CP_C_STORE"
     * },
     * "version": "2",
     * "table": "CP_C_WAREHOUSE",
     * "aftervalue": {
     * "CP_C_WAREHOUSE": {
     * "SG_C_SHARE_STORE_ID": "Nicole聚合仓"
     * }
     * },
     * "group": "cp"
     * }
     *
     * @param sgStoStoreUpdateShareStore 主表
     * @param user                       用户
     * @return ValueHolder
     */
    private ValueHolder updateStore(SgBStoStoreUpdateShareStore sgStoStoreUpdateShareStore, User user, DefaultWebEvent event) {

        log.info("SgBStoreUpdateShareStoreSubmitService.updateStore");

        QuerySession querySession = new QuerySessionImpl(user);

        JSONObject main = new JSONObject();
        main.put("SG_C_SHARE_STORE_ID", sgStoStoreUpdateShareStore.getNewSgCShareStoreId());
        JSONObject cpData = new JSONObject();
        cpData.put("CP_C_WAREHOUSE", main);

        JSONObject result = new JSONObject();
        result.put("objid", sgStoStoreUpdateShareStore.getCpCStoreId());
        result.put("table", "CP_C_WAREHOUSE");
        result.put("fixcolumn", cpData);

        JSONObject aftervalue = new JSONObject();
        aftervalue.put("SG_C_SHARE_STORE_ID", sgStoStoreUpdateShareStore.getNewSgCShareStoreEname());
        JSONObject afterkey = new JSONObject();
        afterkey.put("CP_C_WAREHOUSE", aftervalue);
        result.put("aftervalue", afterkey);

        JSONObject beforevalue = new JSONObject();
        beforevalue.put("SG_C_SHARE_STORE_ID", sgStoStoreUpdateShareStore.getOldSgCShareStoreEname());
        JSONObject beforekey = new JSONObject();
        beforekey.put("CP_C_WAREHOUSE", beforevalue);
        result.put("beforevalue", beforekey);


        event.setEventName("doupdate");
        event.put("menu", "逻辑仓档案修改");
        event.put("param", result);
        querySession.setEvent(event);

        Object o = ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                CstoreSaveCmd.class.getName(), "cp", "1.0");

        return ((CstoreSaveCmd) o).execute(querySession);
    }

    /**
     * 原聚合仓不为空，则需要将原聚合仓的库存进行扣减
     * 对比把需要生成分货退的生成分货退
     *
     * @param sgStoStoreUpdateShareStore 逻辑仓修改聚合仓表
     * @param user                       用户
     * @return ValueHolderV14
     */
    private ValueHolderV14 returnStorage(SgBStoStoreUpdateShareStore sgStoStoreUpdateShareStore, User user, List<String> redisFtpKeys) {

        log.info("SgBStoreUpdateShareStoreSubmitService.returnStorage The inventory of the original polymerization warehouse shall be deducted");

        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "逻辑仓修改聚合仓拉回库存成功！");

        //1.根据聚合仓查询配销仓
        List<SgCSaStore> saStoreList = CommonCacheValUtils.getSaStoreList(sgStoStoreUpdateShareStore.getOldSgCShareStoreId());
        if (CollectionUtils.isEmpty(saStoreList)) {
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("未查询到配销仓，逻辑继续向下执行");
            return v14;
        }

        //2.根据聚合仓查询逻辑仓信息
        List<Long> ssIds = new ArrayList<>();
        ssIds.add(sgStoStoreUpdateShareStore.getOldSgCShareStoreId());
        Map<Long, List<SgCpCStore>> storeList = CommonCacheValUtils.getStoreList(ssIds);
        if (MapUtils.isEmpty(storeList)) {
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("未查询到逻辑仓档案，逻辑继续向下执行");
            return v14;
        }

        //3.查逻辑仓库存
        List<SgBStorage> storage = getStorage(sgStoStoreUpdateShareStore.getCpCStoreId());
        if (CollectionUtils.isEmpty(storage)) {
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("未查询到逻辑仓库存信息，逻辑继续向下执行");
            return v14;
        }

        List<Long> skuIds = storage.stream().map(SgBStorage::getPsCSkuId).collect(Collectors.toList());

        //4.查聚合仓库存
        Map<Long, BigDecimal> ssStorageMap = getSsStorage(skuIds, sgStoStoreUpdateShareStore.getOldSgCShareStoreId(), user);

        Map<Long, BigDecimal> availableMap = storage.stream().collect(Collectors.toMap(SgBStorage::getPsCSkuId,
                SgBStorage::getQtyAvailable, (v1, v2) -> v1));
        //5.库存对比
        Map<Long, BigDecimal> resultMap = compareStorage(skuIds, ssStorageMap, availableMap);

        if (MapUtils.isEmpty(resultMap)) {
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("不需要生成分货退货单，逻辑继续向下执行");
            return v14;
        }

        //对比后需要处理的条码
        List<Long> skuIdList = new ArrayList<>(resultMap.keySet());
        //6。查配销仓库存
        HashMap<String, SgStorageRedisQuerySaResult> saStorageMap = querySaStorage(saStoreList, skuIdList, resultMap,
                user, sgStoStoreUpdateShareStore.getBillNo());
        if (MapUtils.isEmpty(saStorageMap)) {
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("未查询到需要分货退数据，逻辑继续向下执行");
            return v14;
        }

        log.info("SgBStoreUpdateShareStoreSubmitService  resultMap={}", JSONObject.toJSONString(saStorageMap));

        //7.封装分货退数据
        List<SgBShareAllocationReturnBillSaveRequst> returnBillSave = setAllocationReturn(saStorageMap, saStoreList, sgStoStoreUpdateShareStore, user);
        //8。新增并审核 分货退货单
        saveAndSubmitShareAllocationReturn(returnBillSave, redisFtpKeys);

        log.info("SgBStoreUpdateShareStoreSubmitService.returnStorage ValueHolderV14 ={}", JSONObject.toJSONString(v14));
        return v14;
    }

    /**
     * 查询配销仓可用库存
     *
     * @param saStoreList 配销仓id
     * @param skuIds      条码id集合
     * @param user        用户
     * @param billNo      单据编号
     */
    private HashMap<String, SgStorageRedisQuerySaResult> querySaStorage(List<SgCSaStore> saStoreList, List<Long> skuIds,
                                                                        Map<Long, BigDecimal> storageMap,
                                                                        User user, String billNo) {

        log.info("SgBStoreUpdateShareStoreSubmitService.querySaStorage Inquire distribution warehouse stock");

        HashMap<String, SgStorageRedisQuerySaResult> resultMap = new HashMap<>(16);

        if (CollectionUtils.isEmpty(saStoreList) || CollectionUtils.isEmpty(skuIds) || MapUtils.isEmpty(storageMap)) {
            return resultMap;
        }

        List<List<Long>> pageList = StorageUtils.getPageList(skuIds, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);

        //按照优先级降序
        saStoreList.sort(Comparator.comparing(SgCSaStore::getOrderno).reversed());

        for (List<Long> skuids : pageList) {

            for (SgCSaStore saStore : saStoreList) {

                List<SgStorageRedisQuerySaModel> sgStorageRedisSaQueryList = new ArrayList<>();
                for (Long skuId : skuids) {
                    SgStorageRedisQuerySaModel saModel = new SgStorageRedisQuerySaModel();
                    saModel.setSgCSaStoreId(saStore.getId());
                    saModel.setPsCSkuId(skuId);
                    sgStorageRedisSaQueryList.add(saModel);
                }

                ValueHolderV14<HashMap<String, SgStorageRedisQuerySaResult>> valueHolderV14 =
                        sgStorageQueryService.querySaStorageWithRedis(sgStorageRedisSaQueryList, user);

                if (!valueHolderV14.isOK()) {
                    log.error("逻辑仓修改聚合仓（单据编号:{}） 查询配销仓库存 存在异常:{}", billNo, valueHolderV14.getMessage());
                    continue;
                }
                HashMap<String, SgStorageRedisQuerySaResult> data = valueHolderV14.getData();
                if (MapUtils.isNotEmpty(data)) {

                    //分配库存
                    for (Long skuId : skuids) {

                        if (data.containsKey(saStore.getId() + SgConstants.SG_CONNECTOR_MARKS_4 + skuId)
                                && storageMap.containsKey(skuId)) {

                            SgStorageRedisQuerySaResult saResult = data.get(saStore.getId() + SgConstants.SG_CONNECTOR_MARKS_4 + skuId);
                            BigDecimal refundQty = storageMap.get(skuId);
                            BigDecimal qtyAvailable = saResult.getQtyAvailable();
                            if (BigDecimal.ZERO.compareTo(qtyAvailable) == 0
                                    || BigDecimal.ZERO.compareTo(refundQty) == 0) {
                                continue;
                            }

                            //比较量
                            if (refundQty.compareTo(qtyAvailable) <= 0) {
                                storageMap.put(skuId, BigDecimal.ZERO);
                                SgStorageRedisQuerySaResult newSaResult = new SgStorageRedisQuerySaResult();
                                BeanUtils.copyProperties(saResult, newSaResult);
                                newSaResult.setQtyAvailable(refundQty);
                                resultMap.put(saStore.getId() + SgConstants.SG_CONNECTOR_MARKS_4 + skuId, newSaResult);
                            } else {
                                storageMap.put(skuId, refundQty.subtract(qtyAvailable));
                                resultMap.put(saStore.getId() + SgConstants.SG_CONNECTOR_MARKS_4 + skuId, saResult);
                            }

                        }

                    }
                }

            }
        }

        return resultMap;
    }

    /**
     * 查找聚合仓下所有的条码
     * 根据逻辑仓档案查找逻辑仓库存 目的是为了拿出所有的条码
     *
     * @param storeList 逻辑仓档案集合 key = 聚合仓id value = 逻辑仓档案集合
     * @return 返回map key = 聚合仓id value = 条码id集合
     */
    private Map<Long, List<Long>> querySkuByStore(Map<Long, List<SgCpCStore>> storeList, List<SgBStorage> sgStorages) {

        // key 聚合仓id value 条码id集合
        Map<Long, List<Long>> map = new HashMap<>(16);

        for (Long ssid : storeList.keySet()) {
            List<SgCpCStore> stores = storeList.get(ssid);
            List<Long> storeIds = stores.stream().map(SgCpCStore::getId).collect(Collectors.toList());

            List<SgBStorage> data = new ArrayList<>();

            //查询逻辑仓库存 这里是为了拿出所有的条码
            for (Long storeid : storeIds) {
                SgStoragePageQueryRequest sgStoragePageQueryRequest = new SgStoragePageQueryRequest();

                SgStorageQueryRequest sgStorageQueryRequest = new SgStorageQueryRequest();
                List<Long> storeidList = new ArrayList<>();
                storeidList.add(storeid);
                sgStorageQueryRequest.setStoreIds(storeidList);

                SgStoragePageRequest sgStoragePageRequest = new SgStoragePageRequest();
                sgStoragePageRequest.setPageNum(1);
                sgStoragePageRequest.setPageSize(SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
//                sgStoragePageRequest.setPageSize(5);

                sgStoragePageQueryRequest.setPageRequest(sgStoragePageRequest);
                sgStoragePageQueryRequest.setQueryRequest(sgStorageQueryRequest);
                ValueHolderV14<PageInfo<SgBStorage>> pageInfoValueHolderV14 = sgStorageQueryService.queryStoragePage(sgStoragePageQueryRequest,
                        R3SystemUserResource.getSystemRootUser());
                AssertUtils.cannot(!pageInfoValueHolderV14.isOK(), "根据逻辑仓查逻辑仓库存异常：" + pageInfoValueHolderV14.getMessage());

                PageInfo<SgBStorage> dataInfo = pageInfoValueHolderV14.getData();
                if (dataInfo != null) {

                    if (CollectionUtils.isNotEmpty(dataInfo.getList())) {
                        sgStorages.addAll(dataInfo.getList());
                        data.addAll(dataInfo.getList());
                    }

                    //判断是否还有下一页
                    if (dataInfo.isHasNextPage()) {
                        List<SgBStorage> pageStorage = getPageStorage(storeidList, dataInfo);
                        if (CollectionUtils.isNotEmpty(pageStorage)) {
                            sgStorages.addAll(pageStorage);
                            data.addAll(pageStorage);
                        }
                    }
                }
            }


            if (CollectionUtils.isNotEmpty(data)) {
                //取出所有的sku
                List<Long> skuIds = data.stream().map(SgBStorage::getPsCSkuId).collect(Collectors.toList());
                // 去重复
                skuIds = skuIds.stream().distinct().collect(Collectors.toList());
                map.put(ssid, skuIds);
            }
        }
        return map;
    }

    /**
     * 新增并审核 分货退货单
     *
     * @param returnBillSave 入参
     * @param redisFtpKeys   流水
     */
    private void saveAndSubmitShareAllocationReturn(List<SgBShareAllocationReturnBillSaveRequst> returnBillSave, List<String> redisFtpKeys) {

        log.info("SgBStoreUpdateShareStoreSubmitService.saveAndSubmitShareAllocationReturn");

        SgBShareAllocationReturnSaveAndSubmitCmd shareAllocationReturnSaveAndSubmitCmd = (SgBShareAllocationReturnSaveAndSubmitCmd) ApplicationContextHandle.getBean("sgBShareAllocationReturnSaveAndSubmitCmdImpl");
        ValueHolderV14<SgBShareAllocationReturnSaveAndSubmitResult> shareAllocationResult =
                shareAllocationReturnSaveAndSubmitCmd.saveAndSubmit2(returnBillSave);

        AssertUtils.isTrue(shareAllocationResult.isOK(), "分货退货单创建失败！");

        if (Objects.nonNull(shareAllocationResult.getData()) &&
                CollectionUtils.isNotEmpty(shareAllocationResult.getData().getRedisFtpKeys())) {
            redisFtpKeys.addAll(shareAllocationResult.getData().getRedisFtpKeys());
        }

    }


    /**
     * 封装 新增并审核 分货退货单数据
     *
     * @param saStorageMap               入参 拉回库存
     * @param sgStoStoreUpdateShareStore 主表
     * @param saStoreList                配销仓档案
     * @param user                       用户
     * @return List<SgBShareAllocationReturnBillSaveRequst>
     */
    private List<SgBShareAllocationReturnBillSaveRequst> setAllocationReturn(HashMap<String, SgStorageRedisQuerySaResult> saStorageMap,
                                                                             List<SgCSaStore> saStoreList,
                                                                             SgBStoStoreUpdateShareStore sgStoStoreUpdateShareStore,
                                                                             User user) {

        log.info("SgBStoreUpdateShareStoreSubmitService.setAllocationReturn Encapsulate new and review the data of return receipt");
        List<SgBShareAllocationReturnBillSaveRequst> returnBillSaveRequsts = new ArrayList<>();
        if (MapUtils.isEmpty(saStorageMap)) {
            return returnBillSaveRequsts;
        }

        List<SgStorageRedisQuerySaResult> querySaResults = new ArrayList<>(saStorageMap.values());

        //按照配销仓分组
        Map<Long, List<SgStorageRedisQuerySaResult>> saMap = querySaResults.stream()
                .collect(Collectors.groupingBy(SgStorageRedisQuerySaResult::getSgCSaStoreId));
        // 配销仓分组
        Map<Long, SgCSaStore> saStoreMap = saStoreList.stream().collect(Collectors.toMap(SgCSaStore::getId,
                saStore -> saStore, (v1, v2) -> v1));

        saMap.keySet().forEach(key -> {
            List<SgStorageRedisQuerySaResult> sgStorageRedisQuerySaResults = saMap.get(key);

            //库存要大于0
            List<SgStorageRedisQuerySaResult> collect = sgStorageRedisQuerySaResults.stream()
                    .filter(saResult -> BigDecimal.ZERO.compareTo(saResult.getQtyAvailable()) != 0).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(collect)) {
                List<List<SgStorageRedisQuerySaResult>> pageList = StorageUtils.getPageList(collect, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
                for (List<SgStorageRedisQuerySaResult> saResults : pageList) {
                    //封装分货退货单 新增参数  因为从头到尾都在这些配销仓里面查的，所以一定包含这些配销仓中
                    returnBillSaveRequsts.add(setAllocationReturn(saStoreMap.get(key), sgStoStoreUpdateShareStore, saResults, user));
                }
            }
        });

        return returnBillSaveRequsts;
    }

    /**
     * 封装分货退货单 新增参数
     *
     * @param sa                         配销仓
     * @param sgStoStoreUpdateShareStore 信息表
     * @param saResults                  条码
     * @return com.burgeon.r3.sg.share.model.request.allocation.SgBShareAllocationReturnBillSaveRequst
     */
    private SgBShareAllocationReturnBillSaveRequst setAllocationReturn(SgCSaStore sa, SgBStoStoreUpdateShareStore sgStoStoreUpdateShareStore,
                                                                       List<SgStorageRedisQuerySaResult> saResults, User user) {
        // 构建request
        SgBShareAllocationReturnBillSaveRequst request = new SgBShareAllocationReturnBillSaveRequst();
        request.setR3(true);

        SgBShareAllocationReturnSaveRequst mainRequest = new SgBShareAllocationReturnSaveRequst();
        // 处理主表数据
        mainRequest.setBillDate(new Date());
        mainRequest.setSourceBillId(sgStoStoreUpdateShareStore.getId());
        mainRequest.setSourceBillNo(sgStoStoreUpdateShareStore.getBillNo());
        mainRequest.setSourceBillType(SgConstantsIF.BILL_SG_B_STORE_UPDATE_SHARE_STORE);
        mainRequest.setSgCSaStoreId(sa.getId());
        mainRequest.setSgCShareStoreId(sa.getSgCShareStoreId());
        mainRequest.setRemark("逻辑仓：[" + sgStoStoreUpdateShareStore.getBillNo() + "]修改聚合仓生成！");

        List<SgBShareAllocationReturnItemSaveRequst> itemRequestList = new ArrayList<>();


        for (SgStorageRedisQuerySaResult saResult : saResults) {
            SgBShareAllocationReturnItemSaveRequst itemRequest = new SgBShareAllocationReturnItemSaveRequst();
            // 处理明细数据
            itemRequest.setPsCSkuId(saResult.getPsCSkuId());
            itemRequest.setQty(saResult.getQtyAvailable());
            itemRequest.setId(-1L);
            itemRequestList.add(itemRequest);
        }

        CommonCacheValUtils.setSkuInfoBySkuIdList(itemRequestList);

        request.setLoginUser(user);
        request.setAllocationReturnSaveRequst(mainRequest);
        request.setAllocationReturnItemSaveRequst(itemRequestList);

        return request;
    }

    /**
     * 逻辑仓库存对比聚合仓库存 返回 需要拉回的库存
     *
     * @param skuIds       条码集合
     * @param ssStorageMap 聚合仓库存
     * @param availableMap 逻辑仓库存
     * @return MAP
     */
    private Map<Long, BigDecimal> compareStorage(List<Long> skuIds, Map<Long, BigDecimal> ssStorageMap, Map<Long, BigDecimal> availableMap) {

        log.info("SgBStoreUpdateShareStoreSubmitService.compareStorage The logical warehouse stores the inventory that needs to be pulled back compared to the aggregate warehouse");

        Map<Long, BigDecimal> resultMap = new HashMap<>(16);
        JSONArray error = new JSONArray();
        for (Long skuId : skuIds) {
            if (ssStorageMap.containsKey(skuId) && availableMap.containsKey(skuId)) {
                BigDecimal ssStorage = ssStorageMap.get(skuId);
                BigDecimal available = availableMap.get(skuId);
                if (available.compareTo(ssStorage) > 0) {
                    resultMap.put(skuId, available.subtract(ssStorage));
                }
            } else {
                error.add(skuId);
            }
        }
        if (error.size() > 0) {
            log.info("条码不匹配(聚合仓库存为空或者逻辑仓库存为空)条码:{}", JSONObject.toJSONString(error));
        }

        return resultMap;
    }

    /**
     * 查询是否可以拉回库存
     *
     * @param availableMap 需要拉回的量
     * @param shareStoreid 聚合仓ID
     * @return com.burgeon.r3.sg.basic.model.result.SgCallbackShareQtyCalcResult
     */
    private SgCallbackShareQtyCalcResult checkShareStorageQty(Map<Long, BigDecimal> availableMap, Long shareStoreid) {

        log.info("SgBStoreUpdateShareStoreSubmitService.checkShareStorageQty Query whether the inventory can be pulled back");

        SgCallbackShareQtyCalcResult result = new SgCallbackShareQtyCalcResult();

        //调整结果明细信息
        List<SgCallbackShareQtyCalcItemResult> itemResultList = new ArrayList<>();
        //缺货明细列表
        List<SgCallbackLsQtyCalcOutItemResult> outStockItemList = new ArrayList<>();
        List<Long> skuIds = new ArrayList<>(availableMap.keySet());

        List<List<Long>> pageList = StorageUtils.getPageList(skuIds, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
        for (List<Long> skuList : pageList) {

            SgCallbackShareQtyCalcShareRequest sgCallbackShareQtyCalcShareRequest = new SgCallbackShareQtyCalcShareRequest();
            List<SgCallbackShareQtyCalcItemRequest> itemRequests = new ArrayList<>();
            for (Long skuId : skuList) {
                if (availableMap.containsKey(skuId)) {
                    SgCallbackShareQtyCalcItemRequest sgCallbackShareQtyCalcItemRequest = new SgCallbackShareQtyCalcItemRequest();
                    sgCallbackShareQtyCalcItemRequest.setPsCSkuId(skuId);
                    sgCallbackShareQtyCalcItemRequest.setQtyChange(availableMap.get(skuId));
                    itemRequests.add(sgCallbackShareQtyCalcItemRequest);
                }
            }

            sgCallbackShareQtyCalcShareRequest.setItemList(itemRequests);
            sgCallbackShareQtyCalcShareRequest.setShareStoreId(shareStoreid);
            sgCallbackShareQtyCalcShareRequest.setIsExcludeVipSaStore(Boolean.TRUE);

            ValueHolderV14<SgCallbackShareQtyCalcResult> valueHolderV14 = null;
//                    storageCalculationService.calcCallbackShareQtyByShareStore(sgCallbackShareQtyCalcShareRequest,
//                            R3SystemUserResource.getSystemRootUser());

            if (valueHolderV14.isOK()) {

                if (log.isDebugEnabled()) {
                    log.debug("逻辑仓修改聚合仓拉回库存valueHolderV14={}", JSONObject.toJSONString(valueHolderV14));
                }

                SgCallbackShareQtyCalcResult data = valueHolderV14.getData();
                if (data != null) {
                    List<SgCallbackShareQtyCalcItemResult> resultList = data.getItemResultList();
                    List<SgCallbackLsQtyCalcOutItemResult> outStockItems = data.getOutStockItemList();
                    if (CollectionUtils.isNotEmpty(resultList)) {
                        itemResultList.addAll(resultList);
                    }
                    if (CollectionUtils.isNotEmpty(outStockItems)) {
                        outStockItemList.addAll(outStockItems);
                    }
                }

            } else {
                log.info("逻辑仓修改聚合仓拉回库存异常：" + valueHolderV14.getMessage());
            }


        }

        result.setItemResultList(itemResultList);
        result.setOutStockItemList(outStockItemList);
        return result;
    }

    /**
     * 查询聚合仓库存
     *
     * @param skuIds       条码集合
     * @param shareStoreid 聚合仓id
     * @param user         用户
     * @return Map<Long, BigDecimal>
     */
    private Map<Long, BigDecimal> getSsStorage(List<Long> skuIds, Long shareStoreid, User user) {

        log.info("SgBStoreUpdateShareStoreSubmitService.returnStorage query share storage");

        //条码集合
        List<List<Long>> pageList = StorageUtils.getPageList(skuIds, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
        List<SgStorageRedisQuerySsExtResult> data = new ArrayList<>();


        for (List<Long> skuid : pageList) {

            List<SgStorageRedisQuerySsModel> modelsList = new ArrayList<>();

            for (Long id : skuid) {
                SgStorageRedisQuerySsModel sgStorageRedisQuerySsModel = new SgStorageRedisQuerySsModel();
                sgStorageRedisQuerySsModel.setSgCShareStoreId(shareStoreid);
                sgStorageRedisQuerySsModel.setPsCSkuId(id);
                modelsList.add(sgStorageRedisQuerySsModel);
            }

            ValueHolderV14<List<SgStorageRedisQuerySsExtResult>> valueHolderV14 = redisQueryLogic.querySsStorageAvailableWithRedis(modelsList, user);
            if (ResultCode.FAIL == valueHolderV14.getCode()) {
                continue;
            }

            if (CollectionUtils.isEmpty(valueHolderV14.getData())) {
                continue;
            }

            data.addAll(valueHolderV14.getData());
        }

        //聚合库存可用量
        return data.stream().collect(Collectors.toMap(SgStorageRedisQuerySsExtResult::getPsCSkuId,
                SgStorageRedisQuerySsExtResult::getQtySsAvailable, (v1, v2) -> v1));
    }

    /**
     * 查询逻辑仓库存
     *
     * @param storeid 逻辑仓id
     * @return 库存
     */
    private List<SgBStorage> getStorage(Long storeid) {

        log.info("SgBStoreUpdateShareStoreSubmitService.getStorage query store storage");

        List<SgBStorage> data = new ArrayList<>();
        SgStoragePageQueryRequest sgStoragePageQueryRequest = new SgStoragePageQueryRequest();

        SgStorageQueryRequest sgStorageQueryRequest = new SgStorageQueryRequest();
        List<Long> storeidList = new ArrayList<>();
        storeidList.add(storeid);
        sgStorageQueryRequest.setStoreIds(storeidList);

        SgStoragePageRequest sgStoragePageRequest = new SgStoragePageRequest();
        sgStoragePageRequest.setPageNum(1);
        sgStoragePageRequest.setPageSize(SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);

        sgStoragePageQueryRequest.setPageRequest(sgStoragePageRequest);
        sgStoragePageQueryRequest.setQueryRequest(sgStorageQueryRequest);
        ValueHolderV14<PageInfo<SgBStorage>> pageInfoValueHolderV14 = sgStorageQueryService.queryStoragePage(sgStoragePageQueryRequest,
                R3SystemUserResource.getSystemRootUser());
        AssertUtils.cannot(!pageInfoValueHolderV14.isOK(), "根据逻辑仓查逻辑仓库存异常：" + pageInfoValueHolderV14.getMessage());

        PageInfo<SgBStorage> dataInfo = pageInfoValueHolderV14.getData();
        if (dataInfo != null) {

            if (CollectionUtils.isNotEmpty(dataInfo.getList())) {
                data.addAll(dataInfo.getList());
            }

            //判断是否还有下一页
            if (dataInfo.isHasNextPage()) {
                List<SgBStorage> pageStorage = getPageStorage(storeidList, dataInfo);
                if (CollectionUtils.isNotEmpty(pageStorage)) {
                    data.addAll(pageStorage);
                }
            }
        }

        return data;
    }

    /**
     * 分页查询库存
     *
     * @param storeidList 店仓id
     * @param dataInfo    分页参数
     * @return 库存
     */
    private List<SgBStorage> getPageStorage(List<Long> storeidList, PageInfo<SgBStorage> dataInfo) {
        List<SgBStorage> data = new ArrayList<>();
        //获取页面
        int pages = dataInfo.getPages();

        //从第二页开始再查
        for (int i = 2; i <= pages; i++) {

            SgStoragePageQueryRequest sgStoragePageQueryRequest = new SgStoragePageQueryRequest();

            SgStorageQueryRequest sgStorageQueryRequest = new SgStorageQueryRequest();
            sgStorageQueryRequest.setStoreIds(storeidList);

            SgStoragePageRequest sgStoragePageRequest = new SgStoragePageRequest();
            sgStoragePageRequest.setPageNum(i);
            sgStoragePageRequest.setPageSize(SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);


            sgStoragePageQueryRequest.setPageRequest(sgStoragePageRequest);
            sgStoragePageQueryRequest.setQueryRequest(sgStorageQueryRequest);
            ValueHolderV14<PageInfo<SgBStorage>> pageInfoValueHolderV14 = sgStorageQueryService.queryStoragePage(sgStoragePageQueryRequest,
                    R3SystemUserResource.getSystemRootUser());
            AssertUtils.cannot(!pageInfoValueHolderV14.isOK(), "根据逻辑仓分页查逻辑仓库存异常：" + pageInfoValueHolderV14.getMessage());

            PageInfo<SgBStorage> pageInfo = pageInfoValueHolderV14.getData();
            if (pageInfo != null && CollectionUtils.isNotEmpty(pageInfo.getList())) {
                data.addAll(pageInfo.getList());
            }
        }

        return data;
    }

    /**
     * .向[单据状态]=已审核、结束时间>系统当前时间的【寻源策略定义】-【强制寻源规则sg_c_channel_source_strategy_force_item】
     * 明细表中写入：已存在当前逻辑仓，则不处理，不存在则新增一行
     *
     * @param sgStoStoreUpdateShareStore 主表参数
     * @param loginUser                  用户
     * @return ValueHolderV14
     */
    private ValueHolderV14<List<Long>> insertStrategyForceItem(SgBStoStoreUpdateShareStore sgStoStoreUpdateShareStore, User loginUser) {

        log.info("SgBStoreUpdateShareStoreSubmitService.insertStrategyForceItem sg_c_channel_source_strategy_force_item write");

        SgCChannelSourceStrategyItemSaveRequest itemSaveRequest = new SgCChannelSourceStrategyItemSaveRequest();
        itemSaveRequest.setCpCStoreId(sgStoStoreUpdateShareStore.getCpCStoreId());
        itemSaveRequest.setCpCStoreEcode(sgStoStoreUpdateShareStore.getCpCStoreEcode());
        itemSaveRequest.setCpCStoreEname(sgStoStoreUpdateShareStore.getCpCStoreEname());
        itemSaveRequest.setSourceBillNo(sgStoStoreUpdateShareStore.getBillNo());
        itemSaveRequest.setUser(loginUser);
        return sgChannelSourceStrategySaveCmd.insertSourceStrategyItem(itemSaveRequest);
    }

    /**
     * 校验参数
     *
     * @param objid 主表id
     * @return ValueHolderV14
     */
    private ValueHolderV14 checkParam(Long objid) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "逻辑仓修改聚合仓审核成功！");

        if (objid != null) {
            SgBStoStoreUpdateShareStore sgStoStoreUpdateShareStore = mainMapeer.selectById(objid);
            if (sgStoStoreUpdateShareStore == null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("逻辑仓修改聚合仓审核失败：当前记录已不存在！");
                return v14;
            }
            Integer status = sgStoStoreUpdateShareStore.getStatus();
            if (SgStoreConstants.SG_B_STORE_UPDATE_SHARE_STORE_STATUS_01 != status) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("逻辑仓修改聚合仓审核失败：当前单据状态，不允许审核！");
                return v14;
            }

            Long newSgShareStoreId = sgStoStoreUpdateShareStore.getNewSgCShareStoreId();
            Long oldSgShareStoreId = sgStoStoreUpdateShareStore.getOldSgCShareStoreId();
            if (newSgShareStoreId == null && oldSgShareStoreId == null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("逻辑仓修改聚合仓审核失败：当前逻辑仓无可变更的聚合仓，不允许编辑!");
                return v14;
            }

        } else {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("参数为空！");
            return v14;
        }

        return v14;
    }
}
