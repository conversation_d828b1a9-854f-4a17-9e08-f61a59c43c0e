package com.burgeon.r3.sg.store.services.third;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.model.table.store.out.SgBWmsToStoOutResult;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.mapper.out.SgBWmsToStoOutResultMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/13 18:14
 * @Description B2C发货回传中间表
 */
@Slf4j
@Component
public class SgBWmsToStoOutResultService {

    @Resource
    private SgBWmsToStoOutResultMapper mapper;

    /**
     * 重置失败次数
     *
     * @param session
     * @return
     */
    public ValueHolder resetFailCount(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        User user = session.getUser();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        log.info(LogUtil.format("SgBWmsToStoOutResultService.resetFailCount param:{},user:{}",
                "SgBWmsToStoOutResultService.resetFailCount"),
                param.toJSONString(), JSONObject.toJSONString(user));
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        JSONArray ids = param.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            throw new NDSException("请先选择需要重制失败次数的记录！");
        }
        int successCount = 0;
        int failCount = 0;
        if (ids.size() == 1) {
            executeResetFailCount(ids.getLong(0), user);
            return ValueHolderUtils.getSuccessValueHolder("重置失败次数成功！");
        } else {
            for (int i = 0; i < ids.size(); i++) {
                Long id = ids.getLong(i);
                try {
                    executeResetFailCount(id, user);
                    successCount++;
                } catch (Exception e) {
                    log.warn(LogUtil.format("SgBWmsToStoOutResultService.resetFailCount id:{},error:{}",
                            "SgBWmsToStoOutResultService.resetFailCount"), id, Throwables.getStackTraceAsString(e));
                    failCount++;
                }
            }
        }
        return ValueHolderUtils.getSuccessValueHolder("重置失败次数成功" + successCount + "条，失败" + failCount + "条");
    }

    /**
     * 重置失败次数（单条）
     *
     * @param id
     * @param user
     */
    private void executeResetFailCount(Long id, User user) {
        SgBWmsToStoOutResult sgBWmsToStoOutResult = mapper.selectById(id);
        if (sgBWmsToStoOutResult == null) {
            throw new NDSException("记录不存在！");
        }
        if (YesNoEnum.N.getKey().equals(sgBWmsToStoOutResult.getIsactive())) {
            throw new NDSException("记录已作废！");
        }
        if (!(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED == sgBWmsToStoOutResult.getTransformStatus()
                && 6 == sgBWmsToStoOutResult.getFailedCount())) {
            throw new NDSException("仅转化失败且转化次数等于6才允许重置失败次数！");
        }
        SgBWmsToStoOutResult update = new SgBWmsToStoOutResult();
        update.setId(sgBWmsToStoOutResult.getId());
        update.setFailedCount(0);
        StorageUtils.setBModelDefalutDataByUpdate(update, user);
        int count = mapper.updateById(update);
        if (count <= 0) {
            throw new NDSException("更新失败！");
        }
    }

    /**
     * 作废
     *
     * @param session
     * @return
     */
    public ValueHolder voidData(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        User user = session.getUser();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        log.info(LogUtil.format("SgBWmsToStoOutResultService.voidData param:{},user:{}",
                "SgBWmsToStoOutResultService.voidData"),
                param.toJSONString(), JSONObject.toJSONString(user));
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        JSONArray ids = param.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            throw new NDSException("请先选择需要作废的记录！");
        }
        int successCount = 0;
        int failCount = 0;
        if (ids.size() == 1) {
            executeVoidData(ids.getLong(0), user);
            return ValueHolderUtils.getSuccessValueHolder("作废成功！");
        } else {
            for (int i = 0; i < ids.size(); i++) {
                Long id = ids.getLong(i);
                try {
                    executeVoidData(id, user);
                    successCount++;
                } catch (Exception e) {
                    log.warn(LogUtil.format("SgBWmsToStoOutResultService.voidData id:{},error:{}",
                            "SgBWmsToStoOutResultService.voidData"), id, Throwables.getStackTraceAsString(e));
                    failCount++;
                }
            }
        }
        return ValueHolderUtils.getSuccessValueHolder("作废成功" + successCount + "条，失败" + failCount + "条");
    }

    /**
     * 作废（单条）
     *
     * @param id
     * @param user
     */
    private void executeVoidData(Long id, User user) {
        SgBWmsToStoOutResult sgBWmsToStoOutResult = mapper.selectById(id);
        if (sgBWmsToStoOutResult == null) {
            throw new NDSException("记录不存在！");
        }
        if (YesNoEnum.N.getKey().equals(sgBWmsToStoOutResult.getIsactive())) {
            throw new NDSException("记录已作废！");
        }
        if (!(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED == sgBWmsToStoOutResult.getTransformStatus()
                && 6 == sgBWmsToStoOutResult.getFailedCount())) {
            throw new NDSException("仅转化失败且转化次数等于6才允许作废！");
        }
        SgBWmsToStoOutResult update = new SgBWmsToStoOutResult();
        update.setId(sgBWmsToStoOutResult.getId());
        update.setIsactive(YesNoEnum.N.getKey());
        StorageUtils.setBModelDefalutDataByUpdate(update, user);
        int count = mapper.updateById(update);
        if (count <= 0) {
            throw new NDSException("更新失败！");
        }
    }

    /**
     * 标记为转化
     *
     * @param session
     * @return
     */
    public ValueHolder markUnconverted(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        User user = session.getUser();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        log.info(LogUtil.format("SgBWmsToStoOutResultService.markUnconverted param:{},user:{}",
                "SgBWmsToStoOutResultService.markUnconverted"),
                param.toJSONString(), JSONObject.toJSONString(user));
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        JSONArray ids = param.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            throw new NDSException("请先选择需要标记为未转化的记录！");
        }
        int successCount = 0;
        int failCount = 0;
        if (ids.size() == 1) {
            executeMarkUnconverted(ids.getLong(0), user);
            return ValueHolderUtils.getSuccessValueHolder("标记为未转化成功！");
        } else {
            for (int i = 0; i < ids.size(); i++) {
                Long id = ids.getLong(i);
                try {
                    executeMarkUnconverted(id, user);
                    successCount++;
                } catch (Exception e) {
                    log.warn(LogUtil.format("SgBWmsToStoOutResultService.markUnconverted id:{},error:{}",
                            "SgBWmsToStoOutResultService.markUnconverted"), id, Throwables.getStackTraceAsString(e));
                    failCount++;
                }
            }
        }
        return ValueHolderUtils.getSuccessValueHolder("标记为未转化成功" + successCount + "条，失败" + failCount + "条");
    }

    /**
     * 标记为转化（单条）
     *
     * @param id
     * @param user
     */
    private void executeMarkUnconverted(Long id, User user) {
        SgBWmsToStoOutResult sgBWmsToStoOutResult = mapper.selectById(id);
        if (sgBWmsToStoOutResult == null) {
            throw new NDSException("记录不存在！");
        }
        if (YesNoEnum.Y.getKey().equals(sgBWmsToStoOutResult.getIsactive())) {
            throw new NDSException("仅作废状态允许标记为未转化！");
        }
        Integer selectCount = mapper.selectCount(new LambdaQueryWrapper<SgBWmsToStoOutResult>()
                .eq(SgBWmsToStoOutResult::getWmsBillCode, sgBWmsToStoOutResult.getWmsBillCode())
                .eq(SgBWmsToStoOutResult::getIsactive, YesNoEnum.Y.getKey()));
        if (selectCount > 0) {
            throw new NDSException("存在同WMS单号且未作废单据不允许标记为未转化！");
        }
        SgBWmsToStoOutResult update = new SgBWmsToStoOutResult();
        update.setId(sgBWmsToStoOutResult.getId());
        update.setIsactive(YesNoEnum.Y.getKey());
        update.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT);
        update.setFailedCount(0);
        update.setFailedReason("");
        StorageUtils.setBModelDefalutDataByUpdate(update, user);
        int count = mapper.updateById(update);
        if (count <= 0) {
            throw new NDSException("更新失败！");
        }
    }

    /**
     * 批量重置失败次数
     */
    public void resetFailCount() {
        log.info(LogUtil.format("SgBWmsToStoOutResultService.resetFailCount start",
                "SgBWmsToStoOutResultService.resetFailCount"));
        List<SgBWmsToStoOutResult> resultList = mapper.selectList(new LambdaQueryWrapper<SgBWmsToStoOutResult>()
                .eq(SgBWmsToStoOutResult::getTransformStatus, SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED)
                .eq(SgBWmsToStoOutResult::getFailedCount, 6)
                .eq(SgBWmsToStoOutResult::getIsactive, YesNoEnum.Y.getKey()));
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }
        User user = SystemUserResource.getRootUser();
        for (SgBWmsToStoOutResult result : resultList) {
            SgBWmsToStoOutResult update = new SgBWmsToStoOutResult();
            update.setId(result.getId());
            update.setFailedCount(0);
            StorageUtils.setBModelDefalutDataByUpdate(update, user);
            mapper.updateById(update);
        }
    }

}
