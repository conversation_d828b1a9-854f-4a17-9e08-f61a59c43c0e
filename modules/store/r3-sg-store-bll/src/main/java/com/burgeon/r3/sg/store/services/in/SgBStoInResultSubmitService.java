package com.burgeon.r3.sg.store.services.in;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.config.SgStorageMqConfig;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.MQConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInNotices;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInResult;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInResultItem;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransfer;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInNoticesMapper;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInResultItemMapper;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInResultMapper;
import com.burgeon.r3.sg.store.mapper.transfer.SgBStoTransferMapper;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInBillReleasePreinOrStorageRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultSubmitRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoOtherDeliveryBillWarehouseInRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferBillInResultRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferInResultRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferItemInResultRequest;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInResultBillSubmitResult;
import com.burgeon.r3.sg.store.model.result.in.SgBStoOtherDeliveryBillWarehouseInResult;
import com.burgeon.r3.sg.store.services.transfer.SgBStoTransferInResultService;
import com.google.common.base.Preconditions;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderUpdateCmd;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/13 16:13
 */
@Slf4j
@Component
public class SgBStoInResultSubmitService {
    @Autowired
    SgBStoInResultMapper mapper;
    @Autowired
    SgBStoInResultItemMapper itemMapper;
    @Autowired
    private SgBStoInReleaseService stoInReleaseService;
    @Autowired
    private SgBStoInNoticesSaveService stoInNoticesSaveService;

    @Autowired
    private SgBStoInNoticesQueryService stoInNoticesQueryService;

    @Autowired
    private SgBStoInNoticesMapper inNoticesMapper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private SgStorageMqConfig mqConfig;

    @Autowired
    private SgBStoTransferMapper sgStoTransferMapper;

    @Autowired
    private SgBStoOtherDeliveryWarehouseInService warehouseInService;

    @Autowired
    private SgBStoInResultSaveService stoInResultSaveService;
    @Autowired
    private SgBStoInService inService;

    @Reference(group = "oms-fi", version = "1.0")
    private OcBReturnOrderUpdateCmd ocBReturnOrderUpdateCmd;

    private final static String SUCCESS = "success";


    ValueHolder submitInResult(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        SgBStoInResultSubmitService service = ApplicationContextHandle.getBean(SgBStoInResultSubmitService.class);
        List<String> redisBillFtpKeyList = new ArrayList<>();
        ValueHolderV14<SgR3BaseResult> v14 = service.submitInResult(request, redisBillFtpKeyList);
        Long objId = request.getObjId();
        SgBStoInResult stoInResult = mapper.selectById(objId);
        // 结束之后 调用订单中心 修改退换货单状态
        if (stoInResult.getSourceBillType() != null && stoInResult.getSourceBillType() == SgConstantsIF.BILL_TYPE_RETAIL_REF
                && ("THRK".equals(stoInResult.getSourceBillSourceBillType()) ||
                "B2BRK".equals(stoInResult.getSourceBillSourceBillType()))) {
            ocBReturnOrderUpdateCmd.updateByStoInResultSubmit(stoInResult.getSourceBillNo());
        }
        return R3ParamUtils.convertV14WithResult(v14);
    }

    /**
     * 页面审核
     *
     * @param request 入参
     * @return 出参
     * @throws NDSException
     */
    public ValueHolderV14<SgR3BaseResult> submitInResult(SgR3BaseRequest request, List<String> redisBillFtpKeyList) throws NDSException {
        boolean colorAndCodeFlag = getBusinessSystemWithColorAndCode();
        if (log.isDebugEnabled()) {
            log.debug("Start SgBStoInResultSubmitService.submitInResult.ReceiveParams:request={},colorAndCodeFlag:{}"
                    , JSONObject.toJSONString(request), colorAndCodeFlag);
        }
        Long objId = request.getObjId();
        User user = request.getLoginUser();
        //获取逻辑入库单
        SgBStoInResult billResult = mapper.selectById(objId);
        Long sourceBillId = billResult.getSourceBillId();
        Integer sourceBillType = billResult.getSourceBillType();
        String lockKsy = SgConstants.SG_B_STO_IN_RESULT + ":" + sourceBillId + ":" + sourceBillType;
        if (SgRedisLockUtils.lock(lockKsy)) {
            SgBStoInResultSubmitService submitService = ApplicationContextHandle.getBean(SgBStoInResultSubmitService.class);
            try {
                checkParams(objId);
                submitService.submitInResultBill(objId, user, redisBillFtpKeyList, null);
            } catch (Exception e) {
                // 回滚库存
                StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, request.getLoginUser());
                log.error("SgBStoInResultSubmitService.submitInResult. exception_has_occured:{}", Throwables.getStackTraceAsString(e));
                AssertUtils.logAndThrow(e.getMessage());
                //return new ValueHolderV14<>(ResultCode.FAIL, SgConstants.MESSAGE_STATUS_FAIL + ":" + e.getMessage());
            } finally {
                SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
            }
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "审核成功!");
    }

    /**
     * 非R3页面
     *
     * @param request 入参
     * @return 出参
     * @throws NDSException
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<List<SgBStoInResultBillSubmitResult>> submitInResult(
            SgBStoInResultSubmitRequest request) throws NDSException {
        User user = request.getLoginUser();
        List<SgBStoInResultBillSubmitResult> submitResultList = new ArrayList<>();
        ValueHolderV14<List<SgBStoInResultBillSubmitResult>> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        for (Long id : request.getIds()) {
            //获取入库结果单
            SgBStoInResult billResult = mapper.selectById(id);
            Long sourceBillId = billResult.getSourceBillId();
            Integer sourceBillType = billResult.getSourceBillType();
            String lockKsy = SgConstants.SG_B_STO_IN_RESULT + ":" + sourceBillId + ":" + sourceBillType;

            List<String> redisBillFtpKeyList = new ArrayList<>();
            if (request.getRedisBillFtpKeyList() != null) {
                redisBillFtpKeyList = request.getRedisBillFtpKeyList();
            }

            checkParams(id);
            SgRedisLockUtils.lock(lockKsy);
            try {
                SgBStoInResultSubmitService submitService = ApplicationContextHandle.getBean(SgBStoInResultSubmitService.class);
                submitService.submitInResultBill(id, user, redisBillFtpKeyList, request.getInterfaceTypeFlag());
                submitResultList.add(new SgBStoInResultBillSubmitResult(id, redisBillFtpKeyList, ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS));
            } catch (Exception ex) {
                // 回滚库存
                StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, request.getLoginUser());
                AssertUtils.logAndThrow(ex.getMessage());
            } finally {
                SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
            }
        }
        if (CollectionUtils.isNotEmpty(submitResultList)) {
            v14.setData(submitResultList);
        } else {
            v14.setCode(ResultCode.FAIL);
        }
        return v14;
    }

    /**
     * @param id                  逻辑入库单id
     * @param user                登录用户
     * @param redisBillFtpKeyList redis key键
     * @param interfaceTypeFlag   接口类型
     * @throws NDSException
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitInResultBill(Long id, User user, List<String> redisBillFtpKeyList, String interfaceTypeFlag) throws NDSException {

        //获取逻辑入库单
        SgBStoInResult result = mapper.selectById(id);

        List<SgBStoInResultItem> resultItemSelectList = itemMapper.selectList(new QueryWrapper<SgBStoInResultItem>()
                .eq(SgStoreConstants.BILL_STO_IN_RESULT_ID, id));

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start SgBStoInResultSubmitService.submitInResult.updateInNotices:result={}，resultItemSelectList={}",
                            "SgBStoInResultSubmitService.submitInResultBill")
                    , JSONObject.toJSONString(result), JSONObject.toJSONString(resultItemSelectList));
        }

        //重楼：202409301729: 逻辑入库单入库数量 tot_qty_in <=入库通知单数量tot_qty（采购入库除外【SG_B_STO_IN_NOTICES # source_bill_source_bill_type = 222】）
        if (Objects.nonNull(result.getSgBStoInNoticesId())) {
            SgBStoInNotices inNotices = stoInNoticesQueryService.queryById(result.getSgBStoInNoticesId());
            if (Objects.nonNull(inNotices)
                    && !SgConstantsIF.BILL_TYPE_PUR_NAME.equals(inNotices.getSourceBillSourceBillType())
                    && Optional.ofNullable(result.getTotQtyIn()).orElse(BigDecimal.ZERO)
                    .compareTo(Optional.ofNullable(inNotices.getTotQty()).orElse(BigDecimal.ZERO)) > 0) {
                log.warn(LogUtil.format("逻辑入库单审核：除采购入库外，逻辑入库数量不能大于入库通知单数量，" +
                                        "逻辑入库单ID：{}，入库通知单ID：{}，逻辑入库单入库数量：{}，入库通知单数量：{}，业务类型：{}",
                                "SgBStoInResultSubmitService.submitInResultBill"),
                        result.getId(), inNotices.getId(), result.getTotQtyIn(), inNotices.getTotQty(), inNotices.getSourceBillSourceBillType());
                throw new NDSException("入库通知单:" + inNotices.getBillNo() + "入库数量不能大于入库通知单数量!");
            }
        }

        // 更新逻辑入库单 在释放逻辑占和流水之前 防止查询不到入库日期
        SgBStoInResult update = new SgBStoInResult();
        update.setId(id);
        update.setStatus(SgStoreConstants.BILL_STATUS_SUBMIT);
        if (Objects.isNull(result.getInTime())) {
            Date now = new Date();
            result.setInTime(now);
            update.setInTime(now);
        }
        if (SgConstantsIF.BILL_STO_TRANSFER == result.getSourceBillType()) {
            SgBStoTransfer sgStoTransfer = sgStoTransferMapper.selectById(result.getSourceBillId());
            AssertUtils.isTrue(sgStoTransfer != null, "逻辑入库单对应逻辑调拨单:" + result.getSourceBillNo() + "已不存在!");
            //当前逻辑调拨单上的[入库日期]不为空，将本单的[入库日期]。赋值给逻辑入库单的“入库日期”
            String isAutoIn = Optional.ofNullable(sgStoTransfer.getIsAutoIn()).orElse(SgConstants.IS_AUTO_N);
            if (SgConstants.IS_AUTO_Y.equals(isAutoIn) && sgStoTransfer.getInDate() != null) {
                result.setInTime(sgStoTransfer.getInDate());
                update.setInTime(sgStoTransfer.getInDate());
            }
            //转换日期
            Date outDate = DateUtils.truncate(sgStoTransfer.getOutDate(), Calendar.DAY_OF_MONTH);
            Date inTime = DateUtils.truncate(result.getInTime(), Calendar.DAY_OF_MONTH);
            AssertUtils.cannot(outDate.compareTo(inTime) > 0, "入库日期不能小于出库日期!");
        }
        update.setDealStatus(SgStoreConstants.DIFF_HANDLE_STATUS_THREE);
        StorageUtils.setBModelDefalutDataByUpdate(update, user);

        mapper.updateById(update);


        //来源单据类型<>差异单时
        if (SgConstantsIF.BILL_TYPE_DIFF != result.getSourceBillType()) {
            SgBStoInBillReleasePreinOrStorageRequest releaseRequest = new SgBStoInBillReleasePreinOrStorageRequest();
            //逻辑入库单信息
            releaseRequest.setInResult(result);
            //逻辑入库单明细信息
            releaseRequest.setInResultItemList(resultItemSelectList);
            releaseRequest.setLoginUser(user);
            releaseRequest.setServiceNode(SgConstantsIF.SERVICE_NODE_STO_IN_RESULT_SUBMIT);
            releaseRequest.setChangeDate(result.getInTime());
            releaseRequest.setRedisBillFtpKeyList(redisBillFtpKeyList);
            releaseRequest.setInterfaceTypeFlag(interfaceTypeFlag);
            if (result.getSourceBillType() == SgConstantsIF.BILL_TYPE_OTHER_OUT_RESULT || result.getSourceBillType() == SgConstantsIF.BILL_TYPE_RETAIL_REF) {
                releaseRequest.setIsReleasePrein(false);
            }
            ValueHolderV14<List<String>> listValueHolderV14 = inService.sgBStoInUpdatePreinOrStorage(releaseRequest);
            if (!listValueHolderV14.isOK()) {
                AssertUtils.logAndThrow("逻辑入库单审核失败:" + listValueHolderV14.getMessage());
            }
            redisBillFtpKeyList.addAll(listValueHolderV14.getData());
        }

        if (SgConstantsIF.BILL_STO_TRANSFER == result.getSourceBillType()) {
            SgBStoInResultSubmitService bean = ApplicationContextHandle.getBean(SgBStoInResultSubmitService.class);
            bean.asyncInResult(resultItemSelectList, result, user);
        }
        // 发送mq
        switchSourceBillTypeSendMq(result, user);
        // 其他入库单
        if (Integer.valueOf(SgConstantsIF.BILL_OTHER_DELIVERY).equals(result.getSourceBillType())) {
            // 其他入库单-入库服务
            SgBStoOtherDeliveryBillWarehouseInRequest otherInRequest = new SgBStoOtherDeliveryBillWarehouseInRequest();
            buildWarehouseInRequest(result, resultItemSelectList, user, otherInRequest);
            try {
                ValueHolderV14<SgBStoOtherDeliveryBillWarehouseInResult> warehouseInV14 = warehouseInService.warehouseIn(
                        otherInRequest);
                if (!warehouseInV14.isOK()) {
                    throw new NDSException(warehouseInV14.getMessage());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("Error：{}", "其他入库单-入库服务", id)
                        , Throwables.getStackTraceAsString(e));
                throw new NDSException(e.getMessage());
            }
        }

    }


    /**
     * result,resultItemSelectList -> otherInRequest
     *
     * @param result
     * @param resultItemSelectList
     * @param otherInRequest
     */
    private void buildWarehouseInRequest(SgBStoInResult result, List<SgBStoInResultItem> resultItemSelectList
            , User user, SgBStoOtherDeliveryBillWarehouseInRequest otherInRequest) {
        Preconditions.checkArgument(result != null && CollectionUtils.isNotEmpty(resultItemSelectList)
                , "逻辑入库单主表或者明细为空");

        otherInRequest.setSgBStoInResultId(result.getId());
        otherInRequest.setSgBStoInResultBillNo(result.getBillNo());
        otherInRequest.setSgBStoInResultBillType(
                Optional.ofNullable(result.getSourceBillType()).orElse(200).toString());
        otherInRequest.setSgBStoOtherDeliveryId(result.getSourceBillId());
        otherInRequest.setSgBStoOtherDeliveryBillNo(result.getSourceBillNo());

        otherInRequest.setIsLast(Optional.ofNullable(result.getIsLast()).orElse("N"));
        otherInRequest.setInDate(result.getInTime());

        List<SgBStoOtherDeliveryBillWarehouseInRequest.SgBStoOtherDeliveryBillWarehouseInItemRequest> itemRequest
                = new ArrayList<>();
        for (SgBStoInResultItem resultItem : resultItemSelectList) {
            SgBStoOtherDeliveryBillWarehouseInRequest.SgBStoOtherDeliveryBillWarehouseInItemRequest otherItemRequest
                    = new SgBStoOtherDeliveryBillWarehouseInRequest.SgBStoOtherDeliveryBillWarehouseInItemRequest();
            otherItemRequest.setPsCSkuId(resultItem.getPsCSkuId());
            otherItemRequest.setPsCSkuEcode(resultItem.getPsCSkuEcode());
            otherItemRequest.setQtyIn(resultItem.getQty());
            otherItemRequest.setProduceDate(resultItem.getProduceDate());
            itemRequest.add(otherItemRequest);
        }
        otherInRequest.setItemRequests(itemRequest);
        otherInRequest.setLoginUser(user);
    }

    /**
     * 根据来源单据类型发送mq
     *
     * @param result 主表
     */
    private void switchSourceBillTypeSendMq(SgBStoInResult result, User user) {

        if (log.isDebugEnabled()) {
            log.debug("Start SgBStoInResultSubmitService.switchSourceBillTypeSendMq.ReceiveParams:sourceBillType={}"
                    , result.getSourceBillType());
        }

        if (result.getSourceBillType() == SgConstantsIF.BILL_TYPE_RETAIL_REF) {
            retailSendMq(result, user);
        } else {
            log.info("来源单据类型有问题，未找到匹配项！来源类型：" + result.getSourceBillType());
        }

    }

    /**
     * 零售发货单 发送mq
     *
     * @param result 入库单
     */
    private void retailSendMq(SgBStoInResult result, User user) {

        ValueHolderV14<SgBStoInBillSaveRequest> v14 = getSgStonMqResult(result.getId(), result, null, user);
        if (!v14.isOK()) {
            AssertUtils.logAndThrow("逻辑入库单审核异常：封装mq消息体异常：" + v14.getMessage());
        }
        try {

            SgBStoInBillSaveRequest data = v14.getData();
            String tag = mqConfig.getSgToOmsInMqTag();
            String topic = mqConfig.getSgToOmsMqTopic();
            String configName = mqConfig.getSgDefaultConfigName();
            String msgKey = SgConstants.SG_B_STO_OUT_RESULT + ":" + result.getBillNo() + DateUtil.getDateTime(MQConstantsIF.MQ_FORMAT);
//            r3MqSendHelper.sendMessage(configName, JSONObject.toJSONString(data), topic, tag, msgKey);
            defaultProducerSend.sendTopic(mqConfig.getMq5callbackToOms(),
                    tag,
                    JSONObject.toJSONString(data),
                    msgKey);
        } catch (Exception e) {

            log.error("{}.retailSendMq. exception_has_occured:{}", this.getClass().getName(),
                    Throwables.getStackTraceAsString(e));

            AssertUtils.logAndThrow("逻辑入库单审核mq发送失败：" + e.getMessage());
        }
    }

    /**
     * 零售发货单 封装mq body
     *
     * @param inResultId    入库单id
     * @param inResult      入库单主表
     * @param inResultItems 入库单明细
     * @param user          用户
     * @return body
     */
    private ValueHolderV14<SgBStoInBillSaveRequest> getSgStonMqResult(Long inResultId, SgBStoInResult inResult, List<SgBStoInResultItem> inResultItems, User user) {
        ValueHolderV14<SgBStoInBillSaveRequest> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        if (inResultId == null && (inResult == null || CollectionUtils.isEmpty(inResultItems))) {
            vh.setCode(ResultCode.FAIL);
            return vh;
        }

        SgBStoInResult result = inResult;
        List<SgBStoInResultItem> items = inResultItems;

        if (result == null) {
            SgBStoInResultMapper inResultMapper = ApplicationContextHandle.getBean(SgBStoInResultMapper.class);
            result = inResultMapper.selectById(inResultId);
        }
        if (CollectionUtils.isEmpty(inResultItems)) {
            SgBStoInResultItemMapper inResultItemMapper = ApplicationContextHandle.getBean(SgBStoInResultItemMapper.class);
            items = inResultItemMapper.selectList(new QueryWrapper<SgBStoInResultItem>().lambda()
                    .eq(SgBStoInResultItem::getSgBStoInResultId, inResultId));
        }

        SgBStoInSaveRequest billRequest = new SgBStoInSaveRequest();
        List<SgBStoInItemSaveRequest> itemList = Lists.newArrayList();
//        List<SgBStoInResultDefectItemSaveRequest> defectItemList = Lists.newArrayList();

        billRequest.setId(result.getId());
        billRequest.setBillNo(result.getBillNo());
        billRequest.setCpCStoreId(result.getCpCStoreId());
        billRequest.setCpCStoreEcode(result.getCpCStoreEcode());
        billRequest.setCpCStoreEname(result.getCpCStoreEname());
        billRequest.setSourceBillId(result.getSourceBillId());
        billRequest.setSourceBillNo(result.getSourceBillNo());
        billRequest.setSourceBillType(result.getSourceBillType());
        billRequest.setIsLast(result.getIsLast());
        billRequest.setId(result.getId());
        billRequest.setSenderEcode(result.getSenderEcode());
        billRequest.setSenderName(result.getSenderName());
        //判断是否真的最后一次入库  判断通知单差异数量是否为0
        boolean isAll = true;
        SgBStoInNoticesMapper noticesMapper = ApplicationContextHandle.getBean(SgBStoInNoticesMapper.class);
        SgBStoInNotices notices = noticesMapper.selectById(result.getSgBStoInNoticesId());
        if (notices == null || notices.getTotQtyDiff().compareTo(BigDecimal.ZERO) != 0) {
            isAll = false;
        }
        billRequest.setIsAll(isAll);

        for (SgBStoInResultItem item : items) {
            SgBStoInItemSaveRequest itemBillRequest = new SgBStoInItemSaveRequest();
            itemBillRequest.setId(item.getId());
            itemBillRequest.setSgBStoInResultId(result.getId());
            itemBillRequest.setSgBStoInId(result.getSgBStoInId());
            itemBillRequest.setPsCSkuId(item.getPsCSkuId());
            itemBillRequest.setPsCSkuEcode(item.getPsCSkuEcode());
            itemBillRequest.setPsCProId(item.getPsCProId());
            itemBillRequest.setPsCProEcode(item.getPsCProEcode());
            itemBillRequest.setPsCProEname(item.getPsCProEname());
            itemBillRequest.setPsCSpec1Id(item.getPsCSpec1Id());
            itemBillRequest.setPsCSpec1Ecode(item.getPsCSpec1Ecode());
            itemBillRequest.setPsCSpec1Ename(item.getPsCSpec1Ename());
            itemBillRequest.setPsCSpec2Id(item.getPsCSpec2Id());
            itemBillRequest.setPsCSpec2Ecode(item.getPsCSpec2Ecode());
            itemBillRequest.setPsCSpec2Ename(item.getPsCSpec2Ename());
            itemBillRequest.setQtyIn(item.getQty());
            itemList.add(itemBillRequest);
        }

        //2019-07-12 新增残次品明细
//        SgBStoInResultDefectItemMapper defectItemMapper = ApplicationContextHandle.getBean(SgBStoInResultDefectItemMapper.class);
//        List<SgBStoInResultDefectItem> defectItems = defectItemMapper.selectList(new QueryWrapper<SgBStoInResultDefectItem>().lambda()
//                .eq(SgBStoInResultDefectItem::getSgBStoInResultId, result.getId()));
//        defectItems.forEach(defectItem -> {
//            SgBStoInResultDefectItemSaveRequest itemBillRequest = new SgBStoInResultDefectItemSaveRequest();
//            BeanUtils.copyProperties(defectItem, itemBillRequest);
//            defectItemList.add(itemBillRequest);
//        });
//        //2020-06-09 新增装箱明细
//        List<SgBStoInResultPackingItemSaveRequest> packingItemList = Lists.newArrayList();
//        SgBStoInResultPackingItemMapper packingItemMapper = ApplicationContextHandle.getBean(SgBStoInResultPackingItemMapper.class);
//        List<SgBStoInResultPackingItem> packingItems = packingItemMapper.selectList(new QueryWrapper<SgBStoInResultPackingItem>().lambda()
//                .eq(SgBStoInResultPackingItem::getSgBStoInResultId, result.getId()));
//        packingItems.forEach(packingItem -> {
//            SgBStoInResultPackingItemSaveRequest itemBillRequest = new SgBStoInResultPackingItemSaveRequest();
//            BeanUtils.copyProperties(packingItem, itemBillRequest);
//            packingItemList.add(itemBillRequest);
//        });

        SgBStoInBillSaveRequest billSaveRequest = new SgBStoInBillSaveRequest();
        billSaveRequest.setSgStoInSaveRequest(billRequest);
        billSaveRequest.setSgStoInItemSaveRequestList(itemList);
//        billSaveRequest.setDefectItemList(defectItemList);
//        billSaveRequest.setPackingItemLIst(packingItemList);
        billSaveRequest.setLoginUser(user);
        vh.setData(billSaveRequest);

        return vh;
    }

    /**
     * 异步调用【逻辑调拨单入库服务】通知调拨单出库
     *
     * @param resultItemSelectList 逻辑入库单明细
     * @param result               逻辑入库单主表
     * @param user                 用户
     */
//    @Async
    public void asyncInResult(List<SgBStoInResultItem> resultItemSelectList, SgBStoInResult result, User user) {
        boolean colorAndCodeFlag = getBusinessSystemWithColorAndCode();
        if (log.isDebugEnabled()) {
            log.debug("进入异步调用调拨单入库服务   resultItemSelectList = {}  result = {}",
                    JSON.toJSONString(resultItemSelectList), JSON.toJSONString(result));
        }
        List<SgBStoTransferBillInResultRequest> resultRequests = new ArrayList<>();
        SgBStoTransferBillInResultRequest request = new SgBStoTransferBillInResultRequest();
        SgBStoTransferInResultRequest transfer = new SgBStoTransferInResultRequest();
        transfer.setIsLast(result.getIsLast());
        transfer.setLoginUser(user);
        transfer.setInTime(result.getInTime());
        transfer.setInDate(result.getInTime());
        transfer.setRemark(result.getRemark());
        transfer.setSgBStoInResultBillNo(result.getBillNo());
        transfer.setSgBStoInResultId(result.getId());
        transfer.setSgBStoTransferId(result.getSourceBillId());
        transfer.setSgBStoTransferBillNo(result.getSourceBillNo());
        request.setSgBStoTransferInResultRequest(transfer);
        List<SgBStoTransferItemInResultRequest> itemList = resultItemSelectList.stream().map(resultItem -> {
            SgBStoTransferItemInResultRequest item = new SgBStoTransferItemInResultRequest();
            item.setPsCSKuEcode(resultItem.getPsCSkuEcode());
            item.setPsCSKuId(resultItem.getPsCSkuId());
            item.setQtyIn(resultItem.getQty());
            return item;
        }).collect(Collectors.toList());
        request.setStoTransferItemInResultRequests(itemList);
        request.setIsColorAndCodeFlag(colorAndCodeFlag);
        resultRequests.add(request);
        SgBStoTransferInResultService bean = ApplicationContextHandle.getBean(SgBStoTransferInResultService.class);
        ValueHolderV14 v14 = bean.inResultSgStoTransfer(resultRequests);
        if (!v14.isOK()) {
            AssertUtils.logAndThrow("调拨单入库异常！" + v14.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug("异步调用调拨单入库服务出参: v14 ={}", JSONObject.toJSONString(v14));
        }
    }

    public void checkParams(Long id) {
        boolean colorAndCodeFlag = getBusinessSystemWithColorAndCode();
        SgBStoInResult inResult = mapper.selectById(id);
        AssertUtils.notNull(inResult, "当前单据信息不存在!");
        if (SgStoreConstants.BILL_STATUS_UNSUBMIT != inResult.getStatus()) {
            AssertUtils.logAndThrow("当前单据状态，不允许审核！");
        }
        //c)如果本单的[来源单据类型]<>逻辑差异单，
        // 则通过[关联逻辑在途单]查询出[总原单数量]，
        // 再根据[关联逻辑在途单]查询对应的所有的【逻辑入库单】的[总入库数量]之和，
        // 判断[总通知数量]-减去[总入库数量] 是否大于等于0，即表示多入的情况
        if (SgConstantsIF.BILL_STO_TRANSFER == inResult.getSourceBillType()) {
            SgBStoInNotices sgBStoInNotices = inNoticesMapper.selectById(inResult.getSgBStoInNoticesId());
            AssertUtils.notNull(sgBStoInNotices, "当前入库单关联的入库通知单不存在，请检查！在入库通知单ID：" + inResult.getSgBStoInNoticesId());
            //总原单数量=
            BigDecimal totQtyOrign = sgBStoInNotices.getTotQty() != null ? sgBStoInNotices.getTotQty() : BigDecimal.ZERO;
            //已经入库的总数量
            BigDecimal totQtyNoticesIn = sgBStoInNotices.getTotQtyIn() != null ? sgBStoInNotices.getTotQtyIn() : BigDecimal.ZERO;
            //本次逻辑入库单需要入库的总数量
            BigDecimal totQtyResultIn = inResult.getTotQtyIn() != null ? inResult.getTotQtyIn() : BigDecimal.ZERO;
            //2021.8.29 逆向流程负数比较会报错 改绝对值
            if (!colorAndCodeFlag && totQtyResultIn.add(totQtyNoticesIn).abs().compareTo(totQtyOrign.abs()) > 0) {
                AssertUtils.logAndThrow("总入库数量不允许大于总通知数量");
            }
        }


        // 封帐功能  入库时间 大于 逻辑仓档案 最新封帐时间 不允许审核
        Long cpStoreId = inResult.getCpCStoreId();
        List<Long> stores = new ArrayList<>();
        stores.add(cpStoreId);
        Map<Long, SgCpCStore> negativeStock = SgStoreUtils.getNegativeStock(stores);

        if (MapUtils.isEmpty(negativeStock) && negativeStock.get(cpStoreId) == null) {
            AssertUtils.logAndThrow("逻辑入库单审核异常：获取逻辑仓档案失败：未获取到逻辑仓信息！逻辑仓编码：" + inResult.getCpCStoreEcode());
        }

        SgCpCStore store = negativeStock.get(cpStoreId);
        Date inTime = inResult.getInTime();
        if (Objects.isNull(inTime)) {
            inTime = new Date();
        }

        if (log.isDebugEnabled()) {
            log.debug("封帐时间：{} ；入库时间：{}", store.getCurrentSealaccountDate(), inTime);
        }


        if (store != null && store.getCurrentSealaccountDate() != null &&
                !store.getCurrentSealaccountDate().before(inTime)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
            AssertUtils.logAndThrow("逻辑入库单审核异常：入库时间小于封帐时间！封帐时间：" + sdf.format(store.getCurrentSealaccountDate())
                    + "；入库时间：" + sdf.format(inTime));
        }


    }

    /**
     * 获取业务系统参数 是否允许串色串码或多货
     *
     * @return boolean
     */
    public boolean getBusinessSystemWithColorAndCode() {
        boolean colorAndCodeFlag;
        //是否允许逻辑调拨单入库串色串码或多货 标识：true/false 默认true(从CP_C_BUSINESS_SYSTEM_PARAMETERS表获取)
        String param =
                (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(SgConstants.TRANSFER_ALLOW_IN_WITHCOLORANDCODE_OR_MOREGOODS);
        if (StringUtils.isEmpty(param)) {
            colorAndCodeFlag = true;
        } else {
            if (log.isDebugEnabled()) {
                log.debug(" Start SgBStoInResultSubmitService.colorAndCodeFlag param={}",
                        JSONObject.toJSONString(param));
            }
            colorAndCodeFlag = Boolean.valueOf(param);
        }
        return colorAndCodeFlag;
    }
}
