package com.burgeon.r3.sg.store.services.freeze;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.store.freeze.SgBStoFreeze;
import com.burgeon.r3.sg.core.model.table.store.freeze.SgBStoFreezeItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.freeze.SgBStoFreezeItemMapper;
import com.burgeon.r3.sg.store.mapper.freeze.SgBStoFreezeMapper;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeSaveItemRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeSaveRequest;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/16 14:59
 * 冻结单保存服务
 */
@Slf4j
@Component
public class SgBStoFreezeSaveService {

    @Autowired
    private SgBStoFreezeMapper freezeMapper;

    @Autowired
    private SgBStoFreezeItemMapper freezeItemMapper;

    @Autowired
    private CpCStoreMapper cpCStoreMapper;

    /**
     * 冻结单新增/更新服务
     *
     * @param session
     * @return ValueHolder
     */

    ValueHolder save(QuerySession session) {
        SgBStoFreezeBillSaveRequest request = R3ParamUtils.parseSaveObject(session, SgBStoFreezeBillSaveRequest.class);
        SgBStoFreezeSaveService bean = ApplicationContextHandle.getBean(SgBStoFreezeSaveService.class);
        request.setR3(true);
        return R3ParamUtils.convertV14WithResult(bean.save(request));
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> save(SgBStoFreezeBillSaveRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBStoFreezeSaveService save ReceiveParams request:{};", JSONObject.toJSONString(request));
        }
        Long objId = request.getObjId();
        SgBStoFreeze freeze = checkParams(request, objId);
        SgBStoFreezeSaveRequest freezeRequest = request.getFreezeSaveRequest();
        List<SgBStoFreezeSaveItemRequest> freezeItemRequestList = request.getSgBStoFreezeSaveItemRequests();
        if (objId == null || objId < 0) {
            return insertFreeze(freezeRequest, freezeItemRequestList, request.getLoginUser());
        } else {
            return updateFreeze(objId, freezeRequest, freezeItemRequestList, request.getLoginUser(), freeze);
        }
    }

    private ValueHolderV14<SgR3BaseResult> updateFreeze(Long objId, SgBStoFreezeSaveRequest freezeRequest,
                                                        List<SgBStoFreezeSaveItemRequest> freezeItemRequestList, User loginUser, SgBStoFreeze freeze) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBStoFreezeSaveService updateFreeze freezeRequest:{} freezeItemRequestList:{}",
                    JSONObject.toJSONString(freezeRequest), JSONObject.toJSONString(freezeItemRequestList));
        }
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14(ResultCode.SUCCESS, "保存逻辑冻结单成功！");
        String billNo = freeze.getBillNo();

        SgBStoFreeze update = new SgBStoFreeze();
        if (freezeRequest != null) {
            BeanUtils.copyProperties(freezeRequest, update);
        }
        //获取对应冻结单所有明细
        QueryWrapper<SgBStoFreezeItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SgBStoFreezeItem::getSgBStoFreezeId, freeze.getId())
                .eq(SgBStoFreezeItem::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgBStoFreezeItem> freezeItems = freezeItemMapper.selectList(queryWrapper);
        Map<Long, SgBStoFreezeItem> freezeItemMap = freezeItems.stream().collect(Collectors.toMap(SgBStoFreezeItem::getId, Function.identity()));
        String lockKsy = SgConstants.SG_B_STO_FREEZE + ":" + billNo;
        if (SgRedisLockUtils.lock(lockKsy)) {
            try {
                if (CollectionUtils.isNotEmpty(freezeItemRequestList)) {
                    ArrayList<SgBStoFreezeItem> addList = Lists.newArrayList();
                    for (SgBStoFreezeSaveItemRequest itemRequest : freezeItemRequestList) {
                        SgBStoFreezeItem freezeItem = new SgBStoFreezeItem();
                        Long itemId = itemRequest.getId();
                        //新增明细或修改明细
                        if (itemId > 0) {
                            SgBStoFreezeItem item = freezeItemMap.get(itemId);
                            BeanUtils.copyProperties(itemRequest, freezeItem);
                            StorageUtils.setBModelDefalutDataByUpdate(freezeItem, loginUser);
                            freezeItem.setQtyRemain(freezeItem.getQty());
                            freezeItem.setAmtListIn(freezeItem.getQty().multiply(item.getPriceList()));
                            freezeItemMapper.updateById(freezeItem);
                        } else {
                            BigDecimal qty = itemRequest.getQty() == null ? BigDecimal.ONE : itemRequest.getQty();
                            //totQty = totQty.add(qty);
                            CommonCacheValUtils.setSkuInfo(null, itemRequest.getPsCSkuEcode(), itemRequest);
                            BeanUtils.copyProperties(itemRequest, freezeItem);
                            freezeItem.setQty(qty);
                            //相同sku 数量累加
                            String skuEcode = itemRequest.getPsCSkuEcode();
                            SgBStoFreezeItem item = freezeItemMapper.selectOne(new QueryWrapper<SgBStoFreezeItem>().lambda().
                                    eq(SgBStoFreezeItem::getSgBStoFreezeId, objId).eq(SgBStoFreezeItem::getPsCSkuEcode, skuEcode)
                            .eq(SgBStoFreezeItem::getProduceDate,itemRequest.getProduceDate())
                            .eq(SgBStoFreezeItem::getStockType,itemRequest.getStockType()));
                            if (item != null && item.getId() != null) {
                                BigDecimal oldQty = freezeItem.getQty();
                                BigDecimal newQty = item.getQty();
                                BigDecimal allQty = oldQty.add(newQty);
                                freezeItem.setId(item.getId());
                                freezeItem.setQty(allQty);
                                freezeItem.setQtyRemain(allQty);
                                BigDecimal amtListIn = allQty.multiply(freezeItem.getPriceList());
                                freezeItem.setAmtListIn(amtListIn);
                                StorageUtils.setBModelDefalutDataByUpdate(freezeItem, loginUser);
                                freezeItemMapper.updateById(freezeItem);
                                continue;
                            }
                            freezeItem.setId(ModelUtil.getSequence(SgConstants.SG_B_STO_FREEZE_ITEM));
                            freezeItem.setSgBStoFreezeId(objId);
                            freezeItem.setQtyUnfreeze(BigDecimal.ZERO);
                            freezeItem.setQtyRemain(qty);
                            freezeItem.setAmtListIn(qty.multiply(freezeItem.getPriceList()));
                            StorageUtils.setBModelDefalutData(freezeItem, loginUser);
//                            totRowNum++;
                            addList.add(freezeItem);
                        }
                    }
                    List<List<SgBStoFreezeItem>> partition = Lists.partition(addList, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
                    for (List<SgBStoFreezeItem> items : partition) {
                        int result = freezeItemMapper.batchInsert(items);
                        if (result != items.size()) {
                            AssertUtils.logAndThrow("逻辑冻结单明细保存异常", loginUser.getLocale());
                        }
                    }
                }

                List<HashMap> maps = freezeItemMapper.selCountAndSum(objId);
                HashMap hashMap = maps.get(0);
                Long count = (Long) hashMap.get("count");
                BigDecimal sum = (BigDecimal) hashMap.get("sum");
                //主表修改
                StorageUtils.setBModelDefalutDataByUpdate(update, loginUser);
                update.setTotQty(sum);
                update.setTotQtyRemain(sum);
                update.setTotRowNum(count.intValue());
                LambdaUpdateWrapper<SgBStoFreeze> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(SgBStoFreeze::getId, objId);

                freezeMapper.update(update, updateWrapper);
            } catch (Exception e) {
                AssertUtils.logAndThrowException("冻结单保存异常：", e, loginUser.getLocale());
            } finally {
                SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
            }
        } else {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("逻辑冻结单正在被操作，请稍后重试！");
        }
        return v14;
    }

    private ValueHolderV14<SgR3BaseResult> insertFreeze(SgBStoFreezeSaveRequest freezeRequest,
                                                        List<SgBStoFreezeSaveItemRequest> freezeItemRequestList,
                                                        User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBStoFreezeSaveService insertFreeze freezeRequest:{} freezeItemRequestList:{}",
                    JSONObject.toJSONString(freezeRequest), JSONObject.toJSONString(freezeItemRequestList));
        }
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14(ResultCode.SUCCESS, "保存逻辑冻结单成功！");
        SgBStoFreeze insert = new SgBStoFreeze();
        BeanUtils.copyProperties(freezeRequest, insert);
        //获取主表id
        Long objId = ModelUtil.getSequence(SgConstants.SG_B_STO_FREEZE);
        insert.setId(objId);
        if (StringUtils.isEmpty(freezeRequest.getBillNo())) {
            String billNo = SgStoreUtils.getBillNo(SgStoreConstants.SEQ_SG_B_STO_FREEZE,
                    SgConstants.SG_B_STO_FREEZE.toUpperCase(), insert, loginUser.getLocale());
            insert.setBillNo(billNo);
        }
        if (freezeRequest.getBillDate() == null) {
            insert.setBillDate(new Date());
        }
        //单据默认未审核
        insert.setStatus(SgStoreConstants.BILL_STATUS_UNSUBMIT);

        BigDecimal totQty = BigDecimal.ZERO;
        BigDecimal totQtyUnfre = BigDecimal.ZERO;

        ArrayList<SgBStoFreezeItem> itemList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(freezeItemRequestList)) {
            //根据skuid 获取商品code id等字段值
            setSku(freezeItemRequestList);
            for (SgBStoFreezeSaveItemRequest itemRequest : freezeItemRequestList) {
                if (itemRequest.getQty().compareTo(BigDecimal.ZERO) < 0) {
                    AssertUtils.logAndThrow("当前逻辑冻结单明细，数量不允许为负数！");
                }
                BigDecimal qty = itemRequest.getQty() == null ? BigDecimal.ONE : itemRequest.getQty();
                totQty = totQty.add(qty);

                SgBStoFreezeItem insertItem = new SgBStoFreezeItem();
                BeanUtils.copyProperties(itemRequest, insertItem);

                insertItem.setId(ModelUtil.getSequence(SgConstants.SG_B_STO_FREEZE_ITEM));
                insertItem.setSgBStoFreezeId(objId);
                insertItem.setQty(qty);
                insertItem.setQtyUnfreeze(BigDecimal.ZERO);
                insertItem.setQtyRemain(qty);
                BigDecimal amtListIn = insertItem.getPriceList() != null ? qty.multiply(insertItem.getPriceList()) : BigDecimal.ZERO;
                insertItem.setAmtListIn(amtListIn);
                //通用字段赋值
                StorageUtils.setBModelDefalutData(insertItem, loginUser);
                itemList.add(insertItem);
            }
        }

        insert.setTotQty(totQty);
        insert.setTotQtyUnfreeze(totQtyUnfre);
        //第一次新增 总剩余量 = 总数量
        insert.setTotQtyRemain(totQty);
        insert.setTotRowNum(CollectionUtils.isNotEmpty(itemList) ? itemList.size() : 0);
        StorageUtils.setBModelDefalutData(insert, loginUser);
        if (freezeMapper.insert(insert) > 0) {
            List<List<SgBStoFreezeItem>> partition = Lists.partition(itemList, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
            for (List<SgBStoFreezeItem> freezeItems : partition) {
                int result = freezeItemMapper.batchInsert(freezeItems);
                if (freezeItems.size() != result) {
                    AssertUtils.logAndThrow("保存逻辑冻结单明细异常！", loginUser.getLocale());
                }
            }
        } else {
            AssertUtils.logAndThrow("保存逻辑冻结单异常！", loginUser.getLocale());
        }

        SgR3BaseResult sgR3BaseResult = new SgR3BaseResult();
        sgR3BaseResult.setDataJo(objId, SgConstants.SG_B_STO_FREEZE.toUpperCase());
        sgR3BaseResult.setBillNo(insert.getBillNo());
        v14.setData(sgR3BaseResult);
        return v14;
    }

    private SgBStoFreeze checkParams(SgBStoFreezeBillSaveRequest request, Long objId) {
        SgStoreUtils.checkR3BModelDefalut(request);
        AssertUtils.notNull(request, "请求参数不能为空！");
        SgBStoFreeze freeze = new SgBStoFreeze();
        if (objId > 0L) {
            freeze = freezeMapper.selectById(objId);
            AssertUtils.notNull(freeze, "当前逻辑冻结单记录已不存在！");
            Integer billStatus = freeze.getStatus();
            if (billStatus == SgStoreConstants.FREEZE_BILL_STATUS_SUBMIT_NO_UNFREEZE ||
                    billStatus == SgStoreConstants.FREEZE_BILL_STATUS_SUBMIT_PART_IN_UNFREEZE ||
                    billStatus == SgStoreConstants.FREEZE_BILL_STATUS_SUBMIT_ALL_UNFREEZE) {
                AssertUtils.logAndThrow("当前逻辑冻结单记录已审核，不允许编辑！");
            }
            if (billStatus == SgStoreConstants.FREEZE_BILL_STATUS_VOID) {
                AssertUtils.logAndThrow("当前逻辑冻结单记录已作废，不允许编辑！");
            }
        } else {
            //逻辑仓code为空时  冗余逻辑仓信息
            SgBStoFreezeSaveRequest freezeSaveRequest = request.getFreezeSaveRequest();

            if (!StringUtils.isEmpty(freezeSaveRequest.getBillNo())) {
                int count = freezeMapper.selectCount(
                        new QueryWrapper<SgBStoFreeze>().lambda()
                                .eq(SgBStoFreeze::getBillNo, freezeSaveRequest.getBillNo())
                                .eq(SgBStoFreeze::getIsactive, SgConstants.IS_ACTIVE_Y));
                AssertUtils.cannot(count > 0, "当前冻结单已经存在！", request.getLoginUser().getLocale());
            }
            if (StringUtils.isEmpty(freezeSaveRequest.getCpCStoreEcode())) {
                Long cStoreId = freezeSaveRequest.getCpCStoreId();
                SgCpCStore sgCpCStore = cpCStoreMapper.selectById(cStoreId);
                freezeSaveRequest.setCpCStoreEname(sgCpCStore.getCpCStoreEname());
                freezeSaveRequest.setCpCStoreEcode(sgCpCStore.getCpCStoreEcode());
            }
        }
        List<SgBStoFreezeSaveItemRequest> itemRequests = request.getSgBStoFreezeSaveItemRequests();
        if (CollectionUtils.isNotEmpty(itemRequests)) {
            for (SgBStoFreezeSaveItemRequest itemRequest : itemRequests) {
                if (itemRequest.getId() < 0L) {
                    AssertUtils.notNull(itemRequest.getPsCSkuEcode(), "条码不能为空！");
                    if (StringUtils.isBlank(itemRequest.getStockType())) {
                        AssertUtils.logAndThrow("当前逻辑冻结单明细条码:"+itemRequest.getPsCSkuEcode()+"库存类型不能为空！");
                    }
                }
                if (itemRequest.getQty() == null) {
                    itemRequest.setQty(BigDecimal.ONE);
                }
                if (itemRequest.getQty().compareTo(BigDecimal.ZERO) < 0) {
                    AssertUtils.logAndThrow("当前逻辑冻结单明细，数量不允许为负数！");
                }
            }
        }
        return freeze;
    }

    private void setSku(List<SgBStoFreezeSaveItemRequest> itemRequest) {
        List<String> skuCodesList = new ArrayList<>();
        List<Long> skuIds = new ArrayList<>();
        itemRequest.forEach(item -> {
            if (item.getPsCSpec1Id() == null || item.getPsCProEcode() == null) {
                if (!org.springframework.util.StringUtils.isEmpty(item.getPsCSkuEcode())) {
                    skuCodesList.add(item.getPsCSkuEcode());
                } else if (!org.springframework.util.StringUtils.isEmpty(item.getPsCSkuId())) {
                    skuIds.add(item.getPsCSkuId());
                }
            }
        });

        BasicPsQueryService psQueryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
        SkuInfoQueryRequest request = new SkuInfoQueryRequest();
        List<PsCProSkuResult> psProSkuList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(skuCodesList)) {
            request.setSkuEcodeList(skuCodesList);
            HashMap<String, PsCProSkuResult> skuInfoByEcode = psQueryService.getSkuInfoByEcode(request);
            if (MapUtils.isNotEmpty(skuInfoByEcode)) {
                psProSkuList.addAll(skuInfoByEcode.values());
            }
        } else {
            request.setSkuIdList(skuIds);
            HashMap<Long, PsCProSkuResult> skuInfo = psQueryService.getSkuInfo(request);
            if (MapUtils.isNotEmpty(skuInfo)) {
                psProSkuList.addAll(skuInfo.values());
            }
        }

        if (CollectionUtils.isNotEmpty(psProSkuList)) {
            Map<Long, PsCProSkuResult> skuIdMap = new HashMap<>();
            Map<String, PsCProSkuResult> skuEcodeMap = new HashMap<>();
            for (PsCProSkuResult pro : psProSkuList) {
                skuIdMap.put(pro.getId(), pro);
                skuEcodeMap.put(pro.getSkuEcode(), pro);
            }

            for (SgBStoFreezeSaveItemRequest item : itemRequest) {
                if (item.getPsCSkuId() != null && skuIdMap.containsKey(item.getPsCSkuId())) {
                    setSku(item, skuIdMap.get(item.getPsCSkuId()));
                } else if (StringUtils.isNotEmpty(item.getPsCSkuEcode()) && skuEcodeMap.containsKey(item.getPsCSkuEcode())) {
                    setSku(item, skuEcodeMap.get(item.getPsCSkuEcode()));
                }
            }
        }
    }


    private void setSku(SgBStoFreezeSaveItemRequest sourceModel, PsCProSkuResult data) {
        if (sourceModel != null) {
            if (org.springframework.util.StringUtils.isEmpty(sourceModel.getProduceDate())) {
                sourceModel.setProduceDate(SgConstantsIF.DEFAULT_PRODUCE_DATE);
            }
            if (sourceModel.getPsCSkuId() == null) {
                sourceModel.setPsCSkuId(data.getId());
            }
            if (sourceModel.getGbcode() == null) {
                sourceModel.setGbcode(data.getGbcode());
            }

            if (sourceModel.getForcode() == null) {
                sourceModel.setForcode(data.getForcode());
            }

            if (sourceModel.getPsCSkuEcode() == null) {

                sourceModel.setPsCSkuEcode(data.getSkuEcode());
            }
            if (sourceModel.getPsCProId() == null) {
                sourceModel.setPsCProId(data.getPsCProId());
            }

            if (StringUtils.isEmpty(sourceModel.getPsCProEcode())) {
                sourceModel.setPsCProEcode(data.getPsCProEcode());
            }

            if (StringUtils.isEmpty(sourceModel.getPsCProEname())) {
                sourceModel.setPsCProEname(data.getPsCProEname());
            }

            if (sourceModel.getPsCSpec1Id() == null) {
                sourceModel.setPsCSpec1Id(data.getPsCSpec1objId());
            }

            if (StringUtils.isEmpty(sourceModel.getPsCSpec1Ecode())) {
                sourceModel.setPsCSpec1Ecode(data.getClrsEcode());
            }
            if (StringUtils.isEmpty(sourceModel.getPsCSpec1Ename())) {
                sourceModel.setPsCSpec1Ename(data.getClrsEname());
            }

            if (sourceModel.getPsCSpec2Id() == null) {
                sourceModel.setPsCSpec2Id(data.getPsCSpec2objId());
            }

            if (StringUtils.isEmpty(sourceModel.getPsCSpec2Ecode())) {
                sourceModel.setPsCSpec2Ecode(data.getSizesEcode());
            }
            if (StringUtils.isEmpty(sourceModel.getPsCSpec2Ename())) {
                sourceModel.setPsCSpec2Ename(data.getSizesEname());
            }
            if (StringUtils.isEmpty(sourceModel.getGbcode())) {
                sourceModel.setGbcode(data.getGbcode());
            }
            if (sourceModel.getPriceList() == null) {
                sourceModel.setPriceList(Optional.ofNullable(data.getPricelist()).orElse(BigDecimal.ZERO));
            }
        }
    }
}
