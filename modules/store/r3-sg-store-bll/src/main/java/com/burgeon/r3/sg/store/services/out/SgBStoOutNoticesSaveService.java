package com.burgeon.r3.sg.store.services.out;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.basic.services.CpPhyWarehouseService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.core.common.R3ParamConstants;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.common.SgCoreUtilsConstants;
import com.burgeon.r3.sg.core.common.SgFromSourceBillTypeConstants;
import com.burgeon.r3.sg.core.common.SgRedisKeyResources;
import com.burgeon.r3.sg.core.enums.LabelRequestStatusEnum;
import com.burgeon.r3.sg.core.enums.WmsTypeEnum;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNoticesItem;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutResult;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutResultItem;
import com.burgeon.r3.sg.core.model.table.store.transfer.SgBStoTransfer;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesMapper;
import com.burgeon.r3.sg.store.mapper.transfer.SgBStoTransferMapper;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutNoticesBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutNoticesBillVoidRequest;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutNoticesBillSaveResult;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutNoticesBillSelectResult;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutNoticesBillVoidResult;
import com.burgeon.r3.sg.store.services.impl.SgBStoOutNoticesItemServiceImpl;
import com.burgeon.r3.sg.store.services.out.wms.CancelWmsServicesFactory;
import com.burgeon.r3.sg.store.services.rpc.IpRpcService;
import com.burgeon.r3.sg.store.services.tms.out.SgTmsLogisticsCancelService;
import com.burgeon.r3.sg.store.utils.QtyUtils;
import com.burgeon.r3.sg.store.utils.SgNoticesToWmsCommonUtils;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.api.qimen.QimenOrderCancelCmd;
import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/7/2
 * create at : 2021/7/2 4:54 下午
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SgBStoOutNoticesSaveService {
    @Value("${r3.sg.wms.mock.enabled:true}")
    private Boolean isMock;

    private final SgBStoOutNoticesMapper noticesMapper;

    private final CpCPhyWarehouseMapper cpCPhyWarehouseMapper;

    private final SgBStoOutNoticesItemServiceImpl noticesItemServiceImpl;

    private final SgBStoOutNoticesItemMapper noticesItemMapper;

    private final SgTmsLogisticsCancelService cancelService;

    private final SgBStoTransferMapper sgBStoTransferMapper;

    private final CpPhyWarehouseService cpPhyWarehouseService;

    private final IpRpcService ipRpcService;

    private final CpCStoreMapper storeMapper;

    @DubboReference(group = "ip", version = "1.4.0")
    private QimenOrderCancelCmd orderCancelCmd;

    public static final String SYSTEM_CANCELWMS_FAILED_REASON = "warehouse.cancel_wms_failed_reason";

    /**
     * 出库通知单新增-接口调用
     *
     * @param request request
     * @return holder
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgBStoOutNoticesBillSaveResult> addOutNotices(SgBStoOutNoticesBillSaveRequest request, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start SgBStoOutNoticesSaveService.addOutNotices.param:{},user:{};",
                    "SgBStoOutNoticesSaveService.addOutNotices"), JSONObject.toJSONString(request), user);
        }

        // 参数校验
        ValueHolderV14<SgBStoOutNoticesBillSaveResult> checkHolder = checkParams(request, user);
        if (!checkHolder.isOK()) {
            return checkHolder;
        }

        // 新增出库通知单
        SgBStoOutNotices outNotices = new SgBStoOutNotices();
        BeanUtils.copyProperties(request.getOutNoticesSaveRequest(), outNotices);
        outNotices.setId(ModelUtil.getSequence(SgConstants.SG_B_STO_OUT_NOTICES));

        SgBStoOutNoticesBillSaveRequest.SgBStoOutNoticesSaveRequest outNoticesSaveRequest = request.getOutNoticesSaveRequest();

        //订单商品类型
        Integer orderProType = outNoticesSaveRequest.getOrderProType();
        if (Objects.isNull(orderProType)) {
            orderProType = SgCoreUtilsConstants.ORDER_PRO_TYPE_ZP;
        }
        outNotices.setOrderProType(orderProType);

        //收货人详细地址
        outNotices.setReceiverAddress(outNoticesSaveRequest.getReceiverAddress());
        //收货人邮编
        outNotices.setReceiverZip(outNoticesSaveRequest.getReceiverZip());
        //收货人区
        outNotices.setCpCRegionAreaId(StringUtils.isNotEmpty(outNoticesSaveRequest.getReceiverArea()) ?
                Long.valueOf(outNoticesSaveRequest.getReceiverArea()) : null);
        //收货人市
        outNotices.setCpCRegionCityId(StringUtils.isNotEmpty(outNoticesSaveRequest.getReceiverCity()) ?
                Long.valueOf(outNoticesSaveRequest.getReceiverCity()) : null);
        //收货人省
        outNotices.setCpCRegionProvinceId(StringUtils.isNotEmpty(outNoticesSaveRequest.getReceiverProvince()) ?
                Long.valueOf(outNoticesSaveRequest.getReceiverProvince()) : null);
        //收货人电话
        outNotices.setReceiverMobile(outNoticesSaveRequest.getReceiverMobile());
        //收货人手机
        outNotices.setReceiverPhone(outNoticesSaveRequest.getReceiverPhone());
        //收货人
        outNotices.setReceiver(outNoticesSaveRequest.getReceiver());

        // 出库通知单明细新增
        List<SgBStoOutNoticesItem> outNoticesItems = new ArrayList<>();
        BigDecimal totQty = BigDecimal.ZERO;
        for (SgBStoOutNoticesBillSaveRequest.SgBStoOutNoticesItemSaveRequest item : request.getOutNoticesItemSaveRequests()) {
            SgBStoOutNoticesItem noticesItem = new SgBStoOutNoticesItem();
            BeanUtils.copyProperties(item, noticesItem);
            noticesItem.setId(ModelUtil.getSequence(SgConstants.SG_B_STO_OUT_NOTICES_ITEM));
            noticesItem.setSgBStoOutNoticesId(outNotices.getId());
            noticesItem.setQtyDiff(item.getQty());
            StorageUtils.setBModelDefalutData(noticesItem, user);
            noticesItem.setOwnerename(user.getName());
            noticesItem.setModifierename(user.getName());
            noticesItem.setStorageType(StringUtils.isNotEmpty(item.getStorageType()) ? item.getStorageType() : SgConstantsIF.STOCK_TYPE_GOODS);
            outNoticesItems.add(noticesItem);
            totQty = totQty.add(item.getQty());
        }

        if (CollectionUtils.isNotEmpty(outNoticesItems)) {
            List<List<SgBStoOutNoticesItem>> pageItemList = StorageUtils.getPageList(outNoticesItems, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
            for (List<SgBStoOutNoticesItem> items : pageItemList) {
                int i = noticesItemMapper.batchInsert(items);
                if (i != items.size()) {
                    AssertUtils.logAndThrow(Resources.getMessage("出库通知单", user.getLocale()).concat(
                            Resources.getMessage("批量插入记录失败!", user.getLocale())
                    ));
                }
            }
        }

        //出库通知单单号由中台创建(2021.9.9单据编号重复生成问题修复)
        String billNo = SgStoreUtils.getBillNo(SgStoreConstants.SEQ_SG_B_STO_OUT_NOTICES,
                SgConstants.SG_B_STO_OUT_NOTICES.toUpperCase(), outNotices, user.getLocale());
        outNotices.setBillNo(billNo);

        outNotices.setBillDate(Optional.ofNullable(outNotices.getBillDate()).orElse(new Date()));
        outNotices.setBillStatus(SgStoreConstants.BILL_NOTICES_STATUS_INIT);
        outNotices.setTotQty(totQty);
        outNotices.setTotQtyDiff(totQty);
        StorageUtils.setBModelDefalutData(outNotices, user);
        outNotices.setOwnerename(user.getEname());
        outNotices.setModifierename(user.getEname());
        outNotices.setRemark(StorageUtils.strSubString(outNotices.getRemark(), SgConstants.SG_COMMON_REMARK_SIZE));
        outNotices.setBuyerRemark(StorageUtils.strSubString(outNotices.getBuyerRemark(), SgConstants.SG_COMMON_REMARK_SIZE));
        outNotices.setSellerRemark(StorageUtils.strSubString(outNotices.getSellerRemark(), SgConstants.SG_COMMON_REMARK_SIZE));
        outNotices.setThirdPartyFailReason(StorageUtils.strSubString(outNotices.getThirdPartyFailReason(), SgConstants.SG_COMMON_STRING_SIZE));
        /*如果是逻辑调拨单，需要设置-调拨入库实体仓ID*/
        if (SgFromSourceBillTypeConstants.BILL_STO_TRANSFER.equals(outNotices.getSourceBillType()) && Objects.nonNull(outNotices.getSourceBillId())) {
            SgBStoTransfer transfer = sgBStoTransferMapper.selectById(outNotices.getSourceBillId());
            if (Objects.nonNull(transfer) && Objects.nonNull(transfer.getReceiverStoreId())) {
                SgCpCPhyWarehouse cpCPhyWarehouse = cpPhyWarehouseService.queryByStoreId(transfer.getReceiverStoreId());
                /*逻辑调拨单：调拨入库实体仓ID*/
                outNotices.setDbrkCpCPhyWarehouseId(Objects.isNull(cpCPhyWarehouse) ? null : cpCPhyWarehouse.getId());
            }
        }
        noticesMapper.insert(outNotices);

        return new ValueHolderV14<>(new SgBStoOutNoticesBillSaveResult(outNotices.getId(), outNotices.getBillNo()), ResultCode.SUCCESS, "保存成功!");
    }

    /**
     * 出库回写通知单更新状态出库数量
     *
     * @param result      逻辑出库单主表信息
     * @param resultItems 逻辑出库明细信息
     * @param user        用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOutNotices(SgBStoOutResult result, List<SgBStoOutResultItem> resultItems, User user) {
        String eName = user.getEname();

        //校验通知单是否存在
        SgBStoOutNotices updateNotices = noticesMapper.selectOne(new QueryWrapper<SgBStoOutNotices>().lambda()
                .eq(SgBStoOutNotices::getId, result.getSgBStoOutNoticesId()));
        if (updateNotices == null) {
            throw new NDSException("不存在对应的出库通知单!");
        }

        Integer billStatus = updateNotices.getBillStatus();
        if (billStatus != null && (billStatus.equals(SgStoreConstants.BILL_NOTICES_STATUS_ALL_OUT) ||
                billStatus.equals(SgStoreConstants.BILL_NOTICES_STATUS_VOID))) {
            throw new NDSException("逻辑出库单所关联的通知单状态异常，不允许审核!通知单单号:" + updateNotices.getBillNo());
        }

        Map<String, List<SgBStoOutNoticesItem>> noticesItemMap =
                noticesItemMapper.selectList(new QueryWrapper<SgBStoOutNoticesItem>().lambda()
                        .eq(SgBStoOutNoticesItem::getSgBStoOutNoticesId, updateNotices.getId())
                        .eq(SgBStoOutNoticesItem::getCpCStoreEcode, result.getCpCStoreEcode())
                        .in(SgBStoOutNoticesItem::getPsCSkuEcode, resultItems.stream().
                                map(SgBStoOutResultItem::getPsCSkuEcode).collect(Collectors.toSet()))
                ).stream().collect(Collectors.groupingBy(SgBStoOutNoticesItem::getPsCSkuEcode));

        List<SgBStoOutNoticesItem> updateOutNoticesItems = Lists.newArrayList();

        resultItems.forEach(resultItem -> {
            BigDecimal qtyOut = resultItem.getQty();
            List<SgBStoOutNoticesItem> outNoticesItemList = noticesItemMap.get(resultItem.getPsCSkuEcode());
            AssertUtils.cannot(CollectionUtils.isEmpty(outNoticesItemList),
                    "出库通知单:" + updateNotices.getBillNo() + ",逻辑仓:" + result.getCpCStoreEcode()
                            + ",条码:" + resultItem.getPsCSkuEcode() + "的明细不存在!");
            log.info(LogUtil.format("updateOutNotices.outNoticesItemList={}",
                    "SgBStoOutNoticesSaveService.updateOutNotices",
                    updateNotices.getBillNo(), resultItem.getPsCSkuEcode()), JSONObject.toJSONString(outNoticesItemList));
            for (SgBStoOutNoticesItem outNoticesItem : outNoticesItemList) {
                if (qtyOut.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                //相同条码明细中各明细的通知数量
                BigDecimal qtyNotices = outNoticesItem.getQty();
                //相同条码明细中各明细的已出库数量
                BigDecimal orgQtyOut = Optional.ofNullable(outNoticesItem.getQtyOut()).orElse(BigDecimal.ZERO);
                //可用明细的数量
                BigDecimal qtyOutItem = QtyUtils.getItemQtyout(qtyOut, qtyNotices, orgQtyOut);
                SgBStoOutNoticesItem update = new SgBStoOutNoticesItem();
                update.setId(outNoticesItem.getId());
                update.setQtyOut(orgQtyOut.add(qtyOutItem)); // 出库数量
                update.setQtyDiff(outNoticesItem.getQty().subtract(update.getQtyOut())); // 差异数量
                StorageUtils.setBModelDefalutDataByUpdate(update, user);
                update.setModifierename(eName);
                updateOutNoticesItems.add(update);
                //本次出库的数量 - 出库通知单中本条明细的可用数量
                qtyOut = qtyOut.subtract(qtyOutItem);
            }
            //校验出库条码的 出库数量 不能大于 通知数量
            if (qtyOut.compareTo(BigDecimal.ZERO) > 0) {
                throw new NDSException("条码" + resultItem.getPsCSkuEcode() + "出库数量不能大于通知数量！");
            }
        });

        if (CollectionUtils.isNotEmpty(updateOutNoticesItems)) {
            noticesItemServiceImpl.updateBatchById(updateOutNoticesItems, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
        }

        //从明细中获取总数量
        SgBStoOutNoticesItem noticesItem = noticesItemMapper.selectOutNoticesItemTotQty(updateNotices.getId());

        updateNotices.setTotQtyOut(noticesItem.getQtyOut());
        updateNotices.setTotQtyDiff(noticesItem.getQty().subtract(noticesItem.getQtyOut()));
        updateNotices.setOutTime(result.getOutTime() != null ? result.getOutTime() : new Date());
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("SgBStoOutNoticesSaveService.stoOutNoticesSaveService.updateOutNotices.ReceiveParams:result={}",
                    "updateOutNotices.result", result.getSgBStoOutNoticesId()), JSONObject.toJSONString(result));
        }
        if (updateNotices.getSourceBillType() == SgConstantsIF.BILL_TYPE_RETAIL) {
            updateNotices.setBillStatus(updateNotices.getTotQtyDiff().compareTo(BigDecimal.ZERO) == 0 ?
                    SgStoreConstants.BILL_NOTICES_STATUS_ALL_OUT : SgStoreConstants.BILL_NOTICES_STATUS_PART_OUT);
        } else {
            updateNotices.setBillStatus(result.getIsLast().equals(SgConstants.IS_LAST_YES) || updateNotices.getTotQtyDiff().compareTo(BigDecimal.ZERO) == 0 ?
                    SgStoreConstants.BILL_NOTICES_STATUS_ALL_OUT : SgStoreConstants.BILL_NOTICES_STATUS_PART_OUT);
        }

        StorageUtils.setBModelDefalutDataByUpdate(updateNotices, user);
        updateNotices.setModifierename(eName);

        noticesMapper.updateById(updateNotices);
    }

    /**
     * 作废出库通知单
     *
     * @param request requestModel
     * @param user    用户
     */
    public ValueHolderV14 voidOutNotices(SgBStoOutNoticesBillVoidRequest request, SgBStoOutNotices paramNotices, User user, Boolean isWms) {

        log.info(LogUtil.format("voidOutNotices.isWms:{}", "voidOutNotices"), isWms);

        //校验通知单是否存在
        SgBStoOutNotices notices = paramNotices == null ? noticesMapper.selectOne(new QueryWrapper<SgBStoOutNotices>().lambda()
                .eq(SgBStoOutNotices::getSourceBillId, request.getSourceBillId())
                .eq(StringUtils.isNotEmpty(request.getSourceBillNo()), SgBStoOutNotices::getSourceBillNo, request.getSourceBillNo())
                .eq(SgBStoOutNotices::getSourceBillType, request.getSourceBillType())
                .eq(SgBStoOutNotices::getIsactive, SgConstants.IS_ACTIVE_Y)) : paramNotices;
        if (notices == null) {
            return new ValueHolderV14(ResultCode.SUCCESS, "通知单记录不存在!");
        }

        // 判断订单状态是否为“已作废”，若是，则提示： “单据已作废，无需作废！”
        if (SgConstants.IS_ACTIVE_N.equals(notices.getIsactive()) && notices.getBillStatus().equals(SgStoreConstants.BILL_NOTICES_STATUS_VOID)) {
            return new ValueHolderV14(ResultCode.SUCCESS, "作废成功！");
        }

        // 判断订单状态是否为“待出库”若不是，则提示： “出库通知单状态不正确，不允许操作”
        if (notices.getBillStatus().equals(SgStoreConstants.BILL_NOTICES_STATUS_PART_OUT) ||
                notices.getBillStatus().equals(SgStoreConstants.BILL_NOTICES_STATUS_ALL_OUT)) {
            return new ValueHolderV14(ResultCode.FAIL, "作废失败,通知单已出库!");
        }

        if (notices.getWmsStatus() == SgStoreConstants.WMS_UPLOAD_STATUTS_UPLOADING) {
            return new ValueHolderV14(ResultCode.FAIL, "作废出库通知单失败,通知单传WMS中!");
        }
        if (notices.getWmsStatus() == SgStoreConstants.WMS_UPLOAD_STATUTS_SUCCESS
                || notices.getWmsStatus() == SgStoreConstants.WMS_UPLOAD_STATUTS_FAIL) {
            SgCpCPhyWarehouse phyWarehouse = cpCPhyWarehouseMapper.selectById(notices.getCpCPhyWarehouseId());
            if (Objects.isNull(phyWarehouse)) {
                return new ValueHolderV14(ResultCode.FAIL, "作废出库通知单失败,通知单实体仓已不存在或不可用!");
            }
            //当前“实体仓”在【实体仓档案】中的“WMS仓库类型”=巨沃（奇门），则调用云枢纽【奇门接口taobao.qimen.order.cancel( 单据取消接口
            if (isWms) {
                ValueHolderV14 cancelWMS = ipRpcService.cancelWMS(user, notices, phyWarehouse);
                if (!cancelWMS.isOK()) {

                    String message = cancelWMS.getMessage();
                    List<String> failMessage = SgNoticesToWmsCommonUtils.getFailMessage(notices.getBillNo());
                    log.info(LogUtil.format("系统参数从WMS撤回失败原因:{},过滤失败原因:{}", "系统参数从WMS撤回失败原因"),
                            message, JSONObject.toJSONString(failMessage));

                    if (failMessage.contains(message)) {
                        log.info(LogUtil.format("作废出库通知单成功,通知单取消wm成功，返回message:{},入库通知单:{}",
                                "作废出库通知单成功,通知单取消wm成功"), message, notices.getBillNo());
                    } else {
                        return new ValueHolderV14(ResultCode.FAIL, "作废出库通知单失败,通知单取消wms失败!失败原因:" + cancelWMS.getMessage());
                    }

                }
            }
        }
        SgBStoOutNotices updateNotices = new SgBStoOutNotices();
        updateNotices.setId(notices.getId());
        updateNotices.setIsactive(SgConstants.IS_ACTIVE_N);
        updateNotices.setBillStatus(SgStoreConstants.BILL_NOTICES_STATUS_VOID);
        StorageUtils.setBModelDefalutDataByUpdate(updateNotices, user);
        updateNotices.setModifierename(user.getEname());
        updateNotices.setDelerName(user.getName());
        updateNotices.setDelerEname(user.getEname());
        updateNotices.setDelerId(user.getId().longValue());
        updateNotices.setDelTime(new Date());

        try {
            if (SgStoreConstants.IS_PASS_THIRD_PARTY_Y.equals(notices.getIsPassThirdParty())) {
                //作废时候调用ip-tms接口，取消物流
                boolean isCancelTms = LabelRequestStatusEnum.SUCCESS.getCode().equals(notices.getEwaybillStatus()) || LabelRequestStatusEnum.CANCELFAIL.getCode().equals(notices.getEwaybillStatus());
                if (isCancelTms) {
                    SgTmsLogisticsCancelService bean = ApplicationContextHandle.getBean(SgTmsLogisticsCancelService.class);
                    ValueHolder valueHolder = bean.orderCancel(notices, updateNotices);
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("SgBStoOutNoticesSaveService.voidOutNotices.tmsCmd:result={}",
                                "SgBStoOutNoticesSaveService.voidOutNotices.orderCancel.result", notices.getBillNo()), JSONObject.toJSONString(valueHolder));
                    }
                }
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("SgBStoOutNoticesSaveService.voidOutNotices.tmsCmd. exception_has_occured:{}",
                    "SgBStoOutNoticesSaveService.voidOutNotices.orderCancel.exception", notices.getBillNo()), Throwables.getStackTraceAsString(ex));
        }

        noticesMapper.updateById(updateNotices);

        //释放新增通知单传WMS时添加的redis锁
        CusRedisTemplate<String, String> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();
        String lockKsy = SgConstants.SG_B_STO_OUT_NOTICES.toUpperCase() + ":" + notices.getSourceBillId() + ":" + notices.getSourceBillType() + ":" + "ADD";
        try {
            Boolean delete = redisMasterTemplate.delete(lockKsy);
            log.info(LogUtil.format("释放新增通知单传WMS时添加的redis锁,是否释放成功:{}，sourceBillId:{}", "SgBStoOutNoticesSaveService.voidOutNotices.redis.delete"), delete, notices.getSourceBillId());
        } catch (Exception e) {
            log.error(LogUtil.format("SgBStoOutNoticesSaveService.voidOutNotices 释放锁失败,请联系管理员! lockKey:{} exception_has_occured:{};",
                    "SgBStoOutNoticesSaveService.voidOutNotices.redis,error", notices.getSourceBillId(), notices.getSourceBillType()), lockKsy, Throwables.getStackTraceAsString(e));
        }

        return new ValueHolderV14(ResultCode.SUCCESS, "作废成功！");
    }


    /**
     * 作废出库通知单并从第三方撤回
     *
     * @param requests requestModel
     * @param user     用户
     */
    public ValueHolderV14<List<SgBStoOutNoticesBillVoidResult>> voidOutNoticesForOms(List<SgBStoOutNoticesBillVoidRequest> requests, User user) {
        ValueHolderV14<List<SgBStoOutNoticesBillVoidResult>> ret = new ValueHolderV14<>(ResultCode.SUCCESS, "反审核成功!");

        log.info(LogUtil.format("Start SgBStoOutNoticesSaveService.voidOutNoticesForOms Receive param:{};",
                "SgBStoOutNoticesSaveService.voidOutNoticesForOms.start"), JSONObject.toJSONString(requests));

        if (CollectionUtils.isEmpty(requests) || user == null) {
            throw new NDSException("参数或用户不能为空!");
        }

        boolean anyMatch = requests.stream().anyMatch(o ->
                o.getSourceBillId() == null || StringUtils.isEmpty(o.getSourceBillNo()) || o.getSourceBillType() == null);
        if (anyMatch) {
            throw new NDSException("来源单据id,单号或类型缺失!");
        }

        Set<Integer> sourceBillTypes = Sets.newHashSet();
        for (SgBStoOutNoticesBillVoidRequest request : requests) {
            sourceBillTypes.add(request.getSourceBillType());
        }

        if (sourceBillTypes.size() > 1) {
            throw new NDSException("暂不支持多种来源单据类型批量反审核!");
        }

        List<Integer> types = Lists.newArrayList(sourceBillTypes);

        Map<Long, SgBStoOutNotices> noticesMap = noticesMapper.selectList(new QueryWrapper<SgBStoOutNotices>().lambda()
                .in(SgBStoOutNotices::getSourceBillId, requests.stream().map(SgBStoOutNoticesBillVoidRequest::getSourceBillId).collect(Collectors.toSet()))
                .eq(SgBStoOutNotices::getSourceBillType, types.get(0))
                .eq(SgBStoOutNotices::getIsactive, SgConstants.IS_ACTIVE_Y))
                .stream().collect(Collectors.toMap(SgBStoOutNotices::getSourceBillId, Function.identity()));

        List<SgBStoOutNoticesBillVoidResult> datas = Lists.newArrayList();

        // 是否清空逻辑发货单-释放占用
        Boolean isCleanSend = Boolean.FALSE;
        // 是否清空逻辑发货单-释放在途
        Boolean isCleanReceive = Boolean.FALSE;
        Long serviceNode = 0L;
        JSONArray errorArr = new JSONArray();
        List<Long> noticesIdsByJD = Lists.newArrayList();
        for (int i = 0; i < requests.size(); i++) {
            SgBStoOutNoticesBillVoidRequest request = requests.get(i);
            SgBStoOutNotices outNotices = noticesMap.get(request.getSourceBillId());
            if (outNotices != null) {
                String lockKey = SgRedisKeyResources.buildSgBStoOutNoticesKey(outNotices.getId());
                try {
                    SgRedisLockUtils.lock(lockKey);
                    //拿到锁后再查一下最新状态，避免在上一次批量查询到加锁的过程有状态变化
                    outNotices = noticesMapper.selectById(outNotices.getId());
                    if (outNotices == null) {
                        datas.add(new SgBStoOutNoticesBillVoidResult(ResultCode.FAIL, "通知单记录不存在！",
                                request.getSourceBillId(), Boolean.FALSE));
                    }
                    if (SgConstants.IS_ACTIVE_N.equals(outNotices.getIsactive())) {
                        datas.add(new SgBStoOutNoticesBillVoidResult(ResultCode.FAIL, "通知单已作废！",
                                request.getSourceBillId(), Boolean.FALSE));
                    }
                    // 是否传第三方
                    Integer isPassThirdPartyTypeFlag = Optional.ofNullable(outNotices.getIsPassWms()).orElse(SgStoreConstants.IS_PASS_THIRD_PARTY_N);
                    if (SgStoreConstants.IS_PASS_THIRD_PARTY_Y.equals(isPassThirdPartyTypeFlag)) {
                        Long wmsStatus = Optional.ofNullable(outNotices.getWmsStatus()).orElse(SgStoreConstants.WMS_STATUS_WAIT_PASS);
                        if (Long.valueOf(SgStoreConstants.WMS_STATUS_PASSING).equals(wmsStatus)) {
                            datas.add(new SgBStoOutNoticesBillVoidResult(ResultCode.FAIL, "WMS传中，请稍后再试！",
                                    request.getSourceBillId(), Boolean.FALSE));
                        } else if (Long.valueOf(SgStoreConstants.WMS_STATUS_WAIT_PASS).equals(wmsStatus)) {
                            ValueHolderV14 voidNoticesHolder = voidOutNotices(request, outNotices, user, Boolean.FALSE);
                            if (!voidNoticesHolder.isOK()) {
                                datas.add(new SgBStoOutNoticesBillVoidResult(ResultCode.FAIL, voidNoticesHolder.getMessage(), request.getSourceBillId(), Boolean.FALSE));
                            }
                        } else if (Long.valueOf(SgStoreConstants.WMS_STATUS_PASS_SUCCESS).equals(wmsStatus) ||
                                Long.valueOf(SgStoreConstants.WMS_STATUS_PASS_FAILED).equals(wmsStatus)) {
                            SgCpCPhyWarehouse sgCpCPhyWarehouse = cpCPhyWarehouseMapper.selectById(outNotices.getCpCPhyWarehouseId());
                            if (sgCpCPhyWarehouse == null) {
                                datas.add(new SgBStoOutNoticesBillVoidResult(ResultCode.FAIL, "检验查询实体仓为空",
                                        request.getSourceBillId(), Boolean.FALSE));
                            }
                            if ((ThirdWmsTypeEnum.JYWMS.getCode().equals(sgCpCPhyWarehouse.getWmsType())
                                    || ThirdWmsTypeEnum.DBWMS.getCode().equals(sgCpCPhyWarehouse.getWmsType()))
                                    && Long.valueOf(SgStoreConstants.WMS_STATUS_PASS_FAILED).equals(wmsStatus)) {
                                //巨益的取消失败不去调接口，直接作废出库通知单即可
                                ValueHolderV14 voidNoticesHolder = voidOutNotices(request, outNotices, user, Boolean.FALSE);
                                if (!voidNoticesHolder.isOK()) {
                                    datas.add(new SgBStoOutNoticesBillVoidResult(ResultCode.FAIL, voidNoticesHolder.getMessage(), request.getSourceBillId(), Boolean.FALSE));
                                }
                            } else {
                                ValueHolderV14 qmResult = CancelWmsServicesFactory.getInstance().getHandle(WmsTypeEnum.OTHER)
                                        .cancelHandle(outNotices, sgCpCPhyWarehouse, user);
                                if (log.isDebugEnabled()) {
                                    log.debug(LogUtil.format("Finish CancelThirdPart Return results:{};",
                                            "SgBStoOutNoticesSaveService.voidOutNoticesForOms.start", outNotices.getBillNo()), JSONObject.toJSONString(qmResult));
                                }
                                // mock天生成功 TODO注意 线上apollo 改为 false
                                if (isMock) {
                                    // 撤回成功处理
                                    ValueHolderV14 voidNoticesHolder = voidOutNotices(request, outNotices, user, Boolean.FALSE);
                                    if (!voidNoticesHolder.isOK()) {
                                        datas.add(new SgBStoOutNoticesBillVoidResult(ResultCode.FAIL, voidNoticesHolder.getMessage(), request.getSourceBillId(), Boolean.FALSE));
                                    }
                                } else {
                                    if (qmResult != null && qmResult.isOK()) {
                                        //撤回成功处理
                                        ValueHolderV14 voidNoticesHolder = voidOutNotices(request, outNotices, user, Boolean.FALSE);
                                        if (!voidNoticesHolder.isOK()) {
                                            datas.add(new SgBStoOutNoticesBillVoidResult(ResultCode.FAIL, voidNoticesHolder.getMessage(), request.getSourceBillId(), Boolean.FALSE));
                                        }
                                    } else if (qmResult != null && !qmResult.isOK()) {
                                        String qmResultMessage = StringUtils.isNotEmpty(qmResult.getMessage()) ? qmResult.getMessage() : "";
                                        // 撤回失败处理
                                        cancelThirdFailedByWMS(qmResultMessage, outNotices, user, noticesIdsByJD, errorArr,
                                                isCleanSend, isCleanReceive, serviceNode);
                                    }
                                }
                            }
                        } else {
                            datas.add(new SgBStoOutNoticesBillVoidResult(ResultCode.FAIL, "WMS状态超过处理范围，请联系产品确认方案！",
                                    request.getSourceBillId(), Boolean.FALSE));
                        }
                    } else {
                        // 不传第三方直接作废
                        ValueHolderV14 voidNoticesHolder = voidOutNotices(request, outNotices, user, Boolean.FALSE);
                        if (!voidNoticesHolder.isOK()) {
                            datas.add(new SgBStoOutNoticesBillVoidResult(ResultCode.FAIL, voidNoticesHolder.getMessage(), request.getSourceBillId(), Boolean.FALSE));
                        }
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("SgBStoOutNoticesSaveService.voidOutNoticesForOms. exception_has_occured:{};",
                            "SgBStoOutNoticesSaveService.voidOutNoticesForOms.exception"), Throwables.getStackTraceAsString(e));
                    datas.add(new SgBStoOutNoticesBillVoidResult(ResultCode.FAIL, e.getMessage(), request.getSourceBillId(), Boolean.FALSE));
                } finally {
                    SgRedisLockUtils.unlock(lockKey);
                }
            } else {
                datas.add(new SgBStoOutNoticesBillVoidResult(ResultCode.SUCCESS, "反审核成功,通知单记录不存在!", request.getSourceBillId(), Boolean.TRUE));
            }
        }
        if (errorArr.size() > 0) {
            errorArr.forEach(error -> {
                JSONObject object = (JSONObject) error;
                Integer code = object.getInteger(R3ParamConstants.CODE);
                datas.add(new SgBStoOutNoticesBillVoidResult(code,
                        object.getString(R3ParamConstants.MESSAGE), object.getLong(R3ParamConstants.OBJID),
                        ResultCode.SUCCESS == code));
            });
        }

        ret.setData(datas);

        if (datas.size() > 0) {
            // 批量上锁,避免 订单审核的mq延时或堆积，订单又反审核了
            CusRedisTemplate<String, String> redisMasterTemplate = RedisMasterUtils.getObjRedisTemplate();
            for (int i = 0; i < datas.size(); i++) {
                SgBStoOutNoticesBillVoidResult voidResult = datas.get(i);
                if (voidResult.isOk() || !voidResult.getIsExist()) {
                    //撤回作废成功 上redis锁
                    String lockKsy = SgConstants.SG_B_STO_OUT_NOTICES.toUpperCase() + ":" + voidResult.getSourceBillId() + ":" + "BATCH_VOID";
                    String value = String.valueOf(System.currentTimeMillis());
                    Boolean ifAbsent = redisMasterTemplate.opsForValue().setIfAbsent(lockKsy, value);
                    if (ifAbsent != null && ifAbsent) {
                        redisMasterTemplate.expire(lockKsy, 1, TimeUnit.DAYS);
                    }
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("SgBStoOutNoticesSaveService.voidOutNoticesForOms.batchOnLock ifAbsent:{},sourceBillId:{},lockKsy:{},value:{};",
                                "SgBStoOutNoticesSaveService.batchOnLock", voidResult.getSourceBillId()), ifAbsent, voidResult.getSourceBillId(), lockKsy, value);
                    }
                }
            }
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish SgBStoOutNoticesSaveService.voidOutNoticesForOms Return results:{};",
                    "SgBStoOutNoticesSaveService.finish"), JSONObject.toJSONString(ret));
        }
        return ret;
    }

    public ValueHolderV14<List<SgBStoOutNoticesBillSelectResult>> selSgStoOutNotices(List<Long> id) {
        ValueHolderV14<List<SgBStoOutNoticesBillSelectResult>> v14 = new ValueHolderV14(ResultCode.SUCCESS, "success");
        if (log.isDebugEnabled()) {
            log.debug("Finish SgBStoOutNoticesSaveService selSgStoOutNotices id {};", id);
        }
        List<SgBStoOutNoticesBillSelectResult> sgBStoOutNoticesitems = noticesMapper.selectAllQtyOut(id);
        v14.setData(sgBStoOutNoticesitems);
        return v14;
    }

    /**
     * 参数校验(接口调用)
     *
     * @param request 入参
     */
    public ValueHolderV14<SgBStoOutNoticesBillSaveResult> checkParams(SgBStoOutNoticesBillSaveRequest request, User user) {

        ValueHolderV14<SgBStoOutNoticesBillSaveResult> checkHolder = new ValueHolderV14<>(ResultCode.SUCCESS, "校验成功");

        try {
            SgBStoOutNoticesBillSaveRequest.SgBStoOutNoticesSaveRequest outNoticesRequest = request.getOutNoticesSaveRequest();
            List<SgBStoOutNoticesBillSaveRequest.SgBStoOutNoticesItemSaveRequest> itemSaveRequests = request.getOutNoticesItemSaveRequests();

            boolean validate = user != null && outNoticesRequest != null && CollectionUtils.isNotEmpty(itemSaveRequests) &&
                    outNoticesRequest.getOutType() != null && outNoticesRequest.getSourceBillId() != null &&
                    outNoticesRequest.getSourceBillType() != null && StringUtils.isNotEmpty(outNoticesRequest.getSourceBillNo());
            if (!validate) {
                throw new NDSException("用户信息,明细,出库类型,来源单据信息不能为空!");
            }

            boolean anyMatch = itemSaveRequests.stream().anyMatch(itemSaveRequest -> itemSaveRequest.getPsCSkuId() == null ||
                    itemSaveRequest.getCpCStoreId() == null || StringUtils.isEmpty(itemSaveRequest.getCpCStoreEcode()) ||
                    StringUtils.isEmpty(itemSaveRequest.getCpCStoreEname()) || itemSaveRequest.getQty() == null ||
                    itemSaveRequest.getQty().compareTo(BigDecimal.ZERO) == 0 || itemSaveRequest.getSourceBillItemId() == null);
            if (anyMatch) {
                throw new NDSException("明细skuid、来源明细ID、逻辑仓信息、数量不能为空(0)!");
            }

            List<SgBStoOutNotices> noticesList = noticesMapper.selectList(new QueryWrapper<SgBStoOutNotices>().lambda()
                    .eq(SgBStoOutNotices::getSourceBillId, outNoticesRequest.getSourceBillId())
                    .eq(SgBStoOutNotices::getSourceBillType, outNoticesRequest.getSourceBillType())
                    .eq(StringUtils.isNotEmpty(outNoticesRequest.getSourceBillNo()), SgBStoOutNotices::getSourceBillNo, outNoticesRequest.getSourceBillNo())
                    .eq(SgBStoOutNotices::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (CollectionUtils.isNotEmpty(noticesList)) {
                throw new NDSException("出库通知单已存在,不允许重复新增!");
            }


            SgCpCStore store = storeMapper.selectById(itemSaveRequests.get(0).getCpCStoreId());
            outNoticesRequest.setCpCPhyWarehouseId(store.getCpCPhyWarehouseId());
            outNoticesRequest.setCpCPhyWarehouseEcode(store.getCpCPhyWarehouseEcode());
            outNoticesRequest.setCpCPhyWarehouseEname(store.getCpCPhyWarehouseEname());

            CpCPhyWarehouse wareHouse = CommonCacheValUtils.getWareHouseByEcode(store.getCpCPhyWarehouseEcode());
            AssertUtils.notNull(wareHouse, "新增出库通知单失败：实体仓信息存在问题！");
            Integer wmsControlWarehouse = wareHouse.getWmsControlWarehouse();
            if (outNoticesRequest.getIsPassWms() == null) {
                outNoticesRequest.setIsPassWms(wmsControlWarehouse);
            }


        } catch (Exception e) {
            log.error(LogUtil.format("checkParams. exception_has_occured:{}",
                    "SgBStoOutNoticesSaveService.checkParams"), Throwables.getStackTraceAsString(e));
            checkHolder.setCode(ResultCode.FAIL);
            checkHolder.setMessage(e.getMessage());
        }
        return checkHolder;
    }

    /**
     * 撤回失败处理
     *
     * @param qmResultMessage 撤回返回失败原因
     * @param outNotices      通知单
     * @param loginUser       操作用户
     * @param noticesIdsByJD  撤回成功的 快递是京东物流的 通知单ids
     * @param errorArr        错误集合
     * @param isCleanSend
     * @param serviceNode
     */
    private void cancelThirdFailedByWMS(String qmResultMessage, SgBStoOutNotices outNotices, User loginUser, List<Long> noticesIdsByJD,
                                        JSONArray errorArr, Boolean isCleanSend, Boolean isCleanReceive, Long serviceNode) {
        try {

            List<String> failMessage = SgNoticesToWmsCommonUtils.getFailMessage(outNotices.getBillNo());
            log.info(LogUtil.format("系统参数从WMS撤回失败原因:{},过滤失败原因:{}", "系统参数从WMS撤回失败原因"),
                    qmResultMessage, JSONObject.toJSONString(failMessage));

            if (failMessage.contains(qmResultMessage)) {
                //从第三方撤回成功,作废通知单并记录结果
                cancelThirdSuccess(outNotices, loginUser, noticesIdsByJD, errorArr, isCleanSend, isCleanReceive, serviceNode);
            } else {
                errorRecord(outNotices.getSourceBillId(), com.jackrain.nea.constants.ResultCode.FAIL, qmResultMessage, errorArr, null);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("系统参数从WMS撤回失败原因解析异常,error:{}"), Throwables.getStackTraceAsString(e));
            errorRecord(outNotices.getSourceBillId(), com.jackrain.nea.constants.ResultCode.FAIL, e.getMessage(), errorArr,
                    null);
        }
    }


    /**
     * 撤回失败处理
     *
     * @param qmResultMessage 撤回返回失败原因
     * @param outNotices      通知单
     * @param loginUser       操作用户
     * @param noticesIdsByJD  撤回成功的 快递是京东物流的 通知单ids
     * @param errorArr        错误集合
     * @param isCleanSend
     * @param serviceNode
     */
    private void cancelThirdFailed(String qmResultMessage, SgBStoOutNotices outNotices, User loginUser, List<Long> noticesIdsByJD,
                                   JSONArray errorArr, Boolean isCleanSend, Boolean isCleanReceive, Long serviceNode) {
        try {
            //TODO 这里需要区分第三方类型
            String failedReason = AdParamUtil.getParam(SYSTEM_CANCELWMS_FAILED_REASON);
            log.info("系统参数从WMS撤回失败原因:" + failedReason);
            if (StringUtils.isNotEmpty(failedReason)) {
                String[] reasons = failedReason.split(";;");
                List<String> failedReasons = Lists.newArrayList();
                for (String reason : reasons) {
                    String replace = reason.replace("[", "").replace("]", "");
                    failedReasons.add(replace);
                }
                boolean contains = false;
                for (String reason : failedReasons) {
                    if (qmResultMessage.contains(reason)) {
                        contains = true;
                        break;
                    }
                }
                if (contains) {
                    //从第三方撤回成功,作废通知单并记录结果
                    cancelThirdSuccess(outNotices, loginUser, noticesIdsByJD, errorArr, isCleanSend, isCleanReceive, serviceNode);
                } else {
                    errorRecord(outNotices.getSourceBillId(), com.jackrain.nea.constants.ResultCode.FAIL, qmResultMessage, errorArr, null);
                }
            } else {
                errorRecord(outNotices.getSourceBillId(), com.jackrain.nea.constants.ResultCode.FAIL, qmResultMessage, errorArr, null);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("系统参数从WMS撤回失败原因解析异常,error:{}"), Throwables.getStackTraceAsString(e));
            errorRecord(outNotices.getSourceBillId(), com.jackrain.nea.constants.ResultCode.FAIL, e.getMessage(), errorArr,
                    null);
        }
    }

    /**
     * 撤回成功处理
     *
     * @param outNotices     通知单
     * @param loginUser      操作用户
     * @param noticesIdsByJD 撤回成功的 快递是京东物流的 通知单ids
     * @param errorArr       错误集合
     * @param isCleanSend
     * @param serviceNode
     */
    private void cancelThirdSuccess(SgBStoOutNotices outNotices, User loginUser, List<Long> noticesIdsByJD, JSONArray errorArr,
                                    Boolean isCleanSend, Boolean isCleanReceive, Long serviceNode) {

        voidSgPhyOutNoticesSingle(outNotices, loginUser, noticesMapper, isCleanSend, isCleanReceive, serviceNode);

        if (SgConstants.LOGISTICS_ECODE_JD.equals(outNotices.getCpCLogisticsEcode())) {
            noticesIdsByJD.add(outNotices.getId());
        }

        errorRecord(outNotices.getSourceBillId(), com.jackrain.nea.constants.ResultCode.SUCCESS, "作废成功！", errorArr, null);

    }

    /**
     * 单条作废
     *
     * @param outNotices  通知单对象
     * @param user        用户信息
     * @param isCleanSend
     * @param serviceNode
     */
    public void voidSgPhyOutNoticesSingle(SgBStoOutNotices outNotices, User user, SgBStoOutNoticesMapper noticesMapper, Boolean isCleanSend, Boolean isCleanReceive, Long serviceNode) {
        // 判断订单状态是否为“已作废”，若是，则提示： “单据已作废，无需作废！”
        if (SgConstants.IS_ACTIVE_N.equals(outNotices.getIsactive()) &&
                outNotices.getBillStatus() == SgStoreConstants.OUT_NOTICES_STATUS_VOID) {
            AssertUtils.logAndThrow("单据已作废，无需作废！", user.getLocale());
        }

        // 判断订单状态是否为“待出库”若不是，则提示： “出库通知单状态不正确，不允许操作”
        if (outNotices.getBillStatus() != SgStoreConstants.OUT_NOTICES_STATUS_WAIT) {
            AssertUtils.logAndThrow("当前出库通知单已存在出库记录，不允许作废！", user.getLocale());
        }
        outNotices.setIsactive(SgConstants.IS_ACTIVE_N);
        outNotices.setBillStatus(SgStoreConstants.OUT_NOTICES_STATUS_VOID);
        outNotices.setModifierename(user.getEname());
        noticesMapper.updateById(outNotices);

        //释放新增通知单传WMS时添加的redis锁
        CusRedisTemplate<String, String> redisMasterTemplate = RedisMasterUtils.getObjRedisTemplate();
        String lockKsy = SgConstants.SG_B_STO_OUT_NOTICES.toUpperCase() + ":" + outNotices.getSourceBillId() + ":" + outNotices.getSourceBillType() + ":" + "ADD";
        try {
            Boolean delete = redisMasterTemplate.delete(lockKsy);
            log.info(LogUtil.format("释放锁成功，lockKsy:{},delete:{}", lockKsy, delete));
        } catch (Exception e) {
            log.error(LogUtil.format("释放锁失败，请联系管理员！,error:{},lockKsy=", lockKsy),
                    Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 错误信息收集
     *
     * @param mainId   记录ID
     * @param message  错误信息
     * @param errorArr 错误集合
     */
    private void errorRecord(Long mainId, Integer code, String message, JSONArray errorArr, Boolean isExist) {
        JSONObject errorDate = new JSONObject();
        errorDate.put(R3ParamConstants.CODE, code);
        errorDate.put(R3ParamConstants.OBJID, mainId);
        errorDate.put(R3ParamConstants.MESSAGE, message);

        Boolean flag = Optional.ofNullable(isExist).orElse(Boolean.FALSE);
        if (flag) {
            errorDate.put("is_exist", null);
        }
        errorArr.add(errorDate);
    }
}
