package com.burgeon.r3.sg.store.services.out.action.jingdong;

import com.alibaba.dubbo.config.annotation.Reference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInNotices;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInNoticesItem;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNoticesItem;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesMapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ip.api.jingdong.JdServiceCmd;
import com.jackrain.nea.ip.common.JdClpsResultFlagEnum;
import com.jackrain.nea.ip.model.jingdong.request.JdAddPoOrderItemRequest;
import com.jackrain.nea.ip.model.jingdong.request.JdAddPoOrderRequest;
import com.jackrain.nea.ip.model.jingdong.request.JdAddRtsOrderItemRequest;
import com.jackrain.nea.ip.model.jingdong.request.JdAddRtsOrderRequest;
import com.jackrain.nea.ip.model.jingdong.response.JdAddPoOrderResponse;
import com.jackrain.nea.ip.model.jingdong.response.JdAddRtsOrderResponse;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date 2022/7/28 23:46
 * @Description
 */
@Slf4j
@Component
public class SgBStoOutNoticesToJdAddRtsOrderService extends ServiceImpl<SgBStoOutNoticesMapper, SgBStoOutNotices> {

    @Autowired
    private SgBStoOutNoticesMapper sgBStoOutNoticesMapper;

    @Autowired
    private SgBStoOutNoticesItemMapper sgBStoOutNoticesItemMapper;

    @Reference(version = "1.0",group = "ip",timeout = 60000)
    private JdServiceCmd jdServiceCmd;

    public void executeOutNotice(Integer limit) {

        List<JdAddRtsOrderRequest> requestList = sgBStoOutNoticesMapper.queryOutNoticesToJdAddRtsOrderWms(limit);
        log.info(LogUtil.format("SgBStoOutNoticesToJdAddRtsOrderService.executeOutNotice.size={};",
                "SgBStoOutNoticesToJdAddRtsOrderService.executeOutNotice"), requestList.size());
        if (!CollectionUtils.isEmpty(requestList)) {
            //更新为传中
            List<Long> mainTableIds =
                    requestList.stream().map(JdAddRtsOrderRequest::getMainTableId).collect(Collectors.toList());
            SgBStoOutNotices update = new SgBStoOutNotices();
            update.setWmsStatus(SgStoreConstants.WMS_UPLOAD_STATUTS_UPLOADING);
            update.setPassWmsTime(new Date());
            sgBStoOutNoticesMapper.update(update, new LambdaUpdateWrapper<SgBStoOutNotices>()
                    .in(SgBStoOutNotices::getId, mainTableIds));

            //子表信息
            List<SgBStoOutNoticesItem> noticesItems =
                    sgBStoOutNoticesItemMapper.selectList(new LambdaQueryWrapper<SgBStoOutNoticesItem>()
                            .in(SgBStoOutNoticesItem::getSgBStoOutNoticesId, mainTableIds));
            Map<Long, List<SgBStoOutNoticesItem>> itemMap =
                    noticesItems.stream().collect(Collectors.groupingBy(SgBStoOutNoticesItem::getSgBStoOutNoticesId));

            //子表请求参数封装
            for (JdAddRtsOrderRequest request : requestList) {
                List<JdAddRtsOrderItemRequest> itemRequestList = new ArrayList<>();
                List<SgBStoOutNoticesItem> itemList = itemMap.get(request.getMainTableId());
                if (CollectionUtils.isEmpty(itemList)) {
                    continue;
                }
                for (SgBStoOutNoticesItem item : itemList) {
                    JdAddRtsOrderItemRequest itemRequest = new JdAddRtsOrderItemRequest();
                    if (!StringUtils.isEmpty(item.getPsCSkuEcode())) {
                        itemRequest.setItemCode(item.getPsCSkuEcode());
                    }
                    if (item.getQty() != null) {
                        itemRequest.setItemOutQty(String.valueOf(item.getQty()));
                    }
                    itemRequestList.add(itemRequest);
                }
                request.setItemRequestList(itemRequestList);
            }

            List<SgBStoOutNotices> successList = new ArrayList<>();
            List<SgBStoOutNotices> failureList = new ArrayList<>();
            ValueHolderV14<List<JdAddRtsOrderResponse>> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
            try {
                vh = jdServiceCmd.addRtsOrder(requestList);
            } catch (Exception e) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("调用IP服务异常!" + e.getMessage());
            }

            if (!CollectionUtils.isEmpty(vh.getData())) {
                Map<Long,JdAddRtsOrderResponse> failureMap = new HashMap<>();
                for (JdAddRtsOrderResponse response : vh.getData()) {
                    if (JdClpsResultFlagEnum.SUCCESS.getFlag().equals(response.getFlag())) {
                        //成功
                        SgBStoOutNotices outNotices = new SgBStoOutNotices();
                        outNotices.setId(response.getMainTableId());
                        outNotices.setWmsStatus(SgStoreConstants.WMS_UPLOAD_STATUTS_SUCCESS);
                        outNotices.setWmsFailReason("");
                        successList.add(outNotices);
                    }else {
                        //失败
                        failureMap.put(response.getMainTableId(),response);
                    }
                }
                if (!CollectionUtils.isEmpty(failureMap)) {
                    Set<Long> ids = failureMap.keySet();
                    List<SgBStoOutNotices> list = sgBStoOutNoticesMapper.selectBatchIds(ids);
                    for (SgBStoOutNotices model : list) {
                        SgBStoOutNotices outNotices = new SgBStoOutNotices();
                        outNotices.setId(model.getId());
                        outNotices.setWmsStatus(SgStoreConstants.WMS_UPLOAD_STATUTS_FAIL);
                        outNotices.setWmsFailCount(model.getWmsFailCount() == null ? 1 : model.getWmsFailCount() + 1);
                        String message = failureMap.get(model.getId()).getMessage();
                        outNotices.setWmsFailReason(message.length() > 500 ? message.substring(0,499) : message);
                        failureList.add(outNotices);
                    }
                }
            }

            //补偿
            if ((successList.size() + failureList.size()) < mainTableIds.size()) {
                //已收集的ID集合
                List<Long> collectIds = new ArrayList<>();
                if (!CollectionUtils.isEmpty(successList)) {
                    collectIds.addAll(successList.stream().map(SgBStoOutNotices::getId).collect(Collectors.toList()));
                }
                if (!CollectionUtils.isEmpty(failureList)) {
                    collectIds.addAll(failureList.stream().map(SgBStoOutNotices::getId).collect(Collectors.toList()));
                }

                //未收集ID
                List<Long> noCollectIds = new ArrayList<>();
                if (!CollectionUtils.isEmpty(collectIds)) {
                    mainTableIds.removeAll(collectIds);
                }
                noCollectIds = mainTableIds;
                if (!CollectionUtils.isEmpty(noCollectIds)) {
                    List<SgBStoOutNotices> list = sgBStoOutNoticesMapper.selectBatchIds(noCollectIds);
                    for (SgBStoOutNotices model : list) {
                        SgBStoOutNotices outNotices = new SgBStoOutNotices();
                        outNotices.setId(model.getId());
                        outNotices.setWmsStatus(SgStoreConstants.WMS_UPLOAD_STATUTS_FAIL);
                        outNotices.setWmsFailCount(model.getWmsFailCount() == null ? 1 : model.getWmsFailCount() + 1);
                        String message = vh.getMessage();
                        outNotices.setWmsFailReason(message.length() > 500 ? message.substring(0,499) : message);
                        failureList.add(outNotices);
                    }
                }

            }

            if (!CollectionUtils.isEmpty(successList)) {
                this.updateBatchById(successList);
            }
            if (!CollectionUtils.isEmpty(failureList)) {
                this.updateBatchById(failureList);
            }
        }


    }
}
