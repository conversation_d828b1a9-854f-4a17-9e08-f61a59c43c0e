package com.burgeon.r3.sg.store.services.in;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.utils.ExportUtil;
import com.burgeon.r3.sg.channel.model.enumerate.YseNoEnum;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.store.in.SgBStoInResultItem;
import com.burgeon.r3.sg.store.api.in.SgBStoInResultItemExportCmd;
import com.burgeon.r3.sg.store.common.OrderTypeEnum;
import com.burgeon.r3.sg.store.mapper.in.SgBStoInResultItemMapper;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInResultItemExportResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

/**
 * 逻辑入库单明细导出
 *
 * <AUTHOR>
 * @since 2024-10-31 11:43
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgBStoInResultItemExportCmdImpl implements SgBStoInResultItemExportCmd {
    private static final String LOG_OBJ = "SgBStoInResultItemExportCmdImpl.";

    @Resource
    private ExportUtil exportUtil;

    @Resource
    private SgBStoInResultItemMapper sgBStoInResultItemMapper;

    @Resource
    private ThreadPoolTaskExecutor asyncExecutorPool;

    @Resource
    private AsyncTaskManager asyncTaskManager;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(
                JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("导出逻辑入库单明细开始，入参：{}", LOG_OBJ + "execute"), param);
        if (Objects.isNull(param)) {
            throw new NDSException("导出逻辑入库单明细入参非法！");
        }

        JSONArray idArray = param.getJSONArray("ids");
        if (Objects.isNull(idArray) || idArray.size() < 1 || idArray.size() > 2000) {
            throw new NDSException("所选逻辑入库单不能空或超过2000！");
        }


        Set<Long> objIds = new HashSet<>(idArray.toJavaList(Long.class));

        try {
            Integer count = sgBStoInResultItemMapper.selectCount(new QueryWrapper<SgBStoInResultItem>().lambda()
                    .in(SgBStoInResultItem::getSgBStoInResultId, objIds)
                    .eq(SgBStoInResultItem::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (count == 0 || count > 5000) {
                log.error(LogUtil.format("导出逻辑入库单明细行数错误，主表数量:{}，明细数量：{}",
                        LOG_OBJ + "execute"), objIds.size(), count);
                throw new NDSException("导出逻辑入库单明细不能为空超过5000条");
            }
            asyncExecutorPool.execute(() -> exportItems(objIds, session.getUser()));
        } catch (Exception e) {
            log.error(LogUtil.format("导出逻辑入库单明细异常,错误信息：{}",
                    LOG_OBJ + "execute"), Throwables.getStackTraceAsString(e));
            return ValueHolderUtils.fail(e.getMessage());
        }

        return ValueHolderUtils.success("逻辑入库单明细导出任务开始！详情请在我的任务查看！", null);
    }

    private void exportItems(Set<Long> objIds, User user) {
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("逻辑入库单明细导出");
        asyncTaskBody.setTaskType("导出");

        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        doExport(asyncTaskBody, user, objIds);
    }

    private void doExport(AsyncTaskBody asyncTaskBody, User user, Set<Long> objIds) {
        Map<String, Object> taskRetMap = new HashMap<>();

        try {
            List<SgBStoInResultItemExportResult> dataList = generateData(objIds);

            List<String> excelTitle = Lists.newArrayList("入库通知单", "是否最后一次入库", "单据编号", "单据日期", "来源单据来源类型",
                    "业务单据类型", "业务单据编码", "逻辑仓编码", "逻辑仓名称", "发货方编码", "发货方名称", "入库日期", "WMS单据编号",
                    "商品编码", "商品名称", "生产日期", "入库数量");
            List<String> excelAttr = Lists.newArrayList("sgBStoInNoticesNo", "isLastStr", "billNo", "billDate", "sourceBillSourceBillType",
                    "sourceBillTypeStr", "sourceBillNo", "cpCStoreEcode", "cpCStoreEname", "senderEcode", "senderName", "inTime", "wmsBillNo",
                    "psCProEcode", "psCProEname", "produceDate", "qty");
            XSSFWorkbook xssfWorkbook = exportUtil.execute("逻辑入库单明细", "逻辑入库单明细", excelTitle, excelAttr, dataList);
            String fileUrl = exportUtil.saveFileAndPutOss(xssfWorkbook, "逻辑入库单明细", user, "OSS-Bucket/EXPORT/SG_B_STO_IN_RESULT_ITEM/");

            log.info(LogUtil.format("执行逻辑入库单明细导出完成，操作人:{},主表记录数:{},明细记录数:{}，文件地址:{}", LOG_OBJ + "doExport"),
                    user.getId(), objIds.size(), dataList.size(), fileUrl);
            taskRetMap.put("code", ResultCode.SUCCESS);
            taskRetMap.put("data", "点击下载");
            taskRetMap.put("message", objIds.size() + "个逻辑入库单，共导出明细行数：" + dataList.size());

            asyncTaskBody.setUrl(fileUrl);
            asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(taskRetMap)));
        } catch (Exception e) {
            log.error(LogUtil.format("执行逻辑入库单明细导出失败，操作人:{},入参:{}，异常信息:{}", LOG_OBJ + "doExport"),
                    user.getId(), objIds, Throwables.getStackTraceAsString(e));
            taskRetMap.put("code", ResultCode.FAIL);
            taskRetMap.put("message", "导出异常：" + e.getMessage());
            asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(taskRetMap)));
        }
    }

    private List<SgBStoInResultItemExportResult> generateData(Set<Long> mainIds) {
        List<SgBStoInResultItemExportResult> resultList = sgBStoInResultItemMapper.selectItemByMainIds(mainIds);
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }

        for (SgBStoInResultItemExportResult sto : resultList) {
            if (Objects.nonNull(sto.getSourceBillType())) {
                sto.setSourceBillTypeStr(convertSourceBillType(sto.getSourceBillType()));
            }
            if (Objects.nonNull(sto.getIsLast())) {
                sto.setIsLastStr(YseNoEnum.getDescriptionCNByCode(sto.getIsLast()));
            }
        }

        return resultList;
    }

    private String convertSourceBillType(Integer sourceBillType) {
        if (OrderTypeEnum.SOURCE_BILL_TYPE_2.getValue().equals(sourceBillType)) {
            return OrderTypeEnum.SOURCE_BILL_TYPE_2.getDescription();
        }

        if (OrderTypeEnum.SOURCE_BILL_TYPE_17.getValue().equals(sourceBillType)) {
            return OrderTypeEnum.SOURCE_BILL_TYPE_17.getDescription();
        }

        if (OrderTypeEnum.SOURCE_BILL_TYPE_160.getValue().equals(sourceBillType)) {
            return OrderTypeEnum.SOURCE_BILL_TYPE_160.getDescription();
        }

        if (OrderTypeEnum.SOURCE_BILL_TYPE_150.getValue().equals(sourceBillType)) {
            return OrderTypeEnum.SOURCE_BILL_TYPE_150.getDescription();
        }

        return sourceBillType + "";
    }


    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}
