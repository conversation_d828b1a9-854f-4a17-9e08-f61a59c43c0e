package com.burgeon.r3.sg.store.services.freeze;


import com.burgeon.r3.sg.store.api.freeze.SgBStoUnfreezeSaveR3Cmd;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date: 2021-04-22 11:13
 * @Description:
 */

@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgBStoUnfreezeSaveR3CmdImpl extends CommandAdapter implements SgBStoUnfreezeSaveR3Cmd {

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        SgBStoUnfreezeR3SaveService bean = ApplicationContextHandle.getBean(SgBStoUnfreezeR3SaveService.class);
        return bean.save(session);
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}

