package com.burgeon.r3.sg.store.services.freeze;


import com.burgeon.r3.sg.store.api.freeze.SgBStoUnfreezeCmd;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoUnfreezeSubmitRequest;
import com.burgeon.r3.sg.store.model.result.freeze.SgBStoUnfreezeBillSubmitResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

/**
 * @Auther: chenhao
 * @Date: 2021-04-22 11:10
 * @Description:
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgBStoUnfreezeCmdImpl implements SgBStoUnfreezeCmd {

    @Override
    public ValueHolderV14<SgBStoUnfreezeBillSubmitResult> submitSgStoUnfreeze(SgBStoUnfreezeSubmitRequest request) {
        SgBStoUnfreezeSubmitService bean = ApplicationContextHandle.getBean(SgBStoUnfreezeSubmitService.class);
        return bean.submitUnfreeze(request);
    }
}
