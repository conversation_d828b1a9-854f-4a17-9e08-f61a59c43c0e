package com.burgeon.r3.sg.basic.common;

/**
 * @Description:
 * @Author: chenb
 * @Date: 2019/3/16 22:28
 */
public class SgConstantsIF {

    //组别
    public static final String GROUP = "sg";
    //版本
    public static final String VERSION = "1.0";

    public static final String ST_GROUP = "st";

    public static final String DEFAULT_PRODUCE_DATE = "00000000";
    public static final String MAX_PRODUCE_DATE = "99999999";
    public final static String INIT_PRODUCEMONTH = "000000";

    /**
     * 库存类型
     * STORAGE：逻辑库存
     * SA：独享库存
     * SHARE：共享库存
     * CHANNEL：渠道库存
     */
    public static final String STORAGE_TYPE_STORAGE = "STORAGE";
    public static final String STORAGE_TYPE_STORAGE_BATCH = "STORAGE_BATCH";
    public static final String STORAGE_TYPE_SA = "SA";
    public static final String STORAGE_TYPE_SHARE = "SHARE";
    public static final String STORAGE_TYPE_CHANNEL = "CHANNEL";
    public static final String STORAGE_TYPE_CHANNEL_FIXED = "CHANNEL_FIXED";

    // 表示共享池
    public static final String STORAGE_TYPE_SP = "SP";

    //业务节点
    public static final long SERVICE_NODE_PUR_SUBMIT = 1;//采购单审核
    public static final long SERVICE_NODE_PUR_UNSUBMIT = 2;//采购单取消审核
    public static final long SERVICE_NODE_PUR_IN_SUBMIT = 3;//采购单入库审核
    public static final long SERVICE_NODE_PUR_IN = 4;//采购入库
    public static final long SERVICE_NODE_REF_PUR_SUBMIT = 5;//采购退货单审核
    public static final long SERVICE_NODE_REF_PUR_UNSUBMIT = 6;//采购退货单取消审核
    public static final long SERVICE_NODE_REF_PUR_OUT_SUBMIT = 7;//采购退货单出库审核
    public static final long SERVICE_NODE_REF_PUR_OUT = 8;//采购退货出库
    public static final long SERVICE_NODE_SALE_SUBMIT = 9;//销售单审核
    public static final long SERVICE_NODE_SALE_UNSUBMIT = 10;//销售单取消审核
    public static final long SERVICE_NODE_SALE_IN_SUBMIT = 11;//销售单入库审核
    public static final long SERVICE_NODE_SALE_OUT_SUBMIT = 12;//销售单出库审核
    public static final long SERVICE_NODE_SALE_IN = 13;//销售入库
    public static final long SERVICE_NODE_SALE_OUT = 14;//销售出库
    public static final long SERVICE_NODE_REF_SALE_SUBMIT = 15;//销售退货单审核
    public static final long SERVICE_NODE_REF_SALE_UNSUBMIT = 16;//销售退货单取消审核
    public static final long SERVICE_NODE_REF_SALE_IN_SUBMIT = 17;//销售退货单入库审核
    public static final long SERVICE_NODE_REF_SALE_OUT_SUBMIT = 18;//销售退货单出库审核
    public static final long SERVICE_NODE_REF_SALE_IN = 19;//销售退货入库
    public static final long SERVICE_NODE_REF_SALE_OUT = 20;//销售退货出库
    public static final long SERVICE_NODE_TRANSFER_SUBMIT = 21;//调拨单审核
    public static final long SERVICE_NODE_TRANSFER_UNSUBMIT = 22;//调拨单取消审核
    public static final long SERVICE_NODE_TRANSFER_IN_SUBMIT = 23;//调拨单入库审核
    public static final long SERVICE_NODE_TRANSFER_OUT_SUBMIT = 24;//调拨单出库审核
    public static final long SERVICE_NODE_TRANSFER_IN = 25;//调拨入库
    public static final long SERVICE_NODE_TRANSFER_OUT = 26;//调拨出库
    public static final long SERVICE_NODE_RETAIL = 27;//零售
    public static final long SERVICE_NODE_INVENTORY = 28;//盘点损益
    public static final long SERVICE_NODE_OTHERS_INOUT = 29;//其他出入库
    public static final long SERVICE_NODE_TRANSFER_LOCK = 30;//调拨单锁单
    public static final long SERVICE_NODE_TRANSFER_UNLOCK = 31;//调拨单取消锁单
    public static final long SERVICE_NODE_SALE_LOCK = 32;//销售单锁单
    public static final long SERVICE_NODE_SALE_UNLOCK = 33;//销售单取消锁单
    public static final long SERVICE_NODE_TRANSFER_DIFF_DEAL = 34;//调拨单差异处理
    public static final long SERVICE_NODE_SALE_DIFF_DEAL = 35;//销售单单差异处理
    public static final long SERVICE_NODE_SALE_REF_DIFF_DEAL = 36;//销售退货单差异处理

    public static final long SERVICE_NODE_ADJUST_SUBMIT = 37;//逻辑调整单审核
    public static final long SERVICE_NODE_TRANSFER_CANCEL_OUT = 38;//调拨单取消出库

    public static final long SERVICE_NODE_DIRECT_SUBMIT = 39;//直发单审核
    public static final long SERVICE_NODE_DIRECT_IN = 40;//直发单入库
    public static final long SERVICE_NODE_RETAIL_POS_SUBMIT = 41;//零售单审核

    public static final long SERVICE_NODE_IN_UNSUBMIT = 50; //入库单反审核
    public static final long SERVICE_NODE_OUT_UNSUBMIT = 51; //出库单反审核

    public static final long SERVICE_NODE_IN_SUBMIT = 52; //入库单审核
    public static final long SERVICE_NODE_OUT_SUBMIT = 53; //出库单审核

    //区分退货入库与无名件入库
    public static final long SERVICE_NODE_RETAIL_REF_IN = 56; //退货入库(零售退)
    public static final long SERVICE_NODE_RETAIL_REF_NO_SOURCE_IN = 57; //无头件入库(零售退)

    // 业务节点-仓间调拨出库
    public static final long SERVICE_NODE_WMS_CJ_TRANSFER_OUT = 58;
    // 业务节点-仓间调拨入库
    public static final long SERVICE_NODE_WMS_CJ_TRANSFER_IN = 59;
    // 业务节点-组货单审核
    public static final long SERVICE_NODE_GROUP_MANIFEST_SUBMIT = 60;
    // 业务节点-仓内调拨审核
    public static final long SERVICE_NODE_WMS_CN_TRANSFER_SUBMIT = 61;
    // 业务节点-调拨差异处理审核
    public static final long SERVICE_NODE_TRANSFER_DIFF_SUBMIT = 62;
    // 业务节点-库存异常处理
    public static final long SERVICE_NODE_TRANSFER_ERROR_HANDLE = 63;

    // 业务节点-调整单:无头件入库
    public static final long SERVICE_NODE_ADJUST_PROP_NO_SOURCE_IN = 64;
    // 业务节点-调整单:冲无头件
    public static final long SERVICE_NODE_ADJUST_PROP_FLUSH_NO_SOURCE = 65;
    // 业务节点-调整单:错发调整
    public static final long SERVICE_NODE_ADJUST_PROP_MISTAKE_ADJUSTMENT = 66;
    // 业务节点-调整单:无名件返回
    public static final long SERVICE_NODE_ADJUST_PROP_FLUSH_UNNAMED_RETURN = 67;
    // 业务节点-调整单:销退错录
    public static final long SERVICE_NODE_ADJUST_PROP_SALES_RETURN_FAIL_RECORD = 68;
    // 业务节点-调整单:分销错录
    public static final long SERVICE_NODE_ADJUST_PROP_DISTRIB_SALES_FAIL_RECORD = 69;
    // 业务节点-调整单:唯品会退货
    public static final long SERVICE_NODE_ADJUST_PROP_VIP_RETURN = 70;
    // 业务节点-调整单:盘盈盘亏
    public static final long SERVICE_NODE_ADJUST_PROP_INVENTORY = 71;
    // 业务节点-调整单:调拨差异
    public static final long SERVICE_NODE_ADJUST_PROP_TRANSFER_DIFF = 72;
    // 业务节点-调整单:换吊牌
    public static final long SERVICE_NODE_ADJUST_PROP_CHANGE_TAG = 73;
    // 业务节点-调整单:期初调整
    public static final long SERVICE_NODE_ADJUST_PROP_BEGIN_ADJUSTMENT = 74;
    // 业务节点-调整单:库存异动
    public static final long SERVICE_NODE_ADJUST_PROP_ALTER_ADJUSTMENT = 75;
    // 业务节点-调整单:WMS（全量）库存
    public static final long SERVICE_NODE_ADJUST_WMS_STOCK_ADJUSTMENT = 76;

    // 业务节点-配货单新增
    public static final long SERVICE_NODE_PH_OCCUPY = 75;
    //    业务节点-冻结单审核
    public static final long SERVICE_NODE_FREEZE_SUBMIT = 76;
    //    业务节点-解冻单审核
    public static final long SERVICE_NODE_UNFREEZE_SUBMIT = 77;

    // 业务节点-调整单：ERP调整
    public static final long SERVICE_NODE_ADJUST_PROP_ERP = 78;

    //逻辑调拨单审核
    public static final long SERVICE_NODE_STO_TRANSFER_SUBMIT = 80;
    //逻辑调拨单出库
    public static final long SERVICE_NODE_STO_TRANSFER_OUT_SUBMIT = 81;
    // 线上退货 76
    //逻辑调拨单入库
    public static final long SERVICE_NODE_STO_TRANSFER_IN_SUBMIT = 82;
    //逻辑出库单审核
    public static final long SERVICE_NODE_STO_OUT_RESULT_SUBMIT = 83;
    //逻辑入库单审核
    public static final long SERVICE_NODE_STO_IN_RESULT_SUBMIT = 84;

    //逻辑占用单作废
    public static final long SERVICE_NODE_STO_OUT_VOID = 85;
    //逻辑占用单保存
    public static final long SERVICE_NODE_STO_OUT_SAVE = 86;
    //逻辑占用单清空
    public static final long SERVICE_NODE_STO_OUT_CLEAN = 87;

    //逻辑在途单作废
    public static final long SERVICE_NODE_STO_IN_VOID = 88;
    //逻辑在途单保存
    public static final long SERVICE_NODE_STO_IN_SAVE = 89;
    //逻辑在途单清空
    public static final long SERVICE_NODE_STO_IN_CLEAN = 90;

    //分货单审核
    public static final long SERVICE_NODE_SHARE_ALLOCATION_SUBMIT = 91;

    //逻辑占用单更新
    public static final long SERVICE_NODE_STO_OUT_RELEASE = 92;
    //分货退货单审核
    public static final long SERVICE_NODE_SHARE_ALLOCATION_RETURN_SUBMIT = 93;

    //共享调整单审核
    public static final long SERVICE_NODE_SHARE_ADJUST_SUBMIT = 94;

    //共享占用单保存
    public static final long SERVICE_NODE_SHARE_OUT_SAVE = 95;
    //共享占用单清空
    public static final long SERVICE_NODE_SHARE_OUT_CLEAN = 99;
    //共享占用单作废
    public static final long SERVICE_NODE_SHARE_OUT_VOID = 100;
    // 库存调整单
    public static final long SERVICE_NODE_STO_ADJUST_SUBMIT = 96;

    //按数量同步策略 库存同步
    public static final long SERVICE_NODE_CHANNEL_QTY_STOCK_SYNC = 97;
    //按数量同步策略 活动下架
    public static final long SERVICE_NODE_CHANNEL_QTY_SOLD_OUT = 98;
    // 零售发货单寻源
    public static final long SERVICE_NODE_BILL_TYPE_RETAIL_SOURCE = 101;
    //零售发货单出库
    public static final long SERVICE_NODE_BILL_TYPE_RETAIL_OUT = 102;
    //零售发货单删除赠品
    public static final long SERVICE_NODE_BILL_TYPE_RETAIL_DELETE = 103;
    //零售发货单添加赠品
    public static final long SERVICE_NODE_BILL_TYPE_RETAIL_ADD = 104;
    //零售发货单替换商品
    public static final long SERVICE_NODE_BILL_TYPE_RETAIL_REPLACE = 105;
    //零售发货单标记退款完成
    public static final long SERVICE_NODE_BILL_TYPE_RETAIL_REFUND = 106;
    // 业务节点-调整单:冲销调整
    public static final long SERVICE_NODE_ADJUST_PROP_WRITE_OFF = 130;
    //B2B逻辑占用单保存
    public static final long SERVICE_NODE_B2B_STO_OUT_SAVE = 107;
    //蓝鼎逻辑占用单保存
    public static final long SERVICE_NODE_REP_STO_OUT_SAVE = 108;
    //欧睿逻辑占用单保存
    public static final long SERVICE_NODE_OLBP_STO_OUT_SAVE = 109;
    //欧睿，B2B逻辑占用单缺货释放
    public static final long SERVICE_NODE_OLBP_STO_OUT_STOCK = 110;
    //逻辑占用单强制完成
    public static final long SERVICE_NODE_STO_OUT_FORCE_COMPLETE = 120;
    //逻辑出库单 包材调整
    public static final long SERVICE_NODE_PACK_STO_OUT_STOCK = 155;
    //逻辑在途单强制完成
    public static final long SERVICE_NODE_STO_IN_FORCE_COMPLETE = 200;
    //线边仓调整
    public static final long SERVICE_NODE_LINE_EDGE_INOUT = 152;
    //生产日期调整
    public static final long SERVICE_NODE_VALIDITY_ADJUST = 153;
    //O库存调整
    public static final long SERVICE_NODE_ZERO_STORAGE_ADJUST = 154;
    // 冻结出库单提交
    public static final long SERVICE_NODE_FREEZE_OUT_RESULT_SUBMIT = 155;
    // 冻结占用单新增
    public static final long SERVICE_NODE_FREEZE_OUT_INSERT = 156;
    // 冻结占用单保存
    public static final long SERVICE_NODE_FREEZE_OUT_UPDATE = 157;
    // 冻结占用单作废
    public static final long SERVICE_NODE_FREEZE_OUT_VOID = 158;
    // 冻结占用单清空
    public static final long SERVICE_NODE_FREEZE_OUT_CLEAN = 159;
    // 冻结占用单释放
    public static final long SERVICE_NODE_FREEZE_OUT_RELEASE = 160;
    // 包材领用
    public static final long SERVICE_NODE_PACKAGE_REQUISITION = 161;

    //来源单据类型
    public static final int BILL_TYPE_BY_HAND = 0;  //手动新增
    public static final int BILL_TYPE_RETAIL = 1;//零售发货
    public static final int BILL_TYPE_RETAIL_REF = 2;//零售退货单
    public static final int BILL_TYPE_SALE = 3;//销售单
    public static final int BILL_TYPE_SALE_REF = 4;//销售退货单
    public static final int BILL_TYPE_PUR = 5;//采购单
    public static final int BILL_TYPE__ORDER_PUR = 222; //采购订单
    public static final int BILL_TYPE_PUR_REF = 6;//采购退货单
    public static final int BILL_TYPE_TRANSFER = 7;//调拨单
    public static final int BILL_TYPE_VIPSHOP = 8;//唯品会单
    public static final int BILL_TYPE_ADJUST = 9;//库存调整单
    public static final int BILL_TYPE_VIPSHOP_TIME = 10;//唯品会时效订单
    public static final int BILL_TYPE_RETAIL_POS = 11;//零售单
    public static final int BILL_TYPE_DIRECT = 12;//直发单
    public static final int BILL_TYPE_OUT = 13;//出库单
    public static final int BILL_TYPE_IN = 14;//入库单
    public static final int BILL_TYPE_O2O_SUMMARY_INVENTORY_ADJUST = 15; // O2O汇总仓库存调整
    public static final int BILL_TYPE_DIFF = 16;//差异单
    public static final int BILL_STO_TRANSFER = 17;//逻辑调拨单
    public static final int BILL_SHARE_ALLOCATION = 18;//分货单
    public static final int BILL_SHARE_ALLOCATION_RETURN = 240;//分货退货单
    public static final int BILL_STO_OUT = 19;//逻辑占用单
    public static final int BILL_SHARE_OUT = 20;//共享占用单
    public static final int BILL_TYPE_PHY_PROFIT = 21;//盈亏单
    public static final int BILL_TYPE_DIRECT_ORDER = 260;//直发预占单
    /*** 库存盘点 */
    public static final int BILL_TYPE_INVENTORY_REPORT = 23;
    public static final int BILL_TYPE_JITX = 25;//JITX订单
    public static final int BILL_TYPE_FREEZE = 26;//逻辑冻结单
    public static final int BILL_TYPE_UNFREEZE = 27;//逻辑解冻单
    public static final int BILL_TYPE_TRANSFER_DIFF = 28;//调拨差异回传
    public static final int BILL_TYPE_SHARE_AJUST = 29;//共享调整单
    public static final int BILL_TYPE_FOR_WAREHOUSE = 30;//寻仓单
    public static final int BILL_TYPE_SHARE_SA_TRANSFER = 31;//配销仓调拨单(已作废,合并为配销跨聚合仓调拨单)
    public static final int BILL_TYPE_SHARE_SA_BATCH_TRANSFER = 40;//配销仓调拨单批量导入
    public static final int BILL_TYPE_WMS_ALTER = 32;//wms调整单

    public static final int BILL_B2B_STO_TRANSFER = 32;//B2B调拨单
    public static final int BILL_REP_STO_TRANSFER = 33;//蓝鼎调拨单
    public static final int BILL_OLBP_STO_TRANSFER = 34;//欧睿调拨单
    public static final int BILL_B2B_TYPE_SALE = 35;//B2B销售单
    public static final int BILL_REP_TYPE_SALE = 36;//蓝鼎销售单
    public static final int BILL_OLBP_TYPE_SALE = 37;//欧睿销售单

    public static final int BILL_STO_BATCH_TRANSFER = 38;//逻辑调拨单
    public static final int BILL_SG_B_SHARE_TRANSFER = 39;//聚合仓调拨单

    public static final int BILL_SG_B_SHARE_VIP_REPLENISH = 40;//唯品会补货信息表
    public static final int BILL_SG_B_SHARE_SA_CROSS_TRANSFER = 41;//配销跨聚合仓调拨单(已合并为配销仓调拨单)
    public static final int BILL_SG_B_SHARE_SA_ALLOCATION_TRANSFER = 42;//聚合仓->配销仓调拨单
    public static final int BILL_SG_B_SHARE_FROM_SA_TRANSFER = 43;//配销仓到聚合仓调拨单
    public static final int BILL_SG_B_STORE_UPDATE_SHARE_STORE = 44;//逻辑仓修改聚合仓
    public static final int BILL_SG_B_STO_MULTI_CHANNEL_TRANSFER = 45;//多渠道调拨单


    public static final int BILL_TYPE_LY_RETURN = 100;//领用单（归还）
    public static final int BILL_TYPE_LY_NO_RETURN = 101;//领用单（不归还）
    public static final int BILL_TYPE_SEND_BACK = 110;//归还单
    public static final int BILL_TYPE_INVENTORY_LOSS = 120;//盘亏单
    public static final int BILL_TYPE_DRP_FREEZE = 130;//drp冻结单
    public static final int BILL_TYPE_DRP_UNFREEZE = 131;//drp解冻单
    public static final int BILL_SHARE_DISTRIBUTION = 140;//配货单
    public static final int BILL_TYPE_SA_SELECTION_RESULT = 144;//选款结果表
    public static final int BILL_OURUI_SHARE_RESULT = 145;//欧睿共享结果单
    /**
     * 经销商库存同步 库存调整单
     */
    public static final int BILL_TYPE_CUSTOMER_STORE_STORAGE_SYNC = 146;//经销商库存同步调整
    //库存调整单 调整类型
    public static final int CUSTOMER_STORAGE_SYNC_ADJUST_PROP = 79;//经销商调整

    public static final int BILL_OTHER_DELIVERY = 150;//其他入库单
    public static final int BILL_TYPE_OTHER_OUT_RESULT = 160;//其他出库单
    public static final int BILL_TYPE_RESERVED = 170;//预留单(领用)
    public static final int BILL_TYPE_PRODUCTION_FLOW = 180;//生产流程订单


    // 库存调整单新来源单据类型 20220621
    public static final int BILL_TYPE_CHARGE_AGAINST = 210; // 预留单（冲销）
    public static final int BILL_TYPE_SCRAP = 211; // 报废单
    public static final int BILL_TYPE_LINE_EDGE_INOUT = 212; // 线边仓出入库
    public static final int BILL_TYPE_VALIDITY_ADJUST = 213; // 效期调整
    public static final int BILL_TYPE_ZERO_STORAGE_ADJUST = 214; // O库存调整
    public static final int BILL_TYPE_PACKING_OUT = 215; // 包材出库
    public static final int BILL_TYPE_FACTORY_SALE = 216; // 工厂销售单
    public static final int BILL_TYPE_FACTORY_UN_SALE = 217; // 工厂销售退
    public static final int BILL_TYPE_DEFECTIVE_OR_QUALITY = 218; // 转正转残

    public static final int BILL_TYPE_INTERCOMPANY_PURCHASE_ORDER = 219; //公司间采购单
    public static final int BILL_TYPE_PROP_NO_SOURCE_IN = 220; //无头件登记单
    public static final int BILL_TYPE_CROSS_WAREHOUSE_TRANSFER_ORDER = 221; //跨仓移库单
    public static final int BILL_TYPE_PACKAGE_REQUISITION = 223; //领用单（包材领用）
    public static final int BILL_TYPE_CROSS_WAREHOUSE_INSULATION_ORDER = 224; //保温期转储单
    public static final int BILL_TYPE_STATUS_ADJUST = 230; //状态调整单

    public static final int BILL_TYPE_FREEZE_OUT_RESULT = 241; //冻结出库单
    public static final int BILL_TYPE_FREEZE_OUT = 242; //冻结占用单


    public static final int OA_PROCESS_SOURCE_TYPE = 250; // OA 审批流 来源单据类型

    // todo start ref from jodan
    public static final int BILL_TYPE_CARGO = 13;//经销商调货单
    public static final long SERVICE_NODE_CARGO_UNSUBMIT = 42;//经销商调货单取消审核
    public static final long SERVICE_NODE_CARGO_SUBMIT = 43;//经销商调货单审核
    public static final long SERVICE_NODE_CARGO_IN_SUBMIT = 44;//经销商调货单入库审核
    public static final long SERVICE_NODE_CARGO_OUT_SUBMIT = 45;//经销商调货单出库审核

    // 实体仓WH_TYPE
    public static final String WH_TYPE_STO = "02";
    public static final String WH_TYPE_PART = "04";

    //占用错误处理类别
    //报警（允许负库存）
    public static final int PREOUT_OPT_TYPE_WARNING = 1;
    //报错
    public static final int PREOUT_OPT_TYPE_ERROR = 2;
    //报缺货 20230828-问了伯俊李实俊，反馈说这个没有处理，所以不应该用这个类型
    @Deprecated
    public static final int PREOUT_OPT_TYPE_OUT_STOCK = 3;

    //占用更新结果
    //正常（成功）
    public static final int PREOUT_RESULT_SUCCESS = 0;
    //异常（报警）
    public static final int PREOUT_RESULT_WARNING = 1;
    //异常（报错）
    public static final int PREOUT_RESULT_ERROR = 2;
    //异常（缺货）
    public static final int PREOUT_RESULT_OUT_STOCK = 3;

    /**
     * 单据明细修改方式(1.全量 2.增量)
     */
    public static final int ITEM_UPDATE_TYPE_ALL = 1;
    public static final int ITEM_UPDATE_TYPE_INC = 2;

    public static final int OPERATE_TYPE_OCCUPY = 1;//占用
    public static final int OPERATE_TYPE_RELEASE = 2;//释放

    /**
     * 矩阵查询类型（1.逻辑仓 2.实体仓）
     */
    public static final int MATRIX_QUERY_TYPE_STORAGE = 1;
    public static final int MATRIX_QUERY_TYPE_PHY_STORAGE = 2;

    public static final String COMBINED_POOL = "COMBINED:POOL";
    public static final String COMBINED_SKUGROUP = "COMBINED:SKUGROUP";
    public static final String COMBINED_SKU = "COMBINED:SKU";

    /**
     * 商品标识类型（ZP 正品 CC 次品 ）
     */
    public static final String TRADE_MARK_CC = "CC";
    public static final String TRADE_MARK_ZP = "ZP";

    /**
     * 退货仓 TH , 样衣仓 XS , 无吊牌仓 WP，赠品仓 JS
     */
    public static final String TRADE_MARK_TH = "TH";
    public static final String TRADE_MARK_WP = "WP";
    public static final String TRADE_MARK_XS = "XS";
    public static final String TRADE_MARK_JS = "JS";

    /**
     * 店仓属性（01 正品仓 02 次品仓 06 样衣仓,11退货仓,14无吊牌）
     */
    public static final String STORE_TYPE_ZP = "01";
    public static final String STORE_TYPE_CC = "02";
    public static final String STORE_TYPE_TH = "11";
    public static final String STORE_TYPE_YY = "06";
    public static final String STORE_TYPE_NONE_TAG = "14";
    public static final String STORE_TYPE_GIFT = "15";

    /**
     * 电商性质：森马电商
     */
    public static final long STORE_NATURE_SMDS = 14L;

    /**
     * 是否是正品仓
     */
    public static final String IS_MAIN_ZP = "1";
    public static final int IS_MAIN_Y = 1;
    public static final int IS_MAIN_N = 0;

    /**
     * 是否同城购
     */
    public static final String IS_TMLL_CITY_Y = "Y";
    public static final String IS_TMLL_CITY_N = "N";


    /**
     * 通知单回写订单tag
     */
    public static final String MSG_TAG_OUTNOTICES_TO_OMS = "sg_to_oms_wms_out_creat_receipt";

    /**
     * 通知单回写VIPtag
     */
    public static final String MSG_TAG_OUTNOTICES_TO_VIP = "sg_to_vip_out_creat_receipt";

    /**
     * 入库通知单回写订单tag
     */
    public static final String MSG_TAG_INNOTICES_TO_OMS = "sg_to_oms_wms_in_creat_receipt";

    /**
     * WMS 入库回传类型
     * CGRU 采购入库
     * DBRK 调拨入库
     */
    public static final String WMS_ENTRY_ORDER_TYPE_CGRK = "CGRK";

    /**
     * 逻辑仓库存数据修复 修复类型
     */
    public static final int SG_STORAGE_DATA_REPAIR_TYPE_REDIS_FTP = 0;

    public static final String FIELD_NAME_QTY_AVAILABLE = "qtyAvailable";

    public static final String FIELD_NAME_QTY_STORAGE = "qtyStorage";


    /**
     * 单据操作
     */
    public final static String CLEAN = "clean";

    public final static String VOID = "void";

    public final static String SUBMIT = "submit";

    public final static String ADD = "add";

    public final static String SAVE = "save";

    /**
     * Redis库存同步类型（"0":逻辑仓库存 "1":聚合仓库存 "2":配销仓库存 "3":渠道锁定库存 "4":逻辑仓批次库存）
     */
    public final static String SG_STORAGE_REDIS_SYNC_TYPE_STORAGE = "0";
    public final static String SG_STORAGE_REDIS_SYNC_TYPE_SHARE_STORAGE = "1";
    public final static String SG_STORAGE_REDIS_SYNC_TYPE_SA_STORAGE = "2";
    public final static String SG_STORAGE_REDIS_SYNC_TYPE_CHANNEL_FIXED_STORAGE = "3";
    public final static String SG_STORAGE_REDIS_SYNC_TYPE_STORAGE_BATCH = "4";

    /**
     * 库存调整单调整类型 正常调整/异常调整
     */
    public final static int SG_STO_ADJUST_BILL_TYPE_NORMAL = 1;
    public final static int SG_STO_ADJUST_BILL_TYPE_ABNORMAL = 2;


    /**
     * 全渠道发货单 接单状态--未接单/已接单
     */
    public static final int SG_STO_OUT_RCEIVE_STATUS_NOT_RECEIVE = 0;
    public static final int SG_STO_OUT_RCEIVE_STATUS_RECEIVED = 1;
    /**
     * 出库通知单 单据状态--待出库/部分出库/全部出库/已作废
     */
    public static final int SG_STO_OUT_BILL_STATUS_WAIT_OUT = 1;
    public static final int SG_STO_OUT_BILL_STATUS_PSRT_OUT = 2;
    public static final int SG_STO_OUT_BILL_STATUS_ALL_OUT = 3;
    public static final int SG_STO_OUT_BILL_STATUS_CANCELED = 4;

    /**
     * 出库通知单 配送方式--物流派送/门店自提
     */
    public static final String SG_STO_OUT_DELIVERY_WAY_DELIVERY = "1";
    public static final String SG_STO_OUT_DELIVERY_WAY_SELF = "2";

    // 是否允许零售
    public static final String CP_C_STORE_IS_RETAIL_YES = "1";//允许
    public static final String CP_C_STORE_IS_RETAIL_NO = "0";//不允许

    //店铺条码库存同步策略单据状态
    public static final Long SKUSTOCK_STATUS_01 = 1L;//未审核
    public static final Long SKUSTOCK_STATUS_02 = 2L;//已审核
    public static final Long SKUSTOCK_STATUS_03 = 3L;//已作废
    public static final Long SKUSTOCK_STATUS_04 = 4L;//已结案

    //店铺条码 单据状态
    public static final Integer CON_BILL_STATUS_01 = 1;//未审核
    public static final Integer CON_BILL_STATUS_02 = 2;//已审核
    public static final Integer CON_BILL_STATUS_03 = 3;//已作废
    public static final Integer CON_BILL_STATUS_04 = 4;//已结案


    /**
     * 接口标识
     */
    public static final String Interface_FLAG_DRP_OUT_NOTICES = "DRP_DRP_OUT_NOTICES";//drp接口-逻辑占用单创建并自动出库

    public static final String Interface_FLAG_OMS_IN_NOTICES = "OMS_STO_IN_NOTICES_RESULT";//oms接口-逻辑在途单创建并自动入库

    /**
     * drp接口-在途单创建并自动入库 控制是否开启负库存
     */
    public static final String INTERFACE_FLAG_DRP_ISNEGATIVEPREIN = "INTERFACE_FLAG_DRP_ISNEGATIVEPREIN";

    /**
     * 聚合仓分货类型  1 = 配销仓 2 = 共享池
     */
    public static final Integer AUTO_TYPR_SA = 1;
    public static final Integer AUTO_TYPR_SS = 2;

    /**
     * 库存同步状态
     */
    public static final int SYNC_SUCCESS = 2;
    public static final int SYNC_FAIL = 3;

    /**
     * 库存同步类型 1：普通全量  2：普通增量  3：同城购全量  4：同城购增量 5：手动全量 6 手动增量 7 手动同城购全量 8 手动同城购增量
     */
    public static final int SYNC_TYPE_ALL = 1;
    public static final int SYNC_TYPE_ADD = 2;
    public static final int SYNC_TYPE_ALL_CITY = 3;
    public static final int SYNC_TYPE_ADD_CITY = 4;
    public static final int SYNC_TYPE_ALL_HAND = 5;
    public static final int SYNC_TYPE_ADD_HAND = 6;
    public static final int SYNC_TYPE_ALL_CITY_HAND = 7;
    public static final int SYNC_TYPE_ADD_CITY_HAND = 8;

    /**
     * 缓存池同步状态 1：增量 2全量 3 手动增量 4 手动全量
     */
    public static final String SYNC_TYPE_POOL_ADD_CITY = "01";
    public static final String SYNC_TYPE_POOL_ALL_CITY = "02";
    public static final String SYNC_TYPE_POOL_ADD_CITY_HAND = "03";
    public static final String SYNC_TYPE_POOL_ALL_CITY_HAND = "04";

    /**
     * 出库通知单传第三方类型
     */
    public static final int THIRD_PARTY_TYPE_WING = 1;
    public static final int THIRD_PARTY_TYPE_ERP = 2;

    // oms占用库存取消方式-通过明细信息取消
    public static final String OMS_STORAGE_OCCUPY_CANCEL_TYPE_ITEM = "01";

    // oms占用库存取消方式-通过主表信息取消
    public static final String OMS_STORAGE_OCCUPY_CANCEL_TYPE_MAIN = "02";


    /**
     * 虚拟批量逻辑调拨单 单据状态
     */
    public static final Integer SG_B_STO_BATCH_TRANSFER_STATUS_01 = 1;//未审核
    public static final Integer SG_B_STO_BATCH_TRANSFER_STATUS_02 = 2;//部分审核
    public static final Integer SG_B_STO_BATCH_TRANSFER_STATUS_03 = 3;//全部审核
    public static final Integer SG_B_STO_BATCH_TRANSFER_STATUS_04 = 4;//已作废

    /**
     * 虚拟批量逻辑调拨单 明细 状态
     * 0未处理
     * 1成功
     * -1失败
     */
    public static final Integer SG_B_STO_BATCH_TRANSFER_ITEM_STATUS_NO = 0;//未处理
    public static final Integer SG_B_STO_BATCH_TRANSFER_ITEM_STATUS_SUCCESS = 1;//成功
    public static final Integer SG_B_STO_BATCH_TRANSFER_ITEM_STATUS_FAIL = -1;//失败

    /**
     * 是否使用虚拟收货地址
     */
    public static final String IS_TEMPORARY_ADDRESS_Y = "Y";//是
    public static final String IS_TEMPORARY_ADDRESS_N = "N";//否

    //JITX电子面单
    public static final Long CP_C_PLATFORM_ID = 19L;


    /**
     * 需求类型
     * 1 新开店
     * 2 团购
     * 3 促销
     * 4 销补
     * 5 手工补单
     * 6 新品上市
     * 7 V2全国补货
     * 0 加急订单
     * 11:传统经销出货
     * 12:电商订单,
     */
    public static final Integer DEMAND_TYPE_0 = 0;
    public static final Integer DEMAND_TYPE_1 = 1;
    public static final Integer DEMAND_TYPE_2 = 2;
    public static final Integer DEMAND_TYPE_3 = 3;
    public static final Integer DEMAND_TYPE_4 = 4;
    public static final Integer DEMAND_TYPE_5 = 5;
    public static final Integer DEMAND_TYPE_6 = 6;
    public static final Integer DEMAND_TYPE_7 = 7;
    public static final Integer DEMAND_TYPE_10 = 10;
    public static final Integer DEMAND_TYPE_11 = 11;
    public static final Integer DEMAND_TYPE_12 = 12;


    /**
     * TMS业务类型
     * tms_business_type
     * <p>
     * 值：
     * IB-C:Inbound-国内,IB-A:Inbound-国外,
     * OB-F:Outbound-供应商,OB-S:Outbound-自营店,
     * OB-C:Outbound-经销商,SR-S:Store Return-直营店,
     * SR-C:Store Return-销售仓,ST:Store Transfer,
     * DCT:DC Transfer,FT:经销商调配,
     * EC:电商,OTHER:Other,
     * XUNI:虚拟,ST-C:Store transfer-经销商,
     * IB-CRQ:Inbound-CRQ,
     */
    public static final String TMS_BUSINESS_TYPE_IB_C = "IB-C";
    public static final String TMS_BUSINESS_TYPE_IB_A = "IB-A";
    public static final String TMS_BUSINESS_TYPE_OB_F = "OB-F";
    public static final String TMS_BUSINESS_TYPE_OB_S = "OB-S";
    public static final String TMS_BUSINESS_TYPE_OB_C = "OB-C";
    public static final String TMS_BUSINESS_TYPE_SR_S = "SR-S";
    public static final String TMS_BUSINESS_TYPE_SR_C = "SR-C";
    public static final String TMS_BUSINESS_TYPE_ST = "ST";
    public static final String TMS_BUSINESS_TYPE_DCT = "DCT";
    public static final String TMS_BUSINESS_TYPE_EC = "EC";
    public static final String TMS_BUSINESS_TYPE_XUNI = "XUNI";
    public static final String TMS_BUSINESS_TYPE_ST_C = "ST-C";
    public static final String TMS_BUSINESS_TYPE_IB_CRQ = "IB-CRQ";

    public static final String TMS_PLATFORM_ECODE_TB = "TB";
    public static final String TMS_PLATFORM_ECODE_DY = "DY";
    public static final String TMS_PLATFORM_ECODE_JD = "JD";
    public static final String TMS_PLATFORM_ECODE_JD_H = "京东";

    public static final String TMS_ORDER_TYPE_NORMAL = "1";
    public static final String TMS_ORDER_TYPE_BAOZUN = "2";
    public static final String TMS_ORDER_TYPE_TUIHUO = "3";

    /**
     * 出库通知单MQ响应状态
     */
    // TMS 状态：tms 回执成功、tms传失败、 tms回执失败
    public static final int ORDER_RESULT_CODE_TMS_SUCCESS = 10;
    public static final int ORDER_RESULT_CODE_TMS_FAIL = 11;
    public static final int ORDER_RESULT_CODE_TMS_CALLBACK_FAIL = 12;

    // WING 状态：wing 回执成功、wing 传失败、wing 回执失败
    public static final int ORDER_RESULT_CODE_WING_SUCCESS = 15;
    public static final int ORDER_RESULT_CODE_WING_FAIL = 16;
    public static final int ORDER_RESULT_CODE_WING_CALLBACK_FAIL = 18;

    // WMS 状态：成功、失败
    public static final int ORDER_RESULT_CODE_WMS_SUCCESS = 19;
    public static final int ORDER_RESULT_CODE_WMS_FAIL = 20;

    // 该状态表示需要发一个特殊状态的MQ消息：无需传第三方
    public static final int ORDER_RESULT_CODE_NO_THIRD = 17;

    /**
     * 聚合仓调拨单 单据状态
     */
    public static final Integer SG_B_SHARE_TRANSFER_STATUS_00 = 0;//部分审核
    public static final Integer SG_B_SHARE_TRANSFER_STATUS_01 = 1;//未审核
    public static final Integer SG_B_SHARE_TRANSFER_STATUS_02 = 2;//全部审核
    public static final Integer SG_B_SHARE_TRANSFER_STATUS_03 = 3;//全部审核
    public static final Integer SG_B_SHARE_TRANSFER_STATUS_04 = 4;//已作废

    public static final Integer SG_B_SHARE_SA_BATCH_TRANSFER_STATUS_01 = 1;//未审核
    public static final Integer SG_B_SHARE_SA_BATCH_TRANSFER_STATUS_02 = 2;//部分审核
    public static final Integer SG_B_SHARE_SA_BATCH_TRANSFER_STATUS_03 = 3;//全部审核
    public static final Integer SG_B_SHARE_SA_BATCH_TRANSFER_STATUS_04 = 4;//已作废

    /**
     * 聚合仓调拨单 明细 状态
     * 0未处理
     * 1成功
     * -1失败
     */
    public static final Integer SG_B_SHARE_TRANSFER_STATUS_NO = 0;//未处理
    public static final Integer SG_B_SHARE_TRANSFER_STATUS_SUCCESS = 1;//成功
    public static final Integer SG_B_SHARE_TRANSFER_STATUS_FAIL = -1;//失败
    public static final Integer SG_B_SHARE_TRANSFER_STATUS_UNSUCCESS = 2;//部分调拨成功

    /**
     * 是否店发
     */
    public static final int IS_TO_STORE_Y = 1;
    public static final int IS_TO_STORE_N = 0;

    /**
     * 调拨维度
     */
    public static final int TRANSFER_DIMENSION_1 = 1;//条码维度
    public static final int TRANSFER_DIMENSION_2 = 2;//商品维度（款维度）

    /**
     * 经销商id
     */
    public static final Integer C_CUSTOMERUP_ID_GZ00 = 1;

    /**
     * 运输类型
     * 3.物流
     */
    public final static Long CP_C_TRANWAY_ASSIGN_ID_3 = 3L;

    /**
     * 全量库存同步是否关闭库存同步
     */
    public static final Integer FULL_STOCK_SYNC_IS_CLOSE_STOCK_SYNC_YES = 1;
    public static final Integer FULL_STOCK_SYNC_IS_CLOSE_STOCK_SYNC_NO = 0;

    /**
     * 多渠道调拨单单据状态
     * 0 未审核
     * 1 已审核
     */
    public static final Integer SG_B_STO_MULTI_CHANNEL_TRANSFER_UNSUBMIT = 0;
    public static final Integer SG_B_STO_MULTI_CHANNEL_TRANSFER_SUBMIT = 1;

    /**
     * 商品类型
     * 1.正品;2.赠品/小样;3.不限,
     */
    public static final String PRO_TYPE_GOODS = "1";
    public static final String PRO_TYPE_GIFT = "2";
    public static final String PRO_TYPE_UNLIMITED = "3";

    /**
     * 库存类型;0:正品,1:残次,2:返修,3:质检,4:丢失
     */
    public static final String STOCK_TYPE_GOODS = "ZP";
    public static final String STOCK_TYPE_IMPERFECT = "CC";
    public static final String STOCK_TYPE_CALLBACK = "FX";
    public static final String STOCK_TYPE_QUALITY = "ZJ";
    public static final String STOCK_TYPE_LOSE = "DS";
    public static final String STOCK_TYPE_DISABLED = "JY";

    // 解冻业务类型
    public static final int FREEZE_BILL_TYPE_QUALITY_TO_GOOD = 4;
    public static final int FREEZE_BILL_TYPE_CALLBACK_TO_GOOD = 5;
    public static final int FREEZE_BILL_TYPE_IMPERFECT_TO_GOOD = 6;
    public static final int FREEZE_BILL_TYPE_LOSE_TO_GOOD = 7;

    /**
     * mapKey 分隔线
     */
    public static final String MAP_KEY_DIVIDER = "_";


    /**
     * 收货入库-冲销 Z102
     * 采购退货-冲销 Z123
     * 部门领用-冲销 Z202
     * 生产投料-冲销 Z262
     * T库存转非限制 Z411
     * 报废-冲销    Z552
     * 调拨入库-冲销 ZD02
     * 生产入库-冲销 ZP02
     * 捐赠领用-冲销 ZZ12
     * 福利领用-冲销 ZZ14
     * 样品领用-冲销 ZZ16
     * 宣传领用-冲销 ZZ18
     * 赠品领用-冲销 ZZ20
     * 招待领用-冲销 ZZ22
     */
    public static final String WRITE_OFF_Z102 = "Z102";
    public static final String WRITE_OFF_Z123 = "Z123";
    public static final String WRITE_OFF_Z202 = "Z202";
    public static final String WRITE_OFF_Z262 = "Z262";
    public static final String WRITE_OFF_Z411 = "Z411";
    public static final String WRITE_OFF_Z552 = "Z552";
    public static final String WRITE_OFF_ZD02 = "ZD02";
    public static final String WRITE_OFF_ZP02 = "ZP02";
    public static final String WRITE_OFF_ZZ12 = "ZZ12";
    public static final String WRITE_OFF_ZZ14 = "ZZ14";
    public static final String WRITE_OFF_ZZ16 = "ZZ16";
    public static final String WRITE_OFF_ZZ18 = "ZZ18";
    public static final String WRITE_OFF_ZZ20 = "ZZ20";
    public static final String WRITE_OFF_ZZ22 = "ZZ22";
    public static final String WRITE_OFF_ZZ64 = "ZZ64";

    /**
     * WMS仓库类型
     * JD_WMS 京东云仓
     * QM_WMS 走奇门接口
     * {@link com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum}
     */
    @Deprecated
    public static final String JD_WMS = "JDWMS";
    @Deprecated
    public static final String QM_WMS = "QMWMS";

    /**
     * 负库存控制 1 不允许负库存  2 仅允许负可用  0 允许负库存
     */
    public static long IS_NEGATIVE_N = 0L;
    public static long IS_NEGATIVE_Y = 1L;
    public static long IS_NEGATIVE_ONLY_AVAILABLE = 2L;

    /**
     * 最大失败次数
     */
    public final static Integer FAIL_COUNT = 6;

    /**
     * 来源单据来源类型 180生产流程  222采购订单
     */
    public final static String BILL_TYPE_PRODUCTION_FLOW_NAME = "180";
    public final static String BILL_TYPE_PUR_NAME = "222";


    /**
     * 库存异动中间表单据类型
     */
    public final static String BILL_TYPE_KCYK = "KCYK";
    public final static String BILL_TYPE_ZTTZ = "ZTTZ";
    public final static String BILL_TYPE_XBCK = "XBCK";
    public final static String BILL_TYPE_XQGL = "XQGL";
    public final static String BILL_TYPE_OKZC = "OKZC";
    public final static String BILL_TYPE_OKZR = "OKZR";
    public final static String BILL_TYPE_BWQZC = "BWQZC";


    public final static Long BILL_TYPE_OMS_MANAGEMENT_NO = 0L;
    public final static Long BILL_TYPE_OMS_MANAGEMENT_YES = 1L;

    /**
     * 库存调整单调整性质 155 包材调整  64 无头件入库 65 无头件出库
     */
    public final static Integer SG_B_STO_ADJUST_PROP_155 = 155;
    public final static Integer SG_B_STO_ADJUST_PROP_64 = 64;
    public final static Integer SG_B_STO_ADJUST_PROP_65 = 65;

    public final static String SG_B_STO_ADJUST_PROP_BC = "包材调整";
    public final static String SG_B_STO_ADJUST_PROP_WTJRK = "无头件入库";
    public final static String SG_B_STO_ADJUST_PROP_WTJCK = "无头件出库";
    /**
     * sap信息映射单据类别 4 库存调整单
     */
    public final static String SAP_BILL_CATEGORY_04 = "4";


    public final static String RECODE_TABLE_NAME = "OC_B_SAP_SALES_DATA_RECORD";
    public final static String RECODE_TABLE_NAME_ITEM = "OC_B_SAP_SALES_DATA_RECORD_ITEM";

    public final static String SALE_PRODUCT_ATTR_C = "C";
    public final static String SALE_PRODUCT_ATTR_F = "F";
    public final static String SALE_PRODUCT_ATTR_FC = "FC";


}
