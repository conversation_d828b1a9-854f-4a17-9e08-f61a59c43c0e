package com.burgeon.r3.sg.basic.services;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.config.SgStorageControlConfig;
import com.burgeon.r3.sg.basic.logic.SgStorageCheckLogic;
import com.burgeon.r3.sg.basic.model.AbstractSgStorageUpdateCommonModel;
import com.burgeon.r3.sg.basic.model.SgStorageUpdateCommonCfModel;
import com.burgeon.r3.sg.basic.model.SgStorageUpdateCommonLsBatchModel;
import com.burgeon.r3.sg.basic.model.SgStorageUpdateCommonLsModel;
import com.burgeon.r3.sg.basic.model.SgStorageUpdateCommonSaModel;
import com.burgeon.r3.sg.basic.model.SgStorageUpdateCommonSsModel;
import com.burgeon.r3.sg.basic.model.SgStorageUpdateControlModel;
import com.burgeon.r3.sg.basic.model.request.AbstractSgStorageUpdateBillItemRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageSingleUpdateRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageUpdateBillRequest;
import com.burgeon.r3.sg.basic.model.result.SgStorageBillUpdateResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageUpdateCommonResult;
import com.burgeon.r3.sg.basic.utils.StorageAbstractUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.core.utils.StorageLogUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 通用单个单据Redis库存更新接口
 * @Author: chenb
 * @Date: 2019/7/10 00:00
 */
@Component
@Slf4j
public class SgStorageRedisUpdateService {

    @Autowired
    SgStorageControlConfig sgStorageControlConfig;

    /**
     * 通用单个单据库存更新接口
     *
     * @param request
     * @return
     */
    public ValueHolderV14<SgStorageBillUpdateResult> updateStorageBill(SgStorageSingleUpdateRequest request) {
        long startTime = System.currentTimeMillis();

        log.info(LogUtil.format("Start.SgStorageRedisUpdateService.updateStorageBill.ReceiveParams:billNo:{};",
                "SgStorageRedisUpdateService.updateStorageBill"), (request == null || request.getBill() == null) ? 0 : request.getBill().getBillNo());

        ValueHolderV14<SgStorageBillUpdateResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        ValueHolderV14<SgStorageUpdateCommonResult> checkHolder = null;
        SgStorageBillUpdateResult updateResult = new SgStorageBillUpdateResult();
        Map<String, AbstractSgStorageUpdateCommonModel> redisSynchMqItemMap = new HashMap<>(16);
        Map<String, AbstractSgStorageUpdateCommonModel> outStockItemMap = new HashMap<>(16);
        List<AbstractSgStorageUpdateCommonModel> outStockItemList = new ArrayList<>();
        List<AbstractSgStorageUpdateCommonModel> redisSynchMqItemList = new ArrayList<>();
        Map<Integer, List<String>> batchUpdateRedisKeyMap = new HashMap(16);
        Map<Integer, List<String>> batchUpdateRedisQtyMap = new HashMap<>(16);
        int preoutUpdateResult = SgConstantsIF.PREOUT_RESULT_SUCCESS;
        String luaPathResource = null;
        //Redis更新流水键
        String redisBillFtpPrefixKey = null;
        String redisBillFtpSubKey = null;
        Map<String, String> redisBillFtpKeyMap = new HashMap<>(16);
        boolean setDataFlg = false;
        StringBuffer sb = null;
        boolean debugLogOutputFlg = Boolean.TRUE;

        //发生错误的明细数
        long errorItemQty = 0;

        CusRedisTemplate<String, String> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();
        DefaultRedisScript<List> redisScript = new DefaultRedisScript<>();
        redisScript.setResultType(List.class);

        //检查入参
        holder = checkServiceParam(request);
        if (ResultCode.FAIL == holder.getCode()) {
            log.warn(LogUtil.format("SgStorageRedisUpdateService.updateStorageBill.checkServiceParam.error:{};",
                    "SgStorageRedisUpdateService.updateStorageBill"), holder.getMessage());
            return holder;
        }

        SgStorageUpdateBillRequest billInfo = request.getBill();
        User loginUser = request.getLoginUser();

        if (CollectionUtils.isEmpty(billInfo.getItemList())) {
            return holder;
        }

        //初始化库存更新控制对象（APOLLO系统配置优先级最低 默认不允许负库存）
        SgStorageUpdateControlModel controlModel = new SgStorageUpdateControlModel(sgStorageControlConfig);

        //当消费者设置了控制类里的情况下，优先使用消费者的控制类
        if (request.getControlModel() != null) {
            BeanUtils.copyProperties(request.getControlModel(), controlModel);
        }

        if (billInfo != null && !CollectionUtils.isEmpty(billInfo.getItemList()) && billInfo.getItemList().size() > 1000) {
            debugLogOutputFlg = Boolean.FALSE;
        }

        for (AbstractSgStorageUpdateBillItemRequest itemInfo : billInfo.getItemList()) {
            if (log.isDebugEnabled() && debugLogOutputFlg) {
                log.debug(LogUtil.format("SgStorageRedisUpdateService.updateStorageBill. SgStorageUpdateBillItemRequest:{};",
                        "SgStorageRedisUpdateService.updateStorageBill"), JSONObject.toJSONString(itemInfo));
            }

            SgStorageUpdateControlModel detailControlModel = new SgStorageUpdateControlModel();
            AbstractSgStorageUpdateCommonModel updateCommonModel =
                    StorageAbstractUtils.generateSgStorageUpdateCommonModel(itemInfo.getStorageType());

            if (updateCommonModel == null) {
                continue;
            }

            if (StringUtils.isEmpty(luaPathResource)) {
                luaPathResource = updateCommonModel.getUpdateLuaScriptPath();
                redisScript.setLocation(new ClassPathResource(luaPathResource));
            }
            StorageUtils.setBModelDefalutData(updateCommonModel, loginUser);
            BeanUtils.copyProperties(billInfo, updateCommonModel);
            BeanUtils.copyProperties(itemInfo, updateCommonModel);
            checkHolder = SgStorageCheckLogic.checkServiceParam(updateCommonModel, loginUser);

            if (ResultCode.FAIL == checkHolder.getCode()) {
                log.warn(LogUtil.format("SgStorageRedisUpdateService.updateStorageBill.SgStorageCheckLogic.checkServiceParam error:{};",
                        "SgStorageRedisUpdateService.updateStorageBill"), checkHolder.getMessage());
                holder.setCode(ResultCode.FAIL);
                holder.setMessage(checkHolder.getMessage());
                return holder;
            }

            //单据取消的情况下，相应数量取反
            if (billInfo.getIsCancel()) {
                updateCommonModel.generateCancelData();
            }

            if (itemInfo.getControlmodel() == null) {
                detailControlModel = controlModel;
            } else {
                BeanUtils.copyProperties(itemInfo.getControlmodel(), detailControlModel);
            }

            int partition = (int) (updateCommonModel.getPartitionStoreId() % sgStorageControlConfig.getRedisKeyPartitionNum());


            //Redis更新流水键 sg:storage_ftp:%BillNo%#%SourceBillNo%_nanoTime_changeDate_{partition}
            if (StringUtils.isEmpty(redisBillFtpPrefixKey)) {
                sb = new StringBuffer();
                sb.append(updateCommonModel.getRedisFtpPrefix());
                sb.append(billInfo.getBillNo());
                if (!StringUtils.isEmpty(billInfo.getSourceBillNo())) {
                    sb.append(SgConstants.SG_CONNECTOR_MARKS_3);
                    sb.append(billInfo.getSourceBillNo());
                }
                sb.append(SgConstants.SG_CONNECTOR_MARKS_6);
                sb.append(System.nanoTime());
                sb.append(SgConstants.SG_CONNECTOR_MARKS_6);
                sb.append(DateUtil.format(billInfo.getChangeDate(), "yyyyMMdd"));
                sb.append(SgConstants.SG_CONNECTOR_MARKS_6);
                sb.append(SgConstants.SG_CONNECTOR_MARKS_1);
                redisBillFtpPrefixKey = sb.toString();
            }

            //存在多条明细属于不同店仓的情况，需要进行明细拆解
            redisBillFtpSubKey = redisBillFtpPrefixKey.concat(String.valueOf(partition)).concat(SgConstants.SG_CONNECTOR_MARKS_2);
            redisBillFtpKeyMap.put(redisBillFtpSubKey, redisBillFtpSubKey);

            billInfo.setRedisBillFtpKey(redisBillFtpSubKey);
            updateCommonModel.setRedisBillFtpKey(redisBillFtpSubKey);

            //占用是否允许负库存,在途是否允许负库存,在库是否允许负库存,可用在库是否允许负库存(1：允许 0：不允许)
            String strConrol = StorageAbstractUtils.generateRedisControlParams(detailControlModel, itemInfo.getStorageType());

            if (StringUtils.isEmpty(strConrol)) {
                continue;
            }

            //LUA Redis键：sg:storage:{店仓ID%100}:店仓ID:SKUID:生产日期
            sb = new StringBuffer();
            sb.append(updateCommonModel.getRedisKeyPrefix());
            sb.append(SgConstants.SG_CONNECTOR_MARKS_1);
            sb.append(partition);
            sb.append(SgConstants.SG_CONNECTOR_MARKS_2);
            sb.append(SgConstants.SG_CONNECTOR_MARKS_4);
            sb.append(updateCommonModel.getMainStoreId());
            if (updateCommonModel.getHasSubStoreFlg()) {
                sb.append(SgConstants.SG_CONNECTOR_MARKS_6);
                sb.append(updateCommonModel.getSubStoreId());
            }
            sb.append(SgConstants.SG_CONNECTOR_MARKS_4);
            sb.append(updateCommonModel.getMainSkuId());
            if (updateCommonModel.getHasProduceDateFlg()) {
                sb.append(SgConstants.SG_CONNECTOR_MARKS_4);
                sb.append(updateCommonModel.getMainProduceDate());
            }

            if (batchUpdateRedisKeyMap.containsKey(partition)) {
                batchUpdateRedisKeyMap.get(partition).add(sb.toString());
            } else {
                List<String> batchUpdateRedisKeyList = new ArrayList<>();
                batchUpdateRedisKeyList.add(sb.toString());
                batchUpdateRedisKeyMap.put(partition, batchUpdateRedisKeyList);
            }

            String stockItemKey = updateCommonModel.getBillId().toString()
                    .concat(SgConstants.SG_CONNECTOR_MARKS_6)
                    .concat(updateCommonModel.getBillItemId().toString());

            //LUA参数:占用数量,在途数量,在库数量,明细ID,库存控制
            sb = new StringBuffer();
            sb.append(updateCommonModel.generateUpdateRedisQty());
            sb.append(SgConstants.SG_CONNECTOR_MARKS_5);
            sb.append(stockItemKey);
            sb.append(SgConstants.SG_CONNECTOR_MARKS_5);
            sb.append(strConrol);
            sb.append(SgConstants.SG_CONNECTOR_MARKS_5);
            sb.append(partition);
            sb.append(SgConstants.SG_CONNECTOR_MARKS_5);
            if(SgConstantsIF.STORAGE_TYPE_STORAGE.equals(itemInfo.getStorageType())){
                if (StringUtils.isEmpty(updateCommonModel.getFreeStockType())){
                    sb.append(SgConstantsIF.STOCK_TYPE_GOODS);
                }else{
                    sb.append(updateCommonModel.getFreeStockType());
                }
            }

            if (batchUpdateRedisQtyMap.containsKey(partition)) {
                batchUpdateRedisQtyMap.get(partition).add(sb.toString());
            } else {
                List<String> batchUpdateRedisQtyList = new ArrayList<>();
                batchUpdateRedisQtyList.add(sb.toString());
                batchUpdateRedisQtyMap.put(partition, batchUpdateRedisQtyList);
            }

            redisSynchMqItemMap.put(stockItemKey, updateCommonModel);
            outStockItemMap.put(stockItemKey, updateCommonModel);

        }

        for (Integer key : batchUpdateRedisKeyMap.keySet()) {
            List<String> batchUpdateRedisKeyList = batchUpdateRedisKeyMap.get(key);
            List<String> batchUpdateRedisQtyList = batchUpdateRedisQtyMap.get(key);

            //批量更新单据明细分页
            int pageSize = controlModel.getRedisMaxUpdateLimit();
            int listSize = batchUpdateRedisKeyList.size();
            int page = listSize / pageSize;

            if (listSize % pageSize != 0) {
                page++;
            }

            try {
                int fromIndex = 0;
                int toIndex = 0;
                List<String> qtyListItem = null;
                List<String> keyList = null;
                List<String> qtyList = null;

                //分页批量更新
                for (int i = 0; i < page; i++) {
                    fromIndex = i * pageSize;
                    toIndex = (((i + 1) * pageSize > listSize ? listSize : pageSize * (i + 1)));

                    keyList = batchUpdateRedisKeyList.subList(fromIndex, toIndex);
                    qtyList = batchUpdateRedisQtyList.subList(fromIndex, toIndex);

                    qtyListItem = new ArrayList<>();
                    qtyListItem.addAll(qtyList);
                    qtyListItem.add(redisBillFtpPrefixKey.concat(String.valueOf(key)).concat(SgConstants.SG_CONNECTOR_MARKS_2));
                    qtyListItem.add(String.valueOf(controlModel.getPreoutOperateType()));
                    qtyListItem.add(billInfo.getIsCancel() ? SgConstants.IS_TRUE : SgConstants.IS_FALSE);

                    if (log.isInfoEnabled() && debugLogOutputFlg) {
                        log.info(LogUtil.format("SgStorageRedisUpdateService.updateStorageBill.redisTemplate.execute.start.ReceiveParams:keyList:{}, qtyList:{};",
                                "SgStorageRedisUpdateService.updateStorageBill"), JSONObject.toJSONString(keyList), JSONObject.toJSONString(qtyListItem));
                    }

                    List result = redisMasterTemplate.execute(redisScript, keyList,
                            qtyListItem.toArray(new String[qtyListItem.size()]));

                    if (log.isInfoEnabled() && debugLogOutputFlg) {
                        log.info(LogUtil.format("SgStorageRedisUpdateService.updateStorageBill.redisTemplate.execute.end.ReturnResult:result:{};",
                                "SgStorageRedisUpdateService.updateStorageBill"), JSONObject.toJSONString(result));
                    }

                    if (SgConstantsIF.PREOUT_OPT_TYPE_OUT_STOCK != controlModel.getPreoutOperateType()) {
                        setDataFlg = true;
                    }

                    if (!CollectionUtils.isEmpty(result) &&
                            SgConstantsIF.PREOUT_RESULT_SUCCESS != Integer.valueOf((String) result.get(0))) {
                        List<String> billList = (ArrayList) result.get(1);

                        if (!CollectionUtils.isEmpty(billList)) {
                            for (String luaResult : billList) {
                                if (outStockItemMap.containsKey(luaResult)) {
                                    AbstractSgStorageUpdateCommonModel updateCommonModel = outStockItemMap.get(luaResult);
                                    updateCommonModel.setQtyOutOfStock(updateCommonModel.getMainQtyPreoutChange());
                                    outStockItemList.add(updateCommonModel);
                                    redisSynchMqItemMap.remove(luaResult);
                                }
                            }
                        }

                        preoutUpdateResult = Integer.valueOf((String) result.get(0));
                        if (SgConstantsIF.PREOUT_RESULT_OUT_STOCK != preoutUpdateResult) {
                            holder.setCode(ResultCode.FAIL);
                            //报出错的情况下，错误件数为当前批量更新件数
                            errorItemQty = errorItemQty + keyList.size();

                            if (result.get(2) != null && outStockItemMap.containsKey(result.get(2))) {
                                AbstractSgStorageUpdateCommonModel negativeModel = outStockItemMap.get(result.get(2));
                                if (log.isDebugEnabled()) {
                                    negativeModel.exportLog(
                                            "SgStorageRedisUpdateService.updateStorageBill. 当前库存变动明细库存不足！！");
                                }
                                holder.setMessage(negativeModel.getErrorMessage(loginUser.getLocale()));
                            }
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                String errorMsg = StorageLogUtils.getErrMessage(e, loginUser);
                updateResult.setPreoutUpdateResult(controlModel.getPreoutOperateType());

                rollbackStorageBill(new ArrayList<>(redisBillFtpKeyMap.keySet()), Boolean.FALSE);

                holder.setCode(ResultCode.FAIL);
                holder.setData(updateResult);
                holder.setMessage(Resources.getMessage("店仓库存批量更新失败！", loginUser.getLocale()).concat(errorMsg));

                log.error(LogUtil.format("Finish SgStorageRedisUpdateService.updateStorageBill.error:{}, ReturnResult:errorBillItemQty:{} spend time:{}ms;", "SgStorageRedisUpdateService.updateStorageBill"),
                        Throwables.getStackTraceAsString(e), JSONObject.toJSONString(holder.getData() == null ? 0 : holder.getData().getErrorBillItemQty()), System.currentTimeMillis() - startTime);

                return holder;
            }

        }

        if (ResultCode.SUCCESS == holder.getCode() && redisSynchMqItemMap.size() > 0) {
            redisSynchMqItemList.addAll(new ArrayList<>(redisSynchMqItemMap.values()));
        } else if (ResultCode.FAIL == holder.getCode() && setDataFlg) {
            rollbackStorageBill(new ArrayList<>(redisBillFtpKeyMap.keySet()), Boolean.FALSE);
        }

        updateResult.setRedisBillFtpKeyList(new ArrayList<>(redisBillFtpKeyMap.keySet()));
        updateResult.setErrorBillItemQty(errorItemQty);
        updateResult.setPreoutUpdateResult(preoutUpdateResult);
        updateResult.setOutStockItemList(outStockItemList);
        updateResult.setRedisSynchMqItemList(redisSynchMqItemList);
        holder.setData(updateResult);

        // 没有错误明细的情况下，设置成功运行结果，
        // 有错误明细的情况下，在发生错误的明细中设置运行结果
        if (errorItemQty < 1) {
            holder.setCode(ResultCode.SUCCESS);
            holder.setMessage(Resources.getMessage("当前单据的店仓库存变动消息处理完毕！",
                    loginUser.getLocale(), holder.getMessage(), billInfo.getBillNo()));
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish SgStorageRedisUpdateService.updateStorageBill. ReturnResult:errorBillItemQty:{} spend time:{}ms;",
                    "SgStorageRedisUpdateService.updateStorageBill"), JSONObject.toJSONString(holder.getData() == null ? 0 : holder.getData().getErrorBillItemQty()), System.currentTimeMillis() - startTime);
        }

        return holder;
    }

    /**
     * @param request
     * @return
     */
    private ValueHolderV14<SgStorageBillUpdateResult> checkServiceParam(SgStorageSingleUpdateRequest request) {
        ValueHolderV14<SgStorageBillUpdateResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

        if (request == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("请求体为空！"));
            return holder;
        }

        if (request.getLoginUser() == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("操作用户信息不能为空！"));
        }

        User loginUser = request.getLoginUser();

        if (request.getBill() == null || StringUtils.isEmpty(request.getBill().getBillNo())) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("店仓库存更新单据不能为空！", loginUser.getLocale()));
        } else if (CollectionUtils.isEmpty(request.getBill().getItemList())) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("店仓库存更新单据明细不能为空！", loginUser.getLocale()));
        }

        return holder;

    }

    /**
     * @param redisBillFtpKeys
     * @return
     */
    protected ValueHolderV14<SgStorageBillUpdateResult> rollbackStorageBill(List<String> redisBillFtpKeys) {
        return this.rollbackStorageBill(redisBillFtpKeys, Boolean.TRUE);
    }

    /**
     * @param redisBillFtpKeys
     * @param isSyncMq
     * @return
     */
    protected ValueHolderV14<SgStorageBillUpdateResult> rollbackStorageBill(List<String> redisBillFtpKeys,
                                                                            Boolean isSyncMq) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgStorageRedisUpdateService.rollbackStorageBill. ReceiveParams:redisBillFtpKeyList:{};",
                    JSONObject.toJSONString(redisBillFtpKeys));
        }

        ValueHolderV14<SgStorageBillUpdateResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS, "");
        SgStorageBillUpdateResult updateResult = new SgStorageBillUpdateResult();
        List<AbstractSgStorageUpdateCommonModel> redisSynchMqItemList = new ArrayList<>();
        Map<String, List<String>> redisBillFtpKeyLsMap = new HashMap<>(16);
        Map<String, List<String>> redisBillFtpKeySaMap = new HashMap<>(16);
        Map<String, List<String>> redisBillFtpKeySsMap = new HashMap<>(16);
        Map<String, List<String>> redisBillFtpKeyCfMap = new HashMap<>(16);
        Map<String, List<String>> redisBillFtpKeyLsBatchMap = new HashMap<>(16);
        List<String> redisBillFtpKeyList = null;
        String partition = null;
        String billNos = null;
        String billNo = null;
        String sourceBillNo = null;
        String[] billNoList = null;
        Date changeDate = null;

        if (CollectionUtils.isEmpty(redisBillFtpKeys)) {
            return holder;
        }

        //过滤列表中的重复Key
        redisBillFtpKeys = redisBillFtpKeys.stream().distinct().collect(Collectors.toList());

        CusRedisTemplate<String, String> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();

        //获取原Redis库存流水信息
        for (String redisBillFtpKey : redisBillFtpKeys) {
            if (StringUtils.isEmpty(redisBillFtpKey)) {
                continue;
            }

            sourceBillNo = null;

            List<Object> redisBillFtpValueList = redisMasterTemplate.opsForHash().values(redisBillFtpKey);
            if (CollectionUtils.isEmpty(redisBillFtpValueList)) {
                continue;
            }

            partition = redisBillFtpKey.split(SgConstants.SG_CONNECTOR_MARKS_7)[1];
            partition = partition.substring(0, partition.length() - 1);

            //根据流水类型进行库存流水拆解
            if (redisBillFtpKey.startsWith(SgConstants.REDIS_KEY_PREFIX_LS_STORAGE_FTP)) {
                if (redisBillFtpKeyLsMap.containsKey(partition)) {
                    redisBillFtpKeyLsMap.get(partition).add(redisBillFtpKey);
                } else {
                    redisBillFtpKeyList = new ArrayList<>();
                    redisBillFtpKeyList.add(redisBillFtpKey);
                    redisBillFtpKeyLsMap.put(partition, redisBillFtpKeyList);
                }
            } else if (redisBillFtpKey.startsWith(SgConstants.REDIS_KEY_PREFIX_SA_STORAGE_FTP)) {
                if (redisBillFtpKeySaMap.containsKey(partition)) {
                    redisBillFtpKeySaMap.get(partition).add(redisBillFtpKey);
                } else {
                    redisBillFtpKeyList = new ArrayList<>();
                    redisBillFtpKeyList.add(redisBillFtpKey);
                    redisBillFtpKeySaMap.put(partition, redisBillFtpKeyList);
                }
            } else if (redisBillFtpKey.startsWith(SgConstants.REDIS_KEY_PREFIX_SS_STORAGE_FTP)) {
                if (redisBillFtpKeySsMap.containsKey(partition)) {
                    redisBillFtpKeySsMap.get(partition).add(redisBillFtpKey);
                } else {
                    redisBillFtpKeyList = new ArrayList<>();
                    redisBillFtpKeyList.add(redisBillFtpKey);
                    redisBillFtpKeySsMap.put(partition, redisBillFtpKeyList);
                }
            } else if (redisBillFtpKey.startsWith(SgConstants.REDIS_KEY_PREFIX_CF_STORAGE_FTP)) {
                if (redisBillFtpKeyCfMap.containsKey(partition)) {
                    redisBillFtpKeyCfMap.get(partition).add(redisBillFtpKey);
                } else {
                    redisBillFtpKeyList = new ArrayList<>();
                    redisBillFtpKeyList.add(redisBillFtpKey);
                    redisBillFtpKeyCfMap.put(partition, redisBillFtpKeyList);
                }
            } else if (redisBillFtpKey.startsWith(SgConstants.REDIS_KEY_PREFIX_LS_BATCH_STORAGE_FTP)) {
                //逻辑仓批次库存
                if (redisBillFtpKeyLsBatchMap.containsKey(partition)) {
                    redisBillFtpKeyLsBatchMap.get(partition).add(redisBillFtpKey);
                } else {
                    redisBillFtpKeyList = new ArrayList<>();
                    redisBillFtpKeyList.add(redisBillFtpKey);
                    redisBillFtpKeyLsBatchMap.put(partition, redisBillFtpKeyList);
                }
            } else {
                log.error("SgStorageItemBatchUpdateService.updateStorageItem. error:storageType_is_wrong!");
                continue;
            }

            //无需库存MQ同步的情况下，进行下一条处理
            if (!isSyncMq) {
                continue;
            }

            try {
                billNos = redisBillFtpKey.split(SgConstants.SG_CONNECTOR_MARKS_4)[2];
                billNoList = billNos.split(SgConstants.SG_CONNECTOR_MARKS_6);
                billNos = billNoList[0];
                changeDate = billNoList[2] == null ? null : DateUtil.stringToDate(billNoList[2]);

                if (billNos.contains(SgConstants.SG_CONNECTOR_MARKS_3)) {
                    billNoList = billNos.split(SgConstants.SG_CONNECTOR_MARKS_3);
                    billNo = billNoList[0];
                    sourceBillNo = billNoList[1];
                } else {
                    billNo = billNos;
                }
            } catch (Exception e) {
                billNo = null;
                sourceBillNo = null;
                changeDate = null;
            }

            for (Object redisBillFtpValue : redisBillFtpValueList) {
                String[] splitItems = redisBillFtpValue.toString().split(SgConstants.SG_CONNECTOR_MARKS_5);

                if (redisBillFtpKey.startsWith(SgConstants.REDIS_KEY_PREFIX_LS_STORAGE_FTP)) {
                    SgStorageUpdateCommonLsModel commonModel = new SgStorageUpdateCommonLsModel();
                    commonModel.generateRedisSynchMqItem(sgStorageControlConfig, splitItems);
                    commonModel.setRedisBillFtpKey(redisBillFtpKey);
                    commonModel.setBillNo(billNo);
                    commonModel.setChangeDate(changeDate);
                    commonModel.setSourceBillNo(sourceBillNo);
                    redisSynchMqItemList.add(commonModel);
                } else if (redisBillFtpKey.startsWith(SgConstants.REDIS_KEY_PREFIX_SA_STORAGE_FTP)) {
                    SgStorageUpdateCommonSaModel commonModel = new SgStorageUpdateCommonSaModel();
                    commonModel.generateRedisSynchMqItem(sgStorageControlConfig, splitItems);
                    commonModel.setRedisBillFtpKey(redisBillFtpKey);
                    commonModel.setBillNo(billNo);
                    commonModel.setChangeDate(changeDate);
                    commonModel.setSourceBillNo(sourceBillNo);
                    redisSynchMqItemList.add(commonModel);
                } else if (redisBillFtpKey.startsWith(SgConstants.REDIS_KEY_PREFIX_SS_STORAGE_FTP)) {
                    SgStorageUpdateCommonSsModel commonModel = new SgStorageUpdateCommonSsModel();
                    commonModel.generateRedisSynchMqItem(sgStorageControlConfig, splitItems);
                    commonModel.setRedisBillFtpKey(redisBillFtpKey);
                    commonModel.setBillNo(billNo);
                    commonModel.setChangeDate(changeDate);
                    commonModel.setSourceBillNo(sourceBillNo);
                    redisSynchMqItemList.add(commonModel);
                } else if (redisBillFtpKey.startsWith(SgConstants.REDIS_KEY_PREFIX_CF_STORAGE_FTP)) {
                    SgStorageUpdateCommonCfModel commonModel = new SgStorageUpdateCommonCfModel();
                    commonModel.generateRedisSynchMqItem(sgStorageControlConfig, splitItems);
                    commonModel.setRedisBillFtpKey(redisBillFtpKey);
                    commonModel.setBillNo(billNo);
                    commonModel.setChangeDate(changeDate);
                    commonModel.setSourceBillNo(sourceBillNo);
                    redisSynchMqItemList.add(commonModel);
                } else if (redisBillFtpKey.startsWith(SgConstants.REDIS_KEY_PREFIX_LS_BATCH_STORAGE_FTP)) {
                    SgStorageUpdateCommonLsBatchModel commonModel = new SgStorageUpdateCommonLsBatchModel();
                    commonModel.generateRedisSynchMqItem(sgStorageControlConfig, splitItems);
                    commonModel.setRedisBillFtpKey(redisBillFtpKey);
                    commonModel.setBillNo(billNo);
                    commonModel.setChangeDate(changeDate);
                    commonModel.setSourceBillNo(sourceBillNo);
                    redisSynchMqItemList.add(commonModel);
                } else {
                    log.error("SgStorageItemBatchUpdateService.updateStorageItem. error:storageType_is_wrong!");
                    continue;
                }
            }
        }

        DefaultRedisScript<List> redisScript = new DefaultRedisScript<>();
        redisScript.setResultType(List.class);

        if (!MapUtils.isEmpty(redisBillFtpKeyLsMap)) {
            for (String key : redisBillFtpKeyLsMap.keySet()) {
                redisBillFtpKeyList = redisBillFtpKeyLsMap.get(key);
                redisScript.setLocation(new ClassPathResource("lua/RollbackRedisLsStorage.lua"));
                redisMasterTemplate.execute(redisScript, redisBillFtpKeyList, "");
            }
        }

        if (!MapUtils.isEmpty(redisBillFtpKeySaMap)) {
            for (String key : redisBillFtpKeySaMap.keySet()) {
                redisBillFtpKeyList = redisBillFtpKeySaMap.get(key);
                redisScript.setLocation(new ClassPathResource("lua/RollbackRedisSaStorage.lua"));
                redisMasterTemplate.execute(redisScript, redisBillFtpKeyList, "");
            }
        }

        if (!MapUtils.isEmpty(redisBillFtpKeySsMap)) {
            for (String key : redisBillFtpKeySsMap.keySet()) {
                redisBillFtpKeyList = redisBillFtpKeySsMap.get(key);
                redisScript.setLocation(new ClassPathResource("lua/RollbackRedisSsStorage.lua"));
                redisMasterTemplate.execute(redisScript, redisBillFtpKeyList, "");
            }
        }

        if (!MapUtils.isEmpty(redisBillFtpKeyCfMap)) {
            for (String key : redisBillFtpKeyCfMap.keySet()) {
                redisBillFtpKeyList = redisBillFtpKeyCfMap.get(key);
                redisScript.setLocation(new ClassPathResource("lua/RollbackRedisCfStorage.lua"));
                redisMasterTemplate.execute(redisScript, redisBillFtpKeyList, "");
            }
        }

        /**逻辑仓批次库存回滚*/
        if (!MapUtils.isEmpty(redisBillFtpKeyLsBatchMap)) {
            for (String key : redisBillFtpKeyLsBatchMap.keySet()) {
                redisBillFtpKeyList = redisBillFtpKeyLsBatchMap.get(key);
                redisScript.setLocation(new ClassPathResource("lua/RollbackRedisLsBatchStorage.lua"));
                redisMasterTemplate.execute(redisScript, redisBillFtpKeyList, "");
            }
        }

        updateResult.setRedisSynchMqItemList(redisSynchMqItemList);
        holder.setData(updateResult);

        return holder;
    }

}
