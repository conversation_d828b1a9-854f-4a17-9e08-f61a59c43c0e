package com.burgeon.r3.sg.basic.model.mq;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SgStrorageRetailSynchModel implements Serializable {

    private static final long serialVersionUID = 7067333258598794801L;

    /**
     * 变动日期
     */
    private Long changeTime;
    /**
     * 单据编号
     */
    private String billNo;
    /**
     * 店仓id
     */
    private Long cpCStoreId;
    /**
     * 条码id
     */
    private Long psCSkuId;
    /**
     * 条码编码
     */
    private String psCSkuEcode;
    /**
     * 条码编码
     */
    private String psCProEcode;
    /**
     * 可用库存
     */
    private BigDecimal qtyAvailable;
    /**
     * 在库库存
     */
    private BigDecimal qtyStorage;
}
