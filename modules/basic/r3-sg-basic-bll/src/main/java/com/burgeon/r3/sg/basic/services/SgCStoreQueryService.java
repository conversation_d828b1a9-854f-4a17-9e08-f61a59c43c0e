package com.burgeon.r3.sg.basic.services;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.basic.mapper.SgCShareStoreMapper;
import com.burgeon.r3.sg.basic.model.request.SgCShareStoreQueryRequest;
import com.burgeon.r3.sg.basic.model.request.SgStoreInfoQueryRequest;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Auther: chenhao
 * @Date: 2021-05-19 09:36
 * @Description: 共享层基础档案查询
 */
@Component
@Slf4j
public class SgCStoreQueryService {

    @Autowired
    private SgCShareStoreMapper storeMapper;

    @Autowired
    private SgCSaStoreMapper saStoreMapper;

    @Autowired
    private CpCStoreMapper cpCStoreMapper;

    /**
     * 根据ids 查找聚合仓档案
     *
     * @param shareeInfoQueryRequest 入参
     * @return 出参
     */
    public HashMap<Long, SgCShareStore> getShareStoreInfoById(SgStoreInfoQueryRequest shareeInfoQueryRequest) {
        if (log.isDebugEnabled()) {
            log.debug("Start BasicSgStoreQueryService.getShareStoreInfoById={};", JSONObject.toJSONString(shareeInfoQueryRequest));
        }
        HashMap<Long, SgCShareStore> hashMap = new HashMap();
        List<SgCShareStore> sgShareStores = new ArrayList();
//        new SgCShareStore();
        List<Long> sgcShareStoreIds = new ArrayList();
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        if (shareeInfoQueryRequest != null && !CollectionUtils.isEmpty(shareeInfoQueryRequest.getIds())) {
            String type = "SG:SG_C_SHARE_STORE:";

            try {
                Iterator var9;
                if (!CollectionUtils.isEmpty(shareeInfoQueryRequest.getIds())) {
                    var9 = shareeInfoQueryRequest.getIds().iterator();
                    while (var9.hasNext()) {
                        Long id = (Long) var9.next();
                        String cd = type + id;
                        Object sgcShareStore = redisTemplate.opsForValue().get(cd);
                        if (null != sgcShareStore) {
                            SgCShareStore sgShareStore = (SgCShareStore) JSONObject.parseObject(sgcShareStore.toString(), SgCShareStore.class);
                            sgShareStores.add(sgShareStore);
                        } else {
                            sgcShareStoreIds.add(id);
                        }
                    }
                }

                if (!CollectionUtils.isEmpty(sgcShareStoreIds)) {
                    List<SgCShareStore> sgShareStoreList = storeMapper.selectBatchIds(sgcShareStoreIds);
                    if (!CollectionUtils.isEmpty(sgShareStoreList)) {
                        sgShareStores.addAll(sgShareStoreList);
                        Iterator var13 = sgShareStoreList.iterator();
                        while (var13.hasNext()) {
                            SgCShareStore sgShareSto = (SgCShareStore) var13.next();
                            redisTemplate.opsForValue().set(type + sgShareSto.getId(), JSONObject.toJSONString(sgShareSto), 1L, TimeUnit.DAYS);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(sgShareStores)) {
                    var9 = sgShareStores.iterator();
                    while (var9.hasNext()) {
                        SgCShareStore cStore = (SgCShareStore) var9.next();
                        hashMap.put(cStore.getId(), cStore);
                    }
                }
            } catch (Exception var15) {
                AssertUtils.logAndThrowException(var15.getMessage(), var15);
            }
            return hashMap;
        } else {
            return hashMap;
        }
    }

    /**
     * 根据code 查找聚合仓档案
     *
     * @param shareeInfoQueryRequest 入参
     * @return 出参
     */
    public HashMap<String, SgCShareStore> getShareStoreInfoByEcode(SgStoreInfoQueryRequest shareeInfoQueryRequest) {
        if (log.isDebugEnabled()) {
            log.debug("Start BasicSgStoreQueryService.getShareStoreInfoByEcode={};", JSONObject.toJSONString(shareeInfoQueryRequest));
        }
        HashMap<String, SgCShareStore> hashMap = new HashMap();
        List<SgCShareStore> sgShareStores = new ArrayList();
//        new SgCShareStore();
        List<String> sgcShareStoreEcodes = new ArrayList();
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        if (shareeInfoQueryRequest != null && !CollectionUtils.isEmpty(shareeInfoQueryRequest.getEcodes())) {
            String type = "SG:SG_C_SHARE_STORE_ECODE:";
            try {
                Iterator var9;
                if (!CollectionUtils.isEmpty(shareeInfoQueryRequest.getEcodes())) {
                    var9 = shareeInfoQueryRequest.getEcodes().iterator();
                    while (var9.hasNext()) {
                        String ecode = (String) var9.next();
                        String cd = type + ecode;
                        Object sgcShareStore = redisTemplate.opsForValue().get(cd);
                        if (null != sgcShareStore) {
                            SgCShareStore sgShareStore = (SgCShareStore) JSONObject.parseObject(sgcShareStore.toString(), SgCShareStore.class);
                            sgShareStores.add(sgShareStore);
                        } else {
                            sgcShareStoreEcodes.add(ecode);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(sgcShareStoreEcodes)) {
                    List<SgCShareStore> sgShareStoreList = storeMapper.selectList(new QueryWrapper<SgCShareStore>()
                            .lambda()
                            .in(SgCShareStore::getEcode, sgcShareStoreEcodes));
                    if (!CollectionUtils.isEmpty(sgShareStoreList)) {
                        sgShareStores.addAll(sgShareStoreList);
                        Iterator var13 = sgShareStoreList.iterator();
                        while (var13.hasNext()) {
                            SgCShareStore sgShareSto = (SgCShareStore) var13.next();
                            redisTemplate.opsForValue().set(type + sgShareSto.getEcode(), JSONObject.toJSONString(sgShareSto), 1L, TimeUnit.DAYS);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(sgShareStores)) {
                    var9 = sgShareStores.iterator();
                    while (var9.hasNext()) {
                        SgCShareStore cStore = (SgCShareStore) var9.next();
                        hashMap.put(cStore.getEcode(), cStore);
                    }
                }
            } catch (Exception var15) {
                AssertUtils.logAndThrowException(var15.getMessage(), var15);
            }
            return hashMap;
        } else {
            return hashMap;
        }
    }

    /**
     * 根据ids 查找SA仓档案
     *
     * @param shareeInfoQueryRequest 入参
     * @return 出参
     */
    public HashMap<Long, SgCSaStore> getSaStoreInfoById(SgStoreInfoQueryRequest shareeInfoQueryRequest) {
        if (log.isDebugEnabled()) {
            log.debug("Start BasicSgStoreQueryService.getSaStoreInfoById={};", JSONObject.toJSONString(shareeInfoQueryRequest));
        }
        HashMap<Long, SgCSaStore> hashMap = new HashMap();
        List<SgCSaStore> sgSaStores = new ArrayList();
//        new SgCSaStore();
        List<Long> sgcSaStoreIds = new ArrayList();
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        if (shareeInfoQueryRequest != null && !CollectionUtils.isEmpty(shareeInfoQueryRequest.getIds())) {
            String type = "SG:SG_C_SA_STORE:";

            try {
                Iterator var9;
                if (!CollectionUtils.isEmpty(shareeInfoQueryRequest.getIds())) {
                    var9 = shareeInfoQueryRequest.getIds().iterator();
                    while (var9.hasNext()) {
                        Long id = (Long) var9.next();
                        String cd = type + id;
                        Object sgcSaStore = redisTemplate.opsForValue().get(cd);
                        if (null != sgcSaStore) {
                            SgCSaStore sgShareStore = (SgCSaStore) JSONObject.parseObject(sgcSaStore.toString(), SgCSaStore.class);
                            sgSaStores.add(sgShareStore);
                        } else {
                            sgcSaStoreIds.add(id);
                        }
                    }
                }

                if (!CollectionUtils.isEmpty(sgcSaStoreIds)) {
                    List<SgCSaStore> sgShareStoreList = saStoreMapper.selectBatchIds(sgcSaStoreIds);
                    if (!CollectionUtils.isEmpty(sgShareStoreList)) {
                        sgSaStores.addAll(sgShareStoreList);
                        Iterator var13 = sgShareStoreList.iterator();
                        while (var13.hasNext()) {
                            SgCSaStore sgShareSto = (SgCSaStore) var13.next();
                            redisTemplate.opsForValue().set(type + sgShareSto.getId(), JSONObject.toJSONString(sgShareSto), 1L, TimeUnit.DAYS);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(sgSaStores)) {
                    var9 = sgSaStores.iterator();
                    while (var9.hasNext()) {
                        SgCSaStore cStore = (SgCSaStore) var9.next();
                        hashMap.put(cStore.getId(), cStore);
                    }
                }
            } catch (Exception var15) {
                AssertUtils.logAndThrowException(var15.getMessage(), var15);
            }
            return hashMap;
        } else {
            return hashMap;
        }
    }

    /**
     * 根据code 查找SA仓档案
     *
     * @param shareeInfoQueryRequest 入参
     * @return 出参
     */
    public HashMap<String, SgCSaStore> getSaStoreInfoByEcode(SgStoreInfoQueryRequest shareeInfoQueryRequest) {
        if (log.isDebugEnabled()) {
            log.debug("Start BasicSgStoreQueryService.getSaStoreInfoByEcode={};", JSONObject.toJSONString(shareeInfoQueryRequest));
        }
        HashMap<String, SgCSaStore> hashMap = new HashMap();
        List<SgCSaStore> sgSaStores = new ArrayList();
//        new SgCSaStore();
        List<String> sgcSaStoreEcodes = new ArrayList();
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        if (shareeInfoQueryRequest != null && !CollectionUtils.isEmpty(shareeInfoQueryRequest.getEcodes())) {
            String type = "SG:SG_C_SA_STORE_ECODE:";
            try {
                Iterator var9;
                if (!CollectionUtils.isEmpty(shareeInfoQueryRequest.getEcodes())) {
                    var9 = shareeInfoQueryRequest.getEcodes().iterator();
                    while (var9.hasNext()) {
                        String ecode = (String) var9.next();
                        String cd = type + ecode;
                        Object sgcSaStore = redisTemplate.opsForValue().get(cd);
                        if (null != sgcSaStore) {
                            SgCSaStore sgSaStore = (SgCSaStore) JSONObject.parseObject(sgcSaStore.toString(), SgCSaStore.class);
                            sgSaStores.add(sgSaStore);
                        } else {
                            sgcSaStoreEcodes.add(ecode);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(sgcSaStoreEcodes)) {
                    List<SgCSaStore> sgShareStoreList = saStoreMapper.selectList(new QueryWrapper<SgCSaStore>()
                            .lambda()
                            .in(SgCSaStore::getEcode, sgcSaStoreEcodes));
                    if (!CollectionUtils.isEmpty(sgShareStoreList)) {
                        sgSaStores.addAll(sgShareStoreList);
                        Iterator var13 = sgShareStoreList.iterator();
                        while (var13.hasNext()) {
                            SgCSaStore sgShareSto = (SgCSaStore) var13.next();
                            redisTemplate.opsForValue().set(type + sgShareSto.getEcode(), JSONObject.toJSONString(sgShareSto), 1L, TimeUnit.DAYS);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(sgSaStores)) {
                    var9 = sgSaStores.iterator();
                    while (var9.hasNext()) {
                        SgCSaStore cStore = (SgCSaStore) var9.next();
                        hashMap.put(cStore.getEcode(), cStore);
                    }
                }
            } catch (Exception var15) {
                AssertUtils.logAndThrowException(var15.getMessage(), var15);
            }
            return hashMap;
        } else {
            return hashMap;
        }
    }

    /**
     * 跟据聚合仓id查找 所以跟此聚合仓有关系的SA仓
     *
     * @param storeInfoQueryRequest 入参
     * @return 出参
     */
    public HashMap<Long, List<SgCSaStore>> getStoreInfoByShareId(SgStoreInfoQueryRequest storeInfoQueryRequest) {
        HashMap<Long, List<SgCSaStore>> hashMap = new HashMap();
        List<SgCSaStore> sgSaStores = new ArrayList();
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        if (log.isDebugEnabled()) {
            log.debug("Start BasicSgStoreQueryService.getStoreInfoByShareId={};", JSONObject.toJSONString(storeInfoQueryRequest));
        }
        if (storeInfoQueryRequest != null && storeInfoQueryRequest.getShareId() != null) {
            String type = "SG:SG_C_SA_STORE_SHAREID:";
            try {
                String cd = type + storeInfoQueryRequest.getShareId();
                Object sgSaStore = redisTemplate.opsForValue().get(cd);
                if (null != sgSaStore) {
                    sgSaStores = JSONObject.parseArray(sgSaStore.toString(), SgCSaStore.class);
                } else {
                    List<SgCSaStore> sgShareStoreList = saStoreMapper.selectList(new QueryWrapper<SgCSaStore>()
                            .lambda()
                            .in(SgCSaStore::getSgCShareStoreId, storeInfoQueryRequest.getShareId()));
                    if (!CollectionUtils.isEmpty(sgShareStoreList)) {
                        ((List) sgSaStores).addAll(sgShareStoreList);
                        redisTemplate.opsForValue().set(cd, JSONObject.toJSONString(sgShareStoreList), 1L, TimeUnit.DAYS);
                    }
                }
                if (!CollectionUtils.isEmpty((Collection) sgSaStores)) {
                    hashMap.put(storeInfoQueryRequest.getShareId(), sgSaStores);
                }
            } catch (Exception var12) {
                AssertUtils.logAndThrowException(var12.getMessage(), var12);
            }
            return hashMap;
        } else {
            return hashMap;
        }
    }

    /**
     * @param request
     * @param loginUser
     * @return
     */
    public ValueHolderV14<Map<Long, List<SgCpCStore>>> getCpCStoreByShareId(SgCShareStoreQueryRequest request, User loginUser) {

        if (log.isDebugEnabled()) {
            log.debug("Start SgCStoreQueryService.getCpCStoreByShareId. ReceiveParams:request={};", JSONObject.toJSONString(request));
        }

        ValueHolderV14<Map<Long, List<SgCpCStore>>> holder = new ValueHolderV14(ResultCode.SUCCESS, "");
        Map<Long, List<SgCpCStore>> result = new HashMap<>(16);
        Long shareStoreId = null;
        List<SgCpCStore> storeList = null;

        if (loginUser == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("操作用户信息不能为空！"));
            return holder;
        }

        if (request == null ||
                CollectionUtils.isEmpty(request.getSgCShareStoreIds())) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("请求体不能为空！", loginUser.getLocale()));
            return holder;
        }

        List<SgCpCStore> cpCStoreList = cpCStoreMapper.selectList(
                new QueryWrapper<SgCpCStore>().lambda()
                        .in(!CollectionUtils.isEmpty(request.getSgCShareStoreIds()), SgCpCStore::getSgCShareStoreId, request.getSgCShareStoreIds())
                        .eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y)
                        .orderByAsc(SgCpCStore::getSgCShareStoreId));

        if (CollectionUtils.isEmpty(cpCStoreList)) {
            return holder;
        }

        for (SgCpCStore store : cpCStoreList) {

            if (store.getSgCShareStoreId() == null) {
                continue;
            }

            if (!store.getSgCShareStoreId().equals(shareStoreId)) {
                if (!CollectionUtils.isEmpty(storeList)) {
                    result.put(shareStoreId, storeList);
                }
                storeList = new ArrayList<>();
                shareStoreId = store.getSgCShareStoreId();
            }

            storeList.add(store);

        }

        result.put(shareStoreId, storeList);
        holder.setData(result);
        return holder;

    }


    /**
     * 根据聚合仓ID查询可用的店铺
     *
     * @param shareStoreId 聚合仓ID
     * @return 有效逻辑仓数量
     */
    public Integer selectCpCStoreCountByShareId(Long shareStoreId) {
        if (Objects.isNull(shareStoreId)) {
            return 0;
        }

        return cpCStoreMapper.selectCount(new QueryWrapper<SgCpCStore>().lambda()
                .eq(SgCpCStore::getSgCShareStoreId, shareStoreId)
                .eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y));
    }
}
