package com.burgeon.r3.sg.basic.logic;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.config.SgStorageControlConfig;
import com.burgeon.r3.sg.basic.mapper.SgBMonitorShareStorageDiffMapper;
import com.burgeon.r3.sg.basic.mapper.SgBShareStorageMapper;
import com.burgeon.r3.sg.basic.mapper.SgBSpStorageAllocationFtpMapper;
import com.burgeon.r3.sg.basic.mapper.SgBSpStoragePreoutFtpMapper;
import com.burgeon.r3.sg.basic.mapper.SgCShareStoreMapper;
import com.burgeon.r3.sg.basic.model.AbstractSgStorageUpdateCommonModel;
import com.burgeon.r3.sg.basic.model.SgStorageNegativeCheckModel;
import com.burgeon.r3.sg.basic.model.SgStorageUpdateCommonSsModel;
import com.burgeon.r3.sg.basic.model.SgStorageUpdateControlModel;
import com.burgeon.r3.sg.basic.model.request.SgStorageRedisInitRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageRedisMonitorRequest;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisInitResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisMonitorResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySsResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageUpdateCommonResult;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgBMonitorShareStorageDiff;
import com.burgeon.r3.sg.core.model.table.basic.SgBShareStorage;
import com.burgeon.r3.sg.core.model.table.basic.SgBSpStorageAllocationFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgBSpStoragePreoutFtp;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.core.utils.StorageLogUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * @Description:
 * @Author: chenb
 * @Date: 2019/3/13 14:50
 */
@Component
@Slf4j
public class SgShareStorageLogic implements ISgStorageLogic {

    @Autowired
    private SgBShareStorageMapper sgBShareStorageMapper;

    @Autowired
    private SgBSpStorageAllocationFtpMapper sgBSpStorageAllocationFtpMapper;

    @Autowired
    private SgBSpStoragePreoutFtpMapper sgBSpStoragePreoutFtpMapper;

    @Autowired
    private SgBMonitorShareStorageDiffMapper sgBMonitorShareStorageDiffMapper;

    @Autowired
    private SgStorageControlConfig sgStorageControlConfig;

    @Autowired
    private SgCShareStoreMapper sgCShareStoreMapper;

    /**
     * @param storeSsMap
     * @param skuSsMap
     * @param storageNotExistSsMap
     * @param loginUser
     * @return
     */
    public ValueHolderV14<SgStorageUpdateCommonResult> initStorageSsList(Map<Long, Long> storeSsMap,
                                                                         Map<Long, Long> skuSsMap,
                                                                         Map<String, AbstractSgStorageUpdateCommonModel> storageNotExistSsMap,
                                                                         User loginUser) {

        ValueHolderV14<SgStorageUpdateCommonResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS, "");
        SgStorageUpdateCommonResult commonResult = new SgStorageUpdateCommonResult();
        int countResult = 0;

        //插入逻辑仓库存初始数据
        if (org.apache.commons.collections4.MapUtils.isEmpty(storageNotExistSsMap)) {
            return holder;
        }

        //查询已存在的库存记录
        List<SgBShareStorage> resultList = sgBShareStorageMapper.selectListMaster(
                new QueryWrapper<SgBShareStorage>().lambda().select(SgBShareStorage::getSgCShareStoreId, SgBShareStorage::getPsCSkuId)
                        .in(!CollectionUtils.isEmpty(storeSsMap.values()), SgBShareStorage::getSgCShareStoreId, new ArrayList<>(storeSsMap.values()))
                        .in(!CollectionUtils.isEmpty(skuSsMap.values()), SgBShareStorage::getPsCSkuId, new ArrayList<>(skuSsMap.values()))
        );

        //除去【批量初始化库存】列表中已存在的记录
        if (!CollectionUtils.isEmpty(resultList)) {
            String key = null;
            for (SgBShareStorage storageRecode : resultList) {
                key = storageRecode.getSgCShareStoreId() + SgConstants.SG_CONNECTOR_MARKS_6 + storageRecode.getPsCSkuId();
                if (storageNotExistSsMap.containsKey(key)) {
                    storageNotExistSsMap.remove(key);
                }
            }
        }

        //批量初始化库存
        if (storageNotExistSsMap.size() > 0) {

            holder = initStorageList(new ArrayList<>(storageNotExistSsMap.values()), loginUser);

            if (ResultCode.FAIL == holder.getCode()) {
                throw new NDSException(holder.getMessage());
            }

            countResult = countResult + storageNotExistSsMap.size();

        }

        commonResult.setCountResult(countResult);
        holder.setData(commonResult);

        return holder;

    }

    /**
     * 批量初始化库存
     *
     * @param updateModelList
     * @param loginUser
     * @return ValueHolderV14<SgStorageUpdateCommonResult>
     */
    @Override
    public ValueHolderV14<SgStorageUpdateCommonResult> initStorageList(List<AbstractSgStorageUpdateCommonModel> updateModelList,
                                                                       User loginUser) {

        if (log.isDebugEnabled()) {
            log.debug("Start SgShareStorageLogic.initStorageList. ReceiveParams:updateModelList.size={};",
                    updateModelList.size());
        }

        ValueHolderV14<SgStorageUpdateCommonResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        SgStorageUpdateCommonResult commonResult = new SgStorageUpdateCommonResult();
        List<SgBShareStorage> batchInitList = new ArrayList<>();

        try {

            for (AbstractSgStorageUpdateCommonModel updateModel : updateModelList) {

                SgBShareStorage sgBShareStorage = new SgBShareStorage();
                BeanUtils.copyProperties(updateModel, sgBShareStorage);
                sgBShareStorage.setId(ModelUtil.getSequence(SgConstants.SG_B_SHARE_STORAGE));
                sgBShareStorage.setQtySpPreout(BigDecimal.ZERO);
                sgBShareStorage.setQtySpAllocation(BigDecimal.ZERO);
                sgBShareStorage.setPsCBrandId(-1L);

                batchInitList.add(sgBShareStorage);
            }

            List<List<SgBShareStorage>> insertPageList =
                    StorageUtils.getBaseModelPageList(batchInitList, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);

            //分页批量更新
            for (List<SgBShareStorage> pageList : insertPageList) {

                if (CollectionUtils.isEmpty(pageList)) {
                    continue;
                }

                int insertResult = sgBShareStorageMapper.batchInsertByIgnore(pageList);

//                if (insertResult != pageList.size()) {
//                    log.error("SgShareStorageLogic.initStorageList. 聚合仓批量库存初始化插入失败！insertResult:{}", insertResult);
//                    holder.setCode(ResultCode.FAIL);
//                    holder.setMessage(Resources.getMessage("聚合仓批量库存初始化插入失败！", loginUser.getLocale()));
//                    return holder;
//                }
            }

        } catch (Exception e) {
            String errorMsg = StorageLogUtils.getErrMessage(e, loginUser);
            throw new NDSException(Resources.getMessage("聚合仓批量库存初始化插入失败！", loginUser.getLocale()).concat(errorMsg));
        }

        holder.setData(commonResult);
        holder.setCode(ResultCode.SUCCESS);
        holder.setMessage(Resources.getMessage("聚合仓批量库存初始化插入成功！", loginUser.getLocale()));

        return holder;
    }


    /**
     * @param request
     * @param loginUser
     * @return
     */
    @Override
    public ValueHolderV14<SgStorageRedisInitResult> syncRedisStorage(SgStorageRedisInitRequest request, User loginUser) {

        if (log.isDebugEnabled()) {
            log.debug("Start SgShareStorageLogic.syncRedisStorage. ReceiveParams:updateModel={};", JSONObject.toJSONString(request));
        }

        ValueHolderV14<SgStorageRedisInitResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        CusRedisTemplate<String, String> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();
        DefaultRedisScript<List> redisScript = new DefaultRedisScript<>();
        redisScript.setResultType(List.class);
        String luaPathResource = null;
        List<String> keyList = null;
        List<String> qtyList = null;
        StringBuffer sb = null;
        long totalSuccessNum = 0;

        List<Long> storeIds = request.getShareStoreIds();
        List<Long> proIds = request.getProIds();
        List<Long> skuIds = request.getPsCSkuIds();
        List<String> proEcodes = request.getProEcodes();
        List<String> skuEcodes = request.getSkuEcodes();

        if (CollectionUtils.isEmpty(storeIds)) {

            storeIds = sgBShareStorageMapper.selectStorageStoreIds();

            if (CollectionUtils.isEmpty(storeIds)) {
                holder.setMessage(Resources.getMessage("聚合仓库存中未查询到满足条件的聚合仓！",
                        loginUser.getLocale()));
                return holder;
            }

        }

        for (long storeId : storeIds) {

            //获取满足条件的逻辑仓库存件数
            int totalQtty = sgBShareStorageMapper.selectCount(new QueryWrapper<SgBShareStorage>().lambda()
                    .eq(SgBShareStorage::getSgCShareStoreId, storeId)
                    .in(!CollectionUtils.isEmpty(proIds), SgBShareStorage::getPsCProId, proIds)
                    .in(!CollectionUtils.isEmpty(skuIds), SgBShareStorage::getPsCSkuId, skuIds)
                    .in(!CollectionUtils.isEmpty(proEcodes), SgBShareStorage::getPsCProEcode, proEcodes)
                    .in(!CollectionUtils.isEmpty(skuEcodes), SgBShareStorage::getPsCSkuEcode, skuEcodes)
            );

            //分页处理
            int pageSize = sgStorageControlConfig.getMaxQueryLimit();
            int listSize = totalQtty;
            int page = listSize / pageSize;

            if (listSize % pageSize != 0) {
                page++;
            }

            //分页批量更新
            for (int i = 0; i < page; i++) {

                /** 分页查询 **/
                PageHelper.startPage(i + 1, pageSize);
                List<SgBShareStorage> resultList = sgBShareStorageMapper.selectList(
                        new QueryWrapper<SgBShareStorage>().lambda().select(SgBShareStorage::getSgCShareStoreId,
                                SgBShareStorage::getPsCSkuId, SgBShareStorage::getQtySpPreout, SgBShareStorage::getQtySpAllocation)
                                .eq(SgBShareStorage::getSgCShareStoreId, storeId)
                                .in(!CollectionUtils.isEmpty(proIds), SgBShareStorage::getPsCProId, proIds)
                                .in(!CollectionUtils.isEmpty(skuIds), SgBShareStorage::getPsCSkuId, skuIds)
                                .in(!CollectionUtils.isEmpty(proEcodes), SgBShareStorage::getPsCProEcode, proEcodes)
                                .in(!CollectionUtils.isEmpty(skuEcodes), SgBShareStorage::getPsCSkuEcode, skuEcodes)
                                .orderByAsc(SgBShareStorage::getId)
                );

                keyList = new ArrayList();
                qtyList = new ArrayList();

                if (CollectionUtils.isEmpty(resultList)) {
                    continue;
                }

                for (SgBShareStorage sgBStorage : resultList) {

                    if (StringUtils.isEmpty(luaPathResource)) {
                        luaPathResource = sgBStorage.getInitLuaScriptPath();
                        redisScript.setLocation(new ClassPathResource(luaPathResource));
                    }

                    int partition = (int) (sgBStorage.getPartitionStoreId() % sgStorageControlConfig.getRedisKeyPartitionNum());

                    //LUA Redis键：sg:storage:{店仓ID%100}:店仓ID:SKUID
                    sb = new StringBuffer();
                    sb.append(sgBStorage.getRedisKeyPrefix());
                    sb.append(SgConstants.SG_CONNECTOR_MARKS_1);
                    sb.append(partition);
                    sb.append(SgConstants.SG_CONNECTOR_MARKS_2);
                    sb.append(SgConstants.SG_CONNECTOR_MARKS_4);
                    sb.append(sgBStorage.getMainStoreId());
                    sb.append(SgConstants.SG_CONNECTOR_MARKS_4);
                    sb.append(sgBStorage.getPsCSkuId());
                    keyList.add(sb.toString());

                    //LUA参数:占用数量,在途数量,在库数量
                    qtyList.add(sgBStorage.generateUpdateRedisQty().concat(SgConstants.SG_CONNECTOR_MARKS_5)
                            .concat(String.valueOf(partition)));

                }

                if (log.isDebugEnabled()) {
                    log.debug("SgShareStorageLogic.syncRedisStorage. redisTemplate.execute.start. ReceiveParams:resultList size:{};"
                            , resultList.size());
                }

                //调用逻辑仓库存初始化LUA脚本
                List result = redisMasterTemplate.execute(redisScript, keyList, qtyList.toArray(new String[qtyList.size()]));

                if (log.isDebugEnabled()) {
                    log.debug("SgShareStorageLogic.syncRedisStorage. redisTemplate.execute.end. ReturnResult:result:{};"
                            , JSONObject.toJSONString(result));
                }

                if (!CollectionUtils.isEmpty(result) && (Long) result.get(0) != ResultCode.SUCCESS) {

                    log.error("SgShareStorageLogic.syncRedisStorage. Redis聚合仓库存初始化失败！成功件数:{};"
                            , JSONObject.toJSONString(result.get(1)));

                }

                totalSuccessNum = totalSuccessNum + (Long) result.get(1);

                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("SgShareStorageLogic.syncRedisStorage. error:{}", Throwables.getStackTraceAsString(e));
                }
            }
        }

        holder.setMessage(Resources.getMessage("Redis聚合仓库存初始化成功！",
                loginUser.getLocale(),
                totalSuccessNum));

        log.info("Finish SgShareStorageLogic.syncRedisStorage. ReturnResult:holder={};",
                JSONObject.toJSONString(holder));

        return holder;
    }

    /**
     * 获取Redis集群分区店仓ID
     *
     * @return
     */
    @Override
    public List<Long> getPartitionStoreIds() {
        return sgBShareStorageMapper.selectStorageStoreIds();
    }

    @Override
    public List<Long> getMonitorStoreIds(List<Long> storeIds) {
        return sgCShareStoreMapper.selectStoreByIds(storeIds);
    }

    @Override
    public ValueHolderV14<SgStorageRedisMonitorResult> monitorRedisStorage(SgStorageRedisMonitorRequest request, User loginUser) {

        ValueHolderV14<SgStorageRedisMonitorResult> holder = new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        Map<String, SgStorageRedisQuerySsResult> redisStorageMap;
        SgBMonitorShareStorageDiff diff;
        List<SgBShareStorage> dbSgBStorageResult;
        List<String> storeRedisKeysList;
        Map<String, Object> storeRedisKeys;
        List<Long> skuIds;
        String redisManagementKey = null;
        String redisStorageKey = null;

        SgStorageRedisQuerySsResult redisResult;
        int partition = 0;
        long totalSuccessNum = 0;
        long totalErrorNum = 0;

        CusRedisTemplate<String, String> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();
        DefaultRedisScript<List> redisScript = new DefaultRedisScript<>();
        redisScript.setLocation(new ClassPathResource("lua/QueryRedisSsStorage.lua"));
        redisScript.setResultType(List.class);

        for (Long storeId : request.getMainStoreIds()) {

            partition = (int) (storeId % sgStorageControlConfig.getRedisKeyPartitionNum());

            redisManagementKey = SgConstants.REDIS_KEY_MANAGEMENT_SS_STORAGE.concat(SgConstants.SG_CONNECTOR_MARKS_1)
                    .concat(String.valueOf(partition)).concat(SgConstants.SG_CONNECTOR_MARKS_2);

            redisStorageKey = SgConstants.REDIS_KEY_PREFIX_SS_STORAGE.concat(SgConstants.SG_CONNECTOR_MARKS_1)
                    .concat(String.valueOf(partition)).concat(SgConstants.SG_CONNECTOR_MARKS_2)
                    .concat(SgConstants.SG_CONNECTOR_MARKS_4).concat(String.valueOf(storeId))
                    .concat(SgConstants.SG_CONNECTOR_MARKS_4);

            storeRedisKeys = StorageUtils.scanRedisHashKeys(redisManagementKey, redisStorageKey, 1000);

            if (storeRedisKeys == null || storeRedisKeys.isEmpty()) {

                if (log.isDebugEnabled()) {
                    log.debug("SgShareStorageLogic.monitorRedisStorage. info:Redis聚合仓库存记录为空。聚合仓ID:{}", storeId);
                }

                if (request.getIsCheckAllStores()) {
                    createSgBMonitorStorageDiff(null, null, storeId, null,
                            SgConstants.IS_NO, SgConstants.MONITOR_ERROR_TYPE_REDIS_ISNULL, "Redis库存中找不到该聚合仓的DB库存记录！",
                            request.getBatchNo(), loginUser);
                    totalErrorNum++;
                } else {
                    totalSuccessNum++;
                }

                continue;
            }

            storeRedisKeysList = new ArrayList(storeRedisKeys.keySet());

            //批量更新单据明细分页
            int pageSize = sgStorageControlConfig.getMaxQueryLimit();
            int listSize = storeRedisKeysList.size();
            int page = listSize / pageSize;

            if (listSize % pageSize != 0) {
                page++;
            }

            //分页批量更新
            for (int i = 0; i < page; i++) {

                List<String> keyList = storeRedisKeysList.subList(i * pageSize,
                        (((i + 1) * pageSize > listSize ? listSize : pageSize * (i + 1))));

                skuIds = new ArrayList<>();
                redisStorageMap = new HashMap<>();

                for (String redisKey : keyList) {
                    skuIds.add(Long.valueOf(redisKey.split(":")[4]));
                }

                dbSgBStorageResult = sgBShareStorageMapper.selectList(
                        new QueryWrapper<SgBShareStorage>().lambda()
                                .eq(SgBShareStorage::getSgCShareStoreId, storeId)
                                .in(!CollectionUtils.isEmpty(skuIds), SgBShareStorage::getPsCSkuId, skuIds));

                List result = redisMasterTemplate.execute(redisScript, keyList);

                if (!CollectionUtils.isEmpty(result) &&
                        ResultCode.SUCCESS == Integer.valueOf((String) result.get(0))) {

                    List<List> storageResultList = (ArrayList) result.get(1);

                    if (!CollectionUtils.isEmpty(storageResultList)) {

                        for (List storageResult : storageResultList) {

                            if (!CollectionUtils.isEmpty(storageResult)) {

                                SgStorageRedisQuerySsResult queryResult = new SgStorageRedisQuerySsResult();

                                queryResult.setQtySpPreout(new BigDecimal((String) storageResult.get(0)));
                                queryResult.setQtySpAllocation(new BigDecimal((String) storageResult.get(1)));
                                queryResult.setQtySpAvailable(queryResult.getQtySpAllocation().subtract(
                                        queryResult.getQtySpPreout()));

                                redisStorageMap.put(((String) storageResult.get(2)).split(":")[4],
                                        queryResult);

                            }

                        }

                    }

                }

                for (SgBShareStorage dbSgBStorage : dbSgBStorageResult) {

                    BigDecimal dbQtySpPreout = Optional.ofNullable(dbSgBStorage.getQtySpPreout()).orElse(BigDecimal.ZERO);
                    BigDecimal dbQtySpAllocation = Optional.ofNullable(dbSgBStorage.getQtySpAllocation()).orElse(BigDecimal.ZERO);
                    BigDecimal dbQtyAvaliable = dbQtySpAllocation.subtract(dbQtySpPreout);

                    redisResult = redisStorageMap.get(dbSgBStorage.getPsCSkuId().toString());

                    if (redisResult == null) {

                        log.error("SgShareStorageLogic.monitorRedisStorage. error:" +
                                        "Redis聚合仓库存记录不存在。聚合仓ID:{},商品条码ID:{};",
                                dbSgBStorage.getSgCShareStoreId(), dbSgBStorage.getPsCSkuId());

                        createSgBMonitorStorageDiff(dbSgBStorage, null, dbSgBStorage.getSgCShareStoreId(), dbSgBStorage.getPsCSkuId(),
                                SgConstants.IS_YES, SgConstants.MONITOR_ERROR_TYPE_REDIS_AND_DB_DIFF, "Redis库存中找不到该条码的DB库存记录！",
                                request.getBatchNo(), loginUser);

                        totalErrorNum++;

                    } else if (redisResult.getQtySpPreout().compareTo(dbQtySpPreout) != 0 ||
                            redisResult.getQtySpAllocation().compareTo(dbQtySpAllocation) != 0 ||
                            redisResult.getQtySpAvailable().compareTo(dbQtyAvaliable) != 0) {

                        log.error("SgShareStorageLogic.monitorRedisStorage. error:Redis聚合仓库存数据不一致。" +
                                        "聚合仓ID:{},商品条码ID:{},DB共享占用数量:{},DB共享分配数量:{},DB共享分配可用量:{}," +
                                        "Redis共享占用数量:{},Redis共享分配数量:{},Redis共享分配可用量:{};",
                                dbSgBStorage.getSgCShareStoreId(), dbSgBStorage.getPsCSkuId(),
                                dbQtySpPreout, dbQtySpAllocation, dbQtyAvaliable,
                                redisResult.getQtySpPreout(), redisResult.getQtySpAllocation(),
                                redisResult.getQtySpAvailable());

                        createSgBMonitorStorageDiff(dbSgBStorage, redisResult, dbSgBStorage.getSgCShareStoreId(), dbSgBStorage.getPsCSkuId(),
                                SgConstants.IS_YES, SgConstants.MONITOR_ERROR_TYPE_REDIS_AND_DB_DIFF, "Redis库存和DB库存存在差异！",
                                request.getBatchNo(), loginUser);

                        totalErrorNum++;

                    } else if (sgStorageControlConfig.getIsOpenScanNegativeStorage() && (
                            dbQtySpPreout.compareTo(BigDecimal.ZERO) < 0 ||
                                    dbQtySpAllocation.compareTo(BigDecimal.ZERO) < 0 ||
                                    dbQtyAvaliable.compareTo(BigDecimal.ZERO) < 0)) {

                        log.error("SgShareStorageLogic.monitorRedisStorage. error:Redis聚合仓库存存在负库存。" +
                                        "聚合仓ID:{},商品条码ID:{},DB共享占用数量:{},DB共享分配数量:{},DB共享分配可用量:{}," +
                                        "Redis共享占用数量:{},Redis共享分配数量:{},Redis共享分配可用量:{};",
                                dbSgBStorage.getSgCShareStoreId(), dbSgBStorage.getPsCSkuId(),
                                dbQtySpPreout, dbQtySpAllocation, dbQtyAvaliable,
                                redisResult.getQtySpPreout(), redisResult.getQtySpAllocation(),
                                redisResult.getQtySpAvailable());

                        createSgBMonitorStorageDiff(dbSgBStorage, redisResult, dbSgBStorage.getSgCShareStoreId(), dbSgBStorage.getPsCSkuId(),
                                SgConstants.IS_NO, SgConstants.MONITOR_ERROR_TYPE_QTY_IS_NEGATIVE, "Redis库存和DB库存存在负库存！",
                                request.getBatchNo(), loginUser);

                        totalErrorNum++;
                    } else {
                        totalSuccessNum++;
                    }

                    redisStorageMap.remove(dbSgBStorage.getPsCSkuId().toString());

                }

                if (MapUtils.isEmpty(redisStorageMap)) {
                    continue;
                }

                for (String emptySkuId : redisStorageMap.keySet()) {

                    createSgBMonitorStorageDiff(null, null, storeId, Long.valueOf(emptySkuId),
                            SgConstants.IS_YES, SgConstants.MONITOR_ERROR_TYPE_DB_ISNULL, "DB库存中找不到该条码的Redis库存记录！",
                            request.getBatchNo(), loginUser);

                    if (request.getIsDelRedisKey()) {

                        if (log.isDebugEnabled()) {
                            log.debug("SgShareStorageLogic.monitorRedisStorage. info :" +
                                            "删除DB聚合仓库存不存在记录。聚合仓ID:{},商品条码ID:{};",
                                    storeId, emptySkuId);
                        }

                        redisMasterTemplate.delete(redisStorageKey.concat(String.valueOf(emptySkuId)));
                        redisMasterTemplate.opsForHash().delete(redisManagementKey, redisStorageKey.concat(String.valueOf(emptySkuId)));

                    } else {

                        log.error("SgShareStorageLogic.monitorRedisStorage. error :" +
                                        "DB聚合仓库存记录不存在。聚合仓ID:{},商品条码ID:{};",
                                storeId, emptySkuId);
                        totalErrorNum++;

                    }
                }
                redisStorageMap.clear();
            }
            storeRedisKeys.clear();
        }

        SgStorageRedisMonitorResult monitorResult = new SgStorageRedisMonitorResult();
        monitorResult.setTotalSuccessNum(totalSuccessNum);
        monitorResult.setTotalErrorNum(totalErrorNum);
        holder.setData(monitorResult);

        if (log.isDebugEnabled()) {
            log.debug("Finish SgShareStorageLogic.monitorRedisStorage. ReturnResult:holder={}; batchNo:{}",
                    JSONObject.toJSONString(holder), request.getBatchNo());
        }

        return holder;
    }

    /**
     * 生成库存监控差异
     *
     * @param dbSgBStorage
     * @param redisResult
     * @param sgCShareStoreId
     * @param psCSkuId
     * @param isDiff
     * @param errorType
     * @param message
     * @param batchNo
     * @param loginUser
     */
    private void createSgBMonitorStorageDiff(SgBShareStorage dbSgBStorage,
                                             SgStorageRedisQuerySsResult redisResult,
                                             Long sgCShareStoreId,
                                             Long psCSkuId,
                                             Integer isDiff,
                                             String errorType,
                                             String message,
                                             String batchNo,
                                             User loginUser) {

        SgBMonitorShareStorageDiff diff = new SgBMonitorShareStorageDiff();

        if (dbSgBStorage != null) {
            BeanUtils.copyProperties(dbSgBStorage, diff);
        }

        diff.setId(ModelUtil.getSequence(SgConstants.SG_B_MONITOR_SHARE_STORAGE_DIFF));
        diff.setSgCShareStoreId(sgCShareStoreId);

        if (psCSkuId != null) {
            diff.setPsCSkuId(psCSkuId);
        }

        if (redisResult != null) {
            diff.setQtySpRedisPreout(redisResult.getQtySpPreout());
            diff.setQtySpRedisAllocation(redisResult.getQtySpAllocation());
        }

        diff.setIsDiff(isDiff);
        diff.markNegative();
        diff.setErrorType(errorType);
        diff.setMessage(message);
        diff.setBatchNo(batchNo);
        StorageUtils.setBModelDefalutData(diff, loginUser);
        diff.setOwnerename(loginUser.getEname());
        diff.setModifierename(loginUser.getEname());

        sgBMonitorShareStorageDiffMapper.insert(diff);

    }

    /**
     * 更新库存
     *
     * @param updateModel
     * @param loginUser
     * @return ValueHolderV14<SgStorageUpdateCommonResult>
     */
    public ValueHolderV14<SgStorageUpdateCommonResult> updateStorageChange(SgStorageUpdateCommonSsModel updateModel,
                                                                           User loginUser) {

        Date systemDate = new Date();
        ValueHolderV14<SgStorageUpdateCommonResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        SgStorageUpdateCommonResult commonResult = new SgStorageUpdateCommonResult();
        SgStorageUpdateControlModel controlModel = updateModel.getUpdateControlModel();

        updateModel.setModifierid(loginUser.getId() == null ? null : loginUser.getId().longValue());
        updateModel.setModifierename(loginUser.getEname());
        updateModel.setModifiername(loginUser.getName());
        updateModel.setModifieddate(systemDate);

        try {

            //设置负库存控制
            SgStorageNegativeCheckModel checkModel = StorageBasicUtils.getNegativeCheckModel(updateModel);

            int updateResult = sgBShareStorageMapper.updateQtyChange(updateModel, checkModel);

            //在库不允许负库存 并且 更新0条记录的情况下
            if ((!controlModel.isNegativeSpPreout() ||
                    !controlModel.isNegativeSpAllocation())
                    && updateResult <= 0) {

                holder.setCode(ResultCode.FAIL);

                log.info("SgShareStorageLogic.updateStorageChange. 当前库存变动明细库存不足！！单据编号:{}, 聚合仓ID:{}, " +
                                "商品条码ID:{}, 共享占用变动数量:{}, 共享分配变动数量:{}",
                        updateModel.getBillNo(), updateModel.getSgCShareStoreId(),
                        updateModel.getMainSkuId(), updateModel.getQtySpPreoutChange(),
                        updateModel.getQtySpAllocationChange());

                List<AbstractSgStorageUpdateCommonModel> outStockItemList = new ArrayList<>();
                outStockItemList.add(updateModel);
                commonResult.setOutStockItemList(outStockItemList);

                holder.setData(commonResult);
                holder.setMessage(Resources.getMessage("聚合仓库存不足！",
                        loginUser.getLocale(), updateModel.getSgCShareStoreEcode(), updateModel.getPsCSkuEcode(),
                        updateModel.getQtySpPreoutChange(),
                        updateModel.getQtySpAllocationChange()));
                return holder;

            }

            commonResult.setCountResult(updateResult);

            List<SgBShareStorage> selectResult = sgBShareStorageMapper.selectList(
                    new QueryWrapper<SgBShareStorage>().lambda()
                            .eq(SgBShareStorage::getSgCShareStoreId, updateModel.getSgCShareStoreId())
                            .eq(SgBShareStorage::getPsCSkuId, updateModel.getMainSkuId()));

            if (CollectionUtils.isNotEmpty(selectResult)) {

                BigDecimal qtySpPreoutEnd = selectResult.get(0).getQtySpPreout();
                BigDecimal qtySpAllocationEnd = selectResult.get(0).getQtySpAllocation();
                //本版本的期初数量暂时通过期末数量为基准计算
                commonResult.setQtyPreoutBegin(qtySpPreoutEnd.subtract(updateModel.getQtySpPreoutChange()));
                commonResult.setQtyPreoutEnd(qtySpPreoutEnd);
                commonResult.setQtySpAllocationBegin(qtySpAllocationEnd.subtract(updateModel.getQtySpAllocationChange()));
                commonResult.setQtySpAllocationEnd(qtySpAllocationEnd);
            }

        } catch (Exception e) {
            String errorMsg = StorageLogUtils.getErrMessage(e, loginUser);
            throw new NDSException(Resources.getMessage("聚合仓库存数量更新失败！", loginUser.getLocale()).concat(errorMsg));
        }

        holder.setData(commonResult);
        holder.setCode(ResultCode.SUCCESS);
        holder.setMessage(Resources.getMessage("聚合仓库存数量更新成功！", loginUser.getLocale()));

        return holder;
    }

    /**
     * 批量记录库存共享占用库存变动流水
     *
     * @param updateModelList
     * @param loginUser
     * @return ValueHolderV14<SgStorageUpdateCommonResult>
     */
    public ValueHolderV14<SgStorageUpdateCommonResult> insertStoragePreoutFtpList(List<AbstractSgStorageUpdateCommonModel> updateModelList,
                                                                                  User loginUser) {

        ValueHolderV14<SgStorageUpdateCommonResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS, "");
        SgStorageUpdateCommonResult commonResult = new SgStorageUpdateCommonResult();
        List<SgBSpStoragePreoutFtp> batchInsertList = new ArrayList<>();

        try {

            for (AbstractSgStorageUpdateCommonModel abstractModel : updateModelList) {

                SgStorageUpdateCommonSsModel updateModel = (SgStorageUpdateCommonSsModel) abstractModel;

                if (BigDecimal.ZERO.compareTo(updateModel.getQtySpPreoutChange()) != 0) {

                    SgBSpStoragePreoutFtp preoutUpdateModel = new SgBSpStoragePreoutFtp();

                    BeanUtils.copyProperties(updateModel, preoutUpdateModel);
                    preoutUpdateModel.setId(ModelUtil.getSequence(SgConstants.SG_B_SP_STORAGE_PREOUT_FTP));
                    preoutUpdateModel.setQtyChange(updateModel.getQtySpPreoutChange());
                    preoutUpdateModel.setQtyBegin(updateModel.getQtyPreoutBegin());
                    preoutUpdateModel.setQtyEnd(updateModel.getQtyPreoutEnd());

                    if (preoutUpdateModel.getOwnerid() == null) {
                        StorageUtils.setBModelDefalutData(preoutUpdateModel, loginUser);
                        preoutUpdateModel.setOwnerename(loginUser.getEname());
                        preoutUpdateModel.setModifierename(loginUser.getEname());
                    }

                    batchInsertList.add(preoutUpdateModel);
                }

            }

            //流水为空时，直接返回
            if (CollectionUtils.isEmpty(batchInsertList)) {
                return holder;
            }

            List<List<SgBSpStoragePreoutFtp>> insertPageList =
                    StorageUtils.getBaseModelPageList(batchInsertList, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);

            for (List<SgBSpStoragePreoutFtp> pageList : insertPageList) {

                int insertResult = sgBSpStoragePreoutFtpMapper.batchInsert(pageList);

                if (insertResult != pageList.size()) {
                    log.error("SgShareStorageLogic.insertStoragePreoutFtpList 聚合仓批量库存占用变动流水插入失败！insertResult:{}",
                            insertResult);
                    holder.setCode(ResultCode.FAIL);
                    holder.setMessage(Resources.getMessage("聚合仓批量库存占用变动流水插入失败！",
                            loginUser.getLocale()));
                    return holder;
                }

            }

        } catch (Exception e) {
            String errorMsg = StorageLogUtils.getErrMessage(e, loginUser);
            throw new NDSException(Resources.getMessage("聚合仓批量库存占用变动流水插入失败！",
                    loginUser.getLocale()).concat(errorMsg));
        }

        holder.setData(commonResult);
        holder.setCode(ResultCode.SUCCESS);
        holder.setMessage(Resources.getMessage("聚合仓批量库存共享占用变动流水插入成功！", loginUser.getLocale()));

        return holder;
    }

    /**
     * 批量记录库存在库变动流水
     *
     * @param updateModelList
     * @param loginUser
     * @return ValueHolderV14<SgStorageUpdateCommonResult>
     */
    public ValueHolderV14<SgStorageUpdateCommonResult> insertStorageAllocationFtpList(List<AbstractSgStorageUpdateCommonModel> updateModelList,
                                                                                      User loginUser) {

        ValueHolderV14<SgStorageUpdateCommonResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS, "");
        SgStorageUpdateCommonResult commonResult = new SgStorageUpdateCommonResult();
        List<SgBSpStorageAllocationFtp> batchInsertList = new ArrayList<>();

        try {

            for (AbstractSgStorageUpdateCommonModel abstractModel : updateModelList) {

                SgStorageUpdateCommonSsModel updateModel = (SgStorageUpdateCommonSsModel) abstractModel;

                if (BigDecimal.ZERO.compareTo(updateModel.getQtySpAllocationChange()) != 0) {

                    SgBSpStorageAllocationFtp allocationUpdateModel = new SgBSpStorageAllocationFtp();

                    BeanUtils.copyProperties(updateModel, allocationUpdateModel);
                    allocationUpdateModel.setId(ModelUtil.getSequence(SgConstants.SG_B_SP_STORAGE_ALLOCATION_FTP));
                    allocationUpdateModel.setQtyChange(updateModel.getQtySpAllocationChange());
                    allocationUpdateModel.setQtyBegin(updateModel.getQtySpAllocationBegin());
                    allocationUpdateModel.setQtyEnd(updateModel.getQtySpAllocationEnd());

                    if (allocationUpdateModel.getOwnerid() == null) {
                        StorageUtils.setBModelDefalutData(allocationUpdateModel, loginUser);
                        allocationUpdateModel.setOwnerename(loginUser.getEname());
                        allocationUpdateModel.setModifierename(loginUser.getEname());
                    }

                    batchInsertList.add(allocationUpdateModel);
                }
            }

            //流水为空时，直接返回
            if (CollectionUtils.isEmpty(batchInsertList)) {
                return holder;
            }

            List<List<SgBSpStorageAllocationFtp>> insertPageList =
                    StorageUtils.getBaseModelPageList(batchInsertList, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);

            for (List<SgBSpStorageAllocationFtp> pageList : insertPageList) {

                int insertResult = sgBSpStorageAllocationFtpMapper.batchInsert(pageList);

                if (insertResult != pageList.size()) {
                    log.error("SgShareStorageLogic.insertStorageAllocationFtpList 聚合仓批量库存分配变动流水插入失败！insertResult:{}",
                            insertResult);
                    holder.setCode(ResultCode.FAIL);
                    holder.setMessage(Resources.getMessage("聚合仓批量库存分配变动流水插入失败！",
                            loginUser.getLocale()));
                    return holder;
                }

            }
        } catch (Exception e) {
            String errorMsg = StorageLogUtils.getErrMessage(e, loginUser);
            throw new NDSException(Resources.getMessage("聚合仓批量库存分配变动流水插入失败！",
                    loginUser.getLocale()).concat(errorMsg));
        }

        holder.setData(commonResult);
        holder.setCode(ResultCode.SUCCESS);
        holder.setMessage(Resources.getMessage("聚合仓批量库存分配变动流水插入成功！", loginUser.getLocale()));

        return holder;
    }


}
