package com.burgeon.r3.sg.basic.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.burgeon.r3.sg.core.model.table.basic.SgBSaStoragePreoutFtp;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface SgBSaStoragePreoutFtpMapper extends ExtentionMapper<SgBSaStoragePreoutFtp> {

    @Select("/*FORCE_MASTER*/ select * from sg_b_sa_storage_preout_ftp ${ew.customSqlSegment}")
    List<SgBSaStoragePreoutFtp> selectListMaster(@Param(Constants.WRAPPER) Wrapper wrapper);

    @Update("<script>" +
            "UPDATE sg_b_sa_storage_preout_ftp p " +
            "<set>" +
            " p.bill_id = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            "<if test = 'param.billId != null'>" +
            " when #{param.id} then #{param.billId}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.bill_no = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            "<if test = 'param.billNo != null'>" +
            " when #{param.id} then #{param.billNo}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.bill_item_id = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            "<if test = 'param.billItemId != null'>" +
            " when #{param.id} then #{param.billItemId}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.source_bill_id = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            "<if test = 'param.sourceBillId != null'>" +
            " when #{param.id} then #{param.sourceBillId}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.source_bill_no = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            "<if test = 'param.sourceBillNo != null'>" +
            " when #{param.id} then #{param.sourceBillNo}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "</set>" +
            "<where>" +
            " p.id in " +
            " <foreach collection='params' item='param' open='(' separator=',' close=')'>" +
            " #{param.id} " +
            "</foreach>" +
            "</where>" +
            "</script>")
    Integer batchUpdateById(@Param("params") List<SgBSaStoragePreoutFtp> params);
}