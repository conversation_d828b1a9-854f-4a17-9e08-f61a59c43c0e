package com.burgeon.r3.sg.basic.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.mapper.SgBFreezeStorageMapper;
import com.burgeon.r3.sg.basic.mapper.SgBStorageFreezePreoutFtpMapper;
import com.burgeon.r3.sg.basic.mapper.SgBStorageMapper;
import com.burgeon.r3.sg.basic.model.*;
import com.burgeon.r3.sg.basic.model.request.SgStorageRedisRollbackRequest;
import com.burgeon.r3.sg.basic.model.result.SgStorageBillUpdateResult;
import com.burgeon.r3.sg.basic.services.SgStorageRedisBillUpdateService;
import com.burgeon.r3.sg.core.common.R3ParamConstants;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorageFreezePreoutFtp;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: chenb
 * @Date: 2019/3/14 14:30
 */
@Slf4j
public class StorageBasicUtils {

    /**
     * 批量分页查询明细
     *
     * @param queryWrapper 查詢构造器
     * @param mapper       mapper
     */
    public static <T extends BaseModel> List<T> batchQueryItems(LambdaQueryWrapper<T> queryWrapper,
                                                                ExtentionMapper<T> mapper) {
        List<T> items = Lists.newArrayList();
        Integer itemCount = mapper.selectCount(queryWrapper);
        if (itemCount != null && itemCount > 1000) {
            int pageSize = 1000;
            int page = itemCount / pageSize;
            if (itemCount % pageSize != 0) {
                page++;
            }
            for (int i = 0; i < page; i++) {
                PageHelper.startPage(i + 1, pageSize);
                List<T> pageItems = mapper.selectList(queryWrapper);
                PageInfo<T> pageInfo = new PageInfo<>(pageItems);
                items.addAll(pageInfo.getList());
            }
        } else if (itemCount != null && itemCount != 0) {
            items = mapper.selectList(queryWrapper);
        }
        return items;
    }

    /**
     * 批量分页查询明细 - 默认最大限制100W
     *
     * @param queryWrapper 查詢构造器
     * @param mapper       mapper
     */
    public static <T extends BaseModel> List<T> batchQueryByCondition(QueryWrapper<T> queryWrapper,
                                                                      ExtentionMapper<T> mapper, int pageSize) {
        List<T> items = Lists.newArrayList();
        for (int i = 0; i < 1000; i++) {
            PageHelper.startPage(i + 1, pageSize);
            List<T> pageItems = mapper.selectList(queryWrapper);
            PageInfo<T> pageInfo = new PageInfo<>(pageItems);
            List<T> pageInfoList = pageInfo.getList();
            if (CollectionUtils.isNotEmpty(pageInfoList)) {
                items.addAll(pageInfoList);
            }
            if (CollectionUtils.isEmpty(pageInfoList) || pageInfoList.size() < pageSize) {
                break;
            }
        }
        return items;
    }

    /**
     * 分批获取商品信息
     */
    public static Map<String, PsCProSkuResult> getSkus(List<String> skus, int pageSize) {

        if (CollectionUtils.isEmpty(skus)) {
            return null;
        }

        List<List<String>> pageSkuCodeList = StorageUtils.getPageList(skus, pageSize);
        Map<String, PsCProSkuResult> queryAllResult = new HashMap<>(skus.size());
        BasicPsQueryService basicPsQueryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
        for (List<String> skuSubList : pageSkuCodeList) {
            if (CollectionUtils.isNotEmpty(skuSubList)) {
                SkuInfoQueryRequest skuQueryRequest = new SkuInfoQueryRequest();
                skuQueryRequest.setSkuEcodeList(skuSubList);
                Map<String, PsCProSkuResult> queryResult = basicPsQueryService.getSkuInfoByEcode(skuQueryRequest);
                if (MapUtils.isNotEmpty(queryResult)) {
                    queryAllResult.putAll(queryResult);
                } else {
                    AssertUtils.logAndThrow("补充条码信息查询结果为空!skuQueryRequest:" + JSONObject.toJSONString(skuQueryRequest));
                }
            }
        }

        return queryAllResult;
    }

    /**
     * 分批获取商品信息
     */
    public static Map<Long, PsCProSkuResult> getSkusById(List<Long> skus, int pageSize) {

        if (CollectionUtils.isEmpty(skus)) {
            return null;
        }

        List<List<Long>> pageSkuCodeList = StorageUtils.getPageList(skus, pageSize);
        Map<Long, PsCProSkuResult> queryAllResult = new HashMap<>(skus.size());
        BasicPsQueryService basicPsQueryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
        for (List<Long> skuSubList : pageSkuCodeList) {
            if (CollectionUtils.isNotEmpty(skuSubList)) {
                SkuInfoQueryRequest skuQueryRequest = new SkuInfoQueryRequest();
                skuQueryRequest.setSkuIdList(skuSubList);
                Map<Long, PsCProSkuResult> queryResult = basicPsQueryService.getSkuInfo(skuQueryRequest);
                if (MapUtils.isNotEmpty(queryResult)) {
                    queryAllResult.putAll(queryResult);
                } else {
                    AssertUtils.logAndThrow("补充条码信息查询结果为空!skuQueryRequest:" + JSONObject.toJSONString(skuQueryRequest));
                }
            }
        }

        return queryAllResult;
    }

    /**
     * 根据负库存控制请求数据转换成库存更新时的SQL控制参数(逻辑库存)
     *
     * @param updateModel
     * @return
     */
    public static SgStorageNegativeCheckModel getNegativeCheckModel(SgStorageUpdateCommonLsModel updateModel) {

        SgStorageNegativeCheckModel checkModel = new SgStorageNegativeCheckModel();
        SgStorageUpdateControlModel controlModel = updateModel.getUpdateControlModel();

        checkModel.setCheckUnsharedPreoutNegative(!controlModel.isNegativeUnsharedPreout() &&
                (updateModel.getQtyUnsharedPreoutChange().compareTo(BigDecimal.ZERO) < 0 ? true : false));
        checkModel.setCheckSharedPreoutNegative(!controlModel.isNegativeSharedPreout() &&
                (updateModel.getQtySharedPreoutChange().compareTo(BigDecimal.ZERO) < 0 ? true : false));
        checkModel.setCheckPreinNegative(!controlModel.isNegativePrein() &&
                (updateModel.getQtyPreinChange().compareTo(BigDecimal.ZERO) < 0 ? true : false));
        checkModel.setCheckFreezeNegative(!controlModel.isNegativeFreeze() &&
                (updateModel.getQtyFreezeChange().compareTo(BigDecimal.ZERO) < 0 ? true : false));
        checkModel.setCheckChangeNegative(!controlModel.isNegativeStorage() &&
                (updateModel.getQtyStorageChange().compareTo(BigDecimal.ZERO) < 0 ? true : false));
        checkModel.setCheckAvailableNegative(!controlModel.isNegativeAvailable() && (
                (updateModel.getQtySharedPreoutChange().compareTo(BigDecimal.ZERO) > 0 ||
                        updateModel.getQtyUnsharedPreoutChange().compareTo(BigDecimal.ZERO) > 0 ||
                        updateModel.getQtyFreezeChange().compareTo(BigDecimal.ZERO) > 0 ||
                        updateModel.getQtyStorageChange().compareTo(BigDecimal.ZERO) < 0) ? true : false));

        return checkModel;

    }

    /**
     * 根据负库存控制请求数据转换成库存更新时的SQL控制参数(锁定库存)
     *
     * @param updateModel
     * @return
     */
    public static SgStorageNegativeCheckModel getNegativeCheckModel(SgStorageUpdateCommonCfModel updateModel) {

        SgStorageNegativeCheckModel checkModel = new SgStorageNegativeCheckModel();
        SgStorageUpdateControlModel controlModel = updateModel.getUpdateControlModel();

        checkModel.setCheckCfSoldNegative(!controlModel.isNegativeCfSold() &&
                (updateModel.getQtySoldChange().compareTo(BigDecimal.ZERO) < 0 ? true : false));
        checkModel.setCheckCfChangeNegative(!controlModel.isNegativeCfStorage() &&
                (updateModel.getQtyStorageChange().compareTo(BigDecimal.ZERO) < 0 ? true : false));
        checkModel.setCheckCfAvailableNegative(!controlModel.isNegativeCfAvailable() && (
                (updateModel.getQtySoldChange().compareTo(BigDecimal.ZERO) > 0 ||
                        updateModel.getQtyStorageChange().compareTo(BigDecimal.ZERO) < 0) ? true : false));

        return checkModel;

    }

    /**
     * 根据负库存控制请求数据转换成库存更新时的SQL控制参数(聚合库存)
     *
     * @param updateModel
     * @return
     */
    public static SgStorageNegativeCheckModel getNegativeCheckModel(SgStorageUpdateCommonSsModel updateModel) {

        SgStorageNegativeCheckModel checkModel = new SgStorageNegativeCheckModel();
        SgStorageUpdateControlModel controlModel = updateModel.getUpdateControlModel();

        checkModel.setCheckSpPreoutNegative(!controlModel.isNegativeSpPreout() &&
                (updateModel.getQtySpPreoutChange().compareTo(BigDecimal.ZERO) < 0 ? true : false));
        checkModel.setCheckSpAllocationNegative(!controlModel.isNegativeSpAllocation() &&
                (updateModel.getQtySpAllocationChange().compareTo(BigDecimal.ZERO) < 0 ? true : false));
        return checkModel;

    }

    /**
     * 根据负库存控制请求数据转换成库存更新时的SQL控制参数(配销库存)
     *
     * @param updateModel
     * @return
     */
    public static SgStorageNegativeCheckModel getNegativeCheckModel(SgStorageUpdateCommonSaModel updateModel) {

        SgStorageNegativeCheckModel checkModel = new SgStorageNegativeCheckModel();
        SgStorageUpdateControlModel controlModel = updateModel.getUpdateControlModel();

        checkModel.setCheckSaFixedNegative(!controlModel.isNegativeSaFixed() &&
                (updateModel.getQtyFixedAvailableChange().compareTo(BigDecimal.ZERO) < 0 ? true : false));
        checkModel.setCheckSaPreoutNegative(!controlModel.isNegativeSaPreout() &&
                (updateModel.getQtyPreoutChange().compareTo(BigDecimal.ZERO) < 0 ? true : false));
        checkModel.setCheckSaChangeNegative(!controlModel.isNegativeSaStorage() &&
                (updateModel.getQtyStorageChange().compareTo(BigDecimal.ZERO) < 0 ? true : false));
        checkModel.setCheckSaAvailableNegative(!controlModel.isNegativeSaAvailable() && (
                (updateModel.getQtyPreoutChange().compareTo(BigDecimal.ZERO) > 0 ||
                        updateModel.getQtyFixedAvailableChange().compareTo(BigDecimal.ZERO) > 0 ||
                        updateModel.getQtyStorageChange().compareTo(BigDecimal.ZERO) < 0) ? true : false));
        return checkModel;

    }

    /**
     * 根据负库存控制请求数据转换成库存更新时的SQL控制参数(逻辑库存)
     *
     * @param updateModel
     * @return
     */
    public static SgStorageNegativeCheckModel getNegativeCheckModel(SgStorageUpdateCommonLsBatchModel updateModel) {

        SgStorageNegativeCheckModel checkModel = new SgStorageNegativeCheckModel();
        SgStorageUpdateControlModel controlModel = updateModel.getUpdateControlModel();
        checkModel.setCheckChangeNegative(!controlModel.isNegativeStorage() &&
                (updateModel.getQtyStorageChange().compareTo(BigDecimal.ZERO) < 0 ? true : false));
        return checkModel;

    }

    /**
     * 分页批量插入
     *
     * @param mapper
     * @param baseModels
     * @param pageSize
     * @param errMsg
     * @param loginUser
     */
    public static <T extends ExtentionMapper> void batchInsertList(T mapper, List baseModels,
                                                                   Integer pageSize, String errMsg,
                                                                   User loginUser) {
        int resultNum = 0;

        if (CollectionUtils.isEmpty(baseModels)) {
            return;
        }

        List<List> baseModelPageList = StorageUtils.getBaseModelPageList(baseModels, pageSize);

        for (List baseModel : baseModelPageList) {

            resultNum = mapper.batchInsert(baseModel);

            if (resultNum != baseModel.size()) {
                AssertUtils.logAndThrow(Resources.getMessage(errMsg, loginUser.getLocale()).concat(
                        Resources.getMessage("批量插入记录失败!", loginUser.getLocale())
                ));
            }
        }
    }

    /**
     * Redis库存回滚工具方法
     *
     * @param redisBillFtpKeyList Redis库存流水key
     * @param user                用户
     */
    public static ValueHolderV14<SgStorageBillUpdateResult> rollbackStorage(List<String> redisBillFtpKeyList, User user) {

        ValueHolderV14<SgStorageBillUpdateResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS,
                SgConstants.MESSAGE_STATUS_SUCCESS);

        if (CollectionUtils.isEmpty(redisBillFtpKeyList)) {
            return holder;
        }

        //去重复 2021-08-16 因为自动出入库 回滚问题 会出现重复的key，所以添加一个去重复的方法
        redisBillFtpKeyList = redisBillFtpKeyList.stream().distinct().collect(Collectors.toList());

        //删除冻结占用流水
        List<String> freezeRedisFtpKeyList = redisBillFtpKeyList.stream().filter(x ->
                x.contains(SgConstants.SG_B_STO_FREEZE_OUT)).collect(Collectors.toList());

        log.info(LogUtil.format("冻结库存回滚:{}", "freezeRedisFtpKeyList"),
                JSONObject.toJSONString(freezeRedisFtpKeyList));

        if (CollectionUtils.isNotEmpty(freezeRedisFtpKeyList)) {
            redisBillFtpKeyList.removeAll(freezeRedisFtpKeyList);
        }

        SgStorageRedisRollbackRequest rollbackRequest = new SgStorageRedisRollbackRequest();
        rollbackRequest.setRedisBillFtpKeyList(redisBillFtpKeyList);
        rollbackRequest.setLoginUser(user);

        SgStorageRedisBillUpdateService bean =
                ApplicationContextHandle.getBean(SgStorageRedisBillUpdateService.class);
        holder = bean.rollbackStorageBill(rollbackRequest);


        rollbackFreezeOutFtp(freezeRedisFtpKeyList);

        return holder;

    }


    /**
     * 冻结占用流水回滚
     *
     * @param redisFtpKeyList 流水key
     */
    public static void rollbackFreezeOutFtp(List<String> redisFtpKeyList) {

        if (CollectionUtils.isNotEmpty(redisFtpKeyList)) {

            SgBStorageFreezePreoutFtpMapper freezePreoutFtpMapper = ApplicationContextHandle.getBean(SgBStorageFreezePreoutFtpMapper.class);


            List<SgBStorageFreezePreoutFtp> sgStorageFreezePreoutFtps = freezePreoutFtpMapper.selectList(
                    new LambdaQueryWrapper<SgBStorageFreezePreoutFtp>()
                            .in(SgBStorageFreezePreoutFtp::getRedisBillFtpKey, redisFtpKeyList));

            if (CollectionUtils.isNotEmpty(sgStorageFreezePreoutFtps)) {

                log.info(LogUtil.format("冻结库存回滚:{}", "rollbackFreezeOutFtp"),
                        JSONObject.toJSONString(sgStorageFreezePreoutFtps));

                SgBFreezeStorageMapper freezeStorageMapper = ApplicationContextHandle.getBean(SgBFreezeStorageMapper.class);
                SgBStorageMapper storageMapper = ApplicationContextHandle.getBean(SgBStorageMapper.class);

                Map<String, List<SgBStorageFreezePreoutFtp>> ftpMap = sgStorageFreezePreoutFtps.stream().collect(
                        Collectors.groupingBy(x -> x.getCpCStoreId() + x.getPsCSkuId() + x.getProduceDate() + x.getStorageType()));
                for (String key : ftpMap.keySet()) {
                    List<SgBStorageFreezePreoutFtp> ftpList = ftpMap.get(key);
                    //回滚取反
                    BigDecimal qtyChang = ftpList.stream().map(SgBStorageFreezePreoutFtp::getQtyChange).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    freezeStorageMapper.updateFreezePreout(qtyChang.negate(), ftpList.get(0).getCpCStoreId(), ftpList.get(0).getProduceDate(),
                            ftpList.get(0).getStorageType(), ftpList.get(0).getPsCSkuId());
                    storageMapper.updateFreezePreout(qtyChang.negate(), ftpList.get(0).getCpCStoreId(), ftpList.get(0).getProduceDate(),
                            ftpList.get(0).getPsCSkuId());

                }


                freezePreoutFtpMapper.delete(
                        new LambdaQueryWrapper<SgBStorageFreezePreoutFtp>()
                                .in(SgBStorageFreezePreoutFtp::getRedisBillFtpKey, redisFtpKeyList));

//                List<SgBStorageFreezePreoutFtp> batchInsertList = new ArrayList<>();
//
//                for (SgBStorageFreezePreoutFtp freezePreoutFtp : sgStorageFreezePreoutFtps) {
//                    SgBStorageFreezePreoutFtp insert = new SgBStorageFreezePreoutFtp();
//                    BeanUtils.copyProperties(freezePreoutFtp, insert);
//                    insert.setId(ModelUtil.getSequence(SgConstants.SG_B_STORAGE_FREEZE_PREOUT_FTP));
//                    insert.setQtyBegin(freezePreoutFtp.getQtyEnd());
//                    insert.setQtyChange(freezePreoutFtp.getQtyChange().negate());
//                    insert.setQtyEnd(freezePreoutFtp.getQtyBegin());
//                    insert.setRemark("冻结占用流水回滚");
//                    batchInsertList.add(insert);
//                }
//
//                //流水为空时，直接返回
//                if (CollectionUtils.isNotEmpty(batchInsertList)) {
//                    List<List<SgBStorageFreezePreoutFtp>> insertPageList =
//                            StorageUtils.getBaseModelPageList(batchInsertList, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
//
//                    for (List<SgBStorageFreezePreoutFtp> pageList : insertPageList) {
//                        freezePreoutFtpMapper.batchInsert(pageList);
//                    }
//                }


            }


        }

    }

    /**
     * R3框架出参-错误信息收集
     *
     * @param mainId   记录ID
     * @param message  错误信息
     * @param errorArr 错误集合
     */
    public static void errorRecord(Long mainId, String message, JSONArray errorArr) {
        JSONObject errorDate = new JSONObject();
        errorDate.put(R3ParamConstants.CODE, ResultCode.FAIL);
        errorDate.put(R3ParamConstants.OBJID, mainId);
        errorDate.put(R3ParamConstants.MESSAGE, message);
        errorArr.add(errorDate);
    }
}
