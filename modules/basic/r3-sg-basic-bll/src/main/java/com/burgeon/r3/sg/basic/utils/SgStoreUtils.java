package com.burgeon.r3.sg.basic.utils;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseBillBase;
import com.burgeon.r3.sg.core.model.request.SgR3BaseBillItemBase;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.data.basic.model.request.GenerateSerialNumberRequest;
import com.jackrain.nea.data.basic.services.BasicAdQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.BaseModelES;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2019/4/29
 * create at : 2019/4/29 20:24
 */
public class SgStoreUtils {

    public static <T extends SgR3BaseRequest> void checkR3BModelDefalut(T sourceModel) {
        User loginUser = sourceModel.getLoginUser();
        AssertUtils.notNull(loginUser, "用户未登录！");
    }

    public static <T extends SgR3BaseBillBase> void checkBModelDefalut(T sourceModel,
                                                                       boolean sourceBillNoCheckFlag) {
        AssertUtils.notNull(sourceModel.getSourceBillId(), "来源单据ID不能为空！");
        AssertUtils.notNull(sourceModel.getSourceBillType(), "单据类型不能为空！");
        if (sourceBillNoCheckFlag) {
            AssertUtils.notNull(sourceModel.getSourceBillNo(), "来源单据编号为空！");
        }
    }

    public static <T extends SgR3BaseBillItemBase> void checkBModelDefalut(List<T> sourceModels) {
        AssertUtils.notEmpty(sourceModels, "子表信息不能为空！");
        for (T sourceModel : sourceModels) {
            String psCSkuEcode = sourceModel.getPsCSkuEcode();
            AssertUtils.notNull(psCSkuEcode, "条码编码不能为空！");
            AssertUtils.notNull(sourceModel.getSourceBillItemId(), "[" + psCSkuEcode + "]来源明细单据ID不能为空！");
            AssertUtils.notNull(sourceModel.getCpCStoreId(), "[" + psCSkuEcode + "]逻辑仓ID不能为空！");
            AssertUtils.notNull(sourceModel.getCpCStoreEcode(), "[" + psCSkuEcode + "]逻辑仓编码不能为空！");
            AssertUtils.notNull(sourceModel.getCpCStoreEname(), "[" + psCSkuEcode + "]逻辑仓名称不能为空！");
            AssertUtils.notNull(sourceModel.getPsCSkuId(), "[" + psCSkuEcode + "]条码ID不能为空！");
            AssertUtils.notNull(sourceModel.getPsCProId(), "[" + psCSkuEcode + "]商品ID不能为空！");
            AssertUtils.notNull(sourceModel.getPsCProEcode(), "[" + psCSkuEcode + "]商品编码不能为空！");
            AssertUtils.notNull(sourceModel.getPsCProEname(), "[" + psCSkuEcode + "]商品名称不能为空！");
            AssertUtils.notNull(sourceModel.getPsCSpec1Id(), "[" + psCSkuEcode + "]规格1ID不能为空！");
            AssertUtils.notNull(sourceModel.getPsCSpec1Ecode(), "[" + psCSkuEcode + "]规格1编码不能为空！");
            AssertUtils.notNull(sourceModel.getPsCSpec1Ename(), "[" + psCSkuEcode + "]规格1名称不能为空！");
            AssertUtils.notNull(sourceModel.getPsCSpec2Id(), "[" + psCSkuEcode + "]规格2ID不能为空！");
            AssertUtils.notNull(sourceModel.getPsCSpec2Ecode(), "[" + psCSkuEcode + "]规格2编码不能为空！");
            AssertUtils.notNull(sourceModel.getPsCSpec2Ename(), "[" + psCSkuEcode + "]规格2名称不能为空！");
        }
    }


    /**
     * 获取单据编号-走AD
     *
     * @param seq         序号生成器
     * @param table       业务单据表名
     * @param sourceModel
     * @param locale
     * @return 单据编号
     */
    public static <T extends BaseModel> String getBillNo(String seq, String table, T sourceModel, Locale locale) {
        // 获取单据编号
        JSONObject obj = new JSONObject();
        obj.put(table, sourceModel);
        return SequenceGenUtil.generateSquence(seq, obj, locale, false);
    }

    public static <T extends BaseModelES> String getBillNoByRedis(String seq, String table, T sourceModel, User user) {
        BasicAdQueryService adQueryService = ApplicationContextHandle.getBean(BasicAdQueryService.class);
        GenerateSerialNumberRequest adRequest = new GenerateSerialNumberRequest();
        adRequest.setSequenceName(seq);
        JSONObject obj = new JSONObject();
        obj.put(table.toUpperCase(), sourceModel);
        adRequest.setFixColumn(obj);
        adRequest.setTest(false);
        List<String> billNos = adQueryService.generateSerialNumber(adRequest, user);
        if (CollectionUtils.isNotEmpty(billNos)) {
            return billNos.get(0);
        } else {
            throw new NDSException("获取单据编号失败!");
        }
    }


    /**
     * 获取单据编号- 不走AD
     *
     * @param rule 序号生成器
     * @return 单据编号
     */
    @Deprecated
    public static String getBillNo(String rule) {

        // 生成规则：序号生成器OM+年月日+8位流水
        String yyMMdd = FastDateFormat.getInstance("yyMMdd").format(new Date());

        String currentDayRedisKey = "SEQ_" + rule + "_" + yyMMdd;
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();

        //上锁
        Boolean ifAbsent = redisTemplate.opsForValue().setIfAbsent(currentDayRedisKey, "0");
        if (ifAbsent != null && ifAbsent) {
            redisTemplate.expire(currentDayRedisKey, 1, TimeUnit.DAYS);
        }

        Long id = redisTemplate.opsForValue().increment(currentDayRedisKey, 1L);

        return rule + yyMMdd + String.format("%08d", id);
    }


    /**
     * 获取逻辑仓库存控制
     *



     /*
     /**
     * check同步库存占用结果
     *
     * @param err 自定义错误信息
     * @param v14 同步库存结果
     *//*

    public static void isSuccess(String err, ValueHolderV14<SgStorageUpdateResult> v14) {
        SgStorageUpdateResult result = v14.getData();
        boolean flag = result.getPreoutUpdateResult() == SgConstantsIF.PREOUT_RESULT_ERROR;
      */
/*  List<SgStorageOutStockResult> outStockItemList = result.getOutStockItemList();
        if (CollectionUtils.isNotEmpty(outStockItemList)) {
            String errMsg = err;
            for (SgStorageOutStockResult outStockResult : outStockItemList) {
                Long billId = outStockResult.getBillId();
                Long sourceBillItemId = outStockResult.getBillItemId();
                Long psCSkuId = outStockResult.getPsCSkuId();
                Long storeId = outStockResult.getCpCStoreId();
                errMsg += ",来源单据id" + billId + ",来源单据明细" + sourceBillItemId + ",逻辑仓" + storeId + ",条码" + psCSkuId + "占单失败!";
            }
            AssertUtils.logAndThrow(errMsg);
        }*//*

        if (flag) {
            AssertUtils.logAndThrow(err + "同步库存占用失败:" + v14.getMessage());
        }
    }
*/


    /**
     * 获取对象中为空的属性
     *
     * @param source 源对象
     * @return 空对象数组
     */
    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<String>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    /**
     * 获取逻辑仓库存控制
     *
     * @param stores 逻辑仓ids
     * @return map
     */
    public static Map<Long, SgCpCStore> getNegativeStock(List<Long> stores) {
        CpCStoreMapper storeMapper = ApplicationContextHandle.getBean(CpCStoreMapper.class);
        List<SgCpCStore> cpCStores = storeMapper.selectList(
                new QueryWrapper<SgCpCStore>().lambda().select(
                        SgCpCStore::getId, SgCpCStore::getIsnegative, SgCpCStore::getSgCShareStoreId, SgCpCStore::getCurrentSealaccountDate)
                        .in(SgCpCStore::getId, stores));
        return cpCStores.stream().collect(Collectors.toMap(e -> e.getId(), e -> e));
    }

    /**
     * 更新逻辑仓最后封账日
     *
     * @param map 集合
     */
    public static void updateCpCStoreByCustomerId(Map<Long, CpCStore> map) {
        CpCStoreMapper storeMapper = ApplicationContextHandle.getBean(CpCStoreMapper.class);
        List<SgCpCStore> result = new ArrayList<>();
        SgCpCStore sgCpCStore = new SgCpCStore();

        if (map.size() > 0) {
            for (Map.Entry<Long, CpCStore> entry : map.entrySet()) {
                Date currentSealaccountDate = entry.getValue().getCurrentSealaccountDate();
                if (Objects.nonNull(currentSealaccountDate)) {
                    sgCpCStore.setCurrentSealaccountDate(currentSealaccountDate);
                    break;
                }
            }

            storeMapper.update(sgCpCStore, new LambdaQueryWrapper<SgCpCStore>()
                    .in(SgCpCStore::getId, new ArrayList<>(map.keySet())));
        }
    }

    /**
     * 根据逻辑仓id，聚合仓ID，或者逻辑仓档案
     *
     * @param stores 逻辑仓ids
     * @param ssids  聚合仓ids
     * @param pyhIds 实体仓ids
     * @return map
     */
    public static Map<Long, SgCpCStore> getCpCStore(List<Long> stores, List<Long> ssids, List<Long> pyhIds) {
        CpCStoreMapper storeMapper = ApplicationContextHandle.getBean(CpCStoreMapper.class);
        List<SgCpCStore> cpStores = storeMapper.selectList(
                new QueryWrapper<SgCpCStore>().lambda()
                        .in(SgCpCStore::getSgCShareStoreId, ssids)
                        .in(SgCpCStore::getId, stores)
                        .in(SgCpCStore::getCpCPhyWarehouseId, pyhIds)
                        .eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y));
        return cpStores.stream().collect(Collectors.toMap(e -> e.getId(), e -> e));
    }

    /**
     * 复制对象中不为空的属性
     *
     * @param src    源对象
     * @param target 目标对象
     */
    public static void copyPropertiesIgnoreNull(Object src, Object target) {
        BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
    }

    /**
     * 对比逻辑仓占用单 明细中逻辑仓的所属聚合仓
     *
     * @param shareStoreId       聚合仓id
     * @param orgSgStoreOutItems 辑仓占用单 明细
     * @return
     */
    public static List<SgBStoOutItem> contrastByShareId(List<Long> shareStoreId, List<SgBStoOutItem> orgSgStoreOutItems) {

        List<SgBStoOutItem> itemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(shareStoreId)) {
            Map<Long, Long> map = new HashMap<>(16);
            for (Long id : shareStoreId) {
                map.put(id, id);
            }

            List<Long> storeIds = orgSgStoreOutItems.stream().map(SgBStoOutItem::getCpCStoreId).collect(Collectors.toList());
            Map<Long, com.jackrain.nea.cp.result.CpCStore> cpStoreMap = CommonCacheValUtils.queryStoreInfosByIds(storeIds);
            if (MapUtils.isNotEmpty(cpStoreMap)) {
                Map<Long, List<SgBStoOutItem>> itemMap = orgSgStoreOutItems.stream().collect(Collectors.groupingBy(SgBStoOutItem::getCpCStoreId));
                //1.聚合仓不为空2逻辑仓所属聚合仓在入参聚合仓中3，明细中有这个逻辑仓
                for (Long storeId : cpStoreMap.keySet()) {
                    com.jackrain.nea.cp.result.CpCStore store = cpStoreMap.get(storeId);
                    if (store.getSgCShareStoreId() != null &&
                            map.containsKey(store.getSgCShareStoreId()) &&
                            itemMap.containsKey(storeId)) {
                        itemList.addAll(itemMap.get(storeId));
                    }

                }
            }

        } else {
            itemList.addAll(orgSgStoreOutItems);
        }

        return itemList;

    }

    /**
     * @param ids 字符集合
     * @return 引号分隔字符串 "'a','b','c'"
     */
    public static String convertStrToDbIn(Collection<String> ids) {
        String join = StringUtils.join(ids, "','");
        if (StringUtils.isNotBlank(join)) {
            return String.format("'%s'", join);
        }
        return StringUtils.EMPTY;

    }
}
