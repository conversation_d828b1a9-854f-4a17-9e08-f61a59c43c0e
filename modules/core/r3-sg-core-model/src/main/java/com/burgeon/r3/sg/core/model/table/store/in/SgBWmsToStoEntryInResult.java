package com.burgeon.r3.sg.core.model.table.store.in;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * B2B入库回传中间表
 *
 * <AUTHOR>
 * @description sg_b_wms_to_sto_entry_in_result
 * @date 2022-07-13
 */
@TableName(value = "sg_b_wms_to_sto_entry_in_result")
@Data
public class SgBWmsToStoEntryInResult extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;
    /**
     * 单据类型
     */
    @JSONField(name = "BILL_TYPE")
    private String billType;

    /**
     * 转化状态
     */
    @JSONField(name = "TRANSFORM_STATUS")
    private Integer transformStatus;

    /**
     * 入库通知单号
     */
    @JSONField(name = "NOTICES_BILL_NO")
    private String noticesBillNo;

    /**
     * 逻辑入库单id
     */
    @JSONField(name = "RESULT_ID")
    private String resultId;

    /**
     * 逻辑入库单编码
     */
    @JSONField(name = "RESULT_BILL_NO")
    private String resultBillNo;

    /**
     * wms单据编号
     */
    @JSONField(name = "WMS_BILL_CODE")
    private String wmsBillCode;

    /**
     * 外部订单业务id
     */
    @JSONField(name = "OUT_BIZ_CODE")
    private String outBizCode;

    /**
     * wms仓库编码
     */
    @JSONField(name = "WAREHOUSE_CODE")
    private String warehouseCode;

    /**
     * 回传报文
     */
    @JSONField(name = "MESSAGE")
    private String message;

    /**
     * 失败次数
     */
    @JSONField(name = "FAILED_COUNT")
    private Integer failedCount;

    /**
     * 失败原因
     */
    @JSONField(name = "FAILED_REASON")
    private String failedReason;

    /**
     * qmwms = 奇门，jdwms = 京东
     */
    private String wmsWarehouseType;

    @JSONField(name = "TRANSFORMATION_DATA")
    private Date transformationData;
}