package com.burgeon.r3.sg.core.model.table.migration.profit;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName : SgBPhyProfit
 * @Description :
 * <AUTHOR> CD
 * @Date: 2021-07-06 11:36
 */
@TableName(value = "sg_b_phy_profit")
@Data
public class SgBPhyProfit extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "BILL_NO")
    private String billNo;

    @JSONField(name = "BILL_DATE")
    private Date billDate;

    @JSONField(name = "INVENTORY_ID")
    private Long inventoryId;

    @JSONField(name = "INVENTORY_BILL_NO")
    private String inventoryBillNo;

    @JSONField(name = "INVENTORY_STORE_ID")
    private Long inventoryStoreId;

    @JSONField(name = "INVENTORY_STORE_ECODE")
    private String inventoryStoreEcode;

    @JSONField(name = "INVENTORY_STORE_ENAME")
    private String inventoryStoreEname;

    @JSONField(name = "INVENTORY_TYPE")
    private String inventoryType;

    @JSONField(name = "STATUS")
    private Integer status;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "VERSION")
    private Long version;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "STATUS_ID")
    private Long statusId;

    @JSONField(name = "STATUS_ENAME")
    private String statusEname;

    @JSONField(name = "STATUS_NAME")
    private String statusName;

    @JSONField(name = "STATUS_TIME")
    private Date statusTime;

    @JSONField(name = "RESERVE_BIGINT01")
    private Long reserveBigint01;

    @JSONField(name = "RESERVE_BIGINT02")
    private Long reserveBigint02;

    @JSONField(name = "RESERVE_BIGINT03")
    private Long reserveBigint03;

    @JSONField(name = "RESERVE_BIGINT04")
    private Long reserveBigint04;

    @JSONField(name = "RESERVE_BIGINT05")
    private Long reserveBigint05;

    @JSONField(name = "RESERVE_BIGINT06")
    private Long reserveBigint06;

    @JSONField(name = "RESERVE_BIGINT07")
    private Long reserveBigint07;

    @JSONField(name = "RESERVE_BIGINT08")
    private Long reserveBigint08;

    @JSONField(name = "RESERVE_BIGINT09")
    private Long reserveBigint09;

    @JSONField(name = "RESERVE_BIGINT10")
    private Long reserveBigint10;

    @JSONField(name = "RESERVE_VARCHAR01")
    private String reserveVarchar01;

    @JSONField(name = "RESERVE_VARCHAR02")
    private String reserveVarchar02;

    @JSONField(name = "RESERVE_VARCHAR03")
    private String reserveVarchar03;

    @JSONField(name = "RESERVE_VARCHAR04")
    private String reserveVarchar04;

    @JSONField(name = "RESERVE_VARCHAR05")
    private String reserveVarchar05;

    @JSONField(name = "RESERVE_VARCHAR06")
    private String reserveVarchar06;

    @JSONField(name = "RESERVE_VARCHAR07")
    private String reserveVarchar07;

    @JSONField(name = "RESERVE_VARCHAR08")
    private String reserveVarchar08;

    @JSONField(name = "RESERVE_VARCHAR09")
    private String reserveVarchar09;

    @JSONField(name = "RESERVE_VARCHAR10")
    private String reserveVarchar10;

    @JSONField(name = "RESERVE_DECIMAL01")
    private BigDecimal reserveDecimal01;

    @JSONField(name = "RESERVE_DECIMAL02")
    private BigDecimal reserveDecimal02;

    @JSONField(name = "RESERVE_DECIMAL03")
    private BigDecimal reserveDecimal03;

    @JSONField(name = "RESERVE_DECIMAL04")
    private BigDecimal reserveDecimal04;

    @JSONField(name = "RESERVE_DECIMAL05")
    private BigDecimal reserveDecimal05;

    @JSONField(name = "RESERVE_DECIMAL06")
    private BigDecimal reserveDecimal06;

    @JSONField(name = "RESERVE_DECIMAL07")
    private BigDecimal reserveDecimal07;

    @JSONField(name = "RESERVE_DECIMAL08")
    private BigDecimal reserveDecimal08;

    @JSONField(name = "RESERVE_DECIMAL09")
    private BigDecimal reserveDecimal09;

    @JSONField(name = "RESERVE_DECIMAL10")
    private BigDecimal reserveDecimal10;

    @JSONField(name = "DELER_ID")
    private Long delerId;

    @JSONField(name = "DELER_ENAME")
    private String delerEname;

    @JSONField(name = "DELER_NAME")
    private String delerName;

    @JSONField(name = "DEL_TIME")
    private Date delTime;

    @JSONField(name = "STRIKE_BALANCE_ID")
    private Long strikeBalanceId;

    @JSONField(name = "STRIKE_BALANCE_ENAME")
    private String strikeBalanceEname;

    @JSONField(name = "STRIKE_BALANCE_NAME")
    private String strikeBalanceName;

    @JSONField(name = "STRIKE_BALANCE_TIME")
    private Date strikeBalanceTime;

    @JSONField(name = "DRP_STATUS")
    private Integer drpStatus;

    @JSONField(name = "DRP_FAIL_COUNT")
    private Integer drpFailCount;

    @JSONField(name = "DRP_FAIL_REASON")
    private String drpFailReason;

    @JSONField(name = "TOTAL_ACCOUNT_QTY")
    private BigDecimal totalAccountQty;

    @JSONField(name = "TOTAL_INVENTORY_QTY")
    private BigDecimal totalInventoryQty;

    @JSONField(name = "TOTAL_DIFFERENCE_QTY")
    private BigDecimal totalDifferenceQty;

}