package com.burgeon.r3.sg.core.model.table.basic;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "sg_c_operation_log")
@Data
public class SgCOperationLog extends BaseModel {
    @J<PERSON><PERSON>ield(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "TABLE_NAME")
    private String tableName;

    @J<PERSON><PERSON>ield(name = "OPERATION_TYPE")
    private String operationType;

    @J<PERSON><PERSON>ield(name = "UPDATE_ID")
    private Long updateId;

    @JSONField(name = "UPDATE_MODEL_NAME")
    private String updateModelName;

    @J<PERSON><PERSON><PERSON>(name = "MOD_CONTENT")
    private String modContent;

    @J<PERSON><PERSON>ield(name = "BEFORE_DATA")
    private String beforeData;

    @J<PERSON><PERSON>ield(name = "AFTER_DATA")
    private String afterData;

    @<PERSON><PERSON><PERSON>ield(name = "OWNERENAME")
    private String ownerename;
}