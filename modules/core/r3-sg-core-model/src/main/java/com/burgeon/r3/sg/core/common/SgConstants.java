package com.burgeon.r3.sg.core.common;

import java.math.BigDecimal;

/**
 * @Description:
 * @Author: chenb
 * @Date: 2019/3/16 22:28
 */
public class SgConstants {

    /**
     * 中心名
     */
    public static final String CENTER_NAME_SG = "sg";
    /**
     * IP->版本号（RPC服务默认版本号）
     */
    public static final String DUBBO_IP_RPC_VERSION = "1.4.0";

    public static final String CP_C_PHY_WAREHOUSE = "cp_c_phy_warehouse";
    public static final String CP_C_PHY_WAREHOUSE_ITEM = "cp_c_phy_warehouse_item";
    public static final String CP_C_ORG_CHANNEL = "cp_c_org_channel";
    public static final String CP_C_ORG_CHANNEL_ITEM = "cp_c_org_channel_item";
    public static final String CP_C_STORE = "cp_c_store";

    /**
     * drp->中台接口 幂等 rediskey前缀
     */
    public static final String INTERFACE_DRP = "drp";

    /**
     * 表名
     */
    public static final String SG_B_STORAGE = "sg_b_storage";   // 库存查询表
    public static final String SG_B_GROUP_STORAGE = "sg_b_group_storage";//组合商品库存表
    public static final String SG_B_STORAGE_CHANGE_FTP = "sg_b_storage_change_ftp"; // 库存变动流水表
    public static final String SG_B_STORAGE_PREIN_FTP = "sg_b_storage_prein_ftp";  // 库存在途变动流水
    public static final String SG_B_STORAGE_SHARED_PREOUT_FTP = "sg_b_storage_shared_preout_ftp";  // 库存共享占用变动流水表
    public static final String SG_B_STORAGE_UNSHARED_PREOUT_FTP = "sg_b_storage_unshared_preout_ftp";  // 库存非共享占用变动流水
    public static final String SG_B_STORAGE_FREEZE_FTP = "sg_b_storage_freeze_ftp";  // 库存冻结变动流水表
    public static final String SG_B_STORAGE_FREEZE_PREOUT_FTP = "sg_b_storage_freeze_preout_ftp";  // 库存冻结占用变动流水表
    public static final String SG_B_STORAGE_LOCK_LOG = "sg_b_storage_lock_log"; //库存同步幂等管理表
    public static final String SG_B_MONITOR_STORAGE_DIFF = "sg_b_monitor_storage_diff";
    public static final String SG_B_MONITOR_SHARE_STORAGE_DIFF = "sg_b_monitor_share_storage_diff";
    public static final String SG_B_MONITOR_SA_STORAGE_DIFF = "sg_b_monitor_sa_storage_diff";
    public static final String SG_B_CHANNEL_STORAGE_CHANGE_FTP = "sg_b_channel_storage_change_ftp";
    public static final String SG_B_CHANNEL_STORAGE = "sg_b_channel_storage";
    public static final String SG_B_MONITOR_MQ_ERROR_DATAS = "sg_b_monitor_mq_error_datas";
    public static final String SG_B_OMS_TO_WMS_STORAGE_CHECK = "sg_b_oms_to_wms_storage_check";   // oms与wms库存核对表
    public static final String SG_B_OMS_TO_SAP_STORAGE_CHECK = "sg_b_oms_to_sap_storage_check";   // oms与wms库存核对表

    public static final String SG_B_STO_IN = "sg_b_sto_in";
    public static final String SG_B_STO_IN_ITEM = "sg_b_sto_in_item";
    public static final String SG_B_STO_IN_RESULT = "sg_b_sto_in_result";
    public static final String SG_B_STO_IN_RESULT_ITEM = "sg_b_sto_in_result_item";
    public static final String SG_B_STO_OUT = "sg_b_sto_out";

    public static final String SG_B_STO_OUT_NOTICES = "sg_b_sto_out_notices";

    public static final String SG_B_STO_OUT_NOTICES_EXT = "sg_b_sto_out_notices_ext";

    public static final String SG_B_STO_OUT_NOTICES_ITEM = "sg_b_sto_out_notices_item";
    public static final String SG_B_STO_IN_NOTICES = "sg_b_sto_in_notices";
    public static final String SG_B_STO_IN_NOTICES_ITEM = "sg_b_sto_in_notices_item";

    public static final String SG_B_STO_OUT_TMS = "sg_b_sto_out_tms";
    public static final String SG_B_STO_OUT_ITEM = "sg_b_sto_out_item";
    public static final String SG_B_STO_OUT_ITEM_LOG = "sg_b_sto_out_item_log";
    public static final String SG_B_STO_OUT_RESULT = "sg_b_sto_out_result";
    public static final String SG_B_STO_OUT_RESULT_ITEM = "sg_b_sto_out_result_item";
    public static final String SG_B_STO_OUT_RESULT_MILK_CARD_ITEM = "sg_b_sto_out_result_milk_card_item";
    public static final String SG_B_STO_OUT_RESULT_PACK_ITEM = "sg_b_sto_out_result_pack_item";

    public static final String SG_B_STO_FREEZE = "sg_b_sto_freeze";
    public static final String SG_B_STO_DIFF = "sg_b_sto_diff";
    public static final String SG_B_STO_DIFF_ITEM = "sg_b_sto_diff_item";
    public static final String SG_B_STO_FREEZE_UNFREEZE_RESULT = "sg_b_sto_freeze_unfreeze_result";

    public static final String SG_B_STO_FREEZE_ITEM = "sg_b_sto_freeze_item";

    public static final String SG_B_STO_TRANSFER = "sg_b_sto_transfer";
    public static final String SG_B_STO_TRANSFER_CONFIRMATION = "sg_b_sto_transfer_confirmation";
    public static final String SG_B_STO_TRANSFER_ITEM = "sg_b_sto_transfer_item";
    public static final String SG_B_STO_TRANSFER_OUT_ITEM = "sg_b_sto_transfer_out_item";
    public static final String SG_B_STO_TRANSFER_IN_ITEM = "sg_b_sto_transfer_in_item";
    public static final String SG_B_STO_TRANSFER_PACKING_ITEM = "sg_b_sto_transfer_packing_item";
    public static final String SG_B_STO_UNFREEZE = "sg_b_sto_unfreeze";
    public static final String SG_B_STO_UNFREEZE_ITEM = "sg_b_sto_unfreeze_item";
    public static final String SG_B_STO_BATCH_TRANSFER = "sg_b_sto_batch_transfer";
    public static final String SG_B_STO_BATCH_TRANSFER_ITEM = "sg_b_sto_batch_transfer_item";
    public static final String SG_B_STO_BATCH_TRANSFER_PRO_ITEM = "sg_b_sto_batch_transfer_pro_item";

    public static final String SG_B_STO_STORE_ADVANCE_SALE = "sg_b_sto_store_advance_sale";
    public static final String SG_B_STO_STORE_ADVANCE_SALE_ITEM = "sg_b_sto_store_advance_sale_item";

    public static final String SG_B_CHANNEL_ADVANCE_SALE = "sg_b_channel_advance_sale";
    public static final String SG_B_CHANNEL_ADVANCE_SALE_ITEM = "sg_b_channel_advance_sale_item";

    public static final String SG_B_STORAGE_BATCH = "sg_b_storage_batch";   // 逻辑仓批次库存表
    public static final String SG_B_STORAGE_BATCH_CHANGE_FTP = "sg_b_storage_batch_change_ftp";   // 逻辑仓批次库存变动流水表

    /**
     * OMS
     */
    public static final String SG_B_CHANNEL_DEF = "sg_b_channel_def";
    public static final String SG_B_CHANNEL_STORAGE_BUFFER = "sg_b_channel_storage_buffer";
    public static final String SG_B_CHANNEL_STORAGE_BUFFER_PROCEDURE = "sg_b_channel_storage_buffer_procedure";
    public static final String SG_B_PHY_STORAGE_CHANGE_FTP = "sg_b_phy_storage_change_ftp";

    public static final String SG_B_CHANNEL_SYNSTOCK_Q = "sg_b_channel_synstock_q";
    public static final String SG_B_CHANNEL_STORE = "sg_b_channel_store";
    public static final String SG_B_CHANNEL_SYNSTOCK_BATCHNO = "sg_b_channel_synstock_batchno";
    public static final String SG_B_CHANNEL_SYNSTOCK_BATCHNO_ITEM = "sg_b_channel_synstock_batchno_item";
    public static final String REDIS_KEY_PREFIX_TEUS_STORAGE = "sg:teus_storage:";

    public static final String SG_B_CHANNEL_MANUAL_SYN = "sg_b_channel_manual_syn";
    public static final String SG_B_CHANNEL_MANUAL_SYN_ITEM = "sg_b_channel_manual_syn_item";
    public static final String SG_B_TEUS_STORAGE = "sg_b_teus_storage";   // 箱库存查询表
    public static final String SG_B_STORAGE_PREOUT_FTP = "sg_b_storage_preout_ftp";  // 表库存在单变动流水表
    public static final String SG_B_TEUS_STORAGE_CHANGE_FTP = "sg_b_teus_storage_change_ftp"; // 箱库存变动流水表
    public static final String SG_B_TEUS_STORAGE_PREIN_FTP = "sg_b_teus_storage_prein_ftp";  // 箱库存在单变动流水表
    public static final String SG_B_TEUS_STORAGE_PREOUT_FTP = "sg_b_teus_storage_preout_ftp";  // 箱库存在途变动流水表
    public static final String SG_C_WAREHOUSE_SCOPE = "sg_c_warehouse_scope";  // 仓库配送范围
    //平台店铺库存管理日志表
    public static final String SG_C_CHANNEL_STOCK_CONTROL_LOG = "sg_c_channel_stock_control_log";

    public static final String SG_C_CHANNEL_SOURCE_STRATEGY_WAREHOUSE_ITEM = "sg_c_channel_source_strategy_warehouse_item";

    //欧睿共享量同步中台 表名
    public static final String SG_C_OFFLINE_WAREHOUSE_SELECTION = "sg_c_offline_warehouse_selection";//线下自营仓选款
    public static final String SG_C_OWN_STORE_SELECTION = "sg_c_own_store_selection";//自营门店选款
    public static final String SG_C_SMART_STORE_SELECTION = "sg_c_smart_store_selection";//SmartStore门店选款
    public static final String SG_C_STORE_WAREHOUSE_SELECTION = "sg_c_store_warehouse_selection";//sg_c_store_warehouse_selection
    public static final String SG_C_SA_SELECTION = "SG_C_SA_SELECTION";//配销仓选款结果表

    //聚合仓配销仓调拨单 聚合仓配销仓调拨结果单明细 聚合仓配销仓调拨单导入明细
    public static final String SG_B_SHARE_SA_ALLOCATION_TRANSFER = "sg_b_share_sa_allocation_transfer";
    public static final String SG_B_SHARE_SA_ALLOCATION_TRANSFER_ITEM = "sg_b_share_sa_allocation_transfer_item";
    public static final String SG_B_SHARE_SA_ALLOCATION_IMPORT_ITEM = "sg_b_share_sa_allocation_import_item";
    /**
     * sg_b_channel_storage XACT锁 区段
     */
    public static final long XACT_LOCK_PREFIX_CHANNEL_STORAGE = 20000000000000L;

    public static final String SG_C_SHARE_STORE = "sg_c_share_store";
    public static final String SG_C_SA_STORE = "sg_c_sa_store";

    public static final String SG_C_SHARE_POOL = "sg_c_share_pool";
    public static final String SG_C_SHARE_POOL_ITEM = "sg_c_share_pool_item";
    public static final String SG_C_SHARE_POOL_LOG = "sg_c_share_pool_log";

    public static final String SG_C_SHARE_SCORE_FACTOR_STRATEGY = "sg_c_share_score_factor_strategy";

    public static final String SG_B_SHARE_ALLOCATION = "sg_b_share_allocation";
    public static final String SG_B_SHARE_ALLOCATION_ITEM = "sg_b_share_allocation_item";

    public static final String SG_B_SA_STORAGE = "sg_b_sa_storage";
    public static final String SG_B_SA_STORAGE_PREOUT_FTP = "sg_b_sa_storage_preout_ftp";
    public static final String SG_B_SA_STORAGE_CHANGE_FTP = "sg_b_sa_storage_change_ftp";
    public static final String SG_B_SA_STORAGE_FIXED_FTP = "sg_b_sa_storage_fixed_ftp";
    public static final String SG_B_SHARE_STORAGE = "sg_b_share_storage";
    public static final String SG_B_SP_STORAGE_ALLOCATION_FTP = "sg_b_sp_storage_allocation_ftp";
    public static final String SG_B_SP_STORAGE_PREOUT_FTP = "sg_b_sp_storage_preout_ftp";
    public static final String SG_B_CHANNEL_FIXED_STORAGE = "sg_b_channel_fixed_storage";
    public static final String SG_B_CHANNEL_FIXED_STORAGE_CHANGE_FTP = "sg_b_channel_fixed_storage_change_ftp";
    public static final String SG_B_CHANNEL_FIXED_STORAGE_SOLD_FTP = "sg_b_channel_fixed_storage_sold_ftp";

    public static final String SG_B_SHARE_ALLOCATION_RETURN = "sg_b_share_allocation_return";
    public static final String SG_B_SHARE_ALLOCATION_RETURN_ITEM = "sg_b_share_allocation_return_item";

    public static final String SG_B_SHARE_DISTRIBUTION = "sg_b_share_distribution";
    public static final String SG_B_SHARE_DISTRIBUTION_ITEM = "sg_b_share_distribution_item";

    public static final String SG_B_SHARE_ADJUST = "sg_b_share_adjust";
    public static final String SG_B_SHARE_ADJUST_ITEM = "sg_b_share_adjust_item";

    public static final String SG_B_SHARE_OUT = "sg_b_share_out";
    public static final String SG_B_SHARE_OUT_ITEM = "sg_b_share_out_item";
    public static final String SG_B_SHARE_OUT_ITEM_LOG = "sg_b_share_out_item_log";

    public static final String SG_B_STORE_UPDATE_SHARE_STORE = "sg_b_sto_store_update_share_store";
    public static final String SG_B_STORE_UPDATE_SHARE_STORE_ITEM = "sg_b_sto_store_update_share_store_item";

    /**
     * 特殊条码按比例同步策略
     * 特殊条码按比例同步策略视图
     * 特殊条码同步策略独享库存明细
     * 特殊条码同步策略共享库存明细
     */

    public static final String SG_C_CHANNEL_SKU_STRATEGY = "sg_c_channel_sku_strategy";
    public static final String V_SG_C_CHANNEL_SKU_STRATEGY = "v_sg_c_channel_sku_strategy";
    public static final String SG_C_CHANNEL_SKU_STRATEGY_SA_ITEM = "sg_c_channel_sku_strategy_sa_item";
    public static final String SG_C_CHANNEL_SKU_STRATEGY_SP_ITEM = "sg_c_channel_sku_strategy_sp_item";
    public static final String SG_C_CHANNEL_SKU_STRATEGY_LOG = "sg_c_channel_sku_strategy_log";

    /**
     * 店铺指定实体仓设置表
     * 店铺指定实体仓设置指定明细表
     * 店铺指定实体仓设置排除明细表
     */
    public static final String SG_C_CHANNEL_SHOP_APPOINT_WAREHOUSE = "sg_c_channel_shop_appoint_warehouse";
    public static final String SG_C_CHANNEL_SHOP_APPOINT_WAREHOUSE_APPOINT_ITEM = "sg_c_channel_shop_appoint_warehouse_appoint_item";
    public static final String SG_C_CHANNEL_SHOP_APPOINT_WAREHOUSE_EXCLUDE_ITEM = "sg_c_channel_shop_appoint_warehouse_exclude_item";

    /**
     * 按数量同步策略
     * 按数量同步策略明细
     */
    public static final String SG_C_CHANNEL_QTY_STRATEGY = "sg_c_channel_qty_strategy";
    public static final String SG_C_CHANNEL_QTY_STRATEGY_ITEM = "sg_c_channel_qty_strategy_item";

    public static final String SG_C_CHANNEL_STORAGE_SYNC_GRADIENT_STRATEGY = "sg_c_channel_storage_sync_gradient_strategy";
    public static final String SG_C_CHANNEL_STORAGE_SYNC_PRO_GRADIENT_STRATEGY = "sg_c_channel_storage_sync_pro_gradient_strategy";

    public static final String SG_C_CHANNEL_STORAGE_SYNC_GRADIENT_STRATEGY_LOG = "sg_c_channel_storage_sync_gradient_strategy_log";
    public static final String SG_C_CHANNEL_STORAGE_SYNC_PRO_GRADIENT_STRATEGY_LOG = "sg_c_channel_storage_sync_pro_gradient_strategy_log";

    /**
     * 按比例同步策略
     * 按比例同步策略配销仓明细
     */
    public static final String SG_C_CHANNEL_RATIO_STRATEGY = "sg_c_channel_ratio_strategy";
    public static final String SG_C_CHANNEL_RATIO_STRATEGY_ITEM = "sg_c_channel_ratio_strategy_item";
    public static final String SG_C_CHANNEL_RATIO_STRATEGY_LOG = "sg_c_channel_ratio_strategy_log";
    /**
     * 评分策略表
     * 评分策略明细
     */
    public static final String SG_C_SHARE_SCORE_STRATEGY = "sg_c_share_score_strategy";
    public static final String SG_C_SHARE_SCORE_STRATEGY_ITEM = "sg_c_share_score_strategy_item";

    /**
     * 寻源策略规则设置
     */
    public static final String SG_C_SHARE_SOURCE_RULE_STRATEGY = "sg_c_share_source_rule_strategy";

    /**
     * 店仓评分设置表
     */
    public static final String SG_C_STORE_SCORE_STRATEGY = "sg_c_store_score_strategy";


    /**
     * 库存调整单
     */
    public static final String SG_B_STO_ADJUST = "sg_b_sto_adjust";
    public static final String SG_B_STO_ADJUST_ITEM = "sg_b_sto_adjust_item";
    public static final String SG_B_STO_ADJUST_BATCH_ITEM = "sg_b_sto_adjust_batch_item";

    /**
     * 其他入库单
     */
    public static final String SG_B_STO_OTHER_DELIVERY = "sg_b_sto_other_delivery";
    public static final String SG_B_STO_OTHER_DELIVERY_ITEM = "sg_b_sto_other_delivery_item";
    public static final String SG_B_STO_OTHER_DELIVERY_ITEM_IN = "sg_b_sto_other_delivery_item_in";

    /**
     * 寻源策略表
     * 寻源策略表_强制寻源规则明细表
     * 寻源策略表_寻源规则明细表
     * 寻源策略表_评分策略明细表
     */
    public static final String SG_C_CHANNEL_SOURCE_STRATEGY = "sg_c_channel_source_strategy";
    public static final String SG_C_CHANNEL_SOURCE_STRATEGY_FORCE_ITEM = "sg_c_channel_source_strategy_force_item";
    public static final String SG_C_CHANNEL_SOURCE_STRATEGY_RULE_ITEM = "sg_c_channel_source_strategy_rule_item";
    public static final String SG_C_CHANNEL_SOURCE_STRATEGY_SCORE_ITEM = "sg_c_channel_source_strategy_score_item";

    /**
     * 平台库存增量同步表
     * 平台库存增量同步明细表
     * <p>
     * 平台库存全量同步表
     */
    public static final String SG_B_CHANNEL_STORAGE_INC_SYNC = "sg_b_channel_storage_inc_sync";
    public static final String SG_B_CHANNEL_STORAGE_INC_SYNC_ITEM = "sg_b_channel_storage_inc_sync_item";
    public static final String SG_B_CHANNEL_STORAGE_FULL_SYNC = "sg_b_channel_storage_full_sync";

    /**
     * 平台店铺安全库存批量设置表和明细表
     */
    public static final String SG_C_CHANNEL_STORE_SAFETY_SETTING = "sg_c_channel_store_safety_setting";
    public static final String SG_C_CHANNEL_STORE_SAFETY_SETTING_ITEM = "sg_c_channel_store_safety_setting_item";
    /**
     * 全渠道商品表
     */
    public static final String SG_B_OMNI_CHANNEL_PRODUCT = "sg_b_omni_channel_product";
    /**
     * 全渠道商品表
     */
    public static final String SG_B_CHANNEL_PRODUCT = "sg_b_channel_product";
    public static final String SG_B_STO_CLOSE_ACCOUNT = "sg_b_sto_close_account";
    /**
     * 聚合仓库存同步梯度策略(实表)
     * 配销仓库存同步梯度策略(虚表)
     * 配销仓/聚合仓库存梯度策略条件明细
     * 配销仓/聚合仓库存同步梯度策略店铺明细
     */
    public static final String SG_C_SYNC_GRADIENT_STRATEGY = "sg_c_sync_gradient_strategy";
    public static final String SG_C_SYNC_GRADIENT_STRATEGY_SA = "sg_c_sync_gradient_strategy_sa";
    public static final String SG_C_SYNC_GRADIENT_STRATEGY_COND = "sg_c_sync_gradient_strategy_cond";
    public static final String SG_C_SYNC_GRADIENT_STRATEGY_ITEM = "sg_c_sync_gradient_strategy_item";
    public static final String SG_B_SOURCING_RETRY = "sg_b_sourcing_retry";


    /**
     * 冻结占用单
     */
    public static final String SG_B_STO_FREEZE_OUT = "sg_b_sto_freeze_out";
    public static final String SG_B_STO_FREEZE_OUT_ITEM = "sg_b_sto_freeze_out_item";
    /**
     * 冻结出库单
     */
    public static final String SG_B_STO_FREEZE_OUT_RESULT = "sg_b_sto_freeze_out_result";
    public static final String SG_B_STO_FREEZE_OUT_RESULT_ITEM = "sg_b_sto_freeze_out_result_item";

    /**
     * 自动分货
     */
    //一手码设置
    public static final String SG_C_ONESET_SETTING = "sg_c_oneset_setting";
    //一手码配销仓特殊设置
    public static final String SG_C_ONESET_SA_SETTING = "sg_c_oneset_sa_setting";
    //一手码库存阶梯数量
    public static final String SG_C_ONESET_STORAGE_GRADIENT_QTY = "sg_c_oneset_storage_gradient_qty";
    //店铺商品售卖设置
    public static final String SG_C_SHOP_SKU_SALE_SETTING = "sg_c_shop_sku_sale_setting";
    //店铺铺底梯度设置
    public static final String SG_C_SHOP_ALLOCATION_GRADIENT = "sg_c_shop_allocation_gradient";
    //店铺铺底设置
    public static final String SG_C_SHOP_ALLOCATION = "sg_c_shop_allocation";
    //配销仓目标库存汇总
    public static final String SG_B_SA_TARGET_STORAGE_SUMMARY = "sg_b_sa_target_storage_summary";
    //E-COM活动时间设置
    public static final String SG_C_ECOM_ACTIVITY_SETTING = "sg_c_ecom_activity_setting";
    //E-COM活动时间设置-配销仓明细
    public static final String SG_C_ECOM_ACTIVITY_SETTING_SA_ITEM = "sg_c_ecom_activity_setting_sa_item";
    //E-COM活动时间设置-商品明细
    public static final String SG_C_ECOM_ACTIVITY_SETTING_PRO_ITEM = "sg_c_ecom_activity_setting_pro_item";
    //配销仓调拨单
    public static final String SG_B_SHARE_SA_TRANSFER = "SG_B_SHARE_SA_TRANSFER";
    //配销仓调拨单明细
    public static final String SG_B_SHARE_SA_TRANSFER_ITEM = "SG_B_SHARE_SA_TRANSFER_ITEM";
    //配销仓调拨单批量导入
    public static final String SG_B_SHARE_SA_BATCH_TRANSFER = "SG_B_SHARE_SA_BATCH_TRANSFER";
    //配销仓调拨单批量导入明细
    public static final String SG_B_SHARE_SA_BATCH_TRANSFER_ITEM = "SG_B_SHARE_SA_BATCH_TRANSFER_ITEM";
    //电商商品属性维护
    public static final String SG_C_ECOM_PRODUCT_MAINT = "sg_c_ecom_product_maint";
    //商品渠道
    public static final String SG_C_ECOM_PRO_CHANNEL = "sg_c_ecom_pro_channel";

    //唯品会商品补货策略表
    public static final String SG_B_SHARE_VIP_REPLENISH_STRATEGY = "SG_B_SHARE_VIP_REPLENISH_STRATEGY";
    //唯品会商品补货策略明细表
    public static final String SG_B_SHARE_VIP_REPLENISH_STRATEGY_ITEM = "SG_B_SHARE_VIP_REPLENISH_STRATEGY_ITEM";

    // TOC策略
    public static final String SG_C_TOC_STRATEGY = "SG_C_TOC_STRATEGY";
    // TOB策略
    public static final String SG_C_TOB_STRATEGY = "SG_C_TOB_STRATEGY";
    // TOB客户寻源批次数量管理
    public static final String SG_B_TOB_SOURCE_MAX_BATCH = "SG_B_TOB_SOURCE_MAX_BATCH";


    /**
     * 供应商发货接口表
     */
    //经销商店仓对照表
    public static final String SG_C_CUSTOMER_STORE = "sg_c_customer_store";
    //经销商逻辑仓库存表
    public static final String SG_B_CUSTOMER_STORAGE = "sg_b_customer_storage";
    //经销商逻辑仓库存变动流水表
    public static final String SG_B_CUSTOMER_STORAGE_CHANGE_FTP = "sg_b_customer_storage_change_ftp";

    /**
     * 库存查询
     */
    public static final String SG_B_WMS_TO_LS_STORAGE = "sg_b_wms_to_ls_storage";


    //redis 锁
    /**
     * MQ幂等性防重锁
     **/
    public static final String REDIS_KEY_PREFIX_LS_MQ_MESSAGE = "storage_ls_mq:";
    public static final String REDIS_KEY_PREFIX_SS_MQ_MESSAGE = "storage_ss_mq:";
    public static final String REDIS_KEY_PREFIX_SA_MQ_MESSAGE = "storage_sa_mq:";
    public static final String REDIS_KEY_PREFIX_PHY_MQ_MESSAGE = "storage_phy_mq:";
    public static final String REDIS_KEY_PREFIX_LS_STORAGE = "sg:storage:";
    public static final String REDIS_KEY_PREFIX_LS_STORAGE_FTP = "sg:storage_ftp:";
    public static final String REDIS_KEY_PREFIX_SS_STORAGE = "sg:share_storage:";
    public static final String REDIS_KEY_PREFIX_SS_STORAGE_FTP = "sg:share_storage_ftp:";
    public static final String REDIS_KEY_PREFIX_SA_STORAGE = "sg:sa_storage:";
    public static final String REDIS_KEY_PREFIX_SA_STORAGE_FTP = "sg:sa_storage_ftp:";
    public static final String REDIS_KEY_PREFIX_CF_STORAGE = "sg:cf_storage:";
    public static final String REDIS_KEY_PREFIX_CF_STORAGE_FTP = "sg:cf_storage_ftp:";
    public static final String REDIS_KEY_PREFIX_PHY_STORAGE = "sg:phy_storage:";
    public static final String REDIS_KEY_PREFIX_PHY_STORAGE_FTP = "sg:phy_storage_ftp:";
    public static final String REDIS_SUBKEY_SHARED_PREOUT = "shared_preout";
    public static final String REDIS_SUBKEY_UNSHARED_PREOUT = "unshared_preout";
    public static final String REDIS_SUBKEY_PREOUT = "preout";
    public static final String REDIS_SUBKEY_PREIN = "prein";
    public static final String REDIS_SUBKEY_ALLOCATION = "allocation";
    public static final String REDIS_SUBKEY_FREEZE = "freeze";
    public static final String REDIS_SUBKEY_STORAGE = "storage";
    public static final String REDIS_SUBKEY_AVAILABLE = "available";
    public static final String REDIS_KEY_MANAGEMENT_PHY_STORAGE = "sg:key_mgt:phy_storage:part";
    public static final String REDIS_KEY_MANAGEMENT_LS_STORAGE = "sg:key_mgt:storage:part";
    public static final String REDIS_KEY_MANAGEMENT_SS_STORAGE = "sg:key_mgt:share_storage:part";
    public static final String REDIS_KEY_MANAGEMENT_SA_STORAGE = "sg:key_mgt:sa_storage:part";
    public static final String REDIS_KEY_MANAGEMENT_CF_STORAGE = "sg:key_mgt:cf_storage:part";
    public static final String REDIS_KEY_FIND_SOURCE_STRATEGY_C2S = "sg:find_source:c2s:";
    public static final String REDIS_KEY_FIND_SOURCE_STRATEGY_S2L = "sg:find_source:s2l:";
    public static final String REDIS_KEY_FIND_SOURCE_STRATEGY_CC = "sg:find_source:cc:";
    public static final String REDIS_KEY_FIND_SOURCE_RETRY_S2L = "sg:find_source_retry:s2l:";
    public static final String REDIS_KEY_PREFIX_LS_BATCH_STORAGE = "sg:storage_batch:";
    public static final String REDIS_KEY_PREFIX_LS_BATCH_STORAGE_FTP = "sg:storage_batch_ftp:";
    public static final String REDIS_KEY_PREFIX_LS_PRODUCE_DATE = "sg:storage:produce:";

    //大页面：Redis锁
    public static final String REDIS_KEY_CHANNEL_SG = "sg:";
    public static final String REDIS_KEY_CHANNEL_TEMP_KEY = "temp:key:";
    public static final String REDIS_KEY_CHANNEL_TEMP_RES = "temp:res:";

    // 库存同步，redis
    public static final String REDIS_KEY_CHANNEL_PRODUCT_QTY_OUT = "sg:channel_product:qty_out_stock:";

    public static final String IS_TRUE = "1";
    public static final String IS_FALSE = "0";

    public static final String SG_CONNECTOR_MARKS_1 = "{";
    public static final String SG_CONNECTOR_MARKS_2 = "}";
    public static final String SG_CONNECTOR_MARKS_3 = "#";
    public static final String SG_CONNECTOR_MARKS_4 = ":";
    public static final String SG_CONNECTOR_MARKS_5 = ",";
    public static final String SG_CONNECTOR_MARKS_6 = "_";
    public static final String SG_CONNECTOR_MARKS_7 = "\\{";
    public static final String SG_CONNECTOR_MARKS_8 = "【";
    public static final String SG_CONNECTOR_MARKS_9 = "】";
    public static final String SG_CONNECTOR_MARKS_10 = "";

    /**
     * 库存中心事务控制等级
     */
    //消费者事务
    public static final int TRANSACTION_LEVEL_CONSUMER = 1;
    //单据事务
    public static final int TRANSACTION_LEVEL_BILL = 2;
    //明细事务
    public static final int TRANSACTION_LEVEL_ITEM = 3;

    /**
     * 消息队列消息KEY前缀
     */
    public static final String MSG_KEY_HEAD_STORAGE_TO_CHANNEL = "sto_to_chan:";
    public static final String MSG_KEY_HEAD_STORAGE_TO_GROUP = "sto_to_group:";
    public static final String MSG_KEY_HEAD_STORAGE_TO_RETAIL = "sto_to_retail:";

    /**
     * 消息队列消息TAG前缀
     */
    public static final String MSG_KEY_HEAD_STORAGE = "storage_";
    public static final String MSG_KEY_HEAD_PHY_STORAGE = "storage_phy_";
    public static final String MSG_KEY_HEAD_SHARE_STORAGE = "storage_share_";
    public static final String MSG_KEY_HEAD_SA_STORAGE = "storage_sa_";
    public static final String MSG_KEY_HEAD_CF_STORAGE = "storage_cf_";
    public static final String MSG_KEY_GROUP_STORAGE = "group_storage";
    public static final String MSG_KEY_HEAD_CHANNEL_STORAGE = "channel_storage_";
    public static final String MSG_KEY_HEAD_STORAGE_BATCH = "storage_batch_";
    public static final String MSG_KEY_HEAD_STO_RESULT_MILK_CARD = "sto_result_milk_card";

    /**
     * 逻辑层单据消息队列TAG
     */
    public static final String MSG_TAG_STO_OUT = "sto_out";
    public static final String MSG_TAG_STO_OUT_BATCH = "sto_out_batch";
    public static final String MSG_TAG_STO_IN_BATCH = "sto_in_batch";
    public static final String MSG_TAG_STO_OUT_RESULT = "sto_out_result";
    public static final String MSG_TAG_STO_IN = "sto_in";
    public static final String MSG_TAG_STO_IN_RESULT = "sto_in_result";
    public static final String MSG_TAG_STO_TRANSFER = "sto_transfer";
    public static final String MSG_TAG_STO_DIFF = "sto_diff";
    public static final String MSG_TAG_STO_FREEZE = "sto_freeze";
    public static final String MSG_TAG_STO_UNFREEZE = "sto_unfreeze";
    public static final String MSG_TAG_STO_PRESELL_PLAN = "sto_presell_plan";
    public static final String MSG_TAG_SHARE_ALLOCATION = "share_allocation";
    public static final String MSG_TAG_SHARE_ALLOCATION_RETURN = "share_allocation_return";
    public static final String MSG_TAG_SHARE_DISTRIBUTION = "share_distribution";
    public static final String MSG_TAG_SHARE_OUT = "share_out";
    public static final String MSG_TAG_SHARE_ADJUST = "share_adjust";
    public static final String MSG_TAG_STO_TRANSFER_DRP_SRM = "sto_transfer_srm";
    public static final String MSG_TAG_CHANNEL_QTY_STRATEGY = "share_qty_strategy";

    public static final String MSG_TAG_STO_TRANSFER_DRP_TF = "sto_transfer_drp";
    public static final String MSG_TAG_STO_OUT_NOTICES = "sto_out_notices";
    public static final String MSG_TAG_STO_IN_NOTICES = "sto_in_notices";

    /**
     * 逻辑层单据编号前缀
     */
    public static final String BILL_PREFIX_STO_OUT = "LON";
    public static final String BILL_PREFIX_STO_OUT_RESULT = "LOR";
    public static final String BILL_PREFIX_STO_IN = "LIN";
    public static final String BILL_PREFIX_STO_IN_RESULT = "LIR";
    public static final String BILL_PREFIX_STO_TRANSFER = "TFM";
    public static final String BILL_PREFIX_STO_DIFF = "LDF";
    public static final String BILL_PREFIX_STO_FREEZE = "LMF";
    public static final String BILL_PREFIX_STO_UNFREEZE = "LMT";
    public static final String BILL_PREFIX_STO_PRESELL_PLAN = "LPS";
    public static final String BILL_PREFIX_SHARE_ALLOCATION = "STS";
    public static final String BILL_PREFIX_SHARE_ALLOCATION_RETURN = "SSR";
    public static final String BILL_PREFIX_SHARE_DISTRIBUTION = "SAL";
    public static final String BILL_PREFIX_SHARE_OUT = "SPO";
    public static final String BILL_PREFIX_SHARE_ADJUST = "SSP";
    public static final String BILL_PREFIX_STO_TRANSFER_DRP_SRM = "SRM";
    public static final String BILL_PREFIX_CHANNEL_QTY_STRATEGY = "SCS";
    public static final String BILL_PREFIX_STO_TRANSFER_DRP_TF = "TF";
    public static final String BILL_PREFIX_STO_OUT_NOTICES = "ON";
    public static final String BILL_PREFIX_STO_IN_NOTICES = "IN";


    /**
     * 可用
     */
    public static final String IS_ACTIVE_Y = "Y";
    public static final String IS_ACTIVE_N = "N";
    public static final int IS_YES = 1;
    public static final int IS_NO = 0;
    /**
     * 是否
     */
    public static final String IS_YES_OR_NO_Y = "Y";
    public static final String IS_YES_OR_NO_N = "N";

    //是否为最后一次出库
    public static final String IS_LAST_YES = "Y";
    public static final String IS_LAST_NO = "N";

    public static final String IS_AUTO_Y = "Y";
    public static final String IS_AUTO_N = "N";

    public static final Integer IS_AUTO_IN_Y = 1;
    public static final Integer IS_AUTO_IN_N = 0;

    /**
     * 是否审核：Y 审核 N保存
     */
    public static final String IS_SUBMIT_N = "N";
    public static final String IS_SUBMIT_Y = "Y";

    //是否负库存
    public static final long ISNO_FLAG_YES = 1L;  // 不允许负库存
    public static final long ISNO_FLAG_NO = 0L;   // 允许负库存

    public static final String DEFAULT_INFO_ZERO = "0";

    public static final int SG_COMMON_INSERT_PAGE_SIZE_MIN = 200;
    public static final int SG_COMMON_INSERT_PAGE_SIZE = 300;
    public static final int SG_COMMON_UPDATE_PAGE_SIZE = 500;
    public static final int SG_COMMON_MAX_INSERT_PAGE_SIZE = 200;
    public static final int SG_COMMON_ITEM_INSERT_PAGE_SIZE = 1000;
    public static final int SG_COMMON_MAX_QUERY_PAGE_SIZE = 100;
    public static final int SG_COMMON_STRING_COMMON_SIZE = 250;
    public static final int SG_COMMON_STRING_SIZE = 500;
    public static final int SG_COMMON_QUERY_SIZE = 500;
    public static final int SG_COMMON_REMARK_SIZE = 1000;
    public static final int SG_NORMAL_MAX_QUERY_PAGE_SIZE = 1000;
    public static final int SG_COMMON_THREAD_SIZE = 30000;
    public static final int SG_COMMON_STRING_SHORT_SIZE = 50;

    /**
     * redis Key
     */
    public static final String SG_B_WMS_BACK_PHY_STATUSE = "sg:wms:back:phy:status:";

    /**
     * 获取来源平台信息 redist key
     */
    public static final String CP_SELECTBYPLATFORMID_ECODEANDID = "cp:selectbyplatformid:byplatformid";
    /**
     * 根据物流档案和平台查询平台物流编码
     */
    public static final String CP_SELECT_LOGISTICSCODE_BY_PLATFORM = "cp:select:logisticscode:byPlatform:";

    /**
     *
     */
    public static final int SG_B_WMS_BACK_PHY_WEIGHT_PAGENUM = 200;

    /**
     * wms逐日库存同步时，每个线程分配任务数量
     */
    public static final int WMS_THREAD_TASK_NUM = 500;

    //单据状态
    public static final int CON_BILL_STATUS_01 = 1;//未审核
    public static final int CON_BILL_STATUS_02 = 2;//已审核
    public static final int CON_BILL_STATUS_03 = 3;//已作废
    public static final int CON_BILL_STATUS_04 = 4;//已结案

    /**
     * 其他入库单-单据状态
     */
    // 未审核
    public static final Integer OTHER_DELIVERY_BILL_STATUS_00 = 1;
    // 已审核未入库
    public static final Integer OTHER_DELIVERY_BILL_STATUS_21 = 2;
    // 已审核部分入库
    public static final Integer OTHER_DELIVERY_BILL_STATUS_22 = 3;
    // 已审核入库完成
    public static final Integer OTHER_DELIVERY_BILL_STATUS_23 = 4;
    // 已作废
    public static final Integer OTHER_DELIVERY_BILL_STATUS_09 = 5;

    // 失败次数
    public static final String SAP_FAIL_COUNT_KEY = "sap_fail_count";
    // 入库失败次数
    public static final String IN_SAP_FAIL_COUNT_KEY = "in_sap_fail_count";
    // 最大失败次数
    public static final int SAP_MAX_FAIL_COUNT = 5;

    // tableService 请求方式：public static final interface（接口）
    public static final String TABLE_SERVICE_REQUEST_TYPE = "table_service_request_type";
    public static final String TABLE_SERVICE_REQUEST_TYPE_INTERFACE = "public static final interface";
    // 是否批量新增
    public static final String TABLE_SERVICE_IS_INSERT_BACTH = "isinsertbacth";

    /**
     * 日志记录长度配置：debug-5000、error-5000
     */
    public static final int LOG_COMMON_MAX_LENGTH = 5000;
    public static final int LOG_DEBUG_MAX_LENGTH = 5000;
    public static final int LOG_ERROR_MAX_LENGTH = 5000;

    /**
     * 同步状态：0-待同步、1-同步中、2-同步成功、3-同步失败
     */
    public static final String SYNC_STATUS_WAIT = "0";
    public static final String SYNC_STATUS_SUCCESS = "1";
    public static final String SYNC_STATUS_FAIL = "2";

    public static final String REDIS_SEND_WITH_PRIORITY = "SgSendSaveWithPriorityCmd";

    /**
     * redis实体仓库存监控差异：错误类型
     * 1) redisIsNull：redis库存为空
     * 2) dbIsNull：pg库库存为空
     * 3) redisAndDbDiff: redis和pg库库存差异
     * 4) qtyIsNegative：库存为负数
     */
    public static final String MONITOR_ERROR_TYPE_REDIS_ISNULL = "redisIsNull";
    public static final String MONITOR_ERROR_TYPE_DB_ISNULL = "dbIsNull";
    public static final String MONITOR_ERROR_TYPE_REDIS_AND_DB_DIFF = "redisAndDbDiff";
    public static final String MONITOR_ERROR_TYPE_QTY_IS_NEGATIVE = "qtyIsNegative";
    public static final String MESSAGE_STATUS_SUCCESS = "success";
    public static final String MESSAGE_STATUS_FAIL = "fail";

    /**
     * @Description: 库存占用结果 全部占用成功
     */
    public static final String PREOUT_RESULT_All_SUCCESS = "0";
    /**
     * @Description: 库存占用结果 部分占用成功 部分缺货
     */
    public static final String PREOUT_RESULT_PART_SUCCESS = "1";
    /**
     * @Description: 库存占用结果 全部缺货
     */
    public static final String PREOUT_RESULT_ALL_OUT = "2";
    /**
     * @Description: map 初始化大小
     **/
    public static final int MAP_DEFAULT_INITIAL_CAPACITY = 16;
    /**
     * @Description: list 初始化大小
     **/
    public static final int LIST_DEFAULT_INITIAL_CAPACITY = 10;

    /**
     * 配销仓类型
     * 1 活动 2 普通
     */
    public final static String SA_STORE_TYPE_ACTIVITY = "1";
    public final static String SA_STORE_TYPE_NORMAL = "2";

    /**
     * 库存同步梯度策略类型
     * 1 配销仓 2 共享池
     */
    public final static String SYNC_GRADIENT_STRATEGY_SA = "1";
    public final static String SYNC_GRADIENT_STRATEGY_POOL = "2";


    public final static BigDecimal NUMBER_100 = new BigDecimal("100");

    public final static String MQ_DATE_FORMAT = "yyyyMMddHHmmssSSS";
    /**
     * @Description: 唯品店仓性质：1 仓库
     **/
    public final static String VIP_STORE_NATURE_WAREHOUSE = "1";
    /**
     * @Description: 唯品店仓性质： 2 门店
     **/
    public final static String VIP_STORE_NATURE_SHOP = "2";

    // 请求类型
    public static final int REQUEST_TYPE_SAVE = 1;
    public static final int REQUEST_TYPE_SUBMIT = 2;
    public static final int REQUEST_TYPE_SAVE_AND_SUBMIT = 3;

    /**
     * 组合商品同步渠道库存tag
     */
    public static final String MSG_TAG_GROUP_STORAGE_TO_CHANNEL = "group_storage_to_channel";

    /**
     * 组合商品标记
     */
    public static final Integer WARE_TYPE_GROUP_PRO = 1;

    /**
     * 普通商品标记
     */
    public static final Integer WARE_TYPE_NORMAL_PRO = 0;

    /**
     * @Description: 仓库类型-配销仓
     **/
    public static final String SYNC_STORE_TYPE_SA = "01";
    /**
     * @Description: 仓库类型-聚合仓
     **/
    public static final String SYNC_STORE_TYPE_SHARE = "02";
    /**
     * @Description: 仓库类型-渠道锁定库存
     **/
    public static final String SYNC_STORE_TYPE_CHANNEL_FIXED = "03";
    /**
     * @Description: 仓库类型-共享池
     **/
    public static final String SYNC_STORE_TYPE_SHARE_POOL = "04";


    /**
     * @Description: 库存同步方式-增量同步
     **/
    public static final String SYNC_TYPE_ADD = "01";
    /**
     * @Description: 库存同步方式-全量覆盖
     **/
    public static final String SYNC_TYPE_ALL = "02";

    /**
     * 平台
     */
    public static final String PLATFORMTYPE_TAOBAO = "2";
    public static final String PLATFORMTYPE_TAOBAO_DISTRIBUTION = "3";
    public static final String PLATFORMTYPE_JINGDONG = "4";
    public static final String PLATFORMTYPE_SUNING = "12";
    public static final String PLATFORMTYPE_AMAZON = "10";
    public static final String PLATFORMTYPE_PINDUODUO = "27";
    public static final String PLATFORMTYPE_TAOBAO_JINGXIAO = "77";
    public static final String PLATFORMTYPE_TAOBAO_FENXIAO = "3";
    public static final String PLATFORMTYPE_WUXIANGYUN = "37";
    public static final String PLATFORMTYPE_POINTS = "65";
    public static final String PLATFORMTYPE_WEIPINHUI_JITX = "19";
    public static final String PLATFORMTYPE_WEIPINHUI_JIT = "19";
    public static final String PLATFORMTYPE_WEIPINHUI_OXO = "36";
    public static final String PLATFORMTYPE_ALIBABA_ASCP = "66";
    public static final String PLATFORMTYPE_TENGXUN_HUIJU = "400";

    /**
     * @Description: 共享占用单明细库存来源 配销仓
     **/
    public static final String SHARE_OUT_ITEM_STOCK_SOURCE_SA = "01";
    /**
     * @Description: 共享占用单明细库存来源 渠道锁定库存
     **/
    public static final String SHARE_OUT_ITEM_STOCK_SOURCE_CF = "02";
    /**
     * @Description: 共享占用单明细库存来源 共享池
     **/
    public static final String SHARE_OUT_ITEM_STOCK_SOURCE_SHARE = "03";

    /**
     * 调拨类型：1正常调拨，2差异调拨
     */
    public static final Integer TRANSFER_TYPE_NORMAL = 1;
    public static final Integer TRANSFER_TYPE_DIFF = 2;

    /**
     * {@link com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum}
     */
    @Deprecated
    public static final String WMS_TYPE_JDWMS = "JDWMS";
    public static final String LOGISTICS_ECODE_JD = "JD";
    public static final Long JD_PLATFORM_ID = 4L;

    // 聚合仓调拨单
    public static final String SG_B_SHARE_TRANSFER = "sg_b_share_transfer";
    public static final String SG_B_SHARE_TRANSFER_IMPORT_ITEM = "sg_b_share_transfer_import_item";
    public static final String SG_B_SHARE_TRANSFER_ITEM = "sg_b_share_transfer_item";

    /**
     * 增量自动同步
     */
    public static final String IS_AUTO_ADD_Y = "Y";
    public static final String IS_AUTO_ADD_N = "N";


    /**
     * 欧睿库存同步 共享结果单
     */
    public static final String SG_C_SYNC_SHARE_STORAGE_RESULT = "sg_c_sync_share_storage_result";
    //欧睿库存同步 中间表
    public static final String SG_C_PRODUCT_SYSTEM_SHARE_LOG_TEMP = "sg_c_product_system_share_log_temp";

    /**
     * 欧睿商品系统共享日志表
     */
    public static final String SG_C_PRODUCT_SYSTEM_SHARE_LOG = "sg_c_product_system_share_log";

    /**
     * 唯品会商品补货信息表
     */
    public static final String SG_B_SHARE_VIP_REPLENISH = "sg_b_share_vip_replenish";

    /**
     * 配销跨聚合仓调拨单
     * 调拨导入明细
     * 调拨结果明细
     */
    public static final String SG_B_SHARE_SA_CROSS_TRANSFER = "sg_b_share_sa_cross_transfer";
    public static final String SG_B_SHARE_SA_CROSS_TRANSFER_IMPORT_ITEM = "sg_b_share_sa_cross_transfer_import_item";
    public static final String SG_B_SHARE_SA_CROSS_TRANSFER_RESULT_ITEM = "sg_b_share_sa_cross_transfer_result_item";


    //欧睿共享结果单同步状态
    public static final Integer SYNC_STATUS = 0; //未同步
    public static final Integer SYNC_ING_STATUS = 1; //正在同步
    public static final Integer SYNC_SUCCESS_STATUS = 2; //同步成功
    public static final Integer SYNC_FAIL_STATUS = 3;  //同步失败


    /**
     * 下载平台商品计算缓存池
     */
    public static final String SG_B_CHANNEL_PRODUCT_DOWNLOAD_BUFFER = "sg_b_channel_product_download_buffer";

    /**
     * 配销仓到聚合仓调拨单
     * 调拨导入明细
     * 调拨结果明细
     */
    public static final String SG_B_SHARE_FROM_SA_TRANSFER = "sg_b_share_from_sa_transfer";
    public static final String SG_B_SHARE_FROM_SA_TRANSFER_IMPORT_ITEM = "sg_b_share_from_sa_transfer_import_item";
    public static final String SG_B_SHARE_FROM_SA_TRANSFER_RESULT_ITEM = "sg_b_share_from_sa_transfer_result_item";

    /**
     * 业务系统参数
     * TRANSFER_ALLOW_IN_WITHCOLORANDCODE_OR_MOREGOODS  是否允许串色串码
     */
    public static final String TRANSFER_ALLOW_IN_WITHCOLORANDCODE_OR_MOREGOODS = "business_system:transfer_allow_in_withColorAndCode_or_moreGoods";
    public static final String OC_B_ORDER_SEND_TO_TMS_OWNER_COMPANY_CODE = "business_system:oc_b_order_send_to_tms_owner_company_code";
    public static final String O2O_ORDER_SHUNDIANTONG_SHOP = "business_system:o2o_order_shundiantong_shop";

    /**
     * 唯品会平台条码拼接
     */
    public static final String VIP_CHANNEL_PRODUCT_SKU_TAG = "VIP";


    public static final String STRING_TAG_NULL = "null";

    //库存对比策略
    public static final String SG_C_STOCK_CONTRAST_STRATEGY = "SG_C_STOCK_CONTRAST_STRATEGY";

    /**
     * 多渠道调拨单
     */
    public static final String SG_B_STO_MULTI_CHANNEL_TRANSFER = "SG_B_STO_MULTI_CHANNEL_TRANSFER";

    // 店铺自动补货策略新
    public static final String SG_B_SHOP_REPLENISH_STRATEGY = "SG_B_SHOP_REPLENISH_STRATEGY";

    // 店铺自动补货策略序号生成
    public static final String SEQ_SG_B_SHOP_REPLENISH_STRATEGY = "SEQ_SG_B_SHOP_REPLENISH_STRATEGY";

    // 店铺自动补货策略明细新
    public static final String SG_B_SHOP_REPLENISH_STRATEGY_ITEM = "SG_B_SHOP_REPLENISH_STRATEGY_ITEM";
    // 店铺自动补货策略保底库存明细新
    public static final String SG_B_SHOP_REPLENISH_STRATEGY_MINI_STOCK_ITEM = "SG_B_SHOP_REPLENISH_STRATEGY_MINI_STOCK_ITEM";

    // 店铺自动补货策略保底库存明细新
    public static final String SG_B_SHOP_REPLENISH_STRATEGY_MINI_STOCK = "SG_B_SHOP_REPLENISH_STRATEGY_MINI_STOCK";

    // 店铺自动补货策略保底库存明细序号生成
    public static final String SEQ_SG_B_SHOP_REPLENISH_STRATEGY_MINI_STOCK = "SEQ_SG_B_SHOP_REPLENISH_STRATEGY_MINI_STOCK";

    // 店铺自动补货策略分货主表
    public static final String SG_B_SHOP_REPLENISH_STRATEGY_RESULT = "SG_B_SHOP_REPLENISH_STRATEGY_RESULT";

    // 店铺自动补货策略分货明细表
    public static final String SG_B_SHOP_REPLENISH_STRATEGY_RESULT_ITEM = "SG_B_SHOP_REPLENISH_STRATEGY_RESULT_ITEM";

    // 店铺自动补货策略分货退主表
    public static final String SG_B_SHOP_REPLENISH_STRATEGY_RETURN_RESULT = "SG_B_SHOP_REPLENISH_STRATEGY_RETURN_RESULT";

    // 店铺自动补货策略分货退明细表
    public static final String SG_B_SHOP_REPLENISH_STRATEGY_RETURN_RESULT_ITEM = "SG_B_SHOP_REPLENISH_STRATEGY_RETURN_RESULT_ITEM";

    public static final String SG_BILL_LOCK_WMSRETURN = "sg:bill_lock:wms_return";

    public static final String SG_B_WMS_TO_STO_OUT_RESULT = "sg_b_wms_to_sto_out_result";

    public static final String SG_B_STO_OUT_DELIVERY = "sg_b_sto_out_delivery";

    public static final String SG_B_FREEZE_STORAGE = "sg_b_freeze_storage";


    /**
     * WMS发起回传中间表
     */
    public static final String SG_B_WMS_RETURN_MIDDLE_TABLE = "sg_b_wms_return_middle_table";

    /*** 库存盘点 */
    public static final String SG_REDIS_KEY_SG_B_WMS_INVENTORY_REPORT_PREFIX = "sg:bill_lock" +
            ":sg_b_wms_inventory_report:";

    public static final String SG_B_WMS_INVENTORY_REPORT_TABLE = "sg_b_wms_inventory_report";

    /*** 库存盘点回传中间表 转化状态 转化状态(0:未转化;2:转化成功;4:转化失败;) */
    public static final Integer SG_B_WMS_INVENTORY_REPORT_STATUS_NOT_TRANSFER = 0;
    public static final Integer SG_B_WMS_INVENTORY_REPORT_STATUS_TRANSFER_FAIL = 4;
    public static final Integer SG_B_WMS_INVENTORY_REPORT_STATUS_TRANSFER_SUCCESS = 2;

    public static final String SG_B_WMS_STOCK_RESULT = "sg_b_wms_stock_result";

    /*** 订单中心-库存中心-DRP(Wing)-WMS 需两次回传*/
    /*** 第一次 */
    public static final int FIRST_TO_PASS_TYPE = 1;
    /*** 第二次 */
    public static final int SECOND_TO_PASS_TYPE = 2;

    /**
     * 其它出库单单据状态
     * 1-未审核；2-已审核未出库；3-已审核部分出库；4-已审核出库完成；5-已作废
     */
    public static final Integer SG_B_STO_OTHER_OUT_RESULT_STATUS_UN_SUBMIT = 1;
    public static final Integer SG_B_STO_OTHER_OUT_RESULT_STATUS_SUBMIT_NO_OUT = 2;
    public static final Integer SG_B_STO_OTHER_OUT_RESULT_STATUS_PART_OUT = 3;
    public static final Integer SG_B_STO_OTHER_OUT_RESULT_STATUS_ALL_OUT = 4;
    public static final Integer SG_B_STO_OTHER_OUT_RESULT_STATUS_VOID = 5;
    /**
     * 其他出库单
     */
    public static final String SG_B_STO_OTHER_OUT_RESULT = "sg_b_sto_other_out_result";
    public static final String SG_B_STO_OTHER_OUT_RESULT_ITEM = "sg_b_sto_other_out_result_item";
    public static final String SG_B_STO_OTHER_OUT_RESULT_ITEM_OUT = "sg_b_sto_other_out_result_item_out";
    public static final String SG_B_STO_OTHER_OUT_RESULT_BOM_ITEM = "sg_b_sto_other_out_result_bom_item";

    /**
     * 强制完成结案状态
     * 0未结案 1 已结案
     */
    public static final Integer CLOSE_STATUS_UN_CLOSE = 0;
    public static final Integer CLOSE_STATUS_CLOSE = 1;


    /**
     * 生产日期占用类型：1 先进先出
     */
    public static final Integer SG_B_STO_PRODUCE_PREOUT_TYPE = 1;

    /**
     * 是否包含大效期 0否 1是
     */
    public static final Integer SG_B_STO_IS_CONTAINS_VALIDITY_N = 0;
    public static final Integer SG_B_STO_IS_CONTAINS_VALIDITY_Y = 1;

    /**
     * 效期类型 1 新鲜 2 正常 3 潜在效期 4 大效期 5 临期 6 过期
     */
    public static final Integer PS_C_CONTAINS_VALIDITY_TYPE_FRESH = 1;
    public static final Integer PS_C_CONTAINS_VALIDITY_TYPE_NORMAL = 2;
    public static final Integer PS_C_CONTAINS_VALIDITY_TYPE_LATENT_LONG = 3;
    public static final Integer PS_C_CONTAINS_VALIDITY_TYPE_BIG_LONG = 4;

    /**
     * 标准效期定义： 日期类型 1按日  2按月
     */
    public static final int PS_C_CONTAINS_VALIDITY_DATE_TYPE_DATE = 1;
    public static final int PS_C_CONTAINS_VALIDITY_DATE_TYPE_MONTH = 2;

    /**
     * 配销仓效期类型
     */
    public static final String SG_SA_STORE_CONTAINS_VALIDITY_TYPE_UN_SHARE_POOL = "0";
    public static final String SG_SA_STORE_CONTAINS_VALIDITY_TYPE_SHARE_POOL = "1";
    public static final String SG_SA_STORE_CONTAINS_VALIDITY_TYPE_BIG_VALIDITY = "2";

    /**
     * 签收状态
     * 0未签收 1 已签收
     */
    public static final Integer SIGN_STATUS_UN_SIGN = 0;
    public static final Integer SIGN_STATUS_SIGN = 1;

    // 发货确认单接口redis锁
    public static final String SG_BILL_LOCK_B2C_OUT = "sg:bill_lock:b2c:out:";

    /**
     * wms仓库类型
     * QMWMS = 奇门，JDWMS = 京东
     * {@link com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum}
     */
    @Deprecated
    public static final String WMS_WAREHOUSE_TYPE_QMWMS = "QMWMS";
    @Deprecated
    public static final String WMS_WAREHOUSE_TYPE_JDWMS = "JDWMS";
    @Deprecated
    public static final String WMS_WAREHOUSE_TYPE_JYWMS = "JYWMS";

    //b2b入库回传中间表
    public static final String SG_B_WMS_TO_STO_ENTRY_IN_RESULT = "sg_b_wms_to_sto_entry_in_result";
    //b2b出库回传中间表
    public static final String SG_B_WMS_TO_STO_STOCK_OUT_RESULT = "sg_b_wms_to_sto_stock_out_result";
    //发货单缺货中间表
    public static final String SG_B_WMS_TO_ORDER_OUT_STOCK_RESULT = "sg_b_wms_to_order_out_stock_result";
    //退换货回传中间表
    public static final String SG_B_REFUND_IN_TASK = "sg_b_refund_in_task";
    //库存盘点结果回传
    public static final String SG_B_WMS_TO_STO_ADJUST_RESULT = "sg_b_wms_to_sto_adjust_result";
    //仓内加工单通知中间表
    public static final String SG_B_WMS_TO_STOREPROCESS_CONFIRM_RESULT = "sg_b_wms_to_storeprocess_confirm_result";
    //库存异动通知中间表
    public static final String SG_B_INVENTORY_CHANGE_NOTICE_TASK = "sg_b_inventory_change_notice_task";

    /**
     * 临近大效期寻源设置
     */
    public static final String SG_C_STO_NEAR_EXPIRY_DATE_STRATEGY = "sg_c_sto_near_expiry_date_strategy";

    //公共日志
    public static final String SG_C_OPERATION_LOG = "sg_c_operation_log";

    /**
     * 库存回退方式 1 优先级 2 比例
     */
    public static final Integer SG_B_SA_STORAGE_RETURN_TYPE_PRIORITY = 1;
    public static final Integer SG_B_SA_STORAGE_RETURN_TYPE_RATIO = 2;

    //sap信息映射表
    public static final String SG_B_SAP_INFORMATION_MAPPING = "sg_b_sap_information_mapping";

    public static final int LIST_ONLY_ONE = 1;

    // 寻源策略定义类型：1 指定店铺  2 公共
    public static final int SG_C_CHANNEL_SOURCE_STRATEGY_TYPE_SHOP = 1;
    public static final int SG_C_CHANNEL_SOURCE_STRATEGY_TYPE_ALL = 2;

    /**
     * 部门月需求提报，部门月需求提报修改日志，月计划提报
     */
    public static final String SG_C_DEPARTMENT_MONTH_DEMAND = "sg_c_department_month_demand";
    public static final String SG_C_DEPARTMENT_MONTH_DEMAND_LOG = "sg_c_department_month_demand_log";
    public static final String SG_C_MONTH_PRODUCTION_PLAN = "sg_c_month_production_plan";
    public static final String SG_C_DISTRIBUTION_MONTH_LOG = "sg_c_distribution_month_log";

    public static final String SG_C_DEPT_WARN_SA_STORAGE = "sg_c_dept_warn_sa_storage";

    public static final String SG_C_STORE_WARN_STORAGE = "sg_c_store_warn_storage";

    public static final String SG_C_STORE_WARN_PRODUCTION = "sg_c_store_warn_production";

    public static final String SG_C_TRANSFER_ARRIVAL_DAYS_CONFIG = "sg_c_transfer_arrival_days_config";

    /**
     * 效期管理类型
     */
    public static final int TYPE_DAY = 1;//日数
    public static final int TYPE_MONTH = 2;//月数

    public static final String SG_B_SAP_CONSIGNMENT_STORAGE = "sg_b_sap_consignment_storage";

    /**
     * 部门配销仓策略
     */
    public static final String SG_C_DEPARTMENT_SA_STRATEGY = "sg_c_department_sa_strategy";
    public static final String SG_C_DEPARTMENT_SA_STRATEGY_DEPARTMENT = "sg_c_department_sa_strategy_department";
    public static final String SG_C_DEPARTMENT_SA_STRATEGY_SA = "sg_c_department_sa_strategy_sa";

    /**
     * 中间表转化分布式锁前缀
     * 建议：前缀+表名+业务唯一键
     */
    public static final String CONVERT_REDIS_LOCK_PREFIX = "sg:convert:lock:";

    public static final String REDIS_SPLIT = ":";


    /**
     * 其他出库单-生产流程订单-换箱加工
     */
    public static final String OTHER_OUT_RESULT_PRODUCTION_PROCESS_TYPE_CHANGE_BOX = "Z02";
}


