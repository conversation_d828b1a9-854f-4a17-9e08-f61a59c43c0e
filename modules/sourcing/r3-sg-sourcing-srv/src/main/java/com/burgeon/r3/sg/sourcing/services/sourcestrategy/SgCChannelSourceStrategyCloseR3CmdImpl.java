package com.burgeon.r3.sg.sourcing.services.sourcestrategy;

import com.burgeon.r3.sg.sourcing.api.sourcestrategy.SgCChannelSourceStrategyCloseR3Cmd;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/6/21 10:07
 * 寻源策略结案
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgCChannelSourceStrategyCloseR3CmdImpl extends CommandAdapter implements SgCChannelSourceStrategyCloseR3Cmd {
    @Autowired
    private SgCChannelSourceStrategyService service;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return service.closeR3SourceStrategy(session);
    }
}
