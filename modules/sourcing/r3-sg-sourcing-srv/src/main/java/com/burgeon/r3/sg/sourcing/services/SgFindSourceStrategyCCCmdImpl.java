package com.burgeon.r3.sg.sourcing.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.config.SgStorageMqConfig;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.basic.mapper.SgCTocStrategyMapper;
import com.burgeon.r3.sg.basic.model.request.SgBFreezeStorageQueryNewRequest;
import com.burgeon.r3.sg.basic.services.SgBFreezeStorageQueryService;
import com.burgeon.r3.sg.basic.utils.DingTalkTokenEnum;
import com.burgeon.r3.sg.basic.utils.DingTalkUtil;
import com.burgeon.r3.sg.basic.utils.EnvEnum;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.SgMonitorMqEnum;
import com.burgeon.r3.sg.core.enums.YesNoEnum;
import com.burgeon.r3.sg.core.model.request.SgBMonitorMqErrorDatasRequest;
import com.burgeon.r3.sg.core.model.table.basic.SgBFreezeStorage;
import com.burgeon.r3.sg.core.model.table.basic.SgCTocStrategy;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategy;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategyForceItem;
import com.burgeon.r3.sg.core.model.table.store.freeze.out.SgBStoFreezeOut;
import com.burgeon.r3.sg.core.utils.SgMonitorUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.inf.common.enums.StoreTypeEnum;
import com.burgeon.r3.sg.sourcing.api.SgFindSourceStrategyCCCmd;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyForceItemMapper;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyCCRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemCC;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyCCResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsItemResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuCCResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemCCResult;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.burgeon.r3.sg.store.mapper.freeze.out.SgBStoFreezeOutMapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: lijin
 * @time: 2025/02/21
 * @description: 2C残次寻源
 */
@Slf4j
@Component
public class SgFindSourceStrategyCCCmdImpl implements SgFindSourceStrategyCCCmd {

    @Resource
    private SgFindSourceCCPreOutStorageService ccPreOutStorageService;
    @Resource
    private DefaultProducerSend defaultProducerSend;
    @Value("${r3.mq.sg_to_oms.sourcing.delayTime:2000}")
    private Long delayTime;
    @Resource
    private SgMonitorUtils sgMonitorUtils;
    @Resource
    private SgCChannelSourceStrategyMapper strategyMapper;
    @Resource
    private SgStorageMqConfig sgStorageMqConfig;
    @Resource
    private SgBFreezeStorageQueryService sgBFreezeStorageQueryService;
    @Resource
    private CpCStoreMapper storeMapper;
    @Resource
    private CpCPhyWarehouseMapper cpCPhyWarehouseMapper;

    /**
     * @param request 2C残次寻源入参
     * @Description: 2C残次寻源
     * @Author: hwy
     * @Date: 2021/7/6 11:04
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsResult>
     **/
    @Override
    public ValueHolderV14<SgFindSourceStrategyOmsResult> execute(SgFindSourceStrategyCCRequest request) {

        log.info(LogUtil.format("SgFindSourceStrategyCCCmdImpl.execute 2C残次寻源派单 param:{}",
                SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_CC, "sourceBillNo:{}|tid:{}"),
                request.getTraceId(), request.getSourceBillNo(),
                request.getTid(), JSONObject.toJSONString(request));

        ValueHolderV14<SgFindSourceStrategyOmsResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS,
                SgConstants.MESSAGE_STATUS_SUCCESS);
        SgFindSourceStrategyOmsResult omsResult = new SgFindSourceStrategyOmsResult();
        omsResult.setInventedOccupy(request.getInventedOccupy());
        String lockKey = null;
        long startTime = System.currentTimeMillis();
        try {
            //防重锁
            lockKey = SgConstants.REDIS_KEY_FIND_SOURCE_STRATEGY_CC.concat(request.getSourceBillId().toString());
            SgRedisLockUtils.lock(lockKey);
            //参数检查
            valueHolderV14 = checkParam(request);
            if (!valueHolderV14.isOK() && request.getRepeatCustomer()) {
                //因为MQ重复消费导致检验不通过，不做任何处理，并在后面不发送回执消息支告警
                log.error(LogUtil.format("SgFindSourceStrategyCCCmdImpl.execute 2C残次寻源派单 参数检查错误:{}",
                        SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_CC),
                        request.getTraceId(), valueHolderV14.getMessage());
                return valueHolderV14;
            }
            if (!valueHolderV14.isOK()) {
                log.error(LogUtil.format("SgFindSourceStrategyCCCmdImpl.execute 2C残次寻源派单 参数检查错误:{}",
                        SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_CC),
                        request.getTraceId(), valueHolderV14.getMessage());

                omsResult.setMessage(valueHolderV14.getMessage());
                setAllStockOut(request, omsResult, ResultCode.FAIL);
                return valueHolderV14;
            }
            //库存校验
            try {
                //校验寻源策略定义是否存在
                querySourceStrategy(request);
                // 每行的仓辐射交集
                List<Long> sameStoreIdList = new ArrayList<>();
                // 每个仓的最大优先级
                Map<Long, Integer> priorityMap = new HashMap<>();
                //执行仓辐射
                if (request.getAppointWarehouseId() == null) {
                    sameStoreIdList = executeTocStrategy(request, sameStoreIdList, priorityMap);
                } else {
                    sameStoreIdList.add(request.getAppointWarehouseId());
                    priorityMap.put(request.getAppointWarehouseId(), 100);
                }
                //查询实体仓
                List<SgCpCPhyWarehouse> warehouseList = cpCPhyWarehouseMapper.selectBatchIds(sameStoreIdList);
                if (CollectionUtils.isEmpty(warehouseList)) {
                    throw new NDSException("辐射实体仓档案不存在,实体仓id:" + JSONObject.toJSONString(sameStoreIdList));
                }
                Map<Long, SgCpCPhyWarehouse> warehouseMap = warehouseList.stream()
                        .collect(Collectors.toMap(SgCpCPhyWarehouse::getId, Function.identity()));
                //查询逻辑仓冻结仓库存（1.0只查询质检仓库存）
                List<SgBFreezeStorage> freezeStorageList = queryFreezeStorage(request, sameStoreIdList);
                //将库存信息转换为结果集
                setResult(request, freezeStorageList, warehouseMap, priorityMap);
                //判断是否存在唯一仓发货
                collectOneWarehouse(request, priorityMap);
            } catch (NDSException e) {
                omsResult.setMessage(e.getMessage());
                setAllStockOut(request, omsResult, ResultCode.FAIL);
                return valueHolderV14;
            }

            //生成冻结占用单 占用库存
            ValueHolderV14<SgFindSourceStrategyOmsResult> occupyValueHolder = ccPreOutStorageService.occupyStorage(request);
            if (StrategyConstants.RESULT_CODE_PREOUT != occupyValueHolder.getCode()) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(occupyValueHolder.getMessage());
                valueHolderV14.setData(omsResult);
                omsResult.setMessage(occupyValueHolder.getMessage());
                SgFindSourceStrategyOmsResult occupyDataReslut = occupyValueHolder.getData();
                if (ObjectUtils.isNotEmpty(occupyDataReslut)) {
                    omsResult.setIsS2LRetry(occupyDataReslut.getIsS2LRetry());
                }
                int resultCode = StrategyConstants.RESULT_CODE_NO_PREOUT == occupyValueHolder.getCode() ?
                        ResultCode.SUCCESS : ResultCode.FAIL;
                setAllStockOut(request, omsResult, resultCode);
                return valueHolderV14;
            }
            omsResult = occupyValueHolder.getData();
            valueHolderV14.setData(omsResult);
        } catch (Exception e) {
            log.error(LogUtil.format("SgFindSourceStrategyCCCmdImpl.execute 2C残次寻源派单 执行策略发生异常. exception:{}",
                    SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_CC_EXCEPTION, "sourceBillNo:{}"),
                    request.getTraceId(), request.getSourceBillNo(), Throwables.getStackTraceAsString(e));

            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("2C寻源派单 执行策略发生异常!");
            omsResult.setMessage("2C寻源派单 执行策略发生异常!");
            setAllStockOut(request, omsResult, ResultCode.FAIL);
            return valueHolderV14;
        } finally {
            log.info(LogUtil.format("SgFindSourceStrategyCCCmdImpl.execute 2C残次寻源派单 result:{} spend time:{}ms",
                    SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_CC, "sourceBillNo:{}|tid:{}"),
                    request.getTraceId(), request.getSourceBillNo(),
                    request.getTid(), JSONObject.toJSONString(omsResult), System.currentTimeMillis() - startTime);

            // 发送mq消息 给oms 拆单详情
            if (!StringUtils.isEmpty(lockKey)) {
                SgRedisLockUtils.unlock(lockKey, log, this.getClass().getName());
            }
            sendMq(request, omsResult);
        }

        return valueHolderV14;
    }

    /**
     * 确认唯一仓
     *
     * @param request     入参
     * @param priorityMap 实体仓优先级
     */
    private void collectOneWarehouse(SgFindSourceStrategyCCRequest request, Map<Long, Integer> priorityMap) {
        List<Long> oneStockWarehouseIds = FindSourceStrategyUtils.getOneStockWarehouseIdsByCC(request);
        if (CollectionUtils.isEmpty(oneStockWarehouseIds)) {
            throw new NDSException("订单缺货，不存在整单满足的实体仓;");
        }
        //获取优先级最大的实体仓
        Long maxPriorityWarehouseId = null;
        Integer maxPriority = 0;
        for (Long warehouseId : oneStockWarehouseIds) {
            if (priorityMap.get(warehouseId) != null && priorityMap.get(warehouseId).compareTo(maxPriority) >= 0) {
                maxPriorityWarehouseId = warehouseId;
                maxPriority = priorityMap.get(warehouseId);
            }
        }
        if (maxPriorityWarehouseId == null) {
            throw new NDSException("获取优先级最大实体仓失败;");
        }
        request.setMaxPriorityWarehouseId(maxPriorityWarehouseId);
    }

    /**
     * 查询逻辑冻结库存
     *
     * @param request         入参
     * @param sameStoreIdList 辐射仓交集
     * @return
     */
    private List<SgBFreezeStorage> queryFreezeStorage(SgFindSourceStrategyCCRequest request, List<Long> sameStoreIdList) {
        List<SgCpCStore> storeList = storeMapper.selectCpCStoreByWarehouseIdList(sameStoreIdList);
        if (CollectionUtils.isEmpty(storeList)) {
            throw new NDSException("辐射实体仓不存在有效的逻辑仓,实体仓id:" + JSONObject.toJSONString(sameStoreIdList));
        }
        //执行仓排除
        storeList = executeFilterStrategy(request, storeList);
        List<Long> skuIds = new ArrayList<>();
        for (SkuItemCC skuItem : request.getSkuItems()) {
            skuIds.add(skuItem.getPsCSkuId());
        }
        List<Long> storeIds = new ArrayList<>();
        List<String> storeCodes = new ArrayList<>();
        Map<Long, Long> storeToPhy = new HashMap<>();
        Map<Long, SgCpCStore> sgCpCStoreMap = new HashMap<>();
        for (SgCpCStore sgCpCStore : storeList) {
            storeIds.add(sgCpCStore.getId());
            storeCodes.add(sgCpCStore.getCpCStoreEcode());
            storeToPhy.put(sgCpCStore.getId(), sgCpCStore.getCpCPhyWarehouseId());
            sgCpCStoreMap.put(sgCpCStore.getId(), sgCpCStore);
        }
        request.setStoreToPhy(storeToPhy);
        request.setSgCpCStoreMap(sgCpCStoreMap);
        //逻辑仓冻结库存
        SgBFreezeStorageQueryNewRequest newRequest = new SgBFreezeStorageQueryNewRequest();
        newRequest.setStoreIdList(storeIds);
        newRequest.setSkuIdList(skuIds);
        newRequest.setStoreTypeList(Lists.newArrayList(StoreTypeEnum.ZJ.getValue()));
        ValueHolderV14<List<SgBFreezeStorage>> v14 = sgBFreezeStorageQueryService.queryFreezeStorageNew(newRequest);
        if (!v14.isOK()) {
            throw new NDSException(v14.getMessage());
        }
        List<SgBFreezeStorage> freezeStorageList = v14.getData();
        if (CollectionUtils.isEmpty(freezeStorageList)) {
            throw new NDSException("未查询到逻辑仓冻结库存，逻辑仓:" + JSONObject.toJSONString(storeCodes));
        }
        return freezeStorageList;
    }

    /**
     * 执行仓辐射
     *
     * @param request   入参
     * @param storeList 逻辑仓集合
     * @return
     */
    private List<SgCpCStore> executeFilterStrategy(SgFindSourceStrategyCCRequest request, List<SgCpCStore> storeList) {
        if (request.getAppointWarehouseId() == null) {
            SgCChannelSourceStrategyForceItemMapper forceItemMapper =
                    ApplicationContextHandle.getBean(SgCChannelSourceStrategyForceItemMapper.class);
            LambdaQueryWrapper<SgCChannelSourceStrategyForceItem> itemWrapper = new LambdaQueryWrapper<>();
            itemWrapper.eq(SgCChannelSourceStrategyForceItem::getSgCChannelSourceStrategyId, request.getSourceStrategy().getId());
            itemWrapper.eq(SgCChannelSourceStrategyForceItem::getIsactive, SgConstants.IS_ACTIVE_Y);
            List<SgCChannelSourceStrategyForceItem> forceItems = forceItemMapper.selectList(itemWrapper);
            if (CollectionUtils.isNotEmpty(forceItems)) {
                List<Long> forceStoreIds = forceItems.stream()
                        .map(SgCChannelSourceStrategyForceItem::getCpCStoreId).collect(Collectors.toList());
                storeList = storeList.stream()
                        .filter(store -> !forceStoreIds.contains(store.getId()))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(storeList)) {
                throw new NDSException("执行仓排除之后逻辑仓为空！");
            }
        }
        return storeList;
    }

    /**
     * 执行仓辐射策略
     *
     * @param request         入参
     * @param sameStoreIdList 交集实体仓
     * @param priorityMap     实体仓优先级
     * @return
     */
    private List<Long> executeTocStrategy(SgFindSourceStrategyCCRequest request,
                                          List<Long> sameStoreIdList, Map<Long, Integer> priorityMap) {
        // 查询toc仓辐射策略
        List<SgCTocStrategy> strategyList = queryTocStrategy(request);
        log.info("SgFindSourceStrategyCCCmdImpl.executeTocStrategy strategyList:{}",
                JSONObject.toJSONString(strategyList));
        // 对策略结果分别按照商品id、品相 分组，后续使用
        Map<Long, List<SgCTocStrategy>> proMap = new HashMap<>();
        Map<Long, List<SgCTocStrategy>> dimMap = new HashMap<>();
        Map<Long, List<SgCTocStrategy>> dim4Map = new HashMap<>();
        List<SgCTocStrategy> noAllList = new ArrayList<>();
        groupBySkuAndDim(strategyList, proMap, dimMap, dim4Map, noAllList);
        //执行仓辐射
        sameStoreIdList = getStrategyWarehouseByItem(request, sameStoreIdList,
                priorityMap, proMap, dimMap, dim4Map, noAllList);
        // 仓辐射交集为空
        if (CollectionUtils.isEmpty(sameStoreIdList)) {
            throw new NDSException("根据「TOC仓辐射设置」，不存在交集辐射仓；");
        }
        return sameStoreIdList;
    }

    /**
     * 根据订单行执行仓辐射
     *
     * @param request         入参
     * @param sameStoreIdList 交集实体仓
     * @param priorityMap     实体仓优先级
     * @param proMap          商品仓辐射
     * @param dimMap          四级仓辐射
     * @param dim4Map         一级仓辐射
     * @param noAllList       都没配
     * @return
     */
    private List<Long> getStrategyWarehouseByItem(SgFindSourceStrategyCCRequest request,
                                                  List<Long> sameStoreIdList, Map<Long, Integer> priorityMap,
                                                  Map<Long, List<SgCTocStrategy>> proMap,
                                                  Map<Long, List<SgCTocStrategy>> dimMap,
                                                  Map<Long, List<SgCTocStrategy>> dim4Map,
                                                  List<SgCTocStrategy> noAllList) {
        for (int i = 0; i < request.getSkuItems().size(); i++) {
            SkuItemCC skuItemCC = request.getSkuItems().get(i);
            List<Long> strategiesStoreIdList = new ArrayList<>();
            List<SgCTocStrategy> strategies = proMap.get(skuItemCC.getPsCProId());

            if (CollectionUtils.isNotEmpty(strategies)) {
                strategies.forEach(x -> {
                    Integer priority = priorityMap.get(x.getCpCPhyWarehouseId());
                    if (priority == null || priority < x.getReank()) {
                        priorityMap.put(x.getCpCPhyWarehouseId(), x.getReank());
                    }
                    strategiesStoreIdList.add(x.getCpCPhyWarehouseId());
                });
            }

            if (CollectionUtils.isEmpty(strategiesStoreIdList)) {
                strategies = dimMap.get(skuItemCC.getPsCProdimId());
                if (CollectionUtils.isNotEmpty(strategies)) {
                    strategies.forEach(x -> {
                        Integer priority = priorityMap.get(x.getCpCPhyWarehouseId());
                        if (priority == null || priority < x.getReank()) {
                            priorityMap.put(x.getCpCPhyWarehouseId(), x.getReank());
                        }
                        strategiesStoreIdList.add(x.getCpCPhyWarehouseId());
                    });
                }
            }
            if (CollectionUtils.isEmpty(strategiesStoreIdList)) {
                strategies = dim4Map.get(skuItemCC.getPsCProdim4Id());
                if (CollectionUtils.isNotEmpty(strategies)) {
                    strategies.forEach(x -> {
                        Integer priority = priorityMap.get(x.getCpCPhyWarehouseId());
                        if (priority == null || priority < x.getReank()) {
                            priorityMap.put(x.getCpCPhyWarehouseId(), x.getReank());
                        }
                        strategiesStoreIdList.add(x.getCpCPhyWarehouseId());
                    });
                } else {
                    // 20230208 新增保底策略
                    if (CollectionUtils.isNotEmpty(noAllList)) {
                        noAllList.forEach(x -> {
                            Integer priority = priorityMap.get(x.getCpCPhyWarehouseId());
                            if (priority == null || priority < x.getReank()) {
                                priorityMap.put(x.getCpCPhyWarehouseId(), x.getReank());
                            }
                            strategiesStoreIdList.add(x.getCpCPhyWarehouseId());
                        });
                    }
                }
            }
            // 初始化交集
            if (i == 0) {
                sameStoreIdList = strategiesStoreIdList;
            } else {
                sameStoreIdList.retainAll(strategiesStoreIdList);
            }
        }
        return sameStoreIdList;
    }

    /**
     * 分组TOC仓辐射，按照商品、一级、四级
     *
     * @param strategyList TOC仓辐射策略
     * @param proMap       商品分组
     * @param dimMap       四级分组
     * @param dim4Map      一级分组
     * @param noAllList    啥都没配
     * @return void
     */
    private void groupBySkuAndDim(List<SgCTocStrategy> strategyList,
                                  Map<Long, List<SgCTocStrategy>> proMap,
                                  Map<Long, List<SgCTocStrategy>> dimMap,
                                  Map<Long, List<SgCTocStrategy>> dim4Map,
                                  List<SgCTocStrategy> noAllList) {
        strategyList.forEach(x -> {
            if (x.getPsCProId() != null) {
                List<SgCTocStrategy> listByProId = proMap.get(x.getPsCProId());
                if (CollectionUtils.isEmpty(listByProId)) {
                    listByProId = new ArrayList<>();
                    proMap.put(x.getPsCProId(), listByProId);
                }
                listByProId.add(x);
                return;
            }
            if (x.getPsCProdimId() != null) {
                List<SgCTocStrategy> listByMiId = dimMap.get(x.getPsCProdimId());
                if (CollectionUtils.isEmpty(listByMiId)) {
                    listByMiId = new ArrayList<>();
                    dimMap.put(x.getPsCProdimId(), listByMiId);
                }
                listByMiId.add(x);
                return;
            }
            if (x.getPsCProdim4Id() != null) {
                List<SgCTocStrategy> listByMiId = dim4Map.get(x.getPsCProdim4Id());
                if (CollectionUtils.isEmpty(listByMiId)) {
                    listByMiId = new ArrayList<>();
                    dim4Map.put(x.getPsCProdim4Id(), listByMiId);
                }
                listByMiId.add(x);
                return;
            }
            noAllList.add(x);
        });
    }

    /**
     * 查询TOC仓辐射(不包含公斤段)
     *
     * @param request 入参
     * @return
     */
    private List<SgCTocStrategy> queryTocStrategy(SgFindSourceStrategyCCRequest request) {
        List<Long> proIdList = new ArrayList<>();
        List<Long> mid4IdList = new ArrayList<>();
        List<Long> miIdList = new ArrayList<>();
        request.getSkuItems().forEach(x -> {
            if (!proIdList.contains(x.getPsCProId())) {
                proIdList.add(x.getPsCProId());
            }
            if (!mid4IdList.contains(x.getPsCProdim4Id())) {
                mid4IdList.add(x.getPsCProdim4Id());
            }
            if (!miIdList.contains(x.getPsCProdimId())) {
                miIdList.add(x.getPsCProdimId());
            }
        });
        // 这里先对仓辐射辐射级排序
        SgCTocStrategyMapper mapper = ApplicationContextHandle.getBean(SgCTocStrategyMapper.class);
        LambdaQueryWrapper<SgCTocStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SgCTocStrategy::getCpCProvinceId, request.getProvinceId());
        wrapper.and(x -> x.in(SgCTocStrategy::getPsCProId, proIdList)
                .or().in(SgCTocStrategy::getPsCProdimId, miIdList)
                .or().in(SgCTocStrategy::getPsCProdim4Id, mid4IdList)
                .or(y -> y.isNull(SgCTocStrategy::getPsCProdimId)
                        .isNull(SgCTocStrategy::getPsCProdim4Id)
                        .isNull(SgCTocStrategy::getPsCProId)));
        wrapper.isNull(SgCTocStrategy::getStartWeight);
        wrapper.isNull(SgCTocStrategy::getEndWeight);
        wrapper.eq(SgCTocStrategy::getIsactive, SgConstants.IS_ACTIVE_Y);
        wrapper.orderByDesc(SgCTocStrategy::getReank);
        List<SgCTocStrategy> strategyList = mapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(strategyList)) {
            throw new NDSException("根据「TOC仓辐射设置」，未配置仓辐射策略；");
        }
        return strategyList;
    }

    /**
     * 查询【寻源策略定义】
     *
     * @param request 入参
     */
    private void querySourceStrategy(SgFindSourceStrategyCCRequest request) {
        Date currDate = new Date();
        SgCChannelSourceStrategy strategy = strategyMapper.selectOne(
                new LambdaQueryWrapper<SgCChannelSourceStrategy>()
                        .eq(SgCChannelSourceStrategy::getCpCShopId, request.getShopId())
                        .eq(SgCChannelSourceStrategy::getStatus, SgSourcingConstants.BILL_SOURCE_STRATEGY_SUBMIT)
                        .eq(SgCChannelSourceStrategy::getType, SgConstants.SG_C_CHANNEL_SOURCE_STRATEGY_TYPE_SHOP)
                        .eq(SgCChannelSourceStrategy::getIsactive, SgConstants.IS_ACTIVE_Y)
                        .ge(SgCChannelSourceStrategy::getEndTime, currDate)
                        .le(SgCChannelSourceStrategy::getBeginTime, currDate)
                        .orderByDesc(SgCChannelSourceStrategy::getStatusTime)
                        .last("limit 1")
        );

        if (strategy == null) {
            strategy = strategyMapper.selectOne(
                    new LambdaQueryWrapper<SgCChannelSourceStrategy>()
                            .eq(SgCChannelSourceStrategy::getStatus, SgSourcingConstants.BILL_SOURCE_STRATEGY_SUBMIT)
                            .eq(SgCChannelSourceStrategy::getType, SgConstants.SG_C_CHANNEL_SOURCE_STRATEGY_TYPE_ALL)
                            .eq(SgCChannelSourceStrategy::getIsactive, SgConstants.IS_ACTIVE_Y)
                            .ge(SgCChannelSourceStrategy::getEndTime, currDate)
                            .le(SgCChannelSourceStrategy::getBeginTime, currDate)
                            .orderByDesc(SgCChannelSourceStrategy::getStatusTime)
                            .last("limit 1")
            );

            if (strategy == null) {
                log.warn("2C残次寻源派单 店铺ID:{}未定义寻源策略", request.getShopId());
                throw new NDSException(Resources.getMessage("店铺未定义寻源策略!"));
            }
        }
        request.setSourceStrategy(strategy);
    }

    /**
     * @param request:
     * @param omsResult:
     * @Description: 全部缺货
     * @Author: hwy
     * @Date: 2021/7/5 14:18
     * @return: void
     **/
    private void setAllStockOut(SgFindSourceStrategyCCRequest request,
                                SgFindSourceStrategyOmsResult omsResult,
                                int resultCode) {

        List<SkuItemCC> skuItems = request.getSkuItems();
        omsResult.setSourceBillId(request.getSourceBillId());
        omsResult.setSourceBillType(request.getSourceBillType());
        omsResult.setSourceBillNo(request.getSourceBillNo());
        omsResult.setCode(resultCode);
        List<SgFindSourceStrategyOmsItemResult> itemResultList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        List<Long> sourceItemIds = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        omsResult.setItemResultList(itemResultList);
        if (resultCode == ResultCode.SUCCESS) {
            for (SkuItemCC skuItem : skuItems) {
                BigDecimal qty = skuItem.getQty();
                Long sourceItemId = skuItem.getSourceItemId();
                if (sourceItemIds.contains(sourceItemId)) {
                    continue;
                }
                SgFindSourceStrategyOmsItemResult omsItem = new SgFindSourceStrategyOmsItemResult();
                omsItem.setQtyPreOut(qty);
                omsItem.setWareHouseId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                omsItem.setSourceItemId(sourceItemId);
                sourceItemIds.add(sourceItemId);
                itemResultList.add(omsItem);
            }
        }
    }

    /**
     * @param request:
     * @param omsResult:
     * @Description: 发送mq给oms
     * @Author: hwy
     * @Date: 2021/7/6 11:04
     * @return: void
     **/
    private void sendMq(SgFindSourceStrategyCCRequest request, SgFindSourceStrategyOmsResult omsResult) {

        String topic = null;
        String configName = null;
        String tag = null;
        String body = null;
        String msgKey = null;

        try {

            log.info(LogUtil.format("SgFindSourceStrategyCCCmdImpl.sendMq 2C残次寻源派单 向OMS推送寻源结果 omsResult:{}",
                    SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_CC, "sourceBillNo:{}"),
                    request.getTraceId(), request.getSourceBillNo(),
                    JSONObject.toJSONString(omsResult));

            if (Boolean.TRUE.equals(omsResult.getIsS2LRetry())) {
                log.warn("SgFindSourceStrategyCCCmdImpl.sendMq 逻辑仓缺货，二阶段重新寻源，不返回订单中心寻源结果");
                return;
            }
            if (request.getRepeatCustomer()) {
                String text = "订单id:" + request.getSourceBillId() + request.getRepeatCustomerErrorMsg();
                //重复消费了，不回执消息
                DingTalkUtil.sendTextMsg(DingTalkTokenEnum.MQ_REPEAT_CUSTOMER,
                        "[" + EnvEnum.getEnv().name() + "]" + text, false);
                return;
            }

            topic = "R3_SG_TOBECONFIRM_CALL_BACK";
            configName = "default";

            //需要改只有配货单情况需要使用tag
            if (SgConstantsIF.BILL_SHARE_DISTRIBUTION == request.getSourceBillType()) {
                tag = StrategyConstants.APOLLO_KEY_SOURCING_CALL_BACK_DISTRIBUTION_TAG;
            } else if (SgConstantsIF.BILL_TYPE_FOR_WAREHOUSE == request.getSourceBillType()) {
                //寻仓单 返回tag
                tag = StrategyConstants.APOLLO_KEY_SOURCING_CALL_BACK_WAREHOUSE_TAG;
            } else {
                tag = StrategyConstants.APOLLO_KEY_SOURCING_CALL_BACK_TAG;
            }

            if (StringUtils.isEmpty(tag) || StringUtils.isEmpty(configName) || StringUtils.isEmpty(topic)) {
                log.error(LogUtil.format("SgFindSourceStrategyCCCmdImpl.sendMq 2C残次寻源派单 回传OMS的MQ配置不存在，发送消息失败",
                        SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_CC, "sourceBillNo:{}"),
                        request.getTraceId(), request.getSourceBillNo());
                return;
            }

            msgKey = StrategyConstants.MQ_PREFIX.concat(request.getSourceBillId().toString()).concat(DateUtil.getDateTime(SgConstants.MQ_DATE_FORMAT));
            body = JSONObject.toJSONString(omsResult);

            FindSourceStrategyUtils.outputLog(LogUtil.format("SgFindSourceStrategyCCCmdImpl.sendMq 2C残次寻源派单 寻源派单结束发送消息到OMS " +
                            "topic:{} messageKey:{} messageBody:{}",
                    SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_CC, "sourceBillNo:{}"),
                    request.getTraceId(), request.getSourceBillNo(),
                    topic, msgKey, body);

            defaultProducerSend.sendDelayTopic(sgStorageMqConfig.getMq5tobeconfirmCallBackDelay(),
                    tag,
                    body,
                    msgKey,
                    delayTime);
        } catch (Exception e) {
            log.error(LogUtil.format("SgFindSourceStrategyCCCmdImpl.sendMq 2C残次寻源派单 寻源派单结束发送消息到OMS发生异常. exception:{}",
                    SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_CC_EXCEPTION),
                    request.getTraceId(), Throwables.getStackTraceAsString(e));

            SgBMonitorMqErrorDatasRequest sgBMonitorMqErrorDatasRequest = new SgBMonitorMqErrorDatasRequest();
            sgBMonitorMqErrorDatasRequest.setConsumer(SgMonitorMqEnum.OMS);
            sgBMonitorMqErrorDatasRequest.setProducer(SgMonitorMqEnum.SG);
            sgBMonitorMqErrorDatasRequest.setMessage(body);
            sgBMonitorMqErrorDatasRequest.setMsgKey(msgKey);
            sgBMonitorMqErrorDatasRequest.setTag(tag);
            sgBMonitorMqErrorDatasRequest.setTopic(topic);
            sgMonitorUtils.saveMqErrorDatas(sgBMonitorMqErrorDatasRequest, e, R3SystemUserResource.getSystemRootUser());
        }
    }

    /**
     * 将库存信息转换为结果集
     *
     * @param request      入参
     * @param results      逻辑仓冻结库存
     * @param warehouseMap 实体仓档案
     * @param priorityMap  实体仓优先级
     */
    private void setResult(SgFindSourceStrategyCCRequest request, List<SgBFreezeStorage> results,
                           Map<Long, SgCpCPhyWarehouse> warehouseMap,
                           Map<Long, Integer> priorityMap) {

        FindSourceStrategyUtils.outputLog(
                LogUtil.format("SgFindSourceStrategyCCCmdImpl.setResult 残次寻源派单 逻辑仓冻结库存:{}",
                        SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_CC),
                request.getTraceId(), JSONObject.toJSONString(results));
        Map<Long, Long> storeToPhy = request.getStoreToPhy();

        //处理库存信息
        //key:psCSkuId value:<warehouseId,<cpCStoreId,<produceDate,availableQty>>>
        Map<Long, Map<Long, Map<Long, SortedMap<String, BigDecimal>>>> storageMap =
                new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

        if (CollectionUtils.isNotEmpty(results)) {
            results.stream().filter(o -> BigDecimal.ZERO.compareTo(o.getQtyAvailable()) < 0).forEach(o -> {

                BigDecimal qtyAvailable = o.getQtyAvailable();
                Long psCSkuId = o.getPsCSkuId();
                Long cpCStoreId = o.getCpCStoreId();
                Long cpCPhyWarehouseId = storeToPhy.get(cpCStoreId);
                String produceDate = o.getProduceDate();

                Map<Long, Map<Long, SortedMap<String, BigDecimal>>> warehouseInfoMap;
                Map<Long, SortedMap<String, BigDecimal>> logicStorageMap;
                SortedMap<String, BigDecimal> produceDateMap;

                if (storageMap.containsKey(psCSkuId)) {
                    warehouseInfoMap = storageMap.get(psCSkuId);
                    if (warehouseInfoMap.containsKey(cpCPhyWarehouseId)) {
                        logicStorageMap = warehouseInfoMap.get(cpCPhyWarehouseId);
                        if (logicStorageMap.containsKey(cpCStoreId)) {
                            produceDateMap = logicStorageMap.get(cpCStoreId);
                            if (produceDateMap.containsKey(produceDate)) {
                                qtyAvailable = qtyAvailable.add(
                                        produceDateMap.get(produceDate) == null ?
                                                BigDecimal.ZERO : produceDateMap.get(produceDate));
                            }
                        } else {
                            //生成<produceDate,availableQty>
                            produceDateMap = new TreeMap();
                            logicStorageMap.put(cpCStoreId, produceDateMap);
                        }
                    } else {
                        //生成<cpCStoreId,<produceDate,availableQty>>
                        logicStorageMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
                        warehouseInfoMap.put(cpCPhyWarehouseId, logicStorageMap);
                        produceDateMap = new TreeMap();
                        logicStorageMap.put(cpCStoreId, produceDateMap);
                    }
                    produceDateMap.put(produceDate, qtyAvailable);
                } else {
                    //生成<warehouseId,<cpCStoreId,<produceDate,availableQty>>>
                    warehouseInfoMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
                    storageMap.put(psCSkuId, warehouseInfoMap);
                    logicStorageMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
                    warehouseInfoMap.put(cpCPhyWarehouseId, logicStorageMap);
                    produceDateMap = new TreeMap();
                    logicStorageMap.put(cpCStoreId, produceDateMap);
                    produceDateMap.put(o.getProduceDate(), qtyAvailable);
                }
            });
        }

        //将库存信息转换成结果集
        List<SkuItemCC> skuItems = request.getSkuItems();

        SgFindSourceStrategyCCResult sgFindSourceStrategyCCResult = new SgFindSourceStrategyCCResult();
        request.setStrategyBaseResult(sgFindSourceStrategyCCResult);
        List<SgFindSourceStrategySkuCCResult> skuCCResultList = new ArrayList<>(skuItems.size());
        sgFindSourceStrategyCCResult.setSkuResultList(skuCCResultList);

        SgFindSourceStrategySkuCCResult skuCCResult = null;
        List<SgFindSourceStrategyStoreItemCCResult> itemCCResultList = null;
        SgFindSourceStrategyStoreItemCCResult logicItem = null;
        Long psCSkuId = null;
        Long sourceItemId = null;
        String beginProduceDate = null;
        String endProduceDate = null;
        BigDecimal wareHouseQty = null;
        SortedMap<String, BigDecimal> originProduceDateMap = null;
        SortedMap<String, BigDecimal> filterProduceDateMap = null;
        Map<Long, Map<Long, SortedMap<String, BigDecimal>>> phyStorageMap = null;
        Map<Long, SortedMap<String, BigDecimal>> filterLogicStorageMap = null;
        Map<Long, SortedMap<String, BigDecimal>> originStoreProduceDateMap = null;

        //遍历明细
        for (SkuItemCC skuItemCC : skuItems) {

            psCSkuId = skuItemCC.getPsCSkuId();
            sourceItemId = skuItemCC.getSourceItemId();
            beginProduceDate = StringUtils.isEmpty(skuItemCC.getBeginProduceDate()) ?
                    SgConstantsIF.DEFAULT_PRODUCE_DATE : skuItemCC.getBeginProduceDate();
            endProduceDate = StringUtils.isEmpty(skuItemCC.getEndProduceDate()) ?
                    SgConstantsIF.MAX_PRODUCE_DATE : skuItemCC.getEndProduceDate();

            //创建明细对应的结果过集
            skuCCResult = new SgFindSourceStrategySkuCCResult();
            skuCCResult.setPsCSkuId(psCSkuId);
            skuCCResult.setSourceItemId(sourceItemId);
            skuCCResult.setPsCProId(skuItemCC.getPsCProId());
            skuCCResult.setPsCProEcode(skuItemCC.getPsCProEcode());
            skuCCResult.setPsCSkuEcode(skuItemCC.getPsCSkuEcode());
            skuCCResult.setPsCProdimId(skuItemCC.getPsCProdimId());
            skuCCResult.setPsCProdim4Id(skuItemCC.getPsCProdim4Id());
            itemCCResultList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
            skuCCResult.setItemResultList(itemCCResultList);

            skuCCResult.setBeginProduceDate(skuItemCC.getBeginProduceDate());
            skuCCResult.setEndProduceDate(skuItemCC.getEndProduceDate());
            skuCCResult.setQtyPreOut(skuItemCC.getQtyPreOut());

            skuCCResultList.add(skuCCResult);

            //获取明细对应的库存信息
            phyStorageMap = storageMap.get(psCSkuId);

            //不存在库存 设置为缺货
            if (MapUtils.isEmpty(phyStorageMap)) {
                logicItem = new SgFindSourceStrategyStoreItemCCResult();
                logicItem.setStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                logicItem.setLogicStorageMap(new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY));
                //计划占用数量与库存数量不共用，设置尝试去除
                //logicItem.setQty(skuItemCC.getQtyPreOut());
                itemCCResultList.add(logicItem);
                continue;
            }

            // 将库存信息放入结果集<warehouseId,<cpCStoreId,<produceDate,availableQty>>>
            for (Map.Entry<Long, Map<Long, SortedMap<String, BigDecimal>>> entry : phyStorageMap.entrySet()) {

                //同Sku，聚合仓，实体仓，因效期要求不同不能复用
                logicItem = new SgFindSourceStrategyStoreItemCCResult();

                filterLogicStorageMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
                originStoreProduceDateMap = entry.getValue();
                wareHouseQty = BigDecimal.ZERO;

                for (Long storeId : originStoreProduceDateMap.keySet()) {

                    originProduceDateMap = originStoreProduceDateMap.get(storeId);

                    if (originProduceDateMap == null) {
                        continue;
                    }

                    filterProduceDateMap = new TreeMap();

                    for (String produceDate : originProduceDateMap.keySet()) {
                        //过滤满足效期要求的逻辑生产日期可用库存
                        if (beginProduceDate.compareTo(produceDate) <= 0 && endProduceDate.compareTo(produceDate) >= 0) {
                            wareHouseQty = wareHouseQty.add(originProduceDateMap.get(produceDate));
                            filterProduceDateMap.put(produceDate, originProduceDateMap.get(produceDate));
                        }
                    }

                    if (!MapUtils.isEmpty(filterProduceDateMap)) {
                        filterLogicStorageMap.put(storeId, filterProduceDateMap);
                    }
                }

                //不存在库存 设置为缺货
                if (MapUtils.isEmpty(filterLogicStorageMap)) {
                    continue;
                }

                logicItem.setStoreId(entry.getKey());
                logicItem.setPhywarehouseEname(warehouseMap.getOrDefault(entry.getKey(), new SgCpCPhyWarehouse()).getEname());
                logicItem.setLogicStorageMap(filterLogicStorageMap);
                logicItem.setQty(wareHouseQty);
                logicItem.setPriority(priorityMap.getOrDefault(entry.getKey(), 0));
                itemCCResultList.add(logicItem);
            }

            //不存在库存 设置为缺货
            if (CollectionUtils.isEmpty(itemCCResultList)) {
                logicItem = new SgFindSourceStrategyStoreItemCCResult();
                logicItem.setStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                logicItem.setLogicStorageMap(new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY));
                //计划占用数量与库存数量不共用，设置尝试去除
                //logicItem.setQty(skuItemCC.getQtyPreOut());
                itemCCResultList.add(logicItem);
            }
        }

        FindSourceStrategyUtils.outputLog(
                LogUtil.format("SgFindSourceStrategyCCCmdImpl.setResult 2C残次寻源派单 结果集过滤效期逻辑仓结果:{}",
                        SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_CC),
                request.getTraceId(), JSONObject.toJSONString(request));
    }

    /**
     * @param strategyRequest: 入参检查
     * @Description:
     * @Author: hwy
     * @Date: 2021/6/23 11:24
     * @return: void
     **/
    private ValueHolderV14<SgFindSourceStrategyOmsResult> checkParam(SgFindSourceStrategyCCRequest strategyRequest) {

        ValueHolderV14<SgFindSourceStrategyOmsResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS,
                SgConstants.MESSAGE_STATUS_SUCCESS);
        if (strategyRequest == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("2C残次寻源引擎 参数为空!");
            return valueHolderV14;
        }

        StrategyBaseResult strategyBaseResult = new SgFindSourceStrategyCCResult();
        strategyRequest.setStrategyBaseResult(strategyBaseResult);
        List<SkuItemCC> skuItems = strategyRequest.getSkuItems();
        if (CollectionUtils.isEmpty(skuItems)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("2C残次寻源引擎 订单明细不能为空!");
            return valueHolderV14;
        }

        if (strategyRequest.getSourceBillId() == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("2C残次寻源引擎 来源单据id不能为空!");
            return valueHolderV14;
        }

        if (strategyRequest.getSourceBillType() == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("2C残次寻源引擎 来源单据类型不能为空!");
            return valueHolderV14;
        }

        if (strategyRequest.getSourceBillNo() == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("2C残次寻源引擎 来源单据编号不能为空!");
            return valueHolderV14;
        }

        if (strategyRequest.getShopId() == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("2C残次寻源引擎 店铺id不能为空!");
            return valueHolderV14;
        }


        if (strategyRequest.getBillDate() == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("2C残次寻源引擎 单据日期不能为空!");
            return valueHolderV14;
        }

        if (strategyRequest.getProvinceId() == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("2C残次寻源引擎 省不能为空!");
            return valueHolderV14;
        }

        for (SkuItemCC skuItem : skuItems) {
            Long sourceItemId = skuItem.getSourceItemId();

            if (sourceItemId == null) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("2C残次寻源引擎 明细来源id不能为空!");
                return valueHolderV14;
            }

            if (skuItem.getPsCSkuId() == null) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("2C残次寻源引擎 来源明细id:" + sourceItemId + "系统条码不能为空!");
                return valueHolderV14;
            }

            if (skuItem.getQty() == null) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("2C残次寻源引擎 来源明细id:" + sourceItemId + "订单数量不能为空!");
                return valueHolderV14;
            }
            skuItem.setQtyPreOut(skuItem.getQty());
        }

        // 查询来源单据是否已有冻结占用
        SgBStoFreezeOutMapper sgBStoOutMapper = ApplicationContextHandle.getBean(SgBStoFreezeOutMapper.class);
        Integer count = sgBStoOutMapper.selectCount(new LambdaQueryWrapper<SgBStoFreezeOut>()
                .eq(SgBStoFreezeOut::getSourceBillId, strategyRequest.getSourceBillId())
                .eq(SgBStoFreezeOut::getSourceBillType, strategyRequest.getSourceBillType())
                .eq(SgBStoFreezeOut::getIsactive, YesNoEnum.Y.getKey()));
        if (count > 0) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("2C残次寻源引擎 已存在冻结占用单,不允许重复寻源!");
            strategyRequest.setRepeatCustomer(Boolean.TRUE);
            strategyRequest.setRepeatCustomerErrorMsg("2C残次寻源引擎 已存在冻结占用单,不允许重复寻源!");
            return valueHolderV14;
        }
        return valueHolderV14;
    }
}