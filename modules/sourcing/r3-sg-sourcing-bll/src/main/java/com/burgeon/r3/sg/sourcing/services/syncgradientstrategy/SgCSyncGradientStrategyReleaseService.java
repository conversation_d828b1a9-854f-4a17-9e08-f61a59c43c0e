package com.burgeon.r3.sg.sourcing.services.syncgradientstrategy;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.sourcing.syncgradientstrategy.SgCSyncGradientStrategy;
import com.burgeon.r3.sg.core.model.table.sourcing.syncgradientstrategy.SgCSyncGradientStrategyItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.sourcing.mapper.syncgradientstrategy.SgCSyncGradientStrategyItemMapper;
import com.burgeon.r3.sg.sourcing.mapper.syncgradientstrategy.SgCSyncGradientStrategyMapper;
import com.burgeon.r3.sg.stocksync.api.SgChannelStorageOmsInitCmd;
import com.burgeon.r3.sg.stocksync.common.enums.ChannelStoragePoolTypeEnum;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageOmsManualSynchRequest;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/7/14 19:00
 * 配销仓库存梯度策略 库存同步(按钮)
 */
@Slf4j
@Component
public class SgCSyncGradientStrategyReleaseService {
    @Autowired
    private SgCSyncGradientStrategyItemMapper itemMapper;

    @Autowired
    private SgCSyncGradientStrategyMapper mapper;


    public ValueHolder strategyRelease(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        SgCSyncGradientStrategyReleaseService bean = ApplicationContextHandle.getBean(SgCSyncGradientStrategyReleaseService.class);
        return R3ParamUtils.convertV14WithResult(bean.release(request));
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> release(SgR3BaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgCChannelRatioStrategyReleaseService.release.ReceiveParams:request{}"
                    , JSONObject.toJSONString(request));
        }
        List<SgCSyncGradientStrategyItem> gradientStrategyItems = checkParams(request);
        Long objId = request.getObjId();
        User loginUser = request.getLoginUser();
        String lockKey = SgConstants.SG_C_CHANNEL_RATIO_STRATEGY + ":" + objId;
        SgRedisLockUtils.lock(lockKey);
        try {

            // 调用库存同步服务
            stocksync(loginUser, gradientStrategyItems);
            //修改主表状态
            SgCSyncGradientStrategy strategy = new SgCSyncGradientStrategy();
            strategy.setId(objId);
            StorageUtils.setBModelDefalutDataByUpdate(strategy, loginUser);
            strategy.setSyncId(loginUser.getId().longValue());
            strategy.setSyncTime(new Date());
            strategy.setSyncName(loginUser.getName());
            strategy.setSyncEname(loginUser.getEname());
            mapper.updateById(strategy);
        } catch (Exception e) {
            AssertUtils.logAndThrowException(e, loginUser.getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKey, log, this.getClass().getName());
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "库存同步成功");
    }

    /**
     * 库存同步
     *
     * @param loginUser             操作用户
     * @param gradientStrategyItems 店铺明细数据
     */
    public void stocksync(User loginUser, List<SgCSyncGradientStrategyItem> gradientStrategyItems) {
        if (log.isDebugEnabled()) {
            log.debug("配销仓库存梯度策略 stocksync gradientItems = {}", JSONObject.toJSONString(gradientStrategyItems));
        }
        List<Integer> shopIds = gradientStrategyItems.stream().map(o -> {
            return new Integer(o.getCpCShopId().intValue());
        }).collect(Collectors.toList());
        SgChannelStorageOmsManualSynchRequest request = new SgChannelStorageOmsManualSynchRequest();
        SgChannelStorageOmsInitCmd sgChannelStorageOmsInitCmd = (SgChannelStorageOmsInitCmd) ReferenceUtil.refer(
                ApplicationContextHandle.getApplicationContext(),
                SgChannelStorageOmsInitCmd.class.getName(),
                SgConstantsIF.GROUP, SgConstantsIF.VERSION);
        //设置操作方式为按查询条件同步
        request.setOperate(SgChannelStorageOmsManualSynchRequest.QUERY_BY_CONDITION);
        request.setUser(loginUser);
        request.setCpCShopIdList(shopIds);
        request.setSourceno("配销仓库存梯度策略-库存同步");
        request.setPoolType(ChannelStoragePoolTypeEnum.SYNC_POOL_ALL.getValue());
        ValueHolderV14<Boolean> holderV14 = sgChannelStorageOmsInitCmd.manualCalcAndSyncChannelProduct(request);
        JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(holderV14.toJSONObject(),
                SerializerFeature.WriteMapNullValue));
        if (log.isDebugEnabled()) {
            log.debug("配销仓库存梯度策略 手工计算库存并同步结果" + result.toJSONString());
        }
    }

    /**
     * 参数校验
     */
    private List<SgCSyncGradientStrategyItem> checkParams(SgR3BaseRequest reqeust) {
        AssertUtils.notNull(reqeust, "请求参数不能为空");
        Long objId = reqeust.getObjId();
        AssertUtils.notNull(objId, "主表id不能为空");
        SgCSyncGradientStrategy gradientStrategy = mapper.selectById(objId);
        AssertUtils.notNull(gradientStrategy, "当前记录不存在！");
        List<SgCSyncGradientStrategyItem> gradientStrategyItems = itemMapper.selectList(new LambdaQueryWrapper<SgCSyncGradientStrategyItem>()
                .eq(SgCSyncGradientStrategyItem::getSgCSyncGradientStrategyId, objId)
                .eq(SgCSyncGradientStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isEmpty(gradientStrategyItems)) {
            AssertUtils.logAndThrow("当前记录暂无店铺明细，不允许库存同步");
        }
        return gradientStrategyItems;
    }
}
