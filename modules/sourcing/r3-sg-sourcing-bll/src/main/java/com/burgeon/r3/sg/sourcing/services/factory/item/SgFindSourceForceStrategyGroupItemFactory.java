package com.burgeon.r3.sg.sourcing.services.factory.item;


import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.StrategyFactoryBaseResult;
import com.burgeon.r3.sg.sourcing.model.result.factory.SgFindSourceStrategyFactoryResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.services.factory.StrategyFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: hwy
 * @time: 2021/6/7 11:35
 */
@Component
@Slf4j
@Data
public class SgFindSourceForceStrategyGroupItemFactory extends StrategyFactory {

    private List<? extends StrategyFactoryBaseResult> strategiesList;

    private StrategyHandle handle;

    @Override
    public List<SgFindSourceStrategyFactoryResult> getStrategies(StrategyBaseRequest request) {

        ArrayList<SgFindSourceStrategyFactoryResult> results = new ArrayList<>();
        SgFindSourceStrategyFactoryResult result = new SgFindSourceStrategyFactoryResult();

        result.setPriority(10);
        result.setStrategyClazz("com.burgeon.r3.sg.sourcing.services.item.SgFindSourceFilterStrategyService");
        result.setStrategyName("逻辑仓过滤策略");
        results.add(result);

        result = new SgFindSourceStrategyFactoryResult();
        result.setPriority(20);
        result.setStrategyClazz("com.burgeon.r3.sg.sourcing.services.item.SgFindSourceRejectStoreFilterStrategyService");
        result.setStrategyName("拒单策略");
        results.add(result);

        return results;
    }
}