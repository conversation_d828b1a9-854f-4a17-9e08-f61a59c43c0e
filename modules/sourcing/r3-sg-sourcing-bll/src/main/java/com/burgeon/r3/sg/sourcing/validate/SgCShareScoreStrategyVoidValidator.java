package com.burgeon.r3.sg.sourcing.validate;

import com.burgeon.r3.sg.sourcing.model.dto.strategy.SgCShareScoreStrategyDto;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2021/6/9 14:41
 */
@Component
public class SgCShareScoreStrategyVoidValidator extends BaseSingleValidator<SgCShareScoreStrategyDto> {


    @Override
    public String getValidatorMsgName() {
        return "评分策略作废";
    }

    @Override
    public Class<?> getValidatorClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCShareScoreStrategyDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "评分策略保存成功");
        //todo 如果存在单据状态不为已作废的寻源策略使用了此评分策略，则提示：“当前记录已被寻源策略使用，不允许作废”

        return v14;
    }
}
