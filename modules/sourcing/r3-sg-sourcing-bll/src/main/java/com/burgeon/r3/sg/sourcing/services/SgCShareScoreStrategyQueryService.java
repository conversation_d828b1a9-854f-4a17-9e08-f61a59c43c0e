package com.burgeon.r3.sg.sourcing.services;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareScoreStrategyItem;
import com.burgeon.r3.sg.sourcing.mapper.SgCShareScoreStrategyItemMapper;
import com.burgeon.r3.sg.sourcing.mapper.SgCShareScoreStrategyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2021/6/11 15:28
 */
@Component
@Slf4j
public class SgCShareScoreStrategyQueryService {

    @Autowired
    private SgCShareScoreStrategyMapper mapper;
    @Autowired
    private SgCShareScoreStrategyItemMapper itemMapper;

    /**
     * 根据评分因子查询评分策略明细表明细中使用评分因子
     */
    public Integer selectByFactorId(Long factorId) {
        Integer count = itemMapper.selectCount(new LambdaQueryWrapper<SgCShareScoreStrategyItem>()
                .eq(SgCShareScoreStrategyItem::getSgCShareScoreFactorId, factorId)
                .eq(SgCShareScoreStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y));
        return count;
    }
}
