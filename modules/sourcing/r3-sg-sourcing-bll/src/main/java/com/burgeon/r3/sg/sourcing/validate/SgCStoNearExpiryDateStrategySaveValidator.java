package com.burgeon.r3.sg.sourcing.validate;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.core.model.table.sourcing.nearexpirydate.SgCStoNearExpiryDateStrategy;
import com.burgeon.r3.sg.sourcing.mapper.nearexpirydate.SgCStoNearExpiryDateStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.dto.nearexpirydate.SgCStoNearExpiryDateStrategyDTO;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * description::临近大效期寻源设置Validator
 * @Author:  liuwenjin
 * @Date 2022/7/20 16:35
 */
@Slf4j
@Component
public class SgCStoNearExpiryDateStrategySaveValidator extends BaseSingleValidator<SgCStoNearExpiryDateStrategyDTO> {
    @Override
    public String getValidatorMsgName() {
        return "临近大效期寻源设置保存";
    }

    @Override
    public Class<?> getValidatorClass() {
        return this.getClass();
    }

    @Autowired
    private SgCStoNearExpiryDateStrategyMapper mapper;

    @Override
    public ValueHolderV14 validateMainTable(SgCStoNearExpiryDateStrategyDTO mainObject, User loginUser) {
        Long id = mainObject.getId();
        if (mainObject.getId() < 1L) {
            return checkParam(mainObject,new SgCStoNearExpiryDateStrategy(),loginUser);
        }else {
            SgCStoNearExpiryDateStrategy sgCStoNearExpiryDateStrategy = mapper.selectById(id);
            if (sgCStoNearExpiryDateStrategy == null) {
                return new ValueHolderV14(ResultCode.FAIL, "当前记录已不存在！");
            }
            return checkParam(mainObject,sgCStoNearExpiryDateStrategy,loginUser);
        }
    }


    /**
     * description:
     * 记录不存在，则提示：“当前记录已不存在！”确认返回列表页面；
     * 仓库+商品+类型已经存在可用为是的记录，则提示：“记录已经存在，不允许重复录入！”
     * 如果「开始生产天数」大于「结束生产天数」，则提示：“「开始生产天数」大于「结束生产天数」，不允许！”
     * @Author:  liuwenjin
     * @Date 2022/7/20 15:14
     */
    private ValueHolderV14 checkParam(SgCStoNearExpiryDateStrategyDTO mainObject, SgCStoNearExpiryDateStrategy sgCStoNearExpiryDateStrategy,User loginUser) {
        Long id = mainObject.getId();
        Long cpCPhyWarehouseId = mainObject.getCpCPhyWarehouseId() !=null ? mainObject.getCpCPhyWarehouseId():sgCStoNearExpiryDateStrategy.getCpCPhyWarehouseId();
        Long  psCProId= mainObject.getPsCProId();
        String type = mainObject.getType();
        Integer beginParturitionNum=mainObject.getBeginParturitionNum() != null ? mainObject.getBeginParturitionNum() : sgCStoNearExpiryDateStrategy.getBeginParturitionNum();
        Integer endParturitionNum=mainObject.getEndParturitionNum() != null ? mainObject.getEndParturitionNum() : sgCStoNearExpiryDateStrategy.getEndParturitionNum();

        QueryWrapper<SgCStoNearExpiryDateStrategy> queryWrapper = new QueryWrapper<>();
        if (Objects.nonNull(cpCPhyWarehouseId)) {
            queryWrapper.lambda()
                    .eq(SgCStoNearExpiryDateStrategy::getCpCPhyWarehouseId, cpCPhyWarehouseId);
        }
        queryWrapper.lambda()
                .eq(SgCStoNearExpiryDateStrategy::getPsCProId, psCProId != null ? psCProId : sgCStoNearExpiryDateStrategy.getPsCProId())
                //.eq(SgCStoNearExpiryDateStrategy :: getType, type != null ? type : sgCStoNearExpiryDateStrategy.getType())
                .eq(SgCStoNearExpiryDateStrategy::getIsactive, "Y")
                .ne(SgCStoNearExpiryDateStrategy::getId, id);
//        int count =mapper.selectCount(queryWrapper);
//        if (count >0 ){
//            return new ValueHolderV14(ResultCode.FAIL,"记录已经存在，不允许重复录入！");
//        }
        if (beginParturitionNum != null && beginParturitionNum < 0) {
            return new ValueHolderV14(ResultCode.FAIL, "开始生产天数 小于 0，不允许!");
        }
        if (endParturitionNum != null && endParturitionNum < 0) {
            return new ValueHolderV14(ResultCode.FAIL, "结束生产天数 小于 0，不允许!");
        }
        if (beginParturitionNum != null && endParturitionNum != null && beginParturitionNum.compareTo(endParturitionNum) > 0) {
            return new ValueHolderV14(ResultCode.FAIL, "开始生产天数 大于 结束生产天数，不允许!");
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过", loginUser.getLocale()));
    }
}
