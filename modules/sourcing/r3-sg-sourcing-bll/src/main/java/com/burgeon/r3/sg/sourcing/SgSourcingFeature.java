package com.burgeon.r3.sg.sourcing;

import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.sourcing.filter.*;
import com.burgeon.r3.sg.sourcing.validate.*;
import com.jackrain.nea.tableservice.Feature;
import com.jackrain.nea.tableservice.constants.TableServiceConstants;
import com.jackrain.nea.tableservice.feature.FeatureAnnotation;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: sjLi
 * @Date:
 * @Description:
 */
@FeatureAnnotation(value = "SgSourcingFeature", description = "寻源基础档案Feature")
public class SgSourcingFeature extends Feature {

    @Autowired
    private SgCShareSourceRuleStrategySaveValidator sourceRuleStrategySaveValidator;
    @Autowired
    private SgCShareSourceRuleStrategyDeleteValidator sourceRuleStrategyDeleteValidator;

    @Autowired
    SgShareScoreFactorStrategySaveValidator sgShareScoreFactorStrategySaveValidator;

    @Autowired
    SgShareScoreFactorStrategyDeleteValidator sgShareScoreFactorStrategyDeleteValidator;

    @Autowired
    private SgCShareScoreStrategySaveValidator sgShareScoreStrategySaveValidator;
    @Autowired
    private SgCShareScoreStrategyVoidValidator sgShareScoreStrategyVoidValidator;

    @Autowired
    private SgCShareScoreStrategyVoidFilter sgShareScoreStrategyVoidFilter;
    @Autowired
    private SgCShareScoreStrategySaveFilter sgShareScoreStrategySaveFilter;

    @Autowired
    private SgCChannelSourceStrategyVoidFilter sgChannelSourceStrategyVoidFilter;

    @Autowired
    private SgCChannelSourceStrategyVoidValidator sgChannelSourceStrategyVoidValidator;

    @Autowired
    private SgCChannelSourceStrategySubmitValidator sgChannelSourceStrategySubmitValidator;

    @Autowired
    private SgCSyncGradientStrategyDeleteFilter sgSyncGradientStrategyDeleteFilter;

    @Autowired
    private SgCSyncGradientStrategyVoidFilter sgSyncGradientStrategyVoidFilter;

    @Autowired
    private SgCChannelSourceStrategyUnSubmitFilter sgChannelSourceStrategyUnSubmitFilter;

    @Autowired
    private SgCStoNearExpiryDateStrategySaveValidator sgCStoNearExpiryDateStrategySaveValidator;

    @Autowired
    private SgCStoNearExpiryDateStrategySaveFilter sgCStoNearExpiryDateStrategySaveFilter;

    @Autowired
    private  SgCStoNearExpiryDateStrategyDeleteValidator sgCStoNearExpiryDateStrategyDeleteValidator;

    @Override
    protected void initialization() {
        // 寻源规则设置保存
        addValidator(sourceRuleStrategySaveValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_SHARE_SOURCE_RULE_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));
        // 寻源规则设置删除
        addValidator(sourceRuleStrategyDeleteValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_SHARE_SOURCE_RULE_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_DELETE)));
        //评分因子配置.保存
        addValidator(sgShareScoreFactorStrategySaveValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_SHARE_SCORE_FACTOR_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SAVE)));

        //评分因子配置.删除
        addValidator(sgShareScoreFactorStrategyDeleteValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_SHARE_SCORE_FACTOR_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_DELETE)));

        //评分策略表.保存
        addValidator(sgShareScoreStrategySaveValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_SHARE_SCORE_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));
        //评分策略表.保存filter
        addFilter(sgShareScoreStrategySaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_SHARE_SCORE_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));
        //评分策略表.作废
        addValidator(sgShareScoreStrategyVoidValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_SHARE_SCORE_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_VOID)));
        //评分策略表.作废filter
        addFilter(sgShareScoreStrategyVoidFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_SHARE_SCORE_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_VOID)));

        //寻源策略表作废filter
        addFilter(sgChannelSourceStrategyVoidFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_SOURCE_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_VOID)));
        //寻源策略表作废校验
        addValidator(sgChannelSourceStrategyVoidValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_SOURCE_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_VOID)));
        //寻源策略表审核校验
        addValidator(sgChannelSourceStrategySubmitValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_SOURCE_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SUBMIT)));

        //寻源策略表取消审核filter
        addFilter(sgChannelSourceStrategyUnSubmitFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_SOURCE_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_UNSUBMIT)));

        //共享池&&配销仓库存梯度策略删除filter
        addFilter(sgSyncGradientStrategyDeleteFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_SYNC_GRADIENT_STRATEGY) ||
                tableName.equalsIgnoreCase(SgConstants.SG_C_SYNC_GRADIENT_STRATEGY_SA))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_DELETE)));

        //共享池&&配销仓库存梯度策略作废filter
        addFilter(sgSyncGradientStrategyVoidFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_SYNC_GRADIENT_STRATEGY) ||
                tableName.equalsIgnoreCase(SgConstants.SG_C_SYNC_GRADIENT_STRATEGY_SA))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_VOID)));

        //临近大效期寻源设置保存校验
        addValidator(sgCStoNearExpiryDateStrategySaveValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_STO_NEAR_EXPIRY_DATE_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SAVE)));

        //临近大效期寻源设置保存filter
        addFilter(sgCStoNearExpiryDateStrategySaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_STO_NEAR_EXPIRY_DATE_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SAVE)));

        //临近大效期寻源设置删除校验
        addValidator(sgCStoNearExpiryDateStrategyDeleteValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_STO_NEAR_EXPIRY_DATE_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_DELETE)));
    }
}
