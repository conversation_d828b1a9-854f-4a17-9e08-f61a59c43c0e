package com.burgeon.r3.sg.sourcing.services.item;


import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 拆单策略预检执行器
 * @author: chenb
 * @time: 2022/7/20 16:41
 */
@Slf4j
@Component
public class SgFindSourceSplitStrategyPreCheckService extends StrategyHandle {


    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {

        log.info(LogUtil.format("SgFindSourceSplitStrategyPreCheckService.handleRequest S->L二阶段寻源派单 拆单策略预检执行器 param:{}",
                SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L),
                request.getTraceId(), JSONObject.toJSONString(request));

        ValueHolderV14<StrategyBaseResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        SgFindSourceStrategyS2LRequest strategyRequest = (SgFindSourceStrategyS2LRequest) request;
        SgFindSourceStrategyS2LResult strategyResult = (SgFindSourceStrategyS2LResult) strategyRequest.getStrategyBaseResult();
        valueHolderV14.setData(strategyResult);

        Set<Long> phyWarehouseIdResult = null;
        Set<Long> phyWarehouseIds = null;

        if (strategyResult == null) {
            FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略预检执行器获取上游策略执行结果:不存在可使用的仓库");
            valueHolderV14.setMessage("拆单策略预检执行器获取上游策略执行结果:不存在可使用的仓库 策略执行结束!");
            FindSourceStrategyUtils.allOut(strategyRequest);
            valueHolderV14.setData(request.getStrategyBaseResult());
            return valueHolderV14;
        }

        try {

            //查询店铺拆单规则
            String splitType = strategyRequest.getSplitType() == null ?
                    StrategyConstants.ORDER_SPLIT_TYPE_NO : strategyRequest.getSplitType();

            if (!StrategyConstants.ORDER_SPLIT_TYPE_NO.equals(splitType) ||
                    CollectionUtils.isEmpty(strategyRequest.getSkuItems())) {
                FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略预检执行器获取拆单类型为可拆单，策略执行结束");
                return doNext(request, valueHolderV14);
            }

            //拆单类型为不拆的情况下，检查多聚合仓是否存在实体仓交集
            for (SgFindSourceStrategySkuS2LResult skuResult : strategyResult.getSkuResultList()) {

                phyWarehouseIds = skuResult.getItemResultList().stream().filter(
                        x -> !StrategyConstants.OUT_DEFAULT_STORE_ID.equals(x.getStoreId())).map(
                        SgFindSourceStrategyStoreItemS2LResult::getStoreId).collect(Collectors.toSet());

                //执行订单明细中实体仓的交集
                if (phyWarehouseIdResult == null) {
                    phyWarehouseIdResult = phyWarehouseIds;
                } else {
                    phyWarehouseIdResult.retainAll(phyWarehouseIds);
                }

                //判断订单中实体仓是否存在交集
                if (CollectionUtils.isEmpty(phyWarehouseIdResult)) {
                    FindSourceStrategyUtils.outputLog(
                            "S->L二阶段寻源派单 拆单策略预检执行器判断订单中实体仓是否存在交集结果:无单仓库可以整单满足 phyWarehouseIds:{}",
                            phyWarehouseIds);
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage(Resources.getMessage("无单仓库可以整单满足，寻源失败!"));
                    break;
                }
            }

            Map<Long, Long> phyWarehouseIdFilterMap = phyWarehouseIdResult.stream().collect(
                    Collectors.toMap(Function.identity(), Function.identity(), (orderValue, newValue) -> orderValue));
            List<SgFindSourceStrategyStoreItemS2LResult> ItemResultList = null;

            //获取交集的实体仓作为新的实体仓范围
            for (SgFindSourceStrategySkuS2LResult skuResult : strategyResult.getSkuResultList()) {

                ItemResultList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
                for (SgFindSourceStrategyStoreItemS2LResult storeResult : skuResult.getItemResultList()) {

                    //仓库物流可发并且非默认缺货仓的情况下，重新整理实体仓信息
                    if (phyWarehouseIdFilterMap.containsKey(storeResult.getStoreId()) &&
                            !StrategyConstants.OUT_DEFAULT_STORE_ID.equals(storeResult.getStoreId())) {
                        ItemResultList.add(storeResult);
                    }
                }

                if (!CollectionUtils.isEmpty(ItemResultList)) {
                    skuResult.setItemResultList(ItemResultList);
                }

            }

        } catch (Exception e) {
            log.error(LogUtil.format("SgFindSourceSplitStrategyPreCheckService.handleRequest S->L二阶段寻源派单 拆单策略预检执行器发生异常 exception:{}",
                    SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L_EXCEPTION),
                    request.getTraceId(), Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("S->L 二阶段寻源派单 拆单策略预检执行器 发生异常");
        }
        return doNext(request, valueHolderV14);
    }

}