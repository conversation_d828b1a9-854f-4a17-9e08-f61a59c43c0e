package com.burgeon.r3.sg.sourcing.validate;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.model.result.matrix.ApiCommandResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareScoreFactorStrategy;
import com.burgeon.r3.sg.sourcing.mapper.SgCShareScoreFactorStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.dto.strategy.SgCShareScoreFactorStrategyDto;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description 评分因子配置新增/保存
 * <AUTHOR>
 * @Date 2021/6/9 13:20
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgShareScoreFactorStrategySaveValidator extends BaseSingleValidator<SgCShareScoreFactorStrategyDto> {

    @Autowired
    SgCShareScoreFactorStrategyMapper sgCShareScoreFactorStrategyMapper;

    @Override
    public String getValidatorMsgName() {
        return "评分因子设置保存";
    }

    @Override
    public Class getValidatorClass() {
        return SgShareScoreFactorStrategySaveValidator.class;
    }

    @Override
    public ValueHolderV14<ApiCommandResult> validateMainTable(SgCShareScoreFactorStrategyDto mainObject, User loginUser) {
        String ename = mainObject.getEname();
        String ecode = mainObject.getEcode();
        if (mainObject.getId() < 1L) {

            int countEcode = sgCShareScoreFactorStrategyMapper.selectCount(new QueryWrapper<SgCShareScoreFactorStrategy>()
                    .lambda().eq(SgCShareScoreFactorStrategy::getEcode, ecode));
            if (countEcode > 0) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("编码已存在", loginUser.getLocale()));
            }
            int countEname = sgCShareScoreFactorStrategyMapper.selectCount(new QueryWrapper<SgCShareScoreFactorStrategy>().lambda()
                    .eq(SgCShareScoreFactorStrategy::getEname, ename));
            if (countEname > 0) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("名称已存在", loginUser.getLocale()));
            }
        } else {
            if (StringUtils.isNotEmpty(ename)) {
                Integer countEname = sgCShareScoreFactorStrategyMapper.selectCount(new QueryWrapper<SgCShareScoreFactorStrategy>()
                        .lambda().ne(SgCShareScoreFactorStrategy::getId, mainObject.getId())
                        .eq(SgCShareScoreFactorStrategy::getEname, ename));
                if (countEname > 0) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("名称已存在", loginUser.getLocale()));
                }
            }

        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过", loginUser.getLocale()));
    }
}
