package com.burgeon.r3.sg.sourcing.services.item;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.basic.mapper.SgCWarehouseScopeMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCWarehouseScope;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 就近策略
 * @author: hwy
 * @time: 2021/6/9 16:23
 */
@Slf4j
@Component
public class SgFindSourceNearlyStrategyService extends StrategyHandle {

    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {
        FindSourceStrategyUtils.outputLog("SgFindSourceNearlyStrategyService.handleRequest S->L 二阶段寻源派单 就近策略 param:{}", JSONObject.toJSONString(request));
        ValueHolderV14<StrategyBaseResult> valueHolderV14 = new ValueHolderV14<>();
        SgFindSourceStrategyS2LRequest strategyRequest = (SgFindSourceStrategyS2LRequest) request;
        SgFindSourceStrategyS2LResult strategyResult = (SgFindSourceStrategyS2LResult) strategyRequest.getStrategyBaseResult();
        List<SgFindSourceStrategySkuS2LResult> skuResultList = strategyResult.getSkuResultList();
        if (CollectionUtils.isEmpty(skuResultList)) {
            FindSourceStrategyUtils.outputLog(" S->L 二阶段寻源 就近寻源策略 没有可用仓库 终止策略");
            FindSourceStrategyUtils.allOut(strategyRequest);
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setData(strategyRequest.getStrategyBaseResult());
            valueHolderV14.setMessage(" S->L 二阶段寻源 就近寻源策略 没有可用仓库 全部缺货 终止策略");
            return valueHolderV14;
        }
        //区
        Long areaId = strategyRequest.getAreaId();
        //市
        Long cityId = strategyRequest.getCityId();
        //省
        Long provinceId = strategyRequest.getProvinceId();
        try {
            //所有的仓
            List<Long> storeIds = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
            Map<Long, List<SgFindSourceStrategyStoreItemS2LResult>> storeMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            skuResultList.stream().forEach(o -> {
                List<SgFindSourceStrategyStoreItemS2LResult> itemResult = o.getItemResultList();
                if (CollectionUtils.isEmpty(itemResult)) {
                    return;
                }
                itemResult.stream().forEach(i -> {
                    Long storeId = i.getStoreId();
                    if (StrategyConstants.OUT_DEFAULT_STORE_ID.equals(storeId)) {
                        return;
                    }
                    if (!storeIds.contains(storeId)) {
                        storeIds.add(storeId);
                    }
                    if (storeMap.containsKey(storeId)) {
                        List<SgFindSourceStrategyStoreItemS2LResult> itemResultList = storeMap.get(storeId);
                        itemResultList.add(i);
                    } else {
                        List<SgFindSourceStrategyStoreItemS2LResult> itemResultList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
                        itemResultList.add(i);
                        storeMap.put(storeId, itemResultList);
                    }
                });
            });
            // 没有可用仓库 进入下一策略
            if (CollectionUtils.isEmpty(storeIds)) {
                FindSourceStrategyUtils.outputLog("二阶段寻源 就近寻源策略 根据上游策略执行结果 没有有效的实体仓信息 就近策略执行完毕");
                valueHolderV14.setData(strategyRequest.getStrategyBaseResult());
                return doNext(request, valueHolderV14);
            }
            //查询仓的所在地
            CpCPhyWarehouseMapper warehouseMapper = ApplicationContextHandle.getBean(CpCPhyWarehouseMapper.class);
            List<SgCpCPhyWarehouse> warehouses = warehouseMapper.selectList(new QueryWrapper<SgCpCPhyWarehouse>().lambda().
                    select(SgCpCPhyWarehouse::getId, SgCpCPhyWarehouse::getEcode, SgCpCPhyWarehouse::getEname, SgCpCPhyWarehouse::getSellerAreaId,
                            SgCpCPhyWarehouse::getSellerCityId, SgCpCPhyWarehouse::getSellerProvinceId).
                    in(SgCpCPhyWarehouse::getId, storeIds).
                    eq(SgCpCPhyWarehouse::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (CollectionUtils.isEmpty(warehouses)) {
                FindSourceStrategyUtils.outputLog("二阶段寻源 就近寻源策略 根据上游策略执行结果 查询不到实体仓信息 就近策略执行完毕");
                valueHolderV14.setData(strategyRequest.getStrategyBaseResult());
                return doNext(request, valueHolderV14);
            }
            List<Long> areaStoreList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
            List<Long> cityStoreList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
            List<Long> provinceStoreList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
            warehouses.stream().forEach(o -> {
                if (areaId != null && areaId.equals(o.getSellerAreaId())) {
                    if (!areaStoreList.contains(o.getId())) {
                        areaStoreList.add(o.getId());
                    }
                    if (!cityStoreList.contains(o.getId())) {
                        cityStoreList.add(o.getId());
                    }
                    if (!provinceStoreList.contains(o.getId())) {
                        provinceStoreList.add(o.getId());
                    }

                }
                if (cityId != null && cityId.equals(o.getSellerCityId())) {
                    if (!cityStoreList.contains(o.getId())) {
                        cityStoreList.add(o.getId());
                    }
                    if (!provinceStoreList.contains(o.getId())) {
                        provinceStoreList.add(o.getId());
                    }
                }
                if (provinceId != null && provinceId.equals(o.getSellerProvinceId())) {
                    if (!provinceStoreList.contains(o.getId())) {
                        provinceStoreList.add(o.getId());
                    }
                }
            });
            // 查询仓库配送范围
            SgCWarehouseScopeMapper warehouseScopeMapper = ApplicationContextHandle.getBean(SgCWarehouseScopeMapper.class);
            List<SgCWarehouseScope> sgCWarehouseScopes = warehouseScopeMapper.selectList(new QueryWrapper<SgCWarehouseScope>().lambda()
                    .select(SgCWarehouseScope::getCpCPhyWarehouseId, SgCWarehouseScope::getCpCProvinceId, SgCWarehouseScope::getPriority)
                    .in(SgCWarehouseScope::getCpCPhyWarehouseId, storeIds)
                    .eq(SgCWarehouseScope::getCpCProvinceId, provinceId)
                    .eq(SgCWarehouseScope::getIsactive, SgConstants.IS_ACTIVE_Y));
            Map<Integer, List<SgCWarehouseScope>> warehouseScopeMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            List<Integer> prioritys = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(sgCWarehouseScopes)) {
                sgCWarehouseScopes.stream().forEach(o -> {
                    List<SgCWarehouseScope> warehouseScopes = warehouseScopeMap.get(o.getPriority());
                    if (CollectionUtils.isEmpty(warehouseScopes)) {
                        warehouseScopes = new ArrayList<>();
                        warehouseScopeMap.put(o.getPriority(), warehouseScopes);
                        prioritys.add(o.getPriority());
                    }
                    warehouseScopes.add(o);
                });
            }
            Integer priority = 1;
            //删除省内的仓 剩余的为外省仓
            storeIds.removeAll(provinceStoreList);
            //遍历仓 外省<配送范围内的仓<同省<同市<同区 排优先级
            setPriority(storeIds, storeMap, priority);
            // 设置在配送范围内的仓库 优先级
            if (MapUtils.isNotEmpty(warehouseScopeMap)) {
                Collections.sort(prioritys);
                if (log.isDebugEnabled()) {
                    log.debug(" 二阶段寻源 就近寻源策略 仓库配送范围优先级:{} ", prioritys);
                } else {
                    log.info(" 二阶段寻源 就近寻源策略 仓库配送范围优先级:{} ", prioritys);
                }
                for (Integer currPriority : prioritys) {
                    priority = priority + 1;
                    //log.debug("二阶段寻源 就近寻源策略 仓库配送范围优先级 当前阶段优先级:{}", priority);
                    List<SgCWarehouseScope> warehouseScopes = warehouseScopeMap.get(currPriority);
                    if (CollectionUtils.isEmpty(warehouseScopes)) {
                        continue;
                    }
                    List<Long> currStoreIds = warehouseScopes.stream().map(SgCWarehouseScope::getCpCPhyWarehouseId).distinct().collect(Collectors.toList());
                    setPriority(currStoreIds, storeMap, priority);
                }
            }
            // 设置省内的仓库 优先级
            if (CollectionUtils.isNotEmpty(provinceStoreList)) {
                priority = priority + 1;
                setPriority(provinceStoreList, storeMap, priority);
            }
            if (CollectionUtils.isNotEmpty(cityStoreList)) {
                priority = priority + 1;
                setPriority(cityStoreList, storeMap, priority);
            }
            if (CollectionUtils.isNotEmpty(areaStoreList)) {
                priority = priority + 1;
                setPriority(areaStoreList, storeMap, priority);
            }
            valueHolderV14.setData(strategyResult);
        } catch (Exception e) {
            log.error(" S->L 二阶段寻源 就近策略 发生异常:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(" S->L 二阶段寻源 就近策略 发生异常 ");
        }
        return doNext(request, valueHolderV14);
    }


    /**
     * @param storeList:
     * @param storeMap:
     * @param priority:
     * @Description: 设置优先级
     * @Author: hwy
     * @Date: 2021/7/2 10:56
     * @return: void
     **/
    private void setPriority(List<Long> storeList, Map<Long, List<SgFindSourceStrategyStoreItemS2LResult>> storeMap, Integer priority) {
        for (Long storeId : storeList) {
            List<SgFindSourceStrategyStoreItemS2LResult> itemResults = storeMap.get(storeId);
            for (SgFindSourceStrategyStoreItemS2LResult itemResult : itemResults) {
                itemResult.setPriority(priority);
            }
        }
    }
}