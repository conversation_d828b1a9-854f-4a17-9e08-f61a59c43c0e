package com.burgeon.r3.sg.sourcing.services.report;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.model.request.SgStorageQuerySsRequest;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySsResult;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategyMapper;
import com.burgeon.r3.sg.channel.model.request.strategy.SgCChannelSkuStrategyQueryInfoRequest;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelRatioStrategyQueryInfoResult;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelSkuStrategyQuerySpInfoResult;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.sourcing.model.request.report.SgShopShareStorageDevOpsInfoQueryRequest;
import com.burgeon.r3.sg.sourcing.model.request.syncgradientstrategy.SgCSyncGradientStrategyByQtyQueryRequest;
import com.burgeon.r3.sg.sourcing.model.result.report.SgShopShareStorageDevOpsResult;
import com.burgeon.r3.sg.sourcing.model.result.report.ShopShareStorageDevOpsInfoResult;
import com.burgeon.r3.sg.sourcing.model.result.syncgradientstrategy.SgCSyncGradientStrategyByQtyQueryResult;
import com.burgeon.r3.sg.sourcing.services.syncgradientstrategy.SgCSyncGradientStrategyService;
import com.google.common.base.Throwables;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.UserQueryCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: hwy
 * @time: 2021/8/23 17:31
 */

@Component
@Slf4j
public class ShopShareStorageDevOpsInfoService {


    @Autowired
    private SgCSyncGradientStrategyService sgCSyncGradientStrategyService;

    /**
     * @param request:
     * @Description: 平台店铺库存管理-共享池库存查询
     * @Author: hwy
     * @Date: 2021/8/23 21:43
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<com.burgeon.r3.sg.sourcing.model.result.report.SgShopShareStorageDevOpsResult>
     **/
    public ValueHolderV14<SgShopShareStorageDevOpsResult> queryShopShareStorageDevOpsInfo(SgShopShareStorageDevOpsInfoQueryRequest request) {
        ValueHolderV14<SgShopShareStorageDevOpsResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        if (log.isDebugEnabled()) {
            log.debug(" ShopShareStorageDevOpsInfoService.queryShopShareStorageDevOpsInfo param:{}", JSONObject.toJSONString(request));
        }
        SgShopShareStorageDevOpsResult devOpsResult = new SgShopShareStorageDevOpsResult();
        List<ShopShareStorageDevOpsInfoResult> devOpsInfoResultList = new ArrayList<>();
        devOpsResult.setShareStorageInfoList(devOpsInfoResultList);
        valueHolderV14.setData(devOpsResult);
        devOpsResult.setTotalRowCount(0);
        try {
            checkParam(request, valueHolderV14);
            if (!valueHolderV14.isOK()) {
                return valueHolderV14;
            }
            List<Long> shareStoreIds = new ArrayList<>();
            List<Long> psCSkuIds = new ArrayList<>();
            psCSkuIds.add(request.getPsCSkuId());
            // 查询店铺共享池比例设置
            SgCChannelRatioStrategyMapper ratioStrategyMapper = ApplicationContextHandle.getBean(SgCChannelRatioStrategyMapper.class);
            List<Long> shopIds = new ArrayList<>();
            shopIds.add(request.getCpCShopId());
            List<SgCChannelRatioStrategyQueryInfoResult> ratioStrategyInfoResults = ratioStrategyMapper.queryRatioStrategyShareInfoByShopIds(shopIds);
            if (CollectionUtils.isNotEmpty(ratioStrategyInfoResults)) {
                ratioStrategyInfoResults.stream().forEach(o -> {
                    if (!shareStoreIds.contains(o.getSgCShareStoreId())) {
                        shareStoreIds.add(o.getSgCShareStoreId());
                    }
                });
            }
            if (CollectionUtils.isEmpty(shareStoreIds)) {
                return valueHolderV14;
            }
            log.debug("========== 获取用户服务 ========");
            UserQueryCmd userQueryCmd = (UserQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), UserQueryCmd.class.getName(), "cp-ext", "1.0");
            log.debug("========== 查询用户 ========");
            User user = userQueryCmd.getUsersById(request.getUserId());
            log.debug("========== 查询库存 ========");
            //查询聚合仓库存
            SgStorageQueryService storageQueryService = ApplicationContextHandle.getBean(SgStorageQueryService.class);
            SgStorageQuerySsRequest sgStorageQuerySsRequest = new SgStorageQuerySsRequest();
            sgStorageQuerySsRequest.setSkuIds(psCSkuIds);
            sgStorageQuerySsRequest.setSgCShareStoreIds(shareStoreIds);
            ValueHolderV14<List<SgStorageRedisQuerySsResult>> storageResult = storageQueryService.querySsStorageWithRedis(sgStorageQuerySsRequest, user);
            if (!storageResult.isOK()) {
                log.error("ShopShareStorageDevOpsInfoService.queryShopShareStorageDevOpsInfo 查询库存失败:{}", storageResult.getMessage());
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("查询库存失败");
                return valueHolderV14;
            }
            List<SgStorageRedisQuerySsResult> storageResultList = storageResult.getData();
            Map<Long, SgStorageRedisQuerySsResult> storageMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(storageResultList)) {
                storageMap.putAll(storageResultList.stream().collect(Collectors.toMap(SgStorageRedisQuerySsResult::getSgCShareStoreId, Function.identity())));
            }

            ShopShareStorageDevOpsInfoResult devOpsInfoResult = new ShopShareStorageDevOpsInfoResult();
            devOpsInfoResult.setId(1L);
            devOpsInfoResult.setCpCShopId(request.getCpCShopId());
            devOpsInfoResult.setSkuId(request.getSkuId());
            devOpsInfoResult.setPsCSkuId(request.getPsCSkuId());
            devOpsInfoResultList.add(devOpsInfoResult);
            Long sgCSharePoolId = null;
            String sgCSharePoolEcode = null;
            String sgCSharePoolEname = null;
            BigDecimal poolRatio = null;
            BigDecimal qtySync = BigDecimal.ZERO;
            BigDecimal qtyAvailable = BigDecimal.ZERO;
            for (SgCChannelRatioStrategyQueryInfoResult shareRateInfo : ratioStrategyInfoResults) {
                Long sgCShareStoreId = shareRateInfo.getSgCShareStoreId();
                BigDecimal shareRatio = BigDecimal.ZERO;
                sgCSharePoolId = sgCSharePoolId == null ? shareRateInfo.getSgCSharePoolId() : sgCSharePoolId;
                sgCSharePoolEcode = StringUtils.isEmpty(sgCSharePoolEcode) ? shareRateInfo.getSgCSharePoolEcode() : sgCSharePoolEcode;
                sgCSharePoolEname = StringUtils.isEmpty(sgCSharePoolEname) ? shareRateInfo.getSgCSharePoolEname() : sgCSharePoolEname;
                shareRatio = shareRateInfo.getRatio();
                poolRatio = poolRatio == null ? BigDecimal.ZERO : shareRateInfo.getSgCSharePoolRatio();
                SgStorageRedisQuerySsResult storage = storageMap.get(sgCShareStoreId);
                devOpsInfoResult.setQtyAvailable(storage == null ? BigDecimal.ZERO : storage.getQtySpAvailable());
                BigDecimal currShareQty = storage == null ? BigDecimal.ZERO : storage.getQtySpAvailable().multiply(shareRatio).divide(SgConstants.NUMBER_100, 0, BigDecimal.ROUND_DOWN);
                qtySync = qtySync.add(currShareQty);
            }
            qtyAvailable = qtySync;

            // 获取共享池梯度信息
            SgCSyncGradientStrategyByQtyQueryRequest strategyRequest = new SgCSyncGradientStrategyByQtyQueryRequest();
            strategyRequest.setCpCShopId(request.getCpCShopId());
            strategyRequest.setStoreId(sgCSharePoolId);
            strategyRequest.setType(SgConstants.SYNC_GRADIENT_STRATEGY_POOL);
            strategyRequest.setTargetQty(qtySync);
            ValueHolderV14<SgCSyncGradientStrategyByQtyQueryResult> strategyQueryResult =
                    sgCSyncGradientStrategyService.querySyncGradientStrategyByQty(strategyRequest, user);
            BigDecimal calRatio = BigDecimal.ZERO;
            //设置共享池计算用比例
            if (strategyQueryResult.getData() != null) {
                calRatio = strategyQueryResult.getData().getRatio();
            } else {
                calRatio = poolRatio == null ? BigDecimal.ZERO : poolRatio;
            }
            qtySync = qtySync.multiply(calRatio).divide(SgConstants.NUMBER_100, 0, BigDecimal.ROUND_DOWN);
            devOpsInfoResult.setQtyAvailable(qtyAvailable);
            devOpsInfoResult.setRatio(calRatio);
            devOpsInfoResult.setQtysync(qtySync);
            devOpsInfoResult.setSgCSharePoolId(sgCSharePoolId);
            devOpsInfoResult.setSgCSharePoolEcode(sgCSharePoolEcode);
            devOpsInfoResult.setSgCSharePoolEname(sgCSharePoolEname);
            devOpsResult.setTotalRowCount(1);
        } catch (Exception e) {
            log.error(" ShopShareStorageDevOpsInfoService.queryShopShareStorageDevOpsInfo 查询店铺共享池库存失败:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("查询店铺共享池库存失败");
        }
        return valueHolderV14;
    }

    private void checkParam(SgShopShareStorageDevOpsInfoQueryRequest request, ValueHolderV14<SgShopShareStorageDevOpsResult> valueHolderV14) {
        if (request == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("查询参数为空");
            return;
        }
        if (request.getCpCShopId() == null) {
            if (log.isDebugEnabled()) {
                log.debug("店铺id为空");
            }
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("店铺id为空");
            return;
        }

        if (request.getPsCSkuId() == null) {
            if (log.isDebugEnabled()) {
                log.debug("系统条码为空");
            }
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("系统条码为空");
            return;
        }
        if (StringUtils.isEmpty(request.getSkuId())) {
            if (log.isDebugEnabled()) {
                log.debug("平台条码为空");
            }
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("平台条码为空");
            return;
        }

    }


}