package com.burgeon.r3.sg.sourcing.services.easyrule;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategyMapper;
import com.burgeon.r3.sg.channel.model.request.strategy.SgCChannelSkuStrategyQueryInfoRequest;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelRatioStrategyQueryInfoResult;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelSkuStrategyQuerySpInfoResult;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyC2SRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemC2S;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyC2SResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuC2SResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemC2SResult;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeasy.rules.annotation.*;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 通过共享池条码比例策略设置 对订单明细的归属仓排序
 * @author: hwy
 * @time: 2021/6/21 20:04
 */
@Rule
@Slf4j
@Component
@Deprecated
public class SgFindSourceShareRateFirstRule {

    /**
     * @Description:
     **/
    private static final int PRIORITY = 3;

    @Priority
    public int getPriority() {
        return PRIORITY;
    }


    @Condition
    public boolean enableRule(@Fact("request") SgFindSourceStrategyC2SRequest request) {
        FindSourceStrategyUtils.outputLog("C->S寻源策略 寻源引擎 共享库存优先规则 匹配成功");
        return true;
    }


    @Action
    public void doRule(@Fact("request") SgFindSourceStrategyC2SRequest request) {

        log.info("SgFindSourceShareRateFirstRule.duRule Start 来源单据id:{}", request.getSourceBillId());

        //策略执行结果
        SgFindSourceStrategyC2SResult strategyResult = (SgFindSourceStrategyC2SResult) request.getStrategyBaseResult();
        //策略执行结果明细
        List<SgFindSourceStrategySkuC2SResult> skuResultList = CollectionUtils.isEmpty(strategyResult.getSkuResultList()) ? new ArrayList<>() : strategyResult.getSkuResultList();
        strategyResult.setSkuResultList(skuResultList);
        //策略执行结果明细按 来源明细id 聚合
        Map<Long, SgFindSourceStrategySkuC2SResult> skuResultMap = skuResultList.stream().collect(Collectors.toMap(SgFindSourceStrategySkuC2SResult::getSourceItemId, Function.identity()));
        //订单明细
        List<SkuItemC2S> skuItems = request.getSkuItems();
        // 所有的平台条码
        List<String> skuIds = new ArrayList<>();
        //系统条码
        List<Long> psCSkuIds = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);

        //遍历订单加入所有条码
        skuItems.stream().forEach(o -> {
            String skuId = o.getSkuId();
            Long psCSkuId = o.getPsCSkuId();
            if (StringUtils.isNotEmpty(skuId) && !skuIds.contains(skuId)) {
                skuIds.add(o.getSkuId());
            }
            if (!psCSkuIds.contains(psCSkuId)) {
                psCSkuIds.add(o.getPsCSkuId());
            }
        });

        Date currDate = new Date();

        //查询店铺特殊条码比例配置
        SgCChannelSkuStrategyMapper skuStrategyMapper = ApplicationContextHandle.getBean(SgCChannelSkuStrategyMapper.class);
        SgCChannelSkuStrategyQueryInfoRequest queryInfoRequest = new SgCChannelSkuStrategyQueryInfoRequest();
        queryInfoRequest.setCpCShopId(request.getShopId());
        queryInfoRequest.setPsCSkuIds(psCSkuIds);
        queryInfoRequest.setBeginTime(currDate);
        queryInfoRequest.setEndTime(currDate);
        //根据店铺&时间&系统条码查询
        List<SgCChannelSkuStrategyQuerySpInfoResult> specialRateList = skuStrategyMapper.querySkuStrategySpInfo(queryInfoRequest);

        //查询店铺同比比例设置信息
        SgCChannelRatioStrategyMapper ratioStrategyMapper = ApplicationContextHandle.getBean(SgCChannelRatioStrategyMapper.class);
        List<Long> shopIds = new ArrayList<>();
        shopIds.add(request.getShopId());
        List<SgCChannelRatioStrategyQueryInfoResult> ratioStrategyInfoResults = ratioStrategyMapper.queryRatioStrategyShareInfoByShopIds(shopIds);
        if (CollectionUtils.isEmpty(specialRateList) && CollectionUtils.isEmpty(ratioStrategyInfoResults)) {
            FindSourceStrategyUtils.outputLog("SgFindSourceSaRateFirstRule.doRule C->S寻源策略 共享仓比例优先策略 未找到订单:{} 店铺：{} 对应的共享仓比例供货设置", request.getSourceBillId(), request.getShopId());
            return;
        }

        // 以平台sku维度设置的策略
        List<SgCChannelSkuStrategyQuerySpInfoResult> skuSpecialRateList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        Map<String, List<SgCChannelSkuStrategyQuerySpInfoResult>> skuSpRateMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
        // 以平台条码维度设置的策略
        List<SgCChannelSkuStrategyQuerySpInfoResult> psCSkuSpecialRateList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        Map<Long, List<SgCChannelSkuStrategyQuerySpInfoResult>> psSkuSpRateMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

        //遍历区分平台SKU还是平台条码下的策略
        if (CollectionUtils.isNotEmpty(specialRateList)) {
            specialRateList.stream().forEach(o -> {
                String skuId = o.getSkuId();
                boolean flag = skuIds.contains(skuId);
                if (skuId != null && flag) {
                    skuSpecialRateList.add(o);
                } else if (skuId == null) {
                    psCSkuSpecialRateList.add(o);
                }
            });
            skuSpRateMap.putAll(skuSpecialRateList.stream().collect(Collectors.groupingBy(SgCChannelSkuStrategyQuerySpInfoResult::getSkuId)));
            psSkuSpRateMap.putAll(psCSkuSpecialRateList.stream().collect(Collectors.groupingBy(SgCChannelSkuStrategyQuerySpInfoResult::getPsCSkuId)));
        }

        //设置仓库优先级
        skuItems.stream().forEach(o -> {
            Long psCSkuId = o.getPsCSkuId();
            Long itemId = o.getSourceItemId();
            String skuId = o.getSkuId();
            //创建当前条码策略执行结果
            SgFindSourceStrategySkuC2SResult currSkuResult;
            //创建当前条码优先级队列明细
            List<SgFindSourceStrategyStoreItemC2SResult> currStoreItemList;
            // 将当前结果追加到原有结果
            if (skuResultMap.containsKey(itemId)) {
                currSkuResult = skuResultMap.get(itemId);
                currStoreItemList = CollectionUtils.isEmpty(currSkuResult.getItemResult()) ? new ArrayList<>() : currSkuResult.getItemResult();
            } else {
                currSkuResult = new SgFindSourceStrategySkuC2SResult();
                currSkuResult.setSourceItemId(itemId);
                currSkuResult.setPsCSkuId(psCSkuId);
                currStoreItemList = new ArrayList();
                skuResultList.add(currSkuResult);
            }
            currSkuResult.setItemResult(currStoreItemList);
            List<SgCChannelSkuStrategyQuerySpInfoResult> currRateList;
            if (StringUtils.isNotEmpty(skuId)) {
                currRateList = skuSpRateMap.get(skuId);
            } else {
                currRateList = psSkuSpRateMap.get(psCSkuId);
            }
            //设置当前条码特殊比例优先级
            if (CollectionUtils.isNotEmpty(currRateList)) {
                currRateList.stream().forEach(i -> {
                    Long sgCShareStoreId = i.getSgCShareStoreId();
                    Long sgCSharePoolId = i.getSgCSharePoolId();
                    SgFindSourceStrategyStoreItemC2SResult currStoreItem = new SgFindSourceStrategyStoreItemC2SResult();
                    currStoreItem.setSgCShareStoreId(sgCShareStoreId);
                    currStoreItem.setSgCSharePoolId(sgCSharePoolId);
                    currStoreItem.setPriority(i.getOrderno());
                    currStoreItemList.add(currStoreItem);
                });
                //设置当前条码普通比例优先级
            } else if (CollectionUtils.isNotEmpty(ratioStrategyInfoResults)) {
                ratioStrategyInfoResults.stream().forEach(i -> {
                    SgFindSourceStrategyStoreItemC2SResult currStoreItem = new SgFindSourceStrategyStoreItemC2SResult();
                    currStoreItem.setSgCSharePoolId(i.getSgCSharePoolId());
                    currStoreItem.setSgCShareStoreId(i.getSgCShareStoreId());
                    currStoreItem.setPriority(i.getOrderno());
                    currStoreItemList.add(currStoreItem);
                });

            }
        });

        FindSourceStrategyUtils.outputLog("SgFindSourceShareRateFirstRule.doRule result:{}", JSONObject.toJSONString(request));
    }

}