package com.burgeon.r3.sg.sourcing.validate;

import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.model.dto.strategy.SgCChannelSourceStrategyDTO;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/6/4 15:27
 */
@Component
public class SgCChannelSourceStrategyVoidValidator extends BaseSingleValidator<SgCChannelSourceStrategyDTO> {

    @Override
    public String getValidatorMsgName() {
        return "寻源策略表作废";
    }

    @Override
    public Class<?> getValidatorClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCChannelSourceStrategyDTO mainObject, User loginUser) {
        SgCChannelSourceStrategyDTO orignalData = getOrignalData();
        Integer status = orignalData.getStatus();
        if (!SgSourcingConstants.BILL_SOURCE_STRATEGY_UNSUBMIT.equals(status)) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("当前记录不是未审核状态不允许作废！",
                    loginUser.getLocale()));
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
    }

}
