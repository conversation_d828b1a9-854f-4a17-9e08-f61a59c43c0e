package com.burgeon.r3.sg.sourcing.mapper.syncgradientstrategy;

import com.burgeon.r3.sg.core.model.table.sourcing.syncgradientstrategy.SgCSyncGradientStrategyItem;
import com.burgeon.r3.sg.sourcing.model.result.syncgradientstrategy.SgCSyncGradientStrategyShopItemQueryResult;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface SgCSyncGradientStrategyItemMapper extends ExtentionMapper<SgCSyncGradientStrategyItem> {
    /**
     * 定制查询店铺明细
     *
     * @param mainId
     * @param condId
     * @return
     */
    @Select("SELECT id,cp_c_shop_title cpCShopTitle,ratio from sg_c_sync_gradient_strategy_item where " +
            "sg_c_sync_gradient_strategy_id=#{mainId} and sg_c_sync_gradient_strategy_cond_id=#{condId}")
    List<SgCSyncGradientStrategyShopItemQueryResult> selectListByMainIdAndCondId(@Param("mainId") Long mainId,
                                                                                 @Param("condId") Long condId);
}