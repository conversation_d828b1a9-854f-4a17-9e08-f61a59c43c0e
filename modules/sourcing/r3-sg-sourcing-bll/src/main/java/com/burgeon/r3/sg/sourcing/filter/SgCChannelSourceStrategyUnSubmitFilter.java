package com.burgeon.r3.sg.sourcing.filter;

import com.burgeon.r3.sg.basic.services.log.LogCommonService;
import com.burgeon.r3.sg.core.model.table.basic.SgCOperationLog;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategy;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.dto.strategy.SgCChannelSourceStrategyDTO;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleFilter;
import com.jackrain.nea.web.face.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/17 15:21
 */
@Component
public class SgCChannelSourceStrategyUnSubmitFilter extends BaseSingleFilter<SgCChannelSourceStrategyDTO> {
    @Autowired
    private SgCChannelSourceStrategyMapper sgChannelSourceStrategyMapper;

    @Resource
    private LogCommonService logCommonService;

    @Override
    public String getFilterMsgName() {
        return "寻源策略取消审核";
    }

    @Override
    public Class<?> getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgCChannelSourceStrategyDTO mainObject, User loginUser) {
        Long id = mainObject.getId();
        SgCChannelSourceStrategy sourceStrategy = sgChannelSourceStrategyMapper.selectById(id);
        if (Objects.isNull(sourceStrategy)) {
            return new ValueHolderV14(ResultCode.FAIL, "当前记录已不存在!");
        }
        Integer status = sourceStrategy.getStatus();
        if (!SgSourcingConstants.BILL_SOURCE_STRATEGY_SUBMIT.equals(status)) {
            return new ValueHolderV14(ResultCode.FAIL, "当前单据状态不是已审核状态,不允许取消审核!");
        }
        StorageUtils.setBModelDefalutDataByUpdate(mainObject, loginUser);
        mainObject.setModifierename(loginUser.getEname());
        mainObject.setStatus(SgSourcingConstants.BILL_SOURCE_STRATEGY_UNSUBMIT);
        SgCOperationLog operationLog = logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY",
                OperationTypeEnum.RESERVE_AUDIT.getOperationValue(), mainObject.getId(), "寻源策略定义",
                null, null, null, loginUser);
        logCommonService.insertLog(operationLog);
        return new ValueHolderV14(ResultCode.SUCCESS, "取消审核成功！");
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgCChannelSourceStrategyDTO mainObject, User loginUser) {
        return null;
    }
}
