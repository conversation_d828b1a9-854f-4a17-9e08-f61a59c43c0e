package com.burgeon.r3.sg.sourcing.services.report;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.basic.model.request.SgStorageQuerySaRequest;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySaResult;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategyMapper;
import com.burgeon.r3.sg.channel.model.request.strategy.SgCChannelSkuStrategyQueryInfoRequest;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelRatioStrategyQueryInfoResult;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelSkuStrategyQuerySaInfoResult;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.sourcing.model.request.report.SgShopSaStorageDevOpsInfoQueryRequest;
import com.burgeon.r3.sg.sourcing.model.request.syncgradientstrategy.SgCSyncGradientStrategyByQtyQueryRequest;
import com.burgeon.r3.sg.sourcing.model.result.report.SgShopSaStorageDevOpsResult;
import com.burgeon.r3.sg.sourcing.model.result.report.ShopSaStorageDevOpsInfoResult;
import com.burgeon.r3.sg.sourcing.model.result.syncgradientstrategy.SgCSyncGradientStrategyByQtyQueryResult;
import com.burgeon.r3.sg.sourcing.services.syncgradientstrategy.SgCSyncGradientStrategyService;
import com.google.common.base.Throwables;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.UserQueryCmd;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: hwy
 * @time: 2021/8/16 16:19
 */
@Component
@Slf4j
public class ShopSaStorageDevOpsInfoService {


    @Autowired
    private SgCChannelSkuStrategyMapper skuStrategyMapper;

    @Autowired
    private SgCChannelRatioStrategyMapper ratioStrategyMapper;

    @Autowired
    private SgCSyncGradientStrategyService sgCSyncGradientStrategyService;

    @Autowired
    private SgBChannelProductMapper sgBChannelProductMapper;


    public ValueHolderV14<SgShopSaStorageDevOpsResult> queryShopSaStorageDevOpsInfo(SgShopSaStorageDevOpsInfoQueryRequest request) {
        ValueHolderV14<SgShopSaStorageDevOpsResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        SgShopSaStorageDevOpsResult devOpsResult = new SgShopSaStorageDevOpsResult();
        List<ShopSaStorageDevOpsInfoResult> devOpsInfoResultList = new ArrayList<>();
        devOpsResult.setSaStorageInfoList(devOpsInfoResultList);
        valueHolderV14.setData(devOpsResult);
        devOpsResult.setTotalRowCount(0);
        if (log.isDebugEnabled()) {
            log.debug(" ShopSaStorageDevOpsInfoService.queryShopSaStorageDevOpsInfo Start Param:{}", JSONObject.toJSONString(request));
        }
        try {
            checkParam(request, valueHolderV14);
            List<Long> saStoreIds = new ArrayList<>();
            List<Long> psCSkuIds = new ArrayList<>();
            psCSkuIds.add(request.getPsCSkuId());
            Date currDate = new Date();
            //1.查询店铺条码策略配置
            SgCChannelSkuStrategyQueryInfoRequest queryInfoRequest = new SgCChannelSkuStrategyQueryInfoRequest();
            queryInfoRequest.setCpCShopId(request.getCpCShopId());
            queryInfoRequest.setBeginTime(currDate);
            queryInfoRequest.setEndTime(currDate);
            queryInfoRequest.setSkuIds(Arrays.asList(request.getSkuId()));
            // 普通条码策略
            List<SgCChannelRatioStrategyQueryInfoResult> ratioStrategyResults = ratioStrategyMapper.queryRatioStrategyInfoByShopIdAndFlag(request.getCpCShopId(), SgConstants.IS_YES_OR_NO_N);
            if (CollectionUtils.isNotEmpty(ratioStrategyResults)) {
                ratioStrategyResults.stream().forEach(o -> {
                    if (!saStoreIds.contains(o.getSgCSaStoreId())) {
                        saStoreIds.add(o.getSgCSaStoreId());
                    }
                });
            }
            if (CollectionUtils.isEmpty(saStoreIds)) {
                return valueHolderV14;
            }
            SgCSaStoreMapper sgCSaStoreMapper = ApplicationContextHandle.getBean(SgCSaStoreMapper.class);
            List<SgCSaStore> sgCSaStores = sgCSaStoreMapper.selectShareInfoBySaStoreId(saStoreIds);
            Map<Long, SgCSaStore> saStoreInfo = new HashMap<>();
            if (CollectionUtils.isNotEmpty(sgCSaStores)) {
                saStoreInfo.putAll(sgCSaStores.stream().collect(Collectors.toMap(SgCSaStore::getId, Function.identity())));
            }
            if (CollectionUtils.isEmpty(saStoreIds)) {
                valueHolderV14.setCode(ResultCode.SUCCESS);
                return valueHolderV14;
            }
            UserQueryCmd userQueryCmd = (UserQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), UserQueryCmd.class.getName(), "cp-ext", "1.0");
            User user = userQueryCmd.getUsersById(request.getUserId());
            //查询配销仓库存
            SgStorageQueryService storageQueryService = ApplicationContextHandle.getBean(SgStorageQueryService.class);
            SgStorageQuerySaRequest sgStorageQuerySaRequest = new SgStorageQuerySaRequest();
            sgStorageQuerySaRequest.setSkuIds(psCSkuIds);
            sgStorageQuerySaRequest.setSgCSaStoreIds(saStoreIds);
            ValueHolderV14<List<SgStorageRedisQuerySaResult>> storageResult = storageQueryService.querySaStorageWithRedis(sgStorageQuerySaRequest, user);
            if (!storageResult.isOK()) {
                log.error("ShopSaStorageDevOpsInfoService.queryShopSaStorageDevOpsInfo 查询库存失败:{}", storageResult.getMessage());
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("查询库存失败");
                return valueHolderV14;
            }
            List<SgStorageRedisQuerySaResult> storageResultList = storageResult.getData();
            Map<Long, SgStorageRedisQuerySaResult> storageMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(storageResultList)) {
                storageMap.putAll(storageResultList.stream().collect(Collectors.toMap(SgStorageRedisQuerySaResult::getSgCSaStoreId, Function.identity())));
            }

            SgBChannelProduct sgBChannelProduct = sgBChannelProductMapper.selectOne(new QueryWrapper<SgBChannelProduct>().lambda()
                    .eq(SgBChannelProduct::getCpCShopId, request.getCpCShopId())
                    .eq(SgBChannelProduct::getSkuId, request.getSkuId())
                    .eq(SgBChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y));
            String saStoreType = sgBChannelProduct == null ? SgConstants.SA_STORE_TYPE_ACTIVITY : StringUtils.isEmpty(sgBChannelProduct.getSaStoreType()) ? SgConstants.SA_STORE_TYPE_ACTIVITY : sgBChannelProduct.getSaStoreType();
            Long id = 1L;
            for (SgCChannelRatioStrategyQueryInfoResult saRateInfo : ratioStrategyResults) {
                String saType = saRateInfo.getType();
                if (StringUtils.isBlank(saType) && !saType.equals(request.getSaStoreType())){
                    continue;
                }
                Long sgCSaStoreId = saRateInfo.getSgCSaStoreId();
                // 配销仓类型不一致 不做计算
                if (!saStoreType.equals(saRateInfo.getType())) {
                    continue;
                }
                ShopSaStorageDevOpsInfoResult devOpsInfoResult = new ShopSaStorageDevOpsInfoResult();
                devOpsInfoResult.setId(id++);
                devOpsInfoResult.setSaStoreType(saStoreType);
                devOpsInfoResult.setCpCShopId(request.getCpCShopId());
                devOpsInfoResult.setSkuId(request.getSkuId());
                devOpsInfoResult.setPsCSkuId(request.getPsCSkuId());
                devOpsInfoResult.setPriority(saRateInfo.getOrderno());
                devOpsInfoResult.setSgCSaStoreId(sgCSaStoreId);
                devOpsInfoResult.setSgCSaStoreEcode(saRateInfo.getSgCSaStoreEcode());
                devOpsInfoResult.setSgCSaStoreEname(saRateInfo.getSgCSaStoreEname());
                SgStorageRedisQuerySaResult storage = storageMap.get(sgCSaStoreId);
                BigDecimal qtyAvailable = storage == null ? BigDecimal.ZERO : storage.getQtyAvailable();
                BigDecimal ratio = saRateInfo.getRatio();
                Map<String, BigDecimal> qtySyncMap = getQtySync(request, sgCSaStoreId, user, qtyAvailable, ratio);
                ratio = qtySyncMap.get("ratio");
                BigDecimal qtySync = qtySyncMap.get("qtySync");
                devOpsInfoResult.setRatio(ratio);
                devOpsInfoResult.setType(saRateInfo.getType());
                devOpsInfoResult.setQtyStorage(storage == null ? BigDecimal.ZERO : storage.getQtyStorage());
                devOpsInfoResult.setQtyAvailable(qtyAvailable);
                devOpsInfoResult.setQtyPreout(storage == null ? BigDecimal.ZERO : storage.getQtyPreout());
                devOpsInfoResult.setQtysync(qtySync.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : qtySync);
                if (saStoreInfo.containsKey(sgCSaStoreId)) {
                    SgCSaStore sgCSaStore = saStoreInfo.get(sgCSaStoreId);
                    devOpsInfoResult.setSgCShareStoreId(sgCSaStore.getSgCShareStoreId());
                    devOpsInfoResult.setSgCShareStoreEcode(sgCSaStore.getEcode());
                    devOpsInfoResult.setSgCShareStoreEname(sgCSaStore.getEname());
                }
                devOpsInfoResultList.add(devOpsInfoResult);
            }
            if (CollectionUtils.isNotEmpty(devOpsInfoResultList)) {
                //过滤
                devOpsInfoResultList = devOpsInfoResultList.stream().filter(result -> request.getSaStoreType().equals(result.getType())).collect(Collectors.toList());
                Collections.sort(devOpsInfoResultList);
                int size = devOpsInfoResultList.size();
                Integer startIndex = request.getStartindex();
                Integer range = request.getRange();
                if (size < startIndex) {
                    devOpsInfoResultList.clear();
                } else if (startIndex + range <= size) {
                    devOpsInfoResultList = devOpsInfoResultList.subList(startIndex, range + startIndex);
                } else if (startIndex + range > size) {
                    devOpsInfoResultList = devOpsInfoResultList.subList(startIndex, size);
                }
                devOpsResult.setSaStorageInfoList(devOpsInfoResultList);
            }
            devOpsResult.setTotalRowCount(id.intValue() - 1);
        } catch (Exception e) {
            log.error("ShopSaStorageDevOpsInfoService.queryShopSaStorageDevOpsInfo 查询库存信息失败:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("查询库存信息失败");
        }
        return valueHolderV14;
    }

    /**
     * @param request:
     * @param saStoreId:
     * @param user:
     * @param qtyAvailable:
     * @param ratio:
     * @Description: 计算同步库存
     * @Author: hwy
     * @Date: 2021/8/26 12:01
     * @return: java.util.Map<java.lang.String, java.math.BigDecimal>
     **/
    private Map<String, BigDecimal> getQtySync(SgShopSaStorageDevOpsInfoQueryRequest request, Long saStoreId, User user, BigDecimal qtyAvailable, BigDecimal ratio) {
        Map<String, BigDecimal> syncMap = new HashMap<>();
        //获取配销仓库存梯度策略信息
        SgCSyncGradientStrategyByQtyQueryRequest strategyRequest = new SgCSyncGradientStrategyByQtyQueryRequest();
        strategyRequest.setCpCShopId(request.getCpCShopId());
        strategyRequest.setStoreId(saStoreId);
        strategyRequest.setType(SgConstants.SYNC_GRADIENT_STRATEGY_SA);
        strategyRequest.setTargetQty(qtyAvailable);
        ValueHolderV14<SgCSyncGradientStrategyByQtyQueryResult> strategyQueryResult =
                sgCSyncGradientStrategyService.querySyncGradientStrategyByQty(strategyRequest, user);
        if (ResultCode.FAIL == strategyQueryResult.getCode()) {
            log.error(" 查询配销仓梯度策略失败:{}", strategyQueryResult.getMessage());
            throw new NDSException("查询配销仓梯度策略失败" + strategyQueryResult.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug(" ShopSaStorageDevOpsInfoService.getQtySync saStorageHolder:{};",
                    JSONObject.toJSONString(strategyQueryResult.getData()));
        }
        //设置配销仓仓计算用比例
        if (strategyQueryResult.getData() != null) {
            ratio = strategyQueryResult.getData().getRatio();
        }
        BigDecimal qtySync = qtyAvailable.multiply(ratio).divide(SgConstants.NUMBER_100, 0, BigDecimal.ROUND_DOWN);
        syncMap.put("ratio", ratio);
        syncMap.put("qtySync", qtySync);
        return syncMap;
    }

    private void checkParam(SgShopSaStorageDevOpsInfoQueryRequest request, ValueHolderV14<SgShopSaStorageDevOpsResult> valueHolderV14) {
        if (request == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("查询参数为空");
            return;
        }
        if (request.getCpCShopId() == null) {
            if (log.isDebugEnabled()) {
                log.debug("店铺id为空");
            }
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("店铺id为空");
            return;
        }
        if (StringUtils.isEmpty(request.getSkuId())) {
            if (log.isDebugEnabled()) {
                log.debug("平台条码为空");
            }
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("平台条码为空");
            return;
        }
        if (request.getPsCSkuId() == null) {
            if (log.isDebugEnabled()) {
                log.debug("系统条码为空");
            }
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("系统条码为空");
            return;
        }

    }

}