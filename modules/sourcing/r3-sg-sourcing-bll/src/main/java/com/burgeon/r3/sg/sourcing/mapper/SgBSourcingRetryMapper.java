package com.burgeon.r3.sg.sourcing.mapper;

import com.burgeon.r3.sg.core.model.table.sourcing.SgBSourcingRetry;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description:
 * @author: hwy
 * @time: 2021/9/6 19:09
 */

@Repository
public interface SgBSourcingRetryMapper extends ExtentionMapper<SgBSourcingRetry> {

    @Update({"<script>",
            "update sg_b_sourcing_retry set isactive = 'N' where id in ",
            "<foreach item='item' collection='ids' separator=',' open='(' close=')' > #{item} </foreach>",
            "</script>"
    })
    void batchUpdateStatusByIds(@Param("ids") List<Long> ids);

    @Update({
            "<script>",
            "update sg_b_sourcing_retry set retry_count = retry_count+1  where id in " ,
            "<foreach item='item' collection='ids' separator=',' open='(' close=')' > #{item} </foreach>",
            "</script>"
    })
    void batchUpdateCountByIds(@Param("ids") List<Long> ids);
}