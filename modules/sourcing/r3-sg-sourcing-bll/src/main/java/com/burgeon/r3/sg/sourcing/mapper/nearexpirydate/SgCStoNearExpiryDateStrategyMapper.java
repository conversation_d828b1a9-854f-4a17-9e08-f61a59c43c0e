package com.burgeon.r3.sg.sourcing.mapper.nearexpirydate;

import com.burgeon.r3.sg.core.model.table.sourcing.nearexpirydate.SgCStoNearExpiryDateStrategy;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface SgCStoNearExpiryDateStrategyMapper extends ExtentionMapper<SgCStoNearExpiryDateStrategy> {

    @Select("<script> SELECT\n" +
            "\t* \n" +
            "FROM\n" +
            "\tsg_c_sto_near_expiry_date_strategy \n" +
            "WHERE\n" +
            "\tps_c_pro_id = #{proId} \n" +
            "\tAND type = #{type}\n" +
            "\tAND start_time  &lt;= now()\n" +
            "\tAND end_time  &gt;= now()\n" +
            "\tAND ISACTIVE = 'Y'\n" +
            "<if test = 'type == \"1\"'>" +
            "\tAND cp_c_phy_warehouse_id IN " +
            " <foreach collection='storeList' item='storeId' open='(' separator=',' close=')'> #{storeId} </foreach>" +
            "</if> " +
            " </script>")
    List<SgCStoNearExpiryDateStrategy> queryByProIdStoNearExpiryDateStrategy(@Param("proId") Long skuId, @Param("storeList") List<Long> storeList, @Param("type") String type);
}
