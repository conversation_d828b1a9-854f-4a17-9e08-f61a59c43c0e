package com.burgeon.r3.sg.sourcing.services.item;


import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.ext.SgCStoProduceDateRange;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgGroupSkuInfo;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemS2L;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOutOfStockBaseResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 拆单策略
 * @author: hwy
 * @time: 2021/6/11 16:41
 */
@Slf4j
@Component
public class SgFindSourceSplitStrategyService extends StrategyHandle {


    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {

        log.info(LogUtil.format("SgFindSourceSplitStrategyService.handleRequest S->L二阶段寻源派单 拆单策略执行器 param:{}",
                SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L),
                request.getTraceId(), JSONObject.toJSONString(request));

        ValueHolderV14<StrategyBaseResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        SgFindSourceStrategyS2LRequest strategyRequest = (SgFindSourceStrategyS2LRequest) request;
        SgFindSourceStrategyS2LResult strategyResult = (SgFindSourceStrategyS2LResult) strategyRequest.getStrategyBaseResult();

        if (strategyResult == null) {
            FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器获取上游策略执行结果:不存在可使用的仓库");
            valueHolderV14.setMessage("拆单策略执行器获取上游策略执行结果:不存在可使用的仓库 策略执行结束!");
            FindSourceStrategyUtils.allOut(strategyRequest);
            valueHolderV14.setData(request.getStrategyBaseResult());
            return valueHolderV14;
        }

        //设置默认返回值
        SgFindSourceStrategyS2LResult splitResult = strategyResult;

        //2.查询店铺拆单规则
        try {
            switch (strategyRequest.getSplitType()) {
                //按明细拆
                case StrategyConstants.ORDER_SPLIT_TYPE_ITEM:
                    splitResult = splitByItem(strategyRequest, strategyResult);
                    break;
                //按数量拆
                case StrategyConstants.ORDER_SPLIT_TYPE_NUM:
                    splitResult = splitByNum(strategyRequest, strategyResult);
                    break;
                //不拆
                case StrategyConstants.ORDER_SPLIT_TYPE_NO:
                    FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器无单仓库可以整单满足");
                    FindSourceStrategyUtils.allOut(strategyRequest);
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage(Resources.getMessage("无单仓库可以整单满足,寻源失败!",
                            strategyResult.parseRemovedStoreInfoMsg()
                    ));
                    break;
                default: {
                    log.warn("S->L二阶段寻源派单 拆单策略拆单方式类型错误无法执行拆单 策略执行结束!");
                    break;
                }
            }

            //校验拆单策略执行器拆单后拆单数量合计是否超出了原单数量
            Long overSplitSkuId = checkQtyOverSplit(strategyRequest, splitResult);
            if (overSplitSkuId != null) {
                log.error("S->L二阶段寻源派单 拆单策略执行器拆单后拆单数量合计超出了共享占用数量,请联系统管理员 商品条码ID:{}", overSplitSkuId);
                FindSourceStrategyUtils.allOut(strategyRequest);
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(Resources.getMessage("拆单策略执行后拆单数量合计超出了原单数量,请联系系统管理员,寻源失败!"));
            }

            //拆单情况下,无需执行评分策略
            splitResult.setDoScoreStrategy(Boolean.FALSE);
            splitResult.setRemovedStoreInfo(strategyResult.getRemovedStoreInfo());
            splitResult.setOutOfStockMessages(strategyResult.getOutOfStockMessages());

            //发生缺货无法拆单的情况下，不继续占单
            if (!CollectionUtils.isEmpty(strategyResult.getOutOfStockMessages())) {
                valueHolderV14.setCode(ResultCode.FAIL);
                String outOfStockMessages = "";

                for (SgFindSourceStrategyOutOfStockBaseResult baseResult : strategyResult.getOutOfStockMessages()) {
                    outOfStockMessages = outOfStockMessages.concat(baseResult.getOutOfStockMessage());
                }

                valueHolderV14.setMessage(outOfStockMessages);
            }

            strategyRequest.setStrategyBaseResult(splitResult);
            valueHolderV14.setData(splitResult);

        } catch (Exception e) {
            log.error(LogUtil.format("SgFindSourceSplitStrategyService.handleRequest S->L二阶段寻源派单 拆单策略执行器发生异常 exception:{}",
                    SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L_EXCEPTION),
                    request.getTraceId(), Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("S->L 二阶段寻源派单 拆单策略执行器 发生异常");
        }

        return doNext(request, valueHolderV14);
    }

    /**
     * 校验拆单策略执行器拆单后拆单数量合计是否超出了原单数量
     *
     * @param strategyRequest
     * @param splitResult
     * @return
     */
    private Long checkQtyOverSplit(SgFindSourceStrategyS2LRequest strategyRequest,
                                   SgFindSourceStrategyS2LResult splitResult) {

        try {

            Map<Long, BigDecimal> requestSkuIdQtyMap = strategyRequest.getSkuItems().stream().filter(skuItemS2L -> {
                return !StrategyConstants.OUT_DEFAULT_STORE_ID.equals(skuItemS2L.getShareItemId());
            }).collect(Collectors.groupingBy(SkuItemS2L::getPsCSkuId, Collectors.reducing(BigDecimal.ZERO, SkuItemS2L::getQtyPreOut, BigDecimal::add)));

            FindSourceStrategyUtils.outputJsonLog("S->L二阶段寻源派单 拆单策略执行器拆单前条码对应的共享占用单数量:{}",
                    requestSkuIdQtyMap);

            Map<Long, BigDecimal> resultSkuIdQtyMap = splitResult.getSkuResultList().stream().filter(s2LResult -> {
                return !StrategyConstants.OUT_DEFAULT_STORE_ID.equals(s2LResult.getShareItemId());
            }).collect(Collectors.groupingBy(SgFindSourceStrategySkuS2LResult::getPsCSkuId,
                    Collectors.reducing(BigDecimal.ZERO, SgFindSourceStrategySkuS2LResult::getQtyPreOut, BigDecimal::add)));

            FindSourceStrategyUtils.outputJsonLog("S->L二阶段寻源派单 拆单策略执行器拆单后条码对应的逻辑占用数量:{}",
                    resultSkuIdQtyMap);

            for (Long psCSkuId : resultSkuIdQtyMap.keySet()) {
                if (!requestSkuIdQtyMap.containsKey(psCSkuId) ||
                        resultSkuIdQtyMap.get(psCSkuId).compareTo(requestSkuIdQtyMap.get(psCSkuId)) > 0) {
                    return psCSkuId;
                }
            }

        } catch (Exception e) {
            log.error(LogUtil.format("SgFindSourceSplitStrategyService.handleRequest S->L二阶段寻源派单 拆单策略执行器发生异常 exception:{}",
                    SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L_EXCEPTION),
                    strategyRequest.getTraceId(), Throwables.getStackTraceAsString(e));
        }

        return null;

    }

    /**
     * @param strategyRequest:
     * @param strategyResult:
     * @Description: 按数量拆
     * @Author: hwy
     * @Date: 2021/8/10 17:40
     * @return: com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyS2LResult
     **/
    private SgFindSourceStrategyS2LResult splitByNum(SgFindSourceStrategyS2LRequest strategyRequest,
                                                     SgFindSourceStrategyS2LResult strategyResult) {

        FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-开始-执行按数量拆单");

        SgFindSourceStrategyS2LResult splitResult = new SgFindSourceStrategyS2LResult();
        List<SgFindSourceStrategySkuS2LResult> splitSkuResultList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        splitResult.setSkuResultList(splitSkuResultList);
        List<SkuItemS2L> requestSkuItems = strategyRequest.getSkuItems();

        //转化前期策略执行结果为<商品条码Id,<实体仓Id,<生产日期,<逻辑仓Id,可用库存>>>
        Map<Long, Map<Long, SortedMap<String, Map<Long, BigDecimal>>>> skuStorageMap = convertResultList(strategyResult);

        //组合商品
        Map<Long, List<SkuItemS2L>> groupSkuInfo = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
        //未分配的赠品
        List<SkuItemS2L> undistributedGifts = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        //条码库存信息
        List<SgFindSourceStrategySkuS2LResult> skuResultList = strategyResult.getSkuResultList();
        //<shareItemId,itemResultList>
        Map<Long, List<SgFindSourceStrategyStoreItemS2LResult>> skuResultMap = skuResultList.stream().collect(
                Collectors.toMap(SgFindSourceStrategySkuS2LResult::getShareItemId,
                        SgFindSourceStrategySkuS2LResult::getItemResultList));

        List<SkuItemS2L> groupSkuItems;
        SgGroupSkuInfo sgGroupSkuInfo = null;

        for (SkuItemS2L skuItem : requestSkuItems) {

            Long psCSkuId = skuItem.getPsCSkuId();
            //共享占用单占用数量
            BigDecimal qtyPreOut = skuItem.getQtyPreOut();
            Long shareItemId = skuItem.getShareItemId();

            //条码拆单结果
            SgFindSourceStrategySkuS2LResult splitSkuResult = creatNewSplitSkuResult(skuItem);

            //共享占用单明细ID下所有的仓库
            List<SgFindSourceStrategyStoreItemS2LResult> itemResults = skuResultMap.get(shareItemId);

            //当前sku下的所有的仓库
            Map<Long, SortedMap<String, Map<Long, BigDecimal>>> warehouseMap = skuStorageMap.get(psCSkuId);

            //赋值效期范围
            SgCStoProduceDateRange stoProductDateRange = new SgCStoProduceDateRange();
            stoProductDateRange.setBeginDate(skuItem.getBeginProduceDate());
            stoProductDateRange.setEndDate(skuItem.getEndProduceDate());

            //正常商品，按数量拆单的情况下，无实体库存、缺货，直接缺货处理
            if (StrategyConstants.PRO_TYPE_NORMAL.equals(skuItem.getProType()) &&
                    (CollectionUtils.isEmpty(itemResults)
                            || StrategyConstants.OUT_DEFAULT_STORE_ID.equals(shareItemId)
                            || MapUtils.isEmpty(warehouseMap))) {
                //设置条码拆单结果
                setSplitSkuResultSingle(splitSkuResult, qtyPreOut, StrategyConstants.OUT_DEFAULT_STORE_ID, null);
                splitSkuResultList.add(splitSkuResult);
                continue;
            }

            //按照库存可用量降序排序
            if (CollectionUtils.isNotEmpty(itemResults)) {
                Collections.sort(itemResults);
            }

            FindSourceStrategyUtils.outputJsonLog("S->L二阶段寻源派单 拆单策略执行器执行按数量拆单 当前库存信息:{},所有库存信息:{},当前SKU的生产日期维度的库存:{}",
                    itemResults, skuResultMap, warehouseMap);

            //按商品类型统计
            switch (skuItem.getProType()) {
                //正常商品:直接分配仓
                case StrategyConstants.PRO_TYPE_NORMAL: {
                    splitOrderWithNumNormal(skuItem, itemResults, splitSkuResultList, warehouseMap);
                    break;
                }
                //组合商品:按虚拟条码聚合组合商品 后续同一分配
                case StrategyConstants.PRO_TYPE_GROUP: {
                    sgGroupSkuInfo = skuItem.getGroupSkuInfo();
                    if (sgGroupSkuInfo == null) {
                        log.warn("S->L二阶段寻源派单 拆单策略组合商品未指定组合商品信息sourceItemId:{} 策略执行结束!",
                                skuItem.getSourceItemId());
                        break;
                    }

                    Long groupSkuId = sgGroupSkuInfo.getGroupSkuId();
                    if (groupSkuInfo.containsKey(groupSkuId)) {
                        groupSkuItems = groupSkuInfo.get(groupSkuId);
                        groupSkuItems.add(skuItem);
                    } else {
                        groupSkuItems = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
                        groupSkuItems.add(skuItem);
                        groupSkuInfo.put(groupSkuId, groupSkuItems);
                    }
                    break;
                }
                //先记录最后分配
                case StrategyConstants.PRO_TYPE_GIFT: {
                    undistributedGifts.add(skuItem);
                    break;
                }
                default: {
                    log.error("拆单策略 商品类型错误 无法自动拆单");
                    break;
                }
            }
        }

        //分配组合商品
        if (MapUtils.isNotEmpty(groupSkuInfo)) {
            splitOrderWithNumGroup(groupSkuInfo, skuResultMap, splitSkuResultList, skuStorageMap,
                    strategyResult.getOutOfStockMessages());
        }

        //分配赠品
        if (!CollectionUtils.isEmpty(undistributedGifts)) {
            splitOrderWithGift(undistributedGifts, skuResultMap, splitSkuResultList, skuStorageMap,
                    strategyResult.getOutOfStockMessages(), strategyRequest.getSplitType());
        }

        //清理Map
        skuStorageMap.clear();
        groupSkuInfo.clear();
        skuResultMap.clear();

        return splitResult;
    }

    /**
     * @param strategyRequest:
     * @param strategyResult:
     * @Description: 按明细拆单
     * @Author: hwy
     * @Date: 2021/6/16 13:46
     * @return: com.burgeon.r3.sg.sourcing.model.result.FindSourceStrategyResult
     **/
    private SgFindSourceStrategyS2LResult splitByItem(SgFindSourceStrategyS2LRequest strategyRequest,
                                                      SgFindSourceStrategyS2LResult strategyResult) {

        FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-开始-执行按明细拆单");

        SgFindSourceStrategyS2LResult splitResult = new SgFindSourceStrategyS2LResult();
        List<SgFindSourceStrategySkuS2LResult> splitSkuResultList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        splitResult.setSkuResultList(splitSkuResultList);
        List<SkuItemS2L> requestSkuItems = strategyRequest.getSkuItems();

        //转化前期策略执行结果为<商品条码Id,<实体仓Id,<生产日期,<逻辑仓Id,可用库存>>>
        Map<Long, Map<Long, SortedMap<String, Map<Long, BigDecimal>>>> skuStorageMap = convertResultList(strategyResult);

        //订单明细单位的共享占用数量集合（存在同一个共享）<sourceItemId,共享占用数量>
        Map<Long, BigDecimal> preOutQtyMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

        //订单缺货集合<sourceItemId,sourceItemId>
        Map<Long, Long> outSkuItemIdMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

        //整理[订单明细单位的共享占用数量集合],[订单缺货集合]
        requestSkuItems.stream().forEach(o -> {
            Long sourceItemId = o.getSourceItemId();
            BigDecimal preOutQty = o.getQtyPreOut();

            //按照订单明细id合计占单数量
            if (preOutQtyMap.containsKey(sourceItemId)) {
                preOutQty = preOutQty.add(preOutQtyMap.get(sourceItemId));
            }

            preOutQtyMap.put(sourceItemId, preOutQty);

            //缺货的情况下，直接使用订单明细id
            if (StrategyConstants.OUT_DEFAULT_STORE_ID.equals(o.getShareItemId()) && !outSkuItemIdMap.containsKey(sourceItemId)) {
                outSkuItemIdMap.put(sourceItemId, sourceItemId);
            }
        });

        //组合商品
        Map<Long, List<SkuItemS2L>> groupSkuInfo = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
        //未分配的赠品
        List<SkuItemS2L> undistributedGifts = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        //条码库存信息
        List<SgFindSourceStrategySkuS2LResult> skuResultList = strategyResult.getSkuResultList();
        //<shareItemId,itemResultList>
        Map<Long, List<SgFindSourceStrategyStoreItemS2LResult>> skuResultMap = skuResultList.stream().collect(
                Collectors.toMap(SgFindSourceStrategySkuS2LResult::getShareItemId, SgFindSourceStrategySkuS2LResult::getItemResultList));

        List<SkuItemS2L> groupSkuItems = null;
        SgGroupSkuInfo sgGroupSkuInfo = null;

        //遍历订单明细
        for (SkuItemS2L skuItem : requestSkuItems) {
            Long psCSkuId = skuItem.getPsCSkuId();
            //订单数量
            BigDecimal orderQty = skuItem.getQty();
            //共享占用单占用数量
            BigDecimal qtyPreOut = skuItem.getQtyPreOut();
            Long shareItemId = skuItem.getShareItemId();
            Long sourceItemId = skuItem.getSourceItemId();

            //条码拆单结果
            SgFindSourceStrategySkuS2LResult splitSkuResult = creatNewSplitSkuResult(skuItem);

            //共享占用单明细ID下所有的仓库
            List<SgFindSourceStrategyStoreItemS2LResult> itemResults = skuResultMap.get(shareItemId);

            //当前sku下的所有的仓库
            Map<Long, SortedMap<String, Map<Long, BigDecimal>>> warehouseMap = skuStorageMap.get(psCSkuId);

            //赋值效期范围
            SgCStoProduceDateRange productDateRange = new SgCStoProduceDateRange();
            productDateRange.setBeginDate(skuItem.getBeginProduceDate());
            productDateRange.setEndDate(skuItem.getEndProduceDate());

            //正常商品，按行拆单的情况下，无实体库存、缺货、订单数量>共享占单数量的情况下，直接缺货处理
            if (StrategyConstants.PRO_TYPE_NORMAL.equals(skuItem.getProType()) && (
                    CollectionUtils.isEmpty(itemResults)
                            || outSkuItemIdMap.containsKey(sourceItemId)
                            || orderQty.compareTo(preOutQtyMap.get(sourceItemId)) > 0
                            || MapUtils.isEmpty(warehouseMap))) {
                setSplitSkuResultSingle(splitSkuResult, orderQty, StrategyConstants.OUT_DEFAULT_STORE_ID, null);
                splitSkuResultList.add(splitSkuResult);
                continue;
            }

            //按照库存可用量降序排序
            if (CollectionUtils.isNotEmpty(itemResults)) {
                Collections.sort(itemResults);
            }

            FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器执行按明细拆单 当前库存信息:{},所有库存信息:{},当前SKU的生产日期维度的库存:{}",
                    itemResults, skuResultMap, warehouseMap);

            //按商品类型统计
            switch (skuItem.getProType()) {
                //正常商品:直接分配仓
                case StrategyConstants.PRO_TYPE_NORMAL: {
                    splitOrderWithItemNormal(splitSkuResult, itemResults, orderQty, qtyPreOut, warehouseMap, productDateRange);
                    splitSkuResultList.add(splitSkuResult);
                    break;
                }
                //组合商品: 按虚拟条码聚合组合商品 后续同一分配
                case StrategyConstants.PRO_TYPE_GROUP: {
                    sgGroupSkuInfo = skuItem.getGroupSkuInfo();
                    if (sgGroupSkuInfo == null) {
                        log.warn("S->L二阶段寻源派单 拆单策略组合商品未指定组合商品信息sourceItemId:{} 策略执行结束!",
                                skuItem.getSourceItemId());
                        break;
                    }

                    Long groupSkuId = sgGroupSkuInfo.getGroupSkuId();
                    if (groupSkuInfo.containsKey(groupSkuId)) {
                        groupSkuItems = groupSkuInfo.get(groupSkuId);
                        groupSkuItems.add(skuItem);
                    } else {
                        groupSkuItems = new ArrayList<>();
                        groupSkuItems.add(skuItem);
                        groupSkuInfo.put(groupSkuId, groupSkuItems);
                    }
                    break;
                }
                //先记录最后分配
                case StrategyConstants.PRO_TYPE_GIFT: {
                    undistributedGifts.add(skuItem);
                    break;
                }
                default: {
                    log.error("拆单策略 商品类型错误 无法自动拆单");
                    break;
                }
            }
        }

        //分配组合商品
        if (!MapUtils.isEmpty(groupSkuInfo)) {
            splitOrderWithItemGroup(groupSkuInfo, skuResultMap, splitSkuResultList, skuStorageMap,
                    strategyResult.getOutOfStockMessages());
        }

        //分配赠品
        if (!CollectionUtils.isEmpty(undistributedGifts)) {
            splitOrderWithGift(undistributedGifts, skuResultMap, splitSkuResultList, skuStorageMap,
                    strategyResult.getOutOfStockMessages(), strategyRequest.getSplitType());
        }

        //清理Map
        skuStorageMap.clear();
        preOutQtyMap.clear();
        outSkuItemIdMap.clear();
        groupSkuInfo.clear();
        skuResultMap.clear();

        return splitResult;
    }

    /**
     * description:转化ResultList
     *
     * @Author: liuwenjin
     * @Date 2022/8/2 15:37
     */
    private Map<Long, Map<Long, SortedMap<String, Map<Long, BigDecimal>>>> convertResultList(SgFindSourceStrategyS2LResult strategyResult) {

        //<商品条码Id,<实体仓Id,<生产日期,<逻辑仓Id,可用库存>>>
        Map<Long, Map<Long, SortedMap<String, Map<Long, BigDecimal>>>> psSkuMap =
                new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

        //执行所有的条码集合
        for (SgFindSourceStrategySkuS2LResult skuS2LResult : strategyResult.getSkuResultList()) {

            Long psSkuId = skuS2LResult.getPsCSkuId();

            Map<Long, SortedMap<String, Map<Long, BigDecimal>>> warehouseMap = null;

            //判断条码Map中有没有当前条码
            if (psSkuMap.containsKey(psSkuId)) {
                warehouseMap = psSkuMap.get(psSkuId);
            } else {
                warehouseMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            }

            //执行所有的实体仓集合
            for (SgFindSourceStrategyStoreItemS2LResult itemS2LResult : skuS2LResult.getItemResultList()) {

                //实体仓
                Long warehouseId = itemS2LResult.getStoreId();

                SortedMap<String, Map<Long, BigDecimal>> produceDateMap = null;

                //判断实体仓Map中有没有当前实体仓
                if (warehouseMap.containsKey(warehouseId)) {
                    produceDateMap = warehouseMap.get(warehouseId);
                } else {
                    produceDateMap = new TreeMap<>();
                }

                //获取逻辑仓库存
                Map<Long, SortedMap<String, BigDecimal>> logicStorageMap = itemS2LResult.getLogicStorageMap();

                if (MapUtils.isEmpty(logicStorageMap)) {
                    continue;
                }

                //重新整理逻辑仓库存
                for (Long storeId : logicStorageMap.keySet()) {

                    //生产日期，可用数量Map
                    Map<String, BigDecimal> storageProduceDateMap = logicStorageMap.get(storeId);

                    //循环生产日期
                    for (String produceDate : storageProduceDateMap.keySet()) {

                        Map<Long, BigDecimal> storeMap = null;

                        //可用库存
                        BigDecimal qty = storageProduceDateMap.get(produceDate);

                        if (produceDateMap.containsKey(produceDate)) {
                            storeMap = produceDateMap.get(produceDate);
                        } else {
                            storeMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
                        }

                        //批次下逻辑仓库存不存在的情况下
                        if (!storeMap.containsKey(storeId)) {
                            storeMap.put(storeId, qty);
                        }

                        //给仓库map赋值批次
                        if (MapUtils.isNotEmpty(storeMap)) {
                            produceDateMap.put(produceDate, storeMap);
                        }
                    }
                }

                //给仓库map赋值批次
                if (MapUtils.isNotEmpty(produceDateMap)) {
                    warehouseMap.put(warehouseId, produceDateMap);
                }

                //给skumap赋值仓库
                if (MapUtils.isNotEmpty(warehouseMap)) {
                    psSkuMap.put(psSkuId, warehouseMap);
                }
            }
        }

        return psSkuMap;
    }

    /**
     * @param undistributedGifts:
     * @param skuResultMap:
     * @param splitSkuResultList:
     * @Description: 按明细拆分赠品
     * @Author: hwy
     * @Date: 2021/6/17 17:06
     * @return: void
     **/
    private void splitOrderWithGift(List<SkuItemS2L> undistributedGifts,
                                    Map<Long, List<SgFindSourceStrategyStoreItemS2LResult>> skuResultMap,
                                    List<SgFindSourceStrategySkuS2LResult> splitSkuResultList,
                                    Map<Long, Map<Long, SortedMap<String, Map<Long, BigDecimal>>>> skuStorageMap,
                                    List<SgFindSourceStrategyOutOfStockBaseResult> outOfStockMessages,
                                    String splitType) {

        FindSourceStrategyUtils.outputJsonLog("S->L二阶段寻源派单 拆单策略执行器-开始-执行赠品按行拆单 undistributedGifts:{}",
                undistributedGifts);

        //分配缺货仓的条码
        List<Long> outStockSkuList = new ArrayList<>();

        //统计每个仓库分配商品种类的数量 仓库发货能力排序使用
        Map<Long, Integer> warehousePriorityMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
        warehousePriorityMap.put(StrategyConstants.OUT_DEFAULT_STORE_ID, -1);

        //根据当前订单中各实体仓能满足条码数量的顺序进行排序，缺货实体仓优先度最低
        List<Long> warehouseSortedList = splitSkuResultList.stream().map(item -> {
            for (SgFindSourceStrategyStoreItemS2LResult itemResult : item.getItemResultList()) {
                Long warehouseId = itemResult.getStoreId();
                //统计非缺货仓条码数量发货能力
                if (!StrategyConstants.OUT_DEFAULT_STORE_ID.equals(warehouseId)) {
                    Integer priority = warehousePriorityMap.get(warehouseId);
                    priority = priority == null ? 1 : priority + 1;
                    warehousePriorityMap.put(warehouseId, priority);
                    //标记缺货条码
                } else if (!outStockSkuList.contains(item.getPsCSkuId())) {
                    outStockSkuList.add(item.getPsCSkuId());
                }
                return warehouseId;
            }
            return StrategyConstants.OUT_DEFAULT_STORE_ID;
        }).distinct().sorted((p1, p2) -> warehousePriorityMap.get(p2).compareTo(warehousePriorityMap.get(p1))).collect(Collectors.toList());

        FindSourceStrategyUtils.outputJsonLog("S->L二阶段寻源派单 拆单策略执行器-执行赠品按行拆单 遍历已分配商品结果 缺货商品:{} 已分配正常商品的实体仓id:{}",
                outStockSkuList, warehouseSortedList);

        //循环处理赠品
        for (SkuItemS2L skuItem : undistributedGifts) {

            Long bindingSkuId = skuItem.getSgGiftSkuInfo() == null ? null : skuItem.getSgGiftSkuInfo().getBindingSkuId();

            //<warehouseId,SgFindSourceStrategyStoreItemS2LResult>
            Map<Long, SgFindSourceStrategyStoreItemS2LResult> giftStorageMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

            // 非缺货明细
            if (!StrategyConstants.OUT_DEFAULT_STORE_ID.equals(skuItem.getShareItemId())) {
                giftStorageMap = skuResultMap.get(skuItem.getShareItemId()).stream().filter(
                        o -> !StrategyConstants.OUT_DEFAULT_STORE_ID.equals(o.getStoreId())).collect(
                        Collectors.toMap(SgFindSourceStrategyStoreItemS2LResult::getStoreId, Function.identity()));
            }

            if (bindingSkuId == null) {
                splitGiftUnbindingSku(skuItem, giftStorageMap, splitSkuResultList, warehouseSortedList, skuStorageMap,
                        outOfStockMessages, splitType);
            } else {
                spltGiftBindingSku(skuItem, giftStorageMap, splitSkuResultList, warehouseSortedList, skuStorageMap,
                        outOfStockMessages, splitType);
            }
        }
    }

    /**
     * @param skuItem:             待分配的订单赠品明细
     * @param giftStorageMap:      当前明细的库存信息
     * @param splitSkuResultList:  已分配的明细信息
     * @param warehouseSortedList: 对于当前订单的仓库发货能力排行
     * @Description: 按数量分配赠品
     * @Author: hwy
     * @Date: 2021/6/18 14:30
     * @return: void
     **/
    private void splitGiftUnbindingSku(SkuItemS2L skuItem,
                                       Map<Long, SgFindSourceStrategyStoreItemS2LResult> giftStorageMap,
                                       List<SgFindSourceStrategySkuS2LResult> splitSkuResultList,
                                       List<Long> warehouseSortedList,
                                       Map<Long, Map<Long, SortedMap<String, Map<Long, BigDecimal>>>> skuStorageMap,
                                       List<SgFindSourceStrategyOutOfStockBaseResult> outOfStockMessages,
                                       String splitType) {

        FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-开始-执行未绑定赠品拆单 giftSkuId:{}", skuItem.getPsCSkuId());

        //当前赠品分配结果
        SgFindSourceStrategySkuS2LResult splitSkuResult = creatNewSplitSkuResult(skuItem);

        //订单所需赠品数量
        BigDecimal qtyPreOut = skuItem.getQtyPreOut();

        Long filterWarehouseId = null;

        //能够整个发赠品的实体仓
        List<Long> giftWarehouseIds = new ArrayList<>();

        if (MapUtils.isNotEmpty(giftStorageMap)) {
            giftWarehouseIds.addAll(giftStorageMap.values().stream().filter(
                    o -> o.getQty().compareTo(qtyPreOut) >= 0).map(
                    SgFindSourceStrategyStoreItemS2LResult::getStoreId).distinct().collect(Collectors.toList()));
        }

        // 赠品能整行发货
        if (CollectionUtils.isNotEmpty(giftWarehouseIds)) {

            List<Long> filterWarehouseIds = new ArrayList<>();
            filterWarehouseIds.addAll(warehouseSortedList);
            filterWarehouseIds.retainAll(giftWarehouseIds);

            //判断与订单商品是否存在交集
            if (CollectionUtils.isNotEmpty(filterWarehouseIds) && warehouseSortedList.size() > 0) {
                //取一个发货能力高的仓发货
                filterWarehouseId = filterWarehouseIds.get(0);
            } else if (!StrategyConstants.ORDER_SPLIT_TYPE_NO.equals(splitType) && skuItem.getSgGiftSkuInfo().getIsSpilt()) {
                //不存在交集的情况下，并且订单可拆赠品也可拆，取第一个可用实体仓
                filterWarehouseId = giftWarehouseIds.get(0);
            }

            if (filterWarehouseId != null) {
                FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-执行未绑定赠品拆单 非挂靠赠品条码:{}不缺货且可以从仓库:{}发货",
                        skuItem.getPsCSkuId(), filterWarehouseIds);

                splitSkuResult.setQtyPreOut(skuItem.getQtyPreOut());
                setSplitSkuResultSingle(splitSkuResult, skuItem.getQtyPreOut(), filterWarehouseId, giftStorageMap.get(filterWarehouseId));
                splitSkuResultList.add(splitSkuResult);
            } else {
                SgFindSourceStrategyOutOfStockBaseResult outStockResult = creatNewOutStockBaseResult(skuItem);
                outStockResult.setOutOfStockMessage("非挂靠赠品不缺货但与其他商品没有可出库的实体仓，且不允许拆单，无法发货！");
                outOfStockMessages.add(outStockResult);

                FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-执行未绑定赠品拆单 非挂靠赠品条码:{}不缺货但与其他商品没有交集实体仓，且不允许拆单无法发货！",
                        skuItem.getPsCSkuId());
                setSplitSkuResultSingle(splitSkuResult, qtyPreOut, StrategyConstants.OUT_DEFAULT_STORE_ID, null);
                splitSkuResultList.add(splitSkuResult);
            }

            return;

        }

        //赠品不能整行发货并且订单允许按数量拆，赠品可拆
        if (CollectionUtils.isEmpty(giftWarehouseIds) && StrategyConstants.ORDER_SPLIT_TYPE_NUM.equals(splitType)
                && skuItem.getSgGiftSkuInfo().getIsSpilt()) {

            setSplitSkuResultWithNum(skuItem, new ArrayList<>(giftStorageMap.values()), splitSkuResultList,
                    skuStorageMap.get(skuItem.getPsCSkuId()));

            return;
        }

        setSplitSkuResultSingle(splitSkuResult, qtyPreOut, StrategyConstants.OUT_DEFAULT_STORE_ID, null);
        splitSkuResultList.add(splitSkuResult);

        return;

    }

    /**
     * @param skuItem:            订单明细信息-挂靠赠品
     * @param giftStorageMap:     当前赠品的库存
     * @param splitSkuResultList: 已分配的订单明细信息
     * @Description: 挂靠赠品的拆分 :如果能与挂靠商品同仓发出则发同一仓  否则都发缺货仓
     * @Author: hwy
     * @Date: 2021/6/16 16:09
     * @return: void
     **/
    private void spltGiftBindingSku(SkuItemS2L skuItem,
                                    Map<Long, SgFindSourceStrategyStoreItemS2LResult> giftStorageMap,
                                    List<SgFindSourceStrategySkuS2LResult> splitSkuResultList,
                                    List<Long> warehouseSortedList,
                                    Map<Long, Map<Long, SortedMap<String, Map<Long, BigDecimal>>>> skuStorageMap,
                                    List<SgFindSourceStrategyOutOfStockBaseResult> outOfStockMessages,
                                    String splitType) {

        FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-开始-执行绑定赠品拆单 giftSkuId:{}", skuItem.getPsCSkuId());

        Map<Long, SgFindSourceStrategySkuS2LResult> bindingSplitResultMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
        Long bindingSkuId = skuItem.getSgGiftSkuInfo().getBindingSkuId();
        BigDecimal qtyPreOut = skuItem.getQtyPreOut();

        //当前赠品分配结果
        SgFindSourceStrategySkuS2LResult newSplitSkuResult = creatNewSplitSkuResult(skuItem);

        if (splitSkuResultList.size() == 0) {
            FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-执行绑定赠品拆单 挂靠赠品条码:{}缺货, 未找到挂靠商品条码:{}的明细",
                    skuItem.getPsCSkuId(), bindingSkuId);
            setSplitSkuResultSingle(newSplitSkuResult, qtyPreOut, StrategyConstants.OUT_DEFAULT_STORE_ID, null);
            splitSkuResultList.add(newSplitSkuResult);
            return;
        }

        //获取赠品对应的挂靠商品信息
        for (SgFindSourceStrategySkuS2LResult splitResult : splitSkuResultList) {
            if (bindingSkuId.equals(splitResult.getPsCSkuId())) {
                bindingSplitResultMap.put(splitResult.getShareItemId(), splitResult);
            }
        }

        if (MapUtils.isEmpty(bindingSplitResultMap)) {
            log.error("S->L二阶段寻源派单 拆单策略执行器-执行绑定赠品拆单 挂靠赠品条码:{}未找到挂靠商品条码:{}策略执行失败！",
                    skuItem.getPsCSkuId(), bindingSkuId);
            throw new NDSException(Resources.getMessage("拆单策略-明细拆分挂靠赠品未找到挂靠商品",
                    skuItem.getPsCSkuId(), bindingSkuId));
        }

        //获取挂靠商品的实体仓库存信息<warehouseId,SgFindSourceStrategyStoreItemS2LResult>
        Map<Long, SgFindSourceStrategyStoreItemS2LResult> bindingStorageMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
        for (Long shareItemId : bindingSplitResultMap.keySet()) {

            if (StrategyConstants.OUT_DEFAULT_STORE_ID.equals(shareItemId) ||
                    CollectionUtils.isEmpty(bindingSplitResultMap.get(shareItemId).getItemResultList())) {
                continue;
            }

            for (SgFindSourceStrategyStoreItemS2LResult itemResult : bindingSplitResultMap.get(shareItemId).getItemResultList()) {
                bindingStorageMap.put(itemResult.getStoreId(), itemResult);
            }
        }

        //挂靠商品缺货
        if (MapUtils.isEmpty(bindingStorageMap)) {

            //订单不能拆单并且赠品不能拆单，直接设置赠品也为缺货默认仓
            if (StrategyConstants.ORDER_SPLIT_TYPE_NO.equals(splitType) || !skuItem.getSgGiftSkuInfo().getIsSpilt()) {
                log.info("S->L二阶段寻源派单 拆单策略执行器-执行绑定赠品拆单 挂靠赠品条码:{}挂靠商品条码:{}的实体仓为缺货默认仓 赠品不能拆单计入缺货默认仓",
                        skuItem.getPsCSkuId(), bindingSkuId);

                SgFindSourceStrategyOutOfStockBaseResult outStockResult = creatNewOutStockBaseResult(skuItem);
                outStockResult.setOutOfStockMessage("赠品挂靠的主商品缺货且不允许拆单，无法发货！");
                outOfStockMessages.add(outStockResult);

                setSplitSkuResultSingle(newSplitSkuResult, qtyPreOut, StrategyConstants.OUT_DEFAULT_STORE_ID, null);
                splitSkuResultList.add(newSplitSkuResult);
                return;
            }

            //直接执行未绑定商品的拆单策略
            splitGiftUnbindingSku(skuItem, giftStorageMap, splitSkuResultList, warehouseSortedList, skuStorageMap,
                    outOfStockMessages, splitType);

            return;

        }

        //获取挂靠商品实体仓信息
        Set<Long> bindingWarehouseIds = bindingStorageMap.keySet();
        //获取赠品不缺货实体仓
        Set<Long> storeIds = new HashSet<>();
        for (Long storeId : giftStorageMap.keySet()) {
            SgFindSourceStrategyStoreItemS2LResult itemS2LResult = giftStorageMap.get(storeId);
            if (itemS2LResult.getQty().compareTo(skuItem.getQtyPreOut()) >= 0) {
                storeIds.add(itemS2LResult.getStoreId());
            }
        }
        bindingWarehouseIds.retainAll(storeIds);

        //赠品库存中不包含挂靠商品的仓
        if (CollectionUtils.isEmpty(bindingWarehouseIds) || bindingSplitResultMap.containsKey(StrategyConstants.OUT_DEFAULT_STORE_ID)) {
            if (StrategyConstants.ORDER_SPLIT_TYPE_NO.equals(splitType) || !skuItem.getSgGiftSkuInfo().getIsSpilt()) {
                log.info("S->L二阶段寻源派单 拆单策略执行器-执行绑定赠品拆单 挂靠赠品条码:{}挂靠商品条码:{}的实体仓:{}中不包含赠品库存,二者计入缺货默认仓",
                        skuItem.getPsCSkuId(), bindingSkuId, bindingStorageMap.keySet());

                SgFindSourceStrategyOutOfStockBaseResult outStockResult = creatNewOutStockBaseResult(skuItem);
                outStockResult.setOutOfStockMessage("主商品能出库的实体仓中赠品缺货且不允许拆单，主商品和赠品都无法发货！");
                outOfStockMessages.add(outStockResult);

                for (SgFindSourceStrategySkuS2LResult bindingSplitResult : bindingSplitResultMap.values()) {
                    bindingSplitResult.setQtyPreOut(BigDecimal.ZERO);
                    for (SgFindSourceStrategyStoreItemS2LResult itemResult : bindingSplitResult.getItemResultList()) {
                        itemResult.setStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                    }
                }

                setSplitSkuResultSingle(newSplitSkuResult, qtyPreOut, StrategyConstants.OUT_DEFAULT_STORE_ID, null);
                splitSkuResultList.add(newSplitSkuResult);
                return;
            }

            //直接执行未绑定商品的拆单策略
            splitGiftUnbindingSku(skuItem, giftStorageMap, splitSkuResultList, warehouseSortedList, skuStorageMap,
                    outOfStockMessages, splitType);

            return;

        }

        //赋值效期范围
        SgCStoProduceDateRange stoProductDateRange = new SgCStoProduceDateRange();
        stoProductDateRange.setBeginDate(skuItem.getBeginProduceDate());
        stoProductDateRange.setEndDate(skuItem.getEndProduceDate());

        Map<Long, SortedMap<String, Map<Long, BigDecimal>>> warehouseMap = skuStorageMap.get(skuItem.getPsCSkuId());

        for (Long bindWarehouseId : bindingWarehouseIds) {

            //先判断占单数量是否满足实际库存，如果不满足则继续循环
            if (giftStorageMap.get(bindWarehouseId).getQty().compareTo(qtyPreOut) >= 0) {
                //模拟判断库存是否足够扣减
                Boolean isStockEnough = checkSplitOrderQty(stoProductDateRange, warehouseMap, qtyPreOut,
                        bindWarehouseId, Boolean.FALSE);

                if (isStockEnough) {
                    //设置计划占用数量
                    newSplitSkuResult.setQtyPreOut(qtyPreOut);
                    setSplitSkuResultSingle(newSplitSkuResult, qtyPreOut, bindWarehouseId, giftStorageMap.get(bindWarehouseId));
                    splitSkuResultList.add(newSplitSkuResult);
                    return;
                }
            }
        }

        if (StrategyConstants.ORDER_SPLIT_TYPE_ITEM.equals(splitType)) {

            for (Long giftWarehouseId : giftStorageMap.keySet()) {

                //先判断占单数量是否满足实际库存，如果不满足则继续循环
                if (giftStorageMap.get(giftWarehouseId).getQty().compareTo(qtyPreOut) >= 0) {
                    //模拟判断库存是否足够扣减
                    Boolean isStockEnough = checkSplitOrderQty(stoProductDateRange, warehouseMap, qtyPreOut,
                            giftWarehouseId, Boolean.FALSE);

                    if (isStockEnough) {
                        //设置计划占用数量
                        newSplitSkuResult.setQtyPreOut(qtyPreOut);
                        setSplitSkuResultSingle(newSplitSkuResult, qtyPreOut, giftWarehouseId, giftStorageMap.get(giftWarehouseId));
                        splitSkuResultList.add(newSplitSkuResult);
                        return;
                    }
                }
            }

        } else {
            setSplitSkuResultWithNum(skuItem, new ArrayList<>(giftStorageMap.values()), splitSkuResultList,
                    skuStorageMap.get(skuItem.getPsCSkuId()));
            return;
        }

        log.info("S->L二阶段寻源派单 拆单策略执行器-执行绑定赠品拆单 挂靠赠品条码:{}挂靠商品条码:{}的实体仓:{}中不包含赠品库存,二者计入缺货默认仓",
                skuItem.getPsCSkuId(), bindingSkuId, bindingStorageMap.keySet());

        SgFindSourceStrategyOutOfStockBaseResult outStockResult = creatNewOutStockBaseResult(skuItem);
        outStockResult.setOutOfStockMessage("主商品能出库的实体仓中赠品缺货且不允许拆单，主商品和赠品都无法发货！");
        outOfStockMessages.add(outStockResult);

        for (SgFindSourceStrategySkuS2LResult bindingSplitResult : bindingSplitResultMap.values()) {
            bindingSplitResult.setQtyPreOut(BigDecimal.ZERO);
            for (SgFindSourceStrategyStoreItemS2LResult itemResult : bindingSplitResult.getItemResultList()) {
                itemResult.setStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
            }
        }

        setSplitSkuResultSingle(newSplitSkuResult, qtyPreOut, StrategyConstants.OUT_DEFAULT_STORE_ID, null);
        splitSkuResultList.add(newSplitSkuResult);

        //清理
        bindingSplitResultMap.clear();
        bindingStorageMap.clear();

        return;
    }

    /**
     * @param groupSkuInfo:       组合商品信息 key:groupSkuId  value:itemSku
     * @param skuResultMap:       所有商品的库存信息
     * @param splitSkuResultList: 已分配的订单明细信息
     * @param skuStorageMap:
     * @Description: 组合商品按数量分配
     * @Author: hwy
     * @Date: 2021/6/17 11:31
     * @return: void
     **/
    private void splitOrderWithNumGroup(Map<Long, List<SkuItemS2L>> groupSkuInfo,
                                        Map<Long, List<SgFindSourceStrategyStoreItemS2LResult>> skuResultMap,
                                        List<SgFindSourceStrategySkuS2LResult> splitSkuResultList,
                                        Map<Long, Map<Long, SortedMap<String, Map<Long, BigDecimal>>>> skuStorageMap,
                                        List<SgFindSourceStrategyOutOfStockBaseResult> outOfStockMessages) {

        FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-开始-执行组合商品按数量拆单 groupSkuInfo:{}",
                JSONObject.toJSONString(groupSkuInfo));

        //每一个零售发货单明细对应的满足库存的实体仓集合<sourceItemId,<shareItemId,SgFindSourceStrategyStoreItemS2LResult>>
        Map<Long, Map<Long, SgFindSourceStrategyStoreItemS2LResult>> skuItemStoresMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

        //遍历组合商品
        for (Long groupSkuId : groupSkuInfo.keySet()) {

            //满足整个组合商品的交集实体仓
            List<Long> filterWarehouseIds = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);

            //优先级 <warehouseId:priority>
            Map<Long, Integer> priorityMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

            //组合商品是否缺货
            Boolean isGroupOutStockFlag = Boolean.FALSE;

            //遍历组合商品中的条码明细
            for (SkuItemS2L skuItem : groupSkuInfo.get(groupSkuId)) {

                Long psCSkuId = skuItem.getPsCSkuId();
                Long shareItemId = skuItem.getShareItemId();
                BigDecimal qtyPreout = skuItem.getQtyPreOut();

                //赋值效期范围
                SgCStoProduceDateRange stoProductDateRange = new SgCStoProduceDateRange();
                stoProductDateRange.setBeginDate(skuItem.getBeginProduceDate());
                stoProductDateRange.setEndDate(skuItem.getEndProduceDate());

                //当前条码下的所有实体仓库存
                Map<Long, SortedMap<String, Map<Long, BigDecimal>>> warehouseMap = skuStorageMap.get(psCSkuId);

                //共享占用单明细ID对应的仓库为空 则整个组合商品全部缺货 继续下个组合商品的判断
                if (!skuResultMap.containsKey(shareItemId)) {
                    isGroupOutStockFlag = Boolean.TRUE;
                    FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-组合商品按数量拆单明细缺货 组合商品明细条码:{} 组合商品虚拟条码:{}",
                            psCSkuId, groupSkuId);
                    continue;
                }

                //当前组合商品明细条码可发实体仓
                List<Long> itemWarehouseIds = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);

                //满足当前组合商品明细的实体仓库存<warehouseId,SgFindSourceStrategyStoreItemS2LResult>
                //考虑组合商品中存在同SKU不同效期的情况
                Map<Long, SgFindSourceStrategyStoreItemS2LResult> storageResultMap = new LinkedHashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

                for (SgFindSourceStrategyStoreItemS2LResult storageResult : skuResultMap.get(shareItemId)) {

                    if (StrategyConstants.OUT_DEFAULT_STORE_ID.equals(storageResult.getStoreId())) {
                        continue;
                    }

                    //模拟判断库存是否足够扣减
                    Boolean isStockEnough = checkSplitOrderQty(stoProductDateRange, warehouseMap, qtyPreout,
                            storageResult.getStoreId(), Boolean.FALSE);

                    if (isStockEnough) {
                        //收集当前组合商品明细条码可发实体仓
                        itemWarehouseIds.add(storageResult.getStoreId());
                        //收集实体仓优先级
                        if (!priorityMap.containsKey(storageResult.getStoreId())) {
                            priorityMap.put(storageResult.getStoreId(), storageResult.getPriority());
                        }
                    }

                    //按数量拆单，无条件收集实体仓对应的库存数据
                    storageResultMap.put(storageResult.getStoreId(), storageResult);
                }

                //设置每个共享占用单明细id对应的可发货实体仓对应满足条件的库存数量
                if (MapUtils.isNotEmpty(storageResultMap)) {
                    skuItemStoresMap.put(shareItemId, storageResultMap);
                } else {
                    //只存在缺货实体仓的情况
                    isGroupOutStockFlag = Boolean.TRUE;
                    FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-组合商品按数量拆单明细缺货 组合商品明细条码:{} 组合商品虚拟条码:{}",
                            psCSkuId, groupSkuId);
                    continue;
                }

                //不存在满足整单发货的实体仓，不做交集处理
                if (isGroupOutStockFlag) {
                    continue;
                }

                //满足整个组合商品的交集实体仓(第一条明细)的情况下，进行初始化
                if (CollectionUtils.isEmpty(filterWarehouseIds)) {
                    filterWarehouseIds.addAll(itemWarehouseIds);
                    continue;
                }

                //重新计算满足整个组合商品的交集实体仓
                filterWarehouseIds.retainAll(itemWarehouseIds);

                //交集实体仓为空
                if (CollectionUtils.isEmpty(filterWarehouseIds)) {
                    isGroupOutStockFlag = Boolean.TRUE;
                    FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-组合商品按数量拆单明细缺货 当前明细库存与其他明细库存没有交集仓 组合商品明细条码:{} 组合商品虚拟条码:{}",
                            psCSkuId, groupSkuId);
                    continue;
                }

            }

            //交集不为空,针对实体仓优先级排序
            List<Long> sortWarehouseIds = null;
            if (CollectionUtils.isNotEmpty(filterWarehouseIds)) {
                sortWarehouseIds = filterWarehouseIds.stream().sorted((p1, p2) -> priorityMap.get(p2).compareTo(priorityMap.get(p1)))
                        .collect(Collectors.toList());
            }

            //分配明细条码
            for (SkuItemS2L skuItem : groupSkuInfo.get(groupSkuId)) {

                SgFindSourceStrategySkuS2LResult splitSkuResult = creatNewSplitSkuResult(skuItem);

                Map<Long, SgFindSourceStrategyStoreItemS2LResult> storeItemS2LResults = skuItemStoresMap.get(skuItem.getShareItemId());

                //组合商品能整单发货 并且 有整单满足的实体仓
                if (!isGroupOutStockFlag && !CollectionUtils.isEmpty(sortWarehouseIds)) {
                    //不缺货的情况下，取交集实体仓排序后的第一个仓
                    splitSkuResult.setQtyPreOut(skuItem.getQtyPreOut());
                    setSplitSkuResultSingle(splitSkuResult, skuItem.getQtyPreOut(), sortWarehouseIds.get(0),
                            storeItemS2LResults.get(sortWarehouseIds.get(0)));
                    splitSkuResultList.add(splitSkuResult);
                    continue;
                }

                //组合商品不能拆单或共享占用单明细标识为缺货
                if (skuItem.getGroupSkuInfo() == null || !skuItem.getGroupSkuInfo().getIsSpilt() || MapUtils.isEmpty(storeItemS2LResults)) {
                    //不能整单发也不能拆单直接缺货
                    SgFindSourceStrategyOutOfStockBaseResult outStockResult = creatNewOutStockBaseResult(skuItem);
                    if (!skuItem.getGroupSkuInfo().getIsSpilt()) {
                        outStockResult.setOutOfStockMessage("组合商品中部分商品存在缺货且不允许拆单，无法发货！");
                        outOfStockMessages.add(outStockResult);
                    } else {
                        FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-执行组合商品按数量拆单 组合商品中部分商品存在缺货，部分商品无法发货！");
                        //    outStockResult.setOutOfStockMessage("组合商品中部分商品存在缺货，部分商品无法发货！");
                    }

                    setSplitSkuResultSingle(splitSkuResult, skuItem.getQtyPreOut(), StrategyConstants.OUT_DEFAULT_STORE_ID, null);
                    splitSkuResultList.add(splitSkuResult);
                    continue;
                }

                setSplitSkuResultWithNum(skuItem, new ArrayList<>(storeItemS2LResults.values()),
                        splitSkuResultList, skuStorageMap.get(skuItem.getPsCSkuId()));

            }

            //清理
            priorityMap.clear();

        }

        //清理
        skuItemStoresMap.clear();

        FindSourceStrategyUtils.outputJsonLog("S->L二阶段寻源派单 拆单策略执行器-结束-执行组合商品按行拆单 splitSkuResult:{}",
                splitSkuResultList);
    }

    /**
     * @param groupSkuInfo:
     * @param skuResultMap:
     * @param splitSkuResultList:
     * @param skuStorageMap:
     * @Description: 组合商品按明细分配
     * @Date: 2021/6/16 15:38
     * @return: void
     * @Author: hwy
     **/
    private void splitOrderWithItemGroup(Map<Long, List<SkuItemS2L>> groupSkuInfo,
                                         Map<Long, List<SgFindSourceStrategyStoreItemS2LResult>> skuResultMap,
                                         List<SgFindSourceStrategySkuS2LResult> splitSkuResultList,
                                         Map<Long, Map<Long, SortedMap<String, Map<Long, BigDecimal>>>> skuStorageMap,
                                         List<SgFindSourceStrategyOutOfStockBaseResult> outOfStockMessages) {

        FindSourceStrategyUtils.outputJsonLog("S->L二阶段寻源派单 拆单策略执行器-开始-执行组合商品按行拆单 groupSkuInfo:{}",
                groupSkuInfo);

        //每一个零售发货单明细对应的满足库存的实体仓集合<sourceItemId,<warehouseId,SgFindSourceStrategyStoreItemS2LResult>>
        Map<Long, Map<Long, SgFindSourceStrategyStoreItemS2LResult>> skuItemStoresMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

        //遍历组合商品
        for (Long groupSkuId : groupSkuInfo.keySet()) {

            //满足整个组合商品的交集实体仓
            List<Long> filterWarehouseIds = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);

            //优先级 <warehouseId:priority>
            Map<Long, Integer> priorityMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

            //组合商品是否缺货
            Boolean isGroupOutStockFlag = Boolean.FALSE;

            //遍历组合商品中的条码明细
            for (SkuItemS2L skuItem : groupSkuInfo.get(groupSkuId)) {

                Long psCSkuId = skuItem.getPsCSkuId();
                Long shareItemId = skuItem.getShareItemId();
                Long sourceItemId = skuItem.getSourceItemId();
                BigDecimal qtyPreout = skuItem.getQtyPreOut();

                //赋值效期范围
                SgCStoProduceDateRange stoProductDateRange = new SgCStoProduceDateRange();
                stoProductDateRange.setBeginDate(skuItem.getBeginProduceDate());
                stoProductDateRange.setEndDate(skuItem.getEndProduceDate());

                //当前条码下的所有实体仓库存
                Map<Long, SortedMap<String, Map<Long, BigDecimal>>> warehouseMap = skuStorageMap.get(psCSkuId);

                //共享占用单明细为缺货
                if (StrategyConstants.OUT_DEFAULT_STORE_ID.equals(shareItemId)) {
                    isGroupOutStockFlag = Boolean.TRUE;
                    FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-组合商品按行拆单明细缺货 组合商品明细条码:{} 组合商品虚拟条码:{}",
                            psCSkuId, groupSkuId);
                    continue;
                }

                //共享占用单明细ID对应的仓库为空 则整个组合商品全部缺货 继续下个组合商品的判断
                if (!skuResultMap.containsKey(shareItemId)) {
                    isGroupOutStockFlag = Boolean.TRUE;
                    FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-组合商品按行拆单明细缺货 组合商品明细条码:{} 组合商品虚拟条码:{}",
                            psCSkuId, groupSkuId);
                    continue;
                }

                //当前组合商品明细条码可发实体仓
                List<Long> itemWarehouseIds = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);

                //满足当前组合商品明细的实体仓库存<warehouseId,SgFindSourceStrategyStoreItemS2LResult>
                //考虑组合商品中存在同SKU不同效期的情况
                Map<Long, SgFindSourceStrategyStoreItemS2LResult> storageResultMap = new LinkedHashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

                for (SgFindSourceStrategyStoreItemS2LResult storageResult : skuResultMap.get(shareItemId)) {

                    if (StrategyConstants.OUT_DEFAULT_STORE_ID.equals(storageResult.getStoreId())) {
                        continue;
                    }

                    //模拟判断库存是否足够扣减
                    Boolean isStockEnough = checkSplitOrderQty(stoProductDateRange, warehouseMap, qtyPreout,
                            storageResult.getStoreId(), Boolean.FALSE);

                    if (isStockEnough) {

                        //收集当前组合商品明细条码可发实体仓
                        itemWarehouseIds.add(storageResult.getStoreId());
                        //收集实体仓优先级
                        if (!priorityMap.containsKey(storageResult.getStoreId())) {
                            priorityMap.put(storageResult.getStoreId(), storageResult.getPriority());
                        }
                        //收集实体仓对应的库存数据
                        storageResultMap.put(storageResult.getStoreId(), storageResult);
                    }
                }

                //设置每个订单明细id对应的可发货实体仓
                if (MapUtils.isNotEmpty(storageResultMap)) {
                    skuItemStoresMap.put(sourceItemId, storageResultMap);
                } else {
                    //只存在缺货实体仓的情况
                    isGroupOutStockFlag = Boolean.TRUE;
                    FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-组合商品按行拆单明细缺货 组合商品明细条码:{} 组合商品虚拟条码:{}",
                            psCSkuId, groupSkuId);
                    continue;
                }

                //不存在满足整单发货的实体仓，不做交集处理
                if (isGroupOutStockFlag) {
                    continue;
                }

                //满足整个组合商品的交集实体仓(第一条明细)的情况下，进行初始化
                if (CollectionUtils.isEmpty(filterWarehouseIds)) {
                    filterWarehouseIds.addAll(itemWarehouseIds);
                    continue;
                }

                //重新计算满足整个组合商品的交集实体仓
                filterWarehouseIds.retainAll(itemWarehouseIds);

                //交集实体仓为空
                if (CollectionUtils.isEmpty(filterWarehouseIds)) {
                    isGroupOutStockFlag = Boolean.TRUE;
                    FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-组合商品按行拆单明细缺货 当前明细库存与其他明细库存没有交集仓 组合商品明细条码:{} 组合商品虚拟条码:{}",
                            psCSkuId, groupSkuId);
                    continue;
                }
            }

            //交集不为空,针对实体仓优先级排序
            List<Long> sortWarehouseIds = null;
            if (CollectionUtils.isNotEmpty(filterWarehouseIds)) {
                sortWarehouseIds = filterWarehouseIds.stream().sorted((p1, p2) -> priorityMap.get(p2).compareTo(priorityMap.get(p1)))
                        .collect(Collectors.toList());
            }

            //分配明细条码
            for (SkuItemS2L skuItem : groupSkuInfo.get(groupSkuId)) {

                SgFindSourceStrategySkuS2LResult splitSkuResult = creatNewSplitSkuResult(skuItem);

                Map<Long, SgFindSourceStrategyStoreItemS2LResult> storeItemS2LResults = skuItemStoresMap.get(skuItem.getSourceItemId());

                //组合商品能整单发货
                if (!isGroupOutStockFlag) {
                    //不缺货的情况下，取交集实体仓排序后的第一个仓
                    splitSkuResult.setQtyPreOut(skuItem.getQtyPreOut());
                    setSplitSkuResultSingle(splitSkuResult, skuItem.getQtyPreOut(), sortWarehouseIds.get(0),
                            storeItemS2LResults.get(sortWarehouseIds.get(0)));
                    splitSkuResultList.add(splitSkuResult);
                    continue;
                }

                //组合商品不能拆单或共享占用单明细标识为缺货
                if (skuItem.getGroupSkuInfo() == null || !skuItem.getGroupSkuInfo().getIsSpilt() || MapUtils.isEmpty(storeItemS2LResults)) {

                    SgFindSourceStrategyOutOfStockBaseResult outStockResult = creatNewOutStockBaseResult(skuItem);
                    if (!skuItem.getGroupSkuInfo().getIsSpilt()) {
                        outStockResult.setOutOfStockMessage("组合商品中部分商品存在缺货且不允许拆单，无法发货！");
                        outOfStockMessages.add(outStockResult);
                    } else {
                        FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拆单策略执行器-执行组合商品按行拆单 组合商品中部分商品存在缺货，部分商品无法发货！");
                        //    outStockResult.setOutOfStockMessage("组合商品中部分商品存在缺货，部分商品无法发货！");
                    }

                    //不能整单发也不能拆单直接缺货
                    setSplitSkuResultSingle(splitSkuResult, skuItem.getQtyPreOut(), StrategyConstants.OUT_DEFAULT_STORE_ID, null);
                    splitSkuResultList.add(splitSkuResult);
                    continue;
                }

                //默认取第一个实体仓记录
                Optional<SgFindSourceStrategyStoreItemS2LResult> firstItemS2LResult = storeItemS2LResults.values().stream().findFirst();

                //组合商品能拆单
                if (firstItemS2LResult.isPresent()) {
                    splitSkuResult.setQtyPreOut(skuItem.getQtyPreOut());
                    setSplitSkuResultSingle(splitSkuResult, skuItem.getQtyPreOut(), firstItemS2LResult.get().getStoreId(),
                            firstItemS2LResult.get());
                    splitSkuResultList.add(splitSkuResult);
                } else {
                    setSplitSkuResultSingle(splitSkuResult, skuItem.getQtyPreOut(), StrategyConstants.OUT_DEFAULT_STORE_ID, null);
                    splitSkuResultList.add(splitSkuResult);
                }

            }

            //清理
            priorityMap.clear();

        }

        //清理
        skuItemStoresMap.clear();

        FindSourceStrategyUtils.outputJsonLog("S->L二阶段寻源派单 拆单策略执行器-结束-执行组合商品按行拆单 splitSkuResult:{}",
                splitSkuResultList);
    }

    /**
     * @param skuItem:
     * @param itemResults:
     * @param splitSkuResultList:
     * @param warehouseMap:
     * @Description: 正常商品按数量拆
     * @Author: hwy
     * @Date: 2021/6/17 10:35
     * @return: void
     **/
    private void splitOrderWithNumNormal(SkuItemS2L skuItem,
                                         List<SgFindSourceStrategyStoreItemS2LResult> itemResults,
                                         List<SgFindSourceStrategySkuS2LResult> splitSkuResultList,
                                         Map<Long, SortedMap<String, Map<Long, BigDecimal>>> warehouseMap) {

        //不包含当前库存库存时，直接设置缺货仓
        if (CollectionUtils.isEmpty(itemResults)) {
            //当前条码拆单结果
            SgFindSourceStrategySkuS2LResult newSplitSkuResult = creatNewSplitSkuResult(skuItem);
            setSplitSkuResultSingle(newSplitSkuResult, skuItem.getQtyPreOut(), StrategyConstants.OUT_DEFAULT_STORE_ID, null);
            splitSkuResultList.add(newSplitSkuResult);
            return;
        }

        setSplitSkuResultWithNum(skuItem, itemResults, splitSkuResultList, warehouseMap);

        return;

    }

    /**
     * @param splitSkuResult:
     * @param itemResults:
     * @param orderQty:
     * @param qtyPreOut:
     * @param warehouseMap:
     * @param stoProductDateRange:
     * @Description: 正常商品按条码拆
     * @Author: hwy
     * @Date: 2021/6/15 16:41
     * @return: void
     **/
    private void splitOrderWithItemNormal(SgFindSourceStrategySkuS2LResult splitSkuResult,
                                          List<SgFindSourceStrategyStoreItemS2LResult> itemResults,
                                          BigDecimal orderQty,
                                          BigDecimal qtyPreOut,
                                          Map<Long, SortedMap<String, Map<Long, BigDecimal>>> warehouseMap,
                                          SgCStoProduceDateRange stoProductDateRange) {

        int size = itemResults.size();

        for (int i = 0; i < size; i++) {

            SgFindSourceStrategyStoreItemS2LResult itemResult = itemResults.get(i);

            if (StrategyConstants.OUT_DEFAULT_STORE_ID.equals(itemResult.getStoreId())) {

                //全部执行完,记录缺货默认仓
                if (i == size - 1) {
                    setSplitSkuResultSingle(splitSkuResult, orderQty, StrategyConstants.OUT_DEFAULT_STORE_ID, null);
                }
                continue;
            }

            //先判断占单数量是否满足实际库存，如果不满足则继续循环
            if (itemResult.getQty().compareTo(qtyPreOut) >= 0) {
                //模拟判断库存是否足够扣减
                Boolean isStockEnough = checkSplitOrderQty(stoProductDateRange, warehouseMap, qtyPreOut,
                        itemResult.getStoreId(), Boolean.FALSE);

                if (isStockEnough) {
                    //设置计划占用数量
                    splitSkuResult.setQtyPreOut(qtyPreOut);
                    setSplitSkuResultSingle(splitSkuResult, qtyPreOut, itemResult.getStoreId(), itemResult);
                    break;
                }
            }

            //全部执行完  记录缺货默认仓
            if (i == size - 1) {
                setSplitSkuResultSingle(splitSkuResult, orderQty, StrategyConstants.OUT_DEFAULT_STORE_ID, null);
            }
        }
    }

    /**
     * description:检验拆单时正常商品库存是否充足
     *
     * @param stoProductDateRange:
     * @param warehouseMap:
     * @param qtyPreOut:
     * @param warehouseId:
     * @param isRewrite:
     * @Author: liuwenjin
     * @Date 2022/8/4 15:11
     */
    private Boolean checkSplitOrderQty(SgCStoProduceDateRange stoProductDateRange,
                                       Map<Long, SortedMap<String, Map<Long, BigDecimal>>> warehouseMap,
                                       BigDecimal qtyPreOut,
                                       Long warehouseId,
                                       boolean isRewrite) {

        SortedMap<String, Map<Long, BigDecimal>> newProduceDateMap = new TreeMap<>();

        //map无法深copy 使用HashMap putAll
        newProduceDateMap.putAll(warehouseMap.get(warehouseId));
        //模拟占单后的剩余库存
        BigDecimal restQty = qtyPreOut;

        for (String produceDate : newProduceDateMap.keySet()) {

            //判断符合sku日期的仓库
            if (!validateProduceDate(stoProductDateRange, produceDate)) {
                continue;
            }

            Map<Long, BigDecimal> newStoreStorageMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            newStoreStorageMap.putAll(newProduceDateMap.get(produceDate));

            for (Long storeId : newStoreStorageMap.keySet()) {

                //批次库存数量
                BigDecimal qtyAvailable = newStoreStorageMap.get(storeId);

                if (qtyAvailable.compareTo(restQty) >= 0) {
                    //单条仓库批次满足，循环下个sku
                    qtyAvailable = qtyAvailable.subtract(restQty);
                    newStoreStorageMap.put(storeId, qtyAvailable);
                    newProduceDateMap.put(produceDate, newStoreStorageMap);
                    restQty = BigDecimal.ZERO;
                    break;
                } else {
                    restQty = restQty.subtract(qtyAvailable);
                    newStoreStorageMap.put(storeId, BigDecimal.ZERO);
                    newProduceDateMap.put(produceDate, newStoreStorageMap);
                }

            }

            //库存不足并且按照数量拆的情况下，需要扣库存
            if (isRewrite) {
                warehouseMap.put(warehouseId, newProduceDateMap);
            }
        }

        newProduceDateMap.clear();

        return restQty.compareTo(BigDecimal.ZERO) > 0 ? Boolean.FALSE : Boolean.TRUE;
    }

    /**
     * description:验证逻辑仓生产日期是否在范围内
     *
     * @Author: liuwenjin
     * @Date 2022/7/22 19:43
     */
    private boolean validateProduceDate(SgCStoProduceDateRange stoProductDateRange,
                                        String produceDate) {

        String beginDate = stoProductDateRange.getBeginDate();
        String endDate = stoProductDateRange.getEndDate();

        //格式一样 20220722
        if (StringUtils.isNotEmpty(beginDate) && produceDate.compareTo(beginDate) < 0) {
            return Boolean.FALSE;
        }

        if (StringUtils.isNotEmpty(endDate) && endDate.compareTo(produceDate) < 0) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * @param skuItem
     * @return
     */
    private SgFindSourceStrategySkuS2LResult creatNewSplitSkuResult(SkuItemS2L skuItem) {
        SgFindSourceStrategySkuS2LResult newSplitSkuResult = new SgFindSourceStrategySkuS2LResult();
        newSplitSkuResult.setPsCSkuId(skuItem.getPsCSkuId());
        newSplitSkuResult.setShareItemId(skuItem.getShareItemId());
        newSplitSkuResult.setSourceItemId(skuItem.getSourceItemId());
        newSplitSkuResult.setPsCProdimId(skuItem.getPsCProdimId());
        newSplitSkuResult.setPsCProdim4Id(skuItem.getPsCProdim4Id());
        newSplitSkuResult.setItemTotWeight(skuItem.getItemTotWeight());
        newSplitSkuResult.setPsCProId(skuItem.getPsCProId());
        newSplitSkuResult.setBeginProduceDate(skuItem.getBeginProduceDate());
        newSplitSkuResult.setEndProduceDate(skuItem.getEndProduceDate());
        return newSplitSkuResult;
    }

    /**
     * @param skuItem
     * @return
     */
    private SgFindSourceStrategyOutOfStockBaseResult creatNewOutStockBaseResult(SkuItemS2L skuItem) {
        SgFindSourceStrategyOutOfStockBaseResult outStockResult =
                new SgFindSourceStrategyOutOfStockBaseResult();
        outStockResult.setPsCSkuId(skuItem.getPsCSkuId());
        outStockResult.setShareItemId(skuItem.getShareItemId());
        outStockResult.setSourceItemId(skuItem.getSourceItemId());
        return outStockResult;
    }

    private void setSplitSkuResultWithNum(SkuItemS2L skuItem,
                                          List<SgFindSourceStrategyStoreItemS2LResult> itemResults,
                                          List<SgFindSourceStrategySkuS2LResult> splitSkuResultList,
                                          Map<Long, SortedMap<String, Map<Long, BigDecimal>>> warehouseMap) {

        SgFindSourceStrategySkuS2LResult newSplitSkuResult = null;
        BigDecimal totalRestQty = skuItem.getQtyPreOut();
        BigDecimal planPreout = null;

        //赋值效期范围
        SgCStoProduceDateRange stoProductDateRange = new SgCStoProduceDateRange();
        stoProductDateRange.setBeginDate(skuItem.getBeginProduceDate());
        stoProductDateRange.setEndDate(skuItem.getEndProduceDate());

        for (SgFindSourceStrategyStoreItemS2LResult itemResult : itemResults) {

            if (StrategyConstants.OUT_DEFAULT_STORE_ID.equals(itemResult.getStoreId()) ||
                    BigDecimal.ZERO.compareTo(itemResult.getQty()) >= 0) {
                continue;
            }

            newSplitSkuResult = creatNewSplitSkuResult(skuItem);

            //计划占用数量，取库存数量和计划占用数量的较小值
            planPreout = totalRestQty.compareTo(itemResult.getQty()) >= 0 ? itemResult.getQty() : totalRestQty;

            //模拟扣减实体仓库存
            checkSplitOrderQty(stoProductDateRange, warehouseMap, planPreout, itemResult.getStoreId(), Boolean.TRUE);

            //总数减模拟占用剩余数量=占用数量
            newSplitSkuResult.setQtyPreOut(planPreout);
            setSplitSkuResultSingle(newSplitSkuResult, planPreout, itemResult.getStoreId(), itemResult);
            splitSkuResultList.add(newSplitSkuResult);
            totalRestQty = totalRestQty.subtract(planPreout);

            if (totalRestQty.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }

        }

        //最后剩余未数量作为缺货处理
        if (totalRestQty.compareTo(BigDecimal.ZERO) > 0) {
            newSplitSkuResult = creatNewSplitSkuResult(skuItem);
            setSplitSkuResultSingle(newSplitSkuResult, totalRestQty, StrategyConstants.OUT_DEFAULT_STORE_ID, null);
            splitSkuResultList.add(newSplitSkuResult);
        }
    }

    /**
     * @param splitSkuResult:
     * @param qty:
     * @param warehouseId:
     * @param currStorage:
     * @Description: 封装条码拆单结果
     * @Author: hwy
     * @Date: 2021/6/15 16:43
     * @return: com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuS2LResult
     **/
    private void setSplitSkuResultSingle(SgFindSourceStrategySkuS2LResult splitSkuResult,
                                         BigDecimal qty,
                                         Long warehouseId,
                                         SgFindSourceStrategyStoreItemS2LResult currStorage) {

        SgFindSourceStrategyStoreItemS2LResult itemResult = new SgFindSourceStrategyStoreItemS2LResult();
        itemResult.setStoreId(warehouseId);

        if (currStorage != null) {
            HashMap<Long, SortedMap<String, BigDecimal>> storeStorageMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            storeStorageMap.putAll(currStorage.getLogicStorageMap());
            itemResult.setLogicStorageMap(storeStorageMap);
            itemResult.setPriority(currStorage.getPriority());
            itemResult.setQty(currStorage.getQty());
            //分配后减少当前实体仓库存
            BigDecimal wareHouseQty = currStorage.getQty();
            currStorage.setQty(wareHouseQty.subtract(qty));
        }

        List<SgFindSourceStrategyStoreItemS2LResult> itemResults = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        itemResults.add(itemResult);
        splitSkuResult.setItemResultList(itemResults);

        FindSourceStrategyUtils.outputJsonLog("S->L二阶段寻源派单 拆单策略执行器-封装条码拆单结果 splitSkuResult:{}",
                splitSkuResult);

        return;
    }

}