package com.burgeon.r3.sg.sourcing.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutMapper;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutInfoResult;
import com.burgeon.r3.sg.sourcing.common.SourceOrderTypeEnum;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyC2SRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemC2S;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyC2SResult;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BeanCopierUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SgFindSourceStrategyC2SWarehouseService {


    @Autowired
    private SgFindSoureceC2SSendMQService mqService;

    @Autowired
    private SgBShareOutMapper sgBShareOutMapper;

    @Resource
    private ThreadPoolTaskExecutor asyncExecutorPool;

    /**
     * 寻仓单 配货单二阶段寻源
     *
     * @param request
     */
    public void find2Source(SgFindSourceStrategyC2SRequest request, ValueHolderV14<SgFindSourceStrategyC2SResult> valueHolderV14) {
        //需要判断请求明细中时效订单是否存在
        List<SkuItemC2S> skuItems = request.getSkuItems();
        List<SkuItemC2S> vipList = new ArrayList<>();
        List<SkuItemC2S> retailList = new ArrayList<>();
        skuItems.stream().forEach(skuItemC2S -> {
            if (ObjectUtils.isNotEmpty(skuItemC2S.getTimeOrderId())) {
                vipList.add(skuItemC2S);
            } else {
                retailList.add(skuItemC2S);
            }
        });
        List<Long> timeOrderIds = vipList.stream().map(skuItemC2S -> skuItemC2S.getTimeOrderId()).collect(Collectors.toList());
        List<SgBShareOutInfoResult> vipSgBShareOuts = null;
        if (CollectionUtils.isNotEmpty(timeOrderIds)) {
            vipSgBShareOuts = sgBShareOutMapper.findShareOutAndItem(timeOrderIds, SgConstantsIF.BILL_TYPE_VIPSHOP_TIME);
        }
        //明细中没有时效id
        List<SgBShareOutInfoResult> sgBShareOuts = sgBShareOutMapper.selectShareOutBySourceBillId(request.getSourceBillId(), request.getSourceBillType());
        log.debug("SgFindSourceStrategyC2SWarehouseService.find2Source query vip time order result [vipSgBShareOuts = {}] [sgBShareOuts = {}]", JSONObject.toJSONString(vipSgBShareOuts), JSON.toJSON(sgBShareOuts));
        List<SgBShareOutInfoResult> allShareOuts = new ArrayList<>();
        allShareOuts.addAll(vipSgBShareOuts);
        allShareOuts.addAll(sgBShareOuts);
        boolean match = false;
        Map<Long, BigDecimal> collect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(allShareOuts)) {
            //要判断是否有聚合
            //过滤出  按照合并的标记,  合并后的结果集取 共享日志明细表
            Map<Boolean, List<SgBShareOutInfoResult>> mergeMap = allShareOuts.stream().collect(Collectors.groupingBy(SgBShareOutInfoResult::getMergeMark));
            //未合并的结果集
            allShareOuts = mergeMap.get(Boolean.FALSE);
            if (CollectionUtils.isEmpty(allShareOuts)) {
                allShareOuts = new ArrayList<SgBShareOutInfoResult>();
            }
            //合并的结果集
            List<SgBShareOutInfoResult> shareOutInfoResults = mergeMap.get(Boolean.TRUE);
            if (CollectionUtils.isNotEmpty(shareOutInfoResults)) {
                List<Long> ids = shareOutInfoResults.stream().map(SgBShareOutInfoResult::getBillId).collect(Collectors.toList());
                List<SgBShareOutInfoResult> sgBShareOutLogs = sgBShareOutMapper.selectShareOutByLogIds(ids);
                if (CollectionUtils.isNotEmpty(sgBShareOutLogs)) {
                    allShareOuts.addAll(sgBShareOutLogs);
                }
            }
            //通过来源明细id进行聚合
            Map<Long, BigDecimal> sourceItemQtyS = allShareOuts.stream().filter(sgBShareOutInfoResult -> {
                if (sgBShareOutInfoResult.getQtyPreout() == null) {
                    sgBShareOutInfoResult.setQtyPreout(BigDecimal.ZERO);
                }
                return true;
            }).collect(Collectors.groupingBy(SgBShareOutInfoResult::getSourceItemId, Collectors.reducing(BigDecimal.ZERO, SgBShareOutInfoResult::getQtyPreout, BigDecimal::add)));
            collect.putAll(sourceItemQtyS);
            // 当寻仓单占用  小于 时效订单占用时, 才会使用该标志发送 异常mq到oms
            final AtomicBoolean sendFlag = new AtomicBoolean(false);
            match = request.getSkuItems().stream().allMatch(skuItemC2S -> {
                //聚合占用数量
                BigDecimal qtyCount = BigDecimal.ZERO;
                if (skuItemC2S.getTimeOrderItemId() != null) {
                    qtyCount = collect.get(skuItemC2S.getTimeOrderItemId());
                } else {
                    qtyCount = collect.get(skuItemC2S.getSourceItemId());
                }
                if (qtyCount == null || (skuItemC2S.getQty().compareTo(qtyCount) > 0)) {
                    return false;
                }
                //寻仓单的占用数量小于时效订单的占用
                if (skuItemC2S.getQty().compareTo(qtyCount) < 0 && !sendFlag.get()) {
                    sendFlag.set(true);
                    return false;
                }
                //说明相等
                return true;
            });
            //当寻仓单 比 时效订单小
            if (sendFlag.get()) {
                //发送异常mq 到 oms
                log.debug(" SgFindSourceStrategyC2SWarehouseService.find2Source[寻仓单或配货单占用数量小于时效订单占用]");
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(" SgFindSourceStrategyC2SWarehouseService.find2Source[寻仓单或配货单占用数量小于订单占用数量]] C->S寻源派单 寻源引擎");
                return;
            }
        }
        //若 时效订单明细 都相等, 直接进行二阶段寻源
        FindSourceStrategyUtils.outputLog(" SgFindSourceStrategyC2SWarehouseService find2Source CompletableFuture send flag = {}", match);
        if (match) {
            CompletableFuture.runAsync(() -> {
                log.debug("SgFindSourceStrategyC2SWarehouseService find2Source CompletableFuture runAsync request = {}", JSONObject.toJSONString(request));
                mqService.sendMqByWarehouse(request);
                /*});*/
            }, asyncExecutorPool);

            FindSourceStrategyUtils.outputLog(" SgFindSourceStrategyC2SWarehouseService find2Source CompletableFuture 异步发送二阶段");
            return;
        } else {
            log.debug("SgFindSourceStrategyC2SWarehouseService.find2Source[VIP] vip time order not exist request = {}", JSONObject.toJSONString(request));
            //List<SkuItemC2S> skuItems = request.getSkuItems();
            //配合单可能没有时效订单
            Map<Long, List<SgBShareOutInfoResult>> retailOrderMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            if (CollectionUtils.isNotEmpty(sgBShareOuts)) {
                retailOrderMap.putAll(sgBShareOuts.stream().collect(Collectors.groupingBy(SgBShareOutInfoResult::getSourceBIllId)));
            }
            //时效订单
            Map<Long, List<SgBShareOutInfoResult>> timeOrderMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            if (CollectionUtils.isNotEmpty(vipSgBShareOuts)) {
                timeOrderMap.putAll(vipSgBShareOuts.stream().collect(Collectors.groupingBy(SgBShareOutInfoResult::getSourceBIllId)));
            }
            List<SgFindSourceStrategyC2SRequest> vipRequests = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
            //时效订单
            if (CollectionUtils.isNotEmpty(vipList)) {
                vipList.stream().forEach(o -> {
                    BigDecimal qty = o.getQty();
                    Long timeOrderItemId = o.getTimeOrderItemId();
                    BigDecimal needRetryQty, sharePreOutQty = BigDecimal.ZERO;
                    if (MapUtils.isNotEmpty(collect) && collect.containsKey(timeOrderItemId)) {
                        sharePreOutQty = collect.get(timeOrderItemId);
                        if (qty.compareTo(sharePreOutQty) <= 0) {
                            return;
                        }
                    }
                    needRetryQty = qty.subtract(sharePreOutQty);
                    if (BigDecimal.ZERO.compareTo(needRetryQty) != 0) {
                        Long sourceBIllId = o.getTimeOrderId();
                        String sourceBIllNo = o.getTimeOrderNo();
                        Long sourceItemId = o.getTimeOrderItemId();
                        SgFindSourceStrategyC2SRequest strategyC2SRequest = new SgFindSourceStrategyC2SRequest();
                        strategyC2SRequest.setBillDate(request.getBillDate());
                        strategyC2SRequest.setShopId(request.getShopId());
                        strategyC2SRequest.setShopTitle(request.getShopTitle());
                        strategyC2SRequest.setTid(request.getTid());
                        strategyC2SRequest.setSplitType(request.getSplitType());
                        strategyC2SRequest.setSourceBillId(sourceBIllId);
                        strategyC2SRequest.setSourceBillNo(sourceBIllNo);
                        strategyC2SRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_VIPSHOP_TIME);
                        strategyC2SRequest.setInventedOccupy(request.getInventedOccupy());
                        strategyC2SRequest.setWarehouseEcode(request.getWarehouseEcode());
                        strategyC2SRequest.setExcludeWarehouseEcode(request.getExcludeWarehouseEcode());
                        //寻仓单 配货单 去占单, 不需要回执
                        strategyC2SRequest.setReceiptFlag(true);
                        // 库存重新发起寻源需要设置redisKey
                        strategyC2SRequest.setRetryRedisKey(StrategyConstants.VIP_RETRY_REDIS_KEY_PREFIX + request.getSourceBillNo());
                        List<SkuItemC2S> vipSkuItemC2SList = new ArrayList<>(1);
                        SkuItemC2S vipSkuItemC2S = new SkuItemC2S();
                        BeanCopierUtil.copy(o, vipSkuItemC2S);
                        vipSkuItemC2S.setQty(needRetryQty);
                        vipSkuItemC2S.setTimeOrderId(null);
                        vipSkuItemC2S.setTimeOrderItemId(null);
                        vipSkuItemC2S.setSourceItemId(sourceItemId);
                        vipSkuItemC2SList.add(vipSkuItemC2S);
                        strategyC2SRequest.setSkuItems(vipSkuItemC2SList);
                        vipRequests.add(strategyC2SRequest);
                    }
                });
            }

            List<SgFindSourceStrategyC2SRequest> retailRequests = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
            //配货单没有时效订单
            if (CollectionUtils.isNotEmpty(retailList)) {
                Long sourceBIllId = request.getSourceBillId();
                String sourceBIllNo = request.getSourceBillNo();
                SgFindSourceStrategyC2SRequest strategyC2SRequest = new SgFindSourceStrategyC2SRequest();
                strategyC2SRequest.setInventedOccupy(request.getInventedOccupy());
                strategyC2SRequest.setWarehouseEcode(request.getWarehouseEcode());
                strategyC2SRequest.setExcludeWarehouseEcode(request.getExcludeWarehouseEcode());
                strategyC2SRequest.setAreaId(request.getAreaId());
                strategyC2SRequest.setBillDate(request.getBillDate());
                strategyC2SRequest.setCityId(request.getCityId());
                strategyC2SRequest.setProvinceId(request.getProvinceId());
                strategyC2SRequest.setShopId(request.getShopId());
                strategyC2SRequest.setShopTitle(request.getShopTitle());
                strategyC2SRequest.setSourceBillId(sourceBIllId);
                strategyC2SRequest.setSourceBillNo(sourceBIllNo);
                strategyC2SRequest.setSourceBillType(request.getSourceBillType());
                strategyC2SRequest.setSplitType(request.getSplitType());
                strategyC2SRequest.setTid(request.getTid());
                //寻仓单 配货单 去占单, 不需要回执
                strategyC2SRequest.setReceiptFlag(true);
                // 库存重新发起寻源需要设置redisKey
                strategyC2SRequest.setRetryRedisKey(StrategyConstants.VIP_RETRY_REDIS_KEY_PREFIX + sourceBIllNo);
                List<SkuItemC2S> retailSkuItemC2SList = new ArrayList<>();
                strategyC2SRequest.setSkuItems(retailSkuItemC2SList);
                retailList.stream().forEach(o -> {
                    BigDecimal qty = o.getQty();
                    Long sourceItemId = o.getSourceItemId();
                    BigDecimal needRetryQty, sharePreOutQty = BigDecimal.ZERO;
                    if (MapUtils.isNotEmpty(collect) && collect.containsKey(sourceItemId)) {
                        sharePreOutQty = collect.get(sourceItemId);
                        if (qty.compareTo(sharePreOutQty) <= 0) {
                            return;
                        }
                    }
                    needRetryQty = qty.subtract(sharePreOutQty);
                    if (BigDecimal.ZERO.compareTo(needRetryQty) != 0) {

                        SkuItemC2S retailSkuItemC2S = new SkuItemC2S();
                        BeanCopierUtil.copy(o, retailSkuItemC2S);
                        retailSkuItemC2S.setQty(needRetryQty);
                        retailSkuItemC2S.setSourceItemId(sourceItemId);
                        retailSkuItemC2SList.add(retailSkuItemC2S);
                    }
                });

                retailRequests.add(strategyC2SRequest);
            }

            Map<String, List<SgFindSourceStrategyC2SRequest>> requestMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(vipRequests)) {

                requestMap.put(SourceOrderTypeEnum.VIP_TYPE.getCode(), vipRequests);
            }
            if (CollectionUtils.isNotEmpty(retailRequests)) {

                requestMap.put(SourceOrderTypeEnum.NORMAL_TYPE.getCode(), retailRequests);
            }

            if (MapUtils.isNotEmpty(requestMap)) {
                mqService.vipFindSourceRetry(requestMap, request);
            }

        }


    }
}
