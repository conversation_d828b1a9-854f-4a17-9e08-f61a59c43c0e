package com.burgeon.r3.sg.sourcing.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.request.SgStorageQuerySaRequest;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySaResult;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutMapper;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutInfoResult;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyC2SRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgOccupyPlanPriorityRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgOccupyPlanServiceRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgOccupyPlanSkuItemRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemC2S;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanItemResult;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanOutItemResult;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanServiceResult;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: hwy
 * @time: 2021/6/22 10:30
 */
@Component
@Slf4j
public class SgFindSourceOccupyPlanService {

    /**
     * @param request:
     * @Description: 获取派单计划
     * @Author: hwy
     * @Date: 2021/6/22 17:24
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanServiceResult>
     **/
    public ValueHolderV14<SgOccupyPlanServiceResult> getOccupyPlan(SgOccupyPlanServiceRequest request) {
        log.info("SgFindSourceOccupyPlanService.getOccupyPlan Start param:{}", JSONObject.toJSONString(request));
        ValueHolderV14<SgOccupyPlanServiceResult> valueHolderV14 = checkParam(request);
        try {
            //条码明细请求list
            List<SgOccupyPlanSkuItemRequest> skuItemRequests = request.getSkuItemRequests();
            //所有仓 包括条码指定仓
            List<Long> psCSkuIds = skuItemRequests.stream().map(SgOccupyPlanSkuItemRequest::getPsCSkuId).distinct().collect(Collectors.toList());

            //查询库存 条码 - 仓id - 对应库存
            Map<Long, Map<Long, BigDecimal>> storageMap = queryStorage(psCSkuIds, request.getSaStoreIdList(), valueHolderV14);

            // 分配计划结果
            SgOccupyPlanServiceResult sgOccupyPlanServiceResult = new SgOccupyPlanServiceResult();
            sgOccupyPlanServiceResult.setCpCShopId(request.getCpCShopId());
            sgOccupyPlanServiceResult.setSourceBillId(request.getSourceBillId());
            sgOccupyPlanServiceResult.setSourceBillType(request.getSourceBillType());
            //分配结果明细
            //缺货明细
            List<SgOccupyPlanOutItemResult> outItemResultList = new ArrayList<>();
            sgOccupyPlanServiceResult.setItemResultList(new ArrayList<>());
            sgOccupyPlanServiceResult.setOutItmeResultList(outItemResultList);
            //分配库存
            for (SgOccupyPlanSkuItemRequest skuItem : skuItemRequests) {
                Long key = skuItem.getPsCSkuId();
                //设置缺货
                if (!storageMap.containsKey(key)) {
                    SgOccupyPlanOutItemResult sgOccupyPlanOutItmeResult = new SgOccupyPlanOutItemResult();
                    sgOccupyPlanOutItmeResult.setOutQty(skuItem.getQtyChange());
                    sgOccupyPlanOutItmeResult.setPsCSkuId(skuItem.getPsCSkuId());
                    sgOccupyPlanOutItmeResult.setQtyChange(skuItem.getQtyChange());
                    sgOccupyPlanOutItmeResult.setSourceItemId(skuItem.getSourceItemId());
                    outItemResultList.add(sgOccupyPlanOutItmeResult);
                    continue;
                }
                Map<Long, BigDecimal> storeStorageMap = storageMap.get(key);
                // 生成明细占用库存计划
                skuItemPlan(storeStorageMap, sgOccupyPlanServiceResult, skuItem);
            }
            if (CollectionUtils.isNotEmpty(sgOccupyPlanServiceResult.getOutItmeResultList()) && CollectionUtils.isNotEmpty(sgOccupyPlanServiceResult.getItemResultList())) {
                sgOccupyPlanServiceResult.setOccupyPlanResult(SgConstants.PREOUT_RESULT_PART_SUCCESS);
            } else if (CollectionUtils.isNotEmpty(sgOccupyPlanServiceResult.getOutItmeResultList())) {
                sgOccupyPlanServiceResult.setOccupyPlanResult(SgConstants.PREOUT_RESULT_ALL_OUT);
            } else if (CollectionUtils.isNotEmpty(sgOccupyPlanServiceResult.getItemResultList())) {
                sgOccupyPlanServiceResult.setOccupyPlanResult(SgConstants.PREOUT_RESULT_All_SUCCESS);
            }
            valueHolderV14.setData(sgOccupyPlanServiceResult);
        } catch (Exception e) {
            log.error("SgFindSourceOccupyPlanService.getOccupyPlan. error:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("生成分配计划异常!");
        }
        return valueHolderV14;
    }

    /**
     * TODO 虚拟寻源逻辑是否保留
     * 就全匹配、指定仓，配销层优先级重排
     *
     * @param priorityRequests   sa优先级
     * @param storageMap         sa库存
     * @param strategyC2SRequest 寻源参数
     */
    private void allMatch(List<SgOccupyPlanPriorityRequest> priorityRequests, Map<Long, Map<Long, BigDecimal>> storageMap,
                          SgFindSourceStrategyC2SRequest strategyC2SRequest, List<SgOccupyPlanSkuItemRequest> skuItemRequests) {

        if (CollectionUtils.isEmpty(priorityRequests) && CollectionUtils.isEmpty(skuItemRequests)) {
            return;
        }

        try {
            //如果是虚拟寻源，考虑已占用配销仓库存、就全
            if (Boolean.TRUE.equals(strategyC2SRequest.getInventedOccupy())) {
                if (log.isDebugEnabled()) {
                    log.debug(" 一阶段虚拟寻源，生成占用计划");
                }
                //查询已占用共享占用单（唯品会有时效单先占一部分的情况）
                List<Long> timeOrderIds = strategyC2SRequest.getSkuItems().stream()
                        .filter(i -> !org.springframework.util.ObjectUtils.isEmpty(i.getTimeOrderId()))
                        .map(SkuItemC2S::getTimeOrderId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(timeOrderIds)) {
                    SgBShareOutMapper shareOutMapper = ApplicationContextHandle.getBean(SgBShareOutMapper.class);
                    List<SgBShareOutInfoResult> vipSgBShareOuts = shareOutMapper.findShareOutAndItem(timeOrderIds, SgConstantsIF.BILL_TYPE_VIPSHOP_TIME);
                    //时效单占用
                    if (CollectionUtils.isNotEmpty(vipSgBShareOuts)) {
                        //库存加上时效单的占用
                        vipSgBShareOuts.forEach(o -> {
                            Map<Long, BigDecimal> saStorage = storageMap.get(o.getPsCSkuId().toString());
                            if (ObjectUtils.isNotEmpty(saStorage)) {
                                saStorage.put(o.getSgCSaStoreId(), o.getQtyPreout()
                                        .add(Optional.ofNullable(saStorage.get(o.getSgCSaStoreId())).orElse(BigDecimal.ZERO)));
                            }
                        });
                    }
                }

                //计算每个配销仓可以满足的明细数量，能满足的明细越多优先级越高
                //已计算的配销仓库存
                Map<String, Map<Long, BigDecimal>> computedStorageMap = new HashMap<>();
                //配销仓满足率map
                Map<Long, Set<Long>> storeIdSourceItemMap = new HashMap<>();
                strategyC2SRequest.getSkuItems().forEach(skuItemC2S -> {
                    String psCSkuId = skuItemC2S.getPsCSkuId().toString();
                    //原始库存
                    Map<Long, BigDecimal> saStorage = storageMap.get(psCSkuId);
                    if (ObjectUtils.isNotEmpty(saStorage)) {
                        Map<Long, BigDecimal> computedMap = computedStorageMap.computeIfAbsent(psCSkuId, l -> new HashMap<>());
                        saStorage.forEach((k, v) -> {
                            //其他明细已计算库存
                            BigDecimal occupiedQty = Optional.ofNullable(computedMap.get(k)).orElse(BigDecimal.ZERO);
                            //剩余库存满足
                            if (v.subtract(occupiedQty).compareTo(skuItemC2S.getQty()) >= 0) {
                                Set<Long> itemSet = storeIdSourceItemMap.computeIfAbsent(k, l -> new HashSet<>());
                                itemSet.add(skuItemC2S.getSourceItemId());
                                computedMap.put(k, occupiedQty.add(skuItemC2S.getQty()));
                            }
                        });
                    }
                });

                if (log.isDebugEnabled()) {
                    log.debug(" 一阶段虚拟寻源，明细满足情况：{}", JSON.toJSONString(storeIdSourceItemMap));
                }

                //根据满足明细数量降序
                List<Map.Entry<Long, Set<Long>>> sortedSa = storeIdSourceItemMap.entrySet().stream()
                        .sorted((x, y) -> y.getValue().size() - x.getValue().size()).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(priorityRequests)) {
                    Map<Long, SgOccupyPlanPriorityRequest> planPriorityRequestMap = priorityRequests.stream()
                            .collect(Collectors.toMap(SgOccupyPlanPriorityRequest::getStoreId, Function.identity()));
                    //设置普通仓优先级
                    setPriority(sortedSa, storeIdSourceItemMap, planPriorityRequestMap, priorityRequests, strategyC2SRequest);
                }

                //设置特殊条码配置优先级
                skuItemRequests.forEach(item -> {
                    List<SgOccupyPlanPriorityRequest> priorityRequestList = item.getStorePriorities();
                    if (CollectionUtils.isNotEmpty(priorityRequestList)) {
                        Map<Long, SgOccupyPlanPriorityRequest> planPriorityRequestMap = priorityRequestList.stream()
                                .collect(Collectors.toMap(SgOccupyPlanPriorityRequest::getStoreId, Function.identity()));
                        setPriority(sortedSa, storeIdSourceItemMap, planPriorityRequestMap, priorityRequestList, strategyC2SRequest);
                    }
                });

                if (log.isDebugEnabled()) {
                    log.debug(" 一阶段虚拟寻源，配销仓优先级：{},明细配销仓优先级：{}", JSON.toJSONString(priorityRequests),
                            JSON.toJSONString(skuItemRequests));
                }
            }

//            //如果是寻源优先仓库占用、提高优先实体仓对应逻辑仓所属聚合仓下的配销仓优先级
//            PropertiesConf propertiesConf = ApplicationContextHandle.getBean(PropertiesConf.class);
////            Boolean pointStoreOn = propertiesConf.getPropertyBoolean("sg.findSource.pointStore.on");
//            String warehouse = strategyC2SRequest.getWarehouseEcode();
////            if (Boolean.TRUE.equals(pointStoreOn) && ObjectUtils.isNotEmpty(warehouse)) {
//                //查询实体仓对应逻辑仓
//                CpPhyWarehouseService warehouseService = ApplicationContextHandle.getBean(CpPhyWarehouseService.class);
//                List<SgCpCPhyWarehouse> cPhyWarehouseList = warehouseService.getPhyWarehouseByEcode(Arrays
//                        .asList(warehouse.split(SgConstants.SG_CONNECTOR_MARKS_5)));
//                if (log.isDebugEnabled()) {
//                    log.debug(" 一阶段指定仓占，指定实体仓：{}", JSON.toJSONString(cPhyWarehouseList));
//                }
//                if (CollectionUtils.isEmpty(cPhyWarehouseList)) {
//                    return;
//                }
//                Set<Long> shareIds = new HashSet<>();
//                cPhyWarehouseList.forEach(w -> {
//                    Map<Long, List<CpCStore>> longListMap = CommonCacheValUtils.getStoreInfoByPhyId(w.getId());
//                    if (ObjectUtils.isNotEmpty(longListMap) && ObjectUtils.isNotEmpty(longListMap.get(w.getId()))) {
//                        longListMap.get(w.getId()).forEach(store -> shareIds.add(store.getSgCShareStoreId()));
//                    }
//                });
//                if (log.isDebugEnabled()) {
//                    log.debug(" 一阶段指定仓占，聚合仓id：{}", JSON.toJSONString(shareIds));
//                }
//                if (CollectionUtils.isEmpty(shareIds)) {
//                    return;
//                }
//                //查询聚合仓对应配销仓
//                SgCSaStoreMapper mapper = ApplicationContextHandle.getBean(SgCSaStoreMapper.class);
//                List<SgCSaStore> saStoreList = mapper.selectList(new LambdaQueryWrapper<SgCSaStore>()
//                        .in(SgCSaStore::getSgCShareStoreId, shareIds).eq(SgCSaStore::getIsactive, SgConstants.IS_ACTIVE_Y));
//                if (log.isDebugEnabled()) {
//                    log.debug(" 一阶段指定仓占，配销仓：{}", JSON.toJSONString(saStoreList));
//                }
//                if (CollectionUtils.isEmpty(saStoreList)) {
//                    return;
//                }
//
//                List<Long> saIds = saStoreList.stream().map(SgCSaStore::getId).collect(Collectors.toList());
//                setSaPriority(priorityRequests, saIds);
//
//                //设置特殊条码配置优先级
//                skuItemRequests.forEach(item -> {
//                    List<SgOccupyPlanPriorityRequest> priorityRequestList = item.getStorePriorities();
//                    if (CollectionUtils.isNotEmpty(priorityRequestList)) {
//                        setSaPriority(priorityRequestList, saIds);
//                    }
//                });
//
//
//                if (log.isDebugEnabled()) {
//                    log.debug(" 一阶段指定仓占，普通舱优先级：{}，指定仓优先级:{}",
//                            JSON.toJSONString(priorityRequests), JSON.toJSONString(skuItemRequests));
//                }
//            }

        } catch (Exception e) {
            log.error(" 就全匹配、指定仓，配销层优先级重排异常：{}", Throwables.getStackTraceAsString(e));
        }

    }

    /**
     * 一阶段指定仓，配销仓优先级
     *
     * @param priorityRequests 优先级
     * @param saIds            指定配销仓
     */
    private void setSaPriority(List<SgOccupyPlanPriorityRequest> priorityRequests, List<Long> saIds) {
        Optional<SgOccupyPlanPriorityRequest> maxPriority = priorityRequests.stream().max(Comparator.comparing(SgOccupyPlanPriorityRequest::getPriority));
        //提高聚合仓下配销仓优先级(加优先级最高的配销仓的优先级，保证没加的在后面)
        maxPriority.ifPresent(sgOccupyPlanPriorityRequest -> {
            //先把优先级拿出来，直接用get拿会因为引用类型值的变化导致后面加的优先级不正确
            Integer maxPriorityInt = sgOccupyPlanPriorityRequest.getPriority();
            priorityRequests.forEach(p -> {
                if (saIds.contains(p.getStoreId())) {
                    p.setPriority(p.getPriority() + maxPriorityInt);
                }
            });
        });
    }

    /**
     * 重新设置仓库优先级
     *
     * @param sortedSa               配销仓满足率降序集合
     * @param storeIdSourceItemMap   满足率map
     * @param planPriorityRequestMap 配销仓原始优先级map
     * @param priorityRequests       配销仓原始优先级list
     * @param strategyC2SRequest     寻源参数
     */
    private void setPriority(List<Map.Entry<Long, Set<Long>>> sortedSa, Map<Long, Set<Long>> storeIdSourceItemMap,
                             Map<Long, SgOccupyPlanPriorityRequest> planPriorityRequestMap,
                             List<SgOccupyPlanPriorityRequest> priorityRequests, SgFindSourceStrategyC2SRequest strategyC2SRequest) {
        AtomicInteger offSet = new AtomicInteger(10);
        //根据满足率重新赋值优先级
        //全满足的基于同步比例策略优先级加10000
        //部分满足的满足的明细越多，优先级越高
        sortedSa.forEach(sa -> {
            SgOccupyPlanPriorityRequest priorityRequest = planPriorityRequestMap.get(sa.getKey());
            if (ObjectUtils.isNotEmpty(priorityRequest)) {
                if (sa.getValue().size() >= strategyC2SRequest.getSkuItems().size()) {
                    priorityRequest.setPriority(priorityRequest.getPriority() + 10000);
                } else {
                    priorityRequest.setPriority(10000 - offSet.get());
                    offSet.set(offSet.get() + 10);
                }
            }
        });

        //没有明细满足的配销仓优先级置为0
        priorityRequests.forEach(p -> {
            if (!storeIdSourceItemMap.keySet().contains(p.getStoreId())) {
                p.setPriority(0);
            }
        });
    }

    /**
     * @param storageMap:                库存
     * @param sgOccupyPlanServiceResult: 分配计划结果
     * @param skuItem:                   当前分配明细
     * @Description: 特殊条码比例库存分配计划
     * @Author: hwy
     * @Date: 2021/6/22 15:09
     * @return: void
     **/
    private void skuItemPlan(Map<Long, BigDecimal> storageMap,
                             SgOccupyPlanServiceResult sgOccupyPlanServiceResult,
                             SgOccupyPlanSkuItemRequest skuItem) {
        FindSourceStrategyUtils.outputLog(" SgFindSourceOccupyPlanService skuItemPlan in param 配销仓排序之前  skuItem = {}", JSONObject.toJSONString(skuItem));
        //分配计划
        List<SgOccupyPlanItemResult> itemResultList = sgOccupyPlanServiceResult.getItemResultList();
        // 来源单据明细id 唯一
        Map<Long, SgOccupyPlanItemResult> itemResultMap = itemResultList.stream().collect(Collectors.toMap(SgOccupyPlanItemResult::getSourceItemId, Function.identity()));
        List<SgOccupyPlanOutItemResult> outItemResultList = sgOccupyPlanServiceResult.getOutItmeResultList();
        //当前条码需求量
        BigDecimal qtyChange = skuItem.getQtyChange();
        Long sourceItemId = skuItem.getSourceItemId();
        Collections.sort(skuItem.getStorePriorities() );
        for (SgOccupyPlanPriorityRequest specialPriority : skuItem.getStorePriorities()) {
            Long storeId = specialPriority.getStoreId();
            BigDecimal qtyAvailable = storageMap.get(storeId);
            // 当前占用库存
            BigDecimal currQty = BigDecimal.ZERO;
            if (qtyAvailable.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            //库存不够
            if (qtyChange.compareTo(qtyAvailable) > 0) {
                continue;
            }
            //扣库存
            storageMap.put(storeId, qtyAvailable.subtract(qtyChange));
            currQty = qtyChange;
            qtyChange = BigDecimal.ZERO;
            if (currQty.compareTo(BigDecimal.ZERO) != 0) {
                // 设置返回结果
                if (itemResultMap.containsKey(sourceItemId)) {
                    SgOccupyPlanItemResult itemPlan = itemResultMap.get(sourceItemId);
                    Map<Long, BigDecimal> storePlan = itemPlan.getStorePlan();
                    storePlan.put(storeId, currQty);
                    BigDecimal qty = itemPlan.getQty();
                    itemPlan.setQty(qty.add(currQty));
                } else {
                    SgOccupyPlanItemResult itemPlan = new SgOccupyPlanItemResult();
                    itemPlan.setQty(currQty);
                    itemPlan.setSourceItemId(skuItem.getSourceItemId());
                    itemPlan.setPsCSkuId(skuItem.getPsCSkuId());
                    Map<Long, BigDecimal> storePlan = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
                    storePlan.put(storeId, currQty);
                    itemPlan.setStorePlan(storePlan);
                    itemResultList.add(itemPlan);
                    itemResultMap.put(sourceItemId, itemPlan);
                }
            }
            if (qtyChange.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
        }
        //存在缺货 记录
        if (qtyChange.compareTo(BigDecimal.ZERO) > 0) {
            SgOccupyPlanOutItemResult sgOccupyPlanOutItemResult = new SgOccupyPlanOutItemResult();
            sgOccupyPlanOutItemResult.setOutQty(qtyChange);
            sgOccupyPlanOutItemResult.setPsCSkuId(skuItem.getPsCSkuId());
            sgOccupyPlanOutItemResult.setQtyChange(skuItem.getQtyChange());
            sgOccupyPlanOutItemResult.setSourceItemId(skuItem.getSourceItemId());
            outItemResultList.add(sgOccupyPlanOutItemResult);
        }
    }

    /**
     * @param psCSkuIds:      条码
     * @param storeIds:       仓库
     * @param valueHolderV14:
     * @Description: 查询库存
     * @Author: hwy
     * @Date: 2021/6/22 15:27
     * @return: java.util.Map<java.lang.Long, java.util.Map < java.lang.String, java.math.BigDecimal>>
     **/
    private Map<Long, Map<Long, BigDecimal>> queryStorage(List<Long> psCSkuIds, List<Long> storeIds,
                                                          ValueHolderV14 valueHolderV14) {
//        key : psCSkuId 条码id  <聚合仓id, 可用库存>
        Map<Long, Map<Long, BigDecimal>> skuStorageMap = new HashMap<>();
        SgStorageQueryService storageQueryService = ApplicationContextHandle.getBean(SgStorageQueryService.class);
        SgStorageQuerySaRequest sgStorageQuerySaRequest = new SgStorageQuerySaRequest();
        sgStorageQuerySaRequest.setSkuIds(psCSkuIds);
        sgStorageQuerySaRequest.setSgCSaStoreIds(storeIds);
        if (log.isDebugEnabled()) {
            log.debug("C->S生成占用计划 查询配销仓库存 入参:{}", JSONObject.toJSONString(sgStorageQuerySaRequest));
        } else {
            log.info("C->S生成占用计划 查询配销仓库存 入参:{}", JSONObject.toJSONString(sgStorageQuerySaRequest));
        }
        ValueHolderV14<List<SgStorageRedisQuerySaResult>> saStorageResult = storageQueryService.querySaStorageWithRedis(sgStorageQuerySaRequest, R3SystemUserResource.getSystemRootUser());
        if (!saStorageResult.isOK()) {
            log.error("C->S生成占用计划失败 查询配销仓库存失败", saStorageResult.getMessage());
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("C->S生成占用计划失败 查询配销仓库存失败");
            return skuStorageMap;
        }
        List<SgStorageRedisQuerySaResult> storageList = saStorageResult.getData();
        if (CollectionUtils.isEmpty(storageList)) {
            if (log.isDebugEnabled()) {
                log.debug("查询配销仓库存为空 ");
            } else {
                log.info("查询配销仓库存为空 ");
            }
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage("C->S生成占用计划失败 查询配销仓库存为空");
            return skuStorageMap;
        }
        if (log.isDebugEnabled()) {
            log.debug("C->S生成占用计划 查询配销仓库存结果:{}", JSONObject.toJSONString(storageList));
        } else {
            log.info("C->S生成占用计划 查询配销仓库存结果:{}", JSONObject.toJSONString(storageList));
        }
        storageList.forEach(o -> {
            Map<Long, BigDecimal> storeStorageMap;
            Long psCSkuId = o.getPsCSkuId();
            Long sgCSaStoreId = o.getSgCSaStoreId();
            BigDecimal qtyAvailable = o.getQtyAvailable();
            if (skuStorageMap.containsKey(psCSkuId)) {
                storeStorageMap = skuStorageMap.get(psCSkuId);
            } else {
                storeStorageMap = new HashMap<>();
                skuStorageMap.put(psCSkuId, storeStorageMap);
            }
            storeStorageMap.put(sgCSaStoreId, qtyAvailable);
        });
        if (log.isDebugEnabled()) {
            log.debug("C->S生成占用计划 查询库存结果:{}", JSONObject.toJSONString(skuStorageMap));
        } else {
            log.info("C->S生成占用计划 查询库存结果:{}", JSONObject.toJSONString(skuStorageMap));
        }
        return skuStorageMap;
    }

    /**
     * @param request:
     * @Description: 参数检查
     * @Author: hwy
     * @Date: 2021/6/22 15:28
     * @return: java.lang.Boolean
     **/
    private ValueHolderV14<SgOccupyPlanServiceResult> checkParam(SgOccupyPlanServiceRequest request) {
        if (request == null) {
            if (log.isDebugEnabled()) {
                log.debug("SgFindSourceOccupyPlanService.checkParam 入参为空");
            } else {
                log.info("SgFindSourceOccupyPlanService.checkParam 入参为空");
            }
            return new ValueHolderV14<>(ResultCode.FAIL, "入参为空");
        }
        if (CollectionUtils.isEmpty(request.getSkuItemRequests())) {
            if (log.isDebugEnabled()) {
                log.debug("SgFindSourceOccupyPlanService.checkParam 条码明细为空");
            } else {
                log.info("SgFindSourceOccupyPlanService.checkParam 条码明细为空");
            }
            return new ValueHolderV14<>(ResultCode.FAIL, "条码明细为空");
        }
//        List<SgOccupyPlanPriorityRequest> priorityRequests = request.getPriorityRequests();
//        if (CollectionUtils.isEmpty(priorityRequests)) {
//            if (log.isDebugEnabled()) {
//                log.debug("SgFindSourceOccupyPlanService.checkParam 仓库优先级队列为空");
//            } else {
//                log.info("SgFindSourceOccupyPlanService.checkParam 仓库优先级队列为空");
//            }
//            return new ValueHolderV14<>(ResultCode.FAIL, "仓库优先级队列为空");
//        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
    }

}