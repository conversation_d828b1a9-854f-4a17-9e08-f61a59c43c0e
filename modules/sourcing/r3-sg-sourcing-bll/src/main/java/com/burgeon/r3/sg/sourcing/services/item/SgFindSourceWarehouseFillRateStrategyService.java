package com.burgeon.r3.sg.sourcing.services.item;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemS2L;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.request.score.SgWarehouseScoreModel;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 仓库满足率策略执行器
 * @author: lsj
 * @time: 2021/6/15
 */
@Slf4j
@Component
public class SgFindSourceWarehouseFillRateStrategyService extends StrategyHandle {

    /**
     * 策略实现方法
     */
    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {

        log.info(LogUtil.format("SgFindSourceWarehouseFillRateStrategyService.handleRequest S->L二阶段寻源派单 仓库满足率策略执行器 param:{}",
                SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L),
                request.getTraceId(), JSONObject.toJSONString(request));

        SgFindSourceStrategyS2LRequest s2lRequest = (SgFindSourceStrategyS2LRequest) request;
        ValueHolderV14<StrategyBaseResult> result = doHandle(s2lRequest);
        try {
            s2lRequest.setStrategyBaseResult(result.getData());
        } catch (Exception e) {
            log.error(LogUtil.format("SgFindSourceFilterStrategyService.handleRequest S->L二阶段寻源派单 仓库满足率策略执行器 exception:{}",
                    SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L_EXCEPTION),
                    request.getTraceId(), Throwables.getStackTraceAsString(e));
            result.setCode(ResultCode.FAIL);
            result.setMessage("S->L 二阶段寻源 仓库满足率策略 执行异常");
        }
        return doNext(s2lRequest, result);
    }

    /**
     * 执行仓库评分策略
     */
    private ValueHolderV14<StrategyBaseResult> doHandle(SgFindSourceStrategyS2LRequest request) {

        StrategyBaseResult strategyBaseResult = request.getStrategyBaseResult();

        if (!(strategyBaseResult instanceof SgFindSourceStrategyS2LResult)) {
            log.error("SgFindSourceWarehouseFillRateStrategyService.doHandle. error:执行仓库满足率策略时，请求参数的结果对象类型错误！");
            return new ValueHolderV14<>(ResultCode.FAIL, "执行仓库满足率策略时，请求参数的结果对象类型错误！");
        }

        SgFindSourceStrategyS2LResult result = (SgFindSourceStrategyS2LResult) strategyBaseResult;

        if (result == null) {
            log.error("S->L二阶段寻源派单 仓库满足率策略获取上游策略执行结果:result_is_null");
            return new ValueHolderV14<>(ResultCode.FAIL, "仓库满足率策略获取上游策略执行结果:不存在可使用的仓库 策略执行结束!");
        }
        result.setDoNextStrategy(Boolean.TRUE);
        List<SgFindSourceStrategySkuS2LResult> skuResultList = result.getSkuResultList();

        if (skuResultList == null) {
            log.error("S->L二阶段寻源派单 仓库满足率策略获取上游策略执行结果:skuResultList_is_null");
            return new ValueHolderV14<>(ResultCode.FAIL, "仓库满足率策略获取上游策略执行结果:不存在可使用的仓库 策略执行结束!");
        }

        //将skuResultList转换成map，key:shareItemId  value:ItemResultList
        skuResultList = skuResultList.stream().filter(x -> x.getShareItemId() > 0).collect(Collectors.toList());
        Map<Long, List<SgFindSourceStrategyStoreItemS2LResult>> skuResultMap = skuResultList.stream().collect(
                Collectors.toMap(SgFindSourceStrategySkuS2LResult::getShareItemId,
                        SgFindSourceStrategySkuS2LResult::getItemResultList));

        //用map存分数，key:warehouseId, value:score/storeId
        Map<Long, SgWarehouseScoreModel> scoreMap = Collections.synchronizedMap(new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY));
        List<SgFindSourceStrategyStoreItemS2LResult> itemS2LResultList;
        SgWarehouseScoreModel physicsScoreModel;
        Long warehouseId = null;
        BigDecimal score = null;
        boolean isSame = Boolean.FALSE;

        //计算仓库得分
        for (SkuItemS2L skuItem : request.getSkuItems()) {

            if (StrategyConstants.OUT_DEFAULT_STORE_ID.equals(skuItem.getShareItemId())) {
                continue;
            }

            itemS2LResultList = skuResultMap.get(skuItem.getShareItemId());

            if (CollectionUtils.isEmpty(itemS2LResultList)) {
                continue;
            }

            for (SgFindSourceStrategyStoreItemS2LResult item : itemS2LResultList) {

                warehouseId = item.getStoreId();

                if (item.getQty() != null && skuItem.getQtyPreOut() != null &&
                        skuItem.getQtyPreOut().compareTo(BigDecimal.ZERO) != 0) {
                    score = item.getQty().divide(
                            skuItem.getQtyPreOut(), 4, BigDecimal.ROUND_HALF_UP).min(BigDecimal.ONE);
                } else {
                    score = BigDecimal.ZERO;
                }

                physicsScoreModel = scoreMap.get(warehouseId);

                if (physicsScoreModel == null) {
                    physicsScoreModel = new SgWarehouseScoreModel();
                    physicsScoreModel.setScore(score);
                    physicsScoreModel.setOldPriority(item.getPriority());
                    physicsScoreModel.setWarehouseId(warehouseId);
                    scoreMap.put(warehouseId, physicsScoreModel);
                } else {
                    physicsScoreModel.setScore(score.add(physicsScoreModel.getScore()));
                }
            }
        }

        //评分结果
        List<SgWarehouseScoreModel> warehouseScoreModelList = new ArrayList<>(scoreMap.values());

        //按照原优先级、分数升序排序
        Collections.sort(warehouseScoreModelList);

        FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 仓库满足率策略获取实体仓分数升序结果. warehouseScoreModelList:{}",
                warehouseScoreModelList);

        // 记录优先级
        int temp = 10;
        for (int i = 0; i < warehouseScoreModelList.size(); i++) {
            if (i == 0) {
                warehouseScoreModelList.get(i).setPriority(temp);
                continue;
            }
            // 判断原优先级、当前分数与上一个分数结果，如果相同，存当前temp，如果不同，则 + 10
            isSame = warehouseScoreModelList.get(i).getScore().compareTo(warehouseScoreModelList.get(i - 1).getScore()) == 0
                    && warehouseScoreModelList.get(i).getOldPriority().compareTo(warehouseScoreModelList.get(i - 1).getOldPriority()) == 0;
            if (!isSame) {
                temp += 10;
            }
            warehouseScoreModelList.get(i).setPriority(temp);
        }

        Map<Long, Integer> priorityMap = warehouseScoreModelList.stream().collect(
                Collectors.toMap(SgWarehouseScoreModel::getWarehouseId, SgWarehouseScoreModel::getPriority));

        // 赋值优先级
        skuResultList.forEach(skuResult ->
                skuResult.getItemResultList().forEach(item ->
                        item.setPriority(priorityMap.get(item.getStoreId()))));

        result.setSkuResultList(skuResultList);

        //清理Map
        warehouseScoreModelList.clear();
        skuResultMap.clear();
        priorityMap.clear();
        scoreMap.clear();

        ValueHolderV14<StrategyBaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "执行仓库满足率策略成功");
        v14.setData(result);

        FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 仓库满足率策略执行结果. result:{}",
                JSONObject.toJSONString(result));

        return v14;
    }
}