package com.burgeon.r3.sg.sourcing.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.share.model.request.out.*;
import com.burgeon.r3.sg.share.services.out.SgBShareOutReleaseService;
import com.burgeon.r3.sg.share.services.out.SgBShareOutVoidService;
import com.burgeon.r3.sg.sourcing.common.SourceTypeEnum;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.pojo.ShareReleaseDo;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemS2L;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanItemResult;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanServiceResult;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutBillVoidRequest;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutBillVoidResult;
import com.burgeon.r3.sg.store.services.out.SgBStoOutVoidService;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: hwy
 * @time: 2021/7/9 11:36
 */
@Component
@Slf4j
public class SgFindSourceRollBackService {

    public ValueHolderV14 shareOutRollBackStorage(String sourceBIllNo, Long sourceBillId, Integer sourceBillType) {
        ValueHolderV14<SgBStoOutBillVoidResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        try {
            SgBShareOutBillVoidRequest request = new SgBShareOutBillVoidRequest();
            List<SgBShareOutItemVoidRequest> shareOutItemVoidRequests = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
            SgBShareOutVoidRequest sgBShareOutVoidRequest = new SgBShareOutVoidRequest();
            sgBShareOutVoidRequest.setSourceBillId(sourceBillId);
            sgBShareOutVoidRequest.setSourceBillNo(sourceBIllNo);
            sgBShareOutVoidRequest.setSourceBillType(sourceBillType);
            request.setShareOutVoidRequest(sgBShareOutVoidRequest);
            request.setShareOutItemVoidRequests(shareOutItemVoidRequests);
            request.setLoginUser(R3SystemUserResource.getSystemRootUser());
            SgBShareOutVoidService service = ApplicationContextHandle.getBean(SgBShareOutVoidService.class);
            valueHolderV14 = service.voidShareOut(request);
            if (!valueHolderV14.isOK()) {
                log.error("寻源派单 来源单据:{} 回滚共享占用单失败:{}", sourceBillId, valueHolderV14.getMessage());
            }
        } catch (Exception e) {
            log.error("寻源派单 来源单据:{} 回滚共享占用单失败:{}", sourceBillId, Throwables.getStackTraceAsString(e));
        }
        return valueHolderV14;
    }


    public ValueHolderV14<List<String>> shareOutReleaseStorage(SgFindSourceStrategyS2LRequest request, SgOccupyPlanServiceResult occupyPlan) {
        if (log.isDebugEnabled()) {
            log.debug("SgFindSourceRollBackService.shareOutReleaseStorage 二阶段寻源派单 S->L 来源单据:{} 对比逻辑占用单与共享占用单差异 request:{} occupyPlan:{}", request.getSourceBillId(), JSONObject.toJSONString(request), JSONObject.toJSONString(occupyPlan));
        } else {
            log.info("SgFindSourceRollBackService.shareOutReleaseStorage 二阶段寻源派单 S->L 来源单据:{} 对比逻辑占用单与共享占用单差异 request:{} occupyPlan:{}", request.getSourceBillId(), JSONObject.toJSONString(request), JSONObject.toJSONString(occupyPlan));
        }
        ValueHolderV14<List<String>> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        try {
            // 逻辑占用单 占用结果
            List<SkuItemS2L> skuItems = request.getSkuItems();
            // 共享占用单占用结果
            Map<Long, List<SkuItemS2L>> shareOccupyItemMap = skuItems.stream().filter(o -> !StrategyConstants.OUT_DEFAULT_STORE_ID.equals(o.getShareStoreId())).collect(Collectors.groupingBy(SkuItemS2L::getSourceItemId));
            if (MapUtils.isEmpty(shareOccupyItemMap) || occupyPlan == null || CollectionUtils.isEmpty(occupyPlan.getItemResultList())) {
                if (log.isDebugEnabled()) {
                    log.debug("二阶段寻源派单 S->L 来源单据:{} 对比逻辑占用单与共享占用单差异 共享占用单或逻辑占用单为空无需对比");
                } else {
                    log.info("二阶段寻源派单 S->L 来源单据:{} 对比逻辑占用单与共享占用单差异 共享占用单或逻辑占用单为空无需对比");
                }
                valueHolderV14.setMessage("共享占用单或逻辑占用单为占用库存无需对比");
                return valueHolderV14;
            }
            List<SgOccupyPlanItemResult> itemResultList = occupyPlan.getItemResultList();
            HashMap<Long, SgOccupyPlanItemResult> itemResultMap = new HashMap<>();
            List<Long> allLogicStoreIds = new ArrayList<>();
            itemResultList.stream().forEach(o -> {
                Map<Long, BigDecimal> storePlan = o.getStorePlan();
                Set<Long> keySet = storePlan.keySet();
                keySet.stream().forEach(i -> {
                    if (!StrategyConstants.OUT_DEFAULT_STORE_ID.equals(i) && !allLogicStoreIds.contains(i)) {
                        allLogicStoreIds.add(i);
                    }
                });
                if (!itemResultMap.containsKey(o.getSourceItemId())) {
                    itemResultMap.put(o.getSourceItemId(), o);
                }
            });

            // 全部缺货 无需更新
            if (CollectionUtils.isEmpty(allLogicStoreIds)) {
                if (log.isDebugEnabled()) {
                    log.debug("二阶段寻源派单 S->L 来源单据:{} 对比逻辑占用单与共享占用单差异 订单全部缺货无需对比共享占用单与逻辑占用单差异");
                } else {
                    log.info("二阶段寻源派单 S->L 来源单据:{} 对比逻辑占用单与共享占用单差异 订单全部缺货无需对比共享占用单与逻辑占用单差异");
                }
                valueHolderV14.setMessage("订单全部缺货无需对比共享占用单与逻辑占用单差异");
                return valueHolderV14;
            }
            CpCStoreMapper storeMapper = ApplicationContextHandle.getBean(CpCStoreMapper.class);

            List<SgCpCStore> sgCpCStores = storeMapper.selectList(new QueryWrapper<SgCpCStore>().lambda()
                    .select(SgCpCStore::getId, SgCpCStore::getSgCShareStoreId)
                    .in(SgCpCStore::getId, allLogicStoreIds)
                    .eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (CollectionUtils.isEmpty(sgCpCStores)) {
                if (log.isDebugEnabled()) {
                    log.error("二阶段寻源派单 S->L 来源单据:{} 逻辑占用后更新共享占用 根据逻辑仓id查询 逻辑仓信息为空 终止更新");
                }
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("根据逻辑仓id查询 逻辑仓信息为空 终止更新");
                return valueHolderV14;
            }
            Map<Long, List<SgCpCStore>> storeInfoMap = sgCpCStores.stream().collect(Collectors.groupingBy(SgCpCStore::getSgCShareStoreId));
            // 待回滚的共享占用单 key shareStoreId  key:sourceItemId,qty
            Map<Long, Map<Long, BigDecimal>> shareReleaseMap = new HashMap<>();
            Set<Map.Entry<Long, List<SkuItemS2L>>> entries = shareOccupyItemMap.entrySet();
            for (Map.Entry<Long, List<SkuItemS2L>> entry : entries) {
                Long sourceItemId = entry.getKey();
                List<SkuItemS2L> skuItemS2LList = entry.getValue();
                BigDecimal qtyShareOccupy = skuItemS2LList.stream().map(SkuItemS2L::getQtyPreOut).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                SgOccupyPlanItemResult logicOccupyPlan = itemResultMap.get(sourceItemId);
                // 明细全部回滚
                if (logicOccupyPlan == null || MapUtils.isEmpty(logicOccupyPlan.getStorePlan())) {
                    for (SkuItemS2L skuItemS2L : skuItemS2LList) {
                        Long shareStoreId = skuItemS2L.getShareStoreId();
                        BigDecimal qtyPreOut1 = skuItemS2L.getQtyPreOut();
                        if (shareReleaseMap.containsKey(shareStoreId)) {
                            Map<Long, BigDecimal> shareReleaseInfoMap = shareReleaseMap.get(shareStoreId);
                            shareReleaseInfoMap.put(sourceItemId, qtyPreOut1);
                        }
                        Map shareReleaseInfoMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
                        shareReleaseInfoMap.put(sourceItemId, qtyPreOut1);
                        shareReleaseMap.put(shareStoreId, shareReleaseInfoMap);
                    }
                    continue;
                }
                BigDecimal qtyPreOut = logicOccupyPlan.getQty();
                // 没有差异
                if (qtyShareOccupy.compareTo(qtyPreOut) == 0) {
                    continue;
                }
                // 待回滚数量
                BigDecimal qtyNeedRelease = qtyShareOccupy.subtract(qtyPreOut);
                Map<Long, BigDecimal> storePlan = logicOccupyPlan.getStorePlan();
                for (SkuItemS2L skuItemS2L : skuItemS2LList) {
                    if (qtyNeedRelease.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    Long shareStoreId = skuItemS2L.getShareStoreId();
                    // 当前聚合仓占用数量
                    BigDecimal currShareQtyPreOut = skuItemS2L.getQtyPreOut();
                    // 获取聚合仓对应的逻辑仓
                    List<SgCpCStore> currShareLogicStoreList = storeInfoMap.get(shareStoreId);
                    for (SgCpCStore currShareLogicStore : currShareLogicStoreList) {
                        Long logicStoreId = currShareLogicStore.getId();
                        if (!storePlan.containsKey(logicStoreId)) {
                            continue;
                        }
                        BigDecimal currLogicQtyPreOut = storePlan.get(logicStoreId);
                        currShareQtyPreOut = currShareQtyPreOut.subtract(currLogicQtyPreOut);
                        if (currShareQtyPreOut.compareTo(BigDecimal.ZERO) > 0) {
                            storePlan.put(logicStoreId, BigDecimal.ZERO);
                        } else if (currShareQtyPreOut.compareTo(BigDecimal.ZERO) == 0) {
                            break;
                        } else {
                            storePlan.put(logicStoreId, currShareQtyPreOut.negate());
                        }
                    }
                    // 当前聚合仓存在少占用
                    if (currShareQtyPreOut.compareTo(BigDecimal.ZERO) > 0) {
                        qtyNeedRelease = qtyNeedRelease.subtract(currShareQtyPreOut);
                        if (shareReleaseMap.containsKey(shareStoreId)) {
                            Map<Long, BigDecimal> shareReleaseInfoMap = shareReleaseMap.get(shareStoreId);
                            shareReleaseInfoMap.put(sourceItemId, currShareQtyPreOut);
                        }
                        Map shareReleaseInfoMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
                        shareReleaseInfoMap.put(sourceItemId, currShareQtyPreOut);
                        shareReleaseMap.put(shareStoreId, shareReleaseInfoMap);
                    }
                }
            }
            if (MapUtils.isEmpty(shareReleaseMap)) {
                if (log.isDebugEnabled()) {
                    log.debug("二阶段寻源派单 S->L 来源单据:{} 逻辑占用后更新共享占用 共享占用单与逻辑占用单结果一致无需回滚差异", request.getSourceBillId());
                } else {
                    log.info("二阶段寻源派单 S->L 来源单据:{} 逻辑占用后更新共享占用 共享占用单与逻辑占用单结果一致无需回滚差异", request.getSourceBillId());
                }
                valueHolderV14.setMessage("共享占用单与逻辑占用单结果一致无需回滚差异");
                return valueHolderV14;
            }
            if (log.isDebugEnabled()) {
                log.debug("二阶段寻源派单 S->L 来源单据:{} 逻辑占用后更新共享占用 待更新的共享占用单:{}", request.getSourceBillId(), JSONObject.toJSONString(shareReleaseMap));
            } else {
                log.info("二阶段寻源派单 S->L 来源单据:{} 逻辑占用后更新共享占用 待更新的共享占用单:{}", request.getSourceBillId(), JSONObject.toJSONString(shareReleaseMap));
            }
            Set<Map.Entry<Long, Map<Long, BigDecimal>>> shareReleaseEntries = shareReleaseMap.entrySet();
            for (Map.Entry<Long, Map<Long, BigDecimal>> shareReleaseEntry : shareReleaseEntries) {
                Long shareStoreId = shareReleaseEntry.getKey();
                SgBShareOutReleaseService releaseService = ApplicationContextHandle.getBean(SgBShareOutReleaseService.class);
                // 待取消主表
                SgBShareOutBillReleaseRequest sgBShareOutBillReleaseRequest = new SgBShareOutBillReleaseRequest();
                SgBShareOutReleaseRequest sgBShareOutReleaseRequest = new SgBShareOutReleaseRequest();
                sgBShareOutReleaseRequest.setSourceBillid(request.getSourceBillId());
                sgBShareOutReleaseRequest.setSourceBillNo(request.getSourceBillNo());
                sgBShareOutReleaseRequest.setSgCShareStoreId(shareStoreId);
                sgBShareOutBillReleaseRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());
                sgBShareOutBillReleaseRequest.setShareOutReleaseRequest(sgBShareOutReleaseRequest);
                // 只释放占用 不更新出库数量
                sgBShareOutBillReleaseRequest.setIsUpdateOutqty(Boolean.TRUE);
                // 待取消明细
                List<SgBShareOutItemReleaseRequest> releaseRequest = new ArrayList<>();
                sgBShareOutBillReleaseRequest.setShareOutItemReleaseRequests(releaseRequest);
                Map<Long, BigDecimal> shareReleaseInfo = shareReleaseEntry.getValue();
                Set<Map.Entry<Long, BigDecimal>> entrySet = shareReleaseInfo.entrySet();
                for (Map.Entry<Long, BigDecimal> shareReleaseInfoEntry : entrySet) {
                    Long sourceItemId = shareReleaseInfoEntry.getKey();
                    BigDecimal releaseQty = shareReleaseInfoEntry.getValue();
                    // 必然存在
                    List<SkuItemS2L> skuItemS2LS = shareOccupyItemMap.get(sourceItemId);
                    SkuItemS2L skuItemS2L = skuItemS2LS.get(0);
                    SgBShareOutItemReleaseRequest sgBShareOutItemReleaseRequest = new SgBShareOutItemReleaseRequest();
                    sgBShareOutItemReleaseRequest.setSkuId(skuItemS2L.getPsCSkuId());
                    sgBShareOutItemReleaseRequest.setQty(releaseQty);
                    releaseRequest.add(sgBShareOutItemReleaseRequest);
                }
                valueHolderV14 = releaseService.releaseShareOut(sgBShareOutBillReleaseRequest);
            }
        } catch (Exception e) {
            log.error("二阶段寻源派单 S->L 来源单据:{} 逻辑占用后更新共享占用 发生异常:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("二阶段寻源派单 S->L 逻辑占用后更新共享占用 发生异常");
        }
        if (log.isDebugEnabled()) {
            log.debug("二阶段寻源派单 S->L 来源单据:{} 逻辑占用后更新共享占用 更新结果:{}", valueHolderV14.toJSONObject());
        } else {
            log.debug("二阶段寻源派单 S->L 来源单据:{} 逻辑占用后更新共享占用 更新结果:{}", valueHolderV14.toJSONObject());
        }
        return valueHolderV14;

    }

    public ValueHolderV14<List<String>> shareOut2ReleaseStorage(SgFindSourceStrategyS2LRequest request, SgOccupyPlanServiceResult occupyPlan) {
        log.info("SgFindSourceRollBackService.shareOutReleaseStorage 二阶段寻源派单 S->L 来源单据:{} 对比逻辑占用单与共享占用单差异 request:{} occupyPlan:{}", request.getSourceBillId(), JSONObject.toJSONString(request), JSONObject.toJSONString(occupyPlan));
        ValueHolderV14<List<String>> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        try {
            // 逻辑占用单 占用结果
            List<SkuItemS2L> skuItems = request.getSkuItems();
            // 共享占用单占用结果
            Map<Long, List<SkuItemS2L>> shareOccupyItemMap = skuItems.stream().filter(o -> !StrategyConstants.OUT_DEFAULT_STORE_ID.equals(o.getShareStoreId())).collect(Collectors.groupingBy(SkuItemS2L::getSourceItemId));
            if (MapUtils.isEmpty(shareOccupyItemMap) || occupyPlan == null || CollectionUtils.isEmpty(occupyPlan.getItemResultList())) {
                log.info("二阶段寻源派单 S->L 来源单据:{} 对比逻辑占用单与共享占用单差异 共享占用单或逻辑占用单为空无需对比");
                valueHolderV14.setMessage("共享占用单或逻辑占用单为占用库存无需对比");
                return valueHolderV14;
            }
            List<SgOccupyPlanItemResult> itemResultList = occupyPlan.getItemResultList();
            HashMap<Long, SgOccupyPlanItemResult> itemResultMap = new HashMap<>();
            List<Long> allLogicStoreIds = new ArrayList<>();
            itemResultList.forEach(o -> {
                o.getS2LStorePlan().forEach((k, v) -> {
                    if (!StrategyConstants.OUT_DEFAULT_STORE_ID.equals(k) && !allLogicStoreIds.contains(k)) {
                        allLogicStoreIds.add(k);
                    }
                });
                if (!itemResultMap.containsKey(o.getSourceItemId())) {
                    itemResultMap.put(o.getSourceItemId(), o);
                }
            });

            // 全部缺货 无需更新
            if (CollectionUtils.isEmpty(allLogicStoreIds)) {
                log.info("二阶段寻源派单 S->L 来源单据:{} 对比逻辑占用单与共享占用单差异 订单全部缺货无需对比共享占用单与逻辑占用单差异");
                valueHolderV14.setMessage("订单全部缺货无需对比共享占用单与逻辑占用单差异");
                return valueHolderV14;
            }
            CpCStoreMapper storeMapper = ApplicationContextHandle.getBean(CpCStoreMapper.class);

            List<SgCpCStore> sgCpCStores = storeMapper.selectList(new QueryWrapper<SgCpCStore>().lambda()
                    .select(SgCpCStore::getId, SgCpCStore::getSgCShareStoreId)
                    .in(SgCpCStore::getId, allLogicStoreIds)
                    .eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (CollectionUtils.isEmpty(sgCpCStores)) {
                log.error("二阶段寻源派单 S->L 来源单据:{} 逻辑占用后更新共享占用 根据逻辑仓id查询 逻辑仓信息为空 终止更新");
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("根据逻辑仓id查询 逻辑仓信息为空 终止更新");
                return valueHolderV14;
            }
            Map<Long, List<SgCpCStore>> storeInfoMap = sgCpCStores.stream().collect(Collectors.groupingBy(SgCpCStore::getSgCShareStoreId));
            //时效订单占单使用
            // 待回滚的共享占用单 key shareStoreId_timeOrderId  key:sourceItemId,qty
            //Map<String, Map<Long, BigDecimal>> shareReleaseMap = new HashMap<>();
            //待回滚的共享占用单 改用DO
            List<ShareReleaseDo> list = new ArrayList<>();
            Set<Map.Entry<Long, List<SkuItemS2L>>> entries = shareOccupyItemMap.entrySet();
            for (Map.Entry<Long, List<SkuItemS2L>> entry : entries) {
                Long sourceItemId = entry.getKey();
                List<SkuItemS2L> skuItemS2LList = entry.getValue();
                BigDecimal qtyShareOccupy = skuItemS2LList.stream().map(SkuItemS2L::getQtyPreOut).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                SgOccupyPlanItemResult logicOccupyPlan = itemResultMap.get(sourceItemId);
                // 明细全部回滚
                if (logicOccupyPlan == null || MapUtils.isEmpty(logicOccupyPlan.getS2LStorePlan())) {
                    for (SkuItemS2L skuItemS2L : skuItemS2LList) {
                        Long shareStoreId = skuItemS2L.getShareStoreId();
                        Long timeOrderId = skuItemS2L.getTimeOrderId();
                        String timeOrderBillNo = skuItemS2L.getTimeOrderBillNo();
                        Integer type = skuItemS2L.getType();
                        ShareReleaseDo shareReleaseDo = ShareReleaseDo.builder()
                                .shareStoreId(shareStoreId)
                                .timeOrderId(timeOrderId)
                                .timeOrderBillNo(timeOrderBillNo)
                                .type(type)
                                .build();
                        Map<Long, BigDecimal> shareReleaseInfoMap = MapUtils.isEmpty(shareReleaseDo.getMap()) ? new HashMap<Long, BigDecimal>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY) : shareReleaseDo.getMap();
                        BigDecimal qtyPreOut1 = skuItemS2L.getQtyPreOut();
                        shareReleaseInfoMap.put(sourceItemId, qtyPreOut1);
                        shareReleaseDo.setMap(shareReleaseInfoMap);
                        list.add(shareReleaseDo);
                    }
                    continue;
                }
                BigDecimal qtyPreOut = logicOccupyPlan.getQty();
                // 没有差异
                if (qtyShareOccupy.compareTo(qtyPreOut) == 0) {
                    continue;
                }
                // 待回滚数量
                BigDecimal qtyNeedRelease = qtyShareOccupy.subtract(qtyPreOut);
                Map<Long, Map<String, BigDecimal>> s2LStorePlan = logicOccupyPlan.getS2LStorePlan();
                for (SkuItemS2L skuItemS2L : skuItemS2LList) {
                    if (qtyNeedRelease.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    Long shareStoreId = skuItemS2L.getShareStoreId();
                    Long timeOrderId = skuItemS2L.getTimeOrderId();
                    String timeOrderBillNo = skuItemS2L.getTimeOrderBillNo();
                    Integer type = skuItemS2L.getType();
                    // 当前聚合仓占用数量
                    BigDecimal currShareQtyPreOut = skuItemS2L.getQtyPreOut();
                    // 获取聚合仓对应的逻辑仓
                    List<SgCpCStore> currShareLogicStoreList = storeInfoMap.get(shareStoreId);
                    if (CollectionUtils.isNotEmpty(currShareLogicStoreList)) {
                        t:
                        for (SgCpCStore currShareLogicStore : currShareLogicStoreList) {
                            Long logicStoreId = currShareLogicStore.getId();
                            if (!s2LStorePlan.containsKey(logicStoreId)) {
                                continue;
                            }
                            Map<String, BigDecimal> produceDateMap = s2LStorePlan.get(logicStoreId);
                            for (Map.Entry<String, BigDecimal> produceDateEntry : produceDateMap.entrySet()) {
                                String produceDate = produceDateEntry.getKey();
                                BigDecimal currLogicQtyPreOut = produceDateEntry.getValue();
                                currShareQtyPreOut = currShareQtyPreOut.subtract(currLogicQtyPreOut);
                                if (currShareQtyPreOut.compareTo(BigDecimal.ZERO) > 0) {
                                    produceDateMap.put(produceDate, BigDecimal.ZERO);
                                } else if (currShareQtyPreOut.compareTo(BigDecimal.ZERO) == 0) {
                                    // 这里直接跳出逻辑仓，不置空也可以
                                    produceDateMap.put(produceDate, BigDecimal.ZERO);
                                    break t;
                                } else {
                                    produceDateMap.put(produceDate, currShareQtyPreOut.negate());
                                    break t;
                                }
                            }
                        }
                    }
                    // 当前聚合仓存在少占用
                    if (currShareQtyPreOut.compareTo(BigDecimal.ZERO) > 0) {
                        log.debug("zzzz:{}, {}", currShareQtyPreOut.toString(), qtyNeedRelease.toString());
//                        qtyNeedRelease = qtyNeedRelease.subtract(currShareQtyPreOut);
                        ShareReleaseDo shareReleaseDo = ShareReleaseDo.builder()
                                .shareStoreId(shareStoreId)
                                .timeOrderId(timeOrderId)
                                .timeOrderBillNo(timeOrderBillNo)
                                .type(type)
                                .build();
                        Map<Long, BigDecimal> shareReleaseInfoMap = MapUtils.isEmpty(shareReleaseDo.getMap()) ? new HashMap<Long, BigDecimal>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY) : shareReleaseDo.getMap();
                        shareReleaseInfoMap.put(sourceItemId, currShareQtyPreOut);
                        shareReleaseDo.setMap(shareReleaseInfoMap);
                        list.add(shareReleaseDo);
                    }
                }
            }
            if (CollectionUtils.isEmpty(list)) {
                log.info("二阶段寻源派单 S->L 来源单据:{} 逻辑占用后更新共享占用 共享占用单与逻辑占用单结果一致无需回滚差异", request.getSourceBillId());
                valueHolderV14.setMessage("共享占用单与逻辑占用单结果一致无需回滚差异");
                return valueHolderV14;
            }
            log.info("二阶段寻源派单 S->L 来源单据:{} 逻辑占用后更新共享占用 待更新的共享占用单:{}", request.getSourceBillId(), JSONObject.toJSONString(list));
            for (ShareReleaseDo shareReleaseDo : list) {

                SgBShareOutReleaseService releaseService = ApplicationContextHandle.getBean(SgBShareOutReleaseService.class);
                // 待取消主表
                SgBShareOutBillReleaseRequest sgBShareOutBillReleaseRequest = new SgBShareOutBillReleaseRequest();
                sgBShareOutBillReleaseRequest.setSourceFlag(Boolean.TRUE);
                SgBShareOutReleaseRequest sgBShareOutReleaseRequest = new SgBShareOutReleaseRequest();
                if (SourceTypeEnum.VIP_TYPE.getCode().equals(shareReleaseDo.getType())) {
                    //时效订单占单流程
                    sgBShareOutReleaseRequest.setSourceBillid(shareReleaseDo.getTimeOrderId());
                    sgBShareOutReleaseRequest.setSourceBillNo(shareReleaseDo.getTimeOrderBillNo());
                } else {
                    //正常占单
                    sgBShareOutReleaseRequest.setSourceBillid(request.getSourceBillId());
                    sgBShareOutReleaseRequest.setSourceBillNo(request.getSourceBillNo());
                }
                sgBShareOutReleaseRequest.setSgCShareStoreId(shareReleaseDo.getShareStoreId());
                sgBShareOutBillReleaseRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());
                sgBShareOutBillReleaseRequest.setShareOutReleaseRequest(sgBShareOutReleaseRequest);
                // 只释放占用 不更新出库数量
                sgBShareOutBillReleaseRequest.setIsUpdateOutqty(Boolean.TRUE);
                // 待取消明细
                List<SgBShareOutItemReleaseRequest> releaseRequest = new ArrayList<>();
                sgBShareOutBillReleaseRequest.setShareOutItemReleaseRequests(releaseRequest);
                Map<Long, BigDecimal> shareReleaseInfo = shareReleaseDo.getMap();
                Set<Map.Entry<Long, BigDecimal>> entrySet = shareReleaseInfo.entrySet();
                for (Map.Entry<Long, BigDecimal> shareReleaseInfoEntry : entrySet) {
                    Long sourceItemId = shareReleaseInfoEntry.getKey();
                    BigDecimal releaseQty = shareReleaseInfoEntry.getValue();
                    // 必然存在
                    List<SkuItemS2L> skuItemS2LS = shareOccupyItemMap.get(sourceItemId);
                    SkuItemS2L skuItemS2L = skuItemS2LS.get(0);
                    SgBShareOutItemReleaseRequest sgBShareOutItemReleaseRequest = new SgBShareOutItemReleaseRequest();
                    sgBShareOutItemReleaseRequest.setSkuId(skuItemS2L.getPsCSkuId());
                    sgBShareOutItemReleaseRequest.setQty(releaseQty);
                    sgBShareOutItemReleaseRequest.setSourceItemId(sourceItemId);
                    releaseRequest.add(sgBShareOutItemReleaseRequest);
                }
                valueHolderV14 = releaseService.releaseShareOut(sgBShareOutBillReleaseRequest);
            }
        } catch (Exception e) {
            log.error("二阶段寻源派单 S->L 来源单据:{} 逻辑占用后更新共享占用 发生异常:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("二阶段寻源派单 S->L 逻辑占用后更新共享占用 发生异常");
        }
        log.info("二阶段寻源派单 S->L 来源单据:{} 逻辑占用后更新共享占用 更新结果:{}", valueHolderV14.toJSONObject());
        return valueHolderV14;

    }


    public ValueHolderV14<SgBStoOutBillVoidResult> stoOutRollBackStorage(String sourceBIllNo, Long sourceBillId, Integer sourceBillType) {
        ValueHolderV14<SgBStoOutBillVoidResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        try {
            SgBStoOutBillVoidRequest request = new SgBStoOutBillVoidRequest();
            request.setSourceBillId(sourceBillId);
            request.setSourceBillNo(sourceBIllNo);
            request.setSourceBillType(sourceBillType);
            request.setLoginUser(R3SystemUserResource.getSystemRootUser());
            SgBStoOutVoidService service = ApplicationContextHandle.getBean(SgBStoOutVoidService.class);
            valueHolderV14 = service.voidSgBStoOut(request);
            if (!valueHolderV14.isOK()) {
                log.error("寻源派单 来源单据:{} 回滚共享占用单失败:{}", sourceBillId, valueHolderV14.getMessage());
            }
        } catch (Exception e) {
            log.error("寻源派单 来源单据:{} 回滚共享占用单失败:{}", sourceBillId, Throwables.getStackTraceAsString(e));
        }
        return valueHolderV14;
    }


}