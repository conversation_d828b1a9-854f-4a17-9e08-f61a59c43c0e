package com.burgeon.r3.sg.sourcing.services;


import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.burgeon.r3.sg.sourcing.services.factory.item.SgFindSourceForceStrategyGroupItemFactory;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import org.springframework.stereotype.Component;

/**
 * @description: 强制寻源策略组
 * @author: hwy
 * @time: 2021/6/7 11:28
 */
@Component
public class SgFindSourceForceStrategyGroupService extends StrategyHandle {

    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {

        // 两个 handle
        // 1: 强制过滤逻辑仓 SgFindSourceFilterStrategyService
        // 2: 拒单策略 SgFindSourceRejectStoreFilterStrategyService
        SgFindSourceForceStrategyGroupItemFactory itemFactory = ApplicationContextHandle.getBean(SgFindSourceForceStrategyGroupItemFactory.class);
        StrategyHandle itemHandle = itemFactory.buildStrategy(request);
        ValueHolderV14<StrategyBaseResult> valueHolderV14 = itemHandle.handleRequest(request);
        StrategyBaseResult data = valueHolderV14.getData();
        request.setStrategyBaseResult(data);
        return doNext(request, valueHolderV14);

    }

}