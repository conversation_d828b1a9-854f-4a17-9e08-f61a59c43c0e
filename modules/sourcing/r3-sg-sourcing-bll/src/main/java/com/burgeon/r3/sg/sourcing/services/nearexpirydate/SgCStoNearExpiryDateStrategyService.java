package com.burgeon.r3.sg.sourcing.services.nearexpirydate;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.rpc.RpcPsService;
import com.burgeon.r3.sg.core.enums.YesNoEnum;
import com.burgeon.r3.sg.core.model.table.sourcing.nearexpirydate.SgCStoNearExpiryDateStrategy;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.mapper.nearexpirydate.SgCStoNearExpiryDateStrategyMapper;
import com.jackrain.nea.cpext.api.CpcPhyWareHouseQueryCmd;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.web.face.User;
import io.searchbox.strings.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SgCStoNearExpiryDateStrategyService {

    @Autowired
    private SgCStoNearExpiryDateStrategyMapper mapper;
    @Reference(version = "1.0", group = "cp-ext")
    private CpcPhyWareHouseQueryCmd cpcPhyWareHouseQueryCmd;
    @Autowired
    private RpcPsService rpcPsService;


    /**
     * description:查询sku+仓库/无的大效期策略
     *
     * @Author: liuwenjin
     * @Date 2022/7/26 15:50
     */
    public Map<String, SgCStoNearExpiryDateStrategy> queryByProIdStoNearExpiryDateStrategy(Map<Long, List<Long>> listMap, String type) {
        AssertUtils.cannot(listMap == null || type == null, "查询邻近大效期策略入参为空！");
        log.info(" SgCStoNearExpiryDateStrategyService queryByProIdStoNearExpiryDateStrategy listMap = {}，type={}", JSON.toJSONString(listMap), type);
        Map<String, SgCStoNearExpiryDateStrategy> sgCStoNearExpiryDateStrategyMap = new HashMap<>();
        List<SgCStoNearExpiryDateStrategy> listAll = new ArrayList<>();
        try {
            for (Long aLong : listMap.keySet()) {
                Long skuId = aLong;
                List<Long> storeList = listMap.get(skuId);
                List<SgCStoNearExpiryDateStrategy> list = mapper.queryByProIdStoNearExpiryDateStrategy(skuId, storeList, type);
                listAll.addAll(list);
            }
            if (CollectionUtils.isNotEmpty(listAll)) {
                if (SgSourcingConstants.SG_C_STO_NEAR_EXPIRY_DATE_STRATEGY_PRIVATE.equals(type)) {
                    sgCStoNearExpiryDateStrategyMap = listAll.stream().collect(Collectors.toMap(k -> k.getPsCProId() + "-" + k.getCpCPhyWarehouseId()
                            , Function.identity(), (k1, k2) -> k1));
                } else if (SgSourcingConstants.SG_C_STO_NEAR_EXPIRY_DATE_STRATEGY_PUBLIC.equals(type)) {
                    sgCStoNearExpiryDateStrategyMap = listAll.stream().collect(Collectors.toMap(k -> k.getPsCProId() + ""
                            , Function.identity(), (k1, k2) -> k1));
                }
            }
            log.info(" SgCStoNearExpiryDateStrategyService queryByProIdStoNearExpiryDateStrategy sgCStoNearExpiryDateStrategyMap={}",
                    JSON.toJSONString(sgCStoNearExpiryDateStrategyMap), type);
        } catch (Exception e) {
            AssertUtils.logAndThrowException("查询邻近大效期策略异常", e);
        }
        return sgCStoNearExpiryDateStrategyMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public Long save(JSONObject param, User user) {
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        Long objid = param.getLong("objid");
        String tableName = param.getString("table");

        //新增
        if (Objects.isNull(objid) || objid < 0) {
            JSONObject jsonObject = fixColumn.getJSONObject(tableName);
            //新增检查字段有效性
            SgCStoNearExpiryDateStrategy stCWarehouseLogisticStrategy = checkInsertParam(jsonObject);
            Long id = ModelUtil.getSequence(tableName.toLowerCase());
            stCWarehouseLogisticStrategy.setId(id);
            StorageUtils.setBModelDefalutData(stCWarehouseLogisticStrategy, user);
            mapper.insert(stCWarehouseLogisticStrategy);
            objid = id;
        } else {
            //更新
            JSONObject afterValue = param.getJSONObject("aftervalue");
            JSONObject jsonObjectAfter = afterValue.getJSONObject(tableName);
            SgCStoNearExpiryDateStrategy stCWarehouseLogisticStrategy = JSONObject.parseObject(jsonObjectAfter.toJSONString(), SgCStoNearExpiryDateStrategy.class);
            stCWarehouseLogisticStrategy.setId(objid);
            StorageUtils.setBModelDefalutDataByUpdate(stCWarehouseLogisticStrategy, user);
            mapper.updateById(stCWarehouseLogisticStrategy);
        }

        return objid;
    }

    /**
     * 检查入参有效性
     * @param jsonObject
     */
    private SgCStoNearExpiryDateStrategy checkInsertParam(JSONObject jsonObject) {
        if(Objects.isNull(jsonObject)){
            throw new NDSException("入参不能为空");
        }
        SgCStoNearExpiryDateStrategy sgCStoNearExpiryDateStrategy = JSONObject.parseObject(jsonObject.toJSONString(), SgCStoNearExpiryDateStrategy.class);
        if (StringUtils.isBlank(sgCStoNearExpiryDateStrategy.getType())) {
            throw new NDSException("类型不能为空");
        }
        if (Objects.isNull(sgCStoNearExpiryDateStrategy.getPsCProId())) {
            throw new NDSException("商品不能为空");
        }
        //效期管理类型
        if (Objects.isNull(sgCStoNearExpiryDateStrategy.getReserveVarchar01())) {
            throw new NDSException("效期管理类型不能为空");
        }

        if (Objects.isNull(sgCStoNearExpiryDateStrategy.getStartTime())) {
            throw new NDSException("有效期开始时间不能为空");
        }
        if (Objects.isNull(sgCStoNearExpiryDateStrategy.getEndTime())) {
            throw new NDSException("有效期结束时间不能为空");
        }
        if (sgCStoNearExpiryDateStrategy.getStartTime().after(sgCStoNearExpiryDateStrategy.getEndTime())) {
            throw new NDSException("有效期开始时间不能大于有效期结束时间");
        }
        //查询商品信息
        List<Long> proIds = new ArrayList<>();
        proIds.add(sgCStoNearExpiryDateStrategy.getPsCProId());
        List<PsCPro> psCPros = rpcPsService.selProByIds(proIds);
        if (CollectionUtils.isEmpty(psCPros)) {
            throw new NDSException("所选商品不存在");
        }
        sgCStoNearExpiryDateStrategy.setPsCProEcode(psCPros.get(0).getEcode());
        sgCStoNearExpiryDateStrategy.setPsCProEname(psCPros.get(0).getEname());

        //如果指定仓库则校验仓库是否为空
        if(SgSourcingConstants.SG_C_STO_NEAR_EXPIRY_DATE_STRATEGY_PRIVATE.equals(sgCStoNearExpiryDateStrategy.getType())){
            if (Objects.isNull(sgCStoNearExpiryDateStrategy.getCpCPhyWarehouseId())) {
                throw new NDSException("仓库不能为空");
            }
            //判断“仓库+商品+类型”是否已经存在可用为是的记录
            List<SgCStoNearExpiryDateStrategy> sgCStoNearExpiryDateStrategies = mapper.selectList(new QueryWrapper<SgCStoNearExpiryDateStrategy>()
                    .lambda()
                    .eq(SgCStoNearExpiryDateStrategy::getCpCPhyWarehouseId, sgCStoNearExpiryDateStrategy.getCpCPhyWarehouseId())
                    .eq(SgCStoNearExpiryDateStrategy::getPsCProId, sgCStoNearExpiryDateStrategy.getPsCProId())
                    .eq(SgCStoNearExpiryDateStrategy::getType, sgCStoNearExpiryDateStrategy.getType())
                    .eq(SgCStoNearExpiryDateStrategy::getIsactive, YesNoEnum.Y.getKey()));

            boolean isExist = checkIsExist(sgCStoNearExpiryDateStrategies, sgCStoNearExpiryDateStrategy);
            if (isExist) {
                throw new NDSException("策略生效时间交叉，不允许保存！");
            }

            CpCPhyWarehouse cpCPhyWarehouse = cpcPhyWareHouseQueryCmd.queryCpCPhyWarehouseById(sgCStoNearExpiryDateStrategy.getCpCPhyWarehouseId());
            if (Objects.isNull(cpCPhyWarehouse)) {
                throw new NDSException("所选仓库不存在！");
            }
            sgCStoNearExpiryDateStrategy.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
            sgCStoNearExpiryDateStrategy.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
        }else {
            //判断“商品+类型”是否已经存在可用为是的记录
            List<SgCStoNearExpiryDateStrategy> sgCStoNearExpiryDateStrategies = mapper.selectList(new QueryWrapper<SgCStoNearExpiryDateStrategy>()
                    .lambda()
                    .eq(SgCStoNearExpiryDateStrategy::getPsCProId, sgCStoNearExpiryDateStrategy.getPsCProId())
                    .eq(SgCStoNearExpiryDateStrategy::getType, sgCStoNearExpiryDateStrategy.getType())
                    .eq(SgCStoNearExpiryDateStrategy::getIsactive, YesNoEnum.Y.getKey()));
            boolean isExist = checkIsExist(sgCStoNearExpiryDateStrategies, sgCStoNearExpiryDateStrategy);
            if (isExist) {
                throw new NDSException("策略生效时间交叉，不允许保存！");
            }
        }
        return sgCStoNearExpiryDateStrategy;
    }

    private boolean checkIsExist(List<SgCStoNearExpiryDateStrategy> sgCStoNearExpiryDateStrategies, SgCStoNearExpiryDateStrategy sgCStoNearExpiryDateStrategy) {
        if (CollectionUtils.isNotEmpty(sgCStoNearExpiryDateStrategies)) {
            for (SgCStoNearExpiryDateStrategy stoNearExpiryDateStrategy : sgCStoNearExpiryDateStrategies) {
                if (((stoNearExpiryDateStrategy.getStartTime().before(sgCStoNearExpiryDateStrategy.getStartTime())) && stoNearExpiryDateStrategy.getEndTime().after(sgCStoNearExpiryDateStrategy.getStartTime())) ||
                        ((stoNearExpiryDateStrategy.getStartTime().before(sgCStoNearExpiryDateStrategy.getEndTime())) && stoNearExpiryDateStrategy.getEndTime().after(sgCStoNearExpiryDateStrategy.getEndTime()))) {
                    return true;
                }
            }
        }
        return false;
    }
}
