package com.burgeon.r3.sg.sourcing.validate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.model.result.matrix.ApiCommandResult;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareScoreFactorStrategy;
import com.burgeon.r3.sg.core.model.table.sourcing.storescore.SgCStoreScoreStrategy;
import com.burgeon.r3.sg.sourcing.mapper.SgCShareScoreFactorStrategyMapper;
import com.burgeon.r3.sg.sourcing.mapper.storescore.SgCStoreScoreStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.dto.strategy.SgCShareScoreFactorStrategyDto;
import com.burgeon.r3.sg.sourcing.services.SgCShareScoreStrategyQueryService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * @Description 评分因子设置记录删除
 * <AUTHOR>
 * @Date 2021/6/9 15:01
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgShareScoreFactorStrategyDeleteValidator extends BaseSingleValidator<SgCShareScoreFactorStrategyDto> {

    @Autowired
    SgCShareScoreStrategyQueryService sgCShareScoreStrategyQueryService;

    @Autowired
    SgCStoreScoreStrategyMapper sgStoreScoreStrategyMapper;

    @Autowired
    SgCShareScoreFactorStrategyMapper sgCShareScoreFactorStrategyMapper;

    @Override
    public String getValidatorMsgName() {
        return "评分因子设置删除";
    }

    @Override
    public Class getValidatorClass() {
        return SgShareScoreFactorStrategyDeleteValidator.class;
    }

    @Override
    public ValueHolderV14<ApiCommandResult> validateMainTable(SgCShareScoreFactorStrategyDto mainObject,
                                                              User loginUser) {

        if (mainObject.getId() != null && mainObject.getId() > 0L) {
            //判断 评分策略明细 中 可用不为否 的记录中 是否使用了此评分因子。
            Integer countScoreStrategy = sgCShareScoreStrategyQueryService.selectByFactorId(mainObject.getId());
            if (countScoreStrategy > 0) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("当前记录已被评分策略表使用，不允许删除",
                        loginUser.getLocale()));
            }

            SgCShareScoreFactorStrategy factorStrategy =
                    sgCShareScoreFactorStrategyMapper.selectById(mainObject.getId());
            if (StringUtils.isEmpty(factorStrategy)) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("评分因子记录查询失败", loginUser.getLocale()));
            }
            Integer count = sgStoreScoreStrategyMapper.selectCount(new LambdaQueryWrapper<SgCStoreScoreStrategy>()
                    .eq(SgCStoreScoreStrategy::getSgCShareScoreFactorStrategyEcode, factorStrategy.getEcode())
                    .eq(SgCStoreScoreStrategy::getIsactive, SgConstants.IS_ACTIVE_Y));
            //判断 店仓评分表 中 可用不为否 的记录中 是否使用了此评分因子。
            if (count > 0) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("当前记录已被店仓评分表使用，不允许删除",
                        loginUser.getLocale()));
            }
            //TODO : 判断 寻源策略 中 单据状态不为已作废 的记录中 是否使用了此评分因子。

        } else {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("请选择一条需要删除的记录", loginUser.getLocale()));
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过", loginUser.getLocale()));
    }
}
