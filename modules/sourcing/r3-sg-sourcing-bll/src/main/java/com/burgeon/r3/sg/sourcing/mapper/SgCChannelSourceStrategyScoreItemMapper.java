package com.burgeon.r3.sg.sourcing.mapper;

import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategyScoreItem;
import com.burgeon.r3.sg.sourcing.model.result.score.SgCShareScoreFactorStrategyResult;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface SgCChannelSourceStrategyScoreItemMapper extends ExtentionMapper<SgCChannelSourceStrategyScoreItem> {

    /**
     * 获取店铺的各项比分权重
     *
     * @param channelSourceStrategyId 店仓ids
     * @return 权重明细
     */
    @Select("<script> " +
            "select e.ecode, d.weight " +
            "from sg_c_channel_source_strategy_score_item b " +
            "left join sg_c_share_score_strategy c on b.sg_c_share_score_strategy_id = c.id and c.isactive = 'Y' " +
            "left join sg_c_share_score_strategy_item d on d.sg_c_share_score_strategy_id = c.id and d.isactive = 'Y' " +
            "left join sg_c_share_score_factor_strategy e on d.sg_c_share_score_factor_id = e.id " +
            "where b.sg_c_channel_source_strategy_id = #{channelSourceStrategyId} and b.isactive = 'Y' " +
            " </script> ")
    List<SgCShareScoreFactorStrategyResult> queryFactorByShopId(@Param("channelSourceStrategyId") Long channelSourceStrategyId);

}