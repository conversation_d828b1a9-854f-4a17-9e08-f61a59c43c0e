package com.burgeon.r3.sg.sourcing.services.item;


import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.model.request.StCWarehouseLogisticStrategyItemQueryRequest;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticStrategy;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 仓库物流策略执行器
 * @author: chenb
 * @time: 2022/7/21 16:41
 */
@Slf4j
@Component
public class SgFindSourceLogisticsFilterStrategyService extends StrategyHandle {


    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {

        log.info(LogUtil.format("SgFindSourceLogisticsFilterStrategyService.handleRequest S->L二阶段寻源派单 仓库物流策略执行器 param:{}",
                SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L),
                request.getTraceId(), JSONObject.toJSONString(request));

        ValueHolderV14<StrategyBaseResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        SgFindSourceStrategyS2LRequest strategyRequest = (SgFindSourceStrategyS2LRequest) request;
        SgFindSourceStrategyS2LResult strategyResult = (SgFindSourceStrategyS2LResult) strategyRequest.getStrategyBaseResult();
        valueHolderV14.setData(strategyResult);
        //物流可发交集实体仓集合
        Set<Long> phyWarehouseIdResult = new HashSet<>();
        //物流可发拆单并集实体仓集合
        Set<Long> phyWarehouseIdSpilitResult = new HashSet<>();
        //非缺货明细的实体仓集合
        Set<Long> phyWarehouseIds = new HashSet<>();

        //物流排除的实体仓信息
        Map<Long, String> filterWarehouses = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

        if (strategyResult == null) {
            FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 仓库物流策略获取上游策略执行结果:不存在可使用的仓库");
            valueHolderV14.setMessage("仓库物流策略获取上游策略执行结果:不存在可使用的仓库 策略执行结束!");
            FindSourceStrategyUtils.allOut(strategyRequest);
            valueHolderV14.setData(request.getStrategyBaseResult());
            return valueHolderV14;
        }

        try {

            //物流明细不为空的情况下，校验仓库物流设置
            if (MapUtils.isEmpty(strategyRequest.getLogisticsInfo())) {
                return doNext(request, valueHolderV14);
            }

            List<Long> logisticsIds = strategyRequest.getLogisticsInfo().keySet().stream().collect(Collectors.toList());

            //根据物流明细获取仓库物流设置信息
            StCWarehouseLogisticStrategyItemQueryRequest logisticsRequest = new StCWarehouseLogisticStrategyItemQueryRequest();
            logisticsRequest.setCpCLogisticsIds(logisticsIds);
            List<StCWarehouseLogisticStrategy> logisticStrategyList = CommonCacheValUtils.queryLogisticStrategyByDetail(logisticsRequest);

            FindSourceStrategyUtils.outputJsonLog("S->L二阶段寻源派单 仓库物流策略获取仓库物流设置信息结果:{}", logisticStrategyList);

            //未配置仓库物流设置信息的情况下
            if (CollectionUtils.isEmpty(logisticStrategyList)) {
                FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 仓库物流策略获取仓库物流设置信息结果:无仓库可发物流:{}!",
                        strategyRequest.getLogisticsInfo());
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(Resources.getMessage("无仓库可发物流寻源失败!", strategyRequest.getLogisticsInfo().values()));
                FindSourceStrategyUtils.allOut(strategyRequest);
                valueHolderV14.setData(request.getStrategyBaseResult());
                return valueHolderV14;
            }

            phyWarehouseIdResult = logisticStrategyList.stream().map(
                    StCWarehouseLogisticStrategy::getCpCPhyWarehouseId).collect(Collectors.toSet());

            for (SgFindSourceStrategySkuS2LResult skuResult : strategyResult.getSkuResultList()) {

                //获取非缺货默认仓的实体仓Id
                phyWarehouseIds = skuResult.getItemResultList().stream().filter(
                        x -> !StrategyConstants.OUT_DEFAULT_STORE_ID.equals(x.getStoreId())).map(
                        SgFindSourceStrategyStoreItemS2LResult::getStoreId).collect(Collectors.toSet());

                //不允许拆单的情况下：执行订单明细中实体仓的交集，允许拆单的情况下并集
                if (!CollectionUtils.isEmpty(phyWarehouseIds) &&
                        StrategyConstants.ORDER_SPLIT_TYPE_NO.equals(strategyRequest.getSplitType())) {
                    phyWarehouseIdResult.retainAll(phyWarehouseIds);

                    //判断订单中实体仓是否存在交集
                    if (CollectionUtils.isEmpty(phyWarehouseIdResult)) {
                        FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 仓库物流策略判断订单中实体仓不存在交集结果:无仓库可发物流:{}",
                                strategyRequest.getLogisticsInfo());
                        valueHolderV14.setCode(ResultCode.FAIL);
                        valueHolderV14.setMessage("不存在交集的实体仓可发物流：" + strategyRequest.getLogisticsInfo().values() + "，寻源失败!");
                        FindSourceStrategyUtils.allOut(strategyRequest);
                        valueHolderV14.setData(null);
                        return valueHolderV14;
                    }

                } else if (!CollectionUtils.isEmpty(phyWarehouseIds) &&
                        !StrategyConstants.ORDER_SPLIT_TYPE_NO.equals(strategyRequest.getSplitType())) {

                    for (Long warehouseId : phyWarehouseIds) {
                        if (phyWarehouseIdResult.contains(warehouseId)) {
                            phyWarehouseIdSpilitResult.add(warehouseId);
                        }
                    }
                }
            }

            //允许拆单的情况下，并集为空时报错
            if (!StrategyConstants.ORDER_SPLIT_TYPE_NO.equals(strategyRequest.getSplitType())) {

                //判断订单中实体仓是否存在交集
                if (CollectionUtils.isEmpty(phyWarehouseIdSpilitResult)) {
                    FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 仓库物流策略判断订单中实体仓不存在并集结果:无仓库可发物流:{}",
                            strategyRequest.getLogisticsInfo());
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("不存在交集的实体仓可发物流：" + strategyRequest.getLogisticsInfo().values() + "，寻源失败!");
                    FindSourceStrategyUtils.allOut(strategyRequest);
                    valueHolderV14.setData(null);
                    return valueHolderV14;
                }

                phyWarehouseIdResult = phyWarehouseIdSpilitResult;
            }

            Map<Long, Long> phyWarehouseIdFilterMap = phyWarehouseIdResult.stream().collect(
                    Collectors.toMap(Function.identity(), Function.identity(), (orderValue, newValue) -> orderValue));
            List<SgFindSourceStrategyStoreItemS2LResult> ItemResultList = null;

            FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 仓库物流策略获取订单中实体仓交集结果. phyWarehouseIdFilterMap:{}",
                    phyWarehouseIdFilterMap);

            //获取交集的实体仓作为新的实体仓范围
            for (SgFindSourceStrategySkuS2LResult skuResult : strategyResult.getSkuResultList()) {

                ItemResultList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
                for (SgFindSourceStrategyStoreItemS2LResult storeResult : skuResult.getItemResultList()) {

                    //仓库物流可发并且非默认缺货仓的情况下，重新整理实体仓信息
                    if (phyWarehouseIdFilterMap.containsKey(storeResult.getStoreId()) &&
                            !StrategyConstants.OUT_DEFAULT_STORE_ID.equals(storeResult.getStoreId())) {
                        ItemResultList.add(storeResult);
                    } else if (!StrategyConstants.OUT_DEFAULT_STORE_ID.equals(storeResult.getStoreId())) {
                        filterWarehouses.put(storeResult.getStoreId(), storeResult.getPhywarehouseEname());
                    }
                }

                if (CollectionUtils.isEmpty(ItemResultList)) {
                    SgFindSourceStrategyStoreItemS2LResult newItemResult = new SgFindSourceStrategyStoreItemS2LResult();
                    newItemResult.setQty(BigDecimal.ZERO);
                    newItemResult.setStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                    newItemResult.setLogicStorageMap(new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY));
                    ItemResultList.add(newItemResult);
                }

                skuResult.setItemResultList(ItemResultList);
            }

            //设置强制排除的实体仓信息
            if (!MapUtils.isEmpty(filterWarehouses)) {
                Map removedStoreInfo = strategyResult.getRemovedStoreInfo();
                removedStoreInfo.put(SgSourcingConstants.MSG_REMOVED_STORE_INFO_LOGISTICS_FILTER, filterWarehouses.values().toString());
            }

        } catch (Exception e) {
            log.error(LogUtil.format("SgFindSourceFilterStrategyService.handleRequest S->L二阶段寻源派单 仓库物流策略执行器发生异常 exception:{}",
                    SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L_EXCEPTION),
                    request.getTraceId(), Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("S->L 二阶段寻源派单 仓库物流策略执行器发生异常!");
        }

        //清理
        phyWarehouseIdResult.clear();
        phyWarehouseIds.clear();
        phyWarehouseIdSpilitResult.clear();

        return doNext(request, valueHolderV14);
    }

}