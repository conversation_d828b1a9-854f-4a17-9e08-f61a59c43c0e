package com.burgeon.r3.sg.sourcing.validate;

import com.burgeon.r3.sg.basic.services.log.LogCommonService;
import com.burgeon.r3.sg.channel.validate.strategy.SgCChannelSkuStrategySubmitValidator;
import com.burgeon.r3.sg.core.model.table.basic.SgCOperationLog;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.model.dto.strategy.SgCChannelSourceStrategyDTO;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/6/4 15:27
 */
@Component
public class SgCChannelSourceStrategySubmitValidator extends BaseSingleValidator<SgCChannelSourceStrategyDTO> {

    @Resource
    private LogCommonService logCommonService;

    @Override
    public String getValidatorMsgName() {
        return "寻源策略表审核";
    }

    @Override
    public Class getValidatorClass() {
        return SgCChannelSkuStrategySubmitValidator.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCChannelSourceStrategyDTO mainObject, User loginUser) {
        SgCChannelSourceStrategyDTO orignalData = getOrignalData();
        Integer status = orignalData.getStatus();
        if (!SgSourcingConstants.BILL_SOURCE_STRATEGY_UNSUBMIT.equals(status)) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("当前记录不是未审核状态不允许审核！",
                    loginUser.getLocale()));
        }
        SgCOperationLog operationLog = logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY",
                OperationTypeEnum.AUDIT.getOperationValue(), mainObject.getId(), "寻源策略定义",
                null, null, null, loginUser);
        logCommonService.insertLog(operationLog);
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
    }
}
