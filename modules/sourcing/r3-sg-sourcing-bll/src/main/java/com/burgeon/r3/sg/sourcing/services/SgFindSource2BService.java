package com.burgeon.r3.sg.sourcing.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.basic.model.SgStorageRedisQueryLsModel;
import com.burgeon.r3.sg.basic.model.request.SgStorageQuerySaRequest;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQueryResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySaResult;
import com.burgeon.r3.sg.basic.rpc.RpcCpService;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyMapper;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelRatioStrategyQueryInfoResult;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOut;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutMapper;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutBillSaveRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutItemLogSaveRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutItemSaveRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutSaveRequest;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutInfoResult;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutSaveResult;
import com.burgeon.r3.sg.share.services.out.SgBShareOutSaveService;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategy2BRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItem2B;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutMapper;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutItemLogSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutSaveRequest;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutBillSaveResult;
import com.burgeon.r3.sg.store.services.out.SgBStoOutSaveService;
import com.burgeon.r3.sg.store.services.rpc.IpRpcService;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.Enum.CpCDistributionOrganizationLevelEnum;
import com.jackrain.nea.cpext.model.result.CpCSaleOrganizationQueryResult;
import com.jackrain.nea.cpext.model.table.CpCDistributionOrganization;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BeanCopierUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: hwy
 * @time: 2021/6/22 10:30
 */
@Component
@Slf4j
public class SgFindSource2BService {

    @Autowired
    private SgBStoOutMapper stoOutMapper;
    @Autowired
    private SgBShareOutMapper shareOutMapper;
    @Autowired
    private CpCStoreMapper storeMapper;
    @Autowired
    private SgCSaStoreMapper saStoreMapper;
    @Autowired
    private SgStorageQueryService storageQueryService;
    @Autowired
    private SgFindSourceRollBackService sgFindSourceRollBackService;
    @Autowired
    private RpcCpService rpcCpService;
    @Resource
    private IpRpcService ipRpcService;


    public ValueHolderV14<List<SkuItem2B>> findSource2B(SgFindSourceStrategy2BRequest request) {
        ValueHolderV14<List<SkuItem2B>> valueHolderV14 = this.checkParam(request);
        if (!valueHolderV14.isOK()) {
            return valueHolderV14;
        }
        SgFindSource2BService bean = ApplicationContextHandle.getBean(SgFindSource2BService.class);
        try {
            return bean.exec(request);
        } catch (Exception e) {
            log.error("SgFindSource2BService.findSource2B error:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("寻源失败：" + e.getMessage());
            valueHolderV14.setData(request.getSkuItems());
            return valueHolderV14;
        }
    }


    public ValueHolderV14<List<SkuItem2B>> exec(SgFindSourceStrategy2BRequest request) {
        ValueHolderV14<List<SkuItem2B>> v14 =
                new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        log.info(LogUtil.format("SgFindSource2BService.exec sourceBillNo:{},request:{}",
                "SgFindSource2BService.exec", request.getSourceBillNo()), JSONObject.toJSONString(request));
        // 获取逻辑仓
        List<SgCpCStore> storeList = queryStoreByPhy(request);
        if (CollectionUtils.isEmpty(storeList)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("指定实体仓下未查询到逻辑仓");
            v14.setData(request.getSkuItems());
            return v14;
        }
        SgCpCStore store = storeList.get(0);
        if (store.getSgCShareStoreId() == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("指定实体仓对应逻辑仓未找到聚合仓");
            v14.setData(request.getSkuItems());
            return v14;
        }
        //获取配销仓
        List<SgCSaStore> saStoreList = querySaByStore(store);
        if (CollectionUtils.isEmpty(saStoreList)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("当前逻辑仓未找到配销仓");
            v14.setData(request.getSkuItems());
            return v14;
        }
        //异常行的二级分货组织编码
        List<String> errorDistCodes = new ArrayList<>();
        //查询比例同步策略
        List<String> distCodes = request.getSkuItems().stream().
                map(SkuItem2B::getDistCodeLevelTwo).distinct().collect(Collectors.toList());
        Map<String, List<SgCChannelRatioStrategyQueryInfoResult>> normalRatioStrategyMap =
                queryNormalRatioStrategy(distCodes, errorDistCodes);
        if (CollectionUtils.isNotEmpty(errorDistCodes)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("未查询到比例同步策略配置，二级分货组织：" + errorDistCodes);
            v14.setData(request.getSkuItems());
            return v14;
        }
        //策略和指定仓交集之后的配销仓
        List<Long> saIdList = new ArrayList<>();
        //指定对应的配销仓ID
        List<Long> saStoreIdList = saStoreList.stream().map(SgCSaStore::getId).collect(Collectors.toList());
        //根据指定实体仓对应的配销仓ID过滤比例同步策略明细的数据
        for (Map.Entry<String, List<SgCChannelRatioStrategyQueryInfoResult>> entry : normalRatioStrategyMap.entrySet()) {
            List<SgCChannelRatioStrategyQueryInfoResult> strategySaStoreList = entry.getValue().stream()
                    .filter(s -> saStoreIdList.contains(s.getSgCSaStoreId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(strategySaStoreList) &&
                    !errorDistCodes.contains(entry.getKey())) {
                errorDistCodes.add(entry.getKey());
            } else {
                normalRatioStrategyMap.put(entry.getKey(), strategySaStoreList);
                List<Long> strategySaStoreIdList = strategySaStoreList.stream()
                        .map(SgCChannelRatioStrategyQueryInfoResult::getSgCSaStoreId).distinct().collect(Collectors.toList());
                saIdList.addAll(strategySaStoreIdList);
            }
        }
        if (CollectionUtils.isNotEmpty(errorDistCodes)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("指定的实体仓对应的配销仓与策略配置的配销仓无交集，分货组织：" + errorDistCodes);
            v14.setData(request.getSkuItems());
            return v14;
        }

        ValueHolderV14<List<SkuItem2B>> valueHolderV14 = this.stoOutService(store, request);
        if (!valueHolderV14.isOK()) {
            log.warn("SgFindSource2BService.exec stoOutService result error:{}", JSONObject.toJSONString(valueHolderV14));
            return valueHolderV14;
        }

        try {
            v14 = this.shareOutService(normalRatioStrategyMap, request, store.getSgCShareStoreId(), saIdList);
        } catch (Exception e) {
            log.error("SgFindSource2BService.exec shareOutService error:{}", Throwables.getStackTraceAsString(e));
            sgFindSourceRollBackService.stoOutRollBackStorage(request.getSourceBillNo(), request.getSourceBillId(), request.getSourceBillType());
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
            v14.setData(request.getSkuItems());
        }
        if (!v14.isOK()) {
            log.warn("SgFindSource2BService.exec shareOutService result error:{}", JSONObject.toJSONString(valueHolderV14));
            sgFindSourceRollBackService.stoOutRollBackStorage(request.getSourceBillNo(), request.getSourceBillId(), request.getSourceBillType());
            return v14;
        }

        return v14;
    }

    /**
     * 根据逻辑仓查询配销仓
     *
     * @param store
     * @return
     */
    private List<SgCSaStore> querySaByStore(SgCpCStore store) {
        LambdaQueryWrapper<SgCSaStore> saStoreWrapper = new LambdaQueryWrapper<>();
        saStoreWrapper.eq(SgCSaStore::getSgCShareStoreId, store.getSgCShareStoreId());
        saStoreWrapper.eq(SgCSaStore::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgCSaStore> saStoreList = saStoreMapper.selectList(saStoreWrapper);
        return saStoreList;
    }

    /**
     * 根据实体仓查询逻辑仓
     *
     * @param request
     * @return
     */
    private List<SgCpCStore> queryStoreByPhy(SgFindSourceStrategy2BRequest request) {
        LambdaQueryWrapper<SgCpCStore> storeWrapper = new LambdaQueryWrapper<>();
        storeWrapper.eq(SgCpCStore::getCpCPhyWarehouseId, request.getWarehouseId());
        storeWrapper.eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgCpCStore> storeList = storeMapper.selectList(storeWrapper);
        return storeList;
    }

    /**
     * 配销占用单
     *
     * @param normalRatioStrategyMap
     * @param request
     * @param sgCShareStoreId
     * @param saIdList
     * @return
     */
    private ValueHolderV14<List<SkuItem2B>> shareOutService(
            Map<String, List<SgCChannelRatioStrategyQueryInfoResult>> normalRatioStrategyMap,
            SgFindSourceStrategy2BRequest request, Long sgCShareStoreId, List<Long> saIdList) {
        ValueHolderV14<List<SkuItem2B>> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        //查询配销仓库存
        List<Long> psCSkuIdList = request.getSkuItems().stream().map(SkuItem2B::getPsCSkuId).distinct().collect(Collectors.toList());
        SgStorageQuerySaRequest saStorageQueryRequest = new SgStorageQuerySaRequest();
        saStorageQueryRequest.setSgCSaStoreIds(saIdList);
        saStorageQueryRequest.setSkuIds(psCSkuIdList);
        ValueHolderV14<List<SgStorageRedisQuerySaResult>> saStorageV14 =
                storageQueryService.querySaStorageWithRedis(saStorageQueryRequest, request.getUser());
        if (saStorageV14 == null || !saStorageV14.isOK() || CollectionUtils.isEmpty(saStorageV14.getData())) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("查询配销仓库存失败");
            v14.setData(request.getSkuItems());
            return v14;
        }
        Map<String, BigDecimal> saStorageMap = saStorageV14.getData().stream().collect(Collectors.toMap(x ->
                        x.getSgCSaStoreId() + SgConstants.SG_CONNECTOR_MARKS_4 + x.getPsCSkuId(),
                SgStorageRedisQuerySaResult::getQtyAvailable, (k1, k2) -> k1));

        // 定义配销占用单变量
        SgBShareOutBillSaveRequest shareOutBillSaveRequest = new SgBShareOutBillSaveRequest();
        SgBShareOutSaveRequest shareOutSaveRequest = new SgBShareOutSaveRequest();
        List<SgBShareOutItemSaveRequest> shareOutItemSaveRequestList = new ArrayList<>();
        List<SgBShareOutItemLogSaveRequest> shareOutItemLogSaveRequestList = new ArrayList<>();
        shareOutBillSaveRequest.setLoginUser(request.getUser());
        shareOutBillSaveRequest.setShareOutSaveRequest(shareOutSaveRequest);
        shareOutBillSaveRequest.setShareOutItemSaveRequestList(shareOutItemSaveRequestList);
        shareOutBillSaveRequest.setShareOutItemLogSaveRequestList(shareOutItemLogSaveRequestList);

        HashMap<String, List<SgBShareOutItemSaveRequest>> shareOutItemSaveRequestMap = new HashMap<>();

        // 设置主表信息
        shareOutSaveRequest.setSgCShareStoreId(sgCShareStoreId);
        shareOutSaveRequest.setSourceBillId(request.getSourceBillId());
        shareOutSaveRequest.setSourceBillNo(request.getSourceBillNo());
        shareOutSaveRequest.setSourceBillType(request.getSourceBillType());
        shareOutSaveRequest.setServiceNode(SgConstantsIF.SERVICE_NODE_SHARE_OUT_SAVE);
        shareOutSaveRequest.setSourceBillDate(request.getBillDate());
        shareOutSaveRequest.setCpCShopId(request.getShopId());
        shareOutSaveRequest.setTid(request.getTid());

        Map<Long, String> validityMap = CommonCacheValUtils
                .queryValidityDefinitionProduceByPsCSkuIdList(psCSkuIdList, null);

        request.getSkuItems().forEach(x -> {
            //二级分货组织编码对应的比例同步策略
            List<SgCChannelRatioStrategyQueryInfoResult> ratioResultList = normalRatioStrategyMap.get(x.getDistCodeLevelTwo());

            String produceRange = validityMap.get(x.getPsCSkuId());

            String beginValidity, endValidity;

            if (StringUtils.isNotEmpty(produceRange)) {
                String[] split = produceRange.split(SgConstants.SG_CONNECTOR_MARKS_6);
                beginValidity = split[0];
                endValidity = split[1];
            } else {
                beginValidity = null;
                endValidity = null;
            }

            x.getQtyMap().forEach((k, v) -> {
                AtomicReference<BigDecimal> atomicQty = new AtomicReference<>(v);

                boolean flag;
                if (StringUtils.isNotEmpty(produceRange) && k.compareTo(beginValidity) >= 0 && k.compareTo(endValidity) <= 0) {
                    flag = true;
                } else {
                    flag = false;
                }

                ratioResultList.forEach(y -> {

                    BigDecimal orderQty = atomicQty.get();

                    String key = y.getSgCSaStoreId() + SgConstants.SG_CONNECTOR_MARKS_4 + x.getPsCSkuId();
                    BigDecimal qty = saStorageMap.get(key);
                    if (qty == null || qty.compareTo(BigDecimal.ZERO) <= 0 || orderQty.compareTo(BigDecimal.ZERO) <= 0) {
                        return;
                    }

                    if ((flag && !SgConstants.SG_SA_STORE_CONTAINS_VALIDITY_TYPE_BIG_VALIDITY.equals(y.getCategory()))
                            || (!flag && SgConstants.SG_SA_STORE_CONTAINS_VALIDITY_TYPE_BIG_VALIDITY.equals(y.getCategory()))) {
                        return;
                    }

                    List<SgBShareOutItemSaveRequest> shareOutItemRequestList =
                            shareOutItemSaveRequestMap.computeIfAbsent(key, k1 -> new ArrayList<>());
                    SgBShareOutItemSaveRequest shareOutItemSaveRequest = new SgBShareOutItemSaveRequest();
                    shareOutItemRequestList.add(shareOutItemSaveRequest);
                    shareOutItemSaveRequest.setPsCSkuId(x.getPsCSkuId());

                    shareOutItemSaveRequest.setSourceBillItemId(x.getSourceItemId());
                    // 记录配销仓占用库存变动参数
                    shareOutItemSaveRequest.setSgCSaStoreId(y.getSgCSaStoreId());
                    shareOutItemSaveRequest.setSourceStorage(SgConstants.SHARE_OUT_ITEM_STOCK_SOURCE_SA);

                    if (qty.compareTo(orderQty) >= 0) {
                        // 单条满足
                        shareOutItemSaveRequest.setQtyPreout(orderQty);
                        shareOutItemSaveRequest.setQty(orderQty);

                        atomicQty.set(BigDecimal.ZERO);
                        saStorageMap.put(key, qty.subtract(orderQty));
                        x.setQty(x.getQty().subtract(orderQty));
                        return;
                    }
                    shareOutItemSaveRequest.setQtyPreout(qty);
                    shareOutItemSaveRequest.setQty(qty);

                    atomicQty.set(orderQty.subtract(qty));
                    x.setQty(x.getQty().subtract(qty));

                    saStorageMap.put(key, BigDecimal.ZERO);
                });
            });

            AssertUtils.cannot(x.getQty().compareTo(BigDecimal.ZERO) > 0, "配销仓库存不足");
        });

        shareOutItemSaveRequestMap.forEach((k, v) -> {
            SgBShareOutItemSaveRequest shareOutItemSaveRequest = v.get(0);

            if (v.size() == SgConstants.LIST_ONLY_ONE) {
                shareOutItemSaveRequestList.add(shareOutItemSaveRequest);
            } else {
                shareOutSaveRequest.setMergeMark(Boolean.TRUE);
                BigDecimal qty = BigDecimal.ZERO;
                for (SgBShareOutItemSaveRequest x : v) {
                    SgBShareOutItemLogSaveRequest shareOutItemLogSaveRequest = new SgBShareOutItemLogSaveRequest();
                    BeanCopierUtil.copy(x, shareOutItemLogSaveRequest);
                    shareOutItemLogSaveRequestList.add(shareOutItemLogSaveRequest);
                    qty = qty.add(x.getQtyPreout());
                }
                shareOutItemSaveRequest.setQtyPreout(qty);
                shareOutItemSaveRequest.setQty(qty);
                shareOutItemSaveRequestList.add(shareOutItemSaveRequest);
            }
        });

        if (log.isDebugEnabled()) {
            log.debug("SgFindSource2BService.shareOutService shareOutBillSaveRequest:{}", JSONObject.toJSONString(shareOutBillSaveRequest));
        }

        SgBShareOutSaveService shareOutSaveService = ApplicationContextHandle.getBean(SgBShareOutSaveService.class);
        ValueHolderV14<SgBShareOutSaveResult> shareOutResultV14 = shareOutSaveService.redundantSgShareOut(shareOutBillSaveRequest);
        if (shareOutResultV14 == null || !shareOutResultV14.isOK() || shareOutResultV14.getData() == null) {
            log.warn("配销层处理失败：{}", JSONObject.toJSONString(shareOutResultV14));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("生成配销占用单失败");
            v14.setData(request.getSkuItems());
            return v14;
        }
        v14.setData(request.getSkuItems());
        return v14;
    }

    /**
     * 逻辑占用单
     *
     * @param store
     * @param request
     * @return
     */
    private ValueHolderV14<List<SkuItem2B>> stoOutService(SgCpCStore store, SgFindSourceStrategy2BRequest request) {
        ValueHolderV14<List<SkuItem2B>> v14 =
                new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        // 查询逻辑仓库存
        List<SgStorageRedisQueryLsModel> storageQueryModelList = new ArrayList<>();
        request.getSkuItems().forEach(x ->
                x.getQtyMap().forEach((k, v) -> {
                    SgStorageRedisQueryLsModel model = new SgStorageRedisQueryLsModel();
                    model.setCpCStoreId(store.getId());
                    model.setProduceDate(k);
                    model.setPsCSkuId(x.getPsCSkuId());
                    storageQueryModelList.add(model);
                }));
        ValueHolderV14<HashMap<String, SgStorageRedisQueryResult>> lsStorageV14 =
                storageQueryService.queryLsStorageWithRedis(storageQueryModelList, request.getUser());
        if (lsStorageV14 == null || !lsStorageV14.isOK() || MapUtils.isEmpty(lsStorageV14.getData())) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("逻辑仓库存查询失败");
            v14.setData(request.getSkuItems());
            return v14;
        }
        //查询WMS冻结库存
        List<Long> skuIds = request.getSkuItems().stream()
                .map(SkuItem2B::getPsCSkuId).distinct().collect(Collectors.toList());
        Map<String, BigDecimal> inventoryResult =
                ipRpcService.queryInventoryResult(store.getId(), request.getSourceBillNo(), skuIds);
        //处理库存
        HashMap<String, BigDecimal> lsStorageMap = new HashMap<>();
        lsStorageV14.getData().forEach((k, v) -> {
            BigDecimal lockQty = inventoryResult.getOrDefault(k, BigDecimal.ZERO);
            BigDecimal qty = v.getQtyAvailable().subtract(lockQty);
            if (qty.compareTo(BigDecimal.ZERO) <= 0) {
                return;
            }
            lsStorageMap.put(k, qty);
        });

        // 生成逻辑占用单参数
        SgBStoOutBillSaveRequest stoOutBillSaveRequest = new SgBStoOutBillSaveRequest();
        SgBStoOutSaveRequest stoOutSaveRequest = new SgBStoOutSaveRequest();
        HashMap<String, List<SgBStoOutItemSaveRequest>> stoOutItemRequestMap = new HashMap<>();

        List<SgBStoOutItemSaveRequest> stoOutItemSaveRequestList = new ArrayList<>();
        List<SgBStoOutItemLogSaveRequest> stoOutItemSaveLogRequestList = new ArrayList<>();

        // 设置request 信息
        stoOutBillSaveRequest.setUpdateMethod(SgConstantsIF.ITEM_UPDATE_TYPE_ALL);
        stoOutBillSaveRequest.setIsEdge(Boolean.TRUE);
        stoOutBillSaveRequest.setIsForceUnNegative(Boolean.TRUE);
        stoOutBillSaveRequest.setLoginUser(request.getUser());
        stoOutBillSaveRequest.setPreoutWarningType(SgConstantsIF.PREOUT_RESULT_ERROR);
        stoOutBillSaveRequest.setIsCancel(Boolean.FALSE);
        stoOutBillSaveRequest.setIsSourceMerge(Boolean.FALSE);
        stoOutBillSaveRequest.setSgBStoOutSaveRequest(stoOutSaveRequest);
        stoOutBillSaveRequest.setSgBStoOutItemSaveRequests(stoOutItemSaveRequestList);
        stoOutBillSaveRequest.setSgBStoOutItemLogSaveRequests(stoOutItemSaveLogRequestList);

        // 设置主表信息
        stoOutSaveRequest.setSourceBillId(request.getSourceBillId());
        stoOutSaveRequest.setSourceBillNo(request.getSourceBillNo());
        stoOutSaveRequest.setSourceBillType(request.getSourceBillType());
        stoOutSaveRequest.setBillDate(request.getBillDate());
        stoOutSaveRequest.setCpCShopId(request.getShopId());
        stoOutSaveRequest.setTid(request.getTid());

        request.getSkuItems().forEach(x ->
                x.getQtyMap().forEach((k, v) -> {
                    // 校验库存是否足够 下面重新寻源生成逻辑占用单
                    String key = store.getId() + SgConstants.SG_CONNECTOR_MARKS_4 + x.getPsCSkuId() + SgConstants.SG_CONNECTOR_MARKS_4 + k;
                    BigDecimal qty = lsStorageMap.get(key);
                    AssertUtils.cannot(qty == null || qty.compareTo(v) < 0, "库存不足！");
                    lsStorageMap.put(key, qty.subtract(v));

                    List<SgBStoOutItemSaveRequest> itemSaveRequestList = stoOutItemRequestMap.computeIfAbsent(key, k1 -> new ArrayList<>());
                    SgBStoOutItemSaveRequest itemSaveRequest = new SgBStoOutItemSaveRequest();
                    itemSaveRequestList.add(itemSaveRequest);

                    itemSaveRequest.setQty(v);
                    itemSaveRequest.setProduceDate(k);
                    itemSaveRequest.setQtyPreout(v);
                    itemSaveRequest.setCpCStoreId(store.getId());
                    itemSaveRequest.setSourceBillItemId(x.getSourceItemId());
                    itemSaveRequest.setPsCSkuId(x.getPsCSkuId());
                    itemSaveRequest.setTid(request.getTid());
                    itemSaveRequest.setLabelingRequirements(x.getLabelingRequirements());
                    itemSaveRequest.setBeginProduceDate(x.getBeginProduceDate());
                    itemSaveRequest.setEndProduceDate(x.getEndProduceDate());
                }));

        stoOutItemRequestMap.forEach((k, v) -> {
            SgBStoOutItemSaveRequest stoOutItemSaveRequest = v.get(0);

            if (v.size() == SgConstants.LIST_ONLY_ONE) {
                stoOutItemSaveRequestList.add(stoOutItemSaveRequest);
            } else {
                stoOutSaveRequest.setMergeMark(Boolean.TRUE);

                BigDecimal qty = BigDecimal.ZERO;
                for (SgBStoOutItemSaveRequest x : v) {
                    SgBStoOutItemLogSaveRequest stoOutItemLogSaveRequest = new SgBStoOutItemLogSaveRequest();
                    BeanCopierUtil.copy(x, stoOutItemLogSaveRequest);
                    stoOutItemSaveLogRequestList.add(stoOutItemLogSaveRequest);
                    qty = qty.add(x.getQtyPreout());

                    if (StringUtils.isNotEmpty(x.getLabelingRequirements())) {
                        stoOutItemSaveRequest.setLabelingRequirements(x.getLabelingRequirements());
                    }

                    if (StringUtils.isNotEmpty(x.getBeginProduceDate())) {
                        if (StringUtils.isEmpty(stoOutItemSaveRequest.getBeginProduceDate()) || x.getBeginProduceDate().compareTo(stoOutItemSaveRequest.getBeginProduceDate()) < 0) {
                            stoOutItemSaveRequest.setBeginProduceDate(x.getBeginProduceDate());
                        }
                    }
                    if (StringUtils.isNotEmpty(x.getEndProduceDate())) {
                        if (StringUtils.isEmpty(stoOutItemSaveRequest.getEndProduceDate()) || x.getEndProduceDate().compareTo(stoOutItemSaveRequest.getEndProduceDate()) > 0) {
                            stoOutItemSaveRequest.setEndProduceDate(x.getEndProduceDate());
                        }
                    }
                }
                stoOutItemSaveRequest.setQtyPreout(qty);
                stoOutItemSaveRequest.setQty(qty);
                stoOutItemSaveRequestList.add(stoOutItemSaveRequest);
            }
        });

        if (log.isDebugEnabled()) {
            log.debug("SgFindSource2BService.stoOutService stoOutBillSaveRequest:{}", JSONObject.toJSONString(stoOutBillSaveRequest));
        }

        SgBStoOutSaveService stoOutSaveService = ApplicationContextHandle.getBean(SgBStoOutSaveService.class);
        ValueHolderV14<SgBStoOutBillSaveResult> stoOutResultV14 = stoOutSaveService.saveSgStoOut(stoOutBillSaveRequest);
        if (stoOutResultV14 == null || !stoOutResultV14.isOK() || stoOutResultV14.getData() == null) {
            log.warn("逻辑层处理失败：", JSONObject.toJSONString(stoOutResultV14));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("生成逻辑占用单失败");
            v14.setData(request.getSkuItems());
            return v14;
        }
        v14.setData(request.getSkuItems());
        return v14;
    }

    private ValueHolderV14<List<SkuItem2B>> checkParam(SgFindSourceStrategy2BRequest request) {
        ValueHolderV14<List<SkuItem2B>> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        if (request == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请求参数不能为空");
            return v14;
        }
        List<SkuItem2B> skuItems = request.getSkuItems();
        if (CollectionUtils.isEmpty(skuItems)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("来源单据明细不能为空");
            return v14;
        }
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotEmpty(request.getAppointDistLevel3())) {
            //指定三级分货组织查询分货组织
            queryDistByAppointDistLevel3(request.getAppointDistLevel3(), skuItems, sb);
        } else {
            //根据销售部门查询分货组织
            queryDistByDepartCode(skuItems, sb);
        }
        if (sb.length() > 0) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("C->S寻源引擎 查询分货组织异常:" + sb);
            return v14;
        }
        v14.setData(request.getSkuItems());
        if (request.getSourceBillId() == null ||
                StringUtils.isEmpty(request.getSourceBillNo()) || request.getSourceBillType() == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("来源单据信息不能为空");
            return v14;
        }
        if (request.getWarehouseId() == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("指定仓不能为空");
            return v14;
        }
        if (request.getUser() == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("用户不能为空");
            return v14;
        }
        LambdaQueryWrapper<SgBStoOut> stoOutWrapper = new LambdaQueryWrapper<>();
        stoOutWrapper.eq(SgBStoOut::getSourceBillId, request.getSourceBillId());
        stoOutWrapper.eq(SgBStoOut::getSourceBillType, request.getSourceBillType());
        stoOutWrapper.eq(SgBStoOut::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgBStoOut> stoOutList = stoOutMapper.selectListMaster(stoOutWrapper);
        if (CollectionUtils.isNotEmpty(stoOutList)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("已存在逻辑占用单，寻源失败");
            return v14;
        }
        List<SgBShareOutInfoResult> shareOutList =
                shareOutMapper.selectShareOutBySourceBillId(request.getSourceBillId(), request.getSourceBillType());
        if (CollectionUtils.isNotEmpty(shareOutList)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("已存在配销占用单，寻源失败");
            return v14;
        }
        v14.setData(request.getSkuItems());
        return v14;
    }

    /**
     * 根据指定的三级分货组织查询分货组织
     *
     * @param appointDistLevel3 三级分货组织编码
     * @param skuItems          订单行信息
     * @param sb                错误信息
     */
    private void queryDistByAppointDistLevel3(String appointDistLevel3,
                                              List<SkuItem2B> skuItems, StringBuilder sb) {
        Map<Integer, CpCDistributionOrganization> organizationMap = rpcCpService.queryDistAllParentByLevelAndCode(
                CpCDistributionOrganizationLevelEnum.LEVEL_3.getValue(), appointDistLevel3);
        log.info(LogUtil.format("SgFindSourceStrategyC2SCmdImpl.checkParam organizationMap:{}",
                "SgFindSourceStrategyC2SCmdImpl.checkParam"), JSONObject.toJSONString(organizationMap));
        CpCDistributionOrganization organizationLevel3 =
                organizationMap.get(CpCDistributionOrganizationLevelEnum.LEVEL_3.getValue());
        if (organizationLevel3 == null) {
            sb.append("指定的三级分货组织").append(appointDistLevel3).append("不存在！");
        }
        CpCDistributionOrganization organizationLevel2 =
                organizationMap.get(CpCDistributionOrganizationLevelEnum.LEVEL_2.getValue());
        if (organizationLevel2 == null) {
            sb.append("指定三级分货组织").append(appointDistLevel3).append("对应的二级分货组织不存在！");
        }
        CpCDistributionOrganization organizationLevel1 =
                organizationMap.get(CpCDistributionOrganizationLevelEnum.LEVEL_1.getValue());
        if (organizationLevel1 == null) {
            sb.append("指定三级分货组织").append(appointDistLevel3).append("对应的一级分货组织不存在！");
        }
        for (SkuItem2B skuItem2B : skuItems) {
            if (organizationLevel3 != null) {
                skuItem2B.setDistCodeLevelThree(organizationLevel3.getEcode());
                skuItem2B.setDistNameLevelThree(organizationLevel3.getEname());
            }
            if (organizationLevel2 != null) {
                skuItem2B.setDistCodeLevelTwo(organizationLevel2.getEcode());
                skuItem2B.setDistNameLevelTwo(organizationLevel2.getEname());
            }
            if (organizationLevel1 != null) {
                skuItem2B.setDistCodeLevelOne(organizationLevel1.getEcode());
                skuItem2B.setDistNameLevelOne(organizationLevel1.getEname());
            }
        }
    }

    /**
     * 逐行查询分货组织
     *
     * @param skuItems 订单行信息
     * @param sb       错误日志
     */
    private void queryDistByDepartCode(List<SkuItem2B> skuItems, StringBuilder sb) {
        for (SkuItem2B skuItem2B : skuItems) {
            Long sourceItemId = skuItem2B.getSourceItemId();
            if (StringUtils.isEmpty(skuItem2B.getCategoryCode())) {
                sb.append("来源明细id:").append(sourceItemId).append("零级编码不能为空");
                continue;
            }
            if (StringUtils.isEmpty(skuItem2B.getSalesDepartmentCode())) {
                sb.append("来源明细id:").append(sourceItemId).append("销售部门编码不能为空");
                continue;
            }
            if (StringUtils.isEmpty(skuItem2B.getSalesGroupCode())) {
                sb.append("来源明细id:").append(sourceItemId).append("销售组编码不能为空");
                continue;
            }
            //查询分货组织信息
            try {
                CpCSaleOrganizationQueryResult result = getSaleOrganizationQueryResult(skuItem2B);
                skuItem2B.setDistCodeLevelOne(result.getDistCodeLevelOne());
                skuItem2B.setDistNameLevelOne(result.getDistNameLevelOne());
                skuItem2B.setDistCodeLevelTwo(result.getDistCodeLevelTwo());
                skuItem2B.setDistNameLevelTwo(result.getDistNameLevelTwo());
                skuItem2B.setDistCodeLevelThree(result.getDistCodeLevelThree());
                skuItem2B.setDistNameLevelThree(result.getDistNameLevelThree());
            } catch (Exception e) {
                sb.append(e.getMessage());
            }
        }
    }

    /**
     * 查询行对应的销售组织架构
     *
     * @param skuItem
     * @return
     */
    private CpCSaleOrganizationQueryResult getSaleOrganizationQueryResult(SkuItem2B skuItem) {
        CpCSaleOrganizationQueryResult result;
        List<CpCSaleOrganizationQueryResult> organizationQueryResults =
                rpcCpService.querySaleOrganizationByDepartmentCode(skuItem.getCategoryCode(), skuItem.getSalesDepartmentCode());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(organizationQueryResults)) {
            throw new NDSException("商品零级【" + skuItem.getCategoryCode() + "】和销售部门【"
                    + skuItem.getSalesDepartmentCode() + "】未查询到销售组织架构！");
        }
        List<CpCSaleOrganizationQueryResult> resultByGroupCodeList = organizationQueryResults.stream()
                .filter(s -> skuItem.getSalesGroupCode().equals(s.getSalesGroupCode())).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(resultByGroupCodeList)) {
            if (resultByGroupCodeList.size() != 1) {
                throw new NDSException("商品零级【" + skuItem.getCategoryCode() + "】和销售组【"
                        + skuItem.getSalesGroupCode() + "】对应的销售组织架构不唯一！");
            }
            result = resultByGroupCodeList.get(0);
            if (StringUtils.isEmpty(result.getDistCodeLevelOne())) {
                throw new NDSException("商品零级【" + skuItem.getCategoryCode() + "】和销售组【"
                        + skuItem.getSalesGroupCode() + "】对应的销售组织架构的一级分货组织编码不存在或已作废！");
            }
            if (StringUtils.isEmpty(result.getDistNameLevelOne())) {
                throw new NDSException("商品零级【" + skuItem.getCategoryCode() + "】和销售组【"
                        + skuItem.getSalesGroupCode() + "】对应的销售组织架构的一级分货组织名称不存在或已作废！");
            }
            if (StringUtils.isEmpty(result.getDistCodeLevelTwo())) {
                throw new NDSException("商品零级【" + skuItem.getCategoryCode() + "】和销售组【"
                        + skuItem.getSalesGroupCode() + "】对应的销售组织架构的二级分货组织编码不存在或已作废！");
            }
            if (StringUtils.isEmpty(result.getDistNameLevelTwo())) {
                throw new NDSException("商品零级【" + skuItem.getCategoryCode() + "】和销售组【"
                        + skuItem.getSalesGroupCode() + "】对应的销售组织架构的二级分货组织名称不存在或已作废！");
            }
            if (StringUtils.isEmpty(result.getDistCodeLevelThree())) {
                throw new NDSException("商品零级【" + skuItem.getCategoryCode() + "】和销售组【"
                        + skuItem.getSalesGroupCode() + "】对应的销售组织架构的三级分货组织编码不存在或已作废！");
            }
            if (StringUtils.isEmpty(result.getDistNameLevelThree())) {
                throw new NDSException("商品零级【" + skuItem.getCategoryCode() + "】和销售组【"
                        + skuItem.getSalesGroupCode() + "】对应的销售组织架构的三级分货组织名称不存在或已作废！");
            }
        } else {
            List<CpCSaleOrganizationQueryResult> resultByGroupIsNullList = organizationQueryResults.stream()
                    .filter(s -> StringUtils.isEmpty(s.getSalesGroupCode())).collect(Collectors.toList());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(resultByGroupIsNullList)) {
                throw new NDSException("商品零级【" + skuItem.getCategoryCode() + "】和销售组【"
                        + skuItem.getSalesGroupCode() + "】未查询到销售组织架构！");
            } else {
                if (resultByGroupIsNullList.size() != 1) {
                    throw new NDSException("商品零级【" + skuItem.getCategoryCode() + "】和销售部门【"
                            + skuItem.getSalesDepartmentCode() + "】且销售组为空对应的销售组织架构不唯一！");
                } else {
                    result = resultByGroupIsNullList.get(0);
                    if (StringUtils.isEmpty(result.getDistCodeLevelOne())) {
                        throw new NDSException("商品零级【" + skuItem.getCategoryCode() + "】和销售部门【"
                                + skuItem.getSalesDepartmentCode() + "】对应的销售组织架构的一级分货组织编码不存在或已作废！");
                    }
                    if (StringUtils.isEmpty(result.getDistNameLevelOne())) {
                        throw new NDSException("商品零级【" + skuItem.getCategoryCode() + "】和销售部门【"
                                + skuItem.getSalesDepartmentCode() + "】对应的销售组织架构的一级分货组织名称不存在或已作废！");
                    }
                    if (StringUtils.isEmpty(result.getDistCodeLevelTwo())) {
                        throw new NDSException("商品零级【" + skuItem.getCategoryCode() + "】和销售部门【"
                                + skuItem.getSalesDepartmentCode() + "】对应的销售组织架构的二级分货组织编码不存在或已作废！");
                    }
                    if (StringUtils.isEmpty(result.getDistNameLevelTwo())) {
                        throw new NDSException("商品零级【" + skuItem.getCategoryCode() + "】和销售部门【"
                                + skuItem.getSalesDepartmentCode() + "】对应的销售组织架构的二级分货组织名称不存在或已作废！");
                    }
                    if (StringUtils.isEmpty(result.getDistCodeLevelThree())) {
                        throw new NDSException("商品零级【" + skuItem.getCategoryCode() + "】和销售部门【"
                                + skuItem.getSalesDepartmentCode() + "】对应的销售组织架构的三级分货组织编码不存在或已作废！");
                    }
                    if (StringUtils.isEmpty(result.getDistNameLevelThree())) {
                        throw new NDSException("商品零级【" + skuItem.getCategoryCode() + "】和销售部门【"
                                + skuItem.getSalesDepartmentCode() + "】对应的销售组织架构的三级分货组织名称不存在或已作废！");
                    }
                }
            }
        }
        return result;
    }

    /**
     * 根据二级分货组织查询比例同步策略
     *
     * @param distCodes
     * @param errorDistCodes
     * @return
     */
    private Map<String, List<SgCChannelRatioStrategyQueryInfoResult>> queryNormalRatioStrategy(
            List<String> distCodes, List<String> errorDistCodes) {
        SgCChannelRatioStrategyMapper ratioStrategyMapper =
                ApplicationContextHandle.getBean(SgCChannelRatioStrategyMapper.class);
        //查询比例同步策略
        List<SgCChannelRatioStrategyQueryInfoResult> resultList =
                ratioStrategyMapper.queryRatioStrategyByDists(distCodes);
        if (CollectionUtils.isEmpty(resultList)) {
            errorDistCodes.addAll(distCodes);
            return null;
        }
        Map<String, List<SgCChannelRatioStrategyQueryInfoResult>> normalRatioStrategyMap =
                resultList.stream().collect(Collectors.groupingBy(SgCChannelRatioStrategyQueryInfoResult::getDistCodeLevelTwo));
        distCodes.removeAll(normalRatioStrategyMap.keySet());
        if (CollectionUtils.isNotEmpty(distCodes)) {
            errorDistCodes.addAll(distCodes);
            return null;
        }
        return normalRatioStrategyMap;
    }

}