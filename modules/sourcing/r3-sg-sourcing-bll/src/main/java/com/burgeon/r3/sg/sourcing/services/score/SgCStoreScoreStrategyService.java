package com.burgeon.r3.sg.sourcing.services.score;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.r3.sg.basic.services.CpPhyWarehouseService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareScoreFactorStrategy;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.sourcing.storescore.SgCStoreScoreStrategy;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.sourcing.mapper.SgCShareScoreFactorStrategyMapper;
import com.burgeon.r3.sg.sourcing.mapper.storescore.SgCStoreScoreStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.request.score.SgCStoreScoreStrategyQueryRequest;
import com.burgeon.r3.sg.sourcing.model.request.score.SgCStoreScoreStrategySaveRequest;
import com.burgeon.r3.sg.sourcing.model.request.score.SgStoreScoreStrategySaveMainRequest;
import com.burgeon.r3.sg.sourcing.model.request.score.SgStoreScoreStrategySaveRequest;
import com.burgeon.r3.sg.sourcing.model.request.score.SgWarehouseScoreModel;
import com.burgeon.r3.sg.sourcing.model.result.score.SgCStoreScoreStrategyQueryResult;
import com.burgeon.r3.sg.sourcing.model.result.strategy.SgShareScoreFactorStrategyQueryResult;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/9 20:13
 */
@Slf4j
@Component
public class SgCStoreScoreStrategyService extends ServiceImpl<SgCStoreScoreStrategyMapper, SgCStoreScoreStrategy> {
    @Autowired
    private SgCStoreScoreStrategyMapper sgStoreScoreStrategyMapper;
    @Autowired
    private SgCShareScoreFactorStrategyMapper sgShareScoreFactorStrategyMapper;
    @Autowired
    private CpPhyWarehouseService cpPhyWarehouseService;


    /**
     * 查询评分因子表信息 用于表头展示
     *
     * @return ValueHolderV14<List < SgShareScoreFactorStrategyQueryResult>>
     */
    public ValueHolderV14<List<SgShareScoreFactorStrategyQueryResult>> queryScoreFactorList() {
        ValueHolderV14<List<SgShareScoreFactorStrategyQueryResult>> vh = new ValueHolderV14<>(ResultCode.SUCCESS,
                "查询成功!");
        List<SgShareScoreFactorStrategyQueryResult> strategyQueryResults =
                sgShareScoreFactorStrategyMapper.selectListScoreFactorStrategy();
        if (CollectionUtils.isEmpty(strategyQueryResults)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("评分因子设置表未维护数据！");
            return vh;
        }
        vh.setData(strategyQueryResults);
        return vh;
    }

    /**
     * 查询店仓评分设置表
     *
     * @param request 请求参数
     * @return ValueHolderV14<List < SgCStoreScoreStrategyQueryResult>>
     */
    public ValueHolderV14<PageInfo<SgCStoreScoreStrategyQueryResult>> queryByPhyWarehouseIds(SgCStoreScoreStrategyQueryRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("SgCStoreScoreStrategyServices.queryMatrix,storeIds:{},pageNum:{},pageSize:{}",
                    request.getPhyWarehouseIds(),
                    request.getPageNumber(), request.getPageSize());
        }
        ValueHolderV14<PageInfo<SgCStoreScoreStrategyQueryResult>> vh = new ValueHolderV14<>(ResultCode.SUCCESS,
                "查询成功!");
        //根据店仓id查询店仓评分表,不传全量查询
        List<SgWarehouseScoreModel> scoreInfos =
                sgStoreScoreStrategyMapper.queryByPhyWarehouseIds(request.getPhyWarehouseIds());
        int pageSize = request.getPageSize() == null ? SgConstants.SG_COMMON_MAX_QUERY_PAGE_SIZE :
                request.getPageSize();
        int pageNum = request.getPageNumber() == null ? 1 : request.getPageNumber();
        if (CollectionUtils.isNotEmpty(scoreInfos)) {
            List<SgCStoreScoreStrategyQueryResult> matrixResultList = new ArrayList<>();
            Map<Long, List<SgWarehouseScoreModel>> storeMap =
                    scoreInfos.stream().collect(Collectors.groupingBy(SgWarehouseScoreModel::getCpCPhyWarehouseId));
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (List<SgWarehouseScoreModel> scoreInfoList : storeMap.values()) {
                SgCStoreScoreStrategyQueryResult matrixResult = new SgCStoreScoreStrategyQueryResult();
                //获取最初创建人信息以及最近修改人信息
                SgCStoreScoreStrategy sgStoreScoreStrategy =
                        sgStoreScoreStrategyMapper.queryUserInfoByTime(scoreInfoList.get(0).getCpCPhyWarehouseId());
                if (sgStoreScoreStrategy != null) {
                    matrixResult.setOwnerename(sgStoreScoreStrategy.getOwnerename());
                    matrixResult.setModifierename(sgStoreScoreStrategy.getModifierename());
                    matrixResult.setModifieddate(simpleDateFormat.format(sgStoreScoreStrategy.getModifieddate()));
                    matrixResult.setCreationdate(simpleDateFormat.format(sgStoreScoreStrategy.getCreationdate()));
                }
                //总计
                BigDecimal sumScore = scoreInfoList.stream().map(SgWarehouseScoreModel::getScore)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                //评分map前端展示
                Map<String, BigDecimal> scoreMap = scoreInfoList.stream().collect(Collectors.toMap
                        (SgWarehouseScoreModel::getSgCShareScoreFactorStrategyEcode, SgWarehouseScoreModel::getScore));
                matrixResult.setScoreMap(scoreMap);
                Map<String, Long> scoreIdMap = scoreInfoList.stream().collect(Collectors.toMap
                        (SgWarehouseScoreModel::getSgCShareScoreFactorStrategyEcode, SgWarehouseScoreModel::getId));
                matrixResult.setIdMap(scoreIdMap);
                SgCStoreScoreStrategyQueryResult.PhyWarehouseInfo phyWarehouseInfo =
                        new SgCStoreScoreStrategyQueryResult.PhyWarehouseInfo();
                phyWarehouseInfo.setCpPhyWarehouseId(scoreInfoList.get(0).getCpCPhyWarehouseId());
                phyWarehouseInfo.setCpPhyWarehouseEcode(scoreInfoList.get(0).getCpCPhyWarehouseEcode());
                phyWarehouseInfo.setCpPhyWarehouseEname(scoreInfoList.get(0).getCpCPhyWarehouseEname());
                matrixResult.setPhyWarehouseInfo(phyWarehouseInfo);
                matrixResult.setSumScore(sumScore);
                matrixResultList.add(matrixResult);
            }
            //分页返回
            PageInfo<SgCStoreScoreStrategyQueryResult> resultPage = new PageInfo<>(matrixResultList);
            List<SgCStoreScoreStrategyQueryResult> listPaging = StorageUtils.getListPaging(matrixResultList, pageNum
                    , pageSize);
            //手动分页返回
            resultPage.setList(listPaging);
            vh.setData(resultPage);
        }
        return vh;
    }

    /**
     * 店仓评分设置表定制批量新增修改
     *
     * @param request 请求参数
     * @return ValueHolderV14<Long>
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<Long> saveStoreScoreStrategyBatch(SgCStoreScoreStrategySaveRequest request) {
        Long phyWarehouseId = request.getPhyWarehouseId();
        List<SgCStoreScoreStrategySaveRequest.SaveScoreInfo> saveScoreInfos = request.getSaveScoreInfos();
        if (CollectionUtils.isNotEmpty(saveScoreInfos)) {
            saveScoreInfos = saveScoreInfos.stream().filter(s -> s.getScore() != null).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(saveScoreInfos)) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("请输入评分信息后保存！"));
        }
        SgCpCPhyWarehouse phyWarehouseById = cpPhyWarehouseService.getPhyWarehouseById(phyWarehouseId);
        if (phyWarehouseById == null) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("实体仓信息已不存在！"));
        }
        ValueHolderV14<Long> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("保存成功！"));
        User user = request.getLoginUser();
        Map<String, SgCStoreScoreStrategy> scoreStrategyMap = new HashMap<>(16);
        //查询是否存在相同实体仓的评分信息  有则做更新操作
        List<SgCStoreScoreStrategy> scoreStrategyList =
                sgStoreScoreStrategyMapper.selectList(new LambdaQueryWrapper<SgCStoreScoreStrategy>()
                        .eq(SgCStoreScoreStrategy::getCpCPhyWarehouseId, phyWarehouseId)
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isNotEmpty(scoreStrategyList)) {
            scoreStrategyList.forEach(s -> scoreStrategyMap.put(s.getSgCShareScoreFactorStrategyEcode(), s));
        }

        if (CollectionUtils.isNotEmpty(saveScoreInfos)) {
            List<SgCStoreScoreStrategySaveRequest.SaveScoreInfo> minusList =
                    saveScoreInfos.stream().filter(s -> s.getScore() != null).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(minusList)) {
                List<SgCStoreScoreStrategySaveRequest.SaveScoreInfo> leZeroList =
                        minusList.stream().filter(m -> m.getScore().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leZeroList)) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("评分不能为负数！"));
                }
            }
            List<SgCStoreScoreStrategySaveRequest.SaveScoreInfo> updateScoreInfos =
                    saveScoreInfos.stream().filter(s -> s.getId() != null).collect(Collectors.toList());
            List<SgCStoreScoreStrategySaveRequest.SaveScoreInfo> insertScoreInfos =
                    saveScoreInfos.stream().filter(s -> s.getId() == null).collect(Collectors.toList());

            Map<String, SgCShareScoreFactorStrategy> factorStrategyMap = new HashMap<>(16);
            List<String> scoreFactorStrategyEcodeList =
                    saveScoreInfos.stream().map(SgCStoreScoreStrategySaveRequest.SaveScoreInfo::getScoreFactorEcode).collect(Collectors.toList());
            //查询评分因子表信息
            List<SgCShareScoreFactorStrategy> shareScoreFactorStrategies =
                    sgShareScoreFactorStrategyMapper.selectList(new LambdaQueryWrapper<SgCShareScoreFactorStrategy>()
                            .in(CollectionUtils.isNotEmpty(scoreFactorStrategyEcodeList),
                                    SgCShareScoreFactorStrategy::getEcode, scoreFactorStrategyEcodeList)
                            .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (CollectionUtils.isNotEmpty(shareScoreFactorStrategies)) {
                shareScoreFactorStrategies.forEach(s -> factorStrategyMap.put(s.getEcode(), s));
            }
            List<SgCStoreScoreStrategy> updateList = new ArrayList<>();
            List<SgCStoreScoreStrategy> insertList = new ArrayList<>();
            try {
                if (CollectionUtils.isNotEmpty(insertScoreInfos)) {
                    insertStoreScorce(insertScoreInfos, factorStrategyMap, scoreStrategyMap, user, phyWarehouseById,
                            insertList, updateList);
                }
                if (CollectionUtils.isNotEmpty(updateScoreInfos)) {
                    updateStoreScorce(updateScoreInfos, user, phyWarehouseById, updateList);
                }
                if (CollectionUtils.isNotEmpty(updateList)) {
                    this.updateBatchById(updateList);
                }
                if (CollectionUtils.isNotEmpty(insertList)) {
                    this.saveBatch(insertList);
                }
            } catch (Exception e) {
                AssertUtils.logAndThrowException("店仓评分设置表保存失败！", e, request.getLoginUser().getLocale());
            }
        }
        //返回实体仓id  用于前端调用查询
        v14.setData(phyWarehouseId);
        return v14;
    }

    /**
     * 收集新增信息
     *
     * @param insertScoreInfos  前台传输新增数据
     * @param factorStrategyMap 评分因子map
     * @param scoreStrategyMap  店仓评分map
     * @param user              用户
     * @param phyWarehouseById  实体仓
     * @param insertList        新增数据
     * @param updateList        修改数据
     */
    private void insertStoreScorce(List<SgCStoreScoreStrategySaveRequest.SaveScoreInfo> insertScoreInfos, Map<String,
            SgCShareScoreFactorStrategy> factorStrategyMap, Map<String, SgCStoreScoreStrategy> scoreStrategyMap,
                                   User user, SgCpCPhyWarehouse phyWarehouseById,
                                   List<SgCStoreScoreStrategy> insertList,
                                   List<SgCStoreScoreStrategy> updateList) {
        for (SgCStoreScoreStrategySaveRequest.SaveScoreInfo saveScoreInfo : insertScoreInfos) {
            SgCStoreScoreStrategy updateStoreScore = scoreStrategyMap.get(saveScoreInfo.getScoreFactorEcode());
            if (updateStoreScore != null) {
                updateStoreScore.setScore(saveScoreInfo.getScore());
                StorageUtils.setBModelDefalutDataByUpdate(updateStoreScore, user);
                updateStoreScore.setModifierename(user.getEname());
                updateList.add(updateStoreScore);
            } else {
                if (saveScoreInfo.getScore() != null) {
                    SgCStoreScoreStrategy insertStoreScore = new SgCStoreScoreStrategy();
                    insertStoreScore.setId(ModelUtil.getSequence(SgConstants.SG_C_STORE_SCORE_STRATEGY));
                    insertStoreScore.setCpCPhyWarehouseId(phyWarehouseById.getId());
                    insertStoreScore.setCpCPhyWarehouseEcode(phyWarehouseById.getEcode());
                    insertStoreScore.setCpCPhyWarehouseEname(phyWarehouseById.getEname());
                    StorageUtils.setBModelDefalutData(insertStoreScore, user);
                    insertStoreScore.setModifierename(user.getEname());
                    insertStoreScore.setOwnerename(user.getEname());
                    SgCShareScoreFactorStrategy factorStrategy =
                            factorStrategyMap.get(saveScoreInfo.getScoreFactorEcode());
                    insertStoreScore.setScore(saveScoreInfo.getScore());
                    if (factorStrategy != null) {
                        insertStoreScore.setSgCShareScoreFactorStrategyId(factorStrategy.getId());
                        insertStoreScore.setSgCShareScoreFactorStrategyEcode(factorStrategy.getEcode());
                        insertStoreScore.setSgCShareScoreFactorStrategyEname(factorStrategy.getEname());
                    } else {
                        AssertUtils.logAndThrow("评分因子设置表编码为:" + saveScoreInfo.getScoreFactorEcode() + "数据已不存在！");
                    }
                    insertList.add(insertStoreScore);
                }
            }
        }
    }

    /**
     * 收集修改信息
     *
     * @param updateScoreInfos 前台传输修改数据
     * @param user             用户
     * @param phyWarehouseById 实体仓信息
     * @param updateList       更新数据
     */
    private void updateStoreScorce(List<SgCStoreScoreStrategySaveRequest.SaveScoreInfo> updateScoreInfos, User user,
                                   SgCpCPhyWarehouse phyWarehouseById, List<SgCStoreScoreStrategy> updateList) {
        for (SgCStoreScoreStrategySaveRequest.SaveScoreInfo saveScoreInfo : updateScoreInfos) {
            SgCStoreScoreStrategy updateStoreScore = new SgCStoreScoreStrategy();
            updateStoreScore.setId(saveScoreInfo.getId());
            updateStoreScore.setCpCPhyWarehouseId(phyWarehouseById.getId());
            updateStoreScore.setCpCPhyWarehouseEcode(phyWarehouseById.getEcode());
            updateStoreScore.setCpCPhyWarehouseEname(phyWarehouseById.getEname());
            StorageUtils.setBModelDefalutDataByUpdate(updateStoreScore, user);
            updateStoreScore.setModifierename(user.getEname());
            updateStoreScore.setScore(saveScoreInfo.getScore());
            updateList.add(updateStoreScore);
        }
    }

    /**
     * 店仓评分设置表根据店仓storeIds删除数据
     *
     * @param phyWarehouseIds 实体仓id集合
     * @return ValueHolderV14
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 deleteByPhyWarehouseIds(List<Long> phyWarehouseIds) {
        ValueHolderV14 vh = new ValueHolderV14<>(ResultCode.SUCCESS, "删除成功!");
        if (CollectionUtils.isNotEmpty(phyWarehouseIds)) {
            if (log.isDebugEnabled()) {
                log.debug("SgCStoreScoreStrategyService.deleteByPhyWarehouseIds:{}",
                        JSONObject.toJSONString(phyWarehouseIds));
            }
            sgStoreScoreStrategyMapper.delete(new LambdaQueryWrapper<SgCStoreScoreStrategy>()
                    .in(SgCStoreScoreStrategy::getCpCPhyWarehouseId, phyWarehouseIds)
                    .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("删除成功！");
        }
        return vh;
    }

    /**
     * 框架页面新增(提供给导入服务)
     *
     * @param session session
     * @return ValueHolder
     */
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(" Start SgCStoreScoreStrategyService.execute param={}", JSONObject.toJSONString(param));
        }
        SgStoreScoreStrategySaveRequest request = R3ParamUtils.parseSaveObject(session, SgStoreScoreStrategySaveRequest.class);
        request.setR3(true);
        SgCStoreScoreStrategyService service = ApplicationContextHandle.getBean(SgCStoreScoreStrategyService.class);
        return R3ParamUtils.convertV14WithResult(service.save(request));
    }

    /**
     * 新增保存(提供给导入服务)
     *
     * @param request 请求参数
     * @return ValueHolderV14
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> save(SgStoreScoreStrategySaveRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(" Start SgCStoreScoreStrategyService.save mainRequest={}", JSONObject.toJSONString(request));
        }
        SgStoreScoreStrategySaveMainRequest mainRequest = request.getMainRequest();
        Long phyWarehouseId = mainRequest.getCpCPhyWarehouseId();
        Long strategyId = mainRequest.getSgCShareScoreFactorStrategyId();
        if (Objects.isNull(phyWarehouseId) || Objects.isNull(strategyId)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "实体仓信息或评分因子信息不能为空!");
        }
        //查询是否存在相同实体仓的评分信息  有则做更新操作
        SgCStoreScoreStrategy scoreStrategy =
                sgStoreScoreStrategyMapper.selectOne(new LambdaQueryWrapper<SgCStoreScoreStrategy>()
                        .eq(SgCStoreScoreStrategy::getCpCPhyWarehouseId, phyWarehouseId)
                        .eq(SgCStoreScoreStrategy::getSgCShareScoreFactorStrategyId, strategyId)
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        SgCStoreScoreStrategy saveStrategy = new SgCStoreScoreStrategy();
        try {
            if (Objects.nonNull(scoreStrategy)) {
                saveStrategy.setId(scoreStrategy.getId());
                StorageUtils.setBModelDefalutDataByUpdate(saveStrategy, request.getLoginUser());
                saveStrategy.setScore(Optional.ofNullable(mainRequest.getScore()).orElse(BigDecimal.ZERO));
                sgStoreScoreStrategyMapper.updateById(saveStrategy);
                return new ValueHolderV14<>(ResultCode.SUCCESS, "保存成功!");
            }
            SgCpCPhyWarehouse phyWarehouseById = cpPhyWarehouseService.getPhyWarehouseById(phyWarehouseId);
            if (Objects.isNull(phyWarehouseById)) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("实体仓信息已不存在！"));
            }

            SgCShareScoreFactorStrategy scoreFactorStrategy =
                    sgShareScoreFactorStrategyMapper.selectById(mainRequest.getSgCShareScoreFactorStrategyId());
            if (Objects.isNull(scoreFactorStrategy)) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("评分因子信息已不存在！"));
            }
            saveStrategy.setId(ModelUtil.getSequence(SgConstants.SG_C_SHARE_SCORE_FACTOR_STRATEGY));
            StorageUtils.setBModelDefalutData(saveStrategy, request.getLoginUser());
            saveStrategy.setCpCPhyWarehouseId(phyWarehouseById.getId());
            saveStrategy.setCpCPhyWarehouseEname(phyWarehouseById.getEname());
            saveStrategy.setCpCPhyWarehouseEcode(phyWarehouseById.getEcode());
            saveStrategy.setSgCShareScoreFactorStrategyId(scoreFactorStrategy.getId());
            saveStrategy.setSgCShareScoreFactorStrategyEcode(scoreFactorStrategy.getEcode());
            saveStrategy.setSgCShareScoreFactorStrategyEname(scoreFactorStrategy.getEname());
            saveStrategy.setScore(Optional.ofNullable(mainRequest.getScore()).orElse(BigDecimal.ZERO));
            sgStoreScoreStrategyMapper.insert(saveStrategy);
        } catch (Exception e) {
            log.error(" SgCStoreScoreStrategyService.save exception_has_occured:{}",
                    Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrowException(e, request.getLoginUser().getLocale());
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, "保存成功!");
    }
}