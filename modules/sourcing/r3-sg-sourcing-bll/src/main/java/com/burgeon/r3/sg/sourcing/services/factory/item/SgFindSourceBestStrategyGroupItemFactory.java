package com.burgeon.r3.sg.sourcing.services.factory.item;


import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategy;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgCselectForeceStrategyInfoResult;
import com.burgeon.r3.sg.sourcing.model.result.StrategyFactoryBaseResult;
import com.burgeon.r3.sg.sourcing.model.result.factory.SgFindSourceStrategyFactoryResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.services.factory.StrategyFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description: 寻源规则策略组工厂类
 * @author: hwy
 * @time: 2021/6/9 16:10
 */

@Component
@Slf4j
@Data
public class SgFindSourceBestStrategyGroupItemFactory extends StrategyFactory {

    private List<? extends StrategyFactoryBaseResult> strategiesList;

    private StrategyHandle handle;

    @Autowired
    private SgCChannelSourceStrategyMapper strategyMapper;

    /**
     * 寻源规则策略组使用非单例模式
     *
     * @return
     */
    @Override
    public Boolean isSingletonPattern() {
        return Boolean.FALSE;
    }

    @Override
    public List<SgFindSourceStrategyFactoryResult> getStrategies(StrategyBaseRequest request) {

        ArrayList<SgFindSourceStrategyFactoryResult> results = new ArrayList<>();
        SgFindSourceStrategyFactoryResult result = null;

        result = new SgFindSourceStrategyFactoryResult();
        result.setStrategyName("临近大效期策略");
        result.setStrategyClazz("com.burgeon.r3.sg.sourcing.services.item.SgFindSourceNearExpiryDateStrategyService");
        result.setPriority(10);
        results.add(result);

        result = new SgFindSourceStrategyFactoryResult();
        result.setStrategyName("仓优先策略");
        result.setStrategyClazz("com.burgeon.r3.sg.sourcing.services.item.SgFindSourceWarehousePriorityStrategyService");
        result.setPriority(20);
        results.add(result);

        //获取店铺对应的寻源规则
        SgFindSourceStrategyS2LRequest strategyRequest = (SgFindSourceStrategyS2LRequest) request;

        List<SgCselectForeceStrategyInfoResult> strategyInfoResults = strategyMapper.selectRuleStrategyInfo(strategyRequest.getSourceStrategy().getId());

        if (CollectionUtils.isNotEmpty(strategyInfoResults)) {
            for (SgCselectForeceStrategyInfoResult strategyInfoResult : strategyInfoResults) {
                result = new SgFindSourceStrategyFactoryResult();
                //临时解决寻源规则策略最后执行
                result.setPriority(strategyInfoResult.getPriority() + 100);
                result.setStrategyClazz(strategyInfoResult.getClassPath());
                result.setStrategyName(strategyInfoResult.getStrategyName());
                results.add(result);
            }
        }

        return results;
    }
}