package com.burgeon.r3.sg.sourcing.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.basic.model.SgStorageRedisQueryLsModel;
import com.burgeon.r3.sg.basic.model.request.SgStorageQuerySaRequest;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQueryResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySaResult;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.basic.utils.BusinessSyetemParamConfigUtils;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOut;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOut;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutItemMapper;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutMapper;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutBillSaveRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutItemLogSaveRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutItemSaveRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutSaveRequest;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutSaveResult;
import com.burgeon.r3.sg.share.services.out.SgBShareOutSaveService;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStickerRequest;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutMapper;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutItemLogSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutSaveRequest;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutBillSaveResult;
import com.burgeon.r3.sg.store.services.out.SgBStoOutSaveService;
import com.google.common.base.Throwables;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: hwy
 * @time: 2021/6/22 10:30
 */
@Component
@Slf4j
public class SgFindSourceStickerService {

    @Autowired
    private SgBStoOutMapper stoOutMapper;
    @Autowired
    private SgBStoOutItemMapper stoOutItemMapper;
    @Autowired
    private SgBShareOutMapper shareOutMapper;
    @Autowired
    private SgBShareOutItemMapper shareOutItemMapper;
    @Autowired
    private SgCSaStoreMapper saStoreMapper;
    @Autowired
    private SgStorageQueryService storageQueryService;


    public ValueHolderV14 findSourceSticker(SgFindSourceStickerRequest request) {

        ValueHolderV14 valueHolderV14 = this.checkParam(request);
        if (!valueHolderV14.isOK()) {
            return valueHolderV14;
        }

        SgFindSourceStickerService bean = ApplicationContextHandle.getBean(this.getClass());
        try {
            return bean.exec(request);
        } catch (Exception e) {
            log.error("SgFindSource2BService.findSource2B error:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("寻源失败：" + e.getMessage());
            return valueHolderV14;
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 exec(SgFindSourceStickerRequest request) {
        log.info(LogUtil.format("request:{}",
                "SgFindSourceStickerService.exec", request.getSourceBillNo()), JSONObject.toJSONString(request));

        LambdaQueryWrapper<SgBStoOut> stoOutWrapper = new LambdaQueryWrapper<>();
        stoOutWrapper.eq(SgBStoOut::getSourceBillId, request.getSourceBillId());
        stoOutWrapper.eq(SgBStoOut::getSourceBillType, request.getSourceBillType());
        stoOutWrapper.eq(SgBStoOut::getSourceBillNo, request.getSourceBillNo());
        stoOutWrapper.eq(SgBStoOut::getIsactive, SgConstants.IS_ACTIVE_Y);
        SgBStoOut stoOut = stoOutMapper.selectOne(stoOutWrapper);
        if (stoOut == null) {
            return new ValueHolderV14(ResultCode.FAIL, "未查询到逻辑占用单");
        }
        LambdaQueryWrapper<SgBStoOutItem> stoOutItemWrapper = new LambdaQueryWrapper<>();
        stoOutItemWrapper.eq(SgBStoOutItem::getSgBStoOutId, stoOut.getId());
        stoOutItemWrapper.eq(SgBStoOutItem::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgBStoOutItem> stoOutItemList = stoOutItemMapper.selectList(stoOutItemWrapper);
        if (CollectionUtils.isEmpty(stoOutItemList)) {
            return new ValueHolderV14(ResultCode.FAIL, "未查询到逻辑占用单明细");
        }

        List<Long> storeIdList = stoOutItemList.stream()
                .map(SgBStoOutItem::getCpCStoreId).distinct().collect(Collectors.toList());
        // 查询逻辑仓库存
        List<SgStorageRedisQueryLsModel> storageQueryModelList = new ArrayList<>();
        request.getItemList().forEach(y ->
                storeIdList.forEach(x -> {
                    SgStorageRedisQueryLsModel model = new SgStorageRedisQueryLsModel();
                    model.setCpCStoreId(x);
                    model.setProduceDate(SgConstantsIF.DEFAULT_PRODUCE_DATE);
                    model.setPsCSkuId(y.getPsCSkuId());
                    storageQueryModelList.add(model);
                }));
        ValueHolderV14<HashMap<String, SgStorageRedisQueryResult>> lsStorageV14 =
                storageQueryService.queryLsStorageWithRedis(storageQueryModelList, request.getLoginUser());

        if (lsStorageV14 == null || !lsStorageV14.isOK() || MapUtils.isEmpty(lsStorageV14.getData())) {
            return new ValueHolderV14(ResultCode.FAIL, "逻辑仓库存查询失败");
        }

        List<Long> storeId = new ArrayList<>();
        lsStorageV14.getData().forEach((k, v) -> {
            if (v.getQtyAvailable().compareTo(BigDecimal.ZERO) > 0) {
                storeId.add(v.getCpCStoreId());
            }
        });

        if (CollectionUtils.isEmpty(storeId)) {
            return new ValueHolderV14(ResultCode.FAIL, "逻辑仓库存不足！");
        }
        // 随机取一个逻辑仓进行占用(理论上只有一个)
        Long randomStoreId = storeId.get(0);
        CpCStore store = CommonCacheValUtils.getStoreInfo(randomStoreId);

        // 查询配销占用单
        LambdaQueryWrapper<SgBShareOut> shareWrapper = new LambdaQueryWrapper<>();
        shareWrapper.eq(SgBShareOut::getSourceBillType, request.getSourceBillType());
        shareWrapper.eq(SgBShareOut::getSourceBillId, request.getSourceBillId());
        shareWrapper.eq(SgBShareOut::getSourceBillNo, request.getSourceBillNo());
        shareWrapper.eq(SgBShareOut::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgBShareOut> shareOutList = shareOutMapper.selectList(shareWrapper);
        AssertUtils.cannot(CollectionUtils.isEmpty(shareOutList), "未查询到配销占用单！");

        // 获取贴纸策略可以寻源的配销仓编码列表
        String sharedSaStoreCodes = BusinessSyetemParamConfigUtils.stickerSharedSaStoreCode();
        if (StringUtils.isEmpty(sharedSaStoreCodes)) {
            return new ValueHolderV14(ResultCode.FAIL, "贴纸策略可以寻源的配销仓编码为空");
        }

        // 根据逻辑仓所属聚合仓和业务系统参数查询配销仓档案
        LambdaQueryWrapper<SgCSaStore> saWrapper = new LambdaQueryWrapper<>();
        saWrapper.eq(SgCSaStore::getSgCShareStoreId, store.getSgCShareStoreId());
        saWrapper.eq(SgCSaStore::getIsactive, SgConstants.IS_ACTIVE_Y);
        saWrapper.in(SgCSaStore::getEcode, sharedSaStoreCodes);
        SgCSaStore saStore = saStoreMapper.selectOne(saWrapper);
        if (saStore == null) {
            return new ValueHolderV14(ResultCode.FAIL, "配销仓[" + sharedSaStoreCodes + "]不存在！");
        }

        // 查询配销仓库存
        SgStorageQuerySaRequest saStorageQueryRequest = new SgStorageQuerySaRequest();
        saStorageQueryRequest.setSgCSaStoreIds(Lists.newArrayList(saStore.getId()));
        List<Long> psCSkuIdList = request.getItemList().stream()
                .map(SgFindSourceStickerRequest.SgFindSourceStickerItemRequest::getPsCSkuId).collect(Collectors.toList());
        saStorageQueryRequest.setSkuIds(psCSkuIdList);
        ValueHolderV14<List<SgStorageRedisQuerySaResult>> saStorageV14 =
                storageQueryService.querySaStorageWithRedis(saStorageQueryRequest, request.getLoginUser());
        if (!saStorageV14.isOK() || CollectionUtils.isEmpty(saStorageV14.getData())) {
            return new ValueHolderV14(ResultCode.FAIL, "查询配销仓库存失败");
        }
        List<SgStorageRedisQuerySaResult> saStorageResult = saStorageV14.getData().stream()
                .filter(x -> x.getQtyAvailable().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(saStorageResult)) {
            return new ValueHolderV14(ResultCode.FAIL, "配销仓库存不足！");
        }

        ValueHolderV14<SgBStoOutBillSaveResult> valueHolderV14 = stoOutService(randomStoreId, request);
        if (!valueHolderV14.isOK()) {
            log.error("SgFindSource2BService.exec stoOutService result error:{}", JSONObject.toJSONString(valueHolderV14));
            return valueHolderV14;
        }
        List<String> redisKey = valueHolderV14.getData().getRedisKey();

        try {
            ValueHolderV14 shareResultV14 = shareOutService(saStorageResult.get(0).getSgCSaStoreId(), store.getSgCShareStoreId(), request);
            AssertUtils.isTrue(shareResultV14.isOK(), "配销占用单生成失败");
        } catch (Exception e) {
            // 回滚逻辑占用单
            StorageBasicUtils.rollbackStorage(redisKey, request.getLoginUser());

            AssertUtils.logAndThrow("占用库存异常：" + e.getMessage());
        }

        return new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
    }

    /**
     * 配销占用单
     *
     * @param saStoreId 配销仓ID
     * @param shareStoreId 聚合仓ID
     * @param request 请求参数
     */
    private ValueHolderV14 shareOutService(Long saStoreId, Long shareStoreId, SgFindSourceStickerRequest request) {

        // 定义配销占用单变量
        SgBShareOutBillSaveRequest shareOutBillSaveRequest = new SgBShareOutBillSaveRequest();
        SgBShareOutSaveRequest shareOutSaveRequest = new SgBShareOutSaveRequest();
        List<SgBShareOutItemSaveRequest> shareOutItemSaveRequestList = new ArrayList<>();
        List<SgBShareOutItemLogSaveRequest> shareOutItemLogSaveRequestList = new ArrayList<>();
        shareOutBillSaveRequest.setLoginUser(request.getLoginUser());
        shareOutBillSaveRequest.setShareOutSaveRequest(shareOutSaveRequest);
        shareOutBillSaveRequest.setShareOutItemSaveRequestList(shareOutItemSaveRequestList);
        shareOutBillSaveRequest.setShareOutItemLogSaveRequestList(shareOutItemLogSaveRequestList);
        // 配销占用单改成逻辑占用单一样，共用一张占用单
        shareOutBillSaveRequest.setIsSourceMerge(Boolean.TRUE);

        // 设置主表信息
        shareOutSaveRequest.setSgCShareStoreId(shareStoreId);
        shareOutSaveRequest.setSourceBillId(request.getSourceBillId());
        shareOutSaveRequest.setSourceBillNo(request.getSourceBillNo());
        shareOutSaveRequest.setSourceBillType(request.getSourceBillType());
        shareOutSaveRequest.setServiceNode(SgConstantsIF.SERVICE_NODE_SHARE_OUT_SAVE);
        shareOutSaveRequest.setSourceBillDate(request.getBillDate());
        shareOutSaveRequest.setCpCShopId(request.getShopId());
        shareOutSaveRequest.setTid(request.getTid());
        shareOutSaveRequest.setMergeMark(null);
        request.getItemList().forEach(x -> {
            SgBShareOutItemSaveRequest itemRequest = new SgBShareOutItemSaveRequest();
            itemRequest.setQty(x.getQty());
            itemRequest.setPsCSkuId(x.getPsCSkuId());
            itemRequest.setQtyPreout(x.getQty());
            itemRequest.setSourceBillItemId(x.getSourceBillItemId());
            itemRequest.setSgCSaStoreId(saStoreId);
            itemRequest.setSourceStorage(SgConstants.SHARE_OUT_ITEM_STOCK_SOURCE_SA);
            shareOutItemSaveRequestList.add(itemRequest);
        });
        request.getItemList().forEach(x -> {
            SgBShareOutItemLogSaveRequest itemLogRequest = new SgBShareOutItemLogSaveRequest();
            itemLogRequest.setQty(x.getQty());
            itemLogRequest.setPsCSkuId(x.getPsCSkuId());
            itemLogRequest.setQtyPreout(x.getQty());
            itemLogRequest.setSourceBillItemId(x.getSourceBillItemId());
            itemLogRequest.setSgCSaStoreId(saStoreId);
            itemLogRequest.setSourceStorage(SgConstants.SHARE_OUT_ITEM_STOCK_SOURCE_SA);
            shareOutItemLogSaveRequestList.add(itemLogRequest);
        });

        if (log.isDebugEnabled()) {
            log.debug("SgFindSource2BService.shareOutService shareOutBillSaveRequest:{}", JSONObject.toJSONString(shareOutBillSaveRequest));
        }

        SgBShareOutSaveService shareOutSaveService = ApplicationContextHandle.getBean(SgBShareOutSaveService.class);
        ValueHolderV14<SgBShareOutSaveResult> shareOutResultV14 = shareOutSaveService.redundantSgShareOut(shareOutBillSaveRequest);
        if (shareOutResultV14 == null || !shareOutResultV14.isOK() || shareOutResultV14.getData() == null) {
            log.error("配销层处理失败：{}", JSONObject.toJSONString(shareOutResultV14));
            return new ValueHolderV14(ResultCode.FAIL, "生成配销占用单失败");
        }
        return new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
    }

    /**
     * 逻辑占用单
     *
     * @param storeId
     * @param request
     * @return
     */
    private ValueHolderV14<SgBStoOutBillSaveResult> stoOutService(Long storeId, SgFindSourceStickerRequest request) {


        // 生成逻辑占用单参数
        SgBStoOutBillSaveRequest stoOutBillSaveRequest = new SgBStoOutBillSaveRequest();
        SgBStoOutSaveRequest stoOutSaveRequest = new SgBStoOutSaveRequest();

        List<SgBStoOutItemSaveRequest> stoOutItemSaveRequestList = new ArrayList<>();
        List<SgBStoOutItemLogSaveRequest> stoOutItemLogSaveRequestList = new ArrayList<>();

        // 设置request 信息
        stoOutBillSaveRequest.setUpdateMethod(SgConstantsIF.ITEM_UPDATE_TYPE_INC);
        stoOutBillSaveRequest.setIsEdge(Boolean.TRUE);
        stoOutBillSaveRequest.setIsForceUnNegative(Boolean.TRUE);
        stoOutBillSaveRequest.setLoginUser(request.getLoginUser());
        stoOutBillSaveRequest.setPreoutWarningType(SgConstantsIF.PREOUT_RESULT_ERROR);
        stoOutBillSaveRequest.setIsCancel(Boolean.FALSE);
        stoOutBillSaveRequest.setIsSourceMerge(Boolean.TRUE);
        stoOutBillSaveRequest.setSgBStoOutSaveRequest(stoOutSaveRequest);
        stoOutBillSaveRequest.setSgBStoOutItemSaveRequests(stoOutItemSaveRequestList);
        stoOutBillSaveRequest.setSgBStoOutItemLogSaveRequests(stoOutItemLogSaveRequestList);

        // 设置主表信息
        stoOutSaveRequest.setSourceBillId(request.getSourceBillId());
        stoOutSaveRequest.setSourceBillNo(request.getSourceBillNo());
        stoOutSaveRequest.setSourceBillType(request.getSourceBillType());
        stoOutSaveRequest.setBillDate(request.getBillDate());
        stoOutSaveRequest.setCpCShopId(request.getShopId());
        stoOutSaveRequest.setTid(request.getTid());
        stoOutSaveRequest.setMergeMark(null);
        request.getItemList().forEach(x -> {
            SgBStoOutItemSaveRequest itemSaveRequest = new SgBStoOutItemSaveRequest();
            stoOutItemSaveRequestList.add(itemSaveRequest);
            itemSaveRequest.setQty(x.getQty());
            itemSaveRequest.setProduceDate(SgConstantsIF.DEFAULT_PRODUCE_DATE);
            itemSaveRequest.setQtyPreout(x.getQty());
            itemSaveRequest.setCpCStoreId(storeId);
            itemSaveRequest.setSourceBillItemId(x.getSourceBillItemId());
            itemSaveRequest.setPsCSkuId(x.getPsCSkuId());
            itemSaveRequest.setTid(request.getTid());
//            itemSaveRequest.setBeginProduceDate(SgConstantsIF.DEFAULT_PRODUCE_DATE);
//            itemSaveRequest.setEndProduceDate(SgConstantsIF.DEFAULT_PRODUCE_DATE);

        });
        request.getItemList().forEach(x -> {
            SgBStoOutItemLogSaveRequest itemLogSaveRequest = new SgBStoOutItemLogSaveRequest();
            stoOutItemLogSaveRequestList.add(itemLogSaveRequest);
            itemLogSaveRequest.setQty(x.getQty());
            itemLogSaveRequest.setProduceDate(SgConstantsIF.DEFAULT_PRODUCE_DATE);
            itemLogSaveRequest.setQtyPreout(x.getQty());
            itemLogSaveRequest.setCpCStoreId(storeId);
            itemLogSaveRequest.setSourceBillItemId(x.getSourceBillItemId());
            itemLogSaveRequest.setPsCSkuId(x.getPsCSkuId());
            itemLogSaveRequest.setTid(request.getTid());
//            itemLogSaveRequest.setBeginProduceDate(SgConstantsIF.DEFAULT_PRODUCE_DATE);
//            itemLogSaveRequest.setEndProduceDate(SgConstantsIF.DEFAULT_PRODUCE_DATE);

        });

        if (log.isDebugEnabled()) {
            log.debug("SgFindSource2BService.stoOutService stoOutBillSaveRequest:{}", JSONObject.toJSONString(stoOutBillSaveRequest));
        }

        SgBStoOutSaveService stoOutSaveService = ApplicationContextHandle.getBean(SgBStoOutSaveService.class);
        ValueHolderV14<SgBStoOutBillSaveResult> stoOutResultV14 = stoOutSaveService.saveSgStoOut(stoOutBillSaveRequest);
        if (stoOutResultV14 == null || !stoOutResultV14.isOK() || stoOutResultV14.getData() == null) {
            log.error("逻辑层处理失败：", JSONObject.toJSONString(stoOutResultV14));
            return new ValueHolderV14<>(ResultCode.FAIL, "生成逻辑占用单失败");
        }

        return stoOutResultV14;
    }

    private ValueHolderV14 checkParam(SgFindSourceStickerRequest request) {
        if (request == null) {
            return new ValueHolderV14(ResultCode.FAIL, "请求参数不能为空");
        }
        if (request.getSourceBillId() == null || StringUtils.isEmpty(request.getSourceBillNo()) || request.getSourceBillType() == null) {
            return new ValueHolderV14(ResultCode.FAIL, "来源单据信息不能为空");
        }
        if (CollectionUtils.isEmpty(request.getItemList())) {
            return new ValueHolderV14(ResultCode.FAIL, "明细信息不能为空");
        }

        if (request.getLoginUser() == null) {
            return new ValueHolderV14(ResultCode.FAIL, "用户不能为空");
        }

        return new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
    }

}