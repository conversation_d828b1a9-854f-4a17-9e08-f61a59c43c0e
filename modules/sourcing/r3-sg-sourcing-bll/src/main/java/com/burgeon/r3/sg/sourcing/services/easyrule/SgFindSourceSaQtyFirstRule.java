package com.burgeon.r3.sg.sourcing.services.easyrule;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyMapper;
import com.burgeon.r3.sg.channel.model.request.strategy.SgCChanelQtyStrategyQueryInfoRequest;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChanelQtyStrategyQueryInfoResult;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyC2SRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemC2S;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyC2SResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuC2SResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemC2SResult;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jeasy.rules.annotation.*;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 通过渠道锁定库存策略设置 对订单明细的归属仓排序
 * @author: hwy
 * @time: 2021/6/21 14:13
 */

@Rule
@Slf4j
@Component
@Deprecated
public class SgFindSourceSaQtyFirstRule {


    private static final int PRIORITY = 1;

    @Priority
    public int getPriority() {
        return PRIORITY;
    }

    @Condition
    public boolean enableRule(@Fact("request") SgFindSourceStrategyC2SRequest request) {
        FindSourceStrategyUtils.outputLog("C->S寻源策略 寻源引擎 配销仓数量优先规则 匹配成功");
        return true;
    }

    @Action
    public void doRule(@Fact("request") SgFindSourceStrategyC2SRequest request) {

        log.info("SgFindSourceSaQtyFirstRule.doRule Start 来源单据id:{}", request.getSourceBillId());

        //策略执行结果
        SgFindSourceStrategyC2SResult strategyResult = (SgFindSourceStrategyC2SResult) request.getStrategyBaseResult();
        //策略执行结果明细
        List<SgFindSourceStrategySkuC2SResult> skuResultList = CollectionUtils.isEmpty(strategyResult.getSkuResultList()) ? new ArrayList<>() : strategyResult.getSkuResultList();
        strategyResult.setSkuResultList(skuResultList);
        //策略执行结果明细按 来源明细id 聚合
        Map<Long, SgFindSourceStrategySkuC2SResult> skuResultMap = skuResultList.stream().collect(Collectors.toMap(SgFindSourceStrategySkuC2SResult::getSourceItemId, Function.identity()));
        //订单明细
        List<SkuItemC2S> skuItems = request.getSkuItems();
        //平台条码
        List<String> skuIds = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        //系统条码
        List<Long> psCSkuIds = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        //平台商品
        List<String> numiids = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        skuItems.stream().forEach(o -> {
            if (!skuIds.contains(o.getSkuId())) {
                skuIds.add(o.getSkuId());
            }
            if (!numiids.contains(o.getNumiid())) {
                numiids.add(o.getNumiid());
            }
            if (!psCSkuIds.contains(o.getPsCSkuId())) {
                psCSkuIds.add(o.getPsCSkuId());
            }
        });
        //查询店铺配销仓数量同步设置信息
        SgCChannelQtyStrategyMapper channelQtyStrategyMapper = ApplicationContextHandle.getBean(SgCChannelQtyStrategyMapper.class);
        SgCChanelQtyStrategyQueryInfoRequest queryInfoRequest = new SgCChanelQtyStrategyQueryInfoRequest();
        queryInfoRequest.setCpCShopId(request.getShopId());
        // 唯品会按系统条码 其他平台按平台条码
        if (SgConstants.IS_ACTIVE_Y.equals(request.getVipOrderFlag())) {
            queryInfoRequest.setPsCSkuIds(psCSkuIds);
        } else {
            queryInfoRequest.setSkuIds(skuIds);
            queryInfoRequest.setProIds(numiids);
        }
        List<SgCChanelQtyStrategyQueryInfoResult> qtyStrategyInfo = channelQtyStrategyMapper.selectStrategyInfo(queryInfoRequest);
        if (CollectionUtils.isEmpty(qtyStrategyInfo)) {
            FindSourceStrategyUtils.outputLog("C->S寻源策略 数量优先策略 来源订单:{} 店铺:{} 在时间:{} 下没有订单商品的有效渠道锁定库存配置", request.getSourceBillId(), request.getShopId(), request.getBillDate());
            return;
        }
        //策略配置信息按照平台条码id 分组  唯品的按系统条码分组
        Map<Long, List<SgCChanelQtyStrategyQueryInfoResult>> vipStrategyInfoMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
        //其他平台按skuid进行分组
        Map<String, List<SgCChanelQtyStrategyQueryInfoResult>> strategyInfoMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
        if (SgConstants.IS_ACTIVE_Y.equals(request.getVipOrderFlag())) {
            vipStrategyInfoMap.putAll(qtyStrategyInfo.stream().collect(Collectors.groupingBy(SgCChanelQtyStrategyQueryInfoResult::getPsCSkuId)));
        } else {
            strategyInfoMap.putAll(qtyStrategyInfo.stream().collect(Collectors.groupingBy(SgCChanelQtyStrategyQueryInfoResult::getSkuId)));
        }
        //设置仓库优先级
        skuItems.stream().forEach(o -> {
            Long psCSkuId = o.getPsCSkuId();
            Long itemId = o.getSourceItemId();
            String skuId = o.getSkuId();
            List<SgCChanelQtyStrategyQueryInfoResult> currStrategyInfoList;
            if (SgConstants.IS_ACTIVE_Y.equals(request.getVipOrderFlag())) {
                currStrategyInfoList = vipStrategyInfoMap.get(psCSkuId);
            } else {
                currStrategyInfoList = strategyInfoMap.get(skuId);
            }
            //得到当前明细的策略配置信息
            //配置信息为空 跳过当前明细 执行下一个
            if (CollectionUtils.isEmpty(currStrategyInfoList)) {
                return;
            }
            // 当前明细策略结果
            SgFindSourceStrategySkuC2SResult currSkuResult;
            //当前明细策略结果明细
            List<SgFindSourceStrategyStoreItemC2SResult> currStoreItemList;
            //在原有策略结果上追加
            if (skuResultMap.containsKey(itemId)) {
                currSkuResult = skuResultMap.get(itemId);
                currStoreItemList = CollectionUtils.isEmpty(currSkuResult.getItemResult()) ? new ArrayList<>() : currSkuResult.getItemResult();
            } else {
                currSkuResult = new SgFindSourceStrategySkuC2SResult();
                currSkuResult.setSourceItemId(itemId);
                currSkuResult.setPsCSkuId(psCSkuId);
                currStoreItemList = new ArrayList();
            }
            currSkuResult.setItemResult(currStoreItemList);
            skuResultList.add(currSkuResult);
            //在原有策略结果明细上追加   设置仓库及优先级信息
            currStrategyInfoList.stream().forEach(i -> {
                Long sgCSaStoreId = i.getSgCSaStoreId();
                SgFindSourceStrategyStoreItemC2SResult currStoreItem = new SgFindSourceStrategyStoreItemC2SResult();
                currStoreItem.setSgCSaStoreId(sgCSaStoreId);
                currStoreItem.setPriority(i.getOrderno());
                currStoreItemList.add(currStoreItem);
            });
        });

        FindSourceStrategyUtils.outputLog("FindSourceSaQtyFirstRule.doRule result:{}", JSONObject.toJSONString(request));
    }


}