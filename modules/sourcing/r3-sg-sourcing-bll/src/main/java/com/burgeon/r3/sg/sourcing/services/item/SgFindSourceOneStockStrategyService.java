package com.burgeon.r3.sg.sourcing.services.item;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemS2L;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @description: 单仓出库策略
 * @author: hwy
 * @time: 2021/6/9 16:18
 */
@Component
@Slf4j
public class SgFindSourceOneStockStrategyService extends StrategyHandle {

    /**
     * @param request:
     * @Description: 单仓发货策略
     * @Author: hwy
     * @Date: 2021/6/10 13:10
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<com.burgeon.r3.sg.basic.model.result.strategy.StrategyBaseResult>
     **/
    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {

        FindSourceStrategyUtils.outputLog("FindSourceOneStockStrategyService.handleRequest S->L 二阶段寻源派单 单仓出库策略 param:{}",
                JSONObject.toJSONString(request));

        ValueHolderV14<StrategyBaseResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

        //1.转换入参类型
        SgFindSourceStrategyS2LRequest strategyRequest = (SgFindSourceStrategyS2LRequest) request;

        //2.获取上一个策略执行结果
        StrategyBaseResult strategyBaseResult = strategyRequest.getStrategyBaseResult();

        SgFindSourceStrategyS2LResult findSourceStrategyResult = (SgFindSourceStrategyS2LResult) strategyBaseResult;
        List<SgFindSourceStrategySkuS2LResult> skuResultList = findSourceStrategyResult.getSkuResultList();

        try {

            //3.统计符合单仓出库的物理仓
            List<Long> warehouseIds = FindSourceStrategyUtils.getOneStockWarehouseIds(strategyRequest);

            //4.替换结果集
            if (!CollectionUtils.isEmpty(warehouseIds)) {
                replaceResult(warehouseIds, skuResultList);
            }

            //5.判断能否单仓出库
            if (CollectionUtils.isEmpty(warehouseIds)
                    && StrategyConstants.ORDER_SPLIT_TYPE_NO.equals(strategyRequest.getSplitType())) {
                FindSourceStrategyUtils.outputLog("S->L 寻源派单策略 单仓出库策略 没有满足单仓出库的仓且订单不允许拆单 订单全部缺货");
                FindSourceStrategyUtils.allOut(strategyRequest);
            } else if (CollectionUtils.isEmpty(warehouseIds) && Boolean.TRUE.equals(request.getInventedOccupy())) {
                //单仓不满足，虚拟寻源就全提高仓库优先级
                getOptimalStore(request);
            }

            valueHolderV14.setData(strategyBaseResult);

        } catch (Exception e) {
            log.error("S->L 寻源派单策略 单仓出库策略 发生异常:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("S->L 寻源派单策略 单仓出库策略 发生异常");
        }

        //存在下一策略 继续往下执行
        return doNext(request, valueHolderV14);

    }

    /**
     * @param request
     */
    private void getOptimalStore(StrategyBaseRequest request) {

        try {

            FindSourceStrategyUtils.outputLog("FindSourceOneStockStrategyService.handleRequest S->L 二阶段寻源派单 二阶段虚拟寻源 param:{}",
                    JSONObject.toJSONString(request));

            SgFindSourceStrategyS2LRequest strategyS2LRequest = (SgFindSourceStrategyS2LRequest) request;
            List<SkuItemS2L> skuItemS2LS = strategyS2LRequest.getSkuItems();
            SgFindSourceStrategyS2LResult strategyResult = (SgFindSourceStrategyS2LResult) request.getStrategyBaseResult();
            List<SgFindSourceStrategySkuS2LResult> skuResultList = strategyResult.getSkuResultList();

            //计算最全满足实体仓
            Map<Long, Set<Long>> storeIdSourceItemMap = new HashMap<>();
            List<SgFindSourceStrategyStoreItemS2LResult> allS2LResult = new ArrayList<>();
            skuResultList.forEach(result -> {
                List<SgFindSourceStrategyStoreItemS2LResult> itemC2LResults = result.getItemResultList();
                if (CollectionUtils.isNotEmpty(itemC2LResults)) {
                    allS2LResult.addAll(itemC2LResults);
                    itemC2LResults.forEach(itemC2LResult -> {
                        if (!StrategyConstants.OUT_DEFAULT_STORE_ID.equals(itemC2LResult.getStoreId())) {
                            Set<Long> itemSet = storeIdSourceItemMap.computeIfAbsent(itemC2LResult.getStoreId(), k -> new HashSet<>());
                            itemSet.add(result.getSourceItemId());
                        }
                    });
                }
            });
            Map<Long, List<SgFindSourceStrategyStoreItemS2LResult>> phyIdS2LResultMap = allS2LResult.stream()
                    .collect(Collectors.groupingBy(SgFindSourceStrategyStoreItemS2LResult::getStoreId));

            //根据满足明细数量降序
            List<Map.Entry<Long, Set<Long>>> sortedStore = storeIdSourceItemMap.entrySet().stream()
                    .sorted((x, y) -> y.getValue().size() - x.getValue().size()).collect(Collectors.toList());


            AtomicInteger offSet = new AtomicInteger(10);
            //根据满足率重新赋值优先级
            //全满足的基于同步比例策略优先级加10000
            //部分满足的满足的明细越多，优先级越高
            sortedStore.forEach(sa -> {
                List<SgFindSourceStrategyStoreItemS2LResult> s2LResult = phyIdS2LResultMap.get(sa.getKey());
                if (CollectionUtils.isNotEmpty(s2LResult)) {
                    if (sa.getValue().size() >= skuItemS2LS.size()) {
                        s2LResult.forEach(s -> s.setPriority(s.getPriority() + 10000));
                    } else {
                        s2LResult.forEach(s -> s.setPriority(10000 - offSet.get()));
                        offSet.set(offSet.get() + 10);
                    }
                }
            });

            //没有明细满足的配销仓优先级置为0
            allS2LResult.forEach(p -> {
                if (!storeIdSourceItemMap.keySet().contains(p.getStoreId())) {
                    p.setPriority(0);
                }
            });
        } catch (Exception e) {
            log.error(" 二阶段虚拟寻源,就全策略异常：{}", Throwables.getStackTraceAsString(e));
        }
    }


    /**
     * @param warehouseIds:
     * @param skuResultList:
     * @Description: 将满足单仓发货的物理仓替换上个策略的结果集
     * @Author: hwy
     * @Date: 2021/6/10 11:37
     * @return: void
     **/
    private void replaceResult(List<Long> warehouseIds, List<SgFindSourceStrategySkuS2LResult> skuResultList) {

        //存在单仓发货的物理仓 处理返回结果并传递到下一策略
        log.info("FindSourceOneStockStrategyService.handleRequest 单仓发货策略执行完毕 找到满足单仓发货的物理仓:{}",
                JSONObject.toJSONString(warehouseIds));

        //所有满足的结果集
        for (SgFindSourceStrategySkuS2LResult skuResult : skuResultList) {

            //创建新的库存集合
            List<SgFindSourceStrategyStoreItemS2LResult> newItemResultList = new ArrayList<>();
            List<SgFindSourceStrategyStoreItemS2LResult> itemResultList = skuResult.getItemResultList();
            for (SgFindSourceStrategyStoreItemS2LResult item : itemResultList) {

                //属于满足单仓发货物理仓 记录库存
                if (warehouseIds.contains(item.getStoreId()) && !StrategyConstants.OUT_DEFAULT_STORE_ID.equals(item.getStoreId())) {
                    newItemResultList.add(item);
                }
            }

            //将过滤后的库存记录替换到结果(排除缺货逻辑仓)
            if (!CollectionUtils.isEmpty(newItemResultList)) {
                skuResult.setItemResultList(newItemResultList);
            }
        }

    }
}