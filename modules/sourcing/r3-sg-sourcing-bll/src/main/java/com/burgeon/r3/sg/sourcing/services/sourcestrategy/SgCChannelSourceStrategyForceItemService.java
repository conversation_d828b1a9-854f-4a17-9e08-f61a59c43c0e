package com.burgeon.r3.sg.sourcing.services.sourcestrategy;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.r3.impl.oss.AliOssStorageFile;
import com.burgeon.r3.sg.basic.services.log.LogCommonService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.R3ParamConstants;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.table.basic.SgCOperationLog;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceForceStrategy;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategy;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategyForceItem;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgExportUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceForceStrategyMapper;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyForceItemMapper;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.request.sourcestrategy.SgCChannelSourceStrategyForceItemDeleteRequest;
import com.burgeon.r3.sg.sourcing.model.request.sourcestrategy.SgCChannelSourceStrategyItemQueryRequest;
import com.burgeon.r3.sg.sourcing.model.request.sourcestrategy.SgChannelSourceStrategyForceItemImportModel;
import com.burgeon.r3.sg.sourcing.model.result.sourcestrategy.SgCChannelSourceStrategyForceItemResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DataUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/19 17:16
 * 强制寻源规则明细表Service
 */
@Slf4j
@Component
public class SgCChannelSourceStrategyForceItemService extends ServiceImpl<SgCChannelSourceStrategyForceItemMapper,
        SgCChannelSourceStrategyForceItem> {
    @Autowired
    private SgCChannelSourceStrategyForceItemMapper sgChannelSourceStrategyForceItemMapper;
    @Autowired
    private SgCChannelSourceStrategyMapper sgChannelSourceStrategyMapper;
    @Autowired
    private SgCChannelSourceForceStrategyMapper sgChannelSourceForceStrategyMapper;
    @Autowired
    private SgExportUtils exportUtil;
    @Autowired
    private LogCommonService logCommonService;


    /**
     * 根据主表id查询强制寻源规则
     *
     * @param request 请求参数
     * @return ValueHolderV14<List < SgCChannelSourceStrategyForceItemResult>>
     */
    public ValueHolderV14<List<SgCChannelSourceStrategyForceItemResult>> querySgChannelSourceStrategyForceItem(SgCChannelSourceStrategyItemQueryRequest request) {
        ValueHolderV14<List<SgCChannelSourceStrategyForceItemResult>> vh = new ValueHolderV14<>(ResultCode.SUCCESS,
                "查询成功!");
        if (request.getSgCChannelSourceStrategyId() == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数不合法,查询失败!");
            return vh;
        }
        List<SgCChannelSourceStrategyForceItem> forceItems = sgChannelSourceStrategyForceItemMapper.selectList(
                new LambdaQueryWrapper<SgCChannelSourceStrategyForceItem>()
                        .eq(SgCChannelSourceStrategyForceItem::getSgCChannelSourceStrategyId,
                                request.getSgCChannelSourceStrategyId()));
        if (CollectionUtils.isNotEmpty(forceItems)) {
            List<SgCChannelSourceStrategyForceItemResult> results = new ArrayList<>();
            Map<Long, List<SgCChannelSourceStrategyForceItem>> sourceStrategyMap =
                    forceItems.stream().collect(Collectors.groupingBy(SgCChannelSourceStrategyForceItem::getSgCChannelSourceForceStrategyId));
            for (List<SgCChannelSourceStrategyForceItem> items : sourceStrategyMap.values()) {
                SgCChannelSourceStrategyForceItemResult result = new SgCChannelSourceStrategyForceItemResult();
                result.setSgCChannelSourceForceStrategyId(items.get(0).getSgCChannelSourceForceStrategyId());
                result.setSgCChannelSourceForceStrategyEcode(items.get(0).getSgCChannelSourceForceStrategyEcode());
                result.setSgCChannelSourceForceStrategyEname(items.get(0).getSgCChannelSourceForceStrategyEname());
                List<SgCChannelSourceStrategyForceItemResult.StoreInfoResult> storeInfoResults = new ArrayList<>();
                for (SgCChannelSourceStrategyForceItem item : items) {
                    SgCChannelSourceStrategyForceItemResult.StoreInfoResult storeInfoResult =
                            new SgCChannelSourceStrategyForceItemResult.StoreInfoResult();
                    BeanUtils.copyProperties(item, storeInfoResult);
                    storeInfoResults.add(storeInfoResult);
                }
                result.setStoreInfos(storeInfoResults);
                results.add(result);
            }
            vh.setData(results);
        }
        return vh;
    }

    /**
     * 强制寻源规则明细删除
     *
     * @param request 明细id
     * @return ValueHolderV14
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 deleteSgChannelSourceStrategyForceItem(SgCChannelSourceStrategyForceItemDeleteRequest request) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "删除成功！");
        if (request.getItemId() == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("参数不合法,删除失败!");
            return v14;
        }
        SgCChannelSourceStrategyForceItem item = sgChannelSourceStrategyForceItemMapper.selectById(request.getItemId());
        if (item == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("当前记录已不存在,删除失败!");
        } else {
            int i = sgChannelSourceStrategyForceItemMapper.deleteById(request.getItemId());
            if (i > 0) {
                SgCChannelSourceStrategy updateStrategy = new SgCChannelSourceStrategy();
                updateStrategy.setId(item.getSgCChannelSourceStrategyId());
                StorageUtils.setBModelDefalutDataByUpdate(updateStrategy, request.getLoginUser());
                updateStrategy.setModifierename(request.getLoginUser().getEname());
                sgChannelSourceStrategyMapper.updateById(updateStrategy);
                List<SgCOperationLog> operationLogList = new ArrayList<>();
                SgCOperationLog operationLog = logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY_FORCE_ITEM",
                        OperationTypeEnum.DEL.getOperationValue(), item.getSgCChannelSourceStrategyId(),
                        "强制寻源规则", "店仓", item.getCpCStoreEname(),
                        null, request.getLoginUser());
                operationLogList.add(operationLog);
                logCommonService.batchInsertLog(operationLogList);
            }
        }
        return v14;
    }

    /**
     * 根据数据来源删除 寻源策略定义-强制寻源规则
     *
     * @param request
     * @return
     */
    public ValueHolderV14 deleteSgChannelSourceStrategyForceItemBySourceBillNo(SgCChannelSourceStrategyForceItemDeleteRequest request) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "删除成功！");
        if (request == null || CollectionUtils.isEmpty(request.getSourceBillNoList())) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("参数不合法,删除失败!");
            return v14;
        }

        LambdaQueryWrapper<SgCChannelSourceStrategyForceItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SgCChannelSourceStrategyForceItem::getSourceBillNo, request.getSourceBillNoList());
        sgChannelSourceStrategyForceItemMapper.delete(wrapper);
        return v14;
    }

    /**
     * 强制寻源规则  明细导入(不落库)
     *
     * @param importItemModels 导入明细集合
     * @param objId            主表id 有则和数据库数据对比去重
     * @return ValueHolderV14
     */
    public ValueHolderV14<List<SgCChannelSourceStrategyForceItemResult>> importSgChannelSourceStrategyForceItem(List<SgChannelSourceStrategyForceItemImportModel> importItemModels,
                                                                                                                Long objId) {
        log.info(" SgCChannelSourceStrategyForceItemService.importSgChannelSourceStrategyForceItem.importItemModels" +
                ".szie={},objId={}", importItemModels.size(), objId);
        ValueHolderV14<List<SgCChannelSourceStrategyForceItemResult>> vh = new ValueHolderV14<>(ResultCode.SUCCESS,
                "导入成功!");
        List<SgChannelSourceStrategyForceItemImportModel> emptyParamList =
                importItemModels.stream().filter(i -> StringUtils.isBlank(i.getSgChannelSourceForceStrategyEcode()) ||
                        StringUtils.isBlank(i.getCpStoreEcode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(emptyParamList)) {
            List<Integer> emptyLines =
                    emptyParamList.stream().map(SgChannelSourceStrategyForceItemImportModel::getLineNum).collect(Collectors.toList());
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("导入失败!第" + emptyLines + "行强制寻源规则类型编号、逻辑仓编码必填项不能为空,请调整后导入;");
            return vh;
        }
        Map<String, List<SgChannelSourceStrategyForceItemImportModel>> groupByStrategyMap =
                importItemModels.stream().collect(Collectors.groupingBy(SgChannelSourceStrategyForceItemImportModel::getSgChannelSourceForceStrategyEcode));
        Map<String, Long> saveForceItemMap = new HashMap<>(16);
        if (Objects.nonNull(objId) && objId > 0L) {
            List<SgCChannelSourceStrategyForceItem> saveItems =
                    sgChannelSourceStrategyForceItemMapper.selectList(new LambdaQueryWrapper<SgCChannelSourceStrategyForceItem>()
                            .eq(SgCChannelSourceStrategyForceItem::getSgCChannelSourceStrategyId, objId)
                            .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
            saveItems.forEach(i -> saveForceItemMap.put(i.getSgCChannelSourceForceStrategyId() + "_" + i.getCpCStoreId(),
                    i.getId()));
        }
        List<SgCChannelSourceStrategyForceItemResult> resultList = new ArrayList<>();
        StringBuilder stategyErrorBuilder = new StringBuilder();
        int failCount = 0;
        //获取所有逻辑仓
        List<String> storeEcodes =
                importItemModels.stream().map(SgChannelSourceStrategyForceItemImportModel::getCpStoreEcode).distinct().collect(Collectors.toList());
        Map<String, CpCStore> storeMap = CommonCacheValUtils.queryStoreInfosByEcodes(storeEcodes);
        List<String> sourceForceStrategyEcodes =
                importItemModels.stream().map(SgChannelSourceStrategyForceItemImportModel::getSgChannelSourceForceStrategyEcode).distinct().collect(Collectors.toList());
        //获取所有强制寻源规则类型
        List<SgCChannelSourceForceStrategy> forceStrategyList =
                sgChannelSourceForceStrategyMapper.selectList(new LambdaQueryWrapper<SgCChannelSourceForceStrategy>()
                        .in(CollectionUtils.isNotEmpty(sourceForceStrategyEcodes),
                                SgCChannelSourceForceStrategy::getEcode,
                                sourceForceStrategyEcodes)
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        Map<String, Long> channelSourceForceStrategyMap = new HashMap<>(16);
        forceStrategyList.forEach(i -> channelSourceForceStrategyMap.put(i.getEcode(), i.getId()));
        Set<Map.Entry<String, List<SgChannelSourceStrategyForceItemImportModel>>> entrySet =
                groupByStrategyMap.entrySet();
        for (Map.Entry<String, List<SgChannelSourceStrategyForceItemImportModel>> entry : entrySet) {
            String sourceForceStategyEcode = entry.getKey();
            List<SgChannelSourceStrategyForceItemImportModel> modelList = entry.getValue();
            Long sourceForceStategyId = channelSourceForceStrategyMap.get(sourceForceStategyEcode);
            if (Objects.isNull(sourceForceStategyId)) {
                failCount += modelList.size();
                stategyErrorBuilder.append("第").append(modelList.stream().map(SgChannelSourceStrategyForceItemImportModel::getLineNum).collect(Collectors.toList()))
                        .append("行,强制寻源规则类型:").append(sourceForceStategyEcode).append("不存在或不可用!");
                continue;
            }
            SgCChannelSourceStrategyForceItemResult result = new SgCChannelSourceStrategyForceItemResult();
            result.setSgCChannelSourceForceStrategyId(sourceForceStategyId);
            StringBuilder storeErrorBuilder = new StringBuilder();
            Map<String, SgCChannelSourceStrategyForceItemResult.StoreInfoResult> storeInfoResultMap = new HashMap<>(16);
            for (SgChannelSourceStrategyForceItemImportModel importModel : modelList) {
                CpCStore store = storeMap.get(importModel.getCpStoreEcode());
                if (Objects.isNull(store)) {
                    failCount += 1;
                    storeErrorBuilder.append("第").append(importModel.getLineNum()).append("行,逻辑仓不存在或不可用!");
                    continue;
                }
                if (saveForceItemMap.containsKey(sourceForceStategyId + "_" + store.getId())) {
                    continue;
                }
                SgCChannelSourceStrategyForceItemResult.StoreInfoResult storeInfoResult =
                        new SgCChannelSourceStrategyForceItemResult.StoreInfoResult();
                storeInfoResult.setId(-1L);
                storeInfoResult.setCpCStoreId(store.getId());
                storeInfoResult.setCpCStoreEcode(store.getEcode());
                storeInfoResult.setCpCStoreEname(store.getEname());

                storeInfoResultMap.put(sourceForceStategyId + "_" + store.getId(), storeInfoResult);
            }
            if (storeErrorBuilder.length() > 0) {
                stategyErrorBuilder.append(storeErrorBuilder);
            }
            if (MapUtils.isNotEmpty(storeInfoResultMap)) {
                List<SgCChannelSourceStrategyForceItemResult.StoreInfoResult> storeInfoResultList =
                        new ArrayList<>(storeInfoResultMap.values());
                result.setStoreInfos(storeInfoResultList);
                resultList.add(result);
            }
        }
        if (stategyErrorBuilder.length() > 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("导入成功" + (importItemModels.size() - failCount) + "条,失败" + failCount + "条,失败原因:" +
                    stategyErrorBuilder.toString());
        }
        if (CollectionUtils.isNotEmpty(resultList)) {
            vh.setData(resultList);
        }
        log.info("SgCChannelSourceStrategyForceItemService.importSgChannelSourceStrategyForceItem.returnVh={}",
                JSON.toJSONString(vh));
        return vh;
    }

    /**
     * * 强制寻源规则  明细导出
     *
     * @param session session
     * @return ValueHolder
     */
    public ValueHolder export(QuerySession session) throws NDSException {
        log.info("Start SgCChannelSourceStrategyForceItemService.export");
        User user = Optional.ofNullable(session.getUser()).orElse(SystemUserResource.getRootUser());
        ValueHolder vh = new ValueHolder();
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        if (Objects.isNull(request.getObjId())) {
            vh.put(R3ParamConstants.CODE, ResultCode.FAIL);
            vh.put(R3ParamConstants.MESSAGE, "主表id参数缺失,请联系管理员!");
            return vh;
        }
        List<SgCChannelSourceStrategyForceItem> exportItems =
                sgChannelSourceStrategyForceItemMapper.selectList(new LambdaQueryWrapper<SgCChannelSourceStrategyForceItem>()
                        .eq(SgCChannelSourceStrategyForceItem::getSgCChannelSourceStrategyId, request.getObjId())
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));

        if (CollectionUtils.isEmpty(exportItems)) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "明细无数据！导出失败！");
            return vh;
        }
        try {
            vh = createExcel(exportItems, user);
        } catch (Exception e) {
            log.error(" SgCChannelSourceStrategyForceItemService.export. exception_has_occured:{}",
                    Throwables.getStackTraceAsString(e));
            vh.put(R3ParamConstants.CODE, ResultCode.FAIL);
            vh.put(R3ParamConstants.MESSAGE, "导出失败!失败原因:" + e.getMessage());
        }
        log.info(" Finish SgCChannelSourceStrategyForceItemService.export.return.vh={}", JSON.toJSONString(vh));
        return vh;
    }

    /**
     * 上传oss 并下载
     *
     * @param exportItems 明细集合
     * @param user        用户
     * @return vh
     */
    private ValueHolder createExcel(List<SgCChannelSourceStrategyForceItem> exportItems, User user) throws Exception {
        log.info(" Start SgCChannelSourceStrategyForceItemService.createExcel");
        String location = ResourceUtils.getURL("/home/<USER>/tmp").getPath();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        SimpleDateFormat uploadSdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        location = location + File.separator + "export" + File.separator + uuid + File.separator;
        File svrDir = new File(location);
        if (!svrDir.isDirectory()) {
            svrDir.mkdirs();
        }
        String fullFileName = location + "寻源策略定义强制寻源规则明细.xlsx";

        try (FileOutputStream fileOut = new FileOutputStream(fullFileName)) {
            // 创建表格
            SXSSFWorkbook wb = new SXSSFWorkbook();
            // 在工作薄上建一张工作表
            Sheet sheet = wb.createSheet();
            Row row = sheet.createRow(0);
            wb.setCompressTempFiles(true);
            sheet.createFreezePane(0, 0);
            int leino = 0;
            exportUtil.cteateCellheader(wb, row, leino++, "强制寻源规则类型编号", sheet);
            exportUtil.cteateCellheader(wb, row, leino++, "强制寻源规则类型", sheet);
            exportUtil.cteateCellheader(wb, row, leino++, "逻辑仓编码", sheet);
            exportUtil.cteateCellheader(wb, row, leino++, "逻辑仓名称", sheet);
            exportUtil.cteateCellheader(wb, row, leino++, "创建人", sheet);
            exportUtil.cteateCellheader(wb, row, leino++, "创建时间", sheet);
            exportUtil.cteateCellheader(wb, row, leino++, "修改人", sheet);
            exportUtil.cteateCellheader(wb, row, leino++, "修改时间", sheet);

            int rowno = 0;
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (SgCChannelSourceStrategyForceItem strategyForceItem : exportItems) {
                Row rowi = sheet.createRow(++rowno);
                int lieno2 = 0;
                exportUtil.cteateCell(wb, rowi, lieno2++, strategyForceItem.getSgCChannelSourceForceStrategyEcode());
                exportUtil.cteateCell(wb, rowi, lieno2++, strategyForceItem.getSgCChannelSourceForceStrategyEname());
                exportUtil.cteateCell(wb, rowi, lieno2++, strategyForceItem.getCpCStoreEcode());
                exportUtil.cteateCell(wb, rowi, lieno2++, strategyForceItem.getCpCStoreEname());
                exportUtil.cteateCell(wb, rowi, lieno2++, strategyForceItem.getOwnerename());
                exportUtil.cteateCell(wb, rowi, lieno2++, DataUtil.isEmpty(strategyForceItem.getCreationdate()) ?
                        null : dateFormat.format(strategyForceItem.getCreationdate()));
                exportUtil.cteateCell(wb, rowi, lieno2++, strategyForceItem.getModifierename());
                exportUtil.cteateCell(wb, rowi, lieno2++, DataUtil.isEmpty(strategyForceItem.getModifieddate()) ?
                        null : dateFormat.format(strategyForceItem.getModifieddate()));
            }

            wb.write(fileOut);
            fileOut.flush();
            fileOut.close();

            String uploadSavePath = "export/SG_C_CHANNEL_SOURCE_STRATEGY_FORCE_ITEM/";
            String uploadOssFileName = new AliOssStorageFile().uploadFile(uploadSavePath,
                    "寻源策略定义强制寻源规则明细-" + user.getName() + "-" + uploadSdf.format(new Date()), ".xlsx",
                    fullFileName, user.getName());

            String path = null;
            if (StringUtils.isNotEmpty(uploadOssFileName)) {
                path = uploadOssFileName;
            }
            //删除临时文件
            File file = new File(fullFileName);
            if (file.exists() && file.isFile()) {
                boolean fileDelete = file.delete();
                boolean dirDelete = svrDir.delete();
                log.info(" Start SgCChannelSourceStrategyForceItemService.createExcel.fileDelete={},dirDelete={}",
                        fileDelete, dirDelete);
            }
            return ValueHolderUtils.success("导出成功", path == null ? "" : path);
        } catch (Exception e) {
            log.error(" SgCChannelSourceStrategyForceItemService.createExcel. exception_has_occured:{}",
                    Throwables.getStackTraceAsString(e));
            return ValueHolderUtils.fail("导出失败！" + e.getMessage());
        }
    }
}