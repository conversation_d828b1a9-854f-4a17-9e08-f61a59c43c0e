package com.burgeon.r3.sg.sourcing.services;

import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: hwy
 * @time: 2021/6/7 11:02
 */
@Component
public abstract class StrategyHandle {

    /**
     * @Description: 头标志
     **/
    private Boolean headFlag;

    private StrategyHandle next;

    public Boolean getHeadFlag() {
        return headFlag;
    }

    public void setHeadFlag(Boolean headFlag) {
        this.headFlag = headFlag;
    }

    public StrategyHandle getNext() {
        return next;
    }

    public void setNext(StrategyHandle next) {
        this.next = next;
    }


    /**
     * @param request:
     * @Description: 执行策略组
     * @Author: hwy
     * @Date: 2021/6/7 11:22
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<StrategyBaseResult>
     **/
    public abstract ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request);

    /**
     * @param request:
     * @param valueHolder:
     * @Description: 传递策略
     * @Author: hwy
     * @Date: 2021/6/15 14:12
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult>
     **/
    public ValueHolderV14<StrategyBaseResult> doNext(StrategyBaseRequest request,
                                                     ValueHolderV14<StrategyBaseResult> valueHolder) {
        if (valueHolder.isOK()) {

            StrategyHandle next = this.getNext();
            StrategyBaseResult baseResult = valueHolder.getData();

            //存在下一个节点并且指示执行下一个节点时
            if (next != null && baseResult != null && baseResult.isDoNextStrategy()) {
                return next.handleRequest(request);
            }
        }

        return valueHolder;
    }

}