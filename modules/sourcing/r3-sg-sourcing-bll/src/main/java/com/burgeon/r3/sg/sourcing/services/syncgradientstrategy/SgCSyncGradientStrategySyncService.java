package com.burgeon.r3.sg.sourcing.services.syncgradientstrategy;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.api.record.SgCommRecordCmd;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.request.record.SgCommRecordAddRequest;
import com.burgeon.r3.sg.basic.model.request.record.SgCommRecordData;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.request.mq.SgChannelStorageOmsBufferRequest;
import com.burgeon.r3.sg.core.model.table.sourcing.syncgradientstrategy.SgCSyncGradientStrategy;
import com.burgeon.r3.sg.core.model.table.sourcing.syncgradientstrategy.SgCSyncGradientStrategyCond;
import com.burgeon.r3.sg.core.model.table.sourcing.syncgradientstrategy.SgCSyncGradientStrategyItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.sourcing.mapper.syncgradientstrategy.SgCSyncGradientStrategyCondMapper;
import com.burgeon.r3.sg.sourcing.mapper.syncgradientstrategy.SgCSyncGradientStrategyItemMapper;
import com.burgeon.r3.sg.sourcing.mapper.syncgradientstrategy.SgCSyncGradientStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.request.syncgradientstrategy.SgCSyncGradientStrategyBillSaveRequest;
import com.burgeon.r3.sg.sourcing.model.request.syncgradientstrategy.SgCSyncGradientStrategyCondItemSaveRequest;
import com.burgeon.r3.sg.sourcing.model.request.syncgradientstrategy.SgCSyncGradientStrategySaveRequest;
import com.burgeon.r3.sg.sourcing.model.request.syncgradientstrategy.SgCSyncGradientStrategyShopItemSaveRequest;
import com.burgeon.r3.sg.stocksync.api.SgChannelStorageOmsInitCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageOmsManualSynchRequest;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * 共享池库存梯度策略同步服务
 * @version 1.0
 * @date 2021/8/3 16:59
 */
@Slf4j
@Service
public class SgCSyncGradientStrategySyncService {
    @Autowired
    private SgCSyncGradientStrategyMapper sgSyncGradientStrategyMapper;

    @Autowired
    private SgCSyncGradientStrategyCondMapper sgSyncGradientStrategyCondMapper;

    @Autowired
    private SgCSyncGradientStrategyItemMapper sgSyncGradientStrategyItemMapper;

    public ValueHolder stockSync(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        SgCSyncGradientStrategySyncService bean = ApplicationContextHandle.getBean(SgCSyncGradientStrategySyncService.class);
        return R3ParamUtils.convertV14WithResult(bean.stockSync(request));
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 stockSync(SgR3BaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgCSyncGradientStrategySyncService.stockSync :request{}"
                    , JSONObject.toJSONString(request));
        }
        Long objId = request.getObjId();
        User loginUser = request.getLoginUser();
        String lockKey = SgConstants.SG_C_SYNC_GRADIENT_STRATEGY + ":" + loginUser.getId() + ":" + objId;
        List<SgCommRecordData> releaseDatas = new ArrayList<>();
        if (SgRedisLockUtils.lock(lockKey)) {
            List<SgChannelStorageOmsBufferRequest> resultList = checkParams(request);
            try {
                //记录日志
                SgCommRecordData data = new SgCommRecordData();
                data.setMessage(loginUser.getName() + "进行了库存同步操作。");
                data.setColumnName("库存同步");
                releaseDatas.add(data);
//                saveLog(objId, releaseDatas, loginUser);
                //库存同步
                syncStock(resultList, loginUser);
            } catch (Exception e) {
                AssertUtils.logAndThrowException(e, loginUser.getLocale());
            } finally {
                SgRedisLockUtils.unlock(lockKey, log, this.getClass().getName());
            }
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, "库存同步成功");
    }

    /**
     * 手动库存同步
     */
    private void triggerStockSync(Long id) {
    }

    /**
     * 新增和修改，触发库存自动同步
     * 在SgCSyncGradientStrategyService.saveShareStoreSyncGradientStrategy调用保存前
     *
     * @param request 入参
     */
    private void triggerStockAutoSync(SgCSyncGradientStrategyBillSaveRequest request) {
        SgCSyncGradientStrategySaveRequest mainSaveRequest = request.getMainSaveRequest();
        SgCSyncGradientStrategyCondItemSaveRequest condRequest = request.getCondItemSaveRequest();
        SgCSyncGradientStrategyShopItemSaveRequest shopRequest = request.getShopItemSaveRequest();

        List<SgChannelStorageOmsBufferRequest> resultList = new ArrayList<>();

        if (mainSaveRequest.getId() < 0L) {
            //新增，把共享池对应的非空条件和店铺同步到mq
            if (Objects.nonNull(condRequest) && Objects.nonNull(shopRequest)) {
                SgChannelStorageOmsBufferRequest requestItem = new SgChannelStorageOmsBufferRequest();
                requestItem.setChangeNum(shopRequest.getRatio());
                requestItem.setStoreType(SgConstants.SYNC_STORE_TYPE_SHARE_POOL);
                requestItem.setCpCStoreId(shopRequest.getCpCShopId());
                requestItem.setSourceNo("共享池库存梯度新增触发库存同步");
                resultList.add(requestItem);
            }
        } else {
            SgCSyncGradientStrategyCond sgCSyncGradientStrategyCond = sgSyncGradientStrategyCondMapper.selectById(condRequest.getId());
            if (Objects.nonNull(sgCSyncGradientStrategyCond)) {
                boolean condCheck = !sgCSyncGradientStrategyCond.getCondtion().equals(condRequest.getCondtion())
                        || !sgCSyncGradientStrategyCond.getQtyBegin().equals(condRequest.getQtyBegin());
                if (condCheck) {
                    LambdaQueryWrapper<SgCSyncGradientStrategyItem> itemLqw = new LambdaQueryWrapper<>();
                    itemLqw.eq(SgCSyncGradientStrategyItem::getSgCSyncGradientStrategyCondId, sgCSyncGradientStrategyCond.getId());
                    List<SgCSyncGradientStrategyItem> sgCSyncGradientStrategyItems = sgSyncGradientStrategyItemMapper.selectList(itemLqw);
                    //将共享池、条件策略和对应的店铺比例明细打包sendMQ
                    for (SgCSyncGradientStrategyItem strategyItem : sgCSyncGradientStrategyItems) {
                        SgChannelStorageOmsBufferRequest requestItem = new SgChannelStorageOmsBufferRequest();
                        requestItem.setCpCStoreId(strategyItem.getCpCShopId());
                        requestItem.setChangeNum(strategyItem.getRatio());
                        requestItem.setStoreType(SgConstants.SYNC_STORE_TYPE_SHARE_POOL);
                        requestItem.setSourceNo("共享池库存梯度条件策略库存同步" + sgCSyncGradientStrategyCond.getId());
                        resultList.add(requestItem);
                    }
                }
            }

            LambdaQueryWrapper<SgCSyncGradientStrategyItem> shopLqw = new LambdaQueryWrapper<>();
            shopLqw.eq(SgCSyncGradientStrategyItem::getCpCShopId, shopRequest.getCpCShopId())
                    .eq(SgCSyncGradientStrategyItem::getSgCSyncGradientStrategyCondId, condRequest.getId());
            SgCSyncGradientStrategyItem sgCSyncGradientStrategyItem = sgSyncGradientStrategyItemMapper.selectOne(shopLqw);
            if (Objects.nonNull(sgCSyncGradientStrategyItem) && Objects.nonNull(shopRequest.getRatio())) {
                boolean equals = shopRequest.getRatio().equals(sgCSyncGradientStrategyItem.getRatio());
                if (!equals) {
                    //店铺发生了比例修改，sendMQ：共享池id，店铺id，修改的比例
                    SgChannelStorageOmsBufferRequest requestItem = new SgChannelStorageOmsBufferRequest();
                    requestItem.setChangeNum(shopRequest.getRatio());
                    requestItem.setStoreType(SgConstants.SYNC_STORE_TYPE_SHARE_POOL);
                    requestItem.setCpCStoreId(shopRequest.getCpCShopId());
                    requestItem.setSourceNo("共享池库存梯度店铺比例库存同步" + shopRequest.getCpCShopId());
                    resultList.add(requestItem);
                }
            }
        }

        syncStock(resultList, request.getLoginUser());
    }

    /**
     * 共享池库存梯度同步请求
     *
     * @param loginUser 操作用户
     * @param bufferRequests  主表信息
     */
    public void syncStock(List<SgChannelStorageOmsBufferRequest> bufferRequests, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("共享池库存梯度修改触发同步 syncStock strategy = {}", JSONObject.toJSONString(bufferRequests));
        }
        for (SgChannelStorageOmsBufferRequest bufferRequest : bufferRequests) {
            SgChannelStorageOmsManualSynchRequest request = new SgChannelStorageOmsManualSynchRequest();
            SgChannelStorageOmsInitCmd sgChannelStorageOmsInitCmd = (SgChannelStorageOmsInitCmd) ReferenceUtil.refer(
                    ApplicationContextHandle.getApplicationContext(),
                    SgChannelStorageOmsInitCmd.class.getName(),
                    SgConstantsIF.GROUP, SgConstantsIF.VERSION);
            //设置操作方式为按查询条件同步
            request.setOperate(SgChannelStorageOmsManualSynchRequest.QUERY_BY_CONDITION);
            request.setSyncType(bufferRequest.getStoreType());
            request.setUser(loginUser);
            request.setCpCShopId(bufferRequest.getCpCStoreId());
            request.setSourceno(bufferRequest.getSourceNo());

            ValueHolderV14<Boolean> holderV14 = sgChannelStorageOmsInitCmd.manualCalcAndSyncChannelProduct(request);
            if (log.isDebugEnabled()) {
                JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(holderV14.toJSONObject(),
                        SerializerFeature.WriteMapNullValue));
                log.debug("共享池库存梯度触发同步，同步结果:{}", result.toJSONString());
            }
        }
    }

    /**
     * 参数校验
     *
     * @param reqeust 请求参数
     * @return 主表
     */
    private List<SgChannelStorageOmsBufferRequest> checkParams(SgR3BaseRequest reqeust) {
        AssertUtils.notNull(reqeust, "请求参数不能为空");
        Long objId = reqeust.getObjId();
        AssertUtils.notNull(objId, "主表id不能为空");
        SgCSyncGradientStrategy gradientStrategy = sgSyncGradientStrategyMapper.selectById(objId);
        AssertUtils.notNull(gradientStrategy, "主表记录不存在");
        List<SgCSyncGradientStrategyCond> sgCSyncGradientStrategyConds = sgSyncGradientStrategyCondMapper.selectList(new LambdaQueryWrapper<SgCSyncGradientStrategyCond>()
                .eq(SgCSyncGradientStrategyCond::getSgCSyncGradientStrategyId, objId)
                .eq(SgCSyncGradientStrategyCond::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isEmpty(sgCSyncGradientStrategyConds)) {
            AssertUtils.logAndThrow("所选项明细不存在，不允许同步");
        }
        List<SgChannelStorageOmsBufferRequest> resultList = new ArrayList<>();
        for (SgCSyncGradientStrategyCond cond : sgCSyncGradientStrategyConds) {
            LambdaQueryWrapper<SgCSyncGradientStrategyItem> itemLqw = new LambdaQueryWrapper<>();
            itemLqw.eq(SgCSyncGradientStrategyItem::getSgCSyncGradientStrategyCondId, cond.getId());
            List<SgCSyncGradientStrategyItem> sgCSyncGradientStrategyItems = sgSyncGradientStrategyItemMapper.selectList(itemLqw);
            for (SgCSyncGradientStrategyItem item : sgCSyncGradientStrategyItems) {
                SgChannelStorageOmsBufferRequest requestItem = new SgChannelStorageOmsBufferRequest();
                requestItem.setChangeNum(item.getRatio());
                requestItem.setStoreType(SgConstants.SYNC_STORE_TYPE_SHARE_POOL);
                requestItem.setCpCStoreId(item.getCpCShopId());
                requestItem.setSourceNo("共享池库存梯度店库存同步" + item.getCpCShopId());
                resultList.add(requestItem);
            }
        }
        if (CollectionUtils.isEmpty(resultList)) {
            AssertUtils.logAndThrow("所选项明细不存在，不允许同步");
        }
        return resultList;
    }

    private void saveLog(Long id, List<SgCommRecordData> datas, User user) {
        if (CollectionUtils.isNotEmpty(datas)) {
            SgCommRecordCmd sgCommRecordCmd = (SgCommRecordCmd) ReferenceUtil.refer(
                    ApplicationContextHandle.getApplicationContext(),
                    SgCommRecordCmd.class.getName(),
                    SgConstantsIF.GROUP, SgConstantsIF.VERSION);

            SgCommRecordAddRequest request = new SgCommRecordAddRequest();
            request.setRecordId(id);
            request.setTableName(SgConstants.SYNC_STORE_TYPE_SHARE_POOL);

            request.setLogType("3");
            request.setRecordDatas(datas);
            sgCommRecordCmd.addRecord(request, user);
        }
    }
}
