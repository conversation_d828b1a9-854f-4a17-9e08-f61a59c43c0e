package com.burgeon.r3.sg.sourcing.services;

import com.alibaba.fastjson.JSON;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutMapper;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutInfoResult;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyC2SRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemC2S;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyC2SResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuC2SResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemC2SResult;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * className: SgC2SInventedOccupyStrategyService
 * description: 配销层虚拟寻源策略
 *
 * <AUTHOR>
 * create: 2022-03-07
 * @since JDK 1.8
 */
@Component
@Slf4j
@Deprecated
public class SgC2SInventedOccupyStrategyService extends StrategyHandle {

    @Autowired
    private SgBShareOutMapper shareOutMapper;

    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {
        ValueHolderV14<StrategyBaseResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        if(log.isDebugEnabled()){
            log.debug(" 一阶段虚拟寻源,param：{}", JSON.toJSONString(request));
        }
        try {
            SgFindSourceStrategyC2SRequest strategyRequest = (SgFindSourceStrategyC2SRequest) request;
            SgFindSourceStrategyC2SResult strategyC2SResult = (SgFindSourceStrategyC2SResult) strategyRequest.getStrategyBaseResult();
            if(ObjectUtils.isEmpty(strategyC2SResult)){
                return doNext(request,valueHolderV14);
            }
            //整理出所有候选配销仓
            Map<Long, Set<Long>> storeIdSourceItemMap = new HashMap<>();
            List<SgFindSourceStrategySkuC2SResult> skuResultList = strategyC2SResult.getSkuResultList();
            if(CollectionUtils.isNotEmpty(skuResultList)){
                return doNext(request,valueHolderV14);
            }
            skuResultList.forEach(result -> {
                List<SgFindSourceStrategyStoreItemC2SResult> itemC2SResults = result.getItemResult();
                if(CollectionUtils.isNotEmpty(itemC2SResults)){
                    itemC2SResults.forEach(itemC2SResult -> {
                        if(!StrategyConstants.OUT_DEFAULT_STORE_ID.equals(itemC2SResult.getSgCShareStoreId())){
                            Set<Long> itemSet = storeIdSourceItemMap.computeIfAbsent(itemC2SResult.getSgCShareStoreId(), k -> new HashSet<>());
                            itemSet.add(result.getSourceItemId());
                        }
                    });
                }
            });

            //查询已占用共享占用单（唯品会有时效单先占一部分的情况）
            List<Long> timeOrderIds = strategyRequest.getSkuItems().stream()
                    .filter(i -> !ObjectUtils.isEmpty(i.getTimeOrderId()))
                    .map(SkuItemC2S::getTimeOrderId).collect(Collectors.toList());
            List<SgBShareOutInfoResult> sgBShareOuts = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(timeOrderIds)) {
                //时效单占用
                List<SgBShareOutInfoResult> vipSgBShareOuts = shareOutMapper.findShareOutAndItem(timeOrderIds, SgConstantsIF.BILL_TYPE_VIPSHOP_TIME);
                if(CollectionUtils.isNotEmpty(vipSgBShareOuts)){
                    sgBShareOuts.addAll(vipSgBShareOuts);
                }
            }
            //零售发货单占用（兼容其他单据）
            List<SgBShareOutInfoResult> retailShareOuts = shareOutMapper
                    .selectShareOutBySourceBillId(strategyRequest.getSourceBillId(), strategyRequest.getSourceBillType());
            if(CollectionUtils.isNotEmpty(retailShareOuts)){
                sgBShareOuts.addAll(retailShareOuts);
            }
            if(CollectionUtils.isNotEmpty(sgBShareOuts)){
                sgBShareOuts.forEach(o -> {
                    Set<Long> itemSet = storeIdSourceItemMap.computeIfAbsent(o.getSgCShareStoreId(), k -> new HashSet<>());
                    itemSet.add(o.getSourceItemId());
                });
            }

            //计算能满足最多明细的聚合仓
            AtomicLong optimalShareId = new AtomicLong(-1L);
            AtomicInteger lastSize = new AtomicInteger(0);
            storeIdSourceItemMap.forEach((k,v) -> {
                if(v.size() > lastSize.get()){
                    optimalShareId.set(k);
                    lastSize.set(v.size());
                }
            });


            //将最多满足的配销仓优先级置为最大
            skuResultList.forEach(result -> {
                List<SgFindSourceStrategyStoreItemC2SResult> itemC2SResults = result.getItemResult();
                if(CollectionUtils.isNotEmpty(itemC2SResults)){
                    itemC2SResults.forEach(itemC2SResult -> {
                        if(optimalShareId.get() == itemC2SResult.getSgCShareStoreId()){
                            itemC2SResult.setPriority(Integer.MAX_VALUE);
                        }
                    });
                }
            });

            valueHolderV14.setData(strategyC2SResult);
        }catch (Exception e){
            log.error("C->S寻源策略 配销层虚拟寻源策略 执行异常:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("C->S寻源策略 配销层虚拟寻源策略 执行异常");
        }

        return doNext(request,valueHolderV14);
    }
}
