package com.burgeon.r3.sg.sourcing.services.item;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemS2L;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.*;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutNoticesRejectMapper;
import com.burgeon.r3.sg.store.model.request.SgBSelectRejectInfoRequest;
import com.burgeon.r3.sg.store.model.result.SgBSelectRejectInfoResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 拒单表店仓过滤策略
 * @author: hwy
 * @time: 2021/7/9 15:38
 */
@Component
@Slf4j
public class SgFindSourceRejectStoreFilterStrategyService extends StrategyHandle {

    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {

        log.info(LogUtil.format("SgFindSourceRejectStoreFilterStrategyService.handleRequest S->L二阶段寻源派单 拒单策略执行器 param:{}",
                SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L),
                request.getTraceId(), JSONObject.toJSONString(request));

        ValueHolderV14<StrategyBaseResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        SgFindSourceStrategyS2LRequest strategyS2LRequest = (SgFindSourceStrategyS2LRequest) request;
        SgFindSourceStrategyS2LResult strategyResult = (SgFindSourceStrategyS2LResult) strategyS2LRequest.getStrategyBaseResult();
        valueHolderV14.setData(strategyResult);

        //订单明细
        List<SkuItemS2L> skuItems = strategyS2LRequest.getSkuItems();
        //全部系统条码
        List<Long> psCSkuIds = skuItems.stream().map(SkuItemS2L::getPsCSkuId).distinct().collect(Collectors.toList());

        SgBStoOutNoticesRejectMapper rejectMapper = ApplicationContextHandle.getBean(SgBStoOutNoticesRejectMapper.class);
        SgBSelectRejectInfoRequest rejectInfoRequest = new SgBSelectRejectInfoRequest();
        rejectInfoRequest.setBillDate(new Date());
        rejectInfoRequest.setCpCShopId(strategyS2LRequest.getShopId());
        rejectInfoRequest.setPsCSkuIds(psCSkuIds);
        List<SgBSelectRejectInfoResult> sgBSelectRejectInfoResults = rejectMapper.selectRejectInfo(rejectInfoRequest);

        //没有配置,进行下一个策略
        if (CollectionUtils.isEmpty(sgBSelectRejectInfoResults)) {
            FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 拒单策略获取出库通知单中拒单信息结果:未找到店铺ID:{}，条码ID:{}下有效的拒单信息!",
                    strategyS2LRequest.getShopId(), psCSkuIds);
            return doNext(request, valueHolderV14);
        }

        Map<Long, List<SgBSelectRejectInfoResult>> rejectInfoMap = sgBSelectRejectInfoResults.stream().collect(
                Collectors.groupingBy(SgBSelectRejectInfoResult::getPsCSkuId));

        try {

            //将结果集中的逻辑仓过滤 更新结果集
            boolean isAllItemhasNoStore = updateResult(strategyS2LRequest, rejectInfoMap);

            //判断订单明细中的实体仓是否全部被[拒单表店仓过滤策略]过滤后造成实体仓为空
            if (isAllItemhasNoStore) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(Resources.getMessage("逻辑仓存在拒单记录，寻源失败!"));
            }

        } catch (Exception e) {
            log.error(LogUtil.format("SgFindSourceFilterStrategyService.handleRequest S->L二阶段寻源派单 拒单策略发生异常 exception:{}",
                    SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L_EXCEPTION),
                    request.getTraceId(), Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("S->L 二阶段寻源派单 拒单策略发生异常!");
            return valueHolderV14;
        }
        return doNext(request, valueHolderV14);
    }

    /**
     * @param strategyRequest:
     * @param rejectInfoMap:
     * @Description: 将结果集中的逻辑仓过滤 更新结果集
     * @Author: chenb
     * @Date: 2022/8/1 16:47
     * @return: void
     **/
    private boolean updateResult(SgFindSourceStrategyS2LRequest strategyRequest,
                                 Map<Long, List<SgBSelectRejectInfoResult>> rejectInfoMap) {

        FindSourceStrategyUtils.outputLog(
                LogUtil.format("SgFindSourceRejectStoreFilterStrategyService.updateResult S->L二阶段寻源派单 拒单策略需要过滤的条码:{}",
                        SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L),
                strategyRequest.getTraceId(), rejectInfoMap.keySet());

        SgFindSourceStrategyS2LResult strategyResult = (SgFindSourceStrategyS2LResult) strategyRequest.getStrategyBaseResult();
        List<SgFindSourceStrategySkuS2LResult> skuResultList = strategyResult.getSkuResultList();
        List<SgFindSourceStrategyStoreItemS2LResult> newItemResultList = null;
        List<SgFindSourceStrategyStoreItemS2LResult> itemResultList = null;
        List<SgBSelectRejectInfoResult> rejectInfoResults = null;
        Map<Long, String> rejectStoreEnameMap = new HashMap<>();
        Map<Long, SortedMap<String, BigDecimal>> logicStorageMap = null;
        Map<String, BigDecimal> productDateQtyMap = null;
        Long psCSkuId = null;
        Long cpCStoreId = null;
        BigDecimal storageQty = null;
        //当前订单没有满足条件的逻辑仓
        boolean isAllItemhasNoStore = Boolean.TRUE;
        //当前明细的没有满足条件的逻辑仓
        boolean isItemhasNoStore = Boolean.TRUE;
        //拒单排除的逻辑仓信息
        Map<Long, String> filterStores = new HashMap<>();

        for (SgFindSourceStrategySkuS2LResult skuS2LResult : skuResultList) {

            psCSkuId = skuS2LResult.getPsCSkuId();

            //拒单信息里不包含该条码
            if (!rejectInfoMap.containsKey(psCSkuId)) {
                continue;
            }

            itemResultList = skuS2LResult.getItemResultList();

            // 设置新结果集
            newItemResultList = new ArrayList<>(itemResultList.size());
            rejectStoreEnameMap = new HashMap<>();

            for (SgFindSourceStrategyStoreItemS2LResult itemResult : itemResultList) {

                BigDecimal qty = itemResult.getQty();
                logicStorageMap = itemResult.getLogicStorageMap();
                rejectInfoResults = rejectInfoMap.get(psCSkuId);

                for (SgBSelectRejectInfoResult rejectInfoResult : rejectInfoResults) {

                    cpCStoreId = rejectInfoResult.getCpCStoreId();

                    //不存在逻辑仓库存 跳过
                    if (!logicStorageMap.containsKey(cpCStoreId)) {
                        continue;
                    }

                    //设置拒单逻辑仓（明细）
                    rejectStoreEnameMap.put(cpCStoreId, rejectInfoResult.getCpCStoreEname());
                    //设置拒单总逻辑仓
                    filterStores.put(cpCStoreId, rejectInfoResult.getCpCStoreEname());

                    productDateQtyMap = logicStorageMap.get(cpCStoreId);
                    storageQty = BigDecimal.ZERO;

                    //根据逻辑仓ID，进行跨生产日期可用数量聚合
                    if (!MapUtils.isEmpty(productDateQtyMap)) {
                        for (BigDecimal value : productDateQtyMap.values()) {
                            storageQty = storageQty.add(value == null ? BigDecimal.ZERO : value);
                        }
                    }

                    //移除拒单逻辑仓 并扣除库存
                    qty = qty.subtract(storageQty);
                    logicStorageMap.remove(cpCStoreId);
                }

                // 实体仓剩余可用为0 清除结果
                if (BigDecimal.ZERO.compareTo(qty) >= 0) {
                    SgFindSourceStrategyStoreItemS2LResult newItemResult = new SgFindSourceStrategyStoreItemS2LResult();
                    newItemResult.setQty(itemResult.getQty());
                    newItemResult.setStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                    newItemResult.setLogicStorageMap(new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY));
                    newItemResultList.add(newItemResult);
                    continue;
                }

                isAllItemhasNoStore = Boolean.FALSE;
                isItemhasNoStore = Boolean.FALSE;
                // 设置新的可用数量
                itemResult.setQty(qty);
                newItemResultList.add(itemResult);
            }

            //当前明细没有满足条件的逻辑仓，记录缺货原因
            if (isItemhasNoStore) {

                FindSourceStrategyUtils.outputLog(
                        LogUtil.format("SgFindSourceRejectStoreFilterStrategyService.updateResult S->L二阶段寻源派单 拒单策略拒单的逻辑仓:{}",
                                SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L),
                        strategyRequest.getTraceId(), rejectStoreEnameMap.keySet());

                SgFindSourceStrategyOutOfStockBaseResult outOfSStockResult = new SgFindSourceStrategyOutOfStockBaseResult();
                outOfSStockResult.setSourceItemId(skuS2LResult.getSourceItemId());
                outOfSStockResult.setShareItemId(skuS2LResult.getShareItemId());
                outOfSStockResult.setPsCSkuId(skuS2LResult.getPsCSkuId());
                outOfSStockResult.setOutOfStockMessage("逻辑仓:" + rejectStoreEnameMap.values() + "存在拒单记录，寻源失败!");
                strategyRequest.getStrategyBaseResult().getOutOfStockMessages().add(outOfSStockResult);
            }

            skuS2LResult.setItemResultList(newItemResultList);
        }

        //设置强制排除的逻辑仓信息
        if (!MapUtils.isEmpty(filterStores)) {
            Map removedStoreInfo = strategyResult.getRemovedStoreInfo();
            removedStoreInfo.put(SgSourcingConstants.MSG_REMOVED_STORE_INFO_STORE_REJECT, filterStores.values().toString());
        }

        return isAllItemhasNoStore;

    }
}