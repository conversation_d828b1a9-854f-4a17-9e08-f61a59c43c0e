package com.burgeon.r3.sg.sourcing.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.config.SgStorageMqConfig;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.SgMonitorMqEnum;
import com.burgeon.r3.sg.core.model.request.SgBMonitorMqErrorDatasRequest;
import com.burgeon.r3.sg.core.model.table.sourcing.SgBSourcingRetry;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.core.utils.SgMonitorUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutMapper;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutInfoResult;
import com.burgeon.r3.sg.sourcing.common.SourceOrderTypeEnum;
import com.burgeon.r3.sg.sourcing.common.SourceTypeEnum;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.mapper.SgBSourcingRetryMapper;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyC2SRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemC2S;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemS2L;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyC2SResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsForVipTimeResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsItemForVipTimeResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsItemResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuC2SResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemC2SResult;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BeanCopierUtil;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: hwy
 * @time: 2021/6/30 13:34
 */
@Component
@Slf4j
public class SgFindSoureceC2SSendMQService {

    @Autowired
    private PropertiesConf proConfig;

    //    @Autowired
//    private R3MqSendHelper r3MqSendHelper;
    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private SgMonitorUtils sgMonitorUtils;

    @Autowired
    private SgBShareOutMapper sgBShareOutMapper;

    @Autowired
    private SgBSourcingRetryMapper sgBSourcingRetryMapper;
    @Autowired
    private SgStorageMqConfig sgStorageMqConfig;


    /**
     * @param requestMap:
     * @param request:
     * @Description: 唯品会订单关联未完全占用的时效订单重新占单
     * @Author: hwy
     * @Date: 2021/9/7 10:33
     * @return: void
     **/
    @Transactional(rollbackFor = Exception.class)
    public void vipFindSourceRetry(Map<String, List<SgFindSourceStrategyC2SRequest>> requestMap, SgFindSourceStrategyC2SRequest request) {
        FindSourceStrategyUtils.outputLog("SgFindSoureceC2SSendMQService.vipFindSourceRetry 来源单据:{} 时效订单未完全占用库存，使用未完全占用的时效订单再次占用 未完全占用时效订单数量:{}", request.getSourceBillNo(), requestMap.values().size());
        String topic = "";
        String tag = "";
        String body = "";
        String msgKey = "";
        Map<String, String> vipTimeOrderBillNoMap = new HashMap<>();
        String pram = JSONObject.toJSONString(request);
        try {
            //时效单
            List<SgFindSourceStrategyC2SRequest> vipRequests = requestMap.get(SourceOrderTypeEnum.VIP_TYPE.getCode());
            if (CollectionUtils.isNotEmpty(vipRequests)) {
                topic = proConfig.getProperty(StrategyConstants.APOLLO_KEY_SOURCING_VIP_TIME_TOPIC);
//                String configName = proConfig.getProperty(StrategyConstants.APOLLO_KEY_CONFIG_NAME);
                tag = proConfig.getProperty(StrategyConstants.APOLLO_KEY_SOURCING_VIP_TIME_TAG);
                for (SgFindSourceStrategyC2SRequest mqRequest : vipRequests) {
                    body = JSONObject.toJSONString(mqRequest);
                    String sourceBillNo = mqRequest.getSourceBillNo();
                    msgKey = StrategyConstants.MQ_PREFIX_VIP_TIEM + request.getSourceBillNo() + "_" + DateUtil.getDateTime(SgConstants.MQ_DATE_FORMAT);
                    FindSourceStrategyUtils.outputLog("唯品会时效订单再次占用 一阶段寻源派单引擎 来源订单:{} 一阶段寻源派单结束 发送消息到sg 进行一阶段寻源 topic:{} messageKey:{} messageBody:{}", request.getSourceBillId(), topic, msgKey, body);
//                    r3MqSendHelper.sendMessage(configName, body, topic, tag, msgKey);
                    defaultProducerSend.sendTopic(topic,
                            tag,
                            body,
                            msgKey);
                    vipTimeOrderBillNoMap.put(sourceBillNo, sourceBillNo);
                }
            }

            List<SgFindSourceStrategyC2SRequest> retailRequests = requestMap.get(SourceOrderTypeEnum.NORMAL_TYPE.getCode());
            if (CollectionUtils.isNotEmpty(retailRequests)) {
                for (SgFindSourceStrategyC2SRequest retailRequest : retailRequests) {
                    String sourceBillNo = retailRequest.getSourceBillNo();
                    body = JSONObject.toJSONString(retailRequest);
//                    topic = proConfig.getProperty(StrategyConstants.APOLLO_KEY_SOURCING_RETAIL_TOPIC);
                    topic = StrategyConstants.APOLLO_KEY_SOURCING_RETAIL_TOPIC;
//                    String configName = proConfig.getProperty(StrategyConstants.APOLLO_KEY_CONFIG_NAME);
//                    tag = proConfig.getProperty(StrategyConstants.APOLLO_KEY_SOURCING_RETAIL_TAG);
                    tag = StrategyConstants.APOLLO_KEY_SOURCING_RETAIL_TAG;
                    msgKey = StrategyConstants.MQ_PREFIX_RETAIL_ITEM + request.getSourceBillNo() + "_" + DateUtil.getDateTime(SgConstants.MQ_DATE_FORMAT);
//                    r3MqSendHelper.sendMessage(configName, body, topic, tag, msgKey);
                    defaultProducerSend.sendTopic(topic,
                            body,
                            tag,
                            msgKey);
                    vipTimeOrderBillNoMap.put(sourceBillNo, sourceBillNo);

                    //配货单需重试的时候可能会拿不到锁，这里先释放
                    String lockKey = SgConstants.REDIS_KEY_FIND_SOURCE_STRATEGY_C2S + ":" + request.getSourceBillType() + ":" + request.getSourceBillId();
                    SgRedisLockUtils.unlock(lockKey, log, this.getClass().getName());
                }

            }
            //记录待重试的数据
            SgBSourcingRetry sgBSourcingRetry = new SgBSourcingRetry();
            Long sequence = ModelUtil.getSequence(SgConstants.SG_B_CHANNEL_STORAGE_INC_SYNC_ITEM);
            sgBSourcingRetry.setId(sequence);
            sgBSourcingRetry.setSourceBillId(request.getSourceBillId());
            sgBSourcingRetry.setSourceBillNo(request.getSourceBillNo());
            sgBSourcingRetry.setSourceBillType(request.getSourceBillType());
            StorageUtils.setBModelDefalutData(sgBSourcingRetry, R3SystemUserResource.getSystemRootUser());
            sgBSourcingRetryMapper.insert(sgBSourcingRetry);
            // 设置来源订单key
            RedisMasterUtils.getStrRedisTemplate().opsForHash().putAll(StrategyConstants.VIP_RETRY_REDIS_KEY_PREFIX + request.getSourceBillNo(), vipTimeOrderBillNoMap);
            // 设置自动过期
            RedisMasterUtils.getObjRedisTemplate().expire(StrategyConstants.VIP_RETRY_REDIS_KEY_PREFIX + request.getSourceBillNo(),
                    3, TimeUnit.DAYS);
            // 保存原始入参
            RedisMasterUtils.getStrRedisTemplate().opsForValue().set(StrategyConstants.VIP_RETRY_PARAM_REDIS_KEY_PREFIX + request.getSourceBillNo(), pram, 3, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("来源订单:{} 来源单据类型:{} 唯品会时效订单再次占用发送mq异常:{} ", request.getSourceBillNo(), request.getSourceBillType(), Throwables.getStackTraceAsString(e));
            throw new NDSException("唯品会时效订单再次占用发送mq异常");
        }
    }

    /**
     * @param request:
     * @param valueHolderV14:
     * @Description: 时效订单发送mq到oms
     * @Author: hwy
     * @Date: 2021/9/7 10:33
     * @return: void
     **/
    public void sendVipPreOutMsq(SgFindSourceStrategyC2SRequest request, ValueHolderV14 valueHolderV14) {
        FindSourceStrategyUtils.outputLog("SgFindSoureceC2SSendMQService.sendOmsMq 来源单据:{} 唯品会时效订单库存占用结束 发送消息通知订单中心", request.getSourceBillNo());
        String topic = "";
        String tag = "";
        String body = "";
        String msgKey = "";
        try {

            topic = proConfig.getProperty(StrategyConstants.APOLLO_KEY_SOURCING_VIP_TIME_CALL_BACK_TOPIC);
//            String configName = proConfig.getProperty(StrategyConstants.APOLLO_KEY_CONFIG_NAME);
            tag = proConfig.getProperty(StrategyConstants.APOLLO_KEY_SOURCING_VIP_TIME_CALL_BACK_TAG);
            SgFindSourceStrategyOmsForVipTimeResult vipTimeResult = new SgFindSourceStrategyOmsForVipTimeResult();
            vipTimeResult.setCode(valueHolderV14.getCode());
            vipTimeResult.setSourceBillId(request.getSourceBillId());
            vipTimeResult.setSourceBillType(request.getSourceBillType());
            vipTimeResult.setSourceBillNo(request.getSourceBillNo());
            List<SgFindSourceStrategyOmsItemForVipTimeResult> itemList = new ArrayList<>();
            vipTimeResult.setItemResultList(itemList);
            if (!valueHolderV14.isOK()) {
                log.error("SgFindSoureceC2SSendMQService.sendOmsMq 来源单据:{} 唯品会时效订单库存占用异常:{}", request.getSourceBillNo(), valueHolderV14.getMessage());
                List<SkuItemC2S> skuItems = request.getSkuItems();
                if (CollectionUtils.isNotEmpty(skuItems)) {
                    for (SkuItemC2S skuItemC2S : skuItems) {
                        BigDecimal qty = skuItemC2S.getQty();
                        Long sourceItemId = skuItemC2S.getSourceItemId();
                        SgFindSourceStrategyOmsItemForVipTimeResult itemForVipTimeResult = new SgFindSourceStrategyOmsItemForVipTimeResult();
                        itemForVipTimeResult.setQtyPreOut(qty);
                        itemForVipTimeResult.setSourceItemId(sourceItemId);
                        itemForVipTimeResult.setSgCShareStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                        itemList.add(itemForVipTimeResult);
                        continue;
                    }
                }

            } else {
                SgBShareOutMapper sgBShareOutMapper = ApplicationContextHandle.getBean(SgBShareOutMapper.class);
                List<SgBShareOutInfoResult> sgBShareOuts = sgBShareOutMapper.selectShareOutBySourceBillId(request.getSourceBillId(), request.getSourceBillType());
                Map<Long, List<SgBShareOutInfoResult>> sgBShareOutMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(sgBShareOuts)) {
                    sgBShareOutMap.putAll(sgBShareOuts.stream().collect(Collectors.groupingBy(SgBShareOutInfoResult::getSourceItemId)));
                }
                List<SkuItemC2S> skuItems = request.getSkuItems();
                for (SkuItemC2S skuItemC2S : skuItems) {
                    BigDecimal qty = skuItemC2S.getQty();
                    Long sourceItemId = skuItemC2S.getSourceItemId();
                    if (!sgBShareOutMap.containsKey(sourceItemId)) {
                        SgFindSourceStrategyOmsItemForVipTimeResult itemForVipTimeResult = new SgFindSourceStrategyOmsItemForVipTimeResult();
                        itemForVipTimeResult.setQtyPreOut(qty);
                        itemForVipTimeResult.setSourceItemId(sourceItemId);
                        itemForVipTimeResult.setSgCShareStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                        itemList.add(itemForVipTimeResult);
                        continue;
                    }
                    List<SgBShareOutInfoResult> sgBShareOutInfoResults = sgBShareOutMap.get(sourceItemId);
                    BigDecimal qtySharePreout = BigDecimal.ZERO;
                    for (SgBShareOutInfoResult sgBShareOutInfoResult : sgBShareOutInfoResults) {
                        BigDecimal qtyPreout = sgBShareOutInfoResult.getQtyPreout();
                        SgFindSourceStrategyOmsItemForVipTimeResult itemForVipTimeResult = new SgFindSourceStrategyOmsItemForVipTimeResult();
                        itemForVipTimeResult.setQtyPreOut(qtyPreout);
                        itemForVipTimeResult.setSgCShareStoreId(sgBShareOutInfoResult.getSgCShareStoreId());
                        itemForVipTimeResult.setSgCShareStoreEcode(sgBShareOutInfoResult.getSgCShareStoreEcode());
                        itemForVipTimeResult.setSgCShareStoreEcode(sgBShareOutInfoResult.getSgCShareStoreEname());
                        itemForVipTimeResult.setShareOutBillNo(sgBShareOutInfoResult.getBillNo());
                        itemForVipTimeResult.setSourceItemId(sourceItemId);
                        itemList.add(itemForVipTimeResult);
                        qtySharePreout = qtySharePreout.add(qtyPreout);
                    }
                    if (qty.compareTo(qtySharePreout) > 0) {
                        SgFindSourceStrategyOmsItemForVipTimeResult itemForVipTimeResult = new SgFindSourceStrategyOmsItemForVipTimeResult();
                        itemForVipTimeResult.setQtyPreOut(qty.subtract(qtySharePreout));
                        itemForVipTimeResult.setSourceItemId(sourceItemId);
                        itemForVipTimeResult.setSgCShareStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                        itemList.add(itemForVipTimeResult);
                        continue;
                    }
                }
            }
            body = JSONObject.toJSONString(vipTimeResult);
            msgKey = StrategyConstants.MQ_PREFIX_VIP_TIEM + request.getSourceBillNo() + "_" + DateUtil.getDateTime(SgConstants.MQ_DATE_FORMAT);
            FindSourceStrategyUtils.outputLog("唯品会时效订单 一阶段寻源派单引擎 来源订单:{} 一阶段寻源派单结束 发送消息到oms topic:{} messageKey:{} messageBody:{}", request.getSourceBillId(), topic, msgKey, body);
            if (!request.getReceiptFlag()) {
//                r3MqSendHelper.sendMessage(configName, body, topic, tag, msgKey);
                defaultProducerSend.sendTopic(topic,
                        tag,
                        body,
                        msgKey);
            }

        } catch (Exception e) {
            log.error(" 唯品会时效订单 一阶段寻源派单引擎 来源订单:{} 发送消息到oms失败:{}", request.getSourceBillNo(), Throwables.getStackTraceAsString(e));
            SgBMonitorMqErrorDatasRequest sgBMonitorMqErrorDatasRequest = new SgBMonitorMqErrorDatasRequest();
            sgBMonitorMqErrorDatasRequest.setConsumer(SgMonitorMqEnum.OMS);
            sgBMonitorMqErrorDatasRequest.setProducer(SgMonitorMqEnum.SG);
            sgBMonitorMqErrorDatasRequest.setMessage(body);
            sgBMonitorMqErrorDatasRequest.setMsgKey(msgKey);
            sgBMonitorMqErrorDatasRequest.setTag(tag);
            sgBMonitorMqErrorDatasRequest.setTopic(topic);
            sgMonitorUtils.saveMqErrorDatas(sgBMonitorMqErrorDatasRequest, e, R3SystemUserResource.getSystemRootUser());
            return;
        } finally {
            String retryRedisKey = request.getRetryRedisKey();
            // 检查redis是否存在部分寻仓单引起的时效订单占单key 如果是 则删除key
            if (StringUtils.isNotEmpty(retryRedisKey)) {
                removeKey(request.getSourceBillNo(), retryRedisKey);
            }
        }

    }

    /**
     * @param billNo:
     * @Description: 更新唯品会订单寻源
     * @Author: hwy
     * @Date: 2021/9/6 21:56
     * @return: void
     **/
    public void removeKey(String billNo, String redisKey) {
        try {
            Boolean aBoolean = RedisMasterUtils.getStrRedisTemplate().hasKey(redisKey);
            if (aBoolean) {
                log.debug("更新唯品会订单寻源 redisKey 当前时效订单编号:{}", billNo);
                RedisMasterUtils.getStrRedisTemplate().opsForHash().delete(redisKey, billNo);
                Long size = RedisMasterUtils.getStrRedisTemplate().opsForHash().size(redisKey);
                log.debug("更新唯品会订单寻源 redisKey 当前时效订单编号:{} 关联寻仓单剩余待占用时效单数量:{}", billNo, size);
            }
        } catch (Exception e) {
            log.error("更新唯品会订单寻源 redisKey 数据失败");
        }
    }

    /**
     * @param request:
     * @Description: 发送消息通知第二阶段寻源
     * @Author: hwy
     * @Date: 2021/6/29 19:44
     * @return: void
     **/
    public void sendMq(SgFindSourceStrategyC2SRequest request) {
        FindSourceStrategyUtils.outputLog("SgFindSoureceC2SSendMQService.sendMq 来源单据:{} 第一阶段库存占用成功 开始准备第二阶段寻源派单占用库存", request.getSourceBillNo());
        SgBShareOutMapper sgBShareOutMapper = ApplicationContextHandle.getBean(SgBShareOutMapper.class);
        List<SgBShareOutInfoResult> sgBShareOuts = sgBShareOutMapper.selectShareOutBySourceBillId(request.getSourceBillId(), request.getSourceBillType());
        if (CollectionUtils.isEmpty(sgBShareOuts)) {
            FindSourceStrategyUtils.outputLog("SgFindSoureceC2SSendMQService.sendMq 不存在已占用的共享占用单 订单全部占用失败");
            throw new NDSException("SgFindSoureceC2SSendMQService.sendMq 不存在已占用的共享占用单 订单全部占用失败");
        }
        //过滤出  按照合并的标记,  合并后的结果集取 共享日志明细表
        Map<Boolean, List<SgBShareOutInfoResult>> mergeMap = sgBShareOuts.stream().collect(Collectors.groupingBy(SgBShareOutInfoResult::getMergeMark));
        //未合并的结果集
        sgBShareOuts = mergeMap.get(Boolean.FALSE);
        if (CollectionUtils.isEmpty(sgBShareOuts)) {
            sgBShareOuts = new ArrayList<>();
        }
        //合并的结果集
        List<SgBShareOutInfoResult> shareOutInfoResults = mergeMap.get(Boolean.TRUE);
        if (CollectionUtils.isNotEmpty(shareOutInfoResults)) {
            List<Long> ids = shareOutInfoResults.stream().map(SgBShareOutInfoResult::getBillId).collect(Collectors.toList());
            List<SgBShareOutInfoResult> sgBShareOutLogs = sgBShareOutMapper.selectShareOutByLogIds(ids);
            if (CollectionUtils.isNotEmpty(sgBShareOutLogs)) {
                sgBShareOuts.addAll(sgBShareOutLogs);
            }
        }

        List<SkuItemC2S> skuItems = request.getSkuItems();
        List<SkuItemS2L> skuItemList = new ArrayList<>();
        SgFindSourceStrategyS2LRequest strategyRequest = new SgFindSourceStrategyS2LRequest();
        strategyRequest.setLogisticsInfo(request.getLogisticsInfo());
        strategyRequest.setInventedOccupy(request.getInventedOccupy());
        strategyRequest.setShopId(request.getShopId());
        strategyRequest.setSourceBillId(request.getSourceBillId());
        strategyRequest.setSourceBillNo(request.getSourceBillNo());
        strategyRequest.setSourceBillType(request.getSourceBillType());
        strategyRequest.setSplitType(request.getSplitType());
        strategyRequest.setBillDate(request.getBillDate());
        strategyRequest.setTid(request.getTid());
        strategyRequest.setAreaId(request.getAreaId());
        strategyRequest.setCityId(request.getCityId());
        strategyRequest.setProvinceId(request.getProvinceId());
        strategyRequest.setVipOrderFlag(request.getVipOrderFlag());
        strategyRequest.setStoreDeliveryFlag(request.getStoreDeliveryFlag());
        strategyRequest.setWarehouseEcode(request.getWarehouseEcode());
        strategyRequest.setSkuItems(skuItemList);
        strategyRequest.setExcludeWarehouseEcode(request.getExcludeWarehouseEcode());
        strategyRequest.setIsDeliveryToDoor(request.getIsDeliveryToDoor());
        strategyRequest.setIsTobOrder(request.getIsTobOrder());
        strategyRequest.setExpirySaStoreMap(request.getExpirySaStoreMap());
        strategyRequest.setValidityMap(request.getValidityMap());

        Map<Long, List<SgBShareOutInfoResult>> shareOutMap = sgBShareOuts.stream().collect(Collectors.groupingBy(SgBShareOutInfoResult::getSourceItemId));
        SgFindSourceStrategyC2SResult strategyC2SResult = (SgFindSourceStrategyC2SResult) request.getStrategyBaseResult();
        Map<Long, SgFindSourceStrategySkuC2SResult> skuResultMap = strategyC2SResult.getSkuResultList().stream()
                .collect(Collectors.toMap(SgFindSourceStrategySkuC2SResult::getSourceItemId, Function.identity()));

        AtomicLong tempId = new AtomicLong(1L);
        skuItems.forEach(skuItemC2S -> {
            Long sourceItemId = skuItemC2S.getSourceItemId();

            //如果是虚拟寻源，将一阶段结果直接放入二阶段参数，不依赖共享占用单
            if (Boolean.TRUE.equals(request.getInventedOccupy())) {
                SgFindSourceStrategySkuC2SResult skuC2SResult = skuResultMap.get(sourceItemId);
                if (ObjectUtils.isNotEmpty(skuC2SResult)) {
                    List<SgFindSourceStrategyStoreItemC2SResult> itemResult = skuC2SResult.getItemResult();
                    if (CollectionUtils.isNotEmpty(itemResult)) {
                        SgFindSourceStrategyStoreItemC2SResult itemC2SResult = itemResult.get(0);
                        if (!StrategyConstants.OUT_DEFAULT_STORE_ID.equals(itemC2SResult.getSgCShareStoreId())) {
                            SkuItemS2L skuItem = new SkuItemS2L();
                            BeanCopierUtil.copy(skuItemC2S, skuItem);
                            skuItem.setQtyPreOut(skuItemC2S.getQty());
                            //因为二阶段很多地方用-1表示缺货，这里用-2防止被过滤
                            skuItem.setShareItemId(tempId.get());
                            skuItem.setShareBillId(tempId.get());
                            skuItem.setShareStoreId(itemC2SResult.getSgCShareStoreId());
                            skuItem.setSaStoreId(itemC2SResult.getSgCSaStoreId());
                            skuItemList.add(skuItem);
                            //防止二阶段虚拟寻源id重复
                            tempId.set(tempId.get() + 1L);
                            return;
                        }
                    }
                }
                SkuItemS2L skuItem = new SkuItemS2L();
                BeanCopierUtil.copy(skuItemC2S, skuItem);
                skuItem.setQtyPreOut(BigDecimal.ZERO);
                skuItem.setShareItemId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                skuItem.setShareBillId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                skuItem.setShareStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                skuItem.setSaStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                skuItemList.add(skuItem);
                return;
            }

            if (shareOutMap.containsKey(sourceItemId)) {
                List<SgBShareOutInfoResult> sgBShareOutInfoResults = shareOutMap.get(sourceItemId);
                BigDecimal allPreOutQty = sgBShareOutInfoResults.stream().map(sgBShareOut -> {
                    SkuItemS2L skuItem = new SkuItemS2L();
                    BeanCopierUtil.copy(skuItemC2S, skuItem);
                    skuItem.setQtyPreOut(sgBShareOut.getQtyPreout());
                    skuItem.setShareItemId(sgBShareOut.getItemId());
                    skuItem.setShareBillId(sgBShareOut.getBillId());
                    skuItem.setShareStoreId(sgBShareOut.getSgCShareStoreId());
                    skuItem.setSaStoreId(sgBShareOut.getSgCSaStoreId());
                    skuItemList.add(skuItem);
                    return sgBShareOut.getQtyPreout();
                }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                if (skuItemC2S.getQty().compareTo(allPreOutQty) > 0) {
                    SkuItemS2L skuItem = new SkuItemS2L();
                    BeanCopierUtil.copy(skuItemC2S, skuItem);
                    skuItem.setQtyPreOut(BigDecimal.ZERO);
                    skuItem.setShareItemId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                    skuItem.setShareBillId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                    skuItem.setShareStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                    skuItem.setSaStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                    skuItemList.add(skuItem);
                }
            } else {
                SkuItemS2L skuItem = new SkuItemS2L();
                BeanCopierUtil.copy(skuItemC2S, skuItem);
                skuItem.setQtyPreOut(BigDecimal.ZERO);
                skuItem.setShareItemId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                skuItem.setShareBillId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                skuItem.setShareStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                skuItem.setSaStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                skuItemList.add(skuItem);
            }
        });
        //MQ 发送二阶段寻源
        sendR3Mq(strategyRequest, request);
    }

    /**
     * 寻仓单  配货单 二阶段寻源
     *
     * @param request
     */
    public void sendMqByWarehouse(SgFindSourceStrategyC2SRequest request) {
        SgFindSourceStrategyS2LRequest strategyRequest = new SgFindSourceStrategyS2LRequest();
        Boolean flag = false;
        try {
            FindSourceStrategyUtils.outputLog("SgFindSoureceC2SSendMQService.sendMqByWarehouse 来源单据:{} 开始准备第二阶段寻源派单占用库存", request.getSourceBillNo());
            List<SkuItemC2S> skuItems = request.getSkuItems();
            List<SkuItemC2S> vipList = new ArrayList<>();
            skuItems.stream().forEach(skuItemC2S -> {
                if (ObjectUtils.isNotEmpty(skuItemC2S.getTimeOrderId())) {
                    vipList.add(skuItemC2S);
                }
            });
            List<Long> timeOrderIds = vipList.stream().map(skuItemC2S -> skuItemC2S.getTimeOrderId()).collect(Collectors.toList());
            List<SgBShareOutInfoResult> vipSgBShareOuts = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(timeOrderIds)) {
                vipSgBShareOuts = sgBShareOutMapper.findShareOutAndItem(timeOrderIds, SgConstantsIF.BILL_TYPE_VIPSHOP_TIME);
            }
            //明细中没有时效id
            List<SgBShareOutInfoResult> retailShareOuts = sgBShareOutMapper.selectShareOutBySourceBillId(request.getSourceBillId(), request.getSourceBillType());
            if (CollectionUtils.isEmpty(vipSgBShareOuts) && CollectionUtils.isEmpty(retailShareOuts)) {
                flag = true;
                FindSourceStrategyUtils.outputLog("SgFindSoureceC2SSendMQService.sendMqByWarehouse 不存在已占用的共享占用单 订单全部占用失败");
                this.sendOMSCallBackMsg(request, new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS));
                return;
            }
            List<SgBShareOutInfoResult> sgBShareOuts = new ArrayList<>();
            sgBShareOuts.addAll(vipSgBShareOuts);
            if (CollectionUtils.isNotEmpty(retailShareOuts)) {
                sgBShareOuts.addAll(retailShareOuts);
            }

            FindSourceStrategyUtils.outputLog("SgFindSoureceC2SSendMQService.sendMqByWarehouse result final sgBShareOuts = {}", JSONObject.toJSONString(sgBShareOuts));
            //要判断是否有聚合
            //过滤出  按照合并的标记,  合并后的结果集取 共享日志明细表
            Map<Boolean, List<SgBShareOutInfoResult>> mergeMap = sgBShareOuts.stream().collect(Collectors.groupingBy(SgBShareOutInfoResult::getMergeMark));
            //未合并的结果集
            sgBShareOuts = mergeMap.get(Boolean.FALSE);
            if (CollectionUtils.isEmpty(sgBShareOuts)) {
                sgBShareOuts = new ArrayList<>();
            }
            //合并的结果集
            List<SgBShareOutInfoResult> shareOutInfoResults = mergeMap.get(Boolean.TRUE);
            if (CollectionUtils.isNotEmpty(shareOutInfoResults)) {
                List<Long> ids = shareOutInfoResults.stream().map(SgBShareOutInfoResult::getBillId).collect(Collectors.toList());
                List<SgBShareOutInfoResult> sgBShareOutLogs = sgBShareOutMapper.selectShareOutByLogIds(ids);
                if (CollectionUtils.isNotEmpty(sgBShareOutLogs)) {
                    sgBShareOuts.addAll(sgBShareOutLogs);
                }
            }
            List<SkuItemS2L> skuItemList = new ArrayList<>();
            strategyRequest.setShopId(request.getShopId());
            strategyRequest.setInventedOccupy(request.getInventedOccupy());
            strategyRequest.setSourceBillId(request.getSourceBillId());
            strategyRequest.setSourceBillNo(request.getSourceBillNo());
            strategyRequest.setSourceBillType(request.getSourceBillType());
            strategyRequest.setSplitType(request.getSplitType());
            strategyRequest.setBillDate(request.getBillDate());
            strategyRequest.setTid(request.getTid());
            strategyRequest.setAreaId(request.getAreaId());
            strategyRequest.setCityId(request.getCityId());
            strategyRequest.setProvinceId(request.getProvinceId());
            strategyRequest.setVipOrderFlag(request.getVipOrderFlag());
            strategyRequest.setStoreDeliveryFlag(request.getStoreDeliveryFlag());
            strategyRequest.setWarehouseEcode(request.getWarehouseEcode());
            strategyRequest.setSkuItems(skuItemList);
            strategyRequest.setExcludeWarehouseEcode(request.getExcludeWarehouseEcode());

            Map<Long, List<SgBShareOutInfoResult>> shareOutMap = sgBShareOuts.stream().collect(Collectors.groupingBy(SgBShareOutInfoResult::getSourceItemId));
            skuItems.stream().forEach(skuItemC2S -> {
                Long sourceItemId;
                if (skuItemC2S.getTimeOrderId() != null) {
                    sourceItemId = skuItemC2S.getTimeOrderItemId();
                } else {
                    sourceItemId = skuItemC2S.getSourceItemId();
                }
                if (shareOutMap.containsKey(sourceItemId)) {
                    List<SgBShareOutInfoResult> sgBShareOutInfoResults = shareOutMap.get(sourceItemId);
                    BigDecimal allPreOutQty = sgBShareOutInfoResults.stream().map(sgBShareOut -> {
                        SkuItemS2L skuItem = new SkuItemS2L();
                        BeanCopierUtil.copy(skuItemC2S, skuItem);
                        if (skuItemC2S.getTimeOrderId() != null) {
                            skuItem.setType(SourceTypeEnum.VIP_TYPE.getCode());
                            skuItem.setTimeOrderId(skuItemC2S.getTimeOrderId());
                            skuItem.setTimeOrderBillNo(sgBShareOut.getSourceBIllNo());
                        }
                        skuItem.setQtyPreOut(sgBShareOut.getQtyPreout());
                        skuItem.setShareItemId(sgBShareOut.getItemId());
                        skuItem.setShareBillId(sgBShareOut.getBillId());
                        skuItem.setShareStoreId(sgBShareOut.getSgCShareStoreId());
                        skuItemList.add(skuItem);
                        return sgBShareOut.getQtyPreout();
                    }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    if (skuItemC2S.getQty().compareTo(allPreOutQty) > 0) {
                        SkuItemS2L skuItem = new SkuItemS2L();
                        BeanCopierUtil.copy(skuItemC2S, skuItem);
                        skuItem.setQtyPreOut(BigDecimal.ZERO);
                        skuItem.setShareItemId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                        skuItem.setShareBillId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                        skuItem.setShareStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                        skuItemList.add(skuItem);
                    }
                } else {
                    SkuItemS2L skuItem = new SkuItemS2L();
                    BeanCopierUtil.copy(skuItemC2S, skuItem);
                    skuItem.setQtyPreOut(BigDecimal.ZERO);
                    skuItem.setShareItemId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                    skuItem.setShareBillId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                    skuItem.setShareStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                    skuItemList.add(skuItem);
                }
            });
        } finally {
            if (!flag) {
                //MQ 发送二阶段寻源
                sendR3Mq(strategyRequest, request);
            }
        }

    }

    //二阶段寻源 mq 抽取公共方法
    private void sendR3Mq(SgFindSourceStrategyS2LRequest strategyRequest, SgFindSourceStrategyC2SRequest request) {
        String topic = "";
        String tag = "";
        String body = "";
        String msgKey = "";
        try {
//            topic = proConfig.getProperty(StrategyConstants.APOLLO_KEY_SOURCING_S2L_TOPIC);
            topic = StrategyConstants.APOLLO_KEY_SOURCING_S2L_TOPIC;
//            String configName = proConfig.getProperty(StrategyConstants.APOLLO_KEY_CONFIG_NAME);
//            tag = proConfig.getProperty(StrategyConstants.APOLLO_KEY_SOURCING_S2L_TAG);
            tag = StrategyConstants.APOLLO_KEY_SOURCING_S2L_TAG;

            if (StringUtils.isEmpty(tag)
//                    || StringUtils.isEmpty(configName)
                    || StringUtils.isEmpty(topic)) {
                log.error("一阶段寻源派单引擎 来源订单:{} mq配置不存在 无法发送mq消息", request.getSourceBillNo());
                return;
            }
            body = JSONObject.toJSONString(strategyRequest);
            msgKey = StrategyConstants.MQ_PREFIX + request.getSourceBillNo() + "_" + DateUtil.getDateTime(SgConstants.MQ_DATE_FORMAT);
            FindSourceStrategyUtils.outputLog("一阶段寻源派单引擎 来源订单:{} 一阶段寻源派单结束 发送消息开始二阶段寻源 topic:{} messageKey:{} messageBody:{}", request.getSourceBillNo(), topic, msgKey, body);
//            r3MqSendHelper.sendMessage(configName, body, topic, tag, msgKey);
            defaultProducerSend.sendTopic(sgStorageMqConfig.getMq5tobeconfirm(),
                    tag,
                    body,
                    msgKey);
        } catch (Exception e) {
            log.error("一阶段寻源派单引擎 来源订单:{} 发送消息开始二阶段寻源失败:{}", request.getSourceBillNo(), Throwables.getStackTraceAsString(e));
            SgBMonitorMqErrorDatasRequest sgBMonitorMqErrorDatasRequest = new SgBMonitorMqErrorDatasRequest();
            sgBMonitorMqErrorDatasRequest.setConsumer(SgMonitorMqEnum.OMS);
            sgBMonitorMqErrorDatasRequest.setProducer(SgMonitorMqEnum.SG);
            sgBMonitorMqErrorDatasRequest.setMessage(body);
            sgBMonitorMqErrorDatasRequest.setMsgKey(msgKey);
            sgBMonitorMqErrorDatasRequest.setTag(tag);
            sgBMonitorMqErrorDatasRequest.setTopic(topic);
            sgMonitorUtils.saveMqErrorDatas(sgBMonitorMqErrorDatasRequest, e, R3SystemUserResource.getSystemRootUser());
            return;
        }
    }

    //向oms推送 寻源异常
    private void sendOMSCallBackMsg(SgFindSourceStrategyC2SRequest request, ValueHolderV14<SgFindSourceStrategyC2SResult> valueHolderV14) {
        log.debug(LogUtil.format("向oms推送寻源异常:{};result:{}",
                "寻源异常"), request.getSourceBillNo(), JSON.toJSONString(valueHolderV14));
        String topic = "";
        String tag = "";
        String body = "";
        String msgKey = "";
        try {
            SgFindSourceStrategyOmsResult omsResult = new SgFindSourceStrategyOmsResult();
            omsResult.setCode(valueHolderV14.getCode());
            omsResult.setMessage(valueHolderV14.getMessage());
            omsResult.setSourceBillId(request.getSourceBillId());
            omsResult.setSourceBillNo(request.getSourceBillNo());
            omsResult.setSourceBillType(request.getSourceBillType());
            List<SgFindSourceStrategyOmsItemResult> omsItemList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
            omsResult.setItemResultList(omsItemList);
            if (valueHolderV14.getCode() == ResultCode.SUCCESS) {
                request.getSkuItems().stream().forEach(o -> {
                    SgFindSourceStrategyOmsItemResult sgFindSourceStrategyOmsItemResult = new SgFindSourceStrategyOmsItemResult();
                    sgFindSourceStrategyOmsItemResult.setQtyPreOut(o.getQty());
                    sgFindSourceStrategyOmsItemResult.setWareHouseId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                    sgFindSourceStrategyOmsItemResult.setSourceItemId(o.getSourceItemId());
                    omsItemList.add(sgFindSourceStrategyOmsItemResult);
                });
            }
//            topic = proConfig.getProperty(StrategyConstants.APOLLO_KEY_SOURCING_CALL_BACK_TOPIC);
            topic = StrategyConstants.APOLLO_KEY_SOURCING_CALL_BACK_TOPIC;
//            String configName = proConfig.getProperty(StrategyConstants.APOLLO_KEY_CONFIG_NAME);
            //需要改只有配货单情况需要使用tag
            if (SgConstantsIF.BILL_SHARE_DISTRIBUTION == request.getSourceBillType()) {
//                tag = proConfig.getProperty(StrategyConstants.APOLLO_KEY_SOURCING_CALL_BACK_DISTRIBUTION_TAG);
                tag = StrategyConstants.APOLLO_KEY_SOURCING_CALL_BACK_DISTRIBUTION_TAG;
            } else if (SgConstantsIF.BILL_TYPE_FOR_WAREHOUSE == request.getSourceBillType()) {
                //寻仓单 返回tag
//                tag = proConfig.getProperty(StrategyConstants.APOLLO_KEY_SOURCING_CALL_BACK_WAREHOUSE_TAG);
                tag = StrategyConstants.APOLLO_KEY_SOURCING_CALL_BACK_WAREHOUSE_TAG;
            } else {
//                tag = proConfig.getProperty(StrategyConstants.APOLLO_KEY_SOURCING_CALL_BACK_TAG);
                tag = StrategyConstants.APOLLO_KEY_SOURCING_CALL_BACK_TAG;
            }
            if (StringUtils.isEmpty(tag)
//                    || StringUtils.isEmpty(configName)
                    || StringUtils.isEmpty(topic)) {
                log.error("寻源派单引擎 来源订单:{} mq配置不存在 无法发送mq消息", request.getSourceBillNo());
                return;
            }
            body = JSONObject.toJSONString(omsResult);
            msgKey = StrategyConstants.MQ_PREFIX + request.getSourceBillNo() + "_" + DateUtil.getDateTime(SgConstants.MQ_DATE_FORMAT);
            FindSourceStrategyUtils.outputLog("寻源派单引擎 来源订单:{} 寻源派单结束 发送消息到oms topic:{} messageKey:{} messageBody:{}", request.getSourceBillId(), topic, msgKey, body);
//            r3MqSendHelper.sendMessage(configName, body, topic, tag, msgKey);
            defaultProducerSend.sendDelayTopic(sgStorageMqConfig.getMq5tobeconfirmCallBackDelay(),
                    tag,
                    body,
                    msgKey,
                    1L);
            
        } catch (Exception e) {
            log.error("寻源派单 来源订单:{} exception_has_occured:{}", request.getSourceBillNo(),
                    Throwables.getStackTraceAsString(e));
            SgBMonitorMqErrorDatasRequest sgBMonitorMqErrorDatasRequest = new SgBMonitorMqErrorDatasRequest();
            sgBMonitorMqErrorDatasRequest.setConsumer(SgMonitorMqEnum.OMS);
            sgBMonitorMqErrorDatasRequest.setProducer(SgMonitorMqEnum.SG);
            sgBMonitorMqErrorDatasRequest.setMessage(body);
            sgBMonitorMqErrorDatasRequest.setMsgKey(msgKey);
            sgBMonitorMqErrorDatasRequest.setTag(tag);
            sgBMonitorMqErrorDatasRequest.setTopic(topic);
            sgMonitorUtils.saveMqErrorDatas(sgBMonitorMqErrorDatasRequest, e, R3SystemUserResource.getSystemRootUser());
            return;
        }

    }


}