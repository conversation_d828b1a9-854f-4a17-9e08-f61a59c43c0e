package com.burgeon.r3.sg.sourcing.validate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgCoreUtilsConstants;
import com.burgeon.r3.sg.core.model.table.share.strategy.SgCShareSourceRuleStrategy;
import com.burgeon.r3.sg.sourcing.mapper.SgCShareSourceRuleStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.dto.strategy.SgCShareSourceRuleStrategyDTO;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @author: lishijun
 * @Date:
 * @Description:
 */
@Slf4j
@Component
public class SgCShareSourceRuleStrategySaveValidator extends BaseSingleValidator<SgCShareSourceRuleStrategyDTO> {

    @Autowired
    private SgCShareSourceRuleStrategyMapper sourceRuleStrategyMapper;

    @Override
    public String getValidatorMsgName() {
        return "寻源策略规则设置保存";
    }

    @Override
    public Class getValidatorClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCShareSourceRuleStrategyDTO mainObject, User loginUser) {
        LambdaQueryWrapper<SgCShareSourceRuleStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(null != mainObject.getId(), SgCShareSourceRuleStrategy::getId, mainObject.getId());
        wrapper.eq(SgCShareSourceRuleStrategy::getIsactive, SgCoreUtilsConstants.IS_ACTIVE_Y);
        wrapper.and(w ->
                w.eq(StringUtils.isNotEmpty(mainObject.getEcode()), SgCShareSourceRuleStrategy::getEcode,
                        mainObject.getEcode()).or().eq(StringUtils.isNotEmpty(mainObject.getEname()),
                        SgCShareSourceRuleStrategy::getEname, mainObject.getEname()));
        List<SgCShareSourceRuleStrategy> checkList = sourceRuleStrategyMapper.selectList(wrapper);

        if (CollectionUtils.isEmpty(checkList)) {
            return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        }
        SgCShareSourceRuleStrategy errData = checkList.get(0);

        if (errData.getEcode().equals(mainObject.getEcode())) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("编码已存在，不允许保存"));
        }
        return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("名称已存在，不允许保存"));
    }


}