package com.burgeon.r3.sg.sourcing.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.sourcing.SgBSourcingRetry;
import com.burgeon.r3.sg.sourcing.mapper.SgBSourcingRetryMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:
 * @author: hwy
 * @time: 2021/9/6 20:35
 */
@Component
public class SgBSourcingRetryQueryService {


    @Autowired
    private SgBSourcingRetryMapper sgBSourcingRetryMapper;
    
    /**
     * @Description: 查询待寻源的唯品会订单
     * @Author: hwy 
     * @Date: 2021/9/6 21:28
     * @param range: 
     * @param ids: 
     * @return: java.util.List<com.burgeon.r3.sg.core.model.table.sourcing.SgBSourcingRetry>
     **/
    public List<SgBSourcingRetry> querySourcingRetry(Integer range,List<Long> ids,Integer retryCount){
        List<SgBSourcingRetry> sgBSourcingRetries = sgBSourcingRetryMapper.selectList(new QueryWrapper<SgBSourcingRetry>().lambda()
                .in(CollectionUtils.isNotEmpty(ids),SgBSourcingRetry::getId,ids)
                .eq(SgBSourcingRetry::getIsactive, SgConstants.IS_ACTIVE_Y)
                .lt(SgBSourcingRetry::getRetryCount,retryCount)
                .orderByAsc(SgBSourcingRetry::getCreationdate)
                .last(" limit " + range));
        return sgBSourcingRetries;
    }
    
    /**
     * @Description: 更新已寻源的唯品会订单
     * @Author: hwy 
     * @Date: 2021/9/6 21:28
     * @param updateIds: 
     * @return: void
     **/
    public void batchUpdateStatusByIds(List<Long> updateIds) {
        sgBSourcingRetryMapper.batchUpdateStatusByIds(updateIds);
    }

    public void batchUpdateCountByIds(List<Long> updateUnSendIds) {
        sgBSourcingRetryMapper.batchUpdateCountByIds(updateUnSendIds);

    }
}