package com.burgeon.r3.sg.sourcing.services.sourcestrategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.r3.sg.basic.services.log.LogCommonService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCOperationLog;
import com.burgeon.r3.sg.core.model.table.share.strategy.SgCShareSourceRuleStrategy;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategy;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategyRuleItem;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyMapper;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyRuleItemMapper;
import com.burgeon.r3.sg.sourcing.mapper.SgCShareSourceRuleStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.request.sourcestrategy.SgCChannelSourceStrategyItemQueryRequest;
import com.burgeon.r3.sg.sourcing.model.request.sourcestrategy.SgCChannelSourceStrategyRuleItemDeleteRequest;
import com.burgeon.r3.sg.sourcing.model.result.sourcestrategy.SgCChannelSourceStrategyRuleItemPageResult;
import com.burgeon.r3.sg.sourcing.model.result.sourcestrategy.SgCChannelSourceStrategyRuleItemResult;
import com.github.pagehelper.PageInfo;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/19 17:16
 * 寻源规则明细表Service
 */
@Slf4j
@Component
public class SgCChannelSourceStrategyRuleItemService extends ServiceImpl<SgCChannelSourceStrategyRuleItemMapper,
        SgCChannelSourceStrategyRuleItem> {
    @Autowired
    private SgCChannelSourceStrategyRuleItemMapper sgChannelSourceStrategyRuleItemMapper;
    @Autowired
    private SgCShareSourceRuleStrategyMapper sgShareSourceRuleStrategyMapper;
    @Autowired
    private SgCChannelSourceStrategyMapper sgChannelSourceStrategyMapper;
    @Autowired
    private LogCommonService logCommonService;

    /**
     * 寻源规则删除行
     *
     * @param request 请求参数
     * @return ValueHolderV14
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 deleteLine(SgCChannelSourceStrategyRuleItemDeleteRequest request) {
        ValueHolderV14 vh = new ValueHolderV14<>(ResultCode.SUCCESS, "删除成功!");
        if (request.getPriority() == null || request.getSgCChannelSourceStrategyId() == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数不合法,删除失败!");
            return vh;
        }
        List<SgCOperationLog> operationLogList = new ArrayList<>();
        List<SgCChannelSourceStrategyRuleItem> ruleItems = sgChannelSourceStrategyRuleItemMapper.selectList(new LambdaQueryWrapper<SgCChannelSourceStrategyRuleItem>()
                .eq(SgCChannelSourceStrategyRuleItem::getSgCChannelSourceStrategyId,
                        request.getSgCChannelSourceStrategyId())
                .eq(SgCChannelSourceStrategyRuleItem::getPriority, request.getPriority()));
        if (CollectionUtils.isNotEmpty(ruleItems)) {
            for (SgCChannelSourceStrategyRuleItem ruleItem : ruleItems) {
                SgCOperationLog operationLog = logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY_FORCE_ITEM",
                        OperationTypeEnum.DEL.getOperationValue(), ruleItem.getSgCChannelSourceStrategyId(),
                        "寻源规则", "店仓", ruleItem.getSgCShareSourceRuleStrategyEname(),
                        null, request.getLoginUser());
                operationLogList.add(operationLog);
            }
        }
        logCommonService.batchInsertLog(operationLogList);
        int delete =
                sgChannelSourceStrategyRuleItemMapper.delete(new LambdaQueryWrapper<SgCChannelSourceStrategyRuleItem>()
                        .eq(SgCChannelSourceStrategyRuleItem::getSgCChannelSourceStrategyId,
                                request.getSgCChannelSourceStrategyId())
                        .eq(SgCChannelSourceStrategyRuleItem::getPriority, request.getPriority()));
        //更新主表修改人
        if (delete > 0) {
            SgCChannelSourceStrategy updateStrategy = new SgCChannelSourceStrategy();
            updateStrategy.setId(request.getSgCChannelSourceStrategyId());
            StorageUtils.setBModelDefalutDataByUpdate(updateStrategy, request.getLoginUser());
            updateStrategy.setModifierename(request.getLoginUser().getEname());
            sgChannelSourceStrategyMapper.updateById(updateStrategy);
        }
        return vh;
    }

    /**
     * 查询寻源规则
     *
     * @param request 请求参数
     * @return ValueHolderV14<PageInfo < SgCChannelSourceStrategyRuleItemResult>>
     */
    public ValueHolderV14<PageInfo<SgCChannelSourceStrategyRuleItemPageResult>> querySgChannelSourceStrategyRuleItem(SgCChannelSourceStrategyItemQueryRequest request) {
        ValueHolderV14<PageInfo<SgCChannelSourceStrategyRuleItemPageResult>> vh =
                new ValueHolderV14<>(ResultCode.SUCCESS, "查询成功!");
        if (request.getSgCChannelSourceStrategyId() == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数不合法,查询失败!");
            return vh;
        }
        List<SgCChannelSourceStrategyRuleItem> ruleItems = sgChannelSourceStrategyRuleItemMapper.selectList(
                new LambdaQueryWrapper<SgCChannelSourceStrategyRuleItem>()
                        .eq(SgCChannelSourceStrategyRuleItem::getSgCChannelSourceStrategyId,
                                request.getSgCChannelSourceStrategyId()));
        if (CollectionUtils.isNotEmpty(ruleItems)) {
            List<SgCChannelSourceStrategyRuleItemPageResult> results = new ArrayList<>();
            Map<Integer, List<SgCChannelSourceStrategyRuleItem>> priorityMap =
                    ruleItems.stream().collect(Collectors.groupingBy(SgCChannelSourceStrategyRuleItem::getPriority));
            for (List<SgCChannelSourceStrategyRuleItem> items : priorityMap.values()) {
                SgCChannelSourceStrategyRuleItemPageResult result = new SgCChannelSourceStrategyRuleItemPageResult();
                result.setPriority(items.get(0).getPriority());
                List<SgCChannelSourceStrategyRuleItemPageResult.RuleInfoResult> ruleInfoResults = new ArrayList<>();
                for (SgCChannelSourceStrategyRuleItem item : items) {
                    SgCChannelSourceStrategyRuleItemPageResult.RuleInfoResult ruleInfoResult =
                            new SgCChannelSourceStrategyRuleItemPageResult.RuleInfoResult();
                    BeanUtils.copyProperties(item, ruleInfoResult);
                    ruleInfoResults.add(ruleInfoResult);
                }
                result.setRuleInfos(ruleInfoResults);
                results.add(result);
            }
            int pageSize = request.getPageSize() == null ? SgConstants.SG_COMMON_MAX_QUERY_PAGE_SIZE :
                    request.getPageSize();
            int pageNum = request.getPageNumber() == null ? 1 : request.getPageNumber();
            //分页返回
            PageInfo<SgCChannelSourceStrategyRuleItemPageResult> resultPageInfo = new PageInfo<>(results);
            List<SgCChannelSourceStrategyRuleItemPageResult> listPaging =
                    StorageUtils.getListPaging(results, pageNum, pageSize);
            //手动分页返回
            resultPageInfo.setList(listPaging);
            //关联明细表的当前最大寻源层级用于前端累加计算新增行
            SgCChannelSourceStrategyRuleItemPageResult priority =
                    results.stream().max(Comparator.comparing(SgCChannelSourceStrategyRuleItemPageResult::getPriority)).get();
            resultPageInfo.setStartRow(priority.getPriority());
            vh.setData(resultPageInfo);
        }
        return vh;
    }

    /**
     * 寻源策略_寻源规则明细新增行 前端展示
     *
     * @return ValueHolderV14<List < SgCChannelSourceStrategyRuleItemResult>>
     */
    public ValueHolderV14<List<SgCChannelSourceStrategyRuleItemResult>> querySgShareSourceRuleStrategyNewLine() {
        ValueHolderV14<List<SgCChannelSourceStrategyRuleItemResult>> vh = new ValueHolderV14<>(ResultCode.SUCCESS,
                "新增成功!");
        List<SgCShareSourceRuleStrategy> sourceRuleStrategies =
                sgShareSourceRuleStrategyMapper.selectList(new
                        LambdaQueryWrapper<SgCShareSourceRuleStrategy>()
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isNotEmpty(sourceRuleStrategies)) {
            List<SgCChannelSourceStrategyRuleItemResult> ruleItemResultList = new ArrayList<>();
            for (SgCShareSourceRuleStrategy ruleStrategy : sourceRuleStrategies) {
                SgCChannelSourceStrategyRuleItemResult ruleItemResult = new SgCChannelSourceStrategyRuleItemResult();
                ruleItemResult.setSgCShareSourceRuleStrategyEcode(ruleStrategy.getEcode());
                ruleItemResult.setSgCShareSourceRuleStrategyEname(ruleStrategy.getEname());
                ruleItemResult.setSgCShareSourceRuleStrategyId(ruleStrategy.getId());
                ruleItemResult.setId(-1L);
                ruleItemResult.setIsactive(SgConstants.IS_ACTIVE_N);
                ruleItemResult.setPriority(null);
                ruleItemResultList.add(ruleItemResult);
            }
            vh.setData(ruleItemResultList);
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("寻源规则表未维护数据,新增行失败！");
        }
        return vh;
    }
}
