package com.burgeon.r3.sg.sourcing.services.item;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.basic.mapper.SgCTocStrategyMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCTocStrategy;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.stream.Collectors;

/**
 * c仓辐射策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SgFindSourceWarehouseRadiationStrategyService extends StrategyHandle {

    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {

        SgFindSourceStrategyS2LRequest s2LRequest = (SgFindSourceStrategyS2LRequest) request;
        ValueHolderV14<StrategyBaseResult> result = null;
        try {
            result = doHandle(s2LRequest);
        } catch (Exception e) {
            log.error("S->L 二阶段寻源 仓辐射策略 执行异常:{}", Throwables.getStackTraceAsString(e));

            StrategyBaseResult strategyBaseResult = request.getStrategyBaseResult();
            if (!(strategyBaseResult instanceof SgFindSourceStrategyS2LResult)) {
                log.error(" SgFindSourceWarehouseStrategyService doHandle error:执行仓辐射寻源组策略时，请求参数的结果对象类型错误！");
                return new ValueHolderV14<>(ResultCode.FAIL, "执行仓辐射寻源组策略时，请求参数的结果对象类型错误！");
            }
            SgFindSourceStrategyS2LResult result2 = (SgFindSourceStrategyS2LResult) strategyBaseResult;

            result = new ValueHolderV14<>(result2, ResultCode.FAIL, "执行仓辐射策略成功 按评分因子生成逻辑占用单");

            result.setCode(ResultCode.FAIL);
            result.setMessage("S->L 二阶段寻源 仓辐射策略 执行异常");
        }
        s2LRequest.setStrategyBaseResult(result.getData());
        return doNext(s2LRequest, result);
    }

    private ValueHolderV14<StrategyBaseResult> doHandle(SgFindSourceStrategyS2LRequest request) {
        FindSourceStrategyUtils.outputLog("Start SgFindSourceWarehouseRadiationStrategyService.doHandle 仓辐射策略 request:{}",
                JSONObject.toJSONString(request));

        StrategyBaseResult strategyBaseResult = request.getStrategyBaseResult();
        if (!(strategyBaseResult instanceof SgFindSourceStrategyS2LResult)) {
            log.error(" SgFindSourceWarehouseStrategyService doHandle error:执行仓辐射寻源组策略时，请求参数的结果对象类型错误！");
            return new ValueHolderV14<>(ResultCode.FAIL, "执行仓辐射寻源组策略时，请求参数的结果对象类型错误！");
        }
        SgFindSourceStrategyS2LResult result = (SgFindSourceStrategyS2LResult) strategyBaseResult;
        List<SgFindSourceStrategySkuS2LResult> skuResultList = result.getSkuResultList();
        if (CollectionUtils.isEmpty(skuResultList)) {
            log.error("SgFindSourceWarehouseStrategyService doHandle. error:skuResultList_is_null");
            return new ValueHolderV14<>(ResultCode.FAIL, "前次策略执行结果为空！");
        }

        if (StringUtils.isNotEmpty(request.getWarehouseEcode())) {
            log.info("执行仓辐射策略：指定实体仓，跳过仓辐射，直接执行是否满足唯一实体仓");
            return doScore(request, skuResultList, strategyBaseResult, result);
        }

        // 查询toc仓辐射策略
        List<SgCTocStrategy> strategyList = queryTocStrategies(request, skuResultList);
        if (CollectionUtils.isEmpty(strategyList)) {
            // 20220915 丹总说，未配置仓辐射，直接报错
            return new ValueHolderV14<>(ResultCode.FAIL, "根据「TOC仓辐射设置」，未配置仓辐射策略；");
        }
        log.info(LogUtil.format("SgFindSourceWarehouseRadiationStrategyService.doHandle billNo:{},strategyList:{}",
                "SgFindSourceWarehouseRadiationStrategyService.doHandle"), request.getSourceBillNo(), JSON.toJSONString(strategyList));

        // 对策略结果分别按照商品id、品相 分组，后续使用
        Map<Long, List<SgCTocStrategy>> proMap = new HashMap<>();
        Map<Long, List<SgCTocStrategy>> dimAndWeightMap = new HashMap<>();
        Map<Long, List<SgCTocStrategy>> dimMap = new HashMap<>();
        Map<Long, List<SgCTocStrategy>> dim4AndWeightMap = new HashMap<>();
        Map<Long, List<SgCTocStrategy>> dim4Map = new HashMap<>();
        List<SgCTocStrategy> noAllList = new ArrayList<>();
        groupBySkuAndDim(strategyList, proMap, dimAndWeightMap, dimMap, dim4AndWeightMap, dim4Map, noAllList);

        // 判断策略是否全部满足，如果维护不全，按照评分因子逻辑执行，否则输出仓辐射实体仓交集
        List<Long> sameStoreIdList = new ArrayList<>();
        //每个仓的最大优先级
        Map<Long, Integer> priorityMap = new HashMap<>();
        //每行配置的仓辐射仓库
        Map<Long, List<Long>> strategiesStoreIdListMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

        for (int i = 0; i < skuResultList.size(); i++) {
            SgFindSourceStrategySkuS2LResult skuItem = skuResultList.get(i);
            List<Long> strategiesStoreIdList = new ArrayList<>();
            //按照优先级获取辐射仓
            getStrategyWarehouseByItem(proMap, dimAndWeightMap, dimMap, dim4AndWeightMap, dim4Map,
                    noAllList, priorityMap, skuItem, strategiesStoreIdList);
            //暂存每行的辐射仓，new一个新的list，防止后面赋值给sameStoreIdList会被改动
            List<Long> newStrategiesStoreIdList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(strategiesStoreIdList)) {
                newStrategiesStoreIdList.addAll(strategiesStoreIdList);
            }
            strategiesStoreIdListMap.put(skuItem.getSourceItemId(), newStrategiesStoreIdList);

            // 初始化交集
            if (i == 0) {
                sameStoreIdList = strategiesStoreIdList;
            } else {
                sameStoreIdList.retainAll(strategiesStoreIdList);
            }
        }
        log.info(LogUtil.format("SgFindSourceWarehouseRadiationStrategyService.doHandle billNo:{},strategiesStoreIdListMap:{}",
                "SgFindSourceWarehouseRadiationStrategyService.doHandle"), request.getSourceBillNo(), JSON.toJSONString(strategiesStoreIdListMap));
        // 如果仓辐射实体仓交集为空，报错
        if (CollectionUtils.isEmpty(sameStoreIdList) && StrategyConstants.ORDER_SPLIT_TYPE_NO.equals(request.getSplitType())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "根据「TOC仓辐射设置」，订单不可拆单且商品不在同一实体仓发货；");
        }

        List<Long> checkList = new ArrayList<>();
        for (SgFindSourceStrategySkuS2LResult x : skuResultList) {
            for (SgFindSourceStrategyStoreItemS2LResult y : x.getItemResultList()) {
                checkList.add(y.getStoreId());
            }
        }
        if (CollectionUtils.isEmpty(checkList)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "根据「TOC仓辐射设置」，商品不在同一实体仓发货；");
        }

        // 获取到可唯一发货的集合
        List<Long> oneStockWarehouseIds = FindSourceStrategyUtils.getOneStockWarehouseIds(request);

        if (log.isDebugEnabled()) {
            log.debug("zzz:{}  {}", JSONObject.toJSONString(sameStoreIdList), JSONObject.toJSONString(oneStockWarehouseIds));
        }

        // 取交集
        sameStoreIdList.retainAll(oneStockWarehouseIds);

        // 存在可整单发的实体仓集合，则将结果return给逻辑占用单
        if (CollectionUtils.isNotEmpty(sameStoreIdList)) {

            if (log.isDebugEnabled()) {
                log.debug("执行仓辐射策略，按照仓辐射优先级评分：{}", JSONObject.toJSONString(priorityMap));
            }

            // 过滤只保留优先级最高的实体仓
            for (SgFindSourceStrategySkuS2LResult findSourceStrategySkuS2LResult : skuResultList) {
                for (SgFindSourceStrategyStoreItemS2LResult findSourceStrategyStoreItemS2LResult : findSourceStrategySkuS2LResult.getItemResultList()) {
                    if (sameStoreIdList.contains(findSourceStrategyStoreItemS2LResult.getStoreId()) &&
                            priorityMap.get(findSourceStrategyStoreItemS2LResult.getStoreId()) != null) {
                        findSourceStrategyStoreItemS2LResult.setPriority(
                                priorityMap.get(findSourceStrategyStoreItemS2LResult.getStoreId()));
                    } else {
                        findSourceStrategyStoreItemS2LResult.setPriority(0);
                    }
                }
            }
            // 处理多个最大优先级的情况
            adjustPriorityByProduceDate(skuResultList);
            result.setSkuResultList(skuResultList);
            result.setDoNextStrategy(Boolean.FALSE);
            return new ValueHolderV14<>(result, ResultCode.SUCCESS, "执行仓辐射策略成功");
        } else if (result.isNearExpiryDate()) {
            if (CollectionUtils.isEmpty(oneStockWarehouseIds)) {
                return new ValueHolderV14<>(strategyBaseResult, ResultCode.FAIL, "无整仓满足发货，但命中临近大效期策略，请联系管理员。");
            }

            CpCPhyWarehouseMapper warehouseMapper = ApplicationContextHandle.getBean(CpCPhyWarehouseMapper.class);
            List<SgCpCPhyWarehouse> warehouseList = warehouseMapper.selectBatchIds(oneStockWarehouseIds);
            List<String> ecodeList = warehouseList.stream().map(SgCpCPhyWarehouse::getEcode).collect(Collectors.toList());
            //直接返回上一次策略执行结果
            return new ValueHolderV14<>(strategyBaseResult, ResultCode.FAIL,
                    "执行【临近大效期寻源设置】与【ToC仓库辐射设置】结果集无交集：临近大效期范围：" + JSONObject.toJSONString(ecodeList));
        }

        log.info("SgFindSourceWarehouseStrategyService doHandle 执行仓辐射策略 不满足整单发，仓辐射集合：{}",
                JSONObject.toJSONString(sameStoreIdList));
        
        //记录过滤完仓辐射之后缺货的行数
        int haveLine = skuResultList.size();
        //继续执行拆单策略，将每行的有库存仓库过滤，只保留仓辐射范围内的
        for (SgFindSourceStrategySkuS2LResult skuResult : skuResultList) {
            List<SgFindSourceStrategyStoreItemS2LResult> newItemResultList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(skuResult.getItemResultList())) {
                List<Long> strategiesWarehouseIds = strategiesStoreIdListMap.getOrDefault(skuResult.getSourceItemId(), new ArrayList<>());
                for (SgFindSourceStrategyStoreItemS2LResult itemResult : skuResult.getItemResultList()) {
                    if (strategiesWarehouseIds.contains(itemResult.getStoreId())) {
                        newItemResultList.add(itemResult);
                    }
                }
            }
            skuResult.setItemResultList(newItemResultList);
            //设置缺货仓
            if (CollectionUtils.isEmpty(newItemResultList)) {
                haveLine--;
                SgFindSourceStrategyStoreItemS2LResult storeItemResult = new SgFindSourceStrategyStoreItemS2LResult();
                storeItemResult.setStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                storeItemResult.setLogicStorageMap(new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY));
                newItemResultList.add(storeItemResult);
            }
        }
        //有整单满足仓，但是跟仓辐射没有交集，如果不允许拆单且有库存行小于订单行或者可拆每行都缺货
        if (CollectionUtils.isNotEmpty(oneStockWarehouseIds)
                && ((StrategyConstants.ORDER_SPLIT_TYPE_NO.equals(request.getSplitType()) && haveLine < skuResultList.size())
                || (!StrategyConstants.ORDER_SPLIT_TYPE_NO.equals(request.getSplitType())) && haveLine == 0)) {
            CpCPhyWarehouseMapper warehouseMapper = ApplicationContextHandle.getBean(CpCPhyWarehouseMapper.class);
            List<SgCpCPhyWarehouse> warehouseList = warehouseMapper.selectBatchIds(oneStockWarehouseIds);
            List<String> ecodeList = warehouseList.stream().map(SgCpCPhyWarehouse::getEcode).collect(Collectors.toList());
            return new ValueHolderV14<>(strategyBaseResult, ResultCode.FAIL,
                    "存在整单发货仓，但是【ToC仓库辐射设置】未配置完全，可整单发货仓：" + JSONObject.toJSONString(ecodeList));
        }
        //直接返回上一次策略执行结果
        return new ValueHolderV14<>(strategyBaseResult, ResultCode.SUCCESS, " 执行仓辐射策略 获取仓辐射策略为空 ");
    }

    /**
     * 处理多个最大优先级的情况
     * 如果skuResultList.size() == 1，且ItemResultList存在多个，且priority存在多个最大值，
     * 就判断一下logicStorageMap的生产日期谁更小，能找到最小的就将对应的priority加1
     * 如果找不到，比如存在两个最小的就跳过逻辑不处理
     *
     * @param skuResultList
     */
    private void adjustPriorityByProduceDate(List<SgFindSourceStrategySkuS2LResult> skuResultList) {
        if (CollectionUtils.isEmpty(skuResultList) || skuResultList.size() != 1) {
            return;
        }

        SgFindSourceStrategySkuS2LResult skuResult = skuResultList.get(0);
        List<SgFindSourceStrategyStoreItemS2LResult> itemResultList = skuResult.getItemResultList();
        if (CollectionUtils.isEmpty(itemResultList) || itemResultList.size() <= 1) {
            return;
        }

        // 找出最大优先级值
        Integer maxPriority = itemResultList.stream()
                .map(SgFindSourceStrategyStoreItemS2LResult::getPriority)
                .max(Integer::compareTo)
                .orElse(0);

        // 找出所有具有最大优先级的项
        List<SgFindSourceStrategyStoreItemS2LResult> maxPriorityItems = itemResultList.stream()
                .filter(item -> maxPriority.equals(item.getPriority()))
                .collect(Collectors.toList());

        // 如果只有一个最大优先级项，不需要调整
        if (maxPriorityItems.size() <= 1) {
            return;
        }

        // 找出每个最大优先级项的最小生产日期
        Map<SgFindSourceStrategyStoreItemS2LResult, String> itemMinProduceDateMap = new HashMap<>();
        for (SgFindSourceStrategyStoreItemS2LResult item : maxPriorityItems) {
            Map<Long, SortedMap<String, BigDecimal>> logicStorageMap = item.getLogicStorageMap();
            if (MapUtils.isEmpty(logicStorageMap)) {
                continue;
            }

            // 找出所有逻辑仓中的最小生产日期
            logicStorageMap.values().stream()
                    .filter(MapUtils::isNotEmpty)
                    .flatMap(map -> map.keySet().stream())
                    .min(String::compareTo).ifPresent(minProduceDate -> itemMinProduceDateMap.put(item, minProduceDate));

        }

        // 如果没有有效的生产日期信息，不进行调整
        if (itemMinProduceDateMap.isEmpty()) {
            return;
        }

        // 找出最小的生产日期
        String minProduceDate = itemMinProduceDateMap.values().stream()
                .min(String::compareTo)
                .orElse(null);

        // 找出所有具有最小生产日期的项
        List<SgFindSourceStrategyStoreItemS2LResult> minProduceDateItems = itemMinProduceDateMap.entrySet().stream()
                .filter(entry -> minProduceDate.equals(entry.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        // 如果有多个项具有相同的最小生产日期，不进行调整
        if (minProduceDateItems.size() != 1) {
            return;
        }

        FindSourceStrategyUtils.outputLog("SgFindSourceWarehouseRadiationStrategyService.adjustPriorityByProduceDate " +
                "处理多个最大优先级的情况 skuResultList:{}", JSONObject.toJSONString(skuResultList));

        // 将具有最小生产日期的项的优先级加1
        SgFindSourceStrategyStoreItemS2LResult itemToAdjust = minProduceDateItems.get(0);
        itemToAdjust.setPriority(itemToAdjust.getPriority() + 1);
    }
    /**
     * 查询仓辐射
     *
     * @param request
     * @param skuResultList
     * @return
     */
    private List<SgCTocStrategy> queryTocStrategies(SgFindSourceStrategyS2LRequest request,
                                                    List<SgFindSourceStrategySkuS2LResult> skuResultList) {
        List<Long> proIdList = new ArrayList<>();
        List<Long> miIdList = new ArrayList<>();
        List<Long> mid4IdList = new ArrayList<>();
        skuResultList.forEach(x -> {
            if (!proIdList.contains(x.getPsCProId())) {
                proIdList.add(x.getPsCProId());
            }
            if (!miIdList.contains(x.getPsCProdimId())) {
                miIdList.add(x.getPsCProdimId());
            }
            if (!mid4IdList.contains(x.getPsCProdim4Id())) {
                mid4IdList.add(x.getPsCProdim4Id());
            }
        });

        // 这里先对仓辐射辐射级排序
        SgCTocStrategyMapper mapper = ApplicationContextHandle.getBean(SgCTocStrategyMapper.class);
        LambdaQueryWrapper<SgCTocStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SgCTocStrategy::getCpCProvinceId, request.getProvinceId());
        wrapper.and(x -> x.in(SgCTocStrategy::getPsCProId, proIdList)
                .or().in(SgCTocStrategy::getPsCProdimId, miIdList)
                .or().in(SgCTocStrategy::getPsCProdim4Id, mid4IdList)
                .or(y -> y.isNull(SgCTocStrategy::getPsCProdimId)
                        .isNull(SgCTocStrategy::getPsCProdim4Id)
                        .isNull(SgCTocStrategy::getPsCProId)));
        wrapper.eq(SgCTocStrategy::getIsactive, SgConstants.IS_ACTIVE_Y);
        wrapper.orderByDesc(SgCTocStrategy::getReank);
        List<SgCTocStrategy> strategyList = mapper.selectList(wrapper);
        return strategyList;
    }

    /**
     * 根据查询的策略分组
     *
     * @param strategyList     TOC仓辐射策略
     * @param proMap           SKU+仓库+省
     * @param dimAndWeightMap  四级类目+公斤+仓库+省
     * @param dimMap           四级类目+仓库+省
     * @param dim4AndWeightMap 一级类目+公斤+仓库+省
     * @param dim4Map          一级类目+仓库+省
     * @param noAllList        仓+省
     */
    private void groupBySkuAndDim(List<SgCTocStrategy> strategyList, Map<Long, List<SgCTocStrategy>> proMap,
                                  Map<Long, List<SgCTocStrategy>> dimAndWeightMap,
                                  Map<Long, List<SgCTocStrategy>> dimMap,
                                  Map<Long, List<SgCTocStrategy>> dim4AndWeightMap,
                                  Map<Long, List<SgCTocStrategy>> dim4Map, List<SgCTocStrategy> noAllList) {
        strategyList.forEach(x -> {
            if (x.getPsCProId() != null) {
                List<SgCTocStrategy> listByProId = proMap.get(x.getPsCProId());
                if (CollectionUtils.isEmpty(listByProId)) {
                    listByProId = new ArrayList<>();
                    proMap.put(x.getPsCProId(), listByProId);
                }
                listByProId.add(x);
                return;
            }
            if (x.getPsCProdimId() != null && x.getStartWeight() != null && x.getEndWeight() != null) {
                List<SgCTocStrategy> listByMiId = dimAndWeightMap.get(x.getPsCProdimId());
                if (CollectionUtils.isEmpty(listByMiId)) {
                    listByMiId = new ArrayList<>();
                    dimAndWeightMap.put(x.getPsCProdimId(), listByMiId);
                }
                listByMiId.add(x);
                return;
            }
            if (x.getPsCProdimId() != null && x.getStartWeight() == null && x.getEndWeight() == null) {
                List<SgCTocStrategy> listByMiId = dimMap.get(x.getPsCProdimId());
                if (CollectionUtils.isEmpty(listByMiId)) {
                    listByMiId = new ArrayList<>();
                    dimMap.put(x.getPsCProdimId(), listByMiId);
                }
                listByMiId.add(x);
                return;
            }
            if (x.getPsCProdim4Id() != null && x.getStartWeight() != null && x.getEndWeight() != null) {
                List<SgCTocStrategy> listByMiId = dim4AndWeightMap.get(x.getPsCProdim4Id());
                if (CollectionUtils.isEmpty(listByMiId)) {
                    listByMiId = new ArrayList<>();
                    dim4AndWeightMap.put(x.getPsCProdim4Id(), listByMiId);
                }
                listByMiId.add(x);
                return;
            }
            if (x.getPsCProdim4Id() != null && x.getStartWeight() == null && x.getEndWeight() == null) {
                List<SgCTocStrategy> listByMiId = dim4Map.get(x.getPsCProdim4Id());
                if (CollectionUtils.isEmpty(listByMiId)) {
                    listByMiId = new ArrayList<>();
                    dim4Map.put(x.getPsCProdim4Id(), listByMiId);
                }
                listByMiId.add(x);
                return;
            }
            noAllList.add(x);
        });
    }

    /**
     * 按照优先级获取辐射仓
     *
     * @param proMap                SKU+仓库+省
     * @param dimAndWeightMap       四级类目+公斤+仓库+省
     * @param dimMap                四级类目+仓库+省
     * @param dim4AndWeightMap      一级类目+公斤+仓库+省
     * @param dim4Map               一级类目+仓库+省
     * @param noAllList             仓+省
     * @param priorityMap           每个实体仓对应的最大优先级
     * @param skuItem               订单行信息
     * @param strategiesStoreIdList 当前行对应的辐射仓
     */
    private void getStrategyWarehouseByItem(Map<Long, List<SgCTocStrategy>> proMap,
                                            Map<Long, List<SgCTocStrategy>> dimAndWeightMap,
                                            Map<Long, List<SgCTocStrategy>> dimMap,
                                            Map<Long, List<SgCTocStrategy>> dim4AndWeightMap,
                                            Map<Long, List<SgCTocStrategy>> dim4Map, List<SgCTocStrategy> noAllList,
                                            Map<Long, Integer> priorityMap, SgFindSourceStrategySkuS2LResult skuItem,
                                            List<Long> strategiesStoreIdList) {
        //商品
        List<SgCTocStrategy> strategies = proMap.get(skuItem.getPsCProId());
        if (CollectionUtils.isNotEmpty(strategies)) {
            strategies.forEach(x -> {
                Integer priority = priorityMap.get(x.getCpCPhyWarehouseId());
                if (priority == null || priority < x.getReank()) {
                    priorityMap.put(x.getCpCPhyWarehouseId(), x.getReank());
                }
                strategiesStoreIdList.add(x.getCpCPhyWarehouseId());
            });
        }
        BigDecimal itemTotWeight = skuItem.getItemTotWeight();
        //四级+重量
        if (CollectionUtils.isEmpty(strategiesStoreIdList)) {
            strategies = dimAndWeightMap.get(skuItem.getPsCProdimId());
            if (CollectionUtils.isNotEmpty(strategies)) {
                strategies.forEach(x -> {
                    //存在公斤段且不在范围就跳过
                    if (x.getStartWeight() == null || x.getEndWeight() == null) {
                        return;
                    }
                    if (itemTotWeight == null) {
                        return;
                    }
                    if (itemTotWeight.compareTo(x.getEndWeight()) > 0 ||
                            itemTotWeight.compareTo(x.getStartWeight()) <= 0) {
                        return;
                    }
                    Integer priority = priorityMap.get(x.getCpCPhyWarehouseId());
                    if (priority == null || priority < x.getReank()) {
                        priorityMap.put(x.getCpCPhyWarehouseId(), x.getReank());
                    }
                    strategiesStoreIdList.add(x.getCpCPhyWarehouseId());
                });
            }
        }
        //四级
        if (CollectionUtils.isEmpty(strategiesStoreIdList)) {
            strategies = dimMap.get(skuItem.getPsCProdimId());
            if (CollectionUtils.isNotEmpty(strategies)) {
                strategies.forEach(x -> {
                    Integer priority = priorityMap.get(x.getCpCPhyWarehouseId());
                    if (priority == null || priority < x.getReank()) {
                        priorityMap.put(x.getCpCPhyWarehouseId(), x.getReank());
                    }
                    strategiesStoreIdList.add(x.getCpCPhyWarehouseId());
                });
            }
        }
        //一级+重量
        if (CollectionUtils.isEmpty(strategiesStoreIdList)) {
            strategies = dim4AndWeightMap.get(skuItem.getPsCProdim4Id());
            if (CollectionUtils.isNotEmpty(strategies)) {
                strategies.forEach(x -> {
                    //存在公斤段且不在范围就跳过
                    if (x.getStartWeight() == null || x.getEndWeight() == null) {
                        return;
                    }
                    if (itemTotWeight == null) {
                        return;
                    }
                    if (itemTotWeight.compareTo(x.getEndWeight()) > 0 ||
                            itemTotWeight.compareTo(x.getStartWeight()) <= 0) {
                        return;
                    }
                    Integer priority = priorityMap.get(x.getCpCPhyWarehouseId());
                    if (priority == null || priority < x.getReank()) {
                        priorityMap.put(x.getCpCPhyWarehouseId(), x.getReank());
                    }
                    strategiesStoreIdList.add(x.getCpCPhyWarehouseId());
                });
            }
        }
        //一级
        if (CollectionUtils.isEmpty(strategiesStoreIdList)) {
            strategies = dim4Map.get(skuItem.getPsCProdim4Id());
            if (CollectionUtils.isNotEmpty(strategies)) {
                strategies.forEach(x -> {
                    Integer priority = priorityMap.get(x.getCpCPhyWarehouseId());
                    if (priority == null || priority < x.getReank()) {
                        priorityMap.put(x.getCpCPhyWarehouseId(), x.getReank());
                    }
                    strategiesStoreIdList.add(x.getCpCPhyWarehouseId());
                });
            } else {
                // 20230208 新增保底策略
                if (CollectionUtils.isNotEmpty(noAllList)) {
                    noAllList.forEach(x -> {
                        Integer priority = priorityMap.get(x.getCpCPhyWarehouseId());
                        if (priority == null || priority < x.getReank()) {
                            priorityMap.put(x.getCpCPhyWarehouseId(), x.getReank());
                        }
                        strategiesStoreIdList.add(x.getCpCPhyWarehouseId());
                    });
                }
            }
        }
    }

    /**
     * 按照评分因子的逻辑生成逻辑占用单
     *
     * @param request
     * @param skuResultList
     * @param strategyBaseResult
     * @param result
     * @return
     */
    private ValueHolderV14<StrategyBaseResult> doScore(SgFindSourceStrategyS2LRequest request,
                                                       List<SgFindSourceStrategySkuS2LResult> skuResultList,
                                                       StrategyBaseResult strategyBaseResult,
                                                       SgFindSourceStrategyS2LResult result) {
        if (log.isDebugEnabled()) {
            log.debug("SgFindSourceWarehouseRadiationStrategyService.doScore");
        }
        List<List<Long>> storeIdListList = new ArrayList<>();
        for (SgFindSourceStrategySkuS2LResult skuItem : skuResultList) {
            List<Long> storeIdList = new ArrayList<>();
            skuItem.getItemResultList().forEach(x -> storeIdList.add(x.getStoreId()));
            storeIdListList.add(storeIdList);
        }

        List<Long> sameStoreIdList = storeIdListList.get(0);
        storeIdListList.forEach(sameStoreIdList::retainAll);

        if (CollectionUtils.isEmpty(sameStoreIdList)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "根据「TOC仓辐射设置」，商品不在同一实体仓发货；" + result.parseRemovedStoreInfoMsg());
        }

        // 获取到可唯一发货的集合
        List<Long> oneStockWarehouseIds = FindSourceStrategyUtils.getOneStockWarehouseIds(request);

        sameStoreIdList.retainAll(oneStockWarehouseIds);

        // 这里直接返回原入参，
        if (CollectionUtils.isEmpty(sameStoreIdList)) {
            if (StringUtils.isNotEmpty(request.getWarehouseEcode())) {
                return new ValueHolderV14<>(strategyBaseResult, ResultCode.FAIL, "执行仓辐射策略成功，指定实体仓库存不足：" + request.getWarehouseEcode());
            }
            return new ValueHolderV14<>(strategyBaseResult, ResultCode.SUCCESS, "执行仓辐射策略成功，无策略可用，有公共实体仓，无可整单发实体仓");
        }

        skuResultList.forEach(x -> {
            List<SgFindSourceStrategyStoreItemS2LResult> newItemResultList = x.getItemResultList().stream().filter(y -> sameStoreIdList.contains(y.getStoreId())).collect(Collectors.toList());
            x.setItemResultList(newItemResultList);
        });
        result.setSkuResultList(skuResultList);
        result.setDoNextStrategy(Boolean.FALSE);
        return new ValueHolderV14<>(result, ResultCode.SUCCESS, "执行仓辐射策略成功 按评分因子生成逻辑占用单");
    }
}
