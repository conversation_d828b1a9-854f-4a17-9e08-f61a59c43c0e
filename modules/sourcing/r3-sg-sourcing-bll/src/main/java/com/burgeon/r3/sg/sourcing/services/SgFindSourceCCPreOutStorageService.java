package com.burgeon.r3.sg.sourcing.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.inf.common.enums.StoreTypeEnum;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyCCRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemCC;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsItemResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsResult;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanItemResult;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanServiceResult;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.burgeon.r3.sg.store.model.request.freeze.out.SgBStoFreezeOutBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.freeze.out.SgBStoFreezeOutSaveItemLogRequest;
import com.burgeon.r3.sg.store.model.request.freeze.out.SgBStoFreezeOutSaveItemRequest;
import com.burgeon.r3.sg.store.model.request.freeze.out.SgBStoFreezeOutSaveMainRequest;
import com.burgeon.r3.sg.store.model.result.freeze.out.SgBStoFreezeOutBillResult;
import com.burgeon.r3.sg.store.services.freeze.out.SgBStoFreezeOutSaveService;
import com.google.common.base.Throwables;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: hwy
 * @time: 2021/7/3 13:56
 */
@Component
@Slf4j
public class SgFindSourceCCPreOutStorageService {

    @Resource
    private SgFindSourceOccupyPlanCCService occupyPlanCCService;
    @Resource
    private SgBStoFreezeOutSaveService saveSgBStoOut;

    public ValueHolderV14<SgFindSourceStrategyOmsResult> occupyStorage(SgFindSourceStrategyCCRequest request) {
        FindSourceStrategyUtils.outputLog("SgFindSourceCCPreOutStorageService.occupyStorage CC 库存占用服务 param:{}", JSONObject.toJSONString(request));

        ValueHolderV14<SgFindSourceStrategyOmsResult> valueHolderV14 = new ValueHolderV14<>(StrategyConstants.RESULT_CODE_PREOUT, SgConstants.MESSAGE_STATUS_SUCCESS);
        SgOccupyPlanServiceResult occupyPlan = null;
        try {
            // 生成占用计划(根据生产日期升序占用)
            ValueHolderV14<SgOccupyPlanServiceResult> occupyPlanResult = occupyPlanCCService.getOccupyPlan(request);
            // 无占用或异常直接返回
            if (StrategyConstants.RESULT_CODE_PREOUT != occupyPlanResult.getCode()) {
                valueHolderV14.setCode(occupyPlanResult.getCode());
                valueHolderV14.setMessage(occupyPlanResult.getMessage());
                return valueHolderV14;
            }
            occupyPlan = occupyPlanResult.getData();
            FindSourceStrategyUtils.outputLog("SgFindSourceCCPreOutStorageService.getOccupyPlan CC 占用计划结果 param:{}", JSONObject.toJSONString(occupyPlan));
            // 根据占用计划 生成冻结占用单
            addFreezeOutBill(request, occupyPlan, valueHolderV14);
        } catch (Exception e) {
            log.error("SgFindSourceCCPreOutStorageService CC 2C残次寻源派单 来源明细id:{} 库存占用失败", request.getSourceBillId());
            valueHolderV14.setCode(StrategyConstants.RESULT_CODE_PREOUT);
            valueHolderV14.setMessage("CC 2C残次寻源派单 来源明细 库存占用失败");
            return valueHolderV14;
        }

        return valueHolderV14;
    }

    /**
     * @param request:
     * @param occupyPlan:
     * @Description: 新增冻结占用单
     * @Author: hwy
     * @Date: 2021/7/3 16:58
     * @return: com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsResult
     **/
    public SgFindSourceStrategyOmsResult addFreezeOutBill(SgFindSourceStrategyCCRequest request, SgOccupyPlanServiceResult occupyPlan,
                                                          ValueHolderV14<SgFindSourceStrategyOmsResult> valueHolderV14) {
        SgFindSourceStrategyOmsResult omsResult = new SgFindSourceStrategyOmsResult();
        valueHolderV14.setData(omsResult);
        omsResult.setSourceBillId(request.getSourceBillId());
        omsResult.setSourceBillType(request.getSourceBillType());
        omsResult.setSourceBillNo(request.getSourceBillNo());
        List<SgFindSourceStrategyOmsItemResult> omsItemList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        List<Long> wareHouseIds = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        omsResult.setItemResultList(omsItemList);
        // 创建冻结占用单入参
        SgBStoFreezeOutBillSaveRequest freezeOutBillSaveRequest = buildSaveRequest(request, occupyPlan);

        try {
            //todo 传入不允许负可用
            ValueHolderV14<SgBStoFreezeOutBillResult> stoOccupyResult = saveSgBStoOut.freezeOutSave(freezeOutBillSaveRequest);

            if (!stoOccupyResult.isOK()) {
                log.error("SgFindSourceCCPreOutStorageService CC 2C残次寻源派单 来源单据id:{} 冻结占用单生成服务 生成冻结占用单失败 订单全部缺货 :{}", request.getSourceBillId(), valueHolderV14.getMessage());
                valueHolderV14.setCode(StrategyConstants.RESULT_CODE_EXCEPTION);
                valueHolderV14.setMessage("CC 2C残次寻源派单 冻结占用单生成服务 生成冻结占用单失败 订单全部缺货");
                FindSourceStrategyUtils.outputLog("SgFindSourceCCPreOutStorageService CC 2C残次寻源派单 冻结占用单生成服务 来源单据id:{} 生成冻结占用单失败 开始回滚已占用的冻结占用单", request.getSourceBillId(), valueHolderV14.getMessage());
                return omsResult;
            }
            FindSourceStrategyUtils.outputLog("SgFindSourceCCPreOutStorageService.addStoOutBill CC 新增共享占用单结果:{}", stoOccupyResult.toJSONObject());
            SgBStoFreezeOutBillResult occupyResultData = stoOccupyResult.getData();
            setOmsResult(request, freezeOutBillSaveRequest, occupyResultData, omsItemList, wareHouseIds);
            //补充组装返回结果
            supplementOmsResult(request, omsResult, wareHouseIds);
        } catch (Exception e) {
            log.error("SgFindSourceCCPreOutStorageService CC 2C残次寻源派单 来源单据id:{} 生成冻结占用单失败 订单全部缺货 :{}", request.getSourceBillId(), Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(StrategyConstants.RESULT_CODE_EXCEPTION);
            valueHolderV14.setMessage("CC 2C残次寻源派单 冻结占用单生成服务 生成冻结占用单失败 订单全部缺货");
            FindSourceStrategyUtils.outputLog("SgFindSourceCCPreOutStorageService CC 2C残次寻源派单 冻结占用单生成服务 来源单据id:{} 生成冻结占用单失败 开始回滚已占用的冻结占用单", request.getSourceBillId(), valueHolderV14.getMessage());

            return omsResult;
        }
        return omsResult;
    }

    /**
     * @param request:
     * @param omsResult:
     * @Description: 补充返回oms结果
     * @Author: hwy
     * @Date: 2021/7/3 16:54
     * @return: void
     **/
    public void supplementOmsResult(SgFindSourceStrategyCCRequest request, SgFindSourceStrategyOmsResult omsResult, List<Long> wareHouseIds) {
        log.info(LogUtil.format("SgFindSourceCCPreOutStorageService.supplementOmsResult request:{},omsResult:{},wareHouseIds:{}",
                "SgFindSourceCCPreOutStorageService.supplementOmsResult"),
                JSONObject.toJSONString(request), JSONObject.toJSONString(omsResult), JSONObject.toJSONString(wareHouseIds));
        List<SkuItemCC> skuItems = request.getSkuItems();
        List<SgFindSourceStrategyOmsItemResult> omsItemList = omsResult.getItemResultList();
        // 设置物理仓信息
        if (CollectionUtils.isNotEmpty(wareHouseIds)) {
            CpCPhyWarehouseMapper warehouseMapper = ApplicationContextHandle.getBean(CpCPhyWarehouseMapper.class);
            List<SgCpCPhyWarehouse> sgCpCPhyWarehouses = warehouseMapper.selectList(new QueryWrapper<SgCpCPhyWarehouse>().lambda()
                    .in(SgCpCPhyWarehouse::getId, wareHouseIds)
                    .eq(SgCpCPhyWarehouse::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (CollectionUtils.isNotEmpty(sgCpCPhyWarehouses)) {
                Map<Long, SgCpCPhyWarehouse> warehouseMap = sgCpCPhyWarehouses.stream().collect(Collectors.toMap(SgCpCPhyWarehouse::getId, Function.identity()));
                omsItemList.stream().forEach(o -> {
                    Long wareHouseId = o.getWareHouseId();
                    if (warehouseMap.containsKey(wareHouseId)) {
                        SgCpCPhyWarehouse sgCpCPhyWarehouse = warehouseMap.get(wareHouseId);
                        o.setWareHouseEcode(sgCpCPhyWarehouse.getEcode());
                        o.setWareHouseEname(sgCpCPhyWarehouse.getEname());
                    }
                });
            }
        }
        // key sourceItemId
        Map<Long, List<SgFindSourceStrategyOmsItemResult>> itemMap = omsItemList.stream().collect(Collectors.groupingBy(SgFindSourceStrategyOmsItemResult::getSourceItemId));
        //待处理的结果集
        Boolean pendingFlag = Boolean.FALSE;
        Map<Long, List<SgFindSourceStrategyOmsItemResult>> itemPendingMap = new HashMap<>();
        for (SkuItemCC skuItem : skuItems) {
            Long sourceItemId = skuItem.getSourceItemId();
            BigDecimal qty = skuItem.getQty();
            //防止明细重复
            BigDecimal qtyOutStock = BigDecimal.ZERO;
            List<SgFindSourceStrategyOmsItemResult> currSourceItem;
            if (itemMap.containsKey(sourceItemId)) {
                currSourceItem = itemMap.get(sourceItemId);
                BigDecimal qtyPreout = currSourceItem.stream().map(SgFindSourceStrategyOmsItemResult::getQtyPreOut).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                qtyOutStock = qty.subtract(qtyPreout);
                // 未分配完 添加缺货明细
            } else {
                currSourceItem = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
                itemMap.put(sourceItemId, currSourceItem);
                SgFindSourceStrategyOmsItemResult omsItem = new SgFindSourceStrategyOmsItemResult();
                omsItem.setSourceItemId(sourceItemId);
                omsItem.setWareHouseId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                omsItem.setQtyPreOut(qty);
                currSourceItem.add(omsItem);
                continue;
            }
            // 当原单数量的占用 小于 占用计划中当前明细的数量, 说明可能是配货单寻源相同条码  相同明细
            if (qtyOutStock.compareTo(BigDecimal.ZERO) < 0) {
                pendingFlag = Boolean.TRUE;
                //备份原始数据
                List<SgFindSourceStrategyOmsItemResult> tempList = itemPendingMap.get(sourceItemId);
                boolean isNeedBk = Boolean.FALSE;
                if (ObjectUtils.isEmpty(tempList)) {
                    tempList = new ArrayList<>();
                    itemPendingMap.put(sourceItemId, tempList);
                    isNeedBk = Boolean.TRUE;
                }
                for (SgFindSourceStrategyOmsItemResult omsItemResult : currSourceItem) {
                    if (isNeedBk) {
                        SgFindSourceStrategyOmsItemResult itemResult = new SgFindSourceStrategyOmsItemResult();
                        BeanUtils.copyProperties(omsItemResult, itemResult);
                        tempList.add(itemResult);
                    }

                    BigDecimal preOut = omsItemResult.getQtyPreOut();
                    if (qty.compareTo(preOut) >= 0) {
                        omsItemResult.setQtyPreOut(BigDecimal.ZERO);
                    } else {
                        omsItemResult.setQtyPreOut(preOut.subtract(qty));
                    }
                }
                continue;
            }
            // 明细全部分配完毕 跳过
            if (qtyOutStock.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            // 未分配完 添加缺货明细
            SgFindSourceStrategyOmsItemResult omsItem = new SgFindSourceStrategyOmsItemResult();
            omsItem.setSourceItemId(sourceItemId);
            omsItem.setWareHouseId(StrategyConstants.OUT_DEFAULT_STORE_ID);
            omsItem.setQtyPreOut(qtyOutStock);
            currSourceItem.add(omsItem);
        }
        //特殊处理  当前明细占用 小于 占用结果的占用数
        if (pendingFlag) {
            Map<Long, SkuItemCC> mergeMap = new HashMap<>();
            skuItems.stream().forEach(skuItemCC -> {
                Long sourceItemId = skuItemCC.getSourceItemId();
                if (!mergeMap.containsKey(sourceItemId)) {
                    mergeMap.put(sourceItemId, skuItemCC);
                    return;
                }
                SkuItemCC itemCC = mergeMap.get(sourceItemId);
                itemCC.setQty(itemCC.getQty().add(skuItemCC.getQty()));
            });
            for (Long sourceItemId : itemPendingMap.keySet()) {
                List<SgFindSourceStrategyOmsItemResult> itemResultList = itemPendingMap.get(sourceItemId);
                BigDecimal totalQtyPreout = itemResultList.stream().map(SgFindSourceStrategyOmsItemResult::getQtyPreOut).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                SkuItemCC skuItemCC = mergeMap.get(sourceItemId);
                BigDecimal qty = skuItemCC.getQty();

                itemMap.put(sourceItemId, itemResultList);
                if (qty.compareTo(totalQtyPreout) > 0) {

                    SgFindSourceStrategyOmsItemResult omsItem = new SgFindSourceStrategyOmsItemResult();
                    omsItem.setSourceItemId(sourceItemId);
                    omsItem.setWareHouseId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                    omsItem.setQtyPreOut(qty.subtract(totalQtyPreout));
                    List<SgFindSourceStrategyOmsItemResult> itemResults = itemMap.get(sourceItemId);
                    itemResults.add(omsItem);
                }
            }
        }
        List<SgFindSourceStrategyOmsItemResult> newOmsItemList = new ArrayList<>();
        itemMap.values().stream().forEach(o -> {
            o.stream().forEach(i -> {
                newOmsItemList.add(i);
            });
        });
        omsResult.setItemResultList(newOmsItemList);
    }


    /**
     * 将当前占用单 转化为oms明细占用结果
     *
     * @param request
     * @param saveRequest
     * @param saveResult
     * @param omsItemList
     * @param wareHouseIds
     */
    private void setOmsResult(SgFindSourceStrategyCCRequest request, SgBStoFreezeOutBillSaveRequest saveRequest,
                              SgBStoFreezeOutBillResult saveResult,
                              List<SgFindSourceStrategyOmsItemResult> omsItemList, List<Long> wareHouseIds) {
        log.info(LogUtil.format("SgFindSourceCCPreOutStorageService.setOmsResult " +
                        "storeToPhy:{},saveRequest:{},saveResult:{},omsItemList:{},wareHouseIds",
                "SgFindSourceCCPreOutStorageService.setOmsResult"),
                JSONObject.toJSONString(request.getStoreToPhy()), JSONObject.toJSONString(saveRequest)
                , JSONObject.toJSONString(saveResult), JSONObject.toJSONString(omsItemList), JSONObject.toJSONString(wareHouseIds));
        Map<Long, Long> storeToPhy = request.getStoreToPhy();
        // 根据sourceItemId 聚合
        HashMap<Long, SgFindSourceStrategyOmsItemResult> resultMap = new HashMap<>();
        if (!saveRequest.getMergeMark()) {
            List<SgBStoFreezeOutSaveItemRequest> itemRequests = saveRequest.getItemRequests();
            for (SgBStoFreezeOutSaveItemRequest itemRequest : itemRequests) {
                Long sourceBillItemId = itemRequest.getSourceBillItemId();
                SgFindSourceStrategyOmsItemResult omsItemResult = resultMap.computeIfAbsent(sourceBillItemId, k -> new SgFindSourceStrategyOmsItemResult());
                omsItemResult.setSourceItemId(sourceBillItemId);
                omsItemResult.setQtyPreOut(Optional.ofNullable(omsItemResult.getQtyPreOut()).orElse(BigDecimal.ZERO).add(itemRequest.getQtyPreout()));
                Long warehouseId = storeToPhy.get(itemRequest.getCpCStoreId());
                omsItemResult.setWareHouseId(warehouseId);
                if (!wareHouseIds.contains(warehouseId)) {
                    wareHouseIds.add(warehouseId);
                }
                omsItemResult.setStoOutBillNo(saveResult.getBillNo());
            }
        } else {
            List<SgBStoFreezeOutSaveItemLogRequest> itemLogRequests = saveRequest.getItemLogRequests();
            for (SgBStoFreezeOutSaveItemLogRequest itemLogRequest : itemLogRequests) {
                Long sourceBillItemId = itemLogRequest.getSourceBillItemId();
                SgFindSourceStrategyOmsItemResult omsItemResult = resultMap.computeIfAbsent(sourceBillItemId, k -> new SgFindSourceStrategyOmsItemResult());
                omsItemResult.setSourceItemId(sourceBillItemId);
                omsItemResult.setQtyPreOut(Optional.ofNullable(omsItemResult.getQtyPreOut()).orElse(BigDecimal.ZERO).add(itemLogRequest.getQtyPreout()));
                Long warehouseId = storeToPhy.get(itemLogRequest.getCpCStoreId());
                omsItemResult.setWareHouseId(warehouseId);
                if (!wareHouseIds.contains(warehouseId)) {
                    wareHouseIds.add(warehouseId);
                }
                omsItemResult.setStoOutBillNo(saveResult.getBillNo());
            }
        }
        omsItemList.addAll(resultMap.values());
    }


    /**
     * 封装冻结占用单参数
     *
     * @param saveRequest
     * @param occupyPlan
     * @return
     */
    private SgBStoFreezeOutBillSaveRequest buildSaveRequest(SgFindSourceStrategyCCRequest saveRequest, SgOccupyPlanServiceResult occupyPlan) {

        if (log.isInfoEnabled()) {
            log.info(LogUtil.format("寻仓寻物流]TAB 生成占用单封装单据",
                    "SgBStoFreezeOutService.buildSaveRequest", saveRequest.getTid(), saveRequest.getSourceBillNo()));
        }

        SgBStoFreezeOutBillSaveRequest sgStoFreezeOutBillSaveRequest = new SgBStoFreezeOutBillSaveRequest();
        SgBStoFreezeOutSaveMainRequest mainRequest = new SgBStoFreezeOutSaveMainRequest();
        mainRequest.setTid(saveRequest.getTid());
        mainRequest.setSourceBillId(saveRequest.getSourceBillId());

        mainRequest.setSourceBillNo(saveRequest.getSourceBillNo());
        mainRequest.setSourceBillType(saveRequest.getSourceBillType());
        mainRequest.setBillDate(saveRequest.getBillDate());
        mainRequest.setSourceBillDate(saveRequest.getSourceBillDate());
        mainRequest.setCpCShopId(saveRequest.getShopId());


        List<SgOccupyPlanItemResult> itemResultList = occupyPlan.getItemResultList();

        if (CollectionUtils.isNotEmpty(itemResultList)) {
            buildSaveItemRequest(saveRequest, itemResultList, sgStoFreezeOutBillSaveRequest);
            if (CollectionUtils.isNotEmpty(sgStoFreezeOutBillSaveRequest.getItemRequests())) {
                if (log.isInfoEnabled()) {
                    log.info(LogUtil.format("寻仓寻物流]TAB 封装范围批次明细返回结果:{}",
                            "SgBStoFreezeOutService.buildNoProduceItem.v14"),
                            JSONObject.toJSONString(sgStoFreezeOutBillSaveRequest.getItemRequests()));
                }
            }
        }

        if (CollectionUtils.isNotEmpty(sgStoFreezeOutBillSaveRequest.getItemRequests())) {
            List<SgBStoFreezeOutSaveItemRequest> itemRequestList =
                    mergeFreezeOutItem(sgStoFreezeOutBillSaveRequest, sgStoFreezeOutBillSaveRequest.getItemRequests());
            //冗余字段
            CommonCacheValUtils.setSkuInfoByCodeAndProduce(itemRequestList);
            sgStoFreezeOutBillSaveRequest.setItemRequests(itemRequestList);
        }

        sgStoFreezeOutBillSaveRequest.setObjId(-1L);
        sgStoFreezeOutBillSaveRequest.setMainRequest(mainRequest);
        sgStoFreezeOutBillSaveRequest.setLoginUser(SystemUserResource.getRootUser());

        return sgStoFreezeOutBillSaveRequest;
    }

    /**
     * 构建冻结占用单明细参数
     *
     * @param saveRequest
     * @param itemResultList
     * @param sgStoFreezeOutBillSaveRequest
     * @return
     */
    private void buildSaveItemRequest(SgFindSourceStrategyCCRequest saveRequest,
                                      List<SgOccupyPlanItemResult> itemResultList,
                                      SgBStoFreezeOutBillSaveRequest sgStoFreezeOutBillSaveRequest) {

        if (log.isInfoEnabled()) {
            log.info(LogUtil.format("寻仓寻物流]TAB 范围批次明细处理:{}",
                    "SgBStoFreezeOutService.buildNoProduceItem"), JSONObject.toJSONString(itemResultList));
        }
        Map<Long, SgCpCStore> storeMap = saveRequest.getSgCpCStoreMap();
        List<SgBStoFreezeOutSaveItemRequest> itemRequestList = new ArrayList<>();
        List<SgBStoFreezeOutSaveItemLogRequest> itemLogRequestList = new ArrayList<>();
        for (SgOccupyPlanItemResult itemResult : itemResultList) {
            for (Map.Entry<Long, Map<String, BigDecimal>> entry : itemResult.getS2LStorePlan().entrySet()) {
                Long storeId = entry.getKey();
                SgCpCStore store = storeMap.get(storeId);
                for (Map.Entry<String, BigDecimal> decimalEntry : entry.getValue().entrySet()) {
                    String produceDate = decimalEntry.getKey();
                    BigDecimal qty = decimalEntry.getValue();
                    SgBStoFreezeOutSaveItemRequest itemRequest = new SgBStoFreezeOutSaveItemRequest();
                    itemRequest.setStorageType(StoreTypeEnum.ZJ.getValue());
                    itemRequest.setQty(qty);
                    itemRequest.setQtyPreout(qty);
                    itemRequest.setPsCSkuId(itemResult.getPsCSkuId());
//            itemRequest.setPsCProId(itemResult.getPsCProId());
                    itemRequest.setSourceBillItemId(itemResult.getSourceItemId());
                    itemRequest.setCpCStoreId(storeId);
                    itemRequest.setCpCStoreEcode(store.getCpCStoreEcode());
                    itemRequest.setCpCStoreEname(store.getCpCStoreEname());
                    itemRequest.setProduceDate(produceDate);
                    String beginProduceDate = itemResult.getBeginProduceDate();
                    if (StringUtils.isEmpty(beginProduceDate)) {
                        beginProduceDate = SgConstantsIF.DEFAULT_PRODUCE_DATE;
                    }
                    String endProduceDate = itemResult.getEndProduceDate();
                    if (StringUtils.isEmpty(endProduceDate)) {
                        endProduceDate = SgConstantsIF.MAX_PRODUCE_DATE;
                    }
                    itemRequest.setBeginProduceTime(beginProduceDate);
                    itemRequest.setEndProduceTime(endProduceDate);
                    itemRequest.setLabelingRequirements(itemResult.getLabelingRequirements());
                    itemRequestList.add(itemRequest);
                    //创建日志明细的request
                    SgBStoFreezeOutSaveItemLogRequest sgBStoFreezeOutSaveItemLogRequest = new SgBStoFreezeOutSaveItemLogRequest();
                    BeanUtils.copyProperties(itemRequest, sgBStoFreezeOutSaveItemLogRequest);
                    itemLogRequestList.add(sgBStoFreezeOutSaveItemLogRequest);
                }
            }

        }
        sgStoFreezeOutBillSaveRequest.setItemRequests(itemRequestList);
        sgStoFreezeOutBillSaveRequest.setItemLogRequests(itemLogRequestList);
    }

    /**
     * 合并冻结占用单明细
     *
     * @param sgStoFreezeOutBillSaveRequest
     * @param itemRequests                  冻结占用单明细
     * @return java.util.list<com.burgeon.r3.sg.store.model.request.freeze.out.SgBStoFreezeOutSaveItemRequest>
     */
    private List<SgBStoFreezeOutSaveItemRequest> mergeFreezeOutItem(SgBStoFreezeOutBillSaveRequest sgStoFreezeOutBillSaveRequest,
                                                                    List<SgBStoFreezeOutSaveItemRequest> itemRequests) {
        List<SgBStoFreezeOutSaveItemRequest> returnList = new ArrayList<>();
        Map<String, List<SgBStoFreezeOutSaveItemRequest>> groupMap = itemRequests.stream().collect(Collectors.groupingBy(x
                -> x.getPsCSkuId() + x.getProduceDate() + x.getStorageType()));

        for (String key : groupMap.keySet()) {
            List<SgBStoFreezeOutSaveItemRequest> itemRequestList = groupMap.get(key);
            if (itemRequestList.size() > 1) {
                sgStoFreezeOutBillSaveRequest.setMergeMark(true);
                SgBStoFreezeOutSaveItemRequest itemRequest = itemRequestList.get(0);
                BigDecimal qty = itemRequestList.stream().map(SgBStoFreezeOutSaveItemRequest::getQty).
                        reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal qtyPreout = itemRequestList.stream().map(SgBStoFreezeOutSaveItemRequest::getQtyPreout).
                        reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                itemRequest.setQty(qty);
                itemRequest.setQtyPreout(qtyPreout);
                returnList.add(itemRequest);
            } else {
                returnList.add(itemRequestList.get(0));
            }
        }

        return returnList;
    }

}