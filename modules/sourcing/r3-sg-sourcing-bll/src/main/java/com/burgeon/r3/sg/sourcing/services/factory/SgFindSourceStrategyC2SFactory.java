package com.burgeon.r3.sg.sourcing.services.factory;

import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.StrategyFactoryBaseResult;
import com.burgeon.r3.sg.sourcing.model.result.factory.SgFindSourceStrategyFactoryResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: hwy
 * @time: 2021/6/23 13:08
 */
@Component
@Data
public class SgFindSourceStrategyC2SFactory extends StrategyFactory {

    private List<? extends StrategyFactoryBaseResult> strategiesList;

    private StrategyHandle handle;

    /**
     * @Description: 寻源引擎类路径
     **/
    private final static String STRATEGY_CLAZZ = "com.burgeon.r3.sg.sourcing.services.SgFindSourceRuleEngineStrategyService";
    /**
     * @Description: 优先级
     **/
    private final static Integer PRIORITY = 1;
    /**
     * @Description: 名称
     **/
    private final static String STRATEGY_NAME = "寻源规则引擎";

    @Override
    public List<? extends StrategyFactoryBaseResult> getStrategies(StrategyBaseRequest request) {
        ArrayList<SgFindSourceStrategyFactoryResult> results = new ArrayList<>();
        SgFindSourceStrategyFactoryResult result = new SgFindSourceStrategyFactoryResult();
        result.setPriority(PRIORITY);
        result.setStrategyClazz(STRATEGY_CLAZZ);
        result.setStrategyName(STRATEGY_NAME);
        results.add(result);
        return results;
    }
}