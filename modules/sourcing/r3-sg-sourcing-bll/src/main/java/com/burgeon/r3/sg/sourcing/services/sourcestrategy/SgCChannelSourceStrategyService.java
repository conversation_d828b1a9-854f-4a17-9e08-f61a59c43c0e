package com.burgeon.r3.sg.sourcing.services.sourcestrategy;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.r3.sg.basic.annotation.SgOperationLog;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.basic.services.log.LogCommonService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.core.common.R3ParamConstants;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.SgCChannelSourceStrategyTypeEnum;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.basic.SgCOperationLog;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareScoreStrategy;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceForceStrategy;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategy;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategyForceItem;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategyRuleItem;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategyScoreItem;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategyWarehouseItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceForceStrategyMapper;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyForceItemMapper;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyMapper;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyRuleItemMapper;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyScoreItemMapper;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyWarehouseItemMapper;
import com.burgeon.r3.sg.sourcing.mapper.SgCShareScoreStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.request.sourcestrategy.SgCChannelSourceStrategyBillQueryRequest;
import com.burgeon.r3.sg.sourcing.model.request.sourcestrategy.SgCChannelSourceStrategyBillSaveRequest;
import com.burgeon.r3.sg.sourcing.model.request.sourcestrategy.SgCChannelSourceStrategyForceItemSaveRequest;
import com.burgeon.r3.sg.sourcing.model.request.sourcestrategy.SgCChannelSourceStrategyItemSaveRequest;
import com.burgeon.r3.sg.sourcing.model.request.sourcestrategy.SgCChannelSourceStrategyRuleItemSaveRequest;
import com.burgeon.r3.sg.sourcing.model.request.sourcestrategy.SgCChannelSourceStrategySaveRequest;
import com.burgeon.r3.sg.sourcing.model.request.sourcestrategy.SgCChannelSourceStrategyScoreItemSaveRequest;
import com.burgeon.r3.sg.sourcing.model.request.sourcestrategy.SgCChannelSourceStrategyWarehouseItemSaveRequest;
import com.burgeon.r3.sg.sourcing.model.result.sourcestrategy.SgCChannelSourceStrategyResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/18 15:09
 * 寻源策略表Service
 */
@Slf4j
@Component
public class SgCChannelSourceStrategyService extends ServiceImpl<SgCChannelSourceStrategyMapper,
        SgCChannelSourceStrategy> {
    @Autowired
    private SgCChannelSourceStrategyMapper sgChannelSourceStrategyMapper;
    @Autowired
    private SgCChannelSourceStrategyForceItemMapper sgChannelSourceStrategyForceItemMapper;
    @Autowired
    private SgCChannelSourceStrategyRuleItemMapper sgChannelSourceStrategyRuleItemMapper;
    @Autowired
    private SgCChannelSourceStrategyScoreItemMapper sgChannelSourceStrategyScoreItemMapper;
    @Autowired
    private SgCChannelSourceForceStrategyMapper sgChannelSourceForceStrategyMapper;
    @Autowired
    private SgCChannelSourceStrategyRuleItemService sgChannelSourceStrategyRuleItemService;
    @Autowired
    private SgCShareScoreStrategyMapper sgShareScoreStrategyMapper;
    @Autowired
    private CpCPhyWarehouseMapper warehouseMapper;
    @Autowired
    private SgCChannelSourceStrategyWarehouseItemMapper sourceStrategyWarehouseItemMapper;
    @Resource
    private LogCommonService logCommonService;


    /**
     * 寻源策略定制保存
     *
     * @param request 请求参数
     * @return ValueHolderV14
     */
    public ValueHolderV14<Long> save(SgCChannelSourceStrategyBillSaveRequest request) {
        ValueHolderV14<Long> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "保存成功！");
        SgCChannelSourceStrategyService service =
                ApplicationContextHandle.getBean(SgCChannelSourceStrategyService.class);
        try {
            Long mainId = service.saveSgChannelSourceStrategyAndItems(request);
            v14.setData(mainId);
        } catch (Exception e) {

            log.error("{}.save. exception_has_occured:{}", this.getClass().getName(),
                    Throwables.getStackTraceAsString(e));

            v14.setCode(ResultCode.FAIL);
            v14.setMessage("保存失败！" + e.getMessage());
        }
        return v14;
    }

    /**
     * 主表明细表保存逻辑
     *
     * @param request 请求参数
     * @return 主表id
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveSgChannelSourceStrategyAndItems(SgCChannelSourceStrategyBillSaveRequest request) {
        SgCChannelSourceStrategySaveRequest mainSaveRequest = request.getMainSaveRequest();
        User user = request.getLoginUser();
        List<SgCOperationLog> operationLogList = new ArrayList<>();
        boolean isMod = mainSaveRequest.getId() > 0;
        //主表
        Long mainId = checkAndSaveMainTable(mainSaveRequest, user, operationLogList, isMod);
        //强制寻源明细表处理
        List<SgCChannelSourceStrategyForceItemSaveRequest> forceItemSaveRequests = request.getForceItemSaveRequests();
        if (CollectionUtils.isNotEmpty(forceItemSaveRequests)) {
            saveForceItems(forceItemSaveRequests, mainSaveRequest, user, operationLogList, isMod);
        }
        //寻源规则明细表处理
        List<SgCChannelSourceStrategyRuleItemSaveRequest> ruleItemSaveRequests = request.getRuleItemSaveRequests();
        if (CollectionUtils.isNotEmpty(ruleItemSaveRequests)) {
            saveRuleItems(ruleItemSaveRequests, mainSaveRequest, user, operationLogList, isMod);
        }
        //评分策略明细表处理
        SgCChannelSourceStrategyScoreItemSaveRequest scoreItemSaveRequest = request.getScoreItemSaveRequest();
        if (scoreItemSaveRequest != null) {
            saveScoreItem(scoreItemSaveRequest, mainSaveRequest, user, operationLogList, isMod);
        }
        //仓优先策略明细表处理
        List<SgCChannelSourceStrategyWarehouseItemSaveRequest> warehouseItemSaveRequest = request.getWarehouseItemSaveRequest();
        if (CollectionUtils.isNotEmpty(warehouseItemSaveRequest)) {
            saveWarehouseItem(warehouseItemSaveRequest, mainSaveRequest, user, operationLogList, isMod);
        }
        //保存操作日志
        logCommonService.batchInsertLog(operationLogList);
        return mainId;
    }

    /**
     * 仓优先策略明细保存
     *
     * @param warehouseItemSaveRequest
     * @param mainSaveRequest
     * @param user
     * @param operationLogList
     * @param isMod
     */
    private void saveWarehouseItem(List<SgCChannelSourceStrategyWarehouseItemSaveRequest> warehouseItemSaveRequest,
                                   SgCChannelSourceStrategySaveRequest mainSaveRequest, User user,
                                   List<SgCOperationLog> operationLogList, boolean isMod) {
        List<Long> warehouseIds = warehouseItemSaveRequest.stream().map(SgCChannelSourceStrategyWarehouseItemSaveRequest::getCpCPhyWarehouseId).distinct().collect(Collectors.toList());
        AssertUtils.notEmpty(warehouseIds, "参数实体仓id不能为空");
        List<SgCpCPhyWarehouse> warehouseList = warehouseMapper.selectList(Wrappers.<SgCpCPhyWarehouse>lambdaQuery()
                .in(SgCpCPhyWarehouse::getId, warehouseIds)
                .eq(SgCpCPhyWarehouse::getIsactive, SgConstants.IS_ACTIVE_Y));
        AssertUtils.notEmpty(warehouseList, "实体仓不存在");
        Map<Long, SgCpCPhyWarehouse> warehouseMap = warehouseList.stream().collect(Collectors.toMap(SgCpCPhyWarehouse::getId, t -> t));
        List<SgCChannelSourceStrategyWarehouseItem> addItems = new ArrayList<>();
        List<SgCChannelSourceStrategyWarehouseItem> updateItems = new ArrayList<>();
        //查询变更前的数据
        Map<Long, SgCChannelSourceStrategyWarehouseItem> finalWarehouseItemMap =
                querySgCChannelSourceStrategyWarehouseItemMap(warehouseItemSaveRequest);
        warehouseItemSaveRequest.stream().forEach(itemRequest -> {
            SgCpCPhyWarehouse sgCpCPhyWarehouse = warehouseMap.get(itemRequest.getCpCPhyWarehouseId());
            if (ObjectUtils.isEmpty(sgCpCPhyWarehouse)) {
                log.info(" SgCChannelSourceStrategyService saveWarehouseItem 当前实体仓不存在 warehouseId = {}", itemRequest.getCpCPhyWarehouseId());
                return;
            }
            SgCChannelSourceStrategyWarehouseItem warehouseItem = new SgCChannelSourceStrategyWarehouseItem();
            BeanUtils.copyProperties(itemRequest, warehouseItem);
            warehouseItem.setCpCPhyWarehouseEcode(sgCpCPhyWarehouse.getEcode());
            warehouseItem.setCpCPhyWarehouseEname(sgCpCPhyWarehouse.getEname());
            warehouseItem.setSgCChannelSourceStrategyId(mainSaveRequest.getId());
            if (itemRequest.getId() > 0) {
                //修改
                StorageUtils.setBModelDefalutDataByUpdate(warehouseItem, user);
                updateItems.add(warehouseItem);
                //构建仓优先更新操作日志
                buildWarehouseItemModLog(mainSaveRequest, user, operationLogList, isMod, finalWarehouseItemMap, warehouseItem);
            } else {
                //新增
                Long id = ModelUtil.getSequence(SgConstants.SG_C_CHANNEL_SOURCE_STRATEGY_WAREHOUSE_ITEM);
                warehouseItem.setId(id);
                StorageUtils.setBModelDefalutData(warehouseItem, user);
                addItems.add(warehouseItem);
                //构建仓优先新增操作日志
                if (isMod) {
                    buildWarehouseItemAddLog(mainSaveRequest, user, operationLogList, warehouseItem);
                }
            }

        });

        if (CollectionUtils.isNotEmpty(addItems)) {

            sourceStrategyWarehouseItemMapper.batchInsert(addItems);
        }

        if (CollectionUtils.isNotEmpty(updateItems)) {
            for (SgCChannelSourceStrategyWarehouseItem updateItem : updateItems) {

                sourceStrategyWarehouseItemMapper.update(updateItem, Wrappers.<SgCChannelSourceStrategyWarehouseItem>lambdaUpdate()
                        .eq(SgCChannelSourceStrategyWarehouseItem::getId, updateItem.getId())
                        .set(SgCChannelSourceStrategyWarehouseItem::getCpCPhyWarehouseId, updateItem.getCpCPhyWarehouseId())
                        .set(SgCChannelSourceStrategyWarehouseItem::getCpCPhyWarehouseEcode, updateItem.getCpCPhyWarehouseEcode())
                        .set(SgCChannelSourceStrategyWarehouseItem::getCpCPhyWarehouseEname, updateItem.getCpCPhyWarehouseEname())
                        .set(SgCChannelSourceStrategyWarehouseItem::getModifierename, updateItem.getModifierename())
                        .set(SgCChannelSourceStrategyWarehouseItem::getModifieddate, updateItem.getModifieddate())
                        .set(SgCChannelSourceStrategyWarehouseItem::getModifierid, updateItem.getModifierid()));
            }
        }

    }

    /**
     * 构建仓优先新增操作日志
     *
     * @param mainSaveRequest
     * @param user
     * @param operationLogList
     * @param warehouseItem
     */
    private void buildWarehouseItemAddLog(SgCChannelSourceStrategySaveRequest mainSaveRequest, User user,
                                          List<SgCOperationLog> operationLogList,
                                          SgCChannelSourceStrategyWarehouseItem warehouseItem) {
        SgCOperationLog operationLog =
                logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY_WAREHOUSE_ITEM",
                        OperationTypeEnum.ADD.getOperationValue(), mainSaveRequest.getId(),
                        "仓优先", "实体仓", null,
                        warehouseItem.getCpCPhyWarehouseEname(), user);
        operationLogList.add(operationLog);
        SgCOperationLog operationLog1 =
                logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY_WAREHOUSE_ITEM",
                        OperationTypeEnum.ADD.getOperationValue(), mainSaveRequest.getId(),
                        "仓优先", "优先级", null,
                        warehouseItem.getPriority().toString(), user);
        operationLogList.add(operationLog1);
    }

    /**
     * 构建仓优先更新操作日志
     *
     * @param mainSaveRequest
     * @param user
     * @param operationLogList
     * @param isMod
     * @param finalWarehouseItemMap
     * @param warehouseItem
     */
    private void buildWarehouseItemModLog(SgCChannelSourceStrategySaveRequest mainSaveRequest,
                                          User user, List<SgCOperationLog> operationLogList,
                                          boolean isMod, Map<Long, SgCChannelSourceStrategyWarehouseItem> finalWarehouseItemMap, SgCChannelSourceStrategyWarehouseItem warehouseItem) {
        SgCChannelSourceStrategyWarehouseItem oldWarehouseItem = finalWarehouseItemMap.get(warehouseItem.getId());
        if (isMod && oldWarehouseItem != null) {
            if (!ObjectUtil.equals(oldWarehouseItem.getCpCPhyWarehouseId(), warehouseItem.getCpCPhyWarehouseId())) {
                SgCOperationLog operationLog =
                        logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY_WAREHOUSE_ITEM",
                                OperationTypeEnum.MOD.getOperationValue(), mainSaveRequest.getId(),
                                "仓优先", "实体仓", oldWarehouseItem.getCpCPhyWarehouseEname(),
                                warehouseItem.getCpCPhyWarehouseEname(), user);
                operationLogList.add(operationLog);
            }
            if (!ObjectUtil.equals(oldWarehouseItem.getPriority(), warehouseItem.getPriority())) {
                SgCOperationLog operationLog1 =
                        logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY_WAREHOUSE_ITEM",
                                OperationTypeEnum.MOD.getOperationValue(), mainSaveRequest.getId(),
                                "仓优先", "优先级", oldWarehouseItem.getPriority().toString(),
                                warehouseItem.getPriority().toString(), user);
                operationLogList.add(operationLog1);
            }
        }
    }

    /**
     * 查询变更前的数据
     *
     * @param warehouseItemSaveRequest
     * @return
     */
    private Map<Long, SgCChannelSourceStrategyWarehouseItem> querySgCChannelSourceStrategyWarehouseItemMap(List<SgCChannelSourceStrategyWarehouseItemSaveRequest> warehouseItemSaveRequest) {
        List<Long> itemIds = warehouseItemSaveRequest.stream().map(SgCChannelSourceStrategyWarehouseItemSaveRequest::getId)
                .filter(id -> id > 0L).collect(Collectors.toList());
        Map<Long, SgCChannelSourceStrategyWarehouseItem> warehouseItemMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(itemIds)) {
            List<SgCChannelSourceStrategyWarehouseItem> itemList = sourceStrategyWarehouseItemMapper.selectBatchIds(itemIds);
            if (CollectionUtils.isNotEmpty(itemList)) {
                warehouseItemMap = itemList.stream()
                        .collect(Collectors.toMap(SgCChannelSourceStrategyWarehouseItem::getId, Function.identity(), (x, y) -> y));
            }
        }
        return warehouseItemMap;
    }

    /**
     * 强制寻源规则明细表保存
     *
     * @param forceItemSaveRequests 强制寻源规则请求参数
     * @param mainSaveRequest       主表参数(id)
     * @param user                  用户
     * @param operationLogList
     * @param isMod
     */
    private void saveForceItems(List<SgCChannelSourceStrategyForceItemSaveRequest> forceItemSaveRequests,
                                SgCChannelSourceStrategySaveRequest mainSaveRequest, User user, List<SgCOperationLog> operationLogList, boolean isMod) {
        List<SgCChannelSourceStrategyForceItem> strategyForceItems =
                sgChannelSourceStrategyForceItemMapper.selectList(new LambdaQueryWrapper<SgCChannelSourceStrategyForceItem>()
                        .eq(SgCChannelSourceStrategyForceItem::getSgCChannelSourceStrategyId, mainSaveRequest.getId())
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        List<String> unSaveItemList =
                strategyForceItems.stream().map(i -> i.getSgCChannelSourceForceStrategyId() + ":" + i.getCpCStoreId()).collect(Collectors.toList());
        //收集类型id，组装强制寻源规则类型map数据
        Map<Long, SgCChannelSourceForceStrategy> forceStrategyMap = new HashMap<>(16);
        List<Long> forceStrategyIds =
                forceItemSaveRequests.stream().map(SgCChannelSourceStrategyForceItemSaveRequest::getSgCChannelSourceForceStrategyId).collect(Collectors.toList());
        List<SgCChannelSourceForceStrategy> forceStrategyList =
                sgChannelSourceForceStrategyMapper.selectBatchIds(forceStrategyIds);
        if (CollectionUtils.isNotEmpty(forceStrategyList)) {
            forceStrategyList.forEach(f -> forceStrategyMap.put(f.getId(), f));
        }
        List<SgCChannelSourceStrategyForceItem> saveItemList = new ArrayList<>();
        for (SgCChannelSourceStrategyForceItemSaveRequest forceItemSaveRequest : forceItemSaveRequests) {
            SgCChannelSourceForceStrategy sgChannelSourceForceStrategy =
                    forceStrategyMap.get(forceItemSaveRequest.getSgCChannelSourceForceStrategyId());
            if (sgChannelSourceForceStrategy != null) {
                List<SgCChannelSourceStrategyForceItemSaveRequest.StoreInfo> storeInfos =
                        forceItemSaveRequest.getStoreInfos();
                //前端传参 收集新增的数据
                List<SgCChannelSourceStrategyForceItemSaveRequest.StoreInfo> insertForceItemStores =
                        storeInfos.stream().filter(s -> s.getId() < 0L).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(insertForceItemStores)) {
                    List<Long> queryStoreIds = insertForceItemStores.stream()
                            .map(SgCChannelSourceStrategyForceItemSaveRequest.StoreInfo::getCpCStoreId).collect(Collectors.toList());
                    Map<Long, CpCStore> storeInfoByIdMap = CommonCacheValUtils.getStoreInfoByIdList(queryStoreIds);
                    AssertUtils.cannot(MapUtils.isEmpty(storeInfoByIdMap),"寻源策略强制寻源规则查询逻辑仓档案异常!");
                    for (SgCChannelSourceStrategyForceItemSaveRequest.StoreInfo store : insertForceItemStores) {
                        SgCChannelSourceStrategyForceItem forceItem = new SgCChannelSourceStrategyForceItem();
                        CpCStore storeInfo = storeInfoByIdMap != null ? storeInfoByIdMap.get(store.getCpCStoreId()) : null;
                        if (storeInfo != null) {
                            if (unSaveItemList.contains(sgChannelSourceForceStrategy.getId() + ":" + storeInfo.getId())) {
                                continue;
                            }
                            forceItem.setCpCStoreId(storeInfo.getId());
                            forceItem.setCpCStoreEcode(storeInfo.getEcode());
                            forceItem.setCpCStoreEname(storeInfo.getEname());
                        } else {
                            AssertUtils.logAndThrow("当前选择店仓已不存在！", user.getLocale());
                        }
                        forceItem.setSgCChannelSourceForceStrategyId(sgChannelSourceForceStrategy.getId());
                        forceItem.setSgCChannelSourceForceStrategyEcode(sgChannelSourceForceStrategy.getEcode());
                        forceItem.setSgCChannelSourceForceStrategyEname(sgChannelSourceForceStrategy.getEname());
                        forceItem.setId(ModelUtil.getSequence(SgConstants.SG_C_CHANNEL_SOURCE_STRATEGY_FORCE_ITEM));
                        forceItem.setSgCChannelSourceStrategyId(mainSaveRequest.getId());
                        StorageUtils.setBModelDefalutData(forceItem, user);
                        forceItem.setModifierename(user.getEname());
                        forceItem.setOwnerename(user.getEname());
                        saveItemList.add(forceItem);
                        //构建强制寻源规则操作日志
                        if (isMod) {
                            buildForceItemLog(mainSaveRequest, user, operationLogList, forceItem);
                        }
                    }
                }
            } else {
                AssertUtils.logAndThrow("当前类型已不存在！", user.getLocale());
            }
        }
        if (CollectionUtils.isNotEmpty(saveItemList)) {
            sgChannelSourceStrategyForceItemMapper.batchInsert(saveItemList);
        }
    }

    /**
     * 构建强制寻源规则操作日志
     *
     * @param mainSaveRequest
     * @param user
     * @param operationLogList
     * @param forceItem
     */
    private void buildForceItemLog(SgCChannelSourceStrategySaveRequest mainSaveRequest, User user,
                                   List<SgCOperationLog> operationLogList, SgCChannelSourceStrategyForceItem forceItem) {
        SgCOperationLog operationLog =
                logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY_FORCE_ITEM",
                        OperationTypeEnum.ADD.getOperationValue(), mainSaveRequest.getId(),
                        "强制寻源规则", "店仓", null, forceItem.getCpCStoreEname(), user);
        operationLogList.add(operationLog);
    }

    /**
     * 构建寻源规则操作日志
     *
     * @param mainSaveRequest
     * @param user
     * @param operationLogList
     * @param ruleItem
     */
    private void buildRuleItemLog(SgCChannelSourceStrategySaveRequest mainSaveRequest, User user,
                                  List<SgCOperationLog> operationLogList, SgCChannelSourceStrategyRuleItem ruleItem) {
        SgCOperationLog operationLog =
                logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY_RULE_ITEM",
                        OperationTypeEnum.ADD.getOperationValue(), mainSaveRequest.getId(),
                        "寻源规则", "寻源规则", null,
                        ruleItem.getSgCShareSourceRuleStrategyEname(), user);
        operationLogList.add(operationLog);
    }

    /**
     * 构建评分策略操作日志
     *
     * @param mainSaveRequest
     * @param user
     * @param operationLogList
     * @param scoreItem
     */
    private void buildScoreItemLog(SgCChannelSourceStrategySaveRequest mainSaveRequest, User user,
                                   List<SgCOperationLog> operationLogList, SgCChannelSourceStrategyScoreItem scoreItem) {
        SgCOperationLog operationLog =
                logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY_SCORE_ITEM",
                        OperationTypeEnum.ADD.getOperationValue(), mainSaveRequest.getId(),
                        "评分策略", "评分策略", null,
                        scoreItem.getSgCShareScoreStrategyEname(), user);
        operationLogList.add(operationLog);
    }

    /**
     * 寻源规则明细表保存
     *
     * @param ruleItemSaveRequests 寻源规则请求参数
     * @param mainSaveRequest      主表参数(id)
     * @param user                 用户
     * @param operationLogList
     * @param isMod
     */
    private void saveRuleItems(List<SgCChannelSourceStrategyRuleItemSaveRequest> ruleItemSaveRequests,
                               SgCChannelSourceStrategySaveRequest mainSaveRequest, User user,
                               List<SgCOperationLog> operationLogList, boolean isMod) {
        for (SgCChannelSourceStrategyRuleItemSaveRequest ruleItemSaveRequest : ruleItemSaveRequests) {
            Integer priority = ruleItemSaveRequest.getPriority();
            Long mainId = mainSaveRequest.getId();
            List<SgCChannelSourceStrategyRuleItemSaveRequest.SaveShareSourceRuleStrategyInfo> shareSourceRuleStrategyInfos = ruleItemSaveRequest.getShareSourceRuleStrategyInfos();
            //新增行直接保存  不勾选默认为N
            if (ruleItemSaveRequest.isNewLine()) {
                int priorityCount = sgChannelSourceStrategyRuleItemMapper.selectCount(
                        new LambdaQueryWrapper<SgCChannelSourceStrategyRuleItem>()
                                .eq(SgCChannelSourceStrategyRuleItem::getPriority, priority)
                                .eq(SgCChannelSourceStrategyRuleItem::getSgCChannelSourceStrategyId, mainId));
                AssertUtils.cannot(priorityCount > 0, "寻缘层级不允许重复！", user.getLocale());
                List<SgCChannelSourceStrategyRuleItem> saveItemList = new ArrayList<>();
                for (SgCChannelSourceStrategyRuleItemSaveRequest.SaveShareSourceRuleStrategyInfo shareSourceRuleStrategyInfo : shareSourceRuleStrategyInfos) {
                    SgCChannelSourceStrategyRuleItem saveRuleItem = new SgCChannelSourceStrategyRuleItem();
                    saveRuleItem.setPriority(priority);
                    saveRuleItem.setSgCChannelSourceStrategyId(mainId);
                    saveRuleItem.setSgCShareSourceRuleStrategyId(shareSourceRuleStrategyInfo.getSgCShareSourceRuleStrategyId());
                    saveRuleItem.setSgCShareSourceRuleStrategyEcode(shareSourceRuleStrategyInfo.getSgCShareSourceRuleStrategyEcode());
                    saveRuleItem.setSgCShareSourceRuleStrategyEname(shareSourceRuleStrategyInfo.getSgCShareSourceRuleStrategyEname());
                    saveRuleItem.setId(ModelUtil.getSequence(SgConstants.SG_C_CHANNEL_SOURCE_STRATEGY_RULE_ITEM));
                    StorageUtils.setBModelDefalutData(saveRuleItem, user);
                    saveRuleItem.setModifierename(user.getEname());
                    saveRuleItem.setOwnerename(user.getEname());
                    saveRuleItem.setIsactive(shareSourceRuleStrategyInfo.getIsactive());
                    saveItemList.add(saveRuleItem);
                    //构建寻源规则操作日志
                    if (isMod) {
                        buildRuleItemLog(mainSaveRequest, user, operationLogList, saveRuleItem);
                    }
                }
                if (CollectionUtils.isNotEmpty(saveItemList)) {
                    sgChannelSourceStrategyRuleItemService.saveBatch(saveItemList);
                }
            } else {
                //查询当前寻缘层级下 寻源规则表数据
                List<SgCChannelSourceStrategyRuleItem> sourceStrategyRuleItems =
                        sgChannelSourceStrategyRuleItemMapper.selectList(
                                new LambdaQueryWrapper<SgCChannelSourceStrategyRuleItem>()
                                        .eq(SgCChannelSourceStrategyRuleItem::getPriority, priority)
                                        .eq(SgCChannelSourceStrategyRuleItem::getSgCChannelSourceStrategyId, mainId));
                List<SgCChannelSourceStrategyRuleItem> updateItemList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(sourceStrategyRuleItems)) {
                    Map<Long, String> ruleMap = sourceStrategyRuleItems.stream()
                            .collect(Collectors.toMap(SgCChannelSourceStrategyRuleItem::getId, BaseModel::getIsactive));
                    for (SgCChannelSourceStrategyRuleItemSaveRequest.SaveShareSourceRuleStrategyInfo shareSourceRuleStrategyInfo : shareSourceRuleStrategyInfos) {
                        //原表启用情况与前端传输对比 确认是否更新
                        String isactive = ruleMap.get(shareSourceRuleStrategyInfo.getId());
                        if (!isactive.equals(shareSourceRuleStrategyInfo.getIsactive())) {
                            SgCChannelSourceStrategyRuleItem updateItem = new SgCChannelSourceStrategyRuleItem();
                            updateItem.setIsactive(shareSourceRuleStrategyInfo.getIsactive());
                            updateItem.setId(shareSourceRuleStrategyInfo.getId());
                            StorageUtils.setBModelDefalutDataByUpdate(updateItem, user);
                            updateItem.setModifierename(user.getEname());
                            updateItemList.add(updateItem);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(updateItemList)) {
                        sgChannelSourceStrategyRuleItemService.updateBatchById(updateItemList);
                    }
                }
            }
        }
    }

    /**
     * 评分策略明细表保存
     *
     * @param scoreItemSaveRequest 评分策略请求参数
     * @param mainSaveRequest      主表参数(id)
     * @param user                 用户
     * @param operationLogList
     * @param isMod
     */
    private void saveScoreItem(SgCChannelSourceStrategyScoreItemSaveRequest scoreItemSaveRequest,
                               SgCChannelSourceStrategySaveRequest mainSaveRequest, User user,
                               List<SgCOperationLog> operationLogList, boolean isMod) {
        Long sgShareScoreStrategyId = scoreItemSaveRequest.getSgCShareScoreStrategyId();
        SgCShareScoreStrategy sgShareScoreStrategy = sgShareScoreStrategyMapper.selectById(sgShareScoreStrategyId);
        AssertUtils.cannot(sgShareScoreStrategy == null, "当前评分策略已不存在！", user.getLocale());
        SgCChannelSourceStrategyScoreItem saveItem = new SgCChannelSourceStrategyScoreItem();
        if (sgShareScoreStrategy != null) {
            saveItem.setSgCShareScoreStrategyId(sgShareScoreStrategy.getId());
            saveItem.setSgCShareScoreStrategyEname(sgShareScoreStrategy.getEname());
        }
        if (scoreItemSaveRequest.getId() > 0L) {
            saveItem.setId(scoreItemSaveRequest.getId());
            StorageUtils.setBModelDefalutDataByUpdate(saveItem, user);
            saveItem.setModifierename(user.getEname());
            sgChannelSourceStrategyScoreItemMapper.updateById(saveItem);
        } else {
            saveItem.setId(ModelUtil.getSequence(SgConstants.SG_C_CHANNEL_SOURCE_STRATEGY_SCORE_ITEM));
            saveItem.setSgCChannelSourceStrategyId(mainSaveRequest.getId());
            StorageUtils.setBModelDefalutData(saveItem, user);
            saveItem.setModifierename(user.getEname());
            saveItem.setOwnerename(user.getEname());
            sgChannelSourceStrategyScoreItemMapper.insert(saveItem);
            //构建评分策略操作日志
            if (isMod) {
                buildScoreItemLog(mainSaveRequest, user, operationLogList, saveItem);
            }
        }
    }

    /**
     * 主表参数校验和保存
     *
     * @param mainSaveRequest  主表请求参数
     * @param user             用户
     * @param operationLogList
     * @param isMod
     */
    private Long checkAndSaveMainTable(SgCChannelSourceStrategySaveRequest mainSaveRequest, User user, List<SgCOperationLog> operationLogList, boolean isMod) {
        Long id = mainSaveRequest.getId();
        Long platformId = mainSaveRequest.getCpCPlatformId();
        Long cpShopId = mainSaveRequest.getCpCShopId();
        Integer type = mainSaveRequest.getType();
        String ename = mainSaveRequest.getEname();
        if (StringUtils.isNotBlank(ename)) {
            int selectCount =
                    sgChannelSourceStrategyMapper.selectCount(new LambdaQueryWrapper<SgCChannelSourceStrategy>()
                            .eq(SgCChannelSourceStrategy::getEname, ename)
                            .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
            AssertUtils.cannot(selectCount > 0, "名称已存在，不允许保存!");
        }
        SgCChannelSourceStrategy sourceStrategy = new SgCChannelSourceStrategy();
        boolean insertFlag = false;
        BeanUtils.copyProperties(mainSaveRequest, sourceStrategy);
        if (id > 0) {
            SgCChannelSourceStrategy channelSourceStrategy = sgChannelSourceStrategyMapper.selectById(id);
            //逻辑中有变化，所以先copy供记录操作日志使用
            SgCChannelSourceStrategy beforeSourceStrategy = new SgCChannelSourceStrategy();
            BeanUtils.copyProperties(channelSourceStrategy, beforeSourceStrategy);
            if (channelSourceStrategy != null) {
                Integer status = channelSourceStrategy.getStatus();
                AssertUtils.cannot(status.equals(SgSourcingConstants.BILL_SOURCE_STRATEGY_SUBMIT), "单据状态已审核，不允许保存！",
                        user.getLocale());
                AssertUtils.cannot(status.equals(SgSourcingConstants.BILL_SOURCE_STRATEGY_CLOSED), "单据状态已结案，不允许保存！",
                        user.getLocale());
                AssertUtils.cannot(status.equals(SgSourcingConstants.BILL_SOURCE_STRATEGY_VOID), "单据状态已作废，不允许保存！",
                        user.getLocale());
                if (sourceStrategy.getBeginTime() == null) {
                    sourceStrategy.setBeginTime(channelSourceStrategy.getBeginTime());
                }
                if (sourceStrategy.getEndTime() == null) {
                    sourceStrategy.setEndTime(channelSourceStrategy.getEndTime());
                }
                if (sourceStrategy.getCpCShopId() == null) {
                    sourceStrategy.setCpCShopId(channelSourceStrategy.getCpCShopId());
                }
                if (sourceStrategy.getCpCPlatformId() == null) {
                    sourceStrategy.setCpCPlatformId(channelSourceStrategy.getCpCPlatformId());
                }
                StorageUtils.setBModelDefalutDataByUpdate(sourceStrategy, user);
                checkShopAndPlatform(user, platformId, cpShopId, channelSourceStrategy);
                if (isMod) {
                    //构建主表修改日志
                    buildMainTableLog(user, operationLogList, id, sourceStrategy, beforeSourceStrategy);
                }
            } else {
                AssertUtils.logAndThrow("当前记录已不存在！", user.getLocale());
            }
        } else {
            insertFlag = true;
            AssertUtils.cannot(StringUtils.isBlank(ename), "请输入策略名称!", user.getLocale());
            AssertUtils.notNull(type,"类型不能为空");
            AssertUtils.cannot(type == SgConstants.SG_C_CHANNEL_SOURCE_STRATEGY_TYPE_SHOP && cpShopId == null, "平台店铺不能为空!", user.getLocale());
            checkShopAndPlatform(user, platformId, cpShopId, sourceStrategy);
            StorageUtils.setBModelDefalutData(sourceStrategy, user);
            sourceStrategy.setStatus(SgSourcingConstants.BILL_SOURCE_STRATEGY_UNSUBMIT);
            sourceStrategy.setOwnerename(user.getEname());
            AssertUtils.cannot(sourceStrategy.getBeginTime() == null || sourceStrategy.getEndTime() == null,
                    "开始时间和结束时间不能为空！", user.getLocale());
            id = ModelUtil.getSequence(SgConstants.SG_C_CHANNEL_SOURCE_STRATEGY);
            sourceStrategy.setId(id);
            mainSaveRequest.setId(id);
        }
        AssertUtils.cannot(sourceStrategy.getBeginTime().after(sourceStrategy.getEndTime()), "开始时间不允许大于结束时间!",
                user.getLocale());
        if (insertFlag) {
            sgChannelSourceStrategyMapper.insert(sourceStrategy);
        } else {
            sgChannelSourceStrategyMapper.updateById(sourceStrategy);
        }
        return id;
    }

    /**
     * 主表的修改日志
     *
     * @param user
     * @param operationLogList
     * @param id
     * @param sourceStrategy
     * @param channelSourceStrategy
     */
    private void buildMainTableLog(User user, List<SgCOperationLog> operationLogList,
                                   Long id, SgCChannelSourceStrategy sourceStrategy,
                                   SgCChannelSourceStrategy channelSourceStrategy) {
        if (StringUtils.isNotEmpty(sourceStrategy.getEname()) && !ObjectUtil.equals(sourceStrategy.getEname(), channelSourceStrategy.getEname())) {
            SgCOperationLog operationLog = logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY",
                    OperationTypeEnum.MOD.getOperationValue(), id, "寻源策略定义", "策略名称",
                    channelSourceStrategy.getEname(), sourceStrategy.getEname(), user);
            operationLogList.add(operationLog);
        }
        if (sourceStrategy.getType() != null && !ObjectUtil.equals(sourceStrategy.getType(), channelSourceStrategy.getType())) {
            SgCOperationLog operationLog = logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY",
                    OperationTypeEnum.MOD.getOperationValue(), id, "寻源策略定义", "类型",
                    SgCChannelSourceStrategyTypeEnum.getDescription(channelSourceStrategy.getType()),
                    SgCChannelSourceStrategyTypeEnum.getDescription(sourceStrategy.getType()), user);
            operationLogList.add(operationLog);
        }
        if (!ObjectUtil.equals(sourceStrategy.getCpCShopId(), channelSourceStrategy.getCpCShopId())) {
            List<Long> shopIds = new ArrayList<>();
            shopIds.add(sourceStrategy.getCpCShopId());
            shopIds.add(channelSourceStrategy.getCpCShopId());
            HashMap<Long, CpShop> shopInfoList = CommonCacheValUtils.getShopInfoList(shopIds);
            CpShop beforeShop = new CpShop();
            CpShop afterShop = new CpShop();
            if (MapUtils.isNotEmpty(shopInfoList)) {
                beforeShop = shopInfoList.get(channelSourceStrategy.getCpCShopId());
                afterShop = shopInfoList.get(sourceStrategy.getCpCShopId());
            }
            SgCOperationLog operationLog = logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY",
                    OperationTypeEnum.MOD.getOperationValue(), id, "寻源策略定义", "平台店铺",
                    beforeShop.getCpCShopTitle(), afterShop.getCpCShopTitle(), user);
            operationLogList.add(operationLog);
            //平台
//            if (!ObjectUtil.equals(beforeShop.getCpCPlatformId(), afterShop.getCpCPlatformId())) {
//                CpCPlatform beforePlatform = CommonCacheValUtils.selectByPlatformId(beforeShop.getCpCPlatformId());
//                CpCPlatform afterPlatform = CommonCacheValUtils.selectByPlatformId(afterShop.getCpCPlatformId());
//                SgCOperationLog operationLog1 = logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY",
//                        OperationTypeEnum.MOD.getOperationValue(), id, "寻源策略定义", "来源平台",
//                        beforePlatform.getEname(), afterPlatform.getEname(), user);
//                operationLogList.add(operationLog1);
//            }
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (!ObjectUtil.equals(sourceStrategy.getBeginTime(), channelSourceStrategy.getBeginTime())) {
            SgCOperationLog operationLog = logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY",
                    OperationTypeEnum.MOD.getOperationValue(), id, "寻源策略定义", "范围时间（开始时间）",
                    dateFormat.format(channelSourceStrategy.getBeginTime()), dateFormat.format(sourceStrategy.getBeginTime()), user);
            operationLogList.add(operationLog);
        }
        if (!ObjectUtil.equals(sourceStrategy.getEndTime(), channelSourceStrategy.getEndTime())) {
            SgCOperationLog operationLog = logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY",
                    OperationTypeEnum.MOD.getOperationValue(), id, "寻源策略定义", "范围时间（结束时间）",
                    dateFormat.format(channelSourceStrategy.getEndTime()), dateFormat.format(sourceStrategy.getEndTime()), user);
            operationLogList.add(operationLog);
        }
    }

    /**
     * 平台店铺和来源平台关系校验
     *
     * @param user           用户
     * @param platformId     来源平台id
     * @param cpShopId       平台店铺id
     * @param sourceStrategy 主表参数
     */
    private void checkShopAndPlatform(User user, Long platformId, Long cpShopId,
                                      SgCChannelSourceStrategy sourceStrategy) {
        sourceStrategy.setOwnerename(user.getEname());
        sourceStrategy.setModifierename(user.getEname());
        if (cpShopId != null) {
            CpShop shopInfo = CommonCacheValUtils.getShopInfo(cpShopId);
            if (shopInfo != null) {
                sourceStrategy.setCpCShopId(cpShopId);
                sourceStrategy.setCpCShopEcode(shopInfo.getEcode());
                sourceStrategy.setCpCShopTitle(shopInfo.getCpCShopTitle());
                sourceStrategy.setCpCPlatformId(shopInfo.getCpCPlatformId());
                sourceStrategy.setCpCPlatformEcode(shopInfo.getCpCPlatformEcode());
                sourceStrategy.setCpCPlatformEname(shopInfo.getCpCPlatformEname());
            } else {
                AssertUtils.logAndThrow("当前平台店铺已不存在！", user.getLocale());
            }
        }
    }

    /**
     * 寻源策略结案
     *
     * @param session session
     * @return ValueHolder
     */
    @SgOperationLog(operationType = "FINISH", mainTableName = "SG_C_CHANNEL_SOURCE_STRATEGY")
    public ValueHolder closeR3SourceStrategy(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        return R3ParamUtils.convertV14WithResult(closeR3SourceStrategy(request));
    }
    /**
     * 寻源策略结案逻辑
     *
     * @param request r3请求参数
     * @return ValueHolderV14<SgR3BaseResult>
     */
    private ValueHolderV14<SgR3BaseResult> closeR3SourceStrategy(SgR3BaseRequest request) {
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "结案成功！");
        if (log.isDebugEnabled()) {
            log.debug("Start SgCChannelSourceStrategyService.closeR3SourceStrategy:request{}",
                    JSONObject.toJSONString(request));
        }
        List<Long> closeIds = new ArrayList<>();
        //支持列表批量和单对象
        if (CollectionUtils.isNotEmpty(request.getIds())) {
            closeIds = request.getIds();
        }
        if (request.getObjId() != null) {
            closeIds.add(request.getObjId());
        }
        if (CollectionUtils.isNotEmpty(closeIds)) {
            JSONArray errData = new JSONArray();
            List<Long> updateIds = new ArrayList<>();
            //需要结案的数据
            List<SgCChannelSourceStrategy> sourceStrategyList = sgChannelSourceStrategyMapper.selectBatchIds(closeIds);
            if (CollectionUtils.isNotEmpty(sourceStrategyList)) {
                Map<Long, SgCChannelSourceStrategy> strategyMap =
                        sourceStrategyList.stream().collect(Collectors.toMap(SgCChannelSourceStrategy::getId,
                                Function.identity()));
                for (Long closeId : closeIds) {
                    //校验
                    SgCChannelSourceStrategy sourceStrategy = strategyMap.get(closeId);
                    if (sourceStrategy == null) {
                        StorageBasicUtils.errorRecord(closeId, "当前记录已不存在！", errData);
                        continue;
                    }
                    Integer status = sourceStrategy.getStatus();
                    if (!SgSourcingConstants.BILL_SOURCE_STRATEGY_SUBMIT.equals(status)) {
                        StorageBasicUtils.errorRecord(closeId, "当前记录不为审核，不允许结案！", errData);
                        continue;
                    }
                    updateIds.add(closeId);
                }
            } else {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("当前记录已不存在！"));
            }
            if (CollectionUtils.isNotEmpty(updateIds)) {
                User user = request.getLoginUser();
                SgCChannelSourceStrategy updateSourceStrategy = new SgCChannelSourceStrategy();
                updateSourceStrategy.setStatus(SgSourcingConstants.BILL_SOURCE_STRATEGY_CLOSED);
                StorageUtils.setBModelDefalutDataByUpdate(updateSourceStrategy, user);
                updateSourceStrategy.setModifierename(user.getEname());
                updateSourceStrategy.setClosedId(user.getId().longValue());
                updateSourceStrategy.setClosedEname(user.getEname());
                updateSourceStrategy.setClosedName(user.getName());
                updateSourceStrategy.setClosedTime(new Date());
                sgChannelSourceStrategyMapper.update(updateSourceStrategy,
                        new LambdaUpdateWrapper<SgCChannelSourceStrategy>().in(SgCChannelSourceStrategy::getId,
                                updateIds));

            }
            if (CollectionUtils.isNotEmpty(errData)) {
                v14.setCode(ResultCode.FAIL);
                if (closeIds.size() == 1) {
                    v14.setMessage(errData.getJSONObject(0).getString(R3ParamConstants.MESSAGE));
                } else {
                    v14.setMessage("结案成功:" + (closeIds.size() - errData.size() + ",结案失败:" + errData.size()));
                    SgR3BaseResult sgR3BaseResult = new SgR3BaseResult();
                    sgR3BaseResult.setDataArr(errData);
                    v14.setData(sgR3BaseResult);
                }
            }
        } else {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("请求参数不能为空！"));
        }
        return v14;
    }

    /**
     * 寻源策略表单对象查询
     *
     * @param request 请求参数
     * @return ValueHolderV14<SgCChannelSourceStrategyResult>
     */
    public ValueHolderV14<SgCChannelSourceStrategyResult> querySgChannelSourceStrategy(SgCChannelSourceStrategyBillQueryRequest request) {
        ValueHolderV14<SgCChannelSourceStrategyResult> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "查询成功!");
        if (request.getId() == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数不合法,查询失败!");
            return vh;
        }
        SgCChannelSourceStrategy sgChannelSourceStrategy = sgChannelSourceStrategyMapper.selectById(request.getId());
        if (sgChannelSourceStrategy != null) {
            //主表信息
            SgCChannelSourceStrategyResult sourceStrategyResult = new SgCChannelSourceStrategyResult();
            BeanUtils.copyProperties(sgChannelSourceStrategy, sourceStrategyResult);
            //评分策略明细
            SgCChannelSourceStrategyScoreItem scoreItem =
                    sgChannelSourceStrategyScoreItemMapper.selectOne(new LambdaQueryWrapper<SgCChannelSourceStrategyScoreItem>()
                            .eq(SgCChannelSourceStrategyScoreItem::getSgCChannelSourceStrategyId, request.getId()));
            if (scoreItem != null) {
                SgCChannelSourceStrategyResult.ScoreItemResult scoreItemResult =
                        new SgCChannelSourceStrategyResult.ScoreItemResult();
                BeanUtils.copyProperties(scoreItem, scoreItemResult);
                sourceStrategyResult.setScoreItemResult(scoreItemResult);
            }
            vh.setData(sourceStrategyResult);
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("当前记录已不存在!");
        }
        return vh;
    }

    /**
     * 逻辑仓修改聚合仓调用
     *
     * @param itemSaveRequest 请求参数
     * @returnValueHolderV14
     */
    public ValueHolderV14<List<Long>> insertSourceStrategyItem(SgCChannelSourceStrategyItemSaveRequest itemSaveRequest) {

        log.info("SgCChannelSourceStrategyService.insertSourceStrategyItem itemSaveRequest = {} ", JSONObject.toJSONString(itemSaveRequest));
        ValueHolderV14<List<Long>> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "新增成功！");

        try {
            //1。先查满足条件的寻缘策略
            List<SgCChannelSourceStrategy> sgChannelSourceStrategies = sgChannelSourceStrategyMapper.selectList(new LambdaQueryWrapper<SgCChannelSourceStrategy>()
                    .eq(SgCChannelSourceStrategy::getStatus, SgSourcingConstants.BILL_SOURCE_STRATEGY_SUBMIT)
                    .gt(SgCChannelSourceStrategy::getEndTime, new Date()));
            if (CollectionUtils.isNotEmpty(sgChannelSourceStrategies)) {

                List<Long> objIdList = sgChannelSourceStrategies.stream().map(SgCChannelSourceStrategy::getId).collect(Collectors.toList());
                //2。在查满足条件的策略
                List<SgCChannelSourceStrategyForceItem> itemList = sgChannelSourceStrategyForceItemMapper.selectList(new LambdaQueryWrapper<SgCChannelSourceStrategyForceItem>().
                        in(SgCChannelSourceStrategyForceItem::getSgCChannelSourceStrategyId, objIdList)
                        .eq(SgCChannelSourceStrategyForceItem::getCpCStoreId, itemSaveRequest.getCpCStoreId()));
                Map<Long, Long> itemMap = itemList.stream().collect(
                        Collectors.toMap(SgCChannelSourceStrategyForceItem::getSgCChannelSourceStrategyId,
                                SgCChannelSourceStrategyForceItem::getCpCStoreId, (v1, v2) -> v1));

                List<SgCChannelSourceStrategyForceItem> batchInsetList = new ArrayList<>();
                //返回新增明细id
                List<Long> itemIdList = new ArrayList<>();
                //3，未处理的添加明细
                for (Long objId : objIdList) {
                    if (!itemMap.containsKey(objId)) {
                        SgCChannelSourceStrategyForceItem insertItem = new SgCChannelSourceStrategyForceItem();
                        insertItem.setSgCChannelSourceStrategyId(objId);
                        //珂珂：写死
                        insertItem.setSgCChannelSourceForceStrategyId(1L);
                        insertItem.setSgCChannelSourceForceStrategyEcode("PC");
                        insertItem.setSgCChannelSourceForceStrategyEname("排除");
                        insertItem.setCpCStoreId(itemSaveRequest.getCpCStoreId());
                        insertItem.setCpCStoreEcode(itemSaveRequest.getCpCStoreEcode());
                        insertItem.setCpCStoreEname(itemSaveRequest.getCpCStoreEname());
                        insertItem.setSourceBillNo(itemSaveRequest.getSourceBillNo());
                        insertItem.setId(ModelUtil.getSequence(SgConstants.SG_C_CHANNEL_SOURCE_STRATEGY_FORCE_ITEM));
                        StorageUtils.setBModelDefalutData(insertItem, itemSaveRequest.getUser());
                        batchInsetList.add(insertItem);
                        itemIdList.add(insertItem.getId());
                    }
                }

                v14.setData(itemIdList);
                if (CollectionUtils.isNotEmpty(batchInsetList)) {
                    sgChannelSourceStrategyForceItemMapper.batchInsert(batchInsetList);
                } else {
                    v14.setCode(ResultCode.SUCCESS);
                    v14.setMessage("没有需要处理策略明细信息！");
                    return v14;
                }

            } else {
                v14.setCode(ResultCode.SUCCESS);
                v14.setMessage("没有需要处理策略信息！");
                return v14;
            }
        } catch (Exception e) {
            log.error("SgCChannelSourceStrategyService.insertSourceStrategyItem error = {} ", Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("新增失败：" + e.getMessage());
            return v14;
        }

        log.info("SgCChannelSourceStrategyService.insertSourceStrategyItem ValueHolderV14 = {} ", JSONObject.toJSONString(v14));

        return v14;
    }

    /**
     * 逻辑仓修改聚合仓调用
     * 根据id删除明细
     *
     * @param ids id
     * @return ValueHolderV14
     */
    public ValueHolderV14 deleteSourceStrategyItem(List<Long> ids) {

        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "逻辑仓修改聚合仓调用删除成功");

        if (CollectionUtils.isEmpty(ids)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("逻辑仓修改聚合仓调用删除失败！入参为空");
            return v14;
        }

        log.info("SgCChannelSourceStrategyService.deleteSourceStrategyItem ids.size={}", ids.size());

        int i = sgChannelSourceStrategyForceItemMapper.deleteBatchIds(ids);
        if (i != ids.size()) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("逻辑仓修改聚合仓调用删除失败！");
            return v14;
        }
        return v14;
    }
}

