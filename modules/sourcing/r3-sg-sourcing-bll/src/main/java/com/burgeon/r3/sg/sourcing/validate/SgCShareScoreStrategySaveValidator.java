package com.burgeon.r3.sg.sourcing.validate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareScoreStrategy;
import com.burgeon.r3.sg.core.model.table.basic.SgCShareScoreStrategyItem;
import com.burgeon.r3.sg.sourcing.mapper.SgCShareScoreStrategyItemMapper;
import com.burgeon.r3.sg.sourcing.mapper.SgCShareScoreStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.dto.strategy.SgCShareScoreStrategyDto;
import com.burgeon.r3.sg.sourcing.model.dto.strategy.SgCShareScoreStrategyItemDto;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleItemValidator;
import com.jackrain.nea.web.face.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/6/9 13:54
 */
@Component
public class SgCShareScoreStrategySaveValidator extends BaseSingleItemValidator<SgCShareScoreStrategyDto, SgCShareScoreStrategyItemDto> {

    @Autowired
    private SgCShareScoreStrategyMapper mapper;

    @Autowired
    private SgCShareScoreStrategyItemMapper itemMapper;

    @Override
    public String getValidatorMsgName() {
        return "评分策略表保存";
    }

    @Override
    public Class<?> getValidatorClass() {
        return this.getClass();
    }

    /**
     * 校验头表
     */
    @Override
    public ValueHolderV14 validateMainTable(SgCShareScoreStrategyDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "评分策略保存成功");
        Long id = mainObject.getId();
        if (id < 0L) {
            String ename = mainObject.getEname();
            if (StringUtils.isEmpty(ename)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("评分策略名称不能为空", loginUser.getLocale()));
                return v14;
            }
            Integer count = mapper.selectCount(new LambdaQueryWrapper<SgCShareScoreStrategy>()
                    .eq(SgCShareScoreStrategy::getEname, ename)
                    .eq(SgCShareScoreStrategy::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (count > 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("策略名称已存在，不允许保存", loginUser.getLocale()));
                return v14;
            }
        }
        return v14;
    }

    /**
     * 校验明细
     */
    @Override
    public ValueHolderV14 validateSubTable(SgCShareScoreStrategyDto mainObject, List<SgCShareScoreStrategyItemDto> subObjectList, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "评分策略保存成功");
        Long id = mainObject.getId();
        BigDecimal sumWeight = BigDecimal.ZERO;
        BigDecimal decimal = new BigDecimal(100);
        Map<Long, BigDecimal> decimalMap = new HashMap<>();
        if (id > 0) {
            List<SgCShareScoreStrategyItem> strategyItems = itemMapper.selectList(new LambdaQueryWrapper<SgCShareScoreStrategyItem>()
                    .select(SgCShareScoreStrategyItem::getId, SgCShareScoreStrategyItem::getWeight)
                    .eq(SgCShareScoreStrategyItem::getSgCShareScoreStrategyId, id)
                    .eq(SgCShareScoreStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y));
            sumWeight = strategyItems.stream().map(o -> o.getWeight()).reduce(BigDecimal.ZERO, BigDecimal::add);
            decimalMap = strategyItems.stream().collect(Collectors.toMap(o -> o.getId(), o -> o.getWeight()));
        }
        for (SgCShareScoreStrategyItemDto strategyItem : subObjectList) {
            Long itemId = strategyItem.getId();
            BigDecimal weight = strategyItem.getWeight();

            if (weight != null) {
                if (weight.compareTo(BigDecimal.ZERO) < 0) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(Resources.getMessage("权重为负数，不允许保存", loginUser.getLocale()));
                    return v14;
                }
                if (weight.compareTo(decimal) > 0) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(Resources.getMessage("权重不能大于100，不允许保存", loginUser.getLocale()));
                    return v14;
                }
            }
            if (itemId > 0L) {
                BigDecimal oldWeight = decimalMap.get(itemId);
                weight = oldWeight.subtract(weight).negate();
            }
            sumWeight = sumWeight.add(weight);
        }
        if (sumWeight.compareTo(decimal) > 0) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(Resources.getMessage("所有明细权重总数不能大于100，不允许保存", loginUser.getLocale()));
            return v14;
        }
        return v14;
    }
}
