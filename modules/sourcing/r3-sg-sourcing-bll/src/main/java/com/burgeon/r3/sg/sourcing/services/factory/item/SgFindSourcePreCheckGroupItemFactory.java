package com.burgeon.r3.sg.sourcing.services.factory.item;


import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.StrategyFactoryBaseResult;
import com.burgeon.r3.sg.sourcing.model.result.factory.SgFindSourceStrategyFactoryResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.services.factory.StrategyFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 寻源预检策略组工厂类
 * @author: chenb
 * @time: 2022/8/1 16:10
 */

@Component
@Slf4j
@Data
public class SgFindSourcePreCheckGroupItemFactory extends StrategyFactory {

    private List<? extends StrategyFactoryBaseResult> strategiesList;

    private StrategyHandle handle;

    @Override
    public List<SgFindSourceStrategyFactoryResult> getStrategies(StrategyBaseRequest request) {

        ArrayList<SgFindSourceStrategyFactoryResult> results = new ArrayList<>();
        SgFindSourceStrategyFactoryResult result = new SgFindSourceStrategyFactoryResult();
        result.setPriority(10);
        result.setStrategyClazz("com.burgeon.r3.sg.sourcing.services.item.SgFindSourceSplitStrategyPreCheckService");
        result.setStrategyName("拆单规则执行器");
        results.add(result);

        result = new SgFindSourceStrategyFactoryResult();
        result.setPriority(20);
        result.setStrategyClazz("com.burgeon.r3.sg.sourcing.services.item.SgFindSourceLogisticsFilterStrategyService");
        result.setStrategyName("仓库物流策略执行器");
        results.add(result);

        return results;
    }
}