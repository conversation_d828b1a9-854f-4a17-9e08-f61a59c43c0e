package com.burgeon.r3.sg.sourcing.services.item;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategyForceItem;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyForceItemMapper;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.*;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 逻辑仓过滤策略
 * @author: hwy
 * @time: 2021/6/7 17:02
 */
@Component
@Slf4j
public class SgFindSourceFilterStrategyService extends StrategyHandle {


    /**
     * @param request:
     * @Description: 排除仓策略
     * @Author: hwy
     * @Date: 2021/6/8 20:22
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<com.burgeon.r3.sg.basic.model.result.strategy.StrategyBaseResult>
     **/
    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {

        log.info(LogUtil.format("SgFindSourceFilterStrategyService.handleRequest S->L二阶段寻源派单 强制过滤逻辑仓策略 param:{}",
                SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L),
                request.getTraceId(), JSONObject.toJSONString(request));

        SgFindSourceStrategyS2LRequest strategyRequest = (SgFindSourceStrategyS2LRequest) request;
        ValueHolderV14<StrategyBaseResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS,
                SgConstants.MESSAGE_STATUS_SUCCESS);
        valueHolderV14.setData(strategyRequest.getStrategyBaseResult());

        // 20220928 丹哥说：指定仓的情况下，不需要执行排除仓策略
        if (StringUtils.isNotEmpty(strategyRequest.getWarehouseEcode())) {
            log.info("指定实体仓，跳过排除仓策略");
            return doNext(request, valueHolderV14);
        }

//        Long shopId = strategyRequest.getShopId();

        try {

            //查询策略内容
            SgCChannelSourceStrategyForceItemMapper forceItemMapper =
                    ApplicationContextHandle.getBean(SgCChannelSourceStrategyForceItemMapper.class);

            LambdaQueryWrapper<SgCChannelSourceStrategyForceItem> itemWrapper = new LambdaQueryWrapper<>();
            itemWrapper.eq(SgCChannelSourceStrategyForceItem::getSgCChannelSourceStrategyId, strategyRequest.getSourceStrategy().getId());
            itemWrapper.eq(SgCChannelSourceStrategyForceItem::getIsactive, SgConstants.IS_ACTIVE_Y);
            List<SgCChannelSourceStrategyForceItem> forceItems = forceItemMapper.selectList(itemWrapper);

//            List<SgCChannelSourceStrategyForceItem> forceItems = forceItemMapper.selectForceItemByShop(shopId, new Date());

            if (CollectionUtils.isEmpty(forceItems)) {
                FindSourceStrategyUtils.outputLog(
                        LogUtil.format("SgFindSourceFilterStrategyService.handleRequest S->L二阶段寻源派单 强制过滤逻辑仓策略未找到有效的寻源规则，当前策略执行完毕！",
                                SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L),
                        request.getTraceId());
                return doNext(request, valueHolderV14);
            }

            Map<Long, String> filterStoreIds = forceItems.stream().collect(
                    Collectors.toMap(k -> k.getCpCStoreId(), k -> String.valueOf(k.getCpCStoreEname()), (oldVal, newVal) -> oldVal));

            //将结果集中的逻辑仓过滤 更新结果集
            boolean isAllItemhasNoStore = updateResult(strategyRequest, filterStoreIds);

            //判断订单明细中的实体仓是否全部被[强制寻源规则]过滤后造成实体仓为空
            if (isAllItemhasNoStore) {
                log.info(LogUtil.format("SgFindSourceFilterStrategyService.handleRequest S->L二阶段寻源派单 逻辑仓:{}在寻源策略中被排除，寻源失败!",
                        SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L),
                        request.getTraceId(), filterStoreIds.values());
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("逻辑仓:" + filterStoreIds.values() + "在寻源策略中被排除，寻源失败!");
            }

        } catch (Exception e) {
            log.error(LogUtil.format("SgFindSourceFilterStrategyService.handleRequest S->L二阶段寻源派单 强制寻源规则发生异常 exception:{}",
                    SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L_EXCEPTION),
                    request.getTraceId(), Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("S->L 二阶段寻源 强制寻源规则发生异常!");
        }

        //设置传递到下一个策略的结果集
        return doNext(request, valueHolderV14);

    }

    /**
     * @param strategyRequest:
     * @param filterStoreIds:
     * @Description: 将结果集中的逻辑仓过滤 更新结果集
     * @Author: hwy
     * @Date: 2021/7/10 16:47
     * @return: boolean
     **/
    private boolean updateResult(SgFindSourceStrategyS2LRequest strategyRequest, Map<Long, String> filterStoreIds) {

        FindSourceStrategyUtils.outputLog(
                LogUtil.format("SgFindSourceFilterStrategyService.updateResult S->L二阶段寻源派单 强制过滤逻辑仓策略需要过滤的逻辑仓:{}",
                        SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L),
                strategyRequest.getTraceId(), filterStoreIds);

        //遍历原有结果集
        SgFindSourceStrategyS2LResult strategyResult = (SgFindSourceStrategyS2LResult) strategyRequest.getStrategyBaseResult();
        List<SgFindSourceStrategySkuS2LResult> skuResultList = strategyResult.getSkuResultList();
        List<SgFindSourceStrategyStoreItemS2LResult> itemResultList = null;
        List<SgFindSourceStrategyStoreItemS2LResult> newItemResultList = null;
        SgFindSourceStrategyStoreItemS2LResult newItemResult = null;
        Map<Long, SortedMap<String, BigDecimal>> logicStorageMap = null;
        Map<String, BigDecimal> productDateQtyMap = null;
        BigDecimal storageQty = null;
        BigDecimal qty = null;
        //当前订单没有满足条件的逻辑仓
        boolean isAllItemhasNoStore = Boolean.TRUE;
        //当前明细的没有满足条件的逻辑仓
        boolean isItemhasNoStore = Boolean.TRUE;
        //强制排除的逻辑仓信息
        Map<Long, String> filterStores = new HashMap<>();

        for (SgFindSourceStrategySkuS2LResult skuS2LResult : skuResultList) {

            itemResultList = skuS2LResult.getItemResultList();
            newItemResultList = new ArrayList<>(itemResultList.size());
            isItemhasNoStore = Boolean.TRUE;

            for (SgFindSourceStrategyStoreItemS2LResult itemResult : itemResultList) {

                logicStorageMap = itemResult.getLogicStorageMap();
                qty = itemResult.getQty();

                if (MapUtils.isEmpty(logicStorageMap)) {
                    continue;
                }

                // 过滤逻辑仓 减少可用库存
                for (Long filterStoreId : filterStoreIds.keySet()) {
                    if (logicStorageMap.containsKey(filterStoreId)) {

                        productDateQtyMap = logicStorageMap.get(filterStoreId);
                        storageQty = BigDecimal.ZERO;

                        //根据逻辑仓ID，进行跨生产日期可用数量聚合
                        if (!MapUtils.isEmpty(productDateQtyMap)) {
                            for (BigDecimal value : productDateQtyMap.values()) {
                                storageQty = storageQty.add(value == null ? BigDecimal.ZERO : value);
                            }
                        }

                        qty = qty.subtract(storageQty);
                        logicStorageMap.remove(filterStoreId);

                        //设置强制排除的逻辑仓信息
                        filterStores.put(filterStoreId, filterStoreIds.get(filterStoreId));
                    }
                }

                // 移除实体仓剩余可用为0的记录
                if (BigDecimal.ZERO.compareTo(qty) >= 0) {
                    newItemResult = new SgFindSourceStrategyStoreItemS2LResult();
                    newItemResult.setQty(qty);
                    newItemResult.setStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                    newItemResult.setLogicStorageMap(new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY));
                    newItemResultList.add(newItemResult);
                    continue;
                }

                // 当一个实体仓剩余可用>0
                isAllItemhasNoStore = Boolean.FALSE;
                isItemhasNoStore = Boolean.FALSE;
                itemResult.setQty(qty);
                newItemResultList.add(itemResult);
            }

            //当前明细没有满足条件的逻辑仓，记录缺货原因
            if (isItemhasNoStore) {
                SgFindSourceStrategyOutOfStockBaseResult outOfSStockResult = new SgFindSourceStrategyOutOfStockBaseResult();
                outOfSStockResult.setSourceItemId(skuS2LResult.getSourceItemId());
                outOfSStockResult.setShareItemId(skuS2LResult.getShareItemId());
                outOfSStockResult.setPsCSkuId(skuS2LResult.getPsCSkuId());
                outOfSStockResult.setOutOfStockMessage("逻辑仓:" + filterStoreIds.values() + "在寻源策略中被排除，寻源失败!");
                strategyRequest.getStrategyBaseResult().getOutOfStockMessages().add(outOfSStockResult);
            }

            skuS2LResult.setItemResultList(newItemResultList);
        }

        //设置强制排除的逻辑仓信息
        if (!MapUtils.isEmpty(filterStores)) {
            Map removedStoreInfo = strategyResult.getRemovedStoreInfo();
            removedStoreInfo.put(SgSourcingConstants.MSG_REMOVED_STORE_INFO_STORE_FILTER, filterStores.values().toString());
        }

        return isAllItemhasNoStore;
    }


}