package com.burgeon.r3.sg.sourcing.mapper.syncgradientstrategy;

import com.burgeon.r3.sg.core.model.table.sourcing.syncgradientstrategy.SgCSyncGradientStrategy;
import com.burgeon.r3.sg.sourcing.model.result.syncgradientstrategy.SgCSyncGradientStrategyByQtyQueryResult;
import com.burgeon.r3.sg.sourcing.model.result.syncgradientstrategy.SgCSyncGradientStrategyListQueryResult;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface SgCSyncGradientStrategyMapper extends ExtentionMapper<SgCSyncGradientStrategy> {

    @Select({
            "<script>",
            "SELECT m_c.sg_c_sa_store_id, m_c.sg_c_share_pool_id, item.cp_c_shop_id, item.ratio ",
            "FROM ",
            "(SELECT m.id as m_id, m.sg_c_sa_store_id, m.sg_c_share_pool_id, c.qty_begin, c.id as c_id ",
            " FROM sg_c_sync_gradient_strategy m ",
            " INNER JOIN sg_c_sync_gradient_strategy_cond c ON m.id = c.sg_c_sync_gradient_strategy_id ",
            " WHERE ",
            " <if test='type != null and type == \"1\"'>",
            "m.sg_c_sa_store_id = #{storeId} ",
            "</if>",
            " <if test='type != null and type == \"2\"'>",
            "m.sg_c_share_pool_id = #{storeId} ",
            "</if>",
            " AND c.qty_begin >= #{targetQty} and m.isactive = 'Y' and c.isactive = 'Y' ) m_c ",
            " INNER JOIN sg_c_sync_gradient_strategy_item item ",
            "ON m_c.c_id = item.sg_c_sync_gradient_strategy_cond_id AND m_c.m_id = item.sg_c_sync_gradient_strategy_id ",
            "WHERE item.cp_c_shop_id = #{cpCShopId}",
            " and item.isactive = 'Y' ",
            " ORDER BY m_c.qty_begin LIMIT 1 ",
            "</script>"
    })
    SgCSyncGradientStrategyByQtyQueryResult queryGradientStrategyByQty(@Param("cpCShopId") Long cpCShopId,
                                                                       @Param("storeId") Long storeId,
                                                                       @Param("type") String type,
                                                                       @Param("targetQty") BigDecimal targetQty);

    /**
     * 配销仓/共享池库存梯度策略定制界面 列表查询
     *
     * @param sharePoolIds           共享池id
     * @param saStoreIds             配销仓id
     * @param type                   类型
     * @param permissionSharePoolIds 共享池权限id
     * @param permissionSaStoreIds   配销仓权限id
     * @param shopIds                店铺id
     * @param isactives              可用条件
     * @return List<SgCSyncGradientStrategyListQueryResult>
     */
    @Select("<script>" +
            "SELECT " +
            " m.id, " +
            " m.sg_c_sa_store_ename, " +
            " m.sg_c_share_pool_ename, " +
            " m.ownerename, " +
            " m.isactive, " +
            " date_format(m.creationdate,'%Y-%m-%d %H:%i:%s') creationdate, " +
            " m.modifierename, " +
            " date_format(m.modifieddate,'%Y-%m-%d %H:%i:%s') modifieddate, " +
            " c.condtion, " +
            " c.qty_begin, " +
            " s.cp_c_shop_title, " +
            " s.ratio " +
            "FROM " +
            " `sg_c_sync_gradient_strategy` m " +
            "LEFT JOIN sg_c_sync_gradient_strategy_cond c ON m.id = c.sg_c_sync_gradient_strategy_id " +
            "LEFT JOIN sg_c_sync_gradient_strategy_item s ON c.id = s.sg_c_sync_gradient_strategy_cond_id " +
            "WHERE " +
            " m.type = #{type} " +
            " <if test='sharePoolIds != null and !sharePoolIds.isEmpty()'>" +
            " and m.sg_c_share_pool_id in " +
            " <foreach collection='sharePoolIds' item='sharePoolId' open='(' separator=',' close=')'> " +
            " #{sharePoolId} </foreach>" +
            " </if>" +
            " <if test='permissionSharePoolIds != null and !permissionSharePoolIds.isEmpty()'>" +
            " and m.sg_c_share_pool_id in " +
            " <foreach collection='permissionSharePoolIds' item='permissionId' open='(' separator=',' close=')'> " +
            " #{permissionId} </foreach>" +
            " </if>" +
            " <if test='saStoreIds != null and !saStoreIds.isEmpty()'>" +
            " and m.sg_c_sa_store_id in " +
            " <foreach collection='saStoreIds' item='saStoreId' open='(' separator=',' close=')'> " +
            " #{saStoreId} </foreach>" +
            " </if> " +
            " <if test='permissionSaStoreIds != null and !permissionSaStoreIds.isEmpty()'>" +
            " and m.sg_c_sa_store_id in " +
            " <foreach collection='permissionSaStoreIds' item='permissionId' open='(' separator=',' close=')'> " +
            " #{permissionId} </foreach>" +
            " </if> " +
            " <if test='shopIds != null and !shopIds.isEmpty()'>" +
            " and s.cp_c_shop_id in " +
            " <foreach collection='shopIds' item='shopId' open='(' separator=',' close=')'> " +
            " #{shopId} </foreach>" +
            " </if>" +
            " <if test='isactives != null and !isactives.isEmpty()'>" +
            " and m.isactive in " +
            " <foreach collection='isactives' item='isactive' open='(' separator=',' close=')'> " +
            " #{isactive} </foreach>" +
            " </if>" +
            "ORDER BY m.id " +
            "</script>")
    List<SgCSyncGradientStrategyListQueryResult> queryShareStoreSyncGradientStrategyForList(@Param("sharePoolIds") List<Long> sharePoolIds,
                                                                                            @Param("saStoreIds") List<Long> saStoreIds,
                                                                                            @Param("permissionSharePoolIds") List<Long> permissionSharePoolIds,
                                                                                            @Param("permissionSaStoreIds") List<Long> permissionSaStoreIds,
                                                                                            @Param("type") String type,
                                                                                            @Param("shopIds") List<Long> shopIds,
                                                                                            @Param("isactives") List<String> isactives);
}