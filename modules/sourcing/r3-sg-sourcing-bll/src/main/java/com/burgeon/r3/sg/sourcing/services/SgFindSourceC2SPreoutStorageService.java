package com.burgeon.r3.sg.sourcing.services;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutBillSaveRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutItemLogSaveRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutItemSaveRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutSaveRequest;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutSaveResult;
import com.burgeon.r3.sg.share.services.out.SgBShareOutSaveService;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyC2SRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgOccupyPlanPriorityRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgOccupyPlanServiceRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgOccupyPlanSkuItemRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemC2S;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyC2SResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuC2SResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemC2SResult;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanItemResult;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanOutItemResult;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanServiceResult;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 生成共享占用单占用库存
 * @author: hwy
 * @time: 2021/6/21 20:26
 */

@Component
@Slf4j
public class SgFindSourceC2SPreoutStorageService {

    @Autowired
    private SgFindSourceOccupyPlanService occupyPlanService;

    @Autowired
    private SgFindSourceRollBackService sgFindSourceRollBackService;

    /**
     * @param request:
     * @Description: 生成共享占用单占用库存
     * @Author: hwy
     * @Date: 2021/6/30 14:04
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyC2SResult>
     **/
    public ValueHolderV14<SgFindSourceStrategyC2SResult> occupyStorage(StrategyBaseRequest request) {
        FindSourceStrategyUtils.outputLog("SgFindSourceC2SPreoutStorageService.occupyStorage param:{}", JSONObject.toJSONString(request));
        ValueHolderV14<SgFindSourceStrategyC2SResult> valueHolderV14 = new ValueHolderV14<>(StrategyConstants.RESULT_CODE_PREOUT, SgConstants.MESSAGE_STATUS_SUCCESS);
        //入参转换
        SgFindSourceStrategyC2SRequest strategyC2SRequest = (SgFindSourceStrategyC2SRequest) request;
        //策略执行结果
        SgFindSourceStrategyC2SResult strategyResult = (SgFindSourceStrategyC2SResult) strategyC2SRequest.getStrategyBaseResult();

        if (StringUtils.isNotEmpty(strategyC2SRequest.getQuerySaStoreErrorMsg())) {
            FindSourceStrategyUtils.outputLog(strategyC2SRequest.getQuerySaStoreErrorMsg());
            allOut(strategyC2SRequest);
            valueHolderV14.setCode(StrategyConstants.RESULT_CODE_EXCEPTION);
            valueHolderV14.setMessage(strategyC2SRequest.getQuerySaStoreErrorMsg());
            return valueHolderV14;
        }

        if (strategyResult == null) {
            FindSourceStrategyUtils.outputLog("C->S寻源派单 派单服务 来源单据:{} 寻源策略执行结果为空 没有可以发货的仓库 订单全部缺货", strategyC2SRequest.getSourceBillId());
            allOut(strategyC2SRequest);
            valueHolderV14.setCode(StrategyConstants.RESULT_CODE_EXCEPTION);
            valueHolderV14.setMessage("策略执行结果为空 无法满足订单需求 全部缺货");
            return valueHolderV14;
        }

        //策略执行结果明细
        List<SgFindSourceStrategySkuC2SResult> skuStrategyInfoList = strategyResult.getSkuResultList();

        if (CollectionUtils.isEmpty(skuStrategyInfoList)) {
            FindSourceStrategyUtils.outputLog("C->S寻源派单 派单服务 来源单据:{} 寻源策略执行结果为空 没有可以发货的仓库 订单全部缺货", strategyC2SRequest.getSourceBillId());
            allOut(strategyC2SRequest);
            valueHolderV14.setCode(StrategyConstants.RESULT_CODE_EXCEPTION);
            valueHolderV14.setMessage("策略执行结果为空 无法满足订单需求 全部缺货");
            return valueHolderV14;
        }

        log.info("SgFindSourceC2SPreoutStorageService.occupyStorage skuStrategyInfoList:{}", JSONObject.toJSONString(skuStrategyInfoList));

        // 占用计划结果 key:sourceItemId value：<saStoreId, OccupyPlan>
        Map<Long, Map<Long, SgFindSourceStrategyStoreItemC2SResult>> occupyPlanResult = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
        try {
            //生成库存占用计划
            getOccupyPlan(strategyC2SRequest, occupyPlanResult, skuStrategyInfoList);
            log.debug(LogUtil.format("SgFindSourceC2SPreoutStorageService.occupyStorage 生成占用计划结果:{}",
                    "occupyStorage.getOccupyPlan"), JSONObject.toJSONString(occupyPlanResult));
            FindSourceStrategyUtils.outputLog("SgFindSourceC2SPreoutStorageService.occupyStorage 生成占用计划结果:{}", JSONObject.toJSONString(occupyPlanResult));

            //生成共享占用单
            addShareOutBill(strategyC2SRequest, occupyPlanResult, valueHolderV14);

            //设置结果集
            valueHolderV14.setData((SgFindSourceStrategyC2SResult) strategyC2SRequest.getStrategyBaseResult());
        } catch (Exception e) {
            log.error("C->S拆单策略执行异常:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(StrategyConstants.RESULT_CODE_EXCEPTION);
            valueHolderV14.setMessage("C->S拆单策略执行异常");
            return valueHolderV14;
        }
        return valueHolderV14;
    }

    /**
     * @param strategyC2SRequest:  初始参数
     * @param occupyPlanResult:    占用计划结果
     * @param skuStrategyInfoList: 策略信息
     * @Description: 获取库存占用计划
     * @Author: hwy
     * @Date: 2021/7/1 17:53
     * @return: void
     **/
    private void getOccupyPlan(SgFindSourceStrategyC2SRequest strategyC2SRequest, Map<Long, Map<Long, SgFindSourceStrategyStoreItemC2SResult>> occupyPlanResult, List<SgFindSourceStrategySkuC2SResult> skuStrategyInfoList) {
        //订单明细 key: sourceBillId value: <storeId,itemPlan>
        List<SkuItemC2S> skuItems = strategyC2SRequest.getSkuItems();

        //深复制对象 用于保存冲减时的发货数量   防止Long类型自动降级为Integer
        String copyStr = JSONObject.toJSONString(skuItems, SerializerFeature.WriteClassName);
        List<SkuItemC2S> undistributedItem = JSONObject.parseObject(copyStr, new TypeReference<List<SkuItemC2S>>() {});
        //对按数量供货的条码 生成库存占用计划
        if (log.isDebugEnabled()) {
            log.debug("C->S 库存占用服务 使用锁定库存 生成占用计划 当前剩余未分配明细数量:{}", undistributedItem.size());
        } else {
            log.info("C->S 库存占用服务 使用锁定库存 生成占用计划 当前剩余未分配明细数量:{}", undistributedItem.size());
        }
        occupyPlan(strategyC2SRequest, undistributedItem, skuStrategyInfoList, occupyPlanResult);
        if (log.isDebugEnabled()) {
            log.debug("C->S 库存占用服务 使用锁定库存 生成占用计划完成 当前剩余未分配明细数量:{}", undistributedItem.size());
            log.debug("C->S 库存占用服务 使用锁定库存 生成占用计划完成 当前分配结果:{}", JSONObject.toJSONString(occupyPlanResult));
        } else {
            log.info("C->S 库存占用服务 使用锁定库存 生成占用计划完成 当前剩余未分配明细数量:{}", undistributedItem.size());
            log.info("C->S 库存占用服务 使用锁定库存 生成占用计划完成 当前分配结果:{}", JSONObject.toJSONString(occupyPlanResult));
        }

        //还有剩余为分配完的明细 添加缺货 TODO 前面已经对未分配完的明细添加了缺货，为啥还要再添加一次缺货，防止有漏？？？
//        if (CollectionUtils.isNotEmpty(undistributedItem)) {
//            for (SkuItemC2S skuItem : undistributedItem) {
//                BigDecimal qty = skuItem.getQty();
//                Long sourceItemId = skuItem.getSourceItemId();
//                SgFindSourceStrategyStoreItemC2SResult itemC2SResult = new SgFindSourceStrategyStoreItemC2SResult();
//                Map<Long, SgFindSourceStrategyStoreItemC2SResult> itemC2SResultMap = occupyPlanResult.get(sourceItemId);
//                if (MapUtils.isEmpty(itemC2SResultMap)) {
//                    itemC2SResultMap = new HashMap<>();
//                    occupyPlanResult.put(sourceItemId, itemC2SResultMap);
//                }
//                itemC2SResult.setQty(qty);
//                itemC2SResult.setSgCSharePoolId(StrategyConstants.OUT_DEFAULT_STORE_ID);
//                itemC2SResult.setSgCSaStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
//                itemC2SResult.setSgCShareStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
//                itemC2SResult.setOutStockFlag(Boolean.TRUE);
//                itemC2SResultMap.put(StrategyConstants.OUT_DEFAULT_STORE_ID, itemC2SResult);
//            }
//        }
    }

    /**
     * @param strategyC2SRequest: 初始参数
     * @param occupyPlanResult:   key:sourceBillId value:(storeId,occupyPlan)
     * @Description: 新增共享占用单
     * @Author: hwy
     * @Date: 2021/7/1 17:43
     * @return: void
     **/
    private void addShareOutBill(SgFindSourceStrategyC2SRequest strategyC2SRequest,
                                 Map<Long, Map<Long, SgFindSourceStrategyStoreItemC2SResult>> occupyPlanResult,
                                 ValueHolderV14<SgFindSourceStrategyC2SResult> valueHolderV14) {
        if (MapUtils.isEmpty(occupyPlanResult)) {
            if (log.isDebugEnabled()) {
                log.debug("C->S第一阶段寻源派单 占用计划为空  无法生成共享占用单 订单缺货");
            }
            valueHolderV14.setCode(StrategyConstants.RESULT_CODE_NO_PREOUT);
            valueHolderV14.setMessage("C->S第一阶段寻源派单 占用计划为空  无法生成共享占用单 订单缺货");
            return;
        }
        //缺货明细 用于拆单判断
        List<Long> outStockItemIds = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        //创建共享占用单请求参数
        log.debug(LogUtil.format("SgFindSourceC2SPreoutStorageService.occupyStorage strategyC2SRequest:{};occupyPlanResult:{}",
                "occupyStorage.buildStorageChangeRequest"), JSONObject.toJSONString(strategyC2SRequest), JSONObject.toJSONString(occupyPlanResult));
        List<SgBShareOutBillSaveRequest> sgBShareOutBillSaveRequests = buildStorageChangeRequest(strategyC2SRequest, occupyPlanResult, outStockItemIds);
        if (CollectionUtils.isEmpty(sgBShareOutBillSaveRequests)) {
            if (log.isDebugEnabled()) {
                log.debug("C->S第一阶段寻源派单 新增共享占用单服务 共享占用单请求参数创建结果为空 全部缺货");
            } else {
                log.info("C->S第一阶段寻源派单 新增共享占用单服务 共享占用单请求参数创建结果为空 全部缺货");
            }
            FindSourceStrategyUtils.allOutC2S(strategyC2SRequest);
            valueHolderV14.setCode(StrategyConstants.RESULT_CODE_NO_PREOUT);
            valueHolderV14.setMessage("C->S第一阶段寻源派单 新增配销占用单服务 配销占用单请求参数创建结果为空 全部缺货；订单内所有商品在【比例同步策略】的配销仓中库存不足。");
            return;
        }
        FindSourceStrategyUtils.outputLog("C->S第一阶段寻源派单 新增共享占用单服务 新增单据数量:{}", sgBShareOutBillSaveRequests.size());
        //拆单类型
        String splitType = strategyC2SRequest.getSplitType();
//        不拆单 存在明细缺货  不做库存占用
        if (StrategyConstants.ORDER_SPLIT_TYPE_NO.equals(splitType) && CollectionUtils.isNotEmpty(outStockItemIds)) {
            List<String> errorPsCSkuEcode = strategyC2SRequest.getSkuItems().stream().filter(x -> outStockItemIds.contains(x.getSourceItemId())).map(SkuItemC2S::getPsCSkuEcode).collect(Collectors.toList());

            FindSourceStrategyUtils.outputLog("C->S第一阶段寻源派单 拆单策略 订单为整单不拆 存在缺货明细 订单全部缺货");
            FindSourceStrategyUtils.allOutC2S(strategyC2SRequest);
            valueHolderV14.setCode(StrategyConstants.RESULT_CODE_NO_PREOUT);
            valueHolderV14.setMessage("C->S第一阶段寻源派单 拆单策略 订单为整单不拆 存在缺货明细 订单全部缺货 缺货明细条码：" + JSONObject.toJSONString(errorPsCSkuEcode) + "相关商品在【比例同步策略】的配销仓中库存不足。");
            return;
        }
//        按明细拆  所有明细都缺货 不做库存占用
        if (StrategyConstants.ORDER_SPLIT_TYPE_ITEM.equals(splitType)) {
            List<SkuItemC2S> skuItems = strategyC2SRequest.getSkuItems();
//            数量相等表示每条明细都存在缺货
            if (skuItems.size() == outStockItemIds.size()) {
                List<String> errorPsCSkuEcode = strategyC2SRequest.getSkuItems().stream().map(SkuItemC2S::getPsCSkuEcode).collect(Collectors.toList());

                FindSourceStrategyUtils.outputLog("C->S第一阶段寻源派单 拆单策略 订单为按明细拆 所有明细都存在缺货 订单全部缺货");
                FindSourceStrategyUtils.allOutC2S(strategyC2SRequest);
                valueHolderV14.setCode(StrategyConstants.RESULT_CODE_NO_PREOUT);
                valueHolderV14.setMessage("C->S第一阶段寻源派单 拆单策略 订单为整单不拆 存在缺货明细 缺货明细条码：" + JSONObject.toJSONString(errorPsCSkuEcode) + "相关商品在【比例同步策略】的配销仓中库存不足。");
                return;
            }
        }

        //如果为虚拟寻源，不生成共享占用直接返回
        if (Boolean.TRUE.equals(strategyC2SRequest.getInventedOccupy())) {
            setResult(strategyC2SRequest, sgBShareOutBillSaveRequests);
            return;
        }

        //成功的明细
        List<SgBShareOutBillSaveRequest> successRequests = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        SgBShareOutSaveService shareOutSaveService = ApplicationContextHandle.getBean(SgBShareOutSaveService.class);
        Boolean preOutFlag = Boolean.FALSE;
        //生成共享占用单
        try {
            for (SgBShareOutBillSaveRequest saveRequest : sgBShareOutBillSaveRequests) {
                ValueHolderV14<SgBShareOutSaveResult> holderV14 = shareOutSaveService.redundantSgShareOut(saveRequest);
                if (!holderV14.isOK()) {
                    log.warn("C->S第一阶段寻源派单 拆单策略 新增共享占用单服务 生成共享占用单失败:{}", holderV14.getMessage());
                    valueHolderV14.setCode(StrategyConstants.RESULT_CODE_EXCEPTION);
                    valueHolderV14.setMessage("C->S第一阶段寻源派单 拆单策略 新增共享占用单服务 生成共享占用单失败");
                    if (preOutFlag) {
                        FindSourceStrategyUtils.outputLog("C->S拆单策略 新增共享占用单服务 生成共享占用单失败 开始回滚共享占用单");
                        sgFindSourceRollBackService.shareOutRollBackStorage(strategyC2SRequest.getSourceBillNo(), strategyC2SRequest.getSourceBillId(), strategyC2SRequest.getSourceBillType());
                    }
                    successRequests.clear();
                    break;
                }
                preOutFlag = Boolean.TRUE;
                successRequests.add(saveRequest);
            }
        } catch (Exception e) {
            log.error("C->S第一阶段寻源派单  生成共享占用单失败:{}", Throwables.getStackTraceAsString(e));
            successRequests.clear();
            valueHolderV14.setCode(StrategyConstants.RESULT_CODE_EXCEPTION);
            valueHolderV14.setMessage("C->S第一阶段寻源派单  生成共享占用单失败");
            if (preOutFlag) {
                FindSourceStrategyUtils.outputLog("C->S拆单策略 新增共享占用单服务 生成共享占用单失败 开始回滚共享占用单");
                sgFindSourceRollBackService.shareOutRollBackStorage(strategyC2SRequest.getSourceBillNo(), strategyC2SRequest.getSourceBillId(), strategyC2SRequest.getSourceBillType());
            }
        }
        setResult(strategyC2SRequest, successRequests);
    }

    /**
     * @param strategyC2SRequest:
     * @param successRequests:
     * @Description: 封装返回结果
     * @Author: hwy
     * @Date: 2021/6/29 15:59
     * @return: void
     **/
    private void setResult(SgFindSourceStrategyC2SRequest strategyC2SRequest, List<SgBShareOutBillSaveRequest> successRequests) {
        SgFindSourceStrategyC2SResult strategyResult = new SgFindSourceStrategyC2SResult();
        strategyC2SRequest.setStrategyBaseResult(strategyResult);
        Map<Long, SgFindSourceStrategySkuC2SResult> skuC2SResultMap = new HashMap<>();
        List<SkuItemC2S> skuItems = strategyC2SRequest.getSkuItems();
        successRequests.forEach(o -> {
            List<SgBShareOutItemSaveRequest> shareOutItemSaveRequestList = o.getShareOutItemSaveRequestList();
            List<SgBShareOutItemLogSaveRequest> shareOutItemLogSaveRequestList = o.getShareOutItemLogSaveRequestList();
            SgBShareOutSaveRequest shareOutSaveRequest = o.getShareOutSaveRequest();
            Long sgCShareStoreId = shareOutSaveRequest.getSgCShareStoreId();
            //是否有合并
            if (!shareOutSaveRequest.getMergeMark()) {
                shareOutItemSaveRequestList.forEach(i -> {
                    BigDecimal qty = i.getQty();
                    Long psCSkuId = i.getPsCSkuId();
                    Long sourceBillItemId = i.getSourceBillItemId();
                    List<SgFindSourceStrategyStoreItemC2SResult> itemResults;
                    SgFindSourceStrategySkuC2SResult skuC2SResult = skuC2SResultMap.get(sourceBillItemId);
                    if (skuC2SResult == null) {
                        skuC2SResult = new SgFindSourceStrategySkuC2SResult();
                        skuC2SResultMap.put(sourceBillItemId, skuC2SResult);
                        skuC2SResult.setPsCSkuId(psCSkuId);
                        skuC2SResult.setSourceItemId(sourceBillItemId);

                        itemResults = new ArrayList<>();
                        skuC2SResult.setItemResult(itemResults);
                    } else {
                        itemResults = skuC2SResult.getItemResult();
                    }
                    SgFindSourceStrategyStoreItemC2SResult itemC2SResult = new SgFindSourceStrategyStoreItemC2SResult();
                    itemResults.add(itemC2SResult);
                    itemC2SResult.setQty(qty);
                    itemC2SResult.setSgCSaStoreId(i.getSgCSaStoreId());
                    itemC2SResult.setSgCShareStoreId(sgCShareStoreId);
                });
            } else {
                //合并 使用日志明细
                shareOutItemLogSaveRequestList.forEach(i -> {
                    BigDecimal qty = i.getQty();
                    Long psCSkuId = i.getPsCSkuId();
                    Long sourceBillItemId = i.getSourceBillItemId();
                    SgFindSourceStrategySkuC2SResult skuC2SResult = skuC2SResultMap.get(sourceBillItemId);
                    List<SgFindSourceStrategyStoreItemC2SResult> itemResults;
                    if (skuC2SResult == null) {
                        skuC2SResult = new SgFindSourceStrategySkuC2SResult();
                        skuC2SResultMap.put(sourceBillItemId, skuC2SResult);
                        skuC2SResult.setPsCSkuId(psCSkuId);
                        skuC2SResult.setSourceItemId(sourceBillItemId);
                        itemResults = new ArrayList<>();
                        skuC2SResult.setItemResult(itemResults);
                    } else {
                        itemResults = skuC2SResult.getItemResult();
                    }
                    SgFindSourceStrategyStoreItemC2SResult itemC2SResult = new SgFindSourceStrategyStoreItemC2SResult();
                    itemResults.add(itemC2SResult);
                    itemC2SResult.setQty(qty);
                    itemC2SResult.setSgCSaStoreId(i.getSgCSaStoreId());
                    itemC2SResult.setSgCShareStoreId(sgCShareStoreId);
                });
            }
        });
        skuItems.forEach(o -> {
            Long sourceItemId = o.getSourceItemId();
            BigDecimal qty = o.getQty();
            Long psCSkuId = o.getPsCSkuId();
//            不存在添加缺货
            if (!skuC2SResultMap.containsKey(sourceItemId)) {
                SgFindSourceStrategySkuC2SResult skuC2SResult = new SgFindSourceStrategySkuC2SResult();
                skuC2SResultMap.put(sourceItemId, skuC2SResult);
                skuC2SResult.setPsCSkuId(psCSkuId);
                skuC2SResult.setSourceItemId(sourceItemId);
                List<SgFindSourceStrategyStoreItemC2SResult> itemResults = new ArrayList<>();
                skuC2SResult.setItemResult(itemResults);
                SgFindSourceStrategyStoreItemC2SResult itemC2SResult = new SgFindSourceStrategyStoreItemC2SResult();
                itemResults.add(itemC2SResult);
                itemC2SResult.setQty(qty);
                itemC2SResult.setSgCSaStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                itemC2SResult.setSgCShareStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                itemC2SResult.setOutStockFlag(Boolean.TRUE);
                return;
            }
            SgFindSourceStrategySkuC2SResult skuC2SResult = skuC2SResultMap.get(sourceItemId);
            List<SgFindSourceStrategyStoreItemC2SResult> itemResults = skuC2SResult.getItemResult();
            BigDecimal allQty = itemResults.stream().map(SgFindSourceStrategyStoreItemC2SResult::getQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            if (allQty.compareTo(qty) < 0) {
                SgFindSourceStrategyStoreItemC2SResult itemC2SResult = new SgFindSourceStrategyStoreItemC2SResult();
                itemResults.add(itemC2SResult);
                itemC2SResult.setQty(qty);
                itemC2SResult.setSgCSaStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                itemC2SResult.setSgCShareStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                itemC2SResult.setOutStockFlag(Boolean.TRUE);
                return;
            }
        });
        SgFindSourceStrategyC2SResult strategyC2SResult = (SgFindSourceStrategyC2SResult) strategyC2SRequest.getStrategyBaseResult();
        if (strategyC2SResult == null) {
            strategyC2SResult = new SgFindSourceStrategyC2SResult();
            strategyC2SRequest.setStrategyBaseResult(strategyC2SResult);
        }
        strategyC2SResult.setSkuResultList(new ArrayList<>(skuC2SResultMap.values()));
    }

    /**
     * @param strategyC2SRequest: 原始参数
     * @param occupyPlanResult:   key: sourceBillId value:(storeId,itemPlan)
     * @param outStockItemIds:    缺货的明细
     * @Description:
     * @Author: hwy
     * @Date: 2021/6/25 15:25
     * @return: com.burgeon.r3.sg.share.model.request.out.SgBShareOutBillSaveRequest
     **/
    private List<SgBShareOutBillSaveRequest> buildStorageChangeRequest(SgFindSourceStrategyC2SRequest strategyC2SRequest,
                                                                       Map<Long, Map<Long, SgFindSourceStrategyStoreItemC2SResult>> occupyPlanResult,
                                                                       List<Long> outStockItemIds) {
        //共享占用单入参  key 聚合仓id
        Map<Long, SgBShareOutBillSaveRequest> saveRequestMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
        Map<Long, SkuItemC2S> skuItemC2SMap = strategyC2SRequest.getSkuItems().stream().collect(Collectors.toMap(SkuItemC2S::getSourceItemId, Function.identity()));
        for (Map.Entry<Long, Map<Long, SgFindSourceStrategyStoreItemC2SResult>> entry : occupyPlanResult.entrySet()) {
            Long sourceItemId = entry.getKey();
            Map<Long, SgFindSourceStrategyStoreItemC2SResult> itemPlanMap = entry.getValue();

            SkuItemC2S skuItemC2S = skuItemC2SMap.get(sourceItemId);
            for (Map.Entry<Long, SgFindSourceStrategyStoreItemC2SResult> itemPlanEntry : itemPlanMap.entrySet()) {
                Long saStoreId = itemPlanEntry.getKey();
                SgFindSourceStrategyStoreItemC2SResult itemPlan = itemPlanEntry.getValue();
                Long sgShareStoreId = itemPlan.getSgCShareStoreId();
                if (StrategyConstants.OUT_DEFAULT_STORE_ID.equals(saStoreId)) {
                    FindSourceStrategyUtils.outputLog("C->S拆单策略 明细id:{} 库存无法满足 明细缺货", sourceItemId);
                    if (!outStockItemIds.contains(sourceItemId)) {
                        outStockItemIds.add(sourceItemId);
                    }
                    continue;
                }

                //设置共享占用单请求参数
                SgBShareOutBillSaveRequest saveRequest = saveRequestMap.get(sgShareStoreId);
                if (saveRequest == null) {
                    saveRequest = new SgBShareOutBillSaveRequest();
                    saveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());
                    saveRequestMap.put(sgShareStoreId, saveRequest);
                }
                SgBShareOutSaveRequest shareOutSaveRequest = saveRequest.getShareOutSaveRequest();
                if (shareOutSaveRequest == null) {
                    shareOutSaveRequest = new SgBShareOutSaveRequest();
                    saveRequest.setShareOutSaveRequest(shareOutSaveRequest);
                }
                List<SgBShareOutItemSaveRequest> itemList = saveRequest.getShareOutItemSaveRequestList();
                if (CollectionUtils.isEmpty(itemList)) {
                    itemList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
                    saveRequest.setShareOutItemSaveRequestList(itemList);
                }
                List<SgBShareOutItemLogSaveRequest> itemLogList = saveRequest.getShareOutItemLogSaveRequestList();
                if (CollectionUtils.isEmpty(itemLogList)) {
                    itemLogList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
                    saveRequest.setShareOutItemLogSaveRequestList(itemLogList);
                }

                // 主表字段设置
                shareOutSaveRequest.setSgCShareStoreId(sgShareStoreId);
                shareOutSaveRequest.setSourceBillId(strategyC2SRequest.getSourceBillId());
                shareOutSaveRequest.setSourceBillNo(strategyC2SRequest.getSourceBillNo());
                shareOutSaveRequest.setSourceBillType(strategyC2SRequest.getSourceBillType());
                shareOutSaveRequest.setServiceNode(SgConstantsIF.SERVICE_NODE_SHARE_OUT_SAVE);
                shareOutSaveRequest.setSourceBillDate(strategyC2SRequest.getBillDate());
                shareOutSaveRequest.setCpCShopId(strategyC2SRequest.getShopId());
                shareOutSaveRequest.setTid(strategyC2SRequest.getTid());

                // 新增一条明细
                SgBShareOutItemSaveRequest itemSaveRequest = new SgBShareOutItemSaveRequest();
                itemSaveRequest.setPsCSkuId(skuItemC2S.getPsCSkuId());
                itemSaveRequest.setQtyPreout(itemPlan.getQty());
                itemSaveRequest.setQty(itemPlan.getQty());
                itemSaveRequest.setSourceBillItemId(sourceItemId);
                // 记录配销仓占用库存变动参数
                itemSaveRequest.setSgCSaStoreId(saStoreId);
                itemSaveRequest.setSourceStorage(SgConstants.SHARE_OUT_ITEM_STOCK_SOURCE_SA);

                // 新增日志表记录未合并的明细
                SgBShareOutItemLogSaveRequest itemLogSaveRequest = new SgBShareOutItemLogSaveRequest();
                BeanUtils.copyProperties(itemSaveRequest, itemLogSaveRequest);
                itemLogList.add(itemLogSaveRequest);

                // 合并 明细  产品 新逻辑
                if (CollectionUtils.isNotEmpty(itemList)) {

                    SgBShareOutItemSaveRequest shareOutItemSaveRequest = itemList.stream().filter(item -> !ObjectUtils.isEmpty(item.getSgCSaStoreId()) && !ObjectUtils.isEmpty(itemSaveRequest.getSgCSaStoreId()) && item.getSgCSaStoreId().equals(itemSaveRequest.getSgCSaStoreId()) && item.getPsCSkuId().equals(itemSaveRequest.getPsCSkuId())).findFirst().orElse(null);
                    if (!ObjectUtils.isEmpty(shareOutItemSaveRequest)) {
                        BigDecimal qty = ObjectUtils.isEmpty(shareOutItemSaveRequest.getQty()) ? BigDecimal.ZERO : shareOutItemSaveRequest.getQty();
                        BigDecimal qtyPreout = ObjectUtils.isEmpty(shareOutItemSaveRequest.getQtyPreout()) ? BigDecimal.ZERO : shareOutItemSaveRequest.getQtyPreout();

                        shareOutItemSaveRequest.setQtyPreout(qtyPreout.add(itemSaveRequest.getQtyPreout()));
                        shareOutItemSaveRequest.setQty(qty.add(itemSaveRequest.getQty()));
                    }

                    if (ObjectUtils.isEmpty(shareOutItemSaveRequest)) {
                        itemList.add(itemSaveRequest);
                    }
                } else {
                    itemList.add(itemSaveRequest);
                }

                //合并标记  根据日志中条码  和  占单仓 来判断
                if (CollectionUtils.isNotEmpty(itemLogList) && !shareOutSaveRequest.getMergeMark()) {
                    shareOutSaveRequest.setMergeMark(itemLogList.stream().collect(Collectors.groupingBy(itemLog -> itemLog.getPsCSkuId() + SgConstants.SG_CONNECTOR_MARKS_6 + itemLog.getSgCSaStoreId(), Collectors.counting())).values().stream().anyMatch(count -> count > 1));
                }
            }
        }
        // 进行数据清洗, 合并的才添加 日志明细,  未合并的不使用日志明细
        if (MapUtils.isNotEmpty(saveRequestMap)) {
            saveRequestMap.forEach((key, val) -> {
                SgBShareOutSaveRequest shareOutSaveRequest = val.getShareOutSaveRequest();
                if (!shareOutSaveRequest.getMergeMark()) {
                    //直接清除日志
                    val.setShareOutItemLogSaveRequestList(null);
                }
            });
        }

        return new ArrayList<>(saveRequestMap.values());
    }

    /**
     * @param strategyC2SRequest:
     * @Description: 全部缺货
     * @Author: hwy
     * @Date: 2021/6/23 11:08
     * @return: void
     **/
    private void allOut(SgFindSourceStrategyC2SRequest strategyC2SRequest) {
        SgFindSourceStrategyC2SResult strategyC2SResult = new SgFindSourceStrategyC2SResult();
        strategyC2SRequest.setStrategyBaseResult(strategyC2SResult);
        List<SgFindSourceStrategySkuC2SResult> skuResultList = new ArrayList<>();
        strategyC2SResult.setSkuResultList(skuResultList);
        List<SkuItemC2S> skuItems = strategyC2SRequest.getSkuItems();
        for (SkuItemC2S skuItem : skuItems) {
            SgFindSourceStrategySkuC2SResult skuC2SResult = new SgFindSourceStrategySkuC2SResult();
            skuC2SResult.setPsCSkuId(skuItem.getPsCSkuId());
            skuC2SResult.setSourceItemId(skuItem.getSourceItemId());
            SgFindSourceStrategyStoreItemC2SResult itemC2SResult = new SgFindSourceStrategyStoreItemC2SResult();
            itemC2SResult.setSgCSaStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
            itemC2SResult.setSgCShareStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
            itemC2SResult.setQty(skuItem.getQty());
            skuC2SResult.setItemResult(Arrays.asList(itemC2SResult));
            skuResultList.add(skuC2SResult);
        }
    }

    /**
     * @param strategyC2SRequest  :  原始参数
     * @param undistributedItem   :   未分配的明细
     * @param skuStrategyInfoList
     * @param occupyPlanResultMap : 分配结果
     * @Description: 配销仓数量优先分配
     * @Author: hwy
     * @Date: 2021/6/22 19:29
     * @return: void
     **/
    private void occupyPlan(SgFindSourceStrategyC2SRequest strategyC2SRequest,
                            List<SkuItemC2S> undistributedItem,
                            List<SgFindSourceStrategySkuC2SResult> skuStrategyInfoList,
                            Map<Long, Map<Long, SgFindSourceStrategyStoreItemC2SResult>> occupyPlanResultMap) {
        //使用锁定库存策略分配订单明细
        SgOccupyPlanServiceRequest saQtyOccPlanRequest = new SgOccupyPlanServiceRequest();
        saQtyOccPlanRequest.setStrategyC2SRequest(strategyC2SRequest);
        saQtyOccPlanRequest.setCpCShopId(strategyC2SRequest.getShopId());
        saQtyOccPlanRequest.setSourceBillId(strategyC2SRequest.getSourceBillId());
        saQtyOccPlanRequest.setSourceBillType(strategyC2SRequest.getSourceBillType());
        List<SgOccupyPlanSkuItemRequest> skuItemRequests = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        Map<Long, List<SgOccupyPlanPriorityRequest>> priorityRequestMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
        // 设置内层优先级
        saQtyOccPlanRequest.setSkuItemRequests(skuItemRequests);

        List<Long> saStoreIdList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        Map<Long, Long> saToShareStoreMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

        // 处理总配销仓优先级、明细配销仓优先级
        skuStrategyInfoList.forEach(x -> {
            List<SgOccupyPlanPriorityRequest> priorityRequestList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
            x.getItemResult().forEach(y -> {
                SgOccupyPlanPriorityRequest inPriorityRequest = new SgOccupyPlanPriorityRequest();
                inPriorityRequest.setStoreId(y.getSgCSaStoreId());
                inPriorityRequest.setPriority(y.getPriority());
                priorityRequestList.add(inPriorityRequest);

                if (!saStoreIdList.contains(y.getSgCSaStoreId())) {
                    saStoreIdList.add(y.getSgCSaStoreId());
                    saToShareStoreMap.put(y.getSgCSaStoreId(), y.getSgCShareStoreId());
                }
            });
            priorityRequestMap.put(x.getSourceItemId(), priorityRequestList);
        });
        saQtyOccPlanRequest.setSaStoreIdList(saStoreIdList);


        // 更新待分配数量
        undistributedItem.forEach(o -> {
            SgOccupyPlanSkuItemRequest sgOccupyPlanSkuItemRequest = new SgOccupyPlanSkuItemRequest();
            sgOccupyPlanSkuItemRequest.setPsCSkuId(o.getPsCSkuId());
            sgOccupyPlanSkuItemRequest.setQtyChange(o.getQty());
            sgOccupyPlanSkuItemRequest.setSourceItemId(o.getSourceItemId());
            sgOccupyPlanSkuItemRequest.setStorePriorities(priorityRequestMap.get(o.getSourceItemId()));
            skuItemRequests.add(sgOccupyPlanSkuItemRequest);
        });
        //获取派单计划
        ValueHolderV14<SgOccupyPlanServiceResult> occupyPlanResult = occupyPlanService.getOccupyPlan(saQtyOccPlanRequest);
        if (!occupyPlanResult.isOK()) {
            log.error("按数量分配订单明细报错:{}", occupyPlanResult.getMessage());
            return;
        }
        SgOccupyPlanServiceResult occupyPlan = occupyPlanResult.getData();
        //执行结果为空 明细不做处理
        if (occupyPlan == null) {
            return;
        }
        log.info("SgFindSourceC2SPreoutStorageService.occupyPlan occupyPlanResult:{}", JSONObject.toJSONString(occupyPlanResult));
        //解析分配结果
        List<SgOccupyPlanItemResult> itemResultList = occupyPlan.getItemResultList();
        itemResultList.forEach(planItemResult -> {
            Long sourceItemId = planItemResult.getSourceItemId();
            Map<Long, BigDecimal> storePlan = planItemResult.getStorePlan();
            // key : storeId / lock_storeId
            Map<Long, SgFindSourceStrategyStoreItemC2SResult> skuC2SResultMap;
            if (!occupyPlanResultMap.containsKey(sourceItemId)) {
                skuC2SResultMap = new HashMap<>();
                occupyPlanResultMap.put(sourceItemId, skuC2SResultMap);
            } else {
                skuC2SResultMap = occupyPlanResultMap.get(sourceItemId);
            }
            storePlan.forEach((storeId, qty) -> {

                SgFindSourceStrategyStoreItemC2SResult itemC2SResult = new SgFindSourceStrategyStoreItemC2SResult();
                itemC2SResult.setSgCSaStoreId(storeId);
                itemC2SResult.setQty(qty);
                itemC2SResult.setSgCShareStoreId(saToShareStoreMap.get(storeId));
                skuC2SResultMap.put(storeId, itemC2SResult);
                //查看是否包含缺货的明细 TODO 这下面的逻辑待确认是否有用 先注释，不影响逻辑就删除
//                SgFindSourceStrategyStoreItemC2SResult outItemC2SResult = skuC2SResultMap.get(StrategyConstants.OUT_DEFAULT_STORE_ID);
//                //如果该条码存在缺货明细 更新明细缺货数量 为0时移除
//                if (outItemC2SResult != null) {
//                    BigDecimal outQty = outItemC2SResult.getQty();
//                    outQty = outQty.subtract(qty);
//                    if (outQty.compareTo(BigDecimal.ZERO) == 0) {
//                        skuC2SResultMap.remove(StrategyConstants.OUT_DEFAULT_STORE_ID);
//                    } else {
//                        outItemC2SResult.setQty(outQty);
//                    }
//                }
            });
        });
        //缺货商品
        List<SgOccupyPlanOutItemResult> outItemResultList = occupyPlan.getOutItmeResultList();
        //将缺货明细添加到分配结果中
        if (CollectionUtils.isNotEmpty(outItemResultList)) {
            outItemResultList.forEach(outItemResult -> {
                Long sourceItemId = outItemResult.getSourceItemId();
                BigDecimal outQty = outItemResult.getOutQty();
                Map<Long, SgFindSourceStrategyStoreItemC2SResult> skuC2SResultMap;
                if (!occupyPlanResultMap.containsKey(sourceItemId)) {
                    skuC2SResultMap = new HashMap<>();
                    occupyPlanResultMap.put(sourceItemId, skuC2SResultMap);
                } else {
                    skuC2SResultMap = occupyPlanResultMap.get(sourceItemId);
                }
                SgFindSourceStrategyStoreItemC2SResult itemC2SResult = new SgFindSourceStrategyStoreItemC2SResult();
                itemC2SResult.setSgCSaStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                itemC2SResult.setSgCShareStoreId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                itemC2SResult.setOutStockFlag(Boolean.TRUE);
                itemC2SResult.setQty(outQty);
                skuC2SResultMap.put(StrategyConstants.OUT_DEFAULT_STORE_ID, itemC2SResult);
            });
        }
        //更新未分配队列 TODO 重复缺货
//        updateUndistributedItems(undistributedItem, occupyPlanResultMap);
    }

    /**
     * @param undistributedItem:
     * @param occupyPlanResultMap:
     * @Description: 更新未分配队列
     * @Author: hwy
     * @Date: 2021/6/22 20:11
     * @return: void
     **/
    private void updateUndistributedItems(List<SkuItemC2S> undistributedItem, Map<Long, Map<Long, SgFindSourceStrategyStoreItemC2SResult>> occupyPlanResultMap) {
        List<SkuItemC2S> distributedItems = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        for (SkuItemC2S skuItem : undistributedItem) {
            Long sourceItemId = skuItem.getSourceItemId();
            BigDecimal qty = skuItem.getQty();
            //没有分配添加入未分配队列
            if (!occupyPlanResultMap.containsKey(sourceItemId)) {
                continue;
            }
            Map<Long, SgFindSourceStrategyStoreItemC2SResult> skuC2SResultMap = occupyPlanResultMap.get(sourceItemId);
            //未缺货的数量相加
            BigDecimal bigDecimal = skuC2SResultMap.values().stream().filter(o -> !o.getOutStockFlag()).map(SgFindSourceStrategyStoreItemC2SResult::getQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            //分配完添加入已分配队列
            if (qty.compareTo(bigDecimal) == 0) {
                distributedItems.add(skuItem);
            }
        }
        //将已分配完的明细从未分配的明细中删除
        if (CollectionUtils.isNotEmpty(distributedItems)) {
            undistributedItem.removeAll(distributedItems);
        }
    }

}
