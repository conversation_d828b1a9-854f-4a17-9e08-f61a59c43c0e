package com.burgeon.r3.sg.sourcing.services.item;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategy;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategyWarehouseItem;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyMapper;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyWarehouseItemMapper;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仓优先策略
 */
@Slf4j
@Component
public class SgFindSourceWarehousePriorityStrategyService extends StrategyHandle {

    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {

        SgFindSourceStrategyS2LRequest s2LRequest = (SgFindSourceStrategyS2LRequest) request;
        ValueHolderV14<StrategyBaseResult> result = doHandle(s2LRequest);
        try {
            s2LRequest.setStrategyBaseResult(result.getData());
        } catch (Exception e) {
            log.error("S->L 二阶段寻源 仓优先策略 执行异常:{}", Throwables.getStackTraceAsString(e));
            result.setCode(ResultCode.FAIL);
            result.setMessage("S->L 二阶段寻源 仓优先策略 执行异常");
        }
        return doNext(s2LRequest, result);
    }

    /**
     * @param request
     * @return
     */
    private ValueHolderV14<StrategyBaseResult> doHandle(SgFindSourceStrategyS2LRequest request) {

        FindSourceStrategyUtils.outputLog("Start SgFindSourceWarehousePriorityStrategyService doHandle 仓优先策略 request:{}",
                JSONObject.toJSONString(request));

        ValueHolderV14<StrategyBaseResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        StrategyBaseResult strategyBaseResult = request.getStrategyBaseResult();

        if (!(strategyBaseResult instanceof SgFindSourceStrategyS2LResult)) {
            log.error("SgFindSourceWarehousePriorityStrategyService doHandle error:执行仓优先寻源策略时，请求参数的结果对象类型错误！");
            return new ValueHolderV14<>(ResultCode.FAIL, "执行仓优先寻源策略时，请求参数的结果对象类型错误！");
        }

        SgFindSourceStrategyS2LResult result = (SgFindSourceStrategyS2LResult) strategyBaseResult;
        List<SgFindSourceStrategySkuS2LResult> skuResultList = result.getSkuResultList();

        if (CollectionUtils.isEmpty(skuResultList)) {
            log.error("SgFindSourceWarehousePriorityStrategyService doHandle error:skuResultList_is_null");
            return new ValueHolderV14<>(ResultCode.FAIL, "前次策略执行结果为空！");
        }

        // 获取仓优先策略集合
        List<SgCChannelSourceStrategyWarehouseItem> warehouseItems = queryWarehouseStrategyByShopId(request);

        if (CollectionUtils.isEmpty(warehouseItems)) {
            log.info("SgFindSourceWarehousePriorityStrategyService doHandle 执行仓优先策略 获取仓优先策略为空");
            //直接返回上一次策略执行结果
            valueHolderV14.setMessage("执行仓优先策略 获取仓优先策略为空");
            valueHolderV14.setData(strategyBaseResult);
            return valueHolderV14;
        }

        // 获取到可唯一发货的集合
        List<Long> oneStockWarehouseIds = FindSourceStrategyUtils.getOneStockWarehouseIds(request);

        warehouseItems = warehouseItems.stream().filter(x -> oneStockWarehouseIds.contains(x.getCpCPhyWarehouseId())).collect(Collectors.toList());

        if (log.isDebugEnabled()) {
            log.debug("aaa:{}  {}", JSONObject.toJSONString(oneStockWarehouseIds), JSONObject.toJSONString(warehouseItems));
        }

        if (CollectionUtils.isNotEmpty(warehouseItems)) {
            Integer maxPriority = 0;
            List<Long> storeIdList = new ArrayList<>();

            for (SgCChannelSourceStrategyWarehouseItem warehouseItem : warehouseItems) {
                if (maxPriority.equals(warehouseItem.getPriority())) {
                    storeIdList.add(warehouseItem.getCpCPhyWarehouseId());
                }
                if (warehouseItem.getPriority() > maxPriority) {
                    storeIdList.clear();
                    storeIdList.add(warehouseItem.getCpCPhyWarehouseId());
                    maxPriority = warehouseItem.getPriority();
                }
            }

            if (log.isDebugEnabled()) {
                log.debug("sss:{}", JSONObject.toJSONString(storeIdList));
            }

            for (SgFindSourceStrategySkuS2LResult skuS2LResult : skuResultList) {
                List<SgFindSourceStrategyStoreItemS2LResult> itemResultList = new ArrayList<>();
                for (SgFindSourceStrategyStoreItemS2LResult itemS2LResult : skuS2LResult.getItemResultList()) {
                    if (storeIdList.contains(itemS2LResult.getStoreId())) {
                        itemResultList.add(itemS2LResult);
                    }
                }
                skuS2LResult.setItemResultList(itemResultList);
            }
            result.setDoNextStrategy(Boolean.FALSE);
        }

//        // 实体仓优先级map<phyWarehouseId,priority>
//        Map<Long, Integer> warehouseItemPriorityMap = warehouseItems.stream().collect(
//                Collectors.toMap(SgCChannelSourceStrategyWarehouseItem::getCpCPhyWarehouseId,
//                        SgCChannelSourceStrategyWarehouseItem::getPriority,
//                        (o1, o2) -> o1));
//
//        List<SgFindSourceStrategyStoreItemS2LResult> oldItemResultList = null;
//        List<SgFindSourceStrategyStoreItemS2LResult> newItemResultList = null;
//        List<SgFindSourceStrategyStoreItemS2LResult> itemResultList = null;
//        List<SgFindSourceStrategyStoreItemS2LResult> finalItemResultList = null;
//        Long warehouseId = null;
//        Integer maxPriority = null;
//        Integer priority = null;
//
//        for (SgFindSourceStrategySkuS2LResult skuS2LResult : skuResultList) {
//
//            //获取所有的策略结果集
//            oldItemResultList = new ArrayList<>();
//            newItemResultList = new ArrayList<>();
//            itemResultList = skuS2LResult.getItemResultList();
//            finalItemResultList = new ArrayList<>();
//
//            if (CollectionUtils.isNotEmpty(itemResultList)) {
//
//                //获取当前集合中的最大优先级
//                maxPriority = itemResultList.stream().map(
//                        SgFindSourceStrategyStoreItemS2LResult::getPriority).max(Integer::compareTo).get();
//
//                for (SgFindSourceStrategyStoreItemS2LResult itemS2LResult : itemResultList) {
//
//                    warehouseId = itemS2LResult.getStoreId();
//
//                    if (warehouseItemPriorityMap.containsKey(warehouseId)) {
//                        priority = warehouseItemPriorityMap.get(warehouseId) == null ? 0 : warehouseItemPriorityMap.get(warehouseId);
//                        itemS2LResult.setPriority(maxPriority + priority);
//                        newItemResultList.add(itemS2LResult);
//                    } else {
//                        oldItemResultList.add(itemS2LResult);
//                    }
//                }
//
//                //按照优先级排序
//                Collections.sort(newItemResultList,
//                        Comparator.comparingInt(SgFindSourceStrategyStoreItemS2LResult::getPriority).reversed());
//
//                finalItemResultList.addAll(newItemResultList);
//                finalItemResultList.addAll(oldItemResultList);
//            }
//
//            skuS2LResult.setItemResultList(finalItemResultList);
//        }

        FindSourceStrategyUtils.outputLog("Finish SgFindSourceWarehousePriorityStrategyService doHandle 仓优先策略 request:{}",
                JSONObject.toJSONString(request));

        valueHolderV14.setData(strategyBaseResult);

        return valueHolderV14;
    }


    /**
     * 根据shopId 获取仓优先策略集合
     *
     * @return
     */
    private List<SgCChannelSourceStrategyWarehouseItem> queryWarehouseStrategyByShopId(SgFindSourceStrategyS2LRequest request) {

        SgCChannelSourceStrategy channelSourceStrategy = request.getSourceStrategy();

        SgCChannelSourceStrategyWarehouseItemMapper warehouseItemMapper = ApplicationContextHandle.getBean(
                SgCChannelSourceStrategyWarehouseItemMapper.class);
        return warehouseItemMapper.selectList
                (new QueryWrapper<SgCChannelSourceStrategyWarehouseItem>().lambda()
                        .eq(SgCChannelSourceStrategyWarehouseItem::getSgCChannelSourceStrategyId, channelSourceStrategy.getId())
                        .eq(SgCChannelSourceStrategyWarehouseItem::getIsactive, SgConstants.IS_ACTIVE_Y));
    }

}
