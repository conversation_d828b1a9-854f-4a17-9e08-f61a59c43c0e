package com.burgeon.r3.sg.sourcing.services.item;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategy;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyMapper;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyScoreItemMapper;
import com.burgeon.r3.sg.sourcing.mapper.storescore.SgCStoreScoreStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemS2L;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.request.score.SgWarehouseScoreModel;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.burgeon.r3.sg.sourcing.model.result.score.SgCShareScoreFactorStrategyResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.google.common.base.Throwables;
import com.google.common.util.concurrent.AtomicDouble;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * @description: 实体仓评分策略执行器
 * @author: lsj
 * @time: 2021/6/16
 */
@Slf4j
@Component
public class SgFindSourceWarehouseScoreStrategyService extends StrategyHandle {

    /**
     * 策略实现方法
     */
    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {

        log.info(LogUtil.format("SgFindSourceWarehouseScoreStrategyService.handleRequest S->L二阶段寻源派单 实体仓评分策略执行器 param:{}",
                SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L),
                request.getTraceId(), JSONObject.toJSONString(request));

        SgFindSourceStrategyS2LRequest s2LRequest = (SgFindSourceStrategyS2LRequest) request;
        ValueHolderV14<StrategyBaseResult> result = doHandle(s2LRequest);
        try {
            s2LRequest.setStrategyBaseResult(result.getData());
        } catch (Exception e) {
            log.error(LogUtil.format("SgFindSourceFilterStrategyService.handleRequest S->L二阶段寻源派单 实体仓评分策略执行器 exception:{}",
                    SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_S2L_EXCEPTION),
                    request.getTraceId(), Throwables.getStackTraceAsString(e));
            result.setCode(ResultCode.FAIL);
            result.setMessage("S->L 二阶段寻源 评分因子策略 执行异常");
        }
        return doNext(s2LRequest, result);

    }

    /**
     * 执行实体仓评分因子策略
     */
    private ValueHolderV14<StrategyBaseResult> doHandle(SgFindSourceStrategyS2LRequest request) {

        //处理入参
        StrategyBaseResult strategyBaseResult = request.getStrategyBaseResult();
        SgFindSourceStrategyS2LResult result = (SgFindSourceStrategyS2LResult) strategyBaseResult;

        if (result == null) {
            log.error("S->L二阶段寻源派单 评分因子策略获取上游策略执行结果:result_is_null");
            return new ValueHolderV14<>(ResultCode.FAIL, "实体仓评分策略获取上游策略执行结果:不存在可使用的仓库 策略执行结束!");
        }

        List<SgFindSourceStrategySkuS2LResult> skuResultList = result.getSkuResultList();

        if (skuResultList == null) {
            log.error("S->L二阶段寻源派单 评分因子策略获取上游策略执行结果:skuResultList_is_null");
            return new ValueHolderV14<>(ResultCode.FAIL, "实体仓评分策略获取上游策略执行结果:不存在可使用的仓库 策略执行结束!");
        }

        //将所有的店仓作为一个map 集合存放  <warehouseId,warehouseId;priority（优先级）;score（实体仓得分）>
        Map<Long, SgWarehouseScoreModel> scoreMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

        skuResultList.forEach(skuResult ->
                skuResult.getItemResultList().forEach(item -> {
                    if (item.getStoreId() != -1L) {
                        scoreMap.put(item.getStoreId(), new SgWarehouseScoreModel(item.getStoreId(), item.getPriority()));
                    }
                }));

        //遍历
        List<SgWarehouseScoreModel> physicsScoreModelList = new ArrayList<>(scoreMap.values());

        //获取分数重复的店仓集合
        List<SgWarehouseScoreModel> sameScoreList = getSameWarehouseScoreModel(physicsScoreModelList);

        FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 实体仓评分策略获取分数重复的店仓集合结果. sameScoreList.size:{}",
                sameScoreList.size());

        if (CollectionUtils.isEmpty(sameScoreList)) {

            //不需要执行当前策略，直接返回即可
            ValueHolderV14<StrategyBaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "获取分数重复的店仓集合为空！");
            //升序 设置优先级
            physicsScoreModelList.sort(Comparator.comparing(SgWarehouseScoreModel::getOldPriority));

            // 赋值priority 字段
            int temp = 10;
            for (int i = 0; i < physicsScoreModelList.size(); i++) {
                physicsScoreModelList.get(i).setNewPriority(temp);
                temp += 10;
            }

            Map<Long, Integer> priorityMap = physicsScoreModelList.stream().collect(
                    Collectors.toMap(SgWarehouseScoreModel::getWarehouseId, SgWarehouseScoreModel::getNewPriority));

            //新加逻辑 JITX转零售发货单 指定仓优先寻源  == 通过实体仓编码
            List<Long> warehouseIds = request.getWarehouseIds();
            if (!CollectionUtils.isEmpty(warehouseIds) && !MapUtils.isEmpty(priorityMap)) {
                for (int i = 0; i < warehouseIds.size(); i++) {
                    //如果 优先级map中存在, 第一个优先级最高
                    Long warehouseId = warehouseIds.get(i);
                    if (priorityMap.containsKey(warehouseId)) {
                        priorityMap.put(warehouseId, Integer.MAX_VALUE - i);
                    }
                }
            }

            skuResultList.forEach(skuResult -> {
                // 新加逻辑 JITX转零售发货单 排除指定仓库
                List<Long> excludeWarehouseIds = request.getExcludeWarehouseIds();
                List<SgFindSourceStrategyStoreItemS2LResult> itemS2LResults = skuResult.getItemResultList();
                if (!CollectionUtils.isEmpty(excludeWarehouseIds) && !CollectionUtils.isEmpty(itemS2LResults)) {
                    itemS2LResults = itemS2LResults.stream()
                            .filter(r -> !excludeWarehouseIds.contains(r.getStoreId())).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(itemS2LResults)) {
                    // 赋值优先级
                    itemS2LResults.forEach(item -> item.setPriority(priorityMap.get(item.getStoreId())));
                }
                skuResult.setItemResultList(itemS2LResults);
            });

            result.setSkuResultList(skuResultList);
            v14.setData(result);
            return v14;
        }

        //获取评分策略集合
        List<SgCShareScoreFactorStrategyResult> strategyItemList = queryFactorByShopId(request);

        //key:评分因子编码 value：权重
        Map<String, BigDecimal> weightMap = strategyItemList.stream().collect(
                Collectors.toMap(SgCShareScoreFactorStrategyResult::getEcode,
                        SgCShareScoreFactorStrategyResult::getWeight));

        FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 实体仓评分策略获取评分策略集合结果. weightMap:{}",
                weightMap);

        List<Long> warehouseIdList = sameScoreList.stream().map(SgWarehouseScoreModel::getWarehouseId).collect(Collectors.toList());

        //查询物理仓评分
        SgCStoreScoreStrategyMapper strategyMapper = ApplicationContextHandle.getBean(SgCStoreScoreStrategyMapper.class);

        //直接将所有需要查询的数据一起查出来加载到内存中，循环中直接通过warehouseId 获取当前store的分数即可
        List<SgWarehouseScoreModel> scoreInfoList = strategyMapper.queryByPhyWarehouseIds(warehouseIdList);

        FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 实体仓评分策略获取物理仓评分结果. scoreInfoList:{}",
                JSONObject.toJSONString(scoreInfoList));

        //key:warehouseId ,value:物理仓评分信息
        Map<Long, List<SgWarehouseScoreModel>> scoreInfoMap = scoreInfoList.stream().collect(
                Collectors.groupingBy(SgWarehouseScoreModel::getCpCPhyWarehouseId));

        //对重复的店仓，进行第二轮计算得分
        setScore(sameScoreList, scoreInfoMap, weightMap);

        // 将sameList 里面的分数，赋值给SgPhysicsScoreModelList
        Map<Long, SgWarehouseScoreModel> alreadyMap = sameScoreList.stream().collect(
                Collectors.toMap(SgWarehouseScoreModel::getWarehouseId, x -> x, (k1, k2) -> k1));

        physicsScoreModelList.forEach(SgPhysicsScoreModel -> {
            SgWarehouseScoreModel store = alreadyMap.get(SgPhysicsScoreModel.getWarehouseId());
            if (null != store) {
                SgPhysicsScoreModel.setScore(store.getScore());
            } else {
                SgPhysicsScoreModel.setScore(BigDecimal.ZERO);
            }
        });

        // 排序：原优先级升序、分数升序
        physicsScoreModelList.sort((score1, score2) -> {
            int i = score1.getOldPriority().compareTo(score2.getOldPriority());
            if (i != 0) {
                return i;
            }
            return score1.getScore().compareTo(score2.getScore());
        });

        // 赋值priority 字段
        setNewPriority(physicsScoreModelList);

        // 执行第10.2 步：根据保底库存量排序、设置优先级
        setPriorityByMinimumGuarantee(physicsScoreModelList, request.getSkuItems(), skuResultList);

        Map<Long, Integer> priorityMap = physicsScoreModelList.stream().collect(
                Collectors.toMap(SgWarehouseScoreModel::getWarehouseId, SgWarehouseScoreModel::getPriority));

        // 新加逻辑 JITX转零售发货单 指定仓优先寻源  == 通过实体仓编码
        List<Long> warehouseIds = request.getWarehouseIds();
        if (!CollectionUtils.isEmpty(warehouseIds) && !MapUtils.isEmpty(priorityMap)) {
            for (int i = 0; i < warehouseIds.size(); i++) {
                //如果 优先级map中存在, 第一个优先级最高
                Long warehouseId = warehouseIds.get(i);
                if (priorityMap.containsKey(warehouseId)) {
                    priorityMap.put(warehouseId, Integer.MAX_VALUE - i);
                }
            }
        }

        skuResultList.forEach(skuResult -> {
            // 新加逻辑 JITX转零售发货单 排除指定仓库
            List<Long> excludeWarehouseIds = request.getExcludeWarehouseIds();
            List<SgFindSourceStrategyStoreItemS2LResult> itemS2LResults = skuResult.getItemResultList();
            if (!CollectionUtils.isEmpty(excludeWarehouseIds) && !CollectionUtils.isEmpty(itemS2LResults)) {
                itemS2LResults = itemS2LResults.stream()
                        .filter(r -> !excludeWarehouseIds.contains(r.getStoreId())).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(itemS2LResults)) {
                // 赋值优先级
                itemS2LResults.forEach(item -> item.setPriority(priorityMap.get(item.getStoreId())));
            }
            skuResult.setItemResultList(itemS2LResults);
        });

        result.setSkuResultList(skuResultList);

        //清理Map
        physicsScoreModelList.clear();
        scoreInfoList.clear();
        if (warehouseIds != null) {
            warehouseIds.clear();
        }
        scoreMap.clear();
        scoreInfoMap.clear();
        priorityMap.clear();
        alreadyMap.clear();

        ValueHolderV14<StrategyBaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "执行物理仓评分策略和仓库保底率策略成功");
        v14.setData(result);

        FindSourceStrategyUtils.outputLog("S->L二阶段寻源派单 实体仓评分策略执行结果. result:{}",
                JSONObject.toJSONString(result));

        return v14;
    }

    /**
     * 重复店仓计算得分并赋值
     */
    private void setScore(List<SgWarehouseScoreModel> sameScoreList,
                          Map<Long, List<SgWarehouseScoreModel>> scoreInfoMap,
                          Map<String, BigDecimal> weightMap) {

        if (CollectionUtils.isEmpty(sameScoreList)) {
            return;
        }

        sameScoreList.forEach(scoreItem -> {

            //根据storeId查询店仓评分设置表，获取分数
            List<SgWarehouseScoreModel> scoreInfos = scoreInfoMap.get(scoreItem.getWarehouseId());

            if (CollectionUtils.isEmpty(scoreInfos)) {
                scoreInfos = new ArrayList<>();
            }

            //key:评分因子编码 value: 评分
            Map<String, BigDecimal> storeScoreMap = scoreInfos.stream().collect(
                    Collectors.toMap(SgWarehouseScoreModel::getSgCShareScoreFactorStrategyEcode,
                            SgWarehouseScoreModel::getScore, (k1, k2) -> k1));

            //保存分数
            AtomicDouble atom = new AtomicDouble(0);

            weightMap.forEach((k, v) -> {
                BigDecimal score = storeScoreMap.get(k);
                // 对分数赋值默认值
                if (score == null) {
                    score = BigDecimal.ZERO;
                }
                atom.addAndGet(score.multiply(v).doubleValue());
            });

            // 计算得分，赋值给score
            scoreItem.setScore(BigDecimal.valueOf(atom.doubleValue()));

        });
    }

    /**
     * 根据shopId 获取评分策略集合
     */
    private List<SgCShareScoreFactorStrategyResult> queryFactorByShopId(SgFindSourceStrategyS2LRequest request) {

        SgCChannelSourceStrategy channelSourceStrategy = request.getSourceStrategy();

        SgCChannelSourceStrategyScoreItemMapper scoreItemMapper = ApplicationContextHandle.getBean(SgCChannelSourceStrategyScoreItemMapper.class);
        return scoreItemMapper.queryFactorByShopId(channelSourceStrategy.getId());
    }

    /**
     * 对集合设置优先级
     */
    private void setNewPriority(List<SgWarehouseScoreModel> physicsScoreModelList) {

        FindSourceStrategyUtils.outputLog("SgFindSourceWarehouseScoreStrategyService.setNewPriority param:{}",
                JSONObject.toJSONString(physicsScoreModelList));

        int temp = 10;
        for (int i = 0; i < physicsScoreModelList.size(); i++) {
            if (i == 0) {
                physicsScoreModelList.get(i).setNewPriority(temp);
                continue;
            }
            // 判断当前分数与上一个分数结果，如果相同，存当前temp，如果不同，则 + 10
            boolean isSame = physicsScoreModelList.get(i).getScore().compareTo(physicsScoreModelList.get(i - 1).getScore()) == 0
                    && physicsScoreModelList.get(i).getOldPriority().compareTo(physicsScoreModelList.get(i - 1).getOldPriority()) == 0;
            if (!isSame) {
                temp += 10;
            }
            physicsScoreModelList.get(i).setNewPriority(temp);
        }
    }

    /**
     * 对集合设置优先级
     */
    private void setPriority(List<SgWarehouseScoreModel> SgPhysicsScoreModelList) {
        int temp = 10;
        for (int i = 0; i < SgPhysicsScoreModelList.size(); i++) {
            if (i == 0) {
                SgPhysicsScoreModelList.get(i).setPriority(temp);
                continue;
            }
            temp += 10;
            SgPhysicsScoreModelList.get(i).setPriority(temp);
        }
    }

    /**
     * 根据保底库存量排序、设置优先级
     */
    private void setPriorityByMinimumGuarantee(List<SgWarehouseScoreModel> physicsScoreModelList,
                                               List<SkuItemS2L> skuItems,
                                               List<SgFindSourceStrategySkuS2LResult> skuResultList) {

        // 将物理仓库存放在一个map中：   skuId:storeId : qty
        Map<String, BigDecimal> phyQtyMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

        skuResultList.forEach(skuResult ->
                skuResult.getItemResultList().forEach(item ->
                        phyQtyMap.put(skuResult.getShareItemId() + ":" + item.getStoreId(), item.getQty())));

        // 获取相同优先级的数据，放在sameList 中
        Map<Integer, Map<Long, SgWarehouseScoreModel>> sameMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

        List<SgWarehouseScoreModel> sameList = new ArrayList<>();

        physicsScoreModelList.forEach(x -> {
            Map<Long, SgWarehouseScoreModel> storeMap = sameMap.get(x.getNewPriority());
            if (storeMap == null) {
                storeMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
                storeMap.put(x.getWarehouseId(), x);
                sameMap.put(x.getNewPriority(), storeMap);
                return;
            }
            storeMap.putIfAbsent(x.getWarehouseId(), x);
        });

        sameMap.forEach((k, v) -> {
            if (v.keySet().size() > 1) {
                v.forEach((vk, vv) -> sameList.add(vv));
            }
        });

        // sameList 取出id，根据id 找到库存保底量数据，比对库存保底量，如果超了，则记个flag，如果没超，记个!flag，然后看是否需要对id 进行比对
        Map<Long, Boolean> storeInfoMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

        if (CollectionUtils.isNotEmpty(sameList)) {

            // 将物理仓下的逻辑仓加载到内存中
            CpCStoreMapper storeMapper = ApplicationContextHandle.getBean(CpCStoreMapper.class);
            List<Long> storeIds = sameList.stream().map(SgWarehouseScoreModel::getWarehouseId).collect(Collectors.toList());

            List<SgCpCStore> storeInfos = storeMapper.selectCpCStoreByWarehouseIdList(storeIds);
            Map<Long, List<SgCpCStore>> storeMap = storeInfos.stream().collect(Collectors.groupingBy(SgCpCStore::getCpCPhyWarehouseId));

            // 进行分数的比对，放在storeInfoMap，不满足保底库存，则value 为false
            sameList.forEach(score -> {

                List<SgCpCStore> storeInfo = storeMap.get(score.getWarehouseId());

                AssertUtils.cannot(CollectionUtils.isEmpty(storeInfo),
                        "根据物理仓id 未查询到逻辑仓集合，物理仓id：" + score.getWarehouseId());

                FindSourceStrategyUtils.outputLog(
                        "SgFindSourceWarehouseScoreStrategyService.setPriorityByMinimumGuarantee storeInfo.size:{}, score.storeId:{}",
                        storeInfo.size(), score.getWarehouseId());

                BigDecimal minSumQty = storeInfo.stream().map(
                        x -> x.getMinQty() == null ? BigDecimal.ZERO : x.getMinQty())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                AtomicBoolean checkMinimumGuarantee = new AtomicBoolean(true);

                // 这里是遍历所有的需求商品，从商品中获取shareItemId
                skuItems.forEach(item -> {
                    BigDecimal phyQty = phyQtyMap.get(item.getShareItemId() + ":" + score.getWarehouseId());
                    if (phyQty == null || phyQty.subtract(item.getQtyPreOut()).compareTo(minSumQty) < 0) {
                        checkMinimumGuarantee.set(Boolean.FALSE);
                    }
                });

                storeInfoMap.put(score.getWarehouseId(), checkMinimumGuarantee.get());

            });
        }

        // 将sameList 里面的分数，赋值给SgPhysicsScoreModelList，true：1  false：0
        physicsScoreModelList.forEach(physicsScoreModel -> {

            Boolean checkMinimumGuarantee = storeInfoMap.get(physicsScoreModel.getWarehouseId());
            if (null != checkMinimumGuarantee && checkMinimumGuarantee) {
                physicsScoreModel.setScore(BigDecimal.ONE);
            } else {
                physicsScoreModel.setScore(BigDecimal.ZERO);
            }

        });

        // 排序： 原优先级升序、分数降序、id 升序的顺序排序
        physicsScoreModelList.sort((score1, score2) -> {
            int i = score1.getNewPriority().compareTo(score2.getNewPriority());
            if (i != 0) {
                return i;
            }
            int j = score2.getScore().compareTo(score1.getScore());
            if (j != 0) {
                return j;
            }
            return score1.getWarehouseId().compareTo(score2.getWarehouseId());
        });

        // 赋值priority 字段
        setPriority(physicsScoreModelList);
    }

    /**
     * 获取集合中相同优先级的元素
     */
    private List<SgWarehouseScoreModel> getSameWarehouseScoreModel(List<SgWarehouseScoreModel> warehouseScoreModelList) {
        // 返回优先级重复的店仓
        Map<Integer, List<SgWarehouseScoreModel>> sameScoreMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

        warehouseScoreModelList.forEach(x -> {
            List<SgWarehouseScoreModel> storeList = sameScoreMap.get(x.getOldPriority());
            if (storeList == null) {
                storeList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
                storeList.add(x);
                sameScoreMap.put(x.getOldPriority(), storeList);
                return;
            }
            storeList.add(x);
        });

        List<SgWarehouseScoreModel> sameScoreList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);

        sameScoreMap.forEach((k, v) -> {
            if (v.size() > 1) {
                sameScoreList.addAll(v);
            }
        });
        return sameScoreList;
    }
}
