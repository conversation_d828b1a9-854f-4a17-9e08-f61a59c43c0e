package com.burgeon.r3.sg.sourcing.validate;

import com.burgeon.r3.sg.core.model.table.sourcing.nearexpirydate.SgCStoNearExpiryDateStrategy;
import com.burgeon.r3.sg.sourcing.mapper.nearexpirydate.SgCStoNearExpiryDateStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.dto.nearexpirydate.SgCStoNearExpiryDateStrategyDTO;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SgCStoNearExpiryDateStrategyDeleteValidator extends BaseSingleValidator<SgCStoNearExpiryDateStrategyDTO> {
    @Override
    public String getValidatorMsgName() {
        return "临近大效期寻源设置删除";
    }

    @Override
    public Class<?> getValidatorClass() {
        return this.getClass();
    }

    @Autowired
    private SgCStoNearExpiryDateStrategyMapper mapper;

    @Override
    public ValueHolderV14 validateMainTable(SgCStoNearExpiryDateStrategyDTO mainObject, User loginUser) {
        Long id = mainObject.getId();
        SgCStoNearExpiryDateStrategy sgCStoNearExpiryDateStrategy = mapper.selectById(id);
        if (sgCStoNearExpiryDateStrategy == null) {
            return new ValueHolderV14(ResultCode.FAIL, "当前记录已不存在！");
        }
        return new ValueHolderV14(ResultCode.SUCCESS,"校验通过！");
    }
}
