package com.burgeon.r3.sg.sourcing.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.basic.mapper.SgBSaStoragePreoutFtpMapper;
import com.burgeon.r3.sg.basic.mapper.SgBStorageSharedPreoutFtpMapper;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.basic.model.request.SgStorageQueryRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageQuerySaRequest;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQuerySaResult;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyMapper;
import com.burgeon.r3.sg.channel.model.enumerate.YseNoEnum;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelRatioStrategyQueryInfoResult;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.SaCategoryEnum;
import com.burgeon.r3.sg.core.enums.YesNoEnum;
import com.burgeon.r3.sg.core.model.ext.SgBStorageInclShare;
import com.burgeon.r3.sg.core.model.table.basic.SgBSaStoragePreoutFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorageSharedPreoutFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOut;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOutItem;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOutItemLog;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOut;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItem;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItemLog;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutItemLogMapper;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutItemMapper;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutMapper;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutBillSaveRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutItemLogSaveRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutItemSaveRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutSaveRequest;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutSaveResult;
import com.burgeon.r3.sg.share.services.out.SgBShareOutSaveService;
import com.burgeon.r3.sg.sourcing.model.request.SgDirectOrderStorageOccupyItemRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgDirectOrderStorageOccupyRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgDirectOrderStorageTransItemRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgDirectOrderStorageTransRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgDirectOrderStorageOccupyResult;
import com.burgeon.r3.sg.sourcing.model.result.SgDirectOrderStorageTransResult;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutItemLogMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutMapper;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutItemLogSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutSaveRequest;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutBillSaveResult;
import com.burgeon.r3.sg.store.services.out.SgBStoOutSaveService;
import com.burgeon.r3.sg.store.services.rpc.IpRpcService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2024/11/22 09:59
 * @Description 直发预占单（库存中心）
 */
@Component
@Slf4j
public class SgDirectOrderService {

    @Resource
    private SgBShareOutMapper sgBShareOutMapper;
    @Resource
    private SgBShareOutItemMapper sgBShareOutItemMapper;
    @Resource
    private SgBShareOutItemLogMapper sgBShareOutItemLogMapper;
    @Resource
    private SgBSaStoragePreoutFtpMapper sgBSaStoragePreoutFtpMapper;
    @Resource
    private SgBStoOutMapper sgBStoOutMapper;
    @Resource
    private SgBStoOutItemMapper sgBStoOutItemMapper;
    @Resource
    private SgBStoOutItemLogMapper sgBStoOutItemLogMapper;
    @Resource
    private SgBStorageSharedPreoutFtpMapper sgBStorageSharedPreoutFtpMapper;
    @Resource
    private CpCPhyWarehouseMapper cpCPhyWarehouseMapper;
    @Resource
    private SgCSaStoreMapper sgCSaStoreMapper;
    @Resource
    private CpCStoreMapper cpCStoreMapper;
    @Resource
    private IpRpcService ipRpcService;
    @Resource
    private SgFindSourceRollBackService sgFindSourceRollBackService;


    /**
     * 库存平移（预占单->零售发货单）
     *
     * @param request
     * @return
     */
    public ValueHolderV14<SgDirectOrderStorageTransResult> storageTrans(SgDirectOrderStorageTransRequest request) {
        log.info(LogUtil.format("SgDirectOrderService.storageTrans request:{}",
                "SgDirectOrderService.storageTrans"), JSONObject.toJSONString(request));
        ValueHolderV14<SgDirectOrderStorageTransResult> v14 =
                new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        SgDirectOrderStorageTransResult result = new SgDirectOrderStorageTransResult();
        try {
            //参数校验
            checkParam(request);
            List<SgDirectOrderStorageTransItemRequest> saItemRequestList = request.getItemRequestList();
            //复制一份给逻辑成分配用
            List<SgDirectOrderStorageTransItemRequest> storeItemRequestList =
                    JSONObject.parseArray(JSONObject.toJSONString(saItemRequestList), SgDirectOrderStorageTransItemRequest.class);
            //库存转移
            SgDirectOrderService bean = ApplicationContextHandle.getBean(SgDirectOrderService.class);
            bean.storageTransAndReturnWarehouse(request, saItemRequestList, storeItemRequestList, result);
            v14.setData(result);
        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }

    /**
     * 预占单占用
     *
     * @param request
     * @return
     */
    public ValueHolderV14<SgDirectOrderStorageOccupyResult> storageOccupy(SgDirectOrderStorageOccupyRequest request) {
        log.info(LogUtil.format("SgDirectOrderService.storageOccupy request:{}",
                "SgDirectOrderService.storageOccupy"), JSONObject.toJSONString(request));
        ValueHolderV14<SgDirectOrderStorageOccupyResult> v14 =
                new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        SgDirectOrderStorageOccupyResult result = new SgDirectOrderStorageOccupyResult();
        try {
            //参数校验
            checkParam(request);
            List<SgDirectOrderStorageOccupyItemRequest> saItemRequestList = request.getItemRequestList();
            List<SgDirectOrderStorageOccupyItemRequest> storeItemRequestList =
                    JSONObject.parseArray(JSON.toJSONString(saItemRequestList), SgDirectOrderStorageOccupyItemRequest.class);
            //查询配销仓档案
            SgCSaStore saStore = sgCSaStoreMapper.selectById(request.getSaStoreId());
            if (saStore == null) {
                throw new NDSException("配销仓档案不存在！");
            }
            if (saStore.getSgCShareStoreId() == null) {
                throw new NDSException("配销仓没有所属聚合仓！");
            }
            SgCpCStore store = cpCStoreMapper.queryStoreInfo(request.getStoreId());
            if (store == null) {
                throw new NDSException("逻辑仓档案不存在！");
            }
            if (store.getSgCShareStoreId() == null) {
                throw new NDSException("逻辑仓没有所属聚合仓！");
            }
            if (!saStore.getSgCShareStoreId().equals(store.getSgCShareStoreId())) {
                throw new NDSException("配销仓和逻辑仓不属于同一个聚合仓！");
            }
            //校验比例同步策略和效期范围
            checkExpiryDate(request, saItemRequestList, saStore);
            try {
                //逻辑层库存占用
                occupyStoreStorage(request, store, storeItemRequestList, result);
            } catch (Exception e) {
                throw new NDSException(e.getMessage());
            }
            try {
                //配销层库存占用
                occupySaStoreStorage(request, saStore, saItemRequestList, result);
            } catch (Exception e) {
                //回滚逻辑仓占用
                sgFindSourceRollBackService.stoOutRollBackStorage(request.getSourceBillNo(),
                        request.getSourceBillId(), request.getSourceBillType());
                throw new NDSException(e.getMessage());
            }
            v14.setData(result);
        } catch (Exception e) {
            //回滚逻辑仓占用
            sgFindSourceRollBackService.stoOutRollBackStorage(request.getSourceBillNo(),
                    request.getSourceBillId(), request.getSourceBillType());
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }

    /**
     * 校验比例同步策略和效期范围
     *
     * @param request
     * @param itemRequestList
     * @param saStore
     */
    private void checkExpiryDate(SgDirectOrderStorageOccupyRequest request,
                                 List<SgDirectOrderStorageOccupyItemRequest> itemRequestList,
                                 SgCSaStore saStore) {
        //查询比例同步策略
        SgCChannelRatioStrategyMapper ratioStrategyMapper =
                ApplicationContextHandle.getBean(SgCChannelRatioStrategyMapper.class);
        //查询比例同步策略
        List<SgCChannelRatioStrategyQueryInfoResult> resultList =
                ratioStrategyMapper.queryRatioStrategyByDists(Lists.newArrayList(request.getDistCodeLevel2()));
        if (CollectionUtils.isEmpty(resultList)) {
            throw new NDSException("[" + request.getDistCodeLevel2() + "]对应的【比例同步策略】为空！");
        }
        List<Long> channelRatioSaStoreList = resultList.stream()
                .map(SgCChannelRatioStrategyQueryInfoResult::getSgCSaStoreId).collect(Collectors.toList());
        if (!channelRatioSaStoreList.contains(request.getSaStoreId())) {
            throw new NDSException("[" + request.getDistCodeLevel2() + "]对应的【比例同步策略】不包含所选配销仓！");
        }
        //收集sku
        List<Long> skuIds = itemRequestList.stream()
                .map(SgDirectOrderStorageOccupyItemRequest::getSkuId).distinct().collect(Collectors.toList());
        //查询标准效期定义
        Map<Long, String> validityMap = CommonCacheValUtils.
                queryValidityDefinitionProduceByPsCSkuIdList(skuIds, null);
        //逐行校验
        for (SgDirectOrderStorageOccupyItemRequest itemRequest : itemRequestList) {
            String produceRage = validityMap.get(itemRequest.getSkuId());
            // 无效期范围/无标准效期定义 大效期配销仓
            if (StringUtils.isEmpty(produceRage) || itemRequest.getBeginProduceDate() == null
                    || itemRequest.getEndProduceDate() == null) {
                if (!SaCategoryEnum.BIG_VALIDITY.getCode().equals(saStore.getCategory())) {
                    throw new NDSException("[" + itemRequest.getSkuCode() + "]效期范围与配销仓性质不一致！");
                }
                continue;
            }
            String[] split = produceRage.split(SgConstants.SG_CONNECTOR_MARKS_6);
            String beginValidity = split[0];
            String endValidity = split[1];
            // 效期间无交集，选非大效期配销仓
            if (beginValidity.compareTo(itemRequest.getEndProduceDate()) > 0
                    || endValidity.compareTo(itemRequest.getBeginProduceDate()) < 0) {
                if (SaCategoryEnum.BIG_VALIDITY.getCode().equals(saStore.getCategory())) {
                    throw new NDSException("[" + itemRequest.getSkuCode() + "]效期范围与配销仓性质不一致！");
                }
            } else if (beginValidity.compareTo(itemRequest.getBeginProduceDate()) <= 0
                    && endValidity.compareTo(itemRequest.getEndProduceDate()) >= 0) {
                //指定效期范围为大效期，选大效期配销仓
                if (!SaCategoryEnum.BIG_VALIDITY.getCode().equals(saStore.getCategory())) {
                    throw new NDSException("[" + itemRequest.getSkuCode() + "]效期范围与配销仓性质不一致！");
                }
            } else {
                //效期间有交集
                throw new NDSException("[" + itemRequest.getSkuCode() + "]效期范围与【标准效期定义】存在交集！");
            }
        }
    }

    /**
     * 配销仓占用
     *
     * @param request
     * @param saStore
     * @param itemRequestList
     * @param result
     */
    private void occupySaStoreStorage(SgDirectOrderStorageOccupyRequest request, SgCSaStore saStore,
                                      List<SgDirectOrderStorageOccupyItemRequest> itemRequestList,
                                      SgDirectOrderStorageOccupyResult result) {
        //收集sku和按照sourceItemId分组
        Map<Long, SgDirectOrderStorageOccupyItemRequest> itemRequestMap = new HashMap<>();
        List<Long> skuIds = new ArrayList<>();
        for (SgDirectOrderStorageOccupyItemRequest itemRequest : itemRequestList) {
            itemRequestMap.put(itemRequest.getSourceItemId(), itemRequest);
            if (!skuIds.contains(itemRequest.getSkuId())) {
                skuIds.add(itemRequest.getSkuId());
            }
        }
        //查询配销仓库存
        Map<Long, BigDecimal> saStorageMap = querySaStoreStorage(Lists.newArrayList(request.getSaStoreId()), skuIds);
        //构建占用计划
        //sourceItemId,skuId,qty
        Map<Long, BigDecimal> saOccupyPlanMap = buildSaOccupyPlan(itemRequestList, saStorageMap);
        //构建参数
        SgBShareOutBillSaveRequest saveRequest = buildShareOutRequest(request, saStore, itemRequestMap, saOccupyPlanMap);
        //保存配销占用单
        SgBShareOutSaveService shareOutSaveService = ApplicationContextHandle.getBean(SgBShareOutSaveService.class);
        ValueHolderV14<SgBShareOutSaveResult> holderV14 = shareOutSaveService.redundantSgShareOut(saveRequest);
        if (!holderV14.isOK()) {
            throw new NDSException(holderV14.getMessage());
        }
        //查询配销占用单单号
        if (holderV14.getData() != null && holderV14.getData().getId() != null) {
            SgBShareOut sgBShareOut = sgBShareOutMapper.selectById(holderV14.getData().getId());
            if (sgBShareOut != null) {
                result.setShareOutNo(sgBShareOut.getBillNo());
            }
        }

    }

    private SgBShareOutBillSaveRequest buildShareOutRequest(SgDirectOrderStorageOccupyRequest request,
                                                            SgCSaStore saStore,
                                                            Map<Long, SgDirectOrderStorageOccupyItemRequest> itemRequestMap,
                                                            Map<Long, BigDecimal> saOccupyPlanMap) {
        SgBShareOutBillSaveRequest saveRequest = new SgBShareOutBillSaveRequest();
        saveRequest.setLoginUser(request.getUser());
        //构建配销占用单主表参数
        SgBShareOutSaveRequest shareOutSaveRequest = new SgBShareOutSaveRequest();
        saveRequest.setShareOutSaveRequest(shareOutSaveRequest);
        shareOutSaveRequest.setSgCShareStoreId(saStore.getSgCShareStoreId());
        shareOutSaveRequest.setSourceBillId(request.getSourceBillId());
        shareOutSaveRequest.setSourceBillNo(request.getSourceBillNo());
        shareOutSaveRequest.setSourceBillType(request.getSourceBillType());
        shareOutSaveRequest.setServiceNode(SgConstantsIF.SERVICE_NODE_SHARE_OUT_SAVE);
        shareOutSaveRequest.setSourceBillDate(request.getBillDate());
        shareOutSaveRequest.setBillDate(new Date());
        shareOutSaveRequest.setCpCShopId(request.getShopId());
        shareOutSaveRequest.setTid(request.getTid());
        //构建配销占用单明细参数
        List<SgBShareOutItemSaveRequest> itemList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        saveRequest.setShareOutItemSaveRequestList(itemList);
        //构建配销占用单明细日志参数
        List<SgBShareOutItemLogSaveRequest> itemLogList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        saveRequest.setShareOutItemLogSaveRequestList(itemLogList);
        for (Map.Entry<Long, BigDecimal> entry : saOccupyPlanMap.entrySet()) {
            Long sourceItemId = entry.getKey();
            SgDirectOrderStorageOccupyItemRequest itemRequest = itemRequestMap.get(sourceItemId);
            BigDecimal qty = entry.getValue();
            SgBShareOutItemSaveRequest itemSaveRequest = new SgBShareOutItemSaveRequest();
            itemSaveRequest.setPsCSkuId(itemRequest.getSkuId());
            itemSaveRequest.setQtyPreout(qty);
            itemSaveRequest.setQty(qty);
            itemSaveRequest.setSourceBillItemId(sourceItemId);
            // 记录配销仓占用库存变动参数
            itemSaveRequest.setSgCSaStoreId(request.getSaStoreId());
            itemSaveRequest.setSourceStorage(SgConstants.SHARE_OUT_ITEM_STOCK_SOURCE_SA);
            itemList.add(itemSaveRequest);
            SgBShareOutItemLogSaveRequest itemLogSaveRequest = new SgBShareOutItemLogSaveRequest();
            BeanUtils.copyProperties(itemSaveRequest, itemLogSaveRequest);
            itemLogList.add(itemLogSaveRequest);
        }
        Map<String, SgBShareOutItemSaveRequest> itemMap = new HashMap<>();
        for (SgBShareOutItemSaveRequest itemSaveRequest : itemList) {
            String key = itemSaveRequest.getSgCSaStoreId() +
                    SgConstants.SG_CONNECTOR_MARKS_4 + itemSaveRequest.getPsCSkuId();
            if (itemMap.containsKey(key)) {
                SgBShareOutItemSaveRequest outItemSaveRequest = itemMap.get(key);
                outItemSaveRequest.setQty(outItemSaveRequest.getQty().add(itemSaveRequest.getQty()));
                outItemSaveRequest.setQtyPreout(outItemSaveRequest.getQtyPreout().add(itemSaveRequest.getQtyPreout()));
            } else {
                itemMap.put(key, itemSaveRequest);
            }
        }
        List<SgBShareOutItemSaveRequest> itemInsertList = new ArrayList<>(itemMap.values());
        saveRequest.setShareOutItemSaveRequestList(itemInsertList);
        if (itemInsertList.size() < itemLogList.size()) {
            shareOutSaveRequest.setMergeMark(true);
        } else {
            shareOutSaveRequest.setMergeMark(false);
            saveRequest.setShareOutItemLogSaveRequestList(null);
        }
        return saveRequest;
    }

    /**
     * 构建配销占用计划
     *
     * @param itemRequestList
     * @param saStorageMap
     * @return
     */
    private Map<Long, BigDecimal> buildSaOccupyPlan(List<SgDirectOrderStorageOccupyItemRequest> itemRequestList,
                                                    Map<Long, BigDecimal> saStorageMap) {
        Map<Long, BigDecimal> saOccupyPlanMap = new HashMap<>();
        for (SgDirectOrderStorageOccupyItemRequest itemRequest : itemRequestList) {
            BigDecimal qty = itemRequest.getQty();
            BigDecimal qtyAvailable = saStorageMap.getOrDefault(itemRequest.getSkuId(), BigDecimal.ZERO);
            if (qty.compareTo(qtyAvailable) > 0) {
                throw new NDSException("[" + itemRequest.getSkuCode() + "]配销仓库存不足！");
            }
            saOccupyPlanMap.put(itemRequest.getSourceItemId(), qty);
            saStorageMap.put(itemRequest.getSkuId(), qtyAvailable.subtract(qty));
        }
        return saOccupyPlanMap;
    }

    /**
     * 查询配销仓库存
     *
     * @param saStoreIds
     * @param skuIds
     * @return
     */
    private Map<Long, BigDecimal> querySaStoreStorage(List<Long> saStoreIds, List<Long> skuIds) {
        SgStorageQueryService storageQueryService = ApplicationContextHandle.getBean(SgStorageQueryService.class);
        SgStorageQuerySaRequest sgStorageQuerySaRequest = new SgStorageQuerySaRequest();
        sgStorageQuerySaRequest.setSkuIds(skuIds);
        sgStorageQuerySaRequest.setSgCSaStoreIds(saStoreIds);
        ValueHolderV14<List<SgStorageRedisQuerySaResult>> saStorageResult =
                storageQueryService.querySaStorageWithRedis(sgStorageQuerySaRequest, R3SystemUserResource.getSystemRootUser());
        if (!saStorageResult.isOK()) {
            throw new NDSException("查询配销仓库存失败！");
        }
        List<SgStorageRedisQuerySaResult> storageList = saStorageResult.getData();
        if (CollectionUtils.isEmpty(storageList)) {
            throw new NDSException("查询配销仓库存为空！");
        }
        Map<Long, BigDecimal> saStorageMap = storageList.stream()
                .collect(Collectors.toMap(SgStorageRedisQuerySaResult::getPsCSkuId,
                        SgStorageRedisQuerySaResult::getQtyAvailable));
        return saStorageMap;
    }

    /**
     * 逻辑仓库存占用
     *
     * @param request
     * @param store
     * @param itemRequestList
     * @param result
     */
    private void occupyStoreStorage(SgDirectOrderStorageOccupyRequest request, SgCpCStore store,
                                    List<SgDirectOrderStorageOccupyItemRequest> itemRequestList,
                                    SgDirectOrderStorageOccupyResult result) {
        //收集sku和按照sourceItemId分组
        Map<Long, SgDirectOrderStorageOccupyItemRequest> itemRequestMap = new HashMap<>();
        List<Long> skuIds = new ArrayList<>();
        for (SgDirectOrderStorageOccupyItemRequest itemRequest : itemRequestList) {
            itemRequestMap.put(itemRequest.getSourceItemId(), itemRequest);
            if (!skuIds.contains(itemRequest.getSkuId())) {
                skuIds.add(itemRequest.getSkuId());
            }
        }
        //查询逻辑仓库存
        List<SgBStorageInclShare> storageResultData = queryStoreStorage(Lists.newArrayList(request.getStoreId()), skuIds);
        //WMS冻结库存处理
        List<SgBStorageInclShare> storageResultDataActive =
                frozenInventory(request.getSourceBillNo(), store, skuIds, storageResultData);
        //按照sku分组库存数据
        Map<Long, List<SgBStorageInclShare>> storageResultDataMap = storageResultDataActive.stream()
                .collect(Collectors.groupingBy(
                        SgBStorageInclShare::getPsCSkuId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(SgBStorageInclShare::getProduceDate))
                                        .collect(Collectors.toList())
                        )
                ));
        //构建逻辑仓占用计划
        Map<Long, Map<String, BigDecimal>> occupyPlanMap = buildOccupyPlan(itemRequestList, storageResultDataMap);
        //构建逻辑占用单参数
        SgBStoOutBillSaveRequest sgBStoOutBillSaveRequest = buildStoOutRequest(request, itemRequestMap, occupyPlanMap);

        SgBStoOutSaveService stoOutSaveService = ApplicationContextHandle.getBean(SgBStoOutSaveService.class);
        ValueHolderV14<SgBStoOutBillSaveResult> stoOutResultV14 = stoOutSaveService.saveSgStoOut(sgBStoOutBillSaveRequest);
        if (!stoOutResultV14.isOK()) {
            throw new NDSException(stoOutResultV14.getMessage());
        }
        if (stoOutResultV14.getData() != null && CollectionUtils.isNotEmpty(stoOutResultV14.getData().getBillNo())) {
            result.setStoOutNo(stoOutResultV14.getData().getBillNo().get(0));
        }
    }

    /**
     * 根据占用计划构建逻辑占用单参数
     *
     * @param request
     * @param itemRequestMap
     * @param occupyPlanMap
     * @return
     */
    private SgBStoOutBillSaveRequest buildStoOutRequest(SgDirectOrderStorageOccupyRequest request,
                                                        Map<Long, SgDirectOrderStorageOccupyItemRequest> itemRequestMap,
                                                        Map<Long, Map<String, BigDecimal>> occupyPlanMap) {
        SgBStoOutBillSaveRequest sgBStoOutBillSaveRequest = new SgBStoOutBillSaveRequest();
        sgBStoOutBillSaveRequest.setUpdateMethod(SgConstantsIF.ITEM_UPDATE_TYPE_ALL);
        sgBStoOutBillSaveRequest.setIsEdge(Boolean.TRUE);
        sgBStoOutBillSaveRequest.setIsForceUnNegative(Boolean.TRUE);
        sgBStoOutBillSaveRequest.setLoginUser(request.getUser());
        sgBStoOutBillSaveRequest.setPreoutWarningType(SgConstantsIF.PREOUT_RESULT_ERROR);
        sgBStoOutBillSaveRequest.setIsCancel(Boolean.FALSE);
        sgBStoOutBillSaveRequest.setIsSourceMerge(Boolean.FALSE);
        // 设置逻辑占用单主表信息
        SgBStoOutSaveRequest sgBStoOutSaveRequest = new SgBStoOutSaveRequest();
        sgBStoOutSaveRequest.setSourceBillId(request.getSourceBillId());
        sgBStoOutSaveRequest.setSourceBillNo(request.getSourceBillNo());
        sgBStoOutSaveRequest.setSourceBillType(request.getSourceBillType());
        sgBStoOutSaveRequest.setSourceBillDate(request.getBillDate());
        sgBStoOutSaveRequest.setBillDate(new Date());
        sgBStoOutSaveRequest.setCpCShopId(request.getShopId());
        sgBStoOutSaveRequest.setTid(request.getTid());
        sgBStoOutBillSaveRequest.setSgBStoOutSaveRequest(sgBStoOutSaveRequest);
        //创建逻辑占用单明细信息
        List<SgBStoOutItemSaveRequest> sgBStoOutItemSaveRequests = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        sgBStoOutBillSaveRequest.setSgBStoOutItemSaveRequests(sgBStoOutItemSaveRequests);
        //创建逻辑占用单日志明细信息
        List<SgBStoOutItemLogSaveRequest> sgBStoOutItemLogSaveRequests = new ArrayList<>();
        sgBStoOutBillSaveRequest.setSgBStoOutItemLogSaveRequests(sgBStoOutItemLogSaveRequests);
        for (Map.Entry<Long, Map<String, BigDecimal>> entry : occupyPlanMap.entrySet()) {
            Long sourceItemId = entry.getKey();
            SgDirectOrderStorageOccupyItemRequest occupyItemRequest = itemRequestMap.get(sourceItemId);
            Map<String, BigDecimal> produceDateAndQtyMap = entry.getValue();
            for (Map.Entry<String, BigDecimal> decimalEntry : produceDateAndQtyMap.entrySet()) {
                String produceDate = decimalEntry.getKey();
                BigDecimal qty = decimalEntry.getValue();
                SgBStoOutItemSaveRequest sgBStoOutItemSaveRequest = new SgBStoOutItemSaveRequest();
                sgBStoOutItemSaveRequest.setQty(qty);
                sgBStoOutItemSaveRequest.setProduceDate(produceDate);
                sgBStoOutItemSaveRequest.setQtyPreout(qty);
                sgBStoOutItemSaveRequest.setCpCStoreId(request.getStoreId());
                sgBStoOutItemSaveRequest.setSourceBillItemId(sourceItemId);
                sgBStoOutItemSaveRequest.setPsCSkuId(occupyItemRequest.getSkuId());
                sgBStoOutItemSaveRequest.setTid(request.getTid());
                sgBStoOutItemSaveRequest.setBeginProduceDate(occupyItemRequest.getBeginProduceDate());
                sgBStoOutItemSaveRequest.setEndProduceDate(occupyItemRequest.getEndProduceDate());
                sgBStoOutItemSaveRequests.add(sgBStoOutItemSaveRequest);
                //创建日志明细的request
                SgBStoOutItemLogSaveRequest sgBStoOutItemLogSaveRequest = new SgBStoOutItemLogSaveRequest();
                BeanUtils.copyProperties(sgBStoOutItemSaveRequest, sgBStoOutItemLogSaveRequest);
                sgBStoOutItemLogSaveRequests.add(sgBStoOutItemLogSaveRequest);
            }
        }
        Map<String, SgBStoOutItemSaveRequest> itemSaveRequestMap = new HashMap<>();
        for (SgBStoOutItemSaveRequest itemSaveRequest : sgBStoOutItemSaveRequests) {
            String key = itemSaveRequest.getCpCStoreId() + SgConstants.SG_CONNECTOR_MARKS_4 +
                    itemSaveRequest.getPsCSkuId() + SgConstants.SG_CONNECTOR_MARKS_4 +
                    itemSaveRequest.getProduceDate();
            if (itemSaveRequestMap.containsKey(key)) {
                SgBStoOutItemSaveRequest outItemSaveRequest = itemSaveRequestMap.get(key);
                outItemSaveRequest.setQty(outItemSaveRequest.getQty().add(itemSaveRequest.getQty()));
                outItemSaveRequest.setQtyPreout(outItemSaveRequest.getQtyPreout().add(itemSaveRequest.getQtyPreout()));
            } else {
                itemSaveRequestMap.put(key, itemSaveRequest);
            }
        }
        List<SgBStoOutItemSaveRequest> sgBInsertStoOutItemSaveRequests = new ArrayList<>(itemSaveRequestMap.values());
        sgBStoOutBillSaveRequest.setSgBStoOutItemSaveRequests(sgBInsertStoOutItemSaveRequests);
        if (sgBInsertStoOutItemSaveRequests.size() < sgBStoOutItemLogSaveRequests.size()) {
            sgBStoOutSaveRequest.setMergeMark(true);
        } else {
            sgBStoOutSaveRequest.setMergeMark(false);
            sgBStoOutBillSaveRequest.setSgBStoOutItemLogSaveRequests(null);
        }
        return sgBStoOutBillSaveRequest;
    }

    /**
     * @param itemRequestList
     * @param storageResultDataMap
     * @return
     */
    private Map<Long, Map<String, BigDecimal>> buildOccupyPlan(List<SgDirectOrderStorageOccupyItemRequest> itemRequestList,
                                                               Map<Long, List<SgBStorageInclShare>> storageResultDataMap) {
        Map<Long, Map<String, BigDecimal>> occupyPlanMap = new HashMap<>();
        for (SgDirectOrderStorageOccupyItemRequest itemRequest : itemRequestList) {
            List<SgBStorageInclShare> storageInclShareList = storageResultDataMap.get(itemRequest.getSkuId());
            if (CollectionUtils.isEmpty(storageInclShareList)) {
                throw new NDSException("[" + itemRequest.getSkuCode() + "]逻辑仓库存不足！");
            }
            Map<String, BigDecimal> produceDateAndQty;
            if (occupyPlanMap.containsKey(itemRequest.getSourceItemId())) {
                produceDateAndQty = occupyPlanMap.get(itemRequest.getSourceItemId());
            } else {
                produceDateAndQty = new HashMap<>();
                occupyPlanMap.put(itemRequest.getSourceItemId(), produceDateAndQty);
            }
            for (SgBStorageInclShare inclShare : storageInclShareList) {
                //当前行已经满足了
                BigDecimal qty = itemRequest.getQty();
                if (qty.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }
                //当前库存消耗完
                BigDecimal qtyAvailable = inclShare.getQtyAvailable();
                if (qtyAvailable.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                //效期不符合直接跳过
                if (StringUtils.isNotEmpty(itemRequest.getBeginProduceDate()) &&
                        StringUtils.isNotEmpty(itemRequest.getEndProduceDate())) {
                    String produceDate = inclShare.getProduceDate();
                    if (produceDate.compareTo(itemRequest.getEndProduceDate()) > 0 ||
                            produceDate.compareTo(itemRequest.getBeginProduceDate()) < 0) {
                        continue;
                    }
                }
                if (qty.compareTo(qtyAvailable) >= 0) {
                    produceDateAndQty.put(inclShare.getProduceDate(), qtyAvailable);
                    itemRequest.setQty(itemRequest.getQty().subtract(qtyAvailable));
                    inclShare.setQtyAvailable(BigDecimal.ZERO);
                } else {
                    produceDateAndQty.put(inclShare.getProduceDate(), qty);
                    itemRequest.setQty(BigDecimal.ZERO);
                    inclShare.setQtyAvailable(inclShare.getQtyAvailable().subtract(qty));
                }
            }
            if (itemRequest.getQty().compareTo(BigDecimal.ZERO) > 0) {
                throw new NDSException("[" + itemRequest.getSkuCode() + "]逻辑仓库存不足！");
            }
        }
        return occupyPlanMap;
    }

    private List<SgBStorageInclShare> frozenInventory(String billNo, SgCpCStore store, List<Long> skuIds,
                                                      List<SgBStorageInclShare> storageResultData) {
        Map<String, BigDecimal> inventoryResult =
                ipRpcService.queryInventoryResult(store.getId(), billNo, skuIds);
        List<SgBStorageInclShare> storageResultDataActive = new ArrayList<>();
        for (SgBStorageInclShare inclShare : storageResultData) {
            String key = inclShare.getCpCStoreId() + SgConstants.SG_CONNECTOR_MARKS_4 +
                    inclShare.getPsCSkuId() + SgConstants.SG_CONNECTOR_MARKS_4 + inclShare.getProduceDate();
            BigDecimal lockQty = inventoryResult.getOrDefault(key, BigDecimal.ZERO);
            inclShare.setQtyAvailable(inclShare.getQtyAvailable().subtract(lockQty));
            if (inclShare.getQtyAvailable().compareTo(BigDecimal.ZERO) > 0) {
                storageResultDataActive.add(inclShare);
            }
        }
        if (CollectionUtils.isEmpty(storageResultDataActive)) {
            throw new NDSException("WMS冻结库存处理后逻辑仓库存不足！");
        }
        return storageResultDataActive;
    }

    /**
     * @param saStoreIds
     * @param skuIds
     * @return
     */
    private List<SgBStorageInclShare> queryStoreStorage(List<Long> saStoreIds, List<Long> skuIds) {
        SgStorageQueryRequest sgStorageQueryRequest = new SgStorageQueryRequest();
        sgStorageQueryRequest.setSkuIds(skuIds);
        sgStorageQueryRequest.setStoreIds(saStoreIds);
        SgStorageQueryService sgStorageQueryService = ApplicationContextHandle.getBean(SgStorageQueryService.class);
        ValueHolderV14<List<SgBStorageInclShare>> storageResult =
                sgStorageQueryService.queryStorageInclShareWithRedis(sgStorageQueryRequest, R3SystemUserResource.getSystemRootUser());
        if (!storageResult.isOK()) {
            throw new NDSException("查询逻辑仓失败！");
        }
        List<SgBStorageInclShare> storageResultData = storageResult.getData();
        if (CollectionUtils.isEmpty(storageResultData)) {
            throw new NDSException("查询逻辑仓库存为空！");
        }
        return storageResultData;
    }

    /**
     * 库存平移
     *
     * @param request
     * @param saItemRequestList
     * @param storeItemRequestList
     * @param result
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void storageTransAndReturnWarehouse(SgDirectOrderStorageTransRequest request,
                                               List<SgDirectOrderStorageTransItemRequest> saItemRequestList,
                                               List<SgDirectOrderStorageTransItemRequest> storeItemRequestList,
                                               SgDirectOrderStorageTransResult result) {
        //配销仓库存转移
        saTrans(request, saItemRequestList, result, request.getUser());
        //逻辑仓库存转移&&返回逻辑仓id
        storeTrans(request, storeItemRequestList, result, request.getUser());
    }

    /**
     * 逻辑仓库存转移
     *
     * @param request
     * @param storeItemRequestList
     * @param result
     * @param user
     */
    private void storeTrans(SgDirectOrderStorageTransRequest request,
                            List<SgDirectOrderStorageTransItemRequest> storeItemRequestList,
                            SgDirectOrderStorageTransResult result, User user) {
        // 根据 psCSkuId 分组，并在组内根据 beginProduceDate升序排序
        Map<Long, List<SgDirectOrderStorageTransItemRequest>> itemRequestMap = groupAndSort(storeItemRequestList);
        //计算订单总数量和skuId、skuCode映射关系
        BigDecimal orderTotQty = BigDecimal.ZERO;
        Map<Long, String> skuInfoMap = new HashMap<>();
        //订单总数量
        for (SgDirectOrderStorageTransItemRequest itemRequest : storeItemRequestList) {
            orderTotQty = orderTotQty.add(itemRequest.getQty());
            skuInfoMap.put(itemRequest.getPsCSkuId(), itemRequest.getPsCSkuCode());
        }
        //查询预占单逻辑占用单
        SgBStoOut oldDirectStoOut = queryOldDirectStoOut(request, orderTotQty);
        //查询预占单老逻辑占用单明细
        Map<Long, List<SgBStoOutItem>> oldDirectStoOutItemMap = queryOldDirectStoOutItem(oldDirectStoOut, result);
        //取sku并集
        Set<Long> skuIds = Stream.concat(
                itemRequestMap.keySet().stream(),
                oldDirectStoOutItemMap.keySet().stream()
        ).collect(Collectors.toSet());
        //如果刚好正好履约就无需新生成，直接更新老的数量就行
        SgBStoOut newDirectStoOut = new SgBStoOut();
        //仅后面修改占用总数
        BeanUtils.copyProperties(oldDirectStoOut, newDirectStoOut);
        newDirectStoOut.setBillDate(new Date());
        List<SgBStoOutItem> newDirectStoOutItemList = new ArrayList<>();
        //订单的逻辑占用单
        SgBStoOut orderStoOut = new SgBStoOut();
        BeanUtils.copyProperties(oldDirectStoOut, orderStoOut);
        orderStoOut.setSourceBillId(request.getOrderId());
        orderStoOut.setSourceBillNo(request.getOrderNo());
        orderStoOut.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        orderStoOut.setSourceBillDate(request.getOrderBillDate());
        orderStoOut.setTid(request.getOrderTid());
        orderStoOut.setBillDate(new Date());
        orderStoOut.setCpCShopId(request.getShopId());
        List<SgBStoOutItem> orderStoOutItemList = new ArrayList<>();
        List<SgBStoOutItemLog> orderStoOutItemLogList = new ArrayList<>();
        //按照sku分配占用库存
        transStoreStorage(itemRequestMap, skuInfoMap, oldDirectStoOutItemMap, skuIds,
                newDirectStoOutItemList, orderStoOutItemList, orderStoOutItemLogList);
        //查询预占单配销占用流水
        List<SgBStorageSharedPreoutFtp> oldDirectStorageSharePreoutFtps = queryOldDirectStorageSharePreoutFtp(oldDirectStoOut);
        //构建反向流水
        List<SgBStorageSharedPreoutFtp> oldDirectNegateStorageSharePreoutFtps = new ArrayList<>();
        Map<String, SgBStorageSharedPreoutFtp> storageSharePreoutFtpMap = new HashMap<>();
        buildOldDirectNegateStorageSharePreoutFtp(user, oldDirectStorageSharePreoutFtps,
                oldDirectNegateStorageSharePreoutFtps, storageSharePreoutFtpMap);
        //生成订单的逻辑占用单及流水
        saveOrderStoOut(user, orderStoOut, orderStoOutItemList, orderStoOutItemLogList, storageSharePreoutFtpMap, result);
        //如果部分履约生成新的逻辑占用单及新的流水
        saveNewDirectStoOut(result, user, newDirectStoOut, newDirectStoOutItemList, storageSharePreoutFtpMap);
        //作废预占单的老逻辑占用
        oldDirectStoOut.setTotQtyPreout(BigDecimal.ZERO);
        oldDirectStoOut.setBillStatus(SgStoreConstants.BILL_STO_OUT_STATUS_CANCELED);
        oldDirectStoOut.setIsactive(YesNoEnum.N.getKey());
        Long loginUserId = user.getId() == null ? null : user.getId().longValue();
        oldDirectStoOut.setDelerId(loginUserId);
        oldDirectStoOut.setDelerName(user.getName());
        oldDirectStoOut.setDelTime(new Date());
        StorageUtils.setBModelDefalutDataByUpdate(oldDirectStoOut, user);
        sgBStoOutMapper.updateById(oldDirectStoOut);
        SgBStoOutItem oldUpdateItem = new SgBStoOutItem();
        oldUpdateItem.setQtyPreout(BigDecimal.ZERO);
        StorageUtils.setBModelDefalutDataByUpdate(oldUpdateItem, user);
        sgBStoOutItemMapper.update(oldUpdateItem, new LambdaQueryWrapper<SgBStoOutItem>()
                .eq(SgBStoOutItem::getSgBStoOutId, oldDirectStoOut.getId()));
        //插入原占用单反向流水
        sgBStorageSharedPreoutFtpMapper.batchInsert(oldDirectNegateStorageSharePreoutFtps);
    }

    /**
     * 保存预占单新的逻辑占用单
     *
     * @param result
     * @param user
     * @param newDirectStoOut
     * @param newDirectStoOutItemList
     * @param storageSharePreoutFtpMap
     */
    private void saveNewDirectStoOut(SgDirectOrderStorageTransResult result, User user, SgBStoOut newDirectStoOut,
                                     List<SgBStoOutItem> newDirectStoOutItemList,
                                     Map<String, SgBStorageSharedPreoutFtp> storageSharePreoutFtpMap) {
        BigDecimal newDirectPreQty = newDirectStoOutItemList.stream()
                .map(SgBStoOutItem::getQtyPreout).reduce(BigDecimal.ZERO, BigDecimal::add);
        boolean isAllPre = true;
        if (newDirectPreQty.compareTo(BigDecimal.ZERO) > 0) {
            isAllPre = false;
        }
        if (!isAllPre) {
            //理论上新的逻辑占用明细已经是最细力度的，不需要再合并了
            //生成订单的逻辑占用单及流水
            Long newDirectStoOutId = ModelUtil.getSequence(SgConstants.SG_B_STO_OUT);
            newDirectStoOut.setId(newDirectStoOutId);
            String newDirectStoBillNo = SgStoreUtils.getBillNo(SgStoreConstants.SEQ_STO_OUT,
                    SgConstants.SG_B_STO_OUT.toUpperCase().toUpperCase(), newDirectStoOut, user.getLocale());
            result.setStoOutBillNo(newDirectStoBillNo);
            newDirectStoOut.setBillNo(newDirectStoBillNo);
            StorageUtils.setBModelDefalutData(newDirectStoOut, user);
            newDirectStoOut.setTotQtyPreout(newDirectPreQty);
            newDirectStoOut.setMergeMark(false);
            for (SgBStoOutItem sgBStoOutItem : newDirectStoOutItemList) {
                sgBStoOutItem.setId(ModelUtil.getSequence(SgConstants.SG_B_STO_OUT_ITEM));
                sgBStoOutItem.setSgBStoOutId(newDirectStoOutId);
                StorageUtils.setBModelDefalutData(sgBStoOutItem, user);
            }
            List<SgBStorageSharedPreoutFtp> newDirectStoStoragePreoutFtps = new ArrayList<>();
            for (SgBStoOutItem stoOutItem : newDirectStoOutItemList) {
                if (stoOutItem.getQtyPreout().compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                String key = stoOutItem.getPsCSkuId() + SgConstants.SG_CONNECTOR_MARKS_4 + stoOutItem.getProduceDate();
                SgBStorageSharedPreoutFtp sgBStorageSharedPreoutFtp = storageSharePreoutFtpMap.get(key);
                SgBStorageSharedPreoutFtp storageSharedPreoutFtp = new SgBStorageSharedPreoutFtp();
                BeanUtils.copyProperties(sgBStorageSharedPreoutFtp, storageSharedPreoutFtp);
                BigDecimal qtyEnd = sgBStorageSharedPreoutFtp.getQtyEnd();
                BigDecimal qtyBegin = sgBStorageSharedPreoutFtp.getQtyBegin();
                storageSharedPreoutFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SA_STORAGE_PREOUT_FTP));
                storageSharedPreoutFtp.setQtyChange(stoOutItem.getQtyPreout());
                storageSharedPreoutFtp.setQtyBegin(qtyEnd);
                storageSharedPreoutFtp.setQtyEnd(qtyBegin);
                storageSharedPreoutFtp.setRemark("更新库存单据来源信息");
                storageSharedPreoutFtp.setBillId(newDirectStoOut.getId());
                storageSharedPreoutFtp.setBillNo(newDirectStoOut.getBillNo());
                storageSharedPreoutFtp.setBillDate(newDirectStoOut.getBillDate());
                storageSharedPreoutFtp.setBillItemId(stoOutItem.getId());
                storageSharedPreoutFtp.setServiceNode(SgConstantsIF.SERVICE_NODE_STO_OUT_SAVE);
                storageSharedPreoutFtp.setSourceBillId(newDirectStoOut.getSourceBillId());
                storageSharedPreoutFtp.setSourceBillNo(newDirectStoOut.getSourceBillNo());
                storageSharedPreoutFtp.setChangeDate(new Date());
                StorageUtils.setBModelDefalutData(storageSharedPreoutFtp, user);
                newDirectStoStoragePreoutFtps.add(storageSharedPreoutFtp);
            }
            sgBStoOutMapper.insert(newDirectStoOut);
            sgBStoOutItemMapper.batchInsert(newDirectStoOutItemList);
            sgBStorageSharedPreoutFtpMapper.batchInsert(newDirectStoStoragePreoutFtps);
        }
    }

    /**
     * 订单逻辑占用单保存
     *
     * @param user
     * @param orderStoOut
     * @param orderStoOutItemList
     * @param orderStoOutItemLogList
     * @param storageSharePreoutFtpMap
     * @param result
     */
    private void saveOrderStoOut(User user, SgBStoOut orderStoOut, List<SgBStoOutItem> orderStoOutItemList,
                                 List<SgBStoOutItemLog> orderStoOutItemLogList,
                                 Map<String, SgBStorageSharedPreoutFtp> storageSharePreoutFtpMap,
                                 SgDirectOrderStorageTransResult result) {
        Long orderStoOutId = ModelUtil.getSequence(SgConstants.SG_B_STO_OUT);
        orderStoOut.setId(orderStoOutId);
        String orderStoBillNo = SgStoreUtils.getBillNo(SgStoreConstants.SEQ_STO_OUT,
                SgConstants.SG_B_STO_OUT.toUpperCase().toUpperCase(), orderStoOut, user.getLocale());
        result.setOrderStoOutBillNo(orderStoBillNo);
        orderStoOut.setBillNo(orderStoBillNo);
        StorageUtils.setBModelDefalutData(orderStoOut, user);
        Map<String, SgBStoOutItem> orderStoOutItemMap = new HashMap<>();
        BigDecimal orderPreTotQty = BigDecimal.ZERO;
        BigDecimal orderPreTotPreQty = BigDecimal.ZERO;
        for (SgBStoOutItem sgBStoOutItem : orderStoOutItemList) {
            orderPreTotQty = orderPreTotQty.add(sgBStoOutItem.getQty());
            orderPreTotPreQty = orderPreTotPreQty.add(sgBStoOutItem.getQtyPreout());
            String key = sgBStoOutItem.getCpCStoreId() +
                    SgConstants.SG_CONNECTOR_MARKS_4 + sgBStoOutItem.getPsCSkuId() +
                    SgConstants.SG_CONNECTOR_MARKS_4 + sgBStoOutItem.getProduceDate();
            if (!orderStoOutItemMap.containsKey(key)) {
                sgBStoOutItem.setId(ModelUtil.getSequence(SgConstants.SG_B_STO_OUT_ITEM));
                sgBStoOutItem.setSgBStoOutId(orderStoOutId);
                sgBStoOutItem.setTid(orderStoOut.getTid());
                StorageUtils.setBModelDefalutData(sgBStoOutItem, user);
                orderStoOutItemMap.put(key, sgBStoOutItem);
            } else {
                SgBStoOutItem stoOutItem = orderStoOutItemMap.get(key);
                stoOutItem.setQty(stoOutItem.getQty().add(sgBStoOutItem.getQty()));
                stoOutItem.setQtyPreout(stoOutItem.getQtyPreout().add(sgBStoOutItem.getQtyPreout()));
            }
        }
        orderStoOut.setTotQtyOrign(orderPreTotQty);
        orderStoOut.setTotQtyPreout(orderPreTotPreQty);
        Collection<SgBStoOutItem> orderInsertStoOutItemList = orderStoOutItemMap.values();
        if (orderInsertStoOutItemList.size() < orderStoOutItemLogList.size()) {
            orderStoOut.setMergeMark(true);
        } else {
            orderStoOut.setMergeMark(false);
        }
        orderStoOut.setTotRowNum(orderInsertStoOutItemList.size());
        List<SgBStorageSharedPreoutFtp> orderStoStoragePreoutFtps = new ArrayList<>();
        for (SgBStoOutItem stoOutItem : orderInsertStoOutItemList) {
            String key = stoOutItem.getPsCSkuId() + SgConstants.SG_CONNECTOR_MARKS_4 + stoOutItem.getProduceDate();
            SgBStorageSharedPreoutFtp sgBStorageSharedPreoutFtp = storageSharePreoutFtpMap.get(key);
            SgBStorageSharedPreoutFtp stoPreoutFtp = new SgBStorageSharedPreoutFtp();
            BeanUtils.copyProperties(sgBStorageSharedPreoutFtp, stoPreoutFtp);
            BigDecimal qtyEnd = sgBStorageSharedPreoutFtp.getQtyEnd();
            BigDecimal qtyBegin = sgBStorageSharedPreoutFtp.getQtyBegin();
            stoPreoutFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_STORAGE_SHARED_PREOUT_FTP));
            stoPreoutFtp.setQtyChange(stoOutItem.getQtyPreout());
            stoPreoutFtp.setQtyBegin(qtyEnd);
            stoPreoutFtp.setQtyEnd(qtyBegin);
            stoPreoutFtp.setRemark("更新库存单据来源信息");
            stoPreoutFtp.setBillId(orderStoOut.getId());
            stoPreoutFtp.setBillNo(orderStoOut.getBillNo());
            stoPreoutFtp.setBillDate(orderStoOut.getBillDate());
            stoPreoutFtp.setBillType(SgConstantsIF.BILL_TYPE_RETAIL);
            stoPreoutFtp.setBillItemId(stoOutItem.getId());
            stoPreoutFtp.setServiceNode(SgConstantsIF.SERVICE_NODE_STO_OUT_SAVE);
            stoPreoutFtp.setSourceBillId(orderStoOut.getSourceBillId());
            stoPreoutFtp.setSourceBillNo(orderStoOut.getSourceBillNo());
            stoPreoutFtp.setChangeDate(new Date());
            StorageUtils.setBModelDefalutData(stoPreoutFtp, user);
            orderStoStoragePreoutFtps.add(stoPreoutFtp);
        }
        sgBStoOutMapper.insert(orderStoOut);
        sgBStoOutItemMapper.batchInsert(orderInsertStoOutItemList);
        sgBStorageSharedPreoutFtpMapper.batchInsert(orderStoStoragePreoutFtps);
        if (orderStoOut.getMergeMark()) {
            for (SgBStoOutItemLog sgBShareOutItemLog : orderStoOutItemLogList) {
                sgBShareOutItemLog.setId(ModelUtil.getSequence(SgConstants.SG_B_STO_OUT_ITEM_LOG));
                sgBShareOutItemLog.setSgBStoOutId(orderStoOutId);
                sgBShareOutItemLog.setTid(orderStoOut.getTid());
                StorageUtils.setBModelDefalutData(sgBShareOutItemLog, user);
            }
            sgBStoOutItemLogMapper.batchInsert(orderStoOutItemLogList);
        }
    }

    /**
     * 构建反向流水
     *
     * @param user
     * @param oldDirectStorageSharePreoutFtps
     * @param oldDirectNegateStorageSharePreoutFtps
     * @param storageSharePreoutFtpMap
     */
    private void buildOldDirectNegateStorageSharePreoutFtp(User user,
                                                           List<SgBStorageSharedPreoutFtp> oldDirectStorageSharePreoutFtps,
                                                           List<SgBStorageSharedPreoutFtp> oldDirectNegateStorageSharePreoutFtps,
                                                           Map<String, SgBStorageSharedPreoutFtp> storageSharePreoutFtpMap) {
        for (SgBStorageSharedPreoutFtp sgBStorageSharedPreoutFtp : oldDirectStorageSharePreoutFtps) {
            SgBStorageSharedPreoutFtp sharedPreoutFtp = new SgBStorageSharedPreoutFtp();
            BeanUtils.copyProperties(sgBStorageSharedPreoutFtp, sharedPreoutFtp);
            BigDecimal qtyEnd = sgBStorageSharedPreoutFtp.getQtyEnd();
            BigDecimal qtyBegin = sgBStorageSharedPreoutFtp.getQtyBegin();
            BigDecimal qtyChange = sgBStorageSharedPreoutFtp.getQtyChange();
            sharedPreoutFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_STORAGE_SHARED_PREOUT_FTP));
            sharedPreoutFtp.setQtyChange(qtyChange.negate());
            sharedPreoutFtp.setQtyBegin(qtyEnd);
            sharedPreoutFtp.setQtyEnd(qtyBegin);
            sharedPreoutFtp.setRemark("更新库存单据来源信息");
            sharedPreoutFtp.setChangeDate(new Date());
            sharedPreoutFtp.setServiceNode(SgConstantsIF.SERVICE_NODE_STO_OUT_VOID);
            StorageUtils.setBModelDefalutData(sharedPreoutFtp, user);
            oldDirectNegateStorageSharePreoutFtps.add(sharedPreoutFtp);
            //收集用于后面给订单和新预占逻辑占用的流水
            String key = sgBStorageSharedPreoutFtp.getPsCSkuId() + SgConstants.SG_CONNECTOR_MARKS_4 + sgBStorageSharedPreoutFtp.getProduceDate();
            if (!storageSharePreoutFtpMap.containsKey(key)) {
                storageSharePreoutFtpMap.put(key, sgBStorageSharedPreoutFtp);
            }
        }
    }

    /**
     * 查询预占单逻辑仓占用流水
     *
     * @param oldDirectStoOut
     * @return
     */
    private List<SgBStorageSharedPreoutFtp> queryOldDirectStorageSharePreoutFtp(SgBStoOut oldDirectStoOut) {
        List<SgBStorageSharedPreoutFtp> oldDirectStorageSharePreoutFtps =
                sgBStorageSharedPreoutFtpMapper.selectListMaster(new LambdaQueryWrapper<SgBStorageSharedPreoutFtp>()
                        .in(SgBStorageSharedPreoutFtp::getBillId, oldDirectStoOut.getId())
                        .eq(SgBStorageSharedPreoutFtp::getBillType, SgConstantsIF.BILL_TYPE_DIRECT_ORDER));
        BigDecimal storageShareFtpTotQty = oldDirectStorageSharePreoutFtps.stream()
                .map(SgBStorageSharedPreoutFtp::getQtyChange).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (oldDirectStoOut.getTotQtyPreout().compareTo(storageShareFtpTotQty) != 0) {
            throw new NDSException("预占单流水和逻辑占用单的占用数量不一致！");
        }
        return oldDirectStorageSharePreoutFtps;
    }

    /**
     * 分配逻辑仓库存
     *
     * @param itemRequestMap
     * @param skuInfoMap
     * @param oldDirectStoOutItemMap
     * @param skuIds
     * @param newDirectStoOutItemList
     * @param orderStoOutItemList
     * @param orderStoOutItemLogList
     */
    private void transStoreStorage(Map<Long, List<SgDirectOrderStorageTransItemRequest>> itemRequestMap,
                                   Map<Long, String> skuInfoMap, Map<Long, List<SgBStoOutItem>> oldDirectStoOutItemMap,
                                   Set<Long> skuIds, List<SgBStoOutItem> newDirectStoOutItemList,
                                   List<SgBStoOutItem> orderStoOutItemList, List<SgBStoOutItemLog> orderStoOutItemLogList) {
        for (Long skuId : skuIds) {
            List<SgBStoOutItem> sgBStoOutItems = oldDirectStoOutItemMap.get(skuId);
            if (CollectionUtils.isEmpty(sgBStoOutItems)) {
                throw new NDSException("[" + skuInfoMap.getOrDefault(skuId, "") + "]不存在逻辑占用明细！");
            }
            //订单明细
            List<SgDirectOrderStorageTransItemRequest> itemRequests = itemRequestMap.get(skuId);
            if (CollectionUtils.isEmpty(itemRequests)) {
                //订单没有占用直接处理逻辑仓明细，直接复制给直发新生成的
                newDirectStoOutItemList.addAll(sgBStoOutItems);
                continue;
            }
            //比较需求量和占用量是否
            BigDecimal itemRequestQty = itemRequests.stream()
                    .map(SgDirectOrderStorageTransItemRequest::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal preQty = sgBStoOutItems.stream()
                    .map(SgBStoOutItem::getQtyPreout).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (itemRequestQty.compareTo(preQty) > 0) {
                throw new NDSException("[" + skuInfoMap.getOrDefault(skuId, "")
                        + "]订单数量大于逻辑占用明细总数量！");
            }
            //到这说明该sku有需求且肯定能够满足
            for (SgBStoOutItem sgBStoOutItem : sgBStoOutItems) {
                BigDecimal qtyPreout = sgBStoOutItem.getQtyPreout();
                //如果已经分配完了就直接保存到新的预占单的逻辑占用明细
                if (qtyPreout.compareTo(BigDecimal.ZERO) <= 0) {
                    newDirectStoOutItemList.add(sgBStoOutItem);
                    continue;
                }
                //逐行分配
                for (SgDirectOrderStorageTransItemRequest itemRequest : itemRequests) {
                    if (qtyPreout.compareTo(BigDecimal.ZERO) <= 0) {
                        //说明当前行已经分配完了
                        break;
                    }
                    BigDecimal qty = itemRequest.getQty();
                    if (qty.compareTo(BigDecimal.ZERO) <= 0) {
                        //说明前面满足完了
                        continue;
                    }
                    BigDecimal orderPreQty = BigDecimal.ZERO;
                    //不够满足,清零占用明细给到新逻辑占用单,生成零售发货单的占用明细
                    if (qty.compareTo(qtyPreout) >= 0) {
                        orderPreQty = orderPreQty.add(qtyPreout);
                        //处理订单明细数量，下次再满足
                        itemRequest.setQty(qty.subtract(qtyPreout));
                        qtyPreout = BigDecimal.ZERO;
                    } else {
                        orderPreQty = orderPreQty.add(qty);
                        //处理原占用明细，下次再消耗
                        qtyPreout = qtyPreout.subtract(qty);
                        itemRequest.setQty(BigDecimal.ZERO);
                    }
                    SgBStoOutItem orderStoOutItem = new SgBStoOutItem();
                    SgBStoOutItemLog orderStoOutItemLog = new SgBStoOutItemLog();
                    BeanUtils.copyProperties(sgBStoOutItem, orderStoOutItem);
                    BeanUtils.copyProperties(sgBStoOutItem, orderStoOutItemLog);
                    orderStoOutItemList.add(orderStoOutItem);
                    orderStoOutItemLogList.add(orderStoOutItemLog);
                    orderStoOutItem.setQty(orderPreQty);
                    orderStoOutItemLog.setQty(orderPreQty);
                    orderStoOutItem.setQtyPreout(orderPreQty);
                    orderStoOutItemLog.setQtyPreout(orderPreQty);
                    orderStoOutItem.setSourceBillItemId(itemRequest.getOrderItemId());
                    orderStoOutItemLog.setSourceBillItemId(itemRequest.getOrderItemId());
                }
                sgBStoOutItem.setQtyPreout(qtyPreout);
                newDirectStoOutItemList.add(sgBStoOutItem);
            }
        }
    }

    /**
     * 查询逻辑占用单明细及实体仓档案
     *
     * @param oldDirectStoOut
     * @param result
     * @return
     */
    private Map<Long, List<SgBStoOutItem>> queryOldDirectStoOutItem(SgBStoOut oldDirectStoOut,
                                                                    SgDirectOrderStorageTransResult result) {
        List<SgBStoOutItem> oldDirectStoOutItemList = sgBStoOutItemMapper.selectList(new LambdaQueryWrapper<SgBStoOutItem>()
                .eq(SgBStoOutItem::getSgBStoOutId, oldDirectStoOut.getId())
                .eq(SgBStoOutItem::getIsactive, YseNoEnum.YES.getCode()));
        if (CollectionUtils.isEmpty(oldDirectStoOutItemList)) {
            throw new NDSException("无有效状态逻辑占用单明细!");
        }
        //查询实体仓档案
        Long storeId = oldDirectStoOutItemList.get(0).getCpCStoreId();
        SgCpCPhyWarehouse warehouse = cpCPhyWarehouseMapper.selectByStoreId(storeId);
        result.setWarehouseId(warehouse.getId());
        result.setWarehouseCode(warehouse.getEcode());
        result.setWarehouseName(warehouse.getEname());
        return oldDirectStoOutItemList.stream()
                .collect(Collectors.groupingBy(
                        SgBStoOutItem::getPsCSkuId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(SgBStoOutItem::getProduceDate))
                                        .collect(Collectors.toList())
                        )
                ));
    }

    private SgBStoOut queryOldDirectStoOut(SgDirectOrderStorageTransRequest request, BigDecimal orderTotQty) {
        Long sourceBillId = request.getSourceBillId();
        Integer sourceBillType = request.getSourceBillType();
        SgBStoOut oldDirectStoOut = sgBStoOutMapper.selectOne(new LambdaQueryWrapper<SgBStoOut>()
                .eq(SgBStoOut::getSourceBillId, sourceBillId)
                .eq(SgBStoOut::getSourceBillType, sourceBillType)
                .eq(SgBStoOut::getIsactive, YseNoEnum.YES.getCode()));
        if (oldDirectStoOut == null) {
            throw new NDSException("无有效状态逻辑占用单!");
        }
        if (orderTotQty.compareTo(oldDirectStoOut.getTotQtyPreout()) > 0) {
            throw new NDSException("订单总数量大于逻辑占用量!");
        }
        return oldDirectStoOut;
    }

    /**
     * 配销仓库存平移
     *
     * @param request
     * @param saItemRequestList
     * @param result
     */
    private void saTrans(SgDirectOrderStorageTransRequest request,
                         List<SgDirectOrderStorageTransItemRequest> saItemRequestList,
                         SgDirectOrderStorageTransResult result, User user) {
        // 根据 psCSkuId 分组，并在组内根据 beginProduceDate升序排序
        Map<Long, List<SgDirectOrderStorageTransItemRequest>> itemRequestMap = groupAndSort(saItemRequestList);
        //计算订单总数量和skuId、skuCode映射关系
        BigDecimal orderTotQty = BigDecimal.ZERO;
        Map<Long, String> skuInfoMap = new HashMap<>();
        //订单总数量
        for (SgDirectOrderStorageTransItemRequest itemRequest : saItemRequestList) {
            orderTotQty = orderTotQty.add(itemRequest.getQty());
            skuInfoMap.put(itemRequest.getPsCSkuId(), itemRequest.getPsCSkuCode());
        }
        //查询预占单老配销占用单
        SgBShareOut oldDirectShareOut = queryOldDirectShareOut(request, orderTotQty);
        //查询预占单老配销占用单明细
        Map<Long, List<SgBShareOutItem>> oldDirectShareOutItemMap = queryOldDirectShareOutItem(oldDirectShareOut);
        //取sku并集
        Set<Long> skuIds = Stream.concat(
                itemRequestMap.keySet().stream(),
                oldDirectShareOutItemMap.keySet().stream()
        ).collect(Collectors.toSet());
        //预占单新配销占用单（如果刚好正好履约就无需新生成，直接更新老的数量就行）
        SgBShareOut newDirectShareOut = new SgBShareOut();
        BeanUtils.copyProperties(oldDirectShareOut, newDirectShareOut);
        newDirectShareOut.setBillDate(new Date());
        List<SgBShareOutItem> newDirectShareOutItemList = new ArrayList<>();
        //订单的配销占用单
        SgBShareOut orderShareOut = new SgBShareOut();
        BeanUtils.copyProperties(oldDirectShareOut, orderShareOut);
        orderShareOut.setSourceBillId(request.getOrderId());
        orderShareOut.setSourceBillNo(request.getOrderNo());
        orderShareOut.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        orderShareOut.setSourceBillDate(request.getOrderBillDate());
        orderShareOut.setTid(request.getOrderTid());
        orderShareOut.setBillDate(new Date());
        orderShareOut.setCpCShopId(request.getShopId());
        List<SgBShareOutItem> orderShareOutItemList = new ArrayList<>();
        List<SgBShareOutItemLog> orderShareOutItemLogList = new ArrayList<>();
        //按照sku分配占用库存
        transSaStorage(itemRequestMap, skuInfoMap, oldDirectShareOutItemMap,
                skuIds, newDirectShareOutItemList, orderShareOutItemList, orderShareOutItemLogList);
        //构建占用流水参数
        List<SgBSaStoragePreoutFtp> oldDirectSaStoragePreoutFtps = queryOldDirectSaStoragePreoutFtp(oldDirectShareOut);
        //构建预占单反向流水
        List<SgBSaStoragePreoutFtp> oldNegateDirectSaStoragePreoutFtps = new ArrayList<>();
        Map<Long, SgBSaStoragePreoutFtp> saStoragePreoutFtpMap = new HashMap<>();
        buildOldNegateDirectSaStoragePreoutFtp(user, oldDirectSaStoragePreoutFtps,
                oldNegateDirectSaStoragePreoutFtps, saStoragePreoutFtpMap);
        //生成订单的配销占用单及流水
        saveOrderShareOut(user, orderShareOut, orderShareOutItemList, orderShareOutItemLogList, saStoragePreoutFtpMap, result);
        //如果部分履约生成新的配销占用单及新的流水
        saveNewDirectShareOut(result, user, newDirectShareOut, newDirectShareOutItemList, saStoragePreoutFtpMap);
        //作废预占单的老配销占用
        oldDirectShareOut.setTotQtyPreout(BigDecimal.ZERO);
        oldDirectShareOut.setBillStatus(SgShareConstants.SHARE_OUT_BILL_STATUS_VOID);
        oldDirectShareOut.setIsactive(YesNoEnum.N.getKey());
        Long loginUserId = user.getId() == null ? null : user.getId().longValue();
        oldDirectShareOut.setDelerId(loginUserId);
        oldDirectShareOut.setDelerName(user.getName());
        oldDirectShareOut.setDelTime(new Date());
        StorageUtils.setBModelDefalutDataByUpdate(oldDirectShareOut, user);
        sgBShareOutMapper.updateById(oldDirectShareOut);
        SgBShareOutItem oldUpdateItem = new SgBShareOutItem();
        oldUpdateItem.setQtyPreout(BigDecimal.ZERO);
        StorageUtils.setBModelDefalutDataByUpdate(oldUpdateItem, user);
        sgBShareOutItemMapper.update(oldUpdateItem, new LambdaQueryWrapper<SgBShareOutItem>()
                .eq(SgBShareOutItem::getSgBShareOutId, oldDirectShareOut.getId()));
        //插入原占用单反向流水
        sgBSaStoragePreoutFtpMapper.batchInsert(oldNegateDirectSaStoragePreoutFtps);
    }

    /**
     * 保存预占单新的配销占用单
     *
     * @param result
     * @param user
     * @param newDirectShareOut
     * @param newDirectShareOutItemList
     * @param saStoragePreoutFtpMap
     */
    private void saveNewDirectShareOut(SgDirectOrderStorageTransResult result, User user, SgBShareOut newDirectShareOut,
                                       List<SgBShareOutItem> newDirectShareOutItemList,
                                       Map<Long, SgBSaStoragePreoutFtp> saStoragePreoutFtpMap) {
        BigDecimal newDirectPreQty = newDirectShareOutItemList.stream()
                .map(SgBShareOutItem::getQtyPreout).reduce(BigDecimal.ZERO, BigDecimal::add);
        boolean isAllPre = true;
        if (newDirectPreQty.compareTo(BigDecimal.ZERO) > 0) {
            isAllPre = false;
        }
        if (!isAllPre) {
            //理论上新的配销占用明细已经是最细力度的，不需要再合并了
            //生成订单的配销占用单及流水
            Long newDirectShareOutId = ModelUtil.getSequence(SgConstants.SG_B_SHARE_OUT);
            newDirectShareOut.setId(newDirectShareOutId);
            String newDirectShareOutBillNo = SgStoreUtils.getBillNo(SgShareConstants.SEQ_SG_B_SHARE_OUT,
                    SgConstants.SG_B_SHARE_OUT.toUpperCase().toUpperCase(), newDirectShareOut, user.getLocale());
            result.setShareOutBillNo(newDirectShareOutBillNo);
            newDirectShareOut.setBillNo(newDirectShareOutBillNo);
            StorageUtils.setBModelDefalutData(newDirectShareOut, user);
            newDirectShareOut.setTotQtyPreout(newDirectPreQty);
            newDirectShareOut.setMergeMark(false);
            for (SgBShareOutItem sgBShareOutItem : newDirectShareOutItemList) {
                sgBShareOutItem.setId(ModelUtil.getSequence(SgConstants.SG_B_SHARE_OUT_ITEM));
                sgBShareOutItem.setSgBShareOutId(newDirectShareOutId);
                StorageUtils.setBModelDefalutData(sgBShareOutItem, user);
            }
            List<SgBSaStoragePreoutFtp> newDirectSaStoragePreoutFtps = new ArrayList<>();
            for (SgBShareOutItem shareOutItem : newDirectShareOutItemList) {
                if (shareOutItem.getQtyPreout().compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                SgBSaStoragePreoutFtp sgBSaStoragePreoutFtp = saStoragePreoutFtpMap.get(shareOutItem.getPsCSkuId());
                SgBSaStoragePreoutFtp saPreoutFtp = new SgBSaStoragePreoutFtp();
                BeanUtils.copyProperties(sgBSaStoragePreoutFtp, saPreoutFtp);
                BigDecimal qtyEnd = sgBSaStoragePreoutFtp.getQtyEnd();
                BigDecimal qtyBegin = sgBSaStoragePreoutFtp.getQtyBegin();
                saPreoutFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SA_STORAGE_PREOUT_FTP));
                saPreoutFtp.setQtyChange(shareOutItem.getQtyPreout());
                saPreoutFtp.setQtyBegin(qtyEnd);
                saPreoutFtp.setQtyEnd(qtyBegin);
                saPreoutFtp.setRemark("更新库存单据来源信息");
                saPreoutFtp.setBillId(newDirectShareOut.getId());
                saPreoutFtp.setBillNo(newDirectShareOut.getBillNo());
                saPreoutFtp.setBillDate(newDirectShareOut.getBillDate());
                saPreoutFtp.setBillItemId(shareOutItem.getId());
                saPreoutFtp.setServiceNode(SgConstantsIF.SERVICE_NODE_SHARE_OUT_SAVE);
                saPreoutFtp.setSourceBillId(newDirectShareOut.getSourceBillId());
                saPreoutFtp.setSourceBillNo(newDirectShareOut.getSourceBillNo());
                saPreoutFtp.setChangeDate(new Date());
                StorageUtils.setBModelDefalutData(saPreoutFtp, user);
                newDirectSaStoragePreoutFtps.add(saPreoutFtp);
            }
            sgBShareOutMapper.insert(newDirectShareOut);
            sgBShareOutItemMapper.batchInsert(newDirectShareOutItemList);
            sgBSaStoragePreoutFtpMapper.batchInsert(newDirectSaStoragePreoutFtps);
        }
    }

    /**
     * 构建保存订单配销占用单
     *
     * @param user
     * @param orderShareOut
     * @param orderShareOutItemList
     * @param orderShareOutItemLogList
     * @param saStoragePreoutFtpMap
     * @param result
     */
    private void saveOrderShareOut(User user, SgBShareOut orderShareOut, List<SgBShareOutItem> orderShareOutItemList,
                                   List<SgBShareOutItemLog> orderShareOutItemLogList,
                                   Map<Long, SgBSaStoragePreoutFtp> saStoragePreoutFtpMap,
                                   SgDirectOrderStorageTransResult result) {
        Long orderShareOutId = ModelUtil.getSequence(SgConstants.SG_B_SHARE_OUT);
        orderShareOut.setId(orderShareOutId);
        String orderShareOutBillNo = SgStoreUtils.getBillNo(SgShareConstants.SEQ_SG_B_SHARE_OUT,
                SgConstants.SG_B_SHARE_OUT.toUpperCase().toUpperCase(), orderShareOut, user.getLocale());
        result.setOrderShareOutBillNo(orderShareOutBillNo);
        orderShareOut.setBillNo(orderShareOutBillNo);
        StorageUtils.setBModelDefalutData(orderShareOut, user);
        Map<String, SgBShareOutItem> orderShareOutItemMap = new HashMap<>();
        BigDecimal orderPreTotQty = BigDecimal.ZERO;
        BigDecimal orderPreTotPreQty = BigDecimal.ZERO;
        for (SgBShareOutItem sgBShareOutItem : orderShareOutItemList) {
            orderPreTotQty = orderPreTotQty.add(sgBShareOutItem.getQty());
            orderPreTotPreQty = orderPreTotPreQty.add(sgBShareOutItem.getQtyPreout());
            String key = sgBShareOutItem.getSgCSaStoreId() +
                    SgConstants.SG_CONNECTOR_MARKS_4 + sgBShareOutItem.getPsCSkuId();
            if (!orderShareOutItemMap.containsKey(key)) {
                sgBShareOutItem.setId(ModelUtil.getSequence(SgConstants.SG_B_SHARE_OUT_ITEM));
                sgBShareOutItem.setSgBShareOutId(orderShareOutId);
                StorageUtils.setBModelDefalutData(sgBShareOutItem, user);
                orderShareOutItemMap.put(key, sgBShareOutItem);
            } else {
                SgBShareOutItem shareOutItem = orderShareOutItemMap.get(key);
                shareOutItem.setQty(shareOutItem.getQty().add(sgBShareOutItem.getQty()));
                shareOutItem.setQtyPreout(shareOutItem.getQtyPreout().add(sgBShareOutItem.getQtyPreout()));
            }
        }
        orderShareOut.setTotQtyOrign(orderPreTotQty);
        orderShareOut.setTotQtyPreout(orderPreTotPreQty);
        Collection<SgBShareOutItem> orderInsertShareOutItemList = orderShareOutItemMap.values();
        if (orderInsertShareOutItemList.size() < orderShareOutItemLogList.size()) {
            orderShareOut.setMergeMark(true);
        } else {
            orderShareOut.setMergeMark(false);
        }
        orderShareOut.setTotRowNum(orderInsertShareOutItemList.size());
        List<SgBSaStoragePreoutFtp> orderSaStoragePreoutFtps = new ArrayList<>();
        for (SgBShareOutItem shareOutItem : orderInsertShareOutItemList) {
            SgBSaStoragePreoutFtp sgBSaStoragePreoutFtp = saStoragePreoutFtpMap.get(shareOutItem.getPsCSkuId());
            SgBSaStoragePreoutFtp saPreoutFtp = new SgBSaStoragePreoutFtp();
            BeanUtils.copyProperties(sgBSaStoragePreoutFtp, saPreoutFtp);
            BigDecimal qtyEnd = sgBSaStoragePreoutFtp.getQtyEnd();
            BigDecimal qtyBegin = sgBSaStoragePreoutFtp.getQtyBegin();
            saPreoutFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SA_STORAGE_PREOUT_FTP));
            saPreoutFtp.setQtyChange(shareOutItem.getQtyPreout());
            saPreoutFtp.setQtyBegin(qtyEnd);
            saPreoutFtp.setQtyEnd(qtyBegin);
            saPreoutFtp.setRemark("更新库存单据来源信息");
            saPreoutFtp.setBillId(orderShareOut.getId());
            saPreoutFtp.setBillNo(orderShareOut.getBillNo());
            saPreoutFtp.setBillDate(orderShareOut.getBillDate());
            saPreoutFtp.setBillType(SgConstantsIF.BILL_TYPE_RETAIL);
            saPreoutFtp.setBillItemId(shareOutItem.getId());
            saPreoutFtp.setServiceNode(SgConstantsIF.SERVICE_NODE_SHARE_OUT_SAVE);
            saPreoutFtp.setSourceBillId(orderShareOut.getSourceBillId());
            saPreoutFtp.setSourceBillNo(orderShareOut.getSourceBillNo());
            saPreoutFtp.setChangeDate(new Date());
            StorageUtils.setBModelDefalutData(saPreoutFtp, user);
            orderSaStoragePreoutFtps.add(saPreoutFtp);
        }
        sgBShareOutMapper.insert(orderShareOut);
        sgBShareOutItemMapper.batchInsert(orderInsertShareOutItemList);
        sgBSaStoragePreoutFtpMapper.batchInsert(orderSaStoragePreoutFtps);
        if (orderShareOut.getMergeMark()) {
            //有合并才保存日志
            for (SgBShareOutItemLog sgBShareOutItemLog : orderShareOutItemLogList) {
                sgBShareOutItemLog.setId(ModelUtil.getSequence(SgConstants.SG_B_SHARE_OUT_ITEM_LOG));
                sgBShareOutItemLog.setSgBShareOutId(orderShareOutId);
                StorageUtils.setBModelDefalutData(sgBShareOutItemLog, user);
            }
            sgBShareOutItemLogMapper.batchInsert(orderShareOutItemLogList);
        }
    }

    /**
     * 构建预占单反向流水
     *
     * @param user
     * @param oldDirectSaStoragePreoutFtps
     * @param oldNegateDirectSaStoragePreoutFtps
     * @param saStoragePreoutFtpMap
     */
    private void buildOldNegateDirectSaStoragePreoutFtp(User user, List<SgBSaStoragePreoutFtp> oldDirectSaStoragePreoutFtps,
                                                        List<SgBSaStoragePreoutFtp> oldNegateDirectSaStoragePreoutFtps,
                                                        Map<Long, SgBSaStoragePreoutFtp> saStoragePreoutFtpMap) {
        for (SgBSaStoragePreoutFtp storagePreoutFtp : oldDirectSaStoragePreoutFtps) {
            SgBSaStoragePreoutFtp saPreoutFtp = new SgBSaStoragePreoutFtp();
            BeanUtils.copyProperties(storagePreoutFtp, saPreoutFtp);
            BigDecimal qtyEnd = storagePreoutFtp.getQtyEnd();
            BigDecimal qtyBegin = storagePreoutFtp.getQtyBegin();
            BigDecimal qtyChange = storagePreoutFtp.getQtyChange();
            saPreoutFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SA_STORAGE_PREOUT_FTP));
            saPreoutFtp.setQtyChange(qtyChange.negate());
            saPreoutFtp.setQtyBegin(qtyEnd);
            saPreoutFtp.setQtyEnd(qtyBegin);
            saPreoutFtp.setRemark("更新库存单据来源信息");
            saPreoutFtp.setChangeDate(new Date());
            saPreoutFtp.setServiceNode(SgConstantsIF.SERVICE_NODE_SHARE_OUT_VOID);
            StorageUtils.setBModelDefalutData(saPreoutFtp, user);
            oldNegateDirectSaStoragePreoutFtps.add(saPreoutFtp);
            //收集用于后面给订单和新预占配销占用的流水
            Long skuId = storagePreoutFtp.getPsCSkuId();
            if (!saStoragePreoutFtpMap.containsKey(skuId)) {
                saStoragePreoutFtpMap.put(skuId, storagePreoutFtp);
            }
        }
    }

    private List<SgBSaStoragePreoutFtp> queryOldDirectSaStoragePreoutFtp(SgBShareOut oldDirectShareOut) {
        List<SgBSaStoragePreoutFtp> oldDirectSaStoragePreoutFtps =
                sgBSaStoragePreoutFtpMapper.selectListMaster(new LambdaQueryWrapper<SgBSaStoragePreoutFtp>()
                        .in(SgBSaStoragePreoutFtp::getBillId, oldDirectShareOut.getId())
                        .eq(SgBSaStoragePreoutFtp::getBillType, SgConstantsIF.BILL_TYPE_DIRECT_ORDER)
                );
        BigDecimal saStorageFtpTotQty = oldDirectSaStoragePreoutFtps.stream()
                .map(SgBSaStoragePreoutFtp::getQtyChange).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (oldDirectShareOut.getTotQtyPreout().compareTo(saStorageFtpTotQty) != 0) {
            throw new NDSException("预占单流水和配销占用单的占用数量不一致！");
        }
        return oldDirectSaStoragePreoutFtps;
    }

    private void transSaStorage(Map<Long, List<SgDirectOrderStorageTransItemRequest>> itemRequestMap,
                                Map<Long, String> skuInfoMap, Map<Long, List<SgBShareOutItem>> oldDirectShareOutItemMap,
                                Set<Long> skuIds, List<SgBShareOutItem> newDirectShareOutItemList,
                                List<SgBShareOutItem> orderShareOutItemList,
                                List<SgBShareOutItemLog> orderShareOutItemLogList) {
        for (Long skuId : skuIds) {
            List<SgBShareOutItem> sgBShareOutItems = oldDirectShareOutItemMap.get(skuId);
            if (CollectionUtils.isEmpty(sgBShareOutItems)) {
                throw new NDSException("[" + skuInfoMap.getOrDefault(skuId, "") + "]不存在配销占用明细！");
            }
            //订单明细
            List<SgDirectOrderStorageTransItemRequest> itemRequests = itemRequestMap.get(skuId);
            if (CollectionUtils.isEmpty(itemRequests)) {
                newDirectShareOutItemList.addAll(sgBShareOutItems);
                continue;
            }
            //比较需求量和占用量是否
            BigDecimal itemRequestQty = itemRequests.stream()
                    .map(SgDirectOrderStorageTransItemRequest::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal preQty = sgBShareOutItems.stream()
                    .map(SgBShareOutItem::getQtyPreout).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (itemRequestQty.compareTo(preQty) > 0) {
                throw new NDSException("[" + skuInfoMap.getOrDefault(skuId, "")
                        + "]订单数量大于配销占用明细总数量！");
            }
            //到这说明该sku有需求且肯定能够满足
            for (SgBShareOutItem sgBShareOutItem : sgBShareOutItems) {
                BigDecimal qtyPreout = sgBShareOutItem.getQtyPreout();
                //如果已经分配完了就直接保存到新的预占单的配销占用明细
                if (qtyPreout.compareTo(BigDecimal.ZERO) <= 0) {
                    newDirectShareOutItemList.add(sgBShareOutItem);
                    continue;
                }
                //每次判断一下是不是需求满足完了，满足完了就直接处理配销占用明细
                //逐行分配
                for (SgDirectOrderStorageTransItemRequest itemRequest : itemRequests) {
                    if (qtyPreout.compareTo(BigDecimal.ZERO) <= 0) {
                        //说明当前行已经分配完了
                        break;
                    }
                    BigDecimal qty = itemRequest.getQty();
                    if (qty.compareTo(BigDecimal.ZERO) <= 0) {
                        //说明前面满足完了
                        continue;
                    }
                    BigDecimal orderPreQty = BigDecimal.ZERO;
                    //不够满足,清零占用明细给到新配销占用单,生成零售发货单的占用明细
                    if (qty.compareTo(qtyPreout) >= 0) {
                        orderPreQty = orderPreQty.add(qtyPreout);
                        //处理订单明细数量，下次再满足
                        itemRequest.setQty(qty.subtract(qtyPreout));
                        qtyPreout = BigDecimal.ZERO;
                    } else {
                        orderPreQty = orderPreQty.add(qty);
                        //处理原占用明细，下次再消耗
                        qtyPreout = qtyPreout.subtract(qty);
                        itemRequest.setQty(BigDecimal.ZERO);
                    }
                    SgBShareOutItem orderShareOutItem = new SgBShareOutItem();
                    SgBShareOutItemLog orderShareOutItemLog = new SgBShareOutItemLog();
                    BeanUtils.copyProperties(sgBShareOutItem, orderShareOutItem);
                    BeanUtils.copyProperties(sgBShareOutItem, orderShareOutItemLog);
                    orderShareOutItemList.add(orderShareOutItem);
                    orderShareOutItemLogList.add(orderShareOutItemLog);
                    orderShareOutItem.setQty(orderPreQty);
                    orderShareOutItemLog.setQty(orderPreQty);
                    orderShareOutItem.setQtyPreout(orderPreQty);
                    orderShareOutItemLog.setQtyPreout(orderPreQty);
                    orderShareOutItem.setSourceBillItemId(itemRequest.getOrderItemId());
                    orderShareOutItemLog.setSourceBillItemId(itemRequest.getOrderItemId());
                }
                sgBShareOutItem.setQtyPreout(qtyPreout);
                newDirectShareOutItemList.add(sgBShareOutItem);
            }
        }
    }

    /**
     * 查询预占用配销占用明细
     *
     * @param oldDirectShareOut
     * @return
     */
    private Map<Long, List<SgBShareOutItem>> queryOldDirectShareOutItem(SgBShareOut oldDirectShareOut) {
        List<SgBShareOutItem> oldDirectShareOutItemList =
                sgBShareOutItemMapper.selectList(new LambdaQueryWrapper<SgBShareOutItem>()
                        .eq(SgBShareOutItem::getSgBShareOutId, oldDirectShareOut.getId())
                        .eq(SgBShareOutItem::getIsactive, YseNoEnum.YES.getCode()));
        if (CollectionUtils.isEmpty(oldDirectShareOutItemList)) {
            throw new NDSException("无有效状态配销占用单明细!");
        }
        return oldDirectShareOutItemList.stream().collect(Collectors.groupingBy(SgBShareOutItem::getPsCSkuId));
    }

    /**
     * 查询预占单配销占用单
     *
     * @param request
     * @param orderTotQty
     * @return
     */
    private SgBShareOut queryOldDirectShareOut(SgDirectOrderStorageTransRequest request, BigDecimal orderTotQty) {
        Long sourceBillId = request.getSourceBillId();
        Integer sourceBillType = request.getSourceBillType();
        SgBShareOut oldDirectShareOut = sgBShareOutMapper.selectOne(new LambdaQueryWrapper<SgBShareOut>()
                .eq(SgBShareOut::getSourceBillId, sourceBillId)
                .eq(SgBShareOut::getSourceBillType, sourceBillType)
                .eq(SgBShareOut::getIsactive, YseNoEnum.YES.getCode()));
        if (oldDirectShareOut == null) {
            throw new NDSException("无有效状态配销占用单!");
        }
        if (orderTotQty.compareTo(oldDirectShareOut.getTotQtyPreout()) > 0) {
            throw new NDSException("订单总数量大于配销占用量!");
        }
        return oldDirectShareOut;
    }

    /**
     * 将订单明细分组排序
     *
     * @param storeItemRequestList
     * @return
     */
    private Map<Long, List<SgDirectOrderStorageTransItemRequest>> groupAndSort(
            List<SgDirectOrderStorageTransItemRequest> storeItemRequestList) {
        return storeItemRequestList.stream()
                .collect(Collectors.groupingBy(
                        SgDirectOrderStorageTransItemRequest::getPsCSkuId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(SgDirectOrderStorageTransItemRequest::getBeginProduceDate))
                                        .collect(Collectors.toList())
                        )
                ));
    }

    /**
     * 库存平移入参校验
     *
     * @param request
     */
    private void checkParam(SgDirectOrderStorageTransRequest request) {
        if (request == null) {
            throw new NDSException("请求参数不能为空!");
        }
        if (request.getSourceBillId() == null) {
            throw new NDSException("直发预占单id不能为空!");
        }
        if (StringUtils.isEmpty(request.getSourceBillNo())) {
            throw new NDSException("直发预占单编号不能为空!");
        }
        if (request.getSourceBillType() == null) {
            throw new NDSException("来源单据类型不能为空!");
        }
        if (request.getOrderId() == null) {
            throw new NDSException("订单id不能为空!");
        }
        if (StringUtils.isEmpty(request.getOrderNo())) {
            throw new NDSException("订单编号不能为空!");
        }
        if (StringUtils.isEmpty(request.getOrderTid())) {
            throw new NDSException("订单平台单号不能为空!");
        }
        if (Objects.isNull(request.getShopId())) {
            throw new NDSException("店铺不能为空!");
        }
        List<SgDirectOrderStorageTransItemRequest> itemRequestList = request.getItemRequestList();
        if (CollectionUtils.isEmpty(itemRequestList)) {
            throw new NDSException("订单明细不能为空!");
        }
        //如果用户信息没传就取系统管理员
        if (request.getUser() == null) {
            request.setUser(SystemUserResource.getRootUser());
        }
        for (SgDirectOrderStorageTransItemRequest itemRequest : itemRequestList) {
            if (itemRequest.getPsCSkuId() == null) {
                throw new NDSException("skuId不能为空!");
            }
            if (StringUtils.isEmpty(itemRequest.getPsCSkuCode())) {
                throw new NDSException("sku编码不能为空!");
            }
            if (itemRequest.getOrderItemId() == null) {
                throw new NDSException("订单明细id不能为空!");
            }
            if (itemRequest.getQty() == null) {
                throw new NDSException("订单明细数量不能为空!");
            } else if (itemRequest.getQty().compareTo(BigDecimal.ZERO) <= 0) {
                throw new NDSException("订单明细数量必须是正整数!");
            }
            if (StringUtils.isEmpty(itemRequest.getBeginProduceDate())) {
                itemRequest.setBeginProduceDate(SgConstantsIF.DEFAULT_PRODUCE_DATE);
            }
            if (StringUtils.isEmpty(itemRequest.getEndProduceDate())) {
                itemRequest.setEndProduceDate(SgConstantsIF.MAX_PRODUCE_DATE);
            }
        }

    }

    /**
     * 库存占用入参校验
     *
     * @param request
     */
    private void checkParam(SgDirectOrderStorageOccupyRequest request) {
        if (request == null) {
            throw new NDSException("请求参数不能为空!");
        }
        if (request.getSaStoreId() == null) {
            throw new NDSException("配销仓不能为空!");
        }
        if (request.getStoreId() == null) {
            throw new NDSException("逻辑仓不能为空!");
        }
        if (StringUtils.isEmpty(request.getDistCodeLevel2())) {
            throw new NDSException("二级分货组织不能为空!");
        }
        if (request.getSourceBillId() == null) {
            throw new NDSException("来源单据id不能为空!");
        }
        if (StringUtils.isEmpty(request.getSourceBillNo())) {
            throw new NDSException("来源单据编码不能为空!");
        }
        if (request.getSourceBillType() == null) {
            throw new NDSException("来源单据类型不能为空!");
        }
        if (request.getBillDate() == null) {
            throw new NDSException("单据日期不能为空!");
        }
        if (request.getShopId() == null) {
            throw new NDSException("店铺不能为空!");
        }
        //如果用户信息没传就取系统管理员
        if (request.getUser() == null) {
            request.setUser(SystemUserResource.getRootUser());
        }

        List<SgDirectOrderStorageOccupyItemRequest> itemRequestList = request.getItemRequestList();
        if (CollectionUtils.isEmpty(itemRequestList)) {
            throw new NDSException("订单明细不能为空!");
        }

        for (SgDirectOrderStorageOccupyItemRequest itemRequest : itemRequestList) {
            if (itemRequest.getSkuId() == null) {
                throw new NDSException("skuId不能为空!");
            }
            if (StringUtils.isEmpty(itemRequest.getSkuCode())) {
                throw new NDSException("sku编码不能为空!");
            }
            if (itemRequest.getSourceItemId() == null) {
                throw new NDSException("来源单明细id不能为空!");
            }
            if (itemRequest.getQty() == null) {
                throw new NDSException("明细数量不能为空!");
            } else if (itemRequest.getQty().compareTo(BigDecimal.ZERO) <= 0) {
                throw new NDSException("明细数量必须是正整数!");
            }
//            if (StringUtils.isEmpty(itemRequest.getBeginProduceDate())) {
//                itemRequest.setBeginProduceDate(SgConstantsIF.DEFAULT_PRODUCE_DATE);
//            }
//            if (StringUtils.isEmpty(itemRequest.getEndProduceDate())) {
//                itemRequest.setEndProduceDate(SgConstantsIF.MAX_PRODUCE_DATE);
//            }
        }

    }
}
