package com.burgeon.r3.sg.sourcing.services.easyrule;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategyMapper;
import com.burgeon.r3.sg.channel.model.request.strategy.SgCChannelSkuStrategyQueryInfoRequest;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelRatioStrategyQueryInfoResult;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelSkuStrategyQuerySaInfoResult;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyC2SRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemC2S;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyC2SResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuC2SResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemC2SResult;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeasy.rules.annotation.Action;
import org.jeasy.rules.annotation.Condition;
import org.jeasy.rules.annotation.Fact;
import org.jeasy.rules.annotation.Priority;
import org.jeasy.rules.annotation.Rule;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description: 通过配销仓条码比例策略设置 对订单明细的归属仓排序
 * @author: hwy
 * @time: 2021/6/21 16:54
 */
@Rule
@Slf4j
@Component
public class SgFindSourceSaRateFirstRule {

    private static final int PRIORITY = 2;

    public static final String ALLOW_TOC_ORDER_SA_STORE = "allow_toc_order_sa_store";

    @Priority
    public int getPriority() {
        return PRIORITY;
    }

    @Condition
    public boolean enableRule(@Fact("request") SgFindSourceStrategyC2SRequest request) {
        return Boolean.TRUE;
    }

    @Action
    public void doRule(@Fact("request") SgFindSourceStrategyC2SRequest request) {
        log.info(LogUtil.format("SgFindSourceRuleEngineStrategyService.handleRequest C->S一阶段寻源派单 配销仓比例策略 param:{}",
                SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_C2S),
                request.getTraceId(), JSONObject.toJSONString(request));

        //策略执行结果
        SgFindSourceStrategyC2SResult strategyResult = (SgFindSourceStrategyC2SResult) request.getStrategyBaseResult();
        //策略执行结果明细
        List<SgFindSourceStrategySkuC2SResult> skuResultList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        strategyResult.setSkuResultList(skuResultList);
        //订单明细
        List<SkuItemC2S> skuItems = request.getSkuItems();
        //查询可履约的配销仓
        List<Long> saStoreList = queryCanSa(request);
        //所有的平台条码
        List<String> skuIds = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        //系统条码
        List<Long> psCSkuIds = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        //异常行的的二级分货组织
        List<String> distCodes = new ArrayList<>();
        //遍历订单加入所有条码
        skuItems.forEach(o -> {
            String skuId = o.getSkuId();
            Long psCSkuId = o.getPsCSkuId();
            if (StringUtils.isNotEmpty(skuId) && !skuIds.contains(skuId)) {
                skuIds.add(o.getSkuId());
            }
            if (!psCSkuIds.contains(psCSkuId)) {
                psCSkuIds.add(o.getPsCSkuId());
            }
            if (!distCodes.contains(o.getDistCodeLevelTwo())) {
                distCodes.add(o.getDistCodeLevelTwo());
            }
        });
        //比例同步策略
        Map<String, List<SgCChannelRatioStrategyQueryInfoResult>> normalRatioStrategyMap =
                queryNormalRatioStrategy(request, distCodes);
        log.info(LogUtil.format("SgFindSourceSaRateFirstRule.queryNormalRatioStrategy normalRatioStrategyMap:{}",
                "SgFindSourceSaRateFirstRule.queryNormalRatioStrategy"), JSONObject.toJSONString(normalRatioStrategyMap));

        //查询店铺特殊条码比例配置(根据店铺&时间&系统条码查询)
        List<SgCChannelSkuStrategyQuerySaInfoResult> specialRateList = querySpecialRatioStrategy(request, psCSkuIds);
        // 以平台sku维度设置的策略
        List<SgCChannelSkuStrategyQuerySaInfoResult> skuSpecialRateList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        // 以平台条码维度设置的略
        List<SgCChannelSkuStrategyQuerySaInfoResult> psCSkuSpecialRateList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        //遍历区分平台SKU还是平台条码下的策略
        groupSpecialRatio(skuIds, specialRateList, skuSpecialRateList, psCSkuSpecialRateList);
        // 以平台sku维度设置的策略
        Map<String, List<SgCChannelSkuStrategyQuerySaInfoResult>> skuSpecialRateMap =
                CollectionUtils.isEmpty(skuSpecialRateList) ? new HashMap<>()
                        : skuSpecialRateList.stream().collect(Collectors.groupingBy(SgCChannelSkuStrategyQuerySaInfoResult::getSkuId));
        // 以系统sku维度设置的策略
        Map<Long, List<SgCChannelSkuStrategyQuerySaInfoResult>> psCSKuSpecialRateMap =
                CollectionUtils.isEmpty(psCSkuSpecialRateList) ? new HashMap<>()
                        : psCSkuSpecialRateList.stream().collect(Collectors.groupingBy(SgCChannelSkuStrategyQuerySaInfoResult::getPsCSkuId));
        log.info(LogUtil.format("SgFindSourceSaRateFirstRule.querySpecialRatioStrategy skuSpecialRateMap:{},psCSKuSpecialRateMap:{}",
                "SgFindSourceSaRateFirstRule.queryNormalRatioStrategy"),
                JSONObject.toJSONString(skuSpecialRateMap), JSONObject.toJSONString(psCSKuSpecialRateMap));

        //记录所有配销仓类型
//        collectSaCategory(request, normalRatioStrategyMap, skuSpecialRateMap, psCSKuSpecialRateMap);
        //校验可履约仓和策略配置的是否存在交集
        checkExistSaStore(request, skuItems, saStoreList,
                normalRatioStrategyMap, skuSpecialRateMap, psCSKuSpecialRateMap);

        //标准效期定义
        Map<Long, String> validityMap = CommonCacheValUtils.
                queryValidityDefinitionProduceByPsCSkuIdList(psCSkuIds, null);
//        request.setValidityMap(validityMap);
        //记录每行的配销仓以及优先级
        buildItemResult(skuResultList, skuItems, saStoreList,
                normalRatioStrategyMap, skuSpecialRateMap, psCSKuSpecialRateMap, validityMap);
        FindSourceStrategyUtils.outputLog("SgFindSourceSaRateFirstRule.doRule result:{}", JSONObject.toJSONString(request));
    }

    /**
     * 根据比例同步策略收集配销仓类型
     *
     * @param request
     * @param normalRatioStrategyMap
     * @param skuSpecialRateMap
     * @param psCSKuSpecialRateMap
     */
    private void collectSaCategory(SgFindSourceStrategyC2SRequest request,
                                   Map<String, List<SgCChannelRatioStrategyQueryInfoResult>> normalRatioStrategyMap,
                                   Map<String, List<SgCChannelSkuStrategyQuerySaInfoResult>> skuSpecialRateMap,
                                   Map<Long, List<SgCChannelSkuStrategyQuerySaInfoResult>> psCSKuSpecialRateMap) {
        Map<Long, Boolean> expirySaStoreMap = new HashMap<>();
        if (MapUtils.isNotEmpty(normalRatioStrategyMap) && CollectionUtils.isNotEmpty(normalRatioStrategyMap.values())) {
            for (List<SgCChannelRatioStrategyQueryInfoResult> ratioStrategyQueryInfoResultList : normalRatioStrategyMap.values()) {
                for (SgCChannelRatioStrategyQueryInfoResult ratioResult : ratioStrategyQueryInfoResultList) {
                    expirySaStoreMap.put(ratioResult.getSgCSaStoreId(),
                            SgConstants.SG_SA_STORE_CONTAINS_VALIDITY_TYPE_BIG_VALIDITY.equals(ratioResult.getCategory()));
                }
            }
        }
        if (MapUtils.isNotEmpty(skuSpecialRateMap) && CollectionUtils.isNotEmpty(skuSpecialRateMap.values())) {
            for (List<SgCChannelSkuStrategyQuerySaInfoResult> strategyQuerySaInfoResults : skuSpecialRateMap.values()) {
                for (SgCChannelSkuStrategyQuerySaInfoResult result : strategyQuerySaInfoResults) {
                    expirySaStoreMap.put(result.getSgCSaStoreId(),
                            SgConstants.SG_SA_STORE_CONTAINS_VALIDITY_TYPE_BIG_VALIDITY.equals(result.getCategory()));
                }
            }
        }
        if (MapUtils.isNotEmpty(psCSKuSpecialRateMap) && CollectionUtils.isNotEmpty(psCSKuSpecialRateMap.values())) {
            for (List<SgCChannelSkuStrategyQuerySaInfoResult> strategyQuerySaInfoResults : psCSKuSpecialRateMap.values()) {
                for (SgCChannelSkuStrategyQuerySaInfoResult result : strategyQuerySaInfoResults) {
                    expirySaStoreMap.put(result.getSgCSaStoreId(),
                            SgConstants.SG_SA_STORE_CONTAINS_VALIDITY_TYPE_BIG_VALIDITY.equals(result.getCategory()));
                }
            }
        }
        request.setExpirySaStoreMap(expirySaStoreMap);
    }

    /**
     * 记录每行的配销仓以及优先级
     *
     * @param skuResultList          执行策略后的结果
     * @param skuItems               订单行信息
     * @param saStoreList            可履约的配销仓
     * @param normalRatioStrategyMap 比例同步策略
     * @param skuSpecialRateMap      特殊条码比例同步策略BY平台sku
     * @param psCSKuSpecialRateMap   特殊条码比例同步策略BY系统sku
     * @param validityMap            标准效期定义
     */
    private void buildItemResult(List<SgFindSourceStrategySkuC2SResult> skuResultList,
                                 List<SkuItemC2S> skuItems, List<Long> saStoreList,
                                 Map<String, List<SgCChannelRatioStrategyQueryInfoResult>> normalRatioStrategyMap,
                                 Map<String, List<SgCChannelSkuStrategyQuerySaInfoResult>> skuSpecialRateMap,
                                 Map<Long, List<SgCChannelSkuStrategyQuerySaInfoResult>> psCSKuSpecialRateMap,
                                 Map<Long, String> validityMap) {
        skuItems.forEach(o -> {
            //创建当前条码策略执行结果
            SgFindSourceStrategySkuC2SResult currSkuResult = new SgFindSourceStrategySkuC2SResult();
            currSkuResult.setSourceItemId(o.getSourceItemId());
            currSkuResult.setPsCSkuId(o.getPsCSkuId());
            skuResultList.add(currSkuResult);

            //创建当前条码优先级队列明细
            Map<Long, SgFindSourceStrategyStoreItemC2SResult> currStoreItemMap =
                    new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

            //判断添加平台SKU策略还是系统SKU策略
            List<SgCChannelSkuStrategyQuerySaInfoResult> currSpecialRateList;
            if (StringUtils.isNotEmpty(o.getSkuId())) {
                currSpecialRateList = skuSpecialRateMap.get(o.getSkuId());
            } else {
                currSpecialRateList = psCSKuSpecialRateMap.get(o.getPsCSkuId());
            }
            // 按特殊条码比例同步策略同步
            if (CollectionUtils.isNotEmpty(currSpecialRateList)) {
                int priority = 1;
                // 用特殊条码的saStoreId 覆盖比例策略，防止配销仓重复
                for (int i = 0; i < currSpecialRateList.size(); i++) {
                    SgCChannelSkuStrategyQuerySaInfoResult skuStrategy = currSpecialRateList.get(i);
                    if (CollectionUtils.isNotEmpty(saStoreList) && !saStoreList.contains(skuStrategy.getSgCSaStoreId())) {
                        continue;
                    }
                    SgFindSourceStrategyStoreItemC2SResult currStoreItem = new SgFindSourceStrategyStoreItemC2SResult();
                    currStoreItem.setSgCSaStoreId(skuStrategy.getSgCSaStoreId());
                    currStoreItem.setPriority(priority++);
                    currStoreItem.setSgCShareStoreId(skuStrategy.getSgCShareStoreId());
                    currStoreItemMap.put(skuStrategy.getSgCSaStoreId(), currStoreItem);
                }
                currSkuResult.setItemResult(new ArrayList<>(currStoreItemMap.values()));
                return;
            }

            //当前商品的标准效期定义
            String produceRage = validityMap.get(o.getPsCSkuId());
            //当前行的比例同步策略
            List<SgCChannelRatioStrategyQueryInfoResult> normalRateList = normalRatioStrategyMap.get(o.getDistCodeLevelTwo());
            // 无商品效期策略/无标准效期定义 优先大效期
            if (StringUtils.isEmpty(produceRage) || o.getBeginProduceDate() == null || o.getEndProduceDate() == null) {
                // 效期间无交集
                Integer orderNo = normalRateList.stream().map(SgCChannelRatioStrategyQueryInfoResult::getOrderno).max(Integer::compareTo).get();
                // 按比例策略设置
                normalRateList.forEach(ratioStrategy -> {
                    if (SgConstants.SG_SA_STORE_CONTAINS_VALIDITY_TYPE_BIG_VALIDITY.equals(ratioStrategy.getCategory())) {
                        ratioStrategy.setOrderno(ratioStrategy.getOrderno() + orderNo);
                    }
                    SgFindSourceStrategyStoreItemC2SResult buildResult = this.buildResult(saStoreList, ratioStrategy);
                    if (buildResult != null) {
                        currStoreItemMap.put(ratioStrategy.getSgCSaStoreId(), buildResult);
                    }
                });
                currSkuResult.setItemResult(new ArrayList<>(currStoreItemMap.values()));
                return;
            }

            String[] split = produceRage.split(SgConstants.SG_CONNECTOR_MARKS_6);
            String beginValidity = split[0];
            String endValidity = split[1];
            // 效期间无交集，选非大效期配销仓
            if (beginValidity.compareTo(o.getEndProduceDate()) > 0 || endValidity.compareTo(o.getBeginProduceDate()) < 0) {
                normalRateList.forEach(ratioStrategy -> {
                    if (SgConstants.SG_SA_STORE_CONTAINS_VALIDITY_TYPE_BIG_VALIDITY.equals(ratioStrategy.getCategory())) {
                        return;
                    }
                    SgFindSourceStrategyStoreItemC2SResult buildResult = this.buildResult(saStoreList, ratioStrategy);
                    if (buildResult != null) {
                        currStoreItemMap.put(ratioStrategy.getSgCSaStoreId(), buildResult);
                    }
                });
                currSkuResult.setItemResult(new ArrayList<>(currStoreItemMap.values()));
            } else if (beginValidity.compareTo(o.getBeginProduceDate()) <= 0 && endValidity.compareTo(o.getEndProduceDate()) >= 0) {
                //指定效期范围为大效期，选大效期配销仓
                normalRateList.forEach(ratioStrategy -> {
                    if (!SgConstants.SG_SA_STORE_CONTAINS_VALIDITY_TYPE_BIG_VALIDITY.equals(ratioStrategy.getCategory())) {
                        return;
                    }
                    SgFindSourceStrategyStoreItemC2SResult buildResult = this.buildResult(saStoreList, ratioStrategy);
                    if (buildResult != null) {
                        currStoreItemMap.put(ratioStrategy.getSgCSaStoreId(), buildResult);
                    }
                });
                currSkuResult.setItemResult(new ArrayList<>(currStoreItemMap.values()));
            } else {
                //效期间有交集，将大效期优先级提高
                Integer orderNo = normalRateList.stream().map(SgCChannelRatioStrategyQueryInfoResult::getOrderno).max(Integer::compareTo).get();
                normalRateList.forEach(ratioStrategy -> {
                    if (SgConstants.SG_SA_STORE_CONTAINS_VALIDITY_TYPE_BIG_VALIDITY.equals(ratioStrategy.getCategory())) {
                        ratioStrategy.setOrderno(ratioStrategy.getOrderno() + orderNo);
                    }
                    SgFindSourceStrategyStoreItemC2SResult buildResult = this.buildResult(saStoreList, ratioStrategy);
                    if (buildResult != null) {
                        currStoreItemMap.put(ratioStrategy.getSgCSaStoreId(), buildResult);
                    }
                });
                currSkuResult.setItemResult(new ArrayList<>(currStoreItemMap.values()));
            }
        });
    }

    /**
     * 校验可履约的配销仓与策略配置的配销仓是否有交集
     *
     * @param request 寻源入参
     * @param skuItems 订单行信息
     * @param saStoreList 可履约的配销仓
     * @param normalRatioStrategyMap 比例同步策略
     * @param skuSpecialRateMap 特殊条码比例同步策略BY平台sku
     * @param psCSKuSpecialRateMap 特殊条码比例同步策略BY系统sku
     */
    private void checkExistSaStore(SgFindSourceStrategyC2SRequest request, List<SkuItemC2S> skuItems,
                                   List<Long> saStoreList,
                                   Map<String, List<SgCChannelRatioStrategyQueryInfoResult>> normalRatioStrategyMap,
                                   Map<String, List<SgCChannelSkuStrategyQuerySaInfoResult>> skuSpecialRateMap,
                                   Map<Long, List<SgCChannelSkuStrategyQuerySaInfoResult>> psCSKuSpecialRateMap) {
        //异常行的的二级分货组织
        List<String> errorDistCodes = new ArrayList<>();
        for (SkuItemC2S skuItem : skuItems) {
            //判断添加平台SKU策略还是系统SKU策略
            List<SgCChannelSkuStrategyQuerySaInfoResult> currSpecialRateList;
            if (StringUtils.isNotEmpty(skuItem.getSkuId())) {
                currSpecialRateList = skuSpecialRateMap.get(skuItem.getSkuId());
            } else {
                currSpecialRateList = psCSKuSpecialRateMap.get(skuItem.getPsCSkuId());
            }
            //先看是否配置特殊条码
            if (CollectionUtils.isNotEmpty(currSpecialRateList)) {
                List<Long> specialRateSaStoreList = currSpecialRateList.stream()
                        .map(SgCChannelSkuStrategyQuerySaInfoResult::getSgCSaStoreId).distinct().collect(Collectors.toList());
                specialRateSaStoreList.retainAll(saStoreList);
                if (CollectionUtils.isEmpty(specialRateSaStoreList) &&
                        !errorDistCodes.contains(skuItem.getDistCodeLevelTwo())) {
                    errorDistCodes.add(skuItem.getDistCodeLevelTwo());
                    continue;
                }
            }
            //比例同步策略（理论上不会为空，查询时为空就报错了）
            List<SgCChannelRatioStrategyQueryInfoResult> normalRateList =
                    normalRatioStrategyMap.get(skuItem.getDistCodeLevelTwo());
            List<Long> rateSaStoreList = normalRateList.stream()
                    .map(SgCChannelRatioStrategyQueryInfoResult::getSgCSaStoreId).distinct().collect(Collectors.toList());
            rateSaStoreList.retainAll(saStoreList);
            if (CollectionUtils.isEmpty(rateSaStoreList) &&
                    !errorDistCodes.contains(skuItem.getDistCodeLevelTwo())) {
                errorDistCodes.add(skuItem.getDistCodeLevelTwo());
            }
        }
        if (CollectionUtils.isNotEmpty(errorDistCodes)) {
            request.setQuerySaStoreErrorMsg("一阶段寻源：可履约的配销仓与策略配置的配销仓无交集，分货组织：" + errorDistCodes);
            AssertUtils.logAndThrow("一阶段寻源：可履约的配销仓与策略配置的配销仓无交集，分货组织：" + errorDistCodes);
        }
    }

    /**
     * 遍历区分平台SKU还是平台条码下的策略
     *
     * @param skuIds 平台sku
     * @param specialRateList 特殊条码比例同步策略
     * @param skuSpecialRateList 特殊条码比例同步策略BY平台sku
     * @param psCSkuSpecialRateList 特殊条码比例同步策略BY系统sku
     */
    private void groupSpecialRatio(List<String> skuIds, List<SgCChannelSkuStrategyQuerySaInfoResult> specialRateList,
                                   List<SgCChannelSkuStrategyQuerySaInfoResult> skuSpecialRateList,
                                   List<SgCChannelSkuStrategyQuerySaInfoResult> psCSkuSpecialRateList) {
        if (CollectionUtils.isNotEmpty(specialRateList)) {
            specialRateList.forEach(o -> {
                String skuId = o.getSkuId();
                boolean flag = skuIds.contains(skuId);
                if (skuId != null && flag) {
                    skuSpecialRateList.add(o);
                } else if (skuId == null) {
                    psCSkuSpecialRateList.add(o);
                }
            });
        }
    }

    /**
     * 查询特殊条码比例同步策略
     *
     * @param request 寻源入参
     * @param psCSkuIds 系统skuId
     * @return
     */
    private List<SgCChannelSkuStrategyQuerySaInfoResult> querySpecialRatioStrategy(
            SgFindSourceStrategyC2SRequest request, List<Long> psCSkuIds) {
        Date currDate = new Date();
        SgCChannelSkuStrategyMapper skuStrategyMapper = ApplicationContextHandle.getBean(SgCChannelSkuStrategyMapper.class);
        SgCChannelSkuStrategyQueryInfoRequest queryInfoRequest = new SgCChannelSkuStrategyQueryInfoRequest();
        queryInfoRequest.setCpCShopId(request.getShopId());
        queryInfoRequest.setPsCSkuIds(psCSkuIds);
        queryInfoRequest.setBeginTime(currDate);
        queryInfoRequest.setEndTime(currDate);
        return skuStrategyMapper.querySkuStrategy(queryInfoRequest);
    }

    /**
     * 根据二级分货组织查询比例同步策略
     *
     * @param request   寻源入参
     * @param distCodes 所有二级分货部门
     * @return
     */
    private Map<String, List<SgCChannelRatioStrategyQueryInfoResult>> queryNormalRatioStrategy(
            SgFindSourceStrategyC2SRequest request, List<String> distCodes) {
        SgCChannelRatioStrategyMapper ratioStrategyMapper =
                ApplicationContextHandle.getBean(SgCChannelRatioStrategyMapper.class);
        //查询比例同步策略
        List<SgCChannelRatioStrategyQueryInfoResult> resultList =
                ratioStrategyMapper.queryRatioStrategyByDists(distCodes);
        if (CollectionUtils.isEmpty(resultList)) {
            request.setQuerySaStoreErrorMsg("一阶段寻源：未查询到比例同步策略配置异常，分货组织：" + distCodes);
            AssertUtils.logAndThrow("一阶段寻源：未查询到比例同步策略配置异常，分货组织：" + distCodes);
        }
        Map<String, List<SgCChannelRatioStrategyQueryInfoResult>> normalRatioStrategyMap =
                resultList.stream().collect(Collectors.groupingBy(SgCChannelRatioStrategyQueryInfoResult::getDistCodeLevelTwo));
        distCodes.removeAll(normalRatioStrategyMap.keySet());
        if (CollectionUtils.isNotEmpty(distCodes)) {
            request.setQuerySaStoreErrorMsg("一阶段寻源：未查询到比例同步策略配置异常，分货组织：" + distCodes);
            AssertUtils.logAndThrow("一阶段寻源：未查询到比例同步策略配置异常，分货组织：" + distCodes);
        }
        return normalRatioStrategyMap;
    }

    /**
     * 查询可履约的配销仓
     *
     * @param request 寻源入参
     * @return
     */
    private List<Long> queryCanSa(SgFindSourceStrategyC2SRequest request) {
        List<Long> saStoreList;
        if (request.getIsTobOrder()) {
            saStoreList = getSaStoreListByPhyStore(request);
        } else {
            //查询可以履约TOC的配销仓
            saStoreList = getSaStoreListByTocOrder();
            log.info("SgFindSourceRuleEngineStrategyService.handleRequest saStoreListByTocOrder:{}",
                    JSONObject.toJSONString(saStoreList));
            if (CollectionUtils.isEmpty(saStoreList)) {
                request.setQuerySaStoreErrorMsg("一阶段寻源：未查询到能够履约TOC订单的配销仓");
                AssertUtils.logAndThrow("一阶段寻源：未查询到能够履约TOC订单的配销仓");
            }
            if (StringUtils.isNotEmpty(request.getWarehouseEcode())) {
                List<Long> saStoreListByPhyStore = getSaStoreListByPhyStore(request);
                saStoreList.retainAll(saStoreListByPhyStore);
                if (CollectionUtils.isEmpty(saStoreList)) {
                    request.setQuerySaStoreErrorMsg("一阶段寻源：指定实体仓与履约TOC订单的聚合仓对应的配销仓无交集");
                    AssertUtils.logAndThrow("一阶段寻源：指定实体仓与履约TOC订单的聚合仓对应的配销仓无交集");
                }
            }
        }
        return saStoreList;
    }

    /**
     * 根据实体仓编码，获取配销仓范围
     *
     * @param request 寻源入参
     * @return
     */
    private List<Long> getSaStoreListByPhyStore(SgFindSourceStrategyC2SRequest request) {

        List<Long> resultList = new ArrayList<>();

        if (StringUtils.isEmpty(request.getWarehouseEcode())) {
            return resultList;
        }

        CpCStoreMapper storeMapper = ApplicationContextHandle.getBean(CpCStoreMapper.class);
        List<String> warehouseEcodeList = Arrays.asList(request.getWarehouseEcode().split(","));
        List<SgCSaStore> saStoreList = storeMapper.querySaStoreByPhyCode(warehouseEcodeList);

        if (CollectionUtils.isEmpty(saStoreList)) {
            FindSourceStrategyUtils.outputLog("SgFindSourceSaRateFirstRule.getSaStoreListByPhyStore 指定实体仓:{}下无可用配销仓!",
                    request.getWarehouseEcode());
            request.setQuerySaStoreErrorMsg("一阶段寻源：指定实体仓未找到配销仓，请检查仓关联关系；指定实体仓编码：" + request.getWarehouseEcode());
            AssertUtils.logAndThrow("一阶段寻源：指定实体仓未找到配销仓，请检查仓关联关系；指定实体仓编码：" + request.getWarehouseEcode());
        }
        resultList = saStoreList.stream().map(SgCSaStore::getId).collect(Collectors.toList());
        log.info("SgFindSourceSaRateFirstRule.getSaStoreListByPhyStore resultList:{}", JSONObject.toJSONString(resultList));
        return resultList;
    }

    /**
     * 查询可以履约TOC的配销仓
     *
     * @return 可履约TOC的配销仓
     */
    private List<Long> getSaStoreListByTocOrder() {
        CusRedisTemplate<String, String> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
        String saStoreIdStr = strRedisTemplate.opsForValue().get(ALLOW_TOC_ORDER_SA_STORE);
        if (StringUtils.isNotEmpty(saStoreIdStr)) {
            return JSONObject.parseArray(saStoreIdStr, Long.class);
        }
        CpCStoreMapper storeMapper = ApplicationContextHandle.getBean(CpCStoreMapper.class);
        List<Long> saStoreIdList = storeMapper.querySaStoreByTocOrder();
        if (CollectionUtils.isNotEmpty(saStoreIdList)) {
            strRedisTemplate.opsForValue().set(ALLOW_TOC_ORDER_SA_STORE, JSONObject.toJSONString(saStoreIdList), 1, TimeUnit.MINUTES);
        }
        return saStoreIdList;
    }

    /**
     * 构建结果
     *
     * @param saStoreList 可履约的配销仓
     * @param ratioStrategy 比例同步策略
     * @return
     */
    private SgFindSourceStrategyStoreItemC2SResult buildResult(List<Long> saStoreList, SgCChannelRatioStrategyQueryInfoResult ratioStrategy) {
        if (CollectionUtils.isNotEmpty(saStoreList) && !saStoreList.contains(ratioStrategy.getSgCSaStoreId())) {
            return null;
        }
        SgFindSourceStrategyStoreItemC2SResult currStoreItem = new SgFindSourceStrategyStoreItemC2SResult();
        currStoreItem.setSgCSaStoreId(ratioStrategy.getSgCSaStoreId());
        currStoreItem.setPriority(ratioStrategy.getOrderno());
        currStoreItem.setSgCShareStoreId(ratioStrategy.getSgCShareStoreId());
        return currStoreItem;
    }

}