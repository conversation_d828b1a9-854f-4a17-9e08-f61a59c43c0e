package com.burgeon.r3.sg.sourcing.services.factory.item;


import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.StrategyFactoryBaseResult;
import com.burgeon.r3.sg.sourcing.model.result.factory.SgFindSourceStrategyFactoryResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.services.factory.StrategyFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 拆单策略组工厂类
 * @author: chenb
 * @time: 2022/8/11 16:10
 */

@Component
@Slf4j
@Data
public class SgFindSourceSplitStrategyGroupItemFactory extends StrategyFactory {

    private List<? extends StrategyFactoryBaseResult> strategiesList;

    private StrategyHandle handle;

    @Override
    public List<SgFindSourceStrategyFactoryResult> getStrategies(StrategyBaseRequest request) {

        List<SgFindSourceStrategyFactoryResult> results = new ArrayList<>();
        SgFindSourceStrategyFactoryResult result = new SgFindSourceStrategyFactoryResult();
        result.setStrategyName("仓库满足率策略执行器");
        result.setStrategyClazz("com.burgeon.r3.sg.sourcing.services.item.SgFindSourceWarehouseFillRateStrategyService");
        result.setPriority(10);
        results.add(result);

        result = new SgFindSourceStrategyFactoryResult();
        result.setStrategyName("仓库评分因子策略执行器");
        result.setStrategyClazz("com.burgeon.r3.sg.sourcing.services.item.SgFindSourceWarehouseScoreStrategyService");
        result.setPriority(20);
        results.add(result);

        result = new SgFindSourceStrategyFactoryResult();
        result.setStrategyName("拆单策略");
        result.setStrategyClazz("com.burgeon.r3.sg.sourcing.services.item.SgFindSourceSplitStrategyService");
        result.setPriority(30);
        results.add(result);

        return results;
    }
}