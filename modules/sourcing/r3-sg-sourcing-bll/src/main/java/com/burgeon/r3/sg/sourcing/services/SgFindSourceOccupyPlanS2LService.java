package com.burgeon.r3.sg.sourcing.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgBTobSourceMaxBatch;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemS2L;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanItemResult;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanOutItemResult;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanServiceResult;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 对策略结果的 按逻辑仓优先级分配库存
 * @author: hwy
 * @time: 2021/7/2 14:01
 */
@Component
@Slf4j
public class SgFindSourceOccupyPlanS2LService {

    /**
     * @param request:
     * @Description:
     * @Author: hwy
     * @Date: 2021/7/2 14:52
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanServiceResult>
     **/
    public ValueHolderV14<SgOccupyPlanServiceResult> getOccupyPlan(SgFindSourceStrategyS2LRequest request) {
        FindSourceStrategyUtils.outputLog("SgFindSourceOccupyPlanS2LService.getOccupyPlan S->L 获取库存占用计划 param:{}", JSONObject.toJSONString(request));

        ValueHolderV14<SgOccupyPlanServiceResult> valueHolderV14 = new ValueHolderV14<>(StrategyConstants.RESULT_CODE_PREOUT, SgConstants.MESSAGE_STATUS_SUCCESS);
        try {
            SgFindSourceStrategyS2LResult strategyResult = (SgFindSourceStrategyS2LResult) request.getStrategyBaseResult();
            List<SgFindSourceStrategySkuS2LResult> skuResultList = strategyResult.getSkuResultList();
            if (CollectionUtils.isEmpty(skuResultList)) {
                FindSourceStrategyUtils.outputLog("S->L 寻源派单 来源单据id:{} 生成占用计划异常 没有可分配的明细", request.getSourceBillId());
                valueHolderV14.setCode(StrategyConstants.RESULT_CODE_EXCEPTION);
                valueHolderV14.setMessage("S->L 寻源派单 生成占用计划异常 没有可分配的明细");
                return valueHolderV14;
            }
            // 获取可配货的逻辑仓的优先级
            List<SgCpCStore> sgCpCStores = getlogicSortByPrority(request, skuResultList, valueHolderV14);
            if (valueHolderV14.getCode() != StrategyConstants.RESULT_CODE_PREOUT) {
                return valueHolderV14;
            }
            // 初始参数
            Map<Long, SkuItemS2L> skuItemMap = request.getSkuItems().stream().filter(o -> !StrategyConstants.OUT_DEFAULT_STORE_ID.equals(o.getShareItemId())).collect(Collectors.toMap(SkuItemS2L::getShareItemId, Function.identity()));
            // 占用计划明细 key:sourceItemId
            Map<Long, SgOccupyPlanItemResult> itemPlantMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            // 按照优先级占用库存  遍历明细 (更具生产日期升序)
            preByProduceDateAsc(skuResultList, sgCpCStores, skuItemMap, itemPlantMap);
            SgOccupyPlanServiceResult occupyPlan = setOutStockItemPlan(request, itemPlantMap);
            valueHolderV14.setData(occupyPlan);
        } catch (Exception e) {
            log.error("S->L  生成逻辑占用计划失败:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(StrategyConstants.RESULT_CODE_EXCEPTION);
            valueHolderV14.setMessage("S->L  生成逻辑占用计划失败");
        }
        return valueHolderV14;
    }

    /**
     * @param request:
     * @Description:
     * @Author: hwy
     * @Date: 2021/7/2 14:52
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanServiceResult>
     **/
    public ValueHolderV14<SgOccupyPlanServiceResult> getOccupyPlanByTob(SgFindSourceStrategyS2LRequest request, SgBTobSourceMaxBatch sgBTobSourceMaxBatch) {
        FindSourceStrategyUtils.outputLog("SgFindSourceOccupyPlanS2LService.getOccupyPlanByTob S->L 获取库存占用计划 param:{},sgBTobSourceMaxBatch:{}",
                JSONObject.toJSONString(request), JSONObject.toJSONString(sgBTobSourceMaxBatch));

        ValueHolderV14<SgOccupyPlanServiceResult> valueHolderV14 =
                new ValueHolderV14<>(StrategyConstants.RESULT_CODE_PREOUT, SgConstants.MESSAGE_STATUS_SUCCESS);
        try {
            SgFindSourceStrategyS2LResult strategyResult = (SgFindSourceStrategyS2LResult) request.getStrategyBaseResult();
            List<SgFindSourceStrategySkuS2LResult> skuResultList = strategyResult.getSkuResultList();

            if (CollectionUtils.isEmpty(skuResultList)) {
                FindSourceStrategyUtils.outputLog("S->L 寻源派单 来源单据id:{} 生成占用计划异常 没有可分配的明细", request.getSourceBillId());
                valueHolderV14.setCode(StrategyConstants.RESULT_CODE_EXCEPTION);
                valueHolderV14.setMessage("S->L 寻源派单 生成占用计划异常 没有可分配的明细");
                return valueHolderV14;
            }

            // 获取可配货的逻辑仓的优先级
            List<SgCpCStore> sgCpCStores = getlogicSortByPrority(request, skuResultList, valueHolderV14);
            if (valueHolderV14.getCode() != StrategyConstants.RESULT_CODE_PREOUT) {
                return valueHolderV14;
            }
            // 初始参数
            Map<Long, SkuItemS2L> skuItemMap = request.getSkuItems().stream()
                    .filter(o -> !StrategyConstants.OUT_DEFAULT_STORE_ID.equals(o.getShareItemId())).collect(Collectors.toMap(SkuItemS2L::getShareItemId, Function.identity()));
            // 占用计划明细 key:sourceItemId
            Map<Long, SgOccupyPlanItemResult> itemPlantMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            // 按照优先级占用库存  遍历明细（根据生产日期升序占用）
            preByProduceDateAsc(skuResultList, sgCpCStores, skuItemMap, itemPlantMap);
            //获取每行的批次数
            Map<Long, Integer> batchNums = getBatchNum(itemPlantMap);
            boolean geMaxBatchNum = false;
            for (Map.Entry<Long, Integer> entry : batchNums.entrySet()) {
                if (entry.getValue() > sgBTobSourceMaxBatch.getMaxBatchNum()) {
                    geMaxBatchNum = true;
                    break;
                }
            }
            //超过最大批次数，按照数量倒序占用
            if (geMaxBatchNum) {
                //有行数大于最大批次，重新按照数量占用
                FindSourceStrategyUtils.outputLog("SgFindSourceOccupyPlanS2LService.preByNumDesc S->L 根据数量倒序占用 " +
                        "skuResultList:{},batchNums:{}", JSONObject.toJSONString(skuResultList), JSONObject.toJSONString(batchNums));
                itemPlantMap = preByNumDesc(skuResultList, sgCpCStores, skuItemMap);
                Map<Long, Integer> batchNumMap = getBatchNum(itemPlantMap);
                for (Long sourceItemId : batchNumMap.keySet()) {
                    if (batchNumMap.get(sourceItemId) > sgBTobSourceMaxBatch.getMaxBatchNum()) {
                        Map<Long, String> skuMap = request.getSkuItems().stream()
                                .collect(Collectors.toMap(SkuItemS2L::getSourceItemId, SkuItemS2L::getPsCSkuEcode));
                        FindSourceStrategyUtils.outputLog("S->L 寻源派单 来源单据id:" + request.getSourceBillId() + "生成占用计划失败," + skuMap.get(sourceItemId) + "占用行数超过最大批次数！");
                        valueHolderV14.setCode(StrategyConstants.RESULT_CODE_EXCEPTION);
                        valueHolderV14.setMessage(skuMap.get(sourceItemId) + "占用行数超过最大批次数！");
                        return valueHolderV14;
                    }
                }
            }
            SgOccupyPlanServiceResult occupyPlan = setOutStockItemPlan(request, itemPlantMap);
            valueHolderV14.setData(occupyPlan);
        } catch (Exception e) {
            log.error("S->L  生成逻辑占用计划失败:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(StrategyConstants.RESULT_CODE_EXCEPTION);
            valueHolderV14.setMessage("S->L  生成逻辑占用计划失败");
        }
        return valueHolderV14;
    }

    private Map<Long, SgOccupyPlanItemResult> preByNumDesc(List<SgFindSourceStrategySkuS2LResult> skuResultList, List<SgCpCStore> sgCpCStores,
                                                           Map<Long, SkuItemS2L> skuItemMap) {
        FindSourceStrategyUtils.outputLog("SgFindSourceOccupyPlanS2LService.preByNumDesc S->L 根据数量倒序占用 " +
                "skuResultList:{},sgCpCStores:{}", JSONObject.toJSONString(skuResultList));
        // 占用计划明细 key:sourceItemId
        Map<Long, SgOccupyPlanItemResult> itemPlantMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
        // 用于承接库存集合 <psCSkuId, <storeId, <produce, qty>>>
        Map<Long, Map<Long, SortedMap<String, BigDecimal>>> storageMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
        for (SgFindSourceStrategySkuS2LResult skuResult : skuResultList) {
            // <storeId, <produce, qty>>
            Map<Long, SortedMap<String, BigDecimal>> skuStorageMap =
                    storageMap.computeIfAbsent(skuResult.getPsCSkuId(), k -> new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY));
            Long shareItemId = skuResult.getShareItemId();
            if (StrategyConstants.OUT_DEFAULT_STORE_ID.equals(shareItemId)) {
                continue;
            }
            List<SgFindSourceStrategyStoreItemS2LResult> itemResultList = skuResult.getItemResultList();
            SkuItemS2L skuItem = skuItemMap.get(shareItemId);
            // 没有可分配的仓 跳过
            if (CollectionUtils.isEmpty(itemResultList)) {
                break;
            }
            // 当前物理仓占用数量
            BigDecimal qtyPreOut = skuResult.getQtyPreOut();
            Collections.sort(itemResultList);
            // 遍历明细的可分配物理仓
            t:
            for (SgFindSourceStrategyStoreItemS2LResult itemResult : itemResultList) {
                // 当前仓设置为缺货 跳过
                if (StrategyConstants.OUT_DEFAULT_STORE_ID.equals(itemResult.getStoreId())) {
                    continue;
                }
                Map<Long, SortedMap<String, BigDecimal>> logicStorageMap = itemResult.getLogicStorageMap();
                // 没有可分配的逻辑仓 跳过
                if (MapUtils.isEmpty(logicStorageMap)) {
                    break;
                }
                // 存在可分配的逻辑仓  按照逻辑仓优先级占用库存
                for (SgCpCStore sgCpCStore : sgCpCStores) {
                    Long logicStoreId = sgCpCStore.getId();
                    //不包含 跳过
                    if (!logicStorageMap.containsKey(logicStoreId)) {
                        continue;
                    }
                    SortedMap<String, BigDecimal> storeStorageMap = skuStorageMap.computeIfAbsent(logicStoreId, k -> new TreeMap<>());
                    SortedMap<String, BigDecimal> sortedMap = logicStorageMap.get(logicStoreId);
                    //转换sortedMap，转成数量倒序
                    Map<String, BigDecimal> reversedMap = sortedMap.entrySet().stream()
                            .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                            .collect(Collectors.toMap(
                                    Map.Entry::getKey,
                                    Map.Entry::getValue,
                                    (oldValue, newValue) -> oldValue,
                                    LinkedHashMap::new
                            ));
                    FindSourceStrategyUtils.outputLog("SgFindSourceOccupyPlanS2LService.preByNumDesc S->L 转成数量倒序 sourceItemId:{},sortedMap:{},reversedMap:{}",
                            skuResult.getSourceItemId(), JSONObject.toJSONString(sortedMap), JSONObject.toJSONString(reversedMap));
                    for (Map.Entry<String, BigDecimal> entry : reversedMap.entrySet()) {
                        String produceDate = entry.getKey();
                        BigDecimal storage = entry.getValue();
                        BigDecimal logicStorage = storeStorageMap.get(produceDate);
                        if (logicStorage == null) {
                            logicStorage = storage;
                        }
                        // 库存满足全部需求 结束当前明细分配
                        if (logicStorage.compareTo(qtyPreOut) > 0) {
                            setItemPlan(itemPlantMap, skuItem, qtyPreOut, logicStoreId, produceDate);
                            storeStorageMap.put(produceDate, logicStorage.subtract(qtyPreOut));
                            break t;
                        }
                        // 只能发部分
                        if (logicStorage.compareTo(BigDecimal.ZERO) > 0) {
                            setItemPlan(itemPlantMap, skuItem, logicStorage, logicStoreId, produceDate);
                            qtyPreOut = qtyPreOut.subtract(logicStorage);
                            storeStorageMap.put(produceDate, BigDecimal.ZERO);
                        }
                        if (qtyPreOut.compareTo(BigDecimal.ZERO) == 0) {
                            break t;
                        }
                    }
                }
            }
        }
        return itemPlantMap;
    }

    /**
     * 根据占用计划获取每行批次数量
     *
     * @param itemPlantMap
     * @return
     */
    private Map<Long, Integer> getBatchNum(Map<Long, SgOccupyPlanItemResult> itemPlantMap) {
        Map<Long, Integer> batchNums = new HashMap<>();
        for (Long sourceItemId : itemPlantMap.keySet()) {
            //虽然一个实体仓只有一个逻辑仓，这里也兼容一下多个
            Set<String> productDateSet = new HashSet<>();
            SgOccupyPlanItemResult planItemResult = itemPlantMap.get(sourceItemId);
            if (planItemResult != null && MapUtils.isNotEmpty(planItemResult.getS2LStorePlan())) {
                for (Map<String, BigDecimal> decimalMap : planItemResult.getS2LStorePlan().values()) {
                    productDateSet.addAll(decimalMap.keySet());
                }
            }
            batchNums.put(sourceItemId, productDateSet.size());
        }
        return batchNums;
    }

    /**
     * 按照生产日期升序占用库存
     *
     * @param skuResultList
     * @param sgCpCStores
     * @param skuItemMap
     * @param itemPlantMap
     */
    private void preByProduceDateAsc(List<SgFindSourceStrategySkuS2LResult> skuResultList, List<SgCpCStore> sgCpCStores,
                                     Map<Long, SkuItemS2L> skuItemMap, Map<Long, SgOccupyPlanItemResult> itemPlantMap) {
        FindSourceStrategyUtils.outputLog("SgFindSourceOccupyPlanS2LService.preByProduceDateAsc S->L 根据生产日期升序占用 " +
                "skuResultList:{},sgCpCStores:{}", JSONObject.toJSONString(skuResultList));
        // 用于承接库存集合 <psCSkuId, <storeId, <produce, qty>>>
        Map<Long, Map<Long, SortedMap<String, BigDecimal>>> storageMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
        for (SgFindSourceStrategySkuS2LResult skuResult : skuResultList) {
            // <storeId, <produce, qty>>
            Map<Long, SortedMap<String, BigDecimal>> skuStorageMap =
                    storageMap.computeIfAbsent(skuResult.getPsCSkuId(), k -> new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY));
            Long shareItemId = skuResult.getShareItemId();
            if (StrategyConstants.OUT_DEFAULT_STORE_ID.equals(shareItemId)) {
                continue;
            }
            List<SgFindSourceStrategyStoreItemS2LResult> itemResultList = skuResult.getItemResultList();
            SkuItemS2L skuItem = skuItemMap.get(shareItemId);
            // 没有可分配的仓 跳过
            if (CollectionUtils.isEmpty(itemResultList)) {
                break;
            }
            // 当前物理仓占用数量
            BigDecimal qtyPreOut = skuResult.getQtyPreOut();
            Collections.sort(itemResultList);
            // 遍历明细的可分配物理仓
            t:
            for (SgFindSourceStrategyStoreItemS2LResult itemResult : itemResultList) {
                // 当前仓设置为缺货 跳过
                if (StrategyConstants.OUT_DEFAULT_STORE_ID.equals(itemResult.getStoreId())) {
                    continue;
                }
                Map<Long, SortedMap<String, BigDecimal>> logicStorageMap = itemResult.getLogicStorageMap();
                // 没有可分配的逻辑仓 跳过
                if (MapUtils.isEmpty(logicStorageMap)) {
                    break;
                }
                // 存在可分配的逻辑仓  按照逻辑仓优先级占用库存
                for (SgCpCStore sgCpCStore : sgCpCStores) {
                    Long logicStoreId = sgCpCStore.getId();
                    //不包含 跳过
                    if (!logicStorageMap.containsKey(logicStoreId)) {
                        continue;
                    }
                    SortedMap<String, BigDecimal> storeStorageMap = skuStorageMap.computeIfAbsent(logicStoreId, k -> new TreeMap<>());
                    for (Map.Entry<String, BigDecimal> entry : logicStorageMap.get(logicStoreId).entrySet()) {
                        String produceDate = entry.getKey();
                        BigDecimal storage = entry.getValue();
                        BigDecimal logicStorage = storeStorageMap.get(produceDate);
                        if (logicStorage == null) {
                            logicStorage = storage;
                        }
                        // 库存满足全部需求 结束当前明细分配
                        if (logicStorage.compareTo(qtyPreOut) > 0) {
                            setItemPlan(itemPlantMap, skuItem, qtyPreOut, logicStoreId, produceDate);
                            storeStorageMap.put(produceDate, logicStorage.subtract(qtyPreOut));
                            break t;
                        }
                        // 只能发部分
                        if (logicStorage.compareTo(BigDecimal.ZERO) > 0) {
                            setItemPlan(itemPlantMap, skuItem, logicStorage, logicStoreId, produceDate);
                            qtyPreOut = qtyPreOut.subtract(logicStorage);
                            storeStorageMap.put(produceDate, BigDecimal.ZERO);
                        }
                        if (qtyPreOut.compareTo(BigDecimal.ZERO) == 0) {
                            break t;
                        }
                    }
                }
            }
        }
    }

    /**
     * @param request:
     * @param itemPlantMap:
     * @Description: 设置缺货结果
     * @Author: hwy
     * @Date: 2021/7/2 17:37
     * @return: void
     **/
    private SgOccupyPlanServiceResult setOutStockItemPlan(SgFindSourceStrategyS2LRequest request, Map<Long, SgOccupyPlanItemResult> itemPlantMap) {
        SgOccupyPlanServiceResult occupyPlan = new SgOccupyPlanServiceResult();
        occupyPlan.setItemResultList(new ArrayList<>(itemPlantMap.values()));
        occupyPlan.setCpCShopId(request.getShopId());
        occupyPlan.setSourceBillId(request.getSourceBillId());
        occupyPlan.setSourceBillType(request.getSourceBillType());
        List<SgOccupyPlanOutItemResult> outItemResults = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        for (SkuItemS2L skuItem : request.getSkuItems()) {
            BigDecimal qty = skuItem.getQtyPreOut();
            Long sourceItemId = skuItem.getSourceItemId();
            Long psCSkuId = skuItem.getPsCSkuId();
            //没有可占用的库存 记缺货
            if (!itemPlantMap.containsKey(sourceItemId)) {
                SgOccupyPlanOutItemResult outItemResult = new SgOccupyPlanOutItemResult();
                outItemResult.setSourceItemId(sourceItemId);
                outItemResult.setQtyChange(qty);
                outItemResult.setPsCSkuId(psCSkuId);
                outItemResult.setOutQty(qty);
                outItemResults.add(outItemResult);
                continue;
            }
            // 占用量比订单需求量小 记缺货
            SgOccupyPlanItemResult planItemResult = itemPlantMap.get(sourceItemId);
            if (planItemResult.getQty().compareTo(qty) < 0) {
                SgOccupyPlanOutItemResult outItemResult = new SgOccupyPlanOutItemResult();
                outItemResult.setSourceItemId(sourceItemId);
                outItemResult.setQtyChange(qty);
                outItemResult.setPsCSkuId(psCSkuId);
                outItemResult.setOutQty(qty.subtract(planItemResult.getQty()));
                outItemResults.add(outItemResult);
            }
        }
        occupyPlan.setOutItmeResultList(outItemResults);
        if (CollectionUtils.isNotEmpty(occupyPlan.getOutItmeResultList()) && CollectionUtils.isNotEmpty(occupyPlan.getItemResultList())) {
            occupyPlan.setOccupyPlanResult(SgConstants.PREOUT_RESULT_PART_SUCCESS);
        } else if (CollectionUtils.isNotEmpty(occupyPlan.getOutItmeResultList())) {
            occupyPlan.setOccupyPlanResult(SgConstants.PREOUT_RESULT_ALL_OUT);
        } else if (CollectionUtils.isNotEmpty(occupyPlan.getItemResultList())) {
            occupyPlan.setOccupyPlanResult(SgConstants.PREOUT_RESULT_All_SUCCESS);
        }
        return occupyPlan;
    }

    /**
     * @param itemPlantMap:
     * @param skuItem:
     * @param qty:
     * @param logicStoreId:
     * @Description: 设置分配结果
     * @Author: hwy
     * @Date: 2021/7/2 17:36
     * @return: void
     **/
    private void setItemPlan(Map<Long, SgOccupyPlanItemResult> itemPlantMap, SkuItemS2L skuItem, BigDecimal qty, Long logicStoreId, String produceDate) {
        if (!itemPlantMap.containsKey(skuItem.getSourceItemId())) {
            SgOccupyPlanItemResult itemPlan = new SgOccupyPlanItemResult();
            itemPlan.setQty(skuItem.getQtyPreOut());
            itemPlan.setPsCSkuId(skuItem.getPsCSkuId());
            Map<Long, Map<String, BigDecimal>> storePlan = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            Map<String, BigDecimal> produceMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            storePlan.put(logicStoreId, produceMap);
            produceMap.put(produceDate, qty);
            itemPlan.setS2LStorePlan(storePlan);
            itemPlan.setQty(qty);
            itemPlan.setSourceItemId(skuItem.getSourceItemId());
            itemPlan.setBeginProduceDate(skuItem.getBeginProduceDate());
            itemPlan.setEndProduceDate(skuItem.getEndProduceDate());
            if (StringUtils.isNotEmpty(skuItem.getPasteLabel())) {
                itemPlan.setLabelingRequirements(skuItem.getPasteLabel());
            }
            itemPlantMap.put(skuItem.getSourceItemId(), itemPlan);
        } else {
            SgOccupyPlanItemResult planItemResult = itemPlantMap.get(skuItem.getSourceItemId());

            if (StringUtils.isNotEmpty(planItemResult.getBeginProduceDate()) && StringUtils.isNotEmpty(skuItem.getBeginProduceDate()) && planItemResult.getBeginProduceDate().compareTo(skuItem.getBeginProduceDate()) > 0) {
                planItemResult.setBeginProduceDate(skuItem.getBeginProduceDate());
            }
            if (StringUtils.isNotEmpty(planItemResult.getEndProduceDate()) && StringUtils.isNotEmpty(skuItem.getEndProduceDate()) && planItemResult.getEndProduceDate().compareTo(skuItem.getEndProduceDate()) < 0) {
                planItemResult.setEndProduceDate(skuItem.getEndProduceDate());
            }

            if (StringUtils.isNotEmpty(skuItem.getPasteLabel())) {
                planItemResult.setLabelingRequirements(skuItem.getPasteLabel());
            }

            Map<Long, Map<String, BigDecimal>> storePlan = planItemResult.getS2LStorePlan();
            Map<String, BigDecimal> produceMap = storePlan.get(logicStoreId);
            if (produceMap == null) {
                produceMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            }
            BigDecimal oldQty = produceMap.get(produceDate) == null ? BigDecimal.ZERO : produceMap.get(produceDate);
            produceMap.put(produceDate, oldQty.add(qty));
            storePlan.put(logicStoreId, produceMap);
            planItemResult.setQty(planItemResult.getQty().add(qty));
        }
    }


    /**
     * @param skuResultList:
     * @param valueHolderV14:
     * @Description: 获取逻辑仓的优先级
     * @Author: hwy
     * @Date: 2021/7/2 17:22
     * @return: java.util.List<com.burgeon.r3.sg.core.model.table.basic.SgCpCStore>
     **/
    private List<SgCpCStore> getlogicSortByPrority(SgFindSourceStrategyS2LRequest request, List<SgFindSourceStrategySkuS2LResult> skuResultList, ValueHolderV14 valueHolderV14) {
        //所有的逻辑仓id
        List<Long> allStoreIds = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        for (SgFindSourceStrategySkuS2LResult skuResult : skuResultList) {
            List<SgFindSourceStrategyStoreItemS2LResult> itemResultList = skuResult.getItemResultList();
            if (CollectionUtils.isEmpty(itemResultList)) {
                continue;
            }
            for (SgFindSourceStrategyStoreItemS2LResult itemResult : itemResultList) {
                if (StrategyConstants.OUT_DEFAULT_STORE_ID.equals(itemResult.getStoreId())) {
                    continue;
                }
                Map<Long, SortedMap<String, BigDecimal>> logicStorageMap = itemResult.getLogicStorageMap();
                Set<Long> currLogicStoreIds = logicStorageMap.keySet();
                if (CollectionUtils.isNotEmpty(currLogicStoreIds)) {
                    allStoreIds.addAll(currLogicStoreIds);
                }
            }
        }
        //去重后的逻辑仓
        if (CollectionUtils.isEmpty(allStoreIds)) {
            FindSourceStrategyUtils.outputLog("S->L 寻源派单 来源单据id:{} 生成占用计划异常 没有可分配的逻辑仓", request.getSourceBillId());
            valueHolderV14.setMessage("S->L 寻源派单 生成占用计划异常 没有可分配的逻辑仓");
            valueHolderV14.setCode(StrategyConstants.RESULT_CODE_NO_PREOUT);
            return null;
        }
        List<Long> logicStoreIds = allStoreIds.stream().distinct().filter(o -> !StrategyConstants.OUT_DEFAULT_STORE_ID.equals(o)).collect(Collectors.toList());
        CpCStoreMapper storeMapper = ApplicationContextHandle.getBean(CpCStoreMapper.class);
        List<SgCpCStore> sgCpCStores = storeMapper.selectList(new QueryWrapper<SgCpCStore>().lambda()
                .select(SgCpCStore::getId, SgCpCStore::getPriority)
                .in(SgCpCStore::getId, logicStoreIds).eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y)
                .last(" ORDER BY ISNULL(PRIORITY),PRIORITY DESC"));
        if (CollectionUtils.isEmpty(sgCpCStores)) {
            FindSourceStrategyUtils.outputLog("S->L 寻源派单 来源单据id:{} 生成占用计划异常 查询不到逻辑仓信息", request.getSourceBillId());
            valueHolderV14.setCode(StrategyConstants.RESULT_CODE_EXCEPTION);
            valueHolderV14.setMessage("S->L 寻源派单  生成占用计划异常 查询不到逻辑仓信息");
            return null;
        }
        return sgCpCStores;
    }

}