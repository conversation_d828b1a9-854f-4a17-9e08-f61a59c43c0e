package com.burgeon.r3.sg.sourcing.validate;

import com.burgeon.r3.sg.sourcing.mapper.SgCShareSourceRuleStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.dto.strategy.SgCShareSourceRuleStrategyDTO;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: lishijun
 * @Date:
 * @Description:
 */
@Slf4j
@Component
public class SgCShareSourceRuleStrategyDeleteValidator extends BaseSingleValidator<SgCShareSourceRuleStrategyDTO> {

    @Autowired
    private SgCShareSourceRuleStrategyMapper sourceRuleStrategyMapper;

    @Override
    public String getValidatorMsgName() {
        return "寻源策略规则设置删除";
    }

    @Override
    public Class getValidatorClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCShareSourceRuleStrategyDTO mainObject, User loginUser) {
        // TODO 如果存在单据状态不为已作废的寻源策略使用了此规则，则提示：“当前记录已被寻源策略使用，不允许删除”

        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
    }


}