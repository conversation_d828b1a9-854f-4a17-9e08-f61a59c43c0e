package com.burgeon.r3.sg.sourcing.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.basic.mapper.SgBSaStoragePreoutFtpMapper;
import com.burgeon.r3.sg.basic.mapper.SgBSpStoragePreoutFtpMapper;
import com.burgeon.r3.sg.basic.mapper.SgBStorageSharedPreoutFtpMapper;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgBSaStoragePreoutFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgBSpStoragePreoutFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorageSharedPreoutFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOut;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOutItem;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOutItemLog;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOut;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItem;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.share.common.SgShareConstants;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutItemLogMapper;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutItemMapper;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutMapper;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutInfoBySourceResult;
import com.burgeon.r3.sg.sourcing.model.request.SgOmsSplitOrderRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgOmsSplitSubOrderItemRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgOmsSplitSubOrderRequest;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutItemMapper;
import com.burgeon.r3.sg.store.mapper.out.SgBStoOutMapper;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutInfoBySourceResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BeanCopierUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 根据oms拆单结果回写库存中心单据
 * @author: hwy
 * @time: 2021/7/13 13:19
 */
@Component
@Slf4j
public class SgOmsSplitOrderService {

    @Autowired
    private SgBStoOutMapper sgBStoOutMapper;

    @Autowired
    private SgBStoOutItemMapper sgBStoOutItemMapper;

    @Autowired
    private SgBStorageSharedPreoutFtpMapper sharedPreoutFtpMapper;

    @Autowired
    private SgBShareOutMapper sgBShareOutMapper;

    @Autowired
    private SgBShareOutItemMapper sgBShareOutItemMapper;

    @Autowired
    private SgBShareOutItemLogMapper sgBShareOutItemLogMapper;

    @Autowired
    private SgBSaStoragePreoutFtpMapper SgBSaPreoutFtpMapper;

    @Autowired
    private SgBSpStoragePreoutFtpMapper sgBSpPreoutFtpMapper;

    @Autowired
    private CpCStoreMapper cpCStoreMapper;

    //@Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 splitOrder(SgOmsSplitOrderRequest request) {

        log.info(LogUtil.format("SgOmsSplitOrderService.splitOrder request:{}",
                "SgOmsSplitOrderService.splitOrder"), JSONObject.toJSONString(request));

        ValueHolderV14 valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        checkParam(request, valueHolderV14);
        if (!valueHolderV14.isOK()) {
            log.info("SgOmsSplitOrderService.splitOrder valueHolderV14:{}", valueHolderV14.getMessage());
            return valueHolderV14;
        }

        // 保证事务一致
        SgOmsSplitOrderService bean = ApplicationContextHandle.getBean(SgOmsSplitOrderService.class);
        try {
            bean.updateShareAndStoBillInfo(request);
        } catch (Exception e) {
            log.error("更新库存单据来源信息服务 来源单据id{} 更新库存单据信息失败:{}", request.getSourceBillId(), Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
            return valueHolderV14;
        }
        return valueHolderV14;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateShareAndStoBillInfo(SgOmsSplitOrderRequest request) {
        //逻辑层（更新逻辑占用单信息和逻辑仓共享占用流水，先插入原单流水再插入新单流水）
        HashMap<String, Object> map = updateStoOutBillInfo2(request);
        //配销层
        updateShareOutBillInfo2(request, map);
    }

    /**
     * @param request:
     * @Description: 更新共享占用单以及流水
     * @Author: hwy
     * @Date: 2021/8/2 11:35
     * @return: void
     **/
    public void updateShareOutBillInfo(SgOmsSplitOrderRequest request, List<SgBStoOutInfoBySourceResult> stoOutResults) {
        try {
            User loginUser = R3SystemUserResource.getSystemRootUser();
            Long sourceBillId = request.getSourceBillId();
            Integer sourceBillType = request.getSourceBillType();
            List<SgOmsSplitSubOrderRequest> subOrderList = request.getSubOrderList();
            List<SgBShareOutInfoBySourceResult> outInfoBySourceResults = sgBShareOutMapper.selectListBySource(sourceBillId, sourceBillType);
            if (CollectionUtils.isEmpty(outInfoBySourceResults)) {
                log.error("SgOmsSplitOrderService.updateShareOutBillInfo 更新库存单据来源信息服务 来源单据id{} 来源单据编号:{} 该来源单据对应的共享占用单已不存在", sourceBillId, sourceBillType);
                throw new NDSException("更新库存单据来源信息服务失败 该来源单据对应的共享占用单不存在");
            }
            if (log.isDebugEnabled()) {
                log.debug("SgOmsSplitOrderService selectListBySource outInfoBySourceResults {} ",
                        JSONObject.toJSONString(outInfoBySourceResults));
            } else {
                log.info("SgOmsSplitOrderService selectListBySource outInfoBySourceResults {} ",
                        JSONObject.toJSONString(outInfoBySourceResults));
            }

            //共享占用明细map   key为来源明细id
            Map<String, List<SgBShareOutInfoBySourceResult>> shareOutMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            //聚合仓id
            List<Long> sgShareStoreIds = new ArrayList<>();
            //配销仓流水明细  通过占用单据编号去查询
            List<String> saBillNos = new ArrayList<>();
            //共享池流水明细  通过占用单据编号去查询
            List<String> spBillNos = new ArrayList<>();
            //共享占用单id
            List<Long> shareOutIds = new ArrayList<>();
            //共享占用单明细id
            List<Long> shareOutItemIds = new ArrayList<>();
            outInfoBySourceResults.stream().forEach(outInfoSource -> {
                Long sgShareStoreId = outInfoSource.getSgShareStoreId();
                Long sourceBillItemId = outInfoSource.getSourceBillItemId();
                Long itemId = outInfoSource.getItemId();
                String shareBillNo = outInfoSource.getShareBillNo();
                Long shareId = outInfoSource.getShareId();
                String key = sgShareStoreId + ":" + sourceBillItemId;
                sgShareStoreIds.add(sgShareStoreId);
                if (outInfoSource.getSaId() != null) {
                    saBillNos.add(shareBillNo);
                } else {
                    spBillNos.add(shareBillNo);
                }
                if (shareOutMap.containsKey(key)) {
                    List<SgBShareOutInfoBySourceResult> share = shareOutMap.get(key);
                    share.add(outInfoSource);
                } else {
                    List<SgBShareOutInfoBySourceResult> share = new ArrayList<>();
                    share.add(outInfoSource);
                    shareOutMap.put(key, share);
                }
                if (!shareOutIds.contains(shareId)) {
                    shareOutIds.add(shareId);
                }
                shareOutItemIds.add(itemId);
            });
            //查询共享占用单配销仓流水信息
            List<SgBSaStoragePreoutFtp> saStoragePreoutFtps = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(saBillNos)) {
                saStoragePreoutFtps = SgBSaPreoutFtpMapper.selectListMaster(new LambdaQueryWrapper<SgBSaStoragePreoutFtp>()
                        .in(SgBSaStoragePreoutFtp::getBillNo, saBillNos));
            }
            //组装配销仓占用 负流水
            List<SgBSaStoragePreoutFtp> negativeSaPreoutFtps = new ArrayList<>();
            Map<Long, SgBSaStoragePreoutFtp> saStoragePreoutFtpMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            for (SgBSaStoragePreoutFtp storagePreoutFtp : saStoragePreoutFtps) {
                SgBSaStoragePreoutFtp saPreoutFtp = new SgBSaStoragePreoutFtp();
                BeanUtils.copyProperties(storagePreoutFtp, saPreoutFtp);
                BigDecimal qtyEnd = storagePreoutFtp.getQtyEnd();
                BigDecimal qtyBegin = storagePreoutFtp.getQtyBegin();
                BigDecimal qtyChange = storagePreoutFtp.getQtyChange();
                saPreoutFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SA_STORAGE_PREOUT_FTP));
                saPreoutFtp.setQtyChange(qtyChange.negate());
                saPreoutFtp.setQtyBegin(qtyEnd);
                saPreoutFtp.setQtyEnd(qtyBegin);
                saPreoutFtp.setRemark("更新库存单据来源信息");
                negativeSaPreoutFtps.add(saPreoutFtp);

                Long billItemId = storagePreoutFtp.getBillItemId();
                saStoragePreoutFtpMap.put(billItemId, storagePreoutFtp);
            }
            List<SgBSaStoragePreoutFtp> newSaPreoutFtps = new ArrayList<>();

            //查询共享占用单共享池流水信息
            List<SgBSpStoragePreoutFtp> spStoragePreoutFtps = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(spBillNos)) {
                spStoragePreoutFtps = sgBSpPreoutFtpMapper.selectListMaster(new LambdaQueryWrapper<SgBSpStoragePreoutFtp>()
                        .in(SgBSpStoragePreoutFtp::getBillNo, spBillNos));
            }
            //组装共享池占用 负流水
            List<SgBSpStoragePreoutFtp> negativeSpPreoutFtps = new ArrayList<>();
            Map<Long, SgBSpStoragePreoutFtp> spStoragePreoutFtpMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            for (SgBSpStoragePreoutFtp storagePreoutFtp : spStoragePreoutFtps) {
                SgBSpStoragePreoutFtp spPreoutFtp = new SgBSpStoragePreoutFtp();
                BeanUtils.copyProperties(storagePreoutFtp, spPreoutFtp);
                BigDecimal qtyEnd = storagePreoutFtp.getQtyEnd();
                BigDecimal qtyBegin = storagePreoutFtp.getQtyBegin();
                BigDecimal qtyChange = storagePreoutFtp.getQtyChange();
                spPreoutFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SP_STORAGE_PREOUT_FTP));
                spPreoutFtp.setQtyChange(qtyChange.negate());
                spPreoutFtp.setQtyBegin(qtyEnd);
                spPreoutFtp.setQtyEnd(qtyBegin);
                negativeSpPreoutFtps.add(spPreoutFtp);

                Long billItemId = storagePreoutFtp.getBillItemId();
                spStoragePreoutFtpMap.put(billItemId, storagePreoutFtp);
            }

            List<SgBSpStoragePreoutFtp> newSpPreoutFtps = new ArrayList<>();

            //查询对应聚合仓下所有的逻辑仓  cp_c_store
            List<SgCpCStore> sgCpCStores = cpCStoreMapper.selectList(new LambdaQueryWrapper<SgCpCStore>()
                    .select(SgCpCStore::getId, SgCpCStore::getSgCShareStoreId)
                    .in(SgCpCStore::getSgCShareStoreId, sgShareStoreIds)
                    .eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y));
            //key为逻辑仓id  value 为聚合仓id
            Map<Long, Long> sgCpMaps = sgCpCStores.stream().collect(Collectors.toMap(o -> o.getId(), o -> o.getSgCShareStoreId()));
            //查询逻辑占用map
            Map<String, List<SgBStoOutInfoBySourceResult>> stoMap = stoOutResults.stream().collect(Collectors.groupingBy(SgBStoOutInfoBySourceResult::getStoBillNo));
            //入参逻辑占用单map
            Map<String, SgOmsSplitSubOrderRequest> requestStoMap = subOrderList.stream().collect(Collectors.toMap(SgOmsSplitSubOrderRequest::getSgStoOutBillNo, Function.identity()));

            //历史共享占用单
            List<SgBShareOut> oldShareOuts = sgBShareOutMapper.selectList(new LambdaQueryWrapper<SgBShareOut>()
                    .in(SgBShareOut::getId, shareOutIds)
                    .eq(SgBShareOut::getIsactive, SgConstants.IS_ACTIVE_Y));
            Map<Long, SgBShareOut> oldShareOutMap = oldShareOuts.stream().collect(Collectors.toMap(SgBShareOut::getId, Function.identity()));
            //历史共享占用明细单
            List<SgBShareOutItem> oldShareOutItems = sgBShareOutItemMapper.selectList(new LambdaQueryWrapper<SgBShareOutItem>()
                    .in(SgBShareOutItem::getId, shareOutItemIds)
                    .eq(SgBShareOutItem::getIsactive, SgConstants.IS_ACTIVE_Y));
            Map<Long, SgBShareOutItem> oldShareOutItemsMap = oldShareOutItems.stream().collect(Collectors.toMap(SgBShareOutItem::getId, Function.identity()));

            //新增共享占用单map
            Map<String, SgBShareOut> insertShareOutMap = Maps.newHashMap();
            //新增共享占用单明细map
            List<SgBShareOutItem> insertShareOutItemList = new ArrayList<>();
            if (CollectionUtils.isEmpty(saStoragePreoutFtps) && CollectionUtils.isEmpty(spStoragePreoutFtps)) {
                throw new NDSException("更新库存单据来源信息服务失败 该来源单据对应的共享占用单未查询到对应流水");
            }
            if (log.isDebugEnabled()) {
                log.debug("SgOmsSplitOrderService stoMap entrySet {}  insertShareOutItemList {}",
                        JSONObject.toJSONString(stoMap), JSONObject.toJSONString(insertShareOutItemList));
            } else {
                log.info("SgOmsSplitOrderService stoMap entrySet {}  insertShareOutItemList {}",
                        JSONObject.toJSONString(stoMap), JSONObject.toJSONString(insertShareOutItemList));
            }
            for (Map.Entry<String, List<SgBStoOutInfoBySourceResult>> entry : stoMap.entrySet()) {
                List<SgBStoOutInfoBySourceResult> value = entry.getValue();
                String billNo = entry.getKey();
                //新的来源单据信息
                SgOmsSplitSubOrderRequest orderRequest = requestStoMap.get(billNo);
                String newOrderBillNo = orderRequest.getNewOrderBillNo();
                Long newOrderBillId = orderRequest.getNewOrderBillId();
                Integer newOrderBillType = orderRequest.getNewOrderBillType();
                List<SgOmsSplitSubOrderItemRequest> orderItemList = orderRequest.getOrderItemList();
                Map<Long, SgOmsSplitSubOrderItemRequest> itemIdMap = orderItemList.stream().collect(Collectors.
                        toMap(SgOmsSplitSubOrderItemRequest::getOldSourceItemId, Function.identity()));
//                Map<Long, List<SgOmsSplitSubOrderItemRequest>> groupByMap = orderItemList.stream()
//                        .collect(Collectors.groupingBy(SgOmsSplitSubOrderItemRequest::getOldSourceItemId));
                //逻辑占用单集合
                for (SgBStoOutInfoBySourceResult stoOutInfoBySourceResult : value) {
                    Long cStoreId = stoOutInfoBySourceResult.getCpCStoreId();
                    Long shareId = sgCpMaps.get(cStoreId);
                    Long stoBillItemId = stoOutInfoBySourceResult.getSourceBillItemId();
                    //SgBShareOutInfoBySourceResult shareOut = shareOutMap.get(stoBillItemId);

                    String shareKey = shareId + ":" + stoBillItemId;
                    List<SgBShareOutInfoBySourceResult> sourceResults = shareOutMap.get(shareKey);
                    if (CollectionUtils.isEmpty(sourceResults)) {
                        continue;
                    }

                    for (SgBShareOutInfoBySourceResult shareOut : sourceResults) {
                        SgOmsSplitSubOrderItemRequest itemRequest = itemIdMap.get(stoBillItemId);
                        Long newSourceItemId = itemRequest.getNewSourceItemId();
                        BigDecimal changeQty = itemRequest.getQty();
                        String key = billNo + "-" + shareId;
                        if (shareId.equals(shareOut.getSgShareStoreId())) {
                            Long itemId = shareOut.getItemId();
                            if (!insertShareOutMap.containsKey(key)) {
                                //不存在 创建共享占用单
                                SgBShareOut oldShareOut = oldShareOutMap.get(shareOut.getShareId());
                                SgBShareOut newShareOut = new SgBShareOut();
                                BeanUtils.copyProperties(oldShareOut, newShareOut);
                                newShareOut.setId(ModelUtil.getSequence(SgConstants.SG_B_SHARE_OUT));
                                newShareOut.setBillNo(SgStoreUtils.getBillNo(SgShareConstants.SEQ_SG_B_SHARE_OUT,
                                        SgConstants.SG_B_SHARE_OUT.toUpperCase().toUpperCase(), newShareOut, loginUser.getLocale()));
                                newShareOut.setSourceBillId(newOrderBillId);
                                newShareOut.setSourceBillNo(newOrderBillNo);
                                newShareOut.setSourceBillType(newOrderBillType);
                                newShareOut.setTotQtyOrign(BigDecimal.ZERO);
                                newShareOut.setTotQtyOut(BigDecimal.ZERO);
                                newShareOut.setTotQtyPreout(BigDecimal.ZERO);
                                newShareOut.setTotRowNum(0);
                                newShareOut.setRemark("更新库存单据来源信息");
                                StorageUtils.setBModelDefalutData(newShareOut, loginUser);
                                insertShareOutMap.put(key, newShareOut);
                            }
                            //存在添加明细
                            SgBShareOut sgBShareOut = insertShareOutMap.get(key);

                            //主表id
                            Long id = sgBShareOut.getId();
                            SgBShareOutItem oldShareOutItem = oldShareOutItemsMap.get(itemId);
                            sgBShareOut.setTotQtyOrign(sgBShareOut.getTotQtyOrign().add(changeQty));
                            Integer totRowNum = sgBShareOut.getTotRowNum();
                            sgBShareOut.setTotRowNum(++totRowNum);
                            sgBShareOut.setTotQtyPreout(sgBShareOut.getTotQtyPreout().add(changeQty));
                            //true为Sp  false为Sa
                            Boolean isSaOrSpflag = oldShareOutItem.getSgCSaStoreId() == null ? true : false;
                            SgBShareOutItem newShareOutItem = new SgBShareOutItem();
                            BeanUtils.copyProperties(oldShareOutItem, newShareOutItem);
                            newShareOutItem.setId(ModelUtil.getSequence(SgConstants.SG_B_SHARE_OUT_ITEM));
                            newShareOutItem.setSourceBillItemId(newSourceItemId);
                            newShareOutItem.setSgBShareOutId(id);
                            newShareOutItem.setQtyPreout(changeQty);
                            newShareOutItem.setQty(changeQty);
                            StorageUtils.setBModelDefalutData(newShareOutItem, loginUser);
                            insertShareOutItemList.add(newShareOutItem);

                            //生成配销或者配销的流水
                            if (isSaOrSpflag) {
                                //sp  获取原流水的期初
                                if (!spStoragePreoutFtpMap.containsKey(itemId)) {
                                    throw new NDSException("该来源单据对应的共享占用单未查询到对应流水");
                                }
                                SgBSpStoragePreoutFtp spPreoutFtp = spStoragePreoutFtpMap.get(itemId);
                                BigDecimal qtyBegin = spPreoutFtp.getQtyBegin();

                                SgBSpStoragePreoutFtp newPreoutFtp = new SgBSpStoragePreoutFtp();
                                BeanUtils.copyProperties(spPreoutFtp, newPreoutFtp);
                                BigDecimal endQty = qtyBegin.add(changeQty);
                                newPreoutFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SP_STORAGE_PREOUT_FTP));
                                newPreoutFtp.setBillNo(sgBShareOut.getBillNo());
                                newPreoutFtp.setBillId(sgBShareOut.getId());
                                newPreoutFtp.setSourceBillId(newOrderBillId);
                                newPreoutFtp.setSourceBillNo(newOrderBillNo);
                                newPreoutFtp.setBillItemId(newShareOutItem.getId());
                                newPreoutFtp.setQtyBegin(qtyBegin);
                                newPreoutFtp.setQtyChange(changeQty);
                                newPreoutFtp.setRemark("更新库存单据来源信息");
                                newPreoutFtp.setQtyEnd(endQty);
                                newSpPreoutFtps.add(newPreoutFtp);
                                //期初加变动 为下一次期初
                                spPreoutFtp.setQtyBegin(endQty);
                            } else {
                                //sa
                                if (!saStoragePreoutFtpMap.containsKey(itemId)) {
                                    throw new NDSException("该来源单据对应的共享占用单未查询到对应流水");
                                }
                                SgBSaStoragePreoutFtp saPreoutFtp = saStoragePreoutFtpMap.get(itemId);
                                BigDecimal qtyBegin = saPreoutFtp.getQtyBegin();

                                SgBSaStoragePreoutFtp newPreoutFtp = new SgBSaStoragePreoutFtp();
                                BeanUtils.copyProperties(saPreoutFtp, newPreoutFtp);
                                newPreoutFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SA_STORAGE_PREOUT_FTP));
                                newPreoutFtp.setBillNo(sgBShareOut.getBillNo());
                                newPreoutFtp.setBillId(sgBShareOut.getId());
                                newPreoutFtp.setSourceBillId(newOrderBillId);
                                newPreoutFtp.setSourceBillNo(newOrderBillNo);
                                newPreoutFtp.setBillItemId(newShareOutItem.getId());
                                newPreoutFtp.setQtyBegin(qtyBegin);
                                newPreoutFtp.setQtyChange(changeQty);
                                newPreoutFtp.setRemark("更新库存单据来源信息");
                                BigDecimal endQty = qtyBegin.add(changeQty);
                                newPreoutFtp.setQtyEnd(endQty);
                                newSaPreoutFtps.add(newPreoutFtp);
                                //期初加变动 为下一次期初
                                saPreoutFtp.setQtyBegin(endQty);
                            }
                        }
                    }
                }
            }


            //作废对应共享占用单和明细;
            SgBShareOut update = new SgBShareOut();
            update.setIsactive(SgConstants.IS_ACTIVE_N);
            update.setBillStatus(4);
            sgBShareOutMapper.update(update, new LambdaQueryWrapper<SgBShareOut>()
                    .in(SgBShareOut::getId, shareOutIds));
            SgBShareOutItem updataItem = new SgBShareOutItem();
            updataItem.setIsactive(SgConstants.IS_ACTIVE_N);
            sgBShareOutItemMapper.update(updataItem, new LambdaQueryWrapper<SgBShareOutItem>()
                    .in(SgBShareOutItem::getId, shareOutItemIds));
            //新增共享占用单

            List<SgBShareOut> shareOuts = new ArrayList<>(insertShareOutMap.values());
            if (log.isDebugEnabled()) {
                log.debug("SgOmsSplitOrderService insert shareOuts {}  insertShareOutItemList {}",
                        JSONObject.toJSONString(shareOuts), JSONObject.toJSONString(insertShareOutItemList));
            } else {
                log.info("SgOmsSplitOrderService insert shareOuts {}  insertShareOutItemList {}",
                        JSONObject.toJSONString(shareOuts), JSONObject.toJSONString(insertShareOutItemList));
            }
            sgBShareOutMapper.batchInsert(shareOuts);
            sgBShareOutItemMapper.batchInsert(insertShareOutItemList);

            //配销仓/共享池 流水新增
            if (CollectionUtils.isNotEmpty(newSaPreoutFtps)) {
                if (log.isDebugEnabled()) {
                    log.debug("SgOmsSplitOrderService insertSaPreoutFtp negativeSaPreoutFtps {}  newSaPreoutFtps {}",
                            JSONObject.toJSONString(negativeSaPreoutFtps), JSONObject.toJSONString(newSaPreoutFtps));
                } else {
                    log.info("SgOmsSplitOrderService insertSaPreoutFtp negativeSaPreoutFtps {}  newSaPreoutFtps {}",
                            JSONObject.toJSONString(negativeSaPreoutFtps), JSONObject.toJSONString(newSaPreoutFtps));
                }
                SgBSaPreoutFtpMapper.batchInsert(negativeSaPreoutFtps);
                SgBSaPreoutFtpMapper.batchInsert(newSaPreoutFtps);
            }
            if (CollectionUtils.isNotEmpty(newSpPreoutFtps)) {
                if (log.isDebugEnabled()) {
                    log.debug("SgOmsSplitOrderService insertSpPreoutFtp negativeSpPreoutFtps {}  newSpPreoutFtps {}",
                            JSONObject.toJSONString(negativeSpPreoutFtps), JSONObject.toJSONString(newSpPreoutFtps));
                } else {
                    log.info("SgOmsSplitOrderService insertSpPreoutFtp negativeSpPreoutFtps {}  newSpPreoutFtps {}",
                            JSONObject.toJSONString(negativeSpPreoutFtps), JSONObject.toJSONString(newSpPreoutFtps));
                }
                sgBSpPreoutFtpMapper.batchInsert(negativeSpPreoutFtps);
                sgBSpPreoutFtpMapper.batchInsert(newSpPreoutFtps);
            }

        } catch (Exception e) {
            log.error("更新库存单据来源信息服务 来源单据id{} 更新共享占用单失败:{}", request.getSourceBillId(), Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        }

    }

    /**
     * @param request:
     * @Description: 更新逻辑占用单以及流水
     * @Author: hwy
     * @Date: 2021/8/2 11:34
     * @return: void
     **/
    public List<SgBStoOutInfoBySourceResult> updateStoOutBillInfo(SgOmsSplitOrderRequest request) {
        try {
            Long sourceBillId = request.getSourceBillId();
            Integer sourceBillType = request.getSourceBillType();
            List<SgBStoOutInfoBySourceResult> sgBStoOuts = sgBStoOutMapper.selectListBySource(sourceBillId, sourceBillType);

            if (CollectionUtils.isEmpty(sgBStoOuts)) {
                log.error("SgOmsSplitOrderService.updateStoOutBillInfo 更新库存单据来源信息服务 来源单据id{} 来源单据编号:{} 该来源单据对应的逻辑占用单已不存在", sourceBillId, sourceBillType);
                throw new NDSException("更新库存单据来源信息服务失败 该来源单据对应的逻辑占用单不存在");
            }
            //逻辑占用单编号
            List<String> billNos = new ArrayList<>();
            // key: stoBillNo_sourceBillItemId  逻辑占用单编号 + 来源明细id
            Map<String, List<SgBStoOutInfoBySourceResult>> stoBillMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            sgBStoOuts.stream().forEach(stoOut -> {
                String stoBillNo = stoOut.getStoBillNo();
                Long sourceBillItemId = stoOut.getSourceBillItemId();
                Long itemId = stoOut.getItemId();
                String itemKey = stoBillNo + "_" + sourceBillItemId;
                if (!stoBillMap.containsKey(itemKey)) {
                    List<SgBStoOutInfoBySourceResult> stoOuts = new ArrayList<>();
                    stoOuts.add(stoOut);
                    stoBillMap.put(itemKey, stoOuts);
                } else {
                    List<SgBStoOutInfoBySourceResult> stoOuts = stoBillMap.get(itemKey);
                    stoOuts.add(stoOut);
                }
                if (!billNos.contains(stoBillNo)) {
                    billNos.add(stoBillNo);
                }
            });
            List<SgBStorageSharedPreoutFtp> sharedPreoutFtps = sharedPreoutFtpMapper.selectListMaster(new QueryWrapper<SgBStorageSharedPreoutFtp>().lambda()
                    .in(SgBStorageSharedPreoutFtp::getBillNo, billNos));
            if (sharedPreoutFtps.size() != sgBStoOuts.size()) {
                throw new NDSException("该来源单据对应的逻辑占用单未查询到对应流水");
            }
            List<SgBStorageSharedPreoutFtp> newSharedPreoutFtps = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
            //通过 billItemId进行聚合
            Map<Long, List<SgBStorageSharedPreoutFtp>> newSharedPreoutFtpMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            if (CollectionUtils.isNotEmpty(sharedPreoutFtps)) {
                sharedPreoutFtps.stream().forEach(o -> {
                    SgBStorageSharedPreoutFtp newsharedPreoutFtp = new SgBStorageSharedPreoutFtp();
                    BeanCopierUtil.copy(o, newsharedPreoutFtp);
                    BigDecimal qtyEnd = o.getQtyEnd();
                    BigDecimal qtyBegin = o.getQtyBegin();
                    BigDecimal qtyChange = o.getQtyChange();
                    o.setQtyBegin(qtyEnd);
                    o.setQtyBegin(qtyBegin);
                    o.setQtyChange(qtyChange.negate());
                    Long cancelId = ModelUtil.getSequence(SgConstants.SG_B_STORAGE_SHARED_PREOUT_FTP);
                    Long newId = ModelUtil.getSequence(SgConstants.SG_B_STORAGE_SHARED_PREOUT_FTP);
                    o.setId(cancelId);
                    o.setRemark("更新库存单据来源信息");
                    newsharedPreoutFtp.setId(newId);
                    newsharedPreoutFtp.setRemark("更新库存单据来源信息");
                    newSharedPreoutFtps.add(newsharedPreoutFtp);
                });
                newSharedPreoutFtpMap.putAll(newSharedPreoutFtps.stream().collect(Collectors.groupingBy(SgBStorageSharedPreoutFtp::getBillItemId)));
            } else {
                throw new NDSException("更新库存单据来源信息服务 该来源单据对应的逻辑占用单未查询到对应流水");
            }
            // 待更新的逻辑占用单
            Map<Long, SgBStoOut> updateStoOutBillMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            List<SgBStoOutItem> sgBStoOutItems = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
            List<SgOmsSplitSubOrderRequest> subOrderList = request.getSubOrderList();
            subOrderList.stream().forEach(o -> {
                String sgStoOutBillNo = o.getSgStoOutBillNo();
                Long newOrderBillId = o.getNewOrderBillId();
                String newOrderBillNo = o.getNewOrderBillNo();
                Integer newOrderBillType = o.getNewOrderBillType();
                List<SgOmsSplitSubOrderItemRequest> orderItemList = o.getOrderItemList();
                orderItemList.stream().forEach(i -> {
                    Long oldSourceItemId = i.getOldSourceItemId();
                    String itemKey = sgStoOutBillNo + "_" + oldSourceItemId;
                    if (!stoBillMap.containsKey(itemKey)) {
                        return;
                    }
                    Long newSourceItemId = i.getNewSourceItemId();
                    List<SgBStoOutInfoBySourceResult> sgBStoOutInfoBySourceResults = stoBillMap.get(itemKey);
                    sgBStoOutInfoBySourceResults.stream().forEach(j -> {
                        Long stoId = j.getStoId();
                        Long itemId = j.getItemId();
                        // 设置待更新主表
                        if (!updateStoOutBillMap.containsKey(stoId)) {
                            SgBStoOut sgBStoOut = new SgBStoOut();
                            sgBStoOut.setId(stoId);
                            sgBStoOut.setSourceBillId(newOrderBillId);
                            sgBStoOut.setSourceBillNo(newOrderBillNo);
                            sgBStoOut.setSourceBillType(newOrderBillType);
                            updateStoOutBillMap.put(stoId, sgBStoOut);
                        }
                        // 设置待更新明细
                        SgBStoOutItem sgBStoOutItem = new SgBStoOutItem();
                        sgBStoOutItem.setId(j.getItemId());
                        sgBStoOutItem.setSourceBillItemId(newSourceItemId);
                        sgBStoOutItems.add(sgBStoOutItem);
                        if (!newSharedPreoutFtpMap.containsKey(itemId)) {
                            return;
                        }
                        List<SgBStorageSharedPreoutFtp> sgBStorageSharedPreoutFtps = newSharedPreoutFtpMap.get(itemId);
                        sgBStorageSharedPreoutFtps.stream().forEach(k -> {
                            k.setSourceBillId(newSourceItemId);
                            k.setSourceBillNo(newOrderBillNo);
                        });
                    });
                });
            });
            // 更新主表数据
            if (MapUtils.isNotEmpty(updateStoOutBillMap)) {
                List<SgBStoOut> updateSgBStoOuts = new ArrayList<>(updateStoOutBillMap.values());
                sgBStoOutMapper.batchUpdateById(updateSgBStoOuts);
            }
            // 更新明细数据
            if (CollectionUtils.isNotEmpty(sgBStoOutItems)) {
                sgBStoOutItemMapper.batchUpdateById(SgConstants.SG_B_STO_OUT_ITEM, sgBStoOutItems);
            }
            // 修改流水数据
            if (CollectionUtils.isNotEmpty(sharedPreoutFtps)) {
                sharedPreoutFtpMapper.batchInsert(sharedPreoutFtps);
                sharedPreoutFtpMapper.batchInsert(newSharedPreoutFtps);
            }
            return sgBStoOuts;
        } catch (Exception e) {
            log.error("更新库存单据来源信息服务 来源单据id{} 更新逻辑占用单失败:{}", request.getSourceBillId(), Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        }
    }

    /**
     * 更新逻辑占用单以及流水  sku合并
     *
     * @param request
     * @return
     */
    public HashMap<String, Object> updateStoOutBillInfo2(SgOmsSplitOrderRequest request) {
        try {
            Long sourceBillId = request.getSourceBillId();
            Integer sourceBillType = request.getSourceBillType();
            List<SgBStoOutInfoBySourceResult> sgBStoOuts = sgBStoOutMapper.selectListBySource(sourceBillId, sourceBillType);

            if (CollectionUtils.isEmpty(sgBStoOuts)) {
                log.error("SgOmsSplitOrderService.updateStoOutBillInfo 更新库存单据来源信息服务 来源单据id{} 来源单据编号:{} 该来源单据对应的逻辑占用单已不存在", sourceBillId, sourceBillType);
                throw new NDSException("更新库存单据来源信息服务失败 该来源单据对应的逻辑占用单不存在");
            }

            log.info(" SgOmsSplitOrderService updateStoOutBillInfo sgBStoOuts {}", JSONObject.toJSONString(sgBStoOuts));

            //逻辑占用单编号
            List<String> billNos = new ArrayList<>();
            //标记为已合单的逻辑占用单编号
            List<String> mergeBillNos = new ArrayList<>();

            // key: stoBillNo_sourceBillItemId  逻辑占用单编号 + 来源明细id
            Map<String, List<SgBStoOutInfoBySourceResult>> stoBillMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            sgBStoOuts.forEach(stoOut -> {
                String stoBillNo = stoOut.getStoBillNo();
                Long sourceBillItemId = stoOut.getSourceBillItemId();
                Boolean mergeMark = stoOut.getMergeMark();
                String itemKey = stoBillNo + "_" + sourceBillItemId;
                if (!stoBillMap.containsKey(itemKey)) {
                    List<SgBStoOutInfoBySourceResult> stoOuts = new ArrayList<>();
                    stoOuts.add(stoOut);
                    stoBillMap.put(itemKey, stoOuts);
                } else {
                    List<SgBStoOutInfoBySourceResult> stoOuts = stoBillMap.get(itemKey);
                    stoOuts.add(stoOut);
                }
                if (!billNos.contains(stoBillNo)) {
                    billNos.add(stoBillNo);
                }
                if (mergeMark && !mergeBillNos.contains(stoBillNo)) {
                    mergeBillNos.add(stoBillNo);
                }
            });

            //log明细
            Map<String, List<SgBStoOutInfoBySourceResult>> stoBillLogMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            List<SgBStoOutInfoBySourceResult> sgBStoOutLogs = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);

            if (CollectionUtils.isNotEmpty(mergeBillNos)) {
                sgBStoOutLogs = sgBStoOutMapper.selectListByLogSource(sourceBillId, sourceBillType);
                for (SgBStoOutInfoBySourceResult sgBStoOutLog : sgBStoOutLogs) {
                    String stoBillNo = sgBStoOutLog.getStoBillNo();
                    Long sourceBillItemId = sgBStoOutLog.getSourceBillItemId();
                    String itemKey = stoBillNo + "_" + sourceBillItemId;
                    if (!stoBillLogMap.containsKey(itemKey)) {
                        List<SgBStoOutInfoBySourceResult> stoOuts = new ArrayList<>();
                        stoOuts.add(sgBStoOutLog);
                        stoBillLogMap.put(itemKey, stoOuts);
                    } else {
                        List<SgBStoOutInfoBySourceResult> stoOuts = stoBillLogMap.get(itemKey);
                        stoOuts.add(sgBStoOutLog);
                    }
                }
            }
            if (log.isDebugEnabled()) {
                log.debug("SgOmsSplitOrderService updateStoOutBillInfo stoBillMap {} stoBillLogMap {}",
                        JSONObject.toJSONString(stoBillMap), JSONObject.toJSONString(stoBillLogMap));
            }

            List<SgBStorageSharedPreoutFtp> sharedPreoutFtps = sharedPreoutFtpMapper.selectListMaster(new QueryWrapper<SgBStorageSharedPreoutFtp>().lambda()
                    .in(SgBStorageSharedPreoutFtp::getBillNo, billNos));
            log.info(" SgOmsSplitOrderService updateStoOutBillInfo sharedPreoutFtps {}", JSONObject.toJSONString(sharedPreoutFtps));
            if (sharedPreoutFtps.size() != sgBStoOuts.size()) {
                throw new NDSException("该来源单据对应的逻辑占用单未查询到对应流水");
            }

            // TODO 暂时没想到这个鬼玩意儿干啥用的-- 理论上来说，现在存在原流水，
            //  需要生成的流水数据：对冲原流水、新流水：带上新的订单信息
            List<SgBStorageSharedPreoutFtp> newSharedPreoutFtps = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
            //通过 billItemId进行聚合
            Map<Long, List<SgBStorageSharedPreoutFtp>> newSharedPreoutFtpMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            sharedPreoutFtps.forEach(o -> {
                SgBStorageSharedPreoutFtp newsharedPreoutFtp = new SgBStorageSharedPreoutFtp();
                BeanCopierUtil.copy(o, newsharedPreoutFtp);
                BigDecimal qtyEnd = o.getQtyEnd();
                BigDecimal qtyBegin = o.getQtyBegin();
                BigDecimal qtyChange = o.getQtyChange();
                o.setQtyBegin(qtyEnd);
                o.setQtyEnd(qtyBegin);
                o.setQtyChange(qtyChange.negate());
                Long cancelId = ModelUtil.getSequence(SgConstants.SG_B_STORAGE_SHARED_PREOUT_FTP);
                Long newId = ModelUtil.getSequence(SgConstants.SG_B_STORAGE_SHARED_PREOUT_FTP);
                o.setId(cancelId);
                o.setRemark("更新库存单据来源信息");
                o.setCreationdate(new Date());
                o.setModifieddate(new Date());
                newsharedPreoutFtp.setId(newId);
                newsharedPreoutFtp.setCreationdate(new Date());
                newsharedPreoutFtp.setModifieddate(new Date());
                newsharedPreoutFtp.setRemark("更新库存单据来源信息");
                newSharedPreoutFtps.add(newsharedPreoutFtp);
            });
            newSharedPreoutFtpMap.putAll(newSharedPreoutFtps.stream().collect(Collectors.groupingBy(SgBStorageSharedPreoutFtp::getBillItemId)));

            // 待更新的逻辑占用单
            Map<Long, SgBStoOut> updateStoOutBillMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            // 待更新明细
            List<SgBStoOutItem> sgBStoOutItems = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
            // 待更新log明细
            List<SgBStoOutItem> sgBStoOutLogItems = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
            List<SgOmsSplitSubOrderRequest> subOrderList = request.getSubOrderList();
            subOrderList.forEach(o -> {
                String sgStoOutBillNo = o.getSgStoOutBillNo();
                Long newOrderBillId = o.getNewOrderBillId();
                String newOrderBillNo = o.getNewOrderBillNo();
                Integer newOrderBillType = o.getNewOrderBillType();
                List<SgOmsSplitSubOrderItemRequest> orderItemList = o.getOrderItemList();
                orderItemList.forEach(i -> {
                    Long oldSourceItemId = i.getOldSourceItemId();
                    String itemKey = sgStoOutBillNo + "_" + oldSourceItemId;
                    // TODO 理论上不会进这个判断，如果进了，那就是有问题
                    if (!stoBillMap.containsKey(itemKey)) {
                        return;
                    }
                    Long newSourceItemId = i.getNewSourceItemId();
                    List<SgBStoOutInfoBySourceResult> sgBStoOutInfoBySourceResults = stoBillMap.get(itemKey);
                    sgBStoOutInfoBySourceResults.forEach(j -> {
                        Long stoId = j.getStoId();
                        Long itemId = j.getItemId();
                        // 设置待更新主表
                        if (!updateStoOutBillMap.containsKey(stoId)) {
                            SgBStoOut sgBStoOut = new SgBStoOut();
                            sgBStoOut.setId(stoId);
                            sgBStoOut.setSourceBillId(newOrderBillId);
                            sgBStoOut.setSourceBillNo(newOrderBillNo);
                            sgBStoOut.setSourceBillType(newOrderBillType);
                            updateStoOutBillMap.put(stoId, sgBStoOut);
                        }
                        // 设置待更新明细
                        SgBStoOutItem sgBStoOutItem = new SgBStoOutItem();
                        sgBStoOutItem.setId(j.getItemId());
                        sgBStoOutItem.setSourceBillItemId(newSourceItemId);
                        sgBStoOutItems.add(sgBStoOutItem);
                        // TODO 这个判断也不应该进，如果进了，那就是有问题
                        if (!newSharedPreoutFtpMap.containsKey(itemId)) {
                            return;
                        }
                        List<SgBStorageSharedPreoutFtp> sgBStorageSharedPreoutFtps = newSharedPreoutFtpMap.get(itemId);
                        sgBStorageSharedPreoutFtps.forEach(k -> {
                            k.setSourceBillId(newSourceItemId);
                            k.setSourceBillNo(newOrderBillNo);
                        });
                    });

                    //存在sku合并的情况下
                    if (mergeBillNos.contains(sgStoOutBillNo)) {
                        List<SgBStoOutInfoBySourceResult> resultsByLog = stoBillLogMap.get(itemKey);
                        resultsByLog.forEach(j -> {
                            // 设置待更新log明细
                            SgBStoOutItem sgBStoOutItem = new SgBStoOutItem();
                            sgBStoOutItem.setId(j.getItemId());
                            sgBStoOutItem.setSourceBillItemId(newSourceItemId);
                            sgBStoOutLogItems.add(sgBStoOutItem);
                        });
                    }
                });
            });


            // 更新主表数据
            if (MapUtils.isNotEmpty(updateStoOutBillMap)) {
                List<SgBStoOut> updateSgBStoOuts = new ArrayList<>(updateStoOutBillMap.values());
                sgBStoOutMapper.batchUpdateById(updateSgBStoOuts);
            }
            // 更新明细数据
            if (CollectionUtils.isNotEmpty(sgBStoOutItems)) {
                sgBStoOutItemMapper.batchUpdateById(SgConstants.SG_B_STO_OUT_ITEM, sgBStoOutItems);
            }
            //更新log明细数据
            if (CollectionUtils.isNotEmpty(sgBStoOutLogItems)) {
                sgBStoOutItemMapper.batchUpdateById(SgConstants.SG_B_STO_OUT_ITEM_LOG, sgBStoOutLogItems);
            }
            // 修改流水数据
            if (CollectionUtils.isNotEmpty(sharedPreoutFtps)) {
                sharedPreoutFtpMapper.batchInsert(sharedPreoutFtps);
                sharedPreoutFtpMapper.batchInsert(newSharedPreoutFtps);
            }

            HashMap<String, Object> map = new HashMap<>();
            map.put("item", sgBStoOuts);
            map.put("itemlog", sgBStoOutLogs);
            map.put("mergebillno", mergeBillNos);
            return map;
        } catch (Exception e) {
            log.error("更新库存单据来源信息服务 来源单据id{} 更新逻辑占用单失败:{}", request.getSourceBillId(), Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        }
    }

    /**
     * 更新共享占用单以及流水 sku合并
     *
     * @param request
     * @param
     */
    public void updateShareOutBillInfo2(SgOmsSplitOrderRequest request, HashMap<String, Object> map) {
        try {
            User loginUser = R3SystemUserResource.getSystemRootUser();
            Long sourceBillId = request.getSourceBillId();
            Integer sourceBillType = request.getSourceBillType();
            List<SgOmsSplitSubOrderRequest> subOrderList = request.getSubOrderList();
            List<SgBShareOutInfoBySourceResult> outInfoBySourceResults = sgBShareOutMapper.selectListBySource(sourceBillId, sourceBillType);

            List<SgBShareOutInfoBySourceResult> outInfoBySourceLogResults = new ArrayList<>();
            if (CollectionUtils.isEmpty(outInfoBySourceResults)) {
                log.error("SgOmsSplitOrderService.updateShareOutBillInfo 更新库存单据来源信息服务 来源单据id{} 来源单据编号:{} 该来源单据对应的共享占用单已不存在", sourceBillId, sourceBillType);
                throw new NDSException("更新库存单据来源信息服务失败 该来源单据对应的共享占用单不存在");
            }

            log.info("SgOmsSplitOrderService selectListBySource outInfoBySourceResults {} ",
                    JSONObject.toJSONString(outInfoBySourceResults));

            //共享占用明细map   key为来源明细id
            Map<String, List<SgBShareOutInfoBySourceResult>> shareOutMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            //聚合仓id
            List<Long> sgShareStoreIds = new ArrayList<>();
            //配销仓流水明细  通过占用单据编号去查询
            List<String> saBillNos = new ArrayList<>();
            //共享占用单id
            List<Long> shareOutIds = new ArrayList<>();
            //sku合并的共享占用单据编号
            List<String> mergeBillNos = new ArrayList<>();
            //共享占用单明细id
            List<Long> shareOutItemIds = new ArrayList<>();
            outInfoBySourceResults.forEach(outInfoSource -> {
                Long sgShareStoreId = outInfoSource.getSgShareStoreId();
                Long sourceBillItemId = outInfoSource.getSourceBillItemId();
                Long itemId = outInfoSource.getItemId();
                String shareBillNo = outInfoSource.getShareBillNo();
                Long shareId = outInfoSource.getShareId();
                Boolean mergeMark = outInfoSource.getMergeMark();

                String key = sgShareStoreId + ":" + sourceBillItemId;
                if (!sgShareStoreIds.contains(sgShareStoreId)) {
                    sgShareStoreIds.add(sgShareStoreId);
                }
                if (!saBillNos.contains(shareBillNo)) {
                    saBillNos.add(shareBillNo);
                }

                if (shareOutMap.containsKey(key)) {
                    List<SgBShareOutInfoBySourceResult> share = shareOutMap.get(key);
                    share.add(outInfoSource);
                } else {
                    List<SgBShareOutInfoBySourceResult> share = new ArrayList<>();
                    share.add(outInfoSource);
                    shareOutMap.put(key, share);
                }
                if (!shareOutIds.contains(shareId)) {
                    shareOutIds.add(shareId);
                }
                if (mergeMark && !mergeBillNos.contains(shareBillNo)) {
                    mergeBillNos.add(shareBillNo);
                }
                shareOutItemIds.add(itemId);
            });

            //历史共享占用单
            List<SgBShareOut> oldShareOuts = sgBShareOutMapper.selectList(new LambdaQueryWrapper<SgBShareOut>()
                    .in(SgBShareOut::getId, shareOutIds)
                    .eq(SgBShareOut::getIsactive, SgConstants.IS_ACTIVE_Y));
            Map<Long, SgBShareOut> oldShareOutMap = oldShareOuts.stream().collect(Collectors.toMap(SgBShareOut::getId, Function.identity()));
            //历史共享占用明细
            List<SgBShareOutItem> oldShareOutItems = sgBShareOutItemMapper.selectList(new LambdaQueryWrapper<SgBShareOutItem>()
                    .in(SgBShareOutItem::getId, shareOutItemIds)
                    .eq(SgBShareOutItem::getIsactive, SgConstants.IS_ACTIVE_Y));
            Map<Long, SgBShareOutItem> oldShareOutItemsMap = oldShareOutItems.stream().collect(Collectors.toMap(SgBShareOutItem::getId, Function.identity()));
            //打印配销占用单明细日志并统计占用总数
            BigDecimal oldShareOutItemQtyPreout = oldShareOutItems.stream()
                    .map(SgBShareOutItem::getQtyPreout).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            log.info("SgOmsSplitOrderService oldShareOutItems:{},oldShareOutItemQtyPreout:{}",
                    JSONObject.toJSONString(oldShareOutItems), oldShareOutItemQtyPreout);
            //历史共享占用明细log
            Map<Long, SgBShareOutItem> oldShareOutItemLogMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

            //共享占用log明细
            List<Long> shareOutItemLogIds = new ArrayList<>();
            Map<String, List<SgBShareOutInfoBySourceResult>> shareOutLogMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            if (CollectionUtils.isNotEmpty(mergeBillNos)) {
                outInfoBySourceLogResults = sgBShareOutMapper.selectListByLogSource(sourceBillId, sourceBillType);
                outInfoBySourceLogResults.forEach(outInfoSource -> {
                    Long sgShareStoreId = outInfoSource.getSgShareStoreId();
                    Long sourceBillItemId = outInfoSource.getSourceBillItemId();
                    Long itemId = outInfoSource.getItemId();
                    String key = sgShareStoreId + ":" + sourceBillItemId;
                    if (shareOutLogMap.containsKey(key)) {
                        List<SgBShareOutInfoBySourceResult> share = shareOutLogMap.get(key);
                        share.add(outInfoSource);
                    } else {
                        List<SgBShareOutInfoBySourceResult> share = new ArrayList<>();
                        share.add(outInfoSource);
                        shareOutLogMap.put(key, share);
                    }
                    shareOutItemLogIds.add(itemId);
                });
                List<SgBShareOutItem> sgBShareOutItemLogs = sgBShareOutItemLogMapper.selectListByLog(new LambdaQueryWrapper<SgBShareOutItemLog>()
                        .in(SgBShareOutItemLog::getId, shareOutItemLogIds));
                oldShareOutItemLogMap = sgBShareOutItemLogs.stream().collect(Collectors.toMap(SgBShareOutItem::getId, Function.identity()));
            }

            log.info("SgOmsSplitOrderService data shareOutMap {}  shareOutLogMap {}",
                    JSONObject.toJSONString(shareOutMap), JSONObject.toJSONString(shareOutLogMap));

            //查询共享占用单配销仓流水信息
            List<SgBSaStoragePreoutFtp> saStoragePreoutFtps = SgBSaPreoutFtpMapper.selectListMaster(new LambdaQueryWrapper<SgBSaStoragePreoutFtp>()
                    .in(SgBSaStoragePreoutFtp::getBillNo, saBillNos));
            //统计流水占用总数并比对占用单是否一致，不一致则抛异常上次梳理
            BigDecimal saStoragePreoutFtpQtyChange = saStoragePreoutFtps.stream()
                    .map(SgBSaStoragePreoutFtp::getQtyChange).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            log.info("SgOmsSplitOrderService saStoragePreoutFtps:{},saStoragePreoutFtpQtyChange:{}",
                    JSONObject.toJSONString(saStoragePreoutFtps), saStoragePreoutFtpQtyChange);
            if (oldShareOutItemQtyPreout.compareTo(saStoragePreoutFtpQtyChange) != 0) {
                throw new NDSException("更新库存单据来源信息服务失败 该来源单据对应的配销占用数量不等于查询到对应流水的并更数量");
            }
            //组装配销仓占用 负流水
            List<SgBSaStoragePreoutFtp> negativeSaPreoutFtps = new ArrayList<>();
            Map<String, SgBSaStoragePreoutFtp> saStoragePreoutFtpMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            for (SgBSaStoragePreoutFtp storagePreoutFtp : saStoragePreoutFtps) {
                SgBSaStoragePreoutFtp saPreoutFtp = new SgBSaStoragePreoutFtp();
                BeanUtils.copyProperties(storagePreoutFtp, saPreoutFtp);
                BigDecimal qtyEnd = storagePreoutFtp.getQtyEnd();
                BigDecimal qtyBegin = storagePreoutFtp.getQtyBegin();
                BigDecimal qtyChange = storagePreoutFtp.getQtyChange();
                saPreoutFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SA_STORAGE_PREOUT_FTP));
                saPreoutFtp.setQtyChange(qtyChange.negate());
                saPreoutFtp.setQtyBegin(qtyEnd);
                saPreoutFtp.setQtyEnd(qtyBegin);
                saPreoutFtp.setRemark("更新库存单据来源信息");
                StorageUtils.setBModelDefalutData(saPreoutFtp, loginUser);
                negativeSaPreoutFtps.add(saPreoutFtp);

                String shareBillNo = storagePreoutFtp.getBillNo();
                Long psCSkuId = storagePreoutFtp.getPsCSkuId();
                Long saStoreId = storagePreoutFtp.getSgCSaStoreId();
                //流水key
                String saFtpKey = shareBillNo + "-" + saStoreId + "-" + psCSkuId;
                saStoragePreoutFtpMap.put(saFtpKey, storagePreoutFtp);
            }
            HashMap<String, SgBSaStoragePreoutFtp> newSaPreoutFtps = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

            //查询对应聚合仓下所有的逻辑仓  cp_c_store
            List<SgCpCStore> sgCpCStores = cpCStoreMapper.selectList(new LambdaQueryWrapper<SgCpCStore>()
                    .select(SgCpCStore::getId, SgCpCStore::getSgCShareStoreId)
                    .in(SgCpCStore::getSgCShareStoreId, sgShareStoreIds)
                    .eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y));
            //key为逻辑仓id  value 为聚合仓id
            Map<Long, Long> sgCpMaps = sgCpCStores.stream().collect(Collectors.toMap(o -> o.getId(), o -> o.getSgCShareStoreId()));

            if (log.isDebugEnabled()) {
                log.debug("SgOmsSplitOrderService updateShareOutBillInfo reuqestMap {}", JSONObject.toJSONString(map));
            }

            List<SgBStoOutInfoBySourceResult> stoOutResults = (List<SgBStoOutInfoBySourceResult>) map.get("item");
            List<SgBStoOutInfoBySourceResult> stoOutLogResults = (List<SgBStoOutInfoBySourceResult>) map.get("itemlog");
            List<String> stoOutMergeBillNos = (List<String>) map.get("mergebillno");

            //查询逻辑占用map
            Map<String, List<SgBStoOutInfoBySourceResult>> stoMap = stoOutResults.stream().collect(Collectors.groupingBy(SgBStoOutInfoBySourceResult::getStoBillNo));
            Map<String, List<SgBStoOutInfoBySourceResult>> stoLogMap = stoOutLogResults.stream().collect(Collectors.groupingBy(SgBStoOutInfoBySourceResult::getStoBillNo));

            //新增共享占用单map
            Map<String, SgBShareOut> insertShareOutMap = Maps.newHashMap();
            //新增共享占用单明细
            HashMap<String, SgBShareOutItem> insertShareOutSaItems = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);
            //新增共享占用单log明细
            List<SgBShareOutItemLog> insertShareOutItemLogList = new ArrayList<>();
            if (CollectionUtils.isEmpty(saStoragePreoutFtps) &&
                    (saStoragePreoutFtps.size()) != outInfoBySourceResults.size()) {
                //流水生成  高并发时会有延迟 比较流水是否已经全部生成
                throw new NDSException("更新库存单据来源信息服务失败 该来源单据对应的共享占用单未查询到对应流水");
            }

            for (SgOmsSplitSubOrderRequest orderRequest : subOrderList) {

                if (log.isDebugEnabled()) {
                    log.debug("SgOmsSplitOrderService subOrderList orderRequest {}", JSONObject.toJSONString(orderRequest));
                }

                String sgStoOutBillNo = orderRequest.getSgStoOutBillNo();
                Long newOrderBillId = orderRequest.getNewOrderBillId();
                String newOrderBillNo = orderRequest.getNewOrderBillNo();
                Integer newOrderBillType = orderRequest.getNewOrderBillType();

                List<SgOmsSplitSubOrderItemRequest> orderItemList = orderRequest.getOrderItemList();

                //key OldSourceItemId
                Map<Long, SgOmsSplitSubOrderItemRequest> itemIdMap = orderItemList.stream().collect(Collectors.
                        toMap(SgOmsSplitSubOrderItemRequest::getOldSourceItemId, Function.identity()));
                List<SgBStoOutInfoBySourceResult> stoOutInfoBySourceResults = stoMap.get(sgStoOutBillNo);
                // 逻辑占用单 是否合并
                if (stoOutMergeBillNos.contains(sgStoOutBillNo)) {
                    stoOutInfoBySourceResults = stoLogMap.get(sgStoOutBillNo);
                }
                //存在不同逻辑仓 相同聚合仓 来源明细相同的情况下 continue;
                List<String> mergeList = new ArrayList<>();
                for (SgBStoOutInfoBySourceResult outInfoBySourceResult : stoOutInfoBySourceResults) {

                    if (log.isDebugEnabled()) {
                        log.debug("SgOmsSplitOrderService stoOutInfoBySourceResults {}", JSONObject.toJSONString(outInfoBySourceResult));
                    }

                    Long cStoreId = outInfoBySourceResult.getCpCStoreId();
                    Long stoBillItemId = outInfoBySourceResult.getSourceBillItemId();

                    //获取聚合仓id
                    Long shareId = sgCpMaps.get(cStoreId);
                    String shareKey = shareId + ":" + stoBillItemId;
                    if (mergeList.contains(shareKey)) {
                        continue;
                    }
                    mergeList.add(shareKey);

                    String key = sgStoOutBillNo + "-" + shareId;

                    List<SgBShareOutInfoBySourceResult> sourceResults = new ArrayList<>();

                    if (shareOutLogMap.containsKey(shareKey)) {
                        //shareOutlog
                        sourceResults = shareOutLogMap.get(shareKey);
                    } else if (shareOutMap.containsKey(shareKey)) {
                        //shareOut
                        sourceResults = shareOutMap.get(shareKey);
                    }

                    Iterator<SgBShareOutInfoBySourceResult> iterator = sourceResults.iterator();
                    while (iterator.hasNext()) {
                        SgBShareOutInfoBySourceResult shareOut = iterator.next();
                        //共享占用单单据编号  查询流水key
                        String shareBillNo = shareOut.getShareBillNo();
                        Long storeId = shareOut.getSaId();
                        Long skuId = shareOut.getSkuId();
                        BigDecimal qty = shareOut.getQty();

                        String ftpKey = shareBillNo + "-" + storeId + "-" + skuId;

                        SgOmsSplitSubOrderItemRequest itemRequest = itemIdMap.get(stoBillItemId);
                        Long newSourceItemId = itemRequest.getNewSourceItemId();
                        BigDecimal changeQty = itemRequest.getQty();

                        if (log.isDebugEnabled()) {
                            log.debug("SgOmsSplitOrderService sourceResults shareOut {} itemRequest {}",
                                    JSONObject.toJSONString(shareOut), JSONObject.toJSONString(itemRequest));
                        }

                        if (changeQty.compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }

                        Long itemId = shareOut.getItemId();
                        if (!insertShareOutMap.containsKey(key)) {
                            //不存在 创建共享占用单
                            SgBShareOut oldShareOut = oldShareOutMap.get(shareOut.getShareId());
                            SgBShareOut newShareOut = new SgBShareOut();
                            BeanUtils.copyProperties(oldShareOut, newShareOut);
                            newShareOut.setId(ModelUtil.getSequence(SgConstants.SG_B_SHARE_OUT));
                            newShareOut.setBillNo(SgStoreUtils.getBillNo(SgShareConstants.SEQ_SG_B_SHARE_OUT,
                                    SgConstants.SG_B_SHARE_OUT.toUpperCase().toUpperCase(), newShareOut, loginUser.getLocale()));
                            newShareOut.setSourceBillId(newOrderBillId);
                            newShareOut.setSourceBillNo(newOrderBillNo);
                            newShareOut.setSourceBillType(newOrderBillType);
                            newShareOut.setTotQtyOrign(BigDecimal.ZERO);
                            newShareOut.setTotQtyOut(BigDecimal.ZERO);
                            newShareOut.setTotQtyPreout(BigDecimal.ZERO);
                            newShareOut.setTotRowNum(0);
                            newShareOut.setMergeMark(false);
                            newShareOut.setRemark("更新库存单据来源信息");
                            StorageUtils.setBModelDefalutData(newShareOut, loginUser);
                            insertShareOutMap.put(key, newShareOut);
                        }
                        //存在添加明细
                        SgBShareOut sgBShareOut = insertShareOutMap.get(key);

                        String newBillNo = sgBShareOut.getBillNo();
                        //明细key  流水和明细合并
                        String itemKey = newBillNo + "-" + storeId + "-" + skuId;
                        //历史明细
                        SgBShareOutItem oldShareOutItem = oldShareOutItemsMap.get(itemId);
                        if (mergeBillNos.contains(shareBillNo)) {
                            oldShareOutItem = oldShareOutItemLogMap.get(itemId);
                        }

                        //生成配销明细数据和流水
                        Long sequence = ModelUtil.getSequence(SgConstants.SG_B_SHARE_OUT_ITEM);
                        BigDecimal itemQty = assembleItem(insertShareOutSaItems, insertShareOutItemLogList,
                                oldShareOutItem, itemKey, sgBShareOut, newSourceItemId, changeQty, loginUser, sequence);

                        if (!saStoragePreoutFtpMap.containsKey(ftpKey)) {
                            throw new NDSException("该来源单据对应的共享占用单未查询到对应流水");
                        }

                        itemRequest.setQty(changeQty.subtract(itemQty));

                        SgBSaStoragePreoutFtp saPreoutFtp = saStoragePreoutFtpMap.get(ftpKey);
                        BigDecimal qtyBegin = saPreoutFtp.getQtyBegin();

                        SgBSaStoragePreoutFtp newPreoutFtp = new SgBSaStoragePreoutFtp();
                        BeanUtils.copyProperties(saPreoutFtp, newPreoutFtp);
                        //是否流水合并
                        if (!newSaPreoutFtps.containsKey(itemKey)) {
                            newPreoutFtp.setId(ModelUtil.getSequence(SgConstants.SG_B_SA_STORAGE_PREOUT_FTP));
                            newPreoutFtp.setBillNo(newBillNo);
                            newPreoutFtp.setBillId(sgBShareOut.getId());
                            newPreoutFtp.setSourceBillId(newOrderBillId);
                            newPreoutFtp.setSourceBillNo(newOrderBillNo);
                            newPreoutFtp.setBillItemId(sequence);
                            newPreoutFtp.setQtyBegin(qtyBegin);
                            newPreoutFtp.setQtyChange(itemQty);
                            newPreoutFtp.setQtyEnd(qtyBegin.add(itemQty));
                            newPreoutFtp.setRemark("更新库存单据来源信息");
                            StorageUtils.setBModelDefalutData(newPreoutFtp, loginUser);
                            newSaPreoutFtps.put(itemKey, newPreoutFtp);
                        } else {
                            SgBSaStoragePreoutFtp newFtp = newSaPreoutFtps.get(itemKey);
                            BigDecimal begin = newFtp.getQtyBegin();
                            newFtp.setQtyChange(newFtp.getQtyChange().add(itemQty));
                            newFtp.setQtyEnd(begin.add(newFtp.getQtyChange().add(itemQty)));
                        }

                        // 一条明细执行一次 todo 需要修改
                        if (qty.compareTo(changeQty) == 0) {
                            iterator.remove();
                            break;
                        } else if (qty.compareTo(changeQty) > 0) {
                            shareOut.setQty(qty.subtract(changeQty));
                        }
                    }
                }
            }

            //作废对应共享占用单和明细;
            if (CollectionUtils.isNotEmpty(shareOutIds)) {
                SgBShareOut update = new SgBShareOut();
                update.setIsactive(SgConstants.IS_ACTIVE_N);
                update.setBillStatus(4);
                sgBShareOutMapper.update(update, new LambdaQueryWrapper<SgBShareOut>()
                        .in(SgBShareOut::getId, shareOutIds));
            }
            if (CollectionUtils.isNotEmpty(shareOutItemIds)) {
                SgBShareOutItem updataItem = new SgBShareOutItem();
                updataItem.setIsactive(SgConstants.IS_ACTIVE_N);
                sgBShareOutItemMapper.update(updataItem, new LambdaQueryWrapper<SgBShareOutItem>()
                        .in(SgBShareOutItem::getId, shareOutItemIds));
            }
            //作废共享占用单log明细
            if (CollectionUtils.isNotEmpty(shareOutItemLogIds)) {
                SgBShareOutItemLog updataItem = new SgBShareOutItemLog();
                updataItem.setIsactive(SgConstants.IS_ACTIVE_N);
                sgBShareOutItemLogMapper.update(updataItem, new LambdaQueryWrapper<SgBShareOutItemLog>()
                        .in(SgBShareOutItemLog::getId, shareOutItemLogIds));
            }

            //新增共享占用单
            if (MapUtils.isNotEmpty(insertShareOutMap)) {
                List<SgBShareOut> shareOuts = new ArrayList<>(insertShareOutMap.values());
                sgBShareOutMapper.batchInsert(shareOuts);
            }

            //新增主明细
            sgBShareOutItemMapper.batchInsert(insertShareOutSaItems.values());

            //共享占用单log明细
            if (CollectionUtils.isNotEmpty(insertShareOutItemLogList)) {
                sgBShareOutItemLogMapper.batchInsert(insertShareOutItemLogList);
            }

            //配销仓 流水新增
            SgBSaPreoutFtpMapper.batchInsert(negativeSaPreoutFtps);
            List<SgBSaStoragePreoutFtp> ftps = new ArrayList<>(newSaPreoutFtps.values());
            SgBSaPreoutFtpMapper.batchInsert(ftps);

        } catch (Exception e) {
            log.error("更新库存单据来源信息服务 来源单据id{} 更新共享占用单失败:{}", request.getSourceBillId(), Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        }

    }

    /**
     * 组装明细/log明细数据
     *
     * @param insertShareOutItems       需新增的 明细数据 key为 单据编号-(sa仓 or sp仓)id-skuid
     * @param insertShareOutItemLogList 需新增的log明细集合
     * @param oldShareOutItem           历史共享占用单明细数据 ()
     * @param itemKey                   itemkey  用于相同 单据编号 相同仓 相同sku合并
     * @param sgBShareOut               主表信息
     * @param loginUser
     */
    private BigDecimal assembleItem(HashMap<String, SgBShareOutItem> insertShareOutItems,
                                    List<SgBShareOutItemLog> insertShareOutItemLogList,
                                    SgBShareOutItem oldShareOutItem, String itemKey, SgBShareOut sgBShareOut,
                                    Long newSourceItemId, BigDecimal changeQty, User loginUser, Long sequence) {
        //主表id
        Long id = sgBShareOut.getId();

        BigDecimal qty = changeQty.compareTo(oldShareOutItem.getQty()) < 0 ? changeQty : oldShareOutItem.getQty();
        //明细
        if (!insertShareOutItems.containsKey(itemKey)) {
            Integer totRowNum = sgBShareOut.getTotRowNum();
            sgBShareOut.setTotRowNum(++totRowNum);

            SgBShareOutItem newShareOutItem = new SgBShareOutItem();
            BeanUtils.copyProperties(oldShareOutItem, newShareOutItem);

            newShareOutItem.setId(sequence);
            newShareOutItem.setSourceBillItemId(newSourceItemId);
            newShareOutItem.setSgBShareOutId(id);

            newShareOutItem.setQtyPreout(qty);
            newShareOutItem.setQty(qty);
//            newShareOutItem.setQtyPreout(oldShareOutItem.getQty());
//            newShareOutItem.setQty(oldShareOutItem.getQty());
            StorageUtils.setBModelDefalutData(newShareOutItem, loginUser);
            insertShareOutItems.put(itemKey, newShareOutItem);
        } else {
            SgBShareOutItem shareOutItem = insertShareOutItems.get(itemKey);
            if (!sgBShareOut.getMergeMark()) {
                //第一次添加log明细集合
                SgBShareOutItemLog itemLog = new SgBShareOutItemLog();
                BeanUtils.copyProperties(shareOutItem, itemLog);
                itemLog.setId(ModelUtil.getSequence(SgConstants.SG_B_SHARE_OUT_ITEM_LOG));
                insertShareOutItemLogList.add(itemLog);
            }
            //存在合并
            sgBShareOut.setMergeMark(true);
            shareOutItem.setQty(shareOutItem.getQty().add(changeQty));
            shareOutItem.setQtyPreout(shareOutItem.getQtyPreout().add(changeQty));
        }
        //存在合并记录时  所有明细添加明细log
        if (sgBShareOut.getMergeMark()) {
            SgBShareOutItemLog itemLog = new SgBShareOutItemLog();
            BeanUtils.copyProperties(oldShareOutItem, itemLog);
            itemLog.setId(ModelUtil.getSequence(SgConstants.SG_B_SHARE_OUT_ITEM_LOG));
            itemLog.setSourceBillItemId(newSourceItemId);
            itemLog.setSgBShareOutId(id);
            itemLog.setQtyPreout(changeQty);
            itemLog.setQty(changeQty);
            StorageUtils.setBModelDefalutData(itemLog, loginUser);
            insertShareOutItemLogList.add(itemLog);
        }

        //主表数据更新
        sgBShareOut.setTotQtyOrign(sgBShareOut.getTotQtyOrign().add(qty));
        sgBShareOut.setTotQtyPreout(sgBShareOut.getTotQtyPreout().add(qty));

        return qty;
    }

    private void checkParam(SgOmsSplitOrderRequest request, ValueHolderV14 valueHolderV14) {
        if (request == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("更新库存单据来源信息服务 请求参数为空");
            return;
        }
        if (request.getSourceBillId() == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("更新库存单据来源信息服务 原单id为空");
            return;
        }
        if (request.getSourceBillNo() == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("更新库存单据来源信息服务 原单编号为空");
            return;
        }
        if (request.getSourceBillType() == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("更新库存单据来源信息服务 原单类型为空");
            return;
        }
        List<SgOmsSplitSubOrderRequest> subOrderList = request.getSubOrderList();
        if (CollectionUtils.isEmpty(subOrderList)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("更新库存单据来源信息服务 新单据信息为空");
            return;
        }
        for (SgOmsSplitSubOrderRequest subOrder : subOrderList) {
            if (subOrder.getNewOrderBillId() == null) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("更新库存单据来源信息服务 新单id为空");
                return;
            }
            if (subOrder.getNewOrderBillNo() == null) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("更新库存单据来源信息服务 新单编号为空");
                return;
            }
            if (subOrder.getNewOrderBillType() == null) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("更新库存单据来源信息服务 新单类型为空");
                return;
            }
            if (subOrder.getSgStoOutBillNo() == null) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("更新库存单据来源信息服务 单据对应逻辑占用单号为空");
                return;
            }
            List<SgOmsSplitSubOrderItemRequest> orderItemList = subOrder.getOrderItemList();
            if (CollectionUtils.isEmpty(orderItemList)) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("更新库存单据来源信息服务 单据明细为空");
                return;
            }
            if (CollectionUtils.isEmpty(orderItemList)) {
                for (SgOmsSplitSubOrderItemRequest orderItem : orderItemList) {
                    if (orderItem.getNewSourceItemId() == null) {
                        valueHolderV14.setCode(ResultCode.FAIL);
                        valueHolderV14.setMessage("更新库存单据来源信息服务 新单明细id为空");
                        return;
                    }
                    if (orderItem.getOldSourceItemId() == null) {
                        valueHolderV14.setCode(ResultCode.FAIL);
                        valueHolderV14.setMessage("更新库存单据来源信息服务 原单明细id为空");
                        return;
                    }
                    if (orderItem.getPsCSkuId() == null) {
                        valueHolderV14.setCode(ResultCode.FAIL);
                        valueHolderV14.setMessage("更新库存单据来源信息服务 条码id为空");
                        return;
                    }
                    if (orderItem.getQty() == null) {
                        valueHolderV14.setCode(ResultCode.FAIL);
                        valueHolderV14.setMessage("更新库存单据来源信息服务 条码数量为空");
                        return;
                    }

                }
            }
        }
    }
}