package com.burgeon.r3.sg.sourcing.services.syncgradientstrategy;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCSharePool;
import com.burgeon.r3.sg.core.model.table.sourcing.syncgradientstrategy.SgCSyncGradientStrategy;
import com.burgeon.r3.sg.core.model.table.sourcing.syncgradientstrategy.SgCSyncGradientStrategyCond;
import com.burgeon.r3.sg.core.model.table.sourcing.syncgradientstrategy.SgCSyncGradientStrategyItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.mapper.syncgradientstrategy.SgCSyncGradientStrategyCondMapper;
import com.burgeon.r3.sg.sourcing.mapper.syncgradientstrategy.SgCSyncGradientStrategyItemMapper;
import com.burgeon.r3.sg.sourcing.mapper.syncgradientstrategy.SgCSyncGradientStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.request.syncgradientstrategy.*;
import com.burgeon.r3.sg.sourcing.model.result.syncgradientstrategy.SgCSyncGradientStrategyByQtyQueryResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/6/23 19:48
 * 库存梯度策略Service
 */
@Slf4j
@Component
public class SgCSyncGradientStrategyService extends ServiceImpl<SgCSyncGradientStrategyMapper,
        SgCSyncGradientStrategy> {

    @Autowired
    private SgCSyncGradientStrategyCondMapper sgSyncGradientStrategyCondMapper;

    @Autowired
    private SgCSyncGradientStrategyItemMapper sgSyncGradientStrategyItemMapper;

    @Autowired
    private SgCSyncGradientStrategyMapper sgSyncGradientStrategyMapper;

    /**
     * 共享池梯度策略保存
     *
     * @param request 请求参数
     * @return ValueHolderV14
     */
    public ValueHolderV14 saveShareStoreSyncGradientStrategy(SgCSyncGradientStrategyBillSaveRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgCSyncGradientStrategyService.saveShareStoreSyncGradientStrategy. request:{}:",
                    JSONObject.toJSONString(request));
        }
        SgCSyncGradientStrategySaveRequest mainSaveRequest = request.getMainSaveRequest();
        Long mainId = mainSaveRequest.getId();
        ValueHolderV14<Long> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "保存成功!");
        SgCSyncGradientStrategyService service =
                ApplicationContextHandle.getBean(SgCSyncGradientStrategyService.class);
        try {
//            try {
//                //新增，触发库存同步
//                //更新，触发库存同步
//                triggerStockAutoSync(request);
//            } catch (Exception ex) {
//                log.error("SgCSyncGradientStrategyService.saveShareStoreSyncGradientStrategy:触发库存同步发生异常！ error={}",
//                        Throwables.getStackTraceAsString(ex));
//            }
            if (mainSaveRequest.getId() < 0L) {
                mainId = service.insertShareStoreStrategy(request);
            } else {
                service.updateShareStoreStrategy(request);
            }
        } catch (Exception e) {
            log.error("SgCSyncGradientStrategyService.saveShareStoreSyncGradientStrategy exception_has_occured:{}",
                    Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("保存失败！" + e.getMessage());
        }
        vh.setData(mainId);
        return vh;
    }

    /**
     * 新增共享池梯度策略
     *
     * @param request 请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public Long insertShareStoreStrategy(SgCSyncGradientStrategyBillSaveRequest request) {
        SgCSyncGradientStrategySaveRequest mainSaveRequest = request.getMainSaveRequest();
        User user = request.getLoginUser();
        Long sgSharePoolId = mainSaveRequest.getSgCSharePoolId();
        if (sgSharePoolId == null) {
            AssertUtils.logAndThrow("共享池为空，不允许保存！", user.getLocale());
        }
        SgCSyncGradientStrategy sgSyncGradientStrategy = new SgCSyncGradientStrategy();
        setSgSharePoolInfo(user, sgSharePoolId, sgSyncGradientStrategy);
        StorageUtils.setBModelDefalutData(sgSyncGradientStrategy, user);
        sgSyncGradientStrategy.setOwnerename(user.getEname());
        sgSyncGradientStrategy.setModifierename(user.getEname());
        sgSyncGradientStrategy.setRemark(mainSaveRequest.getRemark());
        sgSyncGradientStrategy.setType(SgSourcingConstants.SG_C_SYNC_GRADIENT_STRATEGY_TYPE_SHARE);
        Long mainId = ModelUtil.getSequence(SgConstants.SG_C_SYNC_GRADIENT_STRATEGY);
        sgSyncGradientStrategy.setId(mainId);
        this.save(sgSyncGradientStrategy);
        saveStrategyCondAndShopItem(mainId, request.getCondItemSaveRequest(), request.getShopItemSaveRequest(),
                user);
        return mainId;
    }

    /**
     * 更新校验状态
     *
     * @param mainId 主表id
     */
    private void checkUpdate(Long mainId, User user) {
        SgCSyncGradientStrategy mainTable = sgSyncGradientStrategyMapper.selectById(mainId);
        if (mainTable == null) {
            AssertUtils.logAndThrow("当前记录已不存在！", user.getLocale());
        } else {
            AssertUtils.cannot(SgConstants.IS_ACTIVE_N.equals(mainTable.getIsactive()), "当前记录已作废,不允许保存！",
                    user.getLocale());
        }
    }

    /**
     * 修改共享池梯度策略
     *
     * @param request 请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateShareStoreStrategy(SgCSyncGradientStrategyBillSaveRequest request) {
        SgCSyncGradientStrategySaveRequest mainSaveRequest = request.getMainSaveRequest();
        User user = request.getLoginUser();
        Long mainId = mainSaveRequest.getId();
        checkUpdate(mainId, user);
        Long sgSharePoolId = mainSaveRequest.getSgCSharePoolId();
        if (sgSharePoolId != null || StringUtils.isNotBlank(mainSaveRequest.getRemark())) {
            SgCSyncGradientStrategy sgSyncGradientStrategy = new SgCSyncGradientStrategy();
            sgSyncGradientStrategy.setRemark(mainSaveRequest.getRemark());
            sgSyncGradientStrategy.setId(mainId);
            if (sgSharePoolId != null) {
                setSgSharePoolInfo(user, sgSharePoolId, sgSyncGradientStrategy);
            }
            StorageUtils.setBModelDefalutDataByUpdate(sgSyncGradientStrategy, user);
            sgSyncGradientStrategy.setModifierename(user.getEname());
            this.updateById(sgSyncGradientStrategy);
        }
        saveStrategyCondAndShopItem(mainId, request.getCondItemSaveRequest(), request.getShopItemSaveRequest(),
                user);
    }

    /**
     * 梯度策略条件和店铺明细保存
     *
     * @param mainId              主表id
     * @param condItemSaveRequest 条件明细请求参数
     * @param shopItemSaveRequest 店铺明细请求参数
     * @param user                用户
     */
    private void saveStrategyCondAndShopItem(Long mainId,
                                             SgCSyncGradientStrategyCondItemSaveRequest condItemSaveRequest,
                                             SgCSyncGradientStrategyShopItemSaveRequest shopItemSaveRequest,
                                             User user) {
        if (condItemSaveRequest != null) {
            if (condItemSaveRequest.getId() > 0) {
                if (shopItemSaveRequest != null) {
                    saveStrategyShopItem(user, mainId, condItemSaveRequest.getId(), shopItemSaveRequest);
                }
            } else {
                String condtion = condItemSaveRequest.getCondtion();
                AssertUtils.cannot(StringUtils.isBlank(condtion), "请输入判断条件!");
                BigDecimal qtyBegin = condItemSaveRequest.getQtyBegin();
                if (qtyBegin == null) {
                    AssertUtils.logAndThrow("开始值为空，不允许保存!", user.getLocale());
                } else {
                    AssertUtils.cannot(qtyBegin.compareTo(BigDecimal.ZERO) <= 0, "开始值必须大于0!", user.getLocale());
                }
                int conditon =
                        sgSyncGradientStrategyCondMapper.selectCount(new LambdaQueryWrapper<SgCSyncGradientStrategyCond>()
                                .eq(SgCSyncGradientStrategyCond::getSgCSyncGradientStrategyId, mainId)
                                .eq(SgCSyncGradientStrategyCond::getCondtion, condtion)
                                .eq(SgCSyncGradientStrategyCond::getQtyBegin, qtyBegin));
                AssertUtils.cannot(conditon > 0, "平台店铺存在重复区间的记录，不允许保存!", user.getLocale());
                SgCSyncGradientStrategyCond strategyCond = new SgCSyncGradientStrategyCond();
                strategyCond.setCondtion(condtion);
                strategyCond.setQtyBegin(qtyBegin);
                StorageUtils.setBModelDefalutData(strategyCond, user);
                strategyCond.setOwnerename(user.getEname());
                strategyCond.setModifierename(user.getEname());
                strategyCond.setSgCSyncGradientStrategyId(mainId);
                Long condItemId = ModelUtil.getSequence(SgConstants.SG_C_SYNC_GRADIENT_STRATEGY_COND);
                strategyCond.setId(condItemId);
                sgSyncGradientStrategyCondMapper.insert(strategyCond);
                if (shopItemSaveRequest != null) {
                    saveStrategyShopItem(user, mainId, condItemId, shopItemSaveRequest);
                }
            }
        } else if (shopItemSaveRequest != null) {
            AssertUtils.logAndThrow("请先输入或选择判断条件信息！", user.getLocale());
        }
    }

    /**
     * 获取共享池信息
     *
     * @param user                   用户
     * @param sgSharePoolId          共享池id
     * @param sgSyncGradientStrategy 梯度策略
     */
    private void setSgSharePoolInfo(User user, Long sgSharePoolId, SgCSyncGradientStrategy sgSyncGradientStrategy) {
        int selectCount = sgSyncGradientStrategyMapper.selectCount(new LambdaQueryWrapper<SgCSyncGradientStrategy>()
                .eq(SgCSyncGradientStrategy::getSgCSharePoolId, sgSharePoolId)
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        AssertUtils.cannot(selectCount > 0, "当前共享池梯度策略已存在,不允许重复！", user.getLocale());
        SgCSharePool sharePoolStore = CommonCacheValUtils.getSharePoolStore(sgSharePoolId);
        if (sharePoolStore != null) {
            sgSyncGradientStrategy.setSgCSharePoolId(sharePoolStore.getId());
            sgSyncGradientStrategy.setSgCSharePoolEcode(sharePoolStore.getEcode());
            sgSyncGradientStrategy.setSgCSharePoolEname(sharePoolStore.getEname());
        } else {
            AssertUtils.logAndThrow("当前共享池已不存在！", user.getLocale());
        }
    }

    /**
     * 获取配销仓信息
     *
     * @param user                   用户
     * @param sgSaStoreId            配销仓id
     * @param sgSyncGradientStrategy 梯度策略
     */
    private void setSgSaStoreInfo(User user, Long sgSaStoreId, SgCSyncGradientStrategy sgSyncGradientStrategy) {
        int selectCount = sgSyncGradientStrategyMapper.selectCount(new LambdaQueryWrapper<SgCSyncGradientStrategy>()
                .eq(SgCSyncGradientStrategy::getSgCSaStoreId, sgSaStoreId)
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        AssertUtils.cannot(selectCount > 0, "当前配销仓梯度策略已存在,不允许重复！", user.getLocale());
        SgCSaStore saStore = CommonCacheValUtils.getSaStore(sgSaStoreId);
        if (saStore != null) {
            sgSyncGradientStrategy.setSgCSaStoreId(saStore.getId());
            sgSyncGradientStrategy.setSgCSaStoreEcode(saStore.getEcode());
            sgSyncGradientStrategy.setSgCSaStoreEname(saStore.getEname());
        } else {
            AssertUtils.logAndThrow("当前配销仓已不存在！", user.getLocale());
        }
    }

    /**
     * 梯度策略店铺明细保存
     *
     * @param user                用户
     * @param mainId              主表id
     * @param condItemId          条件明细id
     * @param shopItemSaveRequest 店铺明细请求参数
     */
    private void saveStrategyShopItem(User user, Long mainId, Long condItemId,
                                      SgCSyncGradientStrategyShopItemSaveRequest shopItemSaveRequest) {
        Long cpShopId = shopItemSaveRequest.getCpCShopId();
        BigDecimal ratio = shopItemSaveRequest.getRatio();
        AssertUtils.cannot(cpShopId == null, "店铺为空，不允许保存！");
        AssertUtils.cannot(ratio == null, "比例为空，不允许保存！");
        AssertUtils.cannot(ratio != null && (ratio.compareTo(BigDecimal.ZERO) < 0), "比例必须大于0！",
                user.getLocale());
        SgCSyncGradientStrategyItem strategyItem = new SgCSyncGradientStrategyItem();
        SgCSyncGradientStrategyItem haveItem =
                sgSyncGradientStrategyItemMapper.selectOne(new LambdaQueryWrapper<SgCSyncGradientStrategyItem>()
                        .eq(SgCSyncGradientStrategyItem::getSgCSyncGradientStrategyCondId, condItemId)
                        .eq(SgCSyncGradientStrategyItem::getCpCShopId, cpShopId));
        if (haveItem != null) {
            strategyItem.setId(haveItem.getId());
            strategyItem.setRatio(ratio);
            StorageUtils.setBModelDefalutDataByUpdate(strategyItem, user);
            strategyItem.setModifierename(user.getEname());
            sgSyncGradientStrategyItemMapper.updateById(strategyItem);
        } else {
            CpShop shopInfo = CommonCacheValUtils.getShopInfo(cpShopId);
            if (shopInfo != null) {
                strategyItem.setCpCShopId(shopInfo.getCpCShopId());
                strategyItem.setCpCShopEcode(shopInfo.getEcode());
                strategyItem.setCpCShopTitle(shopInfo.getCpCShopTitle());
            } else {
                AssertUtils.logAndThrow("当前店铺已不存在！");
            }
            strategyItem.setRatio(ratio);
            strategyItem.setSgCSyncGradientStrategyId(mainId);
            strategyItem.setSgCSyncGradientStrategyCondId(condItemId);
            StorageUtils.setBModelDefalutData(strategyItem, user);
            strategyItem.setOwnerename(user.getEname());
            strategyItem.setModifierename(user.getEname());
            strategyItem.setId(ModelUtil.getSequence(SgConstants.SG_C_SYNC_GRADIENT_STRATEGY_ITEM));
            sgSyncGradientStrategyItemMapper.insert(strategyItem);
        }
    }

    /**
     * 配销仓梯度策略保存
     *
     * @param request 请求参数
     * @return ValueHolderV14
     */
    public ValueHolderV14 saveSaStoreSgSyncGradientStrategy(SgCSyncGradientStrategyBillSaveRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(" Start SgCSyncGradientStrategyService.saveSaStoreSgSyncGradientStrategy.request:{}:",
                    JSONObject.toJSONString(request));
        }
        SgCSyncGradientStrategySaveRequest mainSaveRequest = request.getMainSaveRequest();
        ValueHolderV14<Long> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "保存成功!");
        Long mainId = mainSaveRequest.getId();
        SgCSyncGradientStrategyService service =
                ApplicationContextHandle.getBean(SgCSyncGradientStrategyService.class);
        try {
            if (mainSaveRequest.getId() < 0L) {
                mainId = service.insertSaStoreStrategy(request);
            } else {
                service.updateSaStoreStrategy(request);
            }
        } catch (Exception e) {
            log.error("SgCSyncGradientStrategyService.saveSaStoreSgSyncGradientStrategy exception_has_occured:{}",
                    Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("保存失败！" + e.getMessage());
        }
        vh.setData(mainId);
        return vh;
    }

    /**
     * 新增配销仓梯度策略
     *
     * @param request 请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public Long insertSaStoreStrategy(SgCSyncGradientStrategyBillSaveRequest request) {
        SgCSyncGradientStrategySaveRequest mainSaveRequest = request.getMainSaveRequest();
        User user = request.getLoginUser();
        Long sgSaStoreId = mainSaveRequest.getSgCSaStoreId();
        if (sgSaStoreId == null) {
            AssertUtils.logAndThrow("配销仓为空，不允许保存！", user.getLocale());
        }
        SgCSyncGradientStrategy sgSyncGradientStrategy = new SgCSyncGradientStrategy();
        setSgSaStoreInfo(user, sgSaStoreId, sgSyncGradientStrategy);
        StorageUtils.setBModelDefalutData(sgSyncGradientStrategy, user);
        sgSyncGradientStrategy.setOwnerename(user.getEname());
        sgSyncGradientStrategy.setModifierename(user.getEname());
        sgSyncGradientStrategy.setRemark(mainSaveRequest.getRemark());
        sgSyncGradientStrategy.setType(SgSourcingConstants.SG_C_SYNC_GRADIENT_STRATEGY_TYPE_SA);
        Long mainId = ModelUtil.getSequence(SgConstants.SG_C_SYNC_GRADIENT_STRATEGY);
        sgSyncGradientStrategy.setId(mainId);
        this.save(sgSyncGradientStrategy);
        saveStrategyCondAndShopItem(mainId, request.getCondItemSaveRequest(), request.getShopItemSaveRequest(),
                user);
        return mainId;
    }

    /**
     * 修改配销仓梯度策略
     *
     * @param request 请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSaStoreStrategy(SgCSyncGradientStrategyBillSaveRequest request) {
        SgCSyncGradientStrategySaveRequest mainSaveRequest = request.getMainSaveRequest();
        User user = request.getLoginUser();
        Long mainId = mainSaveRequest.getId();
        checkUpdate(mainId, user);
        Long saStoreId = mainSaveRequest.getSgCSaStoreId();
        if (saStoreId != null || StringUtils.isNotBlank(mainSaveRequest.getRemark())) {
            SgCSyncGradientStrategy sgSyncGradientStrategy = new SgCSyncGradientStrategy();
            StorageUtils.setBModelDefalutDataByUpdate(sgSyncGradientStrategy, user);
            sgSyncGradientStrategy.setModifierename(user.getEname());
            sgSyncGradientStrategy.setRemark(mainSaveRequest.getRemark());
            sgSyncGradientStrategy.setId(mainId);
            if (saStoreId != null) {
                setSgSaStoreInfo(user, saStoreId, sgSyncGradientStrategy);
            }
            this.updateById(sgSyncGradientStrategy);
        }
        saveStrategyCondAndShopItem(mainId, request.getCondItemSaveRequest(), request.getShopItemSaveRequest(),
                user);
    }

    /**
     * @param request
     * @param loginUser
     * @return
     */
    public ValueHolderV14<SgCSyncGradientStrategyByQtyQueryResult> querySyncGradientStrategyByQty(SgCSyncGradientStrategyByQtyQueryRequest request,
                                                                                                  User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgCSyncGradientStrategyService.querySyncGradientStrategyByQty. ReceiveParams:request={};",
                    JSONObject.toJSONString(request));
        }

        ValueHolderV14<SgCSyncGradientStrategyByQtyQueryResult> holder = new ValueHolderV14(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

        //检查入参
        holder = checkServiceParam(request, loginUser);

        if (ResultCode.FAIL == holder.getCode()) {
            log.warn("SgCSyncGradientStrategyService.querySyncGradientStrategyByQty. checkServiceParam error:{};",
                    holder.getMessage());
            return holder;
        }

        SgCSyncGradientStrategyByQtyQueryResult queryResult = this.baseMapper.queryGradientStrategyByQty(
                request.getCpCShopId(), request.getStoreId(), request.getType(), request.getTargetQty());

        holder.setData(queryResult);

        if (log.isDebugEnabled()) {
            log.debug("Finish SgCSyncGradientStrategyService.querySyncGradientStrategyByQty. " +
                            "ReturnResult:queryResult:{};",
                    JSONObject.toJSONString(queryResult));
        }

        return holder;
    }

    /**
     * @param request
     * @return
     */
    private ValueHolderV14<SgCSyncGradientStrategyByQtyQueryResult> checkServiceParam(SgCSyncGradientStrategyByQtyQueryRequest request,
                                                                                      User loginUser) {

        ValueHolderV14<SgCSyncGradientStrategyByQtyQueryResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

        if (request == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("请求体为空！"));
            return holder;
        }

        if (loginUser == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("操作用户信息不能为空！"));
            return holder;
        }

        if (request.getCpCShopId() == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("平台店铺不能为空！", loginUser.getLocale()));
        }

        if (request.getStoreId() == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("配销仓或共享池不能为空！", loginUser.getLocale())
                    .concat(" " + holder.getMessage()));
        }

        if (request.getTargetQty() == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("指定的条件数量不能为空！", loginUser.getLocale())
                    .concat(" " + holder.getMessage()));
        }

        return holder;

    }
}
