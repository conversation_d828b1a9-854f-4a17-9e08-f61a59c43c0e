package com.burgeon.r3.sg.sourcing.services.syncgradientstrategy;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.sourcing.syncgradientstrategy.SgCSyncGradientStrategy;
import com.burgeon.r3.sg.core.model.table.sourcing.syncgradientstrategy.SgCSyncGradientStrategyCond;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.mapper.syncgradientstrategy.SgCSyncGradientStrategyCondMapper;
import com.burgeon.r3.sg.sourcing.mapper.syncgradientstrategy.SgCSyncGradientStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.request.syncgradientstrategy.SgCSyncGradientStrategyQueryRequest;
import com.burgeon.r3.sg.sourcing.model.result.syncgradientstrategy.SgCSyncGradientStrategyByIdQueryResult;
import com.burgeon.r3.sg.sourcing.model.result.syncgradientstrategy.SgCSyncGradientStrategyListQueryResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ext.permission.DataPermissionModel;
import com.jackrain.nea.ext.permission.Permissions;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/23 19:48
 */
@Slf4j
@Component
public class SgCSyncGradientStrategyQueryService {

    @Value("${r3.permission.column.sharePoolId:SG_C_SHARE_POOL_ID}")
    private String permissionsColumnSharePool;

    @Value("${r3.permission.column.saStoreId:SG_C_SA_STORE_ID}")
    private String permissionsColumnSaStore;

    @Autowired
    private SgCSyncGradientStrategyCondMapper sgSyncGradientStrategyCondMapper;

    @Autowired
    private SgCSyncGradientStrategyMapper sgSyncGradientStrategyMapper;

    private final static String SELECTALL="bSelect-all";

    /**
     * 共享池梯度策略列表定制查询
     *
     * @param request 请求参数
     * @return ValueHolderV14<PageInfo < SgCSyncGradientStrategyListQueryResult>>
     */
    public ValueHolderV14<PageInfo<SgCSyncGradientStrategyListQueryResult>> queryShareStoreSyncGradientStrategyForList(SgCSyncGradientStrategyQueryRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(" Start.SgCSyncGradientStrategyQueryService.queryShareStore.request:{}",
                    JSONObject.toJSONString(request));
        }
        ValueHolderV14<PageInfo<SgCSyncGradientStrategyListQueryResult>> vh = new ValueHolderV14<>(ResultCode.SUCCESS
                , "查询成功!");
        PageInfo<SgCSyncGradientStrategyListQueryResult> resultPageInfo =
                querySgSyncGradientStrategyPageInfo(request,
                        SgSourcingConstants.SG_C_SYNC_GRADIENT_STRATEGY_TYPE_SHARE);
        vh.setData(resultPageInfo);
        return vh;
    }

    /**
     * 配销仓梯度策略列表定制查询
     *
     * @param request 请求参数
     * @return ValueHolderV14<PageInfo < SgCSyncGradientStrategyListQueryResult>>
     */
    public ValueHolderV14<PageInfo<SgCSyncGradientStrategyListQueryResult>> querySaStoreSgSyncGradientStrategyForList(SgCSyncGradientStrategyQueryRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(" Start.SgCSyncGradientStrategyQueryService.querySaStore.request:{}",
                    JSONObject.toJSONString(request));
        }
        ValueHolderV14<PageInfo<SgCSyncGradientStrategyListQueryResult>> vh = new ValueHolderV14<>(ResultCode.SUCCESS
                , "查询成功!");
        PageInfo<SgCSyncGradientStrategyListQueryResult> resultPageInfo =
                querySgSyncGradientStrategyPageInfo(request, SgSourcingConstants.SG_C_SYNC_GRADIENT_STRATEGY_TYPE_SA);
        vh.setData(resultPageInfo);
        return vh;
    }

    /**
     * 查询列表数据并分页
     *
     * @param request 请求参数
     * @param type    类型
     * @return PageInfo<SgCSyncGradientStrategyListQueryResult>
     */
    private PageInfo<SgCSyncGradientStrategyListQueryResult> querySgSyncGradientStrategyPageInfo(SgCSyncGradientStrategyQueryRequest request, String type) {
        List<Long> shopIds = request.getCpCShopId();
        List<String> isactiveList = request.getIsactive();
        //筛选条件为全部时 会传此字符串
        if (CollectionUtils.isNotEmpty(isactiveList) && isactiveList.size() == 1 && SELECTALL.equals(isactiveList.get(0))) {
            isactiveList.remove(0);
        }
        int pageSize = request.getPageSize() == null ? SgConstants.SG_COMMON_MAX_QUERY_PAGE_SIZE :
                request.getPageSize();
        int pageNum = request.getPageNumber() == null ? 1 : request.getPageNumber();
        List<Long> strategyIds;
        List<SgCSyncGradientStrategyListQueryResult> shareListQueryResults = new ArrayList<>();
        if (SgSourcingConstants.SG_C_SYNC_GRADIENT_STRATEGY_TYPE_SHARE.equals(type)) {
            strategyIds = request.getSgCSharePoolIds();
            //查询条件筛选，无则全量查询
            List<Long> permissionShareIds = queryPermission(request.getLoginUser(), permissionsColumnSharePool);
            //分页返回
            PageHelper.startPage(pageNum, pageSize);
            shareListQueryResults = sgSyncGradientStrategyMapper.queryShareStoreSyncGradientStrategyForList(strategyIds,
                    null, permissionShareIds, null, type, shopIds, isactiveList);
        } else if (SgSourcingConstants.SG_C_SYNC_GRADIENT_STRATEGY_TYPE_SA.equals(type)) {
            strategyIds = request.getSgCSaStoreIds();
            //查询条件筛选，无则全量查询
            List<Long> permissionSaStoreIds = queryPermission(request.getLoginUser(), permissionsColumnSaStore);
            //分页返回
            PageHelper.startPage(pageNum, pageSize);
            shareListQueryResults = sgSyncGradientStrategyMapper.queryShareStoreSyncGradientStrategyForList(null,
                    strategyIds, null, permissionSaStoreIds, type, shopIds, isactiveList);
        }
        return new PageInfo<>(shareListQueryResults);
    }

    /**
     * 查询权限返回权限id
     */
    private List<Long> queryPermission(User user, String permissionsColumnName) {
        if (log.isDebugEnabled()) {
            log.debug(" Start.SgCSyncGradientStrategyQueryService.queryPermission.user:{};permissionsColumnName:{}",
                    JSONObject.toJSONString(user), permissionsColumnName);
        }
        List<Long> permissionIds = new ArrayList<>();
        List<DataPermissionModel> sharePoolPermissions = Permissions.getReadableModels(permissionsColumnName,
                user.getId(), null);
        if (!user.isAdmin()) {
            if (CollectionUtils.isNotEmpty(sharePoolPermissions)) {
                permissionIds =
                        sharePoolPermissions.stream().map(DataPermissionModel::getId).collect(Collectors.toList());
            } else {
                permissionIds.add(-1L);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(" end.SgCSyncGradientStrategyQueryService.queryPermission.permissionIds:{}",
                    JSONObject.toJSONString(permissionIds));
        }
        return permissionIds;
    }

    /**
     * 共享池/配销仓详情界面 主表数据+条件明细集合
     *
     * @param request 请求参数
     * @return ValueHolderV14<SgCSyncGradientStrategyByIdQueryResult>
     */
    public ValueHolderV14<SgCSyncGradientStrategyByIdQueryResult> querySyncGradientStrategyById(SgCSyncGradientStrategyQueryRequest request) {
        ValueHolderV14<SgCSyncGradientStrategyByIdQueryResult> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "查询成功!");
        if (request.getId() == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数不合法,查询失败!");
            return vh;
        }
        SgCSyncGradientStrategy gradientStrategy = sgSyncGradientStrategyMapper.selectById(request.getId());
        if (gradientStrategy != null) {
            SgCSyncGradientStrategyByIdQueryResult byIdResult = new SgCSyncGradientStrategyByIdQueryResult();
            BeanUtils.copyProperties(gradientStrategy, byIdResult);
            List<SgCSyncGradientStrategyCond> strategyCondItems =
                    sgSyncGradientStrategyCondMapper.selectList(new LambdaQueryWrapper<SgCSyncGradientStrategyCond>()
                            .eq(SgCSyncGradientStrategyCond::getSgCSyncGradientStrategyId, request.getId()));
            if (CollectionUtils.isNotEmpty(strategyCondItems)) {
                List<SgCSyncGradientStrategyByIdQueryResult.CondItemResult> condItemResults = new ArrayList<>();
                for (SgCSyncGradientStrategyCond strategyCondItem : strategyCondItems) {
                    SgCSyncGradientStrategyByIdQueryResult.CondItemResult condItemResult =
                            new SgCSyncGradientStrategyByIdQueryResult.CondItemResult();
                    BeanUtils.copyProperties(strategyCondItem, condItemResult);
                    condItemResults.add(condItemResult);
                }
                byIdResult.setCondItemResults(condItemResults);
            }
            vh.setData(byIdResult);
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("当前记录已不存在!");
        }
        return vh;
    }
}
