package com.burgeon.r3.sg.sourcing.services.sourcestrategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.r3.sg.basic.services.log.LogCommonService;
import com.burgeon.r3.sg.core.model.table.basic.SgCOperationLog;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategy;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategyWarehouseItem;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyMapper;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyWarehouseItemMapper;
import com.burgeon.r3.sg.sourcing.model.request.sourcestrategy.SgCChannelSourceStrategyItemQueryRequest;
import com.burgeon.r3.sg.sourcing.model.request.sourcestrategy.SgCChannelSourceStrategyWarehouseItemDeleteRequest;
import com.burgeon.r3.sg.sourcing.model.result.sourcestrategy.SgCChannelSourceStrategyWarehouseItemResult;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/19 17:16
 * 强制寻源规则明细表Service
 */
@Slf4j
@Component
public class SgCChannelSourceStrategyWarehouseItemService extends ServiceImpl<SgCChannelSourceStrategyWarehouseItemMapper,
        SgCChannelSourceStrategyWarehouseItem> {

    @Autowired
    private SgCChannelSourceStrategyWarehouseItemMapper sourceStrategyWarehouseItemMapper;

    @Autowired
    private SgCChannelSourceStrategyMapper sgChannelSourceStrategyMapper;

    @Resource
    private LogCommonService logCommonService;

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 deleteSgChannelSourceStrategyWarehouseItem(SgCChannelSourceStrategyWarehouseItemDeleteRequest request) {

        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "删除成功！");
        if (request.getItemId() == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("参数不合法,删除失败!");
            return v14;
        }
        SgCChannelSourceStrategyWarehouseItem item = sourceStrategyWarehouseItemMapper.selectById(request.getItemId());
        if (item == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("当前记录已不存在,删除失败!");
        } else {
            int i = sourceStrategyWarehouseItemMapper.deleteById(request.getItemId());
            if (i > 0) {
                SgCChannelSourceStrategy updateStrategy = new SgCChannelSourceStrategy();
                updateStrategy.setId(item.getSgCChannelSourceStrategyId());
                StorageUtils.setBModelDefalutDataByUpdate(updateStrategy, request.getLoginUser());
                updateStrategy.setModifierename(request.getLoginUser().getEname());
                sgChannelSourceStrategyMapper.updateById(updateStrategy);
                String beforeData = "[" + item.getCpCPhyWarehouseEname() + "],[" + item.getPriority() + "]";
                SgCOperationLog operationLog =
                        logCommonService.getOperationLog("SG_C_CHANNEL_SOURCE_STRATEGY_WAREHOUSE_ITEM",
                                OperationTypeEnum.DEL.getOperationValue(), item.getSgCChannelSourceStrategyId(),
                                "寻源策略定义", "仓优先", beforeData, null, request.getLoginUser());
                logCommonService.insertLog(operationLog);
            }
        }
        return v14;

    }

    public ValueHolderV14<List<SgCChannelSourceStrategyWarehouseItemResult>> querySgChannelSourceStrategyWarehouseItem(SgCChannelSourceStrategyItemQueryRequest request) {
        ValueHolderV14<List<SgCChannelSourceStrategyWarehouseItemResult>> vh = new ValueHolderV14<>(ResultCode.SUCCESS,
                "查询成功!");
        if (request.getSgCChannelSourceStrategyId() == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数不合法,查询失败!");
            return vh;
        }
        List<SgCChannelSourceStrategyWarehouseItemResult> results = new ArrayList<>();
        vh.setData(results);
        List<SgCChannelSourceStrategyWarehouseItem> warehouseItems = sourceStrategyWarehouseItemMapper.selectList(
                new LambdaQueryWrapper<SgCChannelSourceStrategyWarehouseItem>()
                        .eq(SgCChannelSourceStrategyWarehouseItem::getSgCChannelSourceStrategyId,
                                request.getSgCChannelSourceStrategyId()));
        if (CollectionUtils.isNotEmpty(warehouseItems)) {
            for (SgCChannelSourceStrategyWarehouseItem warehouseItem : warehouseItems) {
                SgCChannelSourceStrategyWarehouseItemResult sgCChannelSourceStrategyWarehouseItemResult = new SgCChannelSourceStrategyWarehouseItemResult();
                BeanUtils.copyProperties(warehouseItem,sgCChannelSourceStrategyWarehouseItemResult);

                results.add(sgCChannelSourceStrategyWarehouseItemResult);

            }

        }
        return vh;
    }
}
