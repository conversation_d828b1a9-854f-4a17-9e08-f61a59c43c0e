package com.burgeon.r3.sg.sourcing.services.factory;


import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.StrategyFactoryBaseResult;
import com.burgeon.r3.sg.sourcing.model.result.factory.SgFindSourceStrategyFactoryResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: hwy
 * @time: 2021/6/7 16:12
 */
@Component
@Slf4j
@Data
public class SgFindSourceStrategyS2L_ScoreFactory extends StrategyFactory {

    private List<? extends StrategyFactoryBaseResult> strategiesList;

    private StrategyHandle handle;

    @Override
    public List<SgFindSourceStrategyFactoryResult> getStrategies(StrategyBaseRequest request) {

        List<SgFindSourceStrategyFactoryResult> results = new ArrayList<>();
        SgFindSourceStrategyFactoryResult result = new SgFindSourceStrategyFactoryResult();
        result.setStrategyName("仓库满足率策略执行器");
        result.setStrategyClazz("com.burgeon.r3.sg.sourcing.services.item.SgFindSourceWarehouseFillRateStrategyService");
        result.setPriority(10);
        results.add(result);

        result = new SgFindSourceStrategyFactoryResult();
        result.setStrategyName("仓库评分因子策略执行器");
        result.setStrategyClazz("com.burgeon.r3.sg.sourcing.services.item.SgFindSourceWarehouseScoreStrategyService");
        result.setPriority(20);
        results.add(result);

        return results;

    }
}