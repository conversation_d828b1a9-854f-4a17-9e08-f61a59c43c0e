package com.burgeon.r3.sg.sourcing.services.maxorder;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.sourcing.maxorder.SgCStoreMaxOrdersSetting;
import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategyForceItem;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.sourcing.mapper.SgCChannelSourceStrategyForceItemMapper;
import com.burgeon.r3.sg.sourcing.mapper.maxorder.SgCStoreMaxOrdersSettingMapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/10/18 13:58
 * <p>
 * 设置门店最大接单量 - 定时任务service
 */
@Slf4j
@Component
public class SgCStoreMaxOrdersSettingService {

    @Autowired
    private SgCStoreMaxOrdersSettingMapper ordersSettingMapper;

    @Autowired
    private SgCChannelSourceStrategyForceItemMapper forceItemMapper;


    public ValueHolderV14 execute(String taskCode) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "执行成功");
        String[] split = taskCode.split("-");
        if (split.length == 0) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("执行失败");
            return v14;
        }
        String str = split[0].toLowerCase();

        SgCStoreMaxOrdersSetting ordersSetting = ordersSettingMapper.selectOne(new LambdaQueryWrapper<SgCStoreMaxOrdersSetting>()
                .eq(SgCStoreMaxOrdersSetting::getTaskCode, taskCode));
        if (ordersSetting == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(" 未查询到对应taskcode " + taskCode);
            return v14;
        }
        String customizeSql = ordersSetting.getCustomizeSql();

        log.info(" start SgCStoreMaxOrdersSettingService taskCode {}", taskCode);

        User user = R3SystemUserResource.getSystemRootUser();

        if (StringUtils.equals(str, "insert")) {
            List<SgCChannelSourceStrategyForceItem> sourceStrategyForceItems = ordersSettingMapper.selStoreByMaxOrder();
            if (CollectionUtils.isEmpty(sourceStrategyForceItems)) {
                return v14;
            }
            log.info(" SgCStoreMaxOrdersSettingService insert taskCode {} sourceStrategyForceItems {}",
                    taskCode, JSONObject.toJSONString(sourceStrategyForceItems));

            int size = sourceStrategyForceItems.size();
            Long[] ids = ModelUtil.getSequence(SgConstants.SG_C_CHANNEL_SOURCE_STRATEGY_FORCE_ITEM, size);

            for (int i = 0; i < sourceStrategyForceItems.size(); i++) {
                SgCChannelSourceStrategyForceItem forceItem = sourceStrategyForceItems.get(i);
                forceItem.setId(ids[i]);
                forceItem.setSgCChannelSourceForceStrategyId(1L);
                forceItem.setSgCChannelSourceForceStrategyEcode("PC");
                forceItem.setSgCChannelSourceForceStrategyEname("排除");
                StorageUtils.setBModelDefalutData(forceItem, user);
                forceItem.setModifierename(user.getEname());
                forceItem.setOwnerename(user.getEname());
            }
            List<List<SgCChannelSourceStrategyForceItem>> data = Lists.partition(sourceStrategyForceItems, SgConstants.SG_COMMON_MAX_INSERT_PAGE_SIZE);
            for (List<SgCChannelSourceStrategyForceItem> items : data) {
                forceItemMapper.batchInsert(items);
            }

        }
        if (StringUtils.equals(str, "update")) {
            //从主库进行操作
            StringBuilder sb = new StringBuilder(" /*FORCE_MASTER*/ ");
            sb.append(customizeSql);
            ordersSettingMapper.updateBySql(sb.toString());
        }
        if (StringUtils.equals(str, "delete")) {
            ordersSettingMapper.deleteBySql(customizeSql);
        }

        return v14;
    }

}
