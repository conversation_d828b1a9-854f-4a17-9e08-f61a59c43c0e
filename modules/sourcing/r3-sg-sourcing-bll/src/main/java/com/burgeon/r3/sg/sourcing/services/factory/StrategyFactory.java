package com.burgeon.r3.sg.sourcing.services.factory;

import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.StrategyFactoryBaseResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * @description:
 * @author: hwy
 * @time: 2021/6/7 16:14
 */

@Component
@Slf4j
public abstract class StrategyFactory {

    public StrategyHandle buildStrategy(StrategyBaseRequest request) {

        List<? extends StrategyFactoryBaseResult> strategyGroups = null;
        StrategyHandle handle = null;

        //策略工厂为单例模式
        if (this.isSingletonPattern()) {

            handle = this.getHandle();

            //单例策略工厂的情况下，直接使用已生成的策略handle
            if (handle != null) {
                return handle;
            }

            strategyGroups = this.getStrategies(request);
            handle = buildLinked(strategyGroups);

            this.setStrategiesList(strategyGroups);
            this.setHandle(handle);

        } else {
            //策略工厂为非单例模式
            strategyGroups = this.getStrategies(request);
            handle = buildLinked(strategyGroups);
        }

        return handle;
    }

    /**
     * @param request:
     * @Description: 获取所有的策略
     * @Author: hwy
     * @Date: 2021/6/7 17:10
     * @return: java.util.List<? extends com.burgeon.r3.sg.basic.model.result.strategy.StrategyFactoryBaseResult>
     **/
    public abstract List<? extends StrategyFactoryBaseResult> getStrategies(StrategyBaseRequest request);

    /**
     * @param baseResultList
     * @Description:
     */
    public abstract void setStrategiesList(List<? extends StrategyFactoryBaseResult> baseResultList);

    /**
     * @return
     */
    public abstract StrategyHandle getHandle();

    /**
     * @param handle
     */
    public abstract void setHandle(StrategyHandle handle);

    /**
     * 策略工厂是否为单例模式（默认为单例）
     *
     * @return
     */
    public Boolean isSingletonPattern() {
        return Boolean.TRUE;
    }

    /**
     * @param element:
     * @Description: 构建策略责任链
     * @Author: hwy
     * @Date: 2021/6/7 15:37
     * @return: com.burgeon.r3.sg.basic.services.strategy.StrategyHandle
     **/
    public static StrategyHandle buildLinked(List<? extends StrategyFactoryBaseResult> element) {

        StrategyHandle previousHandle = null;
        StrategyHandle headHandle = null;

        try {

            if (CollectionUtils.isEmpty(element)) {
                return headHandle;
            }

            //根据优先级排序
            Collections.sort(element);

            for (StrategyFactoryBaseResult baseResult : element) {
                String strategyClazz = baseResult.getStrategyClazz();
                Object o = Class.forName(strategyClazz).newInstance();
                if (!(o instanceof StrategyHandle)) {
                    log.error("策略创建工厂 {} 策略实例没有继承 com.burgeon.r3.sg.basic.services.strategy.StrategyHandle", baseResult.getStrategyName());
                    AssertUtils.logAndThrow("策略创建工厂 实例化策略失败", R3SystemUserResource.getSystemRootUser().getLocale());
                }
                StrategyHandle handle = (StrategyHandle) o;
                if (headHandle == null) {
                    headHandle = handle;
                    previousHandle = handle;
                    handle.setHeadFlag(Boolean.TRUE);
                    continue;
                } else {
                    previousHandle.setNext(handle);
                    previousHandle = handle;
                    handle.setHeadFlag(Boolean.FALSE);
                }
            }
        } catch (Exception e) {
            AssertUtils.logAndThrowException("策略创建工厂 实例化策略失败", e, R3SystemUserResource.getSystemRootUser().getLocale());
        }

        return headHandle;
    }
}