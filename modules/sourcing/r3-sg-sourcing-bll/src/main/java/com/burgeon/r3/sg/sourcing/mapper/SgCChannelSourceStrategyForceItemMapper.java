package com.burgeon.r3.sg.sourcing.mapper;

import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategyForceItem;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Mapper
@Repository
public interface SgCChannelSourceStrategyForceItemMapper extends ExtentionMapper<SgCChannelSourceStrategyForceItem> {

    @Select({
            "<script>",
            "SELECT ",
            " B.CP_C_STORE_ID, ",
            " B.CP_C_STORE_ENAME ",
            " FROM ",
            " ( SELECT ",
            "   ID,",
            "   CP_C_SHOP_ID ",
            "  FROM ",
            "   SG_C_CHANNEL_SOURCE_STRATEGY ",
            "  WHERE ",
            "   STATUS = '2' ",
            "  AND ISACTIVE = 'Y' ",
            " <when test='billDate != null ' >  ",
            " AND BEGIN_TIME &lt;= #{billDate,jdbcType=TIMESTAMP} ",
            " </when>  ",
            " <when test='billDate != null ' >  ",
            " AND END_TIME &gt;= #{billDate,jdbcType=TIMESTAMP} ",
            " </when>  ",
            " <when test='shopId!=null'> ",
            " AND CP_C_SHOP_ID = #{shopId,jdbcType=INTEGER}",
            " </when> ",
            "  ORDER BY ",
            "   ISNULL(status_time), ",
            "   status_time ",
            " desc ",
            "  LIMIT 1 ",
            " ) A ",
            " INNER JOIN SG_C_CHANNEL_SOURCE_STRATEGY_FORCE_ITEM B ON A.ID = B.SG_C_CHANNEL_SOURCE_STRATEGY_ID ",
            " WHERE ",
            " B.ISACTIVE = 'Y'  ",
            "</script>"
    })
    List<SgCChannelSourceStrategyForceItem> selectForceItemByShop(@Param("shopId") Long shopId, @Param("billDate") Date date);

}