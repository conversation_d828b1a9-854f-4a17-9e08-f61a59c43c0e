package com.burgeon.r3.sg.sourcing.services.syncgradientstrategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.sourcing.syncgradientstrategy.SgCSyncGradientStrategy;
import com.burgeon.r3.sg.core.model.table.sourcing.syncgradientstrategy.SgCSyncGradientStrategyItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.sourcing.mapper.syncgradientstrategy.SgCSyncGradientStrategyCondMapper;
import com.burgeon.r3.sg.sourcing.mapper.syncgradientstrategy.SgCSyncGradientStrategyItemMapper;
import com.burgeon.r3.sg.sourcing.mapper.syncgradientstrategy.SgCSyncGradientStrategyMapper;
import com.burgeon.r3.sg.sourcing.model.request.syncgradientstrategy.SgCSyncGradientStrategyDeleteRequest;
import com.burgeon.r3.sg.sourcing.model.request.syncgradientstrategy.SgCSyncGradientStrategyShopItemQueryRequest;
import com.burgeon.r3.sg.sourcing.model.result.syncgradientstrategy.SgCSyncGradientStrategyShopItemQueryResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/23 19:48
 * 库存梯度策略店铺明细service
 */
@Slf4j
@Component
public class SgCSyncGradientStrategyItemService extends ServiceImpl<SgCSyncGradientStrategyItemMapper,
        SgCSyncGradientStrategyItem> {

    @Autowired
    private SgCSyncGradientStrategyCondMapper sgSyncGradientStrategyCondMapper;

    @Autowired
    private SgCSyncGradientStrategyItemMapper sgSyncGradientStrategyItemMapper;

    @Autowired
    private SgCSyncGradientStrategyMapper sgSyncGradientStrategyMapper;

    /**
     * 店铺、条件明细删除
     *
     * @param request 请求参数
     * @param isShop  是否店铺明细
     * @return ValueHolderV14
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 deleteSyncGradientStrategyItemByIds(SgCSyncGradientStrategyDeleteRequest request,
                                                              boolean isShop) {
        List<Long> deleteItemIds = checkDeleteParam(request);
        if (CollectionUtils.isNotEmpty(deleteItemIds)) {
            int i;
            if (isShop) {
                i = sgSyncGradientStrategyItemMapper.deleteBatchIds(deleteItemIds);
            } else {
                i = sgSyncGradientStrategyCondMapper.deleteBatchIds(deleteItemIds);
                sgSyncGradientStrategyItemMapper.delete(new LambdaQueryWrapper<SgCSyncGradientStrategyItem>()
                        .in(SgCSyncGradientStrategyItem::getSgCSyncGradientStrategyCondId, deleteItemIds));
            }
            if (i > 0) {
                SgCSyncGradientStrategy updateStrategy = new SgCSyncGradientStrategy();
                updateStrategy.setId(request.getMainId());
                StorageUtils.setBModelDefalutDataByUpdate(updateStrategy, request.getLoginUser());
                updateStrategy.setModifierename(request.getLoginUser().getEname());
                sgSyncGradientStrategyMapper.updateById(updateStrategy);
            }
        }
        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("删除成功！"));
    }

    /**
     * 校验参数返回删除明细id
     *
     * @param request 请求参数
     * @return List<Long>
     */
    private List<Long> checkDeleteParam(SgCSyncGradientStrategyDeleteRequest request) {
        String itemIds = request.getItemIds();
        AssertUtils.cannot(StringUtils.isBlank(itemIds), "请求参数不能为空！");
        AssertUtils.cannot(request.getLoginUser() == null, "操作用户信息不能为空！");
        return Arrays.stream(itemIds.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
    }

    /**
     * 共享池/配销仓库存梯度策略店铺明细查询  主表id+条件明细查询
     *
     * @param request 请求参数
     * @return ValueHolderV14
     */
    public ValueHolderV14<PageInfo<SgCSyncGradientStrategyShopItemQueryResult>> querySyncGradientStrategyShopItem(SgCSyncGradientStrategyShopItemQueryRequest request) {
        ValueHolderV14<PageInfo<SgCSyncGradientStrategyShopItemQueryResult>> vh =
                new ValueHolderV14<>(ResultCode.SUCCESS,
                        "查询成功!");
        if (request.getSgCSyncGradientStrategyCondId() == null || request.getSgCSyncGradientStrategyId() == null) {
            vh.setCode(com.jackrain.nea.constants.ResultCode.FAIL);
            vh.setMessage("参数不合法,查询失败!");
            return vh;
        }
        int pageSize = request.getPageSize() == null ? SgConstants.SG_COMMON_MAX_QUERY_PAGE_SIZE :
                request.getPageSize();
        int pageNum = request.getPageNumber() == null ? 1 : request.getPageNumber();
        PageHelper.startPage(pageNum, pageSize);
        List<SgCSyncGradientStrategyShopItemQueryResult> shopItemQueryResultList =
                sgSyncGradientStrategyItemMapper.selectListByMainIdAndCondId(request.getSgCSyncGradientStrategyId(),
                        request.getSgCSyncGradientStrategyCondId());
        if (CollectionUtils.isNotEmpty(shopItemQueryResultList)) {
            vh.setData(new PageInfo<>(shopItemQueryResultList));
        }
        return vh;
    }
}
