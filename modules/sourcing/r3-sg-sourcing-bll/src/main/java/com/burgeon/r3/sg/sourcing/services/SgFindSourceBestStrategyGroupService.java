package com.burgeon.r3.sg.sourcing.services;


import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.burgeon.r3.sg.sourcing.services.factory.item.SgFindSourceBestStrategyGroupItemFactory;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @description: 择优策略组
 * @author: hwy
 * @time: 2021/6/9 16:09
 */

@Component
@Slf4j
public class SgFindSourceBestStrategyGroupService extends StrategyHandle {


    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {

        SgFindSourceBestStrategyGroupItemFactory itemFactory = ApplicationContextHandle.getBean(
                SgFindSourceBestStrategyGroupItemFactory.class);
        StrategyHandle itemHandle = itemFactory.buildStrategy(request);
        ValueHolderV14<StrategyBaseResult> valueHolderV14 = itemHandle.handleRequest(request);
        //将本策略执行结果带入下个策略的请求中
        StrategyBaseResult data = valueHolderV14.getData();
        request.setStrategyBaseResult(data);
        return doNext(request, valueHolderV14);
    }
}