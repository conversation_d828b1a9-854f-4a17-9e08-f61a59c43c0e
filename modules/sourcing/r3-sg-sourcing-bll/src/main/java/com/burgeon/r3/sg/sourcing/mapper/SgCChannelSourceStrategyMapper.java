package com.burgeon.r3.sg.sourcing.mapper;

import com.burgeon.r3.sg.core.model.table.sourcing.sourcestrategy.SgCChannelSourceStrategy;
import com.burgeon.r3.sg.sourcing.model.result.SgCselectForeceStrategyInfoResult;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface SgCChannelSourceStrategyMapper extends ExtentionMapper<SgCChannelSourceStrategy> {
    
    @Select({
            "<script>",
            " SELECT ",
            " A.cp_c_shop_id, ",
            " A.begin_time, ",
            " A.end_time, ",
            " C.class_path, ",
            " C.ename, ",
            " B.priority ",
            "FROM ",
            " ( SELECT ",
            "   id, ",
            "   cp_c_shop_id, ",
            "   begin_time, ",
            "   end_time, ",
            "   status_time ",
            "  FROM ",
            "   sg_c_channel_source_strategy ",
            "  WHERE ",
            "   STATUS = '2' ",
            "  AND isactive = 'Y' ",
            " <when test='request.beginTime != null ' >  ",
            " AND begin_time &lt;= #{request.beginTime,jdbcType=TIMESTAMP} ",
            " </when>  ",
            " <when test='request.endTime != null ' >  ",
            " AND end_time &gt;= #{request.endTime,jdbcType=TIMESTAMP} ",
            " </when>  ",
            " <when test='request.cpCShopId!=null'> ",
            " AND cp_c_shop_id = #{request.cpCShopId,jdbcType=INTEGER}",
            " </when> ",
            "  ORDER BY ",
            "   ISNULL(status_time), ",
            "   status_time ",
            " desc ",
            "  LIMIT 1 ",
            " ) A ",
            " INNER JOIN sg_c_channel_source_strategy_force_item B ON ( A.id = B.sg_c_channel_source_strategy_id and B.isactive = 'Y') ",
            " INNER JOIN sg_c_channel_source_force_strategy C ON (C.id = B.sg_c_channel_source_force_strategy_id and C.isactive = 'Y') ",
            "</script>",
    })
    List<SgCselectForeceStrategyInfoResult> selectForceStrategyInfo(@Param("request") SgCChannelSourceStrategy strategy);

    @Select({
            "<script>",
            " SELECT ",
            " C.class_path, ",
            " C.ename, ",
            " B.priority ",
            "FROM ",
            " sg_c_channel_source_strategy_rule_item B ",
            " INNER JOIN sg_c_share_source_rule_strategy C ON (C.ID = B.sg_c_share_source_rule_strategy_id and C.isactive = 'Y')",
            " where B.sg_c_channel_source_strategy_id = #{id} and B.isactive = 'Y' ",
            "ORDER BY B.priority DESC",
            "</script>",
    })
    List<SgCselectForeceStrategyInfoResult> selectRuleStrategyInfo(@Param("id") Long id);
}