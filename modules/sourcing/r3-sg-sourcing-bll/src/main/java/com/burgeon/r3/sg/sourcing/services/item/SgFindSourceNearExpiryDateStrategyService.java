package com.burgeon.r3.sg.sourcing.services.item;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.utils.BusinessSyetemParamConfigUtils;
import com.burgeon.r3.sg.core.common.DateConversionUtil;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.ext.SgCStoProduceDateRange;
import com.burgeon.r3.sg.core.model.table.sourcing.nearexpirydate.SgCStoNearExpiryDateStrategy;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemS2L;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.services.nearexpirydate.SgCStoNearExpiryDateStrategyService;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.SortedMap;
import java.util.TreeMap;


/**
 * description:临近大效期规则执行
 *
 * @Author: liuwenjin
 * @Date 2022/7/21 15:08
 */
@Component
@Slf4j
public class SgFindSourceNearExpiryDateStrategyService extends StrategyHandle {
    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {
        FindSourceStrategyUtils.outputLog("SgFindSourceNearExpiryDateStrategyService.handleRequest 临近大效期规则执行器 param:{}",
                JSONObject.toJSONString(request));
        ValueHolderV14<StrategyBaseResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        SgFindSourceStrategyS2LRequest strategyRequest = (SgFindSourceStrategyS2LRequest) request;
        SgFindSourceStrategyS2LResult strategyResult = (SgFindSourceStrategyS2LResult) strategyRequest.getStrategyBaseResult();
        valueHolderV14.setData(strategyResult);

        Long provinceId = strategyRequest.getProvinceId();
        boolean forbidProvince = BusinessSyetemParamConfigUtils.nearExpireSourceForbidProvince().contains(provinceId);
        if (forbidProvince) {
            FindSourceStrategyUtils.outputLog("C->S 二阶段寻源派单，临近大效期规则执行器 指定省份不进行大效期策略，退出大效期服务：" + provinceId);
            return doNext(request, valueHolderV14);
        }

        SgCStoNearExpiryDateStrategyService service = ApplicationContextHandle.getBean(SgCStoNearExpiryDateStrategyService.class);
        if (strategyRequest.getSkuItems().stream().anyMatch(x -> x.getShareStoreId() != null && x.getShareStoreId() <= 0L)) {
            FindSourceStrategyUtils.outputLog("C->S 二阶段寻源派单，临近大效期规则执行器 存在聚合仓缺货，退出大效期服务");
            //执行下一个
            return doNext(request, valueHolderV14);
        }
        //取出所有的sku 集合
        List<SgFindSourceStrategySkuS2LResult> skuResultList = strategyResult.getSkuResultList();

        try {
            //查询大效期策略入参
            Map<Long, List<Long>> strategyMap = new HashMap<>();
            for (SgFindSourceStrategySkuS2LResult sgFindSourceStrategySkuS2LResult : skuResultList) {
                List<SgFindSourceStrategyStoreItemS2LResult> s2LResults = sgFindSourceStrategySkuS2LResult.getItemResultList();
                List<Long> storeList = new ArrayList<>();
                //没有可用库存直接返回
                if (CollectionUtils.isEmpty(s2LResults)) {
                    //结束
                    FindSourceStrategyUtils.outputLog("C->S 二阶段寻源派单，临近大效期规则执行器sku" + sgFindSourceStrategySkuS2LResult.getPsCSkuId() + "没有可用的仓库，结束当前策略");
                    //执行下一个
                    return doNext(request, valueHolderV14);
                }
                for (SgFindSourceStrategyStoreItemS2LResult s2LResult : s2LResults) {
                    //会存在实体仓为-1的数据，特殊处理
                    if (!StrategyConstants.OUT_DEFAULT_STORE_ID.equals(s2LResult.getStoreId())) {
                        storeList.add(s2LResult.getStoreId());
                    }
                }
                if (CollectionUtils.isNotEmpty(storeList)){
                    strategyMap.put(sgFindSourceStrategySkuS2LResult.getPsCProId(), storeList);
                }
            }

            //key ProId + store , 指定仓大效期策略
            Map<String, SgCStoNearExpiryDateStrategy> sgCStoNearExpiryDateStrategyMap1 = queryStoNearExpiryDateStrategy(strategyMap, service, SgSourcingConstants.SG_C_STO_NEAR_EXPIRY_DATE_STRATEGY_PRIVATE);
            //key ProId  公用大效期策略
            Map<String, SgCStoNearExpiryDateStrategy> sgCStoNearExpiryDateStrategyMap2 = queryStoNearExpiryDateStrategy(strategyMap, service, SgSourcingConstants.SG_C_STO_NEAR_EXPIRY_DATE_STRATEGY_PUBLIC);
            //存在交集的仓库
            List<Long> commonList = new ArrayList<>();
            //满足大效期的仓库找交集
            for (int i = 0; i < skuResultList.size(); i++) {
                Long psCProId = skuResultList.get(i).getPsCProId();
                if (Objects.isNull(psCProId)) {
                    //结束
                    FindSourceStrategyUtils.outputLog("C->S 二阶段寻源派单，临近大效期规则执行器，满足大效期的仓库未找到交集，结束当前策略");
                    //执行下一个
                    return doNext(request, valueHolderV14);
                }
                //循环仓库
                List<SgFindSourceStrategyStoreItemS2LResult> itemResultList = skuResultList.get(i).getItemResultList();
                //ProId下满足大效期的仓库id
                List<Long> storeIds = new ArrayList<>();
                for (SgFindSourceStrategyStoreItemS2LResult storeIdList : itemResultList) {
                    String key = psCProId + "-" + storeIdList.getStoreId();
                    if (sgCStoNearExpiryDateStrategyMap1.containsKey(key)) {
                        SgCStoNearExpiryDateStrategy privateStrategy = sgCStoNearExpiryDateStrategyMap1.get(key);
                        //判断策略在不在大效期范围
                        if (checkStrategy(privateStrategy,storeIdList)){
                            storeIds.add(storeIdList.getStoreId());
                        }
                    }
                }
                //没找到 ProId + store  找 ProId
                if (CollectionUtils.isEmpty(storeIds)) {
                    if (sgCStoNearExpiryDateStrategyMap2.containsKey(psCProId+"")) {
                        SgCStoNearExpiryDateStrategy publicStrategy = sgCStoNearExpiryDateStrategyMap2.get(psCProId+"");
                        for (SgFindSourceStrategyStoreItemS2LResult storeIdList : itemResultList) {
                            if (checkStrategy(publicStrategy,storeIdList)){
                                storeIds.add(storeIdList.getStoreId());
                            }
                        }
                    }
                }
                if (i == 0) {
                    commonList.addAll(storeIds);
                } else {
                    commonList.retainAll(storeIds);
                }
            }
            log.info(LogUtil.format("SgFindSourceNearExpiryDateStrategyService.handleRequest sgCStoNearExpiryDateStrategyMap1:{},sgCStoNearExpiryDateStrategyMap2:{},commonList:{}",
                    "SgFindSourceNearExpiryDateStrategyService.handleRequest"),
                    JSONObject.toJSONString(sgCStoNearExpiryDateStrategyMap1), JSONObject.toJSONString(sgCStoNearExpiryDateStrategyMap2), JSONObject.toJSONString(commonList));
            //判断是否得到仓库大效期的交集
            if (CollectionUtils.isEmpty(commonList)) {
                FindSourceStrategyUtils.outputLog("C->S 二阶段寻源派单，临近大效期规则执行器， 满足订单条码仓库与大效期策略无交集，结束当前策略");
                //执行下一个
                return doNext(request, valueHolderV14);
            }

            //key 仓库 , val <key sku ， val< val 批次 <key 逻辑仓，val 总数>>>
            Map<Long, Map<Long, SortedMap<String, Map<Long, BigDecimal>>>> map = new HashMap<>();
            //得到每个共同仓库每个批次的库存数量
            for (SgFindSourceStrategySkuS2LResult sgFindSourceStrategySkuS2LResult : skuResultList) {
                String psCProId = sgFindSourceStrategySkuS2LResult.getPsCProId().toString();
                Long psSkuId = sgFindSourceStrategySkuS2LResult.getPsCSkuId();
                List<SgFindSourceStrategyStoreItemS2LResult> list = sgFindSourceStrategySkuS2LResult.getItemResultList();
                //循环出仓库
                for (SgFindSourceStrategyStoreItemS2LResult sgFindSourceStrategyStoreItemS2LResult : list) {
                    //仓库
                    Long storeId = sgFindSourceStrategyStoreItemS2LResult.getStoreId();
                    //只计算仓库在公用里面的
                    if (commonList.contains(storeId)) {
                        String key = psCProId + "-" + storeId;
                        //获取仓库的大效期策略
                        SgCStoNearExpiryDateStrategy strategy = null;
                        if (sgCStoNearExpiryDateStrategyMap1.containsKey(key)) {
                            //获取仓库的大效期策略
                            strategy = sgCStoNearExpiryDateStrategyMap1.get(key);
                        } else {
                            if (sgCStoNearExpiryDateStrategyMap2.containsKey(psCProId+"")) {
                                strategy = sgCStoNearExpiryDateStrategyMap2.get(psCProId+"");
                            }
                        }
                        //判断map有没有仓库
                        Map<Long, SortedMap<String, Map<Long, BigDecimal>>> wareMap = new HashMap<>();
                        if (map.containsKey(storeId)) {
                            wareMap = map.get(storeId);
                        }

                        //判断map有没有sku
                        SortedMap<String, Map<Long, BigDecimal>> skuMap = new TreeMap<>();
                        if (wareMap.containsKey(psSkuId)) {
                            skuMap = wareMap.get(psSkuId);
                        }
                        //生产日期是是否存在交集，没有循环直接返回
                        SgCStoProduceDateRange stoProductDateRang = validationProductDate(strategy);
                        FindSourceStrategyUtils.outputLog("C->S 二阶段寻源派单，临近大效期规则执行器 sourceItemId:{},storeId:{},validationProductDate:{}"
                                , sgFindSourceStrategySkuS2LResult.getSourceItemId(), storeId, JSONObject.toJSONString(stoProductDateRang));
                        if (Objects.nonNull(stoProductDateRang)) {
                            //循环出逻辑仓
                            Map<Long, SortedMap<String, BigDecimal>> mapMap = sgFindSourceStrategyStoreItemS2LResult.getLogicStorageMap();
                            //重新赋值逻辑仓 满足大效期的批次
                            for (Long aLong : mapMap.keySet()) {
                                //批次数量map
                                Map<String, BigDecimal> map2 = mapMap.get(aLong);
                                //循环出日期批次
                                for (String logicStorageDate : map2.keySet()) {
                                    //数量
                                    BigDecimal count = map2.get(logicStorageDate);
                                    if (validationLogicStorageDate(stoProductDateRang, logicStorageDate) && count.compareTo(BigDecimal.ZERO) >= 0) {
                                        //判断仓库下批次存不存在
                                        Map<Long, BigDecimal> logicStorageDateMap = new HashMap<>();
                                        if (skuMap.containsKey(logicStorageDate)) {
                                            logicStorageDateMap = skuMap.get(logicStorageDate);
                                        }
                                        //判断批次下逻辑仓存不存在
                                        if (!logicStorageDateMap.containsKey(aLong)) {
                                            logicStorageDateMap.put(aLong, count);
                                        }
                                        //给仓库map赋值批次
                                        if (MapUtils.isNotEmpty(logicStorageDateMap)) {
                                            skuMap.put(logicStorageDate, logicStorageDateMap);
                                        }
                                    }
                                }
                            }

                            if (MapUtils.isNotEmpty(skuMap)) {
                                wareMap.put(psSkuId, skuMap);
                            }

                            if (MapUtils.isNotEmpty(wareMap)) {
                                map.put(storeId, wareMap);
                            }
                        }
                    }
                }
            }

            //过滤能够整单满足的仓库
            List<SkuItemS2L> skuItemS2LList = strategyRequest.getSkuItems();
            if (MapUtils.isEmpty(map)) {
                FindSourceStrategyUtils.outputLog("C->S 二阶段寻源派单，临近大效期规则执行器， 没有可用仓库，结束当前策略");
                //执行下一个
                return doNext(request, valueHolderV14);
            }
            FindSourceStrategyUtils.outputLog("SgFindSourceNearExpiryDateStrategyService.handleRequest 临近大效期规则执行器 可用仓库map:{}",
                    JSONObject.toJSONString(map));
            //符合整单满足的仓库集合
            List<Long> storeIdList = checkStock(map, skuItemS2LList);

            //没有仓库满足整单直接返回
            if (CollectionUtils.isEmpty(storeIdList)) {
                FindSourceStrategyUtils.outputLog("C->S 二阶段寻源派单，临近大效期规则执行器， 仓库不满足整单占用，结束当前策略");
                //执行下一个
                return doNext(request, valueHolderV14);
            } else {
                //过滤掉不满足的仓库 - 逻辑仓 - 批次
                setResult(storeIdList, skuResultList, sgCStoNearExpiryDateStrategyMap1, sgCStoNearExpiryDateStrategyMap2);
                strategyResult.setNearExpiryDate(Boolean.TRUE);
            }

        } catch (Exception e) {
            log.error("S->L 二阶段寻源派单 临近大效期规则执行器 发生异常:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("S->L 二阶段寻源派单 临近大效期规则执行器 发生异常");
        }
        FindSourceStrategyUtils.outputLog("SgFindSourceNearExpiryDateStrategyService.handleRequest 临近大效期规则执行器 next skuIdList:{}",
                JSONObject.toJSONString(skuResultList));
        return doNext(request, valueHolderV14);
    }
    /**
     * description:判断策略在不在大效期范围
     * @Author:  liuwenjin
     * @Date 2022/9/5 18:38
     */
    private boolean checkStrategy(SgCStoNearExpiryDateStrategy strategy, SgFindSourceStrategyStoreItemS2LResult storeIdList) {
        //生产日期
        SgCStoProduceDateRange stoProductDateRang = validationProductDate(strategy);
        //循环出逻辑仓
        Map<Long, SortedMap<String, BigDecimal>> mapMap = storeIdList.getLogicStorageMap();
        //重新赋值逻辑仓 满足大效期的批次
        for (Long aLong : mapMap.keySet()) {
            //批次数量map
            Map<String, BigDecimal> map2 = mapMap.get(aLong);
            //循环出日期批次
            for (String logicStorageDate : map2.keySet()) {
                if (validationLogicStorageDate(stoProductDateRang, logicStorageDate)) {
                   return Boolean.TRUE;
                }
            }
        }
        return Boolean.FALSE;
    }

    /**
     * description:查询实体仓+公用的有效期
     *
     * @Author: liuwenjin
     * @Date 2022/7/26 17:30
     */
    private Map<String, SgCStoNearExpiryDateStrategy> queryStoNearExpiryDateStrategy(Map<Long, List<Long>> map, SgCStoNearExpiryDateStrategyService service, String type) {
        return service.queryByProIdStoNearExpiryDateStrategy(map, type);
    }

    /**
     * description:验证库存是否整单满足,返回满足的实体仓
     *
     * @Author: liuwenjin
     * @Date 2022/7/26 13:55
     */
    private List<Long> checkStock(Map<Long, Map<Long, SortedMap<String, Map<Long, BigDecimal>>>> map, List<SkuItemS2L> skuItemS2LList) {
        List<Long> storeIdList = new ArrayList<>();
        //满足大效期，仓库下所有批次
        for (Long aLong : map.keySet()) {
            Map<Long, SortedMap<String, Map<Long, BigDecimal>>> wareMap = map.get(aLong);
            //仓库是否能整单占用
            boolean flag = Boolean.TRUE;
            //循环整单判断是否当前仓库是否满足整单占用
            if (MapUtils.isNotEmpty(wareMap)){
                for (SkuItemS2L skuItemS2L : skuItemS2LList) {
                    Long psSkuId = skuItemS2L.getPsCSkuId();
                    Map<String, Map<Long, BigDecimal>> skuMap = wareMap.get(psSkuId);
                    SgCStoProduceDateRange stoProductDateRang = new SgCStoProduceDateRange();
                    stoProductDateRang.setBeginDate(skuItemS2L.getBeginProduceDate());
                    stoProductDateRang.setEndDate(skuItemS2L.getEndProduceDate());
                    //订单sku的数量
                    BigDecimal qty = skuItemS2L.getQtyPreOut();
                    BigDecimal differQty = qty;
                    //是否跳出循环
                    boolean isBreak = Boolean.FALSE;
                    //循环出当前sku的逻辑仓
                    if (MapUtils.isNotEmpty(skuMap)){
                        for (String logicStorageDate : skuMap.keySet()) {
                            //判断符合sku日期的仓库
                            if (validationLogicStorageDate(stoProductDateRang, logicStorageDate)) {
                                Map<Long, BigDecimal> storageMap = skuMap.get(logicStorageDate);
                                if (MapUtils.isNotEmpty(storageMap)){
                                    for (Long storage : storageMap.keySet()) {
                                        //批次库存数量
                                        BigDecimal count = storageMap.get(storage);
                                        //差值
                                        differQty = count.subtract(qty);
                                        if (differQty.compareTo(BigDecimal.ZERO) >= 0) {
                                            //单条仓库批次满足，循环下个sku
                                            storageMap.put(storage, differQty);
                                            differQty = BigDecimal.ZERO;
                                            isBreak =  Boolean.TRUE;
                                            break;
                                        } else {
                                            storageMap.put(storage, BigDecimal.ZERO);
                                            //库存差多少下次循环继续占用
                                            qty = differQty.abs();
                                        }
                                    }
                                }
                            }
                            if (isBreak){
                                break;
                            }
                        }
                    }
                    //循环结束发现还是有差值，说明不满足整单占用，跳出当前循环
                    if (differQty.compareTo(BigDecimal.ZERO) != 0) {
                        flag = Boolean.FALSE;
                        break;
                    }
                }
            }
            if (flag) {
                storeIdList.add(aLong);
            }
        }
        return storeIdList;
    }

    /**
     * description:给返回结果赋值
     *
     * @Author: liuwenjin
     * @Date 2022/7/26 11:08
     */
    private void setResult(List<Long> storeIdList, List<SgFindSourceStrategySkuS2LResult> skuIdList, Map<String, SgCStoNearExpiryDateStrategy> sgCStoNearExpiryDateStrategyMap1, Map<String, SgCStoNearExpiryDateStrategy> sgCStoNearExpiryDateStrategyMap2) {
        for (SgFindSourceStrategySkuS2LResult sgFindSourceStrategySkuS2LResult : skuIdList) {
            Long psCProId = sgFindSourceStrategySkuS2LResult.getPsCProId();
            List<SgFindSourceStrategyStoreItemS2LResult> storeList = sgFindSourceStrategySkuS2LResult.getItemResultList();
            //过滤后满足的仓库
            List<SgFindSourceStrategyStoreItemS2LResult> newWareList = new ArrayList<>();
            //循环出仓库
            for (SgFindSourceStrategyStoreItemS2LResult sgFindSourceStrategyStoreItemS2LResult : storeList) {
                //仓库
                Long storeId = sgFindSourceStrategyStoreItemS2LResult.getStoreId();
                //只计算仓库在公用里面的
                if (storeIdList.contains(storeId)) {
                    SgFindSourceStrategyStoreItemS2LResult newWare = new SgFindSourceStrategyStoreItemS2LResult();
                    String key = psCProId + "-" + storeId;
                    //获取仓库的大效期策略
                    SgCStoNearExpiryDateStrategy strategy = null;
                    if (sgCStoNearExpiryDateStrategyMap1.containsKey(key)) {
                        //获取仓库的大效期策略
                        strategy = sgCStoNearExpiryDateStrategyMap1.get(key);
                    } else {
                        if (sgCStoNearExpiryDateStrategyMap2.containsKey(psCProId+"")) {
                            strategy = sgCStoNearExpiryDateStrategyMap2.get(psCProId+"");
                        }
                    }
                    //生产日期是是否存在交集，没有循环直接返回
                    SgCStoProduceDateRange stoProductDateRang = validationProductDate(strategy);

                    //新的逻辑仓
                    Map<Long, SortedMap<String, BigDecimal>> newStoreMap = new HashMap<>();
                    //逻辑仓总数
                    BigDecimal qtyCount = BigDecimal.ZERO;

                    if (Objects.nonNull(stoProductDateRang)) {
                        //循环出逻辑仓
                        Map<Long, SortedMap<String, BigDecimal>> storeMap = sgFindSourceStrategyStoreItemS2LResult.getLogicStorageMap();

                        //循环逻辑仓
                        for (Long aLong : storeMap.keySet()) {
                            //批次数量map
                            SortedMap<String, BigDecimal> stoProductDateMap = storeMap.get(aLong);

                            //新的批次数量
                            SortedMap<String, BigDecimal> newStoProductDateMap = new TreeMap<>();
                            //循环批次
                            for (String date : stoProductDateMap.keySet()) {
                                BigDecimal count = stoProductDateMap.get(date);
                                if (validationLogicStorageDate(stoProductDateRang, date)) {
                                    newStoProductDateMap.put(date, count);
                                    qtyCount = qtyCount.add(count);
                                }
                            }
                            if (MapUtils.isNotEmpty(newStoProductDateMap)) {
                                newStoreMap.put(aLong, newStoProductDateMap);
                            }
                        }

                    }
                    //整单满足过滤后的
                    if (MapUtils.isNotEmpty(newStoreMap)) {
                        newWare.setStoreId(sgFindSourceStrategyStoreItemS2LResult.getStoreId());
                        newWare.setQty(sgFindSourceStrategyStoreItemS2LResult.getQty());
                        newWare.setPriority(sgFindSourceStrategyStoreItemS2LResult.getPriority());
                        newWare.setQty(qtyCount);
                        newWare.setLogicStorageMap(newStoreMap);
                        newWareList.add(newWare);
                    }
                }
            }
            //过滤后的仓库
            if (CollectionUtils.isNotEmpty(newWareList)) {
                sgFindSourceStrategySkuS2LResult.setItemResultList(newWareList);
            }
        }
    }

    /**
     * description:验证逻辑仓批次日期
     *
     * @Author: liuwenjin
     * @Date 2022/7/22 19:43
     */
    private boolean validationLogicStorageDate(SgCStoProduceDateRange stoProductDateRang, String logicStorageDate) {
        String beginDate = stoProductDateRang.getBeginDate();
        String endDate = stoProductDateRang.getEndDate();
        //格式一样 2022-07-22
        if (StringUtils.isNotEmpty(beginDate) && logicStorageDate.compareTo(beginDate) < 0) {
            return false;
        }
        if (StringUtils.isNotEmpty(endDate) && endDate.compareTo(logicStorageDate) < 0) {
            return false;
        }
        return true;
    }

    /**
     * description:验证生产日期和大效期，取出开始时间和结束时间
     *
     * @Author: liuwenjin
     * @Date 2022/7/22 10:09
     */
    private static SgCStoProduceDateRange validationProductDate(SgCStoNearExpiryDateStrategy strategy) {
        //效期管理类型 1 是日数 2是月数  历史数据可能回导致为空异常
        Integer type = Integer.parseInt(strategy.getReserveVarchar01());
        String strategyBeginDate = paresDate(type,strategy.getEndParturitionNum());
        String strategyEndDate = paresDate(type,strategy.getBeginParturitionNum());
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMdd");
        //判断临近大效期时，如果命中记录的【效期管理类型】=月数，
        //则根据当前月 – 开始数/结束数。获的[临近大效期生产开始日期]=系统当前月-[结束数]所在月份的第一天；
        // [临近大效期生产结束日期]= 系统当前月-[开始数] 所在月份的最后一天
        if (SgConstants.TYPE_MONTH ==type){
            //截取转换 20230106
            int n = LocalDate.parse(strategyEndDate,dtf).lengthOfMonth();
            strategyBeginDate = strategyBeginDate.substring(0,strategyBeginDate.length()-2)+"01";
            strategyEndDate = strategyEndDate.substring(0,strategyEndDate.length()-2)+n+"";
        }
        SgCStoProduceDateRange sgCStoProductDateRang = new SgCStoProduceDateRange();
        sgCStoProductDateRang.setBeginDate(strategyBeginDate);
        sgCStoProductDateRang.setEndDate(strategyEndDate);
        return sgCStoProductDateRang;

    }
    /**
     * description:
     * @Author:  liuwenjin
     * @Date 2023/1/10 16:21
     */
    private static String paresDate(Integer type, Integer num) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        if (SgConstants.TYPE_DAY ==type){
            return sf.format(DateConversionUtil.plusDays(new Date(), -num));
        }else if (SgConstants.TYPE_MONTH ==type){
            return sf.format(DateConversionUtil.plusMonth(new Date(), -num));
        }
        return "";
    }

    /**
     * description:解析时间
     *
     * @Author: liuwenjin
     * @Date 2022/7/26 18:21
     */
//    private Date paresDate(Integer beginParturitionNum) {
//        Date date = new Date();
//        return DateConversionUtil.plusDays(date, -beginParturitionNum);
//    }

    public static void main(String[] args) {
        SgCStoNearExpiryDateStrategy strategy = new SgCStoNearExpiryDateStrategy();
        strategy.setReserveVarchar01("2");
        strategy.setBeginParturitionNum(1);
        strategy.setEndParturitionNum(1);
        SgCStoProduceDateRange sgCStoProduceDateRange = validationProductDate(strategy);
        System.out.println(sgCStoProduceDateRange);
    }

}
