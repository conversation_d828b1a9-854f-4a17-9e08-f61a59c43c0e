package com.burgeon.r3.sg.sourcing.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.basic.mapper.SgBSaStoragePreoutFtpMapper;
import com.burgeon.r3.sg.basic.mapper.SgBSpStoragePreoutFtpMapper;
import com.burgeon.r3.sg.basic.mapper.SgBTobSourceMaxBatchMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.enums.YesNoEnum;
import com.burgeon.r3.sg.core.model.table.basic.SgBSaStoragePreoutFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgBSpStoragePreoutFtp;
import com.burgeon.r3.sg.core.model.table.basic.SgBTobSourceMaxBatch;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCPhyWarehouse;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOut;
import com.burgeon.r3.sg.core.model.table.share.out.SgBShareOutItem;
import com.burgeon.r3.sg.core.utils.LogUtils;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutItemMapper;
import com.burgeon.r3.sg.share.mapper.out.SgBShareOutMapper;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemS2L;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsItemResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsResult;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanItemResult;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyPlanServiceResult;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutItemLogSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutSaveRequest;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutBillSaveResult;
import com.burgeon.r3.sg.store.services.out.SgBStoOutSaveService;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: hwy
 * @time: 2021/7/3 13:56
 */
@Component
@Slf4j
public class SgFindSourceS2LPreOutStorageService {

    @Autowired
    private SgFindSourceOccupyPlanS2LService occupyPlanS2LService;
    @Autowired
    private SgFindSourceRollBackService sgFindSourceRollBackService;
    @Autowired
    private SgBShareOutMapper sgBShareOutMapper;
    @Autowired
    private SgBShareOutItemMapper sgBShareOutItemMapper;
    @Autowired
    private SgBSaStoragePreoutFtpMapper saStoragePreoutFtpMapper;
    @Autowired
    private SgBSpStoragePreoutFtpMapper spStoragePreoutFtpMapper;
    @Resource
    private ThreadPoolTaskExecutor asyncExecutorPool;
    @Resource
    private SgBTobSourceMaxBatchMapper sgBTobSourceMaxBatchMapper;

    public ValueHolderV14<SgFindSourceStrategyOmsResult> occupyStorage(SgFindSourceStrategyS2LRequest request) {
        FindSourceStrategyUtils.outputLog("SgFindSourceS2LPreOutStorageService.occupyStorage S->L 库存占用服务 param:{}", JSONObject.toJSONString(request));

        ValueHolderV14<SgFindSourceStrategyOmsResult> valueHolderV14 = new ValueHolderV14<>(StrategyConstants.RESULT_CODE_PREOUT, SgConstants.MESSAGE_STATUS_SUCCESS);
        SgOccupyPlanServiceResult occupyPlan = null;
        try {
            //如果是Tob订单寻源,现查一下是否有批次管理，有批次管理则走批次控制逻辑
            SgBTobSourceMaxBatch sgBTobSourceMaxBatch = null;
            if (request.getIsTobOrder()) {
                sgBTobSourceMaxBatch = sgBTobSourceMaxBatchMapper.selectOne(new LambdaQueryWrapper<SgBTobSourceMaxBatch>()
                        .eq(SgBTobSourceMaxBatch::getCpCShopId, request.getShopId())
                        .eq(SgBTobSourceMaxBatch::getIsactive, YesNoEnum.Y.getKey()));
            }
            ValueHolderV14<SgOccupyPlanServiceResult> occupyPlanResult;
            if (sgBTobSourceMaxBatch == null) {
                // 生成占用计划(根据生产日期升序占用)
                occupyPlanResult = occupyPlanS2LService.getOccupyPlan(request);
            } else {
                // 生成占用计划（先根据生产日期占用，如果超过批次数量就按照数量倒序再占一次）
                occupyPlanResult = occupyPlanS2LService.getOccupyPlanByTob(request, sgBTobSourceMaxBatch);
            }
            // 无占用或异常直接返回
            if (StrategyConstants.RESULT_CODE_PREOUT != occupyPlanResult.getCode()) {
                valueHolderV14.setCode(occupyPlanResult.getCode());
                valueHolderV14.setMessage(occupyPlanResult.getMessage());
                return valueHolderV14;
            }
            occupyPlan = occupyPlanResult.getData();
            FindSourceStrategyUtils.outputLog("SgFindSourceS2LPreOutStorageService.getOccupyPlan S->L 占用计划结果 param:{}", JSONObject.toJSONString(occupyPlan));
            // 根据占用计划 生成逻辑占用单
            addStoOutBill(request, occupyPlan, valueHolderV14);
        } catch (Exception e) {
            log.error("SgFindSourceS2LPreOutStorageService S->L 二阶段寻源派单 来源明细id:{} 库存占用失败", request.getSourceBillId());
            valueHolderV14.setCode(StrategyConstants.RESULT_CODE_PREOUT);
            valueHolderV14.setMessage("S->L 二阶段寻源派单 来源明细 库存占用失败");
            return valueHolderV14;
        }

        return valueHolderV14;
    }

    /**
     * @param request:
     * @param occupyPlan:
     * @Description: 新增逻辑占用单
     * @Author: hwy
     * @Date: 2021/7/3 16:58
     * @return: com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsResult
     **/
    public SgFindSourceStrategyOmsResult addStoOutBill(SgFindSourceStrategyS2LRequest request, SgOccupyPlanServiceResult occupyPlan,
                                                       ValueHolderV14<SgFindSourceStrategyOmsResult> valueHolderV14) {
        SgFindSourceStrategyOmsResult omsResult = new SgFindSourceStrategyOmsResult();
        valueHolderV14.setData(omsResult);
        omsResult.setSourceBillId(request.getSourceBillId());
        omsResult.setSourceBillType(request.getSourceBillType());
        omsResult.setSourceBillNo(request.getSourceBillNo());
        List<SgFindSourceStrategyOmsItemResult> omsItemList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        List<Long> wareHouseIds = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        omsResult.setItemResultList(omsItemList);
        // 创建逻辑占用单入参
        Map<Long, SgBStoOutBillSaveRequest> saveRequestMap = buildStoStorageChangeRequest(request, occupyPlan);
        if (MapUtils.isEmpty(saveRequestMap)) {
            log.error("SgFindSourceS2LPreOutStorageService S->L 二阶段寻源派单 来源单据id:{} 生成逻辑占用单入参为空 订单全部缺货", request.getSourceBillId());
            allOut(request, omsResult, "S->L 二阶段寻源派单 生成逻辑占用单入参为空 订单全部缺货");
            return omsResult;
        }
        FindSourceStrategyUtils.outputLog("SgFindSourceS2LPreOutStorageService.addStoOutBill S->L 新增逻辑占用单数量:{}", saveRequestMap.size());
        CusRedisTemplate<String, String> redisMasterTemplate = RedisMasterUtils.getObjRedisTemplate();
        String retryKey = SgConstants.REDIS_KEY_FIND_SOURCE_RETRY_S2L + request.getSourceBillType() +
                SgConstants.SG_CONNECTOR_MARKS_6 + request.getSourceBillNo() +
                SgConstants.SG_CONNECTOR_MARKS_6 + request.getSourceBillId();
        try {

            SgBStoOutSaveService sgBStoOutSaveService = ApplicationContextHandle.getBean(SgBStoOutSaveService.class);
            Set<Map.Entry<Long, SgBStoOutBillSaveRequest>> entries = saveRequestMap.entrySet();
            for (Map.Entry<Long, SgBStoOutBillSaveRequest> entry : entries) {
                Long wareHouseId = entry.getKey();
                if (!StrategyConstants.OUT_DEFAULT_STORE_ID.equals(wareHouseId) && !wareHouseIds.contains(wareHouseId)) {
                    wareHouseIds.add(wareHouseId);
                }
                SgBStoOutBillSaveRequest saveRequest = entry.getValue();
                ValueHolderV14<SgBStoOutBillSaveResult> stoOccupyResult = sgBStoOutSaveService.saveSgStoOut(saveRequest);
                if (!stoOccupyResult.isOK()) {
                    log.error("SgFindSourceS2LPreOutStorageService S->L 二阶段寻源派单 来源单据id:{} 逻辑占用单生成服务 生成逻辑占用单失败 订单全部缺货 :{}", request.getSourceBillId(), valueHolderV14.getMessage());
                    valueHolderV14.setCode(StrategyConstants.RESULT_CODE_EXCEPTION);
                    valueHolderV14.setMessage("S->L 二阶段寻源派单 逻辑占用单生成服务 生成逻辑占用单失败 订单全部缺货");
                    FindSourceStrategyUtils.outputLog("SgFindSourceS2LPreOutStorageService S->L 二阶段寻源派单 逻辑占用单生成服务 来源单据id:{} 生成逻辑占用单失败 开始回滚已占用的逻辑占用单", request.getSourceBillId(), valueHolderV14.getMessage());
                    sgFindSourceRollBackService.stoOutRollBackStorage(request.getSourceBillNo(), request.getSourceBillId(), request.getSourceBillType());

                    //是否重新二阶段寻源
                    this.s2LRetry(redisMasterTemplate, retryKey, request, omsResult);
                    return omsResult;
                }
                FindSourceStrategyUtils.outputLog("SgFindSourceS2LPreOutStorageService.addStoOutBill S->L 新增共享占用单结果:{}", stoOccupyResult.toJSONObject());
                SgBStoOutBillSaveResult saveResult = stoOccupyResult.getData();
                setOmsResult(entry, saveResult, omsItemList);
            }
            //如果是配货单, 逻辑占用单占用成功需要更新 时效订单中sg_b_share_out 时效订单来源明细id, 来源单号由时效订单改为配货单
            if (SgConstantsIF.BILL_SHARE_DISTRIBUTION == request.getSourceBillType()) {
                //异步更新 共享占用单信息
                log.debug("start SgFindSourceS2LPreOutStorageService addStoOutBill 异步更新共享占用单信息 request = {}", JSONObject.toJSONString(request));
                CompletableFuture.runAsync(() -> {
                    SgFindSourceS2LPreOutStorageService bean = ApplicationContextHandle.getBean(SgFindSourceS2LPreOutStorageService.class);
                    bean.updateShareOut(request);
                    /*});*/
                }, asyncExecutorPool);
            }
            //补充组装返回结果
            supplementOmsResult(request, omsResult, wareHouseIds);

            //寻源无异常，如果存在寻源重试的Rediskey,删除
            if (Boolean.TRUE.equals(redisMasterTemplate.hasKey(retryKey))) {
                redisMasterTemplate.delete(retryKey);
            }
        } catch (Exception e) {
            log.error("SgFindSourceS2LPreOutStorageService S->L 二阶段寻源派单 来源单据id:{} 生成逻辑占用单失败 订单全部缺货 :{}", request.getSourceBillId(), Throwables.getStackTraceAsString(e));
//            allOut(request, omsResult, "S->L 二阶段寻源派单 生成逻辑占用单失败 订单全部缺货");
            valueHolderV14.setCode(StrategyConstants.RESULT_CODE_EXCEPTION);
            valueHolderV14.setMessage("S->L 二阶段寻源派单 逻辑占用单生成服务 生成逻辑占用单失败 订单全部缺货");
            FindSourceStrategyUtils.outputLog("SgFindSourceS2LPreOutStorageService S->L 二阶段寻源派单 逻辑占用单生成服务 来源单据id:{} 生成逻辑占用单失败 开始回滚已占用的逻辑占用单", request.getSourceBillId(), valueHolderV14.getMessage());
            sgFindSourceRollBackService.stoOutRollBackStorage(request.getSourceBillNo(), request.getSourceBillId(), request.getSourceBillType());

            //是否重新二阶段寻源
            this.s2LRetry(redisMasterTemplate, retryKey, request, omsResult);
            return omsResult;
        }
        // 对比差异 更新共享占用单
        if (occupyPlan != null) {
            sgFindSourceRollBackService.shareOut2ReleaseStorage(request, occupyPlan);
        }
        return omsResult;
    }

    /**
     * 逻辑仓缺货，寻源重试判断
     *
     * @param redisMasterTemplate
     * @param retryKey
     * @param request
     * @param omsResult
     */
    private void s2LRetry(CusRedisTemplate<String, String> redisMasterTemplate, String retryKey,
                          SgFindSourceStrategyS2LRequest request, SgFindSourceStrategyOmsResult omsResult) {
        PropertiesConf conf = ApplicationContextHandle.getBean(PropertiesConf.class);
        int maxRetryNum = conf.getProperty("sg.findSource.s2l.outStock.maxRetryNum", 5);
        if (Boolean.TRUE.equals(redisMasterTemplate.hasKey(retryKey))) {
            String tryCount = redisMasterTemplate.opsForValue().get(retryKey);
            //大于最大重试次数，返回寻源结果，删除Rediskey
            if (null != tryCount && Integer.valueOf(tryCount) > maxRetryNum) {
                redisMasterTemplate.delete(retryKey);
            } else {
                omsResult.setIsS2LRetry(Boolean.TRUE);
                //入参标记为虚拟寻源，二阶段重试不释放共享层占用
                request.setInventedOccupy(Boolean.TRUE);
                redisMasterTemplate.opsForValue().increment(retryKey, 1);
            }
        } else {
            omsResult.setIsS2LRetry(Boolean.TRUE);
            //入参标记为虚拟寻源，二阶段重试不释放共享层占用
            request.setInventedOccupy(Boolean.TRUE);
            redisMasterTemplate.opsForValue().setIfAbsent(retryKey, "1");
        }
    }

    /**
     * @param request:
     * @param omsResult:
     * @Description: 全部缺货
     * @Author: hwy
     * @Date: 2021/7/5 14:18
     * @return: void
     **/
    private void allOut(SgFindSourceStrategyS2LRequest request, SgFindSourceStrategyOmsResult omsResult, String msg) {
        List<SkuItemS2L> skuItems = request.getSkuItems();
        omsResult.setSourceBillId(request.getSourceBillId());
        omsResult.setSourceBillNo(request.getSourceBillNo());
        omsResult.setCode(ResultCode.FAIL);
        omsResult.setMessage(msg);
        omsResult.setSourceBillType(request.getSourceBillType());
        List<SgFindSourceStrategyOmsItemResult> itemResultList = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
        omsResult.setItemResultList(itemResultList);
        for (SkuItemS2L skuItem : skuItems) {
            BigDecimal qty = skuItem.getQty();
            Long sourceItemId = skuItem.getSourceItemId();
            SgFindSourceStrategyOmsItemResult omsItem = new SgFindSourceStrategyOmsItemResult();
            omsItem.setQtyPreOut(qty);
            omsItem.setSourceItemId(sourceItemId);
            omsItem.setWareHouseId(StrategyConstants.OUT_DEFAULT_STORE_ID);
            itemResultList.add(omsItem);
        }
    }

    /**
     * @param request:
     * @param omsResult:
     * @Description: 补充返回oms结果
     * @Author: hwy
     * @Date: 2021/7/3 16:54
     * @return: void
     **/
    public void supplementOmsResult(SgFindSourceStrategyS2LRequest request, SgFindSourceStrategyOmsResult omsResult, List<Long> wareHouseIds) {
        List<SkuItemS2L> skuItems = request.getSkuItems();
        List<SgFindSourceStrategyOmsItemResult> omsItemList = omsResult.getItemResultList();
        // 设置物理仓信息
        if (CollectionUtils.isNotEmpty(wareHouseIds)) {
            CpCPhyWarehouseMapper warehouseMapper = ApplicationContextHandle.getBean(CpCPhyWarehouseMapper.class);
            List<SgCpCPhyWarehouse> sgCpCPhyWarehouses = warehouseMapper.selectList(new QueryWrapper<SgCpCPhyWarehouse>().lambda()
                    .in(SgCpCPhyWarehouse::getId, wareHouseIds)
                    .eq(SgCpCPhyWarehouse::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (CollectionUtils.isNotEmpty(sgCpCPhyWarehouses)) {
                Map<Long, SgCpCPhyWarehouse> warehouseMap = sgCpCPhyWarehouses.stream().collect(Collectors.toMap(SgCpCPhyWarehouse::getId, Function.identity()));
                omsItemList.stream().forEach(o -> {
                    Long wareHouseId = o.getWareHouseId();
                    if (warehouseMap.containsKey(wareHouseId)) {
                        SgCpCPhyWarehouse sgCpCPhyWarehouse = warehouseMap.get(wareHouseId);
                        o.setWareHouseEcode(sgCpCPhyWarehouse.getEcode());
                        o.setWareHouseEname(sgCpCPhyWarehouse.getEname());
                    }
                });
            }
        }
        // key sourceItemId
        Map<Long, List<SgFindSourceStrategyOmsItemResult>> itemMap = omsItemList.stream().collect(Collectors.groupingBy(SgFindSourceStrategyOmsItemResult::getSourceItemId));
        //待处理的结果集
        Boolean pendingFlag = Boolean.FALSE;
        Map<Long, List<SgFindSourceStrategyOmsItemResult>> itemPendingMap = new HashMap<>();
        for (SkuItemS2L skuItem : skuItems) {
            Long sourceItemId = skuItem.getSourceItemId();
            BigDecimal qty = skuItem.getQty();
            //防止明细重复
            BigDecimal qtyOutStock = BigDecimal.ZERO;
            List<SgFindSourceStrategyOmsItemResult> currSourceItem;
            if (itemMap.containsKey(sourceItemId)) {
                currSourceItem = itemMap.get(sourceItemId);
                BigDecimal qtyPreout = currSourceItem.stream().map(SgFindSourceStrategyOmsItemResult::getQtyPreOut).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                qtyOutStock = qty.subtract(qtyPreout);
                // 未分配完 添加缺货明细
            } else {
                currSourceItem = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
                itemMap.put(sourceItemId, currSourceItem);
                SgFindSourceStrategyOmsItemResult omsItem = new SgFindSourceStrategyOmsItemResult();
                omsItem.setSourceItemId(sourceItemId);
                omsItem.setWareHouseId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                omsItem.setQtyPreOut(qty);
                currSourceItem.add(omsItem);
                continue;
            }
            // 当原单数量的占用 小于 占用计划中当前明细的数量, 说明可能是配货单寻源相同条码  相同明细
            if (qtyOutStock.compareTo(BigDecimal.ZERO) < 0) {
                pendingFlag = Boolean.TRUE;
                //备份原始数据
                List<SgFindSourceStrategyOmsItemResult> tempList = itemPendingMap.get(sourceItemId);
                boolean isNeedBk = Boolean.FALSE;
                if (ObjectUtils.isEmpty(tempList)) {
                    tempList = new ArrayList<>();
                    itemPendingMap.put(sourceItemId, tempList);
                    isNeedBk = Boolean.TRUE;
                }
                for (SgFindSourceStrategyOmsItemResult omsItemResult : currSourceItem) {
                    if (isNeedBk) {
                        SgFindSourceStrategyOmsItemResult itemResult = new SgFindSourceStrategyOmsItemResult();
                        BeanUtils.copyProperties(omsItemResult, itemResult);
                        tempList.add(itemResult);
                    }

                    BigDecimal preOut = omsItemResult.getQtyPreOut();
                    if (qty.compareTo(preOut) >= 0) {
                        omsItemResult.setQtyPreOut(BigDecimal.ZERO);
                    } else {
                        omsItemResult.setQtyPreOut(preOut.subtract(qty));
                    }
                }
                continue;
            }
            // 明细全部分配完毕 跳过
            if (qtyOutStock.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            // 未分配完 添加缺货明细
            SgFindSourceStrategyOmsItemResult omsItem = new SgFindSourceStrategyOmsItemResult();
            omsItem.setSourceItemId(sourceItemId);
            omsItem.setWareHouseId(StrategyConstants.OUT_DEFAULT_STORE_ID);
            omsItem.setQtyPreOut(qtyOutStock);
            currSourceItem.add(omsItem);
        }
        //特殊处理  当前明细占用 小于 占用结果的占用数
        if (pendingFlag) {
            Map<Long, SkuItemS2L> mergeMap = new HashMap<>();
            skuItems.stream().forEach(skuItemS2L -> {
                Long sourceItemId = skuItemS2L.getSourceItemId();
                if (!mergeMap.containsKey(sourceItemId)) {
                    mergeMap.put(sourceItemId, skuItemS2L);
                    return;
                }
                SkuItemS2L itemS2L = mergeMap.get(sourceItemId);
                itemS2L.setQty(itemS2L.getQty().add(skuItemS2L.getQty()));
            });
            for (Long sourceItemId : itemPendingMap.keySet()) {
                List<SgFindSourceStrategyOmsItemResult> itemResultList = itemPendingMap.get(sourceItemId);
                BigDecimal totalQtyPreout = itemResultList.stream().map(SgFindSourceStrategyOmsItemResult::getQtyPreOut).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                SkuItemS2L skuItemS2L = mergeMap.get(sourceItemId);
                BigDecimal qty = skuItemS2L.getQty();

                itemMap.put(sourceItemId, itemResultList);
                if (qty.compareTo(totalQtyPreout) > 0) {

                    SgFindSourceStrategyOmsItemResult omsItem = new SgFindSourceStrategyOmsItemResult();
                    omsItem.setSourceItemId(sourceItemId);
                    omsItem.setWareHouseId(StrategyConstants.OUT_DEFAULT_STORE_ID);
                    omsItem.setQtyPreOut(qty.subtract(totalQtyPreout));
                    List<SgFindSourceStrategyOmsItemResult> itemResults = itemMap.get(sourceItemId);
                    itemResults.add(omsItem);
                }
            }
        }
        List<SgFindSourceStrategyOmsItemResult> newOmsItemList = new ArrayList<>();
        itemMap.values().stream().forEach(o -> {
            o.stream().forEach(i -> {
                newOmsItemList.add(i);
            });
        });
        omsResult.setItemResultList(newOmsItemList);
    }


    /**
     * @param entry:
     * @param saveResult:
     * @param omsItemList
     * @Description: 将当前占用单 转化为oms明细占用结果
     * @Author: hwy
     * @Date: 2021/7/3 16:42
     * @return: void
     **/
    private void setOmsResult(Map.Entry<Long, SgBStoOutBillSaveRequest> entry,
                              SgBStoOutBillSaveResult saveResult,
                              List<SgFindSourceStrategyOmsItemResult> omsItemList) {
        Long wareHoseId = entry.getKey();
        SgBStoOutBillSaveRequest saveRequest = entry.getValue();
        SgBStoOutSaveRequest sgBStoOutSaveRequest = saveRequest.getSgBStoOutSaveRequest();

        // 根据sourceItemId 聚合
        HashMap<Long, SgFindSourceStrategyOmsItemResult> resultMap = new HashMap<>();

        if (!sgBStoOutSaveRequest.getMergeMark()) {
            List<SgBStoOutItemSaveRequest> sgBStoOutItemSaveRequests = saveRequest.getSgBStoOutItemSaveRequests();
            for (SgBStoOutItemSaveRequest stoOutItem : sgBStoOutItemSaveRequests) {
                Long sourceBillItemId = stoOutItem.getSourceBillItemId();
                SgFindSourceStrategyOmsItemResult omsItemResult = resultMap.computeIfAbsent(sourceBillItemId, k -> new SgFindSourceStrategyOmsItemResult());
                omsItemResult.setSourceItemId(sourceBillItemId);
                omsItemResult.setQtyPreOut(Optional.ofNullable(omsItemResult.getQtyPreOut()).orElse(BigDecimal.ZERO).add(stoOutItem.getQtyPreout()));
                omsItemResult.setWareHouseId(wareHoseId);
                List<String> billNos = saveResult.getBillNo();
                if (CollectionUtils.isNotEmpty(billNos)) {
                    omsItemResult.setStoOutBillNo(billNos.get(0));
                }
            }
        } else {
            List<SgBStoOutItemLogSaveRequest> sgBStoOutItemLogSaveRequests = saveRequest.getSgBStoOutItemLogSaveRequests();
            for (SgBStoOutItemSaveRequest stoOutItem : sgBStoOutItemLogSaveRequests) {
                Long sourceBillItemId = stoOutItem.getSourceBillItemId();
                SgFindSourceStrategyOmsItemResult omsItemResult = resultMap.computeIfAbsent(sourceBillItemId, k -> new SgFindSourceStrategyOmsItemResult());
                omsItemResult.setSourceItemId(sourceBillItemId);
                omsItemResult.setQtyPreOut(Optional.ofNullable(omsItemResult.getQtyPreOut()).orElse(BigDecimal.ZERO).add(stoOutItem.getQtyPreout()));
                omsItemResult.setWareHouseId(wareHoseId);
                List<String> billNos = saveResult.getBillNo();
                if (CollectionUtils.isNotEmpty(billNos)) {
                    omsItemResult.setStoOutBillNo(billNos.get(0));
                }
            }
        }
        omsItemList.addAll(resultMap.values());
    }


    /**
     * @param request:
     * @param occupyPlan:
     * @Description: 创建逻辑占用单参数
     * @Author: hwy
     * @Date: 2021/7/3 14:52
     * @return: java.util.List<com.burgeon.r3.sg.store.model.request.out.SgBStoOutBillSaveRequest>
     **/
    public Map<Long, SgBStoOutBillSaveRequest> buildStoStorageChangeRequest(SgFindSourceStrategyS2LRequest request, SgOccupyPlanServiceResult occupyPlan) {
        List<SgOccupyPlanItemResult> itemResultList = occupyPlan.getItemResultList();
        if (CollectionUtils.isEmpty(itemResultList)) {
            FindSourceStrategyUtils.outputLog("S->L 二阶段寻源派单 来源单据id:{} 占用计划为空 无法生成逻辑占用单 订单全部缺货", request.getSourceBillId());
            return null;
        }
        //去重后的逻辑仓
        Set<Long> logicStoreIds = new HashSet<>();
        itemResultList.stream().map(SgOccupyPlanItemResult::getS2LStorePlan).map(Map::keySet).forEach(logicStoreIds::addAll);
        //查询逻辑仓归属实体仓
        CpCStoreMapper storeMapper = ApplicationContextHandle.getBean(CpCStoreMapper.class);
        List<SgCpCStore> sgCpCStores = storeMapper.selectList(new QueryWrapper<SgCpCStore>().lambda().
                select(SgCpCStore::getId, SgCpCStore::getCpCPhyWarehouseId).
                in(SgCpCStore::getId, logicStoreIds).
                eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y));
        // 逻辑仓对应实体仓 key:logicStoreId value:warehouseId
        Map<Long, Long> storeRelationMap = sgCpCStores.stream()
                .collect(Collectors.toMap(SgCpCStore::getId, SgCpCStore::getCpCPhyWarehouseId));

        // 逻辑占用单集合 key 实体仓id
        Map<Long, SgBStoOutBillSaveRequest> stoPreOutRequestMap = new HashMap<>(SgConstants.MAP_DEFAULT_INITIAL_CAPACITY);

        //遍历占用计划  生成逻辑占用单
        for (SgOccupyPlanItemResult itemPlan : itemResultList) {
            /*来源明细ID*/
            Long sourceItemId = itemPlan.getSourceItemId();
            /*二阶段寻源分配明细 <storeId, <produceDate, qty>>*/
            Map<Long, Map<String, BigDecimal>> storePlan = itemPlan.getS2LStorePlan();
            for (Map.Entry<Long, Map<String, BigDecimal>> entry : storePlan.entrySet()) {
                Long logicStoreId = entry.getKey();
                for (Map.Entry<String, BigDecimal> produceEntry : entry.getValue().entrySet()) {
                    BigDecimal qtyPreout = produceEntry.getValue();
                    if (!storeRelationMap.containsKey(logicStoreId)) {
                        log.error("S->L 二阶段寻源派单 来源单据id:{} 占用计划中的逻辑仓id {} 不存在 订单占用异常 全部缺货", request.getSourceBillId(), logicStoreId);
                        return null;
                    }
                    Long warehouseId = storeRelationMap.get(logicStoreId);
                    SgBStoOutBillSaveRequest sgBStoOutBillSaveRequest;
                    List<SgBStoOutItemSaveRequest> sgBStoOutItemSaveRequests;
                    List<SgBStoOutItemLogSaveRequest> sgBStoOutItemLogSaveRequests;
                    SgBStoOutSaveRequest sgBStoOutSaveRequest;
                    SgBStoOutItemSaveRequest sgBStoOutItemSaveRequest = new SgBStoOutItemSaveRequest();
                    if (!stoPreOutRequestMap.containsKey(warehouseId)) {
                        /*如果这个仓的占用明细第一次被遍历到，生成逻辑占用单，*/
                        sgBStoOutBillSaveRequest = new SgBStoOutBillSaveRequest();
                        sgBStoOutBillSaveRequest.setUpdateMethod(SgConstantsIF.ITEM_UPDATE_TYPE_ALL);
                        sgBStoOutBillSaveRequest.setIsEdge(Boolean.TRUE);
                        sgBStoOutBillSaveRequest.setIsForceUnNegative(Boolean.TRUE);
//                    sgBStoOutBillSaveRequest.setServiceNode();
                        sgBStoOutBillSaveRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());
                        sgBStoOutBillSaveRequest.setPreoutWarningType(SgConstantsIF.PREOUT_RESULT_ERROR);
                        sgBStoOutBillSaveRequest.setIsCancel(Boolean.FALSE);
                        sgBStoOutBillSaveRequest.setIsSourceMerge(Boolean.FALSE);
                        // 设置逻辑占用单主表信息
                        sgBStoOutSaveRequest = new SgBStoOutSaveRequest();
                        sgBStoOutSaveRequest.setSourceBillId(request.getSourceBillId());
                        sgBStoOutSaveRequest.setSourceBillNo(request.getSourceBillNo());
                        sgBStoOutSaveRequest.setSourceBillType(request.getSourceBillType());
                        sgBStoOutSaveRequest.setBillDate(request.getBillDate());
                        sgBStoOutSaveRequest.setCpCShopId(request.getShopId());
                        sgBStoOutSaveRequest.setTid(request.getTid());
                        sgBStoOutBillSaveRequest.setSgBStoOutSaveRequest(sgBStoOutSaveRequest);
                        //创建逻辑占用单明细信息
                        sgBStoOutItemSaveRequests = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
                        sgBStoOutBillSaveRequest.setSgBStoOutItemSaveRequests(sgBStoOutItemSaveRequests);

                        //创建逻辑占用单日志明细信息
                        sgBStoOutItemLogSaveRequests = new ArrayList<>();
                        sgBStoOutBillSaveRequest.setSgBStoOutItemLogSaveRequests(sgBStoOutItemLogSaveRequests);

                        stoPreOutRequestMap.put(warehouseId, sgBStoOutBillSaveRequest);
                    } else {
                        sgBStoOutBillSaveRequest = stoPreOutRequestMap.get(warehouseId);
                        sgBStoOutSaveRequest = sgBStoOutBillSaveRequest.getSgBStoOutSaveRequest();
                        sgBStoOutItemSaveRequests = sgBStoOutBillSaveRequest.getSgBStoOutItemSaveRequests();
                        sgBStoOutItemLogSaveRequests = sgBStoOutBillSaveRequest.getSgBStoOutItemLogSaveRequests();
                    }
                    // 设置逻辑占用单明细信息
                    sgBStoOutItemSaveRequest.setQty(qtyPreout);
                    sgBStoOutItemSaveRequest.setProduceDate(produceEntry.getKey());
                    sgBStoOutItemSaveRequest.setQtyPreout(qtyPreout);
                    sgBStoOutItemSaveRequest.setCpCStoreId(logicStoreId);
                    sgBStoOutItemSaveRequest.setSourceBillItemId(sourceItemId);
                    sgBStoOutItemSaveRequest.setPsCSkuId(itemPlan.getPsCSkuId());
                    sgBStoOutItemSaveRequest.setTid(request.getTid());
                    sgBStoOutItemSaveRequest.setBeginProduceDate(itemPlan.getBeginProduceDate());
                    sgBStoOutItemSaveRequest.setEndProduceDate(itemPlan.getEndProduceDate());
                    sgBStoOutItemSaveRequest.setLabelingRequirements(itemPlan.getLabelingRequirements());

                    //创建日志明细的request：此时-每个占用明细都会生成一个占用明细日志
                    SgBStoOutItemLogSaveRequest sgBStoOutItemLogSaveRequest = new SgBStoOutItemLogSaveRequest();
                    BeanUtils.copyProperties(sgBStoOutItemSaveRequest, sgBStoOutItemLogSaveRequest);
                    sgBStoOutItemLogSaveRequests.add(sgBStoOutItemLogSaveRequest);

                    // 合并明细
                    if (ObjectUtils.isNotEmpty(sgBStoOutItemSaveRequests)) {
                        /*占用明细，当前占用明细与占用参数的仓、品、效期一致时*/
                        SgBStoOutItemSaveRequest itemSaveRequest = sgBStoOutItemSaveRequests.stream()
                                .filter(sgBStoOutItem -> ObjectUtils.isNotEmpty(sgBStoOutItem.getCpCStoreId())
                                        && sgBStoOutItem.getCpCStoreId().equals(sgBStoOutItemSaveRequest.getCpCStoreId())
                                        && sgBStoOutItem.getPsCSkuId().equals(sgBStoOutItemSaveRequest.getPsCSkuId())
                                        && sgBStoOutItem.getProduceDate().equals(produceEntry.getKey()))
                                .findFirst().orElse(null);
                        if (ObjectUtils.isNotEmpty(itemSaveRequest)) {
                            /*占用量*/
                            BigDecimal qtyItem = ObjectUtils.isEmpty(itemSaveRequest.getQty()) ? BigDecimal.ZERO : itemSaveRequest.getQty();
                            /*未释放占用量*/
                            BigDecimal qtyPreoutItem = ObjectUtils.isEmpty(itemSaveRequest.getQtyPreout()) ? BigDecimal.ZERO : itemSaveRequest.getQtyPreout();
                            /*累加到一起*/
                            itemSaveRequest.setQty(qtyItem.add(sgBStoOutItemSaveRequest.getQty()));
                            itemSaveRequest.setQtyPreout(qtyPreoutItem.add(sgBStoOutItemSaveRequest.getQtyPreout()));
                        } else {
                            sgBStoOutItemSaveRequests.add(sgBStoOutItemSaveRequest);
                        }
                    } else {
                        sgBStoOutItemSaveRequests.add(sgBStoOutItemSaveRequest);
                    }

                    //合并标记  根据日志中条码  和  占单仓 来判断
                    if (CollectionUtils.isNotEmpty(sgBStoOutItemLogSaveRequests)
                            && !sgBStoOutSaveRequest.getMergeMark()) {
                        /*判断是否有合并：检查日志明细中是否存在重复的记录，如果存在则设置合并标记*/
                        Map<String, Long> saStoreMap = sgBStoOutItemLogSaveRequests.stream()
                                .collect(Collectors.groupingBy(itemLog -> itemLog.getPsCSkuId() + SgConstants.SG_CONNECTOR_MARKS_6 + itemLog.getCpCStoreId(),
                                        Collectors.counting()));
                        boolean match = saStoreMap.values().stream().anyMatch(count -> count > 1);
                        if (match) {
                            sgBStoOutSaveRequest.setMergeMark(match);
                            continue;
                        }
                    }
                }
            }
        }

        /*20250219：无论是否合并，都添加日志明细*/
//        //进行数据清洗, 合并的才添加 日志明细,  未合并的不使用日志明细
//        if (MapUtils.isNotEmpty(stoPreOutRequestMap)) {
//            stoPreOutRequestMap.forEach((key, val) -> {
//                SgBStoOutSaveRequest sgBStoOutSaveRequest = val.getSgBStoOutSaveRequest();
//                if (!sgBStoOutSaveRequest.getMergeMark()) {
//                    //直接清除日志
//                    val.setSgBStoOutItemLogSaveRequests(null);
//                }
//            });
//        }

        return stoPreOutRequestMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateShareOut(SgFindSourceStrategyS2LRequest request) {
        //如果是配货单, 逻辑占用单占用成功需要更新 时效订单中sg_b_share_out 时效订单来源明细id, 来源单号由时效订单改为配货单
        ////配货单  相同聚合仓  相同条码需要聚合   产品需求
        if (SgConstantsIF.BILL_SHARE_DISTRIBUTION == request.getSourceBillType()) {
            LogUtils.printLog(" Start SgFindSourceS2LPreOutStorageService addStoOutBill 寻仓单二阶段寻源逻辑占用单成功需要修改时效订单的共享占用 start");
            List<SkuItemS2L> skuItems = request.getSkuItems();
            List<SkuItemS2L> vipList = new ArrayList<>();
            List<SkuItemS2L> retailList = new ArrayList<>();
            skuItems.stream().forEach(skuItemS2L -> {
                if (ObjectUtils.isNotEmpty(skuItemS2L.getTimeOrderId())) {
                    vipList.add(skuItemS2L);
                } else {
                    retailList.add(skuItemS2L);
                }
            });
            //获取所有主的共享占用单
            List<SgBShareOut> sgBShareOuts = sgBShareOutMapper.selectList(Wrappers.<SgBShareOut>query().lambda()
                    .or(CollectionUtils.isNotEmpty(vipList), wrapper -> wrapper.eq(SgBShareOut::getSourceBillType, SgConstantsIF.BILL_TYPE_VIPSHOP_TIME).eq(SgBShareOut::getIsactive, SgConstants.IS_ACTIVE_Y).in(SgBShareOut::getSourceBillId, vipList.stream().map(SkuItemS2L::getTimeOrderId).collect(Collectors.toList())))
                    .or(CollectionUtils.isNotEmpty(retailList), wrapper -> wrapper.eq(SgBShareOut::getSourceBillType, SgConstantsIF.BILL_SHARE_DISTRIBUTION).eq(SgBShareOut::getIsactive, SgConstants.IS_ACTIVE_Y).in(SgBShareOut::getSourceBillId, request.getSourceBillId())));
            log.info(" Start SgFindSourceS2LPreOutStorageService addStoOutBill get all sgBShareOuts ={}", JSONObject.toJSONString(sgBShareOuts));
            Map<Long, List<SkuItemS2L>> vipMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(vipList)) {
                vipMap = vipList.stream().collect(Collectors.groupingBy(SkuItemS2L::getTimeOrderId));
            }
            Map<Long, List<SkuItemS2L>> retailMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(retailList)) {
                retailMap = retailList.stream().collect(Collectors.groupingBy(SkuItemS2L::getSourceItemId));
            }
            if (CollectionUtils.isNotEmpty(sgBShareOuts)) {
                List<SgBShareOutItem> shareOutItems = sgBShareOutItemMapper.selectList(Wrappers.<SgBShareOutItem>query().lambda()
                        .in(SgBShareOutItem::getSgBShareOutId, sgBShareOuts.stream().map(SgBShareOut::getId).collect(Collectors.toList()))
                        .eq(SgBShareOutItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                );
                Map<Long, List<SgBShareOutItem>> shareOutItemTempMap = shareOutItems.stream().collect(Collectors.groupingBy(SgBShareOutItem::getSgBShareOutId));
                List<SgBShareOutItem> allSgBShareOutItems = new ArrayList<>();
                for (SgBShareOut sgBShareOut : sgBShareOuts) {
                    Integer sourceBillType = sgBShareOut.getSourceBillType();
                    List<SgBShareOutItem> sgBShareOutItems = shareOutItemTempMap.get(sgBShareOut.getId());
                    if (sourceBillType == SgConstantsIF.BILL_TYPE_VIPSHOP_TIME) {
                        if (MapUtils.isEmpty(vipMap)) {
                            log.info("  Start SgFindSourceS2LPreOutStorageService addStoOutBill 配货单更新来源单据信息 vip 与请求参数不匹配 请检查 sgBShareOut = {}", JSONObject.toJSONString(sgBShareOut));
                            continue;
                        }
                        Map<Long, List<SkuItemS2L>> vimItemMap = vipList.stream().collect(Collectors.groupingBy(SkuItemS2L::getTimeOrderItemId));
                        //当前时效单的所有明细
                        List<SkuItemS2L> skuItemS2LS = vipMap.get(sgBShareOut.getSourceBillId());
                        if (CollectionUtils.isEmpty(skuItemS2LS)) {
                            log.info("  Start SgFindSourceS2LPreOutStorageService addStoOutBill 配货单更新来源单据信息时 vip 对应的请求参数不存在 gSourceBillId = {}", sgBShareOut.getSourceBillId());
                            continue;
                        }
                        List<SgBShareOutItem> vipSgBShareOutItems = new ArrayList<>();
                        Set<Long> sourceBillItemIds = vimItemMap.keySet();
                        for (SgBShareOutItem sgBShareOutItem : sgBShareOutItems) {
                            Long sourceBillItemId = sgBShareOutItem.getSourceBillItemId();
                            if (sourceBillItemIds.contains(sourceBillItemId)) {
                                vipSgBShareOutItems.add(sgBShareOutItem);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(vipSgBShareOutItems)) {

                            for (SgBShareOutItem sgBShareOutItem : vipSgBShareOutItems) {
                                List<SkuItemS2L> itemS2LList = vimItemMap.get(sgBShareOutItem.getSourceBillItemId());
                                if (CollectionUtils.isEmpty(itemS2LList)) {

                                    continue;
                                }
                                SkuItemS2L skuItemS2L = itemS2LList.get(0);
                                sgBShareOutItem.setSourceBillItemId(skuItemS2L.getSourceItemId());
                            }
                            allSgBShareOutItems.addAll(sgBShareOutItems);
                        }
                    } else if (sourceBillType == SgConstantsIF.BILL_SHARE_DISTRIBUTION) {
                        if (MapUtils.isEmpty(retailMap)) {
                            log.info("  Start SgFindSourceS2LPreOutStorageService addStoOutBill 配货单更新来源单据信息 与请求参数不匹配 retail 请检查 sgBShareOut = {}", JSONObject.toJSONString(sgBShareOut));
                            continue;
                        }
                        List<SgBShareOutItem> retailSgBShareOutItems = new ArrayList<>();
                        Set<Long> sourceBillItemIds = retailMap.keySet();
                        for (SgBShareOutItem sgBShareOutItem : sgBShareOutItems) {
                            Long sourceBillItemId = sgBShareOutItem.getSourceBillItemId();
                            if (sourceBillItemIds.contains(sourceBillItemId)) {
                                retailSgBShareOutItems.add(sgBShareOutItem);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(retailSgBShareOutItems)) {

                            for (SgBShareOutItem sgBShareOutItem : retailSgBShareOutItems) {
                                List<SkuItemS2L> itemS2LList = retailMap.get(sgBShareOutItem.getSourceBillItemId());
                                if (CollectionUtils.isEmpty(itemS2LList)) {
                                    log.info("  Start SgFindSourceS2LPreOutStorageService addStoOutBill 配货单更新来源单据信息时 retail 对应的请求参数不存在 SourceBillItemId = {}", sgBShareOutItem.getSourceBillItemId());
                                    continue;
                                }
                                SkuItemS2L skuItemS2L = itemS2LList.get(0);
                                sgBShareOutItem.setSourceBillItemId(skuItemS2L.getSourceItemId());
                            }
                            allSgBShareOutItems.addAll(sgBShareOutItems);
                        }

                    }
                    sgBShareOut.setSourceBillNo(request.getSourceBillNo());
                    sgBShareOut.setSourceBillId(request.getSourceBillId());
                    sgBShareOut.setSourceBillType(request.getSourceBillType());
                }

                //所有的待更新并且合并, 仓和条码的维度  进行合并
                Map<Long, SgBShareOut> finalUpdateMap = new HashMap<>();
                List<SgBShareOut> finalDeleteList = new ArrayList<>();
                // key1: 主表id  key2: 配销仓id_pscskuid
                Map<Long, Map<String, SgBShareOutItem>> finalUpdateItemMap = new HashMap<>();
                List<SgBShareOutItem> finalDeleteItemList = new ArrayList<>();
                //需要更新的共享占用流水
                Map<String, List<String>> finalUpdateShareFtpMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(sgBShareOuts) && CollectionUtils.isNotEmpty(allSgBShareOutItems)) {
                    Map<Long, List<SgBShareOutItem>> allIteamMap = allSgBShareOutItems.stream().collect(Collectors.groupingBy(SgBShareOutItem::getSgBShareOutId));
                    for (SgBShareOut sgBShareOut : sgBShareOuts) {
                        Long sgCShareStoreId = sgBShareOut.getSgCShareStoreId();
                        Long id = sgBShareOut.getId();
                        List<SgBShareOutItem> shareOutItemList = allIteamMap.get(id);
                        if (CollectionUtils.isEmpty(shareOutItemList)) {
                            continue;
                        }
                        Map<String, SgBShareOutItem> storeItemMap = shareOutItemList.stream().collect(Collectors.toMap(sgBShareOutItem -> {
                            if (sgBShareOutItem.getSgCSaStoreId() != null) {
                                return SgConstantsIF.STORAGE_TYPE_SA + SgConstants.SG_CONNECTOR_MARKS_6 + sgBShareOutItem.getSgCSaStoreId() + SgConstants.SG_CONNECTOR_MARKS_6 + sgBShareOutItem.getPsCSkuId();
                            } else if (sgBShareOutItem.getSgCSharePoolId() != null) {
                                return SgConstantsIF.STORAGE_TYPE_SP + SgConstants.SG_CONNECTOR_MARKS_6 + sgBShareOutItem.getSgCSharePoolId() + SgConstants.SG_CONNECTOR_MARKS_6 + sgBShareOutItem.getPsCSkuId();
                            } else {
                                //默认kong
                                return "";
                            }
                        }, t -> t));
                        String billNo = sgBShareOut.getBillNo();
                        if (!finalUpdateMap.containsKey(sgCShareStoreId)) {
                            finalUpdateMap.put(sgCShareStoreId, sgBShareOut);
                            finalUpdateItemMap.put(id, storeItemMap);
                            sgBShareOut.setTotRowNum(storeItemMap.size());
                            List<String> billNos = new ArrayList<>();
                            billNos.add(billNo);
                            finalUpdateShareFtpMap.put(billNo + SgConstants.SG_CONNECTOR_MARKS_6 + id, billNos);
                            continue;
                        }
                        //数量相加
                        SgBShareOut oldSgBShareOut = finalUpdateMap.get(sgCShareStoreId);
                        oldSgBShareOut.setTotQtyOut(oldSgBShareOut.getTotQtyOut().add(sgBShareOut.getTotQtyOut()));
                        oldSgBShareOut.setTotQtyOrign(oldSgBShareOut.getTotQtyOrign().add(sgBShareOut.getTotQtyOrign()));
                        oldSgBShareOut.setTotQtyPreout(oldSgBShareOut.getTotQtyPreout().add(sgBShareOut.getTotQtyPreout()));
                        String oldBillNo = oldSgBShareOut.getBillNo();
                        Long outId = oldSgBShareOut.getId();
                        if (finalUpdateShareFtpMap.containsKey(oldBillNo + SgConstants.SG_CONNECTOR_MARKS_6 + outId)) {
                            List<String> billNos = finalUpdateShareFtpMap.get(oldBillNo + SgConstants.SG_CONNECTOR_MARKS_6 + outId);
                            billNos.add(billNo);
                        } else {
                            List<String> billNos = new ArrayList<>();
                            billNos.add(billNo);
                            finalUpdateShareFtpMap.put(oldBillNo + SgConstants.SG_CONNECTOR_MARKS_6 + outId, billNos);
                        }
                        Map<String, SgBShareOutItem> shareOutItemMap = finalUpdateItemMap.get(oldSgBShareOut.getId());
                        //新
                        Set<String> keys = storeItemMap.keySet();
                        for (String key : keys) {
                            SgBShareOutItem newSgBShareOutItem = storeItemMap.get(key);
                            if (!shareOutItemMap.containsKey(key)) {
                                newSgBShareOutItem.setSgBShareOutId(oldSgBShareOut.getId());
                                shareOutItemMap.put(key, newSgBShareOutItem);
                                oldSgBShareOut.setTotRowNum(oldSgBShareOut.getTotRowNum() + 1);
                            } else {
                                SgBShareOutItem sgBShareOutItem = shareOutItemMap.get(key);

                                sgBShareOutItem.setQty(sgBShareOutItem.getQty().add(newSgBShareOutItem.getQty()));
                                sgBShareOutItem.setQtyOut(sgBShareOutItem.getQtyOut().add(newSgBShareOutItem.getQtyOut()));
                                sgBShareOutItem.setQtyPreout(sgBShareOutItem.getQtyPreout().add(newSgBShareOutItem.getQtyPreout()));

                                finalDeleteItemList.add(newSgBShareOutItem);
                            }
                        }
                        finalDeleteList.add(sgBShareOut);
                    }
                }
                if (MapUtils.isNotEmpty(finalUpdateMap)) {
                    Collection<SgBShareOut> shareOuts = finalUpdateMap.values();
                    sgBShareOutMapper.batchUpdateById(new ArrayList<SgBShareOut>(shareOuts));
                }
                if (CollectionUtils.isNotEmpty(finalDeleteList)) {
                    log.info(" Start SgFindSourceS2LPreOutStorageService addStoOutBill finalDeleteList = {}", JSONObject.toJSONString(finalDeleteList));
                    sgBShareOutMapper.deleteBatchIds(finalDeleteList.stream().map(SgBShareOut::getId).collect(Collectors.toList()));
                }
                if (CollectionUtils.isNotEmpty(finalDeleteItemList)) {
                    log.info(" Start SgFindSourceS2LPreOutStorageService addStoOutBill finalDeleteItemList = {}", JSONObject.toJSONString(finalDeleteItemList));
                    sgBShareOutItemMapper.deleteBatchIds(finalDeleteItemList.stream().map(SgBShareOutItem::getId).collect(Collectors.toList()));
                }
                if (MapUtils.isNotEmpty(finalUpdateItemMap)) {
                    log.info(" Start SgFindSourceS2LPreOutStorageService addStoOutBill finalUpdateItemMap = {}", JSONObject.toJSONString(finalUpdateItemMap));
                    List<SgBShareOutItem> updateItemList = new ArrayList<>();
                    finalUpdateItemMap.forEach((k, v) -> {
                        Map<String, SgBShareOutItem> v1 = v;
                        updateItemList.addAll(v.values());
                    });
                    sgBShareOutItemMapper.batchUpdateByItemId(updateItemList);
                }
                //更新流水
                if (MapUtils.isNotEmpty(finalUpdateShareFtpMap)) {
                    List<SgBSaStoragePreoutFtp> updateSaStoragePreoutFtps = new ArrayList<>();
                    List<SgBSpStoragePreoutFtp> updateSpStoragePreoutFtps = new ArrayList<>();
                    finalUpdateShareFtpMap.forEach((k, v) -> {
                        String[] split = k.split(SgConstants.SG_CONNECTOR_MARKS_6);
                        String billNo = split[0];
                        Long id = Long.valueOf(split[1]);
                        Map<String, SgBShareOutItem> saStoreItemMap = finalUpdateItemMap.get(id);
                        List<SgBSaStoragePreoutFtp> sgBSaStoragePreoutFtps = saStoragePreoutFtpMapper.selectList(Wrappers.<SgBSaStoragePreoutFtp>lambdaQuery().in(SgBSaStoragePreoutFtp::getBillNo, v).eq(SgBSaStoragePreoutFtp::getIsactive, SgConstants.IS_ACTIVE_Y));
                        if (CollectionUtils.isNotEmpty(sgBSaStoragePreoutFtps)) {
                            sgBSaStoragePreoutFtps.stream().forEach(sgBSaStoragePreoutFtp -> {
                                sgBSaStoragePreoutFtp.setBillNo(billNo);
                                sgBSaStoragePreoutFtp.setBillId(id);
                                sgBSaStoragePreoutFtp.setBillType(request.getSourceBillType());
                                if (MapUtils.isNotEmpty(saStoreItemMap)) {
                                    Long psCSkuId = sgBSaStoragePreoutFtp.getPsCSkuId();
                                    Long sgCSaStoreId = sgBSaStoragePreoutFtp.getSgCSaStoreId();
                                    SgBShareOutItem sgBShareOutItem = saStoreItemMap.get(SgConstantsIF.STORAGE_TYPE_SA + SgConstants.SG_CONNECTOR_MARKS_6 + sgCSaStoreId + SgConstants.SG_CONNECTOR_MARKS_6 + psCSkuId);
                                    sgBSaStoragePreoutFtp.setBillItemId(sgBShareOutItem.getId());
                                }
                                sgBSaStoragePreoutFtp.setSourceBillId(request.getSourceBillId());
                                sgBSaStoragePreoutFtp.setSourceBillNo(request.getSourceBillNo());
                            });
                            updateSaStoragePreoutFtps.addAll(sgBSaStoragePreoutFtps);
                        }
                        List<SgBSpStoragePreoutFtp> sgBSpStoragePreoutFtps = spStoragePreoutFtpMapper.selectList(Wrappers.<SgBSpStoragePreoutFtp>lambdaQuery().in(SgBSpStoragePreoutFtp::getBillNo, v).eq(SgBSpStoragePreoutFtp::getIsactive, SgConstants.IS_ACTIVE_Y));
                        if (CollectionUtils.isNotEmpty(sgBSpStoragePreoutFtps)) {
                            sgBSpStoragePreoutFtps.stream().forEach(sgBSpStoragePreoutFtp -> {
                                sgBSpStoragePreoutFtp.setBillNo(billNo);
                                sgBSpStoragePreoutFtp.setBillId(id);
                                sgBSpStoragePreoutFtp.setBillType(request.getSourceBillType());
                                if (MapUtils.isNotEmpty(saStoreItemMap)) {
                                    Long psCSkuId = sgBSpStoragePreoutFtp.getPsCSkuId();
                                    Long sgCShareStoreId = sgBSpStoragePreoutFtp.getSgCShareStoreId();
                                    SgBShareOutItem sgBShareOutItem = saStoreItemMap.get(SgConstantsIF.STORAGE_TYPE_SP + SgConstants.SG_CONNECTOR_MARKS_6 + sgCShareStoreId + SgConstants.SG_CONNECTOR_MARKS_6 + psCSkuId);
                                    sgBSpStoragePreoutFtp.setBillItemId(sgBShareOutItem.getId());
                                }
                                sgBSpStoragePreoutFtp.setSourceBillId(request.getSourceBillId());
                                sgBSpStoragePreoutFtp.setSourceBillNo(request.getSourceBillNo());
                            });
                            updateSpStoragePreoutFtps.addAll(sgBSpStoragePreoutFtps);
                        }

                    });

                    if (CollectionUtils.isNotEmpty(updateSaStoragePreoutFtps)) {
                        saStoragePreoutFtpMapper.batchUpdateById(updateSaStoragePreoutFtps);
                    }
                    if (CollectionUtils.isNotEmpty(updateSpStoragePreoutFtps)) {
                        spStoragePreoutFtpMapper.batchUpdateById(updateSpStoragePreoutFtps);
                    }

                }


            }

        }
    }

}