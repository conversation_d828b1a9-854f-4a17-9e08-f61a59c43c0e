package com.burgeon.r3.sg.sourcing.mapper;


import com.burgeon.r3.sg.core.model.table.basic.SgCShareScoreFactorStrategy;
import com.burgeon.r3.sg.sourcing.model.result.strategy.SgShareScoreFactorStrategyQueryResult;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;


@Mapper
@Repository
public interface SgCShareScoreFactorStrategyMapper extends ExtentionMapper<SgCShareScoreFactorStrategy> {

    @Select({
            "<script>",
            " SELECT ",
            " ID,ECODE,ENAME,REMARK",
            " FROM SG_C_SHARE_SCORE_FACTOR_STRATEGY",
            "</script>"
    })
    List<SgShareScoreFactorStrategyQueryResult> selectListScoreFactorStrategy();

}