package com.burgeon.r3.sg.sourcing.services.item;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCpCStore;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyS2LRequest;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategySkuS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyStoreItemS2LResult;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import com.burgeon.r3.sg.sourcing.utils.FindSourceStrategyUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 唯品会 店/仓过滤策略
 * @author: hwy
 * @time: 2021/7/7 13:32
 */
@Slf4j
@Component
public class SgFindSourceVipFilterStrategyService extends StrategyHandle {


    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {
        FindSourceStrategyUtils.outputLog("SgFindSourceVipFilterStrategyService.handleRequest S->L 二阶段寻源派单 唯品会店/仓过滤策略 param:{}", JSONObject.toJSONString(request));
        ValueHolderV14<StrategyBaseResult> valueHolderV14 = new ValueHolderV14<>();
        SgFindSourceStrategyS2LRequest strategyRequest = (SgFindSourceStrategyS2LRequest) request;
        String vipOrderFlag = strategyRequest.getVipOrderFlag();
        SgFindSourceStrategyS2LResult strategyResult = (SgFindSourceStrategyS2LResult) strategyRequest.getStrategyBaseResult();
        List<SgFindSourceStrategySkuS2LResult> skuResultList = strategyResult.getSkuResultList();
        if (CollectionUtils.isEmpty(skuResultList)) {
            FindSourceStrategyUtils.outputLog("C->S 二阶段寻源派单 唯品会店/仓过滤策略 没有可用的仓 结束当前策略");
            valueHolderV14.setData(strategyResult);
            return doNext(request, valueHolderV14);
        }
        // 非唯品会订单不执行此策略
        if (!SgConstants.IS_ACTIVE_Y.equals(vipOrderFlag)) {
            valueHolderV14.setData(strategyRequest.getStrategyBaseResult());
            return doNext(request, valueHolderV14);
        }
        try {
            List<Long> logicStoreIds = new ArrayList<>(SgConstants.LIST_DEFAULT_INITIAL_CAPACITY);
            skuResultList.stream().forEach(o -> {
                List<SgFindSourceStrategyStoreItemS2LResult> itemResult = o.getItemResultList();
                if (CollectionUtils.isEmpty(itemResult)) {
                    return;
                }
                itemResult.stream().forEach(i -> {
                    Map<Long, SortedMap<String, BigDecimal>> logicStorageMap = i.getLogicStorageMap();
                    if (MapUtils.isNotEmpty(logicStorageMap)) {
                        logicStoreIds.addAll(logicStorageMap.keySet());
                    }
                });
            });
            List<Long> storeIds = logicStoreIds.stream().distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(storeIds)) {
                FindSourceStrategyUtils.outputLog("C->S 二阶段寻源派单 唯品会店/仓过滤策略 没有可用的仓 结束当前策略");
                valueHolderV14.setData(strategyResult);
                doNext(request, valueHolderV14);
            }
            String storeDeliveryFlag = strategyRequest.getStoreDeliveryFlag();
            CpCStoreMapper storeMapper = ApplicationContextHandle.getBean(CpCStoreMapper.class);
            String vipStoreNature = "";
            if (!SgConstants.IS_ACTIVE_Y.equals(storeDeliveryFlag)) {
                vipStoreNature = SgConstants.VIP_STORE_NATURE_SHOP;
            } else {
                vipStoreNature = SgConstants.VIP_STORE_NATURE_WAREHOUSE;
            }
            List<SgCpCStore> sgCpCStores = storeMapper.selectList(new QueryWrapper<SgCpCStore>().lambda()
                    .eq(SgCpCStore::getId, storeIds)
                    .eq(SgCpCStore::getVipStoreNature, vipStoreNature)
                    .eq(SgCpCStore::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (CollectionUtils.isEmpty(sgCpCStores)) {
                FindSourceStrategyUtils.outputLog("C->S 二阶段寻源派单 唯品会店/仓过滤策略 过滤后没有可用的仓 执行下一个策略");
                setResult(skuResultList, sgCpCStores);
                valueHolderV14.setData(strategyResult);
                doNext(request, valueHolderV14);
            }
        } catch (Exception e) {
            log.error(" C->S 二阶段寻源派单 唯品会店/仓过滤策略 执行异常:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("C->S 二阶段寻源派单 唯品会店/仓过滤策略 执行异常");
        }
        return doNext(request, valueHolderV14);
    }

    /**
     * @param skuResultList:
     * @Description: 设置新结果
     * @Author: hwy
     * @Date: 2021/7/7 19:30
     * @return: void
     **/
    private void setResult(List<SgFindSourceStrategySkuS2LResult> skuResultList, List<SgCpCStore> sgCpCStores) {
        //可发货仓库为空  结果集全部置空
        if (CollectionUtils.isEmpty(sgCpCStores)) {
            for (SgFindSourceStrategySkuS2LResult skuResult : skuResultList) {
                List<SgFindSourceStrategyStoreItemS2LResult> newItemResult = new ArrayList<>();
                skuResult.setItemResultList(newItemResult);
            }
            return;
        }
        //存在可发货仓库 更新对应结果集
        List<Long> storeIds = sgCpCStores.stream().map(SgCpCStore::getId).collect(Collectors.toList());
        for (SgFindSourceStrategySkuS2LResult skuResult : skuResultList) {
            List<SgFindSourceStrategyStoreItemS2LResult> itemResultList = skuResult.getItemResultList();
            if (CollectionUtils.isEmpty(itemResultList)) {
                continue;
            }
            List<SgFindSourceStrategyStoreItemS2LResult> newItemResultList = new ArrayList<>();
            for (SgFindSourceStrategyStoreItemS2LResult itemResult : itemResultList) {
                Map<Long, SortedMap<String, BigDecimal>> logicStorageMap = itemResult.getLogicStorageMap();
                if (MapUtils.isEmpty(logicStorageMap)) {
                    continue;
                }
                BigDecimal phyStorage = itemResult.getQty();
                HashMap<Long, SortedMap<String, BigDecimal>> newLogicStorageMap = new HashMap<>();
                for (Map.Entry<Long, SortedMap<String, BigDecimal>> entry : logicStorageMap.entrySet()) {
                    Long storeId = entry.getKey();
                    BigDecimal logicStorage = entry.getValue().values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 不在可分配队列， 减去逻辑仓
                    if (!storeIds.contains(storeId)) {
                        phyStorage = phyStorage.subtract(logicStorage);
                        continue;
                    }
                    newLogicStorageMap.put(storeId, entry.getValue());
                }
                // 不存在可分配仓 清空仓集合
                if (MapUtils.isEmpty(newLogicStorageMap)) {
                    continue;
                }
                itemResult.setLogicStorageMap(newLogicStorageMap);
                itemResult.setQty(phyStorage);
                newItemResultList.add(itemResult);
            }
            //设置新结果
            skuResult.setItemResultList(newItemResultList);
        }
    }
}