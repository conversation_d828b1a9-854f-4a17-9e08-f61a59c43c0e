package com.burgeon.r3.sg.sourcing.services;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.sourcing.common.SgSourcingConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyC2SRequest;
import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyC2SResult;
import com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult;
import com.burgeon.r3.sg.sourcing.services.easyrule.SgFindSourceSaRateFirstRule;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rules;
import org.jeasy.rules.api.RulesEngine;
import org.jeasy.rules.api.RulesEngineParameters;
import org.jeasy.rules.core.DefaultRulesEngine;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: hwy
 * @time: 2021/6/19 10:49
 */
@Component
@Slf4j
public class SgFindSourceRuleEngineStrategyService extends StrategyHandle {

    /**
     * @param request:
     * @Description: 策略规则引擎 按照锁定策略、配销仓比例策略、共享池比例策略 对店铺供货仓排序
     * @Author: hwy
     * @Date: 2021/7/1 13:43
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<com.burgeon.r3.sg.sourcing.model.result.StrategyBaseResult>
     **/
    @Override
    public ValueHolderV14<StrategyBaseResult> handleRequest(StrategyBaseRequest request) {

        ValueHolderV14<StrategyBaseResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS,
                SgConstants.MESSAGE_STATUS_SUCCESS);

        SgFindSourceStrategyC2SRequest strategyRequest = (SgFindSourceStrategyC2SRequest) request;

        log.info(LogUtil.format("SgFindSourceRuleEngineStrategyService.handleRequest C->S一阶段寻源派单 param:{}",
                SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_C2S, "sourceBillNo:{}|tid:{}"),
                request.getTraceId(), strategyRequest.getSourceBillNo(),
                strategyRequest.getTid(), JSONObject.toJSONString(request));

        //2.调用寻源规则引擎
        try {

            RulesEngineParameters parameters = new RulesEngineParameters().skipOnFirstAppliedRule(Boolean.FALSE);
            RulesEngine rulesEngine = new DefaultRulesEngine(parameters);

            //创建寻源规则
            Rules rules = new Rules();
            Facts facts = new Facts();

            // 配销仓比例策略
            rules.register(new SgFindSourceSaRateFirstRule());

            //规则因素，对应的name，要和规则里面的@Fact 一致
            facts.put(SgSourcingConstants.EASY_RULE_FACTS_REQUEST, strategyRequest);

            //执行规则
            rulesEngine.fire(rules, facts);
            SgFindSourceStrategyC2SResult strategyC2SResult = (SgFindSourceStrategyC2SResult) strategyRequest.getStrategyBaseResult();
            valueHolderV14.setData(strategyC2SResult);
        } catch (NDSException e) {
            log.error(LogUtil.format("SgFindSourceRuleEngineStrategyService.handleRequest C->S一阶段寻源派单失败 exception:{}",
                    SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_C2S_EXCEPTION),
                    request.getTraceId(), Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("C->S一阶段寻源失败：" + e.getMessage());
        } catch (Exception e) {
            log.error(LogUtil.format("SgFindSourceRuleEngineStrategyService.handleRequest C->S一阶段寻源派单 寻源规则引擎发生异常 exception:{}",
                    SgSourcingConstants.LOG_COMMON_PRINT_FIND_SOURCE_C2S_EXCEPTION),
                    request.getTraceId(), Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("C->S一阶段寻源 寻源规则引擎发生异常!");
        }

        return doNext(request, valueHolderV14);
    }

}