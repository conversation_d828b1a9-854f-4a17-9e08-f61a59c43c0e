package com.burgeon.r3.sg.sourcing.services.factory;


import com.burgeon.r3.sg.sourcing.model.request.StrategyBaseRequest;
import com.burgeon.r3.sg.sourcing.model.result.StrategyFactoryBaseResult;
import com.burgeon.r3.sg.sourcing.model.result.factory.SgFindSourceStrategyFactoryResult;
import com.burgeon.r3.sg.sourcing.services.StrategyHandle;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: hwy
 * @time: 2021/6/7 16:12
 */
@Component
@Slf4j
@Data
public class SgFindSourceStrategyS2L_FilterFactory extends StrategyFactory {

    private List<? extends StrategyFactoryBaseResult> strategiesList;

    private StrategyHandle handle;

    @Override
    public List<SgFindSourceStrategyFactoryResult> getStrategies(StrategyBaseRequest request) {

        List<SgFindSourceStrategyFactoryResult> results = new ArrayList<>();
        SgFindSourceStrategyFactoryResult result = new SgFindSourceStrategyFactoryResult();
        result.setStrategyName("强制寻源策略组");
        result.setStrategyClazz("com.burgeon.r3.sg.sourcing.services.SgFindSourceForceStrategyGroupService");
        result.setPriority(10);
        results.add(result);

        result = new SgFindSourceStrategyFactoryResult();
        result.setStrategyName("寻源预检策略组");
        result.setStrategyClazz("com.burgeon.r3.sg.sourcing.services.SgFindSourcePreCheckGroupService");
        result.setPriority(20);
        results.add(result);

        result = new SgFindSourceStrategyFactoryResult();
        result.setStrategyName("寻源规则策略组");
        result.setStrategyClazz("com.burgeon.r3.sg.sourcing.services.SgFindSourceBestStrategyGroupService");
        result.setPriority(30);
        results.add(result);

        result = new SgFindSourceStrategyFactoryResult();
        result.setStrategyName("拆单策略");
        result.setStrategyClazz("com.burgeon.r3.sg.sourcing.services.SgFindSourceSplitStrategyGroupService");
        result.setPriority(40);
        results.add(result);

        return results;

    }
}