package com.burgeon.r3.sg.migration.controller;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.migration.api.inventory.SgBPhyInventoryItemImportCmd;
import com.burgeon.r3.sg.migration.common.ExcelReaderUtils;
import com.burgeon.r3.sg.migration.model.request.PdaSaveAndSubmitInvRequest;
import com.burgeon.r3.sg.migration.services.inventory.SgBPhyInventoryAssignmentCmdImpl;
import com.burgeon.r3.sg.migration.services.inventory.SgBPhyInventoryPdaSaveAndSubmitCmdImpl;
import com.burgeon.r3.sg.migration.services.inventory.SgBPhyInventoryQueryCmdImpl;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.srv.dto.AssignDTO;
import com.jackrain.nea.srv.dto.RefColCustomDTO;
import com.jackrain.nea.srv.result.RefColCustomResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.async.AsyncTask;
import com.jackrain.nea.web.common.HttpRequestUnique;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import org.apache.dubbo.config.annotation.DubboReference;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;

/**
 * @ClassName : SgBPhyInventoryCtrl
 * @Description :
 * <AUTHOR> CD
 * @Date: 2021-07-07 17:08
 */
@RestController
@Slf4j
@Api(value = "SgBPhyInventoryCtrl", description = "盘点单接口")
public class SgBPhyInventoryCtrl {

    @Autowired
    private SgBPhyInventoryAssignmentCmdImpl sgBPhyInventoryAssignmentCmd;

    @Autowired
    private SgBPhyInventoryQueryCmdImpl sgBPhyInventoryQueryCmd;

    @Autowired
    private SgBPhyInventoryPdaSaveAndSubmitCmdImpl sgBPhyInventoryPdaSaveAndSubmitCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgBPhyInventoryItemImportCmd itemImportCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "盘点单店仓下拉带出接口")
    @RequestMapping(path = "/api/cs/sg/inventory/assignment", method = RequestMethod.POST)
    public Object assignment(@RequestBody AssignDTO assignDTO) {
        return sgBPhyInventoryAssignmentCmd.selectStoreType(assignDTO);
    }

    @ApiOperation(value = "盘点明细导入接口")
    @RequestMapping(path = "/api/cs/sg/inventory/import/item", method = RequestMethod.POST)
    @HttpRequestUnique
    public JSONObject importSgBPhyInventoryItem(HttpServletRequest request, @RequestParam(value = "param", required = false) String param,
                                                @RequestParam(value = "file", required = true) MultipartFile file) {
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        String objid = request.getParameter("mainId");
        ValueHolder vh = new ValueHolder();
        List<HashMap> list = Lists.newArrayList();
        InputStream is = null;
        try {
            ExcelReaderUtils excelReader = ApplicationContextHandle.getBean(ExcelReaderUtils.class);
            //文件原名称
            String fileName = file.getOriginalFilename();
            is = file.getInputStream();
            if (excelReader.validateExcel(fileName)) {
                vh.put("objid", objid);
                list = excelReader.readInventoryExcelContent(is, fileName);
                if (list != null && list.size() > 0) {
                    HashMap map = list.get(0);
                    if (map != null && map.size() > 0) {
                        if (!("条码".equals(map.get("PS_C_SKU_ECODE")) &&
                                "盘点数量".equals(map.get("QTY")))) {
                            throw new NDSException("模板格式不正确!");
                        }
                    }
                    list.remove(0);
                }
            } else {
                throw new NDSException("文件不是excel格式!");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new NDSException(e.getMessage());
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        ValueHolder result = new ValueHolder();
        if (list != null && list.size() == 0) {
            result.put("code", -1);
            result.put("message", "导入明细不能为空!");
            return result.toJSONObject();
        }

        HashMap hashMap = new HashMap();
        JSONArray jArray = JSONArray.fromObject(list);
        hashMap.put("SC_B_INVENTORY_IMP_ITEM", jArray);
        vh.put("fixcolumn", hashMap);

        DefaultWebEvent event = new DefaultWebEvent("binventoryimport", request, false);
        event.put("param", vh.toJSONObject());
        querySession.setEvent(event);
        SgBPhyInventoryCtrl inventoryCtrl = ApplicationContextHandle.getBean(SgBPhyInventoryCtrl.class);
        return inventoryCtrl.importInventoryItemTask(querySession);
    }

    @AsyncTask(value = "导入")
    public JSONObject importInventoryItemTask(QuerySession querySession) {
        ValueHolder result;
//        Object o = ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
//                SgBPhyInventoryItemImportCmd.class.getName(), "sg", "1.0");
        try {
            result = itemImportCmd.execute(querySession);
            return result.toJSONObject();
        } catch (Exception e) {
            result = new ValueHolder();
            result.put("code", -1);
            result.put("message", e);
            return result.toJSONObject();
        }
    }


    @ApiOperation(value = "盘点下拉带出接口")
    @RequestMapping(path = "/api/cs/sg/inventory/queryProIds", method = {RequestMethod.POST})
    public RefColCustomResult selectProEname(@RequestBody RefColCustomDTO refColCustomDTO) {
        return sgBPhyInventoryQueryCmd.inventoryIdSelectpscProIds(refColCustomDTO);
    }

    @ApiOperation(value = "主从表保存或审核接口")
    @RequestMapping(path = "/api/cs/sg/inventory/saveSubmitTest", method = {RequestMethod.POST})
    public ValueHolderV14<List<PdaSaveAndSubmitInvRequest>> updateSubmitTest(@RequestBody JSONObject param) {
        return sgBPhyInventoryPdaSaveAndSubmitCmd.pdaSaveAndSubmitInventory(param);
    }
}
