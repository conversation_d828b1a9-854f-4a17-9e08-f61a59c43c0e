package com.burgeon.r3.sg.migration.services.inventory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.model.request.SgStorageQueryRequest;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorage;
import com.burgeon.r3.sg.core.model.table.migration.inventory.SgBPhyInventory;
import com.burgeon.r3.sg.core.model.table.migration.inventory.SgBPhyInventoryItem;
import com.burgeon.r3.sg.core.model.table.migration.inventory.SgBPhyInventoryScanItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.migration.common.SgMigrationConstants;
import com.burgeon.r3.sg.migration.mapper.inventory.SgBPhyInventoryItemMapper;
import com.burgeon.r3.sg.migration.mapper.inventory.SgBPhyInventoryMapper;
import com.burgeon.r3.sg.migration.mapper.inventory.SgBPhyInventoryScanItemMapper;
import com.burgeon.r3.sg.migration.model.request.SgBPhyInventoryRequest;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.api.ScreenresultcheckCmd;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.ps.api.SkuListByProIdCmd;
import com.jackrain.nea.ps.api.request.SkuListCmdRequest;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.ps.api.table.PsCSku;
import com.jackrain.nea.psext.api.utils.JsonUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : SgBPhyInventorySaveService
 * @Description :
 * <AUTHOR> CD
 * @Date: 2021-07-01 15:22
 */
@Slf4j
@Component
public class SgBPhyInventorySaveService {

    @Autowired
    private SgBPhyInventoryMapper sgBPhyInventoryMapper;
    @Autowired
    private SgBPhyInventoryItemMapper sgBPhyInventoryItemMapper;
    @Autowired
    private SgBPhyInventoryScanItemMapper sgBPhyInventoryScanItemMapper;
    @Reference(group = "ps", version = "1.0")
    private SkuListByProIdCmd skuListByProIdCmd;

    @Transactional(rollbackFor = Exception.class)
    public ValueHolder save(QuerySession session) {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug("SgBPhyInventorySaveService.debug, param:{}", param.toJSONString());
        }
        User user = session.getUser();
        Long id = param.getLong("objid");
        if (id == -1) {
            // 新增
            valueHolder = insertMain(param, user);
        } else {
            // 修改
            //物料类型
            SgBPhyInventory sgBPhyInventory = sgBPhyInventoryMapper.selectById(id);
            AssertUtils.notNull(sgBPhyInventory, "当前记录已不存在！", user.getLocale());
            if (update(id, param, user)) {
                valueHolder.put("code", ResultCode.SUCCESS);
                valueHolder.put("message", "成功！");
            }
        }

        return valueHolder;
    }

    private ValueHolder insertMain(JSONObject param, User user) {
        ValueHolder valueHolder = new ValueHolder();

        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        JSONObject object = fixColumn.getJSONObject("SG_B_PHY_INVENTORY");

        // 盘点店仓
        Long cpCStoreId = object.getLong("CP_C_STORE_ID");
        // 盘点类型
        String inventoryType = object.getString("INVENTORY_TYPE");
        // 盘点日期
        Date billDate = object.getDate("BILL_DATE");
        //经销商
        Long cpCCustomerId = object.getLong("CP_C_CUSTOMER_ID");

        AssertUtils.notNull(cpCStoreId, Resources.getMessage("盘点店仓不允许为空！", user.getLocale()));
        AssertUtils.notBlank(inventoryType, Resources.getMessage("盘点类型不允许为空！", user.getLocale()));
        AssertUtils.notNull(billDate, Resources.getMessage("盘点日期不允许为空！", user.getLocale()));
        AssertUtils.notNull(cpCCustomerId, Resources.getMessage("经销商不能为空！", user.getLocale()));

        CpCStore store = CommonCacheValUtils.getStoreInfo(cpCStoreId);

        List<SgBPhyInventory> phyInventories = sgBPhyInventoryMapper.selectList(new LambdaQueryWrapper<SgBPhyInventory>()
                .eq(SgBPhyInventory::getCpCStoreId, cpCStoreId)
                .eq(SgBPhyInventory::getBillDate, billDate)
                .ne(SgBPhyInventory::getStatus, SgMigrationConstants.INVENTORY_BILL_STATUS_VOID)
                .eq(SgBPhyInventory::getIsactive, SgConstants.IS_ACTIVE_Y));
        AssertUtils.cannot(CollectionUtils.isNotEmpty(phyInventories), "相同门店相同日期下仅可以创建一张盘点单!");

        SgBPhyInventory sgBPhyInventory = new SgBPhyInventory();
        //抽盘商品
        String psCProIds = object.getString("PS_C_PRO_IDS");
        String proIds = null;//解析后的商品id
        if (SgMigrationConstants.PART_INVENTORY.equals(inventoryType)) {
            AssertUtils.notBlank(psCProIds, Resources.getMessage("抽盘商品不允许为空！", user.getLocale()));
            proIds = objAnalysistor(psCProIds, user);
            AssertUtils.notBlank(proIds, Resources.getMessage("抽盘商品不允许为空！", user.getLocale()));
            sgBPhyInventory.setPsCProIds(psCProIds);
        }

        Long id = ModelUtil.getSequence(SgMigrationConstants.SG_B_PHY_INVENTORY);
        sgBPhyInventory.setId(id);
        sgBPhyInventory.setCpCStoreId(cpCStoreId);
        CpCStore cpCStore = CommonCacheValUtils.getStoreInfo(cpCStoreId);
        AssertUtils.notNull(cpCStore, Resources.getMessage("在系统中未查询到当前店仓！", user.getLocale()));
        sgBPhyInventory.setCpCStoreEcode(cpCStore.getEcode());
        sgBPhyInventory.setCpCStoreEname(cpCStore.getEname());
        sgBPhyInventory.setInventoryType(inventoryType);
        sgBPhyInventory.setBillDate(billDate);
        sgBPhyInventory.setStoreType(object.getString("STORE_TYPE"));
        sgBPhyInventory.setInventoryType(object.getString("INVENTORY_TYPE"));
        sgBPhyInventory.setStatus(SgMigrationConstants.INVENTORY_BILL_STATUS_UNSUBMIT);
        sgBPhyInventory.setTotQty(BigDecimal.ZERO);
        sgBPhyInventory.setRemark(object.getString("REMARK"));
        sgBPhyInventory.setCpCCustomerId(store.getCpCCustomerId());
        sgBPhyInventory.setCpCCustomerEcode(store.getCpCCustomerEcode());
        sgBPhyInventory.setCpCCustomerEname(store.getCpCCustomerEname());
        StorageUtils.setBModelDefalutData(sgBPhyInventory, user);
        sgBPhyInventory.setOwnerename(user.getEname());
        sgBPhyInventory.setModifierename(user.getEname());
        String billNo = SgStoreUtils.getBillNo(SgMigrationConstants.SEQ_SG_B_PHY_INVENTORY,
                SgMigrationConstants.SG_B_PHY_INVENTORY.toUpperCase(), sgBPhyInventory,
                user.getLocale());
        sgBPhyInventory.setBillNo(billNo);


        //补充盘点店仓下的盘点单明细
        initInventoryItem(id, billNo, cpCStoreId, inventoryType, user, proIds);

        int insert = sgBPhyInventoryMapper.insert(sgBPhyInventory);
        if (insert > 0) {
            JSONObject data = new JSONObject();
            data.put("objid", id);
            data.put("tablename", SgMigrationConstants.SG_B_PHY_INVENTORY.toUpperCase());
            valueHolder.put("code", ResultCode.SUCCESS);
            valueHolder.put("message", "成功！");
            valueHolder.put("data", data);
        } else {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "新增盘点单失败！");
        }
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + "mainInsertResult:{}", JSONObject.toJSONString(valueHolder));
        }
        return valueHolder;
    }

    private void initInventoryItem(Long id, String billNo, Long cpCStoreId, String inventoryType, User user, String psCProIds) {
        List<String> skuEcodes = Lists.newArrayList();
        if (SgMigrationConstants.PART_INVENTORY.equals(inventoryType)) {
            //如果是抽盘，则只获取该商品下所有的sku
            List<PsCSku> resultList = getSkuListByProIds(billNo, psCProIds);
            skuEcodes = resultList.stream().map(PsCSku::getEcode).collect(Collectors.toList());
        } else {
            SgStorageQueryService sgStorageQueryService = ApplicationContextHandle.getBean(SgStorageQueryService.class);
            SgStorageQueryRequest sgStorageQueryRequest = new SgStorageQueryRequest();
            sgStorageQueryRequest.setStoreIds(Lists.newArrayList(cpCStoreId));
            ValueHolderV14<List<SgBStorage>> queryPhyStorage = sgStorageQueryService.queryStorage(sgStorageQueryRequest, user);
            if (queryPhyStorage.isOK()) {
                List<SgBStorage> resultList = queryPhyStorage.getData();
                if (CollectionUtils.isNotEmpty(resultList)) {
                    skuEcodes = resultList.stream().map(SgBStorage::getPsCSkuEcode).collect(Collectors.toList());
                }
            } else {
                AssertUtils.logAndThrow(billNo + "查询逻辑仓库存失败!" + queryPhyStorage.getMessage());
            }
        }

        //构造明细
        List<SgBPhyInventoryItem> inventoryItems = fillInventoryItems(id, billNo, skuEcodes, user);
        if (CollectionUtils.isNotEmpty(inventoryItems)) {
            List<List<SgBPhyInventoryItem>> baseModelPageList = StorageUtils.getBaseModelPageList(inventoryItems, SgConstants.SG_COMMON_MAX_INSERT_PAGE_SIZE);
            for (List<SgBPhyInventoryItem> model : baseModelPageList) {
                int i = sgBPhyInventoryItemMapper.batchInsert(model);
                if (i != model.size()) {
                    AssertUtils.logAndThrow("盘点单明细批量新增失败!");
                }
            }
        }
    }

    private List<PsCSku> getSkuListByProIds(String billNo, String psCProIds) {
        SkuListCmdRequest request = new SkuListCmdRequest();
        List<String> proIdList = Arrays.asList(psCProIds.split(","));
        List<Long> proIdLongList = proIdList.stream().map(p -> Long.parseLong(p)).collect(Collectors.toList());
        request.setProIdList(proIdLongList);
        ValueHolderV14<List<PsCSku>> holderV14 = skuListByProIdCmd.querySkuListByProInfoList(request);
        log.info("SgBPhyInventorySaveService.getSkuListByProIds 根据商品id查询所有sku信息 result:{},request:{}",
                JSONObject.toJSON(holderV14), JSONObject.toJSON(request));
        if (holderV14.isOK()) {
            List<PsCSku> resultList = holderV14.getData();
            AssertUtils.notEmpty(resultList, String.format("盘点单：%s，根据商品id查询sku信息为空!", billNo));
            return resultList;
        } else {
            AssertUtils.logAndThrow(billNo + "根据商品id查询sku信息失败!" + holderV14.getMessage());
            return null;
        }
    }

     /*
     * 填充盘点单明细
     */
    private List<SgBPhyInventoryItem> fillInventoryItems(Long id, String billNo, List<String> skuEcode, User user) {
        List<SgBPhyInventoryItem> insertList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(skuEcode)) {
            HashMap<String, PsCProSkuResult> queryResult = new HashMap<>();
            try {
                BasicPsQueryService queryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
                SkuInfoQueryRequest request = new SkuInfoQueryRequest();
                if (skuEcode.size() > 0) {
                    request.setSkuEcodeList(skuEcode);
                    queryResult = queryService.getSkuInfoByEcode(request);
                }
            } catch (Exception e) {
                AssertUtils.logAndThrow(billNo + "商品信息获取失败！" + e.getMessage(), user.getLocale());
            }

            if (CollectionUtils.isNotEmpty(skuEcode)) {
                for (String psCSkuEcode : skuEcode) {
                    PsCProSkuResult skuResult = queryResult.get(psCSkuEcode);
                    if (skuResult == null) {
                        log.error(billNo + "条码" + psCSkuEcode + "不存在!");
                        continue;
                    }
                    if (SgConstants.IS_ACTIVE_N.equalsIgnoreCase(skuResult.getIsactive())) {
                        log.error(billNo + "条码" + psCSkuEcode + "未启用!");
                        continue;
                    }
                    SgBPhyInventoryItem phyInventoryItem = new SgBPhyInventoryItem();
                    phyInventoryItem.setId(ModelUtil.getSequence(SgMigrationConstants.SG_B_PHY_INVENTORY_ITEM));
                    phyInventoryItem.setSgBPhyInventoryId(id);
                    phyInventoryItem.setQty(BigDecimal.ZERO);
                    phyInventoryItem.setPsCSpec1Id(skuResult.getPsCSpec1objId());
                    phyInventoryItem.setPsCSpec1Ecode(skuResult.getClrsEcode());
                    phyInventoryItem.setPsCSpec1Ename(skuResult.getClrsEname());
                    phyInventoryItem.setPsCSpec2Id(skuResult.getPsCSpec2objId());
                    phyInventoryItem.setPsCSpec2Ename(skuResult.getSizesEname());
                    phyInventoryItem.setPsCSpec2Ecode(skuResult.getSizesEcode());
                    phyInventoryItem.setPsCSkuId(skuResult.getId());
                    phyInventoryItem.setPsCSkuEcode(skuResult.getSkuEcode());
                    phyInventoryItem.setPsCProId(skuResult.getPsCProId());
                    phyInventoryItem.setPsCProEcode(skuResult.getPsCProEcode());
                    phyInventoryItem.setPsCProEname(skuResult.getPsCProEname());
                    phyInventoryItem.setPriceList(Optional.ofNullable(skuResult.getPricelist()).orElse(BigDecimal.ZERO));
                    StorageUtils.setBModelDefalutData(phyInventoryItem, user);
                    insertList.add(phyInventoryItem);
                }
            }
        }
        return insertList;
    }

    private Boolean update(Long objId, JSONObject param, User user) {
        JSONObject object = param.getJSONObject("fixcolumn");

        Boolean result;
        SgBPhyInventoryRequest sgBPhyInventoryRequest = JsonUtils.jsonParseClass(object, SgBPhyInventoryRequest.class);
        if (sgBPhyInventoryRequest == null) {
            throw new NDSException("数据异常！");
        }
        log.info(this.getClass().getName() + ",更新盘点单请求对象:{}", JSONObject.toJSON(sgBPhyInventoryRequest));

        SgBPhyInventory sgBPhyInventory = sgBPhyInventoryMapper.selectOne(new LambdaQueryWrapper<SgBPhyInventory>().eq(SgBPhyInventory::getId, objId));
        AssertUtils.notNull(sgBPhyInventory, "当前记录已不存在，请刷新页面！", user.getLocale());
        Integer status = sgBPhyInventory.getStatus();
        AssertUtils.cannot(status.intValue() == SgMigrationConstants.INVENTORY_BILL_STATUS_SUBMIT, Resources.getMessage("单据已审核，不允许保存！", user.getLocale()));
        AssertUtils.cannot(status.intValue() == SgMigrationConstants.INVENTORY_BILL_STATUS_PROFIT, Resources.getMessage("单据已盈亏，不允许保存！", user.getLocale()));
        AssertUtils.cannot(status.intValue() == SgMigrationConstants.INVENTORY_BILL_STATUS_VOID, Resources.getMessage("单据已作废，不允许保存！", user.getLocale()));

        // 盘点明细
        List<SgBPhyInventoryItem> sgBInventoryItems = sgBPhyInventoryRequest.getSgBPhyInventoryItemList();
        log.info(this.getClass().getName() + ",盘点明细数据:{}", JSONObject.toJSON(sgBInventoryItems));
        if (CollectionUtils.isNotEmpty(sgBInventoryItems)) {
            List<SgBPhyInventoryItem> skuIdIsNullItemList = sgBInventoryItems.stream()
                    .filter(o -> o.getPsCSkuId() == null && (o.getId() == null || o.getId() <= 0)).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(skuIdIsNullItemList)) {
                AssertUtils.logAndThrow("请输入条码！", user.getLocale());
            }
            List<SgBPhyInventoryItem> skuIdZeroItemList = sgBInventoryItems.stream()
                    .filter(o -> BigDecimal.ZERO.compareTo(o.getQty()) == 1).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(skuIdZeroItemList)) {
                AssertUtils.logAndThrow("请输入大于等于0的盘点数量！", user.getLocale());
            }

            List<Long> skuIds = sgBInventoryItems.stream().filter(o -> o.getPsCSkuId() != null).map(SgBPhyInventoryItem::getPsCSkuId).distinct().collect(Collectors.toList());
            checkUpdateItemSkuIsPro(sgBPhyInventory, skuIds, user);
        }

        //盘点货位扫描明细
        List<SgBPhyInventoryScanItem> sgBPhyInventoryScanItems = sgBPhyInventoryRequest.getSgBPhyInventoryScanItemList();
        log.info(this.getClass().getName() + ",盘点货位扫描明细数据:{}", JSONObject.toJSON(sgBPhyInventoryScanItems));
        if (CollectionUtils.isNotEmpty(sgBPhyInventoryScanItems)) {
            List<SgBPhyInventoryScanItem> skuIdIsNullScanItemList = sgBPhyInventoryScanItems.stream()
                    .filter(o -> (o.getPsCSkuId() == null || o.getDlBLocationEcode() == null)
                            && (o.getId() == null || o.getId() <= 0)).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(skuIdIsNullScanItemList)) {
                AssertUtils.logAndThrow("请输入条码和货位号！", user.getLocale());
            }
            List<SgBPhyInventoryScanItem> skuIdZeroScanItemList = sgBPhyInventoryScanItems.stream()
                    .filter(o -> BigDecimal.ZERO.compareTo(o.getQty()) > -1).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(skuIdZeroScanItemList)) {
                AssertUtils.logAndThrow("请输入大于0的扫描数量！", user.getLocale());
            }
            List<Long> skuIds = sgBPhyInventoryScanItems.stream().filter(o -> o.getPsCSkuId() != null).map(SgBPhyInventoryScanItem::getPsCSkuId).distinct().collect(Collectors.toList());
            checkUpdateItemSkuIsPro(sgBPhyInventory, skuIds, user);
        }

        updateItem(sgBInventoryItems, objId, user);

        updateScanItem(sgBPhyInventoryScanItems, objId, user);

        // 更新主表
        result = updataSgBPhyInventory(objId, user, param);
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + "updateResult:{}", result);
        }
        return result;
    }

    /**
     * 更新盘点明细
     * @param scBInventoryItems
     * @param objId
     * @param user
     */
    private void updateItem(List<SgBPhyInventoryItem> scBInventoryItems, Long objId, User user) {
        List<SgBPhyInventoryItem> resultList = Lists.newArrayList();
        List<SgBPhyInventoryItem> insertList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(scBInventoryItems)) {
            /*=========将明细id>0的数据先更新，不确定明细进一步判断=========*/
            List<Long> queryCondition = scBInventoryItems.stream().filter(o -> o.getId() != null && o.getId() > 0)
                    .map(SgBPhyInventoryItem::getId).collect(Collectors.toList());

            Map<Long, SgBPhyInventoryItem> exsistMap = CollectionUtils.isNotEmpty(queryCondition) ?
                    sgBPhyInventoryItemMapper.selectList(new LambdaQueryWrapper<SgBPhyInventoryItem>()
                            .in(SgBPhyInventoryItem::getId, queryCondition))
                            .stream().collect(Collectors.toMap(SgBPhyInventoryItem::getId, o -> o)) : Maps.newHashMap();

            for (SgBPhyInventoryItem inventoryItem : scBInventoryItems) {
                SgBPhyInventoryItem existItem = exsistMap.get(inventoryItem.getId());
                if (existItem != null) {
                    // 在存在的明细上修改数量则覆盖
                    BigDecimal qty = Optional.ofNullable(inventoryItem.getQty()).orElse(BigDecimal.ZERO);
                    SgBPhyInventoryItem update = new SgBPhyInventoryItem();
                    update.setId(existItem.getId());
                    update.setQty(qty);
                    StorageUtils.setBModelDefalutDataByUpdate(update, user);
                    update.setModifierename(user.getEname());
                    sgBPhyInventoryItemMapper.updateById(update);
                } else {
                    //明细id==null 或 id<0的数据
                    resultList.add(inventoryItem);
                }
            }
            /*=========将明细id>0的数据先更新，不确定明细进一步判断=========*/


            /*=========处理明细id=null 或id<0的数据=========*/
            if (CollectionUtils.isNotEmpty(resultList)) {
                List<Long> skuIds = resultList.stream().filter(r -> null != r.getPsCSkuId())
                        .map(SgBPhyInventoryItem::getPsCSkuId).distinct().collect(Collectors.toList());

                List<SgBPhyInventoryItem> items = sgBPhyInventoryItemMapper.selectList(new LambdaQueryWrapper<SgBPhyInventoryItem>()
                        .eq(SgBPhyInventoryItem::getSgBPhyInventoryId, objId)
                        .and(o -> o.in(CollectionUtils.isNotEmpty(skuIds), SgBPhyInventoryItem::getPsCSkuId, skuIds)));

                Map<Long, SgBPhyInventoryItem> skusMap = Maps.newHashMap();

                if (CollectionUtils.isNotEmpty(items)) {
                    for (SgBPhyInventoryItem phyInventoryItem : items) {
                        if (null != phyInventoryItem.getPsCSkuId()) {
                            skusMap.put(phyInventoryItem.getPsCSkuId(), phyInventoryItem);
                        }
                    }
                }

                for (SgBPhyInventoryItem item : resultList) {
                    if (item.getPsCSkuId() != null) {
                        // 判断散码map
                        SgBPhyInventoryItem phyItem = skusMap.get(item.getPsCSkuId());
                        if (phyItem != null) {//如果sku在明细中存在，则累加数量
                            SgBPhyInventoryItem update = new SgBPhyInventoryItem();
                            StorageUtils.setBModelDefalutDataByUpdate(update, user);
                            if (log.isDebugEnabled()) {
                                log.debug(this.getClass().getName() + "updateItem:{}", item);
                            }
                            update.setQty(phyItem.getQty().add(item.getQty()));
                            update.setId(phyItem.getId());
                            update.setModifierename(user.getEname());
                            sgBPhyInventoryItemMapper.updateById(update);
                        } else {
                            item.setId(ModelUtil.getSequence(SgMigrationConstants.SG_B_PHY_INVENTORY_ITEM));
                            item.setSgBPhyInventoryId(objId);
                            StorageUtils.setBModelDefalutData(item, user);
                            item.setOwnerename(user.getEname());
                            item.setModifierename(user.getEname());
                            insertList.add(item);
                        }
                    }
                }
                /*=========处理明细id=null 或id<0的数据=========*/
                // 新增明细时 信息补充
                if (CollectionUtils.isNotEmpty(insertList)) {
                    List<SgBPhyInventoryItem> resultInsert = addItemBoxInfo(insertList, user);
                    List<List<SgBPhyInventoryItem>> baseModelPageList = StorageUtils.getBaseModelPageList(resultInsert, SgConstants.SG_COMMON_MAX_INSERT_PAGE_SIZE);
                    for (List<SgBPhyInventoryItem> model : baseModelPageList) {
                        int i = sgBPhyInventoryItemMapper.batchInsert(model);
                        if (i != model.size()) {
                            AssertUtils.logAndThrow("盘点单明细批量新增失败!");
                        }
                    }
                }

            }
        }
    }

    /**
     * 更新盘点扫描明细
     * @param scBInventoryScanItems
     * @param objId
     * @param user
     */
    private void updateScanItem(List<SgBPhyInventoryScanItem> scBInventoryScanItems, Long objId, User user) {
        List<SgBPhyInventoryScanItem> resultList = Lists.newArrayList();
        List<SgBPhyInventoryScanItem> insertList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(scBInventoryScanItems)) {
            /*=========将明细id>0的数据先更新，不确定明细进一步判断=========*/
            List<Long> queryCondition = scBInventoryScanItems.stream().filter(o -> o.getId() != null && o.getId() > 0)
                    .map(SgBPhyInventoryScanItem::getId).collect(Collectors.toList());

            Map<Long, SgBPhyInventoryScanItem> exsistMap = CollectionUtils.isNotEmpty(queryCondition) ?
                    sgBPhyInventoryScanItemMapper.selectList(new LambdaQueryWrapper<SgBPhyInventoryScanItem>()
                            .in(SgBPhyInventoryScanItem::getId, queryCondition))
                            .stream().collect(Collectors.toMap(SgBPhyInventoryScanItem::getId, o -> o)) : Maps.newHashMap();

            for (SgBPhyInventoryScanItem inventoryScaItem : scBInventoryScanItems) {
                SgBPhyInventoryScanItem existItem = exsistMap.get(inventoryScaItem.getId());
                if (existItem != null) {
                    // 在存在的明细上修改数量则覆盖
                    BigDecimal qty = Optional.ofNullable(inventoryScaItem.getQty()).orElse(BigDecimal.ZERO);
                    SgBPhyInventoryScanItem update = new SgBPhyInventoryScanItem();
                    update.setId(existItem.getId());
                    update.setQty(qty);
                    StorageUtils.setBModelDefalutDataByUpdate(update, user);
                    update.setModifierename(user.getEname());
                    update.setIsUpdate(SgMigrationConstants.INVENTORY_SCAN_UPDATE_NO);
                    sgBPhyInventoryScanItemMapper.updateById(update);

                    scamItemIsUpdate(existItem.getId(), existItem.getPsCSkuId(), user);
                } else {
                    //明细id==null 或 id<0的数据
                    resultList.add(inventoryScaItem);
                }
            }
            /*=========将明细id>0的数据先更新，不确定明细进一步判断=========*/


            /*=========处理明细id=null 或id<0的数据=========*/
            if (CollectionUtils.isNotEmpty(resultList)) {
                for (SgBPhyInventoryScanItem inventoryScanItem : resultList) {
                    List<SgBPhyInventoryScanItem> items = sgBPhyInventoryScanItemMapper.selectList(new LambdaQueryWrapper<SgBPhyInventoryScanItem>()
                            .eq(SgBPhyInventoryScanItem::getSgBPhyInventoryId, objId)
                            .eq(SgBPhyInventoryScanItem::getPsCSkuId, inventoryScanItem.getPsCSkuId())
                            .eq(SgBPhyInventoryScanItem::getDlBLocationEcode, inventoryScanItem.getDlBLocationEcode()));
                    if (CollectionUtils.isNotEmpty(items)) {//如果sku在明细中存在，则累加数量
                        //如果根据sku和货位号能查询到数据，则更新第一条数据，因为数据库中根据货位号和sku同时存在只会出现一条
                        SgBPhyInventoryScanItem item = items.get(0);
                        SgBPhyInventoryScanItem update = new SgBPhyInventoryScanItem();
                        StorageUtils.setBModelDefalutDataByUpdate(update, user);
                        if (log.isDebugEnabled()) {
                            log.debug(this.getClass().getName() + "updateScanItem:{}", inventoryScanItem);
                        }
                        update.setQty(item.getQty().add(inventoryScanItem.getQty()));
                        update.setId(item.getId());
                        update.setModifierename(user.getEname());
                        update.setIsUpdate(SgMigrationConstants.INVENTORY_SCAN_UPDATE_NO);
                        sgBPhyInventoryScanItemMapper.updateById(update);

                        scamItemIsUpdate(item.getId(), inventoryScanItem.getPsCSkuId(), user);
                    } else {
                        inventoryScanItem.setId(ModelUtil.getSequence(SgMigrationConstants.SG_B_PHY_INVENTORY_ITEM));
                        inventoryScanItem.setSgBPhyInventoryId(objId);
                        StorageUtils.setBModelDefalutData(inventoryScanItem, user);
                        inventoryScanItem.setOwnerename(user.getEname());
                        inventoryScanItem.setModifierename(user.getEname());
                        inventoryScanItem.setIsUpdate(SgMigrationConstants.INVENTORY_SCAN_UPDATE_NO);
                        insertList.add(inventoryScanItem);

                        scamItemIsUpdate(inventoryScanItem.getId(), inventoryScanItem.getPsCSkuId(), user);
                    }
                }

                /*=========处理明细id=null 或id<0的数据=========*/
                // 新增明细时 信息补充
                if (CollectionUtils.isNotEmpty(insertList)) {
                    List<SgBPhyInventoryScanItem> resultInsert = addScanItemBoxInfo(insertList, user);
                    List<List<SgBPhyInventoryScanItem>> baseModelPageList = StorageUtils.getBaseModelPageList(resultInsert, SgConstants.SG_COMMON_MAX_INSERT_PAGE_SIZE);
                    for (List<SgBPhyInventoryScanItem> model : baseModelPageList) {
                        int i = sgBPhyInventoryScanItemMapper.batchInsert(model);
                        if (i != model.size()) {
                            AssertUtils.logAndThrow("盘点货位扫描明细新增失败!");
                        }
                    }
                }

            }
        }
    }

    /*
     * 补充新增明细信息
     *
     * @param itemList
     * @param user
     * @return
     */
    private List<SgBPhyInventoryItem> addItemBoxInfo(List<SgBPhyInventoryItem> itemList, User user) {
        BasicPsQueryService queryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
        List<SgBPhyInventoryItem> insertList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(itemList)) {
            List<Long> skuIds = itemList.stream()
                    .filter(o -> null != o.getPsCSkuId())
                    .map(SgBPhyInventoryItem::getPsCSkuId).collect(Collectors.toList());

            HashMap<Long, PsCProSkuResult> queryResult = new HashMap<>();
            if (CollectionUtils.isNotEmpty(skuIds)) {
                try {
                    if (skuIds.size() > 0) {
                        SkuInfoQueryRequest request = new SkuInfoQueryRequest();
                        request.setSkuIdList(skuIds);
                        queryResult = queryService.getSkuInfo(request);
                    }
                } catch (Exception e) {
                    AssertUtils.logAndThrow("商品信息获取失败！" + e.getMessage(), user.getLocale());
                }
                for (SgBPhyInventoryItem phyInventoryItem : itemList) {
                    Long psCSkuId = phyInventoryItem.getPsCSkuId();
                    if (null != psCSkuId) {
                        PsCProSkuResult skuResult = queryResult.get(psCSkuId);
                        AssertUtils.notNull(skuResult, "条码:" + psCSkuId + "不存在", user.getLocale());
                        AssertUtils.cannot("N".equalsIgnoreCase(skuResult.getIsactive()), "条码:" + psCSkuId + "未启用", user.getLocale());
                        phyInventoryItem.setPsCSkuId(psCSkuId);
                        phyInventoryItem.setPsCSkuEcode(skuResult.getSkuEcode());
                        phyInventoryItem.setPsCSpec1Id(skuResult.getPsCSpec1objId());
                        phyInventoryItem.setPsCSpec1Ecode(skuResult.getClrsEcode());
                        phyInventoryItem.setPsCSpec1Ename(skuResult.getClrsEname());
                        phyInventoryItem.setPsCSpec2Id(skuResult.getPsCSpec2objId());
                        phyInventoryItem.setPsCSpec2Ename(skuResult.getSizesEname());
                        phyInventoryItem.setPsCSpec2Ecode(skuResult.getSizesEcode());
                        phyInventoryItem.setPsCProId(skuResult.getPsCProId());
                        phyInventoryItem.setPsCProEcode(skuResult.getPsCProEcode());
                        phyInventoryItem.setPsCProEname(skuResult.getPsCProEname());
                        phyInventoryItem.setPriceList(Optional.ofNullable(skuResult.getPricelist()).orElse(BigDecimal.ZERO));
                        insertList.add(phyInventoryItem);
                    }
                }
            }
        }
        return insertList;
    }

    /*
     * 补充新增货位扫描明细信息
     *
     * @param itemList
     * @param user
     * @return
     */
    private List<SgBPhyInventoryScanItem> addScanItemBoxInfo(List<SgBPhyInventoryScanItem> itemScanList, User user) {
        BasicPsQueryService queryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
        List<SgBPhyInventoryScanItem> insertList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(itemScanList)) {
            List<Long> skuIds = itemScanList.stream()
                    .filter(o -> null != o.getPsCSkuId())
                    .map(SgBPhyInventoryScanItem::getPsCSkuId).collect(Collectors.toList());

            HashMap<Long, PsCProSkuResult> queryResult = new HashMap<>();
            if (CollectionUtils.isNotEmpty(skuIds)) {
                try {
                    if (skuIds.size() > 0) {
                        SkuInfoQueryRequest request = new SkuInfoQueryRequest();
                        request.setSkuIdList(skuIds);
                        queryResult = queryService.getSkuInfo(request);
                    }
                } catch (Exception e) {
                    AssertUtils.logAndThrow("商品信息获取失败！" + e.getMessage(), user.getLocale());
                }
                for (SgBPhyInventoryScanItem phyInventoryScanItem : itemScanList) {
                    Long psCSkuId = phyInventoryScanItem.getPsCSkuId();
                    if (null != psCSkuId) {
                        PsCProSkuResult skuResult = queryResult.get(psCSkuId);
                        AssertUtils.notNull(skuResult, "条码:" + psCSkuId + "不存在", user.getLocale());
                        AssertUtils.cannot("N".equalsIgnoreCase(skuResult.getIsactive()), "条码:" + psCSkuId + "未启用", user.getLocale());
                        phyInventoryScanItem.setPsCSkuId(psCSkuId);
                        phyInventoryScanItem.setPsCSkuEcode(skuResult.getSkuEcode());
                        phyInventoryScanItem.setPsCSpec1Id(skuResult.getPsCSpec1objId());
                        phyInventoryScanItem.setPsCSpec1Ecode(skuResult.getClrsEcode());
                        phyInventoryScanItem.setPsCSpec1Ename(skuResult.getClrsEname());
                        phyInventoryScanItem.setPsCSpec2Id(skuResult.getPsCSpec2objId());
                        phyInventoryScanItem.setPsCSpec2Ename(skuResult.getSizesEname());
                        phyInventoryScanItem.setPsCSpec2Ecode(skuResult.getSizesEcode());
                        phyInventoryScanItem.setPsCProId(skuResult.getPsCProId());
                        phyInventoryScanItem.setPsCProEcode(skuResult.getPsCProEcode());
                        phyInventoryScanItem.setPsCProEname(skuResult.getPsCProEname());
                        phyInventoryScanItem.setPriceList(Optional.ofNullable(skuResult.getPricelist()).orElse(BigDecimal.ZERO));
                        insertList.add(phyInventoryScanItem);
                    }
                }
            }
        }
        return insertList;
    }

    /**
     * 更新与当前条码相同的数据的是否更新字段为否
     * @param scanItemId
     * @param skuId
     * @param user
     */
    public void scamItemIsUpdate(Long scanItemId, Long skuId, User user) {
        SgBPhyInventoryScanItem itemIsUpdate = new SgBPhyInventoryScanItem();//当该条码更新时，其余所有相同条码的是否更新字段全部改为否
        itemIsUpdate.setIsUpdate(SgMigrationConstants.INVENTORY_SCAN_UPDATE_NO);
        StorageUtils.setBModelDefalutDataByUpdate(itemIsUpdate, user);
        sgBPhyInventoryScanItemMapper.update(itemIsUpdate, new LambdaQueryWrapper<SgBPhyInventoryScanItem>()
                .ne(SgBPhyInventoryScanItem::getId, scanItemId)
                .eq(SgBPhyInventoryScanItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgBPhyInventoryScanItem::getPsCSkuId, skuId));
    }

    /**
     * 更新主表
     *
     * @param objid
     * @param user
     * @return
     */
    private Boolean updataSgBPhyInventory(Long objid, User user, JSONObject param) {
        // 更新主表数据
        List<SgBPhyInventoryItem> items = sgBPhyInventoryItemMapper.selectList(new LambdaQueryWrapper<SgBPhyInventoryItem>()
                .select(SgBPhyInventoryItem::getQty)
                .eq(SgBPhyInventoryItem::getSgBPhyInventoryId, objid));
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + "updataScbInventory:{}", JSONObject.toJSONString(items));
        }
        // 计算盘点数量
        BigDecimal countQty = items.stream()
                .map(SgBPhyInventoryItem::getQty).reduce(BigDecimal.ZERO, (a, b) -> a.add(b));
        SgBPhyInventory sgBPhyInventory = new SgBPhyInventory();
        sgBPhyInventory.setId(objid);
        sgBPhyInventory.setTotQty(countQty);
        sgBPhyInventory.setModifierename(user.getEname());
        StorageUtils.setBModelDefalutDataByUpdate(sgBPhyInventory, user);

        JSONObject object = param.getJSONObject("aftervalue");
        if (object != null && object.containsKey("SG_B_PHY_INVENTORY")) {
            JSONObject inventoryJson = object.getJSONObject("SG_B_PHY_INVENTORY");
            if (inventoryJson != null && inventoryJson.containsKey("REMARK")) {
                sgBPhyInventory.setRemark(inventoryJson.getString("REMARK"));
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("SgBPhyInventorySaveService.updataParam:{}", JSONObject.toJSONString(sgBPhyInventory));
        }
        int result = sgBPhyInventoryMapper.updateById(sgBPhyInventory);
        return result > 0 ? true : false;
    }

    /**
     * 解析商品
     *
     * @param str  待解析字段
     * @param user 促销活动创建人id
     */
    public String objAnalysistor(String str, User user) throws NDSException {
        try {
            if (StringUtils.isEmpty(str) || !str.contains("value")) {
                return str;
            }
            QuerySessionImpl session = new QuerySessionImpl(user);
            DefaultWebEvent event = new DefaultWebEvent("promotionrefresh", new HashMap());
            event.put("param", JSON.parseObject(str).getJSONObject("value"));
            session.setEvent(event);
            //解析Conditions
            Object checkObject = ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                    "com.jackrain.nea.cp.api.ScreenresultcheckCmd", "cp", "1.0");
            ValueHolder holder = ((ScreenresultcheckCmd) checkObject).execute(session);
            if (holder != null && ResultCode.SUCCESS == holder.toJSONObject().getInteger("code")) {
                JSONArray idsArr = holder.toJSONObject().getJSONArray("ids");
                return StringUtils.join(idsArr, ",");
            }
        } catch (NDSException e) {
            throw new NDSException("解析店仓id失败!" + e.getMessage());
        }
        return null;
    }

    /**
     * 如果是抽盘，则更新条码时检查条码是否属于该商品
     * @param sgBPhyInventory
     * @param skuIdList 传入待更新的条码
     */
    public void checkUpdateItemSkuIsPro(SgBPhyInventory sgBPhyInventory, List<Long> skuIdList, User user) {
        if (SgMigrationConstants.PART_INVENTORY.equals(sgBPhyInventory.getInventoryType())
                && CollectionUtils.isNotEmpty(skuIdList)) {
            String proIds = objAnalysistor(sgBPhyInventory.getPsCProIds(), user);
            List<PsCSku> skuList = getSkuListByProIds(sgBPhyInventory.getBillNo(), proIds);//获取抽盘商品下所有的sku集合
            List<Long> skuIds = skuList.stream().map(PsCSku::getId).distinct().collect(Collectors.toList());//所有商品sku的集合
            //获取抽盘商品不包含的sku集合
            List<Long> notContainsSkuIds = skuIdList.stream().filter(o -> !skuIds.contains(o)).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notContainsSkuIds)) {
                AssertUtils.logAndThrow("输入的sku在抽盘商品中不存在!");
            }
        }
    }
}
