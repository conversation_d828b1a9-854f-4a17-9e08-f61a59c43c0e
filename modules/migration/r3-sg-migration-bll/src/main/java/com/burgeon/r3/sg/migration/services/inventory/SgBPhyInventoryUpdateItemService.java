package com.burgeon.r3.sg.migration.services.inventory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.migration.inventory.SgBPhyInventory;
import com.burgeon.r3.sg.core.model.table.migration.inventory.SgBPhyInventoryItem;
import com.burgeon.r3.sg.core.model.table.migration.inventory.SgBPhyInventoryScanItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.BeanCommonUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.migration.common.SgMigrationConstants;
import com.burgeon.r3.sg.migration.mapper.inventory.SgBPhyInventoryItemMapper;
import com.burgeon.r3.sg.migration.mapper.inventory.SgBPhyInventoryMapper;
import com.burgeon.r3.sg.migration.mapper.inventory.SgBPhyInventoryScanItemMapper;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName : SgBPhyInventoryUpdateItemService
 * @Description : 更新盘点明细service
 * <AUTHOR> CD
 * @Date: 2021-07-02 17:31
 */
@Slf4j
@Component
public class SgBPhyInventoryUpdateItemService {

    @Autowired
    private SgBPhyInventoryMapper sgBPhyInventoryMapper;
    @Autowired
    private SgBPhyInventoryItemMapper sgBPhyInventoryItemMapper;
    @Autowired
    private SgBPhyInventoryScanItemMapper sgBPhyInventoryScanItemMapper;

    public ValueHolder updateItem(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        SgBPhyInventoryUpdateItemService service = ApplicationContextHandle.getBean(SgBPhyInventoryUpdateItemService.class);
        return R3ParamUtils.convertV14WithResult(service.update(request));
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> update(SgR3BaseRequest request) {
        Long id = request.getObjId();
        User user = request.getLoginUser();
        AssertUtils.notNull(id, Resources.getMessage("请选择有效数据！", user.getLocale()));

        SgBPhyInventory inventory = sgBPhyInventoryMapper.selectById(id);
        AssertUtils.notNull(inventory, Resources.getMessage("当前记录已不存在！", user.getLocale()));
        Integer status = inventory.getStatus();
        AssertUtils.cannot(status.intValue() == SgMigrationConstants.INVENTORY_BILL_STATUS_SUBMIT, Resources.getMessage("单据已审核，不允许更新！", user.getLocale()));
        AssertUtils.cannot(status.intValue() == SgMigrationConstants.INVENTORY_BILL_STATUS_PROFIT, Resources.getMessage("单据已盈亏，不允许更新！", user.getLocale()));
        AssertUtils.cannot(status.intValue() == SgMigrationConstants.INVENTORY_BILL_STATUS_VOID, Resources.getMessage("单据已作废，不允许更新！", user.getLocale()));

        List<SgBPhyInventoryItem> itemList = sgBPhyInventoryItemMapper.selectList(new LambdaQueryWrapper<SgBPhyInventoryItem>()
                .eq(SgBPhyInventoryItem::getIsactive, SgConstants.IS_ACTIVE_Y).eq(SgBPhyInventoryItem::getSgBPhyInventoryId, id));
//        AssertUtils.notEmpty(itemList, Resources.getMessage("当前记录无明细，不允许更新！", user.getLocale()));
        if (CollectionUtils.isEmpty(itemList)){
            itemList = new ArrayList<>();
        }
        Map<Long, List<SgBPhyInventoryItem>> itemListMap = itemList.stream().collect(Collectors.groupingBy(SgBPhyInventoryItem::getPsCSkuId));
        if (MapUtils.isEmpty(itemListMap)){
            itemListMap = new HashMap<>();
        }
        List<SgBPhyInventoryScanItem> scanItemList = sgBPhyInventoryScanItemMapper.selectList(new LambdaQueryWrapper<SgBPhyInventoryScanItem>()
                .eq(SgBPhyInventoryScanItem::getIsactive, SgConstants.IS_ACTIVE_Y).eq(SgBPhyInventoryScanItem::getSgBPhyInventoryId, id)
                .eq(SgBPhyInventoryScanItem::getIsUpdate, SgMigrationConstants.INVENTORY_SCAN_UPDATE_NO));
        AssertUtils.notEmpty(scanItemList, Resources.getMessage("当前记录无扫描明细，不允许更新！", user.getLocale()));

        //根据sku对扫描明细进行分组
        Map<Long, List<SgBPhyInventoryScanItem>> scanItemListMap = scanItemList.stream().collect(Collectors.groupingBy(SgBPhyInventoryScanItem::getPsCSkuId));
        log.info("SgBPhyInventoryUpdateItemService.updateItem,根据sku对扫描明细进行分组结果:{}", JSONObject.toJSON(scanItemListMap));
        List<SgBPhyInventoryItem> insertItemList = Lists.newArrayList();
        BigDecimal addQty = BigDecimal.ZERO;
        for (Long scanSku : scanItemListMap.keySet()) {
            List<SgBPhyInventoryScanItem> scanItems = scanItemListMap.get(scanSku);
            BigDecimal scanQtySum = scanItems.stream().map(SgBPhyInventoryScanItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);

            List<SgBPhyInventoryItem> inventoryItems = itemListMap.get(scanSku);
            if (CollectionUtils.isNotEmpty(inventoryItems)) {//如果扫描明细的sku在盘点明细中存在，则更新盘点明细的盘点数量，否则新增一条盘点明细
                SgBPhyInventoryItem updateItem = new SgBPhyInventoryItem();
                updateItem.setId(inventoryItems.get(0).getId());
                updateItem.setQty(scanQtySum);
                addQty = scanQtySum.subtract(inventoryItems.get(0).getQty());
                StorageUtils.setBModelDefalutDataByUpdate(updateItem, user);
                updateItem.setModifierename(user.getEname());
                sgBPhyInventoryItemMapper.updateById(updateItem);
            } else {
                SgBPhyInventoryItem insertItem = BeanCommonUtils.copyProperties(scanItems.get(0), SgBPhyInventoryItem.class);
                insertItem.setId(ModelUtil.getSequence(SgMigrationConstants.SG_B_PHY_INVENTORY_ITEM));
                StorageUtils.setBModelDefalutData(insertItem, user);
                addQty = addQty.add(scanQtySum);
                insertItemList.add(insertItem);
            }

            SgBPhyInventoryScanItem updateScanItem = new SgBPhyInventoryScanItem();
            updateScanItem.setIsUpdate(SgMigrationConstants.INVENTORY_SCAN_UPDATE_YES);
            StorageUtils.setBModelDefalutDataByUpdate(updateScanItem, user);
            sgBPhyInventoryScanItemMapper.update(updateScanItem, new LambdaQueryWrapper<SgBPhyInventoryScanItem>()
                    .in(SgBPhyInventoryScanItem::getId, scanItems.stream().map(SgBPhyInventoryScanItem::getId).distinct().collect(Collectors.toList())));
        }
        //更新调整总数量
        SgBPhyInventory uadetaInventory = new SgBPhyInventory();
        BigDecimal totQty =  addQty.add(inventory.getTotQty());
        uadetaInventory.setTotQty(totQty);
        uadetaInventory.setId(inventory.getId());
        sgBPhyInventoryMapper.updateById(uadetaInventory);

        if (CollectionUtils.isNotEmpty(insertItemList)) {
            sgBPhyInventoryItemMapper.batchInsert(insertItemList);
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

    }

}
