package com.burgeon.r3.sg.stocksync.model.request;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/4/25 15:45
 */
@Data
public class SgChannelStorageOmsBufferMessageRequest implements Serializable {

    private static final long serialVersionUID = 2471696947180731780L;
    
    private Date changeTime;
    @Deprecated
    private String billNo;
    @Deprecated
    private Long cpCshopId;

    private List<SgChannelStorageOmsBufferMessageItemRequest> itemList;
}
