package com.burgeon.r3.sg.stocksync.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.burgeon.r3.sg.stocksync.api.SgChannelProductCalcAndSyncCmd;
import com.burgeon.r3.sg.stocksync.common.OmsResultCode;
import com.burgeon.r3.sg.stocksync.common.enums.ChannelStoragePoolTypeEnum;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageOmsManualSynchRequest;
import com.google.common.collect.Maps;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lin yu
 * @since 2019/8/28
 * create at : 2019/8/28 13:57
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgChannelProductCalcAndSyncCmdImpl extends CommandAdapter implements SgChannelProductCalcAndSyncCmd {

    @Autowired
    private SgChannelStorageOmsService sgChannelStorageOmsService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {

        ValueHolder holderV14 = new ValueHolder();
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);

        if (log.isDebugEnabled()) {
            log.debug("Start SgChannelProductCalcAndSyncCmdImpl.execute Receive Params:{};", JSONObject.toJSONString(param));
        }

        SgChannelStorageOmsManualSynchRequest manualSynchRequest = new SgChannelStorageOmsManualSynchRequest();
        if (CollectionUtils.isNotEmpty(param.getJSONArray("ids"))) {
            List<Integer> ids = param.getJSONArray("ids").stream().map(id -> {
                return Integer.parseInt(id.toString());
            }).collect(Collectors.toList());
            manualSynchRequest.setIdList(ids);
        }
        if (Objects.nonNull(param.getInteger("operate"))) {
            Integer operate = param.getInteger("operate");
            manualSynchRequest.setOperate(operate);
        }
        if (CollectionUtils.isNotEmpty(param.getJSONArray("cpCShopIdList"))) {
            List<Integer> shopIdList = param.getJSONArray("cpCShopIdList").stream().map(id -> Integer.parseInt(id.toString())).collect(Collectors.toList());
            manualSynchRequest.setCpCShopIdList(shopIdList);
        }
        if (StringUtils.isNotEmpty(param.getString("sourceno"))) {
            manualSynchRequest.setSourceno(param.getString("sourceno"));
        }
        if (StringUtils.isNotEmpty(param.getString("batchno"))) {
            manualSynchRequest.setBatchno(param.getString("batchno"));
        }
        Integer poolType = Optional.ofNullable(param.getInteger("poolType")).orElse(ChannelStoragePoolTypeEnum.SYNC_POOL_COMMON.getValue());
        manualSynchRequest.setPoolType(poolType);
        manualSynchRequest.setUser(session.getUser());
        ValueHolderV14 holder = sgChannelStorageOmsService.manualCalcAndSyncChannelProduct(manualSynchRequest);
        HashMap resultMap = Maps.newHashMap();
        resultMap.put(OmsResultCode.KEY_CODE, holder.getCode());
        resultMap.put(OmsResultCode.KEY_MESSAGE, holder.getMessage());
        if (OmsResultCode.isFail(holder)) {
            resultMap.put(OmsResultCode.KEY_DATA, false);
        } else {
            resultMap.put(OmsResultCode.KEY_DATA, true);
        }
        holderV14.setData(resultMap);
        return holderV14;
    }
}
