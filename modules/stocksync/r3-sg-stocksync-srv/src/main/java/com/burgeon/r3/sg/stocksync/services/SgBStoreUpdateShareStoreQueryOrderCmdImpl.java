package com.burgeon.r3.sg.stocksync.services;


import com.burgeon.r3.sg.stocksync.api.SgBStoreUpdateShareStoreQueryOrderCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Auther: chenhao
 * @Date: 2022-01-13 10:22
 * @Description:
 */

@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgBStoreUpdateShareStoreQueryOrderCmdImpl implements SgBStoreUpdateShareStoreQueryOrderCmd {

    @Override
    public ValueHolderV14<Map<String, Integer>> queryOrderByBillNo(List<String> billNoList) {
        SgChannelStorageOmsCalcService bean = ApplicationContextHandle.getBean(SgChannelStorageOmsCalcService.class);
        return bean.queryOrderByBillNo(billNoList);
    }
}
