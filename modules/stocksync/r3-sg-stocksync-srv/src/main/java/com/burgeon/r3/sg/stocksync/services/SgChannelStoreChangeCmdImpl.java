package com.burgeon.r3.sg.stocksync.services;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.stocksync.api.SgChannelStoreChangeCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStoreChangeRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgChannelStoreChangeCmdImpl implements SgChannelStoreChangeCmd {

    @Autowired
    SgChannelStoreChangeService service;

    @Override
    public ValueHolderV14<Integer> saveSbGChannelStore(SgChannelStoreChangeRequest request) {
        return service.saveSbGChannelStore(request);
    }


    @Override
    public ValueHolderV14<Integer> deleteSbGChannelStore(SgChannelStoreChangeRequest request) {
        return service.deleteSbGChannelStore(request);
    }


    @Override
    public ValueHolderV14<Integer> changeCpCStoreTitleById(SgChannelStoreChangeRequest request) {
        return service.changeCpCStoreTitleById(request);
    }

    /**
     * 批量保存和更新店铺逻辑仓关系
     *
     * @param requestList 请求集合
     * @param user        操作用户
     * @return 保存和更新结果对象
     */
    @Override
    public ValueHolderV14<String> saveSbGChannelStoreList(List<SgChannelStoreChangeRequest> requestList, User user) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "保存和更新成功！");
        try {
            if (log.isDebugEnabled()) {
                // 批量保存店铺逻辑仓关系
                log.debug("--Batch save ShopStoreRelation saveSbGChannelStoreList：{}",
                        JSONObject.toJSONString(requestList));
            }
            // 明细数据校验
            int i = 0;
            for (SgChannelStoreChangeRequest req : requestList) {
                i++;
                AssertUtils.notNull(req.getCpCShopId(), "明细第" + i + "条,店铺ID为空！");
                AssertUtils.notNull(req.getCpCChannelId(), "明细第" + i + "条,渠道仓ID为空！");
                AssertUtils.notNull(req.getCpCStoreId(), "明细第" + i + "条,逻辑仓ID为空！");
            }
            AssertUtils.notEmpty(requestList, "批量保存店铺逻辑仓请求requestList为空！");
            boolean isOk = service.saveSbGChannelStoreList(requestList, user);
            if (log.isDebugEnabled()) {
                log.debug("--Batch save ShopStoreRelation，count：{}，result：{}", requestList.size(), isOk);
            }
            AssertUtils.isTrue(isOk, "保存和更新失败！");
        } catch (Exception e) {
            log.error("保存店铺逻辑仓关系异常 error:{}", Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("保存店铺逻辑仓关系异常：" + e.getMessage());
        }
        return v14;
    }
}
