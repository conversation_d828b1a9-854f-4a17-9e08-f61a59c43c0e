package com.burgeon.r3.sg.stocksync.services;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.inf.services.oms.product.SgChannelProductSaveService;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductSaveRequest;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.stocksync.api.SgChannelStorageConsumerCmd;
import com.burgeon.r3.sg.stocksync.common.enums.ChannelStoragePoolTypeEnum;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageOmsCalcRequest;
import com.burgeon.r3.sg.stocksync.model.result.SgChannelStorageOmsCalcResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Author: qinjunlong
 * @Description:test impl class
 * @Date: Create in 15:28 2019/4/10
 */
@Slf4j
@Component
//@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgChannelStorgeConsumerCmdImpl implements SgChannelStorageConsumerCmd {

    @Autowired
    private ChannelProductSyncCompensateService makeUpService;

    @Autowired
    private SgChannelStorageOmsCalcService sgChannelStorageOmsCalcService;

    @Autowired
    private SgChannelStorageOmsBufferService bufferService;

    //TODO：20210710主要问题的点需要调用即将提出的SgChannelProductSaveService
    @Autowired
    private SgChannelProductSaveService productSaveService;

    @Autowired
    private PropertiesConf propertiesConf;

    @Override
    public ValueHolderV14 channelProductSync() {
        return makeUpService.compensate();
    }

    @Override
    public ValueHolderV14 calcChannelStorageConsumer(List<SgChannelStorageOmsCalcRequest> list) {
        ValueHolderV14 holder = new ValueHolderV14();
        Integer channelStoragePoolType = null;
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failedCount = new AtomicInteger(0);
        List<SgBChannelProduct> syncProducts = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            channelStoragePoolType = list.get(0).getChannelStoragePoolType();
        }
        list.stream().forEach(
                i -> {
                    i.setMqConsumerTime(new Date());
                    SgBChannelProduct channelProduct = new SgBChannelProduct();
                    try {
                        ValueHolderV14<SgChannelStorageOmsCalcResult> valueHolderV14 = sgChannelStorageOmsCalcService.calcChannelStorageOms(i);
                        channelProduct.setFinalSyncTime(new Date());
                        channelProduct.setCpCShopId(i.getCpCShopId());
                        channelProduct.setSkuId(i.getSkuId());
                        if (!valueHolderV14.isOK()) {
                            channelProduct.setSyncStatus(SgChannelStorageOmsSynchService.SYNCFAIL);
                            channelProduct.setSyncFailedReason(valueHolderV14.getMessage());
                            failedCount.getAndIncrement();
                            return;
                        }
                        successCount.getAndIncrement();
                        channelProduct.setFinalSyncNum(valueHolderV14.getData().getSyncNum());
                        channelProduct.setSyncStatus(SgChannelStorageOmsSynchService.SYNCSUCCESSS);
                    } catch (Exception e) {
                        channelProduct.setSyncStatus(SgChannelStorageOmsSynchService.SYNCFAIL);
                        channelProduct.setSyncFailedReason(e.getMessage());
                        log.error("{} consumer_channel_calculate_error. exception_has_occured:{}", this.getClass().getName(),
                                Throwables.getStackTraceAsString(e));
                        failedCount.getAndIncrement();
                    } finally {
                        syncProducts.add(channelProduct);
                    }
                }
        );
        boolean sgCalHasDelDeBugBuff = propertiesConf.getPropertyBoolean("sg.cal.has.debug.delete");
        SgChannelProductSaveRequest productSaveRequest = new SgChannelProductSaveRequest();
        productSaveRequest.setSgBChannelProductList(syncProducts);
        ValueHolderV14 updateResult = productSaveService.updateChannelProduct(productSaveRequest);
        if (sgCalHasDelDeBugBuff) {
            if (channelStoragePoolType == null || ChannelStoragePoolTypeEnum.SYNC_POOL_COMMON == ChannelStoragePoolTypeEnum.getType(channelStoragePoolType)) {
                bufferService.deleteChannelBuffer(list.stream().map(SgChannelStorageOmsCalcRequest::getBuffId).collect(Collectors.toList()));
            } else if (ChannelStoragePoolTypeEnum.SYNC_POOL_ALL == ChannelStoragePoolTypeEnum.getType(channelStoragePoolType)) {
                bufferService.deleteChannelProcedureBuffer(list.stream().map(SgChannelStorageOmsCalcRequest::getBuffId).collect(Collectors.toList()));
            }
        }
        return holder;
    }

    @Override
    public ValueHolderV14 calcChannelStorageConsumer(JSONObject param) {
        return sgChannelStorageOmsCalcService.executeTask(param);
    }


}
