package com.burgeon.r3.sg.stocksync.services;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.stocksync.api.SgChannelStorgeManualSyncCallBackCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorgeOmsCallbackAllRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgChannelStorageManualSyncCallbackCmdImpl implements SgChannelStorgeManualSyncCallBackCmd {

    @Autowired
    SgChannelStorageManualSyncCallBackService sgChannelStorageManualSyncCallBackService;

    @Override
    public ValueHolderV14<Integer> callbackAllRPC(SgChannelStorgeOmsCallbackAllRequest request) {
        try {
            return sgChannelStorageManualSyncCallBackService.callbackAllRPC(request);
        } catch (Exception e) {
            log.error("{} 调用渠道库存手工导入同步RPC回执失败. request:{} exception_has_occured:{}", this.getClass().getName(),
                    JSONObject.toJSONString(request), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.SUCCESS, "调用渠道库存手工导入同步RPC回执失败!");
        }
    }
}
