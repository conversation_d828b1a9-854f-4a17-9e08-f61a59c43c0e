package com.burgeon.r3.sg.stocksync.services;

import com.burgeon.r3.sg.stocksync.api.SgChannelStorageOmsSynchCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgStorageOmsSynchronousRequest;
import com.burgeon.r3.sg.stocksync.model.result.SgStorageOmsSynchronousResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
//@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgChannelStorageOmsSynchCmdImpl implements SgChannelStorageOmsSynchCmd {

    @Autowired
    SgChannelStorageOmsSynchService sgChannelStorageOmsSynchService;

    @Override
    public ValueHolderV14<SgStorageOmsSynchronousResult> synchronousStock(SgStorageOmsSynchronousRequest request) {
        return sgChannelStorageOmsSynchService.synchronousStock(request);
    }


}
