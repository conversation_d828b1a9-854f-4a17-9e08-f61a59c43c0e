package com.burgeon.r3.sg.stocksync.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.stocksync.api.SgChannelProductBoundCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelProductBoundRequest;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lin yu
 * @since 2019-11-08
 * create at : 2019-11-08 10:58
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgChannelProductBoundCmdImpl extends CommandAdapter implements SgChannelProductBoundCmd {

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        SgChannelProductBoundService service = ApplicationContextHandle.getBean(SgChannelProductBoundService.class);

        DefaultWebEvent event = session.getEvent();
        User user = session.getUser();

        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        SgChannelProductBoundRequest sgChannelProductBoundRequest = JSONObject.toJavaObject(param, SgChannelProductBoundRequest.class);
        sgChannelProductBoundRequest.setLoginUser(user);

        return R3ParamUtils.convertV14WithResult(service.channelProductBound(sgChannelProductBoundRequest));
    }
}
