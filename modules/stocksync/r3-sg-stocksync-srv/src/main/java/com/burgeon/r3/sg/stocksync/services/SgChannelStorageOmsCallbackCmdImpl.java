package com.burgeon.r3.sg.stocksync.services;

import com.burgeon.r3.sg.stocksync.api.SgChannelStorgeOmsCallbackCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorgeOmsCallbackRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgChannelStorageOmsCallbackCmdImpl implements SgChannelStorgeOmsCallbackCmd {

    @Autowired
    SgChannelStorageOmsCallbackService sgChannelStorageOmsCallbackService;

    @Override
    public ValueHolderV14<Integer> callbackRPC(SgChannelStorgeOmsCallbackRequest request) {
        return sgChannelStorageOmsCallbackService.callbackRPC(request);
    }

}
