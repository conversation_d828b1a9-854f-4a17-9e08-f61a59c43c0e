package com.burgeon.r3.sg.stocksync.services;

import com.burgeon.r3.sg.stocksync.api.SgBSyncChannelStockCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgBSyncChannelStockRequest;
import com.burgeon.r3.sg.stocksync.model.table.SgBSyncChannelStock;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @Date: 2020/7/9 9:26 下午
 * @Desc: 批量插入需要同步的策略
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgBSyncChannelStockCmdImpl implements SgBSyncChannelStockCmd {

    @Autowired
    private SgBSyncChannelStockService service;

    @Override
    public ValueHolderV14<List<SgBSyncChannelStock>> insert(List<SgBSyncChannelStock> channelStockList, User user) {
        return service.insert(channelStockList, user);
    }

    @Override
    public ValueHolderV14<Integer> updateSgBSyncChannelStock(SgBSyncChannelStock var1, User user) {
        return service.updateSgBSyncChannelStock(var1, user);
    }

    @Override
    public ValueHolderV14<Integer> deleteSgBSyncChannelStock(SgBSyncChannelStockRequest request) {
        return service.deleteSgBSyncChannelStock(request);
    }
}
