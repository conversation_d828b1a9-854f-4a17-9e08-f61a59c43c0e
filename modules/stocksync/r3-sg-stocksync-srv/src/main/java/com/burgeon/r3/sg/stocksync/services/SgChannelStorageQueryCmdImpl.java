package com.burgeon.r3.sg.stocksync.services;

import com.burgeon.r3.sg.stocksync.api.SgChannelStorageQueryCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStoragePdaQueryRequest;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageQueryRequest;
import com.burgeon.r3.sg.stocksync.model.result.SgBChannelStoragePdaResult;
import com.burgeon.r3.sg.stocksync.model.table.SgBChannelStorage;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR> lin yu
 * @since 2019-11-11
 * create at : 2019-11-11 20:52
 */
@Slf4j
@Component
//@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgChannelStorageQueryCmdImpl implements SgChannelStorageQueryCmd {

    @Autowired
    SgChannelStorageQueryCmdService service;

    @Override
    public ValueHolderV14<List<SgBChannelStorage>> queryChannelStorage(List<SgChannelStorageQueryRequest> requestList) {
        return service.queryChannelStorage(requestList);
    }

    @Override
    public ValueHolderV14<List<SgBChannelStoragePdaResult>> queryChannelPdaStorage(List<SgChannelStoragePdaQueryRequest> requestList) {
        return service.queryChannelStoragePda(requestList);
    }
}
