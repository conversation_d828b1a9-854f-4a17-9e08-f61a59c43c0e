package com.burgeon.r3.sg.stocksync.services;

import com.burgeon.r3.sg.stocksync.api.SgBChannelProductSyncCmd;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/8 14:03
 * 平台店铺商品表同步按钮
 */
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgBChannelProductSyncCmdImpl extends CommandAdapter implements SgBChannelProductSyncCmd {
    @Autowired
    private SgBChannelProductSyncStockService syncService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return syncService.stockSync(session);
    }
}
