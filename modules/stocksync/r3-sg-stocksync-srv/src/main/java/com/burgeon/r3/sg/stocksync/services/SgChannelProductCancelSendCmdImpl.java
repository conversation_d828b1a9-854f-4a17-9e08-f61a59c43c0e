package com.burgeon.r3.sg.stocksync.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.stocksync.api.SgChannelProductCancelSendCmd;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 取消门店防发货
 *
 * <AUTHOR>
 * @since 2020-08-16
 * create at : 2020-08-16 20:44
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgChannelProductCancelSendCmdImpl extends CommandAdapter implements SgChannelProductCancelSendCmd {

    @Autowired
    private SgChannelProductService sgChannelProductService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        ValueHolder holder = new ValueHolder();
        try {
            DefaultWebEvent event = session.getEvent();
            JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                    "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
            AssertUtils.notEmpty(param.getJSONArray("ids"), "请选择需要取消门店发货的记录");
            List<Long> ids = param.getJSONArray("ids").stream().map(
                    id -> Long.parseLong(id.toString())).collect(Collectors.toList());
            User user = session.getUser();
            return sgChannelProductService.cancelSend(ids, user);
        } catch (Exception e) {

            log.error("{}.execute. exception_has_occured:{}", this.getClass().getName(),
                    Throwables.getStackTraceAsString(e));

            holder.put("code", ResultCode.FAIL);
            holder.put("message", "取消门店发货失败：" + e.getMessage());
            
            return holder;
        }
    }
}
