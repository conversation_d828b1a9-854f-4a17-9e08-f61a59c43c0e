package com.burgeon.r3.sg.stocksync.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.burgeon.r3.sg.stocksync.api.SgChannelProductManualSyncCmd;
import com.burgeon.r3.sg.stocksync.common.OmsResultCode;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageOmsManualSynchRequest;
import com.google.common.collect.Maps;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/8/10
 * create at : 2020/8/10 21:38
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgChannelProductManualSyncCmdImpl extends CommandAdapter implements SgChannelProductManualSyncCmd {

    @Autowired
    private SgChannelProductManualSyncService manualSyncService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        ValueHolder holderV14 = new ValueHolder();
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        SgChannelStorageOmsManualSynchRequest manualSynchRequest = new SgChannelStorageOmsManualSynchRequest();
        if (CollectionUtils.isNotEmpty(param.getJSONArray("ids"))) {
            List<Integer> ids = param.getJSONArray("ids").stream().map(id -> {
                return Integer.parseInt(id.toString());
            }).collect(Collectors.toList());
            manualSynchRequest.setIdList(ids);
            manualSynchRequest.setSourceno("按选中同步库存到页面");
        }
        manualSynchRequest.setUser(session.getUser());
        ValueHolderV14 holder = manualSyncService.manualSyncChannelProduct(manualSynchRequest);
        HashMap resultMap = Maps.newHashMap();
        resultMap.put(OmsResultCode.KEY_CODE, holder.getCode());
        resultMap.put(OmsResultCode.KEY_MESSAGE, holder.getMessage());
        if (OmsResultCode.isFail(holder)) {
            resultMap.put(OmsResultCode.KEY_DATA, false);
        } else {
            resultMap.put(OmsResultCode.KEY_DATA, true);
        }
        holderV14.setData(resultMap);
        return holderV14;
    }
}
