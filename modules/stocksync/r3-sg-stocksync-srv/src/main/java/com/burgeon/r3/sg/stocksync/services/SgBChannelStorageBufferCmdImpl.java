package com.burgeon.r3.sg.stocksync.services;

import com.burgeon.r3.sg.basic.config.SgStorageMqConfig;
import com.burgeon.r3.sg.channel.model.dto.storage.SgBStorageBufferDto;
import com.burgeon.r3.sg.core.model.request.mq.SgChannelStorageOmsBufferRequest;
import com.burgeon.r3.sg.core.model.table.oms.SgBChannelStorageBuffer;
import com.burgeon.r3.sg.stocksync.api.SgBChannelStorageBufferCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferBatchSaveRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <ul>
 *     <li>
 *         add comment here
 *     </li>
 * </ul>
 * 2020/11/2 17:58
 * r3-sg - com.jackrain.nea.sg.basic.services
 *
 * <AUTHOR>
 */
@Component
//@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
@Slf4j
public class SgBChannelStorageBufferCmdImpl implements SgBChannelStorageBufferCmd {

    @Autowired
    private SgStorageMqConfig sgStorageMqConfig;

    @Autowired
    private SgChannelStorageOmsBufferService service;


    @Override
    public ValueHolderV14<Integer> saveDataToChannelStorageBuffer(SgChannelStorageBufferBatchSaveRequest request) {
        return service.batchInsertStorageBuffer(request);
    }

    @Override
    public ValueHolderV14<List<SgBChannelStorageBuffer>> saveDataToChannelStorageBufferByHand(SgChannelStorageBufferBatchSaveRequest request) {
        return service.batchInsertStorageBuffer2(request);
    }

    @Override
    public ValueHolderV14<Integer> saveChannelStorageBufferByFixedNumber(SgChannelStorageBufferBatchSaveRequest request) {
        return service.batchInsertStorageBufferByFixedNumber(request);
    }

    @Override
    public ValueHolderV14<List<SgBChannelStorageBuffer>> saveChannelStorageBufferByFixedNumberByHand(SgChannelStorageBufferBatchSaveRequest request) {
        return service.batchInsertStorageBufferByFixedNumberByHand(request);
    }

    /**
     * @Description: 新增库存同步计算池数据
     * @Author: hwy
     * @Date: 2021/7/19 14:07
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<java.lang.Integer>
     **/
    @Override
    public ValueHolderV14 createChannelStorageOmsBuffer(List<SgChannelStorageOmsBufferRequest> requests) {
        return service.createChannelStorageOmsBuffer(requests, sgStorageMqConfig.getChannelSyncTag());
    }

    @Override
    public List<SgBChannelStorageBuffer> queryByShopIdAndSkuId(List<SgBStorageBufferDto> params) {

        return service.queryByShopIdAndSkuId(params);
    }

}