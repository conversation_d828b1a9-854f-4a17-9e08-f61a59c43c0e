package com.burgeon.r3.sg.stocksync.services;

import com.burgeon.r3.sg.stocksync.api.SgChannelStorageOmsReduceCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageOmsReduceRequest;
import com.burgeon.r3.sg.stocksync.model.result.SgChannelStorageOmsReduceResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: chenss
 * @Date: 2019/4/22 13:15
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgChannelStorageOmsReduceCmdImpl implements SgChannelStorageOmsReduceCmd {

    @Autowired
    private SgChannelStorageOmsReduceService service;

    @Override
    public ValueHolderV14<SgChannelStorageOmsReduceResult> reductChannelStorageOms(SgChannelStorageOmsReduceRequest request,
                                                                                   String messageTag) {
        return service.reductChannelStorageOms(request, messageTag);
    }
}
