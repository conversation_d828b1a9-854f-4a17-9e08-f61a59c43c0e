package com.burgeon.r3.sg.stocksync.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.stocksync.api.SgChannelStorageManualSyncCmd;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lin yu
 * @since 2019-10-16
 * create at : 2019-10-16 14:57
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgChannelStorageManualSyncCmdImpl extends CommandAdapter implements SgChannelStorageManualSyncCmd {

    @Autowired
    private SgChannelStorageManualSyncService sgChannelStorageManualSyncService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        User user = session.getUser();
        JSONObject param = R3ParamUtils.getParamJo(session);
        JSONArray ids = param.getJSONArray("ids");
        return this.sgChannelStorageManualSyncService.manualSync(ids, user);
    }
}
