package com.burgeon.r3.sg.stocksync.services;

import com.burgeon.r3.inf.services.wms.in.SgBWmsStockResultService;
import com.burgeon.r3.sg.stocksync.api.WMSStockQueryBackCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 秦雄飞
 * @time: 2022/1/13 5:43 下午
 * @description:
 */

@Slf4j
@Component
//@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class WMSStockQueryBackCmdImpl implements WMSStockQueryBackCmd {

    @Autowired
    private SgBWmsStockResultService sgBWmsStockResultService;

    @Override
    public ValueHolderV14 createWmsStock(String msg) {
        return sgBWmsStockResultService.parseSaveMsg(msg);
    }
}
