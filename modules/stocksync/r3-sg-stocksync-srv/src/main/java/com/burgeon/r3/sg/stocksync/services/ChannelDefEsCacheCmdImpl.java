package com.burgeon.r3.sg.stocksync.services;

import com.burgeon.r3.sg.stocksync.api.ChannelDefEsCacheCmd;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> liang
 */
@Slf4j
@DubboService(protocol = "dubbo", cluster = "broadcast", validation = "true", version = "1.0", group = "sg-cal")
public class ChannelDefEsCacheCmdImpl implements ChannelDefEsCacheCmd {

    @Autowired
    ChannelDefElasticSearchService service;

    @Override
    public boolean loadCache(Long shopId, String ptProId, String skuId, Long psSkuId) {
        try {
            //TODO service.selectChannelProduct(shopId, ptProId, skuId, psSkuId, new Date());
            return true;
        } catch (Exception e) {
            log.error("{}.loadCache reload ChannelDef cache failed. load data shopid:{} skuId:{} psSkuId:{} exception_has_occured:{}",
                    this.getClass().getName(), shopId, skuId, psSkuId, Throwables.getStackTraceAsString(e));
            return false;
        }
    }
}
