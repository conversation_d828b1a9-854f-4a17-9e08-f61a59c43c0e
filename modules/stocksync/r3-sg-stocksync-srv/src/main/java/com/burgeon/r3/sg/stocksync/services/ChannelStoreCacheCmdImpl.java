package com.burgeon.r3.sg.stocksync.services;

import com.burgeon.r3.sg.stocksync.api.ChannelStoreCacheCmd;
import com.burgeon.r3.sg.stocksync.cache.ChannelStoreLocalCache;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> liang
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", cluster = "broadcast", validation = "true", version = "1.0", group = "sg-cal")
public class ChannelStoreCacheCmdImpl implements ChannelStoreCacheCmd {

    @Autowired
    ChannelStoreLocalCache channelStoreLocalCache;

    @Override
    public boolean update(Long shopId) {
        try {
            channelStoreLocalCache.update(shopId);
        } catch (Exception e) {
            log.error("{} update channel store cache was failed. exception_has_occured:{}", this.getClass().getName(),
                    Throwables.getStackTraceAsString(e));
            return false;
        }
        return true;
    }

    @Override
    public CacheStats getCacheStats() {
        return channelStoreLocalCache.getCacheStats();
    }


}
