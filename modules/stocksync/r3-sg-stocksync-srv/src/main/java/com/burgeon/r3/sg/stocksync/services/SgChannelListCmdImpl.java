package com.burgeon.r3.sg.stocksync.services;

import com.burgeon.r3.sg.stocksync.api.SgChannelListCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferRequest;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageFtpRequest;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageStockQRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2020-05-09
 * create at : 2020-05-09 10:58
 */
@Slf4j
@Component
//@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgChannelListCmdImpl implements SgChannelListCmd {

    @Autowired
    private SgChannelListService service;

    @Override
    public ValueHolderV14 queryChannelStock(SgChannelStorageStockQRequest sgChannelStorageStockQRequest) {
        return service.queryChannelStock(sgChannelStorageStockQRequest);
    }

    @Override
    public ValueHolderV14 queryChannelStockAndExport(SgChannelStorageStockQRequest sgChannelStorageStockQRequest) {
        return service.export(sgChannelStorageStockQRequest);
    }

    @Override
    public ValueHolderV14 queryChannelStorageFtp(SgChannelStorageFtpRequest sgChannelStorageFtpRequest) {
        return service.queryChannelStorageFtp(sgChannelStorageFtpRequest);
    }

    @Override
    public ValueHolderV14 queryChannelStorageBuffer(SgChannelStorageBufferRequest sgChannelStorageBufferRequest) {
        return service.queryChannelStorageBuffer(sgChannelStorageBufferRequest);
    }
}
