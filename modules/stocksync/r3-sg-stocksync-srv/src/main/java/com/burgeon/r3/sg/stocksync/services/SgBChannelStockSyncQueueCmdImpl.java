package com.burgeon.r3.sg.stocksync.services;

import com.burgeon.r3.sg.stocksync.api.SgBChannelStockSyncQueueCmd;
import com.burgeon.r3.sg.stocksync.services.trigger.SgBChannelStockSyncQueueService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/9 16:35
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgBChannelStockSyncQueueCmdImpl implements SgBChannelStockSyncQueueCmd {

    @Autowired
    SgBChannelStockSyncQueueService sgBChannelStockSyncQueueService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return sgBChannelStockSyncQueueService.execute(session);
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}
