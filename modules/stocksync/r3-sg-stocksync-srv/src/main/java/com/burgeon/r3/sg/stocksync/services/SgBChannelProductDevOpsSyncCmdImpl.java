package com.burgeon.r3.sg.stocksync.services;

import com.burgeon.r3.sg.stocksync.api.SgBChannelProductDevOpsSyncCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgBChannelProductDevOpsSyncRequest;
import com.burgeon.r3.sg.stocksync.model.request.SgBChannelStorageIncDevOpsSaveRequest;
import com.burgeon.r3.sg.stocksync.services.report.SgBChannelProductDevOpsSyncService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: hwy
 * @time: 2021/9/16 19:45
 */
@Component
@Slf4j
//@DubboService(protocol = "dubbo", validation = "true", version = "1.0", group = "sg")
public class SgBChannelProductDevOpsSyncCmdImpl implements SgBChannelProductDevOpsSyncCmd {
    @Autowired
    private SgBChannelProductDevOpsSyncService sgBChannelProductDevOpsSyncService;

    @Override
    public ValueHolderV14 syncChannelStorage(SgBChannelProductDevOpsSyncRequest requests) {
        return sgBChannelProductDevOpsSyncService.sgBChannelProductDevOpsSync(requests);
    }

    /**
     * @param request:
     * @Description: 平台店铺库存管理-手动增量同步库存
     * @Author: hwy
     * @Date: 2021/8/25 20:02
     * @return: com.jackrain.nea.util.ValueHolder
     **/
    @Override
    public ValueHolderV14 sgBChannelStorageIncDevOpsSave(SgBChannelStorageIncDevOpsSaveRequest request) {
        return sgBChannelProductDevOpsSyncService.sgBChannelStorageIncDevOpsSave(request);
    }
}