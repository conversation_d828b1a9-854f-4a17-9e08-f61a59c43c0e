<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.burgeon.r3</groupId>
        <artifactId>r3-sg</artifactId>
        <version>3.0.0-SNAPSHOT</version>
        <relativePath>../../../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.burgeon.r3</groupId>
    <artifactId>r3-sg-stocksync-srv</artifactId>
    <packaging>jar</packaging>
    <version>3.0.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sg-stocksync-api</artifactId>
            <version>${r3.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sg-stocksync-bll</artifactId>
            <version>${r3.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-model-query</artifactId>
            <version>${r3.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-service-api-model</artifactId>
            <version>${r3.framework.version}</version>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-service-impl-dubbo</artifactId>
            <version>${r3.framework.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.burgeon.r3</groupId>
                    <artifactId>r3-sg-basic-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sysapi-ext</artifactId>
            <version>${r3.framework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>