package com.burgeon.r3.sg.stocksync.mapper;

import com.burgeon.r3.sg.channel.model.dto.storage.SgBStorageBufferDto;
import com.burgeon.r3.sg.core.model.table.oms.SgBChannelStorageBuffer;
import com.burgeon.r3.sg.stocksync.model.table.SgBChannelSynstockBatchno;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/3 16:49
 */
@Mapper
@Repository
public interface SgBChannelStorageBufferMapper extends ExtentionMapper<SgBChannelStorageBuffer> {
    /**
     * 删除渠道缓存，不存在在系统条码的渠道缓存
     *
     * @param skuIdList
     * @return
     */
    @Delete("<script> " +
            "delete from sg_b_channel_storage_buffer where deal_status not in(3) and sku_id in " +
            "   <foreach collection='skuIdList' item='skuId' open='(' separator=',' close=')'> #{skuId} </foreach> " +
            "</script> ")
    Integer delChannelBuffer(@Param("skuIdList") List<String> skuIdList);

    /**
     * 定时清除渠道计算缓存池   状态为成功
     */
    @Delete("delete from sg_b_channel_storage_buffer where deal_status = 3 ")
    void deleteChannelStorageBuffer();


    /**
     * 查询缓存池中待执行的数据
     */
    @Select("select * from (" +
            "SELECT cp_c_shop_id, sku_id, source_cp_c_shop_id, ware_type FROM ${tableName} " +
            "WHERE deal_status = 1 AND cp_c_shop_id in (${shopIdStr}) and sku_id in (${skuIdStr}) and source_cp_c_shop_id=#{sourceShopId} " +
            "GROUP BY cp_c_shop_id, sku_id, source_cp_c_shop_id, ware_type " +
            ") foo ORDER BY foo.sku_id asc")
    List<SgBChannelStorageBuffer> selectChannelStorageBufferByShopId(@Param("tableName") String tableName,
                                                                     @Param("shopIdStr") String shopIdStr,
                                                                     @Param("skuIdStr") String skuIdStr,
                                                                     @Param("sourceShopId") Long sourceShopId);

    /**
     * 根据批次号查询缓存池中执行中的数据
     */
    @Select("<script> " +
            "select batch_no,source_no,count(1) as tobeNumber " +
            "from sg_b_channel_storage_buffer " +
            "where batch_no in " +
            "<foreach item='item' collection='batchnoList' open='(' separator=',' close=')'> " +
            " #{item} " +
            "</foreach>" +
            "group by batch_no, source_no " +
            " </script>")
    List<SgBChannelSynstockBatchno> selectChannelSynstockBatchnoCount(@Param("batchnoList") List<String> batchnoList);


    @Insert("<script> " +
            "insert ignore into ${tableName}(id,cp_c_shop_id,cp_c_shop_title,sku_id,deal_status," +
            "ad_org_id,ad_client_id, source_no,ware_type,source_cp_c_shop_id,cp_c_platform_id,ps_c_sku_ecode,ps_c_sku_id,ps_c_pro_id,batch_no," +
            "modifieddate,creationdate,ownerid,ownername,ownerename,modifierid,modifiername,modifierename,fixed_qty_flag,fixed_qty,sync_type) " +
            "values" +
            "<foreach item='item' index='index' collection='storageBufferList' separator=',' > " +
            "( #{item.id,jdbcType=INTEGER},#{item.cpCShopId,jdbcType=INTEGER},#{item.cpCShopTitle,jdbcType=VARCHAR},#{item.skuId,jdbcType=VARCHAR},#{item.dealStatus,jdbcType=INTEGER},#{item.adOrgId,jdbcType=INTEGER}" +
            ",#{item.adClientId,jdbcType=INTEGER},#{item.sourceNo,jdbcType=VARCHAR},#{item.wareType,jdbcType=INTEGER},#{item.sourceCpCShopId,jdbcType=INTEGER},#{item.cpCPlatformId,jdbcType=INTEGER}" +
            ",#{item.psCSkuEcode,jdbcType=VARCHAR},#{item.psCSkuId,jdbcType=INTEGER},#{item.psCProId,jdbcType=INTEGER},#{item.batchNo,jdbcType=VARCHAR},now(),now(),#{item.ownerid,jdbcType=INTEGER},#{item.ownername,jdbcType=VARCHAR}" +
            ",#{item.ownerename,jdbcType=VARCHAR},#{item.modifierid,jdbcType=INTEGER},#{item.modifiername,jdbcType=VARCHAR},#{item.modifierename,jdbcType=VARCHAR}" +
            ",#{item.fixedQtyFlag,jdbcType=VARCHAR} ,#{item.fixedQty,jdbcType=NUMERIC} ,#{item.syncType,jdbcType=VARCHAR} )" +
            "</foreach>" +
            " </script> ")
    Integer batchInsertStorageBufferOnConfilict(@Param("tableName") String tableName, @Param("storageBufferList") List<SgBChannelStorageBuffer> storageBufferList);


    /**
     * 批量更新状态
     *
     * @param dealStatus 状态
     * @param ids        ids
     */
    @Update("<script>"
            + "update sg_b_channel_storage_buffer set DEAL_STATUS =#{dealStatus}, modifieddate = now()"
            + "WHERE id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    void batchUpdateBufferStatus(@Param("dealStatus") Integer dealStatus, @Param("ids") List<Long> ids);


    @Delete("DELETE FROM ${tableName} as bufferPool USING ( " +
            "SELECT cp_c_shop_id, sku_id, source_cp_c_shop_id FROM " +
            "${tableName} " +
            "GROUP BY cp_c_shop_id, sku_id, source_cp_c_shop_id " +
            "HAVING " +
            "COUNT ( 1 ) >1 ) as multiplex " +
            "WHERE " +
            "bufferPool.cp_c_shop_id = multiplex.cp_c_shop_id " +
            "AND bufferPool.sku_id = multiplex.sku_id " +
            "AND bufferPool.deal_status <>1 " +
            "And modifieddate <= now() - interval '${interval} MINUTE'")
    Integer batchDelBufferPool(@Param("tableName") String tableName,
                               @Param("interval") Integer interval);


    @Select({"<script>" +
            "select * from sg_b_channel_storage_buffer b " +
            "<where>" +
            "<if test = 'params != null and params.size > 0'>" +
            " (b.cp_c_shop_id,b.sku_id) in " +
            "<foreach item='param' collection='params' separator=',' open='(' close=')' >" +
            " (#{param.shopId},#{param.skuId}) " +
            "</foreach>" +
            "</if>" +
            "</where>" +
            "</script>"})
    List<SgBChannelStorageBuffer> queryByShopIdAndSkuId(@Param("params") List<SgBStorageBufferDto> params);
}
