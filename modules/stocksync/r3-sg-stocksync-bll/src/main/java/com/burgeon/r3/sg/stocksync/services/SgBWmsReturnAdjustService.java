package com.burgeon.r3.sg.stocksync.services;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.mapper.CpCPhyWarehouseMapper;
import com.burgeon.r3.sg.basic.mapper.CpCStoreMapper;
import com.burgeon.r3.sg.basic.rpc.RpcPsService;
import com.burgeon.r3.sg.channel.mapper.storage.SgBWmsReturnMiddleTableMapper;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBWmsReturnMiddleTable;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.stocksync.common.SgResultCode;
import com.burgeon.r3.sg.store.common.SgStoreConstants;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustMainSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustSaveRequest;
import com.burgeon.r3.sg.store.services.adjust.SgBStoAdjustSaveService;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class SgBWmsReturnAdjustService {

    @Autowired
    private SgBWmsReturnMiddleTableMapper wmsReturnMiddleTableMapper;

    @Autowired
    private RpcPsService rpcPsService;

    @Autowired
    private CpCStoreMapper cpCStoreMapper;

    @Autowired
    SgBStoAdjustSaveService sgBStoAdjustSaveService;

    @Autowired
    CpCPhyWarehouseMapper cpCPhyWarehouseMapper;


    public ValueHolderV14 wmsBackInventory() {

        User user = SystemUserResource.getRootUser();

        QueryWrapper<SgBWmsReturnMiddleTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SgBWmsReturnMiddleTable::getIsactive, "Y");
        queryWrapper.lambda().eq(SgBWmsReturnMiddleTable::getOutType, "0");
        queryWrapper.lambda().lt(SgBWmsReturnMiddleTable::getWmsFailedCount, 5);
        queryWrapper.lambda().in(SgBWmsReturnMiddleTable::getOutStatus, new ArrayList<String>() {{
            add("0");
            add("3");
            add("4");
        }});
        queryWrapper.lambda().orderBy(true, true, SgBWmsReturnMiddleTable::getCreationdate);
        PageHelper.startPage(0, 1000);
        List<SgBWmsReturnMiddleTable> list = wmsReturnMiddleTableMapper.selectList(queryWrapper);
        List<SgBWmsReturnMiddleTable> updateList = new ArrayList<>();
        for (SgBWmsReturnMiddleTable wmsReturnMiddleTable : list) {

            SgBStoAdjustSaveRequest request = new SgBStoAdjustSaveRequest();
            request.setObjId(-1L);
            request.setLoginUser(user);

            String message = wmsReturnMiddleTable.getOutResultStr();
            Long wmsFailedCount = wmsReturnMiddleTable.getWmsFailedCount();
            wmsReturnMiddleTable.setOutStatus("2");
            wmsReturnMiddleTable.setWmsFailedReason("");
            wmsReturnMiddleTable.setWmsFailedCount(0L);

            try {
                request = parseInventoryMessage2SgPhyAdjust(wmsReturnMiddleTable);
            } catch (Exception e) {
                log.error("parseInventoryMessage2SgPhyAdjust.Exception{}", e.getMessage(), e);
                wmsReturnMiddleTable.setOutStatus("3");
                wmsReturnMiddleTable.setWmsFailedCount(5L);
                wmsReturnMiddleTable.setWmsFailedReason(StringUtils.substring(e.getMessage(), 0, 500));
                updateList.add(wmsReturnMiddleTable);
                continue;
            }

            try {

                ValueHolderV14 holderV14 = sgBStoAdjustSaveService.saveAndSubmit(request);
                if (SgResultCode.isFail(holderV14)) {
                    wmsReturnMiddleTable.setOutStatus("4");
                    wmsReturnMiddleTable.setWmsFailedCount(wmsFailedCount + 1);
                    wmsReturnMiddleTable.setWmsFailedReason(holderV14.getMessage());
                }
            } catch (Exception e) {
                wmsReturnMiddleTable.setOutStatus("4");
                wmsReturnMiddleTable.setWmsFailedCount(wmsFailedCount + 1);
                wmsReturnMiddleTable.setWmsFailedReason(e.getMessage());
            }

            updateList.add(wmsReturnMiddleTable);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            wmsReturnMiddleTableMapper.batchUpdate(updateList);
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "成功!");
    }


    /**
     * 奇门库存盘点结果WMS回传服务数据转化为库存调整单
     *
     * @param middleTable
     * @return
     */
    private SgBStoAdjustSaveRequest parseInventoryMessage2SgPhyAdjust(SgBWmsReturnMiddleTable middleTable) {
        String message = middleTable.getOutAnalyseStr();
        User user = SystemUserResource.getRootUser();
        log.info("parseInventoryMessage2SgPhyAdjust  message信息：{}", message);
        SgBStoAdjustSaveRequest adjustRequest = new SgBStoAdjustSaveRequest();
        JSONArray profitItem = JSONArray.parseArray(message);
        /** 0、查询商品信息 */
        List<SgBStoAdjustItemSaveRequest> items = Lists.newArrayList();
        List<String> itemCodeList = new ArrayList<>();
        for (Object obj : profitItem) {
            JSONObject item = (JSONObject) obj;
            itemCodeList.add(item.getString("itemCode"));
        }
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(itemCodeList)) {
            AssertUtils.logAndThrow("parseInventoryMessage2SgPhyAdjust 无异动明细差异数量不等于0的数据，不允许冲账", user.getLocale());
        }
        Map<String,PsCSku> skuMap = new HashMap<>();
        ValueHolderV14<List<PsCSku>> valueHolderV14 = rpcPsService.querySKUByEcodeList(itemCodeList);
        if(valueHolderV14.isOK()){
            List<PsCSku> skuList = valueHolderV14.getData();
            for (PsCSku sku : skuList){
                if (skuMap.get(sku.getEcode())==null){
                    skuMap.put(sku.getEcode(),sku);
                }
            }
        }else{
            AssertUtils.logAndThrow("parseInventoryMessage2SgPhyAdjust 异动明细商品信息查询失败！", user.getLocale());
        }

        /** 1、处理主表数据*/
        SgBStoAdjustMainSaveRequest saveRequest = new SgBStoAdjustMainSaveRequest();
        saveRequest.setObjId(-1L);
        saveRequest.setLoginUser(user);
//        saveRequest.setSourceBillId(profit.getId());
        saveRequest.setSourceBillNo(middleTable.getWmsBillNo());
        saveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_PHY_PROFIT);
        // 盘点单时间，取当前时间
        saveRequest.setBillDate(new Date());
        saveRequest.setCpCStoreId(middleTable.getCpCStoreId());
        saveRequest.setCpCStoreEcode(middleTable.getCpCStoreEcode());
        saveRequest.setCpCStoreEname(middleTable.getCpCStoreEname());
        saveRequest.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL);
        saveRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_ADJUST_PROP_INVENTORY);
        saveRequest.setRemark("wms库存异动！"+middleTable.getWmsBillNo());
        saveRequest.setDrpStatus(Integer.valueOf(SgStoreConstants.SEND_DRP_STATUS_UNDECLARED));
        /** 2、处理子表数据 */
        for (Object obj : profitItem) {
            JSONObject item = (JSONObject) obj;
            if (item.getLong("quantity") == 0) {
                continue;
            }
            SgBStoAdjustItemSaveRequest itemSaveRequest = new SgBStoAdjustItemSaveRequest();
            itemSaveRequest.setId(-1L);
            itemSaveRequest.setSourceBillItemId(skuMap.get(item.getLongValue("itemCode")).getId());
            itemSaveRequest.setCpCStoreId(middleTable.getCpCStoreId());
            itemSaveRequest.setCpCStoreEcode(middleTable.getCpCStoreEcode());
            itemSaveRequest.setCpCStoreEname(middleTable.getCpCStoreEname());
            itemSaveRequest.setQty(new BigDecimal(item.getLong("quantity")));
            items.add(itemSaveRequest);
        }
        adjustRequest.setMainRequest(saveRequest);
        adjustRequest.setItems(items);

        SgBStoAdjustSaveService service = ApplicationContextHandle.getBean(SgBStoAdjustSaveService.class);
        ValueHolderV14<SgR3BaseResult> v14 = service.saveAndSubmit(adjustRequest);
        log.info("parseInventoryMessage2SgPhyAdjust  wms库存异动生成调整单 返回结果:{}",
                JSONObject.toJSON(v14));
        if (!v14.isOK()) {
            AssertUtils.logAndThrow(String.format("parseInventoryMessage2SgPhyAdjust 生成调整单失败，失败原因：%s", v14.getMessage()), user.getLocale());
        }

        log.info("WMS回传服务数据转化为库存调整单 parseInventoryMessage2SgPhyAdjust :{}", adjustRequest);
        return adjustRequest;
    }

}
