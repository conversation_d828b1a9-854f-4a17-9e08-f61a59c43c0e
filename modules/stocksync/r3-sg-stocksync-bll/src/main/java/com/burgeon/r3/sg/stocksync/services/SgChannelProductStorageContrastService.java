package com.burgeon.r3.sg.stocksync.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.services.product.SgChannelProductDownloadService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.SgDataMonitorInfo;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.tableExtend.SgBChannelStorageBufferExtend;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.SgSendNoticeUtils;
import com.burgeon.r3.sg.stocksync.api.SgBChannelStorageBufferCmd;
import com.burgeon.r3.sg.stocksync.common.SgStockSyncConstants;
import com.burgeon.r3.sg.stocksync.mapper.SgCStockContrastStrategyMapper;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferBatchSaveRequest;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferSaveRequest;
import com.burgeon.r3.sg.stocksync.model.table.SgCStockContrastStrategy;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.ip.api.cloudhub.CloudHubQueryProductStorageCmd;
import com.jackrain.nea.ip.model.request.VipQueryProductStorageRequest;
import com.jackrain.nea.ip.model.result.VipQueryProductStorageInfoResult;
import com.jackrain.nea.ip.model.result.VipQueryProductStorageResult;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.ps.api.SkuListCmd;
import com.jackrain.nea.ps.api.table.ProSku;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/17 14:21
 */
@Slf4j
@Component
public class SgChannelProductStorageContrastService {

    @Autowired
    private SgCStockContrastStrategyMapper sgStockContrastStrategyMapper;
    @Autowired
    private SgBChannelProductMapper sgChannelProductMapper;
    @Autowired
    private SgChannelProductDownloadService sgChannelProductDownloadService;
    @Autowired
    private SgSendNoticeUtils sgSendNoticeUtils;
    @DubboReference(group = "ip", version = "1.4.0")
    private CloudHubQueryProductStorageCmd cloudHubQueryProductStorageCmd;
    @DubboReference(group = "ps ", version = "1.0")
    private SkuListCmd skuListCmd;
    @DubboReference(group = "sg", version = "1.0")
    private SgBChannelStorageBufferCmd channelStorageBufferCmd;
    @Value("${r3.sg.channel.adb.datasync:r3_rc_datasync}")
    private String adbSchema;
    @Deprecated
    @Value("${r3.sg.threadPoolExecutor.corePoolSize:6}")
    private int corePoolSize;
    @Deprecated
    @Value("${r3.sg.threadPoolExecutor.maxPoolSize:7}")
    private int maxPoolSize;

    @Value("${r3.sg.channelProductStorageContrastTask.pull.vipSkuNum:500}")
    private int pullVipSkuNum;
    @Deprecated
    private static final String THREAD_POOL_NAME = "SgChannelProductStorageContrast_%d";
    /**
     * 线程池改造，详见{@link com.burgeon.r3.sg.basic.config.ThreadPoolConfig}
     */
    @Resource
    private ThreadPoolTaskExecutor commonExecutorPool;

    /**
     * 商品下载状态
     * 下载状态:-1 失败;0 未下载；1 下载中; 2 成功
     */
    private static final Integer DOWNLOAD_FAIL = -1;
    private static final Integer DOWNLOAD_UN = 0;
    private static final Integer DOWNLOAD_ING = 1;
    private static final Integer DOWNLOAD_SUCCESS = 2;

    /**
     * 平台店铺库存对比
     *
     * @return ValueHolderV14
     */
    public ValueHolderV14 storageContrast(List<Long> platformIds, List<Long> shopIds) {
        log.info(" Start SgChannelProductStorageContrastService.storageContrast platformIds={},shopIds={}",
                platformIds, shopIds);
        ValueHolderV14 vh = new ValueHolderV14<>(ResultCode.SUCCESS, "success!");
        Long start = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(platformIds)) {
            //生产环境唯品会平台id 应在定时任务中进行配置 防遗漏 在此补充
            platformIds.add(19L);
        }
        List<SgCStockContrastStrategy> contrastStrategyList =
                sgStockContrastStrategyMapper.selectList(new LambdaQueryWrapper<SgCStockContrastStrategy>()
                        .in(CollectionUtils.isNotEmpty(shopIds), SgCStockContrastStrategy::getCpCShopId, shopIds)
                        .eq(SgCStockContrastStrategy::getIsOpenStockContrast,
                                SgStockSyncConstants.IS_OPEN_STOCK_CONTRAST_Y)
                        .eq(SgCStockContrastStrategy::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isNotEmpty(contrastStrategyList)) {
            Map<Long, CpShop> shopHashMap = new HashMap<>(16);
            //筛选平台类型下的策略,查询店铺信息存入map
            ValueHolderV14<List<SgCStockContrastStrategy>> filterVh =
                    filterShopByPlatform(contrastStrategyList, platformIds, shopHashMap);
            if (!filterVh.isOK()) {
                return filterVh;
            }
            List<SgCStockContrastStrategy> contrastStrategies = filterVh.getData();
            //数据源主要分【平台店铺的在售商品信息】和【库存同步列队】
            List<SgCStockContrastStrategy> dataSourceTypeSaleList =
                    contrastStrategies.stream().filter(c -> SgStockSyncConstants.DATA_SOURCE_TYPE_SALE.equals(c.getDataSourceType())).collect(Collectors.toList());
            List<SgCStockContrastStrategy> dataSourceTypeNList =
                    contrastStrategies.stream().filter(c -> SgStockSyncConstants.DATA_SOURCE_TYPE_N.equals(c.getDataSourceType())).collect(Collectors.toList());
            try {
                //库存对比数据源 在售商品逻辑
                if (CollectionUtils.isNotEmpty(dataSourceTypeSaleList)) {
                    syncDataSourceSale(dataSourceTypeSaleList, shopHashMap);
                }
                //库存对比数据源 N小时内订单商品逻辑
                if (CollectionUtils.isNotEmpty(dataSourceTypeNList)) {
                    syncDataSourceN(dataSourceTypeNList, shopHashMap);
                }
            } catch (Exception e) {
                log.error(" SgChannelProductStorageContrastService.storageContrast. exception_has_occured:{}",
                        Throwables.getStackTraceAsString(e));
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("平台店铺库存对比异常!异常原因:" + e.getMessage());
            }

        }
        Long end = System.currentTimeMillis();
        log.info(" Finish SgChannelProductStorageContrastService.storageContrast vh={},time={}ms",
                JSONObject.toJSONString(vh), end - start);
        return vh;
    }

    /**
     * 根据平台类型&&店铺ids过滤策略
     *
     * @param contrastStrategyList 策略集合
     * @param platformIds          平台类型ids
     * @return ValueHolderV14<List < SgCStockContrastStrategy>>
     */
    private ValueHolderV14<List<SgCStockContrastStrategy>> filterShopByPlatform(List<SgCStockContrastStrategy> contrastStrategyList, List<Long> platformIds, Map<Long, CpShop> shopHashMap) {
        ValueHolderV14<List<SgCStockContrastStrategy>> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "success!");
        List<Long> shopIds =
                contrastStrategyList.stream().distinct().map(SgCStockContrastStrategy::getCpCShopId).collect(Collectors.toList());
        //店铺信息
        List<CpShop> rpcShopList = CommonCacheValUtils.getAllShopInfoList(shopIds);
        if (CollectionUtils.isEmpty(rpcShopList)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "查询平台店铺信息失败!");
        } else {
            //符合该平台类型的店铺id'集合

            List<CpShop> cpShopList =
                    rpcShopList.stream().filter(shop -> platformIds.contains(shop.getCpCPlatformId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(cpShopList)) {
                return new ValueHolderV14<>(ResultCode.FAIL, "没有符合该平台类型下的店铺!");
            }
            cpShopList.forEach(s -> shopHashMap.put(s.getId(), s));
            Set<Long> syncShopIds = shopHashMap.keySet();
            //符合条件的策略
            List<SgCStockContrastStrategy> filterStockContrastStrategyies =
                    contrastStrategyList.stream().filter(contra -> syncShopIds.contains(contra.getCpCShopId())).collect(Collectors.toList());
            vh.setData(filterStockContrastStrategyies);
        }
        return vh;
    }

    /**
     * 库存对比数据源 在售商品逻辑
     *
     * @param sgStockContrastStrategyList 对比策略集合
     * @param shopHashMap                 店铺map
     */
    private void syncDataSourceSale(List<SgCStockContrastStrategy> sgStockContrastStrategyList,
                                    Map<Long, CpShop> shopHashMap) {
        log.info(" SgChannelProductStorageContrastService.syncDataSourceSale sgStockContrastStrategyList.size={}," +
                "shopHashMap.size={}", sgStockContrastStrategyList.size(), shopHashMap.size());
        List<Long> shopIds =
                sgStockContrastStrategyList.stream().map(SgCStockContrastStrategy::getCpCShopId).collect(Collectors.toList());
        List<Integer> saleStatus = new ArrayList<>();
        saleStatus.add(SgChannelConstants.CHANNEL_PRODUCT_STATUS_SALING);
        //查询符合条件的平台店铺商品表
        List<SgBChannelProduct> products = queryChannelProduct(shopIds, null, saleStatus);

        if (CollectionUtils.isNotEmpty(products)) {
            setProductStorageRequest(products, shopHashMap, sgStockContrastStrategyList);
        }
    }

    /**
     * 库存对比数据源 N小时内订单商品逻辑
     *
     * @param sgStockContrastStrategyList 对比策略集合
     * @param shopHashMap                 店铺map
     */
    private void syncDataSourceN(List<SgCStockContrastStrategy> sgStockContrastStrategyList,
                                 Map<Long, CpShop> shopHashMap) {

        log.info(" SgChannelProductStorageContrastService.syncDataSourceN sgStockContrastStrategyList.size={}," +
                "shopHashMap.size={}", sgStockContrastStrategyList.size(), shopHashMap.size());

        Map<Long, List<SgCStockContrastStrategy>> timeMap =
                sgStockContrastStrategyList.stream().collect(Collectors.groupingBy(SgCStockContrastStrategy::getTimeRange));
        Set<Map.Entry<Long, List<SgCStockContrastStrategy>>> entries = timeMap.entrySet();
        //收集所有时效订单内的商品链接  在转换成平台条码
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date nowDate = new Date();
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(nowDate);

        SgChannelProductStorageContrastService bean =
                ApplicationContextHandle.getBean(SgChannelProductStorageContrastService.class);
        for (Map.Entry<Long, List<SgCStockContrastStrategy>> entry : entries) {
            List<SgCStockContrastStrategy> values = entry.getValue();
            Long hour = entry.getKey();
            rightNow.add(Calendar.HOUR, -hour.intValue());
            Date queryTime = rightNow.getTime();
            List<Long> shopIds =
                    values.stream().map(SgCStockContrastStrategy::getCpCShopId).collect(Collectors.toList());
            //adb查询唯品会时效订单前N'小时前的商品连接
            List<String> timeOrderItemCodes = new ArrayList<>();
            try {
                Future<List<String>> garcodeFuture = bean.queryAdbOrderGarCodeList(shopIds,
                        simpleDateFormat.format(queryTime));
                timeOrderItemCodes = garcodeFuture.get();

            } catch (Exception e) {
                log.error("adb查询唯品会时效订单前N小时前下单商品异常：{}", Throwables.getStackTraceAsString(e));
            }
            if (CollectionUtils.isEmpty(timeOrderItemCodes)) {
                return;
            }
            List<List<String>> partition = Lists.partition(timeOrderItemCodes, SgConstants.SG_COMMON_QUERY_SIZE);
            List<Long> skuIds = new ArrayList<>();
            //500一次批量rpc查询条码信息
            for (List<String> gbcodeList : partition) {
                ValueHolderV14<List<ProSku>> holderV14 = skuListCmd.querySkuListByForCodes(gbcodeList);
                if (!holderV14.isOK()) {
                    log.error(" SgChannelProductStorageContrastService.syncDataSourceN.querySkuListByForCodes " +
                            "exception_has_occured:{}", holderV14);
                    continue;
                }
                List<ProSku> rpcData = holderV14.getData();
                List<Long> psSkuIds = rpcData.stream().map(ProSku::getId).collect(Collectors.toList());
                skuIds.addAll(psSkuIds);
            }
            //查询符合条件的平台店铺商品表
            List<SgBChannelProduct> productList = queryChannelProduct(shopIds, skuIds.stream().distinct().collect(Collectors.toList()), null);
            if (CollectionUtils.isNotEmpty(productList)) {
                setProductStorageRequest(productList, shopHashMap, sgStockContrastStrategyList);
            }
        }

    }

    /**
     * 封装请求参数 RPC调用ip唯品会拉取平台商品库存接口
     *
     * @param sgChannelProducts 平台商品集合
     * @param shopHashMap       店铺map
     */
    private void setProductStorageRequest(List<SgBChannelProduct> sgChannelProducts,
                                          Map<Long, CpShop> shopHashMap,
                                          List<SgCStockContrastStrategy> sgStockContrastStrategyList) {

        log.info(" SgChannelProductStorageContrastService.setProductStorageRequest sgChannelProducts.size={}," +
                        "shopHashMap.size={},sgStockContrastStrategyList.size={}",
                sgChannelProducts.size(), shopHashMap.size(), sgStockContrastStrategyList.size());

        Map<String, SgBChannelProduct> skuIdProductMap = new HashMap<>(16);
        sgChannelProducts.forEach(c -> skuIdProductMap.put(c.getSkuId(), c));
        //按照店铺id分组
        Map<Long, List<SgBChannelProduct>> shopIdProductMap =
                sgChannelProducts.stream().collect(Collectors.groupingBy(SgBChannelProduct::getCpCShopId));
        log.info("SgChannelProductStorageContrastService.setProductStorageRequest shopIdProductMap.size={}",
                shopIdProductMap.size());
        List<VipQueryProductStorageRequest> vipQueryProductStorageRequests = new ArrayList<>();
        Map<Long, SgCStockContrastStrategy> shopIdStrategyMap =
                sgStockContrastStrategyList.stream().collect(Collectors.toMap(SgCStockContrastStrategy::getCpCShopId,
                        Function.identity()));
        for (SgBChannelProduct sgChannelProduct : sgChannelProducts) {
            CpShop cpShop = shopHashMap.get(sgChannelProduct.getCpCShopId());
            if (Objects.isNull(cpShop)) {
                continue;
            }
            VipQueryProductStorageRequest vipQueryProductStorageRequest = new VipQueryProductStorageRequest();
            vipQueryProductStorageRequest.setPlatform(String.valueOf(cpShop.getCpCPlatformId()));
            vipQueryProductStorageRequest.setSeller_nick(cpShop.getSellerNick());
            vipQueryProductStorageRequest.setVendor_id(String.valueOf(cpShop.getPlatformSupplierId()));
            //唯品会条码 规则  vip_唯品会平台条码_常态合作编码
            String[] split = sgChannelProduct.getSkuId().split("_");
            AssertUtils.cannot(split.length < 3, "唯品会平台条码:" + sgChannelProduct.getSkuId() + "规则不符合,请检查!");
            vipQueryProductStorageRequest.setSku_id(split[1]);
            //常态合作编码
            vipQueryProductStorageRequest.setCooperation_no(split[2]);
            vipQueryProductStorageRequests.add(vipQueryProductStorageRequest);
        }
        if (CollectionUtils.isEmpty(vipQueryProductStorageRequests)) {
            return;
        }
        List<List<VipQueryProductStorageRequest>> partition = Lists.partition(vipQueryProductStorageRequests,
                pullVipSkuNum);
        //线程池
        /*ThreadPoolExecutor exec = new ThreadPoolExecutor(corePoolSize, maxPoolSize, 1L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(partition.size()),
                new ThreadFactoryBuilder().setNameFormat(THREAD_POOL_NAME).build());*/
        CountDownLatch latch = new CountDownLatch(partition.size());
        List<VipQueryProductStorageInfoResult> storageInfos = new ArrayList<>();
        try {
            List<Future<ValueHolderV14<List<VipQueryProductStorageResult>>>> futures = new ArrayList<>();
            for (List<VipQueryProductStorageRequest> queryProductStorageRequests : partition) {
                Future<ValueHolderV14<List<VipQueryProductStorageResult>>> future =
                        commonExecutorPool.submit(new CallableRpcQueryProductStorage(queryProductStorageRequests, latch));
                futures.add(future);
            }
            latch.await();
            for (Future<ValueHolderV14<List<VipQueryProductStorageResult>>> future : futures) {
                ValueHolderV14<List<VipQueryProductStorageResult>> listValueHolderV14 = future.get();
                if (!listValueHolderV14.isOK()) {
                    log.error(" SgChannelProductStorageContrastService.error.setProductStorageRequest" +
                            ".vipQueryProductStorage failVh={}", listValueHolderV14);
                }
                if (listValueHolderV14.isOK() && CollectionUtils.isNotEmpty(listValueHolderV14.getData())) {
                    List<VipQueryProductStorageResult> storageResults = listValueHolderV14.getData();
                    for (VipQueryProductStorageResult storageResult : storageResults) {
                        if (CollectionUtils.isNotEmpty(storageResult.getInfos())) {
                            storageInfos.addAll(storageResult.getInfos());
                        }
                    }
                }
            }
        } catch (InterruptedException e) {
            log.warn(" 获取唯品会商品库存多线程中断!");
        } catch (ExecutionException e) {
            log.error(" SgChannelProductStorageContrastService.setProductStorageRequest exception_has_occured:{}",
                    Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrow("线程池调用ip唯品会拉取平台商品库存接口失败!");
        } /*finally {
            exec.shutdown();
        }*/
        log.info("SgChannelProductStorageContrastService.setProductStorageRequest vipQueryProductStorageRequests" +
                ".size={},rpcStorageInfos.size={}", vipQueryProductStorageRequests.size(), storageInfos.size());
        //收集插入库存异动计算缓存池数据
        List<VipQueryProductStorageInfoResult> syncStockInfos = new ArrayList<>();
        //收集钉钉预警数据
        List<VipQueryProductStorageInfoResult> dingTalkWarningInfos = new ArrayList<>();
        for (VipQueryProductStorageInfoResult storageInfo : storageInfos) {
            //唯品会平台条码拼装
            String skuId =
                    SgConstants.VIP_CHANNEL_PRODUCT_SKU_TAG + "_" + storageInfo.getSku_id() + "_" + storageInfo.getCooperation_no();
            storageInfo.setSku_id(skuId);
            Long surplus = storageInfo.getSurplus();
            SgBChannelProduct product = skuIdProductMap.get(skuId);

            BigDecimal qtyChannel = Optional.ofNullable(product.getQtyChannel()).orElse(BigDecimal.ZERO);
            //平台库存与系统店铺库存比对结果
            int i = (new BigDecimal(surplus)).compareTo(qtyChannel);
            log.info(" SgChannelProductStorageContrastService.setProductStorageRequest skuId={},compareTo={}",
                    skuId, i);
            //获取店铺库存对比策略
            SgCStockContrastStrategy strategy = shopIdStrategyMap.get(product.getCpCShopId());
            if (i > 0) {
                //如果【库存同步类型】为【1-仅同步平台库存大于系统库存的记录】或者【3-存在差异的记录】，则将记录写入到【库存异动计算缓存池】
                if (SgStockSyncConstants.SYNSTOCK_TYPE_01.equals(strategy.getSynstockType()) || SgStockSyncConstants.SYNSTOCK_TYPE_03.equals(strategy.getSynstockType())) {
                    syncStockInfos.add(storageInfo);
                } else if (SgStockSyncConstants.SYNSTOCK_TYPE_02.equals(strategy.getSynstockType())) {
                    //如果【库存同步类型】为【2-仅同步平台库存小于系统库存的记录】，则通过预警通道发送。
                    dingTalkWarningInfos.add(storageInfo);
                }
            } else if (i < 0) {
                Calendar calendar = Calendar.getInstance();
                int nowHour = calendar.get(Calendar.HOUR_OF_DAY);
                Integer beginHour = strategy.getBeginTime();
                Integer endHour = strategy.getEndTime();
                //【当前系统时间小时数】在【比对起始时间】（包含）和【比对结束时间】（包含）之间
                boolean hourFlag = nowHour >= beginHour && nowHour <= endHour;
                //如果【库存同步类型】为【1-仅同步平台库存大于系统库存的记录】，则通过预警通道发送。
                if (SgStockSyncConstants.SYNSTOCK_TYPE_01.equals(strategy.getSynstockType())) {
                    dingTalkWarningInfos.add(storageInfo);
                }
                //如果【库存同步类型】为【2-仅同步平台库存小于系统库存的记录】或者【3-存在差异的记录】
                if ((SgStockSyncConstants.SYNSTOCK_TYPE_02.equals(strategy.getSynstockType()) || SgStockSyncConstants.SYNSTOCK_TYPE_03.equals(strategy.getSynstockType()))) {
                    //【是否允许同步平台库存小于系统库存】=是
                    if (SgStockSyncConstants.IS_SYNC_STOCK_Y.equals(strategy.getIsSyncStock())) {
                        //【当前系统时间小时数】在【比对起始时间】（包含）和【比对结束时间】（包含）之间，则将记录写入到【库存异动计算缓存池】
                        if (hourFlag) {
                            syncStockInfos.add(storageInfo);
                        } else {
                            //【当前系统时间小时数】不在【比对起始时间】（包含）和【比对结束时间】（包含）之间，则通过预警通道发送
                            dingTalkWarningInfos.add(storageInfo);
                        }
                    } else {
                        //【是否允许同步平台库存小于系统库存】=否,则通过预警通道发送。
                        dingTalkWarningInfos.add(storageInfo);
                    }
                }
            }
        }

        //插入计算缓存池
        if (CollectionUtils.isNotEmpty(syncStockInfos)) {
            syncStock(syncStockInfos, skuIdProductMap, shopHashMap);
        }
        //发送钉钉预警
        if (CollectionUtils.isNotEmpty(dingTalkWarningInfos)) {
            sendDingTalkWarningMessage(dingTalkWarningInfos, shopIdProductMap, shopIdStrategyMap);
        }
        //主键更新平台店铺商品表 平台库存信息
        batchUpdateVipProducts(storageInfos, skuIdProductMap);
    }

    /**
     * 插入库存异动计算缓存池
     *
     * @param storageInfos        下载的平台条码信息集合
     * @param sgChannelProductMap 平台店铺商品表信息(平台条码维度)
     * @param shopHashMap         店铺信息map
     */
    private void syncStock(List<VipQueryProductStorageInfoResult> storageInfos,
                           Map<String, SgBChannelProduct> sgChannelProductMap, Map<Long, CpShop> shopHashMap) {
        log.info("SgChannelProductStorageContrastService.syncStock storageInfos.size={};",
                storageInfos.size());

        SgChannelStorageBufferBatchSaveRequest request = new SgChannelStorageBufferBatchSaveRequest();
        request.setUser(SystemUserResource.getRootUser());
        List<SgChannelStorageBufferSaveRequest> bufferRequests = new ArrayList<>();
        for (VipQueryProductStorageInfoResult storageInfo : storageInfos) {
            SgChannelStorageBufferSaveRequest bufferRequest = new SgChannelStorageBufferSaveRequest();
            SgBChannelProduct product = sgChannelProductMap.get(storageInfo.getSku_id());
            bufferRequest.setWareType(product.getWareType());
            CpShop cpShop = shopHashMap.get(product.getCpCShopId());
            bufferRequest.setCpCPlatformId(cpShop.getCpCPlatformId().intValue());
            bufferRequest.setSourceNo("唯品会平台店铺库存对比" + storageInfo.getSku_id());
            bufferRequest.setDealStatus(SgBChannelStorageBufferExtend.DealStatusEnum.UN_DEAL.getCode());
            bufferRequest.setFixedQty(BigDecimal.ZERO);
            bufferRequest.setFixedQtyFlag(SgConstants.IS_ACTIVE_N);
            bufferRequest.setSkuId(storageInfo.getSku_id());
            bufferRequest.setCpCShopId(product.getCpCShopId());
            bufferRequests.add(bufferRequest);
        }
        request.setBufferSaveRequestList(bufferRequests);
        if (log.isDebugEnabled()) {
            log.debug(" SgChannelProductStorageContrastService.syncStock.saveDataToChannelStorageBuffer " +
                            "request.size={}",
                    JSONObject.toJSONString(request.getBufferSaveRequestList().size()));
        }
        channelStorageBufferCmd.saveDataToChannelStorageBuffer(request);
    }

    /**
     * 发送钉钉告警信息
     *
     * @param storageInfos      下载的平台条码信息集合
     * @param shopIdProductMap  平台店铺商品表信息map(店铺维度)
     * @param shopIdStrategyMap 平台店铺库存对比策略map
     */
    private void sendDingTalkWarningMessage(List<VipQueryProductStorageInfoResult> storageInfos,
                                            Map<Long, List<SgBChannelProduct>> shopIdProductMap,
                                            Map<Long, SgCStockContrastStrategy> shopIdStrategyMap) {

        log.info("SgChannelProductStorageContrastService.sendDingTalkWarningMessage storageInfos.size={};" +
                        "shopIdStrategyMap.size={};",
                storageInfos.size(), shopIdStrategyMap.size());

        Set<Map.Entry<Long, List<SgBChannelProduct>>> entries = shopIdProductMap.entrySet();
        Map<String, VipQueryProductStorageInfoResult> storageInfoMap = new HashMap<>(16);
        storageInfos.forEach(info -> storageInfoMap.put(info.getSku_id(), info));
        for (Map.Entry<Long, List<SgBChannelProduct>> entry : entries) {
            List<JSONObject> jsonObjectList = new ArrayList<>();

            Long shopId = entry.getKey();
            List<SgBChannelProduct> productList = entry.getValue();
            //对比策略
            SgCStockContrastStrategy contrastStrategy = shopIdStrategyMap.get(shopId);
            if (Objects.isNull(contrastStrategy) || StringUtils.isBlank(contrastStrategy.getWarningUrl())) {
                continue;
            }
            for (SgBChannelProduct channelProduct : productList) {
                String skuId = channelProduct.getSkuId();
                VipQueryProductStorageInfoResult storageInfo = storageInfoMap.get(skuId);
                if (Objects.isNull(storageInfo)) {
                    continue;
                }
                JSONObject jo = new JSONObject();
                jo.put("平台店铺", channelProduct.getCpCShopTitle());
                jo.put("商品", channelProduct.getPsCProEcode());
                jo.put("条码", channelProduct.getPsCSkuEcode());
                jo.put("平台商品ID", channelProduct.getNumiid());
                jo.put("平台条码ID", skuId);
                jo.put("平台库存", storageInfo.getSurplus());
                jo.put("系统店铺库存", channelProduct.getQtyChannel());
                jo.put("同步时间", storageInfo.getTimeStamp());
                jsonObjectList.add(jo);
            }
            SgDataMonitorInfo sgDataMonitorInfo = new SgDataMonitorInfo();
            sgDataMonitorInfo.setMonitorName("平台店铺库存对比中台库存异常");
            sgDataMonitorInfo.setDiffInfo(jsonObjectList);
            sgDataMonitorInfo.setCount(storageInfos.size());
            String warningUrl = contrastStrategy.getWarningUrl();
            String[] dingUrlSplit = warningUrl.split(";");
            List<SgDataMonitorInfo> sgDataMonitorInfos = new ArrayList<>();
            sgDataMonitorInfos.add(sgDataMonitorInfo);
            if (log.isDebugEnabled()) {
                log.debug(" SgChannelProductStorageContrastService.sendDingTalkWarningMessage.sendNotice " +
                                "sgDataMonitorInfos.size={}",
                        sgDataMonitorInfos.size());
            }
            try {
                for (String url : dingUrlSplit) {
                    sgSendNoticeUtils.sendNotice(null, url, null, sgDataMonitorInfos);
                }
            } catch (Exception e) {
                log.error(" SgChannelProductStorageContrastService.sendDingTalkWarningMessage.sendNotice. " +
                                "exception_has_occured:{}",
                        Throwables.getStackTraceAsString(e));
            }

        }
    }

    /**
     * 更新同步成功的平台条码
     *
     * @param storageInfos    平台接口返回的条码数据
     * @param skuIdProductMap 对比的系统店铺数据
     */
    private void batchUpdateVipProducts(List<VipQueryProductStorageInfoResult> storageInfos, Map<String,
            SgBChannelProduct> skuIdProductMap) {
        log.info("SgChannelProductStorageContrastService.batchUpdateVipProducts storageInfoSkus={};" +
                        "skuIdProductMap.size={};",
                JSONObject.toJSONString(storageInfos.stream().map(VipQueryProductStorageInfoResult::getSku_id).collect(Collectors.toList())),
               skuIdProductMap.size());

        for (VipQueryProductStorageInfoResult storageInfo : storageInfos) {
            SgBChannelProduct channelProduct = skuIdProductMap.get(storageInfo.getSku_id());
            if (Objects.isNull(channelProduct)) {
                continue;
            }
            //平台库存数量
            BigDecimal qtyStorage = Objects.isNull(storageInfo.getSurplus()) ? BigDecimal.ZERO :
                    new BigDecimal(storageInfo.getSurplus());
            channelProduct.setDownloadStatus(DOWNLOAD_SUCCESS);
            channelProduct.setQtyStorage(qtyStorage);
            channelProduct.setQtyDifferences(channelProduct.getQtyChannel().subtract(new BigDecimal(storageInfo.getSurplus())));
            channelProduct.setTransTime(new Date());
            channelProduct.setModifieddate(new Date());
        }
        try {
            if (CollectionUtils.isNotEmpty(skuIdProductMap.values())) {
                //筛选同步成功的平台条码
                List<SgBChannelProduct> updateProducts =
                        skuIdProductMap.values().stream().filter(s -> DOWNLOAD_SUCCESS.equals(s.getDownloadStatus())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(updateProducts)) {
                    List<List<SgBChannelProduct>> partition = Lists.partition(updateProducts,
                            SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
                    for (List<SgBChannelProduct> sgChannelProducts : partition) {
                        sgChannelProductDownloadService.updateDownloadQtyStorage(sgChannelProducts);
                    }
                }
            }
        } catch (Exception e) {
            log.error(" SgChannelProductStorageContrastService.batchUpdateVipProducts. exception_has_occured:{}",
                    Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrow("唯品会库存对比更新平台店铺商品表异常!异常原因:" + e.getMessage());
        }
    }

    /**
     * adb查询唯品会时效订单前N'小时前的商品连接
     *
     * @param shopIds   店铺id集合
     * @param queryTime 时间
     * @return List<String> 时效订单明细69码集合
     */
    @Async(value = "asyncExecutorPool")
    @TargetDataSource(name = "adb")
    public Future<List<String>> queryAdbOrderGarCodeList(List<Long> shopIds, String queryTime) {
        log.info(" SgChannelProductStorageContrastService.queryAdbOrderGarCodeList.begin");
        Long adbQueryStart = System.currentTimeMillis();
        List<String> timeOrderItemCodes = sgStockContrastStrategyMapper.queryAdbOrderProducts(adbSchema, shopIds,
                queryTime);
        Long adbQueryEnd = System.currentTimeMillis();
        log.info(" SgChannelProductStorageContrastService.queryAdbOrderGarCodeList.timeOrderItemCodes={}," +
                        "queryTime={}ms",
                JSON.toJSONString(timeOrderItemCodes), adbQueryEnd - adbQueryStart);
        return AsyncResult.forValue(timeOrderItemCodes);
    }

    /**
     * 开启线程类
     */
    class CallableRpcQueryProductStorage implements Callable<ValueHolderV14<List<VipQueryProductStorageResult>>> {
        /**
         * 需要查询的数据
         */
        private List<VipQueryProductStorageRequest> requests;
        CountDownLatch latch;

        public CallableRpcQueryProductStorage(List<VipQueryProductStorageRequest> requests, CountDownLatch latch) {
            this.requests = requests;
            this.latch = latch;
        }

        @Override
        public ValueHolderV14<List<VipQueryProductStorageResult>> call() {
            try {
                return cloudHubQueryProductStorageCmd.vipQueryProductStorage(requests);
            } catch (Exception e) {
                log.error(" VipQueryProductStorageCmdImpl.CallableQueryProductStorageWithResult.call. " +
                        "exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            } finally {
                latch.countDown();
            }
            return null;
        }
    }

    /**
     * 分页查询平台店铺商品表数据
     * @param shopIds 店铺集合
     * @param skuIds 商品集合
     * @param saleStatus 销售状态
     * @return List<SgBChannelProduct>
     */
    private List<SgBChannelProduct> queryChannelProduct(List<Long> shopIds, List<Long> skuIds,
                                                        List<Integer> saleStatus) {
        int count = sgChannelProductMapper.selectCount(new LambdaQueryWrapper<SgBChannelProduct>()
                .in(CollectionUtils.isNotEmpty(shopIds), SgBChannelProduct::getCpCShopId, shopIds)
                .in(CollectionUtils.isNotEmpty(skuIds), SgBChannelProduct::getPsCSkuId, skuIds)
                .in(CollectionUtils.isNotEmpty(saleStatus), SgBChannelProduct::getSaleStatus, saleStatus)
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        AssertUtils.cannot(count == 0, "唯品会库存对比取消,平台店铺商品表暂无符合条件的数据!");
        log.info(" SgChannelProductStorageContrastService.queryChannelProduct count={}", count);
        List<SgBChannelProduct> queryResults = Lists.newArrayList();
        if (CollectionUtils.isEmpty(skuIds)){
            int pageSize = SgConstants.SG_COMMON_QUERY_SIZE;
            int page = count / pageSize;
            if (count % pageSize != 0) {
                page++;
            }
            for (int i = 0; i < page; i++) {
                //获取需要库存比对的平台店铺下的条码
                List<SgBChannelProduct> sgChannelProducts =
                        sgChannelProductMapper.queryProductStorageContrastSkuIdInfos(shopIds, skuIds, saleStatus, pageSize,
                                i * pageSize);
                queryResults.addAll(sgChannelProducts);
            }
        }else{
            List<List<Long>> partition = Lists.partition(skuIds, SgConstants.SG_COMMON_QUERY_SIZE);
            for (List<Long> skuList : partition) {
                //获取需要库存比对的平台店铺下的条码
                List<SgBChannelProduct> sgChannelProducts =
                        sgChannelProductMapper.queryProductStorageContrastSkuIdInfos(shopIds, skuList, saleStatus,
                                null,null);
                queryResults.addAll(sgChannelProducts);
            }
        }
        log.info(" SgChannelProductStorageContrastService.queryChannelProduct queryResults.size={}", queryResults.size());
        return queryResults;
    }
}