package com.burgeon.r3.sg.stocksync.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.r3.inf.services.oms.SgOmsShopStorageQueryService;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelStorageSyncGradientStrategyMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelStorageSyncProGradientStrategyMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgOmsStockQuantityResult;
import com.burgeon.r3.sg.core.model.table.channel.gradient.SgCChannelStorageSyncGradientStrategy;
import com.burgeon.r3.sg.core.model.table.channel.gradient.SgCChannelStorageSyncProGradientStrategy;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.oms.OcBOrder;
import com.burgeon.r3.sg.core.model.table.oms.SgBChannelStorageBuffer;
import com.burgeon.r3.sg.core.model.tableExtend.BaseExtend;
import com.burgeon.r3.sg.core.model.tableExtend.SgBChannelStorageBufferExtend;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.DateUtils;
import com.burgeon.r3.sg.core.utils.LogUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShopStorageQueryRequest;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsShopStorageQueryItemResult;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsShopStorageQueryResult;
import com.burgeon.r3.sg.share.model.dto.SgCSkuDataDto;
import com.burgeon.r3.sg.stocksync.common.OmsConstantsIF;
import com.burgeon.r3.sg.stocksync.mapper.SgBChannelStorageBufferMapper;
import com.burgeon.r3.sg.stocksync.mapper.SgBChannelStorageBufferProcedureMapper;
import com.burgeon.r3.sg.stocksync.mapper.SgBChannelSynstockBatchnoMapper;
import com.burgeon.r3.sg.stocksync.mapper.oms.OcBOrderMapper;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageOmsCalcRequest;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageOmsChangeRequest;
import com.burgeon.r3.sg.stocksync.model.result.SgChannelStorageOmsCalcResult;
import com.burgeon.r3.sg.stocksync.model.result.SgChannelStorageOmsChangeResult;
import com.burgeon.r3.sg.stocksync.model.table.SgBChannelSynstockBatchno;
import com.burgeon.r3.sg.stocksync.thread.ChannelCalTask;
import com.burgeon.r3.sg.stocksync.thread.ChannelCalTaskExecutorService;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 全渠道库存计算服务
 *
 * @Author: chenb
 * @Date: 2019/4/22 13:15
 */
@Component
@Slf4j
public class SgChannelStorageOmsCalcService {

    @Autowired
    private ChannelCalTaskExecutorService ChannelCalTaskExecutorService;

    @Autowired
    private SgCChannelStorageSyncGradientStrategyMapper storageSyncGradientStrategyMapper;

    @Autowired
    private SgCChannelStorageSyncProGradientStrategyMapper sgChannelStorageSyncProGradientStrategyMapper;

    @Autowired
    private SgBChannelProductMapper sgBChannelProductMapper;

    @Value("${sg.channel.cal.batch.size:100}")
    private int batchSize;
    @Value("${sg.channel.cal.batch.query.out.stock.size:800}")
    private int batchQuerySize;

    @Autowired
    private SgChannelStorageOmsService sgChannelStorageOmsService;

    @Value("${r3.sg.channel.adb.datasync:r3_rc_datasync}")
    private String adbSchema;

    @Autowired
    private SgBChannelStorageBufferProcedureMapper storageBufferProcedureMapper;

    @Value("${r3.sg.oms.channelStorageCalcBatchNum}")
    private Integer channelStorageCalcBatchNum;

    @Autowired
    private SgChannelStorageOmsBufferService bufferService;

    //    @Autowired
//    private R3MqSendHelper r3MqSendHelper;
    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private SgBChannelStorageBufferMapper bufferMapper;

    @Autowired
    private SgBChannelSynstockBatchnoMapper sgBChannelSynstockBatchnoMapper;

    @Deprecated
    @Value("${sg.sync.channel.corePoolSize.core:5}")
    private int corePoolSize;

    @Deprecated
    @Value("${sg.sync.channel.maxPoolSize.core:10}")
    private int maxPoolSize;

    @Deprecated
    @Value("${sg.sync.channel.keepAliveTime.core:3600000}")
    private Long keepAliveTime;

    @Resource
    private ThreadPoolTaskExecutor commonExecutorPool;
    @Resource
    private ThreadPoolTaskExecutor asyncExecutorPool;

    @Value("${sg.sync.channel.qtyOutStock.flag:true}")
    private Boolean qtyOutStockFlag;

    @Value("${sg.sync.channel.qtyOutStock.time.out:5}")
    private Integer timeOut;


    private static final String THREAD_POOL_NAME = "ManualCalcSyncChannelProduct%d";
    //根据需要创建线程数
    /*private static final ExecutorService executorService = Executors.newSingleThreadExecutor();*/

    public ValueHolderV14<Boolean> executeTask(JSONObject params) {
        ValueHolderV14<Boolean> result = new ValueHolderV14<>();

        Integer channelStoragePoolType = Integer.valueOf(params.getString("channelStoragePoolType"));

        if (log.isDebugEnabled()) {
            log.debug("ChannelStorageCalcTask beginning receive channelStoragePoolType:{};", channelStoragePoolType);
        }

        List<Long> shopIds = Lists.newArrayList();
        List<Long> platformIds = Lists.newArrayList();
        List<Long> excludePlatformIds = Lists.newArrayList();
        JSONArray shopArr = params.getJSONArray("shopArr");
        JSONArray platformArr = params.getJSONArray("platformArr");
        JSONArray excludePlatformArr = params.getJSONArray("excludePlatformArr");
        Integer calcBatchNum = params.getInteger("calcBatchNum");
        if (CollectionUtils.isNotEmpty(shopArr) && (CollectionUtils.isNotEmpty(platformArr) || CollectionUtils.isNotEmpty(excludePlatformArr))) {
            result.setMessage("暂不支持店铺与平台同时执行任务!");
            result.setData(false);
            return result;
        }
        if (CollectionUtils.isNotEmpty(shopArr)) {
            shopIds = JSONArray.parseArray(JSONObject.toJSONString(shopArr), Long.class);
        }
        if (CollectionUtils.isNotEmpty(platformArr)) {
            platformIds = JSONArray.parseArray(JSONObject.toJSONString(platformArr), Long.class);
        }
        if (CollectionUtils.isNotEmpty(excludePlatformArr)) {
            excludePlatformIds = JSONArray.parseArray(JSONObject.toJSONString(excludePlatformArr), Long.class);
        }
        if (calcBatchNum == null) {
            calcBatchNum = channelStorageCalcBatchNum;
        }

        /*
            根据配置的查询行数channelStorageCalcBatchNum, 按ID正序查询【渠道库存计算缓存池表】X行处理状态为未处理的数据
         */
        SgBChannelStorageBufferExtend sgBChannelStorageBufferExtend = new SgBChannelStorageBufferExtend();
        sgBChannelStorageBufferExtend.setListSize(calcBatchNum);
        sgBChannelStorageBufferExtend.setDealStatus(SgBChannelStorageBufferExtend.DealStatusEnum.UN_DEAL.getCode());
        ValueHolderV14<List<SgBChannelStorageBuffer>> bufferResult = bufferService.getChannelStorageOmsBuffer(sgBChannelStorageBufferExtend, shopIds, platformIds, excludePlatformIds, channelStoragePoolType);
        if (!bufferResult.isOK()) {
            result.setData(false);
            result.setMessage(bufferResult.getMessage());
            return result;
        } else if (CollectionUtils.isEmpty(bufferResult.getData())) {
            result.setMessage("无待计算的任务");
            result.setData(true);
            return result;
        }
        List<SgBChannelStorageBuffer> sgBChannelStorageBufferList = bufferResult.getData();

        /*
        设置缓存池类型
         */
        sgBChannelStorageBufferList.forEach(e -> e.setChannelStoragePoolType(channelStoragePoolType));
        /*
            批量更新数据行的处理状态为2
         */
        sgBChannelStorageBufferExtend = new SgBChannelStorageBufferExtend();
        BaseExtend baseExtend = new BaseExtend();
        baseExtend.setIdList(sgBChannelStorageBufferList.stream().map(SgBChannelStorageBuffer::getId).collect(Collectors.toList()));
        sgBChannelStorageBufferExtend.setBaseExtend(baseExtend);
        sgBChannelStorageBufferExtend.setUpdateDealStatus(SgBChannelStorageBufferExtend.DealStatusEnum.DEALING.getCode());
        bufferService.updateChannelStorageOmsBufferStatus(sgBChannelStorageBufferExtend, channelStoragePoolType);

        if (qtyOutStockFlag) {
            try {
                SgChannelStorageOmsCalcService bean = ApplicationContextHandle.getBean(SgChannelStorageOmsCalcService.class);

                CompletableFuture<ValueHolderV14> completableFuture =
                        CompletableFuture.supplyAsync(() -> bean.eliminateOutOfStockOrders(sgBChannelStorageBufferList), asyncExecutorPool);
                ValueHolderV14 queryStockOut = completableFuture.get(timeOut, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("查询缺货数异常：{}", Throwables.getStackTraceAsString(e));
            }
        }


        /*
         * 开始循环调用渠道库存计算服务
         */
        // 再次去除重复记录
        List<ChannelCalTask> callableTasks = new ArrayList<>();
        List<List<SgBChannelStorageBuffer>> channelStorageBufferTask = Lists.partition(sgBChannelStorageBufferList, batchSize);
        if (CollectionUtils.isNotEmpty(channelStorageBufferTask)) {
            for (List<SgBChannelStorageBuffer> channelStorageBuffers : channelStorageBufferTask) {
                if (CollectionUtils.isNotEmpty(channelStorageBuffers)) {
                    callableTasks.add(new ChannelCalTask(channelStorageBuffers, defaultProducerSend, bufferMapper));
                } else {
                    log.error("[ChannelCalTask] init ChannelCalTask is filed.because channelStorageBuffer is empty.");
                }
            }
        }
        try {

            List<SgBChannelStorageBuffer> errorList = ChannelCalTaskExecutorService.invokeAll(callableTasks);
            result.setData(true);
            result.setMessage("[" + DateUtils.formatSync8(new Date(), DateUtils.PATTERN_DATETIME) + "]定时计算全渠道库存计算结果:" +
                    "库存计算缓存池获取记录" + sgBChannelStorageBufferList.size() +
                    "条,执行成功" + (sgBChannelStorageBufferList.size() - errorList.size()) +
                    "条,执行失败 " + errorList.size() + "条");
        } catch (InterruptedException e) {
            log.error("Multiple channel cal task was interruptedException! channelSyncProduct Callable tasks = [{}] exception_has_occured:{}",
                    callableTasks.stream().map(ChannelCalTask::getChannelStorageBuffers).collect(Collectors.toList()).toString(),
                    Throwables.getStackTraceAsString(e));
        }
        return result;
    }


    /**
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgChannelStorageOmsCalcResult> calcChannelStorageOms(SgChannelStorageOmsCalcRequest request) {
        String skuId = request.getSkuId();
        if (log.isDebugEnabled()) {
            log.debug("skuid:{},Start SgChannelStorageOmsCalcService.calcChannelStorageOms.params:{};", skuId, JSONObject.toJSONString(request));
        }

        // 解决手动两个页面获取不到user 的问题
        if (request.getLoginUser() == null) {
            String ownername = request.getOwnername();
            List<String> nameList = new ArrayList<>();
            nameList.add(ownername);
            Map<String, User> userMap = CommonCacheValUtils.getUserListByNameList(nameList);
            User user = userMap.get(ownername);
            if (user == null) {
                request.setLoginUser(R3SystemUserResource.getSystemRootUser());
            } else {
                request.setLoginUser(user);
            }
        }

        ValueHolderV14<SgChannelStorageOmsCalcResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        if (request.check()) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(request.getMessageKey() + "参数校验不通过");
            return holder;
        }
        /**
         根据渠道ID和渠道条码ID查询【渠道商品表】，获取对应的唯一sku 根据 渠道ID+中台skuid 查询渠道条码ID
         */
        SgBChannelProduct channelProduct = sgBChannelProductMapper.selectOne(new QueryWrapper<SgBChannelProduct>().lambda()
                .eq(SgBChannelProduct::getSkuId, request.getSkuId())
                .eq(SgBChannelProduct::getCpCShopId, request.getCpCShopId())
                .eq(SgBChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (channelProduct == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(request.getMessageKey() + "对应平台商品档案记录不存在!");
            log.error("skuid:{},对应平台商品档案记录不存在!", skuId);
            return holder;
        }

        StringBuilder strategyMessage = new StringBuilder("平台skuid[" + request.getSkuId() + "]开始计算渠道库存\n");

        BigDecimal channelQty = getChannelQty(request, channelProduct, holder);

        if (channelQty != null) {
            if (log.isDebugEnabled()) {
                log.debug("skuid:{},channelQty:{},开始更新/新增渠道库存服务", skuId, channelQty);
            }

            if (!holder.isOK()) {
                return holder;
            }

            BigDecimal qtyOutStock = BigDecimal.ZERO;
            if (qtyOutStockFlag) {
                try {
                    CusRedisTemplate<String, Object> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
                    Object o = redisTemplate.opsForValue().get(SgConstants.REDIS_KEY_CHANNEL_PRODUCT_QTY_OUT + channelProduct.getCpCShopId() + SgConstants.SG_CONNECTOR_MARKS_4 + channelProduct.getSkuId());

                    log.info("SgChannelStorageOmsCalcService.channelQty.eliminateOutOfStock:{}", JSONObject.toJSONString(o));
                    if (o != null) {
                        String qtyStr = (String) o;
                        qtyOutStock = new BigDecimal(qtyStr);
                    }

                } catch (Exception e) {
                    log.error("SgChannelStorageOmsCalcService.eliminateOutOfStockOrders error :{}", Throwables.getStackTraceAsString(e));
                }
            }
            log.info("SgChannelStorageOmsCalcService.channelQty.eliminateOutOfStockOrders:{}, {}, {}", channelProduct.getSkuId(), qtyOutStock, channelQty);

            channelQty = channelQty.subtract(qtyOutStock);

            // 计算渠道到平台库存比例
            if (SgConstantsIF.SYNC_TYPE_POOL_ALL_CITY.equals(request.getSyncType())) {
                BigDecimal syncRatio = Optional.ofNullable(channelProduct.getSyncRatio()).orElse(OmsConstantsIF.PERCENT);
                channelQty = syncRatio.multiply(channelQty).divide(OmsConstantsIF.PERCENT, 0, RoundingMode.DOWN);
            }

            if (!SgConstants.IS_ACTIVE_Y.equals(request.getFixedQtyFlag())) {
                //【20211025/CYJ添加逻辑】根据平台店铺+条码编码或商品编码[ 根据平台条码信息去查询【平台店铺商品表】记录，获取条码编码或商品编码，依次匹配]查询是否存在可用的【平台商品库存同步梯度策略】;
                // 如果不存在，则根据平台店铺+商品编码[ 根据平台条码信息去查询【平台店铺商品表】记录，获取商品编码]查询是否存在可用的【平台库存同步梯度策略】（根据商品编码查询商品档案的商品渠道+商品等级是否符合）
                try {
                    channelQty = execStrategyByProductAndShop(channelProduct, channelQty);
                } catch (Exception e) {
                    log.error("SgChannelStorageOmsCalcService.execStrategyByProductAndShop error:{}", Throwables.getStackTraceAsString(e));
                }
            }

            SgChannelStorageOmsChangeRequest sgChannelStorageOmsChangeRequest = new SgChannelStorageOmsChangeRequest();
            sgChannelStorageOmsChangeRequest.setCpCShopId(request.getCpCShopId());
            sgChannelStorageOmsChangeRequest.setSkuId(request.getSkuId());
            sgChannelStorageOmsChangeRequest.setSkuSpec(channelProduct.getSkuSpec());
            // 这里插入的是渠道库存
            sgChannelStorageOmsChangeRequest.setQtyStorage(channelQty);
            Integer operationType = OmsConstantsIF.QTY_OPERATION_TYPE_ALL;
            if (SgConstantsIF.SYNC_TYPE_POOL_ADD_CITY.equals(request.getSyncType())
                    || SgConstantsIF.SYNC_TYPE_POOL_ADD_CITY_HAND.equals(request.getSyncType())) {
                operationType = OmsConstantsIF.QTY_OPERATION_TYPE_ADD;
            }
            sgChannelStorageOmsChangeRequest.setOperationType(operationType);
            sgChannelStorageOmsChangeRequest.setSourceCpCShopId(request.getSourceCpCShopId());
            sgChannelStorageOmsChangeRequest.setSourceNo(request.getSourceNo());
            // mq发送时间
            sgChannelStorageOmsChangeRequest.setMqSendTime(request.getMqSendTime());
            // mq消费时间
            sgChannelStorageOmsChangeRequest.setMqConsumerTime(request.getMqConsumerTime());
            sgChannelStorageOmsChangeRequest.setSyncType(request.getSyncType());
            sgChannelStorageOmsChangeRequest.setQtyOutStock(qtyOutStock);

            sgChannelStorageOmsChangeRequest.setStrategyMessage(strategyMessage);
            sgChannelStorageOmsChangeRequest.setBatchNo(request.getBatchNo());
            sgChannelStorageOmsChangeRequest.setLoginUser(request.getLoginUser());
            ValueHolderV14<SgChannelStorageOmsChangeResult> changeResult = sgChannelStorageOmsService.changeStorage(sgChannelStorageOmsChangeRequest);
            SgChannelStorageOmsCalcResult sgChannelStorageOmsCalcResult = new SgChannelStorageOmsCalcResult();
            holder.setCode(changeResult.getCode());
            holder.setMessage(changeResult.getMessage());
            BeanUtils.copyProperties(request, sgChannelStorageOmsCalcResult);
            holder.setData(sgChannelStorageOmsCalcResult);
            holder.getData().setSyncNum(sgChannelStorageOmsChangeRequest.getQtyStorage());
        }
        return holder;
    }

    /**
     * @param request:
     * @param channelProduct:
     * @param holder:
     * @Description: 获取渠道库存
     * @Author: hwy
     * @Date: 2021/7/15 19:51
     * @return: java.math.BigDecimal
     **/
    private BigDecimal getChannelQty(SgChannelStorageOmsCalcRequest request, SgBChannelProduct channelProduct, ValueHolderV14 holder) {
        if (SgConstants.IS_ACTIVE_Y.equals(request.getFixedQtyFlag())) {
            return request.getFixedQty() == null ? BigDecimal.ZERO : request.getFixedQty();
        }
//        // 查询该条码是否配置渠道有效的数量同步库存策略
//        //查询店铺配销仓数量同步设置信息
//        SgCChannelQtyStrategyMapper channelQtyStrategyMapper = ApplicationContextHandle.getBean(SgCChannelQtyStrategyMapper.class);
//        SgCChanelQtyStrategyQueryInfoRequest queryInfoRequest = new SgCChanelQtyStrategyQueryInfoRequest();
//        queryInfoRequest.setCpCShopId(channelProduct.getCpCShopId());
////        Date currDate = new Date();
////        queryInfoRequest.setBeginTime(currDate);
////        queryInfoRequest.setEndTime(currDate);
        List<String> skuIds = new ArrayList<>();
        skuIds.add(channelProduct.getSkuId());
//        queryInfoRequest.setSkuIds(skuIds);
//        List<SgCChanelQtyStrategyQueryInfoResult> qtyStrategyInfo = channelQtyStrategyMapper.selectStrategyInfo(queryInfoRequest);
        //1.查询库存
        SgOmsShopStorageQueryService storageQueryService = ApplicationContextHandle.getBean(SgOmsShopStorageQueryService.class);
        SgOmsShopStorageQueryRequest storageQueryRequest = new SgOmsShopStorageQueryRequest();
        storageQueryRequest.setCpCShopId(request.getCpCShopId());
//        // 存在数量同步库存策略 以平台条码查询固定数量库存
////        if (CollectionUtils.isNotEmpty(qtyStrategyInfo)) {
//            // 不存在数量同步库存策略 以系统条码查询库存
//        } else {
        storageQueryRequest.setSkuIdList(skuIds);
//        List<Long> psCSkuIds = new ArrayList<>();
//        psCSkuIds.add(channelProduct.getPsCSkuId());
//        storageQueryRequest.setPsCSkuIdList(psCSkuIds);
//        }
        storageQueryRequest.setCpCShopId(request.getCpCShopId());
        ValueHolderV14<SgOmsShopStorageQueryResult> storageQueryResult = storageQueryService.queryOmsShopStorage(storageQueryRequest, R3SystemUserResource.getSystemRootUser());
        if (!storageQueryResult.isOK()) {
            log.error("平台店铺库存计算服务 查询库存失败:{}", storageQueryResult.getMessage());
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(request.getMessageKey() + "平台店铺库存计算服务 查询库存失败");
            return null;
        }

        SgOmsShopStorageQueryResult storageResult = storageQueryResult.getData();
        if (storageResult == null) {
            return BigDecimal.ZERO;
        }
        Map<String, SgOmsShopStorageQueryItemResult> skuStorageResult = storageResult.getSkuStorageResult();
        if (MapUtils.isEmpty(skuStorageResult) || !skuStorageResult.containsKey(channelProduct.getSkuId())) {
            return BigDecimal.ZERO;
        }
        SgOmsShopStorageQueryItemResult sgOmsShopStorageQueryItemResult = skuStorageResult.get(channelProduct.getSkuId());
        Map<Long, BigDecimal> shareStorageInfo = sgOmsShopStorageQueryItemResult.getShareStorageInfo();
        if (MapUtils.isEmpty(shareStorageInfo)) {
            return BigDecimal.ZERO;
        }
        BigDecimal calcQty = shareStorageInfo.values().stream().reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal qtySafety = channelProduct.getQtySafety() == null ? BigDecimal.ZERO : channelProduct.getQtySafety();

        return calcQty.compareTo(qtySafety) >= 0 ? calcQty.subtract(qtySafety) : BigDecimal.ZERO;
    }

    /**
     * 检查是否是爱库存
     *
     * @param request
     * @param channelProduct
     * @return
     */
    private ValueHolderV14<SgChannelStorageOmsCalcResult> checkIsAkc(SgChannelStorageOmsCalcRequest
                                                                             request, SgBChannelProduct channelProduct) {
        if (log.isDebugEnabled()) {
            log.debug("SgChannelStorageOmsCalcService#checkIsAkc:{}", JSONObject.toJSONString(channelProduct));
        }
        SgBChannelProductMapper sgBChannelProductMapper = ApplicationContextHandle.getBean(SgBChannelProductMapper.class);
        ValueHolderV14<SgChannelStorageOmsCalcResult> result = new ValueHolderV14();
        String platformEcode = channelProduct.getCpCPlatformEcode();
        if (StringUtils.isEmpty(platformEcode)) {
            result.setCode(ResultCode.SUCCESS);
            result.setMessage(request.getMessageKey() + ":暂无平台信息!");
            return result;
        }
        if (!"65".equalsIgnoreCase(platformEcode)) {
            result.setCode(ResultCode.SUCCESS);
            result.setMessage(request.getMessageKey() + ":当前平台无需判断!");
            return result;
        }
        if (StringUtils.isEmpty(channelProduct.getReserveVarchar05())) {
            result.setCode(ResultCode.SUCCESS);
            result.setMessage(request.getMessageKey() + ":暂无活动结束时间!");
            return result;
        }
        //判断当前时间是否小于活结束时间，小于则不需要同步
        Date endTime = DateUtils.parseSync8(channelProduct.getReserveVarchar05(), DateUtils.PATTERN_DATETIME);
        Date currentDate = new Date();
        if (log.isDebugEnabled()) {
            log.debug("SgChannelStorageOmsCalcService#checkIsAk#currentDate#befor:" + currentDate.getTime() + ",endTime:" + endTime.getTime());
        }
        if (currentDate.getTime() > endTime.getTime()) {
            if (log.isDebugEnabled()) {
                log.debug("SgChannelStorageOmsCalcService#checkIsAk#currentDate#after:" + currentDate.getTime() + ",endTime:" + endTime.getTime());
            }
            //平台店铺商品表该条数据【无需同步】标记为是
            SgBChannelProduct updateChannelProduct = new SgBChannelProduct();
            updateChannelProduct.setId(channelProduct.getId());
            //更新无需同步
            User user = R3SystemUserResource.getSystemRootUser();
            updateChannelProduct.setReserveVarchar07("1");
            updateChannelProduct.setModifieddate(new Date());
            updateChannelProduct.setModifierid(user.getId().longValue());
            updateChannelProduct.setModifierename(user.getEname());
            sgBChannelProductMapper.updateById(updateChannelProduct);
            result.setCode(ResultCode.FAIL);
            result.setMessage(request.getMessageKey() + ":当前时间大于活动结束时间!");
            return result;
        }
        result.setCode(ResultCode.SUCCESS);
        result.setMessage(request.getMessageKey() + ":数据可同步!");
        return result;
    }

    private List<List<SgBChannelStorageBuffer>> subTask(List<SgBChannelStorageBuffer> buffers) {
        List<List<SgBChannelStorageBuffer>> batchTasks = new ArrayList<>();
        int batchNumber = (buffers.size() / batchSize) + 1;
        for (int i = 0; i < batchNumber; i++) {
            int startIndex = i == 0 ? i : i * batchSize;
            int endSize = i == batchNumber - 1 ? buffers.size() : batchSize * (i + 1);
            batchTasks.add(buffers.subList(startIndex, endSize));
        }
        return batchTasks;
    }

    /*@Async("initChannelStorageAsyncThread")*/
    @Async("asyncExecutorPool")
    public void initChannelStorageOms(List<SgChannelStorageOmsCalcRequest> requestList, String batchno) {

        if (log.isDebugEnabled()) {
            log.debug("Start SgChannelStorageOmsCalcService.initChannelStorageOms batchno:{};", batchno);
        }

        long startTime = System.currentTimeMillis();

        for (SgChannelStorageOmsCalcRequest sgChannelStorageOmsCalcRequest : requestList) {
            try {
                calcChannelStorageOms(sgChannelStorageOmsCalcRequest);
            } catch (Exception e) {
                log.error("SgChannelStorageOmsCalcService.initChannelStorageOms.error Request={} error:{}",
                        JSONObject.toJSONString(sgChannelStorageOmsCalcRequest), Throwables.getStackTraceAsString(e));
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("Finish SgChannelStorageOmsCalcService.initChannelStorageOms batchno:{},consume:{};", batchno, (System.currentTimeMillis() - startTime));
        }

    }


    /**
     * 批量出入缓存池
     *
     * @param requestList
     */
    public Integer insertSgBChannelStorageBufferProduct(List<SgBChannelStorageBuffer> requestList) {
        return storageBufferProcedureMapper.manaualBatchInsertStorageBufferOnConfilict(requestList);
    }

    /**
     * 获取该批次有多少进入了缓存池
     *
     * @param batchNo
     * @return
     */
    public Integer queryCoumtByBatchno(String batchNo) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("batch_no", batchNo);
        return storageBufferProcedureMapper.selectCount(queryWrapper);
    }


    /**
     * 按筛选条件手工同步到页面处理
     *
     * @param requestList
     * @param batchno
     */
    public void initChannelOmsThread(List<SgBChannelStorageBuffer> requestList, String batchno) {
        if (log.isDebugEnabled()) {
            log.debug("StartSgChannelStorageOmsCalcService.initChannelOmsThreadbatchno:{};", batchno);
        }
        //创建自定义线程池
        /*ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNameFormat(THREAD_POOL_NAME).build();
        ThreadPoolExecutor exec = new ThreadPoolExecutor(corePoolSize, maxPoolSize, keepAliveTime, TimeUnit.SECONDS, new LinkedTransferQueue<>(), namedThreadFactory);*/
        List<List<SgBChannelStorageBuffer>> partition = Lists.partition(requestList, corePoolSize);

        List<Future<Integer>> futureList = Lists.newArrayList();
        for (List<SgBChannelStorageBuffer> pageList : partition) {
            if (CollectionUtils.isNotEmpty(pageList)) {
                try {
                    Callable<Integer> task = () -> insertSgBChannelStorageBufferProduct(pageList);
                    Future<Integer> submit = commonExecutorPool.submit(task);
                    futureList.add(submit);
//                    Runnable task = () -> {
//                        long threadStartTime = System.currentTimeMillis();
//                        return insertSgBChannelStorageBufferProduct(pageList);
//                        if (log.isDebugEnabled()) {
//                            log.debug("FinishSgChannelStorageOmsService.initChannelOmsThreadname:{},batchno:{},consume:{};",
//                                    Thread.currentThread().getName(), batchno, (System.currentTimeMillis() - threadStartTime));
//                        }
//                    };
//                    exec.execute(task);
                } catch (Exception e) {
                    log.error("SgChannelStorageOmsCalcService.initChannelOmsThread. error:{};",
                            Throwables.getStackTraceAsString(e));
                }
            }

        }
        Integer count = 0;
        try {
            for (Future<Integer> future : futureList) {
                count += future.get();
            }
        } catch (InterruptedException e) {
            log.error("FinishSgChannelStorageOmsService.initChannelOmsThreadname exception_has_occured:{}", Throwables.getStackTraceAsString(e));
        } catch (ExecutionException e) {
            log.error("FinishSgChannelStorageOmsService.initChannelOmsThreadname exception_has_occured:{}", Throwables.getStackTraceAsString(e));
        }
        // 关闭线程池
        /*exec.shutdown();*/
        //更新的数量和查询的数据不相等 说明有重复的数据
        //查询该批次下有多少进入了缓存池
        if (log.isDebugEnabled()) {
            log.debug("查询该批次下有多少进入了缓存池" + count + "productListsize=" + requestList.size());
        }
        if (count != requestList.size()) {
            //解决修改死锁
            //sgBChannelSynstockBatchnoMapper.updateByBatchno(batchno, "重复");
            List<SgBChannelSynstockBatchno> sgBChannelSynstockBatchnos = sgBChannelSynstockBatchnoMapper.selectList(Wrappers.<SgBChannelSynstockBatchno>lambdaQuery()
                    .eq(SgBChannelSynstockBatchno::getBatchno, batchno));
            if (CollectionUtils.isNotEmpty(sgBChannelSynstockBatchnos)) {
                List<Long> ids = sgBChannelSynstockBatchnos.stream().map(SgBChannelSynstockBatchno::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(ids)) {
                    Collections.sort(ids);
                    sgBChannelSynstockBatchnoMapper.batchUpdateByRemark(ids, "重复");
                }
            }
        }

    }


    /**
     * 增加线程,后续可以合并处理
     *
     * @param requestList
     * @param batchno
     */
    @Deprecated
    public void initChannelStorageOmsThread(List<SgChannelStorageOmsCalcRequest> requestList, String batchno) {

        if (log.isDebugEnabled()) {
            log.debug("Start SgChannelStorageOmsCalcService.initChannelStorageOmsThread batchno:{};", batchno);
        }
        //创建自定义线程池
        /*ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNameFormat(THREAD_POOL_NAME).build();
        ThreadPoolExecutor exec = new ThreadPoolExecutor(corePoolSize, maxPoolSize, keepAliveTime, TimeUnit.SECONDS, new LinkedTransferQueue<>(), namedThreadFactory);*/

        List<List<SgChannelStorageOmsCalcRequest>> partition = Lists.partition(requestList, corePoolSize);
        for (List<SgChannelStorageOmsCalcRequest> pageList : partition) {
            if (CollectionUtils.isNotEmpty(pageList)) {
                try {
                    Runnable task = () -> {
                        long threadStartTime = System.currentTimeMillis();
                        for (SgChannelStorageOmsCalcRequest sgChannelStorageOmsCalcRequest : pageList) {
                            calcChannelStorageOms(sgChannelStorageOmsCalcRequest);
                        }
                        if (log.isDebugEnabled()) {
                            log.debug("Finish SgChannelStorageOmsService.calcChannelStorageOms thread-name:{},batchno:{},consume:{};",
                                    Thread.currentThread().getName(), batchno, (System.currentTimeMillis() - threadStartTime));
                        }
                    };
                    commonExecutorPool.execute(task);
                } catch (Exception e) {
                    log.error("SgChannelStorageOmsCalcService.initChannelStorageOmsThread. error:{}",
                            Throwables.getStackTraceAsString(e));
                }
            }
        }
        // 关闭线程池
        /*exec.shutdown();*/
    }

    /**
     * 根据平台店铺+条码编码或商品编码，依次匹配可用的【平台商品库存同步梯度策略】
     *
     * @param channelProduct 平台商品
     * @param qty            需要同步到平台的库存。
     * @return 需要同步到平台的库存
     */
    private BigDecimal execStrategyByProductAndShop(SgBChannelProduct channelProduct, BigDecimal qty) {
        log.info("Start SgChannelStorageOmsCalcService.execStrategyByProductAndShop begin");
        AssertUtils.cannot(channelProduct == null, "根据平台条码信息去查询[平台商品库存同步梯度策略],平台商品不能为空");
        if (log.isDebugEnabled()) {
            log.debug(" Start SgChannelStorageOmsCalcService.execStrategyByProductAndShop channelProduct={}," +
                    "qty={},skuId={}", JSONObject.toJSONString(channelProduct), qty, channelProduct.getSkuId());
        }
        SgCSkuDataDto sgSkuDataDto = new SgCSkuDataDto();
        CommonCacheValUtils.setSkuInfo(channelProduct.getPsCSkuId(), null, sgSkuDataDto);
        //查询可用的梯度策略
        SgCChannelStorageSyncProGradientStrategy sgChannelStorageSyncProGradientStrategy =
                sgChannelStorageSyncProGradientStrategyMapper.selectOne(Wrappers.<SgCChannelStorageSyncProGradientStrategy>query().lambda()
                        .and(warpper -> warpper.eq(SgCChannelStorageSyncProGradientStrategy::getPsCSkuId,
                                channelProduct.getPsCSkuId()).or().eq(sgSkuDataDto.getPsCProId() != null,
                                SgCChannelStorageSyncProGradientStrategy::getPsCProId, sgSkuDataDto.getPsCProId()))
                        .eq(SgCChannelStorageSyncProGradientStrategy::getCpCShopId, channelProduct.getCpCShopId())
                        .le(SgCChannelStorageSyncProGradientStrategy::getQtyBegin, qty)
                        .ge(SgCChannelStorageSyncProGradientStrategy::getQtyEnd, qty)
                        .eq(SgCChannelStorageSyncProGradientStrategy::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (sgChannelStorageSyncProGradientStrategy == null) {
            return execStrategyByProductAndLevel(qty, channelProduct);
        }
        //如果标准库存值为空，则用C1值作为标准库存值
        BigDecimal qtyStandrd =
                Optional.ofNullable(sgChannelStorageSyncProGradientStrategy.getQtyStandard()).orElse(qty);
        return qtyStandrd.multiply(sgChannelStorageSyncProGradientStrategy.getRatio()).divide(new BigDecimal(100)).compareTo(BigDecimal.ONE) < 0 ?
                BigDecimal.ZERO : qtyStandrd.multiply(sgChannelStorageSyncProGradientStrategy.getRatio()).divide(new BigDecimal(100));
    }

    /**
     * 平台库存同步梯度策略 == 根据商品编码查询商品档案的商品渠道+商品等级
     *
     * @param channelQty     计算出来的店铺库存
     * @param channelProduct 对应平台店铺商品表
     */
    public BigDecimal execStrategyByProductAndLevel(BigDecimal channelQty, SgBChannelProduct channelProduct) {
        LogUtils.printLog(" SgChannelStorageOmsCalcService execStrategyByProductAndLevel in param channelQty = {} , channelProduct = {}", channelQty, JSONObject.toJSONString(channelProduct));
        AssertUtils.notNull(channelProduct, "根据商品编码查询商品档案的商品渠道+商品等级库存同步策略 平台商品不能为空");
        //根据商品id查询对应的商品信息
        List<PsCPro> psCPros = CommonCacheValUtils.queryProListByIds(Arrays.asList(channelProduct.getPsCProId()));
        if (CollectionUtils.isEmpty(psCPros)) {
            LogUtils.printLog("根据商品编码查询商品档案的商品渠道+商品等级库存同步策略 商品不存在");
            AssertUtils.logAndThrow("根据商品编码查询商品档案的商品渠道+商品等级库存同步策略 商品不存在");
        }
        PsCPro psCPro = psCPros.get(0);
        //查询可用的梯度策略 查询条件有问题，新商品类没有这些字段，后期再说
        SgCChannelStorageSyncGradientStrategy sgCChannelStorageSyncGradientStrategy = new SgCChannelStorageSyncGradientStrategy();
//        SgCChannelStorageSyncGradientStrategy sgCChannelStorageSyncGradientStrategy = storageSyncGradientStrategyMapper.selectOne(Wrappers.<SgCChannelStorageSyncGradientStrategy>query().lambda()
////                .eq(SgCChannelStorageSyncGradientStrategy::getChannelName, psCPro.getChannelName())
////                        .eq(SgCChannelStorageSyncGradientStrategy::getCpCShopId, channelProduct.getCpCShopId())
////                .eq(SgCChannelStorageSyncGradientStrategy::getProductLevel, psCPro.getProductLevel())
////                        .ge(SgCChannelStorageSyncGradientStrategy::getQtyEnd, channelQty)
////                        .le(SgCChannelStorageSyncGradientStrategy::getQtyBegin, channelQty)
////                        .eq(SgCChannelStorageSyncGradientStrategy::getIsactive, SgConstants.IS_ACTIVE_Y)
//        );
        if (ObjectUtils.isEmpty(sgCChannelStorageSyncGradientStrategy)) {
            LogUtils.printLog(" SgChannelStorageOmsCalcService execStrategyByProductAndLevel query strategy not exist");
            //策略不存在，
            return channelQty;
        }
        LogUtils.printLog(" SgChannelStorageOmsCalcService execStrategyByProductAndLevel query strategy results = {}", JSONObject.toJSONString(sgCChannelStorageSyncGradientStrategy));
        //先比较起始 和 结束量
        BigDecimal qtyBegin = sgCChannelStorageSyncGradientStrategy.getQtyBegin() == null ? BigDecimal.ZERO : sgCChannelStorageSyncGradientStrategy.getQtyBegin();
        //如果 结束值为null,代表无穷大
        BigDecimal qtyEnd = sgCChannelStorageSyncGradientStrategy.getQtyEnd();
        //店铺库存
        channelQty = channelQty == null ? BigDecimal.ZERO : channelQty;
        boolean beginFlag = channelQty.compareTo(qtyBegin) >= 0;
        boolean endFlag = false;
        if (qtyEnd == null) {
            endFlag = true;
        } else {
            endFlag = channelQty.compareTo(qtyEnd) <= 0;
        }
        if (beginFlag && endFlag) {
            //获取标准库存进行同步
            BigDecimal ratio = sgCChannelStorageSyncGradientStrategy.getRatio();
            BigDecimal qtyStandard = sgCChannelStorageSyncGradientStrategy.getQtyStandard();
            // 舍弃小数位
            if (qtyStandard != null && !(qtyStandard.compareTo(BigDecimal.ZERO) == 0)) {

                channelQty = qtyStandard.multiply(ratio).divide(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_DOWN);
            } else {
                channelQty = channelQty.multiply(ratio).divide(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_DOWN);
            }
        }

        return channelQty;
    }

    /**
     * 2)增加系统参数： 库存同步查询缺货订单时间范围（天），默认值：7，当前值：7
     * 3)统计缺货商品，获得结果集A（店铺、条码、平台条码、缺货数量）
     * 系统时间-“付款时间”<=7、且“状态”=待分配、缺货的【零售发货单】下明细中“取消状态”=否的记录，取值明细中的“数量”
     * 系统时间-“下单时间”<=7、且“单据状态”=待占单、占单中的【唯品时效订单】明细记录，取值明细中的“数量”
     * 系统时间-“下单时间”<=7、且“单据状态”=寻仓中、缺货的【唯品时效订单】明细记录，取值明细中的“缺货数量”
     * 系统时间-“创建时间” <=7、且“状态”=未确认、缺货、占用中的【JIT配货单】明细的记录，取值明细中的“拣货数”
     * 4)如果集合A中
     * 平台条码不为空，缺货数：X
     * 平台条码为空，相同条码合并，缺货数Y
     *
     * @param sgBChannelStorageBufferList bufferList
     * @return 缺货数量
     */
    @TargetDataSource(name = "adb")
    public ValueHolderV14 eliminateOutOfStockOrders(List<SgBChannelStorageBuffer> sgBChannelStorageBufferList) {

        log.info("start SgChannelStorageOmsCalcService.eliminateOutOfStockOrders size:{}", sgBChannelStorageBufferList.size());

        long start = System.currentTimeMillis();

        // 取出系统参数
        String param = AdParamUtil.getParam("r3_sg_stock_sync_date");
        //默认7天
        int orderDay = 7;

        if (StringUtils.isNotEmpty(param)) {
            orderDay = Integer.parseInt(param);
        }
        // 排除缺货数量
        OcBOrderMapper orderMapper = ApplicationContextHandle.getBean(OcBOrderMapper.class);
        LocalDateTime condition = LocalDateTime.now().minusDays(orderDay);
        List<String> removeKeys = new ArrayList<>();

        Map<Long, Integer> shopPlatformMap = sgBChannelStorageBufferList.stream().collect(Collectors.toMap(SgBChannelStorageBuffer::getCpCShopId, SgBChannelStorageBuffer::getCpCPlatformId, (k1, k2) -> k1));

        // 分批次查询数据库
        Map<Long, List<SgBChannelStorageBuffer>> groupList = sgBChannelStorageBufferList.stream().collect(Collectors.groupingBy(SgBChannelStorageBuffer::getCpCShopId));

        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();

        groupList.forEach((shopId, bufferList) -> {
            Integer platform = shopPlatformMap.get(shopId);
            if (null == platform) {
                return;
            }

            boolean isVip = SgConstants.PLATFORMTYPE_WEIPINHUI_JIT.equals(platform.toString()) || SgConstants.PLATFORMTYPE_WEIPINHUI_JITX.equals(platform.toString());

            List<SgOmsStockQuantityResult> resultList = new ArrayList<>();

            List<Long> psCSkuIdList = bufferList.stream().map(SgBChannelStorageBuffer::getPsCSkuId).distinct().collect(Collectors.toList());
            List<List<Long>> psCSkuListList = Lists.partition(psCSkuIdList, batchQuerySize);
            psCSkuListList.forEach(x -> resultList.addAll(orderMapper.queryOutOrderByShop(condition, x, shopId, adbSchema)));

            if (log.isDebugEnabled()) {
                log.debug("order mapper result is ： {}", JSONObject.toJSONString(resultList));
            }

            Map<String, BigDecimal> qtyMap = resultList.stream().collect(
                    Collectors.toMap(x -> {
                        if (isVip) {
                            return x.getPsCSkuId() + SgConstants.SG_CONNECTOR_MARKS_4 + SgConstants.VIP_CHANNEL_PRODUCT_SKU_TAG + SgConstants.SG_CONNECTOR_MARKS_6 + x.getSkuid() + SgConstants.SG_CONNECTOR_MARKS_6 + x.getCooperationNo();
                        } else {
                            return x.getPsCSkuId() + SgConstants.SG_CONNECTOR_MARKS_4 + x.getSkuid();
                        }
                    }, SgOmsStockQuantityResult::getQty, (k1, k2) -> k1));

            bufferList.forEach(x -> {
                // 唯品会平台
                BigDecimal qty = qtyMap.get(x.getPsCSkuId() + SgConstants.SG_CONNECTOR_MARKS_4 + x.getSkuId());
                if (!isVip) {
                    BigDecimal noSkuQty = qtyMap.get(x.getPsCSkuId() + SgConstants.SG_CONNECTOR_MARKS_4 + SgConstants.STRING_TAG_NULL);
                    if (noSkuQty != null) {
                        qty = qty == null ? noSkuQty : qty.add(noSkuQty);
                    }
                }

                if (qty == null) {
                    // 如果为null，则用0 覆盖redis 中的结果  ---- 改为删除key
                    removeKeys.add(SgConstants.REDIS_KEY_CHANNEL_PRODUCT_QTY_OUT + x.getCpCShopId() + SgConstants.SG_CONNECTOR_MARKS_4 + x.getSkuId());
                } else {
                    redisTemplate.opsForValue().set(
                            SgConstants.REDIS_KEY_CHANNEL_PRODUCT_QTY_OUT + x.getCpCShopId() + SgConstants.SG_CONNECTOR_MARKS_4 + x.getSkuId(),
                            qty.toString(), 1, TimeUnit.DAYS);
                }
            });
        });

        redisTemplate.delete(removeKeys);

        long end = System.currentTimeMillis();

        log.info("start SgChannelStorageOmsCalcService.eliminateOutOfStockOrders time:{}, size:{}", end - start, sgBChannelStorageBufferList.size());

        return new ValueHolderV14<>(ResultCode.SUCCESS, "查询成功");
    }

    /**
     * 根据零售发货单 单据编号 查找零售发货单,返回单据状态
     *
     * @param billNoList 单据编号
     * @return ValueHolderV14
     */
    @TargetDataSource(name = "adb")
    public ValueHolderV14<Map<String, Integer>> queryOrderByBillNo(List<String> billNoList) {
        ValueHolderV14<Map<String, Integer>> v14 = new ValueHolderV14<>();
        OcBOrderMapper orderMapper = ApplicationContextHandle.getBean(OcBOrderMapper.class);
        if (CollectionUtils.isEmpty(billNoList)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("入参数为空！");
            return v14;

        }

        log.info("SgChannelStorageOmsCalcService.queryOrderByBillNo billNoList.size = {}", billNoList.size());

        List<List<String>> pageList = StorageUtils.
                getPageList(billNoList, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
        List<OcBOrder> ocOrderList = new ArrayList<>();

        for (List<String> billNos : pageList) {
            List<OcBOrder> ocOrders = orderMapper.queryOrderByBillNo(adbSchema, billNos);
            if (CollectionUtils.isNotEmpty(ocOrders)) {
                ocOrderList.addAll(ocOrders);
            }
        }

        if (CollectionUtils.isNotEmpty(ocOrderList)) {
            Map<String, Integer> statusMap = ocOrderList.stream().collect(Collectors.toMap(OcBOrder::getBillNo,
                    OcBOrder::getOrderStatus, (v1, v2) -> v1));
            v14.setData(statusMap);
        }

        v14.setCode(ResultCode.SUCCESS);
        v14.setMessage("查询成功！");
        return v14;
    }
}