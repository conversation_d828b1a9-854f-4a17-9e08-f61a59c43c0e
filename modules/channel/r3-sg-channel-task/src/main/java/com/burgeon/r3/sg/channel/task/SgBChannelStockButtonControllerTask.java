package com.burgeon.r3.sg.channel.task;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.channel.services.product.SgBChannelStockButtonControllerService;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description 平台店铺库存管理 页面 中按钮的时间控制 定时
 * <AUTHOR>
 * @Date 2021/11/26 16:26
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgBChannelStockButtonControllerTask implements IR3Task {

    @Autowired
    private SgBChannelStockButtonControllerService sgBChannelStockButtonControllerService;

    @Override
    @XxlJob("SgBChannelStockButtonControllerTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        log.info("Start SgBChannelStockButtonControllerTask");

        RunTaskResult runTaskResult = new RunTaskResult();
        try {
            ValueHolderV14 execute = sgBChannelStockButtonControllerService.execute();
            if (execute.isOK()) {
                runTaskResult.setSuccess(true);
                runTaskResult.setMessage("执行成功！");
            } else {
                runTaskResult.setSuccess(false);
                runTaskResult.setMessage("执行失败！");
            }
        } catch (Exception e) {
            runTaskResult.setSuccess(false);
            runTaskResult.setMessage("执行失败！" + e.getMessage());
        }
        return runTaskResult;
    }
}
