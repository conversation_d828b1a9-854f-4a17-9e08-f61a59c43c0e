package com.burgeon.r3.sg.channel.model.request.strategy;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: hwy
 * @time: 2021/6/19 17:47
 */
@Data
public class SgCChannelSkuStrategyQueryInfoRequest implements Serializable {

    private static final long serialVersionUID = 2871015023652751557L;

    /**
     * @Description: 店铺id
     **/
    private Long cpCShopId;
    /**
     * @Description: 平台条码
     **/
    private List<String> skuIds;
    /**
     * @Description: 系统条码
     **/
    private List<Long> psCSkuIds;
    /**
     * @Description: 配销仓
     **/
    private List<Long> sgCSaStoreIds;
    /**
     * @Description: 共享池
     **/
    private List<Long> sgCSharePoolIds;
    /**
     * @Description: 聚合仓
     **/
    private List<Long> sgCShareStoreIds;
    /**
     * @Description: 开始时间
     **/
    private Date beginTime;
    /**
     * @Description: 结束时间
     **/
    private Date endTime;

}