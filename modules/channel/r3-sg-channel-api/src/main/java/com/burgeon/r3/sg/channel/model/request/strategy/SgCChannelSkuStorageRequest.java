package com.burgeon.r3.sg.channel.model.request.strategy;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: sjli
 * @time: 2021/9/16 10:21
 */
@Data
public class SgCChannelSkuStorageRequest implements Serializable {

    private static final long serialVersionUID = -2820222478065787749L;

    private List<SgCChannelSkuStorageQueryRequest> requestList;
    
    private Long userId;
}
