package com.burgeon.r3.sg.channel.validate.gradient;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.model.request.record.SgCommRecordData;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelStorageSyncGradientStrategyMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelStorageSyncGradientStrategyDTO;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelStorageSyncProGradientStrategyDTO;
import com.burgeon.r3.sg.channel.services.strategy.SgCChannelStorageSyncGradientStrategyStockSyncService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.gradient.SgCChannelStorageSyncGradientStrategy;
import com.burgeon.r3.sg.core.model.table.channel.gradient.SgCChannelStorageSyncProGradientStrategy;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * 平台库存同步梯度策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SgCChannelStorageSyncGradientStrategySaveValidator extends BaseSingleValidator<SgCChannelStorageSyncGradientStrategyDTO> {

    @Autowired
    private SgCChannelStorageSyncGradientStrategyStockSyncService service;

    @Autowired
    private SgCChannelStorageSyncGradientStrategyMapper mapper;

    @Override
    public String getValidatorMsgName() {
        return "平台库存同步梯度策略保存";
    }

    @Override
    public Class<?> getValidatorClass() {
        return SgCChannelStorageSyncGradientStrategySaveValidator.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCChannelStorageSyncGradientStrategyDTO mainObject, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过", loginUser.getLocale()));

        if (Objects.nonNull(mainObject.getQtyBegin()) && mainObject.getQtyBegin().compareTo(BigDecimal.ZERO) < 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("库存开始量不能小于0！"));
            return vh;
        }

        if (Objects.nonNull(mainObject.getQtyStandard()) && mainObject.getQtyStandard().compareTo(BigDecimal.ZERO) < 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("标准库存量不可为负数！"));
            return vh;
        }

        if (Objects.nonNull(mainObject.getRatio()) && (mainObject.getRatio().compareTo(BigDecimal.ZERO) < 0 || mainObject.getRatio().compareTo(BigDecimal.valueOf(100)) > 0)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("比例需要在0-100 之间！"));
            return vh;
        }

        SgCChannelStorageSyncGradientStrategyDTO oldData = getOrignalData();

        Long shopId = Objects.isNull(mainObject.getCpCShopId()) ? oldData.getCpCShopId() : mainObject.getCpCShopId();
        String channelName = StringUtils.isEmpty(mainObject.getChannelName()) ? oldData.getChannelName() : mainObject.getChannelName();
        String productLevel = StringUtils.isEmpty(mainObject.getProductLevel()) ? oldData.getProductLevel() : mainObject.getProductLevel();
        BigDecimal qtyBegin = Objects.isNull(mainObject.getQtyBegin()) ? oldData.getQtyBegin() : mainObject.getQtyBegin();
        BigDecimal qtyEnd = Objects.isNull(mainObject.getQtyEnd()) ? oldData.getQtyEnd() : mainObject.getQtyEnd();

        if (Objects.nonNull(qtyEnd) && qtyEnd.compareTo(qtyBegin) <= 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("库存开始量大于库存结束量，不允许保存！"));
            return vh;
        }

        LambdaQueryWrapper<SgCChannelStorageSyncGradientStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SgCChannelStorageSyncGradientStrategy::getCpCShopId, shopId);
        wrapper.eq(SgCChannelStorageSyncGradientStrategy::getChannelName, channelName);
        wrapper.eq(SgCChannelStorageSyncGradientStrategy::getProductLevel, productLevel);
        wrapper.ne(Objects.nonNull(mainObject.getId()), SgCChannelStorageSyncGradientStrategy::getId, mainObject.getId());
        wrapper.eq(SgCChannelStorageSyncGradientStrategy::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgCChannelStorageSyncGradientStrategy> list = mapper.selectList(wrapper);

        if (!CollectionUtils.isEmpty(list)) {
            long count = list.stream().filter(x -> (Objects.isNull(qtyEnd) && Objects.isNull(x.getQtyEnd()))
                    || (qtyBegin.compareTo(x.getQtyBegin()) >= 0 && (Objects.isNull(x.getQtyEnd()) || x.getQtyEnd().compareTo(qtyBegin) >= 0))
                    || (Objects.nonNull(qtyEnd) && qtyEnd.compareTo(x.getQtyBegin()) >= 0 && (Objects.isNull(x.getQtyEnd()) || qtyEnd.compareTo(x.getQtyEnd()) < 0))).count();
            if (count > 0) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("库存起始量与其他记录重复，不允许保存！"));
                return vh;
            }
        }

        return vh;
    }
}
