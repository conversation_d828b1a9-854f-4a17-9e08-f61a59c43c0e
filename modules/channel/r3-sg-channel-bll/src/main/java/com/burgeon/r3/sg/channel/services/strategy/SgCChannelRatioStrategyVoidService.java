package com.burgeon.r3.sg.channel.services.strategy;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyItemMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.channel.ratiostrategy.SgCChannelRatioStrategy;
import com.burgeon.r3.sg.core.model.table.channel.ratiostrategy.SgCChannelRatioStrategyItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/6/16 19:22
 */

@Slf4j
@Component
public class SgCChannelRatioStrategyVoidService {

    @Autowired
    private SgCChannelRatioStrategyMapper mapper;

    @Autowired
    private SgCChannelRatioStrategyItemMapper itemMapper;


    public ValueHolder strategyVoid(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        SgCChannelRatioStrategyVoidService service = ApplicationContextHandle.getBean(SgCChannelRatioStrategyVoidService.class);
        return R3ParamUtils.convertV14WithResult(service.strategyVoid(request));
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> strategyVoid(SgR3BaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBStoFreezeVoidService.voidFreeze.ReceiveParams:request{}"
                    , JSONObject.toJSONString(request));
        }
        Long objId = request.getObjId();
        User loginUser = request.getLoginUser();
        String lockKey = SgConstants.SG_C_CHANNEL_RATIO_STRATEGY + ":" + objId;
        SgRedisLockUtils.lock(lockKey);
        try {
            SgCChannelRatioStrategy ratioStrategy = new SgCChannelRatioStrategy();
            StorageUtils.setBModelDefalutDataByUpdate(ratioStrategy, loginUser);
            ratioStrategy.setDelerId(loginUser.getId().longValue());
            ratioStrategy.setDelerName(loginUser.getName());
            ratioStrategy.setDelerEname(loginUser.getEname());
            ratioStrategy.setDelTime(new Date());
            ratioStrategy.setIsactive(SgConstants.IS_ACTIVE_N);
            ratioStrategy.setId(objId);
            mapper.updateById(ratioStrategy);

            List<SgCChannelRatioStrategyItem> items = itemMapper.selectList(new LambdaQueryWrapper<SgCChannelRatioStrategyItem>()
                    .eq(SgCChannelRatioStrategyItem::getSgCChannelRatioStrategyId, objId)
                    .eq(SgCChannelRatioStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (CollectionUtils.isNotEmpty(items)) {
                List<Long> itemIds = items.stream().map(SgCChannelRatioStrategyItem::getId).collect(Collectors.toList());
                SgCChannelRatioStrategyItem strategyItem = new SgCChannelRatioStrategyItem();
                strategyItem.setIsactive(SgConstants.IS_ACTIVE_N);
                itemMapper.update(strategyItem, new LambdaQueryWrapper<SgCChannelRatioStrategyItem>()
                        .in(SgCChannelRatioStrategyItem::getId, itemIds));
            }
        } catch (Exception e) {
            AssertUtils.logAndThrowException(e, loginUser.getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKey, log, this.getClass().getName());
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "作废成功!");
    }
}
