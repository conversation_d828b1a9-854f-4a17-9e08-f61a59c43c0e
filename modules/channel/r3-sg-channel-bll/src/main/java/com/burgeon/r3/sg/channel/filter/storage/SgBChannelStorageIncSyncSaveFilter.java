package com.burgeon.r3.sg.channel.filter.storage;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgBChannelStorageIncSyncItemMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgBChannelStorageIncSyncMapper;
import com.burgeon.r3.sg.channel.model.dto.storage.SgBChannelStorageIncSyncDto;
import com.burgeon.r3.sg.channel.model.dto.storage.SgBChannelStorageIncSyncItemDto;
import com.burgeon.r3.sg.channel.utils.SgChannelProductUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBChannelStorageIncSync;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBChannelStorageIncSyncItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleItemFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 平台库存增量同步Filter
 * <AUTHOR>
 * @Date 2021/6/23 14:23
 * @Version 1.0
 **/

@Slf4j
@Component
public class SgBChannelStorageIncSyncSaveFilter extends BaseSingleItemFilter<SgBChannelStorageIncSyncDto, SgBChannelStorageIncSyncItemDto> {

    @Autowired
    private SgBChannelStorageIncSyncItemMapper storageIncSyncItemMapper;

    @Autowired
    private SgBChannelStorageIncSyncMapper storageIncSyncMapper;

    @Autowired
    private SgBChannelProductMapper productMapper;


    @Override
    public String getFilterMsgName() {
        return "平台库存增量同步保存";
    }

    @Override
    public Class<?> getFilterClass() {
        return SgBChannelStorageIncSyncSaveFilter.class;
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgBChannelStorageIncSyncDto mainObject, User loginUser) {
        if (mainObject.getId() <= 0L) {
            mainObject.setStatus(SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_UN);
            CpShop shop = CommonCacheValUtils.getShopInfo(mainObject.getCpCShopId());
            mainObject.setCpCShopEcode(shop.getEcode());
            mainObject.setCpCShopTitle(shop.getCpCShopTitle());
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("before main table success"));
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgBChannelStorageIncSyncDto mainObject, User loginUser) {
        return null;
    }


    @Override
    public ValueHolderV14 execBeforeSubTable(SgBChannelStorageIncSyncDto mainObject, List<SgBChannelStorageIncSyncItemDto> subObjectList, User loginUser) {
        if (mainObject.getId() > 0L) {
            SgBChannelStorageIncSync storageIncSyncOld = storageIncSyncMapper.selectById(mainObject.getId());
            mainObject.setCpCShopId(storageIncSyncOld.getCpCShopId());
        }
        List<SgBChannelStorageIncSyncItemDto> storageIncSyncItemDtoList = new ArrayList<>();
        Map<String, SgBChannelStorageIncSyncItemDto> storageIncSyncItemDtoMap = new HashMap<>(16);
        for (SgBChannelStorageIncSyncItemDto storageIncSyncItemDto : subObjectList) {
            storageIncSyncItemDto.setSgBChannelStorageIncSyncId(mainObject.getId());

            //平台条码id
            if (StringUtils.isNotEmpty(storageIncSyncItemDto.getSkuId())) {
                //根据平台店铺ID查询条码信息
                String skuId = storageIncSyncItemDto.getSkuId();
                SgBChannelProduct product = SgChannelProductUtils.getChannelProduct(skuId);
                if (log.isDebugEnabled()) {
                    log.debug("Start SgBChannelStorageIncSyncSaveFilter3.execBeforeSubTable:product={};", JSONObject.toJSONString(product));
                }
                if (product == null) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("未获取到平台条码信息！"));
                }
                storageIncSyncItemDto.setPsCSkuId(product.getPsCSkuId());
                storageIncSyncItemDto.setPsCSkuEcode(product.getPsCSkuEcode());
                storageIncSyncItemDto.setGbcode(product.getGbcode());
                storageIncSyncItemDto.setPsCBrandId(product.getPsCBrandId());
                storageIncSyncItemDto.setPsCProId(product.getPsCProId());
                storageIncSyncItemDto.setPsCProEcode(product.getPsCProEcode());
                storageIncSyncItemDto.setPsCProEname(product.getPsCProEname());
                storageIncSyncItemDto.setPsCSpec1Id(product.getPsCSpec1Id());
                storageIncSyncItemDto.setPsCSpec1Ecode(product.getPsCSpec1Ecode());
                storageIncSyncItemDto.setPsCSpec1Ename(product.getPsCSpec1Ename());
                storageIncSyncItemDto.setPsCSpec2Id(product.getPsCSpec2Id());
                storageIncSyncItemDto.setPsCSpec2Ecode(product.getPsCSpec2Ecode());
                storageIncSyncItemDto.setPsCSpec2Ename(product.getPsCSpec2Ename());
                SgBChannelStorageIncSyncItem storageIncSyncItemsBySkuId = storageIncSyncItemMapper.selectOne(new LambdaQueryWrapper<SgBChannelStorageIncSyncItem>()
                        .eq(SgBChannelStorageIncSyncItem::getSkuId, storageIncSyncItemDto.getSkuId())
                        .eq(SgBChannelStorageIncSyncItem::getSgBChannelStorageIncSyncId, mainObject.getId()));
                if (storageIncSyncItemsBySkuId == null) {
                    storageIncSyncItemDto.setStatus(SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_UN);
                } else {
                    storageIncSyncItemDto.setQty(storageIncSyncItemsBySkuId.getQty().add(storageIncSyncItemDto.getQty()));
                    storageIncSyncItemDto.setId(storageIncSyncItemsBySkuId.getId());
                }
            }
            //平台商品id

            if (storageIncSyncItemDto.getNumiid() != null) {
                if (log.isDebugEnabled()) {
                    log.debug("Start SgBChannelStorageIncSyncSaveFilter3.execBeforeSubTable:getNumiid={};", storageIncSyncItemDto.getNumiid());
                }

                String numiid = storageIncSyncItemDto.getNumiid();
                List<SgBChannelProduct> channelProductList = SgChannelProductUtils.getChannelProduct(numiid, SgConstants.IS_ACTIVE_Y);
                if (CollectionUtils.isEmpty(channelProductList)) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("未获取到平台商品信息！"));
                }
                for (SgBChannelProduct sgBChannelProduct : channelProductList) {
                    SgBChannelStorageIncSyncItemDto sgBChannelStorageIncSyncItemDto = new SgBChannelStorageIncSyncItemDto();
                    BeanUtils.copyProperties(storageIncSyncItemDto, sgBChannelStorageIncSyncItemDto);
                    sgBChannelStorageIncSyncItemDto.setNumiid(numiid);
                    sgBChannelStorageIncSyncItemDto.setSkuId(sgBChannelProduct.getSkuId());
                    sgBChannelStorageIncSyncItemDto.setPsCSkuId(sgBChannelProduct.getPsCSkuId());
                    sgBChannelStorageIncSyncItemDto.setPsCSkuEcode(sgBChannelProduct.getPsCSkuEcode());
                    sgBChannelStorageIncSyncItemDto.setGbcode(sgBChannelProduct.getGbcode());
                    sgBChannelStorageIncSyncItemDto.setPsCBrandId(sgBChannelProduct.getPsCBrandId());
                    sgBChannelStorageIncSyncItemDto.setPsCProId(sgBChannelProduct.getPsCProId());
                    sgBChannelStorageIncSyncItemDto.setPsCProEcode(sgBChannelProduct.getPsCProEcode());
                    sgBChannelStorageIncSyncItemDto.setPsCProEname(sgBChannelProduct.getPsCProEname());
                    sgBChannelStorageIncSyncItemDto.setPsCSpec1Id(sgBChannelProduct.getPsCSpec1Id());
                    sgBChannelStorageIncSyncItemDto.setPsCSpec1Ecode(sgBChannelProduct.getPsCSpec1Ecode());
                    sgBChannelStorageIncSyncItemDto.setPsCSpec1Ename(sgBChannelProduct.getPsCSpec1Ename());
                    sgBChannelStorageIncSyncItemDto.setPsCSpec2Id(sgBChannelProduct.getPsCSpec2Id());
                    sgBChannelStorageIncSyncItemDto.setPsCSpec2Ecode(sgBChannelProduct.getPsCSpec2Ecode());
                    sgBChannelStorageIncSyncItemDto.setPsCSpec2Ename(sgBChannelProduct.getPsCSpec2Ename());

                    SgBChannelStorageIncSyncItem storageIncSyncItemsBySkuId = storageIncSyncItemMapper.selectOne(new LambdaQueryWrapper<SgBChannelStorageIncSyncItem>()
                            .eq(SgBChannelStorageIncSyncItem::getSkuId, sgBChannelStorageIncSyncItemDto.getSkuId())
                            .eq(SgBChannelStorageIncSyncItem::getSgBChannelStorageIncSyncId, mainObject.getId()));
                    if (storageIncSyncItemsBySkuId == null) {
                        sgBChannelStorageIncSyncItemDto.setStatus(SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_UN);
                    } else {
                        sgBChannelStorageIncSyncItemDto.setQty(storageIncSyncItemsBySkuId.getQty().add(storageIncSyncItemDto.getQty()));
                        sgBChannelStorageIncSyncItemDto.setId(storageIncSyncItemsBySkuId.getId());
                    }
                    storageIncSyncItemDtoList.add(sgBChannelStorageIncSyncItemDto);
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelStorageIncSyncSaveFilter3.execBeforeSubTable:storageIncSyncItemDtoList={};", JSONObject.toJSONString(storageIncSyncItemDtoList));
        }
        //平台商品id
        if (CollectionUtils.isNotEmpty(storageIncSyncItemDtoList)) {
            subObjectList.clear();
            List<SgBChannelStorageIncSyncItemDto> storageIncSyncItemList = new ArrayList<>();
            storageIncSyncItemList.addAll(storageIncSyncItemDtoList);
            storageIncSyncItemDtoMap = storageIncSyncItemList.stream()
                    .collect(Collectors.toMap(SgBChannelStorageIncSyncItemDto::getSkuId, stoDiffItem -> stoDiffItem, (v1, v2) -> v1));
            if (log.isDebugEnabled()) {
                log.debug("Start SgBChannelStorageIncSyncSaveFilter3.execBeforeSubTable:storageIncSyncItemDtoMap.getValue={};", JSONObject.toJSONString(storageIncSyncItemDtoMap.values()));
            }
            storageIncSyncItemList.clear();
            storageIncSyncItemList.addAll(storageIncSyncItemDtoMap.values().stream().collect(Collectors.toList()));

            if (log.isDebugEnabled()) {
                log.debug("Start SgBChannelStorageIncSyncSaveFilter3.execBeforeSubTable:storageIncSyncItemList={};", JSONObject.toJSONString(storageIncSyncItemList));
            }

            for (int i = 0; i < storageIncSyncItemList.size(); i++) {
                SgBChannelStorageIncSyncItemDto storageIncSyncItemDto = storageIncSyncItemList.get(i);
                //添加这一个if的作用就是保证使用框架的这个方法，不会报错
                if (i == 0) {
                    subObjectList.add(storageIncSyncItemDto);
                    continue;
                }
                if (storageIncSyncItemDto.getId() < 0) {
                    Long aLong = ModelUtil.getSequence(SgConstants.SG_B_CHANNEL_STORAGE_INC_SYNC_ITEM);
                    storageIncSyncItemDto.setId(aLong);
                    storageIncSyncItemMapper.insert(storageIncSyncItemDto);
                } else {
                    storageIncSyncItemMapper.update(storageIncSyncItemDto, new UpdateWrapper<SgBChannelStorageIncSyncItem>().lambda()
                            .eq(SgBChannelStorageIncSyncItem::getId, storageIncSyncItemDto.getId()));
                }
            }

        }

        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelStorageIncSyncSaveFilter3.execBeforeSubTable:subObjectList={};", JSONObject.toJSONString(subObjectList));
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("before sub table success"));
    }


    @Override
    public ValueHolderV14 execAfterSubTable(SgBChannelStorageIncSyncDto mainObject, List<SgBChannelStorageIncSyncItemDto> subObjectList, User loginUser) {


        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelStorageIncSyncSaveFilter3.execAfterSubTable:mainObject={};", JSONObject.toJSONString(mainObject));
        }
        /*SgBChannelStorageIncSyncItemDto updateStorageIncSyncItemDto = new SgBChannelStorageIncSyncItemDto();
        updateStorageIncSyncItemDto.setSgBChannelStorageIncSyncId(mainObject.getId());
        int update = storageIncSyncItemMapper.update(updateStorageIncSyncItemDto, new UpdateWrapper<SgBChannelStorageIncSyncItem>().lambda()
                .eq(SgBChannelStorageIncSyncItem::getSgBChannelStorageIncSyncId, mainObject.getOldMainId()));
        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelStorageIncSyncSaveFilter3.execAfterSubTable:update={};", update);
        }*/


        for (SgBChannelStorageIncSyncItemDto storageIncSyncItemDto : subObjectList) {
            if (storageIncSyncItemDto.getId() > 0L) {
                SgBChannelStorageIncSyncItem storageIncSyncItemAfter = storageIncSyncItemMapper.selectById(storageIncSyncItemDto.getId());
                if (StringUtils.isEmpty(storageIncSyncItemAfter.getNumiid()) && StringUtils.isEmpty(storageIncSyncItemAfter.getSkuId())) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("平台条码ID和平台商品ID不能同时为空！", loginUser.getLocale()));
                }
            }
        }


        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("before sub table success"));
    }
}
