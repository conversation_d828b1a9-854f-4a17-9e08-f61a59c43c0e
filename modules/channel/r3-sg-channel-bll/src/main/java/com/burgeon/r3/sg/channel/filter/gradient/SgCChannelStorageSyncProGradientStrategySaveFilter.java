package com.burgeon.r3.sg.channel.filter.gradient;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.model.request.record.SgCommRecordData;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelStorageSyncProGradientStrategyMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelStorageSyncGradientStrategyDTO;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelStorageSyncProGradientStrategyDTO;
import com.burgeon.r3.sg.channel.services.strategy.SgCChannelStorageSyncProGradientStrategyStockSyncService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.gradient.SgCChannelStorageSyncGradientStrategy;
import com.burgeon.r3.sg.core.model.table.channel.gradient.SgCChannelStorageSyncProGradientStrategy;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.ps.api.result.PsSkuResult;
import com.jackrain.nea.ps.api.table.ProSku;
import com.jackrain.nea.ps.api.table.PsCPro;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 平台库存同步梯度策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SgCChannelStorageSyncProGradientStrategySaveFilter extends BaseSingleFilter<SgCChannelStorageSyncProGradientStrategyDTO> {

    @Autowired
    private SgCChannelStorageSyncProGradientStrategyMapper mapper;

    @Autowired
    private SgCChannelStorageSyncProGradientStrategyStockSyncService service;

    @Override
    public String getFilterMsgName() {
        return "平台商品库存同步梯度策略";
    }

    @Override
    public Class<?> getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgCChannelStorageSyncProGradientStrategyDTO mainObject, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过", loginUser.getLocale()));

        if (Objects.nonNull(mainObject.getCpCShopId())) {
            CpShop shop = CommonCacheValUtils.getShopInfo(mainObject.getCpCShopId());
            if (Objects.isNull(shop)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("未查询到店铺！"));
                return vh;
            }
            mainObject.setCpCShopTitle(shop.getCpCShopTitle());
        }

        if (Objects.nonNull(mainObject.getPsCSkuId())) {
            PsCProSkuResult skuInfo = CommonCacheValUtils.getSkuInfo(mainObject.getPsCSkuId());

            if (log.isDebugEnabled()) {
                log.debug("SgCChannelStorageSyncProGradientStrategySaveFilter.execBeforeMainTable. skuInfo:{}", JSONObject.toJSONString(skuInfo));
            }

            if (Objects.isNull(skuInfo)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("未查询到条码信息！"));
                return vh;
            }

            mainObject.setPsCSkuEcode(skuInfo.getSkuEcode());

        }

        if (Objects.nonNull(mainObject.getPsCProId())) {
            PsCPro proInfo = CommonCacheValUtils.getProInfo(mainObject.getPsCProId());
            if (Objects.isNull(proInfo)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("未查询到商品信息！"));
                return vh;
            }
            mainObject.setPsCProEcode(proInfo.getEcode());
            mainObject.setPsCProEname(proInfo.getEname());
        }

        return vh;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgCChannelStorageSyncProGradientStrategyDTO mainObject, User loginUser) {

        SgCChannelStorageSyncProGradientStrategyDTO orignalData = getOrignalData();

        if (log.isDebugEnabled()) {
            log.debug("SgCChannelStorageSyncProGradientStrategySaveFilter.execAfterMainTable saveLog:{}", JSONObject.toJSONString(mainObject));
        }

        // 记录日志
        try {

            SgCChannelStorageSyncProGradientStrategy newData = mapper.selectById(mainObject.getId());

            if (log.isDebugEnabled()) {
                log.debug("SgCChannelStorageSyncProGradientStrategySaveFilter.execAfterMainTable:{}", JSONObject.toJSONString(orignalData));
            }


            if (Objects.nonNull(orignalData.getId()) && orignalData.getId() > 0L) {

                if (log.isDebugEnabled()) {
                    log.debug("SgCChannelStorageSyncProGradientStrategySaveFilter.execAfterMainTable aa");
                }


                HashMap<String, List<SgCommRecordData>> map = new HashMap<>();
                List<SgCommRecordData> dataList = new ArrayList<>();

                if (Objects.nonNull(mainObject.getQtyBegin())) {
                    SgCommRecordData data = new SgCommRecordData();
                    data.setColumnName("起始量");
                    data.setBeforeValue(Objects.isNull(orignalData.getQtyBegin()) ? "" : orignalData.getQtyBegin().toString());
                    data.setAfterValue(mainObject.getQtyBegin().toString());
                    data.setMessage("将起始量【" + data.getBeforeValue() + "】修改成【" + data.getAfterValue() + "】\t\n");
                    dataList.add(data);
                }

                if ((Objects.nonNull(newData.getQtyEnd()) || Objects.nonNull(orignalData.getQtyEnd()))
                        && !Objects.equals(newData.getQtyEnd(), orignalData.getQtyEnd())) {
                    SgCommRecordData data = new SgCommRecordData();
                    data.setColumnName("结束量");
                    data.setBeforeValue(Objects.isNull(orignalData.getQtyEnd()) ? "" : orignalData.getQtyEnd().toString());
                    data.setAfterValue(Objects.isNull(mainObject.getQtyEnd()) ? "" : mainObject.getQtyEnd().toString());
                    data.setMessage("将结束量【" + data.getBeforeValue() + "】修改成【" + data.getAfterValue() + "】\t\n");
                    dataList.add(data);
                }

                if ((Objects.nonNull(newData.getQtyStandard()) || Objects.nonNull(orignalData.getQtyStandard()))
                        && !Objects.equals(newData.getQtyStandard(), orignalData.getQtyStandard())) {
                    SgCommRecordData data = new SgCommRecordData();
                    data.setColumnName("库存标准量");
                    data.setBeforeValue(Objects.isNull(orignalData.getQtyStandard()) ? "" : orignalData.getQtyStandard().toString());
                    data.setAfterValue(Objects.isNull(mainObject.getQtyStandard()) ? "" : mainObject.getQtyStandard().toString());
                    data.setMessage("将库存标准量【" + data.getBeforeValue() + "】修改成【" + data.getAfterValue() + "】\t\n");
                    dataList.add(data);
                }

                if ((Objects.nonNull(newData.getRatio()) || Objects.nonNull(orignalData.getRatio()))
                        && !Objects.equals(newData.getRatio(), orignalData.getRatio())) {
                    SgCommRecordData data = new SgCommRecordData();
                    data.setColumnName("同步比例");
                    data.setBeforeValue(Objects.isNull(orignalData.getRatio()) ? "" : orignalData.getRatio().toString());
                    data.setAfterValue(Objects.isNull(mainObject.getRatio()) ? "" : mainObject.getRatio().toString());
                    data.setMessage("将同步比例【" + data.getBeforeValue() + "】修改成【" + data.getAfterValue() + "】\t\n");
                    dataList.add(data);
                }

                if ((StringUtils.isNotEmpty(newData.getRemark()) || StringUtils.isNotEmpty(orignalData.getRemark()))
                        && !StringUtils.equals(newData.getRemark(), orignalData.getRemark())) {
                    SgCommRecordData data = new SgCommRecordData();
                    data.setColumnName("备注");
                    data.setBeforeValue(orignalData.getRemark());
                    data.setAfterValue(mainObject.getRemark());
                    data.setMessage("将备注【" + data.getBeforeValue() + "】修改成【" + data.getAfterValue() + "】\t\n");
                    dataList.add(data);
                }

                map.put("update", dataList);
                service.saveLog(mainObject.getId(), map, loginUser);
            }
        } catch (Exception e) {
            log.error("===" + Throwables.getStackTraceAsString(e));
            log.error("平台库存同步梯度策略记录日志异常");
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过", loginUser.getLocale()));
    }
}
