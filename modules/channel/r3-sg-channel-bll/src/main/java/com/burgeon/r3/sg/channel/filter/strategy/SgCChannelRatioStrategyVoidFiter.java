package com.burgeon.r3.sg.channel.filter.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyItemMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelRatioStrategyDTO;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.ratiostrategy.SgCChannelRatioStrategyItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleFilter;
import com.jackrain.nea.web.face.User;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/6/8 10:07
 */
@Component
public class SgCChannelRatioStrategyVoidFiter extends BaseSingleFilter<SgCChannelRatioStrategyDTO> {

    @Autowired
    private SgCChannelRatioStrategyItemMapper itemMapper;
    
    @Override
    public String getFilterMsgName() {
        return "按比例同步策略作废";
    }

    @Override
    public Class<?> getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgCChannelRatioStrategyDTO mainObject, User loginUser) {
        return null;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgCChannelRatioStrategyDTO mainObject, User loginUser) {
        Long id = mainObject.getId();
        List<SgCChannelRatioStrategyItem> strategyItems = itemMapper.selectList(new LambdaQueryWrapper<SgCChannelRatioStrategyItem>()
                .eq(SgCChannelRatioStrategyItem::getSgCChannelRatioStrategyId, id)
                .eq(SgCChannelRatioStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y));
        //批量作废明细
        if (CollectionUtils.isNotEmpty(strategyItems)) {
            List<Long> itemIds = strategyItems.stream().map(o -> o.getId()).collect(Collectors.toList());
            SgCChannelRatioStrategyItem update = new SgCChannelRatioStrategyItem();
            update.setIsactive(SgConstants.IS_ACTIVE_N);
            itemMapper.update(update, new LambdaQueryWrapper<SgCChannelRatioStrategyItem>()
                    .in(SgCChannelRatioStrategyItem::getId, itemIds));
        }
        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("作废明细成功！"));
    }
}
