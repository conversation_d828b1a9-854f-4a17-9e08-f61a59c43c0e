package com.burgeon.r3.sg.channel.validate.gradient;

import com.burgeon.r3.sg.basic.model.request.record.SgCommRecordData;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelStorageSyncGradientStrategyDTO;
import com.burgeon.r3.sg.channel.services.strategy.SgCChannelStorageSyncGradientStrategyStockSyncService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 平台库存同步梯度策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SgCChannelStorageSyncGradientStrategyVoidValidator extends BaseSingleValidator<SgCChannelStorageSyncGradientStrategyDTO> {

    @Autowired
    private SgCChannelStorageSyncGradientStrategyStockSyncService service;

    @Override
    public String getValidatorMsgName() {
        return "平台库存同步梯度策略作废";
    }

    @Override
    public Class<?> getValidatorClass() {
        return SgCChannelStorageSyncGradientStrategyVoidValidator.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCChannelStorageSyncGradientStrategyDTO mainObject, User loginUser) {
        try {
            HashMap<String, List<SgCommRecordData>> map = new HashMap<>();
            List<SgCommRecordData> list = new ArrayList<>();

            SgCommRecordData data = new SgCommRecordData();
            data.setColumnName("作废");
            data.setMessage(loginUser.getName() + "作废了当前数据");

            list.add(data);
            map.put("void", list);
            service.saveLog(mainObject.getId(), map, loginUser);
        } catch (Exception e) {
            log.error("平台库存同步梯度策略作废异常");
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过", loginUser.getLocale()));
    }
}
