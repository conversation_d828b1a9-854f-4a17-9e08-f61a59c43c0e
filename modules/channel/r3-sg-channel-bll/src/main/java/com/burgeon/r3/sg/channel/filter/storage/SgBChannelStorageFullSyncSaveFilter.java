package com.burgeon.r3.sg.channel.filter.storage;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgBChannelStorageFullSyncMapper;
import com.burgeon.r3.sg.channel.model.dto.storage.SgBChannelStorageFullSyncDto;
import com.burgeon.r3.sg.channel.utils.SgChannelProductUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBChannelStorageFullSync;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description 平台库存全量同步Filter
 * <AUTHOR>
 * @Date 2021/6/23 14:23
 * @Version 1.0
 **/

@Slf4j
@Component
public class SgBChannelStorageFullSyncSaveFilter extends BaseSingleFilter<SgBChannelStorageFullSyncDto> {

    public final static String SG_B_CHANNEL_STORAGE_FULL_SYNC_BATCH_NO = "SG_B_CHANNEL_STORAGE_FULL_SYNC_BATCH_NO";
    @Autowired
    private SgBChannelStorageFullSyncMapper storageFullSyncMapper;

    @Autowired
    private SgBChannelProductMapper productMapper;

    @Override
    public String getFilterMsgName() {
        return "平台库存全量同步保存";
    }

    @Override
    public Class<?> getFilterClass() {
        return SgBChannelStorageFullSyncSaveFilter.class;
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgBChannelStorageFullSyncDto mainObject, User loginUser) {
        if (mainObject.getId() <= 0L) {

            //获取批次号
            String batchNo = SgStoreUtils.getBillNo(SG_B_CHANNEL_STORAGE_FULL_SYNC_BATCH_NO,
                    SgConstants.SG_B_CHANNEL_STORAGE_FULL_SYNC.toUpperCase().toUpperCase(), mainObject, loginUser.getLocale());
            mainObject.setBatchNo(batchNo);

            mainObject.setStatus(SgChannelConstants.CHANNEL_STORAGE_FULL_SYNC_UN);
            CpShop shop = CommonCacheValUtils.getShopInfo(mainObject.getCpCShopId());
            mainObject.setCpCShopEcode(shop.getEcode());
            mainObject.setCpCShopTitle(shop.getCpCShopTitle());


        } else {
            SgBChannelStorageFullSync storageFullSyncOld = storageFullSyncMapper.selectById(mainObject.getId());
            mainObject.setCpCShopId(storageFullSyncOld.getCpCShopId());
        }

        //平台条码不为空
        if (StringUtils.isNotEmpty(mainObject.getSkuId())) {
            //根据平台商铺ID查询条码信息
            String skuId = mainObject.getSkuId();
            SgBChannelProduct product = SgChannelProductUtils.getChannelProduct(skuId);
            if (log.isDebugEnabled()) {
                log.debug("Start SgBChannelStorageIncSyncSaveFilter3.execBeforeSubTable:product={};", JSONObject.toJSONString(product));
            }
            if (product == null) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("未获取到平台条码信息！"));
            }
            mainObject.setPsCSkuId(mainObject.getPsCSkuId());
            mainObject.setPsCSkuEcode(product.getPsCSkuEcode());
            mainObject.setGbcode(product.getGbcode());
            mainObject.setPsCBrandId(product.getPsCBrandId());
            mainObject.setPsCProId(product.getPsCProId());
            mainObject.setPsCProEcode(product.getPsCProEcode());
            mainObject.setPsCProEname(product.getPsCProEname());
            mainObject.setPsCSpec1Id(product.getPsCSpec1Id());
            mainObject.setPsCSpec1Ecode(product.getPsCSpec1Ecode());
            mainObject.setPsCSpec1Ename(product.getPsCSpec1Ename());
            mainObject.setPsCSpec2Id(product.getPsCSpec2Id());
            mainObject.setPsCSpec2Ecode(product.getPsCSpec2Ecode());
            mainObject.setPsCSpec2Ename(product.getPsCSpec2Ename());
        }


        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("before main table success"));
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgBChannelStorageFullSyncDto mainObject, User loginUser) {
        return null;
    }


}
