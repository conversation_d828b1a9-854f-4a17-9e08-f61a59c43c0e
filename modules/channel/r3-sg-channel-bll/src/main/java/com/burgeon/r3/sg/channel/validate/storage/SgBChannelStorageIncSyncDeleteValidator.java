package com.burgeon.r3.sg.channel.validate.storage;

import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.storage.SgBChannelStorageIncSyncItemMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgBChannelStorageIncSyncMapper;
import com.burgeon.r3.sg.channel.model.dto.storage.SgBChannelStorageIncSyncDto;
import com.burgeon.r3.sg.channel.model.dto.storage.SgBChannelStorageIncSyncItemDto;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBChannelStorageIncSync;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBChannelStorageIncSyncItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleItemValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 平台库存增量同步 删除
 * <AUTHOR>
 * @Date 2021/6/23 9:51
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgBChannelStorageIncSyncDeleteValidator extends BaseSingleItemValidator<SgBChannelStorageIncSyncDto, SgBChannelStorageIncSyncItemDto> {

    @Autowired
    public SgBChannelStorageIncSyncItemMapper storageIncSyncItemMapper;
    @Autowired
    public SgBChannelStorageIncSyncMapper storageIncSyncMapper;

    @Override
    public String getValidatorMsgName() {
        return "平台库存增量同步明细删除";
    }

    @Override
    public Class<?> getValidatorClass() {
        return SgBChannelStorageIncSyncDeleteValidator.class;
    }


    @Override
    public ValueHolderV14 validateMainTable(SgBChannelStorageIncSyncDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));

        if (mainObject.getId() == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(Resources.getMessage("请选择需要删除的记录!", loginUser.getLocale()));
            return v14;
        } else {
            SgBChannelStorageIncSync sgBChannelStorageIncSync = storageIncSyncMapper.selectById(mainObject.getId());
            if (SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_IN.equals(sgBChannelStorageIncSync.getStatus()) ||
                    SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_POR_SUCC.equals(sgBChannelStorageIncSync.getStatus())||
                    SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_SUCC.equals(sgBChannelStorageIncSync.getStatus())) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("当前记录已同步，不允许删除!", loginUser.getLocale()));
                return v14;
            }

        }
        return v14;
    }

    @Override
    public ValueHolderV14 validateSubTable(SgBChannelStorageIncSyncDto mainObject, List<SgBChannelStorageIncSyncItemDto> subObjectList, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        List<Long> storageIncSyncItemId = subObjectList.stream().map(SgBChannelStorageIncSyncItemDto::getId).collect(Collectors.toList());

        List<SgBChannelStorageIncSyncItem> sgBChannelStorageIncSyncItems = storageIncSyncItemMapper.selectBatchIds(storageIncSyncItemId);
        if (CollectionUtils.isNotEmpty(sgBChannelStorageIncSyncItems)) {
            Map<Long, SgBChannelStorageIncSyncItem> collectStorageIncSyncItem = sgBChannelStorageIncSyncItems.stream().collect(Collectors.toMap(SgBChannelStorageIncSyncItem::getId,
                    s -> s, (v1, v2) -> v1));

            for (SgBChannelStorageIncSyncItemDto storageIncSyncItemDto : subObjectList) {
                Long itemId = storageIncSyncItemDto.getId();
                if (collectStorageIncSyncItem.containsKey(itemId)) {

                    SgBChannelStorageIncSyncItem sgBChannelStorageIncSyncItem = collectStorageIncSyncItem.get(itemId);

                    if (SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_IN.equals(sgBChannelStorageIncSyncItem.getStatus()) ||
                            SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_POR_SUCC.equals(sgBChannelStorageIncSyncItem.getStatus()) ||
                            SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_SUCC.equals(sgBChannelStorageIncSyncItem.getStatus())) {
                        v14.setCode(ResultCode.FAIL);
                        v14.setMessage(Resources.getMessage("当前记录已同步，不允许删除!", loginUser.getLocale()));
                        return v14;
                    }
                }
            }
        }
        return v14;
    }

}
