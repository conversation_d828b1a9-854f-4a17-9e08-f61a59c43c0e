package com.burgeon.r3.sg.channel.filter.strategy;

import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelSkuStrategyDTO;
import com.burgeon.r3.sg.channel.services.strategy.SgCChannelSkuStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/14 19:53
 */
@Slf4j
@Component
public class SgCChannelSkuStrategySubmitFilter extends BaseSingleFilter<SgCChannelSkuStrategyDTO> {

    @Autowired
    private SgCChannelSkuStrategyService sgChannelSkuStrategyService;

    @Override
    public String getFilterMsgName() {
        return "特殊条码按比例同步策略审核";
    }

    @Override
    public Class getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgCChannelSkuStrategyDTO mainObject, User loginUser) {
        List<Long> objIds = new ArrayList<>();
        objIds.add(mainObject.getId());
        //库存同步
        sgChannelSkuStrategyService.syncStock(objIds, loginUser);
        return null;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgCChannelSkuStrategyDTO mainObject, User loginUser) {
        return null;
    }

}
