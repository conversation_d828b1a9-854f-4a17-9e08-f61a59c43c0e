package com.burgeon.r3.sg.channel.services.strategy;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyItemMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.channel.qtystrategy.SgCChannelQtyStrategy;
import com.burgeon.r3.sg.core.model.table.channel.qtystrategy.SgCChannelQtyStrategyItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.stocksync.api.SgBChannelStorageBufferCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferBatchSaveRequest;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferSaveRequest;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/6/18 10:49
 * 库存同步
 */
@Slf4j
@Component
public class SgCChannelQtyStrategyStockSyncService {

    @Autowired
    private SgCChannelQtyStrategyMapper mapper;

    @Autowired
    private SgCChannelQtyStrategyItemMapper itemMapper;

    //check 和 库存锁定
    @Autowired
    private SgCChannelQtyStrategyService strategyService;

    public ValueHolder stocksync(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        SgCChannelQtyStrategyStockSyncService bean = ApplicationContextHandle.getBean(SgCChannelQtyStrategyStockSyncService.class);
        return R3ParamUtils.convertV14WithResult(bean.stocksync(request));
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> stocksync(SgR3BaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgCChannelQtyStrategyIncrementService.increment.ReceiveParams:request{}"
                    , JSONObject.toJSONString(request));
        }

        Long objId = request.getObjId();
        User loginUser = request.getLoginUser();

        String lockKey = SgConstants.SG_C_CHANNEL_QTY_STRATEGY + ":" + objId;
        List<String> redisBillFtpKeyList = new ArrayList<>();
        SgCChannelQtyStrategy qtyStrategy = strategyService.checkParams(request);
        SgRedisLockUtils.lock(lockKey);
        try {
            List<SgCChannelQtyStrategyItem> qtyStrategyItems = strategyService.updateStorage(qtyStrategy, redisBillFtpKeyList, loginUser, request.isR3());
            // 根据当前的【待发布明细】，所涉及【渠道库存锁定库存】记录
            //根据【渠道库存锁定库存】的剩余量 ，调用全量库存同步接口。
            log.debug("库存同步 Start stocksync  qtyStrategy:{} qtyStrategyItems:{}"
                    , JSONObject.toJSONString(qtyStrategy), JSONObject.toJSONString(qtyStrategyItems));
            skuStocksyncSku(loginUser, qtyStrategy, qtyStrategyItems);
            if (request.isR3()) {
                StorageUtils.setBModelDefalutDataByUpdate(qtyStrategy, loginUser);
                mapper.updateById(qtyStrategy);
            }
        } catch (Exception e) {
            if (CollectionUtils.isNotEmpty(redisBillFtpKeyList)) {
                StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, request.getLoginUser());
            }
            AssertUtils.logAndThrowException(e, loginUser.getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKey, log, this.getClass().getName());
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "已开始库存同步");
    }


    /**
     * 库存同步
     *
     * @param loginUser   操作用户
     * @param qtyStrategy 主表信息
     * @param qtyStrategy 未发布明细
     */
    private void skuStocksyncSku(User loginUser, SgCChannelQtyStrategy qtyStrategy, List<SgCChannelQtyStrategyItem> qtyStrategyItems) {
        if (log.isDebugEnabled()) {
            log.debug("按数量同步策略 stocksync qtyStrategy = {} qtyStrategyItems = {}"
                    , JSONObject.toJSONString(qtyStrategy), JSONObject.toJSONString(qtyStrategyItems));
        }
        Long cShopId = qtyStrategy.getCpCShopId();
        SgBChannelStorageBufferCmd storageBufferCmd = (SgBChannelStorageBufferCmd) ReferenceUtil.refer(
                ApplicationContextHandle.getApplicationContext(),
                SgBChannelStorageBufferCmd.class.getName(),
                SgConstantsIF.GROUP, SgConstantsIF.VERSION);

        SgChannelStorageBufferBatchSaveRequest request = new SgChannelStorageBufferBatchSaveRequest();
        List<SgChannelStorageBufferSaveRequest> saveRequestList = new ArrayList<>();
        String batchno = System.currentTimeMillis() + "" + (int) (Math.random() * 9000 + 1000);

        for (SgCChannelQtyStrategyItem strategyItem : qtyStrategyItems) {
            SgChannelStorageBufferSaveRequest saveRequest = new SgChannelStorageBufferSaveRequest();
            BeanUtils.copyProperties(strategyItem, saveRequest);
            saveRequest.setCpCShopId(cShopId);
            saveRequest.setFixedQtyFlag(SgConstants.IS_ACTIVE_Y);
            saveRequest.setFixedQty(BigDecimal.ONE);
            saveRequest.setSyncType(SgConstants.SYNC_TYPE_ALL);
            saveRequest.setSourceNo("按数量同步策略");
            saveRequestList.add(saveRequest);
        }
        request.setBatchno(batchno);
        request.setUser(loginUser);
        request.setBufferSaveRequestList(saveRequestList);
        ValueHolderV14<Integer> v14 = storageBufferCmd.saveDataToChannelStorageBuffer(request);
        JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(v14.toJSONObject(),
                SerializerFeature.WriteMapNullValue));
        if (log.isDebugEnabled()) {
            log.debug("按数量同步策略 手工计算库存并同步结果" + result.toJSONString());
        }
    }
}
