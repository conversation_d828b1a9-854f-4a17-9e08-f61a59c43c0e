package com.burgeon.r3.sg.channel.services.storage;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgBChannelStorageIncSyncItemMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgBChannelStorageIncSyncMapper;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductQueryRequest;
import com.burgeon.r3.sg.channel.model.request.storage.SgBChannelStorageIncSyncBillSaveRequest;
import com.burgeon.r3.sg.channel.model.request.storage.SgBChannelStorageIncSyncItemSaveRequest;
import com.burgeon.r3.sg.channel.model.request.storage.SgBChannelStorageIncSyncSaveRequest;
import com.burgeon.r3.sg.channel.services.product.SgChannelProductQueryService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBChannelStorageIncSync;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBChannelStorageIncSyncItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/8/31 9:57
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgBChannelStorageIncSyncSaveService {
    public final static String SG_CHANNEL_STORAGE_INC_SYNC_BATCH_NO = "SG_CHANNEL_STORAGE_INC_SYNC_BATCH_NO";

    @Autowired
    private SgBChannelStorageIncSyncMapper storageIncSyncMapper;

    @Autowired
    private SgBChannelStorageIncSyncItemMapper storageIncSyncItemMapper;

    @Autowired
    private SgBChannelProductMapper productMapper;


    public ValueHolder save(QuerySession session) {
        SgBChannelStorageIncSyncBillSaveRequest request = R3ParamUtils.parseSaveObject(session, SgBChannelStorageIncSyncBillSaveRequest.class);
        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelStorageIncSyncSaveService.save:param={}", JSONObject.toJSONString(request));
        }
        request.setR3(true);
        SgBChannelStorageIncSyncSaveService service = ApplicationContextHandle.getBean(SgBChannelStorageIncSyncSaveService.class);
        return R3ParamUtils.convertV14WithResult(service.save(request));
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> save(SgBChannelStorageIncSyncBillSaveRequest request) {
        ValueHolderV14 valueHolderV14 = checkParams(request);
        Long cpCShopId = (Long) valueHolderV14.getData();
        if (log.isDebugEnabled()) {
            log.debug("SgBChannelStorageIncSyncSaveService.save:cpCShopId={}", cpCShopId);
        }
        if (request.getObjId() == null || request.getObjId() < 0) {
            return insert(request, cpCShopId);
        } else {
            return update(request, cpCShopId);
        }

    }

    /**
     * 新增
     *
     * @param request
     * @return
     */
    private ValueHolderV14<SgR3BaseResult> insert(SgBChannelStorageIncSyncBillSaveRequest request, Long cpCShopId) {
        SgBChannelStorageIncSyncSaveRequest storageIncSyncSaveRequest = request.getStorageIncSyncSaveRequest();
        SgBChannelStorageIncSync sgBChannelStorageIncSync = new SgBChannelStorageIncSync();
        BeanUtils.copyProperties(storageIncSyncSaveRequest, sgBChannelStorageIncSync);
        StorageUtils.setBModelDefalutData(sgBChannelStorageIncSync, request.getLoginUser());
        Long objId = ModelUtil.getSequence(SgConstants.SG_B_CHANNEL_STORAGE_INC_SYNC);
        sgBChannelStorageIncSync.setId(objId);
        //获取批次号
        String batchNo = SgStoreUtils.getBillNo(SG_CHANNEL_STORAGE_INC_SYNC_BATCH_NO,
                SgConstants.SG_B_CHANNEL_STORAGE_INC_SYNC.toUpperCase().toUpperCase(), sgBChannelStorageIncSync, request.getLoginUser().getLocale());
        sgBChannelStorageIncSync.setBatchNo(batchNo);


        sgBChannelStorageIncSync.setStatus(SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_UN);
        CpShop shop = CommonCacheValUtils.getShopInfo(storageIncSyncSaveRequest.getCpCShopId());
        if (shop == null) {
            AssertUtils.logAndThrow("未获取到平台店铺信息！", request.getLoginUser().getLocale());
        }
        sgBChannelStorageIncSync.setCpCShopEcode(shop.getEcode());
        sgBChannelStorageIncSync.setCpCShopTitle(shop.getCpCShopTitle());


        List<SgBChannelStorageIncSyncItemSaveRequest> subObjectList = request.getItems();

        List<SgBChannelStorageIncSyncItem> storageIncSyncItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(subObjectList)) {
            for (SgBChannelStorageIncSyncItemSaveRequest storageIncSyncItemSaveRequest : subObjectList) {
                storageIncSyncItemSaveRequest.setSgBChannelStorageIncSyncId(sgBChannelStorageIncSync.getId());
                // 平台商品ID 不为空 和 平台条码id 为空 和  条码id 不为空
                if (StringUtils.isNotEmpty(storageIncSyncItemSaveRequest.getNumiid()) &&
                        StringUtils.isEmpty(storageIncSyncItemSaveRequest.getSkuId()) &&
                        storageIncSyncItemSaveRequest.getPsCSkuId() != null) {
                    if (log.isDebugEnabled()) {
                        log.debug("SgBChannelStorageIncSyncSaveService.insert:param1={}");
                    }

                    SgChannelProductQueryService service = ApplicationContextHandle.getBean(SgChannelProductQueryService.class);
                    SgChannelProductQueryRequest productQueryRequest = new SgChannelProductQueryRequest();

                    List<String> numiidList = new ArrayList<>();
                    List<Long> psCSkuIdList = new ArrayList<>();

                    numiidList.add(storageIncSyncItemSaveRequest.getNumiid());
                    psCSkuIdList.add(storageIncSyncItemSaveRequest.getPsCSkuId());
                    productQueryRequest.setPsCSkuIdList(psCSkuIdList);
                    productQueryRequest.setNumiidList(numiidList);
                    productQueryRequest.setCpCShopId(cpCShopId);
                    ValueHolderV14<List<SgBChannelProduct>> result = service.queryChannelProduct(productQueryRequest);
                    if (!result.isOK() || result.getData().size() <= 0) {
                        AssertUtils.logAndThrow("未获取到平台店铺下的条码信息！", request.getLoginUser().getLocale());
                    }
                    List<SgBChannelProduct> channelProductList = result.getData();
                    for (SgBChannelProduct sgBChannelProduct : channelProductList) {
                        SgBChannelStorageIncSyncItem sgBChannelStorageIncSyncItem = new SgBChannelStorageIncSyncItem();
                        BeanUtils.copyProperties(storageIncSyncItemSaveRequest, sgBChannelStorageIncSyncItem);
                        sgBChannelStorageIncSyncItem.setNumiid(sgBChannelProduct.getNumiid());

                        buildChannelStorageIncSyncItem(sgBChannelStorageIncSyncItem, sgBChannelProduct, request.getObjId());

                        StorageUtils.setBModelDefalutData(sgBChannelStorageIncSyncItem, request.getLoginUser());

                        storageIncSyncItemList.add(sgBChannelStorageIncSyncItem);
                    }


                } else if (StringUtils.isNotEmpty(storageIncSyncItemSaveRequest.getSkuId())) {
                    if (log.isDebugEnabled()) {
                        log.debug("SgBChannelStorageIncSyncSaveService.insert:param2={}");
                    }

                    //平台条码id
                    SgBChannelStorageIncSyncItem sgBChannelStorageIncSyncItem = new SgBChannelStorageIncSyncItem();
                    BeanUtils.copyProperties(storageIncSyncItemSaveRequest, sgBChannelStorageIncSyncItem);
                    //根据平台店铺ID查询条码信息

                    SgChannelProductQueryService service = ApplicationContextHandle.getBean(SgChannelProductQueryService.class);
                    SgChannelProductQueryRequest productQueryRequest = new SgChannelProductQueryRequest();
                    List<String> skuIdList = new ArrayList<>();
                    skuIdList.add(storageIncSyncItemSaveRequest.getSkuId());
                    productQueryRequest.setSkuIdList(skuIdList);
                    productQueryRequest.setCpCShopId(cpCShopId);

                    ValueHolderV14<List<SgBChannelProduct>> result = service.queryChannelProduct(productQueryRequest);
                    if (!result.isOK() || result.getData().size() <= 0) {
                        AssertUtils.logAndThrow("未获取到平台店铺下的平台条码信息！", request.getLoginUser().getLocale());
                    }
                    SgBChannelProduct product = result.getData().get(0);

                    buildChannelStorageIncSyncItem(sgBChannelStorageIncSyncItem, product, request.getObjId());

                    StorageUtils.setBModelDefalutData(sgBChannelStorageIncSyncItem, request.getLoginUser());

                    storageIncSyncItemList.add(sgBChannelStorageIncSyncItem);


                } else if (StringUtils.isNotEmpty(storageIncSyncItemSaveRequest.getNumiid())) {
                    //平台商品id
                    if (log.isDebugEnabled()) {
                        log.debug("SgBChannelStorageIncSyncSaveService.insert:param3={}");
                    }

                    String numiid = storageIncSyncItemSaveRequest.getNumiid();
                    SgChannelProductQueryService service = ApplicationContextHandle.getBean(SgChannelProductQueryService.class);
                    SgChannelProductQueryRequest productQueryRequest = new SgChannelProductQueryRequest();
                    List<String> numiidList = new ArrayList<>();
                    numiidList.add(numiid);
                    productQueryRequest.setNumiidList(numiidList);
                    productQueryRequest.setCpCShopId(cpCShopId);
                    ValueHolderV14<List<SgBChannelProduct>> result = service.queryChannelProduct(productQueryRequest);
                    if (!result.isOK() || result.getData().size() <= 0) {
                        AssertUtils.logAndThrow("未获取到平台店铺下的平台商品信息！", request.getLoginUser().getLocale());
                    }
                    for (SgBChannelProduct sgBChannelProduct : result.getData()) {
                        SgBChannelStorageIncSyncItem sgBChannelStorageIncSyncItem = new SgBChannelStorageIncSyncItem();
                        BeanUtils.copyProperties(storageIncSyncItemSaveRequest, sgBChannelStorageIncSyncItem);
                        sgBChannelStorageIncSyncItem.setNumiid(numiid);

                        buildChannelStorageIncSyncItem(sgBChannelStorageIncSyncItem, sgBChannelProduct, request.getObjId());

                        StorageUtils.setBModelDefalutData(sgBChannelStorageIncSyncItem, request.getLoginUser());


                        storageIncSyncItemList.add(sgBChannelStorageIncSyncItem);
                    }
                } else if (storageIncSyncItemSaveRequest.getPsCSkuId() != null) {//
                    if (log.isDebugEnabled()) {
                        log.debug("SgBChannelStorageIncSyncSaveService.insert:param4={}");
                    }

                    //条码
                    SgChannelProductQueryService service = ApplicationContextHandle.getBean(SgChannelProductQueryService.class);
                    SgChannelProductQueryRequest productQueryRequest = new SgChannelProductQueryRequest();

                    List<Long> psCSkuIdList = new ArrayList<>();
                    psCSkuIdList.add(storageIncSyncItemSaveRequest.getPsCSkuId());
                    productQueryRequest.setPsCSkuIdList(psCSkuIdList);
                    productQueryRequest.setCpCShopId(cpCShopId);
                    ValueHolderV14<List<SgBChannelProduct>> result = service.queryChannelProduct(productQueryRequest);
                    if (!result.isOK() || result.getData().size() <= 0) {
                        AssertUtils.logAndThrow("未获取到平台店铺下的条码信息！", request.getLoginUser().getLocale());
                    }
                    List<SgBChannelProduct> channelProductList = result.getData();
                    for (SgBChannelProduct sgBChannelProduct : channelProductList) {
                        SgBChannelStorageIncSyncItem sgBChannelStorageIncSyncItem = new SgBChannelStorageIncSyncItem();
                        BeanUtils.copyProperties(storageIncSyncItemSaveRequest, sgBChannelStorageIncSyncItem);
                        sgBChannelStorageIncSyncItem.setNumiid(sgBChannelProduct.getNumiid());

                        buildChannelStorageIncSyncItem(sgBChannelStorageIncSyncItem, sgBChannelProduct, request.getObjId());

                        StorageUtils.setBModelDefalutData(sgBChannelStorageIncSyncItem, request.getLoginUser());

                        storageIncSyncItemList.add(sgBChannelStorageIncSyncItem);
                    }
                }


            }
        }
        if (CollectionUtils.isNotEmpty(storageIncSyncItemList)) {
            List<List<SgBChannelStorageIncSyncItem>> insertPageList =
                    StorageUtils.getBaseModelPageList(storageIncSyncItemList, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
            /*批量新增500/次*/
            for (List<SgBChannelStorageIncSyncItem> pageList : insertPageList) {
                if (CollectionUtils.isEmpty(pageList)) {
                    continue;
                }
                int insertResult = storageIncSyncItemMapper.batchInsert(pageList);
                if (insertResult != pageList.size()) {
                    AssertUtils.logAndThrow("保存平台库存增量同步单明细异常！", request.getLoginUser().getLocale());
                }
            }

        }
        storageIncSyncMapper.insert(sgBChannelStorageIncSync);
        SgR3BaseResult baseResult = new SgR3BaseResult();
        baseResult.setDataJo(objId, SgConstants.SG_B_STO_TRANSFER.toUpperCase());
        return new ValueHolderV14<>(baseResult, ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);


    }

    /**
     * 保存
     *
     * @param request
     * @return
     */
    private ValueHolderV14<SgR3BaseResult> update(SgBChannelStorageIncSyncBillSaveRequest request, Long cpCShopId) {
        SgBChannelStorageIncSyncSaveRequest storageIncSyncSaveRequest = request.getStorageIncSyncSaveRequest();

        SgBChannelStorageIncSync update = new SgBChannelStorageIncSync();
        if (storageIncSyncSaveRequest != null) {
            BeanUtils.copyProperties(storageIncSyncSaveRequest, update);
        }
        update.setId(request.getObjId());
        StorageUtils.setBModelDefalutDataByUpdate(update, request.getLoginUser());
        List<SgBChannelStorageIncSyncItemSaveRequest> subObjectList = request.getItems();

        List<SgBChannelStorageIncSyncItem> storageIncSyncItemNoIdList = new ArrayList<>();
        List<SgBChannelStorageIncSyncItem> storageIncSyncItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(subObjectList)) {
            for (SgBChannelStorageIncSyncItemSaveRequest storageIncSyncItemSaveRequest : subObjectList) {


                if (storageIncSyncItemSaveRequest.getId() == null || storageIncSyncItemSaveRequest.getId() < 0L) {

                    storageIncSyncItemSaveRequest.setSgBChannelStorageIncSyncId(request.getObjId());

                    // 平台商品ID 不为空 和 平台条码id 为空 和  条码id 不为空
                    if (StringUtils.isNotEmpty(storageIncSyncItemSaveRequest.getNumiid()) &&
                            StringUtils.isEmpty(storageIncSyncItemSaveRequest.getSkuId()) &&
                            storageIncSyncItemSaveRequest.getPsCSkuId() != null) {

                        if (log.isDebugEnabled()) {
                            log.debug("SgBChannelStorageIncSyncSaveService.update:param1={}");
                        }


                        SgChannelProductQueryService service = ApplicationContextHandle.getBean(SgChannelProductQueryService.class);
                        SgChannelProductQueryRequest productQueryRequest = new SgChannelProductQueryRequest();

                        List<String> numiidList = new ArrayList<>();
                        List<Long> psCSkuIdList = new ArrayList<>();

                        numiidList.add(storageIncSyncItemSaveRequest.getNumiid());
                        psCSkuIdList.add(storageIncSyncItemSaveRequest.getPsCSkuId());
                        productQueryRequest.setPsCSkuIdList(psCSkuIdList);
                        productQueryRequest.setNumiidList(numiidList);
                        productQueryRequest.setCpCShopId(cpCShopId);
                        ValueHolderV14<List<SgBChannelProduct>> result = service.queryChannelProduct(productQueryRequest);
                        if (!result.isOK() || result.getData().size() <= 0) {
                            AssertUtils.logAndThrow("未获取到平台店铺下的条码信息！", request.getLoginUser().getLocale());
                        }
                        List<SgBChannelProduct> channelProductList = result.getData();
                        for (SgBChannelProduct sgBChannelProduct : channelProductList) {
                            SgBChannelStorageIncSyncItem sgBChannelStorageIncSyncItem = new SgBChannelStorageIncSyncItem();
                            BeanUtils.copyProperties(storageIncSyncItemSaveRequest, sgBChannelStorageIncSyncItem);
                            sgBChannelStorageIncSyncItem.setNumiid(sgBChannelProduct.getNumiid());

                            buildChannelStorageIncSyncItem(sgBChannelStorageIncSyncItem, sgBChannelProduct, request.getObjId());
                            storageIncSyncItemNoIdList.add(sgBChannelStorageIncSyncItem);
                        }


                    } else if (StringUtils.isNotEmpty(storageIncSyncItemSaveRequest.getSkuId())) {
                        if (log.isDebugEnabled()) {
                            log.debug("SgBChannelStorageIncSyncSaveService.update:param2={}");
                        }
                        //平台条码id

                        SgBChannelStorageIncSyncItem sgBChannelStorageIncSyncItem = new SgBChannelStorageIncSyncItem();
                        BeanUtils.copyProperties(storageIncSyncItemSaveRequest, sgBChannelStorageIncSyncItem);
                        //根据平台店铺ID查询条码信息
                        String skuId = storageIncSyncItemSaveRequest.getSkuId();
                        SgChannelProductQueryService service = ApplicationContextHandle.getBean(SgChannelProductQueryService.class);
                        SgChannelProductQueryRequest productQueryRequest = new SgChannelProductQueryRequest();
                        List<String> skuIdList = new ArrayList<>();
                        skuIdList.add(skuId);
                        productQueryRequest.setSkuIdList(skuIdList);
                        productQueryRequest.setCpCShopId(cpCShopId);
                        ValueHolderV14<List<SgBChannelProduct>> result = service.queryChannelProduct(productQueryRequest);
                        if (!result.isOK() || result.getData().size() <= 0) {
                            AssertUtils.logAndThrow("未获取到平台店铺下的平台条码信息！", request.getLoginUser().getLocale());
                        }
                        SgBChannelProduct product = result.getData().get(0);
                        buildChannelStorageIncSyncItem(sgBChannelStorageIncSyncItem, product, request.getObjId());
                        storageIncSyncItemNoIdList.add(sgBChannelStorageIncSyncItem);
                    } else if (storageIncSyncItemSaveRequest.getNumiid() != null) {
                        //平台商品id

                        if (log.isDebugEnabled()) {
                            log.debug("SgBChannelStorageIncSyncSaveService.update:param3={}");
                        }
                        String numiid = storageIncSyncItemSaveRequest.getNumiid();
                        SgChannelProductQueryService service = ApplicationContextHandle.getBean(SgChannelProductQueryService.class);
                        SgChannelProductQueryRequest productQueryRequest = new SgChannelProductQueryRequest();
                        List<String> numiidList = new ArrayList<>();
                        numiidList.add(numiid);
                        productQueryRequest.setNumiidList(numiidList);
                        productQueryRequest.setCpCShopId(cpCShopId);
                        ValueHolderV14<List<SgBChannelProduct>> result = service.queryChannelProduct(productQueryRequest);
                        if (!result.isOK() || result.getData().size() <= 0) {
                            AssertUtils.logAndThrow("未获取到平台店铺下的平台商品信息！", request.getLoginUser().getLocale());
                        }
                        List<SgBChannelProduct> channelProductList = result.getData();

                        for (SgBChannelProduct sgBChannelProduct : channelProductList) {
                            SgBChannelStorageIncSyncItem sgBChannelStorageIncSyncItem = new SgBChannelStorageIncSyncItem();
                            BeanUtils.copyProperties(storageIncSyncItemSaveRequest, sgBChannelStorageIncSyncItem);
                            sgBChannelStorageIncSyncItem.setNumiid(numiid);
                            buildChannelStorageIncSyncItem(sgBChannelStorageIncSyncItem, sgBChannelProduct, request.getObjId());
                            storageIncSyncItemNoIdList.add(sgBChannelStorageIncSyncItem);
                        }
                    } else if (storageIncSyncItemSaveRequest.getPsCSkuId() != null) {
                        if (log.isDebugEnabled()) {
                            log.debug("SgBChannelStorageIncSyncSaveService.update:param4={}");
                        }
                        //条码
                        SgChannelProductQueryService service = ApplicationContextHandle.getBean(SgChannelProductQueryService.class);
                        SgChannelProductQueryRequest productQueryRequest = new SgChannelProductQueryRequest();

                        List<Long> psCSkuIdList = new ArrayList<>();
                        psCSkuIdList.add(storageIncSyncItemSaveRequest.getPsCSkuId());
                        productQueryRequest.setPsCSkuIdList(psCSkuIdList);
                        productQueryRequest.setCpCShopId(cpCShopId);
                        ValueHolderV14<List<SgBChannelProduct>> result = service.queryChannelProduct(productQueryRequest);
                        if (!result.isOK() || result.getData().size() <= 0) {
                            AssertUtils.logAndThrow("未获取到平台店铺下的条码信息！", request.getLoginUser().getLocale());
                        }
                        List<SgBChannelProduct> data = result.getData();
                        if (log.isDebugEnabled()) {
                            log.debug("SgBChannelStorageIncSyncSaveService.save:data={}", JSONObject.toJSONString(data));
                        }
                        for (SgBChannelProduct sgBChannelProduct : data) {
                            SgBChannelStorageIncSyncItem sgBChannelStorageIncSyncItem = new SgBChannelStorageIncSyncItem();
                            BeanUtils.copyProperties(storageIncSyncItemSaveRequest, sgBChannelStorageIncSyncItem);
                            sgBChannelStorageIncSyncItem.setNumiid(sgBChannelProduct.getNumiid());

                            buildChannelStorageIncSyncItem(sgBChannelStorageIncSyncItem, sgBChannelProduct, request.getObjId());
                            storageIncSyncItemNoIdList.add(sgBChannelStorageIncSyncItem);
                        }
                    }


                } else {
                    SgBChannelStorageIncSyncItem updateIncSyncItem = new SgBChannelStorageIncSyncItem();
                    BeanUtils.copyProperties(storageIncSyncItemSaveRequest, updateIncSyncItem);
                    updateIncSyncItem.setSgBChannelStorageIncSyncId(request.getObjId());
                    storageIncSyncItemList.add(updateIncSyncItem);
                }
            }
            //只修改了数值
            if (CollectionUtils.isNotEmpty(storageIncSyncItemList) && storageIncSyncItemList.size() > 0) {
                for (SgBChannelStorageIncSyncItem incSyncItem : storageIncSyncItemList) {
                    StorageUtils.setBModelDefalutDataByUpdate(incSyncItem, request.getLoginUser());
                    storageIncSyncItemMapper.updateById(incSyncItem);
                }
            }
            if (log.isDebugEnabled()) {
                log.debug("SgBChannelStorageIncSyncSaveService.update:storageIncSyncItemNoIdList:{};", JSONObject.toJSONString(storageIncSyncItemNoIdList));
            }
            //新增的明细
            if (CollectionUtils.isNotEmpty(storageIncSyncItemNoIdList) && storageIncSyncItemNoIdList.size() > 0) {
                List<SgBChannelStorageIncSyncItem> insertItemList = new ArrayList<>();
                for (SgBChannelStorageIncSyncItem incSyncItem : storageIncSyncItemNoIdList) {
                    Long itemId = incSyncItem.getId();
                    if (itemId != null && itemId > 0) {
                        StorageUtils.setBModelDefalutDataByUpdate(incSyncItem, request.getLoginUser());
                        storageIncSyncItemMapper.updateById(incSyncItem);
                    } else {
                        Long objItemNewId = ModelUtil.getSequence(SgConstants.SG_B_CHANNEL_STORAGE_INC_SYNC_ITEM);
                        incSyncItem.setId(objItemNewId);
                        StorageUtils.setBModelDefalutData(incSyncItem, request.getLoginUser());
                        insertItemList.add(incSyncItem);
                    }
                }
                if (log.isDebugEnabled()) {
                    log.debug("SgBChannelStorageIncSyncSaveService.update:insertItemList:{};", JSONObject.toJSONString(insertItemList));
                }
                /*批量新增500/次*/
                List<List<SgBChannelStorageIncSyncItem>> insertPageList =
                        StorageUtils.getBaseModelPageList(insertItemList, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
                if (log.isDebugEnabled()) {
                    log.debug("SgBChannelStorageIncSyncSaveService.update:insertPageList:{};", JSONObject.toJSONString(insertPageList));
                }

                for (List<SgBChannelStorageIncSyncItem> pageList : insertPageList) {
                    if (CollectionUtils.isEmpty(pageList)) {
                        continue;
                    }
                    int insertResult = storageIncSyncItemMapper.batchInsert(pageList);
                    if (insertResult != pageList.size()) {
                        AssertUtils.logAndThrow("保存平台库存增量明细异常！", request.getLoginUser().getLocale());
                    }
                }

            }
        }
        storageIncSyncMapper.updateById(update);
        SgR3BaseResult baseResult = new SgR3BaseResult();
        return new ValueHolderV14<>(baseResult, ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
    }

    /**
     * 明细组装
     *
     * @param sgBChannelStorageIncSyncItem
     * @param sgBChannelProduct
     * @param mainId
     */
    private void buildChannelStorageIncSyncItem(SgBChannelStorageIncSyncItem sgBChannelStorageIncSyncItem, SgBChannelProduct sgBChannelProduct, Long mainId) {
        sgBChannelStorageIncSyncItem.setNumiid(sgBChannelProduct.getNumiid());
        sgBChannelStorageIncSyncItem.setSkuId(sgBChannelProduct.getSkuId());
        sgBChannelStorageIncSyncItem.setPsCSkuId(sgBChannelProduct.getPsCSkuId());
        sgBChannelStorageIncSyncItem.setPsCSkuEcode(sgBChannelProduct.getPsCSkuEcode());
        sgBChannelStorageIncSyncItem.setGbcode(sgBChannelProduct.getGbcode());
        sgBChannelStorageIncSyncItem.setPsCBrandId(sgBChannelProduct.getPsCBrandId());
        sgBChannelStorageIncSyncItem.setPsCProId(sgBChannelProduct.getPsCProId());
        sgBChannelStorageIncSyncItem.setPsCProEcode(sgBChannelProduct.getPsCProEcode());
        sgBChannelStorageIncSyncItem.setPsCProEname(sgBChannelProduct.getPsCProEname());
        sgBChannelStorageIncSyncItem.setPsCSpec1Id(sgBChannelProduct.getPsCSpec1Id());
        sgBChannelStorageIncSyncItem.setPsCSpec1Ecode(sgBChannelProduct.getPsCSpec1Ecode());
        sgBChannelStorageIncSyncItem.setPsCSpec1Ename(sgBChannelProduct.getPsCSpec1Ename());
        sgBChannelStorageIncSyncItem.setPsCSpec2Id(sgBChannelProduct.getPsCSpec2Id());
        sgBChannelStorageIncSyncItem.setPsCSpec2Ecode(sgBChannelProduct.getPsCSpec2Ecode());
        sgBChannelStorageIncSyncItem.setPsCSpec2Ename(sgBChannelProduct.getPsCSpec2Ename());

        if (mainId == null || mainId < 0) {
            sgBChannelStorageIncSyncItem.setStatus(SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_UN);
            Long objItemNewId = ModelUtil.getSequence(SgConstants.SG_B_CHANNEL_STORAGE_INC_SYNC_ITEM);
            sgBChannelStorageIncSyncItem.setId(objItemNewId);
        } else {
            SgBChannelStorageIncSyncItem storageIncSyncItemsBySkuId = storageIncSyncItemMapper.selectOne(new LambdaQueryWrapper<SgBChannelStorageIncSyncItem>()
                    .eq(SgBChannelStorageIncSyncItem::getSkuId, sgBChannelStorageIncSyncItem.getSkuId())
                    .eq(SgBChannelStorageIncSyncItem::getNumiid, sgBChannelStorageIncSyncItem.getNumiid())
                    .eq(SgBChannelStorageIncSyncItem::getPsCSkuId, sgBChannelStorageIncSyncItem.getPsCSkuId())
                    .eq(SgBChannelStorageIncSyncItem::getSgBChannelStorageIncSyncId, mainId));
            if (log.isDebugEnabled()) {
                log.debug("SgBChannelStorageIncSyncSaveService.buildChannelStorageIncSyncItem.storageIncSyncItemsBySkuId:{};", JSONObject.toJSONString(storageIncSyncItemsBySkuId));
            }
            if (storageIncSyncItemsBySkuId == null) {
                sgBChannelStorageIncSyncItem.setStatus(SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_UN);
                sgBChannelStorageIncSyncItem.setId(-1L);
            } else {
                sgBChannelStorageIncSyncItem.setQty(storageIncSyncItemsBySkuId.getQty().add(sgBChannelStorageIncSyncItem.getQty()));
                sgBChannelStorageIncSyncItem.setId(storageIncSyncItemsBySkuId.getId());
            }
            if (log.isDebugEnabled()) {
                log.debug("SgBChannelStorageIncSyncSaveService.buildChannelStorageIncSyncItem.setQty:{};", sgBChannelStorageIncSyncItem.getQty());
            }
        }
    }

    /**
     * 参数校验
     *
     * @param request
     * @return
     */
    public ValueHolderV14 checkParams(SgBChannelStorageIncSyncBillSaveRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelStorageIncSyncSaveFilter3.checkParams:SgBChannelStorageIncSyncBillSaveRequest.request={};", JSONObject.toJSONString(request));
        }
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        SgBChannelStorageIncSyncSaveRequest storageIncSyncSaveRequest = request.getStorageIncSyncSaveRequest();
        List<SgBChannelStorageIncSyncItemSaveRequest> subObjectList = request.getItems();
        Long cpCShopId;
        if (request.getObjId() != null && request.getObjId() > 0) {
            SgBChannelStorageIncSync sgBChannelStorageIncSync = storageIncSyncMapper.selectById(request.getObjId());
            if (sgBChannelStorageIncSync == null) {
                AssertUtils.logAndThrow("当前记录不存在！", request.getLoginUser().getLocale());
            }
            //主表同步状态为“同步成功”
            if (SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_SUCC.equals(sgBChannelStorageIncSync.getStatus())) {
                AssertUtils.logAndThrow("平台库存增量同步主表状态不符合，不允许保存！", request.getLoginUser().getLocale());
            }
            cpCShopId = sgBChannelStorageIncSync.getCpCShopId();
        } else {
            /*if (storageIncSyncSaveRequest.getBatchNo() == null) {
                AssertUtils.logAndThrow("批次号不能为空！", request.getLoginUser().getLocale());
            }
            Integer count = storageIncSyncMapper.selectCount(new QueryWrapper<SgBChannelStorageIncSync>().lambda()
                    .eq(SgBChannelStorageIncSync::getBatchNo, storageIncSyncSaveRequest.getBatchNo())
                    .eq(SgBChannelStorageIncSync::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (count > 0) {
                AssertUtils.logAndThrow("批次号不能重复！", request.getLoginUser().getLocale());
            }*/
            if (storageIncSyncSaveRequest.getCpCShopId() == null) {
                AssertUtils.logAndThrow("平台店铺不能为空！", request.getLoginUser().getLocale());
            }
            /*if (CollectionUtils.isEmpty(subObjectList)) {
                AssertUtils.logAndThrow("明细信息不能为空！", request.getLoginUser().getLocale());
            }*/
            cpCShopId = storageIncSyncSaveRequest.getCpCShopId();
        }
        v14.setData(cpCShopId);
        if (CollectionUtils.isNotEmpty(subObjectList)) {
            for (SgBChannelStorageIncSyncItemSaveRequest storageIncSyncItemSaveRequest : subObjectList) {
                if (storageIncSyncItemSaveRequest.getId() > 0L) {
                    SgBChannelStorageIncSyncItem storageIncSyncItemOld = storageIncSyncItemMapper.selectById(storageIncSyncItemSaveRequest.getId());
                    //明细同步状态
                    if (SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_IN.equals(storageIncSyncItemOld.getStatus())
                            || SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_POR_SUCC.equals(storageIncSyncItemOld.getStatus())
                            || SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_SUCC.equals(storageIncSyncItemOld.getStatus())) {
                        AssertUtils.logAndThrow("当前记录同步状态不符合，不允许保存！", request.getLoginUser().getLocale());
                    }
                } else {
                    //校验明细表的平台条码ID和平台商品ID是否同时为空或同时存在

                    if (StringUtils.isEmpty(storageIncSyncItemSaveRequest.getSkuId()) && StringUtils.isEmpty(storageIncSyncItemSaveRequest.getNumiid())
                            && storageIncSyncItemSaveRequest.getPsCSkuId() == null) {
                        AssertUtils.logAndThrow("条码或平台条码ID或平台商品ID不能同时为空！", request.getLoginUser().getLocale());
                    }

                    if (storageIncSyncItemSaveRequest.getQty() == null) {
                        AssertUtils.logAndThrow("数量为空，不允许保存！", request.getLoginUser().getLocale());
                    }
                    //原有明细中是否存在平台条码或者平台商品id是非同步状态的记录
                    Integer skuIdNumiIdCount = storageIncSyncItemMapper.selectCount(new QueryWrapper<SgBChannelStorageIncSyncItem>().lambda()
                            .eq(SgBChannelStorageIncSyncItem::getSgBChannelStorageIncSyncId, request.getObjId())
                            .eq(StringUtils.isNotEmpty(storageIncSyncItemSaveRequest.getSkuId()), SgBChannelStorageIncSyncItem::getSkuId, storageIncSyncItemSaveRequest.getSkuId())
                            .eq(StringUtils.isNotEmpty(storageIncSyncItemSaveRequest.getNumiid()), SgBChannelStorageIncSyncItem::getNumiid, storageIncSyncItemSaveRequest.getNumiid())
                            .eq(storageIncSyncItemSaveRequest.getPsCProId() != null, SgBChannelStorageIncSyncItem::getNumiid, storageIncSyncItemSaveRequest.getNumiid())
                            .eq(SgBChannelStorageIncSyncItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                            .ne(SgBChannelStorageIncSyncItem::getStatus, SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_UN));
                    if (skuIdNumiIdCount > 0) {
                        AssertUtils.logAndThrow("明细中已经存在一条平台条码ID为" + storageIncSyncItemSaveRequest.getSkuId()
                                + "平台商品ID为" + storageIncSyncItemSaveRequest.getNumiid() + "的记录，且不是非同步状态，不允许保存！", request.getLoginUser().getLocale());
                    }
                }
            }
        }
        return v14;
    }
}
