package com.burgeon.r3.sg.channel.validate.omni;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.channel.model.dto.omni.SgBOmniChannelProductDto;
import com.burgeon.r3.sg.channel.model.request.omni.SgBOmniChannelProductQueryRequest;
import com.burgeon.r3.sg.channel.services.omni.SgBOmniChannelProductService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.omni.SgBOmniChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 渠道商品新增/作废，平台id和商品id不空通过验证
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/28 11:36
 */
@Slf4j
@Component
public class SgBOmniChannelProductSaveValidator extends BaseSingleValidator<SgBOmniChannelProductDto> {

    @Autowired
    private SgBOmniChannelProductService service;

    @Override
    public String getValidatorMsgName() {
        return "全渠道商品保存";
    }

    @Override
    public Class<?> getValidatorClass() {
        return SgBOmniChannelProductSaveValidator.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgBOmniChannelProductDto mainObject, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过", loginUser.getLocale()));

        //查询平台店铺商品
        SgBOmniChannelProductQueryRequest request = new SgBOmniChannelProductQueryRequest();
        request.setCpCShopId(mainObject.getCpCShopId());
        request.setNumiid(mainObject.getNumiid());
        request.setId(mainObject.getId());

        if (log.isDebugEnabled()) {
            log.debug("Start SgBOmniChannelProductSaveValidator.validateMainTable:object={}", JSONObject.toJSONString(mainObject));
        }

        //前端可能只向后端传发生修改的值,只在关键字段发生变动触发验证
        boolean isCheck = (mainObject.getId() > 0L
                && ((Objects.nonNull(mainObject.getCpCShopId()) && mainObject.getCpCShopId() > 0L)
                || StringUtils.isNotEmpty(mainObject.getNumiid())))
                || mainObject.getId() < 1L;
        if (isCheck) {
            ValueHolderV14<List<SgBOmniChannelProduct>> checkExists = service.checkExists(request);
            if (CollectionUtils.isNotEmpty(checkExists.getData())) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("此条记录已存在，不允许重复添加"));
                return vh;
            }

            ValueHolderV14<List<SgBChannelProduct>> result = service.queryOmniChannelProduct(request);
            if (CollectionUtils.isEmpty(result.getData())) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("店铺平台商品ID不存在，不允许保存"));
                return vh;
            }
            String saStoreType = result.getData().get(0).getSaStoreType();
            if (StringUtils.isNotEmpty(saStoreType) && !SgConstants.SA_STORE_TYPE_ACTIVITY.equals(saStoreType)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("平台商品不为活动商品，不允许保存"));
                return vh;
            }
        }
        return vh;
    }
}
