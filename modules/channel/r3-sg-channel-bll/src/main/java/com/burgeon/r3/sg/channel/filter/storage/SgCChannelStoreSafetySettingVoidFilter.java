package com.burgeon.r3.sg.channel.filter.storage;

import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.model.dto.storage.SgCChannelStoreSafetySettingDto;
import com.burgeon.r3.sg.channel.model.dto.storage.SgCChannelStoreSafetySettingItemDto;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleItemFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Description 平台店铺安全库存作废
 * <AUTHOR>
 * @Date 2021/6/23 14:23
 * @Version 1.0
 **/

@Slf4j
@Component
public class SgCChannelStoreSafetySettingVoidFilter extends BaseSingleItemFilter<SgCChannelStoreSafetySettingDto, SgCChannelStoreSafetySettingItemDto> {

    @Override
    public String getFilterMsgName() {
        return "批量设置平台店铺安全库存保存";
    }

    @Override
    public Class<?> getFilterClass() {
        return SgCChannelStoreSafetySettingVoidFilter.class;
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgCChannelStoreSafetySettingDto mainObject, User loginUser) {
        mainObject.setStatus(SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_STATUS_VOID);
        Long loginUserId = loginUser.getId() == null ? null : loginUser.getId().longValue();
        mainObject.setDelerId(loginUserId);
        mainObject.setDelerEname(loginUser.getEname());
        mainObject.setDelTime(new Date());

        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("before main table success"));
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgCChannelStoreSafetySettingDto mainObject, User loginUser) {
        return null;
    }

    @Override
    public ValueHolderV14 execBeforeSubTable(SgCChannelStoreSafetySettingDto mainObject, List<SgCChannelStoreSafetySettingItemDto> subObjectList, User loginUser) {
        return null;
    }

    @Override
    public ValueHolderV14 execAfterSubTable(SgCChannelStoreSafetySettingDto mainObject, List<SgCChannelStoreSafetySettingItemDto> subObjectList, User loginUser) {
        return null;
    }
}
