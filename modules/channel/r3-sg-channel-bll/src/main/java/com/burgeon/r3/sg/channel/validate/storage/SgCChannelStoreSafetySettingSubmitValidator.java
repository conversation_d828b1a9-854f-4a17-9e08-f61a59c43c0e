package com.burgeon.r3.sg.channel.validate.storage;

import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.storage.SgCChannelStoreSafetySettingMapper;
import com.burgeon.r3.sg.channel.model.dto.storage.SgCChannelStoreSafetySettingDto;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgCChannelStoreSafetySetting;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * @Description 平台店铺安全库存批量设置
 * <AUTHOR>
 * @Date 2021/6/23 9:51
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgCChannelStoreSafetySettingSubmitValidator extends BaseSingleValidator<SgCChannelStoreSafetySettingDto> {

    @Autowired
    private SgCChannelStoreSafetySettingMapper storeSafetySettingMapper;

    @Override
    public String getValidatorMsgName() {
        return "平台店铺安全库存批量设置审核";
    }

    @Override
    public Class<?> getValidatorClass() {
        return SgCChannelStoreSafetySettingSubmitValidator.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCChannelStoreSafetySettingDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        SgCChannelStoreSafetySetting sgCChannelStoreSafetySetting = storeSafetySettingMapper.selectById(mainObject.getId());
        if(sgCChannelStoreSafetySetting == null){
            return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前记录已不存在！", loginUser.getLocale()));
        }
        if(SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_STATUS_SUBMIT == sgCChannelStoreSafetySetting.getStatus()){
            return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前记录已审核，不允许重复审核！", loginUser.getLocale()));
        }
        if(SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_STATUS_VOID == sgCChannelStoreSafetySetting.getStatus()){
            return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前记录已作废，不允许审核！", loginUser.getLocale()));
        }

        return v14;
    }
}
