package com.burgeon.r3.sg.channel.validate.gradient;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.model.request.record.SgCommRecordData;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelStorageSyncProGradientStrategyMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelStorageSyncProGradientStrategyDTO;
import com.burgeon.r3.sg.channel.services.strategy.SgCChannelStorageSyncProGradientStrategyStockSyncService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.gradient.SgCChannelStorageSyncProGradientStrategy;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.ps.api.table.PsCSku;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 平台库存同步梯度策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SgCChannelStorageSyncProGradientStrategyVoidValidator extends BaseSingleValidator<SgCChannelStorageSyncProGradientStrategyDTO> {

    @Autowired
    private SgCChannelStorageSyncProGradientStrategyStockSyncService service;

    @Override
    public String getValidatorMsgName() {
        return "平台商品库存同步梯度策略作废";
    }

    @Override
    public Class<?> getValidatorClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCChannelStorageSyncProGradientStrategyDTO mainObject, User loginUser) {
        try {
            HashMap<String, List<SgCommRecordData>> map = new HashMap<>();
            List<SgCommRecordData> list = new ArrayList<>();

            SgCommRecordData data = new SgCommRecordData();
            data.setColumnName("作废");
            data.setMessage(loginUser.getName() + "作废了当前数据");

            list.add(data);
            map.put("void", list);
            service.saveLog(mainObject.getId(), map, loginUser);
        } catch (Exception e) {
            log.error("平台商品库存同步梯度策略作废异常");
        }


        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过", loginUser.getLocale()));
    }
}
