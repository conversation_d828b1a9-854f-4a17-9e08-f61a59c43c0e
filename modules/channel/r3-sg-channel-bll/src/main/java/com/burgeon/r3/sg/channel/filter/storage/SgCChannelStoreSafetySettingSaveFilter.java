package com.burgeon.r3.sg.channel.filter.storage;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgCChannelStoreSafetySettingItemMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgCChannelStoreSafetySettingMapper;
import com.burgeon.r3.sg.channel.model.dto.storage.SgCChannelStoreSafetySettingDto;
import com.burgeon.r3.sg.channel.model.dto.storage.SgCChannelStoreSafetySettingItemDto;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgCChannelStoreSafetySettingItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.data.basic.model.request.ProInfoQueryRequest;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.ps.api.table.PsCPro;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleItemFilter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * @Description 平台店铺安全库存
 * <AUTHOR>
 * @Date 2021/6/23 14:23
 * @Version 1.0
 **/

@Slf4j
@Component
public class SgCChannelStoreSafetySettingSaveFilter extends BaseSingleItemFilter<SgCChannelStoreSafetySettingDto, SgCChannelStoreSafetySettingItemDto> {

    @Autowired
    private SgCChannelStoreSafetySettingMapper storeSafetySettingMapper;

    @Autowired
    private SgCChannelStoreSafetySettingItemMapper storeSafetySettingItemMapper;

    @Autowired
    private SgBChannelProductMapper sgBChannelProductMapper;

    @Override
    public String getFilterMsgName() {
        return "批量设置平台店铺安全库存保存";
    }

    @Override
    public Class<?> getFilterClass() {
        return SgCChannelStoreSafetySettingSaveFilter.class;
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgCChannelStoreSafetySettingDto mainObject, User loginUser) {
        if (mainObject.getId() < 0L) {
            mainObject.setStatus(SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_STATUS_UN);
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("before main table success"));
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgCChannelStoreSafetySettingDto mainObject, User loginUser) {
        return null;
    }


    @Override
    public ValueHolderV14 execBeforeSubTable(SgCChannelStoreSafetySettingDto mainObject, List<SgCChannelStoreSafetySettingItemDto> subObjectList, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("SgCChannelStoreSafetySettingSaveFilter.execBeforeSubTable:mainObject=:{},subObjectList:{};", JSONObject.toJSONString(mainObject), JSONObject.toJSONString(subObjectList));
        }
        for (SgCChannelStoreSafetySettingItemDto itemDto : subObjectList) {
            SgCChannelStoreSafetySettingItem sgCChannelStoreSafetySettingItem = null;
            if (SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_PRO_TYPE_PS_C_PRO.equals(itemDto.getProType())) {
                //商品类型：（1:商品编）
                //根据商品id获取商品信息
                List<Long> proList = new ArrayList();
                proList.add(itemDto.getPsCProId());
                BasicPsQueryService basicPsQueryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
                ProInfoQueryRequest proInfoQueryRequest = new ProInfoQueryRequest();
                proInfoQueryRequest.setProIdList(proList);
                List<PsCPro> proInfo = basicPsQueryService.getProInfo(proInfoQueryRequest);
                if (CollectionUtils.isEmpty(proInfo)) {
                    return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("商品编码：" + itemDto.getPsCProId() + "不存在！", loginUser.getLocale()));
                }
                PsCPro psCPro = proInfo.get(0);
                itemDto.setPsCProEcode(psCPro.getEcode());
                itemDto.setPsCProEname(psCPro.getEname());
                if (mainObject.getId() > 0L) {
                    sgCChannelStoreSafetySettingItem = storeSafetySettingItemMapper.selectOne(new LambdaQueryWrapper<SgCChannelStoreSafetySettingItem>()
                            .eq(SgCChannelStoreSafetySettingItem::getSgCChannelStoreSafetySettingId, mainObject.getId())
                            .eq(SgCChannelStoreSafetySettingItem::getCpCShopId, itemDto.getCpCShopId())
                            .eq(itemDto.getPsCProId() != null, SgCChannelStoreSafetySettingItem::getPsCProId, itemDto.getPsCProId())
                            .eq(SgCChannelStoreSafetySettingItem::getProType, itemDto.getProType()));
                }

            } else if (SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_PRO_TYPE_PS_C_SKU.equals(itemDto.getProType())) {
                //商品类型：（2:条码 ）
                List<Long> psCSkuIdList = new ArrayList();
                psCSkuIdList.add(itemDto.getPsCSkuId());
                //根据条码id获取条码信息
                BasicPsQueryService basicPsQueryService = ApplicationContextHandle.getBean(BasicPsQueryService.class);
                SkuInfoQueryRequest skuQuery = new SkuInfoQueryRequest();
                skuQuery.setSkuIdList(psCSkuIdList);
                HashMap<Long, PsCProSkuResult> skuInfo = basicPsQueryService.getSkuInfo(skuQuery);
                if (!skuInfo.containsKey(itemDto.getPsCSkuId())) {
                    return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("条码：" + itemDto.getPsCSkuId() + "不存在！", loginUser.getLocale()));
                }
                PsCProSkuResult psCProSkuResult = skuInfo.get(itemDto.getPsCSkuId());
                itemDto.setPsCSkuEcode(psCProSkuResult.getSkuEcode());
                itemDto.setCpCShopTitle(psCProSkuResult.getCpCShopTitle());
                if (mainObject.getId() > 0L) {
                    sgCChannelStoreSafetySettingItem = storeSafetySettingItemMapper.selectOne(new LambdaQueryWrapper<SgCChannelStoreSafetySettingItem>()
                            .eq(SgCChannelStoreSafetySettingItem::getSgCChannelStoreSafetySettingId, mainObject.getId())
                            .eq(SgCChannelStoreSafetySettingItem::getCpCShopId, itemDto.getCpCShopId())
                            .eq(itemDto.getPsCSkuId() != null, SgCChannelStoreSafetySettingItem::getPsCSkuId, itemDto.getPsCSkuId())
                            .eq(SgCChannelStoreSafetySettingItem::getProType, itemDto.getProType()));
                }

            } else if (SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_PRO_TYPE_NUMIID.equals(itemDto.getProType())) {
                //商品类型：（3:平台商品 ）

                List<SgBChannelProduct> sgBChannelProducts = sgBChannelProductMapper.selectList(new LambdaQueryWrapper<SgBChannelProduct>()
                        .eq(SgBChannelProduct::getCpCShopId, itemDto.getCpCShopId())
                        .eq(SgBChannelProduct::getNumiid, itemDto.getNumiid())
                        .eq(SgBChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y));
                if (CollectionUtils.isEmpty(sgBChannelProducts)) {
                    return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前平台店铺下平台商品ID不存在！", loginUser.getLocale()));
                }
                SgBChannelProduct sgBChannelProduct = sgBChannelProducts.get(0);

                itemDto.setCpCShopTitle(sgBChannelProduct.getCpCShopTitle());

                if (mainObject.getId() > 0L) {
                    sgCChannelStoreSafetySettingItem = storeSafetySettingItemMapper.selectOne(new LambdaQueryWrapper<SgCChannelStoreSafetySettingItem>()
                            .eq(SgCChannelStoreSafetySettingItem::getSgCChannelStoreSafetySettingId, mainObject.getId())
                            .eq(SgCChannelStoreSafetySettingItem::getCpCShopId, itemDto.getCpCShopId())
                            .eq(itemDto.getNumiid() != null, SgCChannelStoreSafetySettingItem::getNumiid, itemDto.getNumiid())
                            .eq(SgCChannelStoreSafetySettingItem::getProType, itemDto.getProType()));
                }

            } else if (SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_PRO_TYPE_SKU_ID.equals(itemDto.getProType())) {
                //商品类型：（4:平台条码）

                List<SgBChannelProduct> sgBChannelProducts = sgBChannelProductMapper.selectList(new LambdaQueryWrapper<SgBChannelProduct>()
                        .eq(SgBChannelProduct::getCpCShopId, itemDto.getCpCShopId())
                        .eq(SgBChannelProduct::getSkuId, itemDto.getSkuId())
                        .eq(SgBChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y));
                if (CollectionUtils.isEmpty(sgBChannelProducts)) {
                    return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前平台店铺下平台条码ID不存在！", loginUser.getLocale()));
                }
                SgBChannelProduct sgBChannelProduct = sgBChannelProducts.get(0);
                itemDto.setCpCShopTitle(sgBChannelProduct.getCpCShopTitle());

                if (mainObject.getId() > 0L) {
                    sgCChannelStoreSafetySettingItem = storeSafetySettingItemMapper.selectOne(new LambdaQueryWrapper<SgCChannelStoreSafetySettingItem>()
                            .eq(SgCChannelStoreSafetySettingItem::getSgCChannelStoreSafetySettingId, mainObject.getId())
                            .eq(SgCChannelStoreSafetySettingItem::getCpCShopId, itemDto.getCpCShopId())
                            .eq(itemDto.getSkuId() != null, SgCChannelStoreSafetySettingItem::getSkuId, itemDto.getSkuId())
                            .eq(SgCChannelStoreSafetySettingItem::getProType, itemDto.getProType()));
                }

            }
            if (log.isDebugEnabled()) {
                log.debug("SgCChannelStoreSafetySettingSaveFilter.execBeforeSubTable:sgCChannelStoreSafetySettingItem={};", JSONObject.toJSONString(sgCChannelStoreSafetySettingItem));
            }
            if (sgCChannelStoreSafetySettingItem != null) {
                itemDto.setQtySafety(sgCChannelStoreSafetySettingItem.getQtySafety().add(itemDto.getQtySafety()));
                itemDto.setId(sgCChannelStoreSafetySettingItem.getId());
            }
        }


        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("before sub table success"));
    }

    @Override
    public ValueHolderV14 execAfterSubTable(SgCChannelStoreSafetySettingDto mainObject, List<SgCChannelStoreSafetySettingItemDto> subObjectList, User loginUser) {
        return null;
    }
}
