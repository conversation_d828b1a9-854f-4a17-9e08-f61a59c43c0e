package com.burgeon.r3.sg.channel.validate.strategy;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyItemMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelRatioStrategyDTO;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelRatioStrategyItemDTO;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.ratiostrategy.SgCChannelRatioStrategy;
import com.burgeon.r3.sg.core.model.table.channel.ratiostrategy.SgCChannelRatioStrategyItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleItemValidator;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/6/4 14:21
 */
@Slf4j
@Component
public class SgCChannelRatioStrategySaveValidator extends BaseSingleItemValidator<SgCChannelRatioStrategyDTO,
        SgCChannelRatioStrategyItemDTO> {

    @Autowired
    private SgCChannelRatioStrategyMapper mapper;

    @Autowired
    private SgCChannelRatioStrategyItemMapper itemMapper;

    @Override
    public String getValidatorMsgName() {
        return "按比例同步策略";
    }

    @Override
    public Class getValidatorClass() {
        return SgCChannelRatioStrategySaveValidator.class;
    }

    /**
     * 校验头表
     */
    @Override
    public ValueHolderV14 validateMainTable(SgCChannelRatioStrategyDTO mainObject, User loginUser) {
        log.info(LogUtil.format("SgCChannelRatioStrategySaveValidator.validateMainTable mainObject:{}",
                "SgCChannelRatioStrategySaveValidator.validateMainTable"), JSONObject.toJSONString(mainObject));

        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        Long id = mainObject.getId();
        BigDecimal ratio = mainObject.getSgCSharePoolRatio();
        if (id < 0L) {
            List<SgCChannelRatioStrategy> ratioStrategies =
                    mapper.selectList(new QueryWrapper<SgCChannelRatioStrategy>().lambda()
                            .eq(SgCChannelRatioStrategy::getDistCodeLevelTwo, mainObject.getDistCodeLevelTwo())
                            .eq(SgCChannelRatioStrategy::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (ratioStrategies.size() > 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("二级分货组织编码：" + mainObject.getDistCodeLevelTwo() +
                        "已存在有效的按比例同步策略，不允许重复设置！", loginUser.getLocale()));
                return v14;
            }
        }
        if (ratio != null) {
            if (ratio.compareTo(BigDecimal.ZERO) < 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("共享池同步比例（%）请设置0或以上的数字！", loginUser.getLocale()));
            }
        }
        return v14;
    }

    /**
     * 校验明细
     */
    @Override
    public ValueHolderV14 validateSubTable(SgCChannelRatioStrategyDTO mainObject,
                                           List<SgCChannelRatioStrategyItemDTO> subObjectList, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        Long id = mainObject.getId();
        List<SgCChannelRatioStrategyItem> strategyItems = itemMapper.selectList(new LambdaQueryWrapper<SgCChannelRatioStrategyItem>()
                .eq(SgCChannelRatioStrategyItem::getSgCChannelRatioStrategyId, id));
        Map<Long, SgCChannelRatioStrategyItem> itemMap = strategyItems.stream()
                .collect(Collectors.toMap(SgCChannelRatioStrategyItem::getSgCSaStoreId, Function.identity()));
        if (CollectionUtils.isNotEmpty(subObjectList)) {
            for (SgCChannelRatioStrategyItemDTO itemDTO : subObjectList) {
                Long itemId = itemDTO.getId();
                BigDecimal ratio = itemDTO.getRatio();
                Long saStoreId = itemDTO.getSgCSaStoreId();
                if (ratio != null) {
                    if (ratio.compareTo(BigDecimal.ZERO) < 0) {
                        v14.setCode(ResultCode.FAIL);
                        v14.setMessage(Resources.getMessage("配销仓比例（%）请设置0或以上的数字！", loginUser.getLocale()));
                        return v14;
                    }
                }
                if (id > 0L) {
                    if (itemId < 0L) {
                        //查询是否存在对应配销仓
                        SgCChannelRatioStrategyItem strategyItem = itemMap.get(saStoreId);
                        if (strategyItem != null) {
                            if (strategyItem.getIsactive().equals(SgConstants.IS_ACTIVE_N)) {
                                //存在 但不可用
                                v14.setCode(ResultCode.FAIL);
                                v14.setMessage(Resources.getMessage("明细中已存在当前配销仓：" +
                                        strategyItem.getSgCSaStoreEname() + " 记录，状态为不可用，请先启用！", loginUser.getLocale()));
                                return v14;
                            }
                        }
                    }
                }
            }
        }
        return v14;
    }
}
