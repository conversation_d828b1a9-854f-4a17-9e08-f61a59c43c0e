package com.burgeon.r3.sg.channel.services.sale;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.sale.SgBChannelAdvanceSaleItemMapper;
import com.burgeon.r3.sg.channel.mapper.sale.SgBChannelAdvanceSaleMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.channel.sale.SgBChannelAdvanceSale;
import com.burgeon.r3.sg.core.model.table.channel.sale.SgBChannelAdvanceSaleItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/25 13:21
 */
@Slf4j
@Component
public class SgBChannelAdvanceSaleSubmitService {
    @Autowired
    SgBChannelAdvanceSaleMapper mapper;
    @Autowired
    SgBChannelAdvanceSaleItemMapper itemMapper;

    /**
     * 渠道预售活动页面审核
     *
     * @param session 入参
     * @return 出参
     */
    ValueHolder submitAdvanceSale(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        SgBChannelAdvanceSaleSubmitService service = ApplicationContextHandle.getBean(SgBChannelAdvanceSaleSubmitService.class);
        return R3ParamUtils.convertV14WithResult(service.submitTransfer(request));

    }

    /**
     * 渠道预售活动审核
     *
     * @param request 入参
     * @return 出参
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> submitTransfer(SgR3BaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelAdvanceSaleSubmitService.submitTransfer:request{}", JSONObject.toJSONString(request));
        }

        String lockKsy = SgConstants.SG_B_CHANNEL_ADVANCE_SALE + ":" + request.getObjId();
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "审核成功!");
        SgBChannelAdvanceSale stoChannelAdvanceSale = checkParams(request);
        SgRedisLockUtils.lock(lockKsy);
        try {
            SgBChannelAdvanceSale update = new SgBChannelAdvanceSale();
            StorageUtils.setBModelDefalutDataByUpdate(update, request.getLoginUser());
            update.setStatus(SgChannelConstants.BILL_STATUS_SUBMIT);
            update.setId(request.getObjId());
            update.setTotRowNum(stoChannelAdvanceSale.getTotRowNum());
            mapper.updateById(update);

        } catch (Exception e) {
            AssertUtils.logAndThrowException(e.getMessage(), e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        return v14;
    }


    /**
     * 参数校验
     *
     * @param request 入参
     * @return 出参
     */
    public SgBChannelAdvanceSale checkParams(SgR3BaseRequest request) {
        SgBChannelAdvanceSale stoChannelAdvanceSale = mapper.selectById(request.getObjId());
        if (SgConstants.IS_ACTIVE_N.equals(stoChannelAdvanceSale.getIsactive())) {
            AssertUtils.logAndThrow("当前记录已作废,不允许审核！", request.getLoginUser().getLocale());

        } else if (SgChannelConstants.BILL_STATUS_UNSUBMIT != stoChannelAdvanceSale.getStatus()) {
            AssertUtils.logAndThrow("当前单据状态不允许审核！", request.getLoginUser().getLocale());
        }
        //删除条码为0的
        itemMapper.delete(new QueryWrapper<SgBChannelAdvanceSaleItem>()
                .lambda().eq(SgBChannelAdvanceSaleItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgBChannelAdvanceSaleItem::getSgBChannelAdvanceSaleId, request.getObjId())
                .eq(SgBChannelAdvanceSaleItem::getQty, BigDecimal.ZERO));

        List<SgBChannelAdvanceSaleItem> itemList = itemMapper.selectList(new QueryWrapper<SgBChannelAdvanceSaleItem>()
                .lambda().eq(SgBChannelAdvanceSaleItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgBChannelAdvanceSaleItem::getSgBChannelAdvanceSaleId, request.getObjId()));
        if (CollectionUtils.isEmpty(itemList)) {
            AssertUtils.logAndThrow("当前单据无明细,不允许审核！", request.getLoginUser().getLocale());
        }

        long count = itemList.stream().filter(o -> BigDecimal.ZERO.compareTo(o.getQty()) > 0).count();
        //数量小于0
        if (count > 0L) {
            AssertUtils.logAndThrow("存在明细记录数量小于0，不允许！", request.getLoginUser().getLocale());
        }

        stoChannelAdvanceSale.setTotRowNum(itemList.size());
        return stoChannelAdvanceSale;
    }

}
