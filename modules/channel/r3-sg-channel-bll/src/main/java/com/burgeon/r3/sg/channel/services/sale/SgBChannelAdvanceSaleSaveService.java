package com.burgeon.r3.sg.channel.services.sale;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.sale.SgBChannelAdvanceSaleItemMapper;
import com.burgeon.r3.sg.channel.mapper.sale.SgBChannelAdvanceSaleMapper;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductQueryRequest;
import com.burgeon.r3.sg.channel.model.request.sale.SgBChannelAdvanceSaleBillSaveRequest;
import com.burgeon.r3.sg.channel.model.request.sale.SgBChannelAdvanceSaleItemSaveRequest;
import com.burgeon.r3.sg.channel.model.request.sale.SgBChannelAdvanceSaleSaveRequest;
import com.burgeon.r3.sg.channel.services.product.SgChannelProductQueryService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.sale.SgBChannelAdvanceSale;
import com.burgeon.r3.sg.core.model.table.channel.sale.SgBChannelAdvanceSaleItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2021-06-08 16:21
 * @Description:
 */

@Slf4j
@Component
public class SgBChannelAdvanceSaleSaveService {

    @Autowired
    private SgBChannelAdvanceSaleMapper mapper;
    @Autowired
    private SgBChannelAdvanceSaleItemMapper itemMapper;

    /**
     * 渠道预售活动页面新增/保存
     *
     * @param session 入参
     * @return 出参
     */
    public ValueHolder save(QuerySession session) {
        SgBChannelAdvanceSaleBillSaveRequest request = R3ParamUtils.parseSaveObject(session,
                SgBChannelAdvanceSaleBillSaveRequest.class);
        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelAdvanceSaleSaveService.save:param={}", JSONObject.toJSONString(request));
        }
        request.setR3(true);
        SgBChannelAdvanceSaleSaveService service =
                ApplicationContextHandle.getBean(SgBChannelAdvanceSaleSaveService.class);
        return R3ParamUtils.convertV14WithResult(service.save(request));
    }

    /**
     * 渠道预售活动新增/保存
     *
     * @param request 入参
     * @return 出参
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> save(SgBChannelAdvanceSaleBillSaveRequest request) {
        SgBChannelAdvanceSale sgBChannelAdvanceSale = checkParams(request);
        if (request.getObjId() == null || request.getObjId() < 0) {
            return insert(request);
        } else {
            return update(request, sgBChannelAdvanceSale);
        }
    }

    /**
     * 渠道预售活动新增
     *
     * @param request 入参
     * @return 出参
     */
    private ValueHolderV14<SgR3BaseResult> insert(SgBChannelAdvanceSaleBillSaveRequest request) {
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "保存成功！");
        SgBChannelAdvanceSaleSaveRequest saleSaveRequest = request.getSaleSaveRequest();
        SgBChannelAdvanceSale stoChannelAdvanceSale = new SgBChannelAdvanceSale();

        BeanUtils.copyProperties(saleSaveRequest, stoChannelAdvanceSale);
        StorageUtils.setBModelDefalutData(stoChannelAdvanceSale, request.getLoginUser());
        List<SgBChannelAdvanceSaleItemSaveRequest> items = request.getSaleItemSaveRequests();
        BigDecimal totQty = BigDecimal.ZERO;
        BigDecimal totAmt = BigDecimal.ZERO;
        Integer totRowNum = 0;
        Long objId = ModelUtil.getSequence(SgConstants.SG_B_CHANNEL_ADVANCE_SALE);
        stoChannelAdvanceSale.setId(objId);
        stoChannelAdvanceSale.setStatus(SgChannelConstants.BILL_STATUS_UNSUBMIT);
        String billNo = SgStoreUtils.getBillNo(SgChannelConstants.SEQ_SG_B_CHANNEL_ADVANCE_SALE,
                SgConstants.SG_B_CHANNEL_ADVANCE_SALE.toUpperCase(), stoChannelAdvanceSale,
                request.getLoginUser().getLocale());
        stoChannelAdvanceSale.setBillNo(billNo);

        List<SgBChannelAdvanceSaleItem> insertItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(items)) {
            for (SgBChannelAdvanceSaleItemSaveRequest item : items) {
                CommonCacheValUtils.setSkuInfo(null, item.getPsCSkuEcode(), item);
                SgBChannelAdvanceSaleItem insertItem = new SgBChannelAdvanceSaleItem();
                BeanUtils.copyProperties(item, insertItem);
                Long itemObjId = ModelUtil.getSequence(SgConstants.SG_B_CHANNEL_ADVANCE_SALE_ITEM);
                insertItem.setId(itemObjId);
                insertItem.setSgBChannelAdvanceSaleId(objId);
                if (insertItem.getQty() == null) {
                    insertItem.setQty(BigDecimal.ONE);
                }
                StorageUtils.setBModelDefalutData(insertItem, request.getLoginUser());
                insertItem.setAmtList(insertItem.getQty().multiply(insertItem.getPriceList()));
                totQty = totQty.add(insertItem.getQty());
                totAmt = totAmt.add(insertItem.getAmtList());
                insertItem.setQtySold(BigDecimal.ZERO);
                insertItem.setQtyOut(BigDecimal.ZERO);
                insertItem.setQtyRemain(insertItem.getQty());
                insertItem.setAmtListRemain(insertItem.getQty().multiply(insertItem.getPriceList()));
                insertItem.setAmtListSold(BigDecimal.ZERO);
                insertItem.setAmtListOut(BigDecimal.ZERO);
                totRowNum++;
                insertItemList.add(insertItem);
            }
        }

        stoChannelAdvanceSale.setTotRowNum(totRowNum);
        stoChannelAdvanceSale.setTotAmtList(totAmt);
        stoChannelAdvanceSale.setTotAmtListRemain(totAmt);
        stoChannelAdvanceSale.setTotAmtListSold(BigDecimal.ZERO);
        stoChannelAdvanceSale.setTotAmtLsitOut(BigDecimal.ZERO);
        stoChannelAdvanceSale.setTotQty(totQty);
        stoChannelAdvanceSale.setTotQtyRemain(totQty);
        stoChannelAdvanceSale.setTotQtySold(BigDecimal.ZERO);
        stoChannelAdvanceSale.setTotQtyOut(BigDecimal.ZERO);
        stoChannelAdvanceSale.setSynchronousStatus(SgChannelConstants.SYNCHRONOUS_STATUS_NO_SYN);
        mapper.insert(stoChannelAdvanceSale);

        /*批量新增500/次*/
        List<List<SgBChannelAdvanceSaleItem>> insertPageList =
                StorageUtils.getBaseModelPageList(insertItemList, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
        for (List<SgBChannelAdvanceSaleItem> pageList : insertPageList) {
            if (CollectionUtils.isEmpty(pageList)) {
                continue;
            }
            int insertResult = itemMapper.batchInsert(pageList);
            if (insertResult != pageList.size()) {
                AssertUtils.logAndThrow("保存渠道预售明细异常！", request.getLoginUser().getLocale());
            }
        }

        SgR3BaseResult baseResult = new SgR3BaseResult();
        baseResult.setDataJo(objId, SgConstants.SG_B_CHANNEL_ADVANCE_SALE.toUpperCase());
        v14.setData(baseResult);
        return v14;
    }

    /**
     * 渠道预售活动-更新
     *
     * @param request               入参
     * @param stoChannelAdvanceSale 主表
     * @return 出参
     */
    private ValueHolderV14<SgR3BaseResult> update(SgBChannelAdvanceSaleBillSaveRequest request,
                                                  SgBChannelAdvanceSale stoChannelAdvanceSale) {
        String lockKsy = SgConstants.SG_B_CHANNEL_ADVANCE_SALE + ":" + request.getObjId();
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "保存成功！");
        SgRedisLockUtils.lock(lockKsy);
        try {
            SgBChannelAdvanceSaleSaveRequest saleSaveRequest = request.getSaleSaveRequest();
            SgBChannelAdvanceSale update = new SgBChannelAdvanceSale();

            if (saleSaveRequest != null) {
                BeanUtils.copyProperties(saleSaveRequest, update);
            }
            update.setId(request.getObjId());

            List<SgBChannelAdvanceSaleItemSaveRequest> items = request.getSaleItemSaveRequests();
            if (CollectionUtils.isNotEmpty(items)) {

                List<SgBChannelAdvanceSaleItem> itemList =
                        itemMapper.selectList(new QueryWrapper<SgBChannelAdvanceSaleItem>()
                                .lambda().eq(SgBChannelAdvanceSaleItem::getSgBChannelAdvanceSaleId, request.getObjId()));
                Map<String, SgBChannelAdvanceSaleItem> map = itemList.stream()
                        .collect(Collectors.toMap(SgBChannelAdvanceSaleItem::getPsCSkuEcode, item -> item));
                Map<Long, SgBChannelAdvanceSaleItem> mapId = itemList.stream()
                        .collect(Collectors.toMap(SgBChannelAdvanceSaleItem::getId, item -> item));

                BigDecimal totQty = stoChannelAdvanceSale.getTotQty();
                BigDecimal totQtyRemain = stoChannelAdvanceSale.getTotQtyRemain();
                BigDecimal totAmt = stoChannelAdvanceSale.getTotAmtList();
                BigDecimal totAmtListRemain = stoChannelAdvanceSale.getTotAmtListRemain();
                Integer totRowNum = stoChannelAdvanceSale.getTotRowNum();
                List<SgBChannelAdvanceSaleItem> insertItemList = new ArrayList<>();

                for (SgBChannelAdvanceSaleItemSaveRequest item : items) {
                    Long itemId = item.getId();
                    //编辑行编辑，直接覆盖 不需要矩阵
                    if (itemId != null && itemId > 0) {
                        SgBChannelAdvanceSaleItem queryItem = mapId.get(item.getId());
                        if (queryItem != null) {

                            SgBChannelAdvanceSaleItem updateItem = new SgBChannelAdvanceSaleItem();
                            updateItem.setId(queryItem.getId());
                            BigDecimal qty = item.getQty();
                            updateItem.setQty(qty);
                            updateItem.setQtyRemain(qty.subtract(queryItem.getQtySold()));
                            updateItem.setAmtList(updateItem.getQty().multiply(queryItem.getPriceList()));
                            updateItem.setAmtListRemain(updateItem.getQtyRemain().multiply(queryItem.getPriceList()));
                            StorageUtils.setBModelDefalutDataByUpdate(updateItem, request.getLoginUser());
                            itemMapper.updateById(updateItem);

                            totAmt = totAmt.add(updateItem.getAmtList().subtract(queryItem.getAmtList()));
                            totQty = totQty.add(updateItem.getQty().subtract(queryItem.getQty() == null
                                    ? BigDecimal.ZERO : queryItem.getQty()));
                            totQtyRemain =
                                    totQtyRemain.add(updateItem.getQtyRemain().subtract(queryItem.getQtyRemain() == null
                                            ? BigDecimal.ZERO : queryItem.getQtyRemain()));
                            totAmtListRemain = totAmtListRemain.add(updateItem.getAmtListRemain()
                                    .subtract(queryItem.getAmtListRemain()));

                        } else {
                            AssertUtils.logAndThrow("当前明细不存在！", request.getLoginUser().getLocale());
                        }

                    } else if (map.get(item.getPsCSkuEcode()) != null) {
                        SgBChannelAdvanceSaleItem queryItem = map.get(item.getPsCSkuEcode());
                        SgBChannelAdvanceSaleItem updateItem = new SgBChannelAdvanceSaleItem();
                        updateItem.setId(queryItem.getId());
                        BigDecimal qty = queryItem.getQty().add(item.getQty() == null ? BigDecimal.ONE : item.getQty());
                        updateItem.setQty(qty);
                        updateItem.setQtyRemain(qty.subtract(queryItem.getQtySold()));
                        updateItem.setAmtList(updateItem.getQty().multiply(queryItem.getPriceList()));
                        updateItem.setAmtListRemain(updateItem.getQtyRemain().multiply(queryItem.getPriceList()));
                        StorageUtils.setBModelDefalutDataByUpdate(updateItem, request.getLoginUser());
                        itemMapper.updateById(updateItem);
                        totAmt = totAmt.add(updateItem.getAmtList().subtract(queryItem.getAmtList()));
                        totQty = totQty.add(updateItem.getQty().subtract(queryItem.getQty() == null
                                ? BigDecimal.ZERO : queryItem.getQty()));
                        totQtyRemain =
                                totQtyRemain.add(updateItem.getQtyRemain().subtract(queryItem.getQtyRemain() == null
                                        ? BigDecimal.ZERO : queryItem.getQtyRemain()));
                        totAmtListRemain = totAmtListRemain.add(updateItem.getAmtListRemain()
                                .subtract(queryItem.getAmtListRemain()));

                    } else {
                        CommonCacheValUtils.setSkuInfo(null, item.getPsCSkuEcode(), item);
                        SgBChannelAdvanceSaleItem insertItem = new SgBChannelAdvanceSaleItem();
                        BeanUtils.copyProperties(item, insertItem);
                        insertItem.setSgBChannelAdvanceSaleId(request.getObjId());
                        Long itemObjId = ModelUtil.getSequence(SgConstants.SG_B_CHANNEL_ADVANCE_SALE_ITEM);
                        insertItem.setId(itemObjId);
                        StorageUtils.setBModelDefalutData(insertItem, request.getLoginUser());
                        if (insertItem.getQty() == null) {
                            insertItem.setQty(BigDecimal.ONE);
                        }

                        insertItem.setQtyOut(BigDecimal.ZERO);
                        insertItem.setQtyRemain(insertItem.getQty());
                        insertItem.setQtySold(BigDecimal.ZERO);
                        insertItem.setAmtList(insertItem.getQty().multiply(insertItem.getPriceList()));
                        insertItem.setAmtListRemain(insertItem.getAmtList());
                        insertItem.setAmtListSold(BigDecimal.ZERO);
                        insertItem.setAmtListOut(BigDecimal.ZERO);
                        totQty = totQty.add(insertItem.getQty());
                        totAmt = totAmt.add(insertItem.getAmtList());
                        totQtyRemain = totQtyRemain.add(insertItem.getQtyRemain());
                        totAmtListRemain = totAmtListRemain.add(insertItem.getAmtListRemain());
                        insertItemList.add(insertItem);
                        totRowNum++;
                    }
                }
                update.setId(request.getObjId());
                if (saleSaveRequest != null) {
                    BeanUtils.copyProperties(saleSaveRequest, update);
                }

                update.setTotRowNum(totRowNum);
                update.setTotAmtList(totAmt);
                update.setTotQty(totQty);
                update.setTotQtyRemain(totQtyRemain);
                update.setTotAmtListRemain(totAmtListRemain);

                /*批量新增500/次*/
                List<List<SgBChannelAdvanceSaleItem>> insertPageList =
                        StorageUtils.getBaseModelPageList(insertItemList, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
                for (List<SgBChannelAdvanceSaleItem> pageList : insertPageList) {
                    if (CollectionUtils.isEmpty(pageList)) {
                        continue;
                    }
                    int insertResult = itemMapper.batchInsert(pageList);
                    if (insertResult != pageList.size()) {
                        AssertUtils.logAndThrow("保存渠道预售活动明细异常！", request.getLoginUser().getLocale());
                    }
                }
            }

            StorageUtils.setBModelDefalutDataByUpdate(update, request.getLoginUser());
            mapper.updateById(update);

        } catch (Exception e) {
            AssertUtils.logAndThrowException("渠道预售保存异常", e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }

        return v14;
    }

    /**
     * 参数校验
     *
     * @param request 入参
     * @return 出参
     */
    private SgBChannelAdvanceSale checkParams(SgBChannelAdvanceSaleBillSaveRequest request) {
        SgBChannelAdvanceSaleSaveRequest saleSaveRequest = request.getSaleSaveRequest();
        List<SgBChannelAdvanceSaleItemSaveRequest> saleItemSaveRequests = request.getSaleItemSaveRequests();
        SgBChannelAdvanceSale sgStoChannelAdvanceSale = null;

        if (request.getObjId() != null && request.getObjId() > 0) {
            sgStoChannelAdvanceSale = mapper.selectById(request.getObjId());
            if (SgConstants.IS_ACTIVE_N.equals(sgStoChannelAdvanceSale.getIsactive())) {
                AssertUtils.logAndThrow("当前单据已作废不允许保存！", request.getLoginUser().getLocale());
            }
            if (SgChannelConstants.BILL_STATUS_UNSUBMIT != sgStoChannelAdvanceSale.getStatus()) {
                AssertUtils.logAndThrow("当前单据状态不允许保存！", request.getLoginUser().getLocale());
            }
        }

        if (!request.isR3()) {
            SgStoreUtils.checkR3BModelDefalut(request);
            AssertUtils.notNull(saleSaveRequest, "主表信息不能为空！");
            AssertUtils.notNull(saleItemSaveRequests, "明细信息不能为空！");
        }

        //页面上保存主表和明细不一定都会传 非页面必须传
        if (saleSaveRequest != null) {
            Date beginTime = saleSaveRequest.getBeginTime();
            Date endTime = saleSaveRequest.getEndTime();
            Date outDate = saleSaveRequest.getOutDate();
            Long cpShopId = saleSaveRequest.getCpCShopId();
            if (sgStoChannelAdvanceSale != null) {
                beginTime = beginTime != null ? beginTime : sgStoChannelAdvanceSale.getBeginTime();
                endTime = endTime != null ? endTime : sgStoChannelAdvanceSale.getEndTime();
                cpShopId = cpShopId != null ? cpShopId : sgStoChannelAdvanceSale.getCpCShopId();
                outDate = outDate != null ? outDate : sgStoChannelAdvanceSale.getOutDate();
            }
            CpShop shop = CommonCacheValUtils.getShopInfo(cpShopId);
            AssertUtils.notNull(shop, "对应平台店铺不存在！");
            //冗余字段
            saleSaveRequest.setCpCShopEcode(shop.getEcode());
            saleSaveRequest.setCpCShopEname(shop.getCpCShopTitle());
            AssertUtils.notNull(beginTime, "开始时间为空！");
            AssertUtils.notNull(endTime, "结束时间为空！");

            //发货时间不能小于当前时间
            if (outDate != null) {
                Date date = new Date();
                if (outDate.compareTo(date) < 0) {
                    AssertUtils.logAndThrow("发货时间不能小于当前时间！");
                }
            }

            if (beginTime.compareTo(endTime) > 0) {
                AssertUtils.logAndThrow("开始日期不允许大于结束日期");
            }
            //如果[单据状态]=已审核的【渠道预售计划】[平台店铺]=当前平台店铺、开始时间、结束时间与当前记录开始时间、结束时间存在时间交叉记录。
            List<SgBChannelAdvanceSale> sgStoChannelAdvanceSales = mapper.selectByTimeOverlap(beginTime, endTime,
                    request.getObjId(), cpShopId, SgChannelConstants.BILL_STATUS_SUBMIT);
            if (CollectionUtils.isNotEmpty(sgStoChannelAdvanceSales)) {
                AssertUtils.logAndThrow("平台店铺：" + cpShopId + " 当前时间已存在有效的渠道预售计划不允许重复设置！");
            }
        }

        if (CollectionUtils.isNotEmpty(saleItemSaveRequests)) {
            //根据平台条码id 查平台店铺
            List<String> skuIds = new ArrayList<>();
            //这里不用steam 是因为 当saleItemSaveRequests集合里面skuid为空时候，会破防（CollectionUtils.isNotEmpty(skuIds)会返回true）
            for (SgBChannelAdvanceSaleItemSaveRequest itemSaveRequest : saleItemSaveRequests) {
                if (StringUtils.isNotEmpty(itemSaveRequest.getSkuId())) {
                    skuIds.add(itemSaveRequest.getSkuId());
                }
            }
            //平台条码没有，不是新增，只是改数量
            if (CollectionUtils.isNotEmpty(skuIds)) {

                ValueHolderV14<List<SgBChannelProduct>> listValueHolderV14 = queryChannelProduct(skuIds);
                if (!listValueHolderV14.isOK()) {
                    AssertUtils.logAndThrow("查询平台店铺商品表错误：" + listValueHolderV14.getMessage());
                }
                List<SgBChannelProduct> data = listValueHolderV14.getData();
                Map<String, SgBChannelProduct> productMap = new HashMap<>(16);
                List<Long> list = new ArrayList<>();
                for (SgBChannelProduct product : data) {
                    productMap.put(product.getSkuId(), product);
                    list.add(product.getPsCSkuId());
                }

                //查明细，R3条码跟平台条码绑定 saleItemMap
                List<SgBChannelAdvanceSaleItem> saleItems = batchSelectItemBySkuid(list, request.getObjId());
                Map<Long, String> saleItemMap = null;
                if (CollectionUtils.isNotEmpty(saleItems)) {
                    saleItemMap = saleItems.stream().collect(Collectors.toMap(SgBChannelAdvanceSaleItem::getPsCSkuId,
                            SgBChannelAdvanceSaleItem::getSkuId, (v1, v2) -> v1));
                }

                for (SgBChannelAdvanceSaleItemSaveRequest itemSaveRequest : saleItemSaveRequests) {
                    String skuId = itemSaveRequest.getSkuId();
                    AssertUtils.notNull(skuId, "平台条码id不能为空！");
                    AssertUtils.notNull(itemSaveRequest.getQty(), "预售数量为空！");
                    AssertUtils.cannot(BigDecimal.ZERO.compareTo(itemSaveRequest.getQty()) > 0, "数量不能小于零！");
                    SgBChannelProduct sgChannelProduct = productMap.get(skuId);
                    AssertUtils.notNull(sgChannelProduct, "根据平台条码id找平台店铺未找到！");
                    //平台店铺id
                    Long aLong = sgStoChannelAdvanceSale != null ?
                            sgStoChannelAdvanceSale.getCpCShopId() : saleSaveRequest.getCpCShopId();
                    AssertUtils.cannot(!sgChannelProduct.getCpCShopId().equals(aLong), "当前平台店铺：" +
                            aLong + "不存在此平台条码ID：" + skuId);
                    //saleItemMap的value 对应明细里的skuid  sgChannelProduct是通过传入的skuid查出来的
                    AssertUtils.cannot(saleItemMap != null && saleItemMap.get(sgChannelProduct.getPsCSkuId()) != null
                            && !skuId.equals(saleItemMap.get(sgChannelProduct.getPsCSkuId())), "相同商品同一预售活动内不允许存在2个链接");
                    itemSaveRequest.setPsCSkuId(sgChannelProduct.getPsCSkuId());
                    itemSaveRequest.setPsCSkuEcode(sgChannelProduct.getPsCSkuEcode());
                    itemSaveRequest.setNumiid(sgChannelProduct.getNumiid());
                }
            }
        }
        return sgStoChannelAdvanceSale;
    }

    /**
     * 根据skuid批量查明细信息
     *
     * @param skudis 条码id集合
     * @param objId  主表id
     * @return 明细集合
     */
    private List<SgBChannelAdvanceSaleItem> batchSelectItemBySkuid(List<Long> skudis, Long objId) {
        List<SgBChannelAdvanceSaleItem> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(skudis)) {
            List<List<Long>> pageList = StorageUtils.getPageList(skudis, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
            for (List<Long> ids : pageList) {
                list.addAll(itemMapper.selectList(new QueryWrapper<SgBChannelAdvanceSaleItem>()
                        .lambda()
                        .in(SgBChannelAdvanceSaleItem::getPsCSkuId, ids)
                        .eq(SgBChannelAdvanceSaleItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                        .eq(SgBChannelAdvanceSaleItem::getSgBChannelAdvanceSaleId, objId)));

            }
        }
        return list;
    }

    /**
     * 根据平台条码Id 查询平台商品信息
     *
     * @param skuIds 平台条码Id
     * @return ValueHolderV14<List < SgBChannelProduct>>
     */
    private ValueHolderV14<List<SgBChannelProduct>> queryChannelProduct(List<String> skuIds) {
        SgChannelProductQueryService bean = ApplicationContextHandle.getBean(SgChannelProductQueryService.class);
        SgChannelProductQueryRequest productQueryRequest = new SgChannelProductQueryRequest();
        productQueryRequest.setSkuIdList(skuIds);
        return bean.queryChannelProduct(productQueryRequest);
    }

}
