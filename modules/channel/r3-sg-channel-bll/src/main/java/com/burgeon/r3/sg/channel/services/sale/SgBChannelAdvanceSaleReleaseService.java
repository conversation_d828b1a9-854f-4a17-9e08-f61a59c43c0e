package com.burgeon.r3.sg.channel.services.sale;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.sale.SgBChannelAdvanceSaleItemMapper;
import com.burgeon.r3.sg.channel.mapper.sale.SgBChannelAdvanceSaleMapper;
import com.burgeon.r3.sg.channel.model.request.sale.SgBChannelAdvanceSaleBillReleaseRequest;
import com.burgeon.r3.sg.channel.model.request.sale.SgBChannelAdvanceSaleItemReleaseRequest;
import com.burgeon.r3.sg.channel.model.request.sale.SgBChannelAdvanceSaleReleaseRequest;
import com.burgeon.r3.sg.channel.model.result.sale.SgBChannelAdvanceSaleReleaseResult;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.sale.SgBChannelAdvanceSale;
import com.burgeon.r3.sg.core.model.table.channel.sale.SgBChannelAdvanceSaleItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2021-06-08 19:53
 * @Description: 下单 返回单据编号给 调用者  发货 单号必填
 */

@Slf4j
@Component
public class SgBChannelAdvanceSaleReleaseService {

    @Autowired
    private SgBChannelAdvanceSaleMapper mapper;
    @Autowired
    private SgBChannelAdvanceSaleItemMapper itemMapper;

    /**
     * 渠道预售活动更新
     *
     * @param request 入参
     * @return 出参
     */
    public ValueHolderV14<List<SgBChannelAdvanceSaleReleaseResult>> releaseChannelAdvanceSale(SgBChannelAdvanceSaleBillReleaseRequest request) {
        SgBChannelAdvanceSaleReleaseService bean = ApplicationContextHandle.getBean(SgBChannelAdvanceSaleReleaseService.class);
        ValueHolderV14<List<SgBChannelAdvanceSaleReleaseResult>> v14 = new ValueHolderV14<>();
        try {
            v14 = bean.releaseChannelAdvance(request);
        } catch (Exception e) {
            log.error("SgBChannelAdvanceSaleReleaseService.releaseChannelAdvanceSale. exception_has_occured:{}",
                    Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("渠道预售活动更新失败：" + e.getMessage());
            return v14;
        }
        return v14;
    }

    /**
     * 渠道预售活动更新
     *
     * @param request 入参
     * @return 出参
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<List<SgBChannelAdvanceSaleReleaseResult>> releaseChannelAdvance(SgBChannelAdvanceSaleBillReleaseRequest request) {
        ValueHolderV14<List<SgBChannelAdvanceSaleReleaseResult>> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "更新成功");
        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelAdvanceSaleReleaseService.releaseChannelAdvanceSale ReceiveParams:request={};"
                    , JSONObject.toJSONString(request));
        }

        try {
            checkParam(request);
        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
            return v14;
        }

        try {

            SgBChannelAdvanceSaleReleaseRequest saleReleaseRequest = request.getSaleReleaseRequest();
            List<SgBChannelAdvanceSaleItemReleaseRequest> saleItemReleaseRequests = request.getSaleItemReleaseRequests();
            SgBChannelAdvanceSale update = new SgBChannelAdvanceSale();
            SgBChannelAdvanceSale stoChannelAdvanceSale;
            //下单 返回单据编号给 调用者  发货 单号必填
            boolean isOrder = saleReleaseRequest.getType().equals(SgChannelConstants.CHANNEL_TYPE_ORDER);

            List<SgBChannelAdvanceSaleReleaseResult> list = new ArrayList<>();
            if (isOrder) {
                Date date = new Date();
                stoChannelAdvanceSale = mapper.selectOne(new QueryWrapper<SgBChannelAdvanceSale>()
                        .lambda()
                        .eq(SgBChannelAdvanceSale::getCpCShopId, saleReleaseRequest.getCpCShopId())
                        .le(SgBChannelAdvanceSale::getBeginTime, date)
                        .ge(SgBChannelAdvanceSale::getEndTime, date)
                        .eq(SgBChannelAdvanceSale::getIsactive, SgConstants.IS_ACTIVE_Y)
                        .eq(SgBChannelAdvanceSale::getStatus, SgChannelConstants.BILL_STATUS_SUBMIT));
            } else {
                stoChannelAdvanceSale = mapper.selectOne(new QueryWrapper<SgBChannelAdvanceSale>()
                        .lambda()
                        .eq(SgBChannelAdvanceSale::getBillNo, saleReleaseRequest.getBillNo())
                        .eq(SgBChannelAdvanceSale::getIsactive, SgConstants.IS_ACTIVE_Y)
                        .eq(SgBChannelAdvanceSale::getStatus, SgChannelConstants.BILL_STATUS_SUBMIT));
            }

            if (stoChannelAdvanceSale == null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("渠道预售活动更新失败:未找到满足条件的渠道预售计划");
                return v14;
            }

            //条码集合
            List<Long> psSkuid = saleItemReleaseRequests.stream().map(SgBChannelAdvanceSaleItemReleaseRequest::getPsCSkuId)
                    .collect(Collectors.toList());
            //取出当前符合条件的条码集合
            List<SgBChannelAdvanceSaleItem> saleItems = batchQueryItem(psSkuid, stoChannelAdvanceSale.getId());

            if (CollectionUtils.isEmpty(saleItems)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("渠道预售活动更新失败:未找到满足条件的渠道预售计划明细");
                return v14;
            }

            Map<Long, SgBChannelAdvanceSaleItem> saleItemMap = saleItems.stream()
                    .collect(Collectors.toMap(SgBChannelAdvanceSaleItem::getPsCSkuId,
                            saleItem -> saleItem, (v1, v2) -> v1));
            if (CollectionUtils.isNotEmpty(saleItems)) {

                //汇总信息
                BigDecimal totQtyRemain = stoChannelAdvanceSale.getTotQtyRemain();
                BigDecimal totQtySold = stoChannelAdvanceSale.getTotQtySold();
                BigDecimal totAmtListRemain = stoChannelAdvanceSale.getTotAmtListRemain();
                BigDecimal totAmtListSold = stoChannelAdvanceSale.getTotAmtListSold();
                BigDecimal totQtySend = stoChannelAdvanceSale.getTotQtyOut();
                BigDecimal totAmtLsitSned = stoChannelAdvanceSale.getTotAmtLsitOut();

                JSONArray itemError = new JSONArray();

                //处理明细

                List<SgBChannelAdvanceSaleItem> itemUpdateList = new ArrayList<>();

                for (SgBChannelAdvanceSaleItemReleaseRequest itemReleaseRequest : saleItemReleaseRequests) {
                    SgBChannelAdvanceSaleItem saleItem = saleItemMap.get(itemReleaseRequest.getPsCSkuId());
                    if (saleItem != null) {
                        SgBChannelAdvanceSaleItem updateItem = new SgBChannelAdvanceSaleItem();
                        updateItem.setId(saleItem.getId());
                        StorageUtils.setBModelDefalutDataByUpdate(updateItem, request.getLoginUser());

                        if (isOrder) {
                            updateItem.setQtySold(saleItem.getQtySold().add(itemReleaseRequest.getQtySold()));
                            updateItem.setQtyRemain(saleItem.getQtyRemain().subtract(itemReleaseRequest.getQtySold()));
                            updateItem.setAmtListSold(updateItem.getQtySold().multiply(saleItem.getPriceList()));
                            updateItem.setAmtListRemain(updateItem.getQtyRemain().multiply(saleItem.getPriceList()));
                            totQtySold = totQtySold.add(itemReleaseRequest.getQtySold());
                            totQtyRemain = totQtyRemain.subtract(itemReleaseRequest.getQtySold());
                            totAmtListSold = totAmtListSold.add(itemReleaseRequest.getQtySold()
                                    .multiply(saleItem.getPriceList()));
                            totAmtListRemain = totAmtListRemain.subtract(itemReleaseRequest.getQtySold()
                                    .multiply(saleItem.getPriceList()));
                        } else {
                            updateItem.setQtyOut(saleItem.getQtyOut().add(itemReleaseRequest.getQtyOut()));
                            updateItem.setAmtListOut(updateItem.getQtyOut().multiply(saleItem.getPriceList()));
                            totQtySend = totQtySend.add(itemReleaseRequest.getQtyOut());
                            totAmtLsitSned = totAmtLsitSned.add(itemReleaseRequest.getQtyOut()
                                    .multiply(saleItem.getPriceList()));
                        }

                        if (BigDecimal.ZERO.compareTo(updateItem.getQtyRemain()) > 0) {
                            itemError.add("条码：【" + itemReleaseRequest.getPsCSkuEcode() + "】预售量不足！");
                            continue;
                        }

                        itemUpdateList.add(updateItem);
                        SgBChannelAdvanceSaleReleaseResult releaseResult = new SgBChannelAdvanceSaleReleaseResult();
                        releaseResult.setBillNo(stoChannelAdvanceSale.getBillNo());
                        releaseResult.setSgBChannelAdvanceSaleId(stoChannelAdvanceSale.getId());
                        releaseResult.setOutDate(stoChannelAdvanceSale.getOutDate());
                        releaseResult.setCpCShopId(saleReleaseRequest.getCpCShopId());
                        releaseResult.setSkuId(itemReleaseRequest.getSkuId());
                        releaseResult.setPsCSkuId(itemReleaseRequest.getPsCSkuId());
                        releaseResult.setPsCSkuEcode(itemReleaseRequest.getPsCSkuEcode());
                        releaseResult.setHandle(true);
                        list.add(releaseResult);
                    }
                }

                if (itemError.size() > 0) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage("渠道预售活动更新失败:" + JSONArray.toJSONString(itemError));
                    return v14;
                }

                if (CollectionUtils.isNotEmpty(itemUpdateList)) {
                    for (SgBChannelAdvanceSaleItem item : itemUpdateList) {
                        itemMapper.updateById(item);
                    }
                }

                update.setId(stoChannelAdvanceSale.getId());
                update.setTotQtySold(totQtySold);
                update.setTotQtyRemain(totQtyRemain);
                update.setTotAmtListSold(totAmtListSold);
                update.setTotAmtListRemain(totAmtListRemain);
                update.setTotQtyOut(totQtySend);
                update.setTotAmtLsitOut(totAmtLsitSned);
            }

            v14.setData(list);
            StorageUtils.setBModelDefalutDataByUpdate(update, request.getLoginUser());
            mapper.updateById(update);
        } catch (Exception e) {
            AssertUtils.logAndThrowException("渠道预售活动更新失败", e);
        }
        return v14;
    }

    /**
     * 批量查明细
     */
    private List<SgBChannelAdvanceSaleItem> batchQueryItem(List<Long> psSkuid, Long objid) {
        List<SgBChannelAdvanceSaleItem> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(psSkuid)) {
            //*批量新增500/次*//*
            List<List<Long>> queryList =
                    StorageUtils.getPageList(psSkuid, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
            for (List<Long> ids : queryList) {
                List<SgBChannelAdvanceSaleItem> saleItems = itemMapper.selectList(new QueryWrapper<SgBChannelAdvanceSaleItem>()
                        .lambda()
                        .eq(SgBChannelAdvanceSaleItem::getSgBChannelAdvanceSaleId, objid)
                        .eq(SgBChannelAdvanceSaleItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                        .in(SgBChannelAdvanceSaleItem::getPsCSkuId, ids));
                list.addAll(saleItems);
            }
        }
        return list;
    }

    /**
     * 参数校验
     *
     * @param request 入参
     */
    private void checkParam(SgBChannelAdvanceSaleBillReleaseRequest request) {
        AssertUtils.notNull(request, "参数为空！");
        SgBChannelAdvanceSaleReleaseRequest saleReleaseRequest = request.getSaleReleaseRequest();
        List<SgBChannelAdvanceSaleItemReleaseRequest> saleItemReleaseRequests = request.getSaleItemReleaseRequests();
        AssertUtils.notNull(saleReleaseRequest, "主表参数为空！");
        AssertUtils.cannot(CollectionUtils.isEmpty(saleItemReleaseRequests), "明细为空！");
        AssertUtils.notNull(saleReleaseRequest.getCpCShopId(), "平台店铺为空！");
        AssertUtils.notNull(saleReleaseRequest.getType(), "类型为空！");
        if (saleReleaseRequest.getType().equals(SgChannelConstants.CHANNEL_TYPE_SNED)) {
            AssertUtils.notNull(saleReleaseRequest.getBillNo(), "渠道预售活动单号为空！");
        }
        for (SgBChannelAdvanceSaleItemReleaseRequest itemReleaseRequest : saleItemReleaseRequests) {
            AssertUtils.notNull(itemReleaseRequest.getQtyOut(), "已发货量为空！");
            AssertUtils.notNull(itemReleaseRequest.getQtySold(), "已预售量为空！");
            AssertUtils.notNull(itemReleaseRequest.getPsCSkuId(), "条码为空！");
            AssertUtils.notNull(itemReleaseRequest.getPsCSkuEcode(), "条码code为空！");
        }
    }
}
