package com.burgeon.r3.sg.channel.services.strategy;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.SgStorageRedisQueryCfModel;
import com.burgeon.r3.sg.basic.model.request.SgBSaStorageQueryRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageUpdateControlRequest;
import com.burgeon.r3.sg.basic.model.request.vo.*;
import com.burgeon.r3.sg.basic.model.result.SgBSaStorageQueryResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQueryCfResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageUpdateResult;
import com.burgeon.r3.sg.basic.services.SgBSaStorageQueryService;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyItemMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyMapper;
import com.burgeon.r3.sg.channel.services.SgChannelStorageService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.channel.qtystrategy.SgCChannelQtyStrategy;
import com.burgeon.r3.sg.core.model.table.channel.qtystrategy.SgCChannelQtyStrategyItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/6/18 16:07
 */
@Slf4j
@Component
public class SgCChannelQtyStrategyService {

    @Autowired
    private SgCChannelQtyStrategyMapper mapper;

    @Autowired
    private SgCChannelQtyStrategyItemMapper itemMapper;

    @Autowired
    private SgStorageQueryService sgStorageQueryService;

    @Autowired
    private SgBSaStorageQueryService sgBSaStorageQueryService;

    @Autowired
    private SgChannelStorageService sgChannelStorageService;

    //参数校验
    public SgCChannelQtyStrategy checkParams(SgR3BaseRequest reqeust) {
        AssertUtils.notNull(reqeust, "请求参数不能为空");
        Long objId = reqeust.getObjId();
        User loginUser = reqeust.getLoginUser();
        AssertUtils.notNull(objId, "主表id不能为空");
        SgCChannelQtyStrategy qtyStrategy = mapper.selectById(objId);
        AssertUtils.notNull(qtyStrategy, "当前记录已不存在！");

        itemMapper.delete(new LambdaQueryWrapper<SgCChannelQtyStrategyItem>()
                .eq(SgCChannelQtyStrategyItem::getSgCChannelQtyStrategyId, objId)
                .eq(SgCChannelQtyStrategyItem::getQty, 0)
                .eq(SgCChannelQtyStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y));
        List<SgCChannelQtyStrategyItem> strategyItems = itemMapper.selectList(new LambdaQueryWrapper<SgCChannelQtyStrategyItem>()
                .eq(SgCChannelQtyStrategyItem::getSgCChannelQtyStrategyId, objId)
                //未发布明细
                .eq(SgCChannelQtyStrategyItem::getIspublish, SgConstants.IS_ACTIVE_N)
                .eq(SgCChannelQtyStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isEmpty(strategyItems)) {
            AssertUtils.logAndThrow("当前记录无未发布明细，不允许！", loginUser.getLocale());
        }
        if (reqeust.isR3()) {
            //获取所属聚合仓id
            Long cpShopId = qtyStrategy.getCpCShopId();
            CpShop shopInfo = CommonCacheValUtils.getShopInfo(cpShopId);
            Long sgShareStoreId = shopInfo.getSgCShareStoreId();

            //组装锁定库存查询数据 or 当前商品唯一校验
            List<SgStorageRedisQueryCfModel> cfModels = new ArrayList<>();
            // 配销仓ids 条码id  查询配销仓库存
            List<Long> sgSaStoreIds = new ArrayList<>();
            List<Long> psSkuIds = new ArrayList<>();
            for (SgCChannelQtyStrategyItem item : strategyItems) {
                String skuId = item.getSkuId();
                Long sgSaStoreId = item.getSgCSaStoreId();
                Long psSkuId = item.getPsCSkuId();
                Integer count = itemMapper.selectCount(new LambdaQueryWrapper<SgCChannelQtyStrategyItem>()
                        .eq(SgCChannelQtyStrategyItem::getSkuId, skuId)
                        .ne(SgCChannelQtyStrategyItem::getSgCChannelQtyStrategyId, objId)
                        .eq(SgCChannelQtyStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                        .eq(SgCChannelQtyStrategyItem::getIspublish, SgConstants.IS_ACTIVE_Y));
                if (count > 0) {
                    AssertUtils.logAndThrow("当前商品" + skuId + "已存在其他有效的活动，不允许重复设置");
                }
                SgStorageRedisQueryCfModel model = new SgStorageRedisQueryCfModel();
                model.setCpCShopId(cpShopId);
                model.setSgCSaStoreId(item.getSgCSaStoreId());
                model.setSkuId(item.getSkuId());
                cfModels.add(model);
                sgSaStoreIds.add(sgSaStoreId);
                psSkuIds.add(psSkuId);
            }
            // 获取对应条码的锁定库存
            ValueHolderV14<HashMap<String, SgStorageRedisQueryCfResult>> holderV14 = sgStorageQueryService.queryCfStorageWithRedis(cfModels, loginUser);
            HashMap<String, SgStorageRedisQueryCfResult> map = holderV14.getData();
            if (!holderV14.isOK()) {
                AssertUtils.logAndThrow("查询条码库存异常：" + holderV14.getMessage());
            }
            if (MapUtils.isEmpty(map)) {
                AssertUtils.logAndThrow("未查询到条码库存");
            }

            List<List<Long>> pageList = StorageUtils.getPageList(psSkuIds, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
            List<SgBSaStorageQueryResult> data = new ArrayList<>();
            for (List<Long> skuid : pageList) {
                //查询配销仓可用
                SgBSaStorageQueryRequest queryRequest = new SgBSaStorageQueryRequest();
                queryRequest.setPsCSkuIds(skuid);
                queryRequest.setSgCSaStoreIds(sgSaStoreIds);
                ValueHolderV14<List<SgBSaStorageQueryResult>> queryV14 = sgBSaStorageQueryService.queryStorage(queryRequest);
                if (!queryV14.isOK()) {
                    AssertUtils.logAndThrow("查询配销仓库存异常：" + queryV14.getMessage());
                }

                if (CollectionUtils.isNotEmpty(queryV14.getData())) {
                    data.addAll(queryV14.getData());
                }
            }

            Map<String, BigDecimal> saMap = data.stream().collect(Collectors.toMap(o -> o.getPsCSkuId() + "" + o.getSgCSaStoreId(), o -> o.getQtyAvailable()));
            if (MapUtils.isEmpty(saMap)) {
                AssertUtils.logAndThrow("未查询到对应条码的配销仓库存");
            }
            StringBuilder sb = new StringBuilder();
            for (SgCChannelQtyStrategyItem qtyItem : strategyItems) {
                // 待发布明明细中的[配销仓]所属的聚合仓 不等于 当前店铺在【平台店铺档案】中的聚合仓
                Long saStoreId = qtyItem.getSgCSaStoreId();
                BigDecimal qty = qtyItem.getQty();
                Long pscSkuId = qtyItem.getPsCSkuId();
                String skuEcode = qtyItem.getPsCSkuEcode();
                String skuId = qtyItem.getSkuId();
                if (saStoreId != null) {
                    SgCSaStore sgSaStore = CommonCacheValUtils.getSaStore(saStoreId);
                    if (sgSaStore == null) {
                        AssertUtils.logAndThrow("配销仓信息有误", loginUser.getLocale());
                    }
                    if (!sgSaStore.getSgCShareStoreId().equals(sgShareStoreId)) {
                        AssertUtils.logAndThrow("当前配销仓:" + sgSaStore.getEname() + " 所属的聚合仓 不等于 当前店铺的所属聚合仓，不允许！", loginUser.getLocale());
                    }
                }
                // 若调整数量 qty<0，且绝对值 大于 对应条码在【渠道库存锁定库存】中的[剩余数]Y
                if (qty.compareTo(BigDecimal.ZERO) < 0) {
                    String key = cpShopId + SgConstants.SG_CONNECTOR_MARKS_4 + saStoreId + SgConstants.SG_CONNECTOR_MARKS_4 + skuId;
                    SgStorageRedisQueryCfResult queryCfResult = map.get(key);
                    //可用量
                    BigDecimal qtyAvailable = queryCfResult.getQtyAvailable();
                    if (qtyAvailable.add(qty).compareTo(BigDecimal.ZERO) < 0) {
                        sb.append("当前平台条码" + skuId + " 调整数量 " + qty + "，剩余数 " + qtyAvailable + "，调整后会导致剩余数小于0，不允许！");
                    }
                }
                //是否大于配销仓可用
                if (qty.compareTo(BigDecimal.ZERO) > 0) {
                    String key = pscSkuId + "" + saStoreId;
                    if (!saMap.containsKey(key)) {
                        AssertUtils.logAndThrow("获取配销仓库存信息有误", loginUser.getLocale());
                    }
                    BigDecimal qtyAvailable = saMap.get(key);
                    if (qty.compareTo(qtyAvailable) > 0) {
                        sb.append("当前条码 " + skuEcode + " 调整数量 " + qty + " 配销仓可用量 " + qtyAvailable + "，调整后会导致可用数小于0，不允许！");
                    }
                }
            }
            if (sb.length() > 0) {
                AssertUtils.logAndThrow(sb.toString(), loginUser.getLocale());
            }
        }
        return qtyStrategy;
    }


    /**
     * 渠道库存锁定
     *
     * @param qtyStrategy         主表
     * @param redisBillFtpKeyList redis流水间
     * @param loginUser           用户
     * @param isR3                是否页面
     */
    public List<SgCChannelQtyStrategyItem> updateStorage(SgCChannelQtyStrategy qtyStrategy, List<String> redisBillFtpKeyList, User loginUser, boolean isR3) {
        Long objid = qtyStrategy.getId();
        //未发布明细
        List<SgCChannelQtyStrategyItem> strategyItems = itemMapper.selectList(new LambdaQueryWrapper<SgCChannelQtyStrategyItem>()
                .eq(SgCChannelQtyStrategyItem::getSgCChannelQtyStrategyId, objid)
                .eq(SgCChannelQtyStrategyItem::getIspublish, SgConstants.IS_ACTIVE_N)
                .eq(SgCChannelQtyStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y));
        // 根据本单[平台店铺]、待发布明细（配销仓、平台条码ID、调整数量），新增或者更新【渠道库存锁定库存】
        // 新增或者更新【渠道库存锁定库存】已更新【配销仓库存】的[锁定量]
        SgStorageSingleUpdateCfRequest updateCfRequest = new SgStorageSingleUpdateCfRequest();
        SgStorageUpdateBillCfRequest bill = new SgStorageUpdateBillCfRequest();
        SgStorageUpdateControlRequest controlRequest = new SgStorageUpdateControlRequest();

        bill.setBillId(qtyStrategy.getId());
        bill.setBillNo(qtyStrategy.getBillNo());
        bill.setBillDate(new Date());
        bill.setChangeDate(new Date());
        if (isR3) {
            //库存同步
            bill.setServiceNode(SgConstantsIF.SERVICE_NODE_CHANNEL_QTY_STOCK_SYNC);
        } else {
            //活动下架
            bill.setServiceNode(SgConstantsIF.SERVICE_NODE_CHANNEL_QTY_SOLD_OUT);
        }
        bill.setControlModel(controlRequest);

        List<SgStorageUpdateBillItemCfRequest> itemCfRequests = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        for (SgCChannelQtyStrategyItem item : strategyItems) {
            SgStorageUpdateBillItemCfRequest itemCfRequest = new SgStorageUpdateBillItemCfRequest();
            SgStorageUpdateControlRequest itemControlRequest = new SgStorageUpdateControlRequest();
            BeanUtils.copyProperties(item, itemCfRequest);
            itemCfRequest.setNumiid(item.getProId());
            itemCfRequest.setCpCShopId(qtyStrategy.getCpCShopId());
            itemCfRequest.setCpCShopTitle(qtyStrategy.getCpCShopTitle());
            itemCfRequest.setQtyStorageChange(item.getQty());
            itemCfRequest.setBillItemId(item.getId());
            itemCfRequest.setControlmodel(itemControlRequest);
            itemCfRequests.add(itemCfRequest);
            //获取明细id
            ids.add(item.getId());
        }
        bill.setItemList(itemCfRequests);
        updateCfRequest.setBill(bill);
        updateCfRequest.setMessageKey(SgConstants.SG_C_CHANNEL_QTY_STRATEGY + qtyStrategy.getBillNo());
        updateCfRequest.setLoginUser(loginUser);
        updateCfRequest.setControlModel(controlRequest);
        if (log.isDebugEnabled()) {
            log.debug("按数量同步策略,开始更新渠道库存锁定库存，活动名称={} 入参={}", qtyStrategy.getActivityName(), JSONObject.toJSONString(updateCfRequest));
        }
        ValueHolderV14<SgStorageUpdateResult> cResult = sgChannelStorageService.updateStorageBillWithTrans(updateCfRequest);
        if (log.isDebugEnabled()) {
            log.debug("按数量同步策略{},开始更新共享仓库存，出参={}", qtyStrategy.getActivityName(), JSONObject.toJSONString(cResult));
        }
        if (ResultCode.FAIL == cResult.getCode()) {
            AssertUtils.logAndThrow("更新渠道库存锁定库存异常：" + cResult.getMessage());
        }
        List<String> keyList = cResult.getData().getRedisBillFtpKeyList();
        if (CollectionUtils.isNotEmpty(keyList)) {
            redisBillFtpKeyList.addAll(keyList);
        }
        //更新配销仓 锁定量
        updateSaStorage(qtyStrategy, strategyItems, redisBillFtpKeyList, loginUser, isR3);

        // 将当前【待发布明细】的记录，写入到【已发布明细】。
        SgCChannelQtyStrategyItem update = new SgCChannelQtyStrategyItem();
        update.setIspublish(SgConstants.IS_ACTIVE_Y);
        StorageUtils.setBModelDefalutData(update, loginUser);
        itemMapper.update(update, new LambdaQueryWrapper<SgCChannelQtyStrategyItem>()
                .in(SgCChannelQtyStrategyItem::getId, ids));
        return strategyItems;
    }


    public void updateSaStorage(SgCChannelQtyStrategy qtyStrategy, List<SgCChannelQtyStrategyItem> strategyItems,
                                List<String> redisBillFtpKeyList, User loginUser, boolean isR3) {

        SgStorageSingleUpdateSaRequest updateSaRequest = new SgStorageSingleUpdateSaRequest();
        SgStorageUpdateBillSaRequest billSaRequest = new SgStorageUpdateBillSaRequest();
        SgStorageUpdateControlRequest saControlRequest = new SgStorageUpdateControlRequest();

        billSaRequest.setBillId(qtyStrategy.getId());
        billSaRequest.setBillNo(qtyStrategy.getBillNo());
        billSaRequest.setBillDate(new Date());
        billSaRequest.setChangeDate(new Date());
        if (isR3) {
            //库存同步
            billSaRequest.setServiceNode(SgConstantsIF.SERVICE_NODE_CHANNEL_QTY_STOCK_SYNC);
        } else {
            //活动下架
            billSaRequest.setServiceNode(SgConstantsIF.SERVICE_NODE_CHANNEL_QTY_SOLD_OUT);
        }
        billSaRequest.setControlModel(saControlRequest);

        List<SgStorageUpdateBillItemSaRequest> saItemRequests = new ArrayList<>();
        for (SgCChannelQtyStrategyItem item : strategyItems) {
            SgStorageUpdateBillItemSaRequest saRequest = new SgStorageUpdateBillItemSaRequest();
            SgStorageUpdateControlRequest itemControlRequest = new SgStorageUpdateControlRequest();
            BeanUtils.copyProperties(item, saRequest);
            saRequest.setBillItemId(item.getId());
            saRequest.setQtyFixedAvailableChange(item.getQty());
            saRequest.setControlmodel(itemControlRequest);
            saItemRequests.add(saRequest);
        }
        billSaRequest.setItemList(saItemRequests);
        updateSaRequest.setBill(billSaRequest);
        updateSaRequest.setMessageKey(SgConstants.SG_C_CHANNEL_QTY_STRATEGY + qtyStrategy.getBillNo());
        updateSaRequest.setLoginUser(loginUser);
        updateSaRequest.setControlModel(saControlRequest);
        if (log.isDebugEnabled()) {
            log.debug("更新配销仓锁定量开始 ，活动名称={} 入参={}", qtyStrategy.getActivityName(), JSONObject.toJSONString(updateSaRequest));
        }
        ValueHolderV14<SgStorageUpdateResult> holderV14 = sgChannelStorageService.updateStorageBillWithTrans(updateSaRequest);
        if (log.isDebugEnabled()) {
            log.debug("更新配销仓锁定量结束,活动名称={}，出参={}", qtyStrategy.getActivityName(), JSONObject.toJSONString(holderV14));
        }
        if (ResultCode.FAIL == holderV14.getCode()) {
            AssertUtils.logAndThrow("更新配销仓锁定量异常：" + holderV14.getMessage());
        }
        List<String> keyList = holderV14.getData().getRedisBillFtpKeyList();
        if (CollectionUtils.isNotEmpty(keyList)) {
            redisBillFtpKeyList.addAll(keyList);
        }
    }
}
