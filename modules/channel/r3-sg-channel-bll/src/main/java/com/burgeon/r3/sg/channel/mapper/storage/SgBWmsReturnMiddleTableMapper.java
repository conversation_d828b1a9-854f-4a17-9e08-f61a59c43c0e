package com.burgeon.r3.sg.channel.mapper.storage;

import com.burgeon.r3.sg.core.model.table.channel.storage.SgBWmsReturnMiddleTable;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface SgBWmsReturnMiddleTableMapper extends ExtentionMapper<SgBWmsReturnMiddleTable> {


    @Update("<script>"
            + "UPDATE sg_b_wms_return_middle_table"
            + "<trim prefix='set' suffixOverrides=','>"
            + "modifieddate = now(), "
            + "<trim prefix= 'out_status = case' suffix='end, '>"
            + "<foreach collection='list' item='item' index='index'>"
            + "when id=#{item.id} then #{item.outStatus}"
            + "</foreach>"
            + "</trim>"
            + "<trim prefix= 'wms_failed_count = case' suffix='end, '>"
            + "<foreach collection='list' item='item' index='index'>"
            + "when id=#{item.id} then #{item.wmsFailedCount}"
            + "</foreach>"
            + "</trim>"
            + "<trim prefix= 'wms_failed_reason = case' suffix='end, '>"
            + "<foreach collection='list' item='item' index='index'>"
            + "when id=#{item.id} then #{item.wmsFailedReason}"
            + "</foreach>"
            + "</trim>"
            + "</trim>"
            + "where"
            + "<foreach collection='list' separator='or' item='item' index='index' >"
            + "id=#{item.id}"
            + "</foreach>"
            + "</script>")
    int batchUpdate(@Param("list") List<SgBWmsReturnMiddleTable> list);
}