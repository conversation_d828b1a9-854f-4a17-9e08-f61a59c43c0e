package com.burgeon.r3.sg.channel.validate.storage;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgBChannelStorageFullSyncMapper;
import com.burgeon.r3.sg.channel.model.dto.storage.SgBChannelStorageFullSyncDto;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBChannelStorageFullSync;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description 平台库存全量同步
 * <AUTHOR>
 * @Date 2021/6/24 13:25
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgBChannelStorageFullSyncSaveValidator extends BaseSingleValidator<SgBChannelStorageFullSyncDto> {

    @Autowired
    private SgBChannelStorageFullSyncMapper storageFullSyncMapper;

    @Autowired
    private SgBChannelProductMapper productMapper;

    @Override

    public String getValidatorMsgName() {
        return "平台库存全量同步保存";
    }

    @Override
    public Class<?> getValidatorClass() {
        return SgBChannelStorageFullSyncSaveValidator.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgBChannelStorageFullSyncDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        if (mainObject.getId() > 0L) {
            if (log.isDebugEnabled()) {
                log.debug("Start SgBChannelStorageFullSyncSaveValidator.validateMainTable:mainObject={};",
                        JSONObject.toJSONString(mainObject));
            }
            SgBChannelStorageFullSync sgChannelStorageFullSyncOld = getOrignalData();
            if (sgChannelStorageFullSyncOld == null) {
                return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前记录不存在！", loginUser.getLocale()));
            }
            //同步状态
            if (sgChannelStorageFullSyncOld.getStatus().equals(SgChannelConstants.CHANNEL_STORAGE_FULL_SYNC_IN)
                    || sgChannelStorageFullSyncOld.getStatus().equals(SgChannelConstants.CHANNEL_STORAGE_FULL_SYNC_POR_SUCC)
                    || sgChannelStorageFullSyncOld.getStatus().equals(SgChannelConstants.CHANNEL_STORAGE_FULL_SYNC_SUCC)) {
                return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前记录同步状态不符合，不允许保存！", loginUser.getLocale()));
            }
            //平台条码Id 和平台商品Id
            if (!(mainObject.getSkuId() == null && mainObject.getNumiid() == null)) {
                String storageFullSyncItemSkuId = mainObject.getSkuId();
                String storageFullSyncItemNumiid = mainObject.getNumiid();
                if (mainObject.getNumiid() == null) {
                    storageFullSyncItemSkuId = mainObject.getSkuId();
                    storageFullSyncItemNumiid = sgChannelStorageFullSyncOld.getNumiid();
                } else if (mainObject.getSkuId() == null) {
                    storageFullSyncItemSkuId = sgChannelStorageFullSyncOld.getSkuId();
                    storageFullSyncItemNumiid = mainObject.getNumiid();
                }
                v14 = checkMainTableSkuIdNumiId(storageFullSyncItemSkuId, storageFullSyncItemNumiid, loginUser);
                if (v14.getCode() == ResultCode.FAIL) {
                    return v14;
                }
            }
        } else {
            if (mainObject.getCpCShopId() == null) {
                return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("平台店铺不能为空", loginUser.getLocale()));
            }
            v14 = checkMainTableSkuIdNumiId(mainObject.getSkuId(), mainObject.getNumiid(), loginUser);
            if (v14.getCode() == ResultCode.FAIL) {
                return v14;
            }
            if (mainObject.getQty() == null) {
                return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("数量为空，不允许保存", loginUser.getLocale()));
            }
        }
        //批次号
        /*if (mainObject.getBatchNo() != null) {
            v14 = checkMainTableBatchNO(mainObject, loginUser);
            if (v14.getCode() == ResultCode.FAIL) {
                return v14;
            }
        }*/
        //平台条码Id
        if (mainObject.getSkuId() != null) {
            Integer countSkuId = productMapper.selectCount(new QueryWrapper<SgBChannelProduct>().lambda()
                    .eq(SgBChannelProduct::getCpCShopId, mainObject.getCpCShopId())
                    .eq(SgBChannelProduct::getSkuId, mainObject.getSkuId())
                    .eq(SgBChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (countSkuId <= 0) {
                return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("平台商品记录不存在，不允许保存", loginUser.getLocale()));
            }
        }
        //平台商品Id 判断
        if (mainObject.getNumiid() != null) {
            Integer countNumiid = productMapper.selectCount(new QueryWrapper<SgBChannelProduct>().lambda()
                    .eq(SgBChannelProduct::getCpCShopId, mainObject.getCpCShopId())
                    .eq(SgBChannelProduct::getNumiid, mainObject.getNumiid())
                    .eq(SgBChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (countNumiid <= 0) {
                return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("平台商品记录不存在，不允许保存", loginUser.getLocale()));
            }
        }
        return v14;
    }

    /**
     * 校验批次号
     *
     * @param mainObject
     * @param loginUser
     * @return
     */
    private ValueHolderV14 checkMainTableBatchNO(SgBChannelStorageFullSyncDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        Integer count = storageFullSyncMapper.selectCount(new QueryWrapper<SgBChannelStorageFullSync>().lambda()
                .eq(SgBChannelStorageFullSync::getBatchNo, mainObject.getBatchNo())
                .eq(SgBChannelStorageFullSync::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (count > 0) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("批次号不能重复!", loginUser.getLocale()));
        }
        return v14;
    }

    /**
     * 校验明细表的平台条码ID和平台商品ID
     *
     * @param skuId
     * @param numId
     * @param loginUser
     * @return
     */

    private ValueHolderV14 checkMainTableSkuIdNumiId(String skuId, String numId, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        if (StringUtils.isEmpty(skuId) && StringUtils.isEmpty(numId)) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("平台条码ID或平台商品ID不能同时为空!", loginUser.getLocale()));
        }
        if (StringUtils.isNotEmpty(skuId) && StringUtils.isNotEmpty(numId)) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("平台条码ID和平台商品ID不能同时存在!", loginUser.getLocale()));
        }
        return v14;
    }
}
