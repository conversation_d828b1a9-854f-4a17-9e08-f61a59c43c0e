package com.burgeon.r3.sg.channel.services.product;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.stocksync.common.OmsConstantsIF;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.api.CpShopQueryCmd;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.api.SkuLikeQueryCmd;
import com.jackrain.nea.psext.request.SkuQueryListRequest;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * className: SgChannelProductBandSkuService
 * description: 平台店铺商品绑定功能
 *
 * <AUTHOR>
 * create: 2021-07-03
 * @since JDK 1.8
 */
@Component
@Slf4j
public class SgChannelProductBandSkuService {

    @Autowired
    private SgBChannelProductMapper channelProductMapper;

    @DubboReference(group = "ps-ext", version = "1.0")
    private SkuLikeQueryCmd skuLikeQueryCmd;

    @DubboReference(group = "cp-ext", version = "1.0")
    private CpShopQueryCmd shopQueryCmd;

    /**
     * 手工绑定平台店铺商品
     *
     * @param param CP_C_SHOP_ID-店铺id，PS_C_SPU_IDS-商品id（多个逗号隔开）
     * @return
     */
    public ValueHolderV14<String> bandChannelProduct(JSONObject param, User user) {

        if (log.isDebugEnabled()) {
            log.debug("SgChannelProductSaveService.bandChannelProduct--param:{}", JSON.toJSONString(param));
        }

        Long shopId = param.getLong("CP_C_SHOP_ID");
        String spuIds = param.getString("PS_C_SPU_IDS");

        ValueHolderV14<String> valueHolder = new ValueHolderV14<>(ResultCode.SUCCESS, "绑定成功");

        if (ObjectUtils.isEmpty(shopId)) {
            valueHolder.setCode(ResultCode.FAIL);
            valueHolder.setMessage("请选择平台店铺");
            return valueHolder;
        }
        if (ObjectUtils.isEmpty(spuIds)) {
            valueHolder.setCode(ResultCode.FAIL);
            valueHolder.setMessage("请选择商品");
            return valueHolder;
        }

        //查询店铺、商品信息
        List<SkuQueryListRequest> skuList;
        CpShop shop;
        List<String> spuIdList = Arrays.asList(spuIds.split(SgConstants.SG_CONNECTOR_MARKS_5));
        try {
            //商品
            skuList = skuLikeQueryCmd.querySkuByProIds(spuIdList.stream().map(Long::valueOf).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(skuList)) {
                valueHolder.setMessage("需要绑定的商品无相关条码信息，请先维护商品条码");
                valueHolder.setCode(ResultCode.FAIL);
                return valueHolder;
            }

            //店铺
            List<Long> shopIdList = new ArrayList<>();
            shopIdList.add(shopId);
            List<CpShop> shops = shopQueryCmd.queryShopByIds(shopIdList);
            if (CollectionUtils.isEmpty(shops)) {
                valueHolder.setMessage("需要绑定的店铺已不存在");
                valueHolder.setCode(ResultCode.FAIL);
                return valueHolder;
            }
            shop = shops.get(0);
        } catch (Exception e) {
            log.error("psRpc异常：e:{}", Throwables.getStackTraceAsString(e));
            valueHolder.setMessage("绑定异常：查询店铺、商品详情异常");
            valueHolder.setCode(ResultCode.FAIL);
            return valueHolder;
        }

        //过滤已存在的店铺商品绑定关系
        List<SkuQueryListRequest> addSkuList = skuFilter(shopId, skuList, spuIdList);
        if (CollectionUtils.isEmpty(addSkuList)) {
            return valueHolder;
        }

        //组装绑定关系对象
        List<SgBChannelProduct> insertList = new ArrayList<>();
        addSkuList.forEach(sku -> {
            SgBChannelProduct product = new SgBChannelProduct();
            product.setId(ModelUtil.getSequence(SgConstants.SG_B_CHANNEL_PRODUCT));
            product.setCpCShopId(shop.getId());
            product.setCpCShopTitle(shop.getCpCShopTitle());
            if (!ObjectUtils.isEmpty(shop.getCpCPlatformId())) {
                product.setCpCPlatformId(shop.getCpCPlatformId().intValue());
            }
            product.setSaleStatus(SgChannelConstants.CHANNEL_PRODUCT_STATUS_SALING);
            product.setSkuId(sku.getEcode());
            product.setNumiid(sku.getEcode());
            product.setQtySafety(BigDecimal.ZERO);
            product.setSaStoreType(SgConstants.SA_STORE_TYPE_ACTIVITY);
            product.setIslock(SgConstants.IS_ACTIVE_Y);
            product.setPsCSkuId(sku.getId());
            product.setPsCSkuEcode(sku.getEcode());
            product.setPsCProId(sku.getPsCProId());
            product.setPsCProEcode(sku.getPsCProEcode());
            product.setPsCProEname(sku.getPsCProEname());
            product.setPsCSpec1Id(sku.getColorId());
            product.setPsCSpec1Ecode(sku.getColorEcode());
            product.setPsCSpec1Ename(sku.getColorName());
            product.setPsCSpec2Id(sku.getSizeId());
            product.setPsCSpec2Ecode(sku.getSizeEcode());
            product.setPsCSpec2Ename(sku.getSizeName());
            StorageUtils.setBModelDefalutData(product, user);
            product.setOwnerename(user.getEname());
            product.setModifierename(user.getEname());
            product.setIstrans(SgConstants.IS_AUTO_Y);
            product.setIslock(SgConstants.IS_AUTO_N);
            product.setSyncRatio(OmsConstantsIF.PERCENT);
            insertList.add(product);
        });

        channelProductMapper.batchInsert(insertList);

        return valueHolder;
    }

    /**
     * 过滤已存在的sku
     *
     * @param shopId  店铺id
     * @param skuList sku
     * @param proIds  商品id
     * @return 可以新增的sku
     */
    private List<SkuQueryListRequest> skuFilter(Long shopId, List<SkuQueryListRequest> skuList, List<String> proIds) {
        List<SkuQueryListRequest> addSku = new ArrayList<>();
        List<SgBChannelProduct> productList = channelProductMapper.selectList(new LambdaQueryWrapper<SgBChannelProduct>()
                .eq(SgBChannelProduct::getCpCShopId, shopId)
                .in(SgBChannelProduct::getPsCProId, proIds)
                .eq(SgBChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y));

        skuList.forEach(sku -> {
            boolean isExist = false;
            if (CollectionUtils.isNotEmpty(productList)) {
                for (SgBChannelProduct product : productList) {
                    if (sku.getId().equals(product.getPsCSkuId())) {
                        isExist = true;
                        break;
                    }
                }
            }
            if (!isExist) {
                addSku.add(sku);
            }
        });
        return addSku;
    }
}
