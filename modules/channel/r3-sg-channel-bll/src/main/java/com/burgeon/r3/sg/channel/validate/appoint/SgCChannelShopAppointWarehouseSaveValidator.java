package com.burgeon.r3.sg.channel.validate.appoint;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.appoint.SgCChannelShopAppointWarehouseAppointItemMapper;
import com.burgeon.r3.sg.channel.mapper.appoint.SgCChannelShopAppointWarehouseExcludeItemMapper;
import com.burgeon.r3.sg.channel.mapper.appoint.SgCChannelShopAppointWarehouseMapper;
import com.burgeon.r3.sg.channel.model.dto.appoint.SgCChannelShopAppointWarehouseAppointItemDto;
import com.burgeon.r3.sg.channel.model.dto.appoint.SgCChannelShopAppointWarehouseDto;
import com.burgeon.r3.sg.channel.model.dto.appoint.SgCChannelShopAppointWarehouseExcludeItemDto;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.appoint.SgCChannelShopAppointWarehouse;
import com.burgeon.r3.sg.core.model.table.channel.appoint.SgCChannelShopAppointWarehouseAppointItem;
import com.burgeon.r3.sg.core.model.table.channel.appoint.SgCChannelShopAppointWarehouseExcludeItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseMultiItemsValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/2/25 16:13
 */
@Slf4j
@Component
public class SgCChannelShopAppointWarehouseSaveValidator extends BaseMultiItemsValidator<SgCChannelShopAppointWarehouseDto> {
    @Autowired
    private SgCChannelShopAppointWarehouseMapper mapper;
    @Autowired
    private SgCChannelShopAppointWarehouseAppointItemMapper appointItemMapper;
    @Autowired
    private SgCChannelShopAppointWarehouseExcludeItemMapper excludeItemMapper;

    @Override
    public String getValidatorMsgName() {
        return "店铺指定实体仓设置保存";
    }

    @Override
    public Class getValidatorClass() {
        return SgCChannelShopAppointWarehouseSaveValidator.class;
    }

    @Override
    public Class<?> getSubTableClass(String subTableName) {
        if (SgConstants.SG_C_CHANNEL_SHOP_APPOINT_WAREHOUSE_APPOINT_ITEM.toUpperCase().equals(subTableName)) {
            return SgCChannelShopAppointWarehouseAppointItemDto.class;
        } else if (SgConstants.SG_C_CHANNEL_SHOP_APPOINT_WAREHOUSE_EXCLUDE_ITEM.toUpperCase().equals(subTableName)) {
            return SgCChannelShopAppointWarehouseExcludeItemDto.class;
        }
        return null;
    }

    @Override
    public ValueHolderV14 validateTable(SgCChannelShopAppointWarehouseDto mainObject, Map subObjectMap, User loginUser) {

        if (log.isDebugEnabled()) {
            log.debug("SgCChannelShopAppointWarehouseSaveValidator.validateTable mainObject:{}", JSONObject.toJSONString(mainObject));
        }

        SgCChannelShopAppointWarehouseDto mainTable = getOrignalData();
        Date beginTime = mainObject.getBeginTime();
        Date endTime = mainObject.getEndTime();
        Long shopId = mainObject.getCpCShopId();
        if (beginTime != null || endTime != null || shopId != null) {
            if (beginTime == null) {
                beginTime = mainTable.getBeginTime();
            }
            if (endTime == null) {
                endTime = mainTable.getEndTime();
            }
            if (beginTime.after(endTime)) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("开始时间不允许大于结束时间",
                        loginUser.getLocale()));
            }
            if (shopId != null) {
                shopId = mainTable.getCpCShopId();
            }
            ValueHolderV14 vh = checkShopAndTime(shopId, beginTime, endTime, loginUser);
            if (!vh.isOK()) {
                return vh;
            }
        }

        if (MapUtils.isNotEmpty(subObjectMap)) {
            Object appointItemObj = subObjectMap.get(SgConstants.SG_C_CHANNEL_SHOP_APPOINT_WAREHOUSE_APPOINT_ITEM.toUpperCase());
            Object excludeItemObj = subObjectMap.get(SgConstants.SG_C_CHANNEL_SHOP_APPOINT_WAREHOUSE_EXCLUDE_ITEM.toUpperCase());

            if (appointItemObj != null && excludeItemObj != null) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("不允许同时设置指定明细与排除明细",
                        loginUser.getLocale()));
            }

            // 校验指定明细
            if (appointItemObj != null) {
                if (log.isDebugEnabled()) {
                    log.debug("SgCChannelShopAppointWarehouseSaveValidator.validateTable appointItemObj:{}", JSONObject.toJSONString(appointItemObj));
                }

                List<SgCChannelShopAppointWarehouseAppointItemDto> appointItem = (List<SgCChannelShopAppointWarehouseAppointItemDto>) appointItemObj;
                if (CollectionUtils.isNotEmpty(appointItem)) {
                    return checkAppointItem(mainObject.getId(), appointItem, loginUser);

                }
            }
            // 校验排除明细
            if (excludeItemObj != null) {
                if (log.isDebugEnabled()) {
                    log.debug("SgCChannelShopAppointWarehouseSaveValidator.validateTable excludeItemObj:{}", JSONObject.toJSONString(excludeItemObj));
                }

                List<SgCChannelShopAppointWarehouseExcludeItemDto> excludeItem = (List<SgCChannelShopAppointWarehouseExcludeItemDto>) excludeItemObj;
                if (CollectionUtils.isNotEmpty(excludeItem)) {
                    return checkExcludeItem(mainObject.getId(), excludeItem, loginUser);
                }
            }
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
    }

    private ValueHolderV14 checkExcludeItem(Long id, List<SgCChannelShopAppointWarehouseExcludeItemDto> excludeItem, User loginUser) {
        Map<Long, List<SgCChannelShopAppointWarehouseExcludeItemDto>> checkItem = excludeItem.stream().collect(Collectors.groupingBy(SgCChannelShopAppointWarehouseExcludeItemDto::getCpCStoreId));

        for (List<SgCChannelShopAppointWarehouseExcludeItemDto> item : checkItem.values()) {
            int size = item.size();
            if (size > 1) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("存在" + item.get(0).getCpCStoreEcode() + "重复逻辑仓记录",
                        loginUser.getLocale()));
            }
        }

        if (id < 1L) {
            return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        }

        LambdaQueryWrapper<SgCChannelShopAppointWarehouseAppointItem> appointWrapper = new LambdaQueryWrapper<>();
        appointWrapper.eq(SgCChannelShopAppointWarehouseAppointItem::getSgCChannelShopAppointWarehouseId, id);
        appointWrapper.eq(SgCChannelShopAppointWarehouseAppointItem::getIsactive, SgConstants.IS_ACTIVE_Y);
        if (appointItemMapper.selectCount(appointWrapper) > 0) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("逻辑仓指定明细存在有效记录，不允许保存",
                    loginUser.getLocale()));
        }

        List<Long> storeIds = excludeItem.stream().map(SgCChannelShopAppointWarehouseExcludeItem::getCpCStoreId).collect(Collectors.toList());

        LambdaQueryWrapper<SgCChannelShopAppointWarehouseExcludeItem> excludeWrapper = new LambdaQueryWrapper<>();
        excludeWrapper.eq(SgCChannelShopAppointWarehouseExcludeItem::getSgCChannelShopAppointWarehouseId, id);
        excludeWrapper.in(SgCChannelShopAppointWarehouseExcludeItem::getCpCStoreId, storeIds);
        excludeWrapper.eq(SgCChannelShopAppointWarehouseExcludeItem::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgCChannelShopAppointWarehouseExcludeItem> existList = excludeItemMapper.selectList(excludeWrapper);

        if (CollectionUtils.isNotEmpty(existList)) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("存在" + existList.get(0).getCpCStoreEcode() + "重复逻辑仓记录",
                    loginUser.getLocale()));
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));

    }

    private ValueHolderV14 checkAppointItem(Long id, List<SgCChannelShopAppointWarehouseAppointItemDto> appointItem, User loginUser) {
        Map<Long, List<SgCChannelShopAppointWarehouseAppointItemDto>> checkItem = appointItem.stream().collect(Collectors.groupingBy(SgCChannelShopAppointWarehouseAppointItemDto::getCpCStoreId));

        for (List<SgCChannelShopAppointWarehouseAppointItemDto> item : checkItem.values()) {
            int size = item.size();
            if (size > 1) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("存在" + item.get(0).getCpCStoreEcode() + "重复逻辑仓记录",
                        loginUser.getLocale()));
            }
        }

        if (id < 1L) {
            return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        }

        LambdaQueryWrapper<SgCChannelShopAppointWarehouseExcludeItem> excludeWrapper = new LambdaQueryWrapper<>();
        excludeWrapper.eq(SgCChannelShopAppointWarehouseExcludeItem::getSgCChannelShopAppointWarehouseId, id);
        excludeWrapper.eq(SgCChannelShopAppointWarehouseExcludeItem::getIsactive, SgConstants.IS_ACTIVE_Y);
        if (excludeItemMapper.selectCount(excludeWrapper) > 0) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("逻辑仓排除明细存在有效记录，不允许保存",
                    loginUser.getLocale()));
        }

        List<Long> storeIds = appointItem.stream().map(SgCChannelShopAppointWarehouseAppointItem::getCpCStoreId).collect(Collectors.toList());

        LambdaQueryWrapper<SgCChannelShopAppointWarehouseAppointItem> appointWrapper = new LambdaQueryWrapper<>();
        appointWrapper.eq(SgCChannelShopAppointWarehouseAppointItem::getSgCChannelShopAppointWarehouseId, id);
        appointWrapper.in(SgCChannelShopAppointWarehouseAppointItem::getCpCStoreId, storeIds);
        appointWrapper.eq(SgCChannelShopAppointWarehouseAppointItem::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgCChannelShopAppointWarehouseAppointItem> existList = appointItemMapper.selectList(appointWrapper);

        if (CollectionUtils.isNotEmpty(existList)) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("存在" + existList.get(0).getCpCStoreEcode() + "重复逻辑仓记录",
                    loginUser.getLocale()));
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
    }

    /**
     * 校验是否存在未审核单据平台店铺、开始时间、结束时间与当前记录开始时间、结束时间存在时间交叉记录
     *
     * @param shopId    平台店铺id
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param user      用户
     * @return ValueHolderV14
     */
    public ValueHolderV14 checkShopAndTime(Long shopId, Date beginTime, Date endTime, User user) {
        SgCChannelShopAppointWarehouse skuStrategy =
                mapper.selectOne(new LambdaQueryWrapper<SgCChannelShopAppointWarehouse>()
                        .eq(SgCChannelShopAppointWarehouse::getCpCShopId, shopId)
                        .eq(SgCChannelShopAppointWarehouse::getStatus,
                                SgChannelConstants.BILL_CHANNEL_STRATEGY_SUBMIT)
                        .and(wrapper -> wrapper.between(SgCChannelShopAppointWarehouse::getBeginTime, beginTime, endTime)
                                .or().between(SgCChannelShopAppointWarehouse::getEndTime, beginTime, endTime)));
        if (skuStrategy != null) {
            return new ValueHolderV14<>(ResultCode.FAIL,
                    Resources.getMessage("平台店铺:" + skuStrategy.getCpCShopTitle() +
                            "当前时间已存在有效的店铺指定实体仓设置，不允许重复设置！", user.getLocale()));
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
    }

}
