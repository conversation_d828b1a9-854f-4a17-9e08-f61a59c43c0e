package com.burgeon.r3.sg.channel;

import com.burgeon.r3.sg.channel.filter.appoint.SgCChannelShopAppointWarehouseSaveFilter;
import com.burgeon.r3.sg.channel.filter.gradient.SgCChannelStorageSyncGradientStrategySaveFilter;
import com.burgeon.r3.sg.channel.filter.gradient.SgCChannelStorageSyncProGradientStrategySaveFilter;
import com.burgeon.r3.sg.channel.filter.omni.SgBOmniChannelProductSaveFilter;
import com.burgeon.r3.sg.channel.filter.product.SgBChannelProductSaveFilter;
import com.burgeon.r3.sg.channel.filter.storage.SgBChannelStorageFullSyncSaveFilter;
import com.burgeon.r3.sg.channel.filter.storage.SgBChannelStorageIncSyncSaveFilter;
import com.burgeon.r3.sg.channel.filter.storage.SgCChannelStoreSafetySettingSaveFilter;
import com.burgeon.r3.sg.channel.filter.storage.SgCChannelStoreSafetySettingSubmitFilter;
import com.burgeon.r3.sg.channel.filter.storage.SgCChannelStoreSafetySettingVoidFilter;
import com.burgeon.r3.sg.channel.filter.strategy.SgCChannelQtyStrategySaveFilter;
import com.burgeon.r3.sg.channel.filter.strategy.SgCChannelQtyStrategySubmitFilter;
import com.burgeon.r3.sg.channel.filter.strategy.SgCChannelQtyStrategyVoidFilter;
import com.burgeon.r3.sg.channel.filter.strategy.SgCChannelRatioStrategySaveFilter;
import com.burgeon.r3.sg.channel.filter.strategy.SgCChannelRatioStrategyVoidFiter;
import com.burgeon.r3.sg.channel.filter.strategy.SgCChannelSkuStrategySaveFilter;
import com.burgeon.r3.sg.channel.filter.strategy.SgCChannelSkuStrategySubmitFilter;
import com.burgeon.r3.sg.channel.filter.strategy.SgCChannelSkuStrategyVoidFilter;
import com.burgeon.r3.sg.channel.validate.appoint.SgCChannelShopAppointWarehouseSaveValidator;
import com.burgeon.r3.sg.channel.validate.appoint.SgCChannelShopAppointWarehouseSubmitValidator;
import com.burgeon.r3.sg.channel.validate.appoint.SgCChannelShopAppointWarehouseUnSubmitValidator;
import com.burgeon.r3.sg.channel.validate.appoint.SgCChannelShopAppointWarehouseVoidValidator;
import com.burgeon.r3.sg.channel.validate.gradient.SgCChannelStorageSyncGradientStrategySaveValidator;
import com.burgeon.r3.sg.channel.validate.gradient.SgCChannelStorageSyncProGradientStrategySaveValidator;
import com.burgeon.r3.sg.channel.validate.omni.SgBOmniChannelProductSaveValidator;
import com.burgeon.r3.sg.channel.validate.omni.SgBOmniChannelProductVoidValidator;
import com.burgeon.r3.sg.channel.validate.storage.SgBChannelStorageFullSyncDeleteValidator;
import com.burgeon.r3.sg.channel.validate.storage.SgBChannelStorageFullSyncSaveValidator;
import com.burgeon.r3.sg.channel.validate.storage.SgBChannelStorageIncSyncDeleteValidator;
import com.burgeon.r3.sg.channel.validate.storage.SgBChannelStorageIncSyncSaveValidator;
import com.burgeon.r3.sg.channel.validate.storage.SgCChannelStoreSafetySettingItemDeleteValidator;
import com.burgeon.r3.sg.channel.validate.storage.SgCChannelStoreSafetySettingSaveValidator;
import com.burgeon.r3.sg.channel.validate.storage.SgCChannelStoreSafetySettingSubmitValidator;
import com.burgeon.r3.sg.channel.validate.storage.SgCChannelStoreSafetySettingVoidValidator;
import com.burgeon.r3.sg.channel.validate.strategy.SgCChannelQtyStrategySaveValidator;
import com.burgeon.r3.sg.channel.validate.strategy.SgCChannelQtyStrategySubmitValidator;
import com.burgeon.r3.sg.channel.validate.strategy.SgCChannelRatioStrategySaveValidator;
import com.burgeon.r3.sg.channel.validate.strategy.SgCChannelSkuStrategySaveValidator;
import com.burgeon.r3.sg.channel.validate.strategy.SgCChannelSkuStrategySubmitValidator;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.jackrain.nea.tableservice.Feature;
import com.jackrain.nea.tableservice.constants.TableServiceConstants;
import com.jackrain.nea.tableservice.feature.FeatureAnnotation;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2021/6/3 16:30
 */
@FeatureAnnotation(value = "SgChannelFeature", description = "渠道基础档案Feature")
public class SgChannelFeature extends Feature {

    @Autowired
    private SgCChannelSkuStrategySaveValidator sgChannelSkuStrategySaveValidator;
    @Autowired
    private SgCChannelSkuStrategySubmitValidator sgChannelSkuStrategySubmitValidator;
    @Autowired
    private SgCChannelSkuStrategySubmitFilter sgChannelSkuStrategySubmitFilter;
    @Autowired
    private SgCChannelSkuStrategySaveFilter sgChannelSkuStrategySaveFilter;
    @Autowired
    private SgCChannelSkuStrategyVoidFilter sgChannelSkuStrategyVoidFilter;

    @Autowired
    private SgCChannelRatioStrategySaveValidator sgChannelRatioStrategySaveValidator;
    @Autowired
    private SgCChannelRatioStrategySaveFilter sgChannelRatioStrategySaveFilter;
    @Autowired
    private SgCChannelRatioStrategyVoidFiter sgChannelRatioStrategyVoidFiter;

    @Autowired
    private SgCChannelQtyStrategySaveValidator qtyStrategySaveValidator;
    @Autowired
    private SgCChannelQtyStrategySaveFilter qtyStrategySaveFilter;
    @Autowired
    private SgCChannelQtyStrategySubmitValidator qtyStrategySubmitValidator;
    @Autowired
    private SgCChannelQtyStrategyVoidFilter qtyStrategyVoidFilter;
    @Autowired
    private SgCChannelQtyStrategySubmitFilter qtyStrategySubmitFilter;
    @Autowired
    private SgBChannelStorageIncSyncSaveValidator storageIncSyncValidator;
    @Autowired
    private SgBChannelStorageIncSyncSaveFilter storageIncSyncFilter;
    @Autowired
    private SgBChannelStorageIncSyncDeleteValidator storageIncSyncDeleteValidator;

    @Autowired
    private SgBChannelStorageFullSyncSaveValidator storageFullSyncValidator;
    @Autowired
    private SgBChannelStorageFullSyncSaveFilter storageFullSyncSaveFilter;
    @Autowired
    private SgBChannelStorageFullSyncDeleteValidator storageFullSyncDeleteValidator;

    @Autowired
    SgBOmniChannelProductSaveValidator sgBOmniChannelProductSaveValidator;
    @Autowired
    SgBOmniChannelProductSaveFilter sgBOmniChannelProductSaveFilter;
    @Autowired
    SgBOmniChannelProductVoidValidator sgBOmniChannelProductVoidValidator;

    @Autowired
    private SgBChannelProductSaveFilter sgBChannelProductSaveFilter;

    @Autowired
    private SgCChannelStoreSafetySettingSaveValidator sgCChannelStoreSafetySettingSaveValidator;
    @Autowired
    private SgCChannelStoreSafetySettingSaveFilter sgCChannelStoreSafetySettingSaveFilter;

    @Autowired
    private SgCChannelStoreSafetySettingVoidValidator sgCChannelStoreSafetySettingVoidValidator;
    @Autowired
    private SgCChannelStoreSafetySettingVoidFilter sgCChannelStoreSafetySettingVoidFilter;

    @Autowired
    private SgCChannelStoreSafetySettingItemDeleteValidator sgCChannelStoreSafetySettingItemDeleteValidator;

    @Autowired
    private SgCChannelStoreSafetySettingSubmitValidator sgCChannelStoreSafetySettingSubmitValidator;
    @Autowired
    private SgCChannelStoreSafetySettingSubmitFilter sgCChannelStoreSafetySettingSubmitFilter;

    @Autowired
    private SgCChannelStorageSyncGradientStrategySaveFilter sgCChannelStorageSyncGradientStrategySaveFilter;
    @Autowired
    private SgCChannelStorageSyncGradientStrategySaveValidator sgCChannelStorageSyncGradientStrategySaveValidator;

    @Autowired
    private SgCChannelStorageSyncProGradientStrategySaveFilter sgCChannelStorageSyncProGradientStrategySaveFilter;
    @Autowired
    private SgCChannelStorageSyncProGradientStrategySaveValidator sgCChannelStorageSyncProGradientStrategySaveValidator;

    @Autowired
    private SgCChannelShopAppointWarehouseSaveFilter sgCChannelShopAppointWarehouseSaveFilter;
    @Autowired
    private SgCChannelShopAppointWarehouseSaveValidator sgCChannelShopAppointWarehouseSaveValidator;
    @Autowired
    private SgCChannelShopAppointWarehouseSubmitValidator sgCChannelShopAppointWarehouseSubmitValidator;
    @Autowired
    private SgCChannelShopAppointWarehouseUnSubmitValidator sgCChannelShopAppointWarehouseUnSubmitValidator;
    @Autowired
    private SgCChannelShopAppointWarehouseVoidValidator sgCChannelShopAppointWarehouseVoidValidator;

    @Override
    protected void initialization() {
        //特殊条码按比例同步策略保存
        addValidator(sgChannelSkuStrategySaveValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_SKU_STRATEGY) ||
                tableName.equalsIgnoreCase(SgConstants.V_SG_C_CHANNEL_SKU_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        addFilter(sgChannelSkuStrategySaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_SKU_STRATEGY) ||
                tableName.equalsIgnoreCase(SgConstants.V_SG_C_CHANNEL_SKU_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //特殊条码按比例同步策略作废
        addFilter(sgChannelSkuStrategyVoidFilter, (tableName, actionName) ->
                (SgConstants.SG_C_CHANNEL_SKU_STRATEGY.equalsIgnoreCase(tableName) ||
                        (SgConstants.V_SG_C_CHANNEL_SKU_STRATEGY.equalsIgnoreCase(tableName)))
                        && actionName.equals(TableServiceConstants.ACTION_VOID));

        //特殊条码按比例同步策略审核
        addValidator(sgChannelSkuStrategySubmitValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_SKU_STRATEGY) ||
                tableName.equalsIgnoreCase(SgConstants.V_SG_C_CHANNEL_SKU_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SUBMIT)));

        addFilter(sgChannelSkuStrategySubmitFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_SKU_STRATEGY) ||
                tableName.equalsIgnoreCase(SgConstants.V_SG_C_CHANNEL_SKU_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SUBMIT)));

        // 按数量同步策略 保存
        addValidator(qtyStrategySaveValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_QTY_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        addFilter(qtyStrategySaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_QTY_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        // 按数量同步策略 保存
        addValidator(sgCChannelStorageSyncGradientStrategySaveValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_STORAGE_SYNC_GRADIENT_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        addFilter(sgCChannelStorageSyncGradientStrategySaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_STORAGE_SYNC_GRADIENT_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        // 按数量同步策略 保存
        addValidator(sgCChannelStorageSyncProGradientStrategySaveValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_STORAGE_SYNC_PRO_GRADIENT_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        addFilter(sgCChannelStorageSyncProGradientStrategySaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_STORAGE_SYNC_PRO_GRADIENT_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        // 按数量同步策略 审核
        addValidator(qtyStrategySubmitValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_QTY_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SUBMIT)));

        addFilter(qtyStrategySubmitFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_QTY_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SUBMIT)));

        // 按数量同步策略 作废
        addFilter(qtyStrategyVoidFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_QTY_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_VOID)));

        //按比例同步策略 保存
        addValidator(sgChannelRatioStrategySaveValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_RATIO_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //按比例同步策略 明细作废
        addFilter(sgChannelRatioStrategyVoidFiter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_RATIO_STRATEGY))
                && (actionName.equals(TableServiceConstants.ACTION_VOID)));

        //按比例同步策略 保存filter
        addFilter(sgChannelRatioStrategySaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_RATIO_STRATEGY))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        //平台库存增量同步
        addValidator(storageIncSyncValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_CHANNEL_STORAGE_INC_SYNC))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        addFilter(storageIncSyncFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_CHANNEL_STORAGE_INC_SYNC))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));
        //平台库存增量删除
        addValidator(storageIncSyncDeleteValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_CHANNEL_STORAGE_INC_SYNC)
                || tableName.equalsIgnoreCase(SgConstants.SG_B_CHANNEL_STORAGE_INC_SYNC_ITEM))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_DELETE)));

        //平台库存全量同步：storageFullSyncValidator
        addValidator(storageFullSyncValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_CHANNEL_STORAGE_FULL_SYNC))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        addFilter(storageFullSyncSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_CHANNEL_STORAGE_FULL_SYNC))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        addValidator(storageFullSyncDeleteValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_CHANNEL_STORAGE_FULL_SYNC))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_DELETE)));

        //全渠道商品保存
        addValidator(sgBOmniChannelProductSaveValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_OMNI_CHANNEL_PRODUCT))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));
        addFilter(sgBOmniChannelProductSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_OMNI_CHANNEL_PRODUCT))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));
        //全渠道商品作废
        addValidator(sgBOmniChannelProductVoidValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_OMNI_CHANNEL_PRODUCT))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_VOID)
                || actionName.equals(TableServiceConstants.ACTION_VOID)));

        //平台店铺商品表保存过滤
        addFilter(sgBChannelProductSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_B_CHANNEL_PRODUCT))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));



        //平台店铺安全库存批量设置保存
        addValidator(sgCChannelStoreSafetySettingSaveValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_STORE_SAFETY_SETTING))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));

        addFilter(sgCChannelStoreSafetySettingSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_STORE_SAFETY_SETTING))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)
                || actionName.equals(TableServiceConstants.ACTION_SAVE)));
        //平台店铺安全库存批量设置作废
        addValidator(sgCChannelStoreSafetySettingVoidValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_STORE_SAFETY_SETTING))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_VOID)));

        addFilter(sgCChannelStoreSafetySettingVoidFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_STORE_SAFETY_SETTING))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_VOID)));
        //删除平台店铺安全库存批量设置明细
        addValidator(sgCChannelStoreSafetySettingItemDeleteValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_STORE_SAFETY_SETTING_ITEM))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_DELETE)));

        //平台店铺安全库存批量设置审核
        addValidator(sgCChannelStoreSafetySettingSubmitValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_STORE_SAFETY_SETTING))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SUBMIT)));

        addFilter(sgCChannelStoreSafetySettingSubmitFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_STORE_SAFETY_SETTING))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SUBMIT)));

        // 店铺指定实体仓
        addValidator(sgCChannelShopAppointWarehouseSaveValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_SHOP_APPOINT_WAREHOUSE))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SAVE)
                || actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)));
        addFilter(sgCChannelShopAppointWarehouseSaveFilter, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_SHOP_APPOINT_WAREHOUSE))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SAVE)
                || actionName.equalsIgnoreCase(TableServiceConstants.ACTION_ADD)));
        addValidator(sgCChannelShopAppointWarehouseSubmitValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_SHOP_APPOINT_WAREHOUSE))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_SUBMIT)));
        addValidator(sgCChannelShopAppointWarehouseUnSubmitValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_SHOP_APPOINT_WAREHOUSE))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_UNSUBMIT)));
        addValidator(sgCChannelShopAppointWarehouseVoidValidator, (tableName, actionName)
                -> (tableName.equalsIgnoreCase(SgConstants.SG_C_CHANNEL_SHOP_APPOINT_WAREHOUSE))
                && (actionName.equalsIgnoreCase(TableServiceConstants.ACTION_VOID)));

    }
}
