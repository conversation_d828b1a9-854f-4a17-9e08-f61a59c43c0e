package com.burgeon.r3.sg.channel.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductQueryRequest;
import com.burgeon.r3.sg.channel.services.product.SgChannelProductQueryService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/8 11:17
 */
@Component
public class SgChannelProductUtils {


    /**
     * 获取平台条码信息
     * (20221116 这个方法有问题，必须带上平台店铺查询，因为平台条码不唯一，查的数据可能并不是想要的数据，
     * 建议调用下面一个方法
     * 而且这个查询也没啥必要非要调用公共方法，直接用mapper 去查就好了)
     *
     * @param skuId 平台条码id
     * @return 平台条码信息
     */
    public static SgBChannelProduct getChannelProduct(String skuId) {
        SgChannelProductQueryService service = ApplicationContextHandle.getBean(SgChannelProductQueryService.class);
        SgChannelProductQueryRequest productQueryRequest = new SgChannelProductQueryRequest();
        List<String> skuIdList = new ArrayList<>();
        skuIdList.add(skuId);
        productQueryRequest.setSkuIdList(skuIdList);
        ValueHolderV14<List<SgBChannelProduct>> result = service.queryChannelProduct(productQueryRequest);
        if (result.isOK() && result.getData().size() > 0) {
            return result.getData().get(0);
        }
        return null;
    }

    /**
     * 获取平台条码信息
     *
     * @param skuId 平台条码id
     * @return 平台条码信息
     */
    public static SgBChannelProduct getChannelProduct(String skuId, Long cpCShopId) {
        SgBChannelProductMapper mapper = ApplicationContextHandle.getBean(SgBChannelProductMapper.class);
        LambdaQueryWrapper<SgBChannelProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SgBChannelProduct::getSkuId, skuId);
        wrapper.eq(SgBChannelProduct::getCpCShopId, cpCShopId);
        wrapper.eq(SgBChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y);
        return mapper.selectOne(wrapper);
    }

    /**
     * 获取平台条码信息
     *
     * @param skuId 平台条码id
     * @return 平台条码信息
     */
    public static List<SgBChannelProduct> getChannelProductList(List<String> skuIdList) {
        SgChannelProductQueryService service = ApplicationContextHandle.getBean(SgChannelProductQueryService.class);
        SgChannelProductQueryRequest productQueryRequest = new SgChannelProductQueryRequest();
        productQueryRequest.setSkuIdList(skuIdList);
        ValueHolderV14<List<SgBChannelProduct>> result = service.queryChannelProduct(productQueryRequest);
        if (result.isOK() && result.getData().size() > 0) {
            return result.getData();
        }
        return null;
    }


    /**
     * 获取平台条码信息
     *
     * @param numiid     平台商品id
     * @param numiidFlag 是否是平台商品id Y：表示传入的是平台商品ID
     * @return 平台条码信息
     */
    public static List<SgBChannelProduct> getChannelProduct(String numiid, String numiidFlag) {
        SgChannelProductQueryService service = ApplicationContextHandle.getBean(SgChannelProductQueryService.class);
        SgChannelProductQueryRequest productQueryRequest = new SgChannelProductQueryRequest();
        if (SgConstants.IS_ACTIVE_Y.equals(numiidFlag)) {
            List<String> numiidList = new ArrayList<>();
            numiidList.add(numiid);
            productQueryRequest.setNumiidList(numiidList);
            ValueHolderV14<List<SgBChannelProduct>> result = service.queryChannelProduct(productQueryRequest);
            if (result.isOK() && result.getData().size() > 0) {
                return result.getData();
            }
        }
        return null;
    }

}
