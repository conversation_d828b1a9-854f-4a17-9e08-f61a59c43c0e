package com.burgeon.r3.sg.channel.validate.appoint;

import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.appoint.SgCChannelShopAppointWarehouseAppointItemMapper;
import com.burgeon.r3.sg.channel.mapper.appoint.SgCChannelShopAppointWarehouseExcludeItemMapper;
import com.burgeon.r3.sg.channel.mapper.appoint.SgCChannelShopAppointWarehouseMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelSkuStrategyDTO;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/6/4 15:27
 */
@Component
public class SgCChannelShopAppointWarehouseUnSubmitValidator extends BaseSingleValidator<SgCChannelSkuStrategyDTO> {

    @Autowired
    private SgCChannelShopAppointWarehouseMapper mapper;
    @Autowired
    private SgCChannelShopAppointWarehouseAppointItemMapper appointItemMapper;
    @Autowired
    private SgCChannelShopAppointWarehouseExcludeItemMapper excludeItemMapper;

    @Override
    public String getValidatorMsgName() {
        return "店铺指定实体仓设置取消审核";
    }

    @Override
    public Class getValidatorClass() {
        return SgCChannelShopAppointWarehouseUnSubmitValidator.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCChannelSkuStrategyDTO mainObject, User loginUser) {
        SgCChannelSkuStrategyDTO data = getOrignalData();

        if (!SgChannelConstants.BILL_CHANNEL_STRATEGY_SUBMIT.equals(data.getStatus())) {
            return new ValueHolderV14<>(ResultCode.FAIL,
                    Resources.getMessage("单据状态不为已审核，不允许取消审核", loginUser.getLocale()));
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("取消审核校验通过！"));
    }
}
