package com.burgeon.r3.sg.channel.services;

import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.config.SgStorageControlConfig;
import com.burgeon.r3.sg.basic.model.AbstractSgStorageUpdateCommonModel;
import com.burgeon.r3.sg.basic.model.request.ISgStorageSingleUpdate;
import com.burgeon.r3.sg.basic.model.request.SgStorageSingleUpdateRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageUpdateBillRequest;
import com.burgeon.r3.sg.basic.model.request.vo.SgStorageSingleUpdateCfRequest;
import com.burgeon.r3.sg.basic.model.request.vo.SgStorageSingleUpdateSaRequest;
import com.burgeon.r3.sg.basic.model.result.AbstractSgStorageOutStockResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageBillUpdateResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageUpdateResult;
import com.burgeon.r3.sg.basic.model.result.vo.SgStorageOutStockLsResult;
import com.burgeon.r3.sg.basic.model.result.vo.SgStorageOutStockSaResult;
import com.burgeon.r3.sg.basic.model.result.vo.SgStorageOutStockSsResult;
import com.burgeon.r3.sg.basic.services.SgStorageBillTransUpdateService;
import com.burgeon.r3.sg.basic.services.SgStorageRedisBillUpdateService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/7/20 15:43
 * 库存更新服务
 */
@Slf4j
@Component
public class SgChannelStorageService {

    @Autowired
    private SgStorageBillTransUpdateService transUpdateService;

    @Autowired
    private SgStorageRedisBillUpdateService redisUpdateService;

    @Autowired
    private SgStorageControlConfig sgStorageControlConfig;


    /**
     * @Description: 单张单据渠道锁定库存更新接口（单据单事务）
     * @Param: SgStorageBatchUpdateSaRequest 渠道锁定库存更新请求信息
     * @Return: ValueHolder
     */
    public ValueHolderV14<SgStorageUpdateResult> updateStorageBillWithTrans(SgStorageSingleUpdateCfRequest request) {
        ValueHolderV14<SgStorageUpdateResult> holder = checkServiceParam(request);
        if (ResultCode.FAIL == holder.getCode()) {
            log.warn("SgStorageUpdateCmdImpl.updateStorageBillWithTrans. checkServiceParam error:{};", holder.getMessage());
            return holder;
        }

        SgStorageSingleUpdateRequest singleRequest = new SgStorageSingleUpdateRequest();
        singleRequest.setControlModel(request.getControlModel());
        singleRequest.setLoginUser(request.getLoginUser());
        singleRequest.setMessageKey(request.getMessageKey());

        SgStorageUpdateBillRequest billRequest = new SgStorageUpdateBillRequest();
        BeanUtils.copyProperties(request.getBill(), billRequest);
        this.convertAbstractItemList(billRequest, request.getBill().getItemList());
        singleRequest.setBill(billRequest);

        return updateStorageBillWithTrans(singleRequest);
    }

    /**
     * @Description: 单张单据配销仓库存更新接口（单据单事务）
     * @Param: SgStorageBatchUpdateSaRequest 配销仓库存更新请求信息
     * @Return: ValueHolder
     */
    public ValueHolderV14<SgStorageUpdateResult> updateStorageBillWithTrans(SgStorageSingleUpdateSaRequest request) {

        ValueHolderV14<SgStorageUpdateResult> holder = checkServiceParam(request);
        if (ResultCode.FAIL == holder.getCode()) {
            log.warn("SgStorageUpdateCmdImpl.updateStorageBillWithTrans. checkServiceParam error:{};", holder.getMessage());
            return holder;
        }

        SgStorageSingleUpdateRequest singleRequest = new SgStorageSingleUpdateRequest();
        singleRequest.setControlModel(request.getControlModel());
        singleRequest.setLoginUser(request.getLoginUser());
        singleRequest.setMessageKey(request.getMessageKey());

        SgStorageUpdateBillRequest billRequest = new SgStorageUpdateBillRequest();
        BeanUtils.copyProperties(request.getBill(), billRequest);
        this.convertAbstractItemList(billRequest, request.getBill().getItemList());
        singleRequest.setBill(billRequest);

        return updateStorageBillWithTrans(singleRequest);
    }


    /**
     * @Description: 单张单据库存更新接口（批量单事务）
     * @Param: SgStorageBatchUpdateLsRequest 库存更新请求信息
     * @Return: ValueHolder
     */
    private ValueHolderV14<SgStorageUpdateResult> updateStorageBillWithTrans(SgStorageSingleUpdateRequest request) {

        ValueHolderV14<SgStorageUpdateResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        SgStorageUpdateResult updateResult = new SgStorageUpdateResult();
        ValueHolderV14<SgStorageBillUpdateResult> billUpdateResult;

        if (sgStorageControlConfig.isRedisFunction()) {
            billUpdateResult = redisUpdateService.updateStorageBill(request);
        } else {
            billUpdateResult = transUpdateService.updateStorageBill(request);
        }

        if (billUpdateResult != null && billUpdateResult.getData() != null) {
            updateResult = convertSgUpdateResult(billUpdateResult.getData());
        }

        holder.setData(updateResult);

        if (billUpdateResult != null) {
            holder.setCode(billUpdateResult.getCode());
            holder.setMessage(billUpdateResult.getMessage());
        }

        return holder;

    }


    /**
     * 转换库存更新结果模型（业务逻辑层->接口层）
     *
     * @param updateResult
     * @return
     */
    public static SgStorageUpdateResult convertSgUpdateResult(SgStorageBillUpdateResult updateResult) {

        SgStorageUpdateResult result = new SgStorageUpdateResult();
        List<AbstractSgStorageOutStockResult> outStockItemList = new ArrayList<>();

        if (updateResult != null) {

            result.setErrorBillItemQty(updateResult.getErrorBillItemQty());
            result.setPreoutUpdateResult(updateResult.getPreoutUpdateResult());
            result.setRedisBillFtpKeyList(updateResult.getRedisBillFtpKeyList());

            //解析缺货明细列表
            if (!CollectionUtils.isEmpty(updateResult.getOutStockItemList())) {

                for (AbstractSgStorageUpdateCommonModel commonModel : updateResult.getOutStockItemList()) {
                    AbstractSgStorageOutStockResult outResult = null;
                    switch (commonModel.getStorageType()) {
                        case SgConstantsIF.STORAGE_TYPE_STORAGE:
                            outResult = new SgStorageOutStockLsResult();
                            break;
                        case SgConstantsIF.STORAGE_TYPE_SA:
                            outResult = new SgStorageOutStockSaResult();
                            break;
                        case SgConstantsIF.STORAGE_TYPE_SHARE:
                            outResult = new SgStorageOutStockSsResult();
                            break;
                        default:
                    }

                    BeanUtils.copyProperties(commonModel, outResult);
                    outStockItemList.add(outResult);
                }

                result.setOutStockItemList(outStockItemList);
            }

        }

        return result;
    }

    /**
     * @param request
     * @return
     */
    private ValueHolderV14<SgStorageUpdateResult> checkServiceParam(ISgStorageSingleUpdate request) {

        ValueHolderV14<SgStorageUpdateResult> holder = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

        if (request == null || request.getBillObject() == null ||
                org.springframework.util.CollectionUtils.isEmpty(request.getBillObject().getBillItemList())) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(Resources.getMessage("请求体或单据明细为空！"));
        }

        return holder;

    }

    /**
     * 设置单据明细
     *
     * @param billRequest
     * @param abstractItemList
     */
    private void convertAbstractItemList(SgStorageUpdateBillRequest billRequest,
                                         List abstractItemList) {
        billRequest.setItemList(abstractItemList);
    }
}
