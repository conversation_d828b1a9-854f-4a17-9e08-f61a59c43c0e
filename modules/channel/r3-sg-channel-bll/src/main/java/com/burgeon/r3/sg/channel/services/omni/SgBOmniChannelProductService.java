package com.burgeon.r3.sg.channel.services.omni;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.mapper.omni.SgBOmniChannelProductMapper;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.model.request.omni.SgBOmniChannelProductQueryRequest;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.omni.SgBOmniChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/30 10:11
 */
@Slf4j
@Component
public class SgBOmniChannelProductService {
    @Autowired
    private SgBChannelProductMapper sgBChannelProductMapper;
    @Autowired
    private SgBOmniChannelProductMapper omniChannelProductMapper;

    /**
     * 根据numiid(商品id)+cpCShopId(平台店铺id)查询出可用的全渠道商品
     *
     * @param request 平台商品id和平台店铺id
     * @return 全渠道商品列表
     */
    public ValueHolderV14<List<SgBChannelProduct>> queryOmniChannelProduct(SgBOmniChannelProductQueryRequest request) {
        ValueHolderV14<List<SgBChannelProduct>> holder = new ValueHolderV14<>(ResultCode.SUCCESS, "查询全渠道商品列表成功");

        //入参日志记录
        if (log.isDebugEnabled()) {
            log.debug("Start SgBOmniChannelProductService.queryOmniChannelProduct. ReceiveParams:SgBOmniChannelProductQueryRequest={}"
                    , JSONObject.toJSONString(request));
        }

        LambdaQueryWrapper<SgBChannelProduct> lqw = new LambdaQueryWrapper<SgBChannelProduct>();
        if (Objects.nonNull(request.getId()) && request.getId() > 0L) {
            SgBOmniChannelProduct sgBOmniChannelProduct = omniChannelProductMapper.selectById(request.getId());
            if (Objects.nonNull(request.getCpCShopId()) && request.getCpCShopId() > 0L) {
                lqw.eq(SgBChannelProduct::getCpCShopId, request.getCpCShopId());
            } else {
                lqw.eq(SgBChannelProduct::getCpCShopId, sgBOmniChannelProduct.getCpCShopId());
            }

            if (StringUtils.isNotEmpty(request.getNumiid())) {
                lqw.eq(SgBChannelProduct::getNumiid, request.getNumiid());
            } else {
                lqw.eq(SgBChannelProduct::getNumiid, sgBOmniChannelProduct.getNumiid());
            }
        } else {
            lqw.eq(SgBChannelProduct::getCpCShopId, request.getCpCShopId())
                    .eq(SgBChannelProduct::getNumiid, request.getNumiid());
        }
        lqw.eq(SgBChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgBChannelProduct> sgBChannelProducts = sgBChannelProductMapper.selectList(lqw);
        holder.setData(sgBChannelProducts);
        return holder;
    }

    public ValueHolderV14<List<SgBOmniChannelProduct>> checkExists(SgBOmniChannelProductQueryRequest request) {
        ValueHolderV14<List<SgBOmniChannelProduct>> holder = new ValueHolderV14<>(ResultCode.SUCCESS, "校验全渠道商品列表成功");
        //入参日志记录
        if (log.isDebugEnabled()) {
            log.debug("Start SgBOmniChannelProductService.checkExists. ReceiveParams:request={}"
                    , JSONObject.toJSONString(request));
        }

        LambdaQueryWrapper<SgBOmniChannelProduct> lqw = new LambdaQueryWrapper<>();
        if (Objects.nonNull(request.getId()) && request.getId() > 0L) {
            SgBOmniChannelProduct item = omniChannelProductMapper.selectById(request.getId());

            if (Objects.nonNull(request.getCpCShopId()) && request.getCpCShopId() > 0L) {
                lqw.eq(SgBOmniChannelProduct::getCpCShopId, request.getCpCShopId());
            } else {
                lqw.eq(SgBOmniChannelProduct::getCpCShopId, item.getCpCShopId());
            }

            if (StringUtils.isNotEmpty(request.getNumiid())) {
                lqw.eq(SgBOmniChannelProduct::getNumiid, request.getNumiid());
            } else {
                lqw.eq(SgBOmniChannelProduct::getNumiid, item.getNumiid());
            }
        } else {
            lqw.eq(SgBOmniChannelProduct::getCpCShopId, request.getCpCShopId())
                    .eq(SgBOmniChannelProduct::getNumiid, request.getNumiid());
        }
        lqw.eq(SgBOmniChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgBOmniChannelProduct> list = omniChannelProductMapper.selectList(lqw);
        holder.setData(list);
        return holder;
    }
}
