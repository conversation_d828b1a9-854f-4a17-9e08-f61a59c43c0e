package com.burgeon.r3.sg.channel.enumerate;

public enum SaStoreTypeEnum {

    ACTIVITY("1", "活动"),
    GENERAL("2", "普通"),
    SAME_CITY("3", "同城购");

    private String code;
    private String desc;

    SaStoreTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
