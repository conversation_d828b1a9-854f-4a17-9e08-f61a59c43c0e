package com.burgeon.r3.sg.channel.services.product;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.burgeon.r3.sg.channel.model.event.SysLogEvent;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.common.SpringContextHolder;
import com.burgeon.r3.sg.core.model.table.channel.control.SgCChannelStockControlLog;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/11/24 10:34
 * @Version 1.0
 **/
@Component
@Slf4j
public class SgCChannelStockControlLogService {


    public ValueHolderV14 downloadPlatformNumLog(QuerySession querySession) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        log.info("SgCChannelStockControlLogService.downloadPlatformNumLog.param:{}", param.toString());
        User user = querySession.getUser();
        SgCChannelStockControlLog sgCChannelStockControlLog = new SgCChannelStockControlLog();
        StorageUtils.setBModelDefalutData(sgCChannelStockControlLog, user);
        sgCChannelStockControlLog.setOperationType("下载平台商品");
        sgCChannelStockControlLog.setModContent(param.toString());
        SysLogEvent<SgCChannelStockControlLog> sysLogEvent = new SysLogEvent(sgCChannelStockControlLog);
        SpringContextHolder.publishEvent(sysLogEvent);

        return valueHolderV14;
    }
}
