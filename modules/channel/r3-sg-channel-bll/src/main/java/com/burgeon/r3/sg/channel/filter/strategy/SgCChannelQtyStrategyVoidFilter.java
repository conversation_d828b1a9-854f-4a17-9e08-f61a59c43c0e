package com.burgeon.r3.sg.channel.filter.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyItemMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelQtyStrategyDTO;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.qtystrategy.SgCChannelQtyStrategyItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date
 */
@Slf4j
@Component
public class SgCChannelQtyStrategyVoidFilter extends BaseSingleFilter<SgCChannelQtyStrategyDTO> {

    @Autowired
    private SgCChannelQtyStrategyItemMapper qtyStrategyItemMapper;

    @Override
    public String getFilterMsgName() {
        return "数量同步策略作废";
    }

    @Override
    public Class getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgCChannelQtyStrategyDTO mainObject, User loginUser) {
        return null;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgCChannelQtyStrategyDTO data, User loginUser) {
        Long objid = data.getId();
        //存在已发布明细 不允许作废
        Integer count = qtyStrategyItemMapper.selectCount(new LambdaQueryWrapper<SgCChannelQtyStrategyItem>()
                .eq(SgCChannelQtyStrategyItem::getSgCChannelQtyStrategyId, objid)
                .eq(SgCChannelQtyStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCChannelQtyStrategyItem::getIspublish, SgConstants.IS_ACTIVE_Y));
        if (count > 0) {
            return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前活动库存已同步平台，请进行【商品下架】作废！"));
        }

        data.setStatus(SgConstants.CON_BILL_STATUS_03);

        // 作废明细
        LambdaUpdateWrapper<SgCChannelQtyStrategyItem> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(SgCChannelQtyStrategyItem::getSgCChannelQtyStrategyId, objid);
        wrapper.eq(SgCChannelQtyStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y);
        SgCChannelQtyStrategyItem item = new SgCChannelQtyStrategyItem();
        item.setIsactive(SgConstants.IS_ACTIVE_N);
        qtyStrategyItemMapper.update(item, wrapper);

        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("明细作废成功！"));
    }
}
