package com.burgeon.r3.sg.channel.mapper.strategy;

import com.burgeon.r3.sg.core.model.table.channel.ratiostrategy.SgCChannelRatioStrategyItem;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface SgCChannelRatioStrategyItemMapper extends ExtentionMapper<SgCChannelRatioStrategyItem> {

    /**
     * 根据店铺id查询 比例策略明细关联配销仓性质(和产品沟通 当前明细关联配销仓要么都是大效期 要么都不是)
     * @param shopId 店铺id
     * @return String 配销仓性质
     */
    @Select(" SELECT " +
            "sa.CATEGORY " +
            "FROM " +
            " sg_c_channel_ratio_strategy_item i  " +
            "LEFT JOIN sg_c_channel_ratio_strategy m ON i.sg_c_channel_ratio_strategy_id = m.id " +
            "LEFT JOIN sg_c_sa_store sa on i.sg_c_sa_store_id=sa.id " +
            "WHERE " +
            " m.cp_c_shop_id =#{shopId} and m.isactive='Y' " +
            "and i.isactive='Y' order  by  i.SG_C_SA_STORE_ORDERNO desc limit 1;")
    String querySaStoreCategoryByShop(@Param("shopId") Long shopId);

    @Update("<script>" +
            "UPDATE sg_c_channel_ratio_strategy_item " +
            " SET isactive = 'N',modifiername=#{modifiername},modifierename=#{modifierename},modifieddate=NOW() " +
            " WHERE isactive='Y' " +
            "</script>")
    int voidAllStrategyItem(@Param("modifierid") Integer modifierid, @Param("modifiername") String modifiername, @Param("modifierename") String modifierename);
}