package com.burgeon.r3.sg.channel.services.product;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.burgeon.r3.sg.basic.mapper.SgBSaStorageMapper;
import com.burgeon.r3.sg.channel.enumerate.SaStoreTypeEnum;
import com.burgeon.r3.sg.channel.filter.product.SgBChannelProductSaveFilter;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategyMapper;
import com.burgeon.r3.sg.channel.model.dto.storage.SgBChannelProductDownloadDto;
import com.burgeon.r3.sg.channel.model.dto.storage.SgBStorageBufferDto;
import com.burgeon.r3.sg.channel.model.enumerate.CalcStatusEnum;
import com.burgeon.r3.sg.channel.model.event.SysLogEvent;
import com.burgeon.r3.sg.channel.model.request.product.SgBChannelProductDevOpsQueryRequest;
import com.burgeon.r3.sg.channel.model.request.product.SgBChannelProductDevOpsSaveInfoRequest;
import com.burgeon.r3.sg.channel.model.request.product.SgBChannelProductDevOpsSaveRequest;
import com.burgeon.r3.sg.channel.model.result.product.SgBChannelProductDevOpsQueryInfoResult;
import com.burgeon.r3.sg.channel.model.result.product.SgBChannelProductExcelVo;
import com.burgeon.r3.sg.channel.model.result.product.SgBSaStorageResult;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.common.SpringContextHolder;
import com.burgeon.r3.sg.core.model.table.channel.control.SgCChannelStockControlLog;
import com.burgeon.r3.sg.core.model.table.channel.control.SgCChannelStockControlLogItem;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.oms.SgBChannelStorageBuffer;
import com.burgeon.r3.sg.core.model.tableExtend.SgBChannelStorageBufferExtend;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.stocksync.api.SgBChannelStorageBufferCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferBatchSaveRequest;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferSaveRequest;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageOmsManualSynchRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpShopQueryCmd;
import com.jackrain.nea.cpext.api.UserQueryCmd;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description:
 * @author: hwy
 * @time: 2021/8/25 10:45
 */
@Component
@Slf4j
public class SgBChannelProductDevOpsSaveService {

    @Autowired
    private SgBChannelProductSaveFilter productSaveFilter;

    @Autowired
    private SgBChannelProductMapper sgBChannelProductMapper;

    @Autowired
    private SgCChannelSkuStrategyMapper strategyMapper;

    @Reference(group = "sg", version = "1.0")
    private SgBChannelStorageBufferCmd sgBChannelStorageBufferCmd;

    @Reference(group = "cp-ext", version = "1.0")
    private CpShopQueryCmd shopQueryCmd;

    @Autowired
    private SgChannelProductDownloadService productDownloadService;

    @Autowired
    private SgChannelProductQueryService sgChannelProductQueryService;

    @Autowired
    private SgCChannelRatioStrategyMapper ratioStrategyMapper;

    @Autowired
    private SgBSaStorageMapper storageMapper;

    @Value("${download.url:oss://adb/}")
    private String downloadUrl;

    @Value("${sg.product.batch.update.size:500}")
    private Integer batchUpdateSize;

    @Value("${r3.sg.channel.stock.expirationTime:5}")
    private Integer expirationTime;
    @Value("${r3.sg.channel.stock.expirationTimeFlag:true}")
    private boolean expirationTimeFlag;

    @Value("${r3.sg.channel.adb.schema:r3_oms_sg}")
    private String adbSchema;


    //下载平台库存
    public static final String REDIS_KEY_CHANNEL_DOWNPLAT = "downplat";
    public static final String REDIS_KEY_CHANNEL_DOWN_STOCK = "stock:";
    //同步库存
    public static final String REDIS_KEY_CHANNEL_SYNCSTOCK = "syncstock";

    /**
     * @param request:
     * @Description: 保存信息
     * @Author: hwy
     * @Date: 2021/8/25 14:31
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14
     **/
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 sgBChannelProductDevOpsSave(SgBChannelProductDevOpsSaveRequest request) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        log.debug(" SgBChannelProductDevOpsSaveService.sgBChannelProductDevOpsSave param:{}", JSONObject.toJSONString(request));
        try {
            checkParam(request, valueHolderV14);
            if (!valueHolderV14.isOK()) {
                return valueHolderV14;
            }


            //进行按钮时间控制
            List<SgBChannelProductDevOpsSaveInfoRequest> itemRequestList = request.getSaveInfoRequestList();
            //是否同步库存 按钮  才要进行
            if (expirationTimeFlag && StringUtils.isNotEmpty(itemRequestList.get(0).getIslock())) {
                LocalDateTime localDateTime = LocalDateTime.now();
                DateTimeFormatter sdFormat = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
                String afterDateTime = sdFormat.format(localDateTime.plusSeconds(expirationTime));


                //List<SgBChannelStorageIncDevOpsSaveItemRequest> itemRequestList = request.getItemList();
                SgBChannelProduct sgBChannelProduct = sgBChannelProductMapper.selectById(itemRequestList.get(0).getId());
                Long cpCShopId = sgBChannelProduct.getCpCShopId();
                //设置key1
                String redisKey1 = SgConstants.REDIS_KEY_CHANNEL_SG.concat(SgConstants.SG_CONNECTOR_MARKS_1)
                        .concat(REDIS_KEY_CHANNEL_SYNCSTOCK).concat(SgConstants.SG_CONNECTOR_MARKS_2).concat(SgConstants.SG_CONNECTOR_MARKS_4)
                        .concat(REDIS_KEY_CHANNEL_DOWN_STOCK).concat(cpCShopId.toString());
                String redisKey2 = SgConstants.REDIS_KEY_CHANNEL_SG.concat(SgConstants.SG_CONNECTOR_MARKS_1)
                        .concat(REDIS_KEY_CHANNEL_SYNCSTOCK).concat(SgConstants.SG_CONNECTOR_MARKS_2).concat(SgConstants.SG_CONNECTOR_MARKS_4)
                        .concat(SgConstants.REDIS_KEY_CHANNEL_TEMP_KEY).concat(cpCShopId.toString());
                String redisKey3 = SgConstants.REDIS_KEY_CHANNEL_SG.concat(SgConstants.SG_CONNECTOR_MARKS_1)
                        .concat(REDIS_KEY_CHANNEL_SYNCSTOCK).concat(SgConstants.SG_CONNECTOR_MARKS_2).concat(SgConstants.SG_CONNECTOR_MARKS_4)
                        .concat(SgConstants.REDIS_KEY_CHANNEL_TEMP_RES).concat(cpCShopId.toString());
                List<String> kesys = new ArrayList<>();
                kesys.add(redisKey1);
                kesys.add(redisKey2);
                kesys.add(redisKey3);


                Object[] redisRequest = new Object[itemRequestList.size() * 2];

                for (int i = 0; i < itemRequestList.size(); i++) {
                    redisRequest[i * 2 + 1] = itemRequestList.get(i).getId().toString();
                    redisRequest[i * 2] = afterDateTime;
                }


                CusRedisTemplate<String, String> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();
                DefaultRedisScript<List> redisScript = new DefaultRedisScript<>();
                redisScript.setLocation(new ClassPathResource("lua/StockStorageLockRedis.lua"));
                redisScript.setResultType(List.class);
                log.info("SgBChannelProductDevOpsSaveService.sgBChannelProductDevOpsSync.redisrequest.param:{}", JSONObject.toJSONString(redisRequest));
                List result = redisMasterTemplate.execute(redisScript, kesys, redisRequest);
                log.info("SgBChannelProductDevOpsSaveService.sgBChannelProductDevOpsSync.redisrresult.param:{}", JSONObject.toJSONString(result));
                if (!result.get(0).equals("0")) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("是否同步库存按钮控制处理失败");
                    return valueHolderV14;
                }
                if (result.size() < 2) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("当前所选择的数据请在" + expirationTime + "s之后再进行修改！");
                    return valueHolderV14;
                }
                List<String> filterResultString = (ArrayList) result.get(1);
                if (CollectionUtils.isEmpty(filterResultString)) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("当前所选择的数据请在" + expirationTime + "s之后再进行修改！");
                    return valueHolderV14;
                }
                List<Long> filterResult = filterResultString.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                List<SgBChannelProductDevOpsSaveInfoRequest> itemRequest = new ArrayList<>();
                itemRequestList.forEach(o -> {
                    if (filterResult.contains(o.getId())) {
                        itemRequest.add(o);
                    }
                });
                request.setSaveInfoRequestList(itemRequest);
            }
            log.info("SgBChannelProductDevOpsSaveService.sgBChannelProductDevOpsSave.sgBChannelProductDevOpsSave request={}",
                    JSONObject.toJSONString(request));

            UserQueryCmd userQueryCmd = (UserQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), UserQueryCmd.class.getName(), "cp-ext", "1.0");
            User user = userQueryCmd.getUsersById(request.getUserId());
            List<SgBChannelProductDevOpsSaveInfoRequest> saveInfoRequestList = request.getSaveInfoRequestList();
            List<SgBChannelProductDevOpsSaveInfoRequest> needSyncList = saveInfoRequestList.stream().filter(o -> o.getQtySafety() != null).collect(Collectors.toList());
            for (SgBChannelProductDevOpsSaveInfoRequest saveInfoRequest : saveInfoRequestList) {
                SgBChannelProduct sgBChannelProduct = new SgBChannelProduct();
                sgBChannelProduct.setId(saveInfoRequest.getId());
                if (StringUtils.isNotEmpty(saveInfoRequest.getIslock())) {
                    sgBChannelProduct.setIslock(saveInfoRequest.getIslock());
                }
                if (StringUtils.isNotEmpty(saveInfoRequest.getIstrans())) {
                    sgBChannelProduct.setIstrans(saveInfoRequest.getIstrans());
                }
                if (saveInfoRequest.getQtySafety() != null) {
                    sgBChannelProduct.setQtySafety(saveInfoRequest.getQtySafety());
                }
                StorageUtils.setBModelDefalutDataByUpdate(sgBChannelProduct, user);
                sgBChannelProductMapper.updateById(sgBChannelProduct);
            }
            if (log.isDebugEnabled()) {
                log.debug("SgBChannelProductDevOpsSaveService.sgBChannelProductDevOpsSave.sgBChannelProductDevOpsSave request={}",
                        JSONObject.toJSONString(request));
            }
            if (CollectionUtils.isEmpty(needSyncList)) {
                saveSgChannelStockControlLog(request, user);
                return valueHolderV14;
            }
            List<Long> productIds = needSyncList.stream().map(SgBChannelProductDevOpsSaveInfoRequest::getId).collect(Collectors.toList());
            List<SgBChannelProduct> products = sgBChannelProductMapper.selectList(new QueryWrapper<SgBChannelProduct>().lambda()
                    .in(SgBChannelProduct::getId, productIds)
                    .eq(SgBChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y));
            SgChannelStorageBufferBatchSaveRequest bufferBatchSaveRequest = new SgChannelStorageBufferBatchSaveRequest();
            List<SgChannelStorageBufferSaveRequest> requestItemList = new ArrayList<>();
            products.forEach(product -> {
                SgChannelStorageBufferSaveRequest requestItem = new SgChannelStorageBufferSaveRequest();
                requestItem.setCpCShopId(product.getCpCShopId());
                requestItem.setPsCSkuId(product.getPsCSkuId());
                requestItem.setPsCSkuEcode(product.getPsCSkuEcode());
                requestItem.setSkuId(product.getSkuId());
                requestItem.setCpCShopTitle(product.getCpCShopTitle());
                requestItem.setCpCPlatformId(product.getCpCPlatformId());
                requestItem.setDealStatus(SgBChannelStorageBufferExtend.DealStatusEnum.UN_DEAL.getCode());
                requestItem.setSourceNo("平台店铺库存管理-修改商品安全库存");
                requestItemList.add(requestItem);
            });

            bufferBatchSaveRequest.setUser(user);
            bufferBatchSaveRequest.setBufferSaveRequestList(requestItemList);
            String batchNo = "DEVOPS:" + System.currentTimeMillis() + (int) (Math.random() * 9000 + 1000);
            bufferBatchSaveRequest.setBatchno(batchNo);
            if (log.isDebugEnabled()) {
                log.debug("Start SgBOmniChannelProductSaveFilter.saveDataToChannelStorageBuffer request={}",
                        JSONObject.toJSONString(bufferBatchSaveRequest));
            }
            sgBChannelStorageBufferCmd.saveDataToChannelStorageBuffer(bufferBatchSaveRequest);
            saveSgChannelStockControlLog(request, user);
        } catch (Exception e) {
            log.error("SgBChannelProductDevOpsSaveService.sgBChannelProductDevOpsSave 修改平台商品信息异常:{}",
                    Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("修改平台商品信息异常");
        }
        return valueHolderV14;
    }


    /**
     * 记录新增/修改操作日志
     *
     * @param request
     * @param user
     */
    public void saveSgChannelStockControlLog(SgBChannelProductDevOpsSaveRequest request, User user) {
        log.info("saveSgChannelStockControlLog.param:{}", JSONObject.toJSONString(request));
        List<SgBChannelProductDevOpsSaveInfoRequest> saveInfoRequestList = request.getSaveInfoRequestList();

        if (user == null) {
            UserQueryCmd userQueryCmd = (UserQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), UserQueryCmd.class.getName(), "cp-ext", "1.0");
            user = userQueryCmd.getUsersById(request.getUserId());
        }

        SgCChannelStockControlLog sgCChannelStockControlLog = new SgCChannelStockControlLog();
        StorageUtils.setBModelDefalutData(sgCChannelStockControlLog, user);

        List<SgCChannelStockControlLogItem> controlLogItemList = new ArrayList<>();

        //获取当前的店铺信息
        List<Long> productIds = saveInfoRequestList.stream().map(SgBChannelProductDevOpsSaveInfoRequest::getId).collect(Collectors.toList());
        List<SgBChannelProduct> productLists = sgBChannelProductMapper.selectList(new QueryWrapper<SgBChannelProduct>().lambda()
                .in(SgBChannelProduct::getId, productIds)
                .eq(SgBChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y));

        Map<Long, SgBChannelProduct> collectSgBChannelProductMap = productLists.stream().collect(Collectors.toMap(SgBChannelProduct::getId, Function.identity()));

        for (SgBChannelProductDevOpsSaveInfoRequest saveInfoRequest : saveInfoRequestList) {
            if (collectSgBChannelProductMap.containsKey(saveInfoRequest.getId())) {
                SgBChannelProduct sgBChannelProduct = collectSgBChannelProductMap.get(saveInfoRequest.getId());
                SgCChannelStockControlLogItem sgCChannelStockControlLogItem = new SgCChannelStockControlLogItem();

                sgCChannelStockControlLogItem.setCpCShopId(sgBChannelProduct.getCpCShopId());
                sgCChannelStockControlLogItem.setSkuId(sgBChannelProduct.getSkuId());
                if (StringUtils.isNotEmpty(saveInfoRequest.getIslock())) {
                    sgCChannelStockControlLog.setOperationType("同步库存");
                    sgCChannelStockControlLogItem.setIslock(saveInfoRequest.getIslock());
                } else if (StringUtils.isNotEmpty(saveInfoRequest.getIstrans())) {
                    sgCChannelStockControlLog.setOperationType("是否转仓");
                    sgCChannelStockControlLogItem.setIstrans(saveInfoRequest.getIstrans());
                } else if (StringUtils.isNotEmpty(saveInfoRequest.getSaStoreType()) && saveInfoRequest.getQtySafety() != null) {
                    sgCChannelStockControlLog.setOperationType("修改商品类型");
                    sgCChannelStockControlLogItem.setSaStoreType(saveInfoRequest.getSaStoreType());
                } else if (saveInfoRequest.getQtySafety() != null && StringUtils.isEmpty(saveInfoRequest.getSaStoreType())) {
                    sgCChannelStockControlLog.setOperationType("设定安全库存");
                    sgCChannelStockControlLogItem.setQtySafety(saveInfoRequest.getQtySafety());
                }
                controlLogItemList.add(sgCChannelStockControlLogItem);
            }
        }
        sgCChannelStockControlLog.setModContent(JSONObject.toJSONString(controlLogItemList));

        SysLogEvent<SgCChannelStockControlLog> sysLogEvent = new SysLogEvent(sgCChannelStockControlLog);
        SpringContextHolder.publishEvent(sysLogEvent);
        log.info("end SpringContextHolder.publishEvent.log");

    }


    /**
     * @param request:
     * @param valueHolderV14:
     * @Description: 检查入参
     * @Author: hwy
     * @Date: 2021/8/25 14:30
     * @return: void
     **/
    private void checkParam(SgBChannelProductDevOpsSaveRequest request, ValueHolderV14 valueHolderV14) {
        if (request == null || CollectionUtils.isEmpty(request.getSaveInfoRequestList())) {
            if (log.isDebugEnabled()) {
                log.debug(" SgBChannelProductDevOpsSaveService.checkParam 参数为空");
            }
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("参数为空");
            return;
        }

        if (request.getUserId() == null) {
            if (log.isDebugEnabled()) {
                log.debug(" SgBChannelProductDevOpsSaveService.checkParam 操作人为空");
            }
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("操作人为空");
            return;
        }

        List<SgBChannelProductDevOpsSaveInfoRequest> saveInfoRequestList = request.getSaveInfoRequestList();
        for (SgBChannelProductDevOpsSaveInfoRequest saveInfoRequest : saveInfoRequestList) {
            if (saveInfoRequest.getId() == null) {
                if (log.isDebugEnabled()) {
                    log.debug(" SgBChannelProductDevOpsSaveService.checkParam id为空");
                }
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("id为空");
                return;
            }
            if (StringUtils.isEmpty(saveInfoRequest.getIslock()) && StringUtils.isEmpty(saveInfoRequest.getIstrans()) && saveInfoRequest.getQtySafety() == null) {
                if (log.isDebugEnabled()) {
                    log.debug(" SgBChannelProductDevOpsSaveService.checkParam id:{} 对应记录的待更新字段为空");
                }
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("id为空");
                return;
            }
        }
    }

    //@Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 updateBySaStoreType(SgBChannelProductDevOpsSaveRequest request) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        checkUpdateBySaStoreTypeParam(request, valueHolderV14);
        if (!valueHolderV14.isOK()) {
            return valueHolderV14;
        }
        final List<SgBChannelProductDevOpsSaveInfoRequest> params = request.getSaveInfoRequestList();
        final List<Long> ids = params.stream().map(SgBChannelProductDevOpsSaveInfoRequest::getId).collect(Collectors.toList());
        //触发库存同步
        List<SgChannelStorageOmsManualSynchRequest> requests = new ArrayList<>();
        params.stream().forEach(param -> {
            SgChannelStorageOmsManualSynchRequest requestItem = new SgChannelStorageOmsManualSynchRequest();
            requestItem.setOperate(SgChannelStorageOmsManualSynchRequest.QUERY_BY_CONDITION);
            requestItem.setFixedQty(param.getQtySafety());
            requestItem.setCpCShopId(param.getCpCShopId());
            requestItem.setSkuId(param.getSkuId());
            requestItem.setSourceno("平台店铺商品表触发库存同步" + param.getCpCShopId());

            requests.add(requestItem);
        });
        Long userId = request.getUserId();
        User user = null;
        if (ObjectUtils.isEmpty(userId)) {
            user = R3SystemUserResource.getSystemRootUser();
        } else {
            UserQueryCmd userQueryCmd = (UserQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), UserQueryCmd.class.getName(), "cp-ext", "1.0");
            user = userQueryCmd.getUsersById(userId);
        }

        //这里面有使用 spring 异步任务 事务没什么用
        productSaveFilter.syncStock(requests, user);
        //修改的值任取一个即可, 因为值都是一样
        SgBChannelProductDevOpsSaveService bean = ApplicationContextHandle.getBean(SgBChannelProductDevOpsSaveService.class);
        Integer count = bean.updateSaStoreTypeByIds(ids, params.get(0).getSaStoreType());
        if (count <= 0) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("商品活动类型更新失败");
            return valueHolderV14;
        }
        saveSgChannelStockControlLog(request, user);
        return valueHolderV14;
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer updateSaStoreTypeByIds(List<Long> ids, String saStoreType) {

        return sgBChannelProductMapper.updateSaStoreTypeByIds(ids, saStoreType);

    }

    public void checkUpdateBySaStoreTypeParam(SgBChannelProductDevOpsSaveRequest request, ValueHolderV14 valueHolderV14) {
        if (ObjectUtils.isEmpty(request.getUserId())) {
            log.debug("[SgBChannelProductDevOpsSaveService.checkDownloadParam.request.userId is not null]");
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("用户id不能为空");
            return;
        }

        if (CollectionUtils.isEmpty(request.getSaveInfoRequestList())) {
            log.debug("[SgBChannelProductDevOpsSaveService.checkDownloadParam.request.saveInfoRequestList is not null]");
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("请求参数list不能为空");
            return;
        }
        //同城购不能修改
        final List<SgBChannelProductDevOpsSaveInfoRequest> params = request.getSaveInfoRequestList();
        boolean flag = params.stream().anyMatch(param -> ObjectUtils.isEmpty(param.getQtySafety()) || ObjectUtils.isEmpty(param.getSkuId()) || ObjectUtils.isEmpty(param.getCpCShopId()));
        if (flag) {
            log.debug("[SgBChannelProductDevOpsSaveService.checkDownloadParam.request.qtySafety,skuId,skuId is not error]");
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("请求参数中安全库存和平台条码和店铺id不能为空");
            return;
        }
        final boolean condition = params.stream().anyMatch(req -> {
            if (StringUtils.isEmpty(req.getSaStoreType()) || SaStoreTypeEnum.SAME_CITY.getCode().equals(req.getSaStoreType())) {
                return true;
            }
            return false;
        });
        if (condition) {
            log.debug("[SgBChannelProductDevOpsSaveService.checkDownloadParam.request.getSaStoreType is not error]");
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("请求参数中产品活动类型不能为空并且修改类型不能为同城购");
            return;
        }

    }


    public ValueHolderV14<List<SgBChannelProductExcelVo>> downloadStorage(SgBChannelProductDevOpsQueryRequest request) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        checkDownloadParam(request, valueHolderV14);
        List<SgBChannelProductDevOpsQueryInfoResult> results = sgBChannelProductMapper.selectListByDownload(request);
        log.debug("SgBChannelProductDevOpsSaveService.downloadStorage download excel results.size = {}, query init position = {}", results.size(), request.getStartindex());
        if (CollectionUtils.isEmpty(results)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("query list is null");
            return valueHolderV14;
        }
        long start = System.currentTimeMillis();
        List<SgBChannelProductExcelVo> excelVos = new ArrayList<>();
        List<SgBChannelProductExcelVo> excelList = getExcelList(results);
        excelVos.addAll(excelList);
        if (CollectionUtils.isEmpty(excelVos)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("query result is null");
            return valueHolderV14;
        }
        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setData(excelVos);
        long end = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug("SgBChannelProductDevOpsSaveService.downloadStorage download excel exec time 执行时间 : {}, 导出结果集size : {}", (end - start), excelVos.size());
        }
        return valueHolderV14;
    }

    //获取导出的list 集合
    public List<SgBChannelProductExcelVo> getExcelList(List<SgBChannelProductDevOpsQueryInfoResult> results) {
        List<SgBChannelProductExcelVo> excelVos = new ArrayList<>();
        if (CollectionUtils.isEmpty(results)) {
            return excelVos;
        }
        // 获取店铺的渠道类型 channel
        Long shopId = results.get(0).getCpCShopId();
        List<CpShop> shopList = shopQueryCmd.queryShopByIds(Collections.singletonList(shopId));
        AssertUtils.isNotEmpty("店铺id[" + shopId + "]查询不到店铺信息,请检查", shopList);
        Integer shopChannel = shopList.get(0).getChannel();
        //批量查询配销仓的可用量, 店铺id 固定
        List<Long> psCSkuIds = results.stream().map(SgBChannelProductDevOpsQueryInfoResult::getPsCSkuId).distinct().collect(Collectors.toList());
        List<SgBSaStorageResult> saStorageResults = storageMapper.batchQueryStorage(shopId, psCSkuIds, adbSchema);
        //计算当前商品的库存是否是否已经在计算中
        final List<SgBStorageBufferDto> params = results.stream().map(result -> SgBStorageBufferDto.builder()
                .shopId(result.getCpCShopId())
                .skuId(result.getSkuId())
                .build()).collect(Collectors.toList());
        SgBChannelStorageBufferCmd storageBufferCmd = (SgBChannelStorageBufferCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                SgBChannelStorageBufferCmd.class.getName(), "sg", "1.0");
        List<SgBChannelStorageBuffer> bufferList = storageBufferCmd.queryByShopIdAndSkuId(params);
        results.forEach(res -> {
            if (CollectionUtils.isNotEmpty(bufferList)) {
                boolean flag = bufferList.stream().anyMatch(buffer -> buffer.getCpCShopId().equals(res.getCpCShopId()) && buffer.getSkuId().equals(res.getSkuId()));
                if (flag) {
                    res.setCalcStatus(CalcStatusEnum.CALCING.getCode());
                }
            }
            //查询条码比例,最大值  取值逻辑：当前时间+平台店铺+平台条码ID 在[单据状态]=已审核的【特殊条码比例同步策略】的中的MAX(比例)
            BigDecimal specialRadio = BigDecimal.ZERO;
            /*SgCChannelSkuStrategyRatioDTO ratioDTO = new SgCChannelSkuStrategyRatioDTO();
            ratioDTO.setSkuId(res.getSkuId());
            ratioDTO.setCpCShopId(res.getCpCShopId());
            SgCChannelSkuStrategyResult sgCChannelSkuStrategyResult = strategyMapper.querySkuStrategyRatioMax(ratioDTO);
            if (!ObjectUtils.isEmpty(sgCChannelSkuStrategyResult)) {
                specialRadio = sgCChannelSkuStrategyResult.getSaMaxRatio().compareTo(sgCChannelSkuStrategyResult.getSpMaxRatio()) >= 0 ? sgCChannelSkuStrategyResult.getSaMaxRatio() : sgCChannelSkuStrategyResult.getSpMaxRatio();
            } else {
                //查询改类型的配销仓的最大比例
                specialRadio = ratioStrategyMapper.queryShopStrategyRatioMax(res.getCpCShopId(), res.getSaStoreType());
            }*/
            res.setSpecialRadio(specialRadio);


            //设置配销仓数量
            if (CollectionUtils.isNotEmpty(saStorageResults)) {
                BigDecimal qty = saStorageResults.stream()
                        .filter(s -> s.getSaStoreType().equals(res.getSaStoreType()) && res.getPsCSkuId().equals(s.getPsCSkuId()))
                        .map(SgBSaStorageResult::getQtyAvailable).reduce(BigDecimal.ZERO, BigDecimal::add);
                res.setQtyReal(qty);
                if (Objects.nonNull(shopChannel)) {
                    //设置 渠道内（店铺所属渠道 = 配销仓所属渠道）配销仓库存    和  渠道外（店铺所属渠道 != 配销仓所属渠道）配销仓库存
                    BigDecimal saQtyInChannel = saStorageResults.stream()
                            .filter(s -> s.getSaStoreType().equals(res.getSaStoreType())
                                    && res.getPsCSkuId().equals(s.getPsCSkuId())
                                    && shopChannel.equals(s.getSaChannel()))
                            .map(SgBSaStorageResult::getQtyAvailable).reduce(BigDecimal.ZERO, BigDecimal::add);
                    res.setSaQtyInChannel(saQtyInChannel);
                    res.setSaQtyOutChannel(res.getQtyReal().subtract(saQtyInChannel));
                } else {
                    res.setSaQtyOutChannel(qty);
                    res.setSaQtyInChannel(BigDecimal.ZERO);
                }
            }
            SgBChannelProductExcelVo excelVo = new SgBChannelProductExcelVo();
            BeanUtils.copyProperties(res, excelVo);
            excelVos.add(excelVo);
        });


        return excelVos;
    }


    private void checkDownloadParam(SgBChannelProductDevOpsQueryRequest request, ValueHolderV14 valueHolderV14) {
        if (ObjectUtils.isEmpty(request.getUserId())) {
            log.debug("[SgBChannelProductDevOpsSaveService.checkDownloadParam.request.userId is not null]");
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("用户id不能为空");
            return;
        }
        if (CollectionUtils.isEmpty(request.getCpCShopIds())) {
            log.debug("[SgBChannelProductDevOpsSaveService.checkDownloadParam.request.cpCShopIds is not null]");
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("店铺id不能为空");
            return;
        }
        List<String> brandLabels = request.getBrandLabels();
        if (CollectionUtils.isNotEmpty(brandLabels) && brandLabels.contains("3")) {
            request.setIsTmllCity("Y");
            if (brandLabels.size() == 1) {
                request.setBrandLabels(null);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 batchUpdateByList(List<SgBChannelProduct> updateList) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        if (CollectionUtils.isNotEmpty(updateList)) {

            sgBChannelProductMapper.batchUpdateByList(updateList);

        }
        return valueHolderV14;
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 batchUpdate(List<SgBChannelProduct> updateList) {

        if (CollectionUtils.isNotEmpty(updateList)) {
            updateList.stream().forEach(product -> {

                sgBChannelProductMapper.update(product, Wrappers.<SgBChannelProduct>lambdaUpdate()
                        .set(SgBChannelProduct::getQtyDifferences, product.getQtyDifferences())
                        .set(SgBChannelProduct::getQtyStorage, product.getQtyStorage())
                        .set(SgBChannelProduct::getTransTime, product.getTransTime())
                        .eq(SgBChannelProduct::getId, product.getId())
                        .eq(SgBChannelProduct::getSkuId, product.getSkuId()));

            });

        }
        return null;
    }

    /**
     * 下载平台库存
     *
     * @param downloadDto
     * @return
     */
    public ValueHolderV14 downloadPlatformInventory(SgBChannelProductDownloadDto downloadDto) {

        log.info("SgChannelProductCommonCmdImpl downloadPlatformInventory product in param downloadDto = {}", JSONObject.toJSONString(downloadDto));
        ValueHolderV14<Object> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        if (ObjectUtils.isEmpty(downloadDto) || ObjectUtils.isEmpty(downloadDto.getShopId())
                || ObjectUtils.isEmpty(downloadDto.getItemNums())) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("手动下载平台库存 参数 不能为空");
            return holderV14;
        }
        if (ObjectUtils.isEmpty(downloadDto.getUserId())) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("手动下载平台库存 用户未登录");
            return holderV14;
        }
        //新加下载平台库存逻辑
        ValueHolderV14<List<SgBChannelProduct>> valueHolderV14 = sgChannelProductQueryService.selectByShopIdAndNumiid(downloadDto.getShopId(), downloadDto.getItemNums());
        List<SgBChannelProduct> oldProducts = valueHolderV14.getData();
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(oldProducts)) {
            if (log.isDebugEnabled()) {
                log.debug("SgChannelProductCommonCmdImpl downloadPlatformInventory product or inventory is null");
            }
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("该店铺商品不存在");
            return holderV14;
        }
        List<SgBChannelProduct> oldFiltratProducts = new ArrayList<>();
        //进行按钮时间控制
        if (expirationTimeFlag) {
            LocalDateTime localDateTime = LocalDateTime.now();
            DateTimeFormatter sdFormat = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            String afterDateTime = sdFormat.format(localDateTime.plusSeconds(expirationTime));

            //设置key1 sg:{updratio}:
            String redisKey1 = SgConstants.REDIS_KEY_CHANNEL_SG.concat(SgConstants.SG_CONNECTOR_MARKS_1)
                    .concat(REDIS_KEY_CHANNEL_DOWNPLAT).concat(SgConstants.SG_CONNECTOR_MARKS_2).concat(SgConstants.SG_CONNECTOR_MARKS_4)
                    .concat(REDIS_KEY_CHANNEL_DOWN_STOCK).concat(downloadDto.getShopId().toString());
            String redisKey2 = SgConstants.REDIS_KEY_CHANNEL_SG.concat(SgConstants.SG_CONNECTOR_MARKS_1)
                    .concat(REDIS_KEY_CHANNEL_DOWNPLAT).concat(SgConstants.SG_CONNECTOR_MARKS_2).concat(SgConstants.SG_CONNECTOR_MARKS_4)
                    .concat(SgConstants.REDIS_KEY_CHANNEL_TEMP_KEY).concat(downloadDto.getShopId().toString());
            String redisKey3 = SgConstants.REDIS_KEY_CHANNEL_SG.concat(SgConstants.SG_CONNECTOR_MARKS_1)
                    .concat(REDIS_KEY_CHANNEL_DOWNPLAT).concat(SgConstants.SG_CONNECTOR_MARKS_2).concat(SgConstants.SG_CONNECTOR_MARKS_4)
                    .concat(SgConstants.REDIS_KEY_CHANNEL_TEMP_RES).concat(downloadDto.getShopId().toString());
            List<String> kesys = new ArrayList<>();
            kesys.add(redisKey1);
            kesys.add(redisKey2);
            kesys.add(redisKey3);

            //500一组校验时间控制
            int size = oldProducts.size() / 500 + 1;
            List<List<SgBChannelProduct>> oldProductPatition = Lists.partition(oldProducts, size);

            for (List<SgBChannelProduct> currentList : oldProductPatition) {

                Object[] redisRequest = new Object[currentList.size() * 2];

                for (int i = 0; i < currentList.size(); i++) {
                    redisRequest[i * 2 + 1] = currentList.get(i).getId().toString();
                    redisRequest[i * 2] = afterDateTime;
                }

                CusRedisTemplate<String, String> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();
                DefaultRedisScript<List> redisScript = new DefaultRedisScript<>();
                redisScript.setLocation(new ClassPathResource("lua/StockStorageLockRedis.lua"));
                redisScript.setResultType(List.class);
                log.info("SgCChannelSkuStrategyService.checkSkuStorage.redisrequest.param:{}", JSONObject.toJSONString(redisRequest));
                List result = redisMasterTemplate.execute(redisScript, kesys, redisRequest);
                log.info("SgCChannelSkuStrategyService.checkSkuStorage.redisrresult.param:{}", JSONObject.toJSONString(result));
                if (!result.get(0).equals("0")) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setData(null);
                    valueHolderV14.setMessage("下载平台库存按钮控制处理失败");
                    return valueHolderV14;
                }
                if (result.size() < 2) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setData(null);
                    valueHolderV14.setMessage("当前所选择的数据请在" + expirationTime + "s之后再进行修改！");
                    return valueHolderV14;
                }
                List<String> filterResultString = (ArrayList) result.get(1);

                if (CollectionUtils.isEmpty(filterResultString)) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("当前所选择的数据请在" + expirationTime + "s之后再进行修改！");
                    return valueHolderV14;
                }
                List<Long> filterResult = filterResultString.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                currentList.forEach(o -> {
                    if (filterResult.contains(o.getId())) {
                        oldFiltratProducts.add(o);
                    }
                });
            }

        } else {
            oldFiltratProducts.addAll(oldProducts);
        }


        List<SgBChannelProduct> newProducts = productDownloadService.externalDownloadProducts(oldFiltratProducts);
        if (log.isDebugEnabled()) {
            log.debug("SgChannelProductCommonCmdImpl.downloadPlatformInventory 查询平台商品 results : {}", JSONObject.toJSONString(newProducts));
        }
        if (CollectionUtils.isEmpty(newProducts)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("获取平台商品失败, 失败原因: 平台商品不存在");
            return holderV14;
        }
        Map<String, SgBChannelProduct> oldMap = oldProducts.stream().collect(Collectors.toMap(SgBChannelProduct::getSkuId, Function.identity()));
        List<SgBChannelProduct> params = new ArrayList<>();
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(newProducts)) {
            newProducts.stream().forEach(product -> {
                SgBChannelProduct channelProduct = oldMap.get(product.getSkuId());
                if (ObjectUtils.isNotEmpty(channelProduct)) {
                    channelProduct.setQtyStorage(product.getQtyStorage());
                    BigDecimal qtyChannel = ObjectUtils.isEmpty(channelProduct.getQtyChannel()) ? BigDecimal.ZERO : channelProduct.getQtyChannel();
                    channelProduct.setQtyDifferences(qtyChannel.subtract(product.getQtyStorage()));
                    channelProduct.setTransTime(new Date());

                    params.add(channelProduct);
                }
            });
        }
        //批量更新
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(params)) {
            //排序
            Collections.sort(params, Comparator.comparingLong(SgBChannelProduct::getId));
            //批量更新
            SgBChannelProductDevOpsSaveService service = ApplicationContextHandle.getBean(SgBChannelProductDevOpsSaveService.class);

            int page = params.size() / batchUpdateSize;
            if (params.size() % batchUpdateSize != 0) {

                ++page;
            }
            for (int i = 0; i < page; i++) {

                List<SgBChannelProduct> products = params.subList(i * batchUpdateSize,
                        (((i + 1) * batchUpdateSize > params.size() ? params.size() : batchUpdateSize * (i + 1))));

                service.batchUpdateByList(products);
            }
            //service.batchUpdate(params);
        }

        log.info("Start SpringContextHolder.publishEvent.log");
        UserQueryCmd userQueryCmd = (UserQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), UserQueryCmd.class.getName(), "cp-ext", "1.0");
        User user = userQueryCmd.getUsersById(downloadDto.getUserId());

        SgCChannelStockControlLog sgCChannelStockControlLog = new SgCChannelStockControlLog();
        sgCChannelStockControlLog.setOperationType("手动下载平台库存");
        StorageUtils.setBModelDefalutData(sgCChannelStockControlLog, user);
        sgCChannelStockControlLog.setModContent("\"shopId\":" + downloadDto.getShopId() + ",\"ItemNums\":" + JSONObject.toJSONString(downloadDto.getItemNums()));
        SysLogEvent<SgCChannelStockControlLog> sysLogEvent = new SysLogEvent(sgCChannelStockControlLog);
        SpringContextHolder.publishEvent(sysLogEvent);
        log.info("end SpringContextHolder.publishEvent.log");

        return holderV14;
    }

    @TargetDataSource(name = "adb")
    public ValueHolderV14<String> downloadStorageNew(SgBChannelProductDevOpsQueryRequest request) {
        log.debug("start SgBChannelProductDevOpsSaveService downloadStorageNew 开始导出 request = {}", JSONObject.toJSONString(request));
        long start = System.currentTimeMillis();
        ValueHolderV14 valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        String fileName = "";
        try {
            checkDownloadParam(request, valueHolderV14);
            if (!valueHolderV14.isOK()) {
                log.debug("SgBChannelProductDevOpsSaveService downloadStorageNew params fail valueHolderV14 = {}", JSONObject.toJSONString(valueHolderV14));
                return valueHolderV14;
            }
            Date date = new Date();
            String dateFormat = DateUtil.format(date, "yyyyMMddHHmmssSSS");
            fileName = "v_adb_sg_b_channel_product_" + dateFormat + ".csv";
            request.setFileName(fileName);
            sgBChannelProductMapper.downloadStorageNew(request, adbSchema);

        } catch (Exception e) {
            log.error("SgBChannelProductDevOpsSaveService downloadStorageNew 大页面导出报错: cause={}",
                    Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("导出 excel 报错");
            return valueHolderV14;
        }

        String url = downloadUrl + fileName;
        valueHolderV14.setData(url);

        log.debug("SgBChannelProductDevOpsSaveService downloadStorageNew 执行时间: time = {} , valueHolderV14 = {}", System.currentTimeMillis() - start, JSONObject.toJSONString(valueHolderV14));
        return valueHolderV14;
    }


}
