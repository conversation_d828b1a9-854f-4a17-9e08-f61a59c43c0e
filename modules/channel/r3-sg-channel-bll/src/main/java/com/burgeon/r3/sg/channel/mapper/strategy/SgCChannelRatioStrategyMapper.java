package com.burgeon.r3.sg.channel.mapper.strategy;

import com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelRatioStrategyQueryInfoResult;
import com.burgeon.r3.sg.core.model.table.channel.ratiostrategy.SgCChannelRatioStrategy;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface SgCChannelRatioStrategyMapper extends ExtentionMapper<SgCChannelRatioStrategy> {

    /**
     * @param cpCShopId:
     * @Description: 查询店铺同比比例设置信息
     * 艳吉曰：占单，拆单，查询比例设置等于0的也要查查找出来，重新写一个的原因是不知道上一个能不能改，
     * 保险重新写一个
     * @Author: hwy
     * @Date: 2021/6/19 17:33
     * @return: void
     **/
    @Select({
            "<script>",
            " SELECT ",
            " MAIN.ID, ",
            " MAIN.CP_C_SHOP_ID, ",
            " MAIN.SG_C_SHARE_POOL_ID, ",
            " MAIN.SG_C_SHARE_POOL_RATIO, ",
            " ITEM.SG_C_CHANNEL_RATIO_STRATEGY_ID, ",
            " ITEM.SG_C_SA_STORE_ID,",
            " ITEM.SG_C_SA_STORE_ECODE,",
            " ITEM.SG_C_SA_STORE_ENAME,",
            " ITEM.RATIO, ",
            " COALESCE( ITEM.SG_C_SA_STORE_ORDERNO,99) AS ORDERNO, ",
            " COALESCE(ITEM.SG_C_SA_STORE_TYPE,1) AS TYPE ",
            " FROM SG_C_CHANNEL_RATIO_STRATEGY MAIN ",
            " LEFT JOIN SG_C_CHANNEL_RATIO_STRATEGY_ITEM ITEM ",
            " ON (MAIN.ID = ITEM.SG_C_CHANNEL_RATIO_STRATEGY_ID AND ITEM.RATIO >= 0) ",
            " WHERE MAIN.ISACTIVE ='Y' AND ITEM.ISACTIVE ='Y' ",
            " <when test='cpCShopId != null'> ",
            " AND MAIN.CP_C_SHOP_ID = #{cpCShopId, jdbcType=INTEGER} ",
            " </when > ",
            "</script>"
    })
    List<SgCChannelRatioStrategyQueryInfoResult> queryRatioStrategyInfoByShopId3(@Param("cpCShopId") Long cpCShopId);

    /**
     * @param cpCShopId:
     * @param flag:      Y 代表开
     * @Description: 查询店铺同比比例设置信息
     * @Author: hwy
     * @Date: 2021/6/19 17:33
     * @return: void
     **/
    @Select({
            "<script>",
            " SELECT ",
            " MAIN.ID, ",
            " MAIN.CP_C_SHOP_ID, ",
            " MAIN.SG_C_SHARE_POOL_ID, ",
            " MAIN.SG_C_SHARE_POOL_RATIO, ",
            " ITEM.SG_C_CHANNEL_RATIO_STRATEGY_ID, ",
            " ITEM.SG_C_SA_STORE_ID,",
            " ITEM.SG_C_SA_STORE_ECODE,",
            " ITEM.SG_C_SA_STORE_ENAME,",
            " ITEM.RATIO, ",
            " COALESCE( ITEM.SG_C_SA_STORE_ORDERNO,99) AS ORDERNO, ",
            " COALESCE(ITEM.SG_C_SA_STORE_TYPE,1) AS TYPE ",
            " FROM SG_C_CHANNEL_RATIO_STRATEGY MAIN ",
            " LEFT JOIN SG_C_CHANNEL_RATIO_STRATEGY_ITEM ITEM ",
            " ON MAIN.ID = ITEM.SG_C_CHANNEL_RATIO_STRATEGY_ID ",
            " WHERE MAIN.ISACTIVE ='Y' AND ITEM.ISACTIVE ='Y' ",
            " <when test='cpCShopId != null'> ",
            " AND MAIN.CP_C_SHOP_ID = #{cpCShopId, jdbcType=INTEGER} ",
            " </when > ",
            " <when test='flag != null and flag == \"Y\".toString()'> ",
            " AND ITEM.RATIO &gt; 0 ",
            " </when > ",
            "</script>"
    })
    List<SgCChannelRatioStrategyQueryInfoResult> queryRatioStrategyInfoByShopIdAndFlag(@Param("cpCShopId") Long cpCShopId, @Param("flag") String flag);


    /**
     * 寻源查询比例同步策略：20220725 关联查询配销仓档案信息
     * @param cpCShopId:
     * @param flag:      Y 代表开
     * @Description: 查询店铺同比比例设置信息
     * @Author: hwy
     * @Date: 2021/6/19 17:33
     * @return: void
     **/
    @Select({
            "<script>",
            " SELECT ",
            " MAIN.ID, ",
            " MAIN.CP_C_SHOP_ID, ",
            " MAIN.SG_C_SHARE_POOL_ID, ",
            " MAIN.SG_C_SHARE_POOL_RATIO, ",
            " ITEM.SG_C_CHANNEL_RATIO_STRATEGY_ID, ",
            " ITEM.SG_C_SA_STORE_ID,",
            " ITEM.SG_C_SA_STORE_ECODE,",
            " ITEM.SG_C_SA_STORE_ENAME,",
            " C.SG_C_SHARE_STORE_ID, ",
            " C.CATEGORY, ",
            " ITEM.RATIO, ",
            " COALESCE( ITEM.SG_C_SA_STORE_ORDERNO,99) AS ORDERNO, ",
            " COALESCE(ITEM.SG_C_SA_STORE_TYPE,1) AS TYPE ",
            " FROM SG_C_CHANNEL_RATIO_STRATEGY MAIN ",
            " LEFT JOIN SG_C_CHANNEL_RATIO_STRATEGY_ITEM ITEM ",
            " ON MAIN.ID = ITEM.SG_C_CHANNEL_RATIO_STRATEGY_ID ",
            " LEFT JOIN SG_C_SA_STORE C ON ITEM.SG_C_SA_STORE_ID = C.ID  ",
            " WHERE MAIN.ISACTIVE ='Y' AND ITEM.ISACTIVE ='Y' ",
            " <when test='cpCShopId != null'> ",
            " AND MAIN.CP_C_SHOP_ID = #{cpCShopId, jdbcType=INTEGER} ",
            " </when > ",
            " <when test='flag != null and flag == \"Y\".toString()'> ",
            " AND ITEM.RATIO &gt; 0 ",
            " </when > order by ITEM.sg_c_sa_store_orderno ",
            "</script>"
    })
    List<SgCChannelRatioStrategyQueryInfoResult> queryRatioStrategyByFindSource(@Param("cpCShopId") Long cpCShopId, @Param("flag") String flag);

    /**
     * 根据二级组织编码查询比例同步策略
     *
     * @param distCodes 二级分货组织编码
     * @return
     */
    @Select({
            "<script>",
            " SELECT ",
            " MAIN.ID, ",
            " MAIN.DIST_CODE_LEVEL_TWO, ",
            " MAIN.SG_C_SHARE_POOL_ID, ",
            " MAIN.SG_C_SHARE_POOL_RATIO, ",
            " ITEM.SG_C_CHANNEL_RATIO_STRATEGY_ID, ",
            " ITEM.SG_C_SA_STORE_ID,",
            " ITEM.SG_C_SA_STORE_ECODE,",
            " ITEM.SG_C_SA_STORE_ENAME,",
            " C.SG_C_SHARE_STORE_ID, ",
            " C.CATEGORY, ",
            " ITEM.RATIO, ",
            " COALESCE( ITEM.SG_C_SA_STORE_ORDERNO,99) AS ORDERNO, ",
            " COALESCE(ITEM.SG_C_SA_STORE_TYPE,1) AS TYPE ",
            " FROM SG_C_CHANNEL_RATIO_STRATEGY MAIN ",
            " LEFT JOIN SG_C_CHANNEL_RATIO_STRATEGY_ITEM ITEM ",
            " ON MAIN.ID = ITEM.SG_C_CHANNEL_RATIO_STRATEGY_ID ",
            " LEFT JOIN SG_C_SA_STORE C ON ITEM.SG_C_SA_STORE_ID = C.ID  ",
            " WHERE MAIN.ISACTIVE ='Y' AND ITEM.ISACTIVE ='Y' ",
            " <when test='distCodes != null and distCodes.size>0 ' >  ",
            " AND MAIN.DIST_CODE_LEVEL_TWO IN",
            " <foreach item='item' collection='distCodes' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when>  order by ITEM.sg_c_sa_store_orderno desc ",
            "</script>"
    })
    List<SgCChannelRatioStrategyQueryInfoResult> queryRatioStrategyByDists(@Param("distCodes") List<String> distCodes);

    /**
     * @param cpCShopId:
     * @Description: 查询店铺同比比例设置信息
     * @Author: hwy
     * @Date: 2021/6/19 17:33
     * @return: void
     **/
    @Select({
            "<script>",
            " SELECT ",
            " MAIN.ID, ",
            " MAIN.CP_C_SHOP_ID, ",
            " MAIN.SG_C_SHARE_POOL_ID, ",
            " MAIN.SG_C_SHARE_POOL_RATIO, ",
            " ITEM.SG_C_CHANNEL_RATIO_STRATEGY_ID, ",
            " ITEM.SG_C_SA_STORE_ID,",
            " ITEM.SG_C_SA_STORE_ECODE,",
            " ITEM.SG_C_SA_STORE_ENAME,",
            " ITEM.RATIO, ",
            " COALESCE( ITEM.SG_C_SA_STORE_ORDERNO,99) AS ORDERNO, ",
            " COALESCE(ITEM.SG_C_SA_STORE_TYPE,1) AS TYPE ",
            " FROM SG_C_CHANNEL_RATIO_STRATEGY MAIN ",
            " LEFT JOIN SG_C_CHANNEL_RATIO_STRATEGY_ITEM ITEM ",
            " ON (MAIN.ID = ITEM.SG_C_CHANNEL_RATIO_STRATEGY_ID AND ITEM.ISACTIVE ='Y' AND ITEM.RATIO &gt; 0) ",
            " WHERE MAIN.ISACTIVE ='Y' ",
            " <when test='cpCShopId != null'> ",
            " AND MAIN.CP_C_SHOP_ID = #{cpCShopId, jdbcType=INTEGER} ",
            " </when > ",
            "</script>"
    })
    List<SgCChannelRatioStrategyQueryInfoResult> queryRatioStrategyInfoByShopId2(@Param("cpCShopId") Long cpCShopId);


    /**
     * @param saStoreIds:
     * @Description: 查询店铺同比比例设置信息
     * @Author: hwy
     * @Date: 2021/6/19 17:33
     * @return: void
     **/
    @Select({
            "<script>",
            " SELECT ",
            " MAIN.CP_C_SHOP_ID,",
            " MAIN.SG_C_SHARE_POOL_ID,",
            " MAIN.SG_C_SHARE_POOL_RATIO, ",
            " ITEM.SG_C_CHANNEL_RATIO_STRATEGY_ID,",
            " ITEM.SG_C_SA_STORE_ID,",
            " ITEM.RATIO ",
            " FROM SG_C_CHANNEL_RATIO_STRATEGY MAIN ",
            " LEFT JOIN SG_C_CHANNEL_RATIO_STRATEGY_ITEM ITEM ON MAIN.ID = ITEM.SG_C_CHANNEL_RATIO_STRATEGY_ID ",
            " WHERE MAIN.ISACTIVE = 'Y' AND ITEM.ISACTIVE ='Y' ",
            " <when test='saStoreIds != null and saStoreIds.size>0 ' >  ",
            " AND ITEM.SG_C_SA_STORE_ID IN",
            " <foreach item='item' collection='saStoreIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            "</script>"
    })
    List<SgCChannelRatioStrategyQueryInfoResult> queryRatioStrategyInfoBySaStoreIds(@Param("saStoreIds") List<Long> saStoreIds);

    /**
     * @param shareStoreIds:
     * @Description: 查询店铺同比比例设置信息
     * @Author: hwy
     * @Date: 2021/6/19 17:33
     * @return: void
     **/
    @Select({
            "<script>",
            " SELECT ",
            " MAIN.CP_C_SHOP_ID,",
            " MAIN.SG_C_SHARE_POOL_ID,",
            " MAIN.SG_C_SHARE_POOL_RATIO, ",
            " ITEM.RATIO, ",
            " ITEM.ORDERNO, ",
            " ITEM.SG_C_SHARE_STORE_ID ",
            " FROM SG_C_CHANNEL_RATIO_STRATEGY MAIN ",
            " LEFT JOIN SG_C_SHARE_POOL_ITEM ITEM ON MAIN.SG_C_SHARE_POOL_ID = ITEM.SG_C_SHARE_POOL_ID ",
            " WHERE MAIN.ISACTIVE = 'Y' AND ITEM.ISACTIVE ='Y' ",
            " <when test='shareStoreIds != null and shareStoreIds.size>0 ' >  ",
            " AND ITEM.SG_C_SHARE_STORE_ID IN",
            " <foreach item='item' collection='shareStoreIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            "</script>"
    })
    List<SgCChannelRatioStrategyQueryInfoResult> queryRatioStrategyShareInfoByShareStoreId(@Param("shareStoreIds") List<Long> shareStoreIds);

    /**
     * @param cpCShopIds:
     * @Description: 查询店铺同比比例设置信息
     * @Author: hwy
     * @Date: 2021/6/19 17:33
     * @return: void
     **/
    @Select({
            "<script>",
            " SELECT ",
            " MAIN.CP_C_SHOP_ID,",
            " MAIN.SG_C_SHARE_POOL_ID,",
            " MAIN.SG_C_SHARE_POOL_ECODE,",
            " MAIN.SG_C_SHARE_POOL_ENAME,",
            " MAIN.SG_C_SHARE_POOL_RATIO, ",
            " ITEM.RATIO, ",
            " ITEM.ORDERNO, ",
            " ITEM.SG_C_SHARE_STORE_ID ",
            " FROM SG_C_CHANNEL_RATIO_STRATEGY MAIN ",
            " LEFT JOIN SG_C_SHARE_POOL_ITEM ITEM ON MAIN.SG_C_SHARE_POOL_ID = ITEM.SG_C_SHARE_POOL_ID ",
            " WHERE MAIN.ISACTIVE = 'Y' AND ITEM.ISACTIVE ='Y' ",
            " <when test='cpCShopIds != null and cpCShopIds.size>0 ' >  ",
            " AND MAIN.CP_C_SHOP_ID IN",
            " <foreach item='item' collection='cpCShopIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            "</script>"
    })
    List<SgCChannelRatioStrategyQueryInfoResult> queryRatioStrategyShareInfoByShopIds(@Param("cpCShopIds") List<Long> cpCShopIds);


    /**
     * @param cpCShopIds:
     * @Description: 查询店铺同比比例设置信息
     * 艳吉曰：占单，拆单，查询比例设置等于0的也要查查找出来，重新写一个的原因是不知道上一个能不能改，
     * 保险重新写一个
     * @Author: hwy
     * @Date: 2021/6/19 17:33
     * @return: void
     **/
    @Select({
            "<script>",
            " SELECT ",
            " MAIN.ID, ",
            " MAIN.CP_C_SHOP_ID, ",
            " MAIN.SG_C_SHARE_POOL_ID, ",
            " MAIN.SG_C_SHARE_POOL_RATIO, ",
            " ITEM.SG_C_CHANNEL_RATIO_STRATEGY_ID, ",
            " ITEM.SG_C_SA_STORE_ID,",
            " ITEM.SG_C_SA_STORE_ECODE,",
            " ITEM.SG_C_SA_STORE_ENAME,",
            " ITEM.ISACTIVE,",
            " ITEM.RATIO, ",
            " COALESCE( ITEM.SG_C_SA_STORE_ORDERNO,99) AS ORDERNO, ",
            " COALESCE(ITEM.SG_C_SA_STORE_TYPE,1) AS TYPE ",
            " FROM SG_C_CHANNEL_RATIO_STRATEGY MAIN ",
            " LEFT JOIN SG_C_CHANNEL_RATIO_STRATEGY_ITEM ITEM ",
            " ON (MAIN.ID = ITEM.SG_C_CHANNEL_RATIO_STRATEGY_ID AND ITEM.RATIO >= 0) ",
            " WHERE MAIN.ISACTIVE ='Y' AND ITEM.ISACTIVE ='Y' ",
            " <when test='cpCShopIds != null and cpCShopIds.size>0 ' >  ",
            " AND MAIN.CP_C_SHOP_ID IN",
            " <foreach item='item' collection='cpCShopIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            "</script>"
    })
    List<SgCChannelRatioStrategyQueryInfoResult> queryRatioStrategyInfoByShopIds2(@Param("cpCShopIds") List<Long> cpCShopIds);

    @Update("<script>" +
            "UPDATE sg_c_channel_ratio_strategy " +
            " SET isactive = 'N',modifiername=#{modifiername},modifierename=#{modifierename},modifieddate=NOW() " +
            " WHERE isactive='Y' " +
            "</script>")
    int voidAllStrategy(@Param("modifierid") Integer modifierid, @Param("modifiername") String modifiername, @Param("modifierename") String modifierename);
}