package com.burgeon.r3.sg.channel.services.sale;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.sale.SgBChannelAdvanceSaleItemMapper;
import com.burgeon.r3.sg.channel.mapper.sale.SgBChannelAdvanceSaleMapper;
import com.burgeon.r3.sg.channel.model.request.sale.SgBChannelAdvanceSaleDeleteRequest;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.channel.sale.SgBChannelAdvanceSale;
import com.burgeon.r3.sg.core.model.table.channel.sale.SgBChannelAdvanceSaleItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/21 19:10
 */
@Slf4j
@Component
public class SgBChannelAdvanceSaleDeleteService {
    @Autowired
    SgBChannelAdvanceSaleMapper mapper;
    @Autowired
    SgBChannelAdvanceSaleItemMapper itemMapper;

    /**
     * 渠道预售活动删除页面
     *
     * @param session 入参
     * @return 出参
     */
    ValueHolder delete(QuerySession session) {
        SgBChannelAdvanceSaleDeleteRequest request = R3ParamUtils.parseSaveObject(session, SgBChannelAdvanceSaleDeleteRequest.class);
        request.setR3(true);
        SgBChannelAdvanceSaleDeleteService service = ApplicationContextHandle.getBean(SgBChannelAdvanceSaleDeleteService.class);
        return R3ParamUtils.convertV14WithResult(service.delete(request));
    }

    /**
     * 渠道预售活动删除
     *
     * @param request 入参
     * @return 出参
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> delete(SgBChannelAdvanceSaleDeleteRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelAdvanceSaleDeleteService.delete:param={}", JSONObject.toJSONString(request));
        }
        String lockKey = SgConstants.SG_B_CHANNEL_ADVANCE_SALE + ":" + request.getObjId();
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "删除成功！");
        SgRedisLockUtils.lock(lockKey);
        try {
            SgBChannelAdvanceSale stoChannelAdvanceSale = checkParams(request);
            //明细删除
            if (CollectionUtils.isNotEmpty(request.getItemIds())) {

                BigDecimal totQty = stoChannelAdvanceSale.getTotQty();
                BigDecimal totQtyRemain = stoChannelAdvanceSale.getTotQtyRemain();
                BigDecimal totQtySold = stoChannelAdvanceSale.getTotQtySold();
                BigDecimal totQtySend = stoChannelAdvanceSale.getTotQtyOut();
                Integer totRowNum = stoChannelAdvanceSale.getTotRowNum();
                BigDecimal totAmt = stoChannelAdvanceSale.getTotAmtList();
                BigDecimal totAmtListRemain = stoChannelAdvanceSale.getTotAmtListRemain();
                BigDecimal totAmtListSold = stoChannelAdvanceSale.getTotAmtListSold();
                BigDecimal totAmtLsitSned = stoChannelAdvanceSale.getTotAmtLsitOut();

                List<SgBChannelAdvanceSaleItem> itemList = itemMapper.selectList(new QueryWrapper<SgBChannelAdvanceSaleItem>()
                        .lambda().in(SgBChannelAdvanceSaleItem::getId, request.getItemIds()));
                for (SgBChannelAdvanceSaleItem item : itemList) {
                    if (item.getQty() != null) {
                        totQty = totQty.subtract(item.getQty());
                        totAmt = totAmt.subtract(item.getAmtList());
                    }
                    if (item.getQtyRemain() != null) {
                        totQtyRemain = totQtyRemain.subtract(item.getQtyRemain());
                        totAmtListRemain = totAmtListRemain.subtract(item.getAmtListRemain());
                    }
                    if (item.getQtySold() != null) {
                        totQtySold = totQtySold.subtract(item.getQtySold());
                        totAmtListSold = totAmtListSold.subtract(item.getAmtListSold());
                    }
                    if (item.getQtyOut() != null) {
                        totQtySend = totQtySend.subtract(item.getQtyOut());
                        totAmtLsitSned = totAmtLsitSned.subtract(item.getAmtListOut());
                    }
                }

                itemMapper.delete(new QueryWrapper<SgBChannelAdvanceSaleItem>()
                        .lambda().in(SgBChannelAdvanceSaleItem::getId, request.getItemIds()));

                totRowNum = totRowNum - request.getItemIds().size();
                StorageUtils.setBModelDefalutDataByUpdate(stoChannelAdvanceSale, request.getLoginUser());
                stoChannelAdvanceSale.setTotRowNum(totRowNum);
                stoChannelAdvanceSale.setTotAmtList(totAmt);
                stoChannelAdvanceSale.setTotQty(totQty);
                stoChannelAdvanceSale.setTotAmtListRemain(totAmtListRemain);
                stoChannelAdvanceSale.setTotAmtListSold(totAmtListSold);
                stoChannelAdvanceSale.setTotQtyRemain(totQtyRemain);
                stoChannelAdvanceSale.setTotQtySold(totQtySold);
                stoChannelAdvanceSale.setTotQtyOut(totQtySend);
                stoChannelAdvanceSale.setTotAmtLsitOut(totAmtLsitSned);
                mapper.updateById(stoChannelAdvanceSale);

            } else {

                itemMapper.delete(new QueryWrapper<SgBChannelAdvanceSaleItem>()
                        .lambda().eq(SgBChannelAdvanceSaleItem::getSgBChannelAdvanceSaleId, request.getObjId()));
                mapper.delete(new QueryWrapper<SgBChannelAdvanceSale>()
                        .lambda().eq(SgBChannelAdvanceSale::getId, request.getObjId()));

            }
        } catch (Exception e) {
            AssertUtils.logAndThrowException(e.getMessage(), e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKey, log, this.getClass().getName());

        }
        return v14;
    }

    /**
     * 参数校验
     *
     * @param request 入参
     * @return 出参
     */
    public SgBChannelAdvanceSale checkParams(SgR3BaseRequest request) {
        SgBChannelAdvanceSale stoChannelAdvanceSale = mapper.selectById(request.getObjId());
        if (SgConstants.IS_ACTIVE_N.equalsIgnoreCase(stoChannelAdvanceSale.getIsactive())) {
            AssertUtils.logAndThrow("当前记录已作废,不允许删除！", request.getLoginUser().getLocale());

        } else if (SgChannelConstants.BILL_STATUS_UNSUBMIT != stoChannelAdvanceSale.getStatus()) {
            AssertUtils.logAndThrow("当前单据状态不允许删除！", request.getLoginUser().getLocale());
        }
        return stoChannelAdvanceSale;
    }

}