package com.burgeon.r3.sg.channel.mapper.strategy;

import com.burgeon.r3.sg.core.model.table.channel.skustrategy.SgCChannelSkuStrategySaItem;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface SgCChannelSkuStrategySaItemMapper extends ExtentionMapper<SgCChannelSkuStrategySaItem> {

    /**
     * 根据平台条码和店铺id查询 特殊条码策略明细关联配销仓性质(配销仓档案字段映射到明细字段使用)
     * @param shopId 店铺id
     * @param nowDate 当前时间
     * @param skuIdList 平台条码id
     * @return List<SgCChannelSkuStrategySaItem>
     */
    @Select("<script>" +
            " SELECT " +
            "i.ps_c_sku_id, " +
            "sa.CATEGORY sa_store_type " +
            "FROM " +
            " sg_c_channel_sku_strategy_sa_item i  " +
            "LEFT JOIN sg_c_channel_sku_strategy m ON i.sg_c_channel_sku_strategy_id = m.id " +
            "LEFT JOIN sg_c_sa_store sa on i.sg_c_sa_store_id=sa.id " +
            "WHERE " +
            " m.cp_c_shop_id =#{shopId} and m.`status`=2 and m.end_time &gt;=#{nowDate} and i.PS_C_SKU_ID in " +
            " <foreach collection='skuIdList' item='skuId' open='(' separator=',' close=')'> #{skuId} </foreach> " +
            "and i.isactive='Y';"+
            "</script> ")
    List<SgCChannelSkuStrategySaItem> querySkuIdWithSaStoreCategory(@Param("shopId") Long shopId,
                                                                    @Param("nowDate") String nowDate,
                                                                    @Param("skuIdList") List<Long> skuIdList);
}