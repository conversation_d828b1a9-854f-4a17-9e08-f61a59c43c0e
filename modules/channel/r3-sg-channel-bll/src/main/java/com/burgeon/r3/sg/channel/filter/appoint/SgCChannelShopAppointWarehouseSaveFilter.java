package com.burgeon.r3.sg.channel.filter.appoint;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.channel.mapper.appoint.SgCChannelShopAppointWarehouseAppointItemMapper;
import com.burgeon.r3.sg.channel.mapper.appoint.SgCChannelShopAppointWarehouseExcludeItemMapper;
import com.burgeon.r3.sg.channel.mapper.appoint.SgCChannelShopAppointWarehouseMapper;
import com.burgeon.r3.sg.channel.model.dto.appoint.SgCChannelShopAppointWarehouseAppointItemDto;
import com.burgeon.r3.sg.channel.model.dto.appoint.SgCChannelShopAppointWarehouseDto;
import com.burgeon.r3.sg.channel.model.dto.appoint.SgCChannelShopAppointWarehouseExcludeItemDto;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseMultiItemsFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/6/8 14:53
 */
@Slf4j
@Component
public class SgCChannelShopAppointWarehouseSaveFilter extends BaseMultiItemsFilter<SgCChannelShopAppointWarehouseDto> {

    @Autowired
    private SgCChannelShopAppointWarehouseMapper mapper;
    @Autowired
    private SgCChannelShopAppointWarehouseAppointItemMapper appointItemMapper;
    @Autowired
    private SgCChannelShopAppointWarehouseExcludeItemMapper excludeItemMapper;

    @Override
    public String getFilterMsgName() {
        return "店铺指定实体仓设置保存";
    }

    @Override
    public Class getFilterClass() {
        return SgCChannelShopAppointWarehouseSaveFilter.class;
    }

    @Override
    public Class<?> getSubTableClass(String subTableName) {
        if (SgConstants.SG_C_CHANNEL_SHOP_APPOINT_WAREHOUSE_APPOINT_ITEM.toUpperCase().equalsIgnoreCase(subTableName)) {
            return SgCChannelShopAppointWarehouseAppointItemDto.class;
        } else if (SgConstants.SG_C_CHANNEL_SHOP_APPOINT_WAREHOUSE_EXCLUDE_ITEM.toUpperCase().equalsIgnoreCase(subTableName)) {
            return SgCChannelShopAppointWarehouseExcludeItemDto.class;
        }
        return null;
    }

    @Override
    public ValueHolderV14 execBeforeTable(SgCChannelShopAppointWarehouseDto mainObject, Map subObjectMap, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("SgCChannelShopAppointWarehouseSaveFilter.execBeforeTable mainObject:{}", JSONObject.toJSONString(mainObject));
        }

        if (mainObject.getCpCShopId() != null) {
            CpShop cpShop = CommonCacheValUtils.getShopInfo(mainObject.getCpCShopId());
            if (cpShop != null) {
                mainObject.setCpCShopTitle(cpShop.getCpCShopTitle());
                mainObject.setCpCShopEcode(cpShop.getEcode());
            } else {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("平台店铺已不存在！", loginUser.getLocale()));
            }
        }

        if (MapUtils.isNotEmpty(subObjectMap)) {
            Object appointItemObj = subObjectMap.get(SgConstants.SG_C_CHANNEL_SHOP_APPOINT_WAREHOUSE_APPOINT_ITEM.toUpperCase());
            Object excludeItemObj = subObjectMap.get(SgConstants.SG_C_CHANNEL_SHOP_APPOINT_WAREHOUSE_EXCLUDE_ITEM.toUpperCase());

            if (appointItemObj != null && excludeItemObj != null) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("不允许同时设置指定明细与排除明细",
                        loginUser.getLocale()));
            }

            // 设置指定明细
            if (appointItemObj != null) {
                if (log.isDebugEnabled()) {
                    log.debug("SgCChannelShopAppointWarehouseSaveValidator.validateTable appointItemObj:{}", JSONObject.toJSONString(appointItemObj));
                }

                List<SgCChannelShopAppointWarehouseAppointItemDto> appointItem = (List<SgCChannelShopAppointWarehouseAppointItemDto>) appointItemObj;
                if (CollectionUtils.isNotEmpty(appointItem)) {
                    return setAppointItem(appointItem, loginUser);

                }
            }
            // 设置排除明细
            if (excludeItemObj != null) {
                if (log.isDebugEnabled()) {
                    log.debug("SgCChannelShopAppointWarehouseSaveValidator.validateTable excludeItemObj:{}", JSONObject.toJSONString(excludeItemObj));
                }

                List<SgCChannelShopAppointWarehouseExcludeItemDto> excludeItem = (List<SgCChannelShopAppointWarehouseExcludeItemDto>) excludeItemObj;
                if (CollectionUtils.isNotEmpty(excludeItem)) {
                    return setExcludeItem(excludeItem, loginUser);
                }
            }
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("保存成功！"));
    }

    private ValueHolderV14 setExcludeItem(List<SgCChannelShopAppointWarehouseExcludeItemDto> excludeItem, User loginUser) {
        excludeItem.forEach(x -> {
            if (x.getCpCStoreId() != null) {
                CpCStore store = CommonCacheValUtils.getStoreInfo(x.getCpCStoreId());
                x.setCpCStoreEcode(store.getEcode());
                x.setCpCStoreEname(store.getEname());
            }
        });
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("保存成功！"));
    }

    private ValueHolderV14 setAppointItem(List<SgCChannelShopAppointWarehouseAppointItemDto> appointItem, User loginUser) {
        appointItem.forEach(x -> {
            if (x.getCpCStoreId() != null) {
                CpCStore store = CommonCacheValUtils.getStoreInfo(x.getCpCStoreId());
                x.setCpCStoreEcode(store.getEcode());
                x.setCpCStoreEname(store.getEname());
            }
        });
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("保存成功！"));
    }

    @Override
    public ValueHolderV14 execAfterTable(SgCChannelShopAppointWarehouseDto mainObject, Map subObjectMap, User loginUser) {
        return null;
    }

}
