package com.burgeon.r3.sg.channel.services.strategy;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategyMapper;
import com.burgeon.r3.sg.channel.model.event.SysLogEvent;
import com.burgeon.r3.sg.channel.model.request.strategy.SgCChannelSkuStorageQueryRequest;
import com.burgeon.r3.sg.channel.model.request.strategy.SgCChannelSkuStorageRequest;
import com.burgeon.r3.sg.channel.utils.SgChannelProductUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.common.SpringContextHolder;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.channel.control.SgCChannelStockControlLog;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.skustrategy.SgCChannelSkuStrategy;
import com.burgeon.r3.sg.core.model.tableExtend.SgBChannelStorageBufferExtend;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.stocksync.api.SgBChannelStorageBufferCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferBatchSaveRequest;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferSaveRequest;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.UserQueryCmd;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/7 10:48
 * 特殊条码按比例同步策略Service
 */
@Slf4j
@Component
public class SgCChannelSkuStrategyService extends ServiceImpl<SgBChannelProductMapper, SgBChannelProduct> {

    @Reference(group = "sg", version = "1.0")
    private SgBChannelStorageBufferCmd channelStorageBufferCmd;

    @Autowired
    private SgBChannelProductMapper sgBChannelProductMapper;

    @Autowired
    public RedisTemplate redisTemplate;
    @Value("${r3.sg.channel.stock.expirationTime:5}")
    private Integer expirationTime;
    @Value("${r3.sg.channel.stock.expirationTimeFlag:true}")
    private boolean expirationTimeFlag;

    public static final String REDIS_KEY_CHANNEL_UPDRATIO = "updratio";
    public static final String REDIS_KEY_CHANNEL_UPDRATIO_SYNC = "sync:";

    public ValueHolder closed(QuerySession session) {
        SgR3BaseRequest reqeust = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        reqeust.setR3(true);
        SgCChannelSkuStrategyService service = ApplicationContextHandle.getBean(SgCChannelSkuStrategyService.class);
        return R3ParamUtils.convertV14WithResult(service.closed(reqeust));
    }

    /**
     * 结案
     *
     * @param request 请求参数
     * @return ValueHolderV14<SgR3BaseResult>
     */
    public ValueHolderV14<SgR3BaseResult> closed(SgR3BaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgCChannelSkuStrategyService.closed.ReceiveParams:request{}"
                    , JSONObject.toJSONString(request));
        }
        SgCChannelSkuStrategy channelSkuStrategy = checkParams(request);
        User loginUser = request.getLoginUser();
        String lockKey = SgConstants.SG_C_CHANNEL_SKU_STRATEGY + ":" + request.getObjId();
        SgRedisLockUtils.lock(lockKey);
        try {
            SgCChannelSkuStrategyMapper sgChannelSkuStrategyMapper = ApplicationContextHandle.getBean(SgCChannelSkuStrategyMapper.class);
            StorageUtils.setBModelDefalutDataByUpdate(channelSkuStrategy, request.getLoginUser());
            channelSkuStrategy.setModifierename(loginUser.getEname());
            channelSkuStrategy.setStatus(SgChannelConstants.BILL_CHANNEL_STRATEGY_CLOSED);
            channelSkuStrategy.setClosedId(loginUser.getId().longValue());
            channelSkuStrategy.setClosedName(loginUser.getName());
            channelSkuStrategy.setClosedEname(loginUser.getEname());
            channelSkuStrategy.setClosedTime(new Date());
            sgChannelSkuStrategyMapper.updateById(channelSkuStrategy);
            List<Long> objIds = new ArrayList<>();
            objIds.add(channelSkuStrategy.getId());
            syncStock(objIds, loginUser);
        } catch (Exception e) {
            AssertUtils.logAndThrowException(e.getMessage(), e, loginUser.getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKey, log, this.getClass().getName());
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "结案成功！");
    }

    /**
     * 校验参数
     *
     * @param reqeust 请求参数
     * @return SgCChannelSkuStrategy
     */
    private SgCChannelSkuStrategy checkParams(SgR3BaseRequest reqeust) {
        AssertUtils.notNull(reqeust, "请求参数不能为空");
        SgStoreUtils.checkR3BModelDefalut(reqeust);
        Long objId = reqeust.getObjId();
        AssertUtils.notNull(objId, "主表ID不能为空");
        SgCChannelSkuStrategyMapper sgChannelSkuStrategyMapper = ApplicationContextHandle.getBean(SgCChannelSkuStrategyMapper.class);
        SgCChannelSkuStrategy sgChannelSkuStrategy = sgChannelSkuStrategyMapper.selectById(objId);
        AssertUtils.notNull(sgChannelSkuStrategy, "当前记录已不存在！");
        if (SgChannelConstants.BILL_CHANNEL_STRATEGY_UNSUBMIT.equals(sgChannelSkuStrategy.getStatus())) {
            AssertUtils.logAndThrow("当前记录未审核，不允许结案！");
        } else if (SgChannelConstants.BILL_CHANNEL_STRATEGY_VOID.equals(sgChannelSkuStrategy.getStatus())) {
            AssertUtils.logAndThrow("当前记录已作废，不允许结案！");
        } else if (SgChannelConstants.BILL_CHANNEL_STRATEGY_CLOSED.equals(sgChannelSkuStrategy.getStatus())) {
            AssertUtils.logAndThrow("当前记录已结案，不允许重复结案！");
        }
        return sgChannelSkuStrategy;
    }

    /**
     * 定时任务拉取单据状态=已审核， 结束时间小于等于当前时间的单据结案
     *
     * @return ValueHolderV14
     */
    public ValueHolderV14 execute() {
        if (log.isDebugEnabled()) {
            log.debug("Start SgCChannelSkuStrategyClosedTask.execute");
        }
        SgCChannelSkuStrategyMapper sgChannelSkuStrategyMapper = ApplicationContextHandle.getBean(SgCChannelSkuStrategyMapper.class);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date nowTime = Calendar.getInstance().getTime();
        SgCChannelSkuStrategy updateSkuStrategy = new SgCChannelSkuStrategy();
        User user = R3SystemUserResource.getSystemRootUser();
        StorageUtils.setBModelDefalutDataByUpdate(updateSkuStrategy, user);
        updateSkuStrategy.setModifierename(user.getEname());
        updateSkuStrategy.setClosedId(user.getId().longValue());
        updateSkuStrategy.setClosedEname(user.getEname());
        updateSkuStrategy.setClosedName(user.getName());
        updateSkuStrategy.setClosedTime(nowTime);
        updateSkuStrategy.setStatus(SgChannelConstants.BILL_CHANNEL_STRATEGY_CLOSED);
        List<SgCChannelSkuStrategy> updateList =
                sgChannelSkuStrategyMapper.selectList(new LambdaQueryWrapper<SgCChannelSkuStrategy>()
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                        .eq(SgCChannelSkuStrategy::getStatus, SgChannelConstants.BILL_CHANNEL_STRATEGY_SUBMIT)
                        .le(SgCChannelSkuStrategy::getEndTime, format.format(nowTime)));
        //为空不同步 不更新
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<Long> objIds = updateList.stream().map(SgCChannelSkuStrategy::getId).collect(Collectors.toList());
            syncStock(objIds, user);
            sgChannelSkuStrategyMapper.update(updateSkuStrategy, new LambdaUpdateWrapper<SgCChannelSkuStrategy>()
                    .in(SgCChannelSkuStrategy::getId, objIds));
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "执行成功");
    }

    /**
     * 特殊条码比例同步策略 库存同步
     *
     * @param objIds 主表id集合
     * @param user   用户
     */
    public void syncStock(List<Long> objIds, User user) {
        if (CollectionUtils.isNotEmpty(objIds)) {
            if (log.isDebugEnabled()) {
                log.debug("SgCChannelSkuStrategyServic.syncStock.objIds{}:", JSONObject.toJSONString(objIds));
            }
            try {
                SgCChannelSkuStrategyMapper sgChannelSkuStrategyMapper = ApplicationContextHandle.getBean(SgCChannelSkuStrategyMapper.class);
                List<SgChannelStorageBufferSaveRequest> bufferRequests =
                        sgChannelSkuStrategyMapper.querySkuStrategySyncInfos(objIds);
                if (CollectionUtils.isNotEmpty(bufferRequests)) {
                    SgChannelStorageBufferBatchSaveRequest request = new SgChannelStorageBufferBatchSaveRequest();
                    request.setUser(user);
                    for (SgChannelStorageBufferSaveRequest bufferRequest : bufferRequests) {
                        bufferRequest.setDealStatus(SgBChannelStorageBufferExtend.DealStatusEnum.UN_DEAL.getCode());
                        CpShop shopInfo = CommonCacheValUtils.getShopInfo(bufferRequest.getCpCShopId());
                        if (shopInfo != null) {
                            bufferRequest.setCpCPlatformId(shopInfo.getCpCPlatformId().intValue());
                        } else {
                            AssertUtils.logAndThrow("当前店铺已不存在,库存同步失败！");
                        }
                        SgBChannelProduct channelProduct = SgChannelProductUtils.getChannelProduct(bufferRequest.getSkuId());
                        if (channelProduct != null) {
                            bufferRequest.setWareType(channelProduct.getWareType());
                        } else {
                            AssertUtils.logAndThrow("当前条码已不存在,库存同步失败！");
                        }
                    }
                    request.setBufferSaveRequestList(bufferRequests);
//                    channelStorageBufferCmd.saveDataToChannelStorageBuffer(request);
                }
            } catch (Exception e) {
                AssertUtils.logAndThrowException("库存同步失败！", e, user.getLocale());
            }
        }
    }


    /**
     * 库存同步
     *
     * @param channelProductList 集合
     * @param user               用户
     */
    public void syncStockChannelProduct(List<SgBChannelProduct> channelProductList, User user) {
        if (CollectionUtils.isNotEmpty(channelProductList)) {
            if (log.isDebugEnabled()) {
                log.debug("SgCChannelSkuStrategyServic.syncStock.channelProductList={}:", JSONObject.toJSONString(channelProductList));
            }
            try {
                String batchNo = System.currentTimeMillis() + "" + (int) (Math.random() * 9000 + 1000);
                SgChannelStorageBufferBatchSaveRequest request = new SgChannelStorageBufferBatchSaveRequest();
                request.setUser(user);
                List<SgChannelStorageBufferSaveRequest> bufferRequests = Lists.newArrayList();
                for (SgBChannelProduct channelProduct : channelProductList) {
                    SgChannelStorageBufferSaveRequest bufferRequest = new SgChannelStorageBufferSaveRequest();
                    bufferRequest.setDealStatus(SgBChannelStorageBufferExtend.DealStatusEnum.UN_DEAL.getCode());
                    bufferRequest.setCpCPlatformId(channelProduct.getCpCPlatformId());
                    bufferRequest.setCpCShopId(channelProduct.getCpCShopId());
                    bufferRequest.setSkuId(channelProduct.getSkuId());
                    bufferRequest.setWareType(channelProduct.getWareType());
                    bufferRequest.setFixedQty(BigDecimal.ZERO);
                    bufferRequest.setFixedQtyFlag(SgConstants.IS_ACTIVE_N);
                    bufferRequest.setBatchNo(batchNo);
                    bufferRequest.setSourceNo("修改同步比例库存同步");
                    bufferRequest.setSyncType(SgConstantsIF.SYNC_TYPE_POOL_ALL_CITY);
                    bufferRequests.add(bufferRequest);
                }
                request.setBufferSaveRequestList(bufferRequests);
                request.setBatchno(batchNo);
//                channelStorageBufferCmd.saveDataToChannelStorageBuffer(request);
            } catch (Exception e) {
                AssertUtils.logAndThrowException("库存同步失败！", e, user.getLocale());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> checkSkuStorage(SgCChannelSkuStorageRequest httpRequest) {
        if (log.isDebugEnabled()) {
            log.debug("start SgCChannelSkuStrategyService.checkSkuStorage request : {}",
                    JSONObject.toJSONString(httpRequest));
        }

        checkSkuParam(httpRequest);
        UserQueryCmd userQueryCmd = (UserQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), UserQueryCmd.class.getName(), "cp-ext", "1.0");
        User user = userQueryCmd.getUsersById(httpRequest.getUserId());

        List<Long> ids = httpRequest.getRequestList().stream().map(SgCChannelSkuStorageQueryRequest::getId).collect(Collectors.toList());
        List<SgBChannelProduct> sgBChannelProductList = sgBChannelProductMapper.selectBatchIds(ids);

        AssertUtils.notEmpty(sgBChannelProductList, "该平台店铺商品信息不存在！");

        SgBChannelProduct product = sgBChannelProductList.get(0);
        Long cpCShopId = product.getCpCShopId();
        //进行按钮时间控制
        if (expirationTimeFlag) {
            LocalDateTime localDateTime = LocalDateTime.now();
            DateTimeFormatter sdFormat = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            String afterDateTime = sdFormat.format(localDateTime.plusSeconds(expirationTime));

            List<SgCChannelSkuStorageQueryRequest> requestList = httpRequest.getRequestList();
            //设置key1 sg:{updratio}:
            String redisKey1 = SgConstants.REDIS_KEY_CHANNEL_SG.concat(SgConstants.SG_CONNECTOR_MARKS_1)
                    .concat(REDIS_KEY_CHANNEL_UPDRATIO).concat(SgConstants.SG_CONNECTOR_MARKS_2).concat(SgConstants.SG_CONNECTOR_MARKS_4)
                    .concat(REDIS_KEY_CHANNEL_UPDRATIO_SYNC).concat(String.valueOf(cpCShopId));
            String redisKey2 = SgConstants.REDIS_KEY_CHANNEL_SG.concat(SgConstants.SG_CONNECTOR_MARKS_1)
                    .concat(REDIS_KEY_CHANNEL_UPDRATIO).concat(SgConstants.SG_CONNECTOR_MARKS_2).concat(SgConstants.SG_CONNECTOR_MARKS_4)
                    .concat(SgConstants.REDIS_KEY_CHANNEL_TEMP_KEY).concat(String.valueOf(cpCShopId));
            String redisKey3 = SgConstants.REDIS_KEY_CHANNEL_SG.concat(SgConstants.SG_CONNECTOR_MARKS_1)
                    .concat(REDIS_KEY_CHANNEL_UPDRATIO).concat(SgConstants.SG_CONNECTOR_MARKS_2).concat(SgConstants.SG_CONNECTOR_MARKS_4)
                    .concat(SgConstants.REDIS_KEY_CHANNEL_TEMP_RES).concat(String.valueOf(cpCShopId));
            List<String> kesys = new ArrayList<>();
            kesys.add(redisKey1);
            kesys.add(redisKey2);
            kesys.add(redisKey3);

            Object[] redisRequest = new Object[sgBChannelProductList.size() * 2];
            for (int i = 0; i < sgBChannelProductList.size(); i++) {
                redisRequest[i * 2] = afterDateTime;
                redisRequest[i * 2 + 1] = sgBChannelProductList.get(i).getId().toString();
            }

            CusRedisTemplate<String, String> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();
            DefaultRedisScript<List> redisScript = new DefaultRedisScript<>();
            redisScript.setLocation(new ClassPathResource("lua/StockStorageLockRedis.lua"));
            redisScript.setResultType(List.class);
            log.info("SgCChannelSkuStrategyService.checkSkuStorage.redisrequest.param:{}", JSONObject.toJSONString(redisRequest));
            List result = redisMasterTemplate.execute(redisScript, kesys, redisRequest);
            log.info("SgCChannelSkuStrategyService.checkSkuStorage.redisrresult.param:{}", JSONObject.toJSONString(result));
            if (!result.get(0).equals("0")) {
                return new ValueHolderV14<>(ResultCode.FAIL, "修改同步比例按钮控制处理失败");
            }
            if (result.size() < 2) {
                return new ValueHolderV14<>(ResultCode.FAIL, "当前所选择的数据请在" + expirationTime + "s之后再进行修改！");
            }
            List<String> filterResultString = (ArrayList) result.get(1);

            if (CollectionUtils.isEmpty(filterResultString)) {
                return new ValueHolderV14<>(ResultCode.FAIL, "当前所选择的数据请在" + expirationTime + "s之后再进行修改！");
            }
            List<Long> filterResult = filterResultString.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            List<SgCChannelSkuStorageQueryRequest> filterCChannelSkuStorageQueryRequest = requestList.stream()
                    .filter(o -> filterResult.contains(o.getId())).collect(Collectors.toList());

            sgBChannelProductList = sgBChannelProductList.stream().filter(o -> filterResult.contains(o.getId())).collect(Collectors.toList());
            httpRequest.setRequestList(filterCChannelSkuStorageQueryRequest);
        }

        List<SgBChannelProduct> updateChannelList = Lists.newArrayList();
        for (SgCChannelSkuStorageQueryRequest request : httpRequest.getRequestList()) {
            SgBChannelProduct channelProduct = new SgBChannelProduct();
            channelProduct.setId(request.getId());
            channelProduct.setSyncRatio(request.getRatio());
            channelProduct.setReserveBigint01(null);
            StorageUtils.setBModelDefalutDataByUpdate(channelProduct, user);
            updateChannelList.add(channelProduct);
        }

        if (CollectionUtils.isNotEmpty(updateChannelList)) {
            this.updateBatchById(updateChannelList);
        }

        try {
            if (CollectionUtils.isNotEmpty(sgBChannelProductList)) {
                this.syncStockChannelProduct(sgBChannelProductList, user);
            }
            saveSgChannelStockControlLog(httpRequest, user);
        } catch (Exception e) {
            log.error("SgCChannelSkuStrategyService.checkSkuStorage.error", e);
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "操作成功");
    }


    /**
     * 记录新增/修改操作日志
     *
     * @param request
     * @param user
     */
    public void saveSgChannelStockControlLog(SgCChannelSkuStorageRequest request, User user) {
        log.info("Start SpringContextHolder.publishEvent.log");
        List<SgCChannelSkuStorageQueryRequest> saveInfoRequestList = request.getRequestList();

        if (user == null) {
            UserQueryCmd userQueryCmd = (UserQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), UserQueryCmd.class.getName(), "cp-ext", "1.0");
            user = userQueryCmd.getUsersById(request.getUserId());
        }

        SgCChannelStockControlLog sgCChannelStockControlLog = new SgCChannelStockControlLog();
        sgCChannelStockControlLog.setOperationType("修改同步比例");
        StorageUtils.setBModelDefalutData(sgCChannelStockControlLog, user);
        sgCChannelStockControlLog.setModContent(JSONObject.toJSONString(request.getRequestList()));
        SysLogEvent<SgCChannelStockControlLog> sysLogEvent = new SysLogEvent(sgCChannelStockControlLog);
        SpringContextHolder.publishEvent(sysLogEvent);
        log.info("end SpringContextHolder.publishEvent.log");
    }

    private void checkSkuParam(SgCChannelSkuStorageRequest request) {
        AssertUtils.notNull(request, "请求参数不能为空");
        AssertUtils.notNull(request.getUserId(), "用户不能为空");

        AssertUtils.notEmpty(request.getRequestList(), "请求集合不能为空");

        request.getRequestList().forEach(x -> {
            AssertUtils.notNull(x.getId(), "ID不能为空");
            AssertUtils.notNull(x.getRatio(), "比例不能为空");
        });
    }

}
