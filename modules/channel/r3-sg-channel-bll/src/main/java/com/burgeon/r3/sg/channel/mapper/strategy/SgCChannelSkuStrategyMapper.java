package com.burgeon.r3.sg.channel.mapper.strategy;

import com.burgeon.r3.sg.channel.model.request.strategy.SgCChannelSkuStrategyQueryInfoRequest;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelSkuStrategyQuerySaInfoResult;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelSkuStrategyQuerySpInfoResult;
import com.burgeon.r3.sg.core.model.table.channel.skustrategy.SgCChannelSkuStrategy;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferSaveRequest;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface SgCChannelSkuStrategyMapper extends ExtentionMapper<SgCChannelSkuStrategy> {
    /**
     * @param request:
     * @Description: 查询特殊条码策略信息
     * @Author: hwy
     * @Date: 2021/6/19 17:58
     * @return: java.util.List<com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelSkuStrategyQueryInfoResult>
     **/
    @Select({
            "<script>",
            " SELECT ",
            " A.CP_C_SHOP_ID, ",
            " A.BEGIN_TIME, ",
            " A.END_TIME, ",
            " B.SG_C_SA_STORE_ID, ",
            " B.SG_C_SA_STORE_ECODE, ",
            " B.SG_C_SA_STORE_ENAME, ",
            " B.RATIO, ",
            " B.SKU_ID, ",
            " B.PS_C_SKU_ID, ",
            " COALESCE(C.TYPE,1) AS TYPE, ",
            " COALESCE(C.ORDERNO,99) AS ORDERNO  ",
            " FROM ",
            " SG_C_CHANNEL_SKU_STRATEGY A ",
            " LEFT JOIN SG_C_CHANNEL_SKU_STRATEGY_SA_ITEM B ON A.ID = B.SG_C_CHANNEL_SKU_STRATEGY_ID ",
            " LEFT JOIN  ",
            "  ( ",
            " SELECT ",
            " D.CP_C_SHOP_ID, ",
            " E.SG_C_SA_STORE_ID, ",
            " E.SG_C_SA_STORE_ORDERNO AS ORDERNO, ",
            " E.SG_C_SA_STORE_TYPE AS TYPE ",
            " FROM ",
            " SG_C_CHANNEL_RATIO_STRATEGY D ",
            " INNER JOIN SG_C_CHANNEL_RATIO_STRATEGY_ITEM E ON D.ID = E.SG_C_CHANNEL_RATIO_STRATEGY_ID",
            " WHERE D.ISACTIVE = 'Y' AND E.ISACTIVE = 'Y'",
            " <when test='request.cpCShopId != null' >",
            " AND D.CP_C_SHOP_ID = #{request.cpCShopId,jdbcType=INTEGER} ",
            " </when>  ",
            " ) C  ON A.CP_C_SHOP_ID = C.CP_C_SHOP_ID AND B.SG_C_SA_STORE_ID = C.SG_C_SA_STORE_ID ",
            " WHERE A.ISACTIVE = 'Y' AND B.ISACTIVE='Y' ",
            " AND A.STATUS = '2'",
            " <when test='request.cpCShopId != null' >",
            " AND A.CP_C_SHOP_ID = #{request.cpCShopId,jdbcType=INTEGER} ",
            " </when>  ",
            " <when test='request.beginTime != null ' >  ",
            " AND A.BEGIN_TIME &lt;= #{request.beginTime,jdbcType=TIMESTAMP} ",
            " </when>  ",
            " <when test='request.endTime != null ' >  ",
            " AND A.END_TIME &gt;= #{request.endTime,jdbcType=TIMESTAMP} ",
            " </when>  ",
            " <when test='request.skuIds != null and request.skuIds.size>0 ' >  ",
            " AND B.SKU_ID IN ",
            " <foreach item='item' collection='request.skuIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when>  ",
            " <when test='request.sgCSaStoreIds != null and request.sgCSaStoreIds.size>0 ' >  ",
            " AND B.SG_C_SA_STORE_ID IN ",
            " <foreach item='item' collection='request.sgCSaStoreIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            " <when test='request.psCSkuIds != null and request.psCSkuIds.size>0 ' >  ",
            " AND B.PS_C_SKU_ID IN ",
            " <foreach item='item' collection='request.psCSkuIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            " ORDER BY ISNULL(C.ORDERNO),C.ORDERNO  ",
            "</script>"

    })
    List<SgCChannelSkuStrategyQuerySaInfoResult> querySkuStrategySaInfo(@Param("request") SgCChannelSkuStrategyQueryInfoRequest request);


    /**
     * @param request:
     * @Description: 查询特殊条码策略信息
     * @Author: hwy
     * @Date: 2021/6/19 17:58
     * @return: java.util.List<com.burgeon.r3.sg.channel.model.result.strategy.SgCChannelSkuStrategyQueryInfoResult>
     **/
    @Select({
            "<script>",
            " SELECT ",
            " A.CP_C_SHOP_ID, ",
            " A.BEGIN_TIME, ",
            " A.END_TIME, ",
            " B.SG_C_SA_STORE_ID, ",
            " B.RATIO, ",
            " B.SKU_ID, ",
            " B.PS_C_SKU_ID, ",
            " COALESCE(F.TYPE,1) AS TYPE, ",
            " COALESCE(F.ORDERNO,99) AS ORDERNO  ",
            " FROM ",
            " SG_C_CHANNEL_SKU_STRATEGY A ",
            " LEFT JOIN SG_C_CHANNEL_SKU_STRATEGY_SA_ITEM B ON A.ID = B.SG_C_CHANNEL_SKU_STRATEGY_ID ",
            " LEFT JOIN SG_C_SA_STORE C ON B.SG_C_SA_STORE_ID = C.ID ",
            " LEFT JOIN  ",
            "  ( ",
            " SELECT ",
            " D.CP_C_SHOP_ID, ",
            " E.SG_C_SA_STORE_ID, ",
            " E.SG_C_SA_STORE_ORDERNO AS ORDERNO, ",
            " E.SG_C_SA_STORE_TYPE AS TYPE ",
            " FROM ",
            " SG_C_CHANNEL_RATIO_STRATEGY D ",
            " INNER JOIN SG_C_CHANNEL_RATIO_STRATEGY_ITEM E ON D.ID = E.SG_C_CHANNEL_RATIO_STRATEGY_ID",
            " WHERE D.ISACTIVE = 'Y' AND E.ISACTIVE = 'Y'",
            " <when test='request.cpCShopId != null' >",
            " AND D.CP_C_SHOP_ID = #{request.cpCShopId,jdbcType=INTEGER} ",
            " </when>  ",
            " ) F  ON A.CP_C_SHOP_ID = F.CP_C_SHOP_ID AND B.SG_C_SA_STORE_ID = F.SG_C_SA_STORE_ID ",
            " WHERE A.ISACTIVE = 'Y' AND B.ISACTIVE='Y' AND C.ISACTIVE='Y' AND C.SG_C_SHARE_STORE_ID = #{ssid} ",
            " <when test='request.cpCShopId != null' >",
            " AND A.CP_C_SHOP_ID = #{request.cpCShopId,jdbcType=INTEGER} ",
            " </when>  ",
            " <when test='request.beginTime != null ' >  ",
            " AND A.BEGIN_TIME &lt;= #{request.beginTime,jdbcType=TIMESTAMP} ",
            " </when>  ",
            " <when test='request.endTime != null ' >  ",
            " AND A.END_TIME &gt;= #{request.endTime,jdbcType=TIMESTAMP} ",
            " </when>  ",
            " <when test='request.skuIds != null and request.skuIds.size>0 ' >  ",
            " AND B.SKU_ID IN ",
            " <foreach item='item' collection='request.skuIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when>  ",
            " ORDER BY ISNULL(C.ORDERNO),C.ORDERNO  ",
            "</script>"

    })
    List<SgCChannelSkuStrategyQuerySaInfoResult> querySkuStrategySaInfoV2(@Param("request") SgCChannelSkuStrategyQueryInfoRequest request,
                                                                          @Param("ssid") Long ssid);


    @Select({
            "<script>",
            " SELECT ",
            " A.CP_C_SHOP_ID, ",
            " A.BEGIN_TIME, ",
            " A.END_TIME, ",
            " B.RATIO, ",
            " B.SKU_ID, ",
            " B.PS_C_SKU_ID, ",
            " C.SG_C_SHARE_POOL_ID, ",
            " B.SG_C_SHARE_POOL_ECODE, ",
            " B.SG_C_SHARE_POOL_ENAME, ",
            " C.SG_C_SHARE_STORE_ID, ",
            " C.ORDERNO, ",
            " C.RATIO AS SHARE_RATIO ",
            " FROM ",
            " SG_C_CHANNEL_SKU_STRATEGY A ",
            " LEFT JOIN SG_C_CHANNEL_SKU_STRATEGY_SP_ITEM B ON A.ID = B.SG_C_CHANNEL_SKU_STRATEGY_ID ",
            " LEFT JOIN (  ",
            " SELECT  ",
            "  E.ORDERNO,  ",
            "  E.RATIO,  ",
            "  E.SG_C_SHARE_STORE_ID,  ",
            "  E.SG_C_SHARE_POOL_ID,  ",
            "  D.CP_C_SHOP_ID  ",
            " FROM  ",
            "  SG_C_CHANNEL_RATIO_STRATEGY D  ",
            " INNER JOIN SG_C_SHARE_POOL_ITEM E ON D.SG_C_SHARE_POOL_ID = E.SG_C_SHARE_POOL_ID  AND D.ISACTIVE='Y' AND E.RATIO >0  ",
            " WHERE  ",
            "  D.ISACTIVE = 'Y'  ",
            " <when test='request.cpCShopId != null ' >  ",
            " AND D.CP_C_SHOP_ID = #{request.cpCShopId,jdbcType=INTEGER} ",
            " </when>  ",
            ") C ON A.CP_C_SHOP_ID = C.CP_C_SHOP_ID ",
            " WHERE A.ISACTIVE = 'Y' AND B.ISACTIVE='Y' AND A.STATUS = '2' ",
            " <when test='request.cpCShopId != null ' >  ",
            " AND A.CP_C_SHOP_ID = #{request.cpCShopId,jdbcType=INTEGER} ",
            " </when>  ",
            " <when test='request.beginTime != null ' >  ",
            " AND A.BEGIN_TIME &lt;= #{request.beginTime,jdbcType=TIMESTAMP} ",
            " </when>  ",
            " <when test='request.endTime != null ' >  ",
            " AND A.END_TIME &gt;= #{request.endTime,jdbcType=TIMESTAMP} ",
            " </when>  ",
            " <when test='request.sgCSharePoolIds != null and request.sgCSharePoolIds.size>0 ' >  ",
            " AND B.SG_C_SHARE_POOL_ID IN ",
            " <foreach item='item' collection='request.sgCSharePoolIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            " <when test='request.sgCShareStoreIds != null and request.sgCShareStoreIds.size>0 ' >  ",
            " AND C.SG_C_SHARE_STORE_ID IN ",
            " <foreach item='item' collection='request.sgCShareStoreIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            " <when test='request.skuIds != null and request.skuIds.size>0 ' >  ",
            " AND B.SKU_ID IN",
            " <foreach item='item' collection='request.skuIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when>  ",
            " <when test='request.psCSkuIds != null and request.psCSkuIds.size>0 ' >  ",
            " AND B.PS_C_SKU_ID IN ",
            " <foreach item='item' collection='request.psCSkuIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            " ORDER BY ISNULL(C.ORDERNO),C.ORDERNO  ",
            "</script>"

    })
    List<SgCChannelSkuStrategyQuerySpInfoResult> querySkuStrategySpInfo(@Param("request") SgCChannelSkuStrategyQueryInfoRequest request);

    @Select({"<script>",
            "SELECT" +
                    " concat('特殊条码按比例同步策略:',main.id) as sourceNo, " +
                    " main.cp_c_shop_id cpCShopId," +
                    " 'N' fixedQtyFlag," +
                    "  0  as fixedQty," +
                    " sa.sku_id skuId" +
                    " FROM" +
                    " sg_c_channel_sku_strategy main" +
                    " LEFT JOIN sg_c_channel_sku_strategy_sa_item sa ON main.id = sa.sg_c_channel_sku_strategy_id" +
                    " WHERE sa.isactive='Y' " +
                    "<when test='objIds!= null and objIds.size>0 ' > " +
                    " AND main.id IN" +
                    " <foreach item='item' collection='objIds' separator=',' open='(' close=')' > #{item," +
                    "jdbcType=BIGINT} </foreach>",
            " </when>  " +
                    " UNION" +
                    " SELECT" +
                    " concat('特殊条码按比例同步策略:',main.id) as sourceNo, " +
                    " main.cp_c_shop_id cpCShopId," +
                    " 'N' fixedQtyFlag," +
                    "  0  as fixedQty," +
                    " sp.sku_id skuId" +
                    " FROM" +
                    " sg_c_channel_sku_strategy main" +
                    " LEFT JOIN sg_c_channel_sku_strategy_sp_item sp ON main.id = sp.sg_c_channel_sku_strategy_id" +
                    " WHERE sp.isactive='Y' " +
                    "<when test='objIds!= null and objIds.size>0 ' > " +
                    " AND main.id IN" +
                    " <foreach item='item' collection='objIds' separator=',' open='(' close=')' > #{item," +
                    "jdbcType=BIGINT} </foreach>",
            " </when>  " +
                    "</script>"
    })
    List<SgChannelStorageBufferSaveRequest> querySkuStrategySyncInfos(@Param("objIds") List<Long> objIds);

    /**
     * 寻源，查询特殊条码供货仓库
     * @return
     */
    @Select({
            "<script>",
            " SELECT ",
            " A.CP_C_SHOP_ID, ",
            " A.BEGIN_TIME, ",
            " A.END_TIME, ",
            " B.SG_C_SA_STORE_ID, ",
            " B.SG_C_SA_STORE_ECODE, ",
            " B.SG_C_SA_STORE_ENAME, ",
            " B.RATIO, ",
            " B.SKU_ID, ",
            " B.PS_C_SKU_ID, ",
            " c.category, ",
            " c.sg_c_share_store_id, ",
            " B.PRIORITY ORDERNO ",
            " FROM ",
            " SG_C_CHANNEL_SKU_STRATEGY A ",
            " LEFT JOIN SG_C_CHANNEL_SKU_STRATEGY_SA_ITEM B ON A.ID = B.SG_C_CHANNEL_SKU_STRATEGY_ID ",
            " left join sg_c_sa_store c on b.sg_c_sa_store_id = c.id ",
            " WHERE A.ISACTIVE = 'Y' AND B.ISACTIVE='Y' ",
            " AND A.STATUS = '2'",
            " <when test='request.cpCShopId != null' >",
            " AND A.CP_C_SHOP_ID = #{request.cpCShopId,jdbcType=INTEGER} ",
            " </when>  ",
            " <when test='request.beginTime != null ' >  ",
            " AND A.BEGIN_TIME &lt;= #{request.beginTime,jdbcType=TIMESTAMP} ",
            " </when>  ",
            " <when test='request.endTime != null ' >  ",
            " AND A.END_TIME &gt;= #{request.endTime,jdbcType=TIMESTAMP} ",
            " </when>  ",
            " <when test='request.skuIds != null and request.skuIds.size>0 ' >  ",
            " AND B.SKU_ID IN ",
            " <foreach item='item' collection='request.skuIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when>  ",
            " <when test='request.sgCSaStoreIds != null and request.sgCSaStoreIds.size>0 ' >  ",
            " AND B.SG_C_SA_STORE_ID IN ",
            " <foreach item='item' collection='request.sgCSaStoreIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            " <when test='request.psCSkuIds != null and request.psCSkuIds.size>0 ' >  ",
            " AND B.PS_C_SKU_ID IN ",
            " <foreach item='item' collection='request.psCSkuIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            " ORDER BY ISNULL(b.PRIORITY),b.PRIORITY ",
            "</script>"
    })
    List<SgCChannelSkuStrategyQuerySaInfoResult> querySkuStrategy(@Param("request") SgCChannelSkuStrategyQueryInfoRequest request);


}