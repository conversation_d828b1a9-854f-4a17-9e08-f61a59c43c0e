package com.burgeon.r3.sg.channel.validate.storage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.storage.SgCChannelStoreSafetySettingItemMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgCChannelStoreSafetySettingMapper;
import com.burgeon.r3.sg.channel.model.dto.storage.SgCChannelStoreSafetySettingDto;
import com.burgeon.r3.sg.channel.model.dto.storage.SgCChannelStoreSafetySettingItemDto;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgCChannelStoreSafetySetting;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgCChannelStoreSafetySettingItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleItemValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description 平台店铺安全库存批量设置
 * <AUTHOR>
 * @Date 2021/6/23 9:51
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgCChannelStoreSafetySettingSaveValidator extends BaseSingleItemValidator<SgCChannelStoreSafetySettingDto, SgCChannelStoreSafetySettingItemDto> {

    @Autowired
    private SgCChannelStoreSafetySettingMapper storeSafetySettingMapper;

    @Autowired
    private SgCChannelStoreSafetySettingItemMapper storeSafetySettingItemMapper;


    @Override
    public String getValidatorMsgName() {
        return "平台店铺安全库存批量设置保存";
    }

    @Override
    public Class<?> getValidatorClass() {
        return SgCChannelStoreSafetySettingSaveValidator.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCChannelStoreSafetySettingDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        if (mainObject.getId() > 0L) {
            SgCChannelStoreSafetySetting channelStoreSafetySetting = storeSafetySettingMapper.selectById(mainObject.getId());
            if (channelStoreSafetySetting == null) {
                return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前记录已不存在！", loginUser.getLocale()));
            }
            if (SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_STATUS_SUBMIT == channelStoreSafetySetting.getStatus()) {
                return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前记录已审核，不允许编辑！", loginUser.getLocale()));
            }
            if (SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_STATUS_VOID == channelStoreSafetySetting.getStatus()) {
                return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前记录已作废，不允许编辑！", loginUser.getLocale()));
            }

        } else {
            if (StringUtils.isEmpty(mainObject.getBatchNo())) {
                return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("批次号不能为空！", loginUser.getLocale()));
            }
            Integer batchNoCount = storeSafetySettingMapper.selectCount(new LambdaQueryWrapper<SgCChannelStoreSafetySetting>()
                    .eq(SgCChannelStoreSafetySetting::getBatchNo, mainObject.getBatchNo())
                    .ne(SgCChannelStoreSafetySetting::getStatus, SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_STATUS_VOID));
            if (batchNoCount > 0) {
                return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("前批次号已存在有效记录，不允许重复！！", loginUser.getLocale()));
            }

        }

        return v14;
    }

    @Override
    public ValueHolderV14 validateSubTable(SgCChannelStoreSafetySettingDto mainObject, List<SgCChannelStoreSafetySettingItemDto> subObjectList, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));

        for (SgCChannelStoreSafetySettingItemDto itemDto : subObjectList) {
            if (itemDto.getId() > 0L) {

                SgCChannelStoreSafetySettingItem storeSafetySettingItem = storeSafetySettingItemMapper.selectById(itemDto.getId());
                if (storeSafetySettingItem == null) {
                    return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前明细记录已不存在！", loginUser.getLocale()));
                }

            } else {
                if (itemDto.getCpCShopId() == null) {
                    return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("平台店铺不能为空！", loginUser.getLocale()));
                }
                if (StringUtils.isEmpty(itemDto.getProType())) {
                    return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("商品类型不能为空！", loginUser.getLocale()));
                }
                if (itemDto.getQtySafety() == null) {
                    return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("安全库存不能为空！", loginUser.getLocale()));
                }

                if (SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_PRO_TYPE_PS_C_PRO.equals(itemDto.getProType())) {
                    if (itemDto.getPsCProId() == null) {
                        return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前明细[商品类型]为商品编码,商品编码不能为空！", loginUser.getLocale()));
                    }


                } else if (SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_PRO_TYPE_PS_C_SKU.equals(itemDto.getProType())) {
                    if (itemDto.getPsCSkuId() == null) {
                        return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前明细[商品类型]为条码,条码不能为空！", loginUser.getLocale()));
                    }


                } else if (SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_PRO_TYPE_NUMIID.equals(itemDto.getProType())) {
                    if (itemDto.getNumiid() == null) {
                        return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前明细[商品类型]为平台商品,平台商品ID不能为空！", loginUser.getLocale()));
                    }

                } else if (SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_PRO_TYPE_SKU_ID.equals(itemDto.getProType())) {
                    if (itemDto.getSkuId() == null) {
                        return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前明细[商品类型]为平台条码,平台条码ID不能为空！", loginUser.getLocale()));
                    }

                }
            }
        }
        return v14;
    }
}
