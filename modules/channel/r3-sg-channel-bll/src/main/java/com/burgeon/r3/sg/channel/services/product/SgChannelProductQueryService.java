package com.burgeon.r3.sg.channel.services.product;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.mapper.SgBSaStorageMapper;
import com.burgeon.r3.sg.basic.mapper.oms.SgCommonTableMapper;
import com.burgeon.r3.sg.basic.model.request.SgStoragePageRequest;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategyMapper;
import com.burgeon.r3.sg.channel.model.dto.storage.SgBStorageBufferDto;
import com.burgeon.r3.sg.channel.model.enumerate.CalcStatusEnum;
import com.burgeon.r3.sg.channel.model.request.product.*;
import com.burgeon.r3.sg.channel.model.result.product.*;
import com.burgeon.r3.sg.core.common.R3ParamConstants;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.oms.SgBChannelStorageBuffer;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.stocksync.api.SgBChannelStorageBufferCmd;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpShopQueryCmd;
import com.jackrain.nea.cpext.model.request.CpShopQueryRequest;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/4 14:21
 */
@Slf4j
@Component
public class SgChannelProductQueryService {

    @Autowired
    private SgBChannelProductMapper sgBChannelProductMapper;

    @Autowired
    private SgCChannelSkuStrategyMapper strategyMapper;

    @Autowired
    private SgCChannelRatioStrategyMapper ratioStrategyMapper;

    @Autowired
    private PropertiesConf propertiesConf;

    @Autowired
    private SgBSaStorageMapper storageMapper;

    @Reference(group = "cp-ext", version = "1.0")
    private CpShopQueryCmd shopQueryCmd;

    @Value("${r3.sg.channel.adb.schema:r3_oms_sg}")
    private String adbSchema;

    /**
     * 查询平台商品信息
     *
     * @param request 请求request
     * @return
     */
    public ValueHolderV14<List<SgBChannelProduct>> queryChannelProduct(SgChannelProductQueryRequest request) {

        ValueHolderV14<List<SgBChannelProduct>> holder = new ValueHolderV14(ResultCode.SUCCESS, "查询平台商品信息成功！");

        //入参日志记录
        if (log.isDebugEnabled()) {
            log.debug("Start SgChannelProductQueryService.queryChannelProduct. ReceiveParams:request={}"
                    , JSONObject.toJSONString(request));
        }

        //检查入参
        holder = checkServiceParam(request);
        if (ResultCode.FAIL == holder.getCode()) {
            log.warn("SgChannelProductQueryService.queryChannelProduct. checkServiceParam error:{};",
                    holder.getMessage());
            return holder;
        }

        List<Long> idList = request.getIdList();
        List<String> numiidList = request.getNumiidList();
        List<Long> psCSkuIdList = request.getPsCSkuIdList();
        List<String> skuIdList = request.getSkuIdList();
        List<SgBChannelProduct> sgBChannelProducts = new ArrayList<>();
        Long cpCShopId = request.getCpCShopId();

        try {
            if (CollectionUtils.isNotEmpty(numiidList) && CollectionUtils.isEmpty(skuIdList) && CollectionUtils.isNotEmpty(psCSkuIdList)) {
                sgBChannelProducts = this.sgBChannelProductMapper.selectList(
                        new QueryWrapper<SgBChannelProduct>().lambda()
                                .in(SgBChannelProduct::getNumiid, numiidList)
                                .in(SgBChannelProduct::getPsCSkuId, psCSkuIdList)
                                .eq(cpCShopId != null, SgBChannelProduct::getCpCShopId, cpCShopId));
            } else if (CollectionUtils.isNotEmpty(idList)) {
                sgBChannelProducts = this.sgBChannelProductMapper.selectBatchIds(idList);
            } else if (CollectionUtils.isNotEmpty(numiidList)) {
                sgBChannelProducts = this.sgBChannelProductMapper.selectList(
                        new QueryWrapper<SgBChannelProduct>().lambda()
                                .in(SgBChannelProduct::getNumiid, numiidList)
                                .eq(cpCShopId != null, SgBChannelProduct::getCpCShopId, cpCShopId));
            } else if (CollectionUtils.isNotEmpty(psCSkuIdList)) {
                sgBChannelProducts = this.sgBChannelProductMapper.selectList(
                        new QueryWrapper<SgBChannelProduct>().lambda()
                                .in(SgBChannelProduct::getPsCSkuId, psCSkuIdList)
                                .eq(cpCShopId != null, SgBChannelProduct::getCpCShopId, cpCShopId));
            } else if (CollectionUtils.isNotEmpty(skuIdList)) {
                sgBChannelProducts = this.sgBChannelProductMapper.selectList(
                        new QueryWrapper<SgBChannelProduct>().lambda()
                                .in(SgBChannelProduct::getSkuId, skuIdList)
                                .eq(cpCShopId != null, SgBChannelProduct::getCpCShopId, cpCShopId));
            }

        } catch (Exception e) {
            log.error("SgChannelProductQueryService.queryChannelProduct. exception_has_occured:{}", Throwables.getStackTraceAsString(e));
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("查询平台商品信息异常！详情:" + e.getMessage());
        }

        holder.setData(sgBChannelProducts);

        return holder;
    }

    /**
     * @param request
     * @return
     */
    private ValueHolderV14<List<SgBChannelProduct>> checkServiceParam(SgChannelProductQueryRequest request) {

        ValueHolderV14<List<SgBChannelProduct>> holder = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

        if (request == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("请求体不能为空!");
            return holder;
        }

        if (CollectionUtils.isEmpty(request.getIdList()) &&
                CollectionUtils.isEmpty(request.getNumiidList()) &&
                CollectionUtils.isEmpty(request.getPsCSkuIdList()) &&
                CollectionUtils.isEmpty(request.getSkuIdList())) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("查询的平台商品，平台条码，商品条码集合不能为空!");
            return holder;
        }

        /*if (CollectionUtils.isNotEmpty(request.getNumiidList()) &&
                CollectionUtils.isNotEmpty(request.getPsCSkuIdList())) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("暂不支持平台商品，商品条码同时查询渠道商品信息!");
            return holder;
        }*/

        if (CollectionUtils.isNotEmpty(request.getNumiidList()) &&
                CollectionUtils.isNotEmpty(request.getSkuIdList())) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("暂不支持平台商品，平台条码同时查询渠道商品信息!");
            return holder;
        }

        if (CollectionUtils.isNotEmpty(request.getPsCSkuIdList()) &&
                CollectionUtils.isNotEmpty(request.getSkuIdList())) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("暂不支持商品条码、平台条码同时查询渠道商品信息!");
            return holder;
        }

        return holder;
    }

    private ValueHolderV14<List<SgBChannelProduct>> checkParam(SgChannelProductQueryRequest sgChannelProductQueryRequest) {
        ValueHolderV14<List<SgBChannelProduct>> holder = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

        if (sgChannelProductQueryRequest == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("请求体不能为空!");
            return holder;
        }

        if (CollectionUtils.isEmpty(sgChannelProductQueryRequest.getSkuEcodeList()) && CollectionUtils.isEmpty(sgChannelProductQueryRequest.getSkuIdList())
                && CollectionUtils.isEmpty(sgChannelProductQueryRequest.getNumiidList())) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("查询的系统商品skueEcode集合/平台skuid集合/平台商品ID不能同时为空!");
            return holder;
        }

        /*if (CollectionUtils.isNotEmpty(sgChannelProductQueryRequest.getSkuEcodeList()) && sgChannelProductQueryRequest.getSkuEcodeList().size() > 1) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("不支持多条数据模糊查询!");
            return holder;
        }

        if (CollectionUtils.isNotEmpty(sgChannelProductQueryRequest.getSkuIdList()) && sgChannelProductQueryRequest.getSkuIdList().size() > 1) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("不支持多条数据模糊查询!");
            return holder;
        }
        if (CollectionUtils.isNotEmpty(sgChannelProductQueryRequest.getNumiidList()) && sgChannelProductQueryRequest.getNumiidList().size() > 1) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("不支持多条数据模糊查询!");
            return holder;
        }*/

        return holder;
    }

    /**
     * 根据系统SKU编码模糊查询 支持分页
     */
    public ValueHolderV14<List<SgBChannelProduct>> fuzzyQueryChannelProductByPage(SgChannelProductQueryRequest sgChannelProductQueryRequest, SgStoragePageRequest sgStoragePageRequest) {
        ValueHolderV14<List<SgBChannelProduct>> holder;

        if (log.isDebugEnabled()) {
            log.debug("Start SgChannelProductQueryService.fuzzyQueryChannelProductByPage. ReceiveParams:sgChannelProductQueryRequest={},sgStoragePageRequest={};",
                    JSONObject.toJSONString(sgChannelProductQueryRequest), JSONObject.toJSONString(sgStoragePageRequest));
        }

        holder = checkParam(sgChannelProductQueryRequest);
        if (!holder.isOK()) {
            return holder;
        }

        List<String> skuEcodeList = sgChannelProductQueryRequest.getSkuEcodeList();
        List<String> skuIdList = sgChannelProductQueryRequest.getSkuIdList();
        List<String> numiidList = sgChannelProductQueryRequest.getNumiidList();
        List<SgBChannelProduct> resultList = Lists.newArrayList();
        if (sgStoragePageRequest == null) {
            /** 全量查询 **/
            LambdaQueryWrapper<SgBChannelProduct> lambda = new QueryWrapper<SgBChannelProduct>().lambda();
            if (CollectionUtils.isNotEmpty(skuEcodeList)) {
                if (skuEcodeList.size() == 1) {
                    lambda.likeRight(SgBChannelProduct::getPsCSkuEcode, skuEcodeList.get(0));
                } else {
                    lambda.in(SgBChannelProduct::getPsCSkuEcode, skuEcodeList);
                }
            }
            if (CollectionUtils.isNotEmpty(skuEcodeList) && CollectionUtils.isNotEmpty(skuIdList)) {
                lambda.or();
            }
            if (CollectionUtils.isNotEmpty(skuIdList)) {
                if (skuIdList.size() == 1) {
                    lambda.likeRight(SgBChannelProduct::getSkuId, skuIdList.get(0));
                } else {
                    lambda.in(SgBChannelProduct::getSkuId, skuIdList);
                }
            }
            if (CollectionUtils.isNotEmpty(skuIdList) && CollectionUtils.isNotEmpty(numiidList)) {
                lambda.or();
            }
            if (CollectionUtils.isNotEmpty(numiidList)) {
                if (numiidList.size() == 1) {
                    lambda.likeRight(SgBChannelProduct::getNumiid, numiidList.get(0));
                } else {
                    lambda.in(SgBChannelProduct::getNumiid, numiidList);
                }
            }
            resultList = sgBChannelProductMapper.selectList(lambda);
            holder.setData(resultList);
        } else {
            /** 分页查询 **/
            PageHelper.startPage(sgStoragePageRequest.getPageNum(), sgStoragePageRequest.getPageSize());
            LambdaQueryWrapper<SgBChannelProduct> lambda = new QueryWrapper<SgBChannelProduct>().lambda();
            if (CollectionUtils.isNotEmpty(skuEcodeList)) {
                if (skuEcodeList.size() == 1) {
                    lambda.likeRight(SgBChannelProduct::getPsCSkuEcode, skuEcodeList.get(0));
                } else {
                    lambda.in(SgBChannelProduct::getPsCSkuEcode, skuEcodeList);
                }
            }
            if (CollectionUtils.isNotEmpty(skuEcodeList) && CollectionUtils.isNotEmpty(skuIdList)) {
                lambda.or();
            }
            if (CollectionUtils.isNotEmpty(skuIdList)) {
                if (skuIdList.size() == 1) {
                    lambda.likeRight(SgBChannelProduct::getSkuId, skuIdList.get(0));
                } else {
                    lambda.in(SgBChannelProduct::getSkuId, skuIdList);
                }
            }
            if (CollectionUtils.isNotEmpty(skuIdList) && CollectionUtils.isNotEmpty(numiidList)) {
                lambda.or();
            }
            if (CollectionUtils.isNotEmpty(numiidList)) {
                if (numiidList.size() == 1) {
                    lambda.likeRight(SgBChannelProduct::getNumiid, numiidList.get(0));
                } else {
                    lambda.in(SgBChannelProduct::getNumiid, numiidList);
                }
            }
            resultList = sgBChannelProductMapper.selectList(lambda);

            PageInfo<SgBChannelProduct> resultPage = new PageInfo<>(resultList);
            holder.setData(resultPage.getList());
        }

        if (log.isDebugEnabled()) {
            log.debug("Finish SgChannelProductQueryService.fuzzyQueryChannelProductByPage. Return={};",
                    JSONObject.toJSONString(holder));
        }
        return holder;
    }

    /**
     * 组合商品页面模糊查询平台商品 支持分页
     */
    public ValueHolderV14<List<SgBChannelProduct>> proGroupQueryChannelProductByPage(SgChannelProductQueryRequest sgChannelProductQueryRequest, SgStoragePageRequest sgStoragePageRequest) {
        ValueHolderV14<List<SgBChannelProduct>> holder;

        if (log.isDebugEnabled()) {
            log.debug("Start SgChannelProductQueryService.fuzzyQueryChannelProductByPage. ReceiveParams:sgChannelProductQueryRequest={},sgStoragePageRequest={};",
                    JSONObject.toJSONString(sgChannelProductQueryRequest), JSONObject.toJSONString(sgStoragePageRequest));
        }

        holder = checkParam(sgChannelProductQueryRequest);
        if (!holder.isOK()) {
            return holder;
        }

        List<String> skuEcodeList = sgChannelProductQueryRequest.getSkuEcodeList();
        List<String> skuIdList = sgChannelProductQueryRequest.getSkuIdList();
        List<String> numiidList = sgChannelProductQueryRequest.getNumiidList();
        Long cpCShopId = sgChannelProductQueryRequest.getCpCShopId();

        List<SgBChannelProduct> resultList = Lists.newArrayList();
        if (sgStoragePageRequest == null) {
            /** 全量查询 **/
            LambdaQueryWrapper<SgBChannelProduct> lambda = new QueryWrapper<SgBChannelProduct>().lambda();
            if (CollectionUtils.isNotEmpty(skuEcodeList)) {
                if (skuEcodeList.size() == 1) {
                    lambda.likeRight(SgBChannelProduct::getPsCSkuEcode, skuEcodeList.get(0));
                } else {
                    lambda.in(SgBChannelProduct::getPsCSkuEcode, skuEcodeList);
                }
            }
            if (CollectionUtils.isNotEmpty(skuEcodeList) && CollectionUtils.isNotEmpty(skuIdList)) {
                lambda.or();
            }
            if (CollectionUtils.isNotEmpty(skuIdList)) {
                if (skuIdList.size() == 1) {
                    lambda.likeRight(SgBChannelProduct::getSkuId, skuIdList.get(0));
                } else {
                    lambda.in(SgBChannelProduct::getSkuId, skuIdList);
                }
            }
            if (CollectionUtils.isNotEmpty(skuIdList) && CollectionUtils.isNotEmpty(numiidList)) {
                lambda.or();
            }
            if (CollectionUtils.isNotEmpty(numiidList)) {
                if (numiidList.size() == 1) {
                    lambda.likeRight(SgBChannelProduct::getNumiid, numiidList.get(0));
                } else {
                    lambda.in(SgBChannelProduct::getNumiid, numiidList);
                }
            }
            if (Objects.nonNull(cpCShopId)){
                lambda.eq(SgBChannelProduct::getCpCShopId, cpCShopId);
            }
            resultList = sgBChannelProductMapper.selectList(lambda);
            holder.setData(resultList);
        } else {
            /** 分页查询 **/
            PageHelper.startPage(sgStoragePageRequest.getPageNum(), sgStoragePageRequest.getPageSize());
            LambdaQueryWrapper<SgBChannelProduct> lambda = new QueryWrapper<SgBChannelProduct>().lambda();
            if (CollectionUtils.isNotEmpty(skuEcodeList)) {
                if (skuEcodeList.size() == 1) {
                    lambda.likeRight(SgBChannelProduct::getPsCSkuEcode, skuEcodeList.get(0));
                } else {
                    lambda.in(SgBChannelProduct::getPsCSkuEcode, skuEcodeList);
                }
            }
            if (CollectionUtils.isNotEmpty(skuEcodeList) && CollectionUtils.isNotEmpty(skuIdList)) {
                lambda.or();
            }
            if (CollectionUtils.isNotEmpty(skuIdList)) {
                if (skuIdList.size() == 1) {
                    lambda.likeRight(SgBChannelProduct::getSkuId, skuIdList.get(0));
                } else {
                    lambda.in(SgBChannelProduct::getSkuId, skuIdList);
                }
            }
            if (CollectionUtils.isNotEmpty(skuIdList) && CollectionUtils.isNotEmpty(numiidList)) {
                lambda.or();
            }
            if (CollectionUtils.isNotEmpty(numiidList)) {
                if (numiidList.size() == 1) {
                    lambda.likeRight(SgBChannelProduct::getNumiid, numiidList.get(0));
                } else {
                    lambda.in(SgBChannelProduct::getNumiid, numiidList);
                }
            }
            if (Objects.nonNull(cpCShopId)){
                lambda.eq(SgBChannelProduct::getCpCShopId, cpCShopId);
            }
            resultList = sgBChannelProductMapper.selectList(lambda);

            PageInfo<SgBChannelProduct> resultPage = new PageInfo<>(resultList);
            holder.setData(resultPage.getList());
        }

        if (log.isDebugEnabled()) {
            log.debug("Finish SgChannelProductQueryService.fuzzyQueryChannelProductByPage. Return={};",
                    JSONObject.toJSONString(holder));
        }
        return holder;
    }

    /**
     * 渠道商品页面配置查询
     *
     * @param map
     * @return
     */
    public List<HashMap<String, Object>> queryChannelProductByPageLayout(HashMap map) {
        SgCommonTableMapper sgCommonTableMapper = ApplicationContextHandle.getBean(SgCommonTableMapper.class);
        JSONObject jsonObject = (JSONObject) map.get(R3ParamConstants.DATA);
        return sgCommonTableMapper.listByCondition(jsonObject);
    }

    /**
     * 库存策略根据条件查询渠道商品
     *
     * @param sgChannelProductQueryForSTRequest
     * @return
     */
    public List<SgChannelProductQueryForSTResult> queryChannelProductForST(
            SgChannelProductQueryForSTRequest sgChannelProductQueryForSTRequest) {
        return sgBChannelProductMapper.queryChannelProductForST(sgChannelProductQueryForSTRequest);
    }

    /**
     * 库存策略根据条件查询渠道商品总数
     *
     * @param sgChannelProductQueryForSTRequest
     * @return
     */
    public Integer queryChannelProductCountForST(
            SgChannelProductQueryForSTRequest sgChannelProductQueryForSTRequest) {
        return sgBChannelProductMapper.queryChannelProductCountForST(sgChannelProductQueryForSTRequest);
    }

    /**
     * 查询同城购商品
     *
     * @param request 同城购商品查询请求对象
     * @return 结果对象
     */
    public ValueHolderV14<List<SgSameCityDetrimentItemResult>> querySameCityPurchaseProduct(SgSameCityDetrimentQueryRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("o2o同城购商品查询-querySameCityPurchaseProduct请求：{}", JSONObject.toJSONString(request));
        }
        ValueHolderV14 v14 = new ValueHolderV14();
        try {
            CpShop shop = checkSameCityPurchaseRequest(request);
            LambdaQueryWrapper<SgBChannelProduct> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SgBChannelProduct::getCpCShopId, shop.getId());
            wrapper.eq(SgBChannelProduct::getSendStatus, SgConstants.IS_YES_OR_NO_Y);
            wrapper.eq(StringUtils.isNotBlank(request.getProductCode()),
                    SgBChannelProduct::getPsCProEcode, request.getProductCode());
            List<SgBChannelProduct> products = sgBChannelProductMapper.selectList(wrapper);
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("查询同城购商品成功！");
            List<SgSameCityDetrimentItemResult> items = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(products)) {
                products.forEach(p -> {
                    // 这里返回平台的商品ID以及对应系统中的商品款号
                    SgSameCityDetrimentItemResult item = new SgSameCityDetrimentItemResult();
                    item.setItemId(p.getNumiid());
                    item.setItemCode(p.getPsCProEcode());
                    items.add(item);
                });
            }
            v14.setData(items);
        } catch (Exception e) {
            log.error("SgChannelProductQueryService.querySameCityPurchaseProduct. exception_has_occured:{}",
                    Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("查询同城购商品异常：" + e.getMessage());
        }
        return v14;
    }

    /**
     * 校验同城购请求参数对象
     *
     * @param request 请求参数对象
     * @return 店铺信息
     */
    private CpShop checkSameCityPurchaseRequest(SgSameCityDetrimentQueryRequest request) {
        AssertUtils.notNull(request, "请求对象为空！");
        AssertUtils.notBlank(request.getShopCode(), "店铺编码不能为空！");
        AssertUtils.isTrue("MDFH".equals(request.getTagType()), "不被支持的查询标记类型：" + request.getTagType());
        CpShopQueryRequest shopRequest = new CpShopQueryRequest();
        List<String> shopCodes = new ArrayList<>();
        shopCodes.add(request.getShopCode());
        shopRequest.setShopCodes(shopCodes);
        List<CpShop> shopList = shopQueryCmd.queryShop(shopRequest);
        AssertUtils.notEmpty(shopList, "店铺编码查询不到店铺信息！");
        return shopList.get(0);
    }


    /**
     * 平台店铺商品查询（为OMS->CRM查询）
     *
     * @param request
     * @return
     */
    public ValueHolderV14<PageInfo<SgBChannelProduct>> queryChannelProductByUpdateTime(SgChannelProductQueryByUpdateTimeRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("OMS->CRM平台店铺商品查询-queryChannelProductByUpdateTime：{}", JSONObject.toJSONString(request));
        }
        ValueHolderV14 v14 = new ValueHolderV14();
        try {
            AssertUtils.notNull(request, "请求对象为空！");
            AssertUtils.notNull(request.getPageNum(), "PageNum不能为空！");
            AssertUtils.notNull(request.getPageSize(), "PageSize不能为空！");
            if (request.getModifieddate() == null) {
                Date yesterday = DateUtils.addDays(new Date(), -1);
                request.setModifieddate(yesterday);
            }
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            String dateStr = format.format(request.getModifieddate());
            LambdaQueryWrapper<SgBChannelProduct> wrapper = new LambdaQueryWrapper<>();
            if (!request.getFullData()) {
                wrapper.apply("to_char(modifieddate,'yyyy-MM-dd')={0}", dateStr);
                wrapper.eq(Objects.nonNull(request.getShopId()),
                        SgBChannelProduct::getCpCShopId, request.getShopId());
            }
            PageHelper.startPage(request.getPageNum(), request.getPageSize());
            List<SgBChannelProduct> products = sgBChannelProductMapper.selectList(wrapper);
            PageInfo pageInfo = new PageInfo(products);
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("查询平台店铺商品成功！");
            v14.setData(pageInfo);
        } catch (Exception e) {
            log.error("SgChannelProductQueryService.queryChannelProductByUpdateTime. exception_has_occured:{}",
                    Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("OMS->CRM平台店铺商品查询：" + e.getMessage());
        }
        return v14;
    }


    /**
     * <ul>
     *     <li>
     *         <p/>根据店铺和商品条码查询商品
     *     </li>
     * </ul>
     *
     * @param sku  sku
     * @param shop 店铺
     * @return 返回符合条件的结果集
     */
    public ValueHolderV14<List<SgBChannelProduct>> queryBySkuAndShop(String sku, Long shop) {
        ValueHolderV14<List<SgBChannelProduct>> valueHolderV14 = new ValueHolderV14<>();
        try {
            List<SgBChannelProduct> channelProductList = sgBChannelProductMapper.selectBySkuAndShop(sku, shop);
            valueHolderV14.setData(channelProductList);
        } catch (Exception e) {
            log.error("按照商品条码和店铺查询渠道商品表失败 sku:{},shop:{},error:{}", sku, shop, Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("按照商品条码和店铺查询渠道商品表失败");
        }
        return valueHolderV14;
    }


    /**
     * 创建定时任务，N小时执行1次（可配置），物理删除修改时间为3个月前且【无需同步】标记为 是 的数据（时间可配置）
     */
    public Integer delChannelProductBySyncResult() {
        //Integer limit = propertiesConf.getProperty("r3.sg.oms.channelProduct.limit",500);
        Integer day = propertiesConf.getProperty("r3.sg.oms.channelProduct.day", 30);
        Date now = new Date();
        Date startDate = DateUtils.addDays(now, -day);
        return sgBChannelProductMapper.delChannelProductByDate(startDate);
    }

    /**
     * 根据平台id+平台sku查询平台店铺商品
     *
     * @param skuId
     * @param platform
     * @return
     */
    public ValueHolderV14<List<SgBChannelProduct>> queryByPlatSkuAndPlatform(String skuId,
                                                                             List<Integer> platform,
                                                                             List<Integer> excludePlatform) {

        ValueHolderV14<List<SgBChannelProduct>> holder = new ValueHolderV14<>(ResultCode.SUCCESS, "查询平台店铺商品成功！");

        if (log.isDebugEnabled()) {
            log.debug("Start SgChannelProductQueryService.queryByPlatSkuAndPlatform. ReceiveParams:platSku={},platform={},excludePlatform={}",
                    skuId, platform, excludePlatform);
        }

        if (StringUtils.isEmpty(skuId)) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("平台条码不能为空！");
            return holder;
        }

        if (CollectionUtils.isEmpty(platform) && CollectionUtils.isEmpty(excludePlatform)) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("指定店铺不能为空！");
            return holder;
        }

        try {

            List<SgBChannelProduct> channelProductList = sgBChannelProductMapper.selectList(
                    new QueryWrapper<SgBChannelProduct>().lambda()
                            .eq(SgBChannelProduct::getSkuId, skuId)
                            .in(CollectionUtils.isNotEmpty(platform), SgBChannelProduct::getCpCPlatformId, platform)
                            .notIn(CollectionUtils.isNotEmpty(excludePlatform), SgBChannelProduct::getCpCPlatformId, excludePlatform)
            );

            if (!CollectionUtils.isEmpty(channelProductList)) {
                for (SgBChannelProduct sgBChannelProduct : channelProductList) {
                    //无需同步改为是
                    sgBChannelProduct.setReserveVarchar07("1");
                    sgBChannelProduct.setModifieddate(new Date());
                    sgBChannelProductMapper.updateById(sgBChannelProduct);
                }
            }

            holder.setData(channelProductList);

        } catch (Exception e) {
            log.error("按照平台条码和平台查询渠道商品失败! skuId:{},platform:{},excludePlatform:{},error:{}",
                    skuId, platform, excludePlatform, Throwables.getStackTraceAsString(e));

            holder.setCode(ResultCode.FAIL);
            holder.setMessage("按照商品条码和店铺查询渠道商品失败!");
        }

        return holder;
    }

    /**
     * @param request:
     * @Description: 平台商品管理列表查询
     * @Author: hwy
     * @Date: 2021/8/14 16:48
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14<com.burgeon.r3.sg.channel.model.result.product.SgBChannelProductDevOpsQueryResult>
     **/
    @TargetDataSource(name = "adb")
    public ValueHolderV14<SgBChannelProductDevOpsQueryResult> queryChannelProductDevOpsInfoList(SgBChannelProductDevOpsQueryRequest request) {
        long start = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug(" SgChannelProductQueryService.queryChannelProductDevOpsInfoList Start Param:{}", JSONObject.toJSONString(request));
        }
        ValueHolderV14<SgBChannelProductDevOpsQueryResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        try {
            SgBChannelProductDevOpsQueryResult devOpsQueryResult = new SgBChannelProductDevOpsQueryResult();
            checkParam(request);
            Integer count = sgBChannelProductMapper.selectDevOpsInfoCount(request, adbSchema);
            if (count > 0) {
                List<SgBChannelProductDevOpsQueryInfoResult> sgBChannelProductDevOpsQueryInfoResults = sgBChannelProductMapper.selectDevOpsInfo(request, adbSchema);
                if (CollectionUtils.isNotEmpty(sgBChannelProductDevOpsQueryInfoResults)) {
                    // 获取店铺的渠道类型 channel
                    Long shopId = sgBChannelProductDevOpsQueryInfoResults.get(0).getCpCShopId();
                    List<CpShop> shopList = shopQueryCmd.queryShopByIds(Collections.singletonList(shopId));
                    AssertUtils.isNotEmpty("店铺id[" + shopId + "]查询不到店铺信息,请检查", shopList);
                    Integer shopChannel = shopList.get(0).getChannel();
                    //批量查询配销仓的可用量, 店铺id 固定
                    List<Long> psCSkuIds = sgBChannelProductDevOpsQueryInfoResults.stream().map(SgBChannelProductDevOpsQueryInfoResult::getPsCSkuId).distinct().collect(Collectors.toList());
                    List<SgBSaStorageResult> saStorageResults = storageMapper.batchQueryStorage(shopId, psCSkuIds, adbSchema);
                    //计算当前商品的库存是否是否已经在计算中
                    final List<SgBStorageBufferDto> params = sgBChannelProductDevOpsQueryInfoResults.stream().map(result -> SgBStorageBufferDto.builder()
                            .shopId(result.getCpCShopId())
                            .skuId(result.getSkuId())
                            .build()).collect(Collectors.toList());
                    SgBChannelStorageBufferCmd storageBufferCmd = ApplicationContextHandle.getBean("sgBChannelStorageBufferCmdImpl", SgBChannelStorageBufferCmd.class);
                    List<SgBChannelStorageBuffer> bufferList = storageBufferCmd.queryByShopIdAndSkuId(params);
                    sgBChannelProductDevOpsQueryInfoResults.forEach(res -> {
                        if (CollectionUtils.isNotEmpty(bufferList)) {
                            boolean flag = bufferList.stream().anyMatch(buffer -> buffer.getCpCShopId().equals(res.getCpCShopId()) && buffer.getSkuId().equals(res.getSkuId()));
                            if (flag) {
                                res.setCalcStatus(CalcStatusEnum.CALCING.getCode());
                            }
                        }
                        //设置配销仓数量
                        if (CollectionUtils.isNotEmpty(saStorageResults)) {
                            BigDecimal qty = saStorageResults.stream()
                                    .filter(s -> s.getSaStoreType().equals(res.getSaStoreType()) && res.getPsCSkuId().equals(s.getPsCSkuId()))
                                    .map(SgBSaStorageResult::getQtyAvailable).reduce(BigDecimal.ZERO, BigDecimal::add);
                            res.setQtyReal(qty);
                            if (Objects.nonNull(shopChannel)) {
                                //设置 渠道内（店铺所属渠道 = 配销仓所属渠道）配销仓库存    和  渠道外（店铺所属渠道 != 配销仓所属渠道）配销仓库存
                                BigDecimal saQtyInChannel = saStorageResults.stream()
                                        .filter(s -> s.getSaStoreType().equals(res.getSaStoreType())
                                                && res.getPsCSkuId().equals(s.getPsCSkuId())
                                                && shopChannel.equals(s.getSaChannel()))
                                        .map(SgBSaStorageResult::getQtyAvailable).reduce(BigDecimal.ZERO, BigDecimal::add);
                                res.setSaQtyInChannel(saQtyInChannel);
                                res.setSaQtyOutChannel(res.getQtyReal().subtract(saQtyInChannel));
                            } else {
                                res.setSaQtyOutChannel(qty);
                                res.setSaQtyInChannel(BigDecimal.ZERO);
                            }
                        }
                    });
                }
                devOpsQueryResult.setProductList(sgBChannelProductDevOpsQueryInfoResults);
            }
            devOpsQueryResult.setTotalRowCount(count);
            valueHolderV14.setData(devOpsQueryResult);
        } catch (Exception e) {
            log.error("平台商品管理列表查询 失败：{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("平台商品管理列表查询失败" +  Throwables.getStackTraceAsString(e));
        }
        log.info(" queryChannelProductDevOpsInfoList.valueHolderV14 {}", JSON.toJSONString(valueHolderV14));
        return valueHolderV14;
    }

    /**
     * @param request:
     * @Description: 平台商品管理列表查询入参检查
     * @Author: hwy
     * @Date: 2021/8/14 16:48
     * @return: void
     **/
    private void checkParam(SgBChannelProductDevOpsQueryRequest request) {
        if (request == null) {
            request = new SgBChannelProductDevOpsQueryRequest();
            request.setRange(10);
        }
        if (request.getStartindex() == null) {
            request.setStartindex(0);
        }
        if (request.getRange() == null) {
            request.setRange(10);
        }
        List<String> brandLabels = request.getBrandLabels();
        if (CollectionUtils.isNotEmpty(brandLabels) && brandLabels.contains("3")) {
            request.setIsTmllCity("Y");
            if (brandLabels.size() == 1) {
                request.setBrandLabels(null);
            }
        }
    }

    public ValueHolderV14<List<SgBChannelProduct>> selectByShopIdAndNumiid(Long shopId, List<String> numiids) {
        ValueHolderV14<List<SgBChannelProduct>> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        List<SgBChannelProduct> products = sgBChannelProductMapper.selectByShopIdAndNumiid(shopId, numiids);
        valueHolderV14.setData(products);
        return valueHolderV14;
    }

}
