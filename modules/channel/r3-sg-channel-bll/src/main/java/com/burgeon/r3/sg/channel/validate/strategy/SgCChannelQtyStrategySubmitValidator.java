package com.burgeon.r3.sg.channel.validate.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyItemMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelQtyStrategyDTO;
import com.burgeon.r3.sg.core.model.table.channel.qtystrategy.SgCChannelQtyStrategyItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lishijun
 * @Date:
 * @Description:
 */
@Component
public class SgCChannelQtyStrategySubmitValidator extends BaseSingleValidator<SgCChannelQtyStrategyDTO> {

    @Autowired
    private SgCChannelQtyStrategyItemMapper qtyStrategyItemMapper;

    @Override
    public String getValidatorMsgName() {
        return "数量同步策略审核";
    }

    @Override
    public Class getValidatorClass() {
        return SgCChannelQtyStrategySubmitValidator.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCChannelQtyStrategyDTO qtyStrategyDTO, User user) {
        // 判断id 不能为空
        SgCChannelQtyStrategyDTO data = getOrignalData();

        // 删除数量为0 的数据
        LambdaQueryWrapper<SgCChannelQtyStrategyItem> itemDeleteWrapper = new LambdaQueryWrapper<>();
        itemDeleteWrapper.eq(SgCChannelQtyStrategyItem::getSgCChannelQtyStrategyId, data.getId());
        itemDeleteWrapper.eq(SgCChannelQtyStrategyItem::getQty, 0);
        qtyStrategyItemMapper.delete(itemDeleteWrapper);

        // 如果明细为空，则提示失败：当前记录无明细，不允许审核！
        LambdaQueryWrapper<SgCChannelQtyStrategyItem> itemQueryWrapper = new LambdaQueryWrapper<>();
        itemQueryWrapper.eq(SgCChannelQtyStrategyItem::getSgCChannelQtyStrategyId, data.getId());
        List<SgCChannelQtyStrategyItem> itemList = qtyStrategyItemMapper.selectList(itemQueryWrapper);
        if (CollectionUtils.isEmpty(itemList)) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("当前记录无明细，不允许审核！"));
        }

        // 对现有明细，做调整数量校验
        for (SgCChannelQtyStrategyItem item : itemList) {
            if (BigDecimal.ZERO.compareTo(item.getQty()) > 0) {
                // TODO 获取条码剩余数，qty 的绝对值，不能大于剩余数，否则返回失败：当前条码XXX 调整数量 A，剩余数 Y，调整后会导致剩余数小于0，不允许！

            }
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
    }

}