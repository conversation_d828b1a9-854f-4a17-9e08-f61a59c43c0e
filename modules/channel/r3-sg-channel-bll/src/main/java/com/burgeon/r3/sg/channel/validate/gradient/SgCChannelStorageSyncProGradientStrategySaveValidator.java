package com.burgeon.r3.sg.channel.validate.gradient;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelStorageSyncProGradientStrategyMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelStorageSyncProGradientStrategyDTO;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.gradient.SgCChannelStorageSyncProGradientStrategy;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.ps.api.result.PsSkuResult;
import com.jackrain.nea.ps.api.table.ProSku;
import com.jackrain.nea.ps.api.table.PsCSku;
import com.jackrain.nea.ps.api.table.PsCSkuExt;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 平台库存同步梯度策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SgCChannelStorageSyncProGradientStrategySaveValidator extends BaseSingleValidator<SgCChannelStorageSyncProGradientStrategyDTO> {

    @Autowired
    private SgCChannelStorageSyncProGradientStrategyMapper mapper;

    @Override
    public String getValidatorMsgName() {
        return "平台库存同步梯度策略保存";
    }

    @Override
    public Class<?> getValidatorClass() {
        return SgCChannelStorageSyncProGradientStrategySaveValidator.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCChannelStorageSyncProGradientStrategyDTO mainObject, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过", loginUser.getLocale()));

        // 新增时
        if (Objects.isNull(mainObject.getId()) || mainObject.getId() < 1L) {
            if (Objects.isNull(mainObject.getPsCProId()) && Objects.isNull(mainObject.getPsCSkuId())) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("商品信息为空，不允许保存！"));
                return vh;
            }

            // 以条码维度
            if (Objects.nonNull(mainObject.getPsCSkuId())) {
                PsCProSkuResult skuInfo = CommonCacheValUtils.getSkuInfo(mainObject.getPsCSkuId());
                if (Objects.isNull(skuInfo)) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(Resources.getMessage("未查询到条码信息！"));
                    return vh;
                }
                if (Objects.nonNull(mainObject.getPsCProId())) {
                    if (!mainObject.getPsCProId().equals(skuInfo.getPsCProId())) {
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage(Resources.getMessage("条码编码与商品编码不匹配，不允许保存！"));
                        return vh;
                    }
                }

                // 判断是否有配置商品的数据
                LambdaQueryWrapper<SgCChannelStorageSyncProGradientStrategy> countWrapper = new LambdaQueryWrapper<>();
                countWrapper.eq(SgCChannelStorageSyncProGradientStrategy::getCpCShopId, mainObject.getCpCShopId());
                countWrapper.eq(SgCChannelStorageSyncProGradientStrategy::getPsCProId, skuInfo.getPsCProId());
                countWrapper.isNull(SgCChannelStorageSyncProGradientStrategy::getPsCSkuId);
                countWrapper.eq(SgCChannelStorageSyncProGradientStrategy::getIsactive, SgConstants.IS_ACTIVE_Y);
                Integer count = mapper.selectCount(countWrapper);
                if (count > 0) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(Resources.getMessage("存在商品编码记录"));
                    return vh;
                }
            } else {
                // 以商品维度配置
                List<PsCSku> skuList = CommonCacheValUtils.getSkuInfoListByProId(mainObject.getPsCProId());
                if (CollectionUtils.isEmpty(skuList)) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(Resources.getMessage("未查询到商品对应条码信息"));
                    return vh;
                }
                List<Long> skuIds = skuList.stream().map(PsCSku::getPsCSkuId).collect(Collectors.toList());

                LambdaQueryWrapper<SgCChannelStorageSyncProGradientStrategy> countWrapper = new LambdaQueryWrapper<>();
                countWrapper.eq(SgCChannelStorageSyncProGradientStrategy::getCpCShopId, mainObject.getCpCShopId());
                countWrapper.in(SgCChannelStorageSyncProGradientStrategy::getPsCSkuId, skuIds);
                countWrapper.eq(SgCChannelStorageSyncProGradientStrategy::getIsactive, SgConstants.IS_ACTIVE_Y);
                Integer count = mapper.selectCount(countWrapper);
                if (count > 0) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(Resources.getMessage("存在条码编码记录"));
                    return vh;
                }
            }
        }

        if (Objects.nonNull(mainObject.getQtyBegin()) && mainObject.getQtyBegin().compareTo(BigDecimal.ZERO) < 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("库存开始量不能小于0！"));
            return vh;
        }

        if (Objects.nonNull(mainObject.getQtyStandard()) && mainObject.getQtyStandard().compareTo(BigDecimal.ZERO) < 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("标准库存量不可为负数！"));
            return vh;
        }

//        if (Objects.nonNull(mainObject.getRatio()) && (mainObject.getRatio().compareTo(BigDecimal.ZERO) <= 0 || mainObject.getRatio().compareTo(BigDecimal.valueOf(100)) >= 0)) {
        if (Objects.nonNull(mainObject.getRatio()) && (mainObject.getRatio().compareTo(BigDecimal.ZERO) < 0)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("比例需要大于0！"));
            return vh;
        }

        SgCChannelStorageSyncProGradientStrategyDTO oldData = getOrignalData();

        Long shopId = Objects.isNull(mainObject.getCpCShopId()) ? oldData.getCpCShopId() : mainObject.getCpCShopId();
        Long skuId = Objects.isNull(mainObject.getPsCSkuId()) ? oldData.getPsCSkuId() : mainObject.getPsCSkuId();
        Long proId = Objects.isNull(mainObject.getPsCProId()) ? oldData.getPsCProId() : mainObject.getPsCProId();
        BigDecimal qtyBegin = Objects.isNull(mainObject.getQtyBegin()) ? oldData.getQtyBegin() : mainObject.getQtyBegin();
        BigDecimal qtyEnd = Objects.isNull(mainObject.getQtyEnd()) ? oldData.getQtyEnd() : mainObject.getQtyEnd();

        if (Objects.nonNull(qtyEnd) && qtyEnd.compareTo(qtyBegin) <= 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("库存开始量大于库存结束量，不允许保存！"));
            return vh;
        }

        LambdaQueryWrapper<SgCChannelStorageSyncProGradientStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SgCChannelStorageSyncProGradientStrategy::getCpCShopId, shopId);

        if (Objects.nonNull(skuId)) {
            wrapper.eq(SgCChannelStorageSyncProGradientStrategy::getPsCSkuId, skuId);
        } else {
            wrapper.eq(SgCChannelStorageSyncProGradientStrategy::getPsCProId, proId);
        }
        wrapper.ne(Objects.nonNull(mainObject.getId()), SgCChannelStorageSyncProGradientStrategy::getId, mainObject.getId());
        wrapper.eq(SgCChannelStorageSyncProGradientStrategy::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgCChannelStorageSyncProGradientStrategy> list = mapper.selectList(wrapper);

        if (!CollectionUtils.isEmpty(list)) {
            long count = list.stream().filter(x -> (Objects.isNull(qtyEnd) && Objects.isNull(x.getQtyEnd()))
                    || (qtyBegin.compareTo(x.getQtyBegin()) >= 0 && (Objects.isNull(x.getQtyEnd()) || x.getQtyEnd().compareTo(qtyBegin) >= 0))
                    || (Objects.nonNull(qtyEnd) && qtyEnd.compareTo(x.getQtyBegin()) >= 0 && (Objects.isNull(x.getQtyEnd()) || qtyEnd.compareTo(x.getQtyEnd()) < 0))).count();
            if (count > 0) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("库存起始量与其他记录重复，不允许保存！"));
                return vh;
            }
        }

        return vh;
    }
}
