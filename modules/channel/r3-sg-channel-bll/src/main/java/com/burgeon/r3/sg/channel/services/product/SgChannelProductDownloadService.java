package com.burgeon.r3.sg.channel.services.product;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductDownloadBufferMapper;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.model.enumerate.SaleStatusEnumeration;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductDownloadRequest;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductQueryRequest;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProductDownloadBuffer;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ip.api.taobao.IpBTaobaoQueryItemSkuCmd;
import com.jackrain.nea.ip.model.taobao.IpTaobaoItemSkuRequestModel;
import com.jackrain.nea.ip.model.taobao.SellerItemSku;
import com.jackrain.nea.ip.model.taobao.SellerListResponseModel;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/17 14:21
 */
@Slf4j
@Component
public class SgChannelProductDownloadService {

    @Autowired
    private SgBChannelProductMapper sgChannelProductMapper;

    @DubboReference(group = "ip", version = "1.4.0")
    private IpBTaobaoQueryItemSkuCmd ipTaobaoQueryItemSkuCmd;

    @Value("${sgChannelProduct.task.download.productNum:20}")
    private int downloadProductNum;

    @Value("#{'${sgChannelProduct.task.download.platformIds:2}'.split(',')}")
    private List<Integer> downloadPlatformIds;

    @Autowired
    private SgBChannelProductDownloadBufferMapper sgChannelProductDownloadBufferMapper;

    /**
     * 商品下载状态
     * 下载状态:-1 失败;0 未下载；1 下载中; 2 成功
     */
    private static final Integer DOWNLOAD_FAIL = -1;
    private static final Integer DOWNLOAD_UN = 0;
    private static final Integer DOWNLOAD_ING = 1;
    private static final Integer DOWNLOAD_SUCCESS = 2;

    /**
     * 查询平台商品信息 插入下载平台商品计算缓存池
     *
     * @param requests 请求request
     * @return ValueHolderV14
     */
    public ValueHolderV14 downloadProduct(List<SgChannelProductDownloadRequest> requests) {
        if (log.isDebugEnabled()) {
            log.debug(" Start SgChannelProductDownloadService.threadTask ReceiveParams:request:{}",
                    JSONObject.toJSONString(requests));
        }
        Long start = System.currentTimeMillis();
        ValueHolderV14 vh = new ValueHolderV14<>(ResultCode.SUCCESS, "下载平台条码库存成功!");
        try {
            SgChannelProductDownloadService service =
                    ApplicationContextHandle.getBean(SgChannelProductDownloadService.class);
            for (SgChannelProductDownloadRequest request : requests) {
                service.downloadAllShopSaleStorage(request);
            }

            //            initializeProductsByPlatformIds(request, user);
            //            List<SgBChannelProduct> channelProducts;
            //            for (Long platformId : request.getCpCPlatformId()) {
            //                do {
            //                    channelProducts = sgChannelProductMapper.queryDownloadProduct(downloadProductNum,
            //                    platformId,
            //                            request.getDownloadShopIds());
            //                    if (CollectionUtils.isEmpty(channelProducts)) {
            //                        break;
            //                    }
            //                    List<String> numiids =
            //                            channelProducts.stream().map(SgBChannelProduct::getNumiid).collect
            //                            (Collectors.toList());
            //                    List<List<String>> initializeByNumiids = StorageUtils.getPageList(numiids,
            //                            SgConstants.SG_COMMON_STRING_SHORT_SIZE);
            //                    for (List<String> initializeByNumiid : initializeByNumiids) {
            //                        initializeDownloadProducts(initializeByNumiid);
            //                    }
            //                    //拉取的平台条码库存信息
            //                    List<SgBChannelProduct> updateProducts = downloadProducts(channelProducts);
            //                    if (CollectionUtils.isNotEmpty(updateProducts)) {
            //                        Date nowDate = new Date();
            //                        List<List<SgBChannelProduct>> pageList = StorageUtils.getPageList
            //                                (updateProducts,
            //                                        SgConstants.SG_COMMON_MAX_QUERY_PAGE_SIZE);
            //                        for (List<SgBChannelProduct> products : pageList) {
            //                            updateDownloadProducts(products, nowDate);
            //                        }
            //                    }
            //                }
            //                while (CollectionUtils.isNotEmpty(channelProducts));
            //            }
        } catch (Exception e) {
            log.error(" SgChannelProductDownloadService.fail {}", Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("下载平台条码库存失败!失败原因:" + e.getMessage());
        }
        Long end = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug(" SgChannelProductDownloadService.downloadProduct.result={},耗时:{}ms",
                    JSONObject.toJSONString(vh), end - start);
        }
        return vh;
    }

    /**
     * 查询符合平台类型的商品数据 更新状态未下载
     *
     * @param request 请求参数
     * @param user    用户
     */
    private void initializeProductsByPlatformIds(SgChannelProductQueryRequest request, User user) {
        List<SgBChannelProduct> queryProducts =
                sgChannelProductMapper.selectList(new LambdaQueryWrapper<SgBChannelProduct>()
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                        .eq(SgBChannelProduct::getSaleStatus, SaleStatusEnumeration.SALE.getCode())
                        .in(CollectionUtils.isNotEmpty(request.getCpCPlatformId()), SgBChannelProduct::getCpCPlatformId,
                                request.getCpCPlatformId()));
        if (CollectionUtils.isNotEmpty(queryProducts)) {
            List<Long> updateIds = queryProducts.stream().map(SgBChannelProduct::getId).collect(Collectors.toList());
            SgBChannelProduct channelProduct = new SgBChannelProduct();
            StorageUtils.setBModelDefalutDataByUpdate(channelProduct, user);
            //根据传入的平台类型id 更新符合条件的数据 下载状态=未下载
            channelProduct.setDownloadStatus(DOWNLOAD_UN);

            List<List<Long>> updateIdsPartition = Lists.partition(updateIds,
                    SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
            SgChannelProductDownloadService service =
                    ApplicationContextHandle.getBean(SgChannelProductDownloadService.class);
            for (List<Long> ids : updateIdsPartition) {
                //根据id更新 防止死锁
                service.updateProductById(ids, channelProduct);
            }
        }
    }

    /**
     * 分页并更新 普通商品维度
     *
     * @param numiids 更新数据
     */
    private void initializeDownloadProducts(List<String> numiids) {
        if (CollectionUtils.isNotEmpty(numiids)) {
            SgBChannelProduct channelProduct = new SgBChannelProduct();
            //根据传入的平台类型id 更新符合条件的数据 下载状态
            channelProduct.setDownloadStatus(DOWNLOAD_ING);
            channelProduct.setDownloadFailReason("");

            List<SgBChannelProduct> updateProducts =
                    sgChannelProductMapper.selectList(new LambdaUpdateWrapper<SgBChannelProduct>()
                            .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                            .eq(SgBChannelProduct::getSaleStatus, SaleStatusEnumeration.SALE.getCode())
                            .in(SgBChannelProduct::getNumiid, numiids));
            if (CollectionUtils.isNotEmpty(updateProducts)) {
                List<Long> updateIds =
                        updateProducts.stream().map(SgBChannelProduct::getId).collect(Collectors.toList());
                List<List<Long>> updateIdsPartition = Lists.partition(updateIds,
                        SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
                SgChannelProductDownloadService service =
                        ApplicationContextHandle.getBean(SgChannelProductDownloadService.class);
                for (List<Long> ids : updateIdsPartition) {
                    //根据id更新 防止死锁
                    service.updateProductById(ids, channelProduct);
                }
            }
        }
    }

    /**
     * rpc获取平台条码库存
     *
     * @param updateList 需要更新的平台商品数据
     * @return List<SgBChannelProduct>
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public List<SgBChannelProduct> downloadProducts(List<SgBChannelProduct> updateList) {
        //收集获取的平台条码库存信息
        List<SgBChannelProduct> updateProducts = new ArrayList<>();
        //收集从平台下载无条码信息的平台商品
        List<String> negationNumid = new ArrayList<>();
        //根据店铺分组
        Map<Long, List<SgBChannelProduct>> shopProductMap =
                updateList.stream().collect(Collectors.groupingBy(SgBChannelProduct::getCpCShopId));
        Collection<List<SgBChannelProduct>> values = shopProductMap.values();
        for (List<SgBChannelProduct> value : values) {
            Long cpShopId = value.get(0).getCpCShopId();
            List<String> numiidList = value.stream().map(SgBChannelProduct::getNumiid).collect(Collectors.toList());
            IpTaobaoItemSkuRequestModel requestModel = new IpTaobaoItemSkuRequestModel();
            requestModel.setShopId(cpShopId);
            //同一店铺不同商品逗号隔开调用rpc
            requestModel.setNumIids(String.join(",", numiidList));
            if (log.isDebugEnabled()) {
                log.debug(" Start SgChannelProductDownloadService.downloadProducts requestModel={}",
                        JSONObject.toJSONString(requestModel));
            }
            //rpc调用获取平台库存
            ValueHolderV14 rpcHolderV14 = ipTaobaoQueryItemSkuCmd.taobaoQueryItemList(requestModel);
            if (log.isDebugEnabled()) {
                log.debug(" end SgChannelProductDownloadService.downloadProducts rpcHolderV14.isOk={},have_data={}",
                        rpcHolderV14.getCode(), Objects.nonNull(rpcHolderV14.getData()));
            }
            if (rpcHolderV14.isOK()) {
                if (rpcHolderV14.getData() != null) {
                    List<SellerListResponseModel> data = (List<SellerListResponseModel>) rpcHolderV14.getData();
                    //收集平台库存信息
                    for (SellerListResponseModel datum : data) {
                        List<SellerItemSku> sellerItemSkus = datum.getSellerItemSkus();
                        //返回无条码信息
                        if (CollectionUtils.isEmpty(sellerItemSkus)) {
                            //根据平台商品查询表中有没有唯一对应数据
                            List<SgBChannelProduct> channelProducts =
                                    sgChannelProductMapper.selectList(new LambdaQueryWrapper<SgBChannelProduct>()
                                            .eq(SgBChannelProduct::getNumiid, String.valueOf(datum.getNumIid()))
                                            .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
                            if (CollectionUtils.isNotEmpty(channelProducts) && channelProducts.size() == 1) {
                                SgBChannelProduct product = new SgBChannelProduct();
                                product.setNumiid(channelProducts.get(0).getNumiid());
                                product.setSkuId(channelProducts.get(0).getSkuId());
                                product.setQtyStorage(new BigDecimal(datum.getNum()));
                                updateProducts.add(product);
                            } else {
                                negationNumid.add(String.valueOf(datum.getNumIid()));
                            }
                            continue;
                        }
                        for (SellerItemSku itemSkus : sellerItemSkus) {
                            SgBChannelProduct product = new SgBChannelProduct();
                            product.setNumiid(String.valueOf(datum.getNumIid()));
                            product.setSkuId(String.valueOf(itemSkus.getSkuId()));
                            product.setQtyStorage(new BigDecimal(itemSkus.getQuantity()));
                            updateProducts.add(product);
                        }
                    }
                } else {
                    negationNumid.addAll(numiidList);
                }
            } else {
                //调用接口返回失败 更新状态
                updateDownloadNumIdStatus(negationNumid, DOWNLOAD_FAIL, rpcHolderV14.getMessage());
            }
            //调用接口返回成功 却无条码 更新状态 (这种情况下需要查日志 是ip调用淘宝成功 淘宝无商品信息导致;还是ip调用淘宝失败  没有识别code状态,只返回成功)
            updateDownloadNumIdStatus(negationNumid, DOWNLOAD_SUCCESS, "调用下载平台商品库存成功,但无条码返回信息,请检查店铺平台类型或平台商品信息!");
        }
        return updateProducts;
    }

    /**
     * 更新同步状态 numid维度
     *
     * @param numidList      平台商品ids
     * @param downloadStatus 同步状态
     * @param failReason     失败原因
     */
    private void updateDownloadNumIdStatus(List<String> numidList, Integer downloadStatus, String failReason) {
        if (CollectionUtils.isEmpty(numidList)) {
            return;
        }
        //调用接口返回 更新状态  numid维度
        SgBChannelProduct channelProduct = new SgBChannelProduct();
        channelProduct.setDownloadStatus(downloadStatus);
        channelProduct.setDownloadFailReason(failReason);
        channelProduct.setTransTime(new Date());
        //查询所有平台商品id下的商品
        List<SgBChannelProduct> downloadProducts =
                sgChannelProductMapper.selectList(new LambdaQueryWrapper<SgBChannelProduct>()
                        .in(SgBChannelProduct::getNumiid, numidList)
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isEmpty(downloadProducts)) {
            return;
        }
        List<Long> updateIds =
                downloadProducts.stream().map(SgBChannelProduct::getId).collect(Collectors.toList());
        List<List<Long>> updateIdsPartition = Lists.partition(updateIds, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
        SgChannelProductDownloadService service =
                ApplicationContextHandle.getBean(SgChannelProductDownloadService.class);
        for (List<Long> ids : updateIdsPartition) {
            //根据id更新 防止死锁
            service.updateProductById(ids, channelProduct);
        }

    }

    /**
     * 批量更新
     *
     * @param products 条码库存信息集合
     */
    private void updateDownloadProducts(List<SgBChannelProduct> products, Date nowDate) {
        List<String> skuIdList = products.stream().map(SgBChannelProduct::getSkuId).collect(Collectors.toList());
        List<SgBChannelProduct> updateProductList =
                sgChannelProductMapper.selectList(new LambdaQueryWrapper<SgBChannelProduct>()
                        .in(SgBChannelProduct::getSkuId, skuIdList)
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isNotEmpty(updateProductList)) {
            Map<String, SgBChannelProduct> productMap = updateProductList.stream()
                    .collect(Collectors.toMap(SgBChannelProduct::getSkuId, Function.identity()));

            for (SgBChannelProduct product : products) {
                //获取主键id
                SgBChannelProduct sgChannelProduct = productMap.get(product.getSkuId());
                if (Objects.isNull(sgChannelProduct)) {
                    continue;
                }
                product.setId(sgChannelProduct.getId());
                product.setQtyDifferences(sgChannelProduct.getQtyChannel().subtract(product.getQtyStorage()));
                product.setTransTime(nowDate);
                product.setModifieddate(nowDate);
            }
            SgChannelProductDownloadService service =
                    ApplicationContextHandle.getBean(SgChannelProductDownloadService.class);
            service.updateDownloadQtyStorage(products);
        }
    }

    /**
     * 更新下载状态 和平台库存数
     *
     * @param products 平台库存信息
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateDownloadQtyStorage(List<SgBChannelProduct> products) {
        //根据id更新 防止死锁
        sgChannelProductMapper.updateDownloadProduct(products);
    }

    /**
     * 批量下载平台商品库存(对外接口)
     *
     * @param products 商品信息集合
     * @return sku&&库存数量
     */
    public List<SgBChannelProduct> externalDownloadProducts(List<SgBChannelProduct> products) {
        AssertUtils.cannot(CollectionUtils.isEmpty(products), "下载平台商品库存参数不能为空!");
        List<SgBChannelProduct> unPlatformProducts =
                products.stream().filter(p -> !downloadPlatformIds.contains(p.getCpCPlatformId())).collect(Collectors.toList());
        AssertUtils.cannot(CollectionUtils.isNotEmpty(unPlatformProducts), "当前店铺来源平台不允许下载平台商品库存!");
        //根据numid去重
        List<SgBChannelProduct> distinctProducts =
                products.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                        new TreeSet<>(Comparator.comparing(SgBChannelProduct::getNumiid))), ArrayList::new));

        if (log.isDebugEnabled()) {
            log.debug(" Start SgChannelProductDownloadService.externalDownloadProducts.downloadPlatformIds={}," +
                    "distinctProducts.size={}", JSONObject.toJSONString(downloadPlatformIds), distinctProducts.size());
        }
        List<SgBChannelProduct> resSgChannelProducts = new ArrayList<>();
        List<List<SgBChannelProduct>> pageList = StorageUtils.getPageList(distinctProducts, downloadProductNum);
        try {
            for (List<SgBChannelProduct> channelProducts : pageList) {
                List<SgBChannelProduct> updateProducts = downloadProducts(channelProducts);
                //收集拉取的平台条码库存信息
                if (CollectionUtils.isNotEmpty(updateProducts)) {
                    resSgChannelProducts.addAll(updateProducts);
                }
            }
        } catch (Exception e) {
            log.error(" SgChannelProductDownloadService.externalDownloadProducts.exception_has_occured:{}",
                    Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrowException(e, Locale.getDefault());
        }

        return resSgChannelProducts;
    }

    /**
     * 根据id更新 防止死锁
     *
     * @param updateIds      更新id、集合
     * @param channelProduct 更新内容
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateProductById(List<Long> updateIds, SgBChannelProduct channelProduct) {
        sgChannelProductMapper.update(channelProduct, new LambdaUpdateWrapper<SgBChannelProduct>()
                .in(SgBChannelProduct::getId, updateIds)
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
    }

    /**
     * 下载全店在售库存功能，将需要下载的商品插入缓存池 后台定时任务进行下载同步
     *
     * @param request 请求参数
     * @return ValueHolderV14
     */
    public ValueHolderV14 downloadAllShopSaleStorage(SgChannelProductDownloadRequest request) {
        log.info(" Start SgChannelProductDownloadService.downloadAllShopSaleStorage.request={}", request);
        Long shopId = request.getCpCShopId();
        User user = request.getLoginUser();
        Long platformId = request.getCpCPlatformId();
        if (Objects.isNull(shopId) && Objects.isNull(platformId)) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("参数缺失,请确认参数!", user.getLocale()));
        }

        //收集新增数据 插入缓存池
        List<SgBChannelProductDownloadBuffer> downloadBuffers =
                sgChannelProductMapper.queryNumIdsByShopIdAndPlatformId(request);
        if (CollectionUtils.isEmpty(downloadBuffers)) {
            return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("下载商品库存成功!", user.getLocale()));
        }
        downloadBuffers = downloadBuffers.stream().distinct().collect(Collectors.toList());
        for (SgBChannelProductDownloadBuffer buffer : downloadBuffers) {
            buffer.setId(ModelUtil.getSequence(SgConstants.SG_B_CHANNEL_PRODUCT_DOWNLOAD_BUFFER));
            StorageUtils.setBModelDefalutData(buffer, user);
        }
        //分批插入
        if (CollectionUtils.isNotEmpty(downloadBuffers)) {
            List<List<SgBChannelProductDownloadBuffer>> insertBufferLists = Lists.partition(downloadBuffers,
                    SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
            for (List<SgBChannelProductDownloadBuffer> insertBufferList : insertBufferLists) {
                //numid设置唯一索引 缓存池中存在数据则不插入新数据
                sgChannelProductDownloadBufferMapper.batchInsertDownloadBuffersOnConfilict(SgConstants.SG_B_CHANNEL_PRODUCT_DOWNLOAD_BUFFER, insertBufferList);
            }
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("下载商品库存成功!", user.getLocale()));
    }

    /**
     * 从缓存池拉取数据 下载平台商品库存
     *
     * @param platformIds 需要下载的店铺
     * @return ValueHolderV14
     */
    public ValueHolderV14 downloadByBuffer(List<Long> platformIds, List<Long> shopIds) {
        log.info(" Start SgChannelProductDownloadService.downloadByBuffer");
        Long start = System.currentTimeMillis();
        List<SgBChannelProductDownloadBuffer> downloadBufferList =
                sgChannelProductDownloadBufferMapper.queryNeedDownloadNumId(shopIds, platformIds, DOWNLOAD_UN,
                        SgConstants.SG_NORMAL_MAX_QUERY_PAGE_SIZE);
        if (CollectionUtils.isEmpty(downloadBufferList)) {
            return new ValueHolderV14<>(ResultCode.SUCCESS, "下载平台条码库存成功!");
        }
        //排序更新为下载中防止下次重复拉取
        downloadBufferList.sort(Comparator.comparingLong(SgBChannelProductDownloadBuffer::getId));
        SgBChannelProductDownloadBuffer updateStatusBuffer = new SgBChannelProductDownloadBuffer();
        updateStatusBuffer.setDownloadStatus(DOWNLOAD_ING);
        sgChannelProductDownloadBufferMapper.update(updateStatusBuffer,
                new LambdaUpdateWrapper<SgBChannelProductDownloadBuffer>()
                        .in(SgBChannelProductDownloadBuffer::getId, downloadBufferList.stream()
                                .map(SgBChannelProductDownloadBuffer::getId).collect(Collectors.toList())));

        SgChannelProductDownloadService service =
                ApplicationContextHandle.getBean(SgChannelProductDownloadService.class);

        List<List<SgBChannelProductDownloadBuffer>> partition = Lists.partition(downloadBufferList,
                downloadProductNum);
        for (List<SgBChannelProductDownloadBuffer> downloadBuffers : partition) {
            //20个numid一批获取平台商品库存
            service.downloadProductByBuffers(downloadBuffers);
        }

        Long end = System.currentTimeMillis();
        log.info(" SgChannelProductDownloadService.downloadByBuffer.time:{}ms", end - start);
        return new ValueHolderV14<>(ResultCode.SUCCESS, "下载平台条码库存成功!");
    }

    @Transactional(rollbackFor = Exception.class)
    public void downloadProductByBuffers(List<SgBChannelProductDownloadBuffer> downloadBuffers) {
        List<SgBChannelProduct> downloadProductList = new ArrayList<>();
        for (SgBChannelProductDownloadBuffer downloadBuffer : downloadBuffers) {
            SgBChannelProduct channelProduct = new SgBChannelProduct();
            channelProduct.setNumiid(downloadBuffer.getNumiid());
            channelProduct.setCpCShopId(downloadBuffer.getCpCShopId());
            downloadProductList.add(channelProduct);
        }
        //拉取的平台条码库存信息
        List<SgBChannelProduct> updateProducts = downloadProducts(downloadProductList);

        //为空说明无条码信息 在上一层已经更新numid维度的数据
        if (CollectionUtils.isNotEmpty(updateProducts)) {
            Date nowDate = new Date();
            List<List<SgBChannelProduct>> pageList = StorageUtils.getPageList
                    (updateProducts, SgConstants.SG_COMMON_UPDATE_PAGE_SIZE);
            //删除缓存池下载成功的数据
            sgChannelProductDownloadBufferMapper.deleteBatchIds(downloadBuffers.stream()
                    .map(SgBChannelProductDownloadBuffer::getId).collect(Collectors.toList()));
            for (List<SgBChannelProduct> products : pageList) {
                //更新平台店铺商品表信息 条码id维度
                updateDownloadProducts(products, nowDate);
            }
        }

    }
}
