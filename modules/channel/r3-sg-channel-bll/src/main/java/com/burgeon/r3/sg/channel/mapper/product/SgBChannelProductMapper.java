package com.burgeon.r3.sg.channel.mapper.product;

import com.burgeon.r3.sg.channel.model.request.product.SgBChannelProductDevOpsQueryRequest;
import com.burgeon.r3.sg.channel.model.request.product.SgBChannelProductQueryRequest;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductDownloadRequest;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductQueryForSTRequest;
import com.burgeon.r3.sg.channel.model.result.product.SgBChannelProductDevOpsQueryInfoResult;
import com.burgeon.r3.sg.channel.model.result.product.SgChannelProductQueryForSTResult;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProductDownloadBuffer;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/6 09:21
 */
@Mapper
@Repository
public interface SgBChannelProductMapper extends ExtentionMapper<SgBChannelProduct> {

    @Select("<script> " +
            "select * from sg_b_channel_product where ps_c_sku_ecode = #{sku} and cp_c_shop_id = #{shop}" +
            "</script> ")
    List<SgBChannelProduct> selectBySkuAndShop(@Param("sku") String sku, @Param("shop") Long shop);

    @Select("<script> " +
            "select * from sg_b_channel_product " +
            "where ISACTIVE = 'Y' and " +
            "<foreach separator='or' item='item' index='index' collection='skuShopIdList' open='(' close=')'> " +
            "  ( sku_id = #{item.skuId} and cp_c_shop_id = #{item.cpCShopId})" +
            "</foreach>" +
            "</script> ")
    List<SgBChannelProduct> selectBySkuAndShopIdList(@Param("skuShopIdList") List<SgBChannelProduct> skuShopIdList);


    /**
     * 删除渠道商品，不存在在系统条码的渠道商品
     *
     * @param skuIdList
     * @param skuEcodeList
     * @return
     */
    @Delete(" <script> " +
            " delete from sg_b_channel_product " +
            " where sku_id in " +
            " <foreach collection='skuIdList' item='skuId' open='(' separator=',' close=')'> #{skuId} </foreach> " +
            " <if test ='skuEcodeList != null and skuEcodeList.size()!=0'> and ps_c_sku_ecode in " +
            " <foreach collection='skuEcodeList' item='skuEcode' open='(' separator=',' close=')'> #{skuEcode} </foreach></if> " +
            " </script>")
    Integer delChannelProduct(@Param("skuIdList") List<String> skuIdList, @Param("skuEcodeList") List<String> skuEcodeList);


    /**
     * 查询渠道商品，
     *
     * @param skuIdList
     * @return
     */
    @Select(" <script> " +
            " select ps_c_sku_ecode,sku_id from sg_b_channel_product " +
            " where sku_id in " +
            " <foreach collection='skuIdList' item='skuId' open='(' separator=',' close=')'> #{skuId} </foreach> " +
            " </script>")
    List<SgBChannelProductQueryRequest> queryChannelProductBySkuid(@Param("skuIdList") List<String> skuIdList);

    /**
     * 库存策略查询渠道商品
     *
     * @param
     * @return
     */
    @Select(" <script> " +
            " select numiid,PS_C_PRO_ID,PS_C_PRO_ECODE,PS_C_PRO_ENAME,PS_C_SKU_ID,PS_C_SKU_ECODE,SKU_ID," +
            " GBCODE,PS_C_SPEC1_ID,PS_C_SPEC1_ECODE,PS_C_SPEC1_ENAME,PS_C_SPEC2_ID,PS_C_SPEC2_ECODE," +
            " PS_C_SPEC2_ENAME,CP_C_SHOP_ID,CP_C_SHOP_TITLE,ID " +
            " from sg_b_channel_product where 1=1 " +
            " <if test='sgChannelProductQueryForSTRequest.ptProIdList != null and " +
            " sgChannelProductQueryForSTRequest.ptProIdList.size()!=0 '> and numiid in " +
            " <foreach collection='sgChannelProductQueryForSTRequest.ptProIdList' item='item' open='(' separator=',' close=')'> #{item} </foreach> </if> " +
            " <if test ='sgChannelProductQueryForSTRequest.ptSkuIdList != null and " +
            " sgChannelProductQueryForSTRequest.ptSkuIdList.size()!=0 '> and sku_id in " +
            " <foreach collection='sgChannelProductQueryForSTRequest.ptSkuIdList' item='item' open='(' separator=',' close=')'> #{item} </foreach> </if> " +
            " <if test='sgChannelProductQueryForSTRequest.psCProEcodeList != null and " +
            " sgChannelProductQueryForSTRequest.psCProEcodeList.size()!=0 '> and ps_c_pro_ecode in " +
            " <foreach collection='sgChannelProductQueryForSTRequest.psCProEcodeList' item='item' open='(' separator=',' close=')'> #{item} </foreach> </if> " +

            " <if test='sgChannelProductQueryForSTRequest.psCProIdList != null and " +
            " sgChannelProductQueryForSTRequest.psCProIdList.size()!=0 '> and ps_c_pro_id in " +
            " <foreach collection='sgChannelProductQueryForSTRequest.psCProIdList' item='item' open='(' separator=',' close=')'> #{item} </foreach> </if> " +


            " <if test='sgChannelProductQueryForSTRequest.psCSkuIdList != null and " +
            " sgChannelProductQueryForSTRequest.psCSkuIdList.size()!=0 '> and ps_c_sku_id in " +
            " <foreach collection='sgChannelProductQueryForSTRequest.psCSkuIdList' item='item' open='(' separator=',' close=')'> #{item} </foreach> </if> " +


            " <if test='sgChannelProductQueryForSTRequest.psCSkuEcodeList != null and " +
            " sgChannelProductQueryForSTRequest.psCSkuEcodeList.size()!=0 '> and ps_c_sku_ecode in " +
            " <foreach collection='sgChannelProductQueryForSTRequest.psCSkuEcodeList' item='item' open='(' separator=',' close=')'> #{item} </foreach> </if> " +

            " <if test='sgChannelProductQueryForSTRequest.cpCShopId != null '> and cp_c_shop_id = #{sgChannelProductQueryForSTRequest.cpCShopId} </if> " +
            " <if test='sgChannelProductQueryForSTRequest.cpCShopIdList != null and " +
            " sgChannelProductQueryForSTRequest.cpCShopIdList.size()!=0 '> and cp_c_shop_id in" +
            " <foreach collection='sgChannelProductQueryForSTRequest.cpCShopIdList' item='item' open='(' separator=',' close=')'> ${item} </foreach> </if>" +
            " <if test='sgChannelProductQueryForSTRequest.id != null'> and id &gt;#{sgChannelProductQueryForSTRequest.id}</if> " +
            " order by id asc " +
            " <if test='sgChannelProductQueryForSTRequest.pageNum != null'> limit #{sgChannelProductQueryForSTRequest.pageNum} </if>" +
            "</script> ")
    List<SgChannelProductQueryForSTResult> queryChannelProductForST(@Param("sgChannelProductQueryForSTRequest")
                                                                            SgChannelProductQueryForSTRequest sgChannelProductQueryForSTRequest);

    /**
     * 查询总数
     *
     * @param sgChannelProductQueryForSTRequest
     * @return
     */
    @Select(" <script> " +
            " select count(1) " +
            " from sg_b_channel_product where 1=1 " +
            " <if test='sgChannelProductQueryForSTRequest.ptProIdList != null and " +
            " sgChannelProductQueryForSTRequest.ptProIdList.size()!=0 '> and numiid in " +
            " <foreach collection='sgChannelProductQueryForSTRequest.ptProIdList' item='item' open='(' separator=',' close=')'> #{item} </foreach> </if> " +
            " <if test ='sgChannelProductQueryForSTRequest.ptSkuIdList != null and " +
            " sgChannelProductQueryForSTRequest.ptSkuIdList.size()!=0 '> and sku_id in " +
            " <foreach collection='sgChannelProductQueryForSTRequest.ptSkuIdList' item='item' open='(' separator=',' close=')'> #{item} </foreach> </if> " +
            " <if test='sgChannelProductQueryForSTRequest.psCProEcodeList != null and " +
            " sgChannelProductQueryForSTRequest.psCProEcodeList.size()!=0 '> and ps_c_pro_ecode in " +
            " <foreach collection='sgChannelProductQueryForSTRequest.psCProEcodeList' item='item' open='(' separator=',' close=')'> #{item} </foreach> </if> " +

            " <if test='sgChannelProductQueryForSTRequest.psCProIdList != null and " +
            " sgChannelProductQueryForSTRequest.psCProIdList.size()!=0 '> and ps_c_pro_id in " +
            " <foreach collection='sgChannelProductQueryForSTRequest.psCProIdList' item='item' open='(' separator=',' close=')'> ${item} </foreach> </if> " +
            " <if test='sgChannelProductQueryForSTRequest.psCSkuIdList != null and " +
            " sgChannelProductQueryForSTRequest.psCSkuIdList.size()!=0 '> and ps_c_sku_id in " +
            " <foreach collection='sgChannelProductQueryForSTRequest.psCSkuIdList' item='item' open='(' separator=',' close=')'> ${item} </foreach> </if> " +


            " <if test='sgChannelProductQueryForSTRequest.psCSkuEcodeList != null and " +
            " sgChannelProductQueryForSTRequest.psCSkuEcodeList.size()!=0 '> and ps_c_sku_ecode in " +
            " <foreach collection='sgChannelProductQueryForSTRequest.psCSkuEcodeList' item='item' open='(' separator=',' close=')'> #{item} </foreach> </if> " +
            " <if test='sgChannelProductQueryForSTRequest.cpCShopId != null '> and cp_c_shop_id = #{sgChannelProductQueryForSTRequest.cpCShopId} </if> " +
            " <if test='sgChannelProductQueryForSTRequest.cpCShopIdList != null and " +
            " sgChannelProductQueryForSTRequest.cpCShopIdList.size()!=0 '> and cp_c_shop_id in" +
            " <foreach collection='sgChannelProductQueryForSTRequest.cpCShopIdList' item='item' open='(' separator=',' close=')'> ${item} </foreach> </if></script> ")
    Integer queryChannelProductCountForST(@Param("sgChannelProductQueryForSTRequest")
                                                  SgChannelProductQueryForSTRequest sgChannelProductQueryForSTRequest);

    /**
     * 库存策略查询渠道商品
     *
     * @param
     * @return
     */
    @Select(" <script> " +
            " select numiid,PS_C_PRO_ID,PS_C_PRO_ECODE,PS_C_PRO_ENAME,PS_C_SKU_ID,PS_C_SKU_ECODE,SKU_ID," +
            " GBCODE,PS_C_SPEC1_ID,PS_C_SPEC1_ECODE,PS_C_SPEC1_ENAME,PS_C_SPEC2_ID,PS_C_SPEC2_ECODE," +
            " PS_C_SPEC2_ENAME,CP_C_SHOP_ID,CP_C_SHOP_TITLE,ID " +
            " from sg_b_channel_product where 1=1 " +
            " <if test='sgChannelProductQueryForSTRequest.ptProId != null ' > and numiid = #{sgChannelProductQueryForSTRequest.ptProId} </if>" +
            " <if test ='sgChannelProductQueryForSTRequest.ptSkuId != null'> and sku_id = #{sgChannelProductQueryForSTRequest.ptSkuId} </if>" +
            " <if test='sgChannelProductQueryForSTRequest.psCProEcode != null '> and ps_c_pro_ecode = #{sgChannelProductQueryForSTRequest.psCProEcode} </if> " +
            " <if test='sgChannelProductQueryForSTRequest.psCSkuEcode != null  '> and ps_c_sku_ecode = #{sgChannelProductQueryForSTRequest.psCSkuEcode} </if>" +

            " <if test='sgChannelProductQueryForSTRequest.psCProId != null '> and ps_c_pro_id = ${sgChannelProductQueryForSTRequest.psCProId} </if> " +
            " <if test='sgChannelProductQueryForSTRequest.psCSkuId != null  '> and ps_c_sku_id = ${sgChannelProductQueryForSTRequest.psCSkuId} </if>" +

            " <if test='sgChannelProductQueryForSTRequest.cpCShopId != null '> and cp_c_shop_id = ${sgChannelProductQueryForSTRequest.cpCShopId} </if> " +
            " <if test='sgChannelProductQueryForSTRequest.id != null'> and id &gt;#{sgChannelProductQueryForSTRequest.id}  </if> " +
            " <if test='sgChannelProductQueryForSTRequest.isSort != null'> order by id asc </if> " +
            " <if test='sgChannelProductQueryForSTRequest.pageNum != null'> limit #{sgChannelProductQueryForSTRequest.pageNum} </if>" +
            "</script> ")
    List<SgChannelProductQueryForSTResult> querySingleChannelProductForST(@Param("sgChannelProductQueryForSTRequest")
                                                                                  SgChannelProductQueryForSTRequest sgChannelProductQueryForSTRequest);

    /**
     * 库存策略查询渠道商品总数
     *
     * @param
     * @return
     */
    @Select(" <script> " +
            " select count(1) " +
            " from sg_b_channel_product where 1=1 " +
            " <if test='sgChannelProductQueryForSTRequest.ptProId != null ' > and numiid = #{sgChannelProductQueryForSTRequest.ptProId} </if>" +
            " <if test ='sgChannelProductQueryForSTRequest.ptSkuId != null'> and sku_id = #{sgChannelProductQueryForSTRequest.ptSkuId} </if>" +
            " <if test='sgChannelProductQueryForSTRequest.psCProEcode != null '> and ps_c_pro_ecode = #{sgChannelProductQueryForSTRequest.psCProEcode} </if> " +
            " <if test='sgChannelProductQueryForSTRequest.psCSkuEcode != null  '> and ps_c_sku_ecode = #{sgChannelProductQueryForSTRequest.psCSkuEcode} </if>" +
            " <if test='sgChannelProductQueryForSTRequest.psCProId != null '> and ps_c_pro_id = ${sgChannelProductQueryForSTRequest.psCProId} </if> " +
            " <if test='sgChannelProductQueryForSTRequest.psCSkuId != null  '> and ps_c_sku_id = ${sgChannelProductQueryForSTRequest.psCSkuId} </if>" +

            " <if test='sgChannelProductQueryForSTRequest.cpCShopId != null '> and cp_c_shop_id = ${sgChannelProductQueryForSTRequest.cpCShopId} </if> " +
            " <if test='sgChannelProductQueryForSTRequest.pageNum != null'> limit #{sgChannelProductQueryForSTRequest.pageNum} </if>" +
            "</script> ")
    Integer querySingleChannelProductCountForST(@Param("sgChannelProductQueryForSTRequest")
                                                        SgChannelProductQueryForSTRequest sgChannelProductQueryForSTRequest);

    @Update("update sg_b_channel_product set batch_no= #{batchNo} where id in (${ids})")
    Integer updateBatchnoByIds(@Param("batchNo") String batchNo, @Param("ids") String ids);


    @Select("select cp_c_shop_id,numiid,sku_id from sg_b_channel_product where numiid in (${numiid}) and cp_c_shop_id = ${shop} ")
    List<SgBChannelProduct> selectChannelProductByNumiid(@Param("numiid") String numiid, @Param("shop") Long shop);

    @Select("select cp_c_shop_id,ps_c_sku_ecode,numiid,sku_id from sg_b_channel_product where ps_c_sku_ecode in (${sku}) and cp_c_shop_id = ${shop} ")
    List<SgBChannelProduct> selectChannelProductBySku(@Param("sku") String sku, @Param("shop") Long shop);

    /**
     * 这个方法中：sku_id 已经不能保证唯一了，所以需要加上店铺的查询条件，建议不再使用该方法,用：selectChannelProductBySkuIdAndShopId
     * @param sku
     * @return
     */
    @Select("select * from sg_b_channel_product where sku_id in (${sku}) ")
    List<SgBChannelProduct> selectChannelProductBySkuId(@Param("sku") String sku);

    @Select("select * from sg_b_channel_product where cp_c_shop_id = #{shopId} and sku_id in (${sku}) ")
    List<SgBChannelProduct> selectChannelProductBySkuIdAndShopId(@Param("sku") String sku, @Param("shopId") Long shopId);


    @Select("<script> " +
            "select * from sg_b_channel_product where sku_id = #{skuId} and cp_c_platform_id = #{platform} and cp_c_shop_id = #{shopId} and reserve_varchar07 = '1' " +
            "</script> ")
    List<SgBChannelProduct> queryByPlatSkuAndPlatform(@Param("skuId") String skuId, @Param("platform") Integer platform, @Param("shopId") Long shopId);

    @Select({
            "<script>",
            "/*FORCE_SLAVE*/",
            " SELECT  COUNT(1) ",
            "FROM  ",
            " ${adbSchema}.SG_B_CHANNEL_PRODUCT A  ",
            " WHERE A.ISACTIVE = 'Y' ",
            "<if test='request.psCProEcode != null and request.psCProEcode != \"\"'>",
            " AND A.ps_c_pro_ecode LIKE CONCAT(#{request.psCProEcode},'%')",
            "</if>",
            " <when test='request.qtyDifferences!=null '> ",
            " AND ( A.QTY_DIFFERENCES &gt;= ABS(#{request.qtyDifferences}) OR A.QTY_DIFFERENCES &lt;= -ABS(#{request.qtyDifferences}))",
            "</when>",
            "<if test = 'request.syncStatuss != null and request.syncStatuss.size > 0'>",
            "AND A.sync_status IN ",
            "<foreach item='item' collection='request.syncStatuss' separator=',' open='(' close=')' > CONVERT(#{item},SIGNED) </foreach>",
            "</if>",
            "<if test = 'request.isDiff != null and request.isDiff == \"1\"'>",
            "AND A.QTY_DIFFERENCES != 0 ",
            "</if>",
            "<if test = 'request.isDiff != null and  request.isDiff == \"0\"'>",
            "AND (A.QTY_DIFFERENCES = 0 OR A.QTY_DIFFERENCES IS NULL )",
            "</if>",
            "<when test='request.istranss != null and request.istranss.size > 0'> ",
            "AND A.istrans IN ",
            "<foreach item='item' collection='request.istranss' separator=',' open='(' close=')' > #{item} </foreach>",
            "</when>",
            "<when test='request.islocks != null and request.islocks.size > 0'>",
            "AND A.islock IN ",
            "<foreach item='item' collection='request.islocks' separator=',' open='(' close=')' > #{item} </foreach>",
            "</when>",
            " <when test='request.cpCShopIds!=null and request.cpCShopIds.size>0'> ",
            " AND A.CP_C_SHOP_ID IN ",
            "<foreach item='item' collection='request.cpCShopIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when> ",
            " <when test='request.muniids!=null and request.muniids.size>0'> ",
            " AND A.NUMIID IN ",
            "<foreach item='item' collection='request.muniids' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.skuIds!=null and request.skuIds.size>0'> ",
            " AND A.SKU_ID IN ",
            "<foreach item='item' collection='request.skuIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.psCSpec1Ids!=null and request.psCSpec1Ids.size>0'> ",
            " AND A.PS_C_SPEC1_ID IN ",
            "<foreach item='item' collection='request.psCSpec1Ids' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.psCSpec2Ids!=null and request.psCSpec2Ids.size>0'> ",
            " AND A.PS_C_SPEC2_ID IN ",
            "<foreach item='item' collection='request.psCSpec2Ids' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.psCProIds!=null and request.psCProIds.size>0'> ",
            " AND A.PS_C_PRO_ID IN ",
            "<foreach item='item' collection='request.psCProIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.psCSkuIds!=null and request.psCSkuIds.size>0'> ",
            " AND A.PS_C_SKU_ID IN ",
            "<foreach item='item' collection='request.psCSkuIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.saleStatuss!=null and request.saleStatuss.size>0'> ",
            " AND A.SALE_STATUS IN ",
            "<foreach item='item' collection='request.saleStatuss' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            "<if test = 'request.isTmllCity != null '>",
            "   AND A.is_tmll_city='Y' ",
            "<if test = 'request.brandLabels!=null and request.brandLabels.size>0'>",
            "  AND A.sa_store_type IN ",
            "   <foreach item='item' collection='request.brandLabels' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            "</if>",
            "</if>",
            "<if test = 'request.isTmllCity == null and request.brandLabels!=null and request.brandLabels.size>0'>",
            "  AND A.sa_store_type IN ",
            "   <foreach item='item' collection='request.brandLabels' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            "</if>",
            "</script>"
    })
    Integer selectDevOpsInfoCount(@Param("request") SgBChannelProductDevOpsQueryRequest request, @Param("adbSchema") String adbSchema);

    @Select(" <script> " +
            "delete from sg_b_channel_product where modifieddate &lt;= #{modifydate} and reserve_varchar07 = '1' " +
            "</script> ")
    Integer delChannelProductByDate(@Param("modifydate") Date modifydate);

    @Select({
            "<script>",
            "/*FORCE_SLAVE*/",
            " SELECT  ",
            " A.ID,  ",
            " A.CP_C_SHOP_ID AS cpCShopId,  ",
            " A.CP_C_SHOP_TITLE AS cpCShopEname,  ",
            " A.PS_C_PRO_ID AS psCProId,  ",
            " A.PS_C_PRO_ECODE AS psCProEcode,  ",
            " A.PS_C_PRO_ENAME AS psCProEname,  ",
            " A.CP_C_PLATFORM_ID AS cpCPlatformId,  ",
            " A.NUMIID AS numiid,  ",
            " A.SKU_ID AS skuId,  ",
            " A.ISLOCK AS islock,  ",
            " A.ISTRANS istrans,  ",
            " A.PS_C_SKU_ID AS psCSkuid,  ",
            " A.PS_C_SKU_ECODE AS psCSkuEcode,  ",
            " A.PS_C_SPEC1_ID as psCSpec1Id,",
            " A.PS_C_SPEC1_ECODE as psCSpec1Ecode, ",
            " A.PS_C_SPEC1_ENAME as psCSpec1Ename, ",
            " A.PS_C_SPEC2_ID as psCSpec2Id,",
            " A.PS_C_SPEC2_ECODE as psCSpec2Ecode, ",
            " A.PS_C_SPEC2_ENAME as psCSpec2Ename, ",
            " A.TRANS_TIME as transTime, ",
            " A.sync_status as syncStatus, ",
            " A.sync_failed_reason as syncFailedReason, ",
            " A.SYNC_RATIO AS specialRadio, ",
            " CASE ",
            " WHEN A.is_tmll_city = 'Y' THEN  ",
            " CASE A.sa_store_type  ",
            " WHEN 1 THEN  ",
            " '1,3'  ",
            " WHEN 2 THEN  ",
            " '2,3'  ",
            " ELSE  ",
            " '3'  ",
            " END  ",
            " ELSE  ",
            " A.SA_STORE_TYPE  ",
            " END AS brandLabel, ",
            " A.SALE_STATUS as saleStatus, ",
            " A.QTY_CHANNEL  AS qtyChannel,  ",
            " A.QTY_STORAGE AS qtyPlatform,  ",
            " A.QTY_DIFFERENCES  AS qtyDifferences,  ",
            " 0 AS qtyReal,  ",
            " A.QTY_SAFETY AS qtySafety,  ",
            " A.FINAL_SYNC_NUM AS finalSyncNum,  ",
            " A.sa_store_type AS saStoreType,  ",
            " A.FINAL_SYNC_TIME AS finalSyncTime,  ",
            " A.detail_url AS detailUrl  ",
            "FROM  ",
            " ${adbSchema}.SG_B_CHANNEL_PRODUCT A  ",
            " WHERE A.ISACTIVE = 'Y' ",
            "<if test='request.psCProEcode != null and request.psCProEcode != \"\"'>",
            " AND A.ps_c_pro_ecode LIKE CONCAT(#{request.psCProEcode},'%')",
            "</if>",
            " <when test='request.qtyDifferences!=null '> ",
            " AND ( A.QTY_DIFFERENCES &gt;= ABS(#{request.qtyDifferences}) OR A.QTY_DIFFERENCES &lt;= -ABS(#{request.qtyDifferences})) ",
            " </when> ",
            "<if test = 'request.syncStatuss != null and request.syncStatuss.size > 0'>",
            "AND A.sync_status IN ",
            "<foreach item='item' collection='request.syncStatuss' separator=',' open='(' close=')' > CONVERT(#{item},SIGNED) </foreach>",
            "</if>",
            "<if test = 'request.isDiff != null and request.isDiff == \"1\"'>",
            "AND A.QTY_DIFFERENCES != 0 ",
            "</if>",
            "<if test = 'request.isDiff != null and request.isDiff == \"0\"'>",
            "AND (A.QTY_DIFFERENCES = 0 OR A.QTY_DIFFERENCES IS NULL ) ",
            "</if>",
            "<when test='request.istranss != null and request.istranss.size > 0'> ",
            "AND A.istrans IN ",
            "<foreach item='item' collection='request.istranss' separator=',' open='(' close=')' > #{item} </foreach>",
            "</when>",
            "<when test='request.islocks != null and request.islocks.size > 0'>",
            "AND A.islock IN ",
            "<foreach item='item' collection='request.islocks' separator=',' open='(' close=')' > #{item} </foreach>",
            "</when>",
            " <when test='request.cpCShopIds!=null and request.cpCShopIds.size>0'> ",
            " AND A.CP_C_SHOP_ID IN ",
            "<foreach item='item' collection='request.cpCShopIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when> ",
            " <when test='request.muniids!=null and request.muniids.size>0'> ",
            " AND A.NUMIID IN ",
            "<foreach item='item' collection='request.muniids' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.skuIds!=null and request.skuIds.size>0'> ",
            " AND A.SKU_ID IN ",
            "<foreach item='item' collection='request.skuIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.psCSpec1Ids!=null and request.psCSpec1Ids.size>0'> ",
            " AND A.PS_C_SPEC1_ID IN ",
            "<foreach item='item' collection='request.psCSpec1Ids' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.psCSpec2Ids!=null and request.psCSpec2Ids.size>0'> ",
            " AND A.PS_C_SPEC2_ID IN ",
            "<foreach item='item' collection='request.psCSpec2Ids' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.psCProIds!=null and request.psCProIds.size>0'> ",
            " AND A.PS_C_PRO_ID IN ",
            "<foreach item='item' collection='request.psCProIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.psCSkuIds!=null and request.psCSkuIds.size>0'> ",
            " AND A.PS_C_SKU_ID IN ",
            "<foreach item='item' collection='request.psCSkuIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.saleStatuss!=null and request.saleStatuss.size>0'> ",
            " AND A.SALE_STATUS IN ",
            "<foreach item='item' collection='request.saleStatuss' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            "<if test = 'request.isTmllCity != null '>",
            "   AND A.IS_TMLL_CITY='Y' ",
            "<if test = 'request.brandLabels!=null and request.brandLabels.size>0'>",
            "  AND A.sa_store_type IN ",
            "   <foreach item='item' collection='request.brandLabels' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            "</if>",
            "</if>",
            "<if test = 'request.isTmllCity == null and request.brandLabels!=null and request.brandLabels.size>0'>",
            "  AND A.sa_store_type IN ",
            "   <foreach item='item' collection='request.brandLabels' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            "</if>",
            "<when test='request.ruleDesc!=null '> ",
            " ORDER BY A.qty_differences DESC ",
            " </when> ",
            " <when test='request.ruleAsc!=null '> ",
            " ORDER BY A.qty_differences ASC ",
            " </when> ",
            " <when test='request.ruleDesc == null and request.ruleAsc == null'> ", //默认 根据 商品id, sku_id
            " ORDER BY A.NUMIID DESC,A.PS_C_SKU_ECODE DESC ",
            " </when> ",
            " LIMIT #{request.startindex,jdbcType=INTEGER}, #{request.range,jdbcType=INTEGER}",
            "</script>"
    })
    List<SgBChannelProductDevOpsQueryInfoResult> selectDevOpsInfo(@Param("request") SgBChannelProductDevOpsQueryRequest request, @Param("adbSchema") String adbSchema);


    @Select({
            "<script>",
            "/*FORCE_SLAVE*/",
            " SELECT  ",
            " A.ID,  ",
            " A.CP_C_SHOP_ID AS cpCShopId,  ",
            " A.CP_C_SHOP_TITLE AS cpCShopEname,  ",
            " A.PS_C_PRO_ID AS psCProId,  ",
            " A.PS_C_PRO_ECODE AS psCProEcode,  ",
            " A.PS_C_PRO_ENAME AS psCProEname,  ",
            " A.CP_C_PLATFORM_ID AS cpCPlatformId,  ",
            " A.NUMIID AS numiid,  ",
            " A.SKU_ID AS skuId,  ",
            " A.ISLOCK AS islock,  ",
            " A.ISTRANS istrans,  ",
            " A.PS_C_SKU_ID AS psCSkuid,  ",
            " A.PS_C_SKU_ECODE AS psCSkuEcode,  ",
            " A.PS_C_SPEC1_ID as psCSpec1Id,",
            " A.PS_C_SPEC1_ECODE as psCSpec1Ecode, ",
            " A.PS_C_SPEC1_ENAME as psCSpec1Ename, ",
            " A.PS_C_SPEC2_ID as psCSpec2Id,",
            " A.PS_C_SPEC2_ECODE as psCSpec2Ecode, ",
            " A.PS_C_SPEC2_ENAME as psCSpec2Ename, ",
            " A.TRANS_TIME as transTime, ",
            " A.sync_status as syncStatus, ",
            " A.sync_failed_reason as syncFailedReason, ",
            " CASE ",
            " WHEN A.is_tmll_city = 'Y' THEN  ",
            " CASE A.sa_store_type  ",
            " WHEN 1 THEN  ",
            " '1,3'  ",
            " WHEN 2 THEN  ",
            " '2,3'  ",
            " ELSE  ",
            " '3'  ",
            " END  ",
            " ELSE  ",
            " A.SA_STORE_TYPE  ",
            " END AS brandLabel, ",
            " A.SALE_STATUS as saleStatus, ",
            " A.QTY_CHANNEL  AS qtyChannel,  ",
            " A.QTY_STORAGE AS qtyPlatform,  ",
            " A.QTY_DIFFERENCES  AS qtyDifferences,  ",
            " 0 AS qtyReal,  ",
            " A.QTY_SAFETY AS qtySafety,  ",
            " A.FINAL_SYNC_NUM AS finalSyncNum,  ",
            " A.sa_store_type AS saStoreType,  ",
            " A.FINAL_SYNC_TIME AS finalSyncTime  ",
            "FROM  ",
            " SG_B_CHANNEL_PRODUCT A  ",
            " WHERE A.ISACTIVE = 'Y' ",
            " <when test='request.qtyDifferences!=null '> ",
            " AND ( A.QTY_DIFFERENCES &gt;= ABS(#{request.qtyDifferences}) OR A.QTY_DIFFERENCES &lt;= -ABS(#{request.qtyDifferences})) ",
            " </when> ",
            "<if test = 'request.syncStatuss != null and request.syncStatuss.size > 0'>",
            "AND A.sync_status IN ",
            "<foreach item='item' collection='request.syncStatuss' separator=',' open='(' close=')' > CONVERT(#{item},SIGNED) </foreach>",
            "</if>",
            "<if test = 'request.isDiff != null and request.isDiff == \"1\"'>",
            "AND A.QTY_DIFFERENCES != 0 ",
            "</if>",
            "<if test = 'request.isDiff != null and request.isDiff == \"0\"'>",
            "AND (A.QTY_DIFFERENCES = 0 OR A.QTY_DIFFERENCES IS NULL )",
            "</if>",
            "<when test='request.istranss != null and request.istranss.size > 0'> ",
            "AND A.istrans IN ",
            "<foreach item='item' collection='request.istranss' separator=',' open='(' close=')' > #{item} </foreach>",
            "</when>",
            "<when test='request.islocks != null and request.islocks.size > 0'>",
            "AND A.islock IN ",
            "<foreach item='item' collection='request.islocks' separator=',' open='(' close=')' > #{item} </foreach>",
            "</when>",
            " <when test='request.cpCShopIds!=null and request.cpCShopIds.size>0'> ",
            " AND A.CP_C_SHOP_ID IN ",
            "<foreach item='item' collection='request.cpCShopIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when> ",
            " <when test='request.muniids!=null and request.muniids.size>0'> ",
            " AND A.NUMIID IN ",
            "<foreach item='item' collection='request.muniids' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.skuIds!=null and request.skuIds.size>0'> ",
            " AND A.SKU_ID IN ",
            "<foreach item='item' collection='request.skuIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.psCSpec1Ids!=null and request.psCSpec1Ids.size>0'> ",
            " AND A.PS_C_SPEC1_ID IN ",
            "<foreach item='item' collection='request.psCSpec1Ids' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.psCSpec2Ids!=null and request.psCSpec2Ids.size>0'> ",
            " AND A.PS_C_SPEC2_ID IN ",
            "<foreach item='item' collection='request.psCSpec2Ids' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.psCProIds!=null and request.psCProIds.size>0'> ",
            " AND A.PS_C_PRO_ID IN ",
            "<foreach item='item' collection='request.psCProIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.psCSkuIds!=null and request.psCSkuIds.size>0'> ",
            " AND A.PS_C_SKU_ID IN ",
            "<foreach item='item' collection='request.psCSkuIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.saleStatuss!=null and request.saleStatuss.size>0'> ",
            " AND A.SALE_STATUS IN ",
            "<foreach item='item' collection='request.saleStatuss' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            "<if test = 'request.isTmllCity != null '>",
            "   AND A.IS_TMLL_CITY='Y' ",
            "<if test = 'request.brandLabels!=null and request.brandLabels.size>0'>",
            "  AND A.sa_store_type IN ",
            "   <foreach item='item' collection='request.brandLabels' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            "</if>",
            "</if>",
            "<if test = 'request.isTmllCity == null and request.brandLabels!=null and request.brandLabels.size>0'>",
            "  AND A.sa_store_type IN ",
            "   <foreach item='item' collection='request.brandLabels' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            "</if>",
            "<if test = 'request.range != null' >",
            " limit ${request.startindex},${request.range}",
            "</if>",
            "</script>"
    })
    List<SgBChannelProductDevOpsQueryInfoResult> selectListByDownload(@Param("request") SgBChannelProductDevOpsQueryRequest request);


    @Update({
            "<script>" +
                    "UPDATE sg_b_channel_product p SET p.sa_store_type = #{saStoreType}  WHERE p.id IN " +
                    "<foreach item='id' collection='ids' separator=',' open='(' close=')' > " +
                    " #{id} " +
                    "</foreach>" +
                    "</script>"
    })
    Integer updateSaStoreTypeByIds(@Param("ids") List<Long> ids, @Param("saStoreType") String saStoreType);

    /**
     * 查询平台商品ID和平台店铺(商品状态 销售中)
     *
     * @param downloadProductNum 分页大小
     * @param platformId         平台类型
     * @param shopIds            店铺ids
     * @return List<SgBChannelProduct>
     */
    @Select({"<script>",
            "/*FORCE_SLAVE*/",
            "SELECT ",
            " numiid,cp_c_shop_id ",
            "FROM ",
            " sg_b_channel_product ",
            " where download_status =0 and sale_status =1 and cp_c_platform_id=#{platformId}",
            " <if test='shopIds != null and !shopIds.isEmpty()'>",
            " and cp_c_shop_id in ",
            " <foreach collection='shopIds' item='shopId' open='(' separator=',' close=')'> ",
            "#{shopId} </foreach>",
            " </if>",
            " GROUP BY ",
            " numiid,cp_c_shop_id ",
            "LIMIT #{downloadProductNum};",
            "</script>"})
    List<SgBChannelProduct> queryDownloadProduct(@Param("downloadProductNum") Integer downloadProductNum,
                                                 @Param("platformId") Long platformId,
                                                 @Param("shopIds") List<Long> shopIds);

    /**
     * 批量更新平台条码库存
     *
     * @param params 商品信息
     * @return int
     */
    @Update("<script>" +
            "UPDATE sg_b_channel_product p " +
            "<set>" +
            " p.qty_differences = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            "<if test = 'param.qtyDifferences != null'>" +
            " when #{param.id} then #{param.qtyDifferences}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.qty_storage = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            "<if test = 'param.qtyStorage != null'>" +
            " when #{param.id} then #{param.qtyStorage}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.trans_time = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            "<if test = 'param.transTime != null'>" +
            " when #{param.id} then #{param.transTime}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            " p.download_status = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            "<if test = 'param.downloadStatus != null'>" +
            " when #{param.id} then #{param.downloadStatus}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.trans_time = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            "<if test = 'param.modifieddate != null'>" +
            " when #{param.id} then #{param.modifieddate}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "</set>" +
            "<where>" +
            "(p.id) in " +
            " <foreach collection='params' item='param' open='(' separator=',' close=')'>" +
            " (#{param.id}) " +
            "</foreach>" +
            "</where>" +
            "</script>")
    int updateDownloadProduct(@Param("params") List<SgBChannelProduct> params);


    @Select("<script>" +
            "SELECT * FROM sg_b_channel_product p <where> " +
            "<if test = 'shopId != null'>" +
            " p.cp_c_shop_id = #{shopId}" +
            "</if>" +
            "<if test = 'numiids != null and numiids.size > 0'>" +
            " AND p.numiid in " +
            "<foreach item='numiid' collection='numiids' separator=',' open='(' close=')' > " +
            " #{numiid} " +
            "</foreach>" +
            "</if>" +
            "</where>" +
            "</script>")
    List<SgBChannelProduct> selectByShopIdAndNumiid(@Param("shopId") Long shopId, @Param("numiids") List<String> numiids);

    @Update("<script>" +
            "UPDATE sg_b_channel_product p " +
            "<set>" +
            " p.qty_differences = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            "<if test = 'param.qtyDifferences != null'>" +
            " when #{param.id} then #{param.qtyDifferences}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.qty_storage = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            "<if test = 'param.qtyStorage != null'>" +
            " when #{param.id} then #{param.qtyStorage}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.trans_time = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            "<if test = 'param.transTime != null'>" +
            " when #{param.id} then #{param.transTime}" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "</set>" +
            "<where>" +
            " p.id in " +
            " <foreach collection='params' item='param' open='(' separator=',' close=')'>" +
            " #{param.id} " +
            "</foreach>" +
            "</where>" +
            "</script>")
    Integer batchUpdateByList(@Param("params") List<SgBChannelProduct> params);


    @Insert({
            "<script>" +
                    "/*output_filename=${request.fileName},sql_output_oss_file_head_enable=true*/ ",
            "INSERT OVERWRITE INTO ${adbSchema}.v_adb_sg_b_channel_product  ",
            "SELECT ",
            "  j.id, ",
            " j.cp_c_shop_id '店铺id', ",
            " j.cp_c_shop_title '店铺名称', ",
            " concat(\"='\",j.numiid,\"'\") '平台商品', ",
            " j.ps_c_sku_ecode '条码', ",
            " j.ps_c_pro_ename '商品编码', ",
            " case j.istrans  ",
            " when 'Y' then '是' ",
            " when 'N' then '否' ",
            " else NULL END  '是否转仓', ",
            " case j.islock  ",
            " when 'Y' then '否' ",
            " when 'N' then '是' ",
            " else NULL END  '同步库存', ",
            " case j.sale_status  ",
            " when '1' then '销售中' ",
            " when '2' then '仓库中' ",
            " else NULL END  '上架状态', ",
            "case when j.is_tmll_city = 'Y'   ",
            "  then    ",
            "  case j.sa_store_type   ",
            "  when 1 then '活，同'    ",
            "  when 2 then '普，同'   ",
            "  else '同' END else    ",
            "  case j.sa_store_type   ",
            "  when '1' then '活'  ",
            "  when '2' then '普'  ",
            "  else '' END   ",
            "  END  '商品类型', ",
            " j.sync_ratio  '同步比例', ",
            " IFNULL(j.qty_channel,0)  '店铺库存', ",
            " case when j.buffer_id IS NULL ",
            " then case WHEN k.id is NULL ",
            " then '计算完成' else '计算中' end ",
            " else '计算中' END  '计算状态', ",
            " j.qty_storage  '平台库存', ",
            " j.trans_time  '平台库存下载时间', ",
            " IFNULL(j.qty_differences,0)  '差异数(>=)', ",
            " j.saQtyInChannel  '渠道内配销仓库存', ",
            " j.saQtyOutChannel  '渠道外配销仓库存', ",
            " j.qty_available  '配销仓库存', ",
            " j.qty_safety  '安全库存', ",
            " IFNULL(j.final_sync_num,0)  '最后同步数', ",
            " j.final_sync_time  '最后同步时间', ",
            "case when j.sync_failed_reason = NULL ",
            " then '' else REPLACE(j.sync_failed_reason,',','，') END '同步失败原因', ",
            " case j.sync_status  ",
            " when '0' then '未同步' ",
            " when NULL then '未同步' ",
            " when '2' then '同步成功' ",
            " when '3' then '同步失败' ",
            " else '未同步' END  '同步状态', ",
            " concat(\"='\",j.sku_id,\"'\")  '平台条码' ",
            "FROM ",
            " ( ",
            " SELECT ",
            "  g.*, ",
            "  h.id buffer_id  ",
            " FROM ",
            "  ( ",
            "  SELECT ",
            "    f.id, ",
            "   f.cp_c_shop_id, ",
            "   f.cp_c_shop_title, ",
            "   f.numiid, ",
            "   f.is_tmll_city, ",
            "   f.ps_c_sku_ecode, ",
            "   f.ps_c_pro_ename, ",
            "   f.istrans, ",
            "   f.islock, ",
            "   f.sale_status, ",
            "   f.sa_store_type, ",
            "   f.sync_ratio, ",
            "   FLOOR( f.qty_channel ) qty_channel, ",
            "   FLOOR( f.qty_storage ) qty_storage, ",
            "   f.trans_time, ",
            "   FLOOR( f.qty_differences ) qty_differences, ",
            "   FLOOR( ifnull( m.saQtyInChannel, 0 ) ) saQtyInChannel, ",
            "   FLOOR( ifnull( m.saQtyOutChannel, 0 ) ) saQtyOutChannel, ",
            "   FLOOR( ifnull( m.qty_available, 0 ) ) qty_available, ",
            "   f.qty_safety, ",
            "   FLOOR( f.final_sync_num ) final_sync_num, ",
            "   f.final_sync_time, ",
            "   f.sync_failed_reason, ",
            "   f.ps_c_sku_id, ",
            "   f.ps_c_pro_ecode, ",
            "   f.ps_c_pro_id, ",
            "   f.sync_status, ",
            "   f.sku_id  ",
            "  FROM ",
            "   ( ",
            "   SELECT ",
            "    d.id, ",
            "    d.cp_c_shop_id, ",
            "    d.cp_c_shop_title, ",
            "    d.numiid, ",
            "    d.ps_c_sku_ecode, ",
            "    d.ps_c_pro_ename, ",
            "    d.istrans, ",
            "    d.ps_c_sku_id, ",
            "    d.sale_status, ",
            "    d.sa_store_type, ",
            "    d.islock, ",
            "    d.is_tmll_city, ",
            "    d.qty_channel, ",
            "    d.trans_time, ",
            "    d.qty_differences, ",
            "    d.qty_safety, ",
            "    d.final_sync_num, ",
            "    d.final_sync_time, ",
            "    d.sync_failed_reason, ",
            "    d.sync_status, ",
            "    d.ps_c_pro_id, ",
            "    d.ps_c_pro_ecode, ",
            "    d.sku_id, ",
            "    d.qty_storage, ",
            "    d.sync_ratio ",
            "   FROM ",
            "    ( ",
            "    SELECT ",
            "     s.id, ",
            "     s.cp_c_shop_title, ",
            "     s.numiid, ",
            "     s.ps_c_sku_ecode, ",
            "     s.ps_c_pro_ename, ",
            "     s.istrans, ",
            "     s.sale_status, ",
            "     s.sa_store_type, ",
            "     s.cp_c_shop_id, ",
            "     s.ps_c_sku_id, ",
            "     s.qty_storage, ",
            "     s.islock, ",
            "     s.is_tmll_city, ",
            "     s.qty_channel, ",
            "     s.trans_time, ",
            "     s.qty_differences, ",
            "     s.qty_safety, ",
            "     s.final_sync_num, ",
            "     s.final_sync_time, ",
            "     s.sync_failed_reason, ",
            "     s.sync_status, ",
            "     s.ps_c_pro_id, ",
            "     s.ps_c_pro_ecode, ",
            "     s.sku_id, ",
            "     s.sync_ratio ",
            "    FROM ",
            "     ${adbSchema}.sg_b_channel_product s ",
            "     where s.isactive = 'Y' ",
            "    ) d ",
            "   ) f ",
            "   LEFT JOIN ( ",
            " SELECT saStorage.*,saStorage1.saQtyInChannel,saStorage2.saQtyOutChannel ",
            "  FROM ",
            " ( ",
            "  SELECT k.cp_c_shop_id,k.sg_c_sa_store_id,ss.ps_c_sku_id,k.sg_c_sa_store_type,SUM(FLOOR(IFNULL( ss.qty_available, 0 ) * (k.ratio/100))) AS qty_available ",
            "FROM ",
            " ( ",
            "  SELECT si.ratio,rs.cp_c_shop_id,si.sg_c_sa_store_id,si.sg_c_sa_store_type  ",
            "    FROM ",
            "  ${adbSchema}.sg_c_channel_ratio_strategy rs ",
            "  LEFT JOIN ${adbSchema}.sg_c_channel_ratio_strategy_item si ON rs.id = si.sg_c_channel_ratio_strategy_id  ",
            " WHERE ",
            "  rs.isactive = 'Y'  ",
            "  AND si.isactive = 'Y'    ",
            " ) k ",
            " LEFT JOIN ${adbSchema}.sg_b_sa_storage ss ON k.sg_c_sa_store_id = ss.sg_c_sa_store_id  WHERE ss.isactive = 'Y' ",
            " GROUP BY ",
            " k.cp_c_shop_id, ",
            " k.sg_c_sa_store_type, ",
            " ss.ps_c_sku_id ",
            ") saStorage ",
            " LEFT JOIN ( ",
            "SELECT ",
            "  k.cp_c_shop_id,ss.ps_c_sku_id,k.sg_c_sa_store_type,SUM(FLOOR(IFNULL( ss.qty_available, 0 ) * (k.ratio/100))) AS saQtyInChannel ",
            "FROM ",
            " ( ",
            " SELECT si.ratio,rs.cp_c_shop_id,si.sg_c_sa_store_id,si.sg_c_sa_store_type  ",
            "   FROM ",
            "     r3_base.cp_c_shop s ",
            "     LEFT JOIN ${adbSchema}.sg_c_channel_ratio_strategy rs ON s.id = rs.cp_c_shop_id ",
            "     LEFT JOIN ${adbSchema}.sg_c_channel_ratio_strategy_item si ON rs.id = si.sg_c_channel_ratio_strategy_id ",
            "     LEFT JOIN ${adbSchema}.sg_c_sa_store sss ON si.sg_c_sa_store_id = sss.id ",
            "   WHERE ",
            "       s.cp_c_shop_id in  ",
            "<foreach item='item' collection='request.cpCShopIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            "       AND rs.isactive = 'Y'  AND si.isactive = 'Y' ",
            "       AND  s.channel IS NOT NULL AND  s.channel = sss.channel ",
            ") k ",
            " LEFT JOIN ${adbSchema}.sg_b_sa_storage ss ON k.sg_c_sa_store_id = ss.sg_c_sa_store_id WHERE ss.isactive= 'Y' ",
            " GROUP BY ",
            " k.cp_c_shop_id, ",
            " k.sg_c_sa_store_type, ",
            " ss.ps_c_sku_id ",
            ") saStorage1 ON saStorage.cp_c_shop_id = saStorage1.cp_c_shop_id ",
            "               AND saStorage.sg_c_sa_store_type = saStorage1.sg_c_sa_store_type ",
            "               AND saStorage.ps_c_sku_id = saStorage1.ps_c_sku_id ",
            " LEFT JOIN ( ",
            "SELECT ",
            "  k.cp_c_shop_id,ss.ps_c_sku_id,k.sg_c_sa_store_type,SUM(FLOOR(IFNULL( ss.qty_available, 0 ) * (k.ratio/100))) AS saQtyOutChannel ",
            "FROM ",
            " ( ",
            " SELECT si.ratio,rs.cp_c_shop_id,si.sg_c_sa_store_id,si.sg_c_sa_store_type  ",
            "   FROM ",
            "     r3_base.cp_c_shop s ",
            "     LEFT JOIN ${adbSchema}.sg_c_channel_ratio_strategy rs ON s.id = rs.cp_c_shop_id ",
            "     LEFT JOIN ${adbSchema}.sg_c_channel_ratio_strategy_item si ON rs.id = si.sg_c_channel_ratio_strategy_id ",
            "     LEFT JOIN ${adbSchema}.sg_c_sa_store sss ON si.sg_c_sa_store_id = sss.id ",
            "   WHERE ",
            "       s.cp_c_shop_id in  ",
            "<foreach item='item' collection='request.cpCShopIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            "       AND rs.isactive = 'Y'  AND si.isactive = 'Y' ",
            "       AND ( s.channel IS NULL OR sss.channel IS NULL OR s.channel <![CDATA[ <> ]]> sss.channel ) ",
            ") k ",
            " LEFT JOIN ${adbSchema}.sg_b_sa_storage ss ON k.sg_c_sa_store_id = ss.sg_c_sa_store_id WHERE ss.isactive= 'Y' ",
            " GROUP BY ",
            " k.cp_c_shop_id, ",
            " k.sg_c_sa_store_type, ",
            " ss.ps_c_sku_id ",
            ") saStorage2 ON saStorage.cp_c_shop_id = saStorage2.cp_c_shop_id ",
            "               AND saStorage.sg_c_sa_store_type = saStorage2.sg_c_sa_store_type ",
            "               AND saStorage.ps_c_sku_id = saStorage2.ps_c_sku_id ",
            ") m ON f.cp_c_shop_id = m.cp_c_shop_id AND f.sa_store_type = m.sg_c_sa_store_type AND f.ps_c_sku_id = m.ps_c_sku_id ",
            "  ) g ",
            "  LEFT JOIN ${adbSchema}.sg_b_channel_storage_buffer h ON g.cp_c_shop_id = h.cp_c_shop_id  ",
            "  AND g.sku_id = h.sku_id  ",
            " ) j ",
            " LEFT JOIN ${adbSchema}.sg_b_channel_storage_buffer_procedure k ON j.cp_c_shop_id = k.cp_c_shop_id  ",
            " AND j.sku_id = k.sku_id  ",
            "<where> " +
                    " <when test='request.cpCShopIds!=null and request.cpCShopIds.size>0'> ",
            " AND j.cp_c_shop_id IN ",
            "<foreach item='item' collection='request.cpCShopIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when> ",
            " <when test='request.qtyDifferences!=null '> ",
            " AND ( j.qty_differences &gt;= ABS(#{request.qtyDifferences}) OR j.qty_differences &lt;= -ABS(#{request.qtyDifferences})) ",
            " </when> ",
            "<if test = 'request.isDiff != null and request.isDiff == \"0\"'>",
            "AND (j.qty_differences = 0 OR j.qty_differences IS NULL )",
            "</if>",
            "<if test = 'request.isDiff != null and request.isDiff == \"1\"'>",
            "AND j.qty_differences != 0 ",
            "</if>",
            "<if test = 'request.syncStatuss != null and request.syncStatuss.size > 0'>",
            "AND j.sync_status IN ",
            "<foreach item='item' collection='request.syncStatuss' separator=',' open='(' close=')' > CONVERT(#{item},SIGNED) </foreach>",
            "</if>",
            "<when test='request.istranss != null and request.istranss.size > 0'> ",
            "AND j.istrans IN ",
            "<foreach item='item' collection='request.istranss' separator=',' open='(' close=')' > #{item} </foreach>",
            "</when>",
            "<when test='request.islocks != null and request.islocks.size > 0'>",
            "AND j.islock IN ",
            "<foreach item='item' collection='request.islocks' separator=',' open='(' close=')' > #{item} </foreach>",
            "</when>",
            " <when test='request.muniids!=null and request.muniids.size>0'> ",
            " AND j.numiid IN ",
            "<foreach item='item' collection='request.muniids' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.skuIds!=null and request.skuIds.size>0'> ",
            " AND j.sku_id IN ",
            "<foreach item='item' collection='request.skuIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.psCSpec1Ids!=null and request.psCSpec1Ids.size>0'> ",
            " AND j.ps_c_spec1_id IN ",
            "<foreach item='item' collection='request.psCSpec1Ids' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.psCSpec2Ids!=null and request.psCSpec2Ids.size>0'> ",
            " AND j.ps_c_spec2_id IN ",
            "<foreach item='item' collection='request.psCSpec2Ids' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.psCProIds!=null and request.psCProIds.size>0'> ",
            " AND j.ps_c_pro_id IN ",
            "<foreach item='item' collection='request.psCProIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.psCSkuIds!=null and request.psCSkuIds.size>0'> ",
            " AND j.ps_c_sku_id IN ",
            "<foreach item='item' collection='request.psCSkuIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            " <when test='request.saleStatuss!=null and request.saleStatuss.size>0'> ",
            " AND j.sale_status IN ",
            "<foreach item='item' collection='request.saleStatuss' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when> ",
            "<if test = 'request.isTmllCity != null '>",
            "   AND j.is_tmll_city='Y' ",
            "<if test = 'request.brandLabels!=null and request.brandLabels.size>0'>",
            "  AND j.sa_store_type IN ",
            "   <foreach item='item' collection='request.brandLabels' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            "</if>",
            "</if>",
            "<if test = 'request.isTmllCity == null and request.brandLabels!=null and request.brandLabels.size>0'>",
            "  AND j.sa_store_type IN ",
            "   <foreach item='item' collection='request.brandLabels' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            "</if>",
            " <when test='request.ids!=null and request.ids.size>0'> ",
            " AND j.id IN ",
            "<foreach item='item' collection='request.ids' separator=',' open='(' close=')' > #{item} </foreach>",
            " </when> ",
            "<if test='request.psCProEcode != null and request.psCProEcode != \"\"'>",
            " AND j.ps_c_pro_ecode LIKE CONCAT(#{request.psCProEcode},'%')",
            "</if>",
            "</where>",
            "</script>"
    })
    void downloadStorageNew(@Param("request") SgBChannelProductDevOpsQueryRequest request, @Param("adbSchema") String adbSchema);

    @Select("<script>" +
            "SELECT cp_c_shop_id,cp_c_platform_id,numiid FROM sg_b_channel_product p  <where> " +
            "<if test = 'request.cpCShopId != null'>" +
            "  p.cp_c_shop_id = #{request.cpCShopId}" +
            "</if>" +
            "<if test = 'request.cpCPlatformId != null'>" +
            " And p.cp_c_platform_id = #{request.cpCPlatformId}" +
            "</if>" +
            "</where>   " +
            " <when test='request.numiidList!=null and request.numiidList.size>0'> " +
            " AND p.numiid IN " +
            "<foreach item='item' collection='request.numiidList' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>" +
            " </when> and sale_status =1 " +
            "</script>")
    List<SgBChannelProductDownloadBuffer> queryNumIdsByShopIdAndPlatformId(@Param("request") SgChannelProductDownloadRequest request);

    /**
     * 获取需要库存比对的平台店铺下的条码
     * @param shopIds 店铺ids
     * @return List<SgBChannelProduct>
     */
    @Select("<script>" +
            "SELECT id,cp_c_shop_id,cp_c_platform_id,sku_id,qty_storage,qty_channel,ware_type,numiid,ps_c_sku_ecode," +
            "ps_c_pro_ecode,cp_c_shop_title " +
            "FROM " +
            "sg_b_channel_product p   " +
            " <when test='shopIds!=null and shopIds.size>0'> " +
            " where p.cp_c_shop_id IN " +
            "<foreach item='item' collection='shopIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} " +
            "</foreach>" +
            " </when>  " +
            " <when test='psCSkuIds!=null and psCSkuIds.size>0'> " +
            " AND p.ps_c_sku_id IN " +
            "<foreach item='item' collection='psCSkuIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} " +
            "</foreach>" +
            " </when>"+
            " <when test='saleStatus!=null and saleStatus.size>0'> " +
            " AND p.sale_status IN " +
            "<foreach item='item' collection='saleStatus' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} " +
            "</foreach>" +
            " </when>"+
            " And isactive='Y' order by id" +
            " <when test='page!=null and offset!=null'> " +
            " limit #{page} offset #{offset}"+
            " </when>"+
            "</script>")
    List<SgBChannelProduct> queryProductStorageContrastSkuIdInfos(@Param("shopIds") List<Long> shopIds,
                                                                  @Param("psCSkuIds") List<Long> psCSkuIds,
                                                                  @Param("saleStatus") List<Integer> saleStatus,
                                                                  @Param("page") Integer page,
                                                                  @Param("offset") Integer offset);
}
