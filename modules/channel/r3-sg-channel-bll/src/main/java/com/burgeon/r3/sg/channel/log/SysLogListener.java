package com.burgeon.r3.sg.channel.log;

import com.burgeon.r3.sg.channel.mapper.control.SgCChannelStockControlLogMapper;
import com.burgeon.r3.sg.channel.model.event.SysLogEvent;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.control.SgCChannelStockControlLog;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.web.async.AsyncTask;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;


/**
 * 异步监听日志事件
 */
@Slf4j
@AllArgsConstructor
@Component
public class SysLogListener {

    @Autowired
    private SgCChannelStockControlLogMapper mapper;

    @Order
    @EventListener(SysLogEvent.class)
    public void saveSysLog(SysLogEvent event) {

        SgCChannelStockControlLog sgCChannelStockControlLog = (SgCChannelStockControlLog) event.getSource();

        Long objId = ModelUtil.getSequence(SgConstants.SG_C_CHANNEL_STOCK_CONTROL_LOG);
        sgCChannelStockControlLog.setId(objId);

        mapper.insert(sgCChannelStockControlLog);

    }
}
