package com.burgeon.r3.sg.channel.mapper.sale;

import com.burgeon.r3.sg.core.model.table.channel.sale.SgBChannelAdvanceSale;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@Mapper
public interface SgBChannelAdvanceSaleMapper extends ExtentionMapper<SgBChannelAdvanceSale> {

    @Select("SELECT\n" +
            "\t* \n" +
            "FROM\n" +
            "\tsg_b_channel_advance_sale \n" +
            "WHERE\n" +
            "\t((begin_time >= #{beginTime,jdbcType=TIMESTAMP} AND begin_time <= #{endTime,jdbcType=TIMESTAMP}) or\n" +
            "\t(begin_time <= #{beginTime,jdbcType=TIMESTAMP} AND end_time >= #{endTime,jdbcType=TIMESTAMP}) or\n" +
            "\t(end_time >= #{beginTime,jdbcType=TIMESTAMP} AND end_time <= #{endTime,jdbcType=TIMESTAMP}) or\n" +
            "\t(begin_time >= #{beginTime,jdbcType=TIMESTAMP} AND end_time <= #{endTime,jdbcType=TIMESTAMP})) and id <> #{id} and isactive = 'Y' " +
            " and cp_c_shop_id = #{shopId} and status = #{status} ")
    List<SgBChannelAdvanceSale> selectByTimeOverlap(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime
            , @Param("id") Long id, @Param("shopId") Long shopId, @Param("status") Integer status);
}