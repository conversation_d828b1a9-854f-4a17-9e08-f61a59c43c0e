package com.burgeon.r3.sg.channel.validate.storage;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgBChannelStorageIncSyncItemMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgBChannelStorageIncSyncMapper;
import com.burgeon.r3.sg.channel.model.dto.storage.SgBChannelStorageIncSyncDto;
import com.burgeon.r3.sg.channel.model.dto.storage.SgBChannelStorageIncSyncItemDto;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBChannelStorageIncSync;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBChannelStorageIncSyncItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleItemValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description 平台库存增量同步
 * <AUTHOR>
 * @Date 2021/6/23 9:51
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgBChannelStorageIncSyncSaveValidator
        extends BaseSingleItemValidator<SgBChannelStorageIncSyncDto, SgBChannelStorageIncSyncItemDto> {

    @Autowired
    private SgBChannelStorageIncSyncMapper storageIncSyncMapper;

    @Autowired
    private SgBChannelStorageIncSyncItemMapper storageIncSyncItemMapper;

    @Autowired
    private SgBChannelProductMapper productMapper;

    @Override
    public String getValidatorMsgName() {
        return "平台库存增量同步保存";
    }

    @Override
    public Class<?> getValidatorClass() {
        return SgBChannelStorageIncSyncSaveValidator.class;
    }

    /**
     * 主表保存
     *
     * @param mainObject
     * @param loginUser
     * @return
     */
    @Override
    public ValueHolderV14 validateMainTable(SgBChannelStorageIncSyncDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));


        if (mainObject.getId() > 0L) {
            SgBChannelStorageIncSync sgBChannelStorageIncSync = storageIncSyncMapper.selectById(mainObject.getId());
            if (sgBChannelStorageIncSync == null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("当前记录不存在!", loginUser.getLocale()));
                return v14;
            }
            if (mainObject.getBatchNo() != null) {
                v14 = checkMainTableBatchNO(mainObject, loginUser);
                if (v14.getCode() == ResultCode.FAIL) {
                    return v14;
                }
            }
            //主表同步状态为“同步成功”
            if (SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_SUCC.equals(sgBChannelStorageIncSync.getStatus())) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("平台库存增量同步主表状态不符合，不允许保存!", loginUser.getLocale()));
                return v14;
            }
        } else {
            if (mainObject.getBatchNo() == null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("批次号不能为空!", loginUser.getLocale()));
                return v14;
            }

            v14 = checkMainTableBatchNO(mainObject, loginUser);
            if (v14.getCode() == ResultCode.FAIL) {
                return v14;
            }
            if (mainObject.getCpCShopId() == null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("平台店铺不能为空!", loginUser.getLocale()));
                return v14;
            }
        }
        return v14;
    }

    /**
     * 明细表保存
     *
     * @param mainObject
     * @param subObjectList
     * @param loginUser
     * @return
     */
    @Override
    public ValueHolderV14 validateSubTable(SgBChannelStorageIncSyncDto mainObject,
                                           List<SgBChannelStorageIncSyncItemDto> subObjectList, User loginUser) {

        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        if (mainObject.getId() > 0L) {
            if (mainObject.getCpCShopId() == null) {
                SgBChannelStorageIncSync storageIncSyncOld = storageIncSyncMapper.selectById(mainObject.getId());
                if (storageIncSyncOld == null) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(Resources.getMessage("当前记录不存在"));
                    return v14;
                }
                mainObject.setCpCShopId(storageIncSyncOld.getCpCShopId());
            }
        }
        for (SgBChannelStorageIncSyncItemDto storageIncSyncItemDto : subObjectList) {
            if (storageIncSyncItemDto.getId() > 0L) {
                SgBChannelStorageIncSyncItem storageIncSyncItemOld = storageIncSyncItemMapper.selectById(storageIncSyncItemDto.getId());
                //明细同步状态
                if (SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_IN.equals(storageIncSyncItemOld.getStatus())
                        || SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_POR_SUCC.equals(storageIncSyncItemOld.getStatus())
                        || SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_SUCC.equals(storageIncSyncItemOld.getStatus())) {
                    return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前记录同步状态不符合，不允许保存！", loginUser.getLocale()));
                }
                //TODO：校验明细表的平台条码ID和平台商品ID是否同时为空或同时存在，需要考虑一下
                if (!(storageIncSyncItemDto.getSkuId() == null && storageIncSyncItemDto.getNumiid() == null)) {
                    String storageIncSyncItemSkuId = storageIncSyncItemDto.getSkuId();
                    String storageIncSyncItemNumiid = storageIncSyncItemDto.getNumiid();
                    if (storageIncSyncItemDto.getNumiid() == null) {
                        storageIncSyncItemSkuId = storageIncSyncItemDto.getSkuId();
                        storageIncSyncItemNumiid = storageIncSyncItemOld.getNumiid();
                    } else if (storageIncSyncItemDto.getSkuId() == null) {
                        storageIncSyncItemSkuId = storageIncSyncItemOld.getSkuId();
                        storageIncSyncItemNumiid = storageIncSyncItemDto.getNumiid();
                    }
                    v14 = checkMainTableSkuIdNumiId(storageIncSyncItemSkuId, storageIncSyncItemNumiid, loginUser);
                    if (v14.getCode() == ResultCode.FAIL) {
                        return v14;
                    }
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug("Start SgBChannelStorageIncSyncSaveValidator.insert.validateSubTable:storageIncSyncItemDto={};",
                            JSONObject.toJSONString(storageIncSyncItemDto));
                }
                //校验明细表的平台条码ID和平台商品ID是否同时为空或同时存在
                v14 = checkMainTableSkuIdNumiId(storageIncSyncItemDto.getSkuId(), storageIncSyncItemDto.getNumiid(), loginUser);
                if (v14.getCode() == ResultCode.FAIL) {
                    return v14;
                }
                if (storageIncSyncItemDto.getQty() == null) {
                    return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("数量为空，不允许保存！", loginUser.getLocale()));
                }
                //原有明细中是否存在平台条码或者平台商品id是非同步状态的记录
                Integer skuIdNumiIdCount = storageIncSyncItemMapper.selectCount(new QueryWrapper<SgBChannelStorageIncSyncItem>().lambda()
                        .eq(SgBChannelStorageIncSyncItem::getSgBChannelStorageIncSyncId, mainObject.getId())
                        .eq(storageIncSyncItemDto.getSkuId() != null, SgBChannelStorageIncSyncItem::getSkuId, storageIncSyncItemDto.getSkuId())
                        .eq(storageIncSyncItemDto.getNumiid() != null, SgBChannelStorageIncSyncItem::getNumiid, storageIncSyncItemDto.getNumiid())
                        .eq(SgBChannelStorageIncSyncItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                        .ne(SgBChannelStorageIncSyncItem::getStatus, SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_UN));
                if (skuIdNumiIdCount > 0) {
                    return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("明细中已经存在一条平台条码ID为" + storageIncSyncItemDto.getSkuId()
                            + "平台商品ID为" + storageIncSyncItemDto.getNumiid() + "的记录，且不是非同步状态，不允许保存！", loginUser.getLocale()));
                }
            }
            /*v14 = checktNumiidAndSkuId(storageIncSyncItemDto.getNumiid(), storageIncSyncItemDto.getSkuId(), mainObject.getCpCShopId(), loginUser);
            if (v14.getCode() == ResultCode.FAIL) {
                return v14;
            }*/
        }
        return v14;
    }

    /**
     * 判断平台商品记录是否存在
     *
     * @param numiid
     * @param skuId
     * @param cpCShopId
     * @param loginUser
     * @return
     */
    private ValueHolderV14 checktNumiidAndSkuId(String numiid, String skuId, Long cpCShopId, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        //
        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelStorageIncSyncSaveValidator.checktNumiidAndSkuId:numiid={},skuId={},cpCShopId={};",
                    numiid, skuId, cpCShopId);
        }
        //平台条码Id或平台商品Id + 平台店铺ID 判断
        Integer countNumiSkuId = productMapper.selectCount(new QueryWrapper<SgBChannelProduct>().lambda()
                .eq(SgBChannelProduct::getCpCShopId, cpCShopId)
                .eq(StringUtils.isNotEmpty(skuId), SgBChannelProduct::getSkuId, skuId)
                .eq(StringUtils.isNotEmpty(numiid), SgBChannelProduct::getNumiid, numiid)
                .eq(SgBChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (countNumiSkuId <= 0) {
            return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("平台商品记录不存在，不允许保存！", loginUser.getLocale()));
        }
        return v14;
    }

    /**
     * 主表校验
     *
     * @param mainObject
     * @param loginUser
     * @return
     */
    private ValueHolderV14 checkMainTableBatchNO(SgBChannelStorageIncSyncDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        Integer count = storageIncSyncMapper.selectCount(new QueryWrapper<SgBChannelStorageIncSync>().lambda()
                .eq(SgBChannelStorageIncSync::getBatchNo, mainObject.getBatchNo())
                .eq(SgBChannelStorageIncSync::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (count > 0) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("批次号不能重复!", loginUser.getLocale()));
        }
        return v14;
    }

    /**
     * 校验明细表的平台条码ID和平台商品ID
     *
     * @param skuId
     * @param numId
     * @param loginUser
     * @return
     */

    private ValueHolderV14 checkMainTableSkuIdNumiId(String skuId, String numId, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));

        if (StringUtils.isEmpty(skuId) && StringUtils.isEmpty(numId)) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("平台条码ID或平台商品ID不能同时为空!", loginUser.getLocale()));
        }
        if (StringUtils.isNotEmpty(skuId) && StringUtils.isNotEmpty(numId)) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("平台条码ID和平台商品ID不能同时存在!", loginUser.getLocale()));
        }
        return v14;
    }
}
