package com.burgeon.r3.sg.channel.filter.storage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgCChannelStoreSafetySettingItemMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgCChannelStoreSafetySettingMapper;
import com.burgeon.r3.sg.channel.model.dto.storage.SgCChannelStoreSafetySettingDto;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgCChannelStoreSafetySetting;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgCChannelStoreSafetySettingItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Description 平台店铺安全库存审核
 * <AUTHOR>
 * @Date 2021/6/23 14:23
 * @Version 1.0
 **/

@Slf4j
@Component
public class SgCChannelStoreSafetySettingSubmitFilter extends BaseSingleFilter<SgCChannelStoreSafetySettingDto> {

    @Autowired
    private SgCChannelStoreSafetySettingMapper storeSafetySettingMapper;

    @Autowired
    private SgCChannelStoreSafetySettingItemMapper storeSafetySettingItemMapper;

    @Autowired
    private SgBChannelProductMapper sgBChannelProductMapper;

    @Override
    public String getFilterMsgName() {
        return "平台店铺安全库存审核";
    }

    @Override
    public Class<?> getFilterClass() {
        return SgCChannelStoreSafetySettingSubmitFilter.class;
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgCChannelStoreSafetySettingDto mainObject, User loginUser) {
        List<SgCChannelStoreSafetySettingItem> sgCChannelStoreSafetySettingItems = storeSafetySettingItemMapper.selectList(new LambdaQueryWrapper<SgCChannelStoreSafetySettingItem>()
                .eq(SgCChannelStoreSafetySettingItem::getSgCChannelStoreSafetySettingId, mainObject.getId()));
        if(CollectionUtils.isEmpty(sgCChannelStoreSafetySettingItems)){
            return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("明细为空，不允许审核！", loginUser.getLocale()));
        }
        Long loginUserId = loginUser.getId() == null ? null : loginUser.getId().longValue();
        for (SgCChannelStoreSafetySettingItem item : sgCChannelStoreSafetySettingItems) {
            SgBChannelProduct updateSgBChannelProduct = new SgBChannelProduct();
            updateSgBChannelProduct.setQtySafety(item.getQtySafety());


            updateSgBChannelProduct.setModifierid(loginUserId);
            updateSgBChannelProduct.setModifierename(loginUser.getEname());
            updateSgBChannelProduct.setModifieddate(new Date());
            LambdaUpdateWrapper<SgBChannelProduct> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(SgBChannelProduct::getCpCShopId, item.getCpCShopId());

            if(SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_PRO_TYPE_PS_C_PRO.equals(item.getProType())){
                //（1:商品编码 ）
                wrapper.eq(SgBChannelProduct::getPsCProId, item.getPsCProId());

            }else if(SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_PRO_TYPE_PS_C_SKU.equals(item.getProType())){
                //（2:条码 ）
                wrapper.eq(SgBChannelProduct::getPsCSkuId, item.getPsCSkuId());
            }else if(SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_PRO_TYPE_NUMIID.equals(item.getProType())){
                //（3:平台商品 ）
                wrapper.eq(SgBChannelProduct::getNumiid, item.getNumiid());
            }else if(SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_PRO_TYPE_SKU_ID.equals(item.getProType())){
                //（4:平台条码）
                wrapper.eq(SgBChannelProduct::getSkuId, item.getSkuId());
            }
            sgBChannelProductMapper.update(updateSgBChannelProduct,wrapper);
        }

        mainObject.setStatus(SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_STATUS_SUBMIT);
        mainObject.setStatusId(loginUserId);
        mainObject.setStatusEname(loginUser.getEname());
        mainObject.setStatusTime(new Date());

        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("before main table success"));
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgCChannelStoreSafetySettingDto mainObject, User loginUser) {
        SgCChannelStoreSafetySetting sgCChannelStoreSafetySetting = storeSafetySettingMapper.selectById(mainObject.getId());

        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("批次号："+sgCChannelStoreSafetySetting.getBatchNo()+" 审核成功"));
    }
}
