package com.burgeon.r3.sg.channel.filter.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategySaItemMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategySpItemMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelSkuStrategyDTO;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelSkuStrategySaItemDTO;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelSkuStrategySpItemDTO;
import com.burgeon.r3.sg.channel.utils.SgChannelProductUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.skustrategy.SgCChannelSkuStrategy;
import com.burgeon.r3.sg.core.model.table.channel.skustrategy.SgCChannelSkuStrategySaItem;
import com.burgeon.r3.sg.core.model.table.channel.skustrategy.SgCChannelSkuStrategySpItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseMultiItemsFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/8 14:53
 */
@Slf4j
@Component
public class SgCChannelSkuStrategySaveFilter extends BaseMultiItemsFilter<SgCChannelSkuStrategyDTO> {

    @Autowired
    private SgCChannelSkuStrategySaItemMapper sgChannelSkuStrategySaItemMapper;
    @Autowired
    private SgCChannelSkuStrategySpItemMapper sgChannelSkuStrategySpItemMapper;
    @Autowired
    private SgCChannelRatioStrategyMapper sgChannelRatioStrategyMapper;


    @Override
    public String getFilterMsgName() {
        return "特殊条码按比例同步策略保存";
    }

    @Override
    public Class getFilterClass() {
        return SgCChannelSkuStrategySaveFilter.class;
    }

    @Override
    public Class<?> getSubTableClass(String subTableName) {
        if (SgConstants.SG_C_CHANNEL_SKU_STRATEGY_SA_ITEM.toUpperCase().equalsIgnoreCase(subTableName)) {
            return SgCChannelSkuStrategySaItemDTO.class;
        } else if (SgConstants.SG_C_CHANNEL_SKU_STRATEGY_SP_ITEM.toUpperCase().equalsIgnoreCase(subTableName)) {
            return SgCChannelSkuStrategySpItemDTO.class;
        }
        return null;
    }

    @Override
    public ValueHolderV14 execBeforeTable(SgCChannelSkuStrategyDTO mainObject, Map subObjectMap, User loginUser) {
        if (mainObject.getId() < 1L) {
            CpShop cpShop = CommonCacheValUtils.getShopInfo(mainObject.getCpCShopId());
            if (cpShop != null) {
                mainObject.setCpCShopEcode(cpShop.getEcode());
                mainObject.setCpCShopEname(cpShop.getCpCShopTitle());
            } else {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("平台店铺已不存在！", loginUser.getLocale()));
            }
        } else {
            mainObject.setCpCShopId(getOrignalData().getCpCShopId());
            mainObject.setCpCShopEcode(getOrignalData().getCpCShopEcode());
            mainObject.setCpCShopEname(getOrignalData().getCpCShopEname());
        }
        if (MapUtils.isNotEmpty(subObjectMap)) {
            if (subObjectMap.get(SgConstants.SG_C_CHANNEL_SKU_STRATEGY_SA_ITEM.toUpperCase()) != null) {
                List<SgCChannelSkuStrategySaItemDTO> saItemList =
                        (ArrayList<SgCChannelSkuStrategySaItemDTO>) subObjectMap.get(SgConstants.SG_C_CHANNEL_SKU_STRATEGY_SA_ITEM.toUpperCase());
                if (CollectionUtils.isNotEmpty(saItemList)) {
                    ValueHolderV14 saVh = checkSaItemAndSetValue(saItemList, loginUser, mainObject.getCpCShopId());
                    if (saVh != null) {
                        return saVh;
                    }
                }
            }
            if (subObjectMap.get(SgConstants.SG_C_CHANNEL_SKU_STRATEGY_SP_ITEM.toUpperCase()) != null) {
                List<SgCChannelSkuStrategySpItemDTO> spItemList =
                        (ArrayList<SgCChannelSkuStrategySpItemDTO>) subObjectMap.get(SgConstants.SG_C_CHANNEL_SKU_STRATEGY_SP_ITEM.toUpperCase());
                if (CollectionUtils.isNotEmpty(spItemList)) {
                    ValueHolderV14 spVh = checkSpItemAndSetValue(spItemList, loginUser, mainObject);
                    if (spVh != null) {
                        return spVh;
                    }
                }
            }
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("保存成功！"));
    }

    @Override
    public ValueHolderV14 execAfterTable(SgCChannelSkuStrategyDTO mainObject, Map subObjectMap, User loginUser) {
        return null;
    }

    /**
     * 独享库存明细信息填充冗余信息
     *
     * @param subObjectList 明细集合
     * @param loginUser     用户
     */
    private ValueHolderV14 checkSaItemAndSetValue(List<SgCChannelSkuStrategySaItemDTO> subObjectList, User loginUser,
                                                  Long shopId) {
        if (CollectionUtils.isNotEmpty(subObjectList)) {
            Long mainId = getOrignalData().getId();
            Map<String, List<SgCChannelSkuStrategySaItemDTO>> storeSkuMap =
                    subObjectList.stream().collect(Collectors.groupingBy(e -> e.getSgCSaStoreId() + e.getSkuId()));
            Set<Map.Entry<String, List<SgCChannelSkuStrategySaItemDTO>>> entrySet = storeSkuMap.entrySet();
            for (Map.Entry<String, List<SgCChannelSkuStrategySaItemDTO>> stringListEntry : entrySet) {
                AssertUtils.cannot(stringListEntry.getValue().size() > 1,
                        "保存失败!存在相同配销仓和相同平台条码ID:" + stringListEntry.getValue().get(0).getSkuId() + "请核对数据!");
            }
            for (SgCChannelSkuStrategySaItemDTO skuStrategySaItemDto : subObjectList) {
                if (mainId != null) {
                    SgCChannelSkuStrategySaItem skuStrategySaItem =
                            sgChannelSkuStrategySaItemMapper.selectOne(new LambdaQueryWrapper<SgCChannelSkuStrategySaItem>()
                                    .eq(SgCChannelSkuStrategySaItem::getSgCSaStoreId,
                                            skuStrategySaItemDto.getSgCSaStoreId())
                                    .eq(SgCChannelSkuStrategySaItem::getSgCChannelSkuStrategyId, mainId)
                                    .eq(SgCChannelSkuStrategySaItem::getSkuId, skuStrategySaItemDto.getSkuId()));
                    if (skuStrategySaItem != null) {
                        skuStrategySaItemDto.setId(skuStrategySaItem.getId());
                        StorageUtils.setBModelDefalutDataByUpdate(skuStrategySaItemDto, loginUser);
                        skuStrategySaItemDto.setModifierename(loginUser.getEname());
                        continue;
                    }
                }
                if (skuStrategySaItemDto.getSgCSaStoreId() != null) {
                    //查询配销仓
                    SgCSaStore saStore = CommonCacheValUtils.getSaStore(skuStrategySaItemDto.getSgCSaStoreId());
                    CpShop cpShop = CommonCacheValUtils.getShopInfo(shopId);
                    if (saStore != null && cpShop != null) {
                        skuStrategySaItemDto.setSgCSaStoreEcode(saStore.getEcode());
                        skuStrategySaItemDto.setSgCSaStoreEname(saStore.getEname());
                        //2021 8.2增加配销仓类型，取值来源配销仓档案中的类型
                        skuStrategySaItemDto.setSaStoreType(saStore.getType());
                    } else {
                        //execBeforeTable做了对平台店铺的校验提示
                        return new ValueHolderV14<>(ResultCode.FAIL,
                                Resources.getMessage("当前配销仓已不存在！", loginUser.getLocale()));
                    }
                    if (StringUtils.isNotBlank(skuStrategySaItemDto.getSkuId())) {
                        //查询平台店铺商品表赋值
                        SgBChannelProduct product =
                                SgChannelProductUtils.getChannelProduct(skuStrategySaItemDto.getSkuId(), shopId);
                        if (product != null) {
                            skuStrategySaItemDto.setProId(product.getNumiid());
                            skuStrategySaItemDto.setPsCSkuId(product.getPsCSkuId());
                            skuStrategySaItemDto.setPsCSkuEcode(product.getPsCSkuEcode());
                            skuStrategySaItemDto.setGbcode(product.getGbcode());
                            skuStrategySaItemDto.setPsCProId(product.getPsCProId());
                            skuStrategySaItemDto.setPsCProEcode(product.getPsCProEcode());
                            skuStrategySaItemDto.setPsCProEname(product.getPsCProEname());
                            skuStrategySaItemDto.setPsCSpec1Id(product.getPsCSpec1Id());
                            skuStrategySaItemDto.setPsCSpec1Ecode(product.getPsCSpec1Ecode());
                            skuStrategySaItemDto.setPsCSpec1Ename(product.getPsCSpec1Ename());
                            skuStrategySaItemDto.setPsCSpec2Id(product.getPsCSpec2Id());
                            skuStrategySaItemDto.setPsCSpec2Ecode(product.getPsCSpec2Ecode());
                            skuStrategySaItemDto.setPsCSpec2Ename(product.getPsCSpec2Ename());
                        } else {
                            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage(
                                    "当前平台条码ID:" + skuStrategySaItemDto.getSkuId() + "在平台店铺商品表不存在！",
                                    loginUser.getLocale()));
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * 共享库存明细信息填充冗余信息
     *
     * @param subObjectList 明细集合
     * @param loginUser     用户
     */
    private ValueHolderV14 checkSpItemAndSetValue(List<SgCChannelSkuStrategySpItemDTO> subObjectList, User loginUser,
                                                  SgCChannelSkuStrategy mainTable) {
        if (CollectionUtils.isNotEmpty(subObjectList)) {
            if (subObjectList.size() > 1) {
                Map<String, List<SgCChannelSkuStrategySpItemDTO>> skuSpItemMap =
                        subObjectList.stream().collect(Collectors.groupingBy(SgCChannelSkuStrategySpItem::getSkuId));
                Set<Map.Entry<String, List<SgCChannelSkuStrategySpItemDTO>>> entrySet = skuSpItemMap.entrySet();
                for (Map.Entry<String, List<SgCChannelSkuStrategySpItemDTO>> stringListEntry : entrySet) {
                    AssertUtils.cannot(stringListEntry.getValue().size() > 1,
                            "保存失败!存在相同平台条码ID:" + stringListEntry.getKey() + "请核对数据!");
                }
            }
            Long mainId = getOrignalData().getId();
            for (SgCChannelSkuStrategySpItemDTO skuStrategySpItemDto : subObjectList) {
                if (mainId != null) {
                    SgCChannelSkuStrategySpItem skuStrategySpItem =
                            sgChannelSkuStrategySpItemMapper.selectOne(new LambdaQueryWrapper<SgCChannelSkuStrategySpItem>()
                                    .eq(SgCChannelSkuStrategySpItem::getSkuId, skuStrategySpItemDto.getSkuId())
                                    .eq(SgCChannelSkuStrategySpItem::getSgCChannelSkuStrategyId, mainId)
                                    .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
                    if (skuStrategySpItem != null) {
                        skuStrategySpItemDto.setId(skuStrategySpItem.getId());
                        StorageUtils.setBModelDefalutDataByUpdate(skuStrategySpItemDto, loginUser);
                        skuStrategySpItemDto.setModifierename(loginUser.getEname());
                        continue;
                    }
                }
                //根据当前的平台店铺获取其在【按比例同步策略】的共享池
                if (skuStrategySpItemDto.getId() < 1L) {
//                    SgCChannelRatioStrategy sgChannelRatioStrategy =
//                            sgChannelRatioStrategyMapper.selectOne(new LambdaQueryWrapper<SgCChannelRatioStrategy>()
//                                    .eq(SgCChannelRatioStrategy::getCpCShopId, mainTable.getCpCShopId())
//                                    .eq(SgCChannelRatioStrategy::getIsactive, SgConstants.IS_ACTIVE_Y));
//                    if (sgChannelRatioStrategy != null) {
//                        skuStrategySpItemDto.setSgCSharePoolId(sgChannelRatioStrategy.getSgCSharePoolId());
//                        skuStrategySpItemDto.setSgCSharePoolEcode(sgChannelRatioStrategy.getSgCSharePoolEcode());
//                        skuStrategySpItemDto.setSgCSharePoolEname(sgChannelRatioStrategy.getSgCSharePoolEname());
//                    } else {
//                        return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage(
//                                "当前店铺：" + mainTable.getCpCShopEname() + "不存在供货的共享池！",
//                                loginUser.getLocale()));
//                    }
                    if (StringUtils.isNotBlank(skuStrategySpItemDto.getSkuId())) {
                        //查询平台店铺商品表赋值
                        SgBChannelProduct product =
                                SgChannelProductUtils.getChannelProduct(skuStrategySpItemDto.getSkuId());
                        if (product != null) {
                            skuStrategySpItemDto.setProId(product.getNumiid());
                            skuStrategySpItemDto.setPsCSkuId(product.getPsCSkuId());
                            skuStrategySpItemDto.setPsCSkuEcode(product.getPsCSkuEcode());
                            skuStrategySpItemDto.setGbcode(product.getGbcode());
                            skuStrategySpItemDto.setPsCProId(product.getPsCProId());
                            skuStrategySpItemDto.setPsCProEcode(product.getPsCProEcode());
                            skuStrategySpItemDto.setPsCProEname(product.getPsCProEname());
                            skuStrategySpItemDto.setPsCSpec1Id(product.getPsCSpec1Id());
                            skuStrategySpItemDto.setPsCSpec1Ecode(product.getPsCSpec1Ecode());
                            skuStrategySpItemDto.setPsCSpec1Ename(product.getPsCSpec1Ename());
                            skuStrategySpItemDto.setPsCSpec2Id(product.getPsCSpec2Id());
                            skuStrategySpItemDto.setPsCSpec2Ecode(product.getPsCSpec2Ecode());
                            skuStrategySpItemDto.setPsCSpec2Ename(product.getPsCSpec2Ename());
                        } else {
                            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage(
                                    "当前平台条码ID:" + skuStrategySpItemDto.getSkuId() + "在平台店铺商品表不存在！",
                                    loginUser.getLocale()));
                        }
                    }
                }
            }
        }
        return null;
    }
}
