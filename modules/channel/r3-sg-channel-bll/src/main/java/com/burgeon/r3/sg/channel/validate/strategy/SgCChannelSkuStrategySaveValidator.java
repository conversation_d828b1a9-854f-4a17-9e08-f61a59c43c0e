package com.burgeon.r3.sg.channel.validate.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategyMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategySaItemMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelSkuStrategyDTO;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelSkuStrategySaItemDTO;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelSkuStrategySpItemDTO;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.skustrategy.SgCChannelSkuStrategy;
import com.burgeon.r3.sg.core.model.table.channel.skustrategy.SgCChannelSkuStrategySaItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseMultiItemsValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/6/3 16:13
 */
@Slf4j
@Component
public class SgCChannelSkuStrategySaveValidator extends BaseMultiItemsValidator<SgCChannelSkuStrategyDTO> {
    @Autowired
    private SgCChannelSkuStrategyMapper sgChannelSkuStrategyMapper;
    @Autowired
    private SgCChannelSkuStrategySaItemMapper saItemMapper;

    @Override
    public String getValidatorMsgName() {
        return "特殊条码按比例同步策略保存";
    }

    @Override
    public Class getValidatorClass() {
        return SgCChannelSkuStrategySaveValidator.class;
    }

    @Override
    public Class<?> getSubTableClass(String subTableName) {
        if (SgConstants.SG_C_CHANNEL_SKU_STRATEGY_SA_ITEM.toUpperCase().equals(subTableName)) {
            return SgCChannelSkuStrategySaItemDTO.class;
        } else if (SgConstants.SG_C_CHANNEL_SKU_STRATEGY_SP_ITEM.toUpperCase().equals(subTableName)) {
            return SgCChannelSkuStrategySpItemDTO.class;
        }
        return null;
    }

    @Override
    public ValueHolderV14 validateTable(SgCChannelSkuStrategyDTO mainObject, Map subObjectMap, User loginUser) {
        if (StringUtils.isNotBlank(mainObject.getName())) {
            Integer count = sgChannelSkuStrategyMapper.selectCount(new LambdaQueryWrapper<SgCChannelSkuStrategy>()
                    .eq(SgCChannelSkuStrategy::getName, mainObject.getName())
                    .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (count > 0) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("方案名称已存在！",
                        loginUser.getLocale()));
            }
        }
        SgCChannelSkuStrategyDTO mainTable = getOrignalData();
        Date beginTime = mainObject.getBeginTime();
        Date endTime = mainObject.getEndTime();
        Long cpShopId = mainObject.getCpCShopId();
        if (beginTime != null || endTime != null || cpShopId != null) {
            if (beginTime == null) {
                beginTime = mainTable.getBeginTime();
            }
            if (endTime == null) {
                endTime = mainTable.getEndTime();
            }
            if (beginTime.after(endTime)) {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("开始时间不允许大于结束时间",
                        loginUser.getLocale()));
            }
            if (cpShopId != null) {
                cpShopId = mainTable.getCpCShopId();
            }
            ValueHolderV14 vh = checkShopAndTime(cpShopId, beginTime, endTime, loginUser);
            if (vh != null) {
                return vh;
            }
        }
        if (MapUtils.isNotEmpty(subObjectMap)) {
            if (subObjectMap.get(SgConstants.SG_C_CHANNEL_SKU_STRATEGY_SA_ITEM.toUpperCase()) != null) {
                List<SgCChannelSkuStrategySaItemDTO> saItemList =
                        (ArrayList<SgCChannelSkuStrategySaItemDTO>) subObjectMap.get(SgConstants.SG_C_CHANNEL_SKU_STRATEGY_SA_ITEM.toUpperCase());
                if (CollectionUtils.isNotEmpty(saItemList)) {

                    if (mainTable.getId() != null && mainTable.getId() > 0L) {
                        LambdaQueryWrapper<SgCChannelSkuStrategySaItem> wrapper = new LambdaQueryWrapper<>();
                        wrapper.eq(SgCChannelSkuStrategySaItem::getSgCChannelSkuStrategyId, mainObject.getId());
                        wrapper.eq(SgCChannelSkuStrategySaItem::getIsactive, SgConstants.IS_ACTIVE_Y);
                        List<SgCChannelSkuStrategySaItem> oldSaItemList = saItemMapper.selectList(wrapper);

                        if (CollectionUtils.isNotEmpty(oldSaItemList)) {
                            for (SgCChannelSkuStrategySaItemDTO saItem : saItemList) {
                                if (saItem.getPriority() != null) {
                                    for (SgCChannelSkuStrategySaItem oldStrategy : oldSaItemList) {
                                        if (oldStrategy.getPriority().compareTo(saItem.getPriority()) == 0) {
                                            return new ValueHolderV14<>(ResultCode.FAIL,
                                                    Resources.getMessage("寻源优先级不能重复！", loginUser.getLocale()));
                                        }
                                    }
                                }
                            }
                        }
                    }

                    ValueHolderV14 saVh = checkSaItem(saItemList, loginUser);
                    if (saVh != null) {
                        return saVh;
                    }
                }
            }
            if (subObjectMap.get(SgConstants.SG_C_CHANNEL_SKU_STRATEGY_SP_ITEM.toUpperCase()) != null) {
                List<SgCChannelSkuStrategySpItemDTO> spItemList =
                        (ArrayList<SgCChannelSkuStrategySpItemDTO>) subObjectMap.get(SgConstants.SG_C_CHANNEL_SKU_STRATEGY_SP_ITEM.toUpperCase());
                if (CollectionUtils.isNotEmpty(spItemList)) {
                    ValueHolderV14 spVh = checkSpItem(spItemList, loginUser);
                    if (spVh != null) {
                        return spVh;
                    }
                }
            }
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("主表校验通过"));
    }

    /**
     * 校验是否存在未审核单据平台店铺、开始时间、结束时间与当前记录开始时间、结束时间存在时间交叉记录
     *
     * @param shopId    平台店铺id
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param user      用户
     * @return ValueHolderV14
     */
    public ValueHolderV14 checkShopAndTime(Long shopId, Date beginTime, Date endTime, User user) {
        SgCChannelSkuStrategy skuStrategy =
                sgChannelSkuStrategyMapper.selectOne(new LambdaQueryWrapper<SgCChannelSkuStrategy>()
                        .eq(SgCChannelSkuStrategy::getCpCShopId, shopId)
                        .eq(SgCChannelSkuStrategy::getStatus,
                                SgChannelConstants.BILL_CHANNEL_STRATEGY_SUBMIT)
                        .and(wrapper -> wrapper.between(SgCChannelSkuStrategy::getBeginTime, beginTime, endTime)
                                .or().between(SgCChannelSkuStrategy::getEndTime, beginTime, endTime)));
        if (skuStrategy != null) {
            return new ValueHolderV14<>(ResultCode.FAIL,
                    Resources.getMessage("平台店铺:" + skuStrategy.getCpCShopEname() +
                            "当前时间已存在有效的特殊条码按比例同步策略，不允许重复设置！", user.getLocale()));
        }
        return null;
    }

    /**
     * 校验独享库存明细
     *
     * @param saItemList 独享库存明细
     * @param loginUser  用户
     * @return ValueHolderV14
     */
    private ValueHolderV14 checkSaItem(List<SgCChannelSkuStrategySaItemDTO> saItemList, User loginUser) {
        for (SgCChannelSkuStrategySaItemDTO saItemDto : saItemList) {
            BigDecimal ratio = saItemDto.getRatio();
            if (ratio != null && ratio.compareTo(BigDecimal.ZERO) < 0) {
                return new ValueHolderV14<>(ResultCode.FAIL,
                        Resources.getMessage("比例（%）请设置0或以上的数字！", loginUser.getLocale()));
            }
        }
        return null;
    }

    /**
     * 校验共享库存明细
     *
     * @param spItemList 共享库存明细
     * @param loginUser  用户
     * @return ValueHolderV14
     */
    private ValueHolderV14 checkSpItem(List<SgCChannelSkuStrategySpItemDTO> spItemList, User loginUser) {
        for (SgCChannelSkuStrategySpItemDTO spItemDto : spItemList) {
            BigDecimal ratio = spItemDto.getRatio();
            if (ratio != null && ratio.compareTo(BigDecimal.ZERO) < 0) {
                return new ValueHolderV14<>(ResultCode.FAIL,
                        Resources.getMessage("比例（%）请设置0或以上的数字！", loginUser.getLocale()));
            }
        }
        return null;
    }
}
