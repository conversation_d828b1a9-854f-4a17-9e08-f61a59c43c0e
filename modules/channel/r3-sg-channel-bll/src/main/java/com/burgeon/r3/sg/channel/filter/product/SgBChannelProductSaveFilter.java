package com.burgeon.r3.sg.channel.filter.product;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.stocksync.api.SgChannelStorageOmsInitCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageOmsManualSynchRequest;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleFilter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/15 13:20
 */
@Slf4j
@Component
public class SgBChannelProductSaveFilter extends BaseSingleFilter<SgBChannelProduct> {

    @Autowired
    private SgBChannelProductMapper sgBChannelProductMapper;

    @Override
    public String getFilterMsgName() {
        return "平台店铺商品表保存后同步库存";
    }

    @Override
    public Class<?> getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgBChannelProduct mainObject, User loginUser) {
        ValueHolderV14 resultHolder = new ValueHolderV14(ResultCode.SUCCESS, "平台店铺商品表触发库存同步成功");
        List<SgChannelStorageOmsManualSynchRequest> result = new ArrayList<>();
        SgChannelStorageOmsManualSynchRequest requestItem = new SgChannelStorageOmsManualSynchRequest();
        requestItem.setOperate(SgChannelStorageOmsManualSynchRequest.QUERY_BY_CONDITION);
        if (mainObject.getId() > 0L) {
            SgBChannelProduct sgBChannelProduct = sgBChannelProductMapper.selectById(mainObject.getId());
            if (Objects.isNull(sgBChannelProduct)) {
                resultHolder.setCode(ResultCode.FAIL);
                resultHolder.setMessage("此平台店铺Id异常");
                return resultHolder;
            }
            //安全库存更改
            boolean storageChange = (Objects.nonNull(sgBChannelProduct.getQtySafety()) &&
                    !sgBChannelProduct.getQtySafety().equals(mainObject.getQtySafety())) ||
                    (Objects.isNull(sgBChannelProduct.getQtySafety()) && Objects.nonNull(mainObject.getQtySafety()));
            //配销仓类型更改
            boolean saStoreChange = (Objects.nonNull(sgBChannelProduct.getSaStoreType()) &&
                    !sgBChannelProduct.getSaStoreType().equals(mainObject.getSaStoreType()));
            //是否锁定，从否变成是，立即同步
            boolean lockChange = SgConstants.IS_AUTO_N.equals(mainObject.getIslock()) &&
                    SgConstants.IS_AUTO_Y.equals(mainObject.getIslock());
            if (storageChange || saStoreChange || lockChange) {
                requestItem.setFixedQty(mainObject.getQtySafety());
//                requestItem.setSyncType(SgConstants.SYNC_STORE_TYPE_SHARE);
                requestItem.setCpCShopId(sgBChannelProduct.getCpCShopId());
                requestItem.setSkuId(String.valueOf(sgBChannelProduct.getSkuId()));
                requestItem.setSourceno("平台店铺商品表触发库存同步" + sgBChannelProduct.getCpCShopId());
            }
        } else {
            //特指新链接
            LambdaQueryWrapper<SgBChannelProduct> eq = new LambdaQueryWrapper<SgBChannelProduct>()
                    .eq(SgBChannelProduct::getDetailUrl, mainObject.getDetailUrl())
                    .eq(SgBChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y);
            List<SgBChannelProduct> sgBChannelProducts = sgBChannelProductMapper.selectList(eq);
            if (CollectionUtils.isEmpty(sgBChannelProducts)) {
                requestItem.setFixedQty(mainObject.getQtySafety());
//                requestItem.setSyncType(SgConstants.SYNC_STORE_TYPE_SHARE);
                requestItem.setSkuId(String.valueOf(mainObject.getSkuId()));
                requestItem.setCpCShopId(mainObject.getCpCShopId());
                requestItem.setSourceno("平台店铺商品表触发库存同步" + mainObject.getCpCShopId());
            }
        }
        result.add(requestItem);
        //rpc请求库存同步，告知同步库存
        if (CollectionUtils.isEmpty(result)) {
            resultHolder.setCode(ResultCode.FAIL);
            resultHolder.setMessage("平台店铺商品同步参数为空，不触发同步");
            return resultHolder;
        }
        syncStock(result, loginUser);
        return resultHolder;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgBChannelProduct mainObject, User loginUser) {
        return null;
    }

    /**
     * 库存同步
     *
     * @param loginUser 操作用户
     * @param list      主表信息
     */
    public void syncStock(List<SgChannelStorageOmsManualSynchRequest> list, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("平台店铺商品表触发同步：request = {}", JSONObject.toJSONString(list));
        }

        SgChannelStorageOmsInitCmd sgChannelStorageOmsInitCmd = (SgChannelStorageOmsInitCmd) ReferenceUtil.refer(
                ApplicationContextHandle.getApplicationContext(),
                SgChannelStorageOmsInitCmd.class.getName(),
                SgConstantsIF.GROUP, SgConstantsIF.VERSION);

        list.forEach(f -> {
            f.setUser(loginUser);
            ValueHolderV14<Boolean> booleanValueHolderV14 = sgChannelStorageOmsInitCmd.manualCalcAndSyncChannelProduct(f);
            if (log.isDebugEnabled()) {
                JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(booleanValueHolderV14.toJSONObject(),
                        SerializerFeature.WriteMapNullValue));
                log.debug("平台店铺商品表 库存并同步结果" + result.toJSONString());
            }
        });
    }
}
