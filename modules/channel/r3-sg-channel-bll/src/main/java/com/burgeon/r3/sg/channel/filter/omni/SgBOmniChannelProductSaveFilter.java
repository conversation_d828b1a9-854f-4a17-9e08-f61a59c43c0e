package com.burgeon.r3.sg.channel.filter.omni;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.model.dto.omni.SgBOmniChannelProductDto;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.stocksync.api.SgBChannelStorageBufferCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferBatchSaveRequest;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferSaveRequest;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/13
 */
@Slf4j
@Component
public class SgBOmniChannelProductSaveFilter extends BaseSingleFilter<SgBOmniChannelProductDto> {

    @Reference(group = "sg", version = "1.0")
    private SgBChannelStorageBufferCmd channelStorageBufferCmd;

    @Autowired
    private SgBChannelProductMapper channelProductMapper;

    @Override
    public String getFilterMsgName() {
        return "全渠道商品保存后同步库存";
    }

    @Override
    public Class getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgBOmniChannelProductDto mainObject, User loginUser) {
        return null;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgBOmniChannelProductDto omniChannelProductDto, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("全渠道商品新增成功！",
                loginUser.getLocale()));

        SgBOmniChannelProductDto orignalData = getOrignalData();

        if (log.isDebugEnabled()) {
            log.debug("Start SgBOmniChannelProductSaveFilter.execAfterMainTable:request={}",
                    JSONObject.toJSONString(omniChannelProductDto));
        }

        Long shopId = omniChannelProductDto.getCpCShopId() == null ?
                orignalData.getCpCShopId() : omniChannelProductDto.getCpCShopId();
        String numiid = omniChannelProductDto.getNumiid() == null ?
                orignalData.getNumiid() : omniChannelProductDto.getNumiid();

        LambdaQueryWrapper<SgBChannelProduct> productWrapper = new LambdaQueryWrapper<>();
        productWrapper.eq(SgBChannelProduct::getCpCShopId, shopId);
        productWrapper.eq(SgBChannelProduct::getNumiid, numiid);
        productWrapper.eq(SgBChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgBChannelProduct> productList = channelProductMapper.selectList(productWrapper);

        if (CollectionUtils.isEmpty(productList)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("当前店铺无可用商品！");
            return vh;
        }
        //修改平台店铺商品表中的是否同城购物
        //left join sg_b_omni_channel_product c on a.cp_c_shop_id = c.cp_c_shop_id and a.numiid = c.numiid and c.isactive='y' and c.is_tmll_city = 'y' ",
        if (log.isDebugEnabled()) {
            log.debug("SgBOmniChannelProductSaveFilter.execAfterMainTable:getIsTmllCity={}", omniChannelProductDto.getIsTmllCity());
        }
        SgBChannelProduct sgBChannelProduct = new SgBChannelProduct();
        sgBChannelProduct.setIsTmllCity(omniChannelProductDto.getIsTmllCity());
        channelProductMapper.update(sgBChannelProduct, new LambdaQueryWrapper<SgBChannelProduct>()
                .eq(SgBChannelProduct::getCpCShopId, shopId)
                .eq(SgBChannelProduct::getNumiid, numiid)
                .eq(SgBChannelProduct::getIsactive, SgConstants.IS_ACTIVE_Y));

        SgChannelStorageBufferBatchSaveRequest request = new SgChannelStorageBufferBatchSaveRequest();
        List<SgChannelStorageBufferSaveRequest> requestItemList = new ArrayList<>();
        productList.forEach(product -> {
            SgChannelStorageBufferSaveRequest requestItem = new SgChannelStorageBufferSaveRequest();
            requestItem.setCpCShopId(shopId);
            requestItem.setPsCSkuId(product.getPsCSkuId());

            requestItemList.add(requestItem);
        });

        request.setUser(loginUser);
        request.setBufferSaveRequestList(requestItemList);

        if (log.isDebugEnabled()) {
            log.debug("Start SgBOmniChannelProductSaveFilter.saveDataToChannelStorageBuffer request={}",
                    JSONObject.toJSONString(request));
        }

        channelStorageBufferCmd.saveDataToChannelStorageBuffer(request);

        return vh;
    }
}
