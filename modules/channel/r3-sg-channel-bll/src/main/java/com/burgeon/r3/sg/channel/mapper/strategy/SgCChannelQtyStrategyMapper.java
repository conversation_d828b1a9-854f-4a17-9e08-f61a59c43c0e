package com.burgeon.r3.sg.channel.mapper.strategy;

import com.burgeon.r3.sg.channel.model.request.strategy.SgCChanelQtyStrategyQueryInfoRequest;
import com.burgeon.r3.sg.channel.model.result.strategy.SgCChanelQtyStrategyQueryInfoResult;
import com.burgeon.r3.sg.core.model.table.channel.qtystrategy.SgCChannelQtyStrategy;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface SgCChannelQtyStrategyMapper extends ExtentionMapper<SgCChannelQtyStrategy> {

    /**
     * @param request:
     * @Description: 查询店铺的配销仓设置信息
     * @Author: hwy
     * @Date: 2021/6/19 14:08
     * @return: com.burgeon.r3.sg.channel.model.result.strategy.SgCChanelQtyStrategyQueryInfoResult
     **/
    @Select({
            "<script>",
            " SELECT ",
            " A.CP_C_SHOP_ID, ",
//            " A.BEGIN_TIME, ",
//            " A.END_TIME, ",
            " B.SG_C_SA_STORE_ID, ",
            " C.ORDERNO, ",
            " B.PS_C_SKU_ID, ",
            " B.SKU_ID, ",
            " B.PRO_ID ",
            " FROM ",
            " SG_C_CHANNEL_QTY_STRATEGY A ",
            " LEFT JOIN SG_C_CHANNEL_QTY_STRATEGY_ITEM B ON A.ID = B.SG_C_CHANNEL_QTY_STRATEGY_ID ",
            " LEFT JOIN SG_C_SA_STORE C ON B.SG_C_SA_STORE_ID = C.ID ",
            " WHERE A.ISACTIVE ='Y' AND B.ISACTIVE = 'Y' ",
            " AND B.ISPUBLISH='Y' ",
            " <when test='request.cpCShopId != null ' >  ",
            " AND A.CP_C_SHOP_ID = #{request.cpCShopId,jdbcType=INTEGER} ",
            " </when>  ",
//            " <when test='request.beginTime != null ' >  ",
//            " AND A.BEGIN_TIME &lt;= #{request.beginTime,jdbcType=TIMESTAMP} ",
//            " </when>  ",
//            " <when test='request.endTime != null ' >  ",
//            " AND A.END_TIME &gt;= #{request.endTime,jdbcType=TIMESTAMP} ",
//            " </when>  ",
            " <when test='request.skuIds != null and request.skuIds.size>0 ' >  ",
            " AND B.SKU_ID IN",
            " <foreach item='item' collection='request.skuIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when>  ",
            " <when test='request.proIds != null and request.proIds.size>0 ' >  ",
            " AND B.PRO_ID  IN ",
            " <foreach item='item' collection='request.proIds' separator=',' open='(' close=')' > #{item,jdbcType=VARCHAR} </foreach>",
            " </when>  ",
            " <when test='request.psCSkuIds != null and request.psCSkuIds.size>0 ' >  ",
            " AND B.PS_C_SKU_ID IN ",
            " <foreach item='item' collection='request.psCSkuIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            " <when test='request.sgCSaStoreIds != null and request.sgCSaStoreIds.size>0 ' >  ",
            " AND B.SG_C_SA_STORE_ID IN",
            " <foreach item='item' collection='request.sgCSaStoreIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            " ORDER BY ISNULL(C.ORDERNO),C.ORDERNO  ",
            "</script>"
    })
    List<SgCChanelQtyStrategyQueryInfoResult> selectStrategyInfo(@Param("request") SgCChanelQtyStrategyQueryInfoRequest request);

    /**
     * @param request:
     * @Description: 查询店铺的配销仓设置信息
     * @Author: hwy
     * @Date: 2021/6/19 14:08
     * @return: com.burgeon.r3.sg.channel.model.result.strategy.SgCChanelQtyStrategyQueryInfoResult
     **/
    @Select({
            "<script>",
            " SELECT ",
            " A.CP_C_SHOP_ID, ",
//            " A.BEGIN_TIME, ",
//            " A.END_TIME, ",
            " B.SG_C_SA_STORE_ID, ",
            " C.ORDERNO, ",
            " B.PS_C_SKU_ID, ",
            " B.SKU_ID, ",
            " B.PRO_ID ",
            " FROM ",
            " SG_C_CHANNEL_QTY_STRATEGY A ",
            " LEFT JOIN SG_C_CHANNEL_QTY_STRATEGY_ITEM B ON A.ID = B.SG_C_CHANNEL_QTY_STRATEGY_ID ",
            " LEFT JOIN SG_C_SA_STORE C ON B.SG_C_SA_STORE_ID = C.ID ",
            " WHERE A.ISACTIVE ='Y' AND B.ISACTIVE = 'Y' AND B.ISPUBLISH='Y' AND C.SG_C_SHARE_STORE_ID = #{ssid} ",
            " <when test='request.cpCShopId != null ' >  ",
            " AND A.CP_C_SHOP_ID = #{request.cpCShopId,jdbcType=INTEGER} ",
            " </when>  ",
//            " <when test='request.beginTime != null ' >  ",
//            " AND A.BEGIN_TIME &lt; #{request.beginTime,jdbcType=TIMESTAMP} ",
//            " </when>  ",
//            " <when test='request.endTime != null ' >  ",
//            " AND A.END_TIME &gt; #{request.endTime,jdbcType=TIMESTAMP} ",
//            " </when>  ",
            " <when test='request.skuIds != null and request.skuIds.size>0 ' >  ",
            " AND B.SKU_ID IN",
            " <foreach item='item' collection='request.skuIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            " <when test='request.proIds != null and request.proIds.size>0 ' >  ",
            " AND B.PRO_ID  IN " +
                    " <foreach item='item' collection='request.proIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            " <when test='request.psCSkuIds != null and request.psCSkuIds.size>0 ' >  ",
            " AND B.PS_C_SKU_ID IN ",
            " <foreach item='item' collection='request.psCSkuIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            " <when test='request.sgCSaStoreIds != null and request.sgCSaStoreIds.size>0 ' >  ",
            " AND B.SG_C_SA_STORE_ID IN",
            " <foreach item='item' collection='request.sgCSaStoreIds' separator=',' open='(' close=')' > #{item,jdbcType=INTEGER} </foreach>",
            " </when>  ",
            " ORDER BY ISNULL(C.ORDERNO),C.ORDERNO  ",
            "</script>"
    })
    List<SgCChanelQtyStrategyQueryInfoResult> selectStrategyInfoBych(@Param("request") SgCChanelQtyStrategyQueryInfoRequest request,
                                                                     @Param("ssid") Long ssid);
}