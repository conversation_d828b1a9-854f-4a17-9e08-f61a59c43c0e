package com.burgeon.r3.sg.channel.mapper.product;

import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProductDownloadBuffer;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface SgBChannelProductDownloadBufferMapper extends ExtentionMapper<SgBChannelProductDownloadBuffer> {

    @Select("SELECT id,numiid FROM sg_b_channel_product_download_buffer where cp_c_shop_id=#{shopId} and " +
            "cp_c_platform_id=#{platformId} ")
    List<SgBChannelProductDownloadBuffer> queryUnSyncNumIdsByShopIdAndPlatformId(@Param("shopId") Long shopId,
                                                                                 @Param("platformId") Long platformId);

    @Insert("<script> " +
            "insert ignore into ${tableName}(id,cp_c_shop_id,ad_org_id,ad_client_id, cp_c_platform_id,numiid," +
            "modifieddate,creationdate,ownerid,ownername,ownerename,modifierid,modifiername,modifierename) " +
            "values" +
            "<foreach item='item' index='index' collection='downloadBuffers' separator=',' > " +
            "( #{item.id,jdbcType=INTEGER},#{item.cpCShopId,jdbcType=INTEGER},#{item.adOrgId,jdbcType=INTEGER}" +
            ",#{item.adClientId,jdbcType=INTEGER},#{item.cpCPlatformId,jdbcType=INTEGER},#{item.numiid,jdbcType=VARCHAR}" +
            ",now(),now(),#{item.ownerid,jdbcType=INTEGER},#{item.ownername,jdbcType=VARCHAR}" +
            ",#{item.ownerename,jdbcType=VARCHAR},#{item.modifierid,jdbcType=INTEGER},#{item.modifiername,jdbcType=VARCHAR},#{item.modifierename,jdbcType=VARCHAR})" +
            "</foreach>" +
            " </script> ")
    Integer batchInsertDownloadBuffersOnConfilict(@Param("tableName") String tableName,
                                                  @Param("downloadBuffers") List<SgBChannelProductDownloadBuffer> downloadBuffers);

    /**
     * 查询平台商品ID和平台店铺(商品状态 销售中)
     * @param platformIds 平台ids
     * @param downloadStatus 下载状态
     * @param downloadProductNum 分页大小
     * @return List<SgBChannelProductDownloadBuffer>
     */
    @Select({"<script>",
            "/*FORCE_SLAVE*/",
            "SELECT ",
            " id,numiid,cp_c_shop_id ",
            "FROM ",
            " sg_b_channel_product_download_buffer ",
            "<where> ",
            "<if test='platformIds != null and !platformIds.isEmpty()'>",
            "  cp_c_platform_id in ",
            " <foreach collection='platformIds' item='platformId' open='(' separator=',' close=')'> ",
            "#{platformId} </foreach>",
            " </if>",
            "<if test='shopIds != null and !shopIds.isEmpty()'>",
            " And cp_c_shop_id in ",
            " <foreach collection='shopIds' item='shopId' open='(' separator=',' close=')'> ",
            "#{shopId} </foreach>",
            " </if>",
            "<if test = 'downloadStatus != null'>",
            "AND (download_status = #{downloadStatus} or (download_status=1 and TIMESTAMPDIFF(HOUR,creationdate,NOW())>2))",
            "</if>",
            "</where>   ",
            " GROUP BY ",
            " numiid,cp_c_shop_id ",
            "LIMIT #{downloadProductNum};",
            "</script>"})
    List<SgBChannelProductDownloadBuffer> queryNeedDownloadNumId(@Param("shopIds") List<Long> shopIds,
                                                                 @Param("platformIds") List<Long> platformIds,
                                                                 @Param("downloadStatus") Integer downloadStatus,
                                                                 @Param("downloadProductNum") Integer downloadProductNum);

    /**
     * 更新平台下载库存缓存池失败数据
     * @param ids 主键id
     * @return 数量
     */
    @Update("<script>" +
            "update sg_b_channel_product_download_buffer  " +
            "set download_status = -1," +
            "download_fail_count = download_fail_count + 1 " +
            "<where> " +
            "<if test='ids != null and !ids.isEmpty()'>"+
            "  id in "+
            " <foreach collection='ids' item='id' open='(' separator=',' close=')'> "+
            "#{id} </foreach>"+
            " </if>"+
            "</where>   " +
            "</script>")
    int updateDownloadFailBuffers(@Param("ids") List<Long> ids);

}