package com.burgeon.r3.sg.channel.services.sale;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.sale.SgBChannelAdvanceSaleItemMapper;
import com.burgeon.r3.sg.channel.mapper.sale.SgBChannelAdvanceSaleMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.channel.sale.SgBChannelAdvanceSale;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/22 13:25
 */
@Slf4j
@Component
public class SgBChannelAdvanceSaleVoidService {

    @Autowired
    SgBChannelAdvanceSaleMapper mapper;
    @Autowired
    SgBChannelAdvanceSaleItemMapper itemMapper;


    /**
     * 渠道预售活动页面作废
     *
     * @param session 入参
     * @return 出参
     */
    ValueHolder voidAdvanceSale(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        SgBChannelAdvanceSaleVoidService service = ApplicationContextHandle.getBean(SgBChannelAdvanceSaleVoidService.class);
        return R3ParamUtils.convertV14WithResult(service.voidAdvanceSale(request));
    }

    /**
     * 渠道预售活动作废
     *
     * @param request 入参
     * @return 出参
     */
    public ValueHolderV14<SgR3BaseResult> voidAdvanceSale(SgR3BaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelAdvanceSaleVoidService.voidTransfer.ReceiveParams:request{}"
                    , JSONObject.toJSONString(request));
        }
        String lockKsy = SgConstants.SG_B_CHANNEL_ADVANCE_SALE + ":" + request.getObjId();
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "作废成功！");
        checkParams(request);
        SgRedisLockUtils.lock(lockKsy);
        try {
            SgBChannelAdvanceSale update = new SgBChannelAdvanceSale();
            StorageUtils.setBModelDefalutDataByUpdate(update, request.getLoginUser());
            update.setStatus(SgChannelConstants.BILL_STATUS_VOID);
            update.setIsactive(SgConstants.IS_ACTIVE_N);
            User user = request.getLoginUser();
            // 添加作废人相关信息
            update.setDelerId(user.getId().longValue());
            update.setDelerName(user.getName());
            update.setDelerEname(user.getEname());
            update.setDelTime(new Date());
            mapper.update(update, new QueryWrapper<SgBChannelAdvanceSale>().lambda().eq(SgBChannelAdvanceSale::getId,
                    request.getObjId()));

        } catch (Exception e) {
            AssertUtils.logAndThrowException(e.getMessage(), e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        return v14;
    }


    /**
     * 参数校验
     *
     * @param request 入参
     * @return 出参
     */
    private SgBChannelAdvanceSale checkParams(SgR3BaseRequest request) {
        SgBChannelAdvanceSale stoChannelAdvanceSale = mapper.selectById(request.getObjId());
        AssertUtils.notNull(stoChannelAdvanceSale, "当前记录已不存在！");
        if (SgConstants.IS_ACTIVE_N.equals(stoChannelAdvanceSale.getIsactive())) {
            AssertUtils.logAndThrow("当前记录已作废,不允许重复作废！", request.getLoginUser().getLocale());

        } else if (SgChannelConstants.BILL_STATUS_UNSUBMIT != stoChannelAdvanceSale.getStatus()) {
            AssertUtils.logAndThrow("当前单据状态不允许作废！", request.getLoginUser().getLocale());
        }

        return stoChannelAdvanceSale;
    }
}
