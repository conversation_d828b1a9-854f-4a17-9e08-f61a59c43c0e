package com.burgeon.r3.sg.channel.services.storage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgBChannelStorageIncSyncItemMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgBChannelStorageIncSyncMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgBChannelStorageIncSyncSuccessItemMapper;
import com.burgeon.r3.sg.core.common.R3ParamConstants;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBChannelStorageIncSync;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBChannelStorageIncSyncItem;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBChannelStorageIncSyncSuccessItem;
import com.burgeon.r3.sg.core.model.table.oms.SgBChannelStorageBuffer;
import com.burgeon.r3.sg.core.model.tableExtend.SgBChannelStorageBufferExtend;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.stocksync.api.SgBChannelStorageBufferCmd;
import com.burgeon.r3.sg.stocksync.api.SgChannelStorageConsumerCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferBatchSaveRequest;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferSaveRequest;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageOmsCalcRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BeanCopierUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * description：平台库存增量同步
 *
 * <AUTHOR>
 * @date 2021/8/5
 */
@Slf4j
@Component
public class SgBChannelStorageIncSyncService {
    @Autowired
    private SgBChannelStorageIncSyncMapper sgChannelStorageIncSyncMapper;

    @Autowired
    private SgBChannelStorageIncSyncItemMapper sgChannelStorageIncSyncItemMapper;

    @Reference(version = "1.0", group = "sg")
    private SgBChannelStorageBufferCmd sgBChannelStorageBufferCmd;

    @Autowired
    private SgBChannelProductMapper channelProductMapper;

    @Autowired
    private SgBChannelStorageIncSyncSuccessItemMapper successItemMapper;

    @Reference(version = "1.0", group = "sg")
    private SgChannelStorageConsumerCmd sgChannelStorageConsumerCmd;


    /**
     * 库存同步状态
     * 0-未同步
     * 1-同步中
     * 2-部分成功
     * 3-同步成功
     * 4-同步失败
     */
    public static final int WAIT_SYNC = 0;
    public static final int DOING_SYNC = 1;
    public static final int SUCCESS_PART = 2;
    public static final int SUCCESS_SYNC = 3;
    public static final int FAIL_SYNC = 4;

    /**
     * 平台库存全量同步 库存同步功能
     *
     * @param session 参数
     * @return ValueHolder
     */
    public ValueHolder stockSyncAction(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        return R3ParamUtils.convertV14WithResult(stockSync(request));
    }

    /**
     * 库存同步逻辑
     *
     * @param request r3请求参数
     * @return ValueHolderV14<SgR3BaseResult>
     */
    public ValueHolderV14<SgR3BaseResult> stockSync(SgR3BaseRequest request) {
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "同步成功！");
        if (log.isDebugEnabled()) {
            log.debug(" Start SgBChannelStorageIncSyncService.stockSync:request{}",
                    JSONObject.toJSONString(request));
        }
        List<Long> syncIds = new ArrayList<>();
        //支持列表批量和单对象
        if (CollectionUtils.isNotEmpty(request.getIds())) {
            syncIds = request.getIds();
        }
        if (request.getObjId() != null) {
            syncIds.add(request.getObjId());
        }
        if (CollectionUtils.isNotEmpty(syncIds)) {
            JSONArray errData = new JSONArray();
            //需要同步的数据
            List<SgBChannelStorageIncSync> storageSyncs = sgChannelStorageIncSyncMapper.selectBatchIds(syncIds);
            if (CollectionUtils.isNotEmpty(storageSyncs)) {
                List<SgBChannelStorageIncSyncItem> sgBChannelStorageIncSyncItems = sgChannelStorageIncSyncItemMapper
                        .selectList(new LambdaQueryWrapper<SgBChannelStorageIncSyncItem>()
                                .in(SgBChannelStorageIncSyncItem::getSgBChannelStorageIncSyncId, syncIds));
                Map<Long, SgBChannelStorageIncSync> syncMap =
                        storageSyncs.stream().collect(Collectors.toMap(SgBChannelStorageIncSync::getId,
                                Function.identity()));
                Map<Long, List<SgBChannelStorageIncSyncItem>> itemMap = sgBChannelStorageIncSyncItems.stream()
                        .collect(Collectors.groupingBy(SgBChannelStorageIncSyncItem::getSgBChannelStorageIncSyncId));

                for (Long syncId : syncIds) {
                    SgChannelStorageBufferBatchSaveRequest batchSaveRequest = new SgChannelStorageBufferBatchSaveRequest();
                    batchSaveRequest.setUser(request.getLoginUser());
                    List<SgChannelStorageBufferSaveRequest> bufferSaveRequestList = new ArrayList<>();
                    //校验
                    SgBChannelStorageIncSync storageIncSync = syncMap.get(syncId);
                    if (storageIncSync == null) {
                        StorageBasicUtils.errorRecord(syncId, "当前记录已不存在！", errData);
                        continue;
                    }
                    if (SgConstants.IS_ACTIVE_N.equals(storageIncSync.getIsactive())) {
                        StorageBasicUtils.errorRecord(syncId, "当前单据状态，不允许进行库存同步！", errData);
                        continue;
                    }
                    if (SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_SUCC.equals(storageIncSync.getStatus())) {
                        StorageBasicUtils.errorRecord(syncId, "当前单据已同步成功，不允许重复库存同步！", errData);
                        continue;
                    }
                    List<SgBChannelStorageIncSyncItem> incSyncItemList = itemMap.get(syncId);
                    if (CollectionUtils.isEmpty(incSyncItemList)) {
                        StorageBasicUtils.errorRecord(syncId, "当前单据明细为空！", errData);
                        continue;
                    }
                    List<SgBChannelStorageIncSyncItem> notSyncItemList = incSyncItemList.stream().filter(x -> !(
                                    SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_SUCC.equals(x.getStatus())
                                            || SgChannelConstants.CHANNEL_STORAGE_INC_SYNC_ITEM_IN.equals(x.getStatus())))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(notSyncItemList)) {
                        StorageBasicUtils.errorRecord(syncId, "当前单据明细同步中或同步成功，不允许重复同步！", errData);
                        continue;
                    }

                    try {
                        //"sg:incSync:" + batchNo + "_" + shopId + "_" + numiid;  按商品id  进行同步的情况才有返回值
                        Map<String, String> redisMap = syncSingle(storageIncSync, bufferSaveRequestList, notSyncItemList);
                        batchSaveRequest.setBufferSaveRequestList(bufferSaveRequestList);
                        batchSaveRequest.setBatchno(storageIncSync.getBatchNo());
                        SgBChannelStorageIncSyncService incSyncService = ApplicationContextHandle.getBean(SgBChannelStorageIncSyncService.class);
                        //将数据插入缓存池
                        ValueHolderV14<List<SgBChannelStorageBuffer>> listValueHolderV14 = incSyncService.saveDataToBufferByHand(storageIncSync, request.getLoginUser(), batchSaveRequest, notSyncItemList, errData);

                        //将平台商品同步的条码存入Redis
                        if (!redisMap.isEmpty()) {
                            Set<String> keys = redisMap.keySet();
                            keys.forEach(key -> {
                                CusRedisTemplate<String, String> strRedis = RedisMasterUtils.getStrRedisTemplate();
                                strRedis.opsForValue().set(key, redisMap.get(key));
                            });
                        }

                        List<SgBChannelStorageBuffer> data = listValueHolderV14.getData();
                        if (data != null && data.size() > 0) {
                            calcChannelStorageConsumer(data);
                        }
                    } catch (Exception e) {
                        log.error("增量同步异常===>{}", Throwables.getStackTraceAsString(e));
                        StorageBasicUtils.errorRecord(syncId, e.getMessage(), errData);
                    }
                }

            } else {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("当前记录已不存在！"));
            }
            if (CollectionUtils.isNotEmpty(errData)) {
                v14.setCode(ResultCode.FAIL);
                if (syncIds.size() == 1) {
                    v14.setMessage(errData.getJSONObject(0).getString(R3ParamConstants.MESSAGE));
                } else {
                    v14.setMessage("同步成功:" + (syncIds.size() - errData.size() + ",同步失败:" + errData.size()));
                    SgR3BaseResult sgR3BaseResult = new SgR3BaseResult();
                    sgR3BaseResult.setDataArr(errData);
                    v14.setData(sgR3BaseResult);
                }
            }
        } else {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("请求参数不能为空！"));
        }
        return v14;
    }

    private void calcChannelStorageConsumer(List<SgBChannelStorageBuffer> buffers) {
        List<SgChannelStorageOmsCalcRequest> list = new ArrayList<>();
        for (SgBChannelStorageBuffer buffer : buffers) {
            SgChannelStorageOmsCalcRequest request = new SgChannelStorageOmsCalcRequest();
            request.setMqSendTime(new Date());
            request.setBuffId(buffer.getId());
            BeanCopierUtil.copy(buffer, request);
            list.add(request);
        }
        sgChannelStorageConsumerCmd.calcChannelStorageConsumer(list);
    }

    /**
     * 插入缓存池
     *
     * @param sgChannelStorageIncSync 增量数据
     * @param user                    用户
     * @param batchSaveRequest        请求数据
     * @param notSyncItemList         明细
     * @param errData                 错误信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveDataToBuffer(SgBChannelStorageIncSync sgChannelStorageIncSync, User user,
                                 SgChannelStorageBufferBatchSaveRequest batchSaveRequest,
                                 List<SgBChannelStorageIncSyncItem> notSyncItemList, JSONArray errData) {

        if (log.isDebugEnabled()) {
            log.debug("syncId【{}】，增量同步结果==={}", sgChannelStorageIncSync.getId(), JSON.toJSONString(notSyncItemList));
        }
        updateSyncRecord(sgChannelStorageIncSync, user, notSyncItemList, errData);

        ValueHolderV14 v14 = sgBChannelStorageBufferCmd.saveDataToChannelStorageBuffer(batchSaveRequest);
        AssertUtils.isTrue(v14.isOK(), v14.getMessage());
    }

    /**
     * 插入缓存池
     *
     * @param sgChannelStorageIncSync 增量数据
     * @param user                    用户
     * @param batchSaveRequest        请求数据
     * @param notSyncItemList         明细
     * @param errData                 错误信息
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<List<SgBChannelStorageBuffer>> saveDataToBufferByHand(SgBChannelStorageIncSync sgChannelStorageIncSync, User user,
                                                                                SgChannelStorageBufferBatchSaveRequest batchSaveRequest,
                                                                                List<SgBChannelStorageIncSyncItem> notSyncItemList, JSONArray errData) {

        if (log.isDebugEnabled()) {
            log.debug("syncId【{}】，增量同步结果==={}", sgChannelStorageIncSync.getId(), JSON.toJSONString(notSyncItemList));
        }
        updateSyncRecord(sgChannelStorageIncSync, user, notSyncItemList, errData);

//        AssertUtils.isTrue(v14.isOK(),v14.getMessage());
        return sgBChannelStorageBufferCmd.saveDataToChannelStorageBufferByHand(batchSaveRequest);
    }

    /**
     * 单个增量记录同步
     */
    private Map<String, String> syncSingle(SgBChannelStorageIncSync sgChannelStorageIncSync,
                                           List<SgChannelStorageBufferSaveRequest> bufferSaveRequestList,
                                           List<SgBChannelStorageIncSyncItem> notSyncItemList) {

        CpShop shopInfo = CommonCacheValUtils.getShopInfo(sgChannelStorageIncSync.getCpCShopId());
        AssertUtils.cannot(ObjectUtils.isEmpty(shopInfo), "当前店铺已不存在,库存同步失败！");

        List<String> numiids = new ArrayList<>();
        List<String> skuIds = new ArrayList<>();
        notSyncItemList.forEach(item -> {
            if (ObjectUtils.isEmpty(item.getSkuId())) {
                //同步商品下所有条码情况--从平台店铺商品表查询所有条码
                numiids.add(item.getNumiid());
            } else {
                //条码不为空的情况商品为空，需要查询商品用于盘判断是否同城购商品
                skuIds.add(item.getSkuId());
            }
        });
        LambdaQueryWrapper<SgBChannelProduct> proWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtils.isNotEmpty(numiids)) {
            proWrapper.eq(SgBChannelProduct::getIsactive, R3CommonResultConstants.VALUE_Y);
            proWrapper.and(qw -> {
                qw.in(SgBChannelProduct::getNumiid, numiids);
                if (CollectionUtils.isNotEmpty(skuIds)) {
                    qw.or(qwo -> qwo.in(SgBChannelProduct::getSkuId, skuIds));
                }
                return qw;
            });
        } else if (CollectionUtils.isNotEmpty(skuIds)) {
            proWrapper.eq(SgBChannelProduct::getIsactive, R3CommonResultConstants.VALUE_Y);
            proWrapper.in(SgBChannelProduct::getSkuId, skuIds);
        }
        AssertUtils.cannot(proWrapper.isEmptyOfWhere(), "需要同步库存的平台商品或条码不存在");
        List<SgBChannelProduct> products = channelProductMapper.selectList(proWrapper);
        AssertUtils.cannot(CollectionUtils.isEmpty(products), "需要同步库存的平台商品或条码不存在");

        //用于只填了商品编码的明细
        Map<String, List<SgBChannelProduct>> proMap = products.stream().collect(Collectors
                .groupingBy(SgBChannelProduct::getNumiid));
        //用于填充条码明细
        Map<String, SgBChannelProduct> skuMap = products.stream().collect(Collectors
                .toMap(SgBChannelProduct::getSkuId, p -> p));

        Map<String, String> redisMap = Maps.newHashMap();
        for (SgBChannelStorageIncSyncItem syncItem : notSyncItemList) {
            SgChannelStorageBufferSaveRequest request = new SgChannelStorageBufferSaveRequest();
            request.setBatchNo(sgChannelStorageIncSync.getBatchNo());
            request.setCpCPlatformId(shopInfo.getCpCPlatformId().intValue());
            request.setCpCShopId(shopInfo.getId());
            request.setCpCShopTitle(shopInfo.getCpCShopTitle());
            request.setSyncType(SgConstantsIF.SYNC_TYPE_POOL_ADD_CITY_HAND);
            request.setSourceNo("手工增量库存同步,批次号[" + sgChannelStorageIncSync.getBatchNo() + "]");
            request.setDealStatus(SgBChannelStorageBufferExtend.DealStatusEnum.UN_DEAL.getCode());

            if (ObjectUtils.isEmpty(syncItem.getSkuId())) {
                //如果明细是平台商品id，根据条码进行逐个同步
                List<SgBChannelProduct> channelProducts = proMap.get(syncItem.getNumiid());
                AssertUtils.cannot(CollectionUtils.isEmpty(channelProducts),
                        "平台商品[" + syncItem.getNumiid() + "]未匹配到对应条码");
                StringBuilder skuBuilder = new StringBuilder();
                channelProducts.forEach(cp -> {
                    syncBySku(sgChannelStorageIncSync, request, syncItem, skuMap.get(cp.getSkuId()));
                    bufferSaveRequestList.add(request);
                    skuBuilder.append(cp.getSkuId()).append(",");
                });
                //将商品维度的同步明细存入Redis，便于回写的时候判断是否改商品下的所有平台条码都同步完成
                redisMap.put(getSkuListKey(sgChannelStorageIncSync.getBatchNo(),
                                sgChannelStorageIncSync.getCpCShopId(), syncItem.getNumiid()),
                        skuBuilder.toString().substring(0, skuBuilder.length() - 1));
            } else {
                syncBySku(sgChannelStorageIncSync, request, syncItem, skuMap.get(syncItem.getSkuId()));
                bufferSaveRequestList.add(request);
            }
        }
        return redisMap;
    }

    /**
     * 根据平台条码同步增量库存
     *
     * @param sgChannelStorageIncSync 增量主表
     * @param request                 请求参数
     * @param syncItem                增量明细
     * @param channelProduct          平台条码明细
     */
    private void syncBySku(SgBChannelStorageIncSync sgChannelStorageIncSync, SgChannelStorageBufferSaveRequest request,
                           SgBChannelStorageIncSyncItem syncItem, SgBChannelProduct channelProduct) {

        try {
            AssertUtils.cannot(ObjectUtils.isEmpty(channelProduct), "平台条码[" + syncItem.getSkuId() + "]不存在");

            request.setPsCProId(channelProduct.getPsCProId());
            request.setPsCSkuId(channelProduct.getPsCSkuId());
            request.setPsCSkuEcode(channelProduct.getPsCSkuEcode());
            request.setPtProId(channelProduct.getNumiid());
            request.setSkuId(channelProduct.getSkuId());
            request.setFixedQty(syncItem.getQty());
            request.setFixedQtyFlag(R3CommonResultConstants.VALUE_Y);

            //同步并更新增量库存状态
            if (log.isDebugEnabled()) {
                log.debug(" 选择同步的数据：{}", JSON.toJSONString(request));
            }
            if (ObjectUtils.isEmpty(syncItem.getStatus()) || WAIT_SYNC == syncItem.getStatus()) {
                syncItem.setStatus(DOING_SYNC);
            }
        } catch (Exception e) {
            log.error("syncId[{}]--skuId[{}] error:{}", sgChannelStorageIncSync.getId(), syncItem.getSkuId(),
                    Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 更新数据库状态
     *
     * @param sgChannelStorageIncSync 主表
     * @param user                    用户
     * @param notSyncItemList         明细
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSyncRecord(SgBChannelStorageIncSync sgChannelStorageIncSync, User user,
                                 List<SgBChannelStorageIncSyncItem> notSyncItemList, JSONArray errData) {
        Date now = new Date();

        SgBChannelStorageIncSync updateModel = new SgBChannelStorageIncSync();
        updateModel.setId(sgChannelStorageIncSync.getId());
        updateModel.setModifierid(Long.valueOf(user.getId()));
        updateModel.setModifiername(user.getName());
        updateModel.setModifierename(user.getEname());
        updateModel.setModifieddate(now);

        Map<Integer, List<Long>> itemMap = checkStatus(updateModel, errData, notSyncItemList);

        Set<Integer> keys = itemMap.keySet();
        keys.forEach(key -> {
            if (SUCCESS_SYNC == key || SUCCESS_PART == key) {
                //明细完成后移动到成功明细表
                moveSuccessItem(itemMap.get(key), user, key);
            } else {
                //同步中、同步失败
                SgBChannelStorageIncSyncItem updateItem = new SgBChannelStorageIncSyncItem();
                updateItem.setModifierid(Long.valueOf(user.getId()));
                updateItem.setModifiername(user.getName());
                updateItem.setModifierename(user.getEname());
                updateItem.setModifieddate(now);
                updateItem.setStatus(key);
                sgChannelStorageIncSyncItemMapper.update(updateItem, new LambdaQueryWrapper<SgBChannelStorageIncSyncItem>()
                        .in(SgBChannelStorageIncSyncItem::getId, itemMap.get(key)));
            }
        });

        List<SgBChannelStorageIncSyncItem> items = sgChannelStorageIncSyncItemMapper
                .selectList(new LambdaQueryWrapper<SgBChannelStorageIncSyncItem>()
                        .eq(SgBChannelStorageIncSyncItem::getSgBChannelStorageIncSyncId, sgChannelStorageIncSync.getId()));
        List<SgBChannelStorageIncSyncSuccessItem> successItems = successItemMapper
                .selectList(new LambdaQueryWrapper<SgBChannelStorageIncSyncSuccessItem>()
                        .eq(SgBChannelStorageIncSyncSuccessItem::getSgBChannelStorageIncSyncId, sgChannelStorageIncSync.getId()));
        if (CollectionUtils.isEmpty(items)) {
            //查询增量表是否全部同步完成，是的话主表更新为同步成功
            updateModel.setStatus(SUCCESS_SYNC);
        } else {
            boolean isFailed = true;
            for (SgBChannelStorageIncSyncItem item : items) {
                if (FAIL_SYNC != item.getStatus()) {
                    isFailed = false;
                }
            }
            if (isFailed && CollectionUtils.isEmpty(successItems)) {
                //如果剩余全部是同步失败,主表状态同步失败
                updateModel.setStatus(FAIL_SYNC);
            } else if (isFailed) {
                //主表状态部分成功
                updateModel.setStatus(SUCCESS_PART);
            }
        }

        sgChannelStorageIncSyncMapper.updateById(updateModel);
    }

    /**
     * 将成功的明细挪到同步成功的明细表中
     *
     * @param itemIds 需要同步的明细id
     */
    private void moveSuccessItem(List<Long> itemIds, User user, Integer key) {
        if (ObjectUtils.isEmpty(itemIds)) {
            return;
        }
        List<SgBChannelStorageIncSyncItem> oldItems = sgChannelStorageIncSyncItemMapper.selectBatchIds(itemIds);
        if (CollectionUtils.isEmpty(oldItems)) {
            return;
        }

        //删除原明细
        sgChannelStorageIncSyncItemMapper.deleteBatchIds(itemIds);

        //新增成功明细
        List<SgBChannelStorageIncSyncSuccessItem> insertList = new ArrayList<>();
        oldItems.forEach(item -> {
            SgBChannelStorageIncSyncSuccessItem insertItem = new SgBChannelStorageIncSyncSuccessItem();
            BeanUtils.copyProperties(item, insertItem);
            insertItem.setId(ModelUtil.getSequence("SG_B_CHANNEL_STORAGE_INC_SYNC_SUCCESS_ITEM"));
            insertItem.setOwnerid(Long.valueOf(user.getId()));
            insertItem.setOwnername(user.getName());
            insertItem.setOwnerename(user.getEname());
            Date now = new Date();
            insertItem.setCreationdate(now);
            insertItem.setModifierid(Long.valueOf(user.getId()));
            insertItem.setModifierename(user.getEname());
            insertItem.setModifiername(user.getName());
            insertItem.setModifieddate(now);
            insertItem.setStatus(key);
            insertList.add(insertItem);
        });
        successItemMapper.batchInsert(insertList);
    }

    /**
     * 根据明细同步状态更新主表状态
     *
     * @param updateModel     主表
     * @param errData         错误map
     * @param notSyncItemList 明细
     * @return 需要更新的明细map
     */
    private Map<Integer, List<Long>> checkStatus(SgBChannelStorageIncSync updateModel, JSONArray errData,
                                                 List<SgBChannelStorageIncSyncItem> notSyncItemList) {

        List<Long> successIds = new ArrayList<>();
        List<Long> doingIds = new ArrayList<>();
        Map<Integer, List<Long>> map = Maps.newHashMap();
        List<Long> failIds = new ArrayList<>();
        List<Long> partIds = new ArrayList<>();
        List<SgBChannelStorageIncSyncItem> errItems = new ArrayList<>();
        notSyncItemList.forEach(item -> {
            if (null == item.getStatus()) {
                item.setStatus(WAIT_SYNC);
            }
            if (WAIT_SYNC == item.getStatus() || FAIL_SYNC == item.getStatus()) {
                failIds.add(item.getId());
                errItems.add(item);
            } else if (DOING_SYNC == item.getStatus()) {
                doingIds.add(item.getId());
            } else if (SUCCESS_SYNC == item.getStatus()) {
                successIds.add(item.getId());
            } else if (SUCCESS_PART == item.getStatus()) {
                partIds.add(item.getId());
            }
        });

        if (CollectionUtils.isNotEmpty(partIds)) {
            map.put(SUCCESS_PART, partIds);
        }

        if (CollectionUtils.isNotEmpty(doingIds)) {
            //加入缓存池，主表状态同步中
            updateModel.setStatus(DOING_SYNC);
            map.put(DOING_SYNC, doingIds);
        }
        if (CollectionUtils.isNotEmpty(successIds)) {
            map.put(SUCCESS_SYNC, successIds);
        }
        if (CollectionUtils.isNotEmpty(failIds)) {
            map.put(FAIL_SYNC, failIds);
            //加入缓存池出现失败，记录错误信息
            StorageBasicUtils.errorRecord(updateModel.getId(), "部分明细同步失败：\n" + printSyncItem(errItems), errData);
        }

        return map;
    }

    /**
     * 打印增量同步明细
     *
     * @param items 明细
     * @return 打印信息
     */
    private String printSyncItem(List<SgBChannelStorageIncSyncItem> items) {
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isNotEmpty(items)) {
            items.forEach(item -> {
                if (ObjectUtils.isEmpty(item.getSkuId())) {
                    builder.append("平台商品ID【").append(item.getNumiid()).append("】;\n");
                } else {
                    builder.append("平台条码ID【").append(item.getSkuId()).append("】;\n");
                }
            });
        }
        return builder.toString();
    }

    /**
     * 拼接批次同步商品Redis
     *
     * @param batchNo 批次
     * @param shopId  店铺id
     * @param numiid  平台商品id
     * @return Rediskey
     */
    public String getSkuListKey(String batchNo, Long shopId, String numiid) {
        return "sg:incSync:" + batchNo + "_" + shopId + "_" + numiid;
    }

    /**
     * 获取同步中状态的明细
     *
     * @return 增量同步明细
     */
    public Map<SgBChannelStorageIncSync, List<SgBChannelStorageIncSyncItem>> getItemMap() {
        Map<SgBChannelStorageIncSync, List<SgBChannelStorageIncSyncItem>> result = Maps.newHashMap();

        //查询同步中的增量同步记录
        List<SgBChannelStorageIncSync> incSyncs = sgChannelStorageIncSyncMapper
                .selectList(new LambdaQueryWrapper<SgBChannelStorageIncSync>()
                        .eq(SgBChannelStorageIncSync::getStatus, DOING_SYNC).last(" limit 500"));
        if (CollectionUtils.isEmpty(incSyncs)) {
            return result;
        }
        List<Long> syncIds = incSyncs.stream().mapToLong(SgBChannelStorageIncSync::getId)
                .boxed().collect(Collectors.toList());

        //查询记录对应的同步明细
        List<SgBChannelStorageIncSyncItem> itemList = sgChannelStorageIncSyncItemMapper
                .selectList(new LambdaQueryWrapper<SgBChannelStorageIncSyncItem>()
                        .eq(SgBChannelStorageIncSyncItem::getStatus, DOING_SYNC)
                        .in(SgBChannelStorageIncSyncItem::getSgBChannelStorageIncSyncId, syncIds));
        if (CollectionUtils.isEmpty(itemList)) {
            log.error("手工增量库存同步状态异常-->{}", incSyncs);
            return result;
        }

        incSyncs.forEach(incSync -> {
            List<SgBChannelStorageIncSyncItem> items = new ArrayList<>();
            itemList.forEach(item -> {
                if (incSync.getId().equals(item.getSgBChannelStorageIncSyncId())) {
                    items.add(item);
                }
            });
            if (CollectionUtils.isNotEmpty(items)) {
                result.put(incSync, items);
            } else {
                log.error("手工增量库存同步状态异常-->{}", JSON.toJSONString(incSync));
            }
        });

        return result;
    }
}
