package com.burgeon.r3.sg.channel.filter.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyItemMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategyMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategySaItemMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategySpItemMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelQtyStrategyDTO;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.qtystrategy.SgCChannelQtyStrategyItem;
import com.burgeon.r3.sg.core.model.table.channel.skustrategy.SgCChannelSkuStrategy;
import com.burgeon.r3.sg.core.model.table.channel.skustrategy.SgCChannelSkuStrategySaItem;
import com.burgeon.r3.sg.core.model.table.channel.skustrategy.SgCChannelSkuStrategySpItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date
 */
@Slf4j
@Component
public class SgCChannelQtyStrategySubmitFilter extends BaseSingleFilter<SgCChannelQtyStrategyDTO> {

    @Autowired
    private SgCChannelQtyStrategyItemMapper qtyStrategyItemMapper;
    @Autowired
    private SgCChannelSkuStrategyMapper skuStrategyMapper;
    @Autowired
    private SgCChannelSkuStrategySaItemMapper skuStrategySaItemMapper;
    @Autowired
    private SgCChannelSkuStrategySpItemMapper skuStrategySpItemMapper;

    @Override
    public String getFilterMsgName() {
        return "数量同步策略审核";
    }

    @Override
    public Class getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgCChannelQtyStrategyDTO mainObject, User loginUser) {
        return null;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgCChannelQtyStrategyDTO data, User loginUser) {
        // TODO 执行审核逻辑：根据本单[平台店铺]、独享库存区域信息（配销仓、平台条码ID、调整数量），新增或者更新【渠道库存锁定库存查询】

        // 如果明细为空，则提示失败：当前记录无明细，不允许审核！
        LambdaQueryWrapper<SgCChannelQtyStrategyItem> itemQueryWrapper = new LambdaQueryWrapper<>();
        itemQueryWrapper.eq(SgCChannelQtyStrategyItem::getSgCChannelQtyStrategyId, data.getId());
        List<SgCChannelQtyStrategyItem> itemList = qtyStrategyItemMapper.selectList(itemQueryWrapper);

        // 更改特殊条码按比例同步策略中独享库存、共享库存的可用字段为否
        LambdaQueryWrapper<SgCChannelSkuStrategy> skuStrategyQueryWrapper = new LambdaQueryWrapper<>();
        skuStrategyQueryWrapper.eq(SgCChannelSkuStrategy::getCpCShopId, data.getCpCShopId());
        skuStrategyQueryWrapper.eq(SgCChannelSkuStrategy::getStatus, SgChannelConstants.BILL_CHANNEL_STRATEGY_SUBMIT);
        List<SgCChannelSkuStrategy> skuStrategyList = skuStrategyMapper.selectList(skuStrategyQueryWrapper);

        // 特殊条码按比例同步策略主表id 集合
        List<Long> skuStrategyIdList = skuStrategyList.stream().map(SgCChannelSkuStrategy::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(skuStrategyIdList)) {
            // 如果没有符合条件的特殊条码比例同步策略中的状态，直接返回成功
            return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("审核成功！"));
        }

        // 明细skuId 集合
        List<String> skuIdList = itemList.stream().map(SgCChannelQtyStrategyItem::getSkuId).collect(Collectors.toList());

        // 批量修改特殊条码同步策略两个明细
        LambdaUpdateWrapper<SgCChannelSkuStrategySaItem> skuStrategySaItemLambdaWrapper = new LambdaUpdateWrapper<>();
        skuStrategySaItemLambdaWrapper.in(SgCChannelSkuStrategySaItem::getSkuId, skuIdList);
        skuStrategySaItemLambdaWrapper.in(SgCChannelSkuStrategySaItem::getSgCChannelSkuStrategyId, skuStrategyIdList);
        SgCChannelSkuStrategySaItem skuStrategySaItem = new SgCChannelSkuStrategySaItem();
        skuStrategySaItem.setIsactive(SgConstants.IS_ACTIVE_N);
        skuStrategySaItemMapper.update(skuStrategySaItem, skuStrategySaItemLambdaWrapper);

        LambdaUpdateWrapper<SgCChannelSkuStrategySpItem> skuStrategySpItemLambdaWrapper = new LambdaUpdateWrapper<>();
        skuStrategySpItemLambdaWrapper.in(SgCChannelSkuStrategySpItem::getSkuId, skuIdList);
        skuStrategySpItemLambdaWrapper.in(SgCChannelSkuStrategySpItem::getSgCChannelSkuStrategyId, skuStrategyIdList);
        SgCChannelSkuStrategySpItem skuStrategySpItem = new SgCChannelSkuStrategySpItem();
        skuStrategySpItem.setIsactive(SgConstants.IS_ACTIVE_N);
        skuStrategySpItemMapper.update(skuStrategySpItem, skuStrategySpItemLambdaWrapper);

        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("审核成功！"));
    }
}
