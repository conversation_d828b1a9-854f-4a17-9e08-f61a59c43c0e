package com.burgeon.r3.sg.channel.services.storage;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.storage.SgBChannelStorageIncSyncMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBChannelStorageIncSync;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2021-10-22 10:49
 * @Description: 平台库存手工增量 -库存同步
 */

@Slf4j
@Component
public class SgBChannelStorageIncSyncByAutoService {
    @Autowired
    private SgBChannelStorageIncSyncMapper mapper;
    @Autowired
    private SgBChannelStorageIncSyncService syncService;

    /**
     * 自动任务
     *
     * @return 返回状态
     */
    public ValueHolderV14 execute() {

        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelStorageIncSyncByAutoService.execute Automatic task synchronization inventory");
        }

        List<SgBChannelStorageIncSync> sgChannelStorageIncSyncs = mapper.selectList(new QueryWrapper<SgBChannelStorageIncSync>()
                .lambda()
                .eq(SgBChannelStorageIncSync::getStatus, SgChannelConstants.CHANNEL_STORAGE_FULL_SYNC_UN)
                .like(SgBChannelStorageIncSync::getRemark, "由系统增量自动同步任务生成")
                .eq(SgBChannelStorageIncSync::getIsactive, SgConstants.IS_ACTIVE_Y));

        if (CollectionUtils.isNotEmpty(sgChannelStorageIncSyncs)) {
            //分批次库存同步
            List<Long> ids = sgChannelStorageIncSyncs.stream().map(SgBChannelStorageIncSync::getId).collect(Collectors.toList());
            List<List<Long>> pageIdList = StorageUtils.getPageList(ids, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);

            int syncSuccess = 0;
            for (List<Long> objids : pageIdList) {
                try {
                    SgR3BaseRequest r3BaseRequest = new SgR3BaseRequest();
                    r3BaseRequest.setIds(objids);
                    r3BaseRequest.setR3(Boolean.TRUE);
                    r3BaseRequest.setLoginUser(R3SystemUserResource.getSystemRootUser());
                    ValueHolderV14<SgR3BaseResult> v14 = syncService.stockSync(r3BaseRequest);
                    if (!v14.isOK()) {
                        log.error("error SgBChannelStorageIncSyncByAutoService.execute 调用库存同步方法返回失败，失败原因：{}",
                                v14.getMessage());
                    } else {
                        syncSuccess += objids.size();
                    }
                } catch (Exception e) {
                    log.error("SgBChannelStorageIncSyncByAutoService.execute 平台库存手工增量异常：error:{}",
                            Throwables.getStackTraceAsString(e));
                }
            }

            if (log.isDebugEnabled()) {
                log.debug("End SgBChannelStorageIncSyncByAutoService.execute 同步总数:{},同步成功数:{},失败次数:{}",
                        ids.size(), syncSuccess, ids.size() - syncSuccess);
            }

        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, "平台库存手工增量自动任务同步成功");
    }
}
