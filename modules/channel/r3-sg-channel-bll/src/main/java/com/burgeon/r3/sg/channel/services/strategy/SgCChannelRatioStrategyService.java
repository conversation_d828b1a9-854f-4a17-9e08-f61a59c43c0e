package com.burgeon.r3.sg.channel.services.strategy;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.mapper.SgCSaStoreMapper;
import com.burgeon.r3.sg.basic.rpc.RpcCpService;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyItemMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyMapper;
import com.burgeon.r3.sg.channel.model.vo.SgCChannelRatioStrategyImpVo;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.channel.ratiostrategy.SgCChannelRatioStrategy;
import com.burgeon.r3.sg.core.model.table.channel.ratiostrategy.SgCChannelRatioStrategyItem;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/6/16 19:22
 */

@Slf4j
@Component
public class SgCChannelRatioStrategyService {
    @Autowired
    private RpcCpService rpcCpService;
    @Autowired
    private SgCSaStoreMapper sgCSaStoreMapper;
    @Autowired
    private SgCChannelRatioStrategyMapper sgCChannelRatioStrategyMapper;
    @Autowired
    private SgCChannelRatioStrategyItemMapper sgCChannelRatioStrategyItemMapper;


    @Transactional(rollbackFor = Exception.class)
    public int batchSaveData(List<SgCChannelRatioStrategyImpVo> checkedImpVos, User user) {
        log.info("## 比例同步策略导入数据：" + JSONObject.toJSONString(checkedImpVos));

        if(CollectionUtils.isEmpty(checkedImpVos)){
            return 0;
        }
        List<SgCChannelRatioStrategy> insertList = new ArrayList();
        List<SgCChannelRatioStrategyItem> insertItemList = new ArrayList<>();

        //查询平台店铺
        List<String> shopNames = checkedImpVos.stream().map(SgCChannelRatioStrategyImpVo::getPlatformShopName).distinct().collect(Collectors.toList());
        List<CpShop> shops = rpcCpService.getShopByNames(shopNames);
        Map<String, CpShop> shopMap = shops.stream().collect(Collectors.toMap(CpShop::getCpCShopTitle, Function.identity(), (key1, key2) -> key2));

        //查询配销仓
        List<String> storeNames = checkedImpVos.stream().map(SgCChannelRatioStrategyImpVo::getSgCSaStoreName).distinct().collect(Collectors.toList());
        List<SgCSaStore> sgCSaStoreList = sgCSaStoreMapper.selectList(new QueryWrapper<SgCSaStore>().lambda().eq(SgCSaStore::getIsactive, "Y").in(SgCSaStore::getEname, storeNames));
        Map<String, SgCSaStore> storeMap = sgCSaStoreList.stream().collect(Collectors.toMap(SgCSaStore::getEname, Function.identity(), (key1, key2) -> key2));

        //获取店铺id
        List<Long> shopIds = shops.stream().map(CpShop::getId).collect(Collectors.toList());
        //查询该店铺是否存在比例同步策略，若存在则不新增主表，若不存在则再判断是否有明细新增
        if(CollectionUtils.isNotEmpty(shopIds)){
            List<SgCChannelRatioStrategy> existMainList = sgCChannelRatioStrategyMapper.selectList(
                    new QueryWrapper<SgCChannelRatioStrategy>().lambda()
//                            .in(SgCChannelRatioStrategy::getCpCShopId, shopIds)
                            .eq(SgCChannelRatioStrategy::getIsactive, "Y"));
            Map<Long, SgCChannelRatioStrategy> shopRatioMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(existMainList)){
//                shopRatioMap = existMainList.stream().collect(Collectors.toMap(SgCChannelRatioStrategy::getCpCShopId, Function.identity(), (key1, key2) -> key2));
            }
            StringBuilder checkMessage = new StringBuilder();

            //通过平台店铺分组
            Map<String, List<SgCChannelRatioStrategyImpVo>> checkedImpMap = checkedImpVos.stream().collect(Collectors.groupingBy(SgCChannelRatioStrategyImpVo::getPlatformShopName));
            for(Map.Entry<String, List<SgCChannelRatioStrategyImpVo>> entry:checkedImpMap.entrySet()){
                Long objid = null;
                String shopName = entry.getKey();
                CpShop shop = shopMap.get(shopName);
                if(Objects.isNull(shop)){
                    checkMessage.append("[平台店铺名称不存在]");
                    List<SgCChannelRatioStrategyImpVo> impVos = entry.getValue();
                    for(SgCChannelRatioStrategyImpVo impVo:impVos){
                        if(StringUtils.isNotBlank(impVo.getDesc())){
                            impVo.setDesc(impVo.getDesc()+checkMessage.toString());
                        }else{
                            impVo.setDesc(checkMessage.toString());
                        }
                    }
                    checkMessage.setLength(0);
                }else{
                    //处理主表数据
                    SgCChannelRatioStrategy existMain = shopRatioMap.get(shop.getId());
                    if(Objects.isNull(existMain)) {
                        objid = ModelUtil.getSequence("SG_C_CHANNEL_RATIO_STRATEGY");
                        SgCChannelRatioStrategy po = new SgCChannelRatioStrategy();
                        po.setId(objid);
//                        po.setCpCShopEcode(shop.getEcode());
//                        po.setCpCShopId(shop.getId());
//                        po.setCpCShopTitle(shop.getCpCShopTitle());
                        makeCreateField(po, user);
                        insertList.add(po);
                    }else{
                        objid = existMain.getId();
                    }

                    //处理子表数据
                    List<SgCChannelRatioStrategyImpVo> impVos = entry.getValue();
                    //检查配销仓档案是否存在
                    for(SgCChannelRatioStrategyImpVo impVo:impVos){
                        if(Objects.isNull(storeMap.get(impVo.getSgCSaStoreName()))){
                            checkMessage.append("[配销仓名称不存在]");
                            if(StringUtils.isNotBlank(impVo.getDesc())){
                                impVo.setDesc(impVo.getDesc()+checkMessage.toString());
                            }else{
                                impVo.setDesc(checkMessage.toString());
                            }
                            checkMessage.setLength(0);
                        }
                    }

                    //检查导入文件里是否有重复配销仓数据
                    Map<String, List<SgCChannelRatioStrategyImpVo>> itemImpGroup = impVos.stream().collect(Collectors.groupingBy(SgCChannelRatioStrategyImpVo::getSgCSaStoreName));
                    for(Map.Entry<String, List<SgCChannelRatioStrategyImpVo>> itemEntry:itemImpGroup.entrySet()){
                        if(itemEntry.getValue().size()>1){
                            checkMessage.append("[配销仓重复]");
                            List<SgCChannelRatioStrategyImpVo> itemImpVos = itemEntry.getValue();
                            for(SgCChannelRatioStrategyImpVo impVo:itemImpVos){
                                if(StringUtils.isNotBlank(impVo.getDesc())){
                                    impVo.setDesc(impVo.getDesc()+checkMessage.toString());
                                }else{
                                    impVo.setDesc(checkMessage.toString());
                                }
                            }
                            checkMessage.setLength(0);
                        }
                    }
                    //检查导入文件里是否有重复优先级数据
                    Map<Integer, List<SgCChannelRatioStrategyImpVo>> itemImpGroup1 = impVos.stream().collect(Collectors.groupingBy(SgCChannelRatioStrategyImpVo::getPriority));
                    for(Map.Entry<Integer, List<SgCChannelRatioStrategyImpVo>> itemEntry:itemImpGroup1.entrySet()){
                        if(itemEntry.getValue().size()>1){
                            checkMessage.append("[优先级重复]");
                            List<SgCChannelRatioStrategyImpVo> itemImpVos = itemEntry.getValue();
                            for(SgCChannelRatioStrategyImpVo impVo:itemImpVos){
                                if(StringUtils.isNotBlank(impVo.getDesc())){
                                    impVo.setDesc(impVo.getDesc()+checkMessage.toString());
                                }else{
                                    impVo.setDesc(checkMessage.toString());
                                }
                            }
                            checkMessage.setLength(0);
                        }
                    }

                    List<SgCChannelRatioStrategyImpVo> validItemImpVos = impVos.stream().filter(p -> StringUtils.isBlank(p.getDesc())).collect(Collectors.toList());
                    //检查配销仓，优先级与库中数据是否有重复
                    if(CollectionUtils.isNotEmpty(validItemImpVos)){
                        List<Long> validStoreIds = new ArrayList<>();
                        List<Integer> validPriority = new ArrayList<>();
                        for(SgCChannelRatioStrategyImpVo vo:validItemImpVos){
                            validStoreIds.add(storeMap.get(vo.getSgCSaStoreName()).getId());
                            validPriority.add(vo.getPriority());
                        }

                        List<SgCChannelRatioStrategyItem> existItems = sgCChannelRatioStrategyItemMapper.selectList(new QueryWrapper<SgCChannelRatioStrategyItem>().lambda()
                                .eq(SgCChannelRatioStrategyItem::getSgCChannelRatioStrategyId, objid)
                                .in(SgCChannelRatioStrategyItem::getSgCSaStoreId, validStoreIds)
                                .eq(SgCChannelRatioStrategyItem::getIsactive, "Y"));
                        if(CollectionUtils.isNotEmpty(existItems)){
                            List<String> collect = existItems.stream().map(SgCChannelRatioStrategyItem::getSgCSaStoreEname).collect(Collectors.toList());
                            for(SgCChannelRatioStrategyImpVo vo:validItemImpVos){
                                if(collect.contains(vo.getSgCSaStoreName())){
                                    checkMessage.append("[配销仓与库中重复]");
                                    if(StringUtils.isNotBlank(vo.getDesc())){
                                        vo.setDesc(vo.getDesc()+checkMessage.toString());
                                    }else{
                                        vo.setDesc(checkMessage.toString());
                                    }
                                    checkMessage.setLength(0);
                                }
                            }
                        }

                        existItems = sgCChannelRatioStrategyItemMapper.selectList(new QueryWrapper<SgCChannelRatioStrategyItem>().lambda()
                                .eq(SgCChannelRatioStrategyItem::getSgCChannelRatioStrategyId, objid)
                                .in(SgCChannelRatioStrategyItem::getSgCSaStoreOrderno, validPriority)
                                .eq(SgCChannelRatioStrategyItem::getIsactive, "Y"));
                        if(CollectionUtils.isNotEmpty(existItems)){
                            List<String> collect = existItems.stream().map(SgCChannelRatioStrategyItem::getSgCSaStoreEname).collect(Collectors.toList());
                            for(SgCChannelRatioStrategyImpVo vo:validItemImpVos){
                                if(collect.contains(vo.getSgCSaStoreName())){
                                    checkMessage.append("[优先级与库中重复]");
                                    if(StringUtils.isNotBlank(vo.getDesc())){
                                        vo.setDesc(vo.getDesc()+checkMessage.toString());
                                    }else{
                                        vo.setDesc(checkMessage.toString());
                                    }
                                    checkMessage.setLength(0);
                                }
                            }
                        }

                    }

                    List<SgCChannelRatioStrategyImpVo> finalItemVos = impVos.stream().filter(p -> StringUtils.isBlank(p.getDesc())).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(finalItemVos)){
                        for(SgCChannelRatioStrategyImpVo itemVo:finalItemVos){
                            SgCChannelRatioStrategyItem item = new SgCChannelRatioStrategyItem();
                            item.setId(ModelUtil.getSequence("SG_C_CHANNEL_RATIO_STRATEGY_ITEM"));
                            item.setRatio(itemVo.getRatio());
                            item.setSgCChannelRatioStrategyId(objid);
                            item.setSgCSaStoreEname(itemVo.getSgCSaStoreName());
                            item.setSgCSaStoreId(storeMap.get(itemVo.getSgCSaStoreName()).getId());
                            item.setSgCSaStoreEcode(storeMap.get(itemVo.getSgCSaStoreName()).getEcode());
                            item.setSgCSaStoreOrderno(itemVo.getPriority());
                            makeCreateField(item, user);
                            insertItemList.add(item);
                        }
                    }

                }
            }
            if(CollectionUtils.isNotEmpty(insertList)){
                sgCChannelRatioStrategyMapper.batchInsert(insertList);
            }
            if(CollectionUtils.isNotEmpty(insertItemList)){
                sgCChannelRatioStrategyItemMapper.batchInsert(insertItemList);
            }
        }
        int successSize = insertItemList.size();
        return successSize;
    }



    /**
     * @param baseModelDO
     * @param user
     * @return void
     * @Descroption 创建信息字段修改
     * @Author: 洪艺安
     * @Date 2019/8
     */
    public static void makeCreateField(BaseModel baseModelDO, User user) {
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        baseModelDO.setAdClientId((long) user.getClientId());//所属公司
        baseModelDO.setAdOrgId((long) user.getOrgId());//所属组织
        baseModelDO.setOwnerid(Long.valueOf(user.getId()));//创建人id
        baseModelDO.setCreationdate(timestamp);//创建时间
        baseModelDO.setOwnername(user.getName());//创建人用户名
        baseModelDO.setModifierid(Long.valueOf(user.getId()));//修改人id
        baseModelDO.setModifiername(user.getName());//修改人用户名
        baseModelDO.setModifieddate(timestamp);//修改时间
        baseModelDO.setIsactive("Y");//是否启用
    }
}
