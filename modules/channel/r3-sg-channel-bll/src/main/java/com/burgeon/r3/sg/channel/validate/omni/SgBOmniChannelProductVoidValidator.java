package com.burgeon.r3.sg.channel.validate.omni;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.channel.mapper.omni.SgBOmniChannelProductMapper;
import com.burgeon.r3.sg.channel.model.dto.omni.SgBOmniChannelProductDto;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.omni.SgBOmniChannelProduct;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 渠道商品作废验证，可用非平台同城购通过验证
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/29 10:31
 */
@Slf4j
@Component
public class SgBOmniChannelProductVoidValidator extends BaseSingleValidator<SgBOmniChannelProductDto> {

    @Autowired
    SgBOmniChannelProductMapper mapper;

    @Override
    public String getValidatorMsgName() {
        return "全渠道商品作废";
    }

    @Override
    public Class<?> getValidatorClass() {
        return SgBOmniChannelProductVoidValidator.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgBOmniChannelProductDto mainObject, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过", loginUser.getLocale()));
        if (log.isDebugEnabled()) {
            log.debug("Start SgChannelGoodVoidValidator.void:request={}", JSONObject.toJSONString(mainObject));
        }
        if (Objects.isNull(mainObject) || Objects.isNull(mainObject.getId())) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("传入参数不能为空", loginUser.getLocale()));
        }

        SgBOmniChannelProduct good = mapper.selectById(mainObject.getId());

        if (Objects.isNull(good)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("当前作废的记录未查询到，作废失败", loginUser.getLocale()));
            return vh;
        }

        if (SgConstants.IS_YES_OR_NO_Y.equalsIgnoreCase(good.getIsTmllCity())) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("当前记录是平台同城购，不允许作废操作", loginUser.getLocale()));
            return vh;
        }
        return vh;
    }
}
