package com.burgeon.r3.sg.channel.filter.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.rpc.RpcCpService;
import com.burgeon.r3.sg.basic.services.log.LogCommonService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelRatioStrategyItemMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelRatioStrategyDTO;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelRatioStrategyItemDTO;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCOperationLog;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.basic.SgCSharePool;
import com.burgeon.r3.sg.core.model.table.channel.ratiostrategy.SgCChannelRatioStrategyItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.Enum.CpCDistributionOrganizationLevelEnum;
import com.jackrain.nea.cpext.model.table.CpCDistributionOrganization;
import com.jackrain.nea.enums.YesOrNoEnum;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleItemFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/6/8 17:29
 */
@Component
@Slf4j
public class SgCChannelRatioStrategySaveFilter extends BaseSingleItemFilter<SgCChannelRatioStrategyDTO, SgCChannelRatioStrategyItemDTO> {

    @Autowired
    private SgCChannelRatioStrategyItemMapper itemMapper;
    @Autowired
    private LogCommonService logCommonService;
    @Resource
    private RpcCpService rpcCpService;

    @Override
    public String getFilterMsgName() {
        return "按比例同步策略保存";
    }

    @Override
    public Class<?> getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgCChannelRatioStrategyDTO mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("按比例同步策略保存"));
        Long id = mainObject.getId();
        if (mainObject.getSgCSharePoolId() != null) {
            SgCSharePool sharePoolStore = CommonCacheValUtils.getSharePoolStore(mainObject.getSgCSharePoolId());
            if (sharePoolStore == null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("共享池信息有误", loginUser.getLocale()));
                return v14;
            }
            mainObject.setSgCSharePoolEcode(sharePoolStore.getEcode());
            mainObject.setSgCSharePoolEname(sharePoolStore.getEname());
        }
        if (id < 0L) {
            //二级分货组织
            CpCDistributionOrganization distributionOrganization =
                    rpcCpService.queryDistOrganizationByLevelAndCode(CpCDistributionOrganizationLevelEnum.LEVEL_2.getValue(), mainObject.getDistCodeLevelTwo());
            if (distributionOrganization == null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("二级分货组织不存在", loginUser.getLocale()));
                return v14;
            }
            mainObject.setDistNameLevelTwo(distributionOrganization.getEname());
        }

        return v14;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgCChannelRatioStrategyDTO mainObject, User loginUser) {
        return null;
    }

    @Override
    public ValueHolderV14 execBeforeSubTable(SgCChannelRatioStrategyDTO mainObject, List<SgCChannelRatioStrategyItemDTO> subObjectList, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        Long id = mainObject.getId();
        List<SgCOperationLog> updateDataList = new ArrayList<>();
        //查询历史明细数据
        List<SgCChannelRatioStrategyItem> strategyItems = itemMapper.selectList(new LambdaQueryWrapper<SgCChannelRatioStrategyItem>()
                .eq(SgCChannelRatioStrategyItem::getSgCChannelRatioStrategyId, id));
        Map<Long, SgCChannelRatioStrategyItem> itemMap = strategyItems.stream().collect(Collectors.toMap(o -> o.getId(), Function.identity()));
        Map<Long, SgCChannelRatioStrategyItem> strategyItemMap = strategyItems.stream().
                collect(Collectors.toMap(SgCChannelRatioStrategyItem::getSgCSaStoreId, Function.identity()));

        List<Integer> storeOrdernoList = strategyItems.stream().map(o -> o.getSgCSaStoreOrderno()).collect(Collectors.toList());
        for (SgCChannelRatioStrategyItemDTO itemDTO : subObjectList) {
            Long sgSaStoreId = itemDTO.getSgCSaStoreId();
            Integer sgCSaStoreOrderno = itemDTO.getSgCSaStoreOrderno();

            if (sgCSaStoreOrderno != null && sgCSaStoreOrderno > 0) {
                if (storeOrdernoList.contains(sgCSaStoreOrderno)) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage("优先级不允许重复");
                    return v14;
                }
                Integer count = itemMapper.selectCount(new LambdaQueryWrapper<SgCChannelRatioStrategyItem>()
                        .eq(SgCChannelRatioStrategyItem::getSgCChannelRatioStrategyId, id)
                        .eq(SgCChannelRatioStrategyItem::getSgCSaStoreOrderno, sgCSaStoreOrderno));
                if (count > 0) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage("优先级不允许重复");
                    return v14;
                }
            }
            SgCSaStore saStore = null;
            if (sgSaStoreId != null) {
                saStore = CommonCacheValUtils.getSaStore(sgSaStoreId);
                if (saStore == null) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage("配销仓信息有误");
                    return v14;
                }
                itemDTO.setSgCSaStoreEname(saStore.getEname());
                itemDTO.setSgCSaStoreEcode(saStore.getEcode());
                itemDTO.setSgCSaStoreType(saStore.getType());
                if (id > 0L) {
                    SgCChannelRatioStrategyItem strategyItem = strategyItemMap.get(sgSaStoreId);
                    if (strategyItem != null) {
                        itemDTO.setId(strategyItem.getId());
                    }
                }
            }
            Long itemId = itemDTO.getId();
            if (itemId > 0L) {
                SgCChannelRatioStrategyItem strategyItem = itemMap.get(itemId);
                BigDecimal newRatio = itemDTO.getRatio();
                String isactive = itemDTO.getIsactive();
                BigDecimal oldRatio = strategyItem.getRatio();
                Integer oldOrderno = strategyItem.getSgCSaStoreOrderno();
                Integer newOrderno = itemDTO.getSgCSaStoreOrderno();
                SgCSaStore sgCSaStore = CommonCacheValUtils.getSaStore(strategyItem.getSgCSaStoreId());
                if (newRatio != null) {
                    SgCOperationLog operationLog = logCommonService.getOperationLog("SG_C_CHANNEL_RATIO_STRATEGY",
                            OperationTypeEnum.MOD.getOperationValue(), id, "比例同步策略", sgCSaStore.getEname() + "比例",
                            String.valueOf(oldRatio), String.valueOf(newRatio), loginUser);
                    updateDataList.add(operationLog);
                }
                if (StringUtils.isNotEmpty(isactive)) {
                    if (StringUtils.equals(SgConstants.IS_ACTIVE_Y, isactive)) {
                        SgCOperationLog operationLog = logCommonService.getOperationLog("SG_C_CHANNEL_RATIO_STRATEGY",
                                OperationTypeEnum.MOD.getOperationValue(), id, "比例同步策略", sgCSaStore.getEname() + "启用状态",
                                YesOrNoEnum.NO.getName(), YesOrNoEnum.YES.getName(), loginUser);
                        updateDataList.add(operationLog);
                    }
                    if (StringUtils.equals(SgConstants.IS_ACTIVE_N, isactive)) {
                        SgCOperationLog operationLog = logCommonService.getOperationLog("SG_C_CHANNEL_RATIO_STRATEGY",
                                OperationTypeEnum.MOD.getOperationValue(), id, "比例同步策略", sgCSaStore.getEname() + "启用状态",
                                YesOrNoEnum.YES.getName(), YesOrNoEnum.NO.getName(), loginUser);
                        updateDataList.add(operationLog);
                    }
//                    List<SgCChannelRatioStrategyItem> collect = strategyItems.stream()
//                            .filter(f -> f.getId().equals(itemDTO.getId()) &&
//                                    BigDecimal.ZERO.compareTo(f.getRatio()) < 0)
//                            .collect(Collectors.toList());
//                    if (CollectionUtils.isNotEmpty(collect)) {
//                        calcAndSyncStorage(id);
//                    }
                }
                if (newOrderno != null && newOrderno > 0) {
                    SgCOperationLog operationLog = logCommonService.getOperationLog("SG_C_CHANNEL_RATIO_STRATEGY",
                            OperationTypeEnum.MOD.getOperationValue(), id, "比例同步策略", sgCSaStore.getEname() + "寻源优先级",
                            oldOrderno.toString(), newOrderno.toString(), loginUser);
                    updateDataList.add(operationLog);
                }
            } else {
                StringBuilder sb = new StringBuilder();
                sb.append("[配销仓:" + saStore != null ? saStore.getEname() : "" + "],");
                sb.append("[比例:" + itemDTO.getRatio() + "],");
                sb.append("[寻源优先级:" + itemDTO.getSgCSaStoreOrderno() + "]");
                SgCOperationLog operationLog = logCommonService.getOperationLog("SG_C_CHANNEL_RATIO_STRATEGY",
                        OperationTypeEnum.ADD.getOperationValue(), id, "比例同步策略", "新增",
                        null, sb.toString(), loginUser);
                updateDataList.add(operationLog);
            }
        }
        if (CollectionUtils.isNotEmpty(updateDataList)) {
            logCommonService.batchInsertLog(updateDataList);
        }
        return v14;
    }

    @Override
    public ValueHolderV14 execAfterSubTable(SgCChannelRatioStrategyDTO mainObject, List<SgCChannelRatioStrategyItemDTO> subObjectList, User loginUser) {
        return null;
    }
}
