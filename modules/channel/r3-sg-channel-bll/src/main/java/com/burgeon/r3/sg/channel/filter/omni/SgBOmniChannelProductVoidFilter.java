package com.burgeon.r3.sg.channel.filter.omni;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.channel.model.dto.omni.SgBOmniChannelProductDto;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/29 13:47
 */
@Slf4j
@Component
public class SgBOmniChannelProductVoidFilter extends BaseSingleFilter<SgBOmniChannelProductDto> {
    @Override
    public String getFilterMsgName() {
        return "全渠道商品作废过滤";
    }

    @Override
    public Class getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgBOmniChannelProductDto mainObject, User loginUser) {
        return null;
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgBOmniChannelProductDto mainObject, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("全渠道商品作废成功！", loginUser.getLocale()));

        if (log.isDebugEnabled()) {
            log.debug("Start SgChannelGoodVoidFilter.void:request={}", JSONObject.toJSONString(mainObject));
        }

        if (Objects.isNull(mainObject)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("传入参数不能为空", loginUser.getLocale()));
        }

        if (SgConstants.IS_ACTIVE_N.equalsIgnoreCase(mainObject.getIsactive())) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("当前记录已经不可用，不允许重复操作", loginUser.getLocale()));
            return vh;
        }

        if (SgConstants.IS_YES_OR_NO_Y.equalsIgnoreCase(mainObject.getIsTmllCity())) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("当前记录是平台同城购，不允许作废操作", loginUser.getLocale()));
            return vh;
        }
        return vh;
    }
}
