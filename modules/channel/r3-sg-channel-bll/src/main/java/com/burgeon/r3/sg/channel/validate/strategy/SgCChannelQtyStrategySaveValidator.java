package com.burgeon.r3.sg.channel.validate.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyItemMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelQtyStrategyDTO;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelQtyStrategyItemDTO;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.qtystrategy.SgCChannelQtyStrategy;
import com.burgeon.r3.sg.core.model.table.channel.qtystrategy.SgCChannelQtyStrategyItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleItemValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: lishijun
 * @Date:
 * @Description:
 */
@Slf4j
@Component
public class SgCChannelQtyStrategySaveValidator extends BaseSingleItemValidator<SgCChannelQtyStrategyDTO, SgCChannelQtyStrategyItemDTO> {

    @Autowired
    private SgCChannelQtyStrategyMapper mapper;

    @Autowired
    private SgCChannelQtyStrategyItemMapper itemMapper;


    @Override
    public String getValidatorMsgName() {
        return "数量同步策略保存";
    }

    @Override
    public Class getValidatorClass() {
        return SgCChannelQtyStrategySaveValidator.class;
    }

    @Override
    public ValueHolderV14 validateSubTable(SgCChannelQtyStrategyDTO qtyStrategyDTO, List<SgCChannelQtyStrategyItemDTO> qtyStrategyItemDTOList, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        Long id = qtyStrategyDTO.getId();
        LambdaQueryWrapper<SgCChannelQtyStrategyItem> queryWrapper = new LambdaQueryWrapper<>();
        if (id > 0L) {
            queryWrapper.ne(SgCChannelQtyStrategyItem::getSgCChannelQtyStrategyId, id);
        }
        for (SgCChannelQtyStrategyItemDTO qtyStrategyItemDTO : qtyStrategyItemDTOList) {
            // 若当前平台条码ID，在其他[可用]=是的【按数量同步策略】已发布明细中存在，
            // 则提示：“当前商品已存在其他有效的活动，不允许重复设置”
            if (qtyStrategyItemDTO.getId() < 0L) {
                String skuId = qtyStrategyItemDTO.getSkuId();
                queryWrapper.eq(SgCChannelQtyStrategyItem::getSkuId, skuId)
                        .eq(SgCChannelQtyStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                        .eq(SgCChannelQtyStrategyItem::getIspublish, SgConstants.IS_ACTIVE_Y);
                Integer count = itemMapper.selectCount(queryWrapper);
                if (count > 0) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(Resources.getMessage("当前商品" + skuId + "已存在其他有效的活动，不允许重复设置"));
                    return v14;
                }
            }
        }
        return v14;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCChannelQtyStrategyDTO qtyStrategyDTO, User user) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        Long id = qtyStrategyDTO.getId();

        Long cpShopId = qtyStrategyDTO.getCpCShopId();
        String activityName = qtyStrategyDTO.getActivityName();
        boolean activityNameFlag = true;
        if (id > 0L) {
            SgCChannelQtyStrategy oldSkuStrategy = getOrignalData();
            cpShopId = oldSkuStrategy.getCpCShopId();
            activityNameFlag = false;
            if (StringUtils.isNotEmpty(activityName)) {
                //名称相同无需判重
                activityNameFlag = !(oldSkuStrategy.getActivityName().equals(activityName));
            }
        }
        //如果平台店铺+活动名称 不唯一，则提示：当前店铺此存在此活动的设置，不允许重复设置
        if (activityNameFlag) {
            Integer count = mapper.selectCount(new LambdaQueryWrapper<SgCChannelQtyStrategy>()
                    .eq(SgCChannelQtyStrategy::getCpCShopId, cpShopId)
                    .eq(SgCChannelQtyStrategy::getActivityName, activityName)
                    .eq(SgCChannelQtyStrategy::getIsactive, SgConstants.IS_ACTIVE_Y));
            if (count > 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(Resources.getMessage("当前店铺此存在此活动的设置，不允许重复设置", user.getLocale()));
                return v14;
            }
        }
        return v14;
    }
}