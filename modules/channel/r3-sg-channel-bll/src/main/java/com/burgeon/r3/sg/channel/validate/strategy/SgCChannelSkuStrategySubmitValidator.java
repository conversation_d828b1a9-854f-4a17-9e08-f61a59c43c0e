package com.burgeon.r3.sg.channel.validate.strategy;

import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelSkuStrategyDTO;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/6/4 15:27
 */
@Component
public class SgCChannelSkuStrategySubmitValidator extends BaseSingleValidator<SgCChannelSkuStrategyDTO> {

    @Override
    public String getValidatorMsgName() {
        return "特殊条码按比例同步策略审核";
    }

    @Override
    public Class getValidatorClass() {
        return SgCChannelSkuStrategySubmitValidator.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCChannelSkuStrategyDTO mainObject, User loginUser) {
        //TODO 若当前平台店铺+ 平台条码ID在【渠道库存锁定库存查询】的[剩余量]大于0，则提示：当前店铺的平台条码ID：XXX存在指定数量同步，不允许再设置比例同步策略。
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("审核校验通过！"));
    }
}
