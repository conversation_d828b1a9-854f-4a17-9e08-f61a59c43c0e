package com.burgeon.r3.sg.channel.services.appoint;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.appoint.SgCChannelShopAppointWarehouseAppointItemMapper;
import com.burgeon.r3.sg.channel.mapper.appoint.SgCChannelShopAppointWarehouseExcludeItemMapper;
import com.burgeon.r3.sg.channel.mapper.appoint.SgCChannelShopAppointWarehouseMapper;
import com.burgeon.r3.sg.channel.model.result.appoint.SgCChannelShopAppointWarehouseQueryResult;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.table.channel.appoint.SgCChannelShopAppointWarehouse;
import com.burgeon.r3.sg.core.model.table.channel.appoint.SgCChannelShopAppointWarehouseAppointItem;
import com.burgeon.r3.sg.core.model.table.channel.appoint.SgCChannelShopAppointWarehouseExcludeItem;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/03/02 14:53
 */
@Slf4j
@Component
public class SgCChannelShopAppointWarehouseService {

    @Autowired
    private SgCChannelShopAppointWarehouseMapper mapper;

    @Autowired
    private SgCChannelShopAppointWarehouseAppointItemMapper appointItemMapper;
    @Autowired
    private SgCChannelShopAppointWarehouseExcludeItemMapper excludeItemMapper;

    /**
     * 查询店铺排除/指定实体仓信息
     */
    public ValueHolderV14<SgCChannelShopAppointWarehouseQueryResult> queryShopAppointWarehouseByShop(Long shopId) {
        if (shopId == null) {
            return new ValueHolderV14<>(ResultCode.FAIL, "店铺id 不能为空");
        }

        log.info("SgCChannelShopAppointWarehouseQueryService.queryShopAppointWarehouseByShop shopId:{}", shopId);

        LambdaQueryWrapper<SgCChannelShopAppointWarehouse> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SgCChannelShopAppointWarehouse::getCpCShopId, shopId);
        wrapper.eq(SgCChannelShopAppointWarehouse::getStatus, SgChannelConstants.BILL_CHANNEL_STRATEGY_SUBMIT);
        wrapper.eq(SgCChannelShopAppointWarehouse::getIsactive, SgConstants.IS_ACTIVE_Y);
        SgCChannelShopAppointWarehouse appointWarehouse = null;
        try {
            appointWarehouse = mapper.selectOne(wrapper);
        } catch (Exception e) {
            log.error("SgCChannelShopAppointWarehouseQueryService.queryShopAppointWarehouseByShop error:{}", Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, "查询策略异常");
        }

        if (appointWarehouse == null) {
            return new ValueHolderV14<>(ResultCode.FAIL, "该店铺无策略");
        }
        SgCChannelShopAppointWarehouseQueryResult result = new SgCChannelShopAppointWarehouseQueryResult();
        result.setChannelShopAppointWarehouse(appointWarehouse);

        LambdaQueryWrapper<SgCChannelShopAppointWarehouseAppointItem> appointWrapper = new LambdaQueryWrapper<>();
        appointWrapper.eq(SgCChannelShopAppointWarehouseAppointItem::getSgCChannelShopAppointWarehouseId, appointWarehouse.getId());
        appointWrapper.eq(SgCChannelShopAppointWarehouseAppointItem::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgCChannelShopAppointWarehouseAppointItem> appointItemList = appointItemMapper.selectList(appointWrapper);

        result.setItemType(SgCChannelShopAppointWarehouseQueryResult.APPOINT_ITEM);
        result.setAppointItemList(appointItemList);

        if (CollectionUtils.isEmpty(appointItemList)) {
            LambdaQueryWrapper<SgCChannelShopAppointWarehouseExcludeItem> excludeWrapper = new LambdaQueryWrapper<>();
            excludeWrapper.eq(SgCChannelShopAppointWarehouseExcludeItem::getSgCChannelShopAppointWarehouseId, appointWarehouse.getId());
            excludeWrapper.eq(SgCChannelShopAppointWarehouseExcludeItem::getIsactive, SgConstants.IS_ACTIVE_Y);
            List<SgCChannelShopAppointWarehouseExcludeItem> excludeItemList = excludeItemMapper.selectList(excludeWrapper);

            if (CollectionUtils.isEmpty(excludeItemList)) {
                return new ValueHolderV14<>(ResultCode.FAIL, "该策略无明细!");
            }

            result.setItemType(SgCChannelShopAppointWarehouseQueryResult.EXCLUDE_ITEM);
            result.setExcludeItemList(excludeItemList);
        }
        return new ValueHolderV14<>(result, ResultCode.SUCCESS, "success");
    }

    /**
     * 结案
     * @param session
     * @return
     */
    public ValueHolder eventSubmit(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);

        User user = request.getLoginUser();

        SgCChannelShopAppointWarehouse data = mapper.selectById(request.getObjId());

        if (data == null) {
            return R3ParamUtils.convertV14WithResult(new ValueHolderV14<>(com.jackrain.nea.sys.constants.ResultCode.FAIL,
                    Resources.getMessage("单据已不存在", user.getLocale())));
        }

        if (!SgChannelConstants.BILL_CHANNEL_STRATEGY_SUBMIT.equals(data.getStatus())) {
            return R3ParamUtils.convertV14WithResult(new ValueHolderV14<>(com.jackrain.nea.sys.constants.ResultCode.FAIL,
                    Resources.getMessage("单据状态不为已审核，不允许结案", user.getLocale())));
        }

        data.setStatus(SgChannelConstants.BILL_CHANNEL_STRATEGY_CLOSED);
        data.setEventId(Long.valueOf(user.getId()));
        data.setEventEname(user.getEname());
        data.setEventName(user.getName());
        data.setEventTime(new Date());

        mapper.updateById(data);
        return R3ParamUtils.convertV14WithResult(new ValueHolderV14<>(com.jackrain.nea.sys.constants.ResultCode.SUCCESS,
                Resources.getMessage("结案成功", user.getLocale())));
    }
}
