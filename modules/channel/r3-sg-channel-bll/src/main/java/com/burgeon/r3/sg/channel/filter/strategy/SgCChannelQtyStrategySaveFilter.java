package com.burgeon.r3.sg.channel.filter.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.model.SgStorageRedisQueryCfModel;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQueryCfResult;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.SgStoreUtils;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyItemMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelQtyStrategyDTO;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelQtyStrategyItemDTO;
import com.burgeon.r3.sg.channel.utils.SgChannelProductUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.qtystrategy.SgCChannelQtyStrategyItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleItemFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date
 */
@Slf4j
@Component
public class SgCChannelQtyStrategySaveFilter extends BaseSingleItemFilter<SgCChannelQtyStrategyDTO, SgCChannelQtyStrategyItemDTO> {

    @Autowired
    private SgCChannelQtyStrategyItemMapper qtyStrategyItemMapper;

    @Autowired
    private SgStorageQueryService sgStorageQueryService;


    @Override
    public ValueHolderV14 execBeforeSubTable(SgCChannelQtyStrategyDTO mainObject, List<SgCChannelQtyStrategyItemDTO> subObjectList, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("before sub table success"));
        // 根据平台店铺id 查询平台店铺数据，如果不存在，则返回FAIL
        Long shopId = mainObject.getCpCShopId();
        if (shopId == null || shopId < 1L) {
            shopId = getOrignalData().getCpCShopId();
        }
        CpShop shop = CommonCacheValUtils.getShopInfo(shopId);
        if (shop == null) {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("未获取到正确的平台店铺！"));
        }

        List<SgStorageRedisQueryCfModel> cfModels = new ArrayList<>();
        //条件校验 字段冗余
        for (SgCChannelQtyStrategyItemDTO qtyStrategyItemDTO : subObjectList) {
            // 配销仓
            if (null != qtyStrategyItemDTO.getSgCSaStoreId()) {
                // 查询配销仓数据，如果不存在，则返回失败：
                SgCSaStore store = CommonCacheValUtils.getSaStore(qtyStrategyItemDTO.getSgCSaStoreId());
                if (null == store) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("未获取到配销仓信息！"));
                }
                // 控制输入配销仓只能来源于平台店铺所属聚合仓下的配销仓
                if (!store.getSgCShareStoreId().equals(shop.getSgCShareStoreId())) {
                    return new ValueHolderV14<>(ResultCode.FAIL,
                            Resources.getMessage("当前配销仓:" + store.getEname() + "所属的聚合仓不等于当前" +
                                    "店铺的所属聚合仓,不允许！", loginUser.getLocale()));
                }
                // 赋值默认值
                qtyStrategyItemDTO.setSgCSaStoreEcode(store.getEcode());
                qtyStrategyItemDTO.setSgCSaStoreEname(store.getEname());

                mainObject.setCpCShopEcode(shop.getEcode());
                mainObject.setCpCShopTitle(shop.getCpCShopTitle());
            }
            // 平台条码id
            String skuId = qtyStrategyItemDTO.getSkuId();
            if (StringUtils.isNotEmpty(skuId)) {
                // 根据平台条码id 查询商品信息
                SgBChannelProduct product = SgChannelProductUtils.getChannelProduct(skuId);
                if (product == null) {
                    return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("未获取到平台条码信息！"));
                }
                // 赋值默认值
                qtyStrategyItemDTO.setProId(product.getNumiid());
                qtyStrategyItemDTO.setPsCSkuId(product.getPsCSkuId());
                qtyStrategyItemDTO.setPsCSkuEcode(product.getPsCSkuEcode());
                qtyStrategyItemDTO.setGbcode(product.getGbcode());
                qtyStrategyItemDTO.setPsCBrandId(product.getPsCBrandId());
                qtyStrategyItemDTO.setPsCProId(product.getPsCProId());
                qtyStrategyItemDTO.setPsCProEcode(product.getPsCProEcode());
                qtyStrategyItemDTO.setPsCProEname(product.getPsCProEname());
                qtyStrategyItemDTO.setPsCSpec1Id(product.getPsCSpec1Id());
                qtyStrategyItemDTO.setPsCSpec2Id(product.getPsCSpec2Id());
                qtyStrategyItemDTO.setPsCSpec1Ecode(product.getPsCSpec1Ecode());
                qtyStrategyItemDTO.setPsCSpec2Ecode(product.getPsCSpec2Ecode());
                qtyStrategyItemDTO.setPsCSpec1Ename(product.getPsCSpec1Ename());
                qtyStrategyItemDTO.setPsCSpec2Ename(product.getPsCSpec2Ename());
            }
            //页面修改 防止锁定库存查询空值
            if (qtyStrategyItemDTO.getId() > 0L) {
                SgCChannelQtyStrategyItem qtyStrategyItem = qtyStrategyItemMapper.selectById(qtyStrategyItemDTO.getId());
                qtyStrategyItemDTO.setSgCSaStoreId(qtyStrategyItem.getSgCSaStoreId());
                qtyStrategyItemDTO.setPsCSkuId(qtyStrategyItem.getPsCSkuId());
                qtyStrategyItemDTO.setSkuId(qtyStrategyItem.getSkuId());
            }
            //锁定库存查询组装数据
            SgStorageRedisQueryCfModel model = new SgStorageRedisQueryCfModel();
            model.setCpCShopId(shopId);
            model.setSgCSaStoreId(qtyStrategyItemDTO.getSgCSaStoreId());
            model.setSkuId(qtyStrategyItemDTO.getSkuId());
            cfModels.add(model);
        }
        // 获取对应条码的锁定库存
        ValueHolderV14<HashMap<String, SgStorageRedisQueryCfResult>> holderV14 = sgStorageQueryService.queryCfStorageWithRedis(cfModels, loginUser);
        HashMap<String, SgStorageRedisQueryCfResult> map = holderV14.getData();
        if (!holderV14.isOK() || MapUtils.isEmpty(map)) {
            v14.setCode(holderV14.getCode());
            v14.setMessage(Resources.getMessage("查询到条码锁定库存异常"));
            return v14;
        }
        //调整数量检验  未同步明细累计
        for (SgCChannelQtyStrategyItemDTO qtyStrategyItemDTO : subObjectList) {
            BigDecimal qty = qtyStrategyItemDTO.getQty();
            Long saStoreId = qtyStrategyItemDTO.getSgCSaStoreId();
            Long psSkuId = qtyStrategyItemDTO.getPsCSkuId();
            String skuId = qtyStrategyItemDTO.getSkuId();
            //  若调整数量 A<0，且绝对值 大于 对应条码在【渠道库存锁定库存】中的[剩余数]Y，不允许！
            if (null != qty) {
                if (BigDecimal.ZERO.compareTo(qty) > 0) {
                    String key = shopId + SgConstants.SG_CONNECTOR_MARKS_4 + saStoreId + SgConstants.SG_CONNECTOR_MARKS_4 + skuId;
                    if (!map.containsKey(key)) {
                        v14.setCode(ResultCode.FAIL);
                        v14.setMessage(Resources.getMessage("平台条码【" + skuId + "】未查询到渠道锁定库存"));
                        return v14;
                    }
                    SgStorageRedisQueryCfResult queryCfResult = map.get(key);
                    BigDecimal qtyAvailable = queryCfResult.getQtyAvailable();
                    if (qty.compareTo(qtyAvailable) > 0) {
                        v14.setCode(ResultCode.FAIL);
                        v14.setMessage(Resources.getMessage("当前条码" + psSkuId + " 调整数量 " + qty + "，剩余数 " + qtyAvailable + "，调整后会导致剩余数小于0，不允许！"));
                        return v14;
                    }

                }
            }
            //未同步明细存在相同进行累计 已同步新增
            if (Objects.isNull(qtyStrategyItemDTO.getId()) || qtyStrategyItemDTO.getId() < 1L) {
                LambdaQueryWrapper<SgCChannelQtyStrategyItem> itemWrapper = new LambdaQueryWrapper<>();
                itemWrapper.eq(SgCChannelQtyStrategyItem::getSgCChannelQtyStrategyId, mainObject.getId());
                itemWrapper.eq(SgCChannelQtyStrategyItem::getSgCSaStoreId, qtyStrategyItemDTO.getSgCSaStoreId());
                itemWrapper.eq(SgCChannelQtyStrategyItem::getSkuId, qtyStrategyItemDTO.getSkuId());
                itemWrapper.eq(SgCChannelQtyStrategyItem::getIspublish, SgConstants.IS_ACTIVE_N);
                SgCChannelQtyStrategyItem item = qtyStrategyItemMapper.selectOne(itemWrapper);

                if (Objects.nonNull(item)) {
                    qtyStrategyItemDTO.setId(item.getId());
                    qtyStrategyItemDTO.setQty(item.getQty().add(qtyStrategyItemDTO.getQty()));
                }
            }
        }
        return v14;
    }

    @Override
    public ValueHolderV14 execAfterSubTable(SgCChannelQtyStrategyDTO mainObject, List<SgCChannelQtyStrategyItemDTO> subObjectList, User loginUser) {
        return null;
    }

    @Override
    public String getFilterMsgName() {
        return "数量同步策略保存";
    }

    @Override
    public Class<?> getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgCChannelQtyStrategyDTO qtyStrategyDTO, User loginUser) {
        qtyStrategyDTO.setStatus(SgChannelConstants.BILL_CHANNEL_STRATEGY_UNSUBMIT);
        String billNo = SgStoreUtils.getBillNo(SgChannelConstants.SEQ_SG_C_CHANNEL_QTY_STRATEGY,
                SgConstants.SG_B_STO_FREEZE.toUpperCase(), qtyStrategyDTO, loginUser.getLocale());
        qtyStrategyDTO.setBillNo(billNo);
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("before main table success"));
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgCChannelQtyStrategyDTO mainObject, User loginUser) {
        return null;
    }
}
