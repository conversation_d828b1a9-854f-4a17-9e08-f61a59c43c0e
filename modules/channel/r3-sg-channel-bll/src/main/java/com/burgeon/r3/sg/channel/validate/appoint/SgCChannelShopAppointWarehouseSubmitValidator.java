package com.burgeon.r3.sg.channel.validate.appoint;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.appoint.SgCChannelShopAppointWarehouseAppointItemMapper;
import com.burgeon.r3.sg.channel.mapper.appoint.SgCChannelShopAppointWarehouseExcludeItemMapper;
import com.burgeon.r3.sg.channel.mapper.appoint.SgCChannelShopAppointWarehouseMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelSkuStrategyDTO;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.appoint.SgCChannelShopAppointWarehouse;
import com.burgeon.r3.sg.core.model.table.channel.appoint.SgCChannelShopAppointWarehouseAppointItem;
import com.burgeon.r3.sg.core.model.table.channel.appoint.SgCChannelShopAppointWarehouseExcludeItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/4 15:27
 */
@Component
public class SgCChannelShopAppointWarehouseSubmitValidator extends BaseSingleValidator<SgCChannelSkuStrategyDTO> {

    @Autowired
    private SgCChannelShopAppointWarehouseMapper mapper;
    @Autowired
    private SgCChannelShopAppointWarehouseAppointItemMapper appointItemMapper;
    @Autowired
    private SgCChannelShopAppointWarehouseExcludeItemMapper excludeItemMapper;

    @Override
    public String getValidatorMsgName() {
        return "店铺指定实体仓设置审核";
    }

    @Override
    public Class getValidatorClass() {
        return SgCChannelShopAppointWarehouseSubmitValidator.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCChannelSkuStrategyDTO mainObject, User loginUser) {
        SgCChannelSkuStrategyDTO data = getOrignalData();
        ValueHolderV14 vh = checkShopAndTime(data.getCpCShopId(), data.getBeginTime(), data.getEndTime(), loginUser);
        if (!vh.isOK()) {
            return vh;
        }

        if (!SgChannelConstants.BILL_CHANNEL_STRATEGY_UNSUBMIT.equals(data.getStatus())) {
            return new ValueHolderV14<>(ResultCode.FAIL,
                    Resources.getMessage("单据状态不为未审核，不允许审核", loginUser.getLocale()));
        }
        LambdaQueryWrapper<SgCChannelShopAppointWarehouseAppointItem> appointWrapper = new LambdaQueryWrapper<>();
        appointWrapper.eq(SgCChannelShopAppointWarehouseAppointItem::getSgCChannelShopAppointWarehouseId, data.getId());
        appointWrapper.eq(SgCChannelShopAppointWarehouseAppointItem::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgCChannelShopAppointWarehouseAppointItem> appointItemList = appointItemMapper.selectList(appointWrapper);

        LambdaQueryWrapper<SgCChannelShopAppointWarehouseExcludeItem> excludeWrapper = new LambdaQueryWrapper<>();
        excludeWrapper.eq(SgCChannelShopAppointWarehouseExcludeItem::getSgCChannelShopAppointWarehouseId, data.getId());
        excludeWrapper.eq(SgCChannelShopAppointWarehouseExcludeItem::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgCChannelShopAppointWarehouseExcludeItem> excludeItemList = excludeItemMapper.selectList(excludeWrapper);

        if (!CollectionUtils.isEmpty(appointItemList) && !CollectionUtils.isEmpty(excludeItemList)) {
            return new ValueHolderV14<>(ResultCode.FAIL,
                    Resources.getMessage("不可同时设置指定明细与排除明细", loginUser.getLocale()));
        } else if (!CollectionUtils.isEmpty(appointItemList) && !CollectionUtils.isEmpty(excludeItemList)) {
            return new ValueHolderV14<>(ResultCode.FAIL,
                    Resources.getMessage("无明细", loginUser.getLocale()));
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("审核校验通过！"));
    }

    public ValueHolderV14 checkShopAndTime(Long shopId, Date beginTime, Date endTime, User user) {
        SgCChannelShopAppointWarehouse skuStrategy =
                mapper.selectOne(new LambdaQueryWrapper<SgCChannelShopAppointWarehouse>()
                        .eq(SgCChannelShopAppointWarehouse::getCpCShopId, shopId)
                        .eq(SgCChannelShopAppointWarehouse::getStatus,
                                SgChannelConstants.BILL_CHANNEL_STRATEGY_SUBMIT)
                        .and(wrapper -> wrapper.between(SgCChannelShopAppointWarehouse::getBeginTime, beginTime, endTime)
                                .or().between(SgCChannelShopAppointWarehouse::getEndTime, beginTime, endTime)));
        if (skuStrategy != null) {
            return new ValueHolderV14<>(ResultCode.FAIL,
                    Resources.getMessage("平台店铺:" + skuStrategy.getCpCShopTitle() +
                            "当前时间已存在有效的店铺指定实体仓设置，不允许重复设置！", user.getLocale()));
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
    }
}
