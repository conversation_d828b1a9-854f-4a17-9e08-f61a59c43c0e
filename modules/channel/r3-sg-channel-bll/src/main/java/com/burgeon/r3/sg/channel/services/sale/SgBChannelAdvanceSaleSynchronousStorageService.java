package com.burgeon.r3.sg.channel.services.sale;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.sale.SgBChannelAdvanceSaleItemMapper;
import com.burgeon.r3.sg.channel.mapper.sale.SgBChannelAdvanceSaleMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyItemMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategyMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategySaItemMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.request.mq.SgChannelStorageOmsBufferRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.channel.qtystrategy.SgCChannelQtyStrategy;
import com.burgeon.r3.sg.core.model.table.channel.qtystrategy.SgCChannelQtyStrategyItem;
import com.burgeon.r3.sg.core.model.table.channel.sale.SgBChannelAdvanceSale;
import com.burgeon.r3.sg.core.model.table.channel.sale.SgBChannelAdvanceSaleItem;
import com.burgeon.r3.sg.core.model.table.channel.skustrategy.SgCChannelSkuStrategy;
import com.burgeon.r3.sg.core.model.table.channel.skustrategy.SgCChannelSkuStrategySaItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.SgRedisLockUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.stocksync.api.SgBChannelStorageBufferCmd;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2021-06-17 10:09
 * @Description:
 */
@Slf4j
@Component
public class SgBChannelAdvanceSaleSynchronousStorageService {

    @Autowired
    SgBChannelAdvanceSaleMapper mapper;
    @Autowired
    SgBChannelAdvanceSaleItemMapper itemMapper;
    @Autowired
    private SgCChannelQtyStrategyMapper strategyMapper;
    @Autowired
    private SgCChannelQtyStrategyItemMapper strategyItemMapper;
    @Autowired
    private SgCChannelSkuStrategyMapper sgChannelSkuStrategyMapper;
    @Autowired
    private SgCChannelSkuStrategySaItemMapper skuStrategySaItemMapper;
    @Reference(version = "1.0", group = "sg")
    SgBChannelStorageBufferCmd channelStorageBufferCmd;

    /**
     * 同步库存
     *
     * @param session 入参
     * @return 出参
     */
    public ValueHolder synchronousStorage(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        SgBChannelAdvanceSaleSynchronousStorageService service =
                ApplicationContextHandle.getBean(SgBChannelAdvanceSaleSynchronousStorageService.class);
        return R3ParamUtils.convertV14WithResult(service.synchronousStorage(request));
    }

    /**
     * 同步库存
     *
     * @param request 入参
     * @return 出参
     */
    public ValueHolderV14<SgR3BaseResult> synchronousStorage(SgR3BaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelAdvanceSaleSynchronousStorageService.synchronousStorage:request{}",
                    JSONObject.toJSONString(request));
        }

        SgBChannelAdvanceSale sale = checkParam(request);
        User loginUser = request.getLoginUser();
        String lockKsy = SgConstants.SG_B_CHANNEL_ADVANCE_SALE + ":" + request.getObjId();
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "同步成功!");
        SgRedisLockUtils.lock(lockKsy);
        try {
            //当前时间
            Date date = new Date();
            //平台店铺+平台条码ID的同步库存量等于 X。结果集A1
            List<SgBChannelAdvanceSaleItem> sgChannelAdvanceSaleItems = itemMapper.selectList(new QueryWrapper<SgBChannelAdvanceSaleItem>()
                    .lambda()
                    .eq(SgBChannelAdvanceSaleItem::getSgBChannelAdvanceSaleId, sale.getId())
                    .eq(SgBChannelAdvanceSaleItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                    .gt(SgBChannelAdvanceSaleItem::getQtyRemain, BigDecimal.ZERO));
            AssertUtils.cannot(CollectionUtils.isEmpty(sgChannelAdvanceSaleItems), "当前渠道预售活动无可用明细！");

            //跟【店铺锁定策略】比较
            sgChannelAdvanceSaleItems = compareToStrategy(sale, sgChannelAdvanceSaleItems, date);
            //跟【特殊条码店铺锁定策略】比较
            sgChannelAdvanceSaleItems = compareToSkuStrategy(sale, sgChannelAdvanceSaleItems, date);

            ArrayList<SgChannelStorageOmsBufferRequest> requestList = new ArrayList<>();
            sgChannelAdvanceSaleItems.forEach(item -> {
                SgChannelStorageOmsBufferRequest channelRequest = new SgChannelStorageOmsBufferRequest();
                channelRequest.setCpCStoreId(sale.getCpCShopId());
                channelRequest.setChangeNum(item.getQty());
                channelRequest.setPsCSkuId(item.getPsCSkuId());
                channelRequest.setFixedQtyFlag(SgConstants.IS_YES_OR_NO_Y);
                channelRequest.setFixedQty(item.getQty());
                channelRequest.setSyncType(SgConstants.SYNC_TYPE_ALL);
                channelRequest.setSourceNo(sale.getBillNo());
                requestList.add(channelRequest);
            });
            ValueHolderV14 syncV14 = channelStorageBufferCmd.createChannelStorageOmsBuffer(requestList);
            if (!syncV14.isOK()) {
                AssertUtils.logAndThrow("库存同步库存失败");
            }

            //更新主表
            SgBChannelAdvanceSale updateSale = new SgBChannelAdvanceSale();
            updateSale.setId(sale.getId());
            updateSale.setSynchronousStatus(SgChannelConstants.SYNCHRONOUS_STATUS_SYN);
            updateSale.setSynchronousId(loginUser.getId().longValue());
            updateSale.setSynchronousName(loginUser.getName());
            updateSale.setSynchronousEname(loginUser.getEname());
            updateSale.setSynchronousTime(date);
            mapper.updateById(updateSale);

        } catch (Exception e) {
            AssertUtils.logAndThrowException("渠道预售活动同步库存异常：", e, request.getLoginUser().getLocale());
        } finally {
            SgRedisLockUtils.unlock(lockKsy, log, this.getClass().getName());
        }
        if (log.isDebugEnabled()) {
            log.debug("end SgBChannelAdvanceSaleSynchronousStorageService.synchronousStorage:ValueHolderV14{}",
                    JSONObject.toJSONString(v14));
        }
        return v14;
    }

    /**
     * b)查询[平台店铺]=当前单据平台店铺、且满足当前时间内的、单据状态=已审核的【店铺锁定策略】，结果集A2
     * 如果结果集A1与A2有交集，则将作废A1中A2存在的记录，即不同步平台
     *
     * @param sale                      渠道预售活动主表
     * @param sgChannelAdvanceSaleItems 渠道预售活动明细
     * @param date                      当前时间
     * @return 返回差集后的渠道预售活动明细
     */
    private List<SgBChannelAdvanceSaleItem> compareToStrategy(SgBChannelAdvanceSale sale,
                                                              List<SgBChannelAdvanceSaleItem> sgChannelAdvanceSaleItems,
                                                              Date date) {

        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelAdvanceSaleSynchronousStorageService.compareToStrategy:itemsSize{}",
                    sgChannelAdvanceSaleItems.size());
        }

        //平台条码ID集合
        List<String> skuids = sgChannelAdvanceSaleItems.stream()
                .map(SgBChannelAdvanceSaleItem::getSkuId).collect(Collectors.toList());

        //[平台店铺]=当前单据平台店铺、且满足当前时间内的、单据状态=已审核的【店铺锁定策略】 （店铺锁定策略 = 数量同步策略 （贺文易曰））
        List<SgCChannelQtyStrategy> sgChannelQtyStrategies = strategyMapper.selectList(new QueryWrapper<SgCChannelQtyStrategy>()
                .lambda()
                .eq(SgCChannelQtyStrategy::getCpCShopId, sale.getCpCShopId())
//                .ge(SgCChannelQtyStrategy::getBeginTime, date)
//                .le(SgCChannelQtyStrategy::getEndTime, date)
                .ge(SgCChannelQtyStrategy::getStatus, SgChannelConstants.BILL_CHANNEL_STRATEGY_SUBMIT)
                .eq(SgCChannelQtyStrategy::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isNotEmpty(sgChannelQtyStrategies)) {
            //数量同步策略id
            List<Long> strateaiesIds = sgChannelQtyStrategies.stream().map(SgCChannelQtyStrategy::getId)
                    .collect(Collectors.toList());
            List<SgCChannelQtyStrategyItem> strategyItemList = batchQuerySgChannelQtyStrategyItem(strateaiesIds, skuids);

            //非空表示渠道预售活动的平台id在店铺锁定策略 有交集
            if (CollectionUtils.isNotEmpty(strategyItemList)) {
                // 如果结果集A1与A2有交集，则将作废A1中A2存在的记录，即不同步平台
                // 这里求差集：原因：这里查出来的strategyItemList里面的数据都是根据sgChannelAdvanceSaleItems里面的平台条码id
                sgChannelAdvanceSaleItems = sgChannelAdvanceSaleItems.stream()
                        .filter(m -> !strategyItemList
                                .stream().map(SgCChannelQtyStrategyItem::getSkuId)
                                .collect(Collectors.toList()).contains(m.getSkuId()))
                        .collect(Collectors.toList());
            }

        }
        return sgChannelAdvanceSaleItems;
    }

    /**
     * 批量查询数量同步策略明细
     * 条件：主表id+平台条码id
     *
     * @param strateaiesIds 数量同步策略id集合
     * @param skuids        条码id集合
     * @return List<SgCChannelQtyStrategyItem> 数量同步策略明细集合
     */
    private List<SgCChannelQtyStrategyItem> batchQuerySgChannelQtyStrategyItem(List<Long> strateaiesIds, List<String> skuids) {
        List<SgCChannelQtyStrategyItem> items = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(strateaiesIds) && CollectionUtils.isNotEmpty(skuids)) {
            List<List<String>> pageSkuidList =
                    StorageUtils.getPageList(skuids, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);

            for (List<String> skuidList : pageSkuidList) {
                List<SgCChannelQtyStrategyItem> pageItemList = strategyItemMapper.selectList(new LambdaQueryWrapper<SgCChannelQtyStrategyItem>()
                        .eq(SgCChannelQtyStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                        .in(SgCChannelQtyStrategyItem::getSgCChannelQtyStrategyId, strateaiesIds)
                        .in(SgCChannelQtyStrategyItem::getSkuId, skuidList));
                if (CollectionUtils.isNotEmpty(pageItemList)) {
                    items.addAll(pageItemList);
                }
            }

        }
        return items;
    }

    /**
     * c)查询[平台店铺]= 当前单据平台店铺、 且满足当前时间内的、单据状态=已审核的【特殊条码店铺锁定策略】，结果集A3
     * 如果结果集A3与A1有交集，则将作废A1中A3存在的记录，即不同步平台
     *
     * @param sale                      渠道预售活动主表
     * @param sgChannelAdvanceSaleItems 渠道预售活动明细
     * @param date                      当前时间
     * @return 返回差集后的渠道预售活动明细
     */
    private List<SgBChannelAdvanceSaleItem> compareToSkuStrategy(SgBChannelAdvanceSale sale,
                                                                 List<SgBChannelAdvanceSaleItem> sgChannelAdvanceSaleItems,
                                                                 Date date) {

        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelAdvanceSaleSynchronousStorageService.compareToSkuStrategy:itemsSize{}",
                    sgChannelAdvanceSaleItems.size());
        }

        //平台条码ID集合
        List<String> skuids = sgChannelAdvanceSaleItems.stream()
                .map(SgBChannelAdvanceSaleItem::getSkuId).collect(Collectors.toList());

        //当前单据平台店铺、 且满足当前时间内的、单据状态=已审核的【特殊条码店铺锁定策略】，结果集A3
        // （特殊条码店铺锁定策略 = 特殊条码按比例同步策略 （贺文易曰））
        List<SgCChannelSkuStrategy> sgChannelSkuStrategies = sgChannelSkuStrategyMapper.selectList(new LambdaQueryWrapper<SgCChannelSkuStrategy>()
                .eq(SgCChannelSkuStrategy::getCpCShopId, sale.getCpCShopId())
                .ge(SgCChannelSkuStrategy::getBeginTime, date)
                .le(SgCChannelSkuStrategy::getEndTime, date)
                .ge(SgCChannelSkuStrategy::getStatus, SgChannelConstants.BILL_CHANNEL_STRATEGY_SUBMIT)
                .eq(SgCChannelSkuStrategy::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isNotEmpty(sgChannelSkuStrategies)) {
            List<Long> skuStrategyIds = sgChannelSkuStrategies.stream().map(SgCChannelSkuStrategy::getId)
                    .collect(Collectors.toList());
            List<SgCChannelSkuStrategySaItem> skuStrategySaItems = batchQuerySgChannelSkuStrategyItem(skuStrategyIds, skuids);

            //非空表示渠道预售活动的平台id在店铺锁定策略 有交集
            if (CollectionUtils.isNotEmpty(skuStrategySaItems)) {
                // 如果结果集A3与A1有交集，则将作废A1中A3存在的记录，即不同步平台
                // 这里求差集：原因：这里查出来的skuStrategySaItems里面的数据都是根据sgChannelAdvanceSaleItems里面的平台条码id
                sgChannelAdvanceSaleItems = sgChannelAdvanceSaleItems.stream()
                        .filter(m -> !skuStrategySaItems
                                .stream().map(SgCChannelSkuStrategySaItem::getSkuId)
                                .collect(Collectors.toList()).contains(m.getSkuId()))
                        .collect(Collectors.toList());
            }
        }
        return sgChannelAdvanceSaleItems;
    }

    /**
     * 批量查询特殊条码店铺锁定策略明细
     * 条件：主表id+平台条码id
     *
     * @param skuStrategyIds 数量同步策略id集合
     * @param skuids         条码id集合
     * @return List<SgCChannelQtyStrategyItem> 特殊条码店铺锁定策略明细明细集合
     */
    private List<SgCChannelSkuStrategySaItem> batchQuerySgChannelSkuStrategyItem(List<Long> skuStrategyIds, List<String> skuids) {
        List<SgCChannelSkuStrategySaItem> items = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(skuStrategyIds) && CollectionUtils.isNotEmpty(skuids)) {
            List<List<String>> pageSkuidList =
                    StorageUtils.getPageList(skuids, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);

            for (List<String> skuidList : pageSkuidList) {
                List<SgCChannelSkuStrategySaItem> pageItemList = skuStrategySaItemMapper.selectList(new LambdaQueryWrapper<SgCChannelSkuStrategySaItem>()
                        .eq(SgCChannelSkuStrategySaItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                        .in(SgCChannelSkuStrategySaItem::getSgCChannelSkuStrategyId, skuStrategyIds)
                        .in(SgCChannelSkuStrategySaItem::getSkuId, skuidList));
                if (CollectionUtils.isNotEmpty(pageItemList)) {
                    items.addAll(pageItemList);
                }
            }

        }
        return items;
    }

    /**
     * 参数校验
     *
     * @param request 入参
     * @return 出参
     */
    private SgBChannelAdvanceSale checkParam(SgR3BaseRequest request) {
        AssertUtils.notNull(request, "入参为空！");

        Long objId = request.getObjId();
        AssertUtils.notNull(objId, "主表ID为空！");
        SgBChannelAdvanceSale sale = mapper.selectById(objId);
        AssertUtils.notNull(sale, "当前记录不存在，请检查！！");

        Integer synchronousStatus = sale.getSynchronousStatus();
        Integer status = sale.getStatus();
        if (SgChannelConstants.BILL_STATUS_SUBMIT != status) {
            AssertUtils.logAndThrow("当前单据状态，不允许进行预售活动库存同步！");
        }
        if (SgChannelConstants.SYNCHRONOUS_STATUS_SYN.equals(synchronousStatus)) {
            AssertUtils.logAndThrow("当前预售活动库存已同步，不允许重复同步！");
        }

        return sale;
    }

}
