package com.burgeon.r3.sg.channel.services.strategy;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.model.SgStorageRedisQueryCfModel;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQueryCfResult;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyItemMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.channel.qtystrategy.SgCChannelQtyStrategy;
import com.burgeon.r3.sg.core.model.table.channel.qtystrategy.SgCChannelQtyStrategyItem;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/6/18 16:15
 * 商品下架
 */
@Slf4j
@Component
public class SgCChannelQtyStrategySoldOutService {

    @Autowired
    private SgCChannelQtyStrategyMapper mapper;

    @Autowired
    private SgCChannelQtyStrategyItemMapper itemMapper;

    @Autowired
    private SgStorageQueryService sgStorageQueryService;

    @Autowired
    private SgCChannelQtyStrategyStockSyncService sgCChannelQtyStrategyStockSyncService;


    public ValueHolder soldout(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        SgCChannelQtyStrategySoldOutService bean = ApplicationContextHandle.getBean(SgCChannelQtyStrategySoldOutService.class);
        return R3ParamUtils.convertV14WithResult(bean.soldout(request));
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> soldout(SgR3BaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SgCChannelQtyStrategySoldOutService.soldout.ReceiveParams:request{}"
                    , JSONObject.toJSONString(request));
        }
        User loginUser = request.getLoginUser();
        List<String> redisBillFtpKeyList = new ArrayList<>();
        try {
            SgCChannelQtyStrategy qtyStrategy = checkParams(request);
            //获取已发明细的剩余量的负数，新增至未发布明细
            selItem(qtyStrategy, loginUser);
            //调用 库存同步服务的逻辑
            SgR3BaseRequest baseRequest = new SgR3BaseRequest();
            baseRequest.setLoginUser(loginUser);
            baseRequest.setObjId(request.getObjId());
            baseRequest.setR3(false);
            sgCChannelQtyStrategyStockSyncService.stocksync(baseRequest);

            //主表修作废
            StorageUtils.setBModelDefalutDataByUpdate(qtyStrategy, loginUser);
            qtyStrategy.setIsactive(SgConstants.IS_ACTIVE_N);
            mapper.updateById(qtyStrategy);

            //明细作废
            SgCChannelQtyStrategyItem strategyItem = new SgCChannelQtyStrategyItem();
            StorageUtils.setBModelDefalutDataByUpdate(strategyItem, loginUser);
            strategyItem.setIsactive(SgConstants.IS_ACTIVE_N);
            itemMapper.update(strategyItem, new LambdaQueryWrapper<SgCChannelQtyStrategyItem>()
                    .eq(SgCChannelQtyStrategyItem::getSgCChannelQtyStrategyId, request.getObjId())
                    .eq(SgCChannelQtyStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y));
        } catch (Exception e) {
            if (CollectionUtils.isNotEmpty(redisBillFtpKeyList)) {
                StorageBasicUtils.rollbackStorage(redisBillFtpKeyList, loginUser);
            }
            AssertUtils.logAndThrowException(e, loginUser.getLocale());
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "活动已下架");
    }


    private void selItem(SgCChannelQtyStrategy qtyStrategy, User loginUser) {
        // 调整数量：【渠道库存锁定库存】中[剩余量]的负数
        Long id = qtyStrategy.getId();
        Long cShopId = qtyStrategy.getCpCShopId();
        //获取已发布明细
        List<SgCChannelQtyStrategyItem> strategyItems = itemMapper.selectList(new LambdaQueryWrapper<SgCChannelQtyStrategyItem>()
                .eq(SgCChannelQtyStrategyItem::getSgCChannelQtyStrategyId, id)
                .eq(SgCChannelQtyStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(SgCChannelQtyStrategyItem::getIspublish, SgConstants.IS_ACTIVE_Y));
        if (CollectionUtils.isEmpty(strategyItems)) {
            AssertUtils.logAndThrow("暂无已发布明细，无需下架");
        }
        Map<String, SgCChannelQtyStrategyItem> itemMap = strategyItems.stream().collect(Collectors.toMap(o -> o.getSgCSaStoreId() + "" + o.getSkuId(), Function.identity(), (o1, o2) -> o1));
        List<SgCChannelQtyStrategyItem> items = new ArrayList(itemMap.values());
        log.debug(" 数量策略去重后的明细 itemlist:{}", JSONObject.toJSONString(items));
        List<SgStorageRedisQueryCfModel> cfModels = new ArrayList<>();
        for (SgCChannelQtyStrategyItem item : items) {
            SgStorageRedisQueryCfModel model = new SgStorageRedisQueryCfModel();
            model.setCpCShopId(cShopId);
            model.setSgCSaStoreId(item.getSgCSaStoreId());
            model.setSkuId(item.getSkuId());
            cfModels.add(model);
        }
        // 获取对应条码的锁定库存
        ValueHolderV14<HashMap<String, SgStorageRedisQueryCfResult>> holderV14 = sgStorageQueryService.queryCfStorageWithRedis(cfModels, loginUser);
        if (!holderV14.isOK()) {
            AssertUtils.logAndThrow("查询条码库存失败：" + holderV14.getMessage());
        }
        HashMap<String, SgStorageRedisQueryCfResult> map = holderV14.getData();
        if (MapUtils.isEmpty(map)) {
            AssertUtils.logAndThrow("未查询到对应条码的锁定库存");
        }
        for (SgCChannelQtyStrategyItem item : items) {
            String key = cShopId + SgConstants.SG_CONNECTOR_MARKS_4 + item.getSgCSaStoreId() + SgConstants.SG_CONNECTOR_MARKS_4 + item.getSkuId();
            if (!map.containsKey(key)) {
                AssertUtils.logAndThrow("未查询到条码【" + item.getSkuId() + "】锁定库存");
            }
            SgStorageRedisQueryCfResult redisQueryCfResult = map.get(key);
            BigDecimal qtyAvailable = redisQueryCfResult.getQtyAvailable();
            item.setId(ModelUtil.getSequence(SgConstants.SG_C_CHANNEL_QTY_STRATEGY_ITEM));
            StorageUtils.setBModelDefalutData(item, loginUser);
            item.setQty(qtyAvailable.negate());
            item.setIspublish(SgConstants.IS_ACTIVE_N);
        }
        log.debug(" 获取渠道库存锁定库存中剩余量的负数  itemlist:{}", JSONObject.toJSONString(items));
        List<List<SgCChannelQtyStrategyItem>> partition = Lists.partition(items, SgConstants.SG_COMMON_INSERT_PAGE_SIZE);
        for (List<SgCChannelQtyStrategyItem> qtyStrategyItems : partition) {
            int result = itemMapper.batchInsert(items);
            if (result != qtyStrategyItems.size()) {
                AssertUtils.logAndThrow("添加未发布明细异常");
            }
        }

    }


    private SgCChannelQtyStrategy checkParams(SgR3BaseRequest request) {
        AssertUtils.notNull(request, "请求参数不能为空");
        Long objId = request.getObjId();
        User loginUser = request.getLoginUser();
        AssertUtils.notNull(objId, "主表id不能为空");
        AssertUtils.notNull(loginUser, "操作用户不能为空");
        SgCChannelQtyStrategy qtyStrategy = mapper.selectById(objId);
        AssertUtils.notNull(qtyStrategy, "当前记录不存在");
        Integer count = itemMapper.selectCount(new LambdaQueryWrapper<SgCChannelQtyStrategyItem>()
                .eq(SgCChannelQtyStrategyItem::getSgCChannelQtyStrategyId, objId)
                .eq(SgCChannelQtyStrategyItem::getIspublish, SgConstants.IS_ACTIVE_N)
                .eq(SgCChannelQtyStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y));
        if (count > 0) {
            AssertUtils.logAndThrow("存在未发布明细记录，不允许活动下架");
        }
        return qtyStrategy;
    }
}
