package com.burgeon.r3.sg.channel.services.strategy;

import com.burgeon.r3.sg.basic.api.record.SgCommRecordCmd;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.request.record.SgCommRecordAddRequest;
import com.burgeon.r3.sg.basic.model.request.record.SgCommRecordData;
import com.burgeon.r3.sg.basic.services.SgBSaStorageQueryService;
import com.burgeon.r3.sg.basic.services.SgStorageQueryService;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyItemMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelQtyStrategyMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelStorageSyncGradientStrategyMapper;
import com.burgeon.r3.sg.channel.model.enumerate.SaleStatusEnumeration;
import com.burgeon.r3.sg.channel.services.SgChannelStorageService;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.table.channel.gradient.SgCChannelStorageSyncGradientStrategy;
import com.burgeon.r3.sg.core.model.table.channel.gradient.SgCChannelStorageSyncProGradientStrategy;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.stocksync.api.SgChannelStorageOmsInitCmd;
import com.burgeon.r3.sg.stocksync.common.enums.ChannelStoragePoolTypeEnum;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageOmsManualSynchRequest;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/6/18 16:07
 */
@Slf4j
@Component
public class SgCChannelStorageSyncGradientStrategyStockSyncService {

    @Autowired
    private SgCChannelStorageSyncGradientStrategyMapper mapper;

    public ValueHolder stockSync(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        SgCChannelStorageSyncGradientStrategyStockSyncService bean = ApplicationContextHandle.getBean(SgCChannelStorageSyncGradientStrategyStockSyncService.class);
        return R3ParamUtils.convertV14WithResult(bean.stockSync(request));
    }

    /**
     * 梯度策略库存同步
     */
    public ValueHolderV14 stockSync(SgR3BaseRequest request) {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "同步成功");

        SgCChannelStorageSyncGradientStrategy mainObject = mapper.selectById(request.getObjId());
        if (null == mainObject) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("未查询到该对象");
            return vh;
        }

        try {
            HashMap<String, List<SgCommRecordData>> map = new HashMap<>();
            List<SgCommRecordData> list = new ArrayList<>();

            SgCommRecordData data = new SgCommRecordData();
            data.setColumnName("库存同步");
            data.setMessage(request.getLoginUser().getName()+"进行了库存同步操作");

            list.add(data);
            map.put("sync", list);
            saveLog(request.getObjId(), map, request.getLoginUser());
        } catch (Exception e) {
            log.error("库存同步梯度策略日志记录异常");
        }

        SgChannelStorageOmsManualSynchRequest stockSyncRequest = new SgChannelStorageOmsManualSynchRequest();
        SgChannelStorageOmsInitCmd sgChannelStorageOmsInitCmd = (SgChannelStorageOmsInitCmd) ReferenceUtil.refer(
                ApplicationContextHandle.getApplicationContext(),
                SgChannelStorageOmsInitCmd.class.getName(),
                SgConstantsIF.GROUP, SgConstantsIF.VERSION);
        //设置操作方式为按查询条件同步
        stockSyncRequest.setOperate(SgChannelStorageOmsManualSynchRequest.QUERY_BY_CONDITION);
        stockSyncRequest.setUser(R3SystemUserResource.getSystemRootUser());
        stockSyncRequest.setCpCShopId(mainObject.getCpCShopId());
        stockSyncRequest.setChannelName(mainObject.getChannelName());
        stockSyncRequest.setProductLevel(mainObject.getProductLevel());
        stockSyncRequest.setSaleStatus(SaleStatusEnumeration.SALE.getCode());
        stockSyncRequest.setSourceno("平台商品库存同步梯度策略-库存同步");
        stockSyncRequest.setPoolType(ChannelStoragePoolTypeEnum.SYNC_POOL_ALL.getValue());
        ValueHolderV14<Boolean> result = sgChannelStorageOmsInitCmd.manualCalcAndSyncChannelProduct(stockSyncRequest);

        if (result.isOK()) {
            return vh;
        }
        vh.setCode(ResultCode.FAIL);
        vh.setMessage("同步失败");
        return vh;
    }

    /**
     * 保存日志
     * @param id
     * @param map
     * @param user
     */
    public void saveLog(Long id, HashMap<String, List<SgCommRecordData>> map, User user) {
        if (MapUtils.isNotEmpty(map)) {
            SgCommRecordCmd sgCommRecordCmd = (SgCommRecordCmd) ReferenceUtil.refer(
                    ApplicationContextHandle.getApplicationContext(),
                    SgCommRecordCmd.class.getName(),
                    SgConstantsIF.GROUP, SgConstantsIF.VERSION);

            List<SgCommRecordData> update = map.get("update");
            List<SgCommRecordData> aVoid = map.get("void");
            List<SgCommRecordData> sync = map.get("sync");

            SgCommRecordAddRequest request = new SgCommRecordAddRequest();
            request.setRecordId(id);
            request.setTableName(SgConstants.SG_C_CHANNEL_STORAGE_SYNC_GRADIENT_STRATEGY_LOG);

            if (CollectionUtils.isNotEmpty(update)) {
                request.setLogType("修改");
                request.setRecordDatas(update);
                sgCommRecordCmd.addRecord(request, user);
            }
            if (CollectionUtils.isNotEmpty(aVoid)) {
                request.setLogType("作废");
                request.setRecordDatas(aVoid);
                sgCommRecordCmd.addRecord(request, user);
            }
            if (CollectionUtils.isNotEmpty(sync)) {
                request.setLogType("同步");
                request.setRecordDatas(sync);
                sgCommRecordCmd.addRecord(request, user);
            }
        }
    }

}
