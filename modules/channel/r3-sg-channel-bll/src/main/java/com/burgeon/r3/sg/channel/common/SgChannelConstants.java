package com.burgeon.r3.sg.channel.common;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/3 9:28
 */
public class SgChannelConstants {
    /**
     * 渠道层管理 同步策略单据状态
     * 1 未审核 2已审核 3 已作废 4已结案
     */
    public final static Integer BILL_CHANNEL_STRATEGY_UNSUBMIT = 1;
    public final static Integer BILL_CHANNEL_STRATEGY_SUBMIT = 2;
    public final static Integer BILL_CHANNEL_STRATEGY_VOID = 3;
    public final static Integer BILL_CHANNEL_STRATEGY_CLOSED = 4;

    /**
     * 审核类型
     * 1 未审核 2已审核 3 已作废
     */
    public final static int BILL_STATUS_UNSUBMIT = 1;
    public final static int BILL_STATUS_SUBMIT = 2;
    public final static int BILL_STATUS_VOID = 3;

    /**
     * 单据编号生成器
     * 渠道预售活动
     */
    public final static String SEQ_SG_B_CHANNEL_ADVANCE_SALE = "SEQ_SG_B_CHANNEL_ADVANCE_SALE";

    public final static String SEQ_SG_C_CHANNEL_QTY_STRATEGY = "SG_C_CHANNEL_QTY_STRATEGY";

    /**
     * 类型
     * 1 下单 2 发货
     */
    public final static Integer CHANNEL_TYPE_ORDER = 1;
    public final static Integer CHANNEL_TYPE_SNED = 2;

    /**
     * 同步状态
     * 1 已同步 2 未同步
     */
    public final static Integer SYNCHRONOUS_STATUS_SYN = 1;
    public final static Integer SYNCHRONOUS_STATUS_NO_SYN = 2;

    /**
     * 平台库存增量同步状态
     * 0 未同步 1 部分同步 2 同步成功
     */
    public final static Integer CHANNEL_STORAGE_INC_SYNC_UN = 0;
    public final static Integer CHANNEL_STORAGE_INC_SYNC_POR = 1;
    public final static Integer CHANNEL_STORAGE_INC_SYNC_SUCC = 2;

    /**
     * 平台库存增量明细同步状态
     * 0 未同步 1 同步中 2 部分成功 3 同步成功 4 同步失败
     */
    public final static Integer CHANNEL_STORAGE_INC_SYNC_ITEM_UN = 0;
    public final static Integer CHANNEL_STORAGE_INC_SYNC_ITEM_IN = 1;
    public final static Integer CHANNEL_STORAGE_INC_SYNC_ITEM_POR_SUCC = 2;
    public final static Integer CHANNEL_STORAGE_INC_SYNC_ITEM_SUCC = 3;
    public final static Integer CHANNEL_STORAGE_INC_SYNC_ITEM_FAIL = 4;

    /**
     * 平台库存全量同步状态 CHANNEL_STORAGE_FULL_SYNC
     * 0 未同步 1 同步中 2 部分成功 3 同步成功 4 同步失败
     */
    public final static Integer CHANNEL_STORAGE_FULL_SYNC_UN = 0;
    public final static Integer CHANNEL_STORAGE_FULL_SYNC_IN = 1;
    public final static Integer CHANNEL_STORAGE_FULL_SYNC_POR_SUCC = 2;
    public final static Integer CHANNEL_STORAGE_FULL_SYNC_SUCC = 3;
    public final static Integer CHANNEL_STORAGE_FULL_SYNC_FAIL = 4;

    /**
     * 同步类型 01正常 02渠道预售
     */
    public final static String CHANNEL_STORAGE_SYNTYPE_NORMAL = "01";
    public final static String CHANNEL_STORAGE_SYNTYPE_CHANNEL = "02";

    /**
     * 商品状态 1销售中 2仓库中
     */
    public static final int CHANNEL_PRODUCT_STATUS_SALING = 1;
    public static final int CHANNEL_PRODUCT_STATUS_STORING = 2;

    /**
     * 批量设置平台店铺安全库存 单据状态
     * 1 未审核 2已审核 3 已作废
     */
    public final static int CHANNEL_STORE_SAFETY_SETTING_STATUS_UN = 1;
    public final static int CHANNEL_STORE_SAFETY_SETTING_STATUS_SUBMIT = 2;
    public final static int CHANNEL_STORE_SAFETY_SETTING_STATUS_VOID = 3;

    /**
     * 批量设置平台店铺安全库存 商品类型
     * （1:商品编码 2:条码 3:平台商品 4:平台条码）
     */
    public final static String CHANNEL_STORE_SAFETY_SETTING_PRO_TYPE_PS_C_PRO = "1";
    public final static String CHANNEL_STORE_SAFETY_SETTING_PRO_TYPE_PS_C_SKU = "2";
    public final static String CHANNEL_STORE_SAFETY_SETTING_PRO_TYPE_NUMIID = "3";
    public final static String CHANNEL_STORE_SAFETY_SETTING_PRO_TYPE_SKU_ID = "4";
}
