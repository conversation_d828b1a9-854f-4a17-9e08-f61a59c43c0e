package com.burgeon.r3.sg.channel.services.storage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.basic.utils.StorageBasicUtils;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgBChannelStorageFullSyncMapper;
import com.burgeon.r3.sg.channel.model.request.storage.SgChannelStorageFullSyncRequest;
import com.burgeon.r3.sg.core.common.R3ParamConstants;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBChannelStorageFullSync;
import com.burgeon.r3.sg.core.model.table.oms.SgBChannelStorageBuffer;
import com.burgeon.r3.sg.core.model.tableExtend.SgBChannelStorageBufferExtend;
import com.burgeon.r3.sg.core.utils.AssertUtils;
import com.burgeon.r3.sg.core.utils.R3ParamUtils;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.burgeon.r3.sg.stocksync.api.SgBChannelStorageBufferCmd;
import com.burgeon.r3.sg.stocksync.api.SgChannelStorageConsumerCmd;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferBatchSaveRequest;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageBufferSaveRequest;
import com.burgeon.r3.sg.stocksync.model.request.SgChannelStorageOmsCalcRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.cpext.api.UserQueryCmd;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BeanCopierUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/15 10:48
 * 平台库存全量同步Service
 */
@Slf4j
@Component
public class SgBChannelStorageFullSyncService extends ServiceImpl<SgBChannelStorageFullSyncMapper, SgBChannelStorageFullSync> {
    @Autowired
    private SgBChannelStorageFullSyncMapper sgChannelStorageFullSyncMapper;

    @Reference(version = "1.0", group = "sg")
    private SgBChannelStorageBufferCmd sgChannelStorageBufferCmd;

    @Autowired
    private SgBChannelProductMapper sgChannelProductMapper;

    @Reference(version = "1.0", group = "sg")
    private SgChannelStorageConsumerCmd sgChannelStorageConsumerCmd;

    @Reference(group = "cp-ext", version = "1.0")
    private UserQueryCmd userQueryCmd;

    /**
     * 库存同步状态
     * 0-未同步
     * 1-同步中
     * 2-部分成功
     * 3-同步成功
     * 4-同步失败
     */
    public static final int WAIT_SYNC = 0;
    public static final int DOING_SYNC = 1;
    public static final int SUCCESS_PART = 2;
    public static final int SUCCESS_SYNC = 3;
    public static final int FAIL_SYNC = 4;

    /**
     * 平台库存全量同步 库存同步功能
     *
     * @param session 参数
     * @return ValueHolder
     */
    public ValueHolder stockSyncAction(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        return R3ParamUtils.convertV14WithResult(stockSync(request));
    }

    /**
     * 库存同步逻辑
     *
     * @param request r3请求参数
     * @return ValueHolderV14<SgR3BaseResult>
     */
    private ValueHolderV14<SgR3BaseResult> stockSync(SgR3BaseRequest request) {
        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "操作成功！");
        if (log.isDebugEnabled()) {
            log.debug("Start SgBChannelStorageFullSyncService.stockSync:request{}",
                    JSONObject.toJSONString(request));
        }
        List<Long> syncIds = new ArrayList<>();
        //支持列表批量和单对象
        if (CollectionUtils.isNotEmpty(request.getIds())) {
            syncIds = request.getIds();
        }
        if (request.getObjId() != null) {
            syncIds.add(request.getObjId());
        }
        if (CollectionUtils.isNotEmpty(syncIds)) {
            JSONArray errData = new JSONArray();
            List<Long> updateIds = new ArrayList<>();

            //需要同步的数据
            List<SgBChannelStorageFullSync> storageSyncs = sgChannelStorageFullSyncMapper.selectBatchIds(syncIds);

            long count = storageSyncs.stream().filter(x -> x.getStatus() != WAIT_SYNC).count();

            if (count > 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("状态有不等于未同步的状态，不能执行同步，请选择正确的状态");
                return v14;
            }

            if (CollectionUtils.isNotEmpty(storageSyncs)) {
                List<SgBChannelProduct> products = getSgBChannelProducts(storageSyncs);
                if (CollectionUtils.isEmpty(products)){
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage("当前批次商品或条码已不存在或不可用!");
                    return v14;
                }
                //用于只填了商品编码的明细
                Map<String, List<SgBChannelProduct>> proMap = products.stream().collect(Collectors
                        .groupingBy(SgBChannelProduct::getNumiid));
                //用于填充条码明细
                Map<String, SgBChannelProduct> skuMap = products.stream().collect(Collectors
                        .toMap(SgBChannelProduct::getSkuId, p -> p));
                Map<Long, SgBChannelStorageFullSync> syncMap =
                        storageSyncs.stream().collect(Collectors.toMap(SgBChannelStorageFullSync::getId,
                                Function.identity()));
                // 校验参数和构建库存同步参数
                checkAndSetData(syncIds, errData, updateIds, syncMap, proMap, skuMap,
                        request.getLoginUser());

//                if (CollectionUtils.isNotEmpty(updateIds)) {
//                    SgBChannelStorageFullSync update = new SgBChannelStorageFullSync();
//                    update.setStatus(SgChannelConstants.CHANNEL_STORAGE_FULL_SYNC_IN);
//                    StorageUtils.setBModelDefalutDataByUpdate(update, request.getLoginUser());
//                    update.setModifierename(request.getLoginUser().getEname());
//                    //更新状态
//                    sgChannelStorageFullSyncMapper.update(update, new LambdaUpdateWrapper<SgBChannelStorageFullSync>()
//                            .in(SgBChannelStorageFullSync::getId, updateIds));
//                }
            } else {
                return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("当前记录已不存在！"));
            }
            if (CollectionUtils.isNotEmpty(errData)) {
                v14.setCode(ResultCode.FAIL);
                if (syncIds.size() == 1) {
                    v14.setMessage(errData.getJSONObject(0).getString(R3ParamConstants.MESSAGE));
                } else {
                    v14.setMessage("操作成功:" + (syncIds.size() - errData.size() + ",操作失败:" + errData.size()));
                    SgR3BaseResult sgR3BaseResult = new SgR3BaseResult();
                    sgR3BaseResult.setDataArr(errData);
                    v14.setData(sgR3BaseResult);
                }
            }
        } else {
            return new ValueHolderV14<>(ResultCode.FAIL, Resources.getMessage("请求参数不能为空！"));
        }
        return v14;
    }

    private List<SgBChannelProduct> getSgBChannelProducts(List<SgBChannelStorageFullSync> storageSyncs) {
        List<String> numiids = new ArrayList<>();
        List<String> skuIds = new ArrayList<>();
        storageSyncs.forEach(item -> {
            if (ObjectUtils.isEmpty(item.getSkuId())) {
                //同步商品下所有条码情况--从平台店铺商品表查询所有条码
                numiids.add(item.getNumiid());
            } else {
                //条码不为空的情况商品为空，需要查询商品用于盘判断是否同城购商品
                skuIds.add(item.getSkuId());
            }
        });
        LambdaQueryWrapper<SgBChannelProduct> proWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtils.isNotEmpty(numiids)) {
            proWrapper.eq(SgBChannelProduct::getIsactive, R3CommonResultConstants.VALUE_Y);
            proWrapper.and(qw -> {
                qw.in(SgBChannelProduct::getNumiid, numiids);
                if (CollectionUtils.isNotEmpty(skuIds)) {
                    qw.or(qwo -> qwo.in(SgBChannelProduct::getSkuId, skuIds));
                }
                return qw;
            });
        } else if (CollectionUtils.isNotEmpty(skuIds)) {
            proWrapper.eq(SgBChannelProduct::getIsactive, R3CommonResultConstants.VALUE_Y);
            proWrapper.in(SgBChannelProduct::getSkuId, skuIds);
        }
        AssertUtils.cannot(proWrapper.isEmptyOfWhere(), "需要同步库存的平台商品或条码不存在");
        List<SgBChannelProduct> products = sgChannelProductMapper.selectList(proWrapper);
        if (CollectionUtils.isEmpty(products)) {
            SgBChannelStorageFullSync update = new SgBChannelStorageFullSync();
            update.setRemark("商品或条码不存在或不可用,同步失败!");
            update.setStatus(FAIL_SYNC);
            StorageUtils.setBModelDefalutDataByUpdate(update, SystemUserResource.getRootUser());
            List<Long> updateIds =
                    storageSyncs.stream().map(SgBChannelStorageFullSync::getId).collect(Collectors.toList());
            sgChannelStorageFullSyncMapper.update(update, new LambdaUpdateWrapper<SgBChannelStorageFullSync>()
                    .in(SgBChannelStorageFullSync::getId,updateIds));
        }
        return products;
    }

    /**
     * 校验参数和构建库存同步参数
     *
     * @param syncIds   同步数据id
     * @param errData   错误信息收集
     * @param updateIds 需要更新的数据id
     * @param syncMap   同步数据map
     */
    private void checkAndSetData(List<Long> syncIds, JSONArray errData, List<Long> updateIds,
                                 Map<Long, SgBChannelStorageFullSync> syncMap,
                                 Map<String, List<SgBChannelProduct>> proMap, Map<String,
            SgBChannelProduct> skuMap, User user) {
        Map<String, String> redisMap = new HashMap<>(16);
        for (Long syncId : syncIds) {
            SgChannelStorageBufferBatchSaveRequest batchSaveRequest = new SgChannelStorageBufferBatchSaveRequest();

            //校验
            SgBChannelStorageFullSync storageFullSync = syncMap.get(syncId);
            if (storageFullSync == null) {
                StorageBasicUtils.errorRecord(syncId, "当前记录已不存在！", errData);
                continue;
            }
            if (SgConstants.IS_ACTIVE_N.equals(storageFullSync.getIsactive())) {
                StorageBasicUtils.errorRecord(syncId, "当前单据状态，不允许进行库存同步！", errData);
                continue;
            }
            if (SgChannelConstants.CHANNEL_STORAGE_FULL_SYNC_IN.equals(storageFullSync.getStatus())) {
                StorageBasicUtils.errorRecord(syncId, "当前单据同步中，不允许重复库存同步！", errData);
                continue;
            }
            if (SgChannelConstants.CHANNEL_STORAGE_FULL_SYNC_SUCC.equals(storageFullSync.getStatus())) {
                StorageBasicUtils.errorRecord(syncId, "当前单据已同步成功，不允许重复库存同步！", errData);
                continue;
            }
            //2021.8.12 若只有商品则同步商品关联的所有条码
            List<SgChannelStorageBufferSaveRequest> requestList = new ArrayList<>();
            if (ObjectUtils.isEmpty(storageFullSync.getSkuId())) {
                //如果明细是平台商品id，根据条码进行逐个同步
                List<SgBChannelProduct> channelProducts = proMap.get(storageFullSync.getNumiid());
                if (CollectionUtils.isNotEmpty(channelProducts)) {
                    StringBuilder skuBuilder = new StringBuilder();
                    channelProducts.forEach(cp -> {
                        setData(storageFullSync, requestList, skuMap.get(cp.getSkuId()), cp.getSkuId(), errData);
                        skuBuilder.append(cp.getSkuId()).append(",");
                    });
                    //将商品维度的同步明细存入Redis，便于回写的时候判断是否改商品下的所有平台条码都同步完成
                    redisMap.put(getSkuListKey(storageFullSync.getBatchNo(),
                                    storageFullSync.getCpCShopId(), storageFullSync.getNumiid()),
                            skuBuilder.toString().substring(0, skuBuilder.length() - 1));
                } else {
                    StorageBasicUtils.errorRecord(syncId, "当前商品无对应平台条码，不允许库存同步！", errData);
                }
            } else {
                setData(storageFullSync, requestList, skuMap.get(storageFullSync.getSkuId()),
                        storageFullSync.getSkuId(), errData);
            }
            batchSaveRequest.setBufferSaveRequestList(requestList);
            batchSaveRequest.setUser(user);
            batchSaveRequest.setBatchno(storageFullSync.getBatchNo());
            try {
                //将数据插入缓存池
                ValueHolderV14<List<SgBChannelStorageBuffer>> listValueHolderV14 = sgChannelStorageBufferCmd.saveChannelStorageBufferByFixedNumberByHand(batchSaveRequest);
                if (!redisMap.isEmpty()) {
                    Set<String> keys = redisMap.keySet();
                    keys.forEach(key -> {
                        CusRedisTemplate<String, String> strRedis = RedisMasterUtils.getStrRedisTemplate();
                        strRedis.opsForValue().set(key, redisMap.get(key));
                    });
                }

                SgBChannelStorageFullSync update = new SgBChannelStorageFullSync();
                update.setStatus(SgChannelConstants.CHANNEL_STORAGE_FULL_SYNC_IN);
                StorageUtils.setBModelDefalutDataByUpdate(update, user);
                update.setModifierename(user.getEname());
                //更新状态
                sgChannelStorageFullSyncMapper.update(update, new LambdaUpdateWrapper<SgBChannelStorageFullSync>()
                        .eq(SgBChannelStorageFullSync::getId, syncId));

                List<SgBChannelStorageBuffer> data = listValueHolderV14.getData();
                if (data != null && data.size() > 0) {
                    calcChannelStorageConsumer(data);
                }
            } catch (Exception e) {
                log.error("SgBChannelStorageFullSyncService.checkAndSetData.syncStockError exception_has_occured:{}",
                        Throwables.getStackTraceAsString(e));
                StorageBasicUtils.errorRecord(syncId, e.getMessage(), errData);
            }
//            updateIds.add(syncId);
        }
    }

    private void calcChannelStorageConsumer(List<SgBChannelStorageBuffer> buffers) {
        List<SgChannelStorageOmsCalcRequest> list = new ArrayList<>();
        for (SgBChannelStorageBuffer buffer : buffers) {
            SgChannelStorageOmsCalcRequest request = new SgChannelStorageOmsCalcRequest();
            request.setMqSendTime(new Date());
            request.setBuffId(buffer.getId());
            BeanCopierUtil.copy(buffer, request);
            list.add(request);
        }
        sgChannelStorageConsumerCmd.calcChannelStorageConsumer(list);
    }

    /**
     * 封装同步数据
     */
    public void setData(SgBChannelStorageFullSync storageFullSync, List<SgChannelStorageBufferSaveRequest> requestList,
                        SgBChannelProduct channelProduct, String skuId, JSONArray errData) {
        try {
            if (ObjectUtils.isEmpty(channelProduct)) {
                StorageBasicUtils.errorRecord(storageFullSync.getId(), "平台条码[" + skuId + "]不存在", errData);
                return;
            }
            SgChannelStorageBufferSaveRequest request = new SgChannelStorageBufferSaveRequest();
            request.setBatchNo(storageFullSync.getBatchNo());
            CpShop shopInfo = CommonCacheValUtils.getShopInfo(storageFullSync.getCpCShopId());
            if (shopInfo != null) {
                request.setCpCPlatformId(shopInfo.getCpCPlatformId().intValue());
            } else {
                StorageBasicUtils.errorRecord(storageFullSync.getId(), "当前店铺已不存在,库存同步失败！", errData);
                return;
            }
            request.setCpCPlatformId(shopInfo.getCpCPlatformId().intValue());
            request.setCpCShopId(shopInfo.getId());
            request.setCpCShopTitle(shopInfo.getCpCShopTitle());
            request.setSyncType(SgConstantsIF.SYNC_TYPE_POOL_ALL_CITY_HAND);
            request.setSourceNo("平台库存全量同步,批次号[" + storageFullSync.getBatchNo() + "]");
            request.setDealStatus(SgBChannelStorageBufferExtend.DealStatusEnum.UN_DEAL.getCode());
            request.setPsCProId(channelProduct.getPsCProId());
            request.setPsCSkuId(channelProduct.getPsCSkuId());
            request.setPsCSkuEcode(channelProduct.getPsCSkuEcode());
            request.setPtProId(channelProduct.getNumiid());
            request.setSkuId(channelProduct.getSkuId());
            request.setFixedQty(storageFullSync.getQty());
            request.setFixedQtyFlag(R3CommonResultConstants.VALUE_Y);
            requestList.add(request);
            //同步并更新全量库存状态
            if (log.isDebugEnabled()) {
                log.debug(" 手工全量选择同步的数据：{}", JSON.toJSONString(request));
            }
        } catch (Exception e) {
            log.error("SgBChannelStorageFullSyncService.setData.syncId[{}]--skuId[{}] error:{}",
                    storageFullSync.getId(), skuId, Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 拼接批次同步商品Redis
     *
     * @param batchNo 批次
     * @param shopId  店铺id
     * @param numiid  平台商品id
     * @return Rediskey
     */
    public String getSkuListKey(String batchNo, Long shopId, String numiid) {
        return "sg:fullSync:" + batchNo + "_" + shopId + "_" + numiid;
    }

    /**
     * 获取同步中状态的明细
     *
     * @return 全量量同步明细
     */
    public Map<SgBChannelStorageFullSync, List<SgBChannelProduct>> getItemMap(Integer limit) {
        Map<SgBChannelStorageFullSync, List<SgBChannelProduct>> result = Maps.newHashMap();

        //查询同步中的全量同步记录
        List<SgBChannelStorageFullSync> storageSyncs = sgChannelStorageFullSyncMapper
                .selectList(new LambdaQueryWrapper<SgBChannelStorageFullSync>()
                        .eq(SgBChannelStorageFullSync::getStatus, DOING_SYNC).last(" limit " + limit));
        if (CollectionUtils.isEmpty(storageSyncs)) {
            return result;
        } else {
            List<SgBChannelProduct> sgChannelProducts = getSgBChannelProducts(storageSyncs);
            if (CollectionUtils.isEmpty(sgChannelProducts)) {
                return result;
            }
            //用于只填了商品编码的明细
            Map<String, List<SgBChannelProduct>> proMap = sgChannelProducts.stream().collect(Collectors
                    .groupingBy(SgBChannelProduct::getNumiid));
            //用于填充条码明细
            Map<String, SgBChannelProduct> skuMap = sgChannelProducts.stream().collect(Collectors
                    .toMap(SgBChannelProduct::getSkuId, p -> p));
            for (SgBChannelStorageFullSync storageSync : storageSyncs) {
                if (ObjectUtils.isEmpty(storageSync.getSkuId())) {
                    List<SgBChannelProduct> products = proMap.get(storageSync.getNumiid());
                    if (CollectionUtils.isNotEmpty(products)) {
                        result.put(storageSync, products);
                    } else {
                        log.error("手工全量库存同步状态异常-->{}", JSON.toJSONString(storageSync));
                    }
                } else {
                    SgBChannelProduct sgChannelProduct = skuMap.get(storageSync.getSkuId());
                    if (sgChannelProduct != null) {
                        List<SgBChannelProduct> sgChannelProductList = new ArrayList<>();
                        sgChannelProductList.add(sgChannelProduct);
                        result.put(storageSync, sgChannelProductList);
                    }
                }
            }
        }
        return result;
    }

    public ValueHolderV14 stockSyncByBatch(SgChannelStorageFullSyncRequest request) {
        if (request == null || request.getUserId() == null || StringUtils.isEmpty(request.getBatchNo())) {
            return new ValueHolderV14(ResultCode.FAIL, "参数有误");
        }

        User user = userQueryCmd.getUsersById(request.getUserId());

        if (user == null) {
            return new ValueHolderV14(ResultCode.FAIL, "未查询到用户");
        }

        LambdaQueryWrapper<SgBChannelStorageFullSync> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SgBChannelStorageFullSync::getBatchNo, request.getBatchNo());
        wrapper.eq(SgBChannelStorageFullSync::getStatus, WAIT_SYNC);
        wrapper.eq(SgBChannelStorageFullSync::getIsactive, SgConstants.IS_ACTIVE_Y);
        List<SgBChannelStorageFullSync> storageSyncs = sgChannelStorageFullSyncMapper.selectList(wrapper);

        if (CollectionUtils.isEmpty(storageSyncs)) {
            return new ValueHolderV14(ResultCode.FAIL, "没有需要同步的商品或批次号无效");
        }
        List<SgBChannelProduct> products = getSgBChannelProducts(storageSyncs);
        if (CollectionUtils.isEmpty(products)){
            return new ValueHolderV14(ResultCode.FAIL, "当前批次商品或条码已不存在或不可用");
        }
        //用于只填了商品编码的明细
        Map<String, List<SgBChannelProduct>> proMap = products.stream().collect(Collectors
                .groupingBy(SgBChannelProduct::getNumiid));
        //用于填充条码明细
        Map<String, SgBChannelProduct> skuMap = products.stream().collect(Collectors
                .toMap(SgBChannelProduct::getSkuId, p -> p));
        Map<Long, SgBChannelStorageFullSync> syncMap =
                storageSyncs.stream().collect(Collectors.toMap(SgBChannelStorageFullSync::getId,
                        Function.identity()));
        // 校验参数和构建库存同步参数
        List<Long> syncIds = storageSyncs.stream().map(SgBChannelStorageFullSync::getId).collect(Collectors.toList());
        JSONArray errData = new JSONArray();
        checkAndSetData(syncIds, errData, null, syncMap, proMap, skuMap, user);

        ValueHolderV14<SgR3BaseResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "操作成功");
        if (CollectionUtils.isNotEmpty(errData)) {
            v14.setCode(ResultCode.FAIL);
            if (syncIds.size() == 1) {
                v14.setMessage(errData.getJSONObject(0).getString(R3ParamConstants.MESSAGE));
            } else {
                v14.setMessage("操作成功:" + (syncIds.size() - errData.size() + ",操作失败:" + errData.size()));
                SgR3BaseResult sgR3BaseResult = new SgR3BaseResult();
                sgR3BaseResult.setDataArr(errData);
                v14.setData(sgR3BaseResult);
            }
        }
        return v14;
    }
}
