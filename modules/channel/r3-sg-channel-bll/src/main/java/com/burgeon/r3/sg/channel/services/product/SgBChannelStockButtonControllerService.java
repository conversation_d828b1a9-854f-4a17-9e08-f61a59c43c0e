package com.burgeon.r3.sg.channel.services.product;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.utils.CommonCacheValUtils;
import com.burgeon.r3.sg.channel.mapper.product.SgBChannelProductMapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.utils.RedisMasterUtils;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/11/26 16:29
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgBChannelStockButtonControllerService {

    @Autowired
    private SgBChannelProductMapper sgBChannelProductMapper;

    @Value("${r3.sg.channel.stock.expirationTime:5}")
    private Integer expirationTime;

    public static final String REDIS_KEY_CHANNEL_DOWNPLAT = "downplat";
    public static final String REDIS_KEY_CHANNEL_DOWN_STOCK = "stock:";
    public static final String REDIS_KEY_CHANNEL_SYNCSTOCK = "syncstock";
    public static final String REDIS_KEY_CHANNEL_INC_STOCK = "incstock";
    public static final String REDIS_KEY_CHANNEL_ALL_STOCK = "allstock";
    public static final String REDIS_KEY_CHANNEL_STOCK_SYNC = "sync:";
    public static final String REDIS_KEY_CHANNEL_UPDRATIO = "updratio";
    public static final String REDIS_KEY_CHANNEL_UPDRATIO_SYNC = "sync:";

    public ValueHolderV14 execute() {
        LocalDateTime localDateTime = LocalDateTime.now();
        DateTimeFormatter sdFormat = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        Long beforDateTime = Long.valueOf(sdFormat.format(localDateTime.minusSeconds(expirationTime)));


        //下平台库存
        String downLoadPlatStock = SgConstants.REDIS_KEY_CHANNEL_SG.concat(SgConstants.SG_CONNECTOR_MARKS_1)
                .concat(REDIS_KEY_CHANNEL_DOWNPLAT).concat(SgConstants.SG_CONNECTOR_MARKS_2).concat(SgConstants.SG_CONNECTOR_MARKS_4)
                .concat(REDIS_KEY_CHANNEL_DOWN_STOCK);

        //同步库存
        String updateSyncStockFlag = SgConstants.REDIS_KEY_CHANNEL_SG.concat(SgConstants.SG_CONNECTOR_MARKS_1)
                .concat(REDIS_KEY_CHANNEL_SYNCSTOCK).concat(SgConstants.SG_CONNECTOR_MARKS_2).concat(SgConstants.SG_CONNECTOR_MARKS_4)
                .concat(REDIS_KEY_CHANNEL_DOWN_STOCK);
        //全量库存同步

        String allStockSync = SgConstants.REDIS_KEY_CHANNEL_SG.concat(SgConstants.SG_CONNECTOR_MARKS_1)
                .concat(REDIS_KEY_CHANNEL_ALL_STOCK).concat(SgConstants.SG_CONNECTOR_MARKS_2).concat(SgConstants.SG_CONNECTOR_MARKS_4)
                .concat(REDIS_KEY_CHANNEL_STOCK_SYNC);

        //增量库存同步
        String incStockSync = SgConstants.REDIS_KEY_CHANNEL_SG.concat(SgConstants.SG_CONNECTOR_MARKS_1)
                .concat(REDIS_KEY_CHANNEL_INC_STOCK).concat(SgConstants.SG_CONNECTOR_MARKS_2).concat(SgConstants.SG_CONNECTOR_MARKS_4)
                .concat(REDIS_KEY_CHANNEL_STOCK_SYNC);

        //修改同步比例
        String updateSyncRatio = SgConstants.REDIS_KEY_CHANNEL_SG.concat(SgConstants.SG_CONNECTOR_MARKS_1)
                .concat(REDIS_KEY_CHANNEL_UPDRATIO).concat(SgConstants.SG_CONNECTOR_MARKS_2).concat(SgConstants.SG_CONNECTOR_MARKS_4)
                .concat(REDIS_KEY_CHANNEL_UPDRATIO_SYNC);

        CusRedisTemplate<String, List<String>> redisMasterTemplate = RedisMasterUtils.getStrRedisTemplate();

        List<CpShop> allShopInfoList = CommonCacheValUtils.getAllShopInfoList(null);
        log.info("SgBChannelStockButtonControllerService.execute.size.param:{}", allShopInfoList.size());
        if (CollectionUtils.isNotEmpty(allShopInfoList)) {
            List<Long> cpShopIdList = allShopInfoList.stream().map(CpShop::getCpCShopId).collect(Collectors.toList());
            log.info("SgBChannelStockButtonControllerService.execute.listId.param:{}", JSONObject.toJSONString(cpShopIdList));
            for (Long cpShopId : cpShopIdList) {

                if (redisMasterTemplate.hasKey(downLoadPlatStock + cpShopId)) {
                    redisMasterTemplate.opsForZSet().removeRangeByScore(downLoadPlatStock + cpShopId, 0L, beforDateTime);
                }
                if (redisMasterTemplate.hasKey(updateSyncStockFlag + cpShopId)) {
                    redisMasterTemplate.opsForZSet().removeRangeByScore(updateSyncStockFlag + cpShopId, 0L, beforDateTime);
                }
                if (redisMasterTemplate.hasKey(allStockSync + cpShopId)) {
                    redisMasterTemplate.opsForZSet().removeRangeByScore(allStockSync + cpShopId, 0L, beforDateTime);
                }
                if (redisMasterTemplate.hasKey(incStockSync + cpShopId)) {
                    redisMasterTemplate.opsForZSet().removeRangeByScore(incStockSync + cpShopId, 0L, beforDateTime);
                }
                if (redisMasterTemplate.hasKey(updateSyncRatio + cpShopId)) {
                    redisMasterTemplate.opsForZSet().removeRangeByScore(updateSyncRatio + cpShopId, 0L, beforDateTime);
                }
            }
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "执行成功");
    }
}
