package com.burgeon.r3.sg.channel.validate.storage;

import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.storage.SgBChannelStorageFullSyncMapper;
import com.burgeon.r3.sg.channel.model.dto.storage.SgBChannelStorageFullSyncDto;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgBChannelStorageFullSync;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description 平台库存全量同步
 * <AUTHOR>
 * @Date 2021/6/24 13:25
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgBChannelStorageFullSyncDeleteValidator extends BaseSingleValidator<SgBChannelStorageFullSyncDto> {

    @Autowired
    private SgBChannelStorageFullSyncMapper storageFullSyncMapper;

    @Override
    public String getValidatorMsgName() {
        return "平台库存全量同步删除";
    }

    @Override
    public Class<?> getValidatorClass() {
        return SgBChannelStorageFullSyncDeleteValidator.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgBChannelStorageFullSyncDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        SgBChannelStorageFullSync sgBChannelStorageFullSyncOld = storageFullSyncMapper.selectById(mainObject.getId());
        if (sgBChannelStorageFullSyncOld == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(Resources.getMessage("当前记录不存在!", loginUser.getLocale()));
            return v14;
        }

        //同步状态
        if (sgBChannelStorageFullSyncOld.getStatus().equals(SgChannelConstants.CHANNEL_STORAGE_FULL_SYNC_IN)
                || sgBChannelStorageFullSyncOld.getStatus().equals(SgChannelConstants.CHANNEL_STORAGE_FULL_SYNC_POR_SUCC)
                || sgBChannelStorageFullSyncOld.getStatus().equals(SgChannelConstants.CHANNEL_STORAGE_FULL_SYNC_SUCC)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(Resources.getMessage("当前记录同步状态不符合，不允许删除！", loginUser.getLocale()));
            return v14;
        }
        return v14;
    }

}
