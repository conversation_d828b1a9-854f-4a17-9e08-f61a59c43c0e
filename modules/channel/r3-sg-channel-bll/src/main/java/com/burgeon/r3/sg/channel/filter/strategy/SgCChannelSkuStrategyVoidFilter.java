package com.burgeon.r3.sg.channel.filter.strategy;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategySaItemMapper;
import com.burgeon.r3.sg.channel.mapper.strategy.SgCChannelSkuStrategySpItemMapper;
import com.burgeon.r3.sg.channel.model.dto.strategy.SgCChannelSkuStrategyDTO;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.skustrategy.SgCChannelSkuStrategySaItem;
import com.burgeon.r3.sg.core.model.table.channel.skustrategy.SgCChannelSkuStrategySpItem;
import com.burgeon.r3.sg.core.utils.StorageUtils;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.filter.BaseSingleFilter;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/6/4 19:53
 */
@Slf4j
@Component
public class SgCChannelSkuStrategyVoidFilter extends BaseSingleFilter<SgCChannelSkuStrategyDTO> {

    @Autowired
    private SgCChannelSkuStrategySaItemMapper sgChannelSkuStrategySaItemMapper;
    @Autowired
    private SgCChannelSkuStrategySpItemMapper sgChannelSkuStrategySpItemMapper;

    @Override
    public String getFilterMsgName() {
        return "特殊条码按比例同步策略作废";
    }

    @Override
    public Class getFilterClass() {
        return this.getClass();
    }

    @Override
    public ValueHolderV14 execBeforeMainTable(SgCChannelSkuStrategyDTO mainObject, User loginUser) {
        mainObject.setStatus(SgChannelConstants.BILL_CHANNEL_STRATEGY_VOID);
        mainObject.setIsactive(SgConstants.IS_ACTIVE_N);
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("作废主表成功！"));
    }

    @Override
    public ValueHolderV14 execAfterMainTable(SgCChannelSkuStrategyDTO mainObject, User loginUser) {
        Long mainId = mainObject.getId();
        //作废独享明细
        SgCChannelSkuStrategySaItem updateSaItem = new SgCChannelSkuStrategySaItem();
        updateSaItem.setIsactive(SgConstants.IS_ACTIVE_N);
        StorageUtils.setBModelDefalutDataByUpdate(updateSaItem, loginUser);
        updateSaItem.setModifierename(loginUser.getEname());
        sgChannelSkuStrategySaItemMapper.update(updateSaItem, new LambdaUpdateWrapper<SgCChannelSkuStrategySaItem>()
                .eq(SgCChannelSkuStrategySaItem::getSgCChannelSkuStrategyId, mainId)
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        //作废共享明细
        SgCChannelSkuStrategySpItem updateSpItem = new SgCChannelSkuStrategySpItem();
        updateSpItem.setIsactive(SgConstants.IS_ACTIVE_N);
        StorageUtils.setBModelDefalutDataByUpdate(updateSpItem, loginUser);
        updateSpItem.setModifierename(loginUser.getEname());
        sgChannelSkuStrategySpItemMapper.update(updateSpItem, new LambdaUpdateWrapper<SgCChannelSkuStrategySpItem>()
                .eq(SgCChannelSkuStrategySpItem::getSgCChannelSkuStrategyId, mainId)
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
        return new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("作废明细成功！"));
    }
}
