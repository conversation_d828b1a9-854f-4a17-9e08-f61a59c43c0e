package com.burgeon.r3.sg.channel.validate.storage;

import com.burgeon.r3.sg.channel.common.SgChannelConstants;
import com.burgeon.r3.sg.channel.mapper.storage.SgCChannelStoreSafetySettingItemMapper;
import com.burgeon.r3.sg.channel.mapper.storage.SgCChannelStoreSafetySettingMapper;
import com.burgeon.r3.sg.channel.model.dto.storage.SgCChannelStoreSafetySettingDto;
import com.burgeon.r3.sg.channel.model.dto.storage.SgCChannelStoreSafetySettingItemDto;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgCChannelStoreSafetySetting;
import com.burgeon.r3.sg.core.model.table.channel.storage.SgCChannelStoreSafetySettingItem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.tableservice.validate.BaseSingleItemValidator;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description 平台店铺安全库存批量设置
 * <AUTHOR>
 * @Date 2021/6/23 9:51
 * @Version 1.0
 **/
@Slf4j
@Component
public class SgCChannelStoreSafetySettingItemDeleteValidator extends BaseSingleItemValidator<SgCChannelStoreSafetySettingDto, SgCChannelStoreSafetySettingItemDto> {
    @Autowired
    private SgCChannelStoreSafetySettingMapper storeSafetySettingMapper;

    @Autowired
    private SgCChannelStoreSafetySettingItemMapper storeSafetySettingItemMapper;

    @Override
    public String getValidatorMsgName() {
        return  "平台店铺安全库存批量设置删除";
    }

    @Override
    public Class<?> getValidatorClass() {
        return SgCChannelStoreSafetySettingItemDeleteValidator.class;
    }

    @Override
    public ValueHolderV14 validateMainTable(SgCChannelStoreSafetySettingDto mainObject, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));

        SgCChannelStoreSafetySetting sgCChannelStoreSafetySetting = storeSafetySettingMapper.selectById(mainObject.getId());
        if(sgCChannelStoreSafetySetting == null){
            return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前记录已不存在！", loginUser.getLocale()));
        }
        if(SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_STATUS_SUBMIT == sgCChannelStoreSafetySetting.getStatus()){
            return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前记录已作废，不允许删除明细！", loginUser.getLocale()));
        }
        if(SgChannelConstants.CHANNEL_STORE_SAFETY_SETTING_STATUS_VOID == sgCChannelStoreSafetySetting.getStatus()){
            return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前记录已审核，不允许删除明细！", loginUser.getLocale()));
        }

        return v14;
    }

    @Override
    public ValueHolderV14 validateSubTable(SgCChannelStoreSafetySettingDto mainObject, List<SgCChannelStoreSafetySettingItemDto> subObjectList, User loginUser) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, Resources.getMessage("校验通过"));
        for (SgCChannelStoreSafetySettingItemDto itemDto : subObjectList) {

            SgCChannelStoreSafetySettingItem sgCChannelStoreSafetySettingItem = storeSafetySettingItemMapper.selectById(itemDto.getId());
            if(sgCChannelStoreSafetySettingItem == null){
                return new ValueHolderV14(ResultCode.FAIL, Resources.getMessage("当前明细记录已不存在！", loginUser.getLocale()));
            }
        }
        return v14;
    }
}
